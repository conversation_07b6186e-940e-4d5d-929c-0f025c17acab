from typing import List, Any, Optional, Dict
import random
import string
from utils import RequestSender, ExtractResponse
import json
import heapq
from GeneralPrompt import GENERATE_AVAILABLE_ACTIONS_PROMPT,SCORING_AVAILABLE_ACTIONS_PROMPT, GENERATE_NEXT_STATE_PROMPT
from DomainPrompt import DOMAIN_DESCRIPTION_BLOCKSWORLD

domain_mapping = {
    "blocksworld": DOMAIN_DESCRIPTION_BLOCKSWORLD
}

class Node:
    """
    Description
    -----------
    Node of the tree, each node represents a state.
    
    Parameters
    ----------
    node_id : the ID of the node, which is a unique identifier for the node.
    parent_id : the parent id of this node.
    children_id : children of this node, corresponding to available_actions.
    available_actions : 2D list, each element is a list of [available_action, score].
    state : the state of the tree node.
    action_taken : the last action taken from parent to this node.
    history_actions : the actions taken from root to this node.
    g_cost : the cost from root to this node.
    h_cost : the cost from this node to goal, estimated by LLM.
    status : whether this node is active or pruned.
    flag : whether this state is the goal state.
    pruned_reason : if pruned, the reason for pruning.
    """
    
    def __init__(self, node_id: int, parent_id: Optional[int] = None, state: str = "", query: str = "", g_cost: float = 0.0, h_cost: float = 0.0, **kwargs):
        
        self.node_id: int = node_id
        self.parent_id: Optional[int] = parent_id
        self.children_id: List[int] = []
        self.available_actions: Optional[List[List[Any]]] = kwargs.get("available_actions", [])
        self.state: str = state
        self.action_taken: Optional[List[str]] = kwargs.get("action_taken", [])
        self.history_actions: List[str] = kwargs.get("history_actions", [])
        self.query: str = query
        self.g_cost: float = g_cost
        self.h_cost: float = h_cost
        self.flag: bool = False #True means this state is the goal state
        self.status: bool = True  # True (1) = active, False (0) = pruned
        self.pruned_reason: Optional[str] = None # INFINITE_HEURISTIC_VALUE or DOMAIN_KNOWLEDGE_VIOLATION
        
        
    @property
    def f_cost(self) -> float:
        """count total cost f, f = g+h = actual cost + heuristic cost. """
        h_val = self.h_cost if self.h_cost is not None else 0.0
        return self.g_cost + h_val


    def __lt__(self, other: 'Node') -> bool:
        """compare nodes for priority queue (A* algorithm)."""
        return self.f_cost < other.f_cost
    


class SearchTree:
    """
    Search tree with nodes.
    
    Parameters
    ----------
    root_h_cost : the heuristic cost of the root node.
    initial_state : the initial state.
    query : the goal state.
    
    """
    
    def __init__(self, domain:str, root_h_cost: float, initial_state: str, query:str, **kwargs):
        
        #All nodes
        self.nodes: Dict[int, Node] = {} #store all nodes, key is node_id
        self.next_node_id: int = 0
    
        #ROOT NODE
        self.root_node = Node(node_id=self._get_new_id(), parent_id=-1, g_cost=0.0, h_cost=root_h_cost, state=initial_state, query=query) 
        self.root_id: int = self.root_node.node_id
        self.nodes[self.root_node.node_id] = self.root_node
        root_h_cost = root_h_cost

        #Plan info
        self.domain = domain
        self.initial_state = initial_state
        self.query: str = query
        self.solution: Optional[List[str]] = None

        #LLM info
        self.model = kwargs.get("model", "gpt-5")


    def _get_new_id(self) -> int:
        """return a new node id"""
        new_id = self.next_node_id
        self.next_node_id += 1
        return new_id

    

    def add_node(self, parent_node:Node, action, **kwargs) -> Node:
        
        """
        Add a new node into the tree based on current node.

        Parameters
        ----------
        parent_node : Node, the parent node
        action : str, the action taken from parent to this node
        **kwargs : additional parameters

        Returns
        -------
        Node : the newly created child node
        """
        
        if not isinstance(parent_node, Node) or parent_node.node_id not in self.nodes:
            raise ValueError(f"Illegal Tree Node or Node Id.")
            
        history = parent_node.history_actions + [action]
        
        #store parent's id
        new_node = Node(
            node_id=self._get_new_id(),
            parent_id=parent_node.node_id,
            action_taken=action,
            g_cost=parent_node.g_cost + 1.0,
            h_cost=kwargs.get("h_cost", 0.0),
            state=kwargs.get("state", ""),
            available_actions=kwargs.get("available_actions", []),
            history_actions=history,
            query=kwargs.get("query", "")
        )
        
        #add the new node into the node list
        self.nodes[new_node.node_id] = new_node
        
        #store children's id
        parent_node.children_id.append(new_node.node_id)
        
        return new_node


    def get_node(self, node_id: int) -> Node:
        """
        Assess a node by node_id
        """
        if node_id not in self.nodes:
            raise ValueError(f"Node ID {node_id} does not exist.")
        return self.nodes[node_id]



    def _get_new_state(self, parent_node: Node, action: str, **kwargs) -> str:
        """
        Generate a new state based on the parent node and action.
        """

        prompt = (
            f"[Domain Description]{DOMAIN_DESCRIPTION_BLOCKSWORLD}\n"
            f"Current State: {parent_node.state}\n"
            f"Action: {action}\n"
            f"{GENERATE_NEXT_STATE_PROMPT}"
        )

        response = RequestSender(prompt, model=self.model, **kwargs)
        new_state = ExtractResponse(response, response_type='STATE')

        return new_state
    

    def _get_available_actions(self, node: Node, **kwargs) -> List[List[Any]]:
        """
        Generate available actions and their scores for a given node using LLM.

        Parameters
        ----------
        node : Node, the node for which to generate available actions
        **kwargs : additional parameters for LLM request

        Returns
        -------
        List[List[Any]] : list of [action, score] pairs
        """

        # Generate prompt for LLM to create available actions
        prompt = (
            f"[Domain Description]{DOMAIN_DESCRIPTION_BLOCKSWORLD}\n"
            f"Current State: {node.state}\n"
            f"Goal: {node.query}\n"
            f"{GENERATE_AVAILABLE_ACTIONS_PROMPT}"
        )

        # Call LLM to generate available actions
        response = RequestSender(prompt, model=self.model, **kwargs)
        actions = ExtractResponse(response, response_type='ACTION')

        available_actions = []
        for action in actions:
            prompt = (
                f"[Domain Description]{DOMAIN_DESCRIPTION_BLOCKSWORLD}\n"
                f"Current State: {node.state}\n"
                f"Goal: {node.query}\n"
                f"Action: {action}\n"
                f"{SCORING_AVAILABLE_ACTIONS_PROMPT}"
            )
            response = RequestSender(prompt, model=self.model, **kwargs)
            score = ExtractResponse(response, response_type='SCORE')
            available_actions.append([action, score])
        
        #sort
        available_actions.sort(key=lambda x: x[1], reverse=True)
        node.available_actions = available_actions.copy()
        return available_actions
    

    def _expand_a_new_node(self, parent_node: Node, action: str, **kwargs) -> Node:
        """
        Expand a new node based on the parent node and the selected action.
        """

        new_state = self._get_new_state(parent_node, action, **kwargs)
        new_node = self.add_node(parent_node, action, state=new_state, **kwargs)
        return new_node


    def _expand_nodes(self, parent_node: Node, top_k: int, **kwargs) -> List[Node]:
        """
        expand top-k actions with highest scores into new nodes.
        """
        # Generate available actions for the parent node
        available_actions = self._get_available_actions(parent_node, **kwargs)

        # Select top-k actions
        top_k_actions = available_actions[:top_k]
        new_nodes = []

        for action, score in top_k_actions:
            new_node = self._expand_a_new_node(parent_node, action, **kwargs)
            new_nodes.append(new_node)
        return new_nodes


    def _is_goal_state(self, node: Node, **kwargs) -> bool:
        """
        Check if the current node represents a goal state.
        This uses LLM to determine if the current state matches the goal.

        Parameters
        ----------
        node : Node, the node to check
        **kwargs : additional parameters for LLM request

        Returns
        -------
        bool : True if this is a goal state, False otherwise
        """

        prompt = (
            f"[Domain Description]{DOMAIN_DESCRIPTION_BLOCKSWORLD}\n"
            f"Current State: {node.state}\n"
            f"Goal: {node.query}\n"
            f"Question: Does the current state satisfy the goal? Answer with 'True' if the goal is achieved, 'False' otherwise.\n"
            f"Your answer should be a single word: True or False."
        )

        response = RequestSender(prompt, model=self.model)
        return ExtractResponse(response, response_type='BOOL')


    def tree_search(self, top_k: int = 3, max_depth: int = 10, max_nodes: int = 100, **kwargs) -> Optional[Node]:
        """
        Perform BFS to find a goal state.

        Parameters
        ----------
        top_k : int, number of top actions to expand at each step
        max_depth : int, maximum search depth
        max_nodes : int, maximum number of nodes to explore
        **kwargs : additional parameters for LLM requests
        - save_name: the name of jsonl file to save the tree with all nodes

        Returns
        -------
        Optional[Node] : the goal node if found, None otherwise
        """

        # Initialize search with root node
        frontier = [self.root_node]  # Nodes to explore
        explored_count = 0

        while frontier and explored_count < max_nodes:
            # Get current node to explore
            current_node = frontier.pop(0)  # BFS-style exploration
            explored_count += 1

            print(f"Exploring node {current_node.node_id} at depth {len(current_node.history_actions)}")

            # Check if current node is goal state
            if self._is_goal_state(current_node, **kwargs):
                current_node.flag = True
                print(f"Goal found at node {current_node.node_id}!")
                self.solution = self.get_solution_path(current_node)
                self.save_tree_to_jsonl("search_tree_test.jsonl")
                return current_node

            # Check depth limit
            if len(current_node.history_actions) >= max_depth:
                print(f"Reached max depth at node {current_node.node_id}")
                continue

            # Expand current node
            try:
                new_nodes = self._expand_nodes(current_node, top_k, **kwargs)

                # Add new nodes to frontier
                for new_node in new_nodes:
                    frontier.append(new_node)
                    print(f"Added node {new_node.node_id} with action: {new_node.action_taken}")

            except Exception as e:
                print(f"Error expanding node {current_node.node_id}: {e}")
                continue

        print(f"Search completed. Explored {explored_count} nodes. No goal found.")
        self.save_tree_to_jsonl(kwargs.get("save_name", "search_tree_test_2.jsonl"))
        return None


    def get_solution_path(self, goal_node: Node) -> List[str]:
        """
        Extract the solution path from root to goal node.

        Parameters
        ----------
        goal_node : Node, the goal node

        Returns
        -------
        List[str] : list of actions from root to goal
        """
        if not goal_node or not goal_node.flag:
            return []

        return goal_node.history_actions.copy()


    def print_tree_stats(self):
        """
        Print statistics about the current tree.
        """
        total_nodes = len(self.nodes)
        active_nodes = sum(1 for node in self.nodes.values() if node.status)
        goal_nodes = sum(1 for node in self.nodes.values() if node.flag)

        print(f"Tree Statistics:")
        print(f"  Total nodes: {total_nodes}")
        print(f"  Active nodes: {active_nodes}")
        print(f"  Goal nodes: {goal_nodes}")
        print(f"  Root node ID: {self.root_id}")


    def save_tree_to_jsonl(self, filename: str, include_metadata: bool = True):
        """
        Save all nodes in the search tree to a JSONL file.

        Parameters
        ----------
        filename : str, the output filename (should end with .jsonl)
        include_metadata : bool, whether to include tree metadata in the first line

        Returns
        -------
        str : path to the saved file
        """
        import json
        import os
        from datetime import datetime

        # Ensure filename ends with .jsonl
        if not filename.endswith('.jsonl'):
            filename += '.jsonl'

        try:
            with open(filename, 'w', encoding='utf-8') as f:

                # Write metadata as first line if requested
                if include_metadata:
                    metadata = {
                        "type": "tree_metadata",
                        "domain": self.domain,
                        "initial_state": self.initial_state,
                        "query": self.query,
                        "root_id": self.root_id,
                        "total_nodes": len(self.nodes),
                        "active_nodes": sum(1 for node in self.nodes.values() if node.status),
                        "goal_nodes": sum(1 for node in self.nodes.values() if node.flag),
                        "solution": self.solution if hasattr(self, 'solution') and self.solution else None,
                        "timestamp": datetime.now().strftime("%y%m%d_%H:%M:%S"),
                        "model": self.model
                    }
                    f.write(json.dumps(metadata, ensure_ascii=False) + '\n')

                # Write each node as a separate line
                for node_id in sorted(self.nodes.keys()):
                    node = self.nodes[node_id]
                    node_data = {
                        "type": "node",
                        "node_id": node.node_id,
                        "parent_id": node.parent_id,
                        "children_id": node.children_id.copy(),
                        "state": node.state,
                        "action_taken": node.action_taken,
                        "history_actions": node.history_actions.copy(),
                        "query": node.query,
                        "g_cost": node.g_cost,
                        "h_cost": node.h_cost,
                        "f_cost": node.f_cost,
                        "flag": node.flag,
                        "status": node.status,
                        "pruned_reason": node.pruned_reason,
                        "available_actions": node.available_actions.copy() if node.available_actions else []
                    }
                    f.write(json.dumps(node_data, ensure_ascii=False) + '\n')

            print(f"Tree saved successfully to: {filename}")
            print(f"Total nodes saved: {len(self.nodes)}")
            return os.path.abspath(filename)

        except Exception as e:
            print(f"Error saving tree to {filename}: {e}")
            raise


    def load_tree_from_jsonl(self, filename: str):
        """
        Load a search tree from a JSONL file.

        Parameters
        ----------
        filename : str, the input filename

        Returns
        -------
        dict : metadata from the file (if present)
        """
        import json
        import os

        if not os.path.exists(filename):
            raise FileNotFoundError(f"File not found: {filename}")

        metadata = None
        loaded_nodes = {}

        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue

                    try:
                        data = json.loads(line)

                        if data.get("type") == "tree_metadata":
                            metadata = data
                            # Update tree properties from metadata
                            self.domain = data.get("domain", self.domain)
                            self.initial_state = data.get("initial_state", self.initial_state)
                            self.query = data.get("query", self.query)
                            self.root_id = data.get("root_id", self.root_id)
                            if data.get("solution"):
                                self.solution = data["solution"]

                        elif data.get("type") == "node":
                            # Create node from data
                            node = Node(
                                node_id=data["node_id"],
                                parent_id=data["parent_id"],
                                state=data["state"],
                                query=data["query"],
                                g_cost=data["g_cost"],
                                h_cost=data["h_cost"]
                            )

                            # Set additional properties
                            node.children_id = data["children_id"]
                            node.action_taken = data["action_taken"]
                            node.history_actions = data["history_actions"]
                            node.flag = data["flag"]
                            node.status = data["status"]
                            node.pruned_reason = data["pruned_reason"]
                            node.available_actions = data["available_actions"]

                            loaded_nodes[node.node_id] = node

                    except json.JSONDecodeError as e:
                        print(f"Warning: Invalid JSON on line {line_num}: {e}")
                        continue

            # Replace current nodes with loaded nodes
            self.nodes = loaded_nodes

            # Update next_node_id to avoid conflicts
            if loaded_nodes:
                self.next_node_id = max(loaded_nodes.keys()) + 1

            # Update root_node reference
            if self.root_id in self.nodes:
                self.root_node = self.nodes[self.root_id]

            print(f"Tree loaded successfully from: {filename}")
            print(f"Total nodes loaded: {len(self.nodes)}")

            return metadata

        except Exception as e:
            print(f"Error loading tree from {filename}: {e}")
            raise


