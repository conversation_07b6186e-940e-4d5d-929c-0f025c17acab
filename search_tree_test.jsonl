{"type": "tree_metadata", "domain": "blocksworld", "initial_state": "The white block is on top of the olive block. The turquoise block is on the table. The olive block is on the table. The red block is on top of the turquoise block. The white block is clear. The magenta block is clear. The magenta block is on the table. The red block is clear.", "query": "the turquoise block is not on the table and the olive block is not on the table", "root_id": 0, "total_nodes": 31, "active_nodes": 31, "goal_nodes": 0, "solution": null, "timestamp": "2025-09-15T15:09:02.418975", "model": "gpt-4o"}
{"type": "node", "node_id": 0, "parent_id": -1, "children_id": [1, 2], "state": "The white block is on top of the olive block. The turquoise block is on the table. The olive block is on the table. The red block is on top of the turquoise block. The white block is clear. The magenta block is clear. The magenta block is on the table. The red block is clear.", "action_taken": [], "history_actions": [], "query": "the turquoise block is not on the table and the olive block is not on the table", "g_cost": 0.0, "h_cost": 1.0, "f_cost": 1.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": [["Moving the red block from the turquoise block onto the table", 4.6], ["Moving the red block from the turquoise block to the magenta block", 4.3], ["Moving the white block from the olive block onto the table", 4.2], ["Moving the white block from the olive block to the magenta block", 4.2], ["Moving the white block from the olive block to the red block", 2.8], ["Moving the red block from the turquoise block to the white block", 2.8], ["Moving the magenta block from the table to the white block", 1.8], ["Moving the magenta block from the table to the red block", 1.5]]}
{"type": "node", "node_id": 1, "parent_id": 0, "children_id": [3, 4], "state": "The white block is on top of the olive block. The turquoise block is on the table. The olive block is on the table. The red block is on top of the olive block. The white block is clear. The magenta block is clear. The magenta block is on the table. The red block is clear. The turquoise block is clear", "action_taken": "Move the red block from turquoise block to olive block", "history_actions": ["Move the red block from turquoise block to olive block"], "query": "", "g_cost": 1.0, "h_cost": 0.0, "f_cost": 1.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 2, "parent_id": 0, "children_id": [5, 6], "state": "The white block is on top of the olive block. The turquoise block is on the table. The olive block is on the table. The red block is on the table. The white block is clear. The magenta block is clear. The magenta block is on the table. The red block is clear. The turquoise block is clear", "action_taken": "Move the red block from turquoise block onto the table", "history_actions": ["Move the red block from turquoise block onto the table"], "query": "", "g_cost": 1.0, "h_cost": 0.0, "f_cost": 1.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 3, "parent_id": 1, "children_id": [7, 8], "state": "The white block is on top of the olive block. The turquoise block is on the table. The olive block is on the table. The white block is clear. The magenta block is clear. The magenta block is on the table. The red block is clear. The turquoise block is clear. The olive block is clear. The red block is on the table", "action_taken": "Move red block from olive block onto table", "history_actions": ["Move the red block from turquoise block to olive block", "Move red block from olive block onto table"], "query": "", "g_cost": 2.0, "h_cost": 0.0, "f_cost": 2.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 4, "parent_id": 1, "children_id": [9, 10], "state": "The white block is on the table. The turquoise block is on the table and is clear. The olive block is on the table and is clear. The red block is on top of the olive block and is clear. The magenta block is clear and on the table", "action_taken": "Move white block from olive block onto table", "history_actions": ["Move the red block from turquoise block to olive block", "Move white block from olive block onto table"], "query": "", "g_cost": 2.0, "h_cost": 0.0, "f_cost": 2.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 5, "parent_id": 2, "children_id": [11, 12], "state": "The white block is on top of the olive block. The turquoise block is on top of the magenta block. The olive block is on the table. The red block is on the table. The white block is clear. The turquoise block is clear. The red block is clear. The magenta block is not clear", "action_taken": "Move the turquoise block onto the magenta block", "history_actions": ["Move the red block from turquoise block onto the table", "Move the turquoise block onto the magenta block"], "query": "", "g_cost": 2.0, "h_cost": 0.0, "f_cost": 2.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 6, "parent_id": 2, "children_id": [13, 14], "state": "The white block is on top of the olive block. The turquoise block is on the table. The olive block is on the table. The red block is on top of the turquoise block. The white block is clear. The magenta block is clear. The magenta block is on the table. The turquoise block is not clear. The red block is clear", "action_taken": "Move the red block onto the turquoise block", "history_actions": ["Move the red block from turquoise block onto the table", "Move the red block onto the turquoise block"], "query": "", "g_cost": 2.0, "h_cost": 0.0, "f_cost": 2.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 7, "parent_id": 3, "children_id": [15, 16], "state": "The white block is on top of the olive block. The turquoise block is on top of the red block. The olive block is on the table. The white block is clear. The magenta block is clear. The magenta block is on the table. The turquoise block is clear. The olive block is clear. The red block is not clear", "action_taken": "Moving the turquoise block from the table to the red block", "history_actions": ["Move the red block from turquoise block to olive block", "Move red block from olive block onto table", "Moving the turquoise block from the table to the red block"], "query": "", "g_cost": 3.0, "h_cost": 0.0, "f_cost": 3.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 8, "parent_id": 3, "children_id": [17, 18], "state": "The white block is on top of the olive block. The turquoise block is on top of the magenta block. The olive block is on the table. The white block is clear. The magenta block is not clear. The red block is clear. The olive block is clear. The red block is on the table", "action_taken": "Moving the turquoise block from the table to the magenta block", "history_actions": ["Move the red block from turquoise block to olive block", "Move red block from olive block onto table", "Moving the turquoise block from the table to the magenta block"], "query": "", "g_cost": 3.0, "h_cost": 0.0, "f_cost": 3.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 9, "parent_id": 4, "children_id": [19, 20], "state": "The white block is on the table. The turquoise block is on the table and is clear. The olive block is on the table and is not clear. The red block is on top of the olive block and is clear. The magenta block is clear and on the top of the olive block", "action_taken": "Move the magenta block from the table to the olive block", "history_actions": ["Move the red block from turquoise block to olive block", "Move white block from olive block onto table", "Move the magenta block from the table to the olive block"], "query": "", "g_cost": 3.0, "h_cost": 0.0, "f_cost": 3.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 10, "parent_id": 4, "children_id": [21, 22], "state": "The white block is on the table. The turquoise block is on the top of the olive block and is clear. The olive block is not clear and is on the table. The red block is on top of the olive block and clear. The magenta block is clear and on the table", "action_taken": "Move the turquoise block from the table to the olive block", "history_actions": ["Move the red block from turquoise block to olive block", "Move white block from olive block onto table", "Move the turquoise block from the table to the olive block"], "query": "", "g_cost": 3.0, "h_cost": 0.0, "f_cost": 3.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 11, "parent_id": 5, "children_id": [23, 24], "state": "The white block is on top of the olive block. The turquoise block is on top of the olive block. The olive block is on the table. The red block is on the table. The white block is clear. The turquoise block is clear. The red block is clear. The magenta block is clear", "action_taken": "Moving the turquoise block from the magenta block to the olive block", "history_actions": ["Move the red block from turquoise block onto the table", "Move the turquoise block onto the magenta block", "Moving the turquoise block from the magenta block to the olive block"], "query": "", "g_cost": 3.0, "h_cost": 0.0, "f_cost": 3.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 12, "parent_id": 5, "children_id": [25, 26], "state": "The white block is on top of the olive block. The turquoise block is on top of the red block. The olive block is on the table. The red block is not clear. The white block is clear. The turquoise block is clear. The magenta block is clear. The magenta block is on the table", "action_taken": "Moving the turquoise block from the magenta block to the red block", "history_actions": ["Move the red block from turquoise block onto the table", "Move the turquoise block onto the magenta block", "Moving the turquoise block from the magenta block to the red block"], "query": "", "g_cost": 3.0, "h_cost": 0.0, "f_cost": 3.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 13, "parent_id": 6, "children_id": [27, 28], "state": "The white block is on top of the olive block. The turquoise block is on the table and is clear. The olive block is on the table. The red block is on the table and is clear. The magenta block is clear and on the table", "action_taken": "Move Red block from Turquoise block onto the table", "history_actions": ["Move the red block from turquoise block onto the table", "Move the red block onto the turquoise block", "Move Red block from Turquoise block onto the table"], "query": "", "g_cost": 3.0, "h_cost": 0.0, "f_cost": 3.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 14, "parent_id": 6, "children_id": [29, 30], "state": "The white block is on top of the olive block. The turquoise block is on the table. The olive block is on the table. The red block is on top of the turquoise block. The white block is clear. The magenta block is clear. The magenta block is on the table. The turquoise block is not clear. The red block is clear", "action_taken": "Move Turquoise block from table onto Magenta block", "history_actions": ["Move the red block from turquoise block onto the table", "Move the red block onto the turquoise block", "Move Turquoise block from table onto Magenta block"], "query": "", "g_cost": 3.0, "h_cost": 0.0, "f_cost": 3.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 15, "parent_id": 7, "children_id": [], "state": "The white block is on top of the olive block. The turquoise block is on top of the red block. The magenta block is on top of the olive block. The magenta block is clear. The turquoise block is clear. The white block is clear. The olive block is not clear. The olive block is on the table. The red block is not clear", "action_taken": "Move magenta block from the table to olive block", "history_actions": ["Move the red block from turquoise block to olive block", "Move red block from olive block onto table", "Moving the turquoise block from the table to the red block", "Move magenta block from the table to olive block"], "query": "", "g_cost": 4.0, "h_cost": 0.0, "f_cost": 4.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 16, "parent_id": 7, "children_id": [], "state": "The white block is on top of the magenta block. The turquoise block is on top of the red block. The olive block is on the table. The white block is clear. The magenta block is not clear. The magenta block is on the table. The turquoise block is clear. The olive block is clear. The red block is not clear", "action_taken": "Move white block from olive block to magenta block", "history_actions": ["Move the red block from turquoise block to olive block", "Move red block from olive block onto table", "Moving the turquoise block from the table to the red block", "Move white block from olive block to magenta block"], "query": "", "g_cost": 4.0, "h_cost": 0.0, "f_cost": 4.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 17, "parent_id": 8, "children_id": [], "state": "The white block is on top of the olive block. The olive block is on the table. The white block is clear. The magenta block is clear. The red block is clear. The olive block is clear. The red block is on the table. The turquoise block is on the table", "action_taken": "Move the turquoise block from the magenta block onto the table", "history_actions": ["Move the red block from turquoise block to olive block", "Move red block from olive block onto table", "Moving the turquoise block from the table to the magenta block", "Move the turquoise block from the magenta block onto the table"], "query": "", "g_cost": 4.0, "h_cost": 0.0, "f_cost": 4.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 18, "parent_id": 8, "children_id": [], "state": "The white block is on the table. The turquoise block is on top of the magenta block. The olive block is on the table. The white block is clear. The magenta block is not clear. The red block is clear. The olive block is clear. The red block is on the table", "action_taken": "Move the white block from the olive block onto the table", "history_actions": ["Move the red block from turquoise block to olive block", "Move red block from olive block onto table", "Moving the turquoise block from the table to the magenta block", "Move the white block from the olive block onto the table"], "query": "", "g_cost": 4.0, "h_cost": 0.0, "f_cost": 4.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 19, "parent_id": 9, "children_id": [], "state": "The white block is on the table. The turquoise block is on the table and is clear. The olive block is on the table and is clear. The red block is on top of the olive block and is clear. The magenta block is on the table and is clear", "action_taken": "Moving the magenta block onto the table", "history_actions": ["Move the red block from turquoise block to olive block", "Move white block from olive block onto table", "Move the magenta block from the table to the olive block", "Moving the magenta block onto the table"], "query": "", "g_cost": 4.0, "h_cost": 0.0, "f_cost": 4.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 20, "parent_id": 9, "children_id": [], "state": "The white block is not clear and has the turquoise block on top. The turquoise block is on top of the white block and is clear. The olive block is not clear. The red block is on top of the olive block and is clear. The magenta block is clear and on the top of the olive block", "action_taken": "Moving the turquoise block onto the white block", "history_actions": ["Move the red block from turquoise block to olive block", "Move white block from olive block onto table", "Move the magenta block from the table to the olive block", "Moving the turquoise block onto the white block"], "query": "", "g_cost": 4.0, "h_cost": 0.0, "f_cost": 4.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 21, "parent_id": 10, "children_id": [], "state": "The white block is on the table. The turquoise block is on the top of the magenta block and clear. The olive block is clear and on the table. The red block is on top of the olive block and clear. The magenta block is not clear and on the table", "action_taken": "Move the turquoise block from the olive block to the magenta block", "history_actions": ["Move the red block from turquoise block to olive block", "Move white block from olive block onto table", "Move the turquoise block from the table to the olive block", "Move the turquoise block from the olive block to the magenta block"], "query": "", "g_cost": 4.0, "h_cost": 0.0, "f_cost": 4.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 22, "parent_id": 10, "children_id": [], "state": "The white block is clear and on top of the turquoise block. The turquoise block is not clear and is on top of the olive block. The olive block is not clear and is on the table. The red block is clear and on top of the olive block. The magenta block is clear and on the table", "action_taken": "Move the white block from the table to the turquoise block", "history_actions": ["Move the red block from turquoise block to olive block", "Move white block from olive block onto table", "Move the turquoise block from the table to the olive block", "Move the white block from the table to the turquoise block"], "query": "", "g_cost": 4.0, "h_cost": 0.0, "f_cost": 4.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 23, "parent_id": 11, "children_id": [], "state": "The white block is on top of the olive block. The turquoise block is on top of the olive block. The olive block is on the table. The red block is on the table. The white block is clear. The turquoise block is not clear. The red block is clear. The magenta block is on top of the turquoise block. The magenta block is clear", "action_taken": "Move the magenta block from the table to the turquoise block", "history_actions": ["Move the red block from turquoise block onto the table", "Move the turquoise block onto the magenta block", "Moving the turquoise block from the magenta block to the olive block", "Move the magenta block from the table to the turquoise block"], "query": "", "g_cost": 4.0, "h_cost": 0.0, "f_cost": 4.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 24, "parent_id": 11, "children_id": [], "state": "The white block is on the table. The turquoise block is on top of the olive block. The olive block is on the table. The red block is on the table. The white block is clear. The turquoise block is clear. The red block is clear. The magenta block is clear. The olive block is clear", "action_taken": "Move the white block from the olive block onto the table", "history_actions": ["Move the red block from turquoise block onto the table", "Move the turquoise block onto the magenta block", "Moving the turquoise block from the magenta block to the olive block", "Move the white block from the olive block onto the table"], "query": "", "g_cost": 4.0, "h_cost": 0.0, "f_cost": 4.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 25, "parent_id": 12, "children_id": [], "state": "The white block is on top of the olive block. The turquoise block is on top of the magenta block. The olive block is on the table. The red block is clear. The white block is clear. The turquoise block is clear. The magenta block is not clear. The magenta block is on the table", "action_taken": "Moving the turquoise block from the red block to the magenta block", "history_actions": ["Move the red block from turquoise block onto the table", "Move the turquoise block onto the magenta block", "Moving the turquoise block from the magenta block to the red block", "Moving the turquoise block from the red block to the magenta block"], "query": "", "g_cost": 4.0, "h_cost": 0.0, "f_cost": 4.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 26, "parent_id": 12, "children_id": [], "state": "The white block is on top of the olive block. The turquoise block is on the table. The olive block is on the table. The red block is clear. The white block is clear. The turquoise block is clear. The magenta block is clear. The magenta block is on the table", "action_taken": "Moving the turquoise block from the red block onto the table", "history_actions": ["Move the red block from turquoise block onto the table", "Move the turquoise block onto the magenta block", "Moving the turquoise block from the magenta block to the red block", "Moving the turquoise block from the red block onto the table"], "query": "", "g_cost": 4.0, "h_cost": 0.0, "f_cost": 4.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 27, "parent_id": 13, "children_id": [], "state": "The white block is on top of the olive block. The turquoise block is on top of the red block and is clear. The olive block is on the table. The red block is on the table and is not clear. The magenta block is clear and on the table", "action_taken": "Move the turquoise block from the table to the red block", "history_actions": ["Move the red block from turquoise block onto the table", "Move the red block onto the turquoise block", "Move Red block from Turquoise block onto the table", "Move the turquoise block from the table to the red block"], "query": "", "g_cost": 4.0, "h_cost": 0.0, "f_cost": 4.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 28, "parent_id": 13, "children_id": [], "state": "The white block is on top of the olive block. The turquoise block is on the top of the magenta block and is clear. The olive block is on the table. The red block is on the table and is clear. The magenta block is on the table and is not clear", "action_taken": "Move the turquoise block from the table to the magenta block", "history_actions": ["Move the red block from turquoise block onto the table", "Move the red block onto the turquoise block", "Move Red block from Turquoise block onto the table", "Move the turquoise block from the table to the magenta block"], "query": "", "g_cost": 4.0, "h_cost": 0.0, "f_cost": 4.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 29, "parent_id": 14, "children_id": [], "state": "The white block is on the table. The olive block is on the table. The turquoise block is on the table. The red block is on top of the turquoise block. The white block is clear. The magenta block is clear. The magenta block is on the table. The turquoise block is not clear. The red block is clear. The olive block is clear", "action_taken": "Move white block onto the table", "history_actions": ["Move the red block from turquoise block onto the table", "Move the red block onto the turquoise block", "Move Turquoise block from table onto Magenta block", "Move white block onto the table"], "query": "", "g_cost": 4.0, "h_cost": 0.0, "f_cost": 4.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
{"type": "node", "node_id": 30, "parent_id": 14, "children_id": [], "state": "The red block is on the table. The turquoise block is clear and is on the table. The olive block is on the table. The white block is on top of the olive block. The white block is clear. The magenta block is clear. The magenta block is on the table", "action_taken": "Move red block onto the table", "history_actions": ["Move the red block from turquoise block onto the table", "Move the red block onto the turquoise block", "Move Turquoise block from table onto Magenta block", "Move red block onto the table"], "query": "", "g_cost": 4.0, "h_cost": 0.0, "f_cost": 4.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": []}
