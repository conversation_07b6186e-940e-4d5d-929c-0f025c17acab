(define (problem blocks-world-problem-1)
  (:domain blocks-world)
  
  (:objects 
    a b c d e f - block 
  )
  
  (:init 
    ;; 初始状态：积木c在b上，b在a上，a在桌子上
    (clear c)       ; 积木c顶部是空的
    (on c b)        ; 积木c在积木b上面
    (on b a)        ; 积木b在积木a上面  
    (ontable a)     ; 积木a在桌子上
    (handempty)     ; 手是空的
    (ontable d)
    (ontable e)
    (ontable f)
    (clear d)
    (clear e)
    (clear f)
  )
  
  (:goal 
    ;; 目标状态：积木a在b上，b在c上，c在桌子上
    (and 
      (on a b)      ; 积木a在积木b上面
      (on b c)      ; 积木b在积木c上面
      (ontable c)   ; 积木c在桌子上
      (on e d)
    )
  )
)