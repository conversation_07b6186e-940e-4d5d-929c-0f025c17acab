from tree import Node, SearchTree
from GeneralPrompt import GENERATE_AVAILABLE_ACTIONS_PROMPT, SCORING_AVAILABLE_ACTIONS_PROMPT, CHECK_GOAL_STATE_PROMPT
from DomainPrompt import DOMAIN_DESCRIPTION_BLOCKSWORLD
from utils import RequestSender, ExtractResponse
from typing import List, Any




test_example = {"problem_id": "goalrecognition-blocksworld-5-2-12878", "state": "The white block is on top of the olive block. The turquoise block is on the table. The olive block is on the table. The red block is on top of the turquoise block. The white block is clear. The magenta block is clear. The magenta block is on the table. The red block is clear.", "observations": "<PERSON> moves the white block from the olive block onto the table. <PERSON> moves the olive block from the table to the magenta block.", "query": "the turquoise block is not on the table and the olive block is not on the table", "label": 1}

actions = ['Moving the magenta block from the table to the white block', 'Moving the magenta block from the table to the red block', 'Moving the white block from the olive block onto the table', 'Moving the red block from the turquoise block onto the table', 'Moving the white block from the olive block to the red block', 'Moving the white block from the olive block to the magenta block', 'Moving the red block from the turquoise block to the white block', 'Moving the red block from the turquoise block to the magenta block']

available_actions = [['Moving the red block from the turquoise block onto the table', 4.6], ['Moving the red block from the turquoise block to the magenta block', 4.3], ['Moving the white block from the olive block onto the table', 4.2], ['Moving the white block from the olive block to the magenta block', 4.2], ['Moving the white block from the olive block to the red block', 2.8], ['Moving the red block from the turquoise block to the white block', 2.8], ['Moving the magenta block from the table to the white block', 1.8], ['Moving the magenta block from the table to the red block', 1.5]]


search_tree = SearchTree(domain="blocksworld", root_h_cost=1.0, initial_state=test_example["state"], query=test_example["query"], model="gpt-4o")

search_tree.root_node.available_actions = available_actions

search_tree.tree_search(top_k=2, max_depth=4, max_nodes=30, save_name="new-test-0915")
