(define (domain blocks-world)
  (:requirements :strips :typing)
  
  (:types block)
  
  (:predicates 
    (on ?x - block ?y - block)     ; 积木x在积木y上面
    (ontable ?x - block)           ; 积木x在桌子上
    (clear ?x - block)             ; 积木x的顶部是空的
    (handempty)                    ; 手是空的
    (holding ?x - block)           ; 手里拿着积木x
  )

  ;; 从桌子上拿起积木
  (:action pick-up
    :parameters (?x - block)
    :precondition (and 
      (clear ?x)          ; 积木顶部必须是空的
      (ontable ?x)        ; 积木必须在桌子上
      (handempty)         ; 手必须是空的
    )
    :effect (and 
      (not (ontable ?x))  ; 积木不再在桌子上
      (not (clear ?x))    ; 积木顶部不再空
      (not (handempty))   ; 手不再空
      (holding ?x)        ; 现在手里拿着积木
    )
  )

  ;; 把积木放到桌子上
  (:action put-down
    :parameters (?x - block)
    :precondition (holding ?x)     ; 手里必须拿着积木x
    :effect (and 
      (not (holding ?x))  ; 不再拿着积木
      (clear ?x)          ; 积木顶部现在是空的
      (handempty)         ; 手现在是空的
      (ontable ?x)        ; 积木现在在桌子上
    )
  )

  ;; 把一个积木叠到另一个积木上
  (:action stack
    :parameters (?x - block ?y - block)
    :precondition (and 
      (holding ?x)        ; 手里必须拿着积木x
      (clear ?y)          ; 目标积木y顶部必须是空的
    )
    :effect (and 
      (not (holding ?x))  ; 不再拿着积木x
      (not (clear ?y))    ; 积木y顶部不再空
      (clear ?x)          ; 积木x顶部现在是空的
      (handempty)         ; 手现在是空的
      (on ?x ?y)          ; 积木x现在在积木y上面
    )
  )

  ;; 从另一个积木上拿起积木
  (:action unstack
    :parameters (?x - block ?y - block)
    :precondition (and 
      (on ?x ?y)          ; 积木x必须在积木y上面
      (clear ?x)          ; 积木x顶部必须是空的
      (handempty)         ; 手必须是空的
    )
    :effect (and 
      (holding ?x)        ; 现在手里拿着积木x
      (clear ?y)          ; 积木y顶部现在是空的
      (not (clear ?x))    ; 积木x顶部不再空
      (not (handempty))   ; 手不再空
      (not (on ?x ?y))    ; 积木x不再在积木y上面
    )
  )
)