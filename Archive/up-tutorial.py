from unified_planning.io import PD<PERSON><PERSON>eader
from unified_planning.shortcuts import Ones<PERSON><PERSON>lan<PERSON>
from unified_planning.shortcuts import get_environment
from unified_planning.shortcuts import SequentialSimulator
get_environment().credits_stream = None

# 1. 初始化 reader
reader = PDDLReader()
domain_file = "blocksworld.pddl"
problem_file = "blocksworld.instance.6.pddl"
problem = reader.parse_problem(domain_file, problem_file)

# 查看 problem 的基本信息
#print("Problem name:", problem.name)
#print("Domain fluents:", list(problem.fluents))
#print("Actions:", problem.actions)


# 2. 求解，默认用 pyperplan
#    这里不指定名字的话，UP 会自动挑选一个合适的可用 planner
plan_list = []
with <PERSON><PERSON>Planner(problem_kind=problem.kind) as planner:
    #print("Using planner:", planner.name)
    result = planner.solve(problem)

    if result.plan is not None:
        for action in result.plan.actions:
            plan_list.append(action)
    else:
        print("No plan found.")
        
        
print(plan_list)

# 3. 基于 S0 和的规划进行演进。直接使用 plan 对象进行演进
if result.plan is not None:
    simulator = SequentialSimulator(problem)
    state = simulator.get_initial_state()
    progression = []
    progression.append(("s0", state))

    for i, action in enumerate(result.plan.actions):
        new_state = simulator.apply(state, action)
        progression.append((f"a{i}", str(action)))
        progression.append((f"s{i+1}", new_state))
        state = new_state

    #for tag, elem in progression:
     #   print(tag, elem)
    print(progression)
