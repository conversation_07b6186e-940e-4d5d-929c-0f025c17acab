#!/usr/bin/env python3

import tarski
import tarski.fstrips as fs
from tarski.io.pddl import write_problem



# define predicate
domain = tarski.fstrips.language("blocksworld")
print(domain)
handempty = domain.predicate('handempty')
on = domain.predicate('on', 'object', 'object')
ontable = domain.predicate('ontable', 'object')
clear = domain.predicate('clear', 'object')
holding = domain.predicate('holding', 'object')



#define the problem
problem = fs.create_fstrips_problem(domain_name="blocksworld", problem_name="tutorial_ptoblem", language=domain)


# action schemas
x = domain.variable('x', 'object')
y = domain.variable('y', 'object')

pick_up = problem.action('pick-up', [x],
                    precondition=clear(x) & ontable(x) & handempty(),
                    effects=[fs.DelEffect(ontable(x)),
                             fs.DelEffect(clear(x)),
                             fs.DelEffect(handempty()),
                             fs.AddEffect(holding(x))])

pick_down = problem.action('put-down', [x],
                   precondition=holding(x),
                   effects=[fs.AddEffect(ontable(x)),
                            fs.AddEffect(clear(x)),
                            fs.AddEffect(handempty()),
                            fs.DelEffect(holding(x))])

unstack = problem.action('unstack', [x, y],
                   precondition=on(x, y) & clear(x) & handempty(),
                   effects=[fs.DelEffect(on(x, y)),
                            fs.AddEffect(clear(y)),
                            fs.DelEffect(clear(x)),
                            fs.DelEffect(handempty()),
                            fs.AddEffect(holding(x))])

stack = problem.action('stack', [x, y],
                   precondition=holding(x) & clear(y) & (x != y),
                   effects=[fs.AddEffect(on(x, y)),
                            fs.DelEffect(clear(y)),
                            fs.AddEffect(clear(x)),
                            fs.AddEffect(handempty()),
                            fs.DelEffect(holding(x))])




#grounding
b1, b2, b3 = [domain.constant(f'b{k}', 'object') for k in range(1, 4)]


#define initial states -> create a model
# b1
# b2  b3
init = tarski.model.create(domain)
init.add(clear(b1))
init.add(clear(b3))
init.add(on(b1, b2))
init.add(ontable(b2))
init.add(ontable(b3))
init.add(handempty())
problem.init = init
print(problem.init.as_atoms())


#define goal
# b1 
# b2
# b3
# 可以用定义的 predicate 来写
# & 符号就是 PDDL 的 and
problem.goal = on(b1, b2) & on(b2, b3) & clear(b1)


#
# 指定 domain 文件名和 problem 文件名
domain_file = 'tarski_output_domain.pddl'
problem_file = 'tarski_output_problem.pddl'

write_problem(problem, domain_filename=domain_file, 
              instance_filename=problem_file)

print("SUCCESSFUL")