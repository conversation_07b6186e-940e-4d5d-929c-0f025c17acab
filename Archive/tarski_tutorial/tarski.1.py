from tarski.io import PDDLReader

# 1. 读取 PDDL
reader = PDDLReader()
reader.parse_domain('blocksworld.pddl')
print(reader)
problem = reader.parse_instance('blocksworld.instance.6.pddl')
print(problem)

# 2. 从 problem 拿到 language (= domain)
domain = problem.language
print(domain)

# 3. 初始状态
state = problem.init
print(state)

# 4. 获取动作（操作符）
actions = domain._operators  # 注意：Tarski 0.8.2 中 _operators 才能访问
print(actions)


# 5. plan 列表
plan = ["unstack c b", "put-down c", "unstack b a", "stack b c", "pick-up a", "stack a b", "pick-up e", "stack e d"]

# 6. 定义辅助函数，把 plan 字符串转成 action + 参数字典
def parse_action(step_str):
    parts = step_str.split()
    name = parts[0]
    args = parts[1:]
    # 根据 domain 中动作参数顺序构造字典
    op = actions[name]
    param_names = [p.name for p in op.parameters]
    if len(args) != len(param_names):
        raise ValueError(f"参数数量与 domain 定义不匹配: {step_str}")
    return op, dict(zip(param_names, args))

# 7. 遍历 plan 执行动作
for i, step_str in enumerate(plan, 1):
    op, arg_dict = parse_action(step_str)
    grounded = op.ground(arg_dict)
    if grounded.is_applicable(state):
        state = grounded.apply(state)
        print(f"Step {i}: {step_str}")
        print(state)
    else:
        print(f"Step {i}: {step_str} NOT APPLICABLE")
        break
