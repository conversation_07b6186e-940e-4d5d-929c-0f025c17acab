from tarski.io import PDDLReader

# 创建 reader
reader = PDDLReader(raise_on_error=True)

# 解析 domain 文件
reader.parse_domain("blocksworld.pddl")

# 解析 problem 文件
problem = reader.parse_instance("blocksworld.instance.6.pddl")

lang = problem.language

print("谓词：", lang.predicates)
print("动作：")
for act in problem.actions:
    print("  ", act.name, act.parameters)

print("初始状态：", problem.init.as_atoms())
print("目标：", problem.goal)