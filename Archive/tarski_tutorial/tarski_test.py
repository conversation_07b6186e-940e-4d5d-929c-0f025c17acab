from tarski.io import PDDLReader

# 1. 读取 PDDL
reader = PDDLReader()
reader.parse_domain('blocksworld.pddl')
print(reader.parse_domain('blocksworld.pddl'))
problem = reader.parse_instance('blocksworld.instance.6.pddl')
print(problem)


# 2. 从 problem 拿到 language (= domain)
domain = problem.language

# 3. 初始状态
init_state = problem.init

# 4. 获取动作（操作符）
actions = domain._operators
print("Available actions:", actions.keys())

# 4. 执行动作 (s, a → s')
plan = ["unstack c b", "put-down c", "unstack b a", "stack b c", "pick-up a", "stack a b", "pick-up e", "stack e d"]
action = actions['unstack']
grounded = action.ground({'?x': 'c', '?y': 'b'})  # 实例化动作
if grounded.is_applicable(init_state):
    next_state = grounded.apply(init_state)
    print("Next state:", next_state)

