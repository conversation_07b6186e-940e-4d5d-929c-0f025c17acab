# tarski_grounding.py
from tarski.io import PDDLReader
from tarski.grounding import LPGroundingStrategy
from tarski.grounding.errors import ReachabilityLPUnsolvable
import shutil
import sys

DOMAIN_FILE = "blocksworld.pddl"
PROBLEM_FILE = "blocksworld.instance.6.pddl"

def ensure_gringo():
    if shutil.which("gringo") is None:
        msg = (
            "未找到 `gringo` 可执行文件。请先安装 Potassco 套件：\n"
            "  - macOS（Homebrew）:  brew install clingo   （通常会提供 gringo 别名）\n"
            "  - conda:             conda install -c conda-forge clingo\n"
            "若依然没有 `gringo` 命令，可创建一个同名包装脚本，内部调用：clingo --mode=gringo \"$@\""
        )
        print(msg, file=sys.stderr)
        sys.exit(127)

def main():
    # 1) 读入 PDDL
    reader = PDDLReader(raise_on_error=True)
    reader.parse_domain(DOMAIN_FILE)
    problem = reader.parse_instance(PROBLEM_FILE)

    # 2) 确认 gringo 可用（Tarski 的 LP grounding 会直接调用它）
    ensure_gringo()

    # 3) Grounding（可达分析 + 实例化）
    grounder = LPGroundingStrategy(problem)  # 默认会开启 ground_actions
    try:
        # 3.1 可达的 ground actions：字典 {schema_name: [参数绑定元组, ...]}
        actions = grounder.ground_actions()
        print("===== Reachable Ground Actions =====")
        total = 0
        for name, bindings in actions.items():
            print(f"- {name}: {len(bindings)} bindings")
            for b in bindings:
                # b 形如 ('b1',) 或 ('x','y') 的参数元组
                print(f"  {name}{b}")
            total += len(bindings)
        print(f"Total reachable ground actions: {total}")

        # 3.2 可达的 ground fluents（状态变量）
        lpvars = grounder.ground_state_variables()
        print("\n===== Reachable Fluents =====")
        count = 0
        for i, atom in lpvars.enumerate():
            print(f"{i}: {atom}")
            count += 1
        print(f"Total reachable fluents: {count}")

    except ReachabilityLPUnsolvable:
        # 删除自由化近似下被判定为不可解
        print("Grounding 判定该实例（在 delete-free 近似下）不可解。")

if __name__ == "__main__":
    main()