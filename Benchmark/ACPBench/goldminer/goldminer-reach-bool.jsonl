{"id": 5011635154821118594, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-2f and its arm is empty. The following locations have hard rock: f1-3f, f3-1f, f1-1f, f3-3f, and f1-2f. The following locations have soft rock: f3-2f, f2-2f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-2f location.", "question": "Is it possible to transition to a state where the following holds: The robot is at position f0-0f?", "answer": "yes"}
{"id": 2159023389531114356, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-1f and is holding a laser. The following locations have hard rock: f1-4f and f1-3f. The following locations have soft rock: f2-4f, f1-2f, f0-3f, f0-4f, f2-1f, f2-2f, f2-3f, and f0-2f. The gold is at f0-4f location.", "question": "Is it possible to transition to a state where the following holds: The robot is at position f0-4f and The robot is at position f1-4f?", "answer": "no"}
{"id": -4304504674581420587, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-2f and its arm is empty. The following locations have soft rock: f1-2f, f1-3f, f2-1f, f2-2f, and f2-3f. The gold is at f0-3f location. The laser is at f0-1f location.", "question": "Is it possible to transition to a state where the following holds: The robot is holding a laser and The robot is at position f0-1f?", "answer": "yes"}
{"id": 5939611851157161387, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-2f and its arm is empty. The following locations have hard rock: f2-2f, f2-1f, and f0-3f. The following locations have soft rock: f2-3f and f1-3f. The gold is at f1-3f location. The laser is at f1-1f location.", "question": "Is it possible to transition to a state where the following holds: The robot is at position f0-1f?", "answer": "yes"}
{"id": 2256183000316335299, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-1f and is holding a laser. The following locations have hard rock: f2-2f and f0-3f. The following locations have soft rock: f1-3f, f2-3f, and f0-2f. The gold is at f1-3f location.", "question": "Is it possible to transition to a state where the following holds: The robot is at position f0-1f?", "answer": "yes"}
{"id": -5876511576232440146, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-1f and its arm is empty. The following locations have hard rock: f1-3f, f3-1f, f3-3f, and f1-2f. The following locations have soft rock: f3-2f, f2-2f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f1-0f location.", "question": "Is it possible to transition to a state where the following holds: The gold is at f0-2f location?", "answer": "no"}
{"id": 488906920822912350, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and is holding gold. The following locations have hard rock: f1-3f, f1-1f, and f1-4f. The following locations have soft rock: f2-2f, f2-4f, f1-2f, f2-1f, and f2-3f. The gold is at f0-4f location. The laser is at f0-0f location.", "question": "Is it possible to transition to a state where the following holds: The robot is at position f0-2f?", "answer": "yes"}
{"id": -4917035875754716528, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have hard rock: f1-3f, f1-1f, and f1-4f. The following locations have soft rock: f2-2f, f2-4f, f1-2f, f2-1f, f0-4f, and f2-3f. The gold is at f0-4f location. The laser is at f0-1f location.", "question": "Is it possible to transition to a state where the following holds: The robot is holding a bomb and The robot is at position f0-1f?", "answer": "yes"}
{"id": 2255197774336346575, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and is holding a bomb. The following locations have hard rock: f1-3f, f3-1f, f3-3f, and f1-2f. The following locations have soft rock: f3-2f, f2-2f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-1f location.", "question": "Is it possible to transition to a state where the following holds: The robot is at position f0-2f?", "answer": "yes"}
{"id": -2451579030057291609, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and is holding a laser. The following locations have hard rock: f1-4f, f1-3f, and f1-1f. The following locations have soft rock: f2-4f, f1-2f, f0-4f, f2-1f, f2-2f, and f2-3f. The gold is at f0-4f location.", "question": "Is it possible to transition to a state where the following holds: The laser is at f0-2f location and The robot's arm is empty?", "answer": "yes"}
