{"id": 280253941723595118, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have hard rock: f1-4f, f1-1f, and f1-3f. The following locations have soft rock: f0-1f, f1-2f, f2-2f, f0-2f, f0-3f, f2-4f, f0-4f, f2-1f, and f2-3f. The gold is at f0-4f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robot is at position f0-4f", "answer": "yes"}
{"id": -8617844163438410652, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and its arm is empty. The following locations have hard rock: f2-1f, f2-2f, and f0-3f. The following locations have soft rock: f1-3f, f0-1f, f0-2f, and f2-3f. The gold is at f1-3f location. The laser is at f1-1f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The laser is at f0-0f location", "answer": "no"}
{"id": 6897451989597429681, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have hard rock: f1-4f and f1-3f. The following locations have soft rock: f1-2f, f2-2f, f2-4f, f0-4f, f2-1f, and f2-3f. The gold is at f0-4f location. The laser is at f0-1f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The laser is at f1-1f location", "answer": "no"}
{"id": -8407702394168503875, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and is holding a bomb. The following locations have hard rock: f1-2f, f1-3f, f3-3f, and f3-1f. The following locations have soft rock: f2-2f, f3-2f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robot is at position f0-3f", "answer": "yes"}
{"id": -5388907887471418014, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-2f and its arm is empty. The following locations have hard rock: f1-4f, f1-1f, and f1-3f. The following locations have soft rock: f1-2f, f2-2f, f2-4f, f2-1f, and f2-3f. The gold is at f0-4f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robot is at position f0-4f", "answer": "yes"}
{"id": -4135187869449008489, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x3 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and is holding a laser. The following locations have hard rock: f1-2f. The following locations have soft rock: f2-1f, f2-2f, f0-2f, and f1-1f. The gold is at f0-2f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robot is at position f0-2f", "answer": "yes"}
{"id": -1530896123572976710, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-2f and its arm is empty. The following locations have hard rock: f1-2f, f1-1f, f1-3f, f3-3f, and f3-1f. The following locations have soft rock: f2-2f, f3-2f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Location(s) f0-3f is clear", "answer": "yes"}
{"id": 2024454633805047539, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x3 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and its arm is empty. The following locations have hard rock: f1-2f. The following locations have soft rock: f2-1f, f2-2f, f0-2f, and f1-1f. The gold is at f0-2f location. The laser is at f0-1f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robot is at position f1-2f", "answer": "no"}
{"id": 6667684931946719687, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and is holding a bomb. The following locations have hard rock: f1-2f, f1-1f, f1-3f, f3-3f, and f3-1f. The following locations have soft rock: f2-2f, f3-2f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-1f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robot's arm is empty", "answer": "yes"}
{"id": 7068634563046551094, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-2f, f1-1f, f1-3f, f3-3f, and f3-1f. The following locations have soft rock: f2-2f, f3-2f, f0-3f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Soft rock at f2-1f", "answer": "no"}
