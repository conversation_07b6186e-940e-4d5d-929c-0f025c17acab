{"id": 6830370408662954840, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-4f, f1-1f, and f1-3f. The following locations have soft rock: f2-4f, f1-2f, f0-1f, f2-3f, f0-3f, f0-4f, f2-2f, f0-2f, and f2-1f. The gold is at f0-4f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up the laser at location f0-0f, aim the laser from location f0-0f to location f0-1f, aim the laser from location f0-0f to location f0-1f, move to location f0-1f from location f0-0f, aim the laser from location f0-1f to location f0-2f, move to location f0-2f from location f0-1f, aim the laser from location f0-2f to location f0-3f, place the laser at location f0-2f, move to location f0-1f from location f0-2f, move to location f0-0f from location f0-1f, pick up the bomb at location f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, move to location f0-3f from location f0-2f, trigger the explosion of the bomb at location f0-3f, which is connected to location f0-4f, move to location f0-4f from location f0-3f, retrieve gold from location f0-4f, move to location f0-3f from location f0-4f\"; can the following action be removed from this plan and still have a valid plan: move to location f0-3f from location f0-4f?", "answer": "yes"}
{"id": -1826411954901360631, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f0-2f and f1-1f. The following locations have soft rock: f1-3f, f2-2f, f1-2f, f0-1f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up the laser at loc f0-0f, aim the laser from location f0-0f to location f0-1f, move to location f0-1f from location f0-0f, aim the laser from location f0-1f to location f0-2f, aim the laser from location f0-1f to location f1-1f, move to location f0-0f from location f0-1f, place the laser at location f0-0f, pick up the bomb at loc f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, trigger the explosion of the bomb at location f0-2f, which is connected to location f0-3f, move to location f0-3f from location f0-2f, pick up gold at loc f0-3f\"; can the following action be removed from this plan and still have a valid plan: aim the laser from location f0-1f to location f1-1f?", "answer": "yes"}
{"id": 7196213546310408428, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f0-2f and f1-1f. The following locations have soft rock: f1-3f, f2-2f, f1-2f, f0-1f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move from loc f2-0f to loc f1-0f, move from loc f1-0f to loc f0-0f, pick up the laser at loc f0-0f, fire the laser from location f0-0f to location f0-1f, fire the laser from location f0-0f to location f0-1f, move from loc f0-0f to loc f0-1f, fire the laser from location f0-1f to location f0-2f, place the laser at location f0-1f, move from loc f0-1f to loc f0-0f, pick up the bomb at location f0-0f, move from loc f0-0f to loc f0-1f, move from loc f0-1f to loc f0-2f, trigger the explosion of the bomb at location f0-2f, which is connected to location f0-3f, move from loc f0-2f to loc f0-3f, pick up the gold from location f0-3f, move from loc f0-3f to loc f0-2f, move from loc f0-2f to loc f0-1f\"; can the following action be removed from this plan and still have a valid plan: move from loc f0-0f to loc f0-1f?", "answer": "no"}
{"id": -3270008089837678575, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-1f, f3-3f, f1-2f, f1-3f, and f3-1f. The following locations have soft rock: f3-2f, f0-1f, f2-3f, f0-3f, f2-2f, f0-2f, and f2-1f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up the bomb at loc f0-0f, detonate bomb at loc f0-0f connected to loc f0-1f, pick up laser at loc f0-0f, move to location f0-1f from location f0-0f, aim the laser from location f0-1f to location f0-2f, move to location f0-0f from location f0-1f, put down laser at loc f0-0f, pick up the bomb at loc f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, detonate bomb at loc f0-2f connected to loc f0-3f, move to location f0-3f from location f0-2f, retrieve gold from location f0-3f\"; can the following action be removed from this plan and still have a valid plan: detonate bomb at loc f0-0f connected to loc f0-1f?", "answer": "no"}
{"id": -8291403248385986065, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-1f, f3-3f, f1-2f, f1-3f, and f3-1f. The following locations have soft rock: f3-2f, f0-1f, f2-3f, f0-3f, f2-2f, f0-2f, and f2-1f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move from loc f2-0f to loc f1-0f, move from loc f1-0f to loc f0-0f, pick up laser at loc f0-0f, fire laser from loc f0-0f to loc f0-1f, move from loc f0-0f to loc f0-1f, put down the laser at location f0-1f, pick up laser at loc f0-1f, fire laser from loc f0-1f to loc f0-2f, put down the laser at location f0-1f, move from loc f0-1f to loc f0-0f, pick up the bomb at location f0-0f, move from loc f0-0f to loc f0-1f, move from loc f0-1f to loc f0-2f, trigger the explosion of the bomb at location f0-2f, which is connected to location f0-3f, move from loc f0-2f to loc f0-3f, pick up gold at loc f0-3f\"; can the following action be removed from this plan and still have a valid plan: move from loc f0-1f to loc f0-0f?", "answer": "no"}
{"id": 4582670661047382133, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f0-3f, f2-2f, and f2-1f. The following locations have soft rock: f1-3f, f0-2f, f1-1f, f1-2f, f0-1f, and f2-3f. The gold is at f1-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move from location f2-0f to location f1-0f, move from location f1-0f to location f0-0f, pick up the laser at location f0-0f, move from location f0-0f to location f1-0f, aim the laser from location f1-0f to location f1-1f, move from location f1-0f to location f1-1f, aim the laser from location f1-1f to location f1-2f, aim the laser from location f1-1f to location f0-1f, move from location f1-1f to location f0-1f, move from location f0-1f to location f0-0f, put down the laser at location f0-0f, pick up bomb at loc f0-0f, move from location f0-0f to location f0-1f, move from location f0-1f to location f1-1f, move from location f1-1f to location f1-2f, trigger the explosion of the bomb at location f1-2f, which is connected to location f1-3f, move from location f1-2f to location f1-3f, pick up the gold from location f1-3f\"; can the following action be removed from this plan and still have a valid plan: trigger the explosion of the bomb at location f1-2f, which is connected to location f1-3f?", "answer": "no"}
{"id": 482796750516259182, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f0-2f and f1-1f. The following locations have soft rock: f1-3f, f2-2f, f1-2f, f0-1f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up laser at loc f0-0f, direct the laser from location f0-0f to location f0-1f, move to location f0-1f from location f0-0f, direct the laser from location f0-1f to location f0-2f, direct the laser from location f0-1f to location f1-1f, place the laser at location f0-1f, move to location f0-0f from location f0-1f, pick up the bomb at location f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, detonate bomb at loc f0-2f connected to loc f0-3f, move to location f0-3f from location f0-2f, move to location f0-2f from location f0-3f, move to location f0-3f from location f0-2f, retrieve gold from location f0-3f\"; can the following action be removed from this plan and still have a valid plan: move to location f1-0f from location f2-0f?", "answer": "no"}
{"id": 3382412964919315245, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f0-2f and f1-1f. The following locations have soft rock: f1-3f, f2-2f, f1-2f, f0-1f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f2-0f from location f1-0f, move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, acquire the laser from location f0-0f, direct the laser from location f0-0f to location f0-1f, move to location f0-1f from location f0-0f, direct the laser from location f0-1f to location f0-2f, move to location f0-0f from location f0-1f, place the laser at location f0-0f, pick up the bomb at location f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, trigger the explosion of the bomb at location f0-2f, which is connected to location f0-3f, move to location f0-3f from location f0-2f, pick up gold at location f0-3f, move to location f0-2f from location f0-3f\"; can the following action be removed from this plan and still have a valid plan: move to location f0-2f from location f0-3f?", "answer": "yes"}
{"id": 721763952103877612, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-4f, f1-1f, and f1-3f. The following locations have soft rock: f2-4f, f1-2f, f0-1f, f2-3f, f0-3f, f0-4f, f2-2f, f0-2f, and f2-1f. The gold is at f0-4f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up the laser at loc f0-0f, fire the laser from location f0-0f to location f0-1f, move to location f0-1f from location f0-0f, fire the laser from location f0-1f to location f0-2f, move to location f0-2f from location f0-1f, fire the laser from location f0-2f to location f0-3f, fire the laser from location f0-2f to location f1-2f, move to location f0-1f from location f0-2f, move to location f0-0f from location f0-1f, place the laser at location f0-0f, retrieve the bomb from location f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, move to location f0-3f from location f0-2f, trigger the explosion of the bomb at location f0-3f, which is connected to location f0-4f, move to location f0-4f from location f0-3f, pick up gold at location f0-4f, move to location f0-3f from location f0-4f\"; can the following action be removed from this plan and still have a valid plan: fire the laser from location f0-2f to location f1-2f?", "answer": "yes"}
{"id": 4145217687300971887, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f0-3f, f2-2f, and f2-1f. The following locations have soft rock: f1-3f, f0-2f, f1-1f, f1-2f, f0-1f, and f2-3f. The gold is at f1-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"travel from location f2-0f to location f1-0f, travel from location f1-0f to location f0-0f, pick up laser at loc f0-0f, fire laser from loc f0-0f to loc f0-1f, travel from location f0-0f to location f1-0f, fire laser from loc f1-0f to loc f1-1f, fire laser from loc f1-0f to loc f1-1f, travel from location f1-0f to location f1-1f, fire laser from loc f1-1f to loc f1-2f, put down laser at loc f1-1f, travel from location f1-1f to location f0-1f, travel from location f0-1f to location f0-0f, pick up bomb at loc f0-0f, travel from location f0-0f to location f1-0f, travel from location f1-0f to location f1-1f, travel from location f1-1f to location f1-2f, detonate bomb at loc f1-2f connected to loc f1-3f, travel from location f1-2f to location f1-3f, pick up the gold from location f1-3f\"; can the following action be removed from this plan and still have a valid plan: travel from location f1-0f to location f1-1f?", "answer": "no"}
