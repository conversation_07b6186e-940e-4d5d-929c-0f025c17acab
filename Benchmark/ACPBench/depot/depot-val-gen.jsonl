{"id": 3921485196543106055, "group": "validation_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 5 pallets, 2 distributors, 5 hoists, 3 depots, 2 crates, numbered consecutively. Currently, pallet0, pallet2, pallet4, pallet3, and crate1 are clear; hoist4, hoist2, hoist0, hoist3, and hoist1 are available; pallet2 is at depot2, truck0 is at depot1, truck1 is at distributor0, hoist1 is at depot1, crate0 is at depot1, hoist0 is at depot0, pallet0 is at depot0, hoist3 is at distributor0, hoist4 is at distributor1, pallet1 is at depot1, hoist2 is at depot2, crate1 is at depot1, pallet3 is at distributor0, and pallet4 is at distributor1; crate1 is on crate0 and crate0 is on pallet1. The goal is to reach a state where the following facts hold: crate0 is on pallet1 and crate1 is on pallet2. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the ground ?z at position ?p using the hoist ?x, (drop ?x ?y ?z ?p) - drop crate ?y from hoist ?x onto surface ?z at place ?p, (load ?x ?y ?z ?p) - load crate ?y into truck ?z at place ?p with hoist ?x, and (unload ?x ?y ?z ?p) - unload the crate ?y from the truck ?z at the place ?p using the hoist ?x.", "question": "What is the first inapplicable action in the next sequence of actions: \"(lift hoist1 crate1 crate0 depot1) (load hoist1 crate1 truck0 depot1) (unload hoist0 crate1 truck1 depot0) (unload hoist2 crate1 truck0 depot2) (drive truck0 depot2 distributor1) (drive truck0 distributor1 depot0) (drop hoist2 crate1 pallet2 depot2)\"?", "answer": 2, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-3-2-2-5-5-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot1) (at crate1 depot1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 distributor0) (at hoist4 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 distributor0) (at pallet4 distributor1) (at truck0 depot1) (at truck1 distributor0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (clear crate1) (clear pallet0) (clear pallet2) (clear pallet3) (clear pallet4) (on crate0 pallet1) (on crate1 crate0))\n    (:goal (and (on crate0 pallet1) (on crate1 pallet2)))\n)"}
{"id": 2970914431957463556, "group": "validation_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 5 pallets, 2 distributors, 5 hoists, 3 depots, 2 crates, numbered consecutively. Currently, pallet0, pallet2, pallet4, pallet3, and crate1 are clear; hoist4, hoist2, hoist0, hoist3, and hoist1 are available; pallet2 is at depot2, truck0 is at depot1, truck1 is at distributor0, hoist1 is at depot1, crate0 is at depot1, hoist0 is at depot0, pallet0 is at depot0, hoist3 is at distributor0, hoist4 is at distributor1, pallet1 is at depot1, hoist2 is at depot2, crate1 is at depot1, pallet3 is at distributor0, and pallet4 is at distributor1; crate1 is on crate0 and crate0 is on pallet1. The goal is to reach a state where the following facts hold: crate0 is on pallet1 and crate1 is on pallet2. The available actions are: (drive ?x ?y ?z) - drive the truck ?x from ?y to ?z, (lift ?x ?y ?z ?p) - use the hoist ?x to lift the crate ?y from the surface ?z at location ?p, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - use the hoist ?x to lift and place the crate ?y from place ?p into the truck ?z, and (unload ?x ?y ?z ?p) - unload the crate ?y from the truck ?z at the place ?p using the hoist ?x.", "question": "What is the first inapplicable action in the next sequence of actions: \"(lift hoist1 crate1 crate0 depot1) (load hoist1 crate1 truck0 depot1) (lift hoist1 crate0 pallet4 depot1) (drive truck1 distributor0 depot2) (unload hoist2 crate1 truck0 depot2) (drop hoist2 crate1 pallet2 depot2) (drive truck1 depot2 distributor0)\"?", "answer": 2, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-3-2-2-5-5-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot1) (at crate1 depot1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 distributor0) (at hoist4 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 distributor0) (at pallet4 distributor1) (at truck0 depot1) (at truck1 distributor0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (clear crate1) (clear pallet0) (clear pallet2) (clear pallet3) (clear pallet4) (on crate0 pallet1) (on crate1 crate0))\n    (:goal (and (on crate0 pallet1) (on crate1 pallet2)))\n)"}
{"id": -8421475437091287431, "group": "validation_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 5 pallets, 2 distributors, 5 hoists, 3 depots, 2 crates, numbered consecutively. Currently, pallet0, pallet2, pallet4, pallet3, and crate1 are clear; hoist4, hoist2, hoist0, hoist3, and hoist1 are available; pallet2 is at depot2, truck0 is at depot1, truck1 is at distributor0, hoist1 is at depot1, crate0 is at depot1, hoist0 is at depot0, pallet0 is at depot0, hoist3 is at distributor0, hoist4 is at distributor1, pallet1 is at depot1, hoist2 is at depot2, crate1 is at depot1, pallet3 is at distributor0, and pallet4 is at distributor1; crate1 is on crate0 and crate0 is on pallet1. The goal is to reach a state where the following facts hold: crate0 is on pallet1 and crate1 is on pallet2. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from location ?y to location ?z, (lift ?x ?y ?z ?p) - use hoist ?x to lift crate ?y from surface ?z at place ?p, (drop ?x ?y ?z ?p) - place the crate ?y on the surface ?z at the place ?p using the hoist ?x, (load ?x ?y ?z ?p) - load the crate ?y from place ?p with hoist ?x into the truck ?z, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p.", "question": "What is the first inapplicable action in the next sequence of actions: \"(lift hoist1 crate1 crate0 depot1) (load hoist1 crate1 truck0 depot1) (drive truck0 depot1 depot2) (drive truck1 distributor0 depot1) (drive truck1 depot1 depot2) (unload hoist2 crate1 truck0 depot2) (drive truck1 depot1 distributor0)\"?", "answer": 6, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-3-2-2-5-5-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot1) (at crate1 depot1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 distributor0) (at hoist4 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 distributor0) (at pallet4 distributor1) (at truck0 depot1) (at truck1 distributor0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (clear crate1) (clear pallet0) (clear pallet2) (clear pallet3) (clear pallet4) (on crate0 pallet1) (on crate1 crate0))\n    (:goal (and (on crate0 pallet1) (on crate1 pallet2)))\n)"}
{"id": 1118142788780448311, "group": "validation_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 6 pallets, 2 distributors, 6 hoists, 4 depots, 3 crates, numbered consecutively. Currently, crate0, pallet0, crate2, pallet2, pallet4, and crate1 are clear; hoist4, hoist2, hoist0, hoist5, hoist3, and hoist1 are available; pallet2 is at depot2, hoist3 is at depot3, truck0 is at distributor1, pallet4 is at distributor0, hoist1 is at depot1, hoist0 is at depot0, hoist4 is at distributor0, pallet0 is at depot0, truck1 is at depot0, crate1 is at distributor1, pallet1 is at depot1, pallet3 is at depot3, hoist2 is at depot2, crate2 is at depot1, hoist5 is at distributor1, pallet5 is at distributor1, and crate0 is at depot3; crate1 is on pallet5, crate2 is on pallet1, and crate0 is on pallet3. The goal is to reach a state where the following facts hold: crate1 is on crate2, crate0 is on pallet1, and crate2 is on pallet4. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from location ?y to location ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the ground ?z at position ?p using the hoist ?x, (drop ?x ?y ?z ?p) - drop the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - use the hoist ?x to lift and place the crate ?y from place ?p into the truck ?z, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x.", "question": "What is the first inapplicable action in the next sequence of actions: \"(lift hoist1 crate2 pallet1 depot1) (lift hoist5 crate1 pallet5 distributor1) (load hoist5 crate1 truck0 distributor1) (drive truck0 distributor1 depot1) (load hoist1 crate2 truck0 depot1) (drive truck0 depot1 depot0) (drive truck0 depot0 distributor0) (unload hoist4 crate2 truck0 distributor0) (drop hoist4 crate2 pallet4 distributor0) (unload hoist4 crate1 truck0 distributor0) (drive truck0 distributor0 depot3) (lift hoist3 crate0 pallet3 depot3) (load hoist3 crate0 truck0 depot3) (drive truck0 depot3 depot1) (unload hoist1 crate0 truck0 depot1) (drop hoist4 crate1 crate2 distributor0) (lift hoist4 crate0 pallet0 distributor0)\"?", "answer": 16, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - Crate depot0 depot1 depot2 depot3 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot3) (at crate1 distributor1) (at crate2 depot1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 distributor1) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (clear crate0) (clear crate1) (clear crate2) (clear pallet0) (clear pallet2) (clear pallet4) (on crate0 pallet3) (on crate1 pallet5) (on crate2 pallet1))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": -2168315043838138642, "group": "validation_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 6 pallets, 2 distributors, 6 hoists, 4 depots, 3 crates, numbered consecutively. Currently, crate0, pallet0, crate2, pallet2, pallet4, and crate1 are clear; hoist4, hoist2, hoist0, hoist5, hoist3, and hoist1 are available; pallet2 is at depot2, hoist3 is at depot3, truck0 is at distributor1, pallet4 is at distributor0, hoist1 is at depot1, hoist0 is at depot0, hoist4 is at distributor0, pallet0 is at depot0, truck1 is at depot0, crate1 is at distributor1, pallet1 is at depot1, pallet3 is at depot3, hoist2 is at depot2, crate2 is at depot1, hoist5 is at distributor1, pallet5 is at distributor1, and crate0 is at depot3; crate1 is on pallet5, crate2 is on pallet1, and crate0 is on pallet3. The goal is to reach a state where the following facts hold: crate1 is on crate2, crate0 is on pallet1, and crate2 is on pallet4. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from the place ?y to the place ?z, (lift ?x ?y ?z ?p) - use the hoist ?x to lift the crate ?y from the surface ?z at location ?p, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x and place it on the surface ?z at location ?p, (load ?x ?y ?z ?p) - use hoist ?x to load crate ?y into truck ?z at place ?p, and (unload ?x ?y ?z ?p) - unload the crate ?y from the truck ?z at the place ?p using the hoist ?x.", "question": "What is the first inapplicable action in the next sequence of actions: \"(drive truck1 depot0 depot2) (lift hoist5 crate1 pallet5 distributor1) (load hoist5 crate1 truck0 distributor1) (lift hoist1 crate2 pallet1 depot1) (drive truck0 distributor1 depot1) (load hoist1 crate2 truck0 depot1) (drive truck0 depot1 depot3) (lift hoist3 crate0 pallet3 depot3) (load hoist3 crate0 truck0 depot3) (drive truck0 depot3 distributor0) (lift hoist2 crate2 pallet4 depot2) (drop hoist4 crate2 pallet4 distributor0) (unload hoist4 crate1 truck0 distributor0) (drive truck0 distributor0 depot1) (unload hoist1 crate0 truck0 depot1) (drop hoist4 crate1 crate2 distributor0) (drop hoist1 crate0 pallet1 depot1)\"?", "answer": 10, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - Crate depot0 depot1 depot2 depot3 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot3) (at crate1 distributor1) (at crate2 depot1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 distributor1) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (clear crate0) (clear crate1) (clear crate2) (clear pallet0) (clear pallet2) (clear pallet4) (on crate0 pallet3) (on crate1 pallet5) (on crate2 pallet1))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": -3216220073392836062, "group": "validation_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 6 pallets, 2 distributors, 6 hoists, 4 depots, 3 crates, numbered consecutively. Currently, crate0, pallet0, crate2, pallet2, pallet4, and crate1 are clear; hoist4, hoist2, hoist0, hoist5, hoist3, and hoist1 are available; pallet2 is at depot2, hoist3 is at depot3, truck0 is at distributor1, pallet4 is at distributor0, hoist1 is at depot1, hoist0 is at depot0, hoist4 is at distributor0, pallet0 is at depot0, truck1 is at depot0, crate1 is at distributor1, pallet1 is at depot1, pallet3 is at depot3, hoist2 is at depot2, crate2 is at depot1, hoist5 is at distributor1, pallet5 is at distributor1, and crate0 is at depot3; crate1 is on pallet5, crate2 is on pallet1, and crate0 is on pallet3. The goal is to reach a state where the following facts hold: crate1 is on crate2, crate0 is on pallet1, and crate2 is on pallet4. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from location ?y to location ?z, (lift ?x ?y ?z ?p) - lift crate ?y from surface ?z at place ?p using hoist ?x, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x and place it on the surface ?z at location ?p, (load ?x ?y ?z ?p) - load crate ?y into truck ?z at place ?p with hoist ?x, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p.", "question": "What is the first inapplicable action in the next sequence of actions: \"(lift hoist1 crate2 pallet1 depot1) (lift hoist5 crate1 pallet5 distributor1) (load hoist5 crate1 truck0 distributor1) (drive truck0 distributor1 depot3) (lift hoist3 crate0 pallet3 depot3) (load hoist3 crate0 truck0 depot3) (drive truck1 depot0 depot3) (drive truck0 depot3 depot1) (drop hoist3 crate2 crate1 depot3) (unload hoist1 crate0 truck0 depot1) (drive truck0 depot1 distributor0) (drop hoist1 crate0 pallet1 depot1) (drive truck1 depot3 depot2) (unload hoist4 crate2 truck0 distributor0) (drop hoist4 crate2 pallet4 distributor0) (unload hoist4 crate1 truck0 distributor0) (drop hoist4 crate1 crate2 distributor0)\"?", "answer": 8, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - Crate depot0 depot1 depot2 depot3 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot3) (at crate1 distributor1) (at crate2 depot1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 distributor1) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (clear crate0) (clear crate1) (clear crate2) (clear pallet0) (clear pallet2) (clear pallet4) (on crate0 pallet3) (on crate1 pallet5) (on crate2 pallet1))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": 1977692003259756199, "group": "validation_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 9 pallets, 2 distributors, 9 hoists, 7 depots, 2 crates, numbered consecutively. Currently, crate0, pallet5, pallet0, pallet2, pallet8, pallet4, crate1, pallet6, and pallet7 are clear; hoist8, hoist4, hoist6, hoist2, hoist0, hoist5, hoist3, hoist7, and hoist1 are available; pallet2 is at depot2, hoist3 is at depot3, truck0 is at depot2, hoist7 is at distributor0, pallet4 is at depot4, pallet7 is at distributor0, hoist1 is at depot1, crate0 is at depot1, hoist0 is at depot0, pallet6 is at depot6, pallet0 is at depot0, hoist8 is at distributor1, truck1 is at depot0, crate1 is at depot3, pallet5 is at depot5, pallet1 is at depot1, pallet3 is at depot3, hoist2 is at depot2, hoist4 is at depot4, pallet8 is at distributor1, hoist5 is at depot5, and hoist6 is at depot6; crate1 is on pallet3 and crate0 is on pallet1. The goal is to reach a state where the following facts hold: crate0 is on pallet8 and crate1 is on pallet3. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - use the hoist ?x to lift the crate ?y from the surface ?z at location ?p, (drop ?x ?y ?z ?p) - drop the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - use the hoist ?x to lift and place the crate ?y from place ?p into the truck ?z, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x.", "question": "What is the first inapplicable action in the next sequence of actions: \"(drive truck1 depot0 depot4) (drive truck1 depot2 distributor0) (drive truck0 depot0 depot1) (lift hoist1 crate0 pallet1 depot1) (load hoist1 crate0 truck0 depot1) (drive truck0 depot1 distributor1) (unload hoist8 crate0 truck0 distributor1) (drop hoist8 crate0 pallet8 distributor1)\"?", "answer": 1, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-7-2-2-9-9-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot1) (at crate1 depot3) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 distributor0) (at hoist8 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 distributor0) (at pallet8 distributor1) (at truck0 depot2) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (clear crate0) (clear crate1) (clear pallet0) (clear pallet2) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (on crate0 pallet1) (on crate1 pallet3))\n    (:goal (and (on crate0 pallet8) (on crate1 pallet3)))\n)"}
{"id": -1739905958665935863, "group": "validation_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 4 pallets, 2 distributors, 4 hoists, 2 depots, 2 crates, numbered consecutively. Currently, crate0, pallet2, pallet1, and crate1 are clear; hoist2, hoist0, hoist3, and hoist1 are available; pallet2 is at distributor0, pallet3 is at distributor1, hoist1 is at depot1, hoist2 is at distributor0, hoist0 is at depot0, pallet0 is at depot0, crate0 is at depot0, truck1 is at depot0, crate1 is at distributor1, pallet1 is at depot1, truck0 is at distributor0, and hoist3 is at distributor1; crate0 is on pallet0 and crate1 is on pallet3. The goal is to reach a state where the following facts hold: crate0 is on crate1 and crate1 is on pallet0. The available actions are: (drive ?x ?y ?z) - drive truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the ground ?z at position ?p using the hoist ?x, (drop ?x ?y ?z ?p) - drop the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - use the hoist ?x to lift and place the crate ?y from place ?p into the truck ?z, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p.", "question": "What is the first inapplicable action in the next sequence of actions: \"(drive truck0 distributor0 depot0) (lift hoist0 crate0 pallet0 depot0) (drive truck0 depot0 distributor1) (lift hoist3 crate1 pallet3 distributor1) (drop hoist1 crate1 crate0 depot1) (drive truck0 distributor1 depot0) (load hoist0 crate0 truck0 depot0) (unload hoist0 crate1 truck0 depot0) (drop hoist0 crate1 pallet0 depot0) (unload hoist0 crate0 truck0 depot0) (drop hoist0 crate0 crate1 depot0) (drive truck0 depot0 distributor0)\"?", "answer": 4, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot0) (at crate1 distributor1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 distributor0) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (clear crate0) (clear crate1) (clear pallet1) (clear pallet2) (on crate0 pallet0) (on crate1 pallet3))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": 4752737873644014544, "group": "validation_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 4 pallets, 2 distributors, 4 hoists, 2 depots, 2 crates, numbered consecutively. Currently, crate0, pallet2, pallet1, and crate1 are clear; hoist2, hoist0, hoist3, and hoist1 are available; pallet2 is at distributor0, pallet3 is at distributor1, hoist1 is at depot1, hoist2 is at distributor0, hoist0 is at depot0, pallet0 is at depot0, crate0 is at depot0, truck1 is at depot0, crate1 is at distributor1, pallet1 is at depot1, truck0 is at distributor0, and hoist3 is at distributor1; crate0 is on pallet0 and crate1 is on pallet3. The goal is to reach a state where the following facts hold: crate0 is on crate1 and crate1 is on pallet0. The available actions are: (drive ?x ?y ?z) - drive the truck ?x from ?y to ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the surface ?z at place ?p using the hoist ?x, (drop ?x ?y ?z ?p) - drop the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - use the hoist ?x to lift and place the crate ?y from place ?p into the truck ?z, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x.", "question": "What is the first inapplicable action in the next sequence of actions: \"(drive truck0 distributor0 distributor1) (lift hoist3 crate1 pallet3 distributor1) (lift hoist3 crate1 pallet3 distributor1) (drive truck0 distributor1 depot0) (lift hoist0 crate0 pallet0 depot0) (load hoist0 crate0 truck0 depot0) (drive truck1 depot0 distributor0) (drive truck1 distributor0 depot1) (unload hoist0 crate1 truck0 depot0) (drop hoist0 crate1 pallet0 depot0) (unload hoist0 crate0 truck0 depot0) (drop hoist0 crate0 crate1 depot0)\"?", "answer": 2, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot0) (at crate1 distributor1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 distributor0) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (clear crate0) (clear crate1) (clear pallet1) (clear pallet2) (on crate0 pallet0) (on crate1 pallet3))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": -4310793711796293398, "group": "validation_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 6 pallets, 2 distributors, 6 hoists, 4 depots, 3 crates, numbered consecutively. Currently, crate0, pallet0, crate2, pallet2, pallet4, and crate1 are clear; hoist4, hoist2, hoist0, hoist5, hoist3, and hoist1 are available; pallet2 is at depot2, hoist3 is at depot3, truck0 is at distributor1, pallet4 is at distributor0, hoist1 is at depot1, hoist0 is at depot0, hoist4 is at distributor0, pallet0 is at depot0, truck1 is at depot0, crate1 is at distributor1, pallet1 is at depot1, pallet3 is at depot3, hoist2 is at depot2, crate2 is at depot1, hoist5 is at distributor1, pallet5 is at distributor1, and crate0 is at depot3; crate1 is on pallet5, crate2 is on pallet1, and crate0 is on pallet3. The goal is to reach a state where the following facts hold: crate1 is on crate2, crate0 is on pallet1, and crate2 is on pallet4. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - use the hoist ?x to lift the crate ?y from the surface ?z at location ?p, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x and place it on the surface ?z at location ?p, (load ?x ?y ?z ?p) - use the hoist ?x to lift and place the crate ?y from place ?p into the truck ?z, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p.", "question": "What is the first inapplicable action in the next sequence of actions: \"(lift hoist5 crate1 pallet5 distributor1) (load hoist5 crate1 truck0 distributor1) (drive truck0 depot1 depot0) (drive truck0 distributor0 depot3) (lift hoist1 crate2 pallet1 depot1) (lift hoist3 crate0 pallet3 depot3) (load hoist3 crate0 truck0 depot3) (drive truck0 depot3 depot1) (load hoist1 crate2 truck0 depot1) (unload hoist1 crate0 truck0 depot1) (drive truck0 depot1 distributor0) (unload hoist4 crate2 truck0 distributor0) (drop hoist1 crate0 pallet1 depot1) (drop hoist4 crate2 pallet4 distributor0) (unload hoist4 crate1 truck0 distributor0) (drop hoist4 crate1 crate2 distributor0) (drive truck0 distributor0 depot0)\"?", "answer": 2, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - Crate depot0 depot1 depot2 depot3 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot3) (at crate1 distributor1) (at crate2 depot1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 distributor1) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (clear crate0) (clear crate1) (clear crate2) (clear pallet0) (clear pallet2) (clear pallet4) (on crate0 pallet3) (on crate1 pallet5) (on crate2 pallet1))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
