{"id": -4342773075478632270, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 6 hoists, 2 distributors, 4 depots, 3 crates, 6 pallets, numbered consecutively. Currently, pallet0, pallet2, crate0, pallet5, pallet4, and pallet1 are clear; hoist5, hoist2, hoist3, hoist4, and hoist0 are available; hoist1 is at depot1, crate0 is at depot3, hoist3 is at depot3, pallet2 is at depot2, pallet0 is at depot0, truck0 is at depot1, pallet4 is at distributor0, pallet3 is at depot3, truck1 is at depot3, pallet1 is at depot1, hoist5 is at distributor1, hoist2 is at depot2, hoist4 is at distributor0, pallet5 is at distributor1, and hoist0 is at depot0; crate0 is on pallet3; crate1 is in truck0; hoist1 is lifting crate2.", "question": "Is it possible to transition to a state where the action \"drive truck truck0 from place depot3 to place distributor1\" can be applied?", "answer": "yes"}
{"id": -5541930256487907280, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 9 hoists, 2 distributors, 7 depots, 2 crates, 9 pallets, numbered consecutively. Currently, pallet8, pallet0, pallet2, pallet7, pallet1, crate1, pallet6, pallet5, and pallet4 are clear; hoist5, hoist6, hoist7, hoist1, hoist2, hoist8, hoist3, hoist4, and hoist0 are available; hoist1 is at depot1, pallet6 is at depot6, hoist3 is at depot3, pallet2 is at depot2, pallet0 is at depot0, hoist4 is at depot4, truck0 is at distributor1, pallet3 is at depot3, hoist8 is at distributor1, pallet7 is at distributor0, pallet8 is at distributor1, pallet1 is at depot1, hoist2 is at depot2, pallet4 is at depot4, truck1 is at depot6, hoist5 is at depot5, hoist0 is at depot0, pallet5 is at depot5, hoist7 is at distributor0, hoist6 is at depot6, and crate1 is at depot3; crate1 is on pallet3; crate0 is in truck0.", "question": "Is it possible to transition to a state where the action \"lift the crate crate0 from the surface crate0 at place depot2 using the hoist hoist2\" can be applied?", "answer": "no"}
{"id": -7684579972944915918, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 5 hoists, 2 distributors, 3 depots, 2 crates, 5 pallets, numbered consecutively. Currently, pallet0, crate1, pallet4, pallet3, and pallet1 are clear; hoist2, hoist3, hoist4, and hoist0 are available; hoist1 is at depot1, truck1 is at depot0, pallet2 is at depot2, pallet0 is at depot0, pallet1 is at depot1, truck0 is at depot2, hoist2 is at depot2, pallet4 is at distributor1, crate1 is at depot2, hoist3 is at distributor0, hoist0 is at depot0, pallet3 is at distributor0, and hoist4 is at distributor1; crate1 is on pallet2; hoist1 is lifting crate0.", "question": "Is it possible to transition to a state where the action \"navigate the truck truck1 from place distributor0 to place distributor1\" can be applied?", "answer": "yes"}
{"id": 765685902985112447, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 4 hoists, 2 distributors, 2 depots, 2 crates, 4 pallets, numbered consecutively. Currently, pallet0, pallet2, pallet3, and pallet1 are clear; hoist1, hoist2, and hoist3 are available; pallet3 is at distributor1, hoist2 is at distributor0, hoist1 is at depot1, pallet2 is at distributor0, pallet0 is at depot0, truck0 is at distributor1, hoist3 is at distributor1, pallet1 is at depot1, truck1 is at depot1, and hoist0 is at depot0; crate1 is in truck1; hoist0 is lifting crate0.", "question": "Is it possible to transition to a state where the action \"sail boat truck0 from place distributor1 to place depot0\" can be applied?", "answer": "no"}
{"id": -7925189841199365641, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 9 hoists, 2 distributors, 7 depots, 2 crates, 9 pallets, numbered consecutively. Currently, pallet0, pallet2, pallet7, pallet1, crate0, crate1, pallet6, pallet5, and pallet4 are clear; hoist5, hoist6, hoist7, hoist1, hoist2, hoist8, hoist3, hoist4, and hoist0 are available; hoist1 is at depot1, pallet6 is at depot6, truck1 is at distributor0, crate0 is at distributor1, hoist3 is at depot3, pallet2 is at depot2, pallet0 is at depot0, hoist4 is at depot4, truck0 is at distributor1, pallet3 is at depot3, hoist8 is at distributor1, pallet7 is at distributor0, pallet8 is at distributor1, pallet1 is at depot1, hoist2 is at depot2, pallet4 is at depot4, hoist5 is at depot5, hoist0 is at depot0, pallet5 is at depot5, hoist7 is at distributor0, hoist6 is at depot6, and crate1 is at depot3; crate0 is on pallet8 and crate1 is on pallet3.", "question": "Is it possible to transition to a state where the action \"drive truck truck0 from place depot3 to place depot2\" can be applied?", "answer": "yes"}
{"id": -4561319760216957932, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 9 hoists, 2 distributors, 7 depots, 2 crates, 9 pallets, numbered consecutively. Currently, pallet8, pallet0, pallet2, pallet7, crate0, pallet6, pallet5, pallet4, and pallet3 are clear; hoist5, hoist6, hoist7, hoist1, hoist2, hoist8, hoist4, and hoist0 are available; hoist1 is at depot1, crate0 is at depot1, pallet6 is at depot6, hoist3 is at depot3, pallet2 is at depot2, pallet0 is at depot0, hoist4 is at depot4, truck1 is at depot5, pallet3 is at depot3, hoist8 is at distributor1, pallet7 is at distributor0, pallet8 is at distributor1, pallet1 is at depot1, truck0 is at depot2, hoist2 is at depot2, pallet4 is at depot4, hoist5 is at depot5, hoist0 is at depot0, pallet5 is at depot5, hoist7 is at distributor0, and hoist6 is at depot6; crate0 is on pallet1; hoist3 is lifting crate1.", "question": "Is it possible to transition to a state where the action \"navigate the truck truck1 from location depot0 to location distributor1\" can be applied?", "answer": "yes"}
{"id": -3289685932003074007, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 4 hoists, 2 distributors, 2 depots, 2 crates, 4 pallets, numbered consecutively. Currently, pallet2, crate1, pallet3, and pallet1 are clear; hoist1, hoist2, and hoist3 are available; pallet3 is at distributor1, hoist2 is at distributor0, hoist1 is at depot1, pallet2 is at distributor0, pallet0 is at depot0, truck0 is at distributor1, hoist3 is at distributor1, pallet1 is at depot1, truck1 is at depot1, hoist0 is at depot0, and crate1 is at depot0; crate1 is on pallet0; hoist0 is lifting crate0.", "question": "Is it possible to transition to a state where the action \"navigate the boat truck0 from the place distributor1 to the place depot1\" can be applied?", "answer": "no"}
{"id": -464943208582304044, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 12 hoists, 2 distributors, 10 depots, 2 crates, 12 pallets, numbered consecutively. Currently, pallet8, pallet9, pallet0, pallet2, pallet7, pallet10, pallet11, pallet6, pallet5, pallet4, pallet3, and pallet1 are clear; hoist5, hoist6, hoist7, hoist9, hoist1, hoist8, hoist10, hoist3, hoist4, and hoist0 are available; hoist1 is at depot1, pallet6 is at depot6, pallet8 is at depot8, hoist3 is at depot3, pallet2 is at depot2, pallet0 is at depot0, pallet7 is at depot7, hoist4 is at depot4, hoist7 is at depot7, hoist8 is at depot8, hoist11 is at distributor1, pallet3 is at depot3, hoist10 is at distributor0, pallet10 is at distributor0, pallet1 is at depot1, hoist9 is at depot9, hoist2 is at depot2, pallet4 is at depot4, pallet11 is at distributor1, hoist5 is at depot5, hoist0 is at depot0, truck1 is at depot8, pallet5 is at depot5, truck0 is at depot0, pallet9 is at depot9, and hoist6 is at depot6; hoist2 is lifting crate1 and hoist11 is lifting crate0.", "question": "Is it possible to transition to a state where the action \"drop crate crate0 from hoist hoist7 onto surface crate0 at place depot7\" can be applied?", "answer": "no"}
{"id": 360372597903677301, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 6 hoists, 2 distributors, 4 depots, 3 crates, 6 pallets, numbered consecutively. Currently, pallet0, pallet2, pallet5, pallet4, pallet3, and pallet1 are clear; hoist5, hoist1, hoist2, hoist3, and hoist4 are available; hoist1 is at depot1, truck1 is at depot0, hoist3 is at depot3, pallet2 is at depot2, pallet0 is at depot0, pallet4 is at distributor0, pallet3 is at depot3, truck0 is at depot3, pallet1 is at depot1, hoist5 is at distributor1, hoist2 is at depot2, hoist4 is at distributor0, pallet5 is at distributor1, and hoist0 is at depot0; crate2 is in truck0 and crate1 is in truck0; hoist0 is lifting crate0.", "question": "Is it possible to transition to a state where the action \"lift the crate crate1 from the surface crate1 at place depot1 using the hoist hoist1\" can be applied?", "answer": "no"}
{"id": -8631580485741033800, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 4 hoists, 2 distributors, 2 depots, 2 crates, 4 pallets, numbered consecutively. Currently, pallet2, crate1, pallet3, and pallet1 are clear; hoist1, hoist2, hoist3, and hoist0 are available; pallet3 is at distributor1, hoist2 is at distributor0, hoist1 is at depot1, pallet2 is at distributor0, pallet0 is at depot0, truck0 is at depot1, hoist3 is at distributor1, pallet1 is at depot1, truck1 is at distributor1, hoist0 is at depot0, and crate1 is at depot0; crate1 is on pallet0; crate0 is in truck1.", "question": "Is it possible to transition to a state where the action \"pack hoist hoist0 into crate crate1 in truck truck0 at place depot0\" can be applied?", "answer": "no"}
