{"id": 4379027384567786014, "group": "progression_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 6 hoists, 6 pallets, 2 distributors, 3 crates, 2 trucks, 4 depots, numbered consecutively. Currently, pallet0, pallet2, pallet1, pallet3, pallet5, and crate2 are clear; hoist5, hoist0, hoist2, and hoist3 are available; truck1 is at distributor1, hoist1 is at depot1, pallet2 is at depot2, hoist2 is at depot2, hoist0 is at depot0, hoist4 is at distributor0, crate2 is at distributor0, pallet3 is at depot3, truck0 is at distributor0, pallet4 is at distributor0, pallet1 is at depot1, pallet0 is at depot0, pallet5 is at distributor1, hoist5 is at distributor1, and hoist3 is at depot3; crate2 is on pallet4; hoist1 is lifting crate0 and hoist4 is lifting crate1. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Break down the outcomes of performing the action \"place the crate crate0 on the surface pallet1 at the place depot1 using the hoist hoist1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear pallet1)", "(lifting hoist1 crate0)"], "pos": ["(available hoist1)", "(at crate0 depot1)", "(on crate0 pallet1)", "(clear crate0)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - Crate depot0 depot1 depot2 depot3 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - Pallet truck0 truck1 - Truck)\n    (:init (at crate2 distributor0) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 distributor0) (at truck1 distributor1) (available hoist0) (available hoist2) (available hoist3) (available hoist5) (clear crate2) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet3) (clear pallet5) (lifting hoist1 crate0) (lifting hoist4 crate1) (on crate2 pallet4))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": 1558330494140017519, "group": "progression_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 6 hoists, 6 pallets, 2 distributors, 3 crates, 2 trucks, 4 depots, numbered consecutively. Currently, pallet0, pallet2, pallet1, pallet3, pallet5, and crate2 are clear; hoist5, hoist0, hoist4, hoist2, and hoist3 are available; truck0 is at depot1, truck1 is at distributor0, hoist1 is at depot1, pallet2 is at depot2, hoist2 is at depot2, hoist0 is at depot0, hoist4 is at distributor0, crate2 is at distributor0, pallet3 is at depot3, pallet4 is at distributor0, pallet1 is at depot1, pallet0 is at depot0, pallet5 is at distributor1, hoist5 is at distributor1, and hoist3 is at depot3; crate2 is on pallet4; crate1 is in truck1; hoist1 is lifting crate0. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Break down the outcomes of performing the action \"use the hoist hoist4 to unload the crate crate1 from the truck truck1 at location distributor0\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(available hoist4)", "(in crate1 truck1)"], "pos": ["(lifting hoist4 crate1)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - Crate depot0 depot1 depot2 depot3 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - Pallet truck0 truck1 - Truck)\n    (:init (at crate2 distributor0) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 depot1) (at truck1 distributor0) (available hoist0) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (clear crate2) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet3) (clear pallet5) (in crate1 truck1) (lifting hoist1 crate0) (on crate2 pallet4))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": -7944177106303252886, "group": "progression_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 hoists, 9 pallets, 2 distributors, 2 crates, 2 trucks, 7 depots, numbered consecutively. Currently, pallet0, pallet8, pallet2, pallet6, pallet1, pallet7, pallet5, crate1, and pallet4 are clear; hoist5, hoist0, hoist4, hoist1, hoist6, hoist7, hoist2, and hoist3 are available; pallet7 is at distributor0, hoist1 is at depot1, truck1 is at distributor1, pallet2 is at depot2, hoist0 is at depot0, hoist2 is at depot2, hoist6 is at depot6, pallet4 is at depot4, hoist7 is at distributor0, pallet8 is at distributor1, pallet6 is at depot6, truck0 is at depot3, pallet3 is at depot3, hoist8 is at distributor1, hoist4 is at depot4, pallet1 is at depot1, pallet0 is at depot0, hoist3 is at depot3, hoist5 is at depot5, crate1 is at depot3, and pallet5 is at depot5; crate1 is on pallet3; hoist8 is lifting crate0. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Break down the outcomes of performing the action \"use the hoist hoist8 to lift and place the crate crate0 from place distributor1 into the truck truck1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(lifting hoist8 crate0)"], "pos": ["(available hoist8)", "(in crate0 truck1)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-7-2-2-9-9-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 - Pallet truck0 truck1 - Truck)\n    (:init (at crate1 depot3) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 distributor0) (at hoist8 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 distributor0) (at pallet8 distributor1) (at truck0 depot3) (at truck1 distributor1) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (lifting hoist8 crate0) (on crate1 pallet3))\n    (:goal (and (on crate0 pallet8) (on crate1 pallet3)))\n)"}
{"id": 5124297326178144303, "group": "progression_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 6 hoists, 6 pallets, 2 distributors, 3 crates, 2 trucks, 4 depots, numbered consecutively. Currently, pallet0, pallet2, pallet3, pallet5, pallet4, and crate2 are clear; hoist5, hoist0, hoist4, hoist1, and hoist2 are available; hoist1 is at depot1, pallet2 is at depot2, hoist2 is at depot2, hoist0 is at depot0, hoist4 is at distributor0, truck1 is at depot2, crate2 is at depot1, truck0 is at depot3, pallet3 is at depot3, pallet4 is at distributor0, pallet1 is at depot1, pallet0 is at depot0, pallet5 is at distributor1, hoist5 is at distributor1, and hoist3 is at depot3; crate2 is on pallet1; crate1 is in truck0; hoist3 is lifting crate0. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Break down the outcomes of performing the action \"drive the truck truck0 from depot3 to depot1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at truck0 depot3)"], "pos": ["(at truck0 depot1)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - Crate depot0 depot1 depot2 depot3 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - Pallet truck0 truck1 - Truck)\n    (:init (at crate2 depot1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 depot3) (at truck1 depot2) (available hoist0) (available hoist1) (available hoist2) (available hoist4) (available hoist5) (clear crate2) (clear pallet0) (clear pallet2) (clear pallet3) (clear pallet4) (clear pallet5) (in crate1 truck0) (lifting hoist3 crate0) (on crate2 pallet1))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": 227133824786024634, "group": "progression_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 4 hoists, 4 pallets, 2 distributors, 2 crates, 2 trucks, 2 depots, numbered consecutively. Currently, pallet0, pallet2, pallet1, and pallet3 are clear; hoist1, hoist2, and hoist3 are available; truck0 is at depot1, truck1 is at distributor1, hoist1 is at depot1, hoist0 is at depot0, hoist3 is at distributor1, hoist2 is at distributor0, pallet2 is at distributor0, pallet1 is at depot1, pallet0 is at depot0, and pallet3 is at distributor1; crate0 is in truck0; hoist0 is lifting crate1. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Break down the outcomes of performing the action \"navigate the truck truck0 from place depot1 to place depot0\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at truck0 depot1)"], "pos": ["(at truck0 depot0)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 depot1) (at truck1 distributor1) (available hoist1) (available hoist2) (available hoist3) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet3) (in crate0 truck0) (lifting hoist0 crate1))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": 1565586270639584500, "group": "progression_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 6 hoists, 6 pallets, 2 distributors, 3 crates, 2 trucks, 4 depots, numbered consecutively. Currently, pallet0, pallet2, pallet1, pallet3, pallet5, and pallet4 are clear; hoist5, hoist0, hoist4, and hoist2 are available; hoist1 is at depot1, pallet2 is at depot2, hoist2 is at depot2, hoist0 is at depot0, hoist4 is at distributor0, truck1 is at depot2, truck0 is at depot3, pallet3 is at depot3, pallet4 is at distributor0, pallet1 is at depot1, pallet0 is at depot0, pallet5 is at distributor1, hoist5 is at distributor1, and hoist3 is at depot3; crate1 is in truck0; hoist3 is lifting crate0 and hoist1 is lifting crate2. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Break down the outcomes of performing the action \"navigate the truck truck1 from place depot2 to place depot0\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at truck1 depot2)"], "pos": ["(at truck1 depot0)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - Crate depot0 depot1 depot2 depot3 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - Pallet truck0 truck1 - Truck)\n    (:init (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 depot3) (at truck1 depot2) (available hoist0) (available hoist2) (available hoist4) (available hoist5) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet3) (clear pallet4) (clear pallet5) (in crate1 truck0) (lifting hoist1 crate2) (lifting hoist3 crate0))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": -5230922975762663191, "group": "progression_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 hoists, 9 pallets, 2 distributors, 2 crates, 2 trucks, 7 depots, numbered consecutively. Currently, pallet0, crate0, pallet2, pallet6, pallet1, pallet7, pallet5, crate1, and pallet4 are clear; hoist5, hoist0, hoist4, hoist1, hoist8, hoist6, hoist7, hoist2, and hoist3 are available; pallet7 is at distributor0, hoist1 is at depot1, pallet2 is at depot2, hoist0 is at depot0, hoist2 is at depot2, hoist6 is at depot6, pallet4 is at depot4, hoist7 is at distributor0, pallet8 is at distributor1, pallet6 is at depot6, truck1 is at depot3, pallet3 is at depot3, crate0 is at distributor1, hoist8 is at distributor1, hoist4 is at depot4, pallet1 is at depot1, truck0 is at distributor1, pallet0 is at depot0, hoist3 is at depot3, hoist5 is at depot5, crate1 is at depot3, and pallet5 is at depot5; crate0 is on pallet8 and crate1 is on pallet3. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Break down the outcomes of performing the action \"lift crate crate0 from surface pallet8 at place distributor1 using hoist hoist8\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at crate0 distributor1)", "(clear crate0)", "(on crate0 pallet8)", "(available hoist8)"], "pos": ["(clear pallet8)", "(lifting hoist8 crate0)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-7-2-2-9-9-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 distributor1) (at crate1 depot3) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 distributor0) (at hoist8 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 distributor0) (at pallet8 distributor1) (at truck0 distributor1) (at truck1 depot3) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (clear crate0) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (on crate0 pallet8) (on crate1 pallet3))\n    (:goal (and (on crate0 pallet8) (on crate1 pallet3)))\n)"}
{"id": -7162936096113845352, "group": "progression_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 hoists, 12 pallets, 2 distributors, 2 crates, 2 trucks, 10 depots, numbered consecutively. Currently, pallet10, pallet0, pallet8, pallet9, pallet6, pallet1, pallet7, pallet3, pallet5, crate1, pallet4, and pallet11 are clear; hoist5, hoist0, hoist11, hoist4, hoist1, hoist8, hoist6, hoist10, hoist7, hoist2, hoist9, and hoist3 are available; crate1 is at depot2, hoist1 is at depot1, pallet2 is at depot2, hoist10 is at distributor0, hoist0 is at depot0, hoist2 is at depot2, hoist6 is at depot6, pallet8 is at depot8, pallet4 is at depot4, hoist11 is at distributor1, pallet11 is at distributor1, hoist9 is at depot9, pallet6 is at depot6, pallet10 is at distributor0, pallet7 is at depot7, pallet3 is at depot3, hoist7 is at depot7, hoist4 is at depot4, pallet9 is at depot9, pallet1 is at depot1, truck1 is at depot7, truck0 is at distributor1, pallet0 is at depot0, pallet5 is at depot5, hoist3 is at depot3, hoist5 is at depot5, and hoist8 is at depot8; crate1 is on pallet2; crate0 is in truck0. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Break down the outcomes of performing the action \"navigate the truck truck1 from place depot7 to place depot4\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at truck1 depot7)"], "pos": ["(at truck1 depot4)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-10-2-2-12-12-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 depot7 depot8 depot9 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist10 hoist11 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 hoist9 - Hoist pallet0 pallet1 pallet10 pallet11 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 pallet9 - Pallet truck0 truck1 - Truck)\n    (:init (at crate1 depot2) (at hoist0 depot0) (at hoist1 depot1) (at hoist10 distributor0) (at hoist11 distributor1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 depot7) (at hoist8 depot8) (at hoist9 depot9) (at pallet0 depot0) (at pallet1 depot1) (at pallet10 distributor0) (at pallet11 distributor1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 depot7) (at pallet8 depot8) (at pallet9 depot9) (at truck0 distributor1) (at truck1 depot7) (available hoist0) (available hoist1) (available hoist10) (available hoist11) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (available hoist9) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet10) (clear pallet11) (clear pallet3) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (clear pallet9) (in crate0 truck0) (on crate1 pallet2))\n    (:goal (and (on crate0 pallet11) (on crate1 pallet9)))\n)"}
{"id": -7833336129866513046, "group": "progression_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 6 hoists, 6 pallets, 2 distributors, 3 crates, 2 trucks, 4 depots, numbered consecutively. Currently, pallet0, crate0, pallet2, pallet3, pallet5, and crate1 are clear; hoist5, hoist0, hoist4, hoist1, hoist2, and hoist3 are available; hoist1 is at depot1, pallet2 is at depot2, hoist0 is at depot0, hoist2 is at depot2, hoist4 is at distributor0, crate2 is at distributor0, pallet3 is at depot3, crate1 is at distributor0, truck0 is at distributor0, pallet4 is at distributor0, pallet1 is at depot1, truck1 is at depot1, pallet0 is at depot0, pallet5 is at distributor1, crate0 is at depot1, hoist5 is at distributor1, and hoist3 is at depot3; crate0 is on pallet1, crate1 is on crate2, and crate2 is on pallet4. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Break down the outcomes of performing the action \"lift the crate crate0 from the ground pallet1 at position depot1 using the hoist hoist1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(available hoist1)", "(at crate0 depot1)", "(on crate0 pallet1)", "(clear crate0)"], "pos": ["(clear pallet1)", "(lifting hoist1 crate0)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - Crate depot0 depot1 depot2 depot3 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot1) (at crate1 distributor0) (at crate2 distributor0) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 distributor0) (at truck1 depot1) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (clear crate0) (clear crate1) (clear pallet0) (clear pallet2) (clear pallet3) (clear pallet5) (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": 4868745375750499098, "group": "progression_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 hoists, 9 pallets, 2 distributors, 2 crates, 2 trucks, 7 depots, numbered consecutively. Currently, pallet0, pallet8, pallet2, pallet6, pallet1, pallet7, pallet5, crate1, and pallet4 are clear; hoist5, hoist0, hoist4, hoist1, hoist8, hoist6, hoist7, hoist2, and hoist3 are available; pallet7 is at distributor0, hoist1 is at depot1, pallet2 is at depot2, truck0 is at depot0, hoist0 is at depot0, hoist2 is at depot2, hoist6 is at depot6, pallet4 is at depot4, hoist7 is at distributor0, pallet8 is at distributor1, pallet6 is at depot6, pallet3 is at depot3, hoist8 is at distributor1, hoist4 is at depot4, pallet1 is at depot1, truck1 is at depot1, pallet0 is at depot0, hoist3 is at depot3, hoist5 is at depot5, crate1 is at depot3, and pallet5 is at depot5; crate1 is on pallet3; crate0 is in truck1. The available propositions are: (at ?x ?y) - ?x is at ?y, (on ?x ?y) - ?x is on ?y, (in ?x ?y) - ?x is in ?y, (lifting ?x ?y) - ?x is lifting ?y, (available ?x) - ?x is available, and (clear ?x) - ?x is clear.", "question": "Break down the outcomes of performing the action \"drive truck truck0 from place depot0 to place distributor1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at truck0 depot0)"], "pos": ["(at truck0 distributor1)"]}, "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-7-2-2-9-9-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 - Pallet truck0 truck1 - Truck)\n    (:init (at crate1 depot3) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 distributor0) (at hoist8 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 distributor0) (at pallet8 distributor1) (at truck0 depot0) (at truck1 depot1) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (in crate0 truck1) (on crate1 pallet3))\n    (:goal (and (on crate0 pallet8) (on crate1 pallet3)))\n)"}
