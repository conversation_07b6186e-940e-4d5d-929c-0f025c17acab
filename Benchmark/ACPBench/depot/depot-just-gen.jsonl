{"id": 6331319241807974336, "group": "action_justification_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 2 crates, 5 pallets, 3 depots, 5 hoists, numbered consecutively. Currently, pallet4, crate1, pallet2, pallet3, and pallet0 are clear; hoist3, hoist2, hoist0, hoist4, and hoist1 are available; hoist1 is at depot1, crate1 is at depot1, truck0 is at depot1, pallet0 is at depot0, hoist3 is at distributor0, hoist4 is at distributor1, truck1 is at distributor0, pallet1 is at depot1, hoist2 is at depot2, pallet4 is at distributor1, hoist0 is at depot0, crate0 is at depot1, pallet3 is at distributor0, and pallet2 is at depot2; crate0 is on pallet1 and crate1 is on crate0. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from the place ?y to the place ?z, (lift ?x ?y ?z ?p) - lift crate ?y from surface ?z at place ?p using hoist ?x, (drop ?x ?y ?z ?p) - drop the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - load crate ?y into truck ?z at place ?p with hoist ?x, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x. The goal is to reach a state where the following facts hold: crate0 is on pallet1 and crate1 is on pallet2.", "question": "Simplify the plan \"(drive truck1 distributor0 depot0) (lift hoist1 crate1 crate0 depot1) (drive truck1 depot0 distributor1) (load hoist1 crate1 truck0 depot1) (drive truck0 depot1 depot2) (unload hoist2 crate1 truck0 depot2) (drop hoist2 crate1 pallet2 depot2)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(drive truck1 depot0 distributor1)", "1"]], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-3-2-2-5-5-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot1) (at crate1 depot1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 distributor0) (at hoist4 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 distributor0) (at pallet4 distributor1) (at truck0 depot1) (at truck1 distributor0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (clear crate1) (clear pallet0) (clear pallet2) (clear pallet3) (clear pallet4) (on crate0 pallet1) (on crate1 crate0))\n    (:goal (and (on crate0 pallet1) (on crate1 pallet2)))\n)"}
{"id": 2973600575390736971, "group": "action_justification_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 3 crates, 6 pallets, 4 depots, 6 hoists, numbered consecutively. Currently, crate2, pallet4, crate1, crate0, pallet2, and pallet0 are clear; hoist3, hoist5, hoist2, hoist0, hoist4, and hoist1 are available; hoist1 is at depot1, pallet3 is at depot3, hoist5 is at distributor1, crate1 is at distributor1, pallet0 is at depot0, crate2 is at depot1, pallet5 is at distributor1, crate0 is at depot3, truck1 is at depot0, hoist3 is at depot3, pallet1 is at depot1, hoist2 is at depot2, truck0 is at distributor1, hoist0 is at depot0, pallet4 is at distributor0, hoist4 is at distributor0, and pallet2 is at depot2; crate2 is on pallet1, crate1 is on pallet5, and crate0 is on pallet3. The available actions are: (drive ?x ?y ?z) - drive truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - use the hoist ?x to lift the crate ?y from the surface ?z at location ?p, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x and place it on the surface ?z at location ?p, (load ?x ?y ?z ?p) - load crate ?y into truck ?z at place ?p with hoist ?x, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x. The goal is to reach a state where the following facts hold: crate0 is on pallet1, crate1 is on crate2, and crate2 is on pallet4.", "question": "Simplify the plan \"(lift hoist1 crate2 pallet1 depot1) (lift hoist5 crate1 pallet5 distributor1) (load hoist5 crate1 truck0 distributor1) (drive truck0 distributor1 depot2) (drive truck0 depot2 depot3) (lift hoist3 crate0 pallet3 depot3) (load hoist3 crate0 truck0 depot3) (drive truck0 depot3 depot1) (load hoist1 crate2 truck0 depot1) (unload hoist1 crate0 truck0 depot1) (drive truck0 depot1 distributor0) (drop hoist1 crate0 pallet1 depot1) (drive truck1 depot0 distributor1) (unload hoist4 crate2 truck0 distributor0) (drop hoist4 crate2 pallet4 distributor0) (unload hoist4 crate1 truck0 distributor0) (drop hoist4 crate1 crate2 distributor0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(drive truck1 depot0 distributor1)", "1"]], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - Crate depot0 depot1 depot2 depot3 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot3) (at crate1 distributor1) (at crate2 depot1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 distributor1) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (clear crate0) (clear crate1) (clear crate2) (clear pallet0) (clear pallet2) (clear pallet4) (on crate0 pallet3) (on crate1 pallet5) (on crate2 pallet1))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": 5456589496148673338, "group": "action_justification_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 2 crates, 4 pallets, 2 depots, 4 hoists, numbered consecutively. Currently, crate1, crate0, pallet2, and pallet1 are clear; hoist3, hoist2, hoist0, and hoist1 are available; hoist3 is at distributor1, truck0 is at distributor0, hoist1 is at depot1, crate1 is at distributor1, pallet0 is at depot0, pallet2 is at distributor0, hoist2 is at distributor0, truck1 is at depot0, pallet3 is at distributor1, pallet1 is at depot1, hoist0 is at depot0, and crate0 is at depot0; crate1 is on pallet3 and crate0 is on pallet0. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from the place ?y to the place ?z, (lift ?x ?y ?z ?p) - use hoist ?x to lift crate ?y from surface ?z at place ?p, (drop ?x ?y ?z ?p) - drop crate ?y from hoist ?x onto surface ?z at place ?p, (load ?x ?y ?z ?p) - load crate ?y into truck ?z at place ?p with hoist ?x, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p. The goal is to reach a state where the following facts hold: crate1 is on pallet0 and crate0 is on crate1.", "question": "Simplify the plan \"(drive truck0 distributor0 distributor1) (lift hoist3 crate1 pallet3 distributor1) (load hoist3 crate1 truck0 distributor1) (drive truck0 distributor1 depot0) (lift hoist0 crate0 pallet0 depot0) (load hoist0 crate0 truck1 depot0) (unload hoist0 crate1 truck0 depot0) (drive truck0 depot0 distributor0) (drop hoist0 crate1 pallet0 depot0) (unload hoist0 crate0 truck1 depot0) (drop hoist0 crate0 crate1 depot0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(drive truck0 depot0 distributor0)", "1"]], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot0) (at crate1 distributor1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 distributor0) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (clear crate0) (clear crate1) (clear pallet1) (clear pallet2) (on crate0 pallet0) (on crate1 pallet3))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": 6838174623295545624, "group": "action_justification_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 3 crates, 6 pallets, 4 depots, 6 hoists, numbered consecutively. Currently, crate2, pallet4, crate1, crate0, pallet2, and pallet0 are clear; hoist3, hoist5, hoist2, hoist0, hoist4, and hoist1 are available; hoist1 is at depot1, pallet3 is at depot3, hoist5 is at distributor1, crate1 is at distributor1, pallet0 is at depot0, crate2 is at depot1, pallet5 is at distributor1, crate0 is at depot3, truck1 is at depot0, hoist3 is at depot3, pallet1 is at depot1, hoist2 is at depot2, truck0 is at distributor1, hoist0 is at depot0, pallet4 is at distributor0, hoist4 is at distributor0, and pallet2 is at depot2; crate2 is on pallet1, crate1 is on pallet5, and crate0 is on pallet3. The available actions are: (drive ?x ?y ?z) - drive truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the surface ?z at place ?p using the hoist ?x, (drop ?x ?y ?z ?p) - place the crate ?y on the surface ?z at the place ?p using the hoist ?x, (load ?x ?y ?z ?p) - load the crate ?y from place ?p with hoist ?x into the truck ?z, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p. The goal is to reach a state where the following facts hold: crate0 is on pallet1, crate1 is on crate2, and crate2 is on pallet4.", "question": "Simplify the plan \"(drive truck1 depot0 depot3) (drive truck1 depot3 depot0) (lift hoist1 crate2 pallet1 depot1) (lift hoist5 crate1 pallet5 distributor1) (load hoist5 crate1 truck0 distributor1) (drive truck0 distributor1 depot1) (load hoist1 crate2 truck0 depot1) (drive truck0 depot1 depot3) (lift hoist3 crate0 pallet3 depot3) (load hoist3 crate0 truck0 depot3) (drive truck0 depot3 depot0) (drive truck0 depot0 distributor0) (unload hoist4 crate2 truck0 distributor0) (drop hoist4 crate2 pallet4 distributor0) (unload hoist4 crate1 truck0 distributor0) (drop hoist4 crate1 crate2 distributor0) (drive truck0 distributor0 depot1) (unload hoist1 crate0 truck0 depot1) (drop hoist1 crate0 pallet1 depot1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(drive truck1 depot0 depot3)", "(drive truck1 depot3 depot0)", "-1"]], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - Crate depot0 depot1 depot2 depot3 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot3) (at crate1 distributor1) (at crate2 depot1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 distributor1) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (clear crate0) (clear crate1) (clear crate2) (clear pallet0) (clear pallet2) (clear pallet4) (on crate0 pallet3) (on crate1 pallet5) (on crate2 pallet1))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": 4415840755663936757, "group": "action_justification_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 3 crates, 6 pallets, 4 depots, 6 hoists, numbered consecutively. Currently, crate2, pallet4, crate1, crate0, pallet2, and pallet0 are clear; hoist3, hoist5, hoist2, hoist0, hoist4, and hoist1 are available; hoist1 is at depot1, pallet3 is at depot3, hoist5 is at distributor1, crate1 is at distributor1, pallet0 is at depot0, crate2 is at depot1, pallet5 is at distributor1, crate0 is at depot3, truck1 is at depot0, hoist3 is at depot3, pallet1 is at depot1, hoist2 is at depot2, truck0 is at distributor1, hoist0 is at depot0, pallet4 is at distributor0, hoist4 is at distributor0, and pallet2 is at depot2; crate2 is on pallet1, crate1 is on pallet5, and crate0 is on pallet3. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from location ?y to location ?z, (lift ?x ?y ?z ?p) - use hoist ?x to lift crate ?y from surface ?z at place ?p, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x and place it on the surface ?z at location ?p, (load ?x ?y ?z ?p) - use the hoist ?x to lift and place the crate ?y from place ?p into the truck ?z, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p. The goal is to reach a state where the following facts hold: crate0 is on pallet1, crate1 is on crate2, and crate2 is on pallet4.", "question": "Simplify the plan \"(lift hoist1 crate2 pallet1 depot1) (lift hoist5 crate1 pallet5 distributor1) (load hoist5 crate1 truck0 distributor1) (drive truck0 distributor1 depot3) (lift hoist3 crate0 pallet3 depot3) (load hoist3 crate0 truck0 depot3) (drive truck0 depot3 depot1) (load hoist1 crate2 truck0 depot1) (unload hoist1 crate0 truck0 depot1) (drive truck0 depot1 distributor0) (unload hoist4 crate2 truck0 distributor0) (drop hoist4 crate2 pallet4 distributor0) (unload hoist4 crate1 truck0 distributor0) (drop hoist4 crate1 crate2 distributor0) (lift hoist4 crate1 crate2 distributor0) (drop hoist4 crate1 crate2 distributor0) (drop hoist1 crate0 pallet1 depot1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(drop hoist4 crate1 crate2 distributor0)", "(lift hoist4 crate1 crate2 distributor0)", "-1"], ["(lift hoist4 crate1 crate2 distributor0)", "(drop hoist4 crate1 crate2 distributor0)", "-1"]], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - Crate depot0 depot1 depot2 depot3 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot3) (at crate1 distributor1) (at crate2 depot1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 distributor1) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (clear crate0) (clear crate1) (clear crate2) (clear pallet0) (clear pallet2) (clear pallet4) (on crate0 pallet3) (on crate1 pallet5) (on crate2 pallet1))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": -487003789600493461, "group": "action_justification_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 2 crates, 9 pallets, 7 depots, 9 hoists, numbered consecutively. Currently, pallet4, crate1, crate0, pallet2, pallet5, pallet8, pallet7, pallet0, and pallet6 are clear; hoist3, hoist5, hoist8, hoist2, hoist0, hoist7, hoist4, hoist6, and hoist1 are available; hoist4 is at depot4, hoist1 is at depot1, hoist5 is at depot5, pallet3 is at depot3, truck0 is at depot2, crate1 is at depot3, hoist7 is at distributor0, pallet0 is at depot0, pallet6 is at depot6, hoist6 is at depot6, truck1 is at depot0, hoist3 is at depot3, pallet1 is at depot1, pallet5 is at depot5, hoist8 is at distributor1, pallet7 is at distributor0, pallet8 is at distributor1, hoist2 is at depot2, pallet4 is at depot4, crate0 is at depot1, hoist0 is at depot0, and pallet2 is at depot2; crate0 is on pallet1 and crate1 is on pallet3. The available actions are: (drive ?x ?y ?z) - drive truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - use hoist ?x to lift crate ?y from surface ?z at place ?p, (drop ?x ?y ?z ?p) - drop the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - use the hoist ?x to lift and place the crate ?y from place ?p into the truck ?z, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p. The goal is to reach a state where the following facts hold: crate0 is on pallet8 and crate1 is on pallet3.", "question": "Simplify the plan \"(drive truck1 depot0 depot6) (drive truck1 depot6 depot0) (drive truck1 depot0 distributor1) (drive truck1 distributor1 depot6) (drive truck1 depot6 depot1) (lift hoist1 crate0 pallet1 depot1) (load hoist1 crate0 truck1 depot1) (drive truck1 depot1 distributor1) (unload hoist8 crate0 truck1 distributor1) (drop hoist8 crate0 pallet8 distributor1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(drive truck1 depot0 depot6)", "(drive truck1 depot6 depot0)", "-1"]], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-7-2-2-9-9-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 - Hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot1) (at crate1 depot3) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 distributor0) (at hoist8 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 distributor0) (at pallet8 distributor1) (at truck0 depot2) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (clear crate0) (clear crate1) (clear pallet0) (clear pallet2) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (on crate0 pallet1) (on crate1 pallet3))\n    (:goal (and (on crate0 pallet8) (on crate1 pallet3)))\n)"}
{"id": 4443395937553807182, "group": "action_justification_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 2 crates, 12 pallets, 10 depots, 12 hoists, numbered consecutively. Currently, pallet4, crate1, pallet1, crate0, pallet5, pallet10, pallet3, pallet7, pallet0, pallet9, pallet6, and pallet11 are clear; hoist3, hoist5, hoist8, hoist2, hoist0, hoist7, hoist10, hoist4, hoist11, hoist6, hoist9, and hoist1 are available; pallet9 is at depot9, crate0 is at depot8, hoist4 is at depot4, hoist1 is at depot1, hoist5 is at depot5, pallet3 is at depot3, pallet8 is at depot8, pallet0 is at depot0, pallet6 is at depot6, hoist6 is at depot6, hoist10 is at distributor0, crate1 is at depot2, hoist9 is at depot9, pallet7 is at depot7, hoist7 is at depot7, pallet10 is at distributor0, truck0 is at depot0, hoist3 is at depot3, pallet1 is at depot1, pallet5 is at depot5, truck1 is at depot4, hoist2 is at depot2, pallet4 is at depot4, hoist0 is at depot0, hoist8 is at depot8, pallet2 is at depot2, pallet11 is at distributor1, and hoist11 is at distributor1; crate1 is on pallet2 and crate0 is on pallet8. The available actions are: (drive ?x ?y ?z) - drive the truck ?x from ?y to ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the ground ?z at position ?p using the hoist ?x, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - load the crate ?y into the truck ?z at the place ?p with the hoist ?x, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Simplify the plan \"(drive truck1 depot4 distributor1) (drive truck1 distributor1 depot4) (drive truck0 depot0 depot2) (drive truck1 depot4 depot3) (drive truck1 depot3 depot8) (lift hoist8 crate0 pallet8 depot8) (load hoist8 crate0 truck1 depot8) (lift hoist2 crate1 pallet2 depot2) (drive truck1 depot8 distributor1) (unload hoist11 crate0 truck1 distributor1) (load hoist2 crate1 truck0 depot2) (drive truck0 depot2 depot9) (drop hoist11 crate0 pallet11 distributor1) (unload hoist9 crate1 truck0 depot9) (drop hoist9 crate1 pallet9 depot9)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(drive truck1 depot4 distributor1)", "(drive truck1 distributor1 depot4)", "-1"]], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-10-2-2-12-12-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 depot7 depot8 depot9 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist10 hoist11 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 hoist9 - Hoist pallet0 pallet1 pallet10 pallet11 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 pallet9 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot8) (at crate1 depot2) (at hoist0 depot0) (at hoist1 depot1) (at hoist10 distributor0) (at hoist11 distributor1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 depot7) (at hoist8 depot8) (at hoist9 depot9) (at pallet0 depot0) (at pallet1 depot1) (at pallet10 distributor0) (at pallet11 distributor1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 depot7) (at pallet8 depot8) (at pallet9 depot9) (at truck0 depot0) (at truck1 depot4) (available hoist0) (available hoist1) (available hoist10) (available hoist11) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (available hoist9) (clear crate0) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet10) (clear pallet11) (clear pallet3) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet9) (on crate0 pallet8) (on crate1 pallet2))\n    (:goal (and (on crate0 pallet11) (on crate1 pallet9)))\n)"}
{"id": 2194123697025956822, "group": "action_justification_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 2 crates, 12 pallets, 10 depots, 12 hoists, numbered consecutively. Currently, pallet4, crate1, pallet1, crate0, pallet5, pallet10, pallet3, pallet7, pallet0, pallet9, pallet6, and pallet11 are clear; hoist3, hoist5, hoist8, hoist2, hoist0, hoist7, hoist10, hoist4, hoist11, hoist6, hoist9, and hoist1 are available; pallet9 is at depot9, crate0 is at depot8, hoist4 is at depot4, hoist1 is at depot1, hoist5 is at depot5, pallet3 is at depot3, pallet8 is at depot8, pallet0 is at depot0, pallet6 is at depot6, hoist6 is at depot6, hoist10 is at distributor0, crate1 is at depot2, hoist9 is at depot9, pallet7 is at depot7, hoist7 is at depot7, pallet10 is at distributor0, truck0 is at depot0, hoist3 is at depot3, pallet1 is at depot1, pallet5 is at depot5, truck1 is at depot4, hoist2 is at depot2, pallet4 is at depot4, hoist0 is at depot0, hoist8 is at depot8, pallet2 is at depot2, pallet11 is at distributor1, and hoist11 is at distributor1; crate1 is on pallet2 and crate0 is on pallet8. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from location ?y to location ?z, (lift ?x ?y ?z ?p) - use hoist ?x to lift crate ?y from surface ?z at place ?p, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x and place it on the surface ?z at location ?p, (load ?x ?y ?z ?p) - load crate ?y into truck ?z at place ?p with hoist ?x, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Simplify the plan \"(drive truck1 depot4 distributor1) (drive truck1 distributor1 depot4) (drive truck0 depot0 depot4) (lift hoist2 crate1 pallet2 depot2) (lift hoist8 crate0 pallet8 depot8) (drive truck0 depot4 depot2) (load hoist2 crate1 truck0 depot2) (drive truck0 depot2 depot9) (unload hoist9 crate1 truck0 depot9) (drop hoist9 crate1 pallet9 depot9) (drive truck0 depot9 depot8) (load hoist8 crate0 truck0 depot8) (drive truck0 depot8 distributor1) (unload hoist11 crate0 truck0 distributor1) (drop hoist11 crate0 pallet11 distributor1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(drive truck1 depot4 distributor1)", "(drive truck1 distributor1 depot4)", "-1"]], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-10-2-2-12-12-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 depot7 depot8 depot9 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist10 hoist11 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 hoist9 - Hoist pallet0 pallet1 pallet10 pallet11 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 pallet9 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot8) (at crate1 depot2) (at hoist0 depot0) (at hoist1 depot1) (at hoist10 distributor0) (at hoist11 distributor1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 depot7) (at hoist8 depot8) (at hoist9 depot9) (at pallet0 depot0) (at pallet1 depot1) (at pallet10 distributor0) (at pallet11 distributor1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 depot7) (at pallet8 depot8) (at pallet9 depot9) (at truck0 depot0) (at truck1 depot4) (available hoist0) (available hoist1) (available hoist10) (available hoist11) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (available hoist9) (clear crate0) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet10) (clear pallet11) (clear pallet3) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet9) (on crate0 pallet8) (on crate1 pallet2))\n    (:goal (and (on crate0 pallet11) (on crate1 pallet9)))\n)"}
{"id": 5129461475863880114, "group": "action_justification_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 2 crates, 4 pallets, 2 depots, 4 hoists, numbered consecutively. Currently, crate1, crate0, pallet2, and pallet1 are clear; hoist3, hoist2, hoist0, and hoist1 are available; hoist3 is at distributor1, truck0 is at distributor0, hoist1 is at depot1, crate1 is at distributor1, pallet0 is at depot0, pallet2 is at distributor0, hoist2 is at distributor0, truck1 is at depot0, pallet3 is at distributor1, pallet1 is at depot1, hoist0 is at depot0, and crate0 is at depot0; crate1 is on pallet3 and crate0 is on pallet0. The available actions are: (drive ?x ?y ?z) - drive truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the surface ?z at place ?p using the hoist ?x, (drop ?x ?y ?z ?p) - drop the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - load crate ?y into truck ?z at place ?p with hoist ?x, and (unload ?x ?y ?z ?p) - unload the crate ?y from the truck ?z at the place ?p using the hoist ?x. The goal is to reach a state where the following facts hold: crate1 is on pallet0 and crate0 is on crate1.", "question": "Simplify the plan \"(lift hoist0 crate0 pallet0 depot0) (drive truck1 depot0 distributor1) (lift hoist3 crate1 pallet3 distributor1) (load hoist3 crate1 truck1 distributor1) (drive truck1 distributor1 depot1) (drive truck1 depot1 depot0) (load hoist0 crate0 truck1 depot0) (unload hoist0 crate1 truck1 depot0) (drop hoist0 crate1 pallet0 depot0) (unload hoist0 crate0 truck1 depot0) (drive truck1 depot0 distributor1) (drop hoist0 crate0 crate1 depot0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(drive truck1 depot0 distributor1)", "2"]], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-2-2-2-4-4-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist2 hoist3 - Hoist pallet0 pallet1 pallet2 pallet3 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot0) (at crate1 distributor1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 distributor0) (at hoist3 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 distributor0) (at pallet3 distributor1) (at truck0 distributor0) (at truck1 depot0) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (clear crate0) (clear crate1) (clear pallet1) (clear pallet2) (on crate0 pallet0) (on crate1 pallet3))\n    (:goal (and (on crate0 crate1) (on crate1 pallet0)))\n)"}
{"id": -1232510925228490981, "group": "action_justification_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 2 distributors, 2 crates, 12 pallets, 10 depots, 12 hoists, numbered consecutively. Currently, pallet4, crate1, pallet1, crate0, pallet5, pallet10, pallet3, pallet7, pallet0, pallet9, pallet6, and pallet11 are clear; hoist3, hoist5, hoist8, hoist2, hoist0, hoist7, hoist10, hoist4, hoist11, hoist6, hoist9, and hoist1 are available; pallet9 is at depot9, crate0 is at depot8, hoist4 is at depot4, hoist1 is at depot1, hoist5 is at depot5, pallet3 is at depot3, pallet8 is at depot8, pallet0 is at depot0, pallet6 is at depot6, hoist6 is at depot6, hoist10 is at distributor0, crate1 is at depot2, hoist9 is at depot9, pallet7 is at depot7, hoist7 is at depot7, pallet10 is at distributor0, truck0 is at depot0, hoist3 is at depot3, pallet1 is at depot1, pallet5 is at depot5, truck1 is at depot4, hoist2 is at depot2, pallet4 is at depot4, hoist0 is at depot0, hoist8 is at depot8, pallet2 is at depot2, pallet11 is at distributor1, and hoist11 is at distributor1; crate1 is on pallet2 and crate0 is on pallet8. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the surface ?z at place ?p using the hoist ?x, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x onto the surface ?z at the place ?p, (load ?x ?y ?z ?p) - load the crate ?y from place ?p with hoist ?x into the truck ?z, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Simplify the plan \"(drive truck1 depot4 depot8) (lift hoist8 crate0 pallet8 depot8) (load hoist8 crate0 truck1 depot8) (drive truck1 depot8 distributor1) (unload hoist11 crate0 truck1 distributor1) (drop hoist11 crate0 pallet11 distributor1) (drive truck1 distributor1 depot2) (lift hoist2 crate1 pallet2 depot2) (load hoist2 crate1 truck1 depot2) (drive truck1 depot2 depot9) (unload hoist9 crate1 truck1 depot9) (drop hoist9 crate1 pallet9 depot9) (drive truck0 depot0 depot9)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(drive truck0 depot0 depot9)", "1"]], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-10-2-2-12-12-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - Crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 depot7 depot8 depot9 - Depot distributor0 distributor1 - Distributor hoist0 hoist1 hoist10 hoist11 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 hoist9 - Hoist pallet0 pallet1 pallet10 pallet11 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 pallet9 - Pallet truck0 truck1 - Truck)\n    (:init (at crate0 depot8) (at crate1 depot2) (at hoist0 depot0) (at hoist1 depot1) (at hoist10 distributor0) (at hoist11 distributor1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 depot7) (at hoist8 depot8) (at hoist9 depot9) (at pallet0 depot0) (at pallet1 depot1) (at pallet10 distributor0) (at pallet11 distributor1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 depot7) (at pallet8 depot8) (at pallet9 depot9) (at truck0 depot0) (at truck1 depot4) (available hoist0) (available hoist1) (available hoist10) (available hoist11) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (available hoist9) (clear crate0) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet10) (clear pallet11) (clear pallet3) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet9) (on crate0 pallet8) (on crate1 pallet2))\n    (:goal (and (on crate0 pallet11) (on crate1 pallet9)))\n)"}
