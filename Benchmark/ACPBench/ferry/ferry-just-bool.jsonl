{"id": -8065996215240873535, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c26, c2, c45, c10, c28, c47, c36, c14, c0, c38, c20, and c35 are at l4; c3, c31, c43, c7, c5, c49, c29, c23, c22, c15, c39, c4, c37, and c42 are at l3; c46, c19, c34, c44, c33, c21, c24, c48, c32, and c13 are at l1; c30, c16, c12, and c8 are at l2; c27, c9, c17, c41, c1, c6, c25, c18, c11, and c40 are at l0. The goal is to reach a state where the following facts hold: Car c0 is at location l2, Car c12 is at location l3, Car c49 is at location l1, Car c35 is at location l2, Car c26 is at location l2, Car c6 is at location l3, Car c19 is at location l1, Car c10 is at location l0, Car c24 is at location l0, Car c31 is at location l3, Car c20 is at location l3, Car c21 is at location l2, Car c30 is at location l4, Car c4 is at location l0, Car c41 is at location l4, Car c48 is at location l0, Car c11 is at location l1, Car c9 is at location l4, Car c46 is at location l4, Car c5 is at location l4, Car c17 is at location l0, Car c34 is at location l3, Car c36 is at location l0, Car c33 is at location l1, Car c47 is at location l0, Car c44 is at location l0, Car c28 is at location l4, Car c8 is at location l4, Car c3 is at location l2, Car c23 is at location l3, Car c39 is at location l2, Car c32 is at location l1, Car c22 is at location l2, Car c14 is at location l4, Car c1 is at location l0, Car c15 is at location l1, Car c27 is at location l4, Car c38 is at location l4, Car c16 is at location l0, Car c40 is at location l2, Car c25 is at location l3, Car c18 is at location l0, Car c37 is at location l3, Car c2 is at location l1, Car c43 is at location l2, Car c13 is at location l3, Car c29 is at location l4, Car c45 is at location l0, Car c7 is at location l0, and Car c42 is at location l0.", "question": "Given the plan: \"board the car c13 at location l1 on to the ferry, sail from location l1 to location l3, debark car c13 to location l3 from the ferry, board the car c29 at location l3 on to the ferry, sail from location l3 to location l4, debark car c29 to location l4 from the ferry, board the car c26 at location l4 on to the ferry, sail from location l4 to location l2, debark car c26 to location l2 from the ferry, board the car c16 at location l2 on to the ferry, sail from location l2 to location l0, debark car c16 to location l0 from the ferry, board the car c40 at location l0 on to the ferry, sail from location l0 to location l2, debark car c40 to location l2 from the ferry, board the car c12 at location l2 on to the ferry, sail from location l2 to location l3, debark car c12 to location l3 from the ferry, board the car c15 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l1, debark car c15 to location l1 from the ferry, board the car c21 at location l1 on to the ferry, sail from location l1 to location l2, debark car c21 to location l2 from the ferry, board the car c30 at location l2 on to the ferry, sail from location l2 to location l3, sail from location l3 to location l4, debark car c30 to location l4 from the ferry, board the car c10 at location l4 on to the ferry, sail from location l4 to location l2, sail from location l2 to location l0, debark car c10 to location l0 from the ferry, board the car c11 at location l0 on to the ferry, sail from location l0 to location l1, debark car c11 to location l1 from the ferry, board the car c24 at location l1 on to the ferry, sail from location l1 to location l0, debark car c24 to location l0 from the ferry, board the car c25 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l3, debark car c25 to location l3 from the ferry, board the car c22 at location l3 on to the ferry, sail from location l3 to location l2, debark car c22 to location l2 from the ferry, board the car c8 at location l2 on to the ferry, sail from location l2 to location l3, sail from location l3 to location l4, debark car c8 to location l4 from the ferry, board the car c2 at location l4 on to the ferry, sail from location l4 to location l2, sail from location l2 to location l1, debark car c2 to location l1 from the ferry, board the car c34 at location l1 on to the ferry, sail from location l1 to location l0, sail from location l0 to location l3, debark car c34 to location l3 from the ferry, board the car c3 at location l3 on to the ferry, sail from location l3 to location l2, debark car c3 to location l2 from the ferry, sail from location l2 to location l3, board the car c39 at location l3 on to the ferry, sail from location l3 to location l2, debark car c39 to location l2 from the ferry, sail from location l2 to location l3, board the car c4 at location l3 on to the ferry, sail from location l3 to location l0, debark car c4 to location l0 from the ferry, board the car c27 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l4, debark car c27 to location l4 from the ferry, board the car c20 at location l4 on to the ferry, sail from location l4 to location l2, sail from location l2 to location l3, debark car c20 to location l3 from the ferry, board the car c42 at location l3 on to the ferry, sail from location l3 to location l0, debark car c42 to location l0 from the ferry, board the car c41 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l4, debark car c41 to location l4 from the ferry, board the car c36 at location l4 on to the ferry, sail from location l4 to location l2, sail from location l2 to location l0, debark car c36 to location l0 from the ferry, board the car c6 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l3, debark car c6 to location l3 from the ferry, board the car c43 at location l3 on to the ferry, sail from location l3 to location l2, debark car c43 to location l2 from the ferry, sail from location l2 to location l3, board the car c49 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l1, debark car c49 to location l1 from the ferry, board the car c44 at location l1 on to the ferry, sail from location l1 to location l0, debark car c44 to location l0 from the ferry, board the car c9 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l4, debark car c9 to location l4 from the ferry, sail from location l4 to location l1, board the car c46 at location l1 on to the ferry, sail from location l1 to location l4, debark car c46 to location l4 from the ferry, board the car c35 at location l4 on to the ferry, sail from location l4 to location l2, debark car c35 to location l2 from the ferry, sail from location l2 to location l1, board the car c48 at location l1 on to the ferry, sail from location l1 to location l0, debark car c48 to location l0 from the ferry, sail from location l0 to location l3, board the car c5 at location l3 on to the ferry, sail from location l3 to location l4, debark car c5 to location l4 from the ferry, board the car c45 at location l4 on to the ferry, sail from location l4 to location l0, debark car c45 to location l0 from the ferry, sail from location l0 to location l3, board the car c7 at location l3 on to the ferry, sail from location l3 to location l0, debark car c7 to location l0 from the ferry, sail from location l0 to location l4, board the car c0 at location l4 on to the ferry, sail from location l4 to location l2, debark car c0 to location l2 from the ferry, sail from location l2 to location l0, sail from location l0 to location l4, board the car c47 at location l4 on to the ferry, sail from location l4 to location l0, debark car c47 to location l0 from the ferry\"; can the following action be removed from this plan and still have a valid plan: sail from location l1 to location l3?", "answer": "no"}
{"id": -4047479053016228401, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c0 and c1 are at l0; c2 is at l1. The goal is to reach a state where the following facts hold: Car c1 is at location l0, Car c0 is at location l1, and Car c2 is at location l1.", "question": "Given the plan: \"sail from location l1 to location l0, sail from location l0 to location l1, sail from location l1 to location l0, board the car c0 at the location l0, debark the car c0 to location l0 from the ferry, board the car c0 at the location l0, sail from location l0 to location l1, debark the car c0 to location l1 from the ferry, sail from location l1 to location l0\"; can the following action be removed from this plan and still have a valid plan: sail from location l1 to location l0?", "answer": "yes"}
{"id": 9064347322980490596, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c3 is at l2; c9, c2, and c6 are at l0; c1, c7, c8, c5, c4, and c0 are at l1. The goal is to reach a state where the following facts hold: Car c4 is at location l0, Car c0 is at location l2, Car c3 is at location l2, Car c5 is at location l2, Car c9 is at location l0, Car c1 is at location l1, Car c6 is at location l2, Car c2 is at location l1, Car c8 is at location l2, and Car c7 is at location l0.", "question": "Given the plan: \"travel by sea from location l2 to location l0, travel by sea from location l0 to location l1, board the car c7 at the location l1, travel by sea from location l1 to location l0, debark the car c7 to location l0 from the ferry, board the car c2 at the location l0, travel by sea from location l0 to location l1, debark the car c2 to location l1 from the ferry, board the car c8 at the location l1, travel by sea from location l1 to location l2, debark the car c8 to location l2 from the ferry, travel by sea from location l2 to location l1, board the car c5 at the location l1, travel by sea from location l1 to location l2, debark the car c5 to location l2 from the ferry, travel by sea from location l2 to location l1, board the car c4 at the location l1, travel by sea from location l1 to location l0, debark the car c4 to location l0 from the ferry, board the car c6 at the location l0, travel by sea from location l0 to location l2, debark the car c6 to location l2 from the ferry, travel by sea from location l2 to location l1, board the car c0 at the location l1, travel by sea from location l1 to location l2, debark the car c0 to location l2 from the ferry\"; can the following action be removed from this plan and still have a valid plan: travel by sea from location l1 to location l0?", "answer": "no"}
{"id": -2948642499284021664, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c3 is at l2; c9, c2, and c6 are at l0; c1, c7, c8, c5, c4, and c0 are at l1. The goal is to reach a state where the following facts hold: Car c4 is at location l0, Car c0 is at location l2, Car c3 is at location l2, Car c5 is at location l2, Car c9 is at location l0, Car c1 is at location l1, Car c6 is at location l2, Car c2 is at location l1, Car c8 is at location l2, and Car c7 is at location l0.", "question": "Given the plan: \"sail from location l2 to location l1, board car c0 at location l1, sail from location l1 to location l2, debark the car c0 to location l2 from the ferry, sail from location l2 to location l1, board car c5 at location l1, debark the car c5 to location l1 from the ferry, board car c8 at location l1, sail from location l1 to location l2, debark the car c8 to location l2 from the ferry, sail from location l2 to location l1, board car c4 at location l1, sail from location l1 to location l0, debark the car c4 to location l0 from the ferry, board car c2 at location l0, sail from location l0 to location l1, debark the car c2 to location l1 from the ferry, board car c7 at location l1, sail from location l1 to location l0, debark the car c7 to location l0 from the ferry, board car c6 at location l0, sail from location l0 to location l2, debark the car c6 to location l2 from the ferry, sail from location l2 to location l1, board car c5 at location l1, sail from location l1 to location l2, debark the car c5 to location l2 from the ferry\"; can the following action be removed from this plan and still have a valid plan: debark the car c6 to location l2 from the ferry?", "answer": "no"}
{"id": 8307555940186464125, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0 and c2 are at l4; c1 is at l0. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c0 is at location l3, and Car c1 is at location l3.", "question": "Given the plan: \"embark the car c1 at location l0 on to the ferry, travel by sea from location l0 to location l3, debark the car c1 from the ferry to location l3, travel by sea from location l3 to location l0, travel by sea from location l0 to location l4, embark the car c0 at location l4 on to the ferry, travel by sea from location l4 to location l3, debark the car c0 from the ferry to location l3, travel by sea from location l3 to location l4, embark the car c2 at location l4 on to the ferry, travel by sea from location l4 to location l3, debark the car c2 from the ferry to location l3, travel by sea from location l3 to location l2\"; can the following action be removed from this plan and still have a valid plan: debark the car c1 from the ferry to location l3?", "answer": "no"}
{"id": 5370407710788629186, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c26, c2, c45, c10, c28, c47, c36, c14, c0, c38, c20, and c35 are at l4; c3, c31, c43, c7, c5, c49, c29, c23, c22, c15, c39, c4, c37, and c42 are at l3; c46, c19, c34, c44, c33, c21, c24, c48, c32, and c13 are at l1; c30, c16, c12, and c8 are at l2; c27, c9, c17, c41, c1, c6, c25, c18, c11, and c40 are at l0. The goal is to reach a state where the following facts hold: Car c0 is at location l2, Car c12 is at location l3, Car c49 is at location l1, Car c35 is at location l2, Car c26 is at location l2, Car c6 is at location l3, Car c19 is at location l1, Car c10 is at location l0, Car c24 is at location l0, Car c31 is at location l3, Car c20 is at location l3, Car c21 is at location l2, Car c30 is at location l4, Car c4 is at location l0, Car c41 is at location l4, Car c48 is at location l0, Car c11 is at location l1, Car c9 is at location l4, Car c46 is at location l4, Car c5 is at location l4, Car c17 is at location l0, Car c34 is at location l3, Car c36 is at location l0, Car c33 is at location l1, Car c47 is at location l0, Car c44 is at location l0, Car c28 is at location l4, Car c8 is at location l4, Car c3 is at location l2, Car c23 is at location l3, Car c39 is at location l2, Car c32 is at location l1, Car c22 is at location l2, Car c14 is at location l4, Car c1 is at location l0, Car c15 is at location l1, Car c27 is at location l4, Car c38 is at location l4, Car c16 is at location l0, Car c40 is at location l2, Car c25 is at location l3, Car c18 is at location l0, Car c37 is at location l3, Car c2 is at location l1, Car c43 is at location l2, Car c13 is at location l3, Car c29 is at location l4, Car c45 is at location l0, Car c7 is at location l0, and Car c42 is at location l0.", "question": "Given the plan: \"board the car c48 at location l1, debark car c48 to location l1 from the ferry, board the car c13 at location l1, travel by sea from location l1 to location l3, debark car c13 to location l3 from the ferry, board the car c22 at location l3, travel by sea from location l3 to location l2, debark car c22 to location l2 from the ferry, board the car c16 at location l2, travel by sea from location l2 to location l0, debark car c16 to location l0 from the ferry, board the car c27 at location l0, travel by sea from location l0 to location l4, debark car c27 to location l4 from the ferry, board the car c10 at location l4, travel by sea from location l4 to location l0, debark car c10 to location l0 from the ferry, board the car c40 at location l0, travel by sea from location l0 to location l2, debark car c40 to location l2 from the ferry, travel by sea from location l2 to location l4, board the car c2 at location l4, travel by sea from location l4 to location l1, debark car c2 to location l1 from the ferry, board the car c21 at location l1, travel by sea from location l1 to location l2, debark car c21 to location l2 from the ferry, travel by sea from location l2 to location l4, board the car c20 at location l4, travel by sea from location l4 to location l3, debark car c20 to location l3 from the ferry, board the car c15 at location l3, travel by sea from location l3 to location l0, travel by sea from location l0 to location l1, debark car c15 to location l1 from the ferry, board the car c24 at location l1, travel by sea from location l1 to location l0, debark car c24 to location l0 from the ferry, board the car c41 at location l0, travel by sea from location l0 to location l4, debark car c41 to location l4 from the ferry, board the car c36 at location l4, travel by sea from location l4 to location l0, debark car c36 to location l0 from the ferry, board the car c9 at location l0, travel by sea from location l0 to location l4, debark car c9 to location l4 from the ferry, board the car c45 at location l4, travel by sea from location l4 to location l0, debark car c45 to location l0 from the ferry, board the car c11 at location l0, travel by sea from location l0 to location l1, debark car c11 to location l1 from the ferry, board the car c34 at location l1, travel by sea from location l1 to location l0, travel by sea from location l0 to location l3, debark car c34 to location l3 from the ferry, board the car c29 at location l3, travel by sea from location l3 to location l0, travel by sea from location l0 to location l4, debark car c29 to location l4 from the ferry, board the car c47 at location l4, travel by sea from location l4 to location l0, debark car c47 to location l0 from the ferry, board the car c25 at location l0, travel by sea from location l0 to location l1, travel by sea from location l1 to location l3, debark car c25 to location l3 from the ferry, board the car c39 at location l3, travel by sea from location l3 to location l1, travel by sea from location l1 to location l2, debark car c39 to location l2 from the ferry, board the car c12 at location l2, travel by sea from location l2 to location l3, debark car c12 to location l3 from the ferry, board the car c43 at location l3, travel by sea from location l3 to location l1, travel by sea from location l1 to location l2, debark car c43 to location l2 from the ferry, board the car c30 at location l2, travel by sea from location l2 to location l3, travel by sea from location l3 to location l4, debark car c30 to location l4 from the ferry, board the car c26 at location l4, travel by sea from location l4 to location l2, debark car c26 to location l2 from the ferry, board the car c8 at location l2, travel by sea from location l2 to location l3, travel by sea from location l3 to location l4, debark car c8 to location l4 from the ferry, board the car c35 at location l4, travel by sea from location l4 to location l2, debark car c35 to location l2 from the ferry, travel by sea from location l2 to location l3, board the car c4 at location l3, travel by sea from location l3 to location l0, debark car c4 to location l0 from the ferry, board the car c6 at location l0, travel by sea from location l0 to location l1, travel by sea from location l1 to location l3, debark car c6 to location l3 from the ferry, board the car c42 at location l3, travel by sea from location l3 to location l0, debark car c42 to location l0 from the ferry, travel by sea from location l0 to location l1, board the car c44 at location l1, travel by sea from location l1 to location l0, debark car c44 to location l0 from the ferry, travel by sea from location l0 to location l1, board the car c46 at location l1, travel by sea from location l1 to location l2, travel by sea from location l2 to location l4, debark car c46 to location l4 from the ferry, travel by sea from location l4 to location l3, board the car c3 at location l3, travel by sea from location l3 to location l1, debark car c3 to location l1 from the ferry, board the car c48 at location l1, travel by sea from location l1 to location l0, debark car c48 to location l0 from the ferry, travel by sea from location l0 to location l3, board the car c49 at location l3, travel by sea from location l3 to location l1, debark car c49 to location l1 from the ferry, travel by sea from location l1 to location l3, board the car c5 at location l3, travel by sea from location l3 to location l4, debark car c5 to location l4 from the ferry, board the car c0 at location l4, travel by sea from location l4 to location l0, travel by sea from location l0 to location l2, debark car c0 to location l2 from the ferry, travel by sea from location l2 to location l3, board the car c7 at location l3, travel by sea from location l3 to location l0, debark car c7 to location l0 from the ferry, travel by sea from location l0 to location l1, board the car c3 at location l1, travel by sea from location l1 to location l2, debark car c3 to location l2 from the ferry\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: board the car c48 at location l1 and debark car c48 to location l1 from the ferry?", "answer": "yes"}
{"id": -2030480802923632111, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c0, c15, c20, c23, c10, c8, c30, c9, c17, c35, c14, c44, c3, c1, c6, c29, c16, c2, c18, c33, and c11 are at l0; c46, c25, c40, c49, c47, c19, c34, c36, c12, c43, c7, c39, c5, c42, c21, c31, c38, c28, c26, c48, c24, c37, c4, c22, c32, c41, c27, c45, and c13 are at l1. The goal is to reach a state where the following facts hold: Car c0 is at location l0, Car c18 is at location l1, Car c49 is at location l1, Car c20 is at location l0, Car c35 is at location l1, Car c47 is at location l1, Car c8 is at location l0, Car c17 is at location l1, Car c34 is at location l1, Car c10 is at location l1, Car c3 is at location l1, Car c30 is at location l0, Car c21 is at location l0, Car c9 is at location l0, Car c38 is at location l0, Car c7 is at location l1, Car c39 is at location l1, Car c36 is at location l0, Car c5 is at location l1, Car c33 is at location l1, Car c42 is at location l1, Car c22 is at location l0, Car c31 is at location l1, Car c24 is at location l1, Car c14 is at location l0, Car c28 is at location l1, Car c19 is at location l0, Car c44 is at location l0, Car c48 is at location l1, Car c1 is at location l1, Car c43 is at location l0, Car c4 is at location l1, Car c32 is at location l1, Car c6 is at location l1, Car c29 is at location l0, Car c41 is at location l1, Car c15 is at location l1, Car c25 is at location l0, Car c27 is at location l1, Car c23 is at location l1, Car c2 is at location l0, Car c13 is at location l0, Car c46 is at location l0, Car c12 is at location l0, Car c37 is at location l0, Car c45 is at location l0, Car c11 is at location l0, Car c40 is at location l0, Car c16 is at location l1, and Car c26 is at location l0.", "question": "Given the plan: \"board the car c36 at location l1, sail from location l1 to location l0, debark car c36 to location l0 from the ferry, board the car c1 at location l0, sail from location l0 to location l1, debark car c1 to location l1 from the ferry, board the car c12 at location l1, sail from location l1 to location l0, debark car c12 to location l0 from the ferry, board the car c10 at location l0, sail from location l0 to location l1, debark car c10 to location l1 from the ferry, board the car c13 at location l1, sail from location l1 to location l0, debark car c13 to location l0 from the ferry, board the car c15 at location l0, sail from location l0 to location l1, debark car c15 to location l1 from the ferry, board the car c19 at location l1, sail from location l1 to location l0, debark car c19 to location l0 from the ferry, board the car c16 at location l0, sail from location l0 to location l1, debark car c16 to location l1 from the ferry, board the car c21 at location l1, sail from location l1 to location l0, debark car c21 to location l0 from the ferry, board the car c17 at location l0, sail from location l0 to location l1, debark car c17 to location l1 from the ferry, board the car c22 at location l1, sail from location l1 to location l0, debark car c22 to location l0 from the ferry, board the car c18 at location l0, sail from location l0 to location l1, debark car c18 to location l1 from the ferry, board the car c25 at location l1, sail from location l1 to location l0, debark car c25 to location l0 from the ferry, board the car c23 at location l0, sail from location l0 to location l1, debark car c23 to location l1 from the ferry, board the car c26 at location l1, sail from location l1 to location l0, debark car c26 to location l0 from the ferry, board the car c3 at location l0, sail from location l0 to location l1, debark car c3 to location l1 from the ferry, board the car c37 at location l1, sail from location l1 to location l0, debark car c37 to location l0 from the ferry, board the car c33 at location l0, sail from location l0 to location l1, debark car c33 to location l1 from the ferry, board the car c38 at location l1, sail from location l1 to location l0, debark car c38 to location l0 from the ferry, board the car c35 at location l0, sail from location l0 to location l1, debark car c35 to location l1 from the ferry, board the car c40 at location l1, sail from location l1 to location l0, debark car c40 to location l0 from the ferry, board the car c6 at location l0, sail from location l0 to location l1, debark car c6 to location l1 from the ferry, board the car c43 at location l1, sail from location l1 to location l0, debark car c43 to location l0 from the ferry, sail from location l0 to location l1, board the car c45 at location l1, sail from location l1 to location l0, debark car c45 to location l0 from the ferry, sail from location l0 to location l1, board the car c46 at location l1, sail from location l1 to location l0, debark car c46 to location l0 from the ferry, board the car c36 at location l0, debark car c36 to location l0 from the ferry\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: board the car c36 at location l0 and debark car c36 to location l0 from the ferry?", "answer": "yes"}
{"id": 6384988473335147074, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0 and c2 are at l4; c1 is at l0. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c0 is at location l3, and Car c1 is at location l3.", "question": "Given the plan: \"embark the car c1 at location l0 on to the ferry, sail from location l0 to location l3, debark the car c1 from the ferry to location l3, sail from location l3 to location l2, sail from location l2 to location l4, embark the car c0 at location l4 on to the ferry, sail from location l4 to location l3, debark the car c0 from the ferry to location l3, sail from location l3 to location l2, sail from location l2 to location l4, embark the car c2 at location l4 on to the ferry, sail from location l4 to location l3, debark the car c2 from the ferry to location l3, sail from location l3 to location l4\"; can the following action be removed from this plan and still have a valid plan: sail from location l0 to location l3?", "answer": "no"}
{"id": -5337622938030096811, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c26, c2, c45, c10, c28, c47, c36, c14, c0, c38, c20, and c35 are at l4; c3, c31, c43, c7, c5, c49, c29, c23, c22, c15, c39, c4, c37, and c42 are at l3; c46, c19, c34, c44, c33, c21, c24, c48, c32, and c13 are at l1; c30, c16, c12, and c8 are at l2; c27, c9, c17, c41, c1, c6, c25, c18, c11, and c40 are at l0. The goal is to reach a state where the following facts hold: Car c0 is at location l2, Car c12 is at location l3, Car c49 is at location l1, Car c35 is at location l2, Car c26 is at location l2, Car c6 is at location l3, Car c19 is at location l1, Car c10 is at location l0, Car c24 is at location l0, Car c31 is at location l3, Car c20 is at location l3, Car c21 is at location l2, Car c30 is at location l4, Car c4 is at location l0, Car c41 is at location l4, Car c48 is at location l0, Car c11 is at location l1, Car c9 is at location l4, Car c46 is at location l4, Car c5 is at location l4, Car c17 is at location l0, Car c34 is at location l3, Car c36 is at location l0, Car c33 is at location l1, Car c47 is at location l0, Car c44 is at location l0, Car c28 is at location l4, Car c8 is at location l4, Car c3 is at location l2, Car c23 is at location l3, Car c39 is at location l2, Car c32 is at location l1, Car c22 is at location l2, Car c14 is at location l4, Car c1 is at location l0, Car c15 is at location l1, Car c27 is at location l4, Car c38 is at location l4, Car c16 is at location l0, Car c40 is at location l2, Car c25 is at location l3, Car c18 is at location l0, Car c37 is at location l3, Car c2 is at location l1, Car c43 is at location l2, Car c13 is at location l3, Car c29 is at location l4, Car c45 is at location l0, Car c7 is at location l0, and Car c42 is at location l0.", "question": "Given the plan: \"load the car c13 at location l1 on to the ferry, sail from location l1 to location l3, unload the car c13 from the ferry to location l3, load the car c22 at location l3 on to the ferry, sail from location l3 to location l2, unload the car c22 from the ferry to location l2, load the car c16 at location l2 on to the ferry, sail from location l2 to location l0, unload the car c16 from the ferry to location l0, load the car c27 at location l0 on to the ferry, sail from location l0 to location l4, unload the car c27 from the ferry to location l4, load the car c10 at location l4 on to the ferry, sail from location l4 to location l0, unload the car c10 from the ferry to location l0, load the car c40 at location l0 on to the ferry, sail from location l0 to location l2, unload the car c40 from the ferry to location l2, load the car c12 at location l2 on to the ferry, sail from location l2 to location l3, unload the car c12 from the ferry to location l3, sail from location l3 to location l0, load the car c41 at location l0 on to the ferry, sail from location l0 to location l4, unload the car c41 from the ferry to location l4, load the car c2 at location l4 on to the ferry, sail from location l4 to location l1, unload the car c2 from the ferry to location l1, load the car c21 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c21 from the ferry to location l2, load the car c30 at location l2 on to the ferry, sail from location l2 to location l3, sail from location l3 to location l4, unload the car c30 from the ferry to location l4, load the car c20 at location l4 on to the ferry, sail from location l4 to location l3, unload the car c20 from the ferry to location l3, sail from location l3 to location l0, load the car c9 at location l0 on to the ferry, sail from location l0 to location l4, unload the car c9 from the ferry to location l4, load the car c36 at location l4 on to the ferry, sail from location l4 to location l0, unload the car c36 from the ferry to location l0, load the car c11 at location l0 on to the ferry, sail from location l0 to location l1, unload the car c11 from the ferry to location l1, load the car c24 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c24 from the ferry to location l2, load the car c8 at location l2 on to the ferry, sail from location l2 to location l4, unload the car c8 from the ferry to location l4, load the car c26 at location l4 on to the ferry, sail from location l4 to location l2, unload the car c26 from the ferry to location l2, load the car c24 at location l2 on to the ferry, sail from location l2 to location l0, unload the car c24 from the ferry to location l0, load the car c25 at location l0 on to the ferry, sail from location l0 to location l3, unload the car c25 from the ferry to location l3, load the car c15 at location l3 on to the ferry, sail from location l3 to location l1, unload the car c15 from the ferry to location l1, load the car c34 at location l1 on to the ferry, sail from location l1 to location l3, unload the car c34 from the ferry to location l3, load the car c29 at location l3 on to the ferry, sail from location l3 to location l4, unload the car c29 from the ferry to location l4, load the car c35 at location l4 on to the ferry, sail from location l4 to location l2, unload the car c35 from the ferry to location l2, sail from location l2 to location l0, load the car c6 at location l0 on to the ferry, sail from location l0 to location l3, unload the car c6 from the ferry to location l3, load the car c3 at location l3 on to the ferry, sail from location l3 to location l2, unload the car c3 from the ferry to location l2, sail from location l2 to location l1, load the car c44 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c44 from the ferry to location l0, sail from location l0 to location l1, load the car c46 at location l1 on to the ferry, sail from location l1 to location l4, unload the car c46 from the ferry to location l4, load the car c45 at location l4 on to the ferry, sail from location l4 to location l0, unload the car c45 from the ferry to location l0, sail from location l0 to location l1, load the car c48 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c48 from the ferry to location l0, sail from location l0 to location l3, load the car c39 at location l3 on to the ferry, sail from location l3 to location l2, unload the car c39 from the ferry to location l2, sail from location l2 to location l3, load the car c4 at location l3 on to the ferry, sail from location l3 to location l0, unload the car c4 from the ferry to location l0, sail from location l0 to location l3, load the car c42 at location l3 on to the ferry, sail from location l3 to location l0, unload the car c42 from the ferry to location l0, sail from location l0 to location l3, load the car c43 at location l3 on to the ferry, sail from location l3 to location l2, unload the car c43 from the ferry to location l2, sail from location l2 to location l3, load the car c49 at location l3 on to the ferry, sail from location l3 to location l1, unload the car c49 from the ferry to location l1, sail from location l1 to location l0, sail from location l0 to location l3, load the car c5 at location l3 on to the ferry, sail from location l3 to location l4, unload the car c5 from the ferry to location l4, load the car c47 at location l4 on to the ferry, sail from location l4 to location l0, unload the car c47 from the ferry to location l0, sail from location l0 to location l3, load the car c7 at location l3 on to the ferry, sail from location l3 to location l0, unload the car c7 from the ferry to location l0, sail from location l0 to location l2, sail from location l2 to location l4, load the car c0 at location l4 on to the ferry, sail from location l4 to location l2, unload the car c0 from the ferry to location l2\"; can the following action be removed from this plan and still have a valid plan: sail from location l0 to location l2?", "answer": "no"}
{"id": -3378745792146229810, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0 and c2 are at l4; c1 is at l0. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c0 is at location l3, and Car c1 is at location l3.", "question": "Given the plan: \"embark the car c1 at location l0 on to the ferry, travel by sea from location l0 to location l2, travel by sea from location l2 to location l0, travel by sea from location l0 to location l3, debark car c1 to location l3 from the ferry, travel by sea from location l3 to location l4, embark the car c2 at location l4 on to the ferry, travel by sea from location l4 to location l3, debark car c2 to location l3 from the ferry, travel by sea from location l3 to location l4, embark the car c0 at location l4 on to the ferry, travel by sea from location l4 to location l3, debark car c0 to location l3 from the ferry, travel by sea from location l3 to location l1\"; can the following action be removed from this plan and still have a valid plan: travel by sea from location l3 to location l1?", "answer": "yes"}
{"id": -3115201149135125328, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 2 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c1 and c0 are at l1. The goal is to reach a state where the following facts hold: Car c0 is at location l0 and Car c1 is at location l2.", "question": "Given the plan: \"load the car c1 at location l1 on to the ferry, unload the car c1 from the ferry to location l1, load the car c1 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c1 from the ferry to location l2, load the car c1 at location l2 on to the ferry, unload the car c1 from the ferry to location l2, sail from location l2 to location l1, load the car c0 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c0 from the ferry to location l0\"; can the following action be removed from this plan and still have a valid plan: load the car c1 at location l1 on to the ferry?", "answer": "no"}
{"id": 461367997804057344, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 2 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0 and c1 are at l0. The goal is to reach a state where the following facts hold: Car c1 is at location l1 and Car c0 is at location l1.", "question": "Given the plan: \"board the car c1 at location l0 on to the ferry, debark car c1 to location l0 from the ferry, board the car c1 at location l0 on to the ferry, sail from location l0 to location l1, debark car c1 to location l1 from the ferry, board the car c1 at location l1 on to the ferry, debark car c1 to location l1 from the ferry, sail from location l1 to location l0, board the car c0 at location l0 on to the ferry, sail from location l0 to location l1, debark car c0 to location l1 from the ferry, board the car c0 at location l1 on to the ferry, debark car c0 to location l1 from the ferry\"; can the following action be removed from this plan and still have a valid plan: board the car c1 at location l0 on to the ferry?", "answer": "no"}
{"id": 6046980184545774381, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 2 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0 and c1 are at l0. The goal is to reach a state where the following facts hold: Car c1 is at location l1 and Car c0 is at location l1.", "question": "Given the plan: \"embark the car c0 at location l0 on to the ferry, debark the car c0 to location l0 from the ferry, embark the car c0 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l0, sail from location l0 to location l1, debark the car c0 to location l1 from the ferry, sail from location l1 to location l0, embark the car c1 at location l0 on to the ferry, sail from location l0 to location l1, debark the car c1 to location l1 from the ferry, embark the car c0 at location l1 on to the ferry, sail from location l1 to location l0, sail from location l0 to location l1, debark the car c0 to location l1 from the ferry\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: debark the car c0 to location l0 from the ferry and embark the car c0 at location l0 on to the ferry?", "answer": "yes"}
{"id": 84104770484963469, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 2 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c1 and c0 are at l1. The goal is to reach a state where the following facts hold: Car c0 is at location l0 and Car c1 is at location l2.", "question": "Given the plan: \"embark the car c0 at location l1 on to the ferry, debark the car c0 from the ferry to location l1, embark the car c0 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c0 from the ferry to location l0, sail from location l0 to location l1, embark the car c1 at location l1 on to the ferry, sail from location l1 to location l2, debark the car c1 from the ferry to location l2\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: embark the car c0 at location l1 on to the ferry and debark the car c0 from the ferry to location l1?", "answer": "yes"}
{"id": -6991290755221663171, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 10 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0, c9, c2, c8, c3, c1, and c6 are at l0; c7, c5, and c4 are at l1. The goal is to reach a state where the following facts hold: Car c0 is at location l0, Car c4 is at location l0, Car c9 is at location l1, Car c5 is at location l0, Car c2 is at location l1, Car c8 is at location l0, Car c1 is at location l0, Car c6 is at location l0, Car c7 is at location l0, and Car c3 is at location l1.", "question": "Given the plan: \"board the car c3 at location l0, debark car c3 to location l0 from the ferry, board the car c9 at location l0, sail from location l0 to location l1, debark car c9 to location l1 from the ferry, board the car c7 at location l1, sail from location l1 to location l0, debark car c7 to location l0 from the ferry, board the car c3 at location l0, sail from location l0 to location l1, debark car c3 to location l1 from the ferry, board the car c4 at location l1, sail from location l1 to location l0, debark car c4 to location l0 from the ferry, board the car c2 at location l0, sail from location l0 to location l1, debark car c2 to location l1 from the ferry, board the car c5 at location l1, sail from location l1 to location l0, debark car c5 to location l0 from the ferry, sail from location l0 to location l1\"; can the following action be removed from this plan and still have a valid plan: board the car c3 at location l0?", "answer": "no"}
