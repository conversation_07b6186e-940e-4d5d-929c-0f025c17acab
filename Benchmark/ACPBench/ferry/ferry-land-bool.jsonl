{"id": -3755988908408204914, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c7 on board. The cars are at locations as follows: c11, c15, c32, c19, c2, c33, and c49 are at l1; c44, c45, c36, c10, c47, c1, c16, c18, c24, c42, c4, c17, and c48 are at l0; c31, c12, c43, c25, c37, c20, c34, c23, c13, and c6 are at l3; c46, c14, c0, c28, c30, c27, c5, c8, c9, c41, c38, and c29 are at l4; c39, c40, c3, c21, c22, c35, and c26 are at l2. The goal is to reach a state where the following facts hold: Car c11 is at location l1, Car c44 is at location l0, Car c45 is at location l0, Car c31 is at location l3, Car c12 is at location l3, Car c36 is at location l0, Car c46 is at location l4, Car c14 is at location l4, Car c39 is at location l2, Car c28 is at location l4, Car c15 is at location l1, Car c25 is at location l3, Car c30 is at location l4, Car c27 is at location l4, Car c5 is at location l4, Car c10 is at location l0, Car c32 is at location l1, Car c37 is at location l3, Car c40 is at location l2, Car c20 is at location l3, Car c19 is at location l1, Car c34 is at location l3, Car c8 is at location l4, Car c9 is at location l4, Car c23 is at location l3, Car c47 is at location l0, Car c3 is at location l2, Car c21 is at location l2, Car c1 is at location l0, Car c41 is at location l4, Car c16 is at location l0, Car c7 is at location l0, Car c22 is at location l2, Car c43 is at location l2, Car c0 is at location l2, Car c2 is at location l1, Car c18 is at location l0, Car c13 is at location l3, Car c33 is at location l1, Car c6 is at location l3, Car c35 is at location l2, Car c26 is at location l2, Car c38 is at location l4, Car c49 is at location l1, Car c24 is at location l0, Car c42 is at location l0, Car c4 is at location l0, Car c29 is at location l4, Car c17 is at location l0, and Car c48 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? There are no cars on the ferry", "answer": "yes"}
{"id": 3617573445680283767, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c44, c14, c36, c8, c2, c0, c19, c13, c38, c11, c12, c20, c40, c25, c9, c29, c21, c43, c30, c22, and c26 are at l0; c23, c48, c7, c15, c39, c31, c32, c45, c27, c3, c17, c34, c28, c1, c33, c46, c4, c42, c16, c49, c37, c35, c6, c18, c47, c5, c10, c41, and c24 are at l1. The goal is to reach a state where the following facts hold: Car c45 is at location l0, Car c44 is at location l0, Car c14 is at location l0, Car c23 is at location l1, Car c37 is at location l0, Car c36 is at location l0, Car c48 is at location l1, Car c8 is at location l0, Car c7 is at location l1, Car c15 is at location l1, Car c2 is at location l0, Car c39 is at location l1, Car c46 is at location l0, Car c31 is at location l1, Car c32 is at location l1, Car c0 is at location l0, Car c19 is at location l0, Car c13 is at location l0, Car c38 is at location l0, Car c27 is at location l1, Car c3 is at location l1, Car c17 is at location l1, Car c34 is at location l1, Car c11 is at location l0, Car c12 is at location l0, Car c20 is at location l0, Car c28 is at location l1, Car c40 is at location l0, Car c25 is at location l0, Car c1 is at location l1, Car c9 is at location l0, Car c29 is at location l0, Car c33 is at location l1, Car c21 is at location l0, Car c4 is at location l1, Car c42 is at location l1, Car c43 is at location l0, Car c16 is at location l1, Car c49 is at location l1, Car c35 is at location l1, Car c30 is at location l0, Car c6 is at location l1, Car c18 is at location l1, Car c47 is at location l1, Car c5 is at location l1, Car c10 is at location l1, Car c22 is at location l0, Car c41 is at location l1, Car c24 is at location l1, and Car c26 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c30 is on the ferry", "answer": "no"}
{"id": 5442417161932553298, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l4, with the car c1 on board. The cars are at locations as follows: c2 and c0 are at l3. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c0 is at location l3, and Car c1 is at location l3.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The ferry is at l3 location", "answer": "yes"}
{"id": 941512129145842155, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c23, c44, c14, c33, c35, c8, c3, c2, c0, c19, c6, c13, c11, c16, c12, c20, c9, c15, c29, c18, c30, and c17 are at l0; c38, c36, c48, c5, c7, c26, c39, c40, c31, c25, c32, c43, c27, c22, c34, c28, c1, c46, c4, c42, c49, c21, c37, c47, c10, c41, c24, and c45 are at l1. The goal is to reach a state where the following facts hold: Car c45 is at location l0, Car c44 is at location l0, Car c14 is at location l0, Car c23 is at location l1, Car c37 is at location l0, Car c36 is at location l0, Car c48 is at location l1, Car c8 is at location l0, Car c7 is at location l1, Car c15 is at location l1, Car c2 is at location l0, Car c39 is at location l1, Car c46 is at location l0, Car c31 is at location l1, Car c32 is at location l1, Car c0 is at location l0, Car c19 is at location l0, Car c13 is at location l0, Car c38 is at location l0, Car c27 is at location l1, Car c3 is at location l1, Car c17 is at location l1, Car c34 is at location l1, Car c11 is at location l0, Car c12 is at location l0, Car c20 is at location l0, Car c28 is at location l1, Car c40 is at location l0, Car c25 is at location l0, Car c1 is at location l1, Car c9 is at location l0, Car c29 is at location l0, Car c33 is at location l1, Car c21 is at location l0, Car c4 is at location l1, Car c42 is at location l1, Car c43 is at location l0, Car c16 is at location l1, Car c49 is at location l1, Car c35 is at location l1, Car c30 is at location l0, Car c6 is at location l1, Car c18 is at location l1, Car c47 is at location l1, Car c5 is at location l1, Car c10 is at location l1, Car c22 is at location l0, Car c41 is at location l1, Car c24 is at location l1, and Car c26 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c14 is on board the ferry", "answer": "no"}
{"id": 7589887814401972365, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c46 on board. The cars are at locations as follows: c45, c44, c14, c37, c36, c8, c2, c0, c13, c38, c11, c12, c20, c40, c25, c9, c29, c21, c43, c30, c22, and c26 are at l0; c23, c48, c7, c15, c39, c31, c32, c19, c27, c3, c17, c34, c28, c1, c33, c4, c42, c16, c49, c35, c6, c18, c47, c5, c10, c41, and c24 are at l1. The goal is to reach a state where the following facts hold: Car c45 is at location l0, Car c44 is at location l0, Car c14 is at location l0, Car c23 is at location l1, Car c37 is at location l0, Car c36 is at location l0, Car c48 is at location l1, Car c8 is at location l0, Car c7 is at location l1, Car c15 is at location l1, Car c2 is at location l0, Car c39 is at location l1, Car c46 is at location l0, Car c31 is at location l1, Car c32 is at location l1, Car c0 is at location l0, Car c19 is at location l0, Car c13 is at location l0, Car c38 is at location l0, Car c27 is at location l1, Car c3 is at location l1, Car c17 is at location l1, Car c34 is at location l1, Car c11 is at location l0, Car c12 is at location l0, Car c20 is at location l0, Car c28 is at location l1, Car c40 is at location l0, Car c25 is at location l0, Car c1 is at location l1, Car c9 is at location l0, Car c29 is at location l0, Car c33 is at location l1, Car c21 is at location l0, Car c4 is at location l1, Car c42 is at location l1, Car c43 is at location l0, Car c16 is at location l1, Car c49 is at location l1, Car c35 is at location l1, Car c30 is at location l0, Car c6 is at location l1, Car c18 is at location l1, Car c47 is at location l1, Car c5 is at location l1, Car c10 is at location l1, Car c22 is at location l0, Car c41 is at location l1, Car c24 is at location l1, and Car c26 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c2 is on board the ferry", "answer": "no"}
{"id": -9036743003168256916, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1, with the car c1 on board. The cars are at locations as follows: c0 is at l0; c2 is at l4. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c0 is at location l3, and Car c1 is at location l3.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c0 is on the ferry", "answer": "yes"}
{"id": -7882800440783655287, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c45, c44, c14, c37, c8, c2, c46, c0, c19, c13, c38, c11, c12, c20, c40, c25, c9, c29, c21, c43, c30, c22, and c26 are at l0; c23, c36, c48, c7, c15, c39, c31, c32, c27, c3, c17, c34, c28, c1, c33, c4, c42, c16, c49, c35, c6, c18, c47, c5, c10, c41, and c24 are at l1. The goal is to reach a state where the following facts hold: Car c45 is at location l0, Car c44 is at location l0, Car c14 is at location l0, Car c23 is at location l1, Car c37 is at location l0, Car c36 is at location l0, Car c48 is at location l1, Car c8 is at location l0, Car c7 is at location l1, Car c15 is at location l1, Car c2 is at location l0, Car c39 is at location l1, Car c46 is at location l0, Car c31 is at location l1, Car c32 is at location l1, Car c0 is at location l0, Car c19 is at location l0, Car c13 is at location l0, Car c38 is at location l0, Car c27 is at location l1, Car c3 is at location l1, Car c17 is at location l1, Car c34 is at location l1, Car c11 is at location l0, Car c12 is at location l0, Car c20 is at location l0, Car c28 is at location l1, Car c40 is at location l0, Car c25 is at location l0, Car c1 is at location l1, Car c9 is at location l0, Car c29 is at location l0, Car c33 is at location l1, Car c21 is at location l0, Car c4 is at location l1, Car c42 is at location l1, Car c43 is at location l0, Car c16 is at location l1, Car c49 is at location l1, Car c35 is at location l1, Car c30 is at location l0, Car c6 is at location l1, Car c18 is at location l1, Car c47 is at location l1, Car c5 is at location l1, Car c10 is at location l1, Car c22 is at location l0, Car c41 is at location l1, Car c24 is at location l1, and Car c26 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c48 is at location l0", "answer": "no"}
{"id": -1207328381549894196, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1, with the car c8 on board. The cars are at locations as follows: c1, c2, and c7 are at l1; c9, c6, and c4 are at l0; c0, c3, and c5 are at l2. The goal is to reach a state where the following facts hold: Car c8 is at location l2, Car c1 is at location l1, Car c9 is at location l0, Car c0 is at location l2, Car c4 is at location l0, Car c2 is at location l1, Car c3 is at location l2, Car c5 is at location l2, Car c6 is at location l2, and Car c7 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c6 is at location l1", "answer": "no"}
{"id": 6580681051203913382, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c11, c15, c32, c19, c2, c33, and c49 are at l1; c44, c45, c36, c10, c47, c1, c16, c18, c24, c42, c4, c17, and c48 are at l0; c31, c12, c7, c25, c37, c20, c34, c23, c22, c13, and c6 are at l3; c46, c14, c0, c28, c30, c27, c5, c8, c9, c41, c38, and c29 are at l4; c39, c40, c3, c21, c43, c35, and c26 are at l2. The goal is to reach a state where the following facts hold: Car c11 is at location l1, Car c44 is at location l0, Car c45 is at location l0, Car c31 is at location l3, Car c12 is at location l3, Car c36 is at location l0, Car c46 is at location l4, Car c14 is at location l4, Car c39 is at location l2, Car c28 is at location l4, Car c15 is at location l1, Car c25 is at location l3, Car c30 is at location l4, Car c27 is at location l4, Car c5 is at location l4, Car c10 is at location l0, Car c32 is at location l1, Car c37 is at location l3, Car c40 is at location l2, Car c20 is at location l3, Car c19 is at location l1, Car c34 is at location l3, Car c8 is at location l4, Car c9 is at location l4, Car c23 is at location l3, Car c47 is at location l0, Car c3 is at location l2, Car c21 is at location l2, Car c1 is at location l0, Car c41 is at location l4, Car c16 is at location l0, Car c7 is at location l0, Car c22 is at location l2, Car c43 is at location l2, Car c0 is at location l2, Car c2 is at location l1, Car c18 is at location l0, Car c13 is at location l3, Car c33 is at location l1, Car c6 is at location l3, Car c35 is at location l2, Car c26 is at location l2, Car c38 is at location l4, Car c49 is at location l1, Car c24 is at location l0, Car c42 is at location l0, Car c4 is at location l0, Car c29 is at location l4, Car c17 is at location l0, and Car c48 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The ferry is at l3 location", "answer": "yes"}
{"id": 3211016090457128984, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l3 location and it is empty. The cars are at locations as follows: c11, c48, c15, c32, c19, c44, c2, c33, and c46 are at l1; c31, c12, c39, c43, c7, c5, c37, c20, c34, c23, c13, c3, c49, c42, and c4 are at l3; c36, c14, c0, c28, c26, c30, c27, c45, c47, c38, c35, and c29 are at l4; c10, c6, c1, c16, c25, c9, c18, c24, c17, and c41 are at l0; c40, c21, c22, and c8 are at l2. The goal is to reach a state where the following facts hold: Car c11 is at location l1, Car c44 is at location l0, Car c45 is at location l0, Car c31 is at location l3, Car c12 is at location l3, Car c36 is at location l0, Car c46 is at location l4, Car c14 is at location l4, Car c39 is at location l2, Car c28 is at location l4, Car c15 is at location l1, Car c25 is at location l3, Car c30 is at location l4, Car c27 is at location l4, Car c5 is at location l4, Car c10 is at location l0, Car c32 is at location l1, Car c37 is at location l3, Car c40 is at location l2, Car c20 is at location l3, Car c19 is at location l1, Car c34 is at location l3, Car c8 is at location l4, Car c9 is at location l4, Car c23 is at location l3, Car c47 is at location l0, Car c3 is at location l2, Car c21 is at location l2, Car c1 is at location l0, Car c41 is at location l4, Car c16 is at location l0, Car c7 is at location l0, Car c22 is at location l2, Car c43 is at location l2, Car c0 is at location l2, Car c2 is at location l1, Car c18 is at location l0, Car c13 is at location l3, Car c33 is at location l1, Car c6 is at location l3, Car c35 is at location l2, Car c26 is at location l2, Car c38 is at location l4, Car c49 is at location l1, Car c24 is at location l0, Car c42 is at location l0, Car c4 is at location l0, Car c29 is at location l4, Car c17 is at location l0, and Car c48 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c45 is on the ferry", "answer": "yes"}
{"id": 1263458375528833442, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 20 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c14, c8, c3, c2, c10, c0, c6, c13, c11, c16, c9, c15, c18, and c17 are at l0; c7, c12, c19, c1, c4, and c5 are at l1. The goal is to reach a state where the following facts hold: Car c11 is at location l1, Car c8 is at location l1, Car c3 is at location l0, Car c7 is at location l1, Car c10 is at location l0, Car c0 is at location l0, Car c12 is at location l1, Car c19 is at location l1, Car c13 is at location l0, Car c17 is at location l1, Car c1 is at location l1, Car c9 is at location l0, Car c15 is at location l0, Car c14 is at location l1, Car c2 is at location l1, Car c4 is at location l1, Car c16 is at location l1, Car c6 is at location l1, Car c18 is at location l1, and Car c5 is at location l1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c12 is at location l0", "answer": "no"}
{"id": 2870824961692944373, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 20 cars, numbered consecutively. Currently, the ferry is at l0, with the car c1 on board. The cars are at locations as follows: c14, c8, c3, c2, c10, c0, c6, c13, c11, c16, c9, c15, and c17 are at l0; c7, c12, c19, c4, c18, and c5 are at l1. The goal is to reach a state where the following facts hold: Car c11 is at location l1, Car c8 is at location l1, Car c3 is at location l0, Car c7 is at location l1, Car c10 is at location l0, Car c0 is at location l0, Car c12 is at location l1, Car c19 is at location l1, Car c13 is at location l0, Car c17 is at location l1, Car c1 is at location l1, Car c9 is at location l0, Car c15 is at location l0, Car c14 is at location l1, Car c2 is at location l1, Car c4 is at location l1, Car c16 is at location l1, Car c6 is at location l1, Car c18 is at location l1, and Car c5 is at location l1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Ferry has car c7 on board", "answer": "no"}
{"id": -9041134821424221552, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 20 cars, numbered consecutively. Currently, the ferry is at l0, with the car c2 on board. The cars are at locations as follows: c11, c7, c12, c19, c1, c14, c4, c16, c18, and c5 are at l1; c8, c3, c10, c0, c6, c13, c9, c15, and c17 are at l0. The goal is to reach a state where the following facts hold: Car c11 is at location l1, Car c8 is at location l1, Car c3 is at location l0, Car c7 is at location l1, Car c10 is at location l0, Car c0 is at location l0, Car c12 is at location l1, Car c19 is at location l1, Car c13 is at location l0, Car c17 is at location l1, Car c1 is at location l1, Car c9 is at location l0, Car c15 is at location l0, Car c14 is at location l1, Car c2 is at location l1, Car c4 is at location l1, Car c16 is at location l1, Car c6 is at location l1, Car c18 is at location l1, and Car c5 is at location l1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c17 is on board the ferry", "answer": "yes"}
{"id": -1941137116217247756, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 10 cars, numbered consecutively. Currently, the ferry is at l0, with the car c6 on board. The cars are at locations as follows: c0, c8, c1, c5, and c7 are at l0; c2, c3, c9, and c4 are at l1. The goal is to reach a state where the following facts hold: Car c0 is at location l0, Car c6 is at location l0, Car c8 is at location l0, Car c4 is at location l0, Car c2 is at location l1, Car c3 is at location l1, Car c9 is at location l1, Car c1 is at location l0, Car c5 is at location l0, and Car c7 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c8 is at location l1", "answer": "no"}
{"id": -6125716678316937654, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 20 cars, numbered consecutively. Currently, the ferry is at l1, with the car c18 on board. The cars are at locations as follows: c14, c8, c3, c2, c10, c0, c6, c13, c1, c11, c16, c9, c15, and c17 are at l0; c7, c12, c19, c4, and c5 are at l1. The goal is to reach a state where the following facts hold: Car c11 is at location l1, Car c8 is at location l1, Car c3 is at location l0, Car c7 is at location l1, Car c10 is at location l0, Car c0 is at location l0, Car c12 is at location l1, Car c19 is at location l1, Car c13 is at location l0, Car c17 is at location l1, Car c1 is at location l1, Car c9 is at location l0, Car c15 is at location l0, Car c14 is at location l1, Car c2 is at location l1, Car c4 is at location l1, Car c16 is at location l1, Car c6 is at location l1, Car c18 is at location l1, and Car c5 is at location l1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? There are no cars on the ferry", "answer": "yes"}
