{"id": 2356866965899380801, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c21 on board. The cars are at locations as follows: c26, c40, c39, c42, c47, c10, c43, c46, c37, c49, c4, c31, c34, c45, c41, c27, c32, c24, c15, c28, c48, c5, c7, c25, c22, c16, c36, and c1 are at l1; c18, c33, c29, c12, c14, c19, c35, c0, c11, c8, c2, c3, c30, c9, c38, c6, c23, c13, c20, c44, and c17 are at l0.", "question": "Is it possible to transition to a state where the action \"debark the car c0 to location l1 from the ferry\" can be applied?", "answer": "yes"}
{"id": 7698814873333889767, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c16 on board. The cars are at locations as follows: c26, c40, c39, c42, c47, c10, c43, c46, c37, c49, c4, c31, c34, c45, c41, c27, c32, c24, c15, c21, c28, c48, c5, c7, c25, c22, c36, and c1 are at l1; c18, c33, c29, c12, c14, c19, c35, c0, c11, c8, c2, c3, c30, c9, c38, c6, c23, c13, c20, c44, and c17 are at l0.", "question": "Is it possible to transition to a state where the action \"debark the car c21 to location l0 from the ferry\" can be applied?", "answer": "yes"}
{"id": -4558179676875521210, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l3, with the car c6 on board. The cars are at locations as follows: c39, c3, c40, c21, c35, and c26 are at l2; c34, c31, c23, c22, c37, c5, c20, c42, c43, c7, c12, c13, c49, and c25 are at l3; c30, c29, c14, c28, c47, c38, c36, c41, c27, c0, c8, and c45 are at l4; c18, c4, c24, c9, c10, c1, c16, and c17 are at l0; c2, c19, c46, c33, c32, c15, c11, c48, and c44 are at l1.", "question": "Is it possible to transition to a state where the action \"debark car c6 to location l1 from the ferry\" can be applied?", "answer": "yes"}
{"id": 9124159555422587712, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c5, c6, c3, and c0 are at l2; c9, c2, and c7 are at l0; c8, c1, and c4 are at l1.", "question": "Is it possible to transition to a state where the action \"embark the car l2 at location c8 on to the ferry\" can be applied?", "answer": "no"}
{"id": -8595330654831203901, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0, with the car c0 on board. The cars are at locations as follows: c2 and c1 are at l1.", "question": "Is it possible to transition to a state where the action \"debark the car c0 to location l0 from the airplane\" can be applied?", "answer": "no"}
{"id": -8308441047776414098, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l3 location and it is empty. The cars are at locations as follows: c2 and c1 are at l3; c0 is at l0.", "question": "Is it possible to transition to a state where the action \"travel by sea from location l2 to location l1\" can be applied?", "answer": "yes"}
{"id": 1960596875631641725, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1, with the car c2 on board. The cars are at locations as follows: c1 is at l3; c0 is at l1.", "question": "Is it possible to transition to a state where the action \"unload the car c2 from the ferry to location l4\" can be applied?", "answer": "yes"}
{"id": -7437794737911834133, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c36 on board. The cars are at locations as follows: c39, c3, c40, c21, c35, c26, and c22 are at l2; c34, c31, c23, c37, c5, c20, c42, c43, c7, c12, c6, c13, c49, and c25 are at l3; c30, c29, c14, c28, c47, c38, c41, c9, c27, c0, c8, and c45 are at l4; c18, c4, c24, c10, c1, c44, c16, and c17 are at l0; c2, c19, c46, c33, c32, c15, c11, and c48 are at l1.", "question": "Is it possible to transition to a state where the action \"fly from location l4 to location l1\" can be applied?", "answer": "no"}
{"id": -7164236778301594118, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c0 is at l4; c2 is at l0; c1 is at l3.", "question": "Is it possible to transition to a state where the action \"debark the car c0 from the ferry to location c1\" can be applied?", "answer": "no"}
{"id": 5808532745636152861, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c40 on board. The cars are at locations as follows: c29, c12, c26, c14, c25, c19, c35, c0, c11, c8, c2, c22, c30, c9, c38, c6, c21, c13, c36, c20, and c44 are at l0; c23, c39, c42, c47, c10, c18, c43, c3, c46, c37, c33, c49, c4, c31, c34, c45, c41, c27, c32, c24, c15, c28, c48, c5, c7, c16, c17, and c1 are at l1.", "question": "Is it possible to transition to a state where the action \"drive from location l0 to location l1\" can be applied?", "answer": "no"}
{"id": -1990152005808638716, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 20 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c12, c19, c4, c11, c5, c7, c16, and c1 are at l1; c15, c18, c14, c0, c8, c3, c2, c9, c6, c10, c13, and c17 are at l0.", "question": "Is it possible to transition to a state where the action \"board the car c19 at location l1\" can be applied?", "answer": "yes"}
{"id": -2794295456136976062, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 20 cars, numbered consecutively. Currently, the ferry is at l0, with the car c2 on board. The cars are at locations as follows: c12, c14, c18, c19, c4, c11, c5, c7, c16, and c17 are at l1; c15, c8, c0, c3, c9, c6, c10, c13, and c1 are at l0.", "question": "Is it possible to transition to a state where the action \"debark the car c8 from the ferry to location l1\" can be applied?", "answer": "yes"}
{"id": -4761836809351361279, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 20 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c12, c14, c1, c19, c4, c11, c5, c7, and c17 are at l1; c15, c18, c0, c8, c2, c3, c9, c6, c10, c13, and c16 are at l0.", "question": "Is it possible to transition to a state where the action \"unload the car c7 from the ferry to location l0\" can be applied?", "answer": "yes"}
{"id": -829731445227953700, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 2 cars, numbered consecutively. Currently, the ferry is at l0, with the car c0 on board. The cars are at locations as follows: c1 is at l1.", "question": "Is it possible to transition to a state where the action \"embark the car c1 at location l1 into the airplane\" can be applied?", "answer": "no"}
{"id": -2643254463469407116, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 20 cars, numbered consecutively. Currently, the ferry is at l1, with the car c5 on board. The cars are at locations as follows: c12, c6, c14, c2, c18, c19, c8, c4, c11, c7, c16, c17, and c1 are at l1; c15, c0, c3, c9, c10, and c13 are at l0.", "question": "Is it possible to transition to a state where the action \"travel by sea from location l0 to location l1\" can be applied?", "answer": "yes"}
