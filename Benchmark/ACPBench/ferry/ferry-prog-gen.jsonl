{"id": 3080925388833165113, "group": "progression_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l4 location and it is empty. The cars are at locations as follows: c1 is at l3; c0 is at l4; c2 is at l2. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on the ferry.", "question": "Break down the outcomes of performing the action \"sail from location l4 to location l0\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at-ferry l4)"], "pos": ["(at-ferry l0)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c1 l3) (at c2 l2) (at-ferry l4) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3))\n    (:goal (and (at c0 l3) (at c1 l3) (at c2 l3)))\n)"}
{"id": -4143600109699337607, "group": "progression_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l4, with the car c2 on board. The cars are at locations as follows: c1 and c0 are at l3. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Ferry has car ?c on board.", "question": "Break down the outcomes of performing the action \"debark car c2 to location l4 from the ferry\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(on c2)"], "pos": ["(empty-ferry)", "(at c2 l4)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l3) (at c1 l3) (at-ferry l4) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3) (on c2))\n    (:goal (and (at c0 l3) (at c1 l3) (at c2 l3)))\n)"}
{"id": 1615456140714437651, "group": "progression_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c3 on board. The cars are at locations as follows: c38, c46, c10, c31, c1, c13, c17, c4, c43, c47, c28, c27, c15, c40, c45, c34, c23, c42, c41, c39, c24, c48, c5, c49, c7, c18, c32, and c16 are at l1; c20, c11, c19, c22, c37, c26, c6, c29, c0, c33, c36, c9, c12, c44, c8, c2, c30, c35, c21, c14, and c25 are at l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Car ?c is on board the ferry.", "question": "Break down the outcomes of performing the action \"travel by sea from location l1 to location l0\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at-ferry l1)"], "pos": ["(at-ferry l0)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l1) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c2 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l0) (at c34 l1) (at c35 l0) (at c36 l0) (at c37 l0) (at c38 l1) (at c39 l1) (at c4 l1) (at c40 l1) (at c41 l1) (at c42 l1) (at c43 l1) (at c44 l0) (at c45 l1) (at c46 l1) (at c47 l1) (at c48 l1) (at c49 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l1) (not-eq l0 l1) (not-eq l1 l0) (on c3))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l0) (at c3 l1) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1)))\n)"}
{"id": -9211741084036399563, "group": "progression_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c21 on board. The cars are at locations as follows: c38, c46, c10, c31, c1, c4, c26, c43, c47, c28, c37, c27, c40, c22, c45, c34, c42, c41, c39, c24, c48, c25, c49, c5, c19, c32, c36, and c7 are at l1; c3, c20, c11, c17, c16, c23, c6, c13, c29, c0, c33, c9, c12, c44, c8, c18, c2, c30, c35, c14, and c15 are at l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on board the ferry.", "question": "Break down the outcomes of performing the action \"sail from location l0 to location l1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at-ferry l0)"], "pos": ["(at-ferry l1)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l0) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l1) (at c2 l0) (at c20 l0) (at c22 l1) (at c23 l0) (at c24 l1) (at c25 l1) (at c26 l1) (at c27 l1) (at c28 l1) (at c29 l0) (at c3 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l0) (at c34 l1) (at c35 l0) (at c36 l1) (at c37 l1) (at c38 l1) (at c39 l1) (at c4 l1) (at c40 l1) (at c41 l1) (at c42 l1) (at c43 l1) (at c44 l0) (at c45 l1) (at c46 l1) (at c47 l1) (at c48 l1) (at c49 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l0) (not-eq l0 l1) (not-eq l1 l0) (on c21))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l0) (at c3 l1) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1)))\n)"}
{"id": 3205502915375973972, "group": "progression_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c20, c11, c19, c46, c22, c37, c26, c13, c29, c45, c40, c0, c36, c9, c12, c44, c8, c2, c30, c38, c43, c21, and c14 are at l0; c10, c31, c6, c1, c17, c4, c33, c47, c28, c35, c27, c15, c34, c42, c23, c41, c39, c24, c48, c25, c49, c5, c7, c3, c18, c32, and c16 are at l1. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Ferry has car ?c on board.", "question": "Break down the outcomes of performing the action \"travel by sea from location l0 to location l1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at-ferry l0)"], "pos": ["(at-ferry l1)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c2 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l1) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c3 l1) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c4 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l0) (at c3 l1) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1)))\n)"}
{"id": 1131373886503585195, "group": "progression_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c38, c46, c10, c31, c1, c4, c26, c43, c47, c28, c27, c15, c40, c22, c45, c34, c42, c41, c39, c24, c48, c25, c49, c5, c21, c32, c36, and c7 are at l1; c3, c20, c11, c19, c37, c17, c16, c23, c6, c13, c29, c0, c33, c9, c12, c44, c8, c18, c2, c30, c35, and c14 are at l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Ferry has car ?c on board.", "question": "Break down the outcomes of performing the action \"load the car c19 at location l0 on to the ferry\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(empty-ferry)", "(at c19 l0)"], "pos": ["(on c19)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l0) (at c2 l0) (at c20 l0) (at c21 l1) (at c22 l1) (at c23 l0) (at c24 l1) (at c25 l1) (at c26 l1) (at c27 l1) (at c28 l1) (at c29 l0) (at c3 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l0) (at c34 l1) (at c35 l0) (at c36 l1) (at c37 l0) (at c38 l1) (at c39 l1) (at c4 l1) (at c40 l1) (at c41 l1) (at c42 l1) (at c43 l1) (at c44 l0) (at c45 l1) (at c46 l1) (at c47 l1) (at c48 l1) (at c49 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l0) (at c3 l1) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1)))\n)"}
{"id": -8901273463975505422, "group": "progression_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c5, c6, c3, c8, and c0 are at l2; c7, c9, and c4 are at l0; c1 and c2 are at l1. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Car ?c is on the ferry.", "question": "Break down the outcomes of performing the action \"board the car c7 at the location l0\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at c7 l0)", "(empty-ferry)"], "pos": ["(on c7)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 l2 - location)\n    (:init (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1))\n    (:goal (and (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0)))\n)"}
{"id": -5182851309451752606, "group": "progression_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l4, with the car c29 on board. The cars are at locations as follows: c34, c5, c43, c3, c49, c4, c39, c7, c13, c23, c37, c25, c31, and c42 are at l3; c47, c0, c35, c14, c28, c38, c27, c20, c36, c30, c26, and c45 are at l4; c46, c2, c33, c15, c11, c48, c44, c19, and c32 are at l1; c1, c17, c24, c16, c6, c41, c40, c9, c10, and c18 are at l0; c8, c22, c12, and c21 are at l2. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Ferry has car ?c on board.", "question": "Break down the outcomes of performing the action \"debark the car c29 from the ferry to location l4\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(on c29)"], "pos": ["(empty-ferry)", "(at c29 l4)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c1 l0) (at c10 l0) (at c11 l1) (at c12 l2) (at c13 l3) (at c14 l4) (at c15 l1) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l1) (at c2 l1) (at c20 l4) (at c21 l2) (at c22 l2) (at c23 l3) (at c24 l0) (at c25 l3) (at c26 l4) (at c27 l4) (at c28 l4) (at c3 l3) (at c30 l4) (at c31 l3) (at c32 l1) (at c33 l1) (at c34 l3) (at c35 l4) (at c36 l4) (at c37 l3) (at c38 l4) (at c39 l3) (at c4 l3) (at c40 l0) (at c41 l0) (at c42 l3) (at c43 l3) (at c44 l1) (at c45 l4) (at c46 l1) (at c47 l4) (at c48 l1) (at c49 l3) (at c5 l3) (at c6 l0) (at c7 l3) (at c8 l2) (at c9 l0) (at-ferry l4) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3) (on c29))\n    (:goal (and (at c0 l2) (at c1 l0) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l4) (at c6 l3) (at c7 l0) (at c8 l4) (at c9 l4) (at c10 l0) (at c11 l1) (at c12 l3) (at c13 l3) (at c14 l4) (at c15 l1) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l1) (at c20 l3) (at c21 l2) (at c22 l2) (at c23 l3) (at c24 l0) (at c25 l3) (at c26 l2) (at c27 l4) (at c28 l4) (at c29 l4) (at c30 l4) (at c31 l3) (at c32 l1) (at c33 l1) (at c34 l3) (at c35 l2) (at c36 l0) (at c37 l3) (at c38 l4) (at c39 l2) (at c40 l2) (at c41 l4) (at c42 l0) (at c43 l2) (at c44 l0) (at c45 l0) (at c46 l4) (at c47 l0) (at c48 l0) (at c49 l1)))\n)"}
{"id": -7742569249522788114, "group": "progression_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c46 on board. The cars are at locations as follows: c38, c31, c13, c4, c26, c43, c47, c28, c12, c37, c27, c40, c22, c45, c34, c42, c41, c39, c24, c48, c25, c49, c5, c21, c19, c32, c36, and c7 are at l1; c3, c20, c1, c11, c17, c16, c23, c6, c29, c0, c33, c9, c10, c44, c8, c18, c2, c30, c35, c14, and c15 are at l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on board the ferry.", "question": "Break down the outcomes of performing the action \"sail from location l1 to location l0\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at-ferry l1)"], "pos": ["(at-ferry l0)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c10 l0) (at c11 l0) (at c12 l1) (at c13 l1) (at c14 l0) (at c15 l0) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l1) (at c2 l0) (at c20 l0) (at c21 l1) (at c22 l1) (at c23 l0) (at c24 l1) (at c25 l1) (at c26 l1) (at c27 l1) (at c28 l1) (at c29 l0) (at c3 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l0) (at c34 l1) (at c35 l0) (at c36 l1) (at c37 l1) (at c38 l1) (at c39 l1) (at c4 l1) (at c40 l1) (at c41 l1) (at c42 l1) (at c43 l1) (at c44 l0) (at c45 l1) (at c47 l1) (at c48 l1) (at c49 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l1) (not-eq l0 l1) (not-eq l1 l0) (on c46))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l0) (at c3 l1) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1)))\n)"}
{"id": -4643949752170237498, "group": "progression_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c1 is at l3; c2 is at l4; c0 is at l1. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on board the ferry.", "question": "Break down the outcomes of performing the action \"sail from location l0 to location l4\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at-ferry l0)"], "pos": ["(at-ferry l4)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l1) (at c1 l3) (at c2 l4) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3))\n    (:goal (and (at c0 l3) (at c1 l3) (at c2 l3)))\n)"}
{"id": 297440160406485545, "group": "progression_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 10 cars, numbered consecutively. \nCurrently, the ferry is at l1, with the car c2 on board. The cars are at locations as follows: c5, c9, and c4 are at l1; c3, c0, c1, c6, c7, and c8 are at l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Ferry has car ?c on board.", "question": "Break down the outcomes of performing the action \"debark car c2 to location l1 from the ferry\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(on c2)"], "pos": ["(empty-ferry)", "(at c2 l1)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c3 l0) (at c4 l1) (at c5 l1) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1) (at-ferry l1) (not-eq l0 l1) (not-eq l1 l0) (on c2))\n    (:goal (and (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1)))\n)"}
{"id": -1913100025448013694, "group": "progression_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 5 cars, numbered consecutively. \nCurrently, the ferry is at l0, with the car c2 on board. The cars are at locations as follows: c3, c0, and c1 are at l0; c4 is at l1. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Car ?c is on the ferry.", "question": "Break down the outcomes of performing the action \"debark the car c2 to location l0 from the ferry\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(on c2)"], "pos": ["(empty-ferry)", "(at c2 l0)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c5)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c3 l0) (at c4 l1) (at-ferry l0) (not-eq l0 l1) (not-eq l1 l0) (on c2))\n    (:goal (and (at c0 l1) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l0)))\n)"}
{"id": 4772210962720336132, "group": "progression_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 5 cars, numbered consecutively. \nCurrently, the ferry is at l1, with the car c4 on board. The cars are at locations as follows: c3 and c1 are at l0; c2 and c0 are at l1. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Car ?c is on the ferry.", "question": "Break down the outcomes of performing the action \"travel by sea from location l1 to location l0\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at-ferry l1)"], "pos": ["(at-ferry l0)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c5)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 - car l0 l1 - location)\n    (:init (at c0 l1) (at c1 l0) (at c2 l1) (at c3 l0) (at-ferry l1) (not-eq l0 l1) (not-eq l1 l0) (on c4))\n    (:goal (and (at c0 l1) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l0)))\n)"}
{"id": -3945458095189975128, "group": "progression_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 20 cars, numbered consecutively. \nCurrently, the ferry is at l0, with the car c2 on board. The cars are at locations as follows: c3, c6, c13, c0, c9, c8, c10, c14, and c15 are at l0; c1, c17, c4, c12, c16, c11, c5, c19, c18, and c7 are at l1. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Ferry has car ?c on board.", "question": "Break down the outcomes of performing the action \"debark car c2 to location l0 from the ferry\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(on c2)"], "pos": ["(empty-ferry)", "(at c2 l0)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c20)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c10 l0) (at c11 l1) (at c12 l1) (at c13 l0) (at c14 l0) (at c15 l0) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l1) (at c3 l0) (at c4 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l0) (not-eq l0 l1) (not-eq l1 l0) (on c2))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l1) (at c3 l0) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l1) (at c9 l0) (at c10 l0) (at c11 l1) (at c12 l1) (at c13 l0) (at c14 l1) (at c15 l0) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l1)))\n)"}
{"id": 6210118823157974422, "group": "progression_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 5 cars, numbered consecutively. \nCurrently, the ferry is at l1, with the car c0 on board. The cars are at locations as follows: c2, c3, and c4 are at l0; c1 is at l1. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Ferry has car ?c on board.", "question": "Break down the outcomes of performing the action \"debark the car c0 from the ferry to location l1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(on c0)"], "pos": ["(empty-ferry)", "(at c0 l1)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c5)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 - car l0 l1 - location)\n    (:init (at c1 l1) (at c2 l0) (at c3 l0) (at c4 l0) (at-ferry l1) (not-eq l0 l1) (not-eq l1 l0) (on c0))\n    (:goal (and (at c0 l1) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l0)))\n)"}
