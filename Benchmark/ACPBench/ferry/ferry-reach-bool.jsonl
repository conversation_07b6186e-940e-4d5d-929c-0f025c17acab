{"id": 1268984985790259256, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0, with the car c1 on board. The cars are at locations as follows: c0 and c2 are at l3.", "question": "Is it possible to transition to a state where the following holds: The ferry is empty and The ferry is at l2 location?", "answer": "yes"}
{"id": -3826593486022472982, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c2 is at l1; c0 and c1 are at l3.", "question": "Is it possible to transition to a state where the following holds: The ferry is at l1 location?", "answer": "yes"}
{"id": 3086831723562678790, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c9, c2, and c7 are at l0; c3, c0, c6, and c5 are at l2; c1, c4, and c8 are at l1.", "question": "Is it possible to transition to a state where the following holds: The ferry is at l0 location and Car c0 is on board the ferry?", "answer": "yes"}
{"id": 254220531529050744, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c1 is at l1; c2 and c0 are at l4.", "question": "Is it possible to transition to a state where the following holds: The ferry is at l4 location and The ferry is at l0 location?", "answer": "no"}
{"id": 6273095533827635039, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c28, c31, c1, c32, c24, c22, c38, c41, c49, c5, c43, c21, c40, c39, c26, c10, c4, c27, c48, c47, c46, c36, c45, c34, c42, c7, c25, c37, and c12 are at l1; c17, c33, c2, c13, c20, c11, c16, c29, c30, c3, c14, c15, c8, c6, c18, c19, c0, c23, c9, c44, and c35 are at l0.", "question": "Is it possible to transition to a state where the following holds: Car c48 is on board the ferry and The ferry is at l0 location?", "answer": "yes"}
{"id": -1387152592430020428, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l2, with the car c35 on board. The cars are at locations as follows: c17, c16, c24, c1, c10, c6, c18, and c9 are at l0; c34, c4, c42, c20, c31, c13, c7, c49, c5, c37, c12, c23, c25, and c43 are at l3; c3, c0, c21, c39, c22, and c40 are at l2; c41, c28, c47, c26, c27, c45, c36, c30, c8, c38, c29, and c14 are at l4; c19, c32, c11, c48, c33, c15, c2, c46, and c44 are at l1.", "question": "Is it possible to transition to a state where the following holds: The ferry is at l3 location?", "answer": "yes"}
{"id": -3167712919509616391, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c17 on board. The cars are at locations as follows: c28, c31, c1, c32, c24, c22, c38, c41, c49, c5, c43, c16, c40, c39, c4, c10, c27, c48, c15, c47, c46, c36, c45, c34, c42, c7, c25, and c37 are at l1; c33, c2, c13, c20, c11, c29, c30, c3, c14, c26, c8, c6, c18, c19, c0, c23, c12, c9, c44, c35, and c21 are at l0.", "question": "Is it possible to transition to a state where the following holds: The ferry is empty and Car c17 is at location l0?", "answer": "yes"}
{"id": -3526496914030249199, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c0, c2, and c1 are at l3.", "question": "Is it possible to transition to a state where the following holds: Ferry has car l4 on board and Car c2 is at location l3?", "answer": "no"}
{"id": -8510261219970440273, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c2 and c1 are at l3; c0 is at l0.", "question": "Is it possible to transition to a state where the following holds: The ferry is at l0 location?", "answer": "yes"}
{"id": 1461482101892106760, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l4 location and it is empty. The cars are at locations as follows: c17, c16, c45, c42, c24, c1, c36, c10, c4, c18, c48, and c44 are at l0; c34, c20, c6, c31, c13, c7, c37, c12, c23, and c25 are at l3; c43, c26, c3, c35, c21, c39, c22, and c40 are at l2; c41, c28, c0, c47, c27, c9, c30, c5, c8, c46, c38, c29, and c14 are at l4; c19, c32, c49, c11, c33, c15, and c2 are at l1.", "question": "Is it possible to transition to a state where the following holds: The ferry is at l3 location and Car c5 is on board the ferry?", "answer": "yes"}
{"id": -2426698749034015429, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 10 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c2, c7, and c5 are at l1; c3, c4, c6, c9, c1, c0, and c8 are at l0.", "question": "Is it possible to transition to a state where the following holds: Car c2 is at location c0?", "answer": "no"}
{"id": -8931355586395996072, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 10 cars, numbered consecutively. Currently, the ferry is at l0, with the car c1 on board. The cars are at locations as follows: c2, c7, and c4 are at l1; c3, c9, c6, c5, c0, and c8 are at l0.", "question": "Is it possible to transition to a state where the following holds: The ferry is at c3 location and Car c8 is at location l0?", "answer": "no"}
{"id": -7281146360401604897, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 2 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c1 is at l2; c0 is at l1.", "question": "Is it possible to transition to a state where the following holds: The ferry is at l2 location?", "answer": "yes"}
{"id": -7522838240986012391, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c3, c9, c6, c2, c1, c0, and c8 are at l0; c7, c4, and c5 are at l1.", "question": "Is it possible to transition to a state where the following holds: The ferry is at l0 location and Car c6 is on the ferry?", "answer": "yes"}
{"id": -1202705330726218439, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 20 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c2, c13, c16, c3, c15, c8, c10, c6, c18, c0, and c9 are at l0; c17, c14, c1, c19, c5, c11, c4, c7, and c12 are at l1.", "question": "Is it possible to transition to a state where the following holds: Car l0 is on the ferry?", "answer": "no"}
