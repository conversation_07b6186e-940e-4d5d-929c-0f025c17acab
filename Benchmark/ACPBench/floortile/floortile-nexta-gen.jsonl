{"id": 5356903822780222455, "group": "goal_closer_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_6 is to the right of tile_5, tile_5 is to the right of tile_4, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_1 is down from tile_4, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_4 is down from tile_7, and tile_6 is down from tile_9 Currently, robot robot2 is at tile_1 and holding color black and robot robot1 is at tile_3 and holding color white; tile_4 and tile_2 are clear; tile_6 is painted white, tile_7 is painted black, tile_8 is painted white, tile_5 is painted black, and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_4 is painted in white color, and Tile tile_9 is painted in black color. The available actions are: (change-color ?r ?c ?c2) - alter the color of the robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - paint the tile ?y above the tile ?x with color ?c using the robot ?r, (paint-down ?r ?y ?x ?c) - use robot ?r to paint the tile ?y downwards from the tile ?x with the color ?c, (up ?r ?x ?y) - move the robot ?r up from tile ?x to tile ?y, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y which is to the right of the tile ?x, and (left ?r ?x ?y) - navigate robot ?r from tile ?x to tile ?y to its left.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(left robot1 tile_3 tile_2)", "(change-color robot2 black black)", "(paint-up robot2 tile_4 tile_1 black)", "(change-color robot1 white black)"], "no": ["(up robot2 tile_1 tile_4)", "(right robot2 tile_1 tile_2)", "(change-color robot1 white white)"], "opt": "2", "yes": ["(change-color robot2 black white)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_2) (clear tile_4) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_3) (robot-at robot2 tile_1) (robot-has robot1 white) (robot-has robot2 black) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": 6140161446988959192, "group": "goal_closer_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 20 tiles. The tiles locations are: tile_7 is to the right of tile_6, tile_20 is to the right of tile_19, tile_14 is to the right of tile_13, tile_5 is to the right of tile_4, tile_13 is to the right of tile_12, tile_17 is to the right of tile_16, tile_9 is to the right of tile_8, tile_18 is to the right of tile_17, tile_4 is to the right of tile_3, tile_10 is to the right of tile_9, tile_12 is to the right of tile_11, tile_3 is to the right of tile_2, tile_15 is to the right of tile_14, tile_2 is to the right of tile_1, tile_8 is to the right of tile_7, and tile_19 is to the right of tile_18. Further, tile_7 is down from tile_12, tile_15 is down from tile_20, tile_6 is down from tile_11, tile_14 is down from tile_19, tile_9 is down from tile_14, tile_3 is down from tile_8, tile_13 is down from tile_18, tile_8 is down from tile_13, tile_2 is down from tile_7, tile_5 is down from tile_10, tile_10 is down from tile_15, tile_11 is down from tile_16, tile_12 is down from tile_17, tile_1 is down from tile_6, and tile_4 is down from tile_9 Currently, robot robot2 is at tile_4 and holding color black and robot robot1 is at tile_9 and holding color white; tile_2, tile_14, tile_7, tile_12, tile_5, tile_1, tile_3, and tile_8 are clear; tile_20 is painted white, tile_13 is painted black, tile_17 is painted black, tile_11 is painted black, tile_10 is painted white, tile_6 is painted white, tile_19 is painted black, tile_16 is painted white, tile_18 is painted white, and tile_15 is painted black. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_20 is painted in white color, Tile tile_14 is painted in white color, Tile tile_8 is painted in white color, Tile tile_19 is painted in black color, Tile tile_12 is painted in white color, Tile tile_13 is painted in black color, Tile tile_16 is painted in white color, Tile tile_18 is painted in white color, Tile tile_17 is painted in black color, Tile tile_9 is painted in black color, Tile tile_11 is painted in black color, Tile tile_10 is painted in white color, and Tile tile_15 is painted in black color. The available actions are: (change-color ?r ?c ?c2) - alter the color of the robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - paint the tile ?y above the tile ?x with color ?c using the robot ?r, (paint-down ?r ?y ?x ?c) - apply color ?c to tile ?y which is below tile ?x using robot ?r, (up ?r ?x ?y) - move the robot ?r up from tile ?x to tile ?y, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y which is to the right of the tile ?x, and (left ?r ?x ?y) - move the robot ?r from the tile ?x to the tile on its left ?y.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(left robot2 tile_4 tile_3)", "(left robot1 tile_9 tile_8)", "(change-color robot2 black black)", "(up robot1 tile_9 tile_14)", "(change-color robot1 white white)"], "no": ["(right robot2 tile_4 tile_5)", "(change-color robot2 black white)", "(change-color robot1 white black)"], "opt": "11", "yes": ["(paint-up robot1 tile_14 tile_9 white)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_12) (clear tile_14) (clear tile_2) (clear tile_3) (clear tile_5) (clear tile_7) (clear tile_8) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_10 white) (painted tile_11 black) (painted tile_13 black) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white) (painted tile_6 white) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_9) (robot-at robot2 tile_4) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": 8934594740782947276, "group": "goal_closer_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 12 tiles. The tiles locations are: tile_11 is to the right of tile_10, tile_6 is to the right of tile_5, tile_5 is to the right of tile_4, tile_9 is to the right of tile_8, tile_12 is to the right of tile_11, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_9 is down from tile_12, tile_1 is down from tile_4, tile_8 is down from tile_11, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_7 is down from tile_10, tile_4 is down from tile_7, and tile_6 is down from tile_9 Currently, robot robot1 is at tile_7 and holding color white and robot robot2 is at tile_8 and holding color black; tile_3, tile_9, tile_5, tile_4, tile_1, tile_11, tile_6, and tile_2 are clear; tile_12 is painted white and tile_10 is painted white. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_4 is painted in white color, Tile tile_12 is painted in white color, Tile tile_9 is painted in black color, Tile tile_11 is painted in black color, and Tile tile_10 is painted in white color. The available actions are: (change-color ?r ?c ?c2) - change the color of robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - use robot ?r to paint the tile ?y above the tile ?x with the color ?c, (paint-down ?r ?y ?x ?c) - use robot ?r to paint the tile ?y downwards from the tile ?x with the color ?c, (up ?r ?x ?y) - move the robot ?r up from tile ?x to tile ?y, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move robot ?r from tile ?x to the right tile tile ?y, and (left ?r ?x ?y) - move the robot ?r from tile ?x to the left tile ?y.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(change-color robot1 white black)", "(change-color robot2 black white)", "(up robot2 tile_8 tile_11)", "(paint-down robot2 tile_5 tile_8 black)", "(paint-down robot1 tile_4 tile_7 white)", "(change-color robot1 white white)", "(down robot1 tile_7 tile_4)"], "no": ["(right robot2 tile_8 tile_9)", "(change-color robot2 black black)", "(down robot2 tile_8 tile_5)"], "opt": "19", "yes": ["(paint-up robot2 tile_11 tile_8 black)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_11) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_12 white) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_7) (robot-at robot2 tile_8) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": 5717528304829862590, "group": "goal_closer_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 12 tiles. The tiles locations are: tile_11 is to the right of tile_10, tile_6 is to the right of tile_5, tile_5 is to the right of tile_4, tile_9 is to the right of tile_8, tile_12 is to the right of tile_11, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_9 is down from tile_12, tile_1 is down from tile_4, tile_8 is down from tile_11, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_7 is down from tile_10, tile_4 is down from tile_7, and tile_6 is down from tile_9 Currently, robot robot1 is at tile_8 and holding color white and robot robot2 is at tile_7 and holding color black; tile_12, tile_10, tile_3, tile_9, tile_5, tile_1, tile_4, tile_11, tile_6, and tile_2 are clear. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_4 is painted in white color, Tile tile_12 is painted in white color, Tile tile_9 is painted in black color, Tile tile_11 is painted in black color, and Tile tile_10 is painted in white color. The available actions are: (change-color ?r ?c ?c2) - modify the color of the robot ?r from ?c to ?c2, (paint-up ?r ?y ?x ?c) - apply color ?c to tile ?y above tile ?x using robot ?r, (paint-down ?r ?y ?x ?c) - paint the tile ?y down from tile ?x with color ?c using the robot ?r, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y upwards, (down ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y going downwards, (right ?r ?x ?y) - move the robot ?r from tile ?x to the right tile ?y, and (left ?r ?x ?y) - move the robot ?r from tile ?x to the left tile ?y.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(paint-down robot1 tile_5 tile_8 white)", "(paint-up robot1 tile_11 tile_8 white)", "(change-color robot1 white white)", "(paint-up robot2 tile_10 tile_7 black)", "(paint-down robot2 tile_4 tile_7 black)"], "no": ["(change-color robot2 black black)", "(up robot2 tile_7 tile_10)", "(up robot1 tile_8 tile_11)"], "opt": "25", "yes": ["(change-color robot1 white black)", "(change-color robot2 black white)", "(right robot1 tile_8 tile_9)", "(down robot1 tile_8 tile_5)", "(down robot2 tile_7 tile_4)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_10) (clear tile_11) (clear tile_12) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_8) (robot-at robot2 tile_7) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": 5310494947176114357, "group": "goal_closer_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_6 is to the right of tile_5, tile_5 is to the right of tile_4, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_1 is down from tile_4, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_4 is down from tile_7, and tile_6 is down from tile_9 Currently, robot robot1 is at tile_6 and holding color black and robot robot2 is at tile_8 and holding color white; tile_7, tile_3, tile_5, tile_4, tile_1, and tile_2 are clear; tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_4 is painted in white color, and Tile tile_9 is painted in black color. The available actions are: (change-color ?r ?c ?c2) - alter the color of the robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - use robot ?r to paint the tile ?y above the tile ?x with the color ?c, (paint-down ?r ?y ?x ?c) - paint the tile ?y down from tile ?x with color ?c using the robot ?r, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y going upwards, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - navigate robot ?r from tile ?x to tile ?y to the right, and (left ?r ?x ?y) - move robot ?r from tile ?x to the left tile tile ?y.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(paint-down robot2 tile_5 tile_8 white)", "(paint-down robot1 tile_3 tile_6 black)", "(change-color robot2 white black)"], "no": ["(left robot2 tile_8 tile_7)", "(change-color robot1 black black)", "(change-color robot2 white white)"], "opt": "14", "yes": ["(down robot1 tile_6 tile_3)", "(change-color robot1 black white)", "(down robot2 tile_8 tile_5)", "(left robot1 tile_6 tile_5)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_7) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_6) (robot-at robot2 tile_8) (robot-has robot1 black) (robot-has robot2 white) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": -3424396222385080053, "group": "goal_closer_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_6 is to the right of tile_5, tile_5 is to the right of tile_4, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_1 is down from tile_4, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_4 is down from tile_7, and tile_6 is down from tile_9 Currently, robot robot2 is at tile_1 and holding color white and robot robot1 is at tile_4 and holding color black; tile_3, tile_5, tile_6, and tile_2 are clear; tile_7 is painted black, tile_8 is painted white, and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_4 is painted in white color, and Tile tile_9 is painted in black color. The available actions are: (change-color ?r ?c ?c2) - modify the color of the robot ?r from ?c to ?c2, (paint-up ?r ?y ?x ?c) - paint the tile ?y above the tile ?x with color ?c using the robot ?r, (paint-down ?r ?y ?x ?c) - use robot ?r to paint the tile ?y downwards from the tile ?x with the color ?c, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y upwards, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move robot ?r from tile ?x to the right tile tile ?y, and (left ?r ?x ?y) - navigate robot ?r from tile ?x to tile ?y to its left.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(change-color robot1 black black)", "(change-color robot1 black white)"], "no": ["(right robot2 tile_1 tile_2)", "(change-color robot2 white white)", "(change-color robot2 white black)"], "opt": "7", "yes": ["(right robot1 tile_4 tile_5)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_2) (clear tile_3) (clear tile_5) (clear tile_6) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_4) (robot-at robot2 tile_1) (robot-has robot1 black) (robot-has robot2 white) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": 137501235137686572, "group": "goal_closer_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 20 tiles. The tiles locations are: tile_7 is to the right of tile_6, tile_20 is to the right of tile_19, tile_14 is to the right of tile_13, tile_5 is to the right of tile_4, tile_13 is to the right of tile_12, tile_17 is to the right of tile_16, tile_9 is to the right of tile_8, tile_18 is to the right of tile_17, tile_4 is to the right of tile_3, tile_10 is to the right of tile_9, tile_12 is to the right of tile_11, tile_3 is to the right of tile_2, tile_15 is to the right of tile_14, tile_2 is to the right of tile_1, tile_8 is to the right of tile_7, and tile_19 is to the right of tile_18. Further, tile_7 is down from tile_12, tile_15 is down from tile_20, tile_6 is down from tile_11, tile_14 is down from tile_19, tile_9 is down from tile_14, tile_3 is down from tile_8, tile_13 is down from tile_18, tile_8 is down from tile_13, tile_2 is down from tile_7, tile_5 is down from tile_10, tile_10 is down from tile_15, tile_11 is down from tile_16, tile_12 is down from tile_17, tile_1 is down from tile_6, and tile_4 is down from tile_9 Currently, robot robot2 is at tile_4 and holding color black and robot robot1 is at tile_2 and holding color white; tile_13, tile_14, tile_7, tile_12, tile_9, tile_5, tile_1, tile_3, tile_17, and tile_8 are clear; tile_20 is painted white, tile_11 is painted black, tile_10 is painted white, tile_6 is painted white, tile_19 is painted black, tile_16 is painted white, tile_18 is painted white, and tile_15 is painted black. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_20 is painted in white color, Tile tile_14 is painted in white color, Tile tile_8 is painted in white color, Tile tile_19 is painted in black color, Tile tile_12 is painted in white color, Tile tile_13 is painted in black color, Tile tile_16 is painted in white color, Tile tile_18 is painted in white color, Tile tile_17 is painted in black color, Tile tile_9 is painted in black color, Tile tile_11 is painted in black color, Tile tile_10 is painted in white color, and Tile tile_15 is painted in black color. The available actions are: (change-color ?r ?c ?c2) - modify the color of the robot ?r from ?c to ?c2, (paint-up ?r ?y ?x ?c) - paint tile ?y above tile ?x with color ?c using robot ?r, (paint-down ?r ?y ?x ?c) - paint tile ?y down from tile ?x with color ?c using robot ?r, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y going upwards, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - navigate robot ?r from tile ?x to tile ?y to the right, and (left ?r ?x ?y) - move robot ?r from tile ?x to the left tile tile ?y.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(change-color robot1 white black)", "(paint-up robot1 tile_7 tile_2 white)", "(change-color robot2 black white)", "(right robot2 tile_4 tile_5)", "(paint-up robot2 tile_9 tile_4 black)"], "no": ["(left robot1 tile_2 tile_1)", "(change-color robot1 white white)", "(change-color robot2 black black)"], "opt": "20", "yes": ["(up robot1 tile_2 tile_7)", "(up robot2 tile_4 tile_9)", "(right robot1 tile_2 tile_3)", "(left robot2 tile_4 tile_3)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_12) (clear tile_13) (clear tile_14) (clear tile_17) (clear tile_3) (clear tile_5) (clear tile_7) (clear tile_8) (clear tile_9) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_10 white) (painted tile_11 black) (painted tile_15 black) (painted tile_16 white) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white) (painted tile_6 white) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_2) (robot-at robot2 tile_4) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": -8838399526941609715, "group": "goal_closer_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 20 tiles. The tiles locations are: tile_7 is to the right of tile_6, tile_20 is to the right of tile_19, tile_14 is to the right of tile_13, tile_5 is to the right of tile_4, tile_13 is to the right of tile_12, tile_17 is to the right of tile_16, tile_9 is to the right of tile_8, tile_18 is to the right of tile_17, tile_4 is to the right of tile_3, tile_10 is to the right of tile_9, tile_12 is to the right of tile_11, tile_3 is to the right of tile_2, tile_15 is to the right of tile_14, tile_2 is to the right of tile_1, tile_8 is to the right of tile_7, and tile_19 is to the right of tile_18. Further, tile_7 is down from tile_12, tile_15 is down from tile_20, tile_6 is down from tile_11, tile_14 is down from tile_19, tile_9 is down from tile_14, tile_3 is down from tile_8, tile_13 is down from tile_18, tile_8 is down from tile_13, tile_2 is down from tile_7, tile_5 is down from tile_10, tile_10 is down from tile_15, tile_11 is down from tile_16, tile_12 is down from tile_17, tile_1 is down from tile_6, and tile_4 is down from tile_9 Currently, robot robot1 is at tile_8 and holding color white and robot robot2 is at tile_2 and holding color white; tile_5, tile_6, tile_4, tile_1, and tile_3 are clear; tile_20 is painted white, tile_13 is painted black, tile_17 is painted black, tile_12 is painted white, tile_11 is painted black, tile_10 is painted white, tile_7 is painted black, tile_15 is painted black, tile_19 is painted black, tile_16 is painted white, tile_14 is painted white, tile_18 is painted white, and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_20 is painted in white color, Tile tile_14 is painted in white color, Tile tile_8 is painted in white color, Tile tile_19 is painted in black color, Tile tile_12 is painted in white color, Tile tile_13 is painted in black color, Tile tile_16 is painted in white color, Tile tile_18 is painted in white color, Tile tile_17 is painted in black color, Tile tile_9 is painted in black color, Tile tile_11 is painted in black color, Tile tile_10 is painted in white color, and Tile tile_15 is painted in black color. The available actions are: (change-color ?r ?c ?c2) - change the color of robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - use robot ?r to paint the tile ?y above the tile ?x with the color ?c, (paint-down ?r ?y ?x ?c) - apply color ?c to tile ?y which is below tile ?x using robot ?r, (up ?r ?x ?y) - move robot ?r up from tile ?x to tile ?y, (down ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y going downwards, (right ?r ?x ?y) - navigate robot ?r from tile ?x to tile ?y to the right, and (left ?r ?x ?y) - move the robot ?r from the tile ?x to the tile on its left ?y.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(right robot2 tile_2 tile_3)", "(change-color robot2 white white)", "(down robot1 tile_8 tile_3)", "(paint-down robot1 tile_3 tile_8 white)"], "no": ["(change-color robot1 white white)", "(change-color robot1 white black)", "(change-color robot2 white black)"], "opt": "4", "yes": ["(left robot2 tile_2 tile_1)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white) (painted tile_7 black) (painted tile_9 black) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_8) (robot-at robot2 tile_2) (robot-has robot1 white) (robot-has robot2 white) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": 5294060632364530814, "group": "goal_closer_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_6 is to the right of tile_5, tile_5 is to the right of tile_4, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_1 is down from tile_4, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_4 is down from tile_7, and tile_6 is down from tile_9 Currently, robot robot1 is at tile_3 and holding color white and robot robot2 is at tile_6 and holding color black; tile_7, tile_9, tile_5, tile_4, tile_1, tile_2, and tile_8 are clear. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_4 is painted in white color, and Tile tile_9 is painted in black color. The available actions are: (change-color ?r ?c ?c2) - modify the color of the robot ?r from ?c to ?c2, (paint-up ?r ?y ?x ?c) - apply color ?c to tile ?y above tile ?x using robot ?r, (paint-down ?r ?y ?x ?c) - apply color ?c to tile ?y which is below tile ?x using robot ?r, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y upwards, (down ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y going downwards, (right ?r ?x ?y) - move robot ?r from tile ?x to the right tile tile ?y, and (left ?r ?x ?y) - move the robot ?r from tile ?x to the left tile ?y.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(change-color robot2 black black)", "(up robot2 tile_6 tile_9)", "(change-color robot2 black white)", "(left robot2 tile_6 tile_5)"], "no": ["(left robot1 tile_3 tile_2)", "(change-color robot1 white white)", "(change-color robot1 white black)"], "opt": "14", "yes": ["(paint-up robot2 tile_9 tile_6 black)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_2) (clear tile_4) (clear tile_5) (clear tile_7) (clear tile_8) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_3) (robot-at robot2 tile_6) (robot-has robot1 white) (robot-has robot2 black) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": -1056806289194457161, "group": "goal_closer_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 3 robots and 20 tiles. The tiles locations are: tile_7 is to the right of tile_6, tile_20 is to the right of tile_19, tile_14 is to the right of tile_13, tile_5 is to the right of tile_4, tile_13 is to the right of tile_12, tile_17 is to the right of tile_16, tile_9 is to the right of tile_8, tile_18 is to the right of tile_17, tile_4 is to the right of tile_3, tile_10 is to the right of tile_9, tile_12 is to the right of tile_11, tile_3 is to the right of tile_2, tile_15 is to the right of tile_14, tile_2 is to the right of tile_1, tile_8 is to the right of tile_7, and tile_19 is to the right of tile_18. Further, tile_7 is down from tile_12, tile_15 is down from tile_20, tile_6 is down from tile_11, tile_14 is down from tile_19, tile_9 is down from tile_14, tile_3 is down from tile_8, tile_13 is down from tile_18, tile_8 is down from tile_13, tile_2 is down from tile_7, tile_5 is down from tile_10, tile_10 is down from tile_15, tile_11 is down from tile_16, tile_12 is down from tile_17, tile_1 is down from tile_6, and tile_4 is down from tile_9 Currently, robot robot2 is at tile_2 and holding color black, robot robot1 is at tile_3 and holding color black, and robot robot3 is at tile_4 and holding color white; tile_14, tile_10, tile_9, tile_5, tile_1, tile_19, tile_15, and tile_8 are clear; tile_20 is painted white, tile_13 is painted black, tile_17 is painted black, tile_12 is painted white, tile_11 is painted black, tile_6 is painted white, tile_7 is painted black, tile_16 is painted white, and tile_18 is painted white. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_20 is painted in white color, Tile tile_14 is painted in white color, Tile tile_8 is painted in white color, Tile tile_19 is painted in black color, Tile tile_12 is painted in white color, Tile tile_13 is painted in black color, Tile tile_16 is painted in white color, Tile tile_18 is painted in white color, Tile tile_17 is painted in black color, Tile tile_9 is painted in black color, Tile tile_11 is painted in black color, Tile tile_10 is painted in white color, and Tile tile_15 is painted in black color. The available actions are: (change-color ?r ?c ?c2) - change the color of robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - paint tile ?y above tile ?x with color ?c using robot ?r, (paint-down ?r ?y ?x ?c) - apply color ?c to tile ?y which is below tile ?x using robot ?r, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y upwards, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y which is to the right of the tile ?x, and (left ?r ?x ?y) - move the robot ?r from the tile ?x to the tile on its left ?y.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(paint-up robot1 tile_8 tile_3 black)", "(paint-up robot3 tile_9 tile_4 white)", "(change-color robot2 black black)"], "no": ["(change-color robot3 white white)", "(change-color robot1 black black)", "(left robot2 tile_2 tile_1)"], "opt": "18", "yes": ["(change-color robot3 white black)", "(change-color robot2 black white)", "(up robot3 tile_4 tile_9)", "(change-color robot1 black white)", "(right robot3 tile_4 tile_5)", "(up robot1 tile_3 tile_8)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-3)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 robot3 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_10) (clear tile_14) (clear tile_15) (clear tile_19) (clear tile_5) (clear tile_8) (clear tile_9) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_20 white) (painted tile_6 white) (painted tile_7 black) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_3) (robot-at robot2 tile_2) (robot-at robot3 tile_4) (robot-has robot1 black) (robot-has robot2 black) (robot-has robot3 white) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": 9045061590954961400, "group": "goal_closer_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 2 robots and 9 tiles. \nThe tiles locations are: tile_6 is to the right of tile_5, tile_5 is to the right of tile_4, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_1 is down from tile_4, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_4 is down from tile_7, and tile_6 is down from tile_9 \nCurrently, robot robot2 is at tile_3 and holding color black and robot robot1 is at tile_5 and holding color white; tile_7, tile_4, tile_6, tile_1, and tile_2 are clear; tile_8 is painted white and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_4 is painted in white color, and Tile tile_9 is painted in black color. The available actions are: (change-color ?r ?c ?c2) - modify the color of the robot ?r from ?c to ?c2, (paint-up ?r ?y ?x ?c) - apply color ?c to tile ?y above tile ?x using robot ?r, (paint-down ?r ?y ?x ?c) - use robot ?r to paint the tile ?y downwards from the tile ?x with the color ?c, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y going upwards, (down ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y going downwards, (right ?r ?x ?y) - move robot ?r from tile ?x to the right tile tile ?y, and (left ?r ?x ?y) - move robot ?r from tile ?x to the left tile tile ?y.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(paint-down robot1 tile_2 tile_5 white)", "(paint-up robot2 tile_6 tile_3 black)"], "no": ["(up robot2 tile_3 tile_6)", "(change-color robot2 black black)", "(change-color robot1 white white)"], "opt": "11", "yes": ["(change-color robot1 white black)", "(left robot2 tile_3 tile_2)", "(change-color robot2 black white)", "(right robot1 tile_5 tile_6)", "(down robot1 tile_5 tile_2)", "(left robot1 tile_5 tile_4)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_2) (clear tile_4) (clear tile_6) (clear tile_7) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_8 white) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_5) (robot-at robot2 tile_3) (robot-has robot1 white) (robot-has robot2 black) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": -3079910840192405070, "group": "goal_closer_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 2 robots and 12 tiles. \nThe tiles locations are: tile_11 is to the right of tile_10, tile_6 is to the right of tile_5, tile_5 is to the right of tile_4, tile_9 is to the right of tile_8, tile_12 is to the right of tile_11, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_9 is down from tile_12, tile_1 is down from tile_4, tile_8 is down from tile_11, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_7 is down from tile_10, tile_4 is down from tile_7, and tile_6 is down from tile_9 \nCurrently, robot robot1 is at tile_3 and holding color white and robot robot2 is at tile_4 and holding color white; tile_1 and tile_2 are clear; tile_6 is painted white, tile_7 is painted black, tile_8 is painted white, tile_5 is painted black, tile_12 is painted white, tile_9 is painted black, tile_11 is painted black, and tile_10 is painted white. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_4 is painted in white color, Tile tile_12 is painted in white color, Tile tile_9 is painted in black color, Tile tile_11 is painted in black color, and Tile tile_10 is painted in white color. The available actions are: (change-color ?r ?c ?c2) - change the color of robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - paint the tile ?y above the tile ?x with color ?c using the robot ?r, (paint-down ?r ?y ?x ?c) - paint tile ?y down from tile ?x with color ?c using robot ?r, (up ?r ?x ?y) - move robot ?r up from tile ?x to tile ?y, (down ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y going downwards, (right ?r ?x ?y) - move robot ?r from tile ?x to the right tile tile ?y, and (left ?r ?x ?y) - move the robot ?r from the tile ?x to the tile on its left ?y.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(paint-down robot2 tile_1 tile_4 white)", "(change-color robot1 white white)", "(left robot1 tile_3 tile_2)"], "no": ["(change-color robot2 white white)", "(change-color robot1 white black)", "(change-color robot2 white black)"], "opt": "2", "yes": ["(down robot2 tile_4 tile_1)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_2) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_3) (robot-at robot2 tile_4) (robot-has robot1 white) (robot-has robot2 white) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": 3684575812818374602, "group": "goal_closer_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 2 robots and 12 tiles. \nThe tiles locations are: tile_11 is to the right of tile_10, tile_6 is to the right of tile_5, tile_5 is to the right of tile_4, tile_9 is to the right of tile_8, tile_12 is to the right of tile_11, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_9 is down from tile_12, tile_1 is down from tile_4, tile_8 is down from tile_11, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_7 is down from tile_10, tile_4 is down from tile_7, and tile_6 is down from tile_9 \nCurrently, robot robot1 is at tile_9 and holding color white and robot robot2 is at tile_2 and holding color white; tile_3, tile_5, tile_6, tile_1, and tile_8 are clear; tile_7 is painted black, tile_4 is painted white, tile_12 is painted white, tile_11 is painted black, and tile_10 is painted white. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_4 is painted in white color, Tile tile_12 is painted in white color, Tile tile_9 is painted in black color, Tile tile_11 is painted in black color, and Tile tile_10 is painted in white color. The available actions are: (change-color ?r ?c ?c2) - modify the color of the robot ?r from ?c to ?c2, (paint-up ?r ?y ?x ?c) - paint the tile ?y above the tile ?x with color ?c using the robot ?r, (paint-down ?r ?y ?x ?c) - use robot ?r to paint the tile ?y downwards from the tile ?x with the color ?c, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y upwards, (down ?r ?x ?y) - move the robot ?r from the tile ?x to the tile ?y going downwards, (right ?r ?x ?y) - move robot ?r from tile ?x to the right tile tile ?y, and (left ?r ?x ?y) - move the robot ?r from tile ?x to the left tile ?y.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(paint-up robot2 tile_5 tile_2 white)", "(paint-down robot1 tile_6 tile_9 white)"], "no": ["(change-color robot2 white white)", "(change-color robot1 white white)", "(left robot2 tile_2 tile_1)"], "opt": "11", "yes": ["(right robot2 tile_2 tile_3)", "(up robot2 tile_2 tile_5)", "(change-color robot1 white black)", "(left robot1 tile_9 tile_8)", "(down robot1 tile_9 tile_6)", "(change-color robot2 white black)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_3) (clear tile_5) (clear tile_6) (clear tile_8) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_4 white) (painted tile_7 black) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_9) (robot-at robot2 tile_2) (robot-has robot1 white) (robot-has robot2 white) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": 3756329149424287912, "group": "goal_closer_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 2 robots and 9 tiles. \nThe tiles locations are: tile_6 is to the right of tile_5, tile_5 is to the right of tile_4, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_1 is down from tile_4, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_4 is down from tile_7, and tile_6 is down from tile_9 \nCurrently, robot robot2 is at tile_3 and holding color white and robot robot1 is at tile_4 and holding color black; tile_1 and tile_2 are clear; tile_6 is painted white, tile_7 is painted black, tile_8 is painted white, tile_5 is painted black, and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_4 is painted in white color, and Tile tile_9 is painted in black color. The available actions are: (change-color ?r ?c ?c2) - change the color of robot ?r from color ?c to color ?c2, (paint-up ?r ?y ?x ?c) - use robot ?r to paint the tile ?y above the tile ?x with the color ?c, (paint-down ?r ?y ?x ?c) - paint the tile ?y down from tile ?x with color ?c using the robot ?r, (up ?r ?x ?y) - move the robot ?r from tile ?x to tile ?y upwards, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - navigate robot ?r from tile ?x to tile ?y to the right, and (left ?r ?x ?y) - move the robot ?r from the tile ?x to the tile on its left ?y.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(change-color robot2 white white)", "(paint-down robot1 tile_1 tile_4 black)"], "no": ["(change-color robot1 black black)", "(left robot2 tile_3 tile_2)", "(change-color robot2 white black)"], "opt": "3", "yes": ["(change-color robot1 black white)", "(down robot1 tile_4 tile_1)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_2) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_4) (robot-at robot2 tile_3) (robot-has robot1 black) (robot-has robot2 white) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": 5486418990315461297, "group": "goal_closer_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 2 robots and 9 tiles. \nThe tiles locations are: tile_6 is to the right of tile_5, tile_5 is to the right of tile_4, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_1 is down from tile_4, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_4 is down from tile_7, and tile_6 is down from tile_9 \nCurrently, robot robot1 is at tile_5 and holding color white and robot robot2 is at tile_2 and holding color black; tile_3, tile_4, tile_6, and tile_1 are clear; tile_7 is painted black, tile_8 is painted white, and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_4 is painted in white color, and Tile tile_9 is painted in black color. The available actions are: (change-color ?r ?c ?c2) - modify the color of the robot ?r from ?c to ?c2, (paint-up ?r ?y ?x ?c) - paint tile ?y above tile ?x with color ?c using robot ?r, (paint-down ?r ?y ?x ?c) - paint the tile ?y down from tile ?x with color ?c using the robot ?r, (up ?r ?x ?y) - move the robot ?r up from tile ?x to tile ?y, (down ?r ?x ?y) - move robot ?r down from tile ?x to tile ?y, (right ?r ?x ?y) - navigate robot ?r from tile ?x to tile ?y to the right, and (left ?r ?x ?y) - move robot ?r from tile ?x to the left tile tile ?y.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(right robot2 tile_2 tile_3)", "(change-color robot1 white white)", "(change-color robot1 white black)"], "no": ["(change-color robot2 black white)", "(change-color robot2 black black)", "(left robot2 tile_2 tile_1)"], "opt": "7", "yes": ["(left robot1 tile_5 tile_4)", "(right robot1 tile_5 tile_6)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_3) (clear tile_4) (clear tile_6) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_5) (robot-at robot2 tile_2) (robot-has robot1 white) (robot-has robot2 black) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
