{"id": -2717395259725572867, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 12 tiles. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_12 is to the right of tile_11, tile_11 is to the right of tile_10, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_5 is down from tile_8, tile_9 is down from tile_12, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_7 is down from tile_10, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_4 and holding color white and robot robot1 is at tile_7 and holding color white; tile_10, tile_5, tile_2, tile_1, tile_6, tile_12, tile_11, tile_3, tile_9, and tile_8 are clear. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_10 is painted in white color, Tile tile_5 is painted in black color, Tile tile_11 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, Tile tile_12 is painted in white color, and Tile tile_4 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot robot2 is at tile_9 location. B. tile_7 is clear. C. Tile tile_10 is painted in black color. D. Robot robot2 is at tile_5 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot2 is at tile_9 location", "tile_7 is clear", "Tile tile_10 is painted in black color", "Robot robot2 is at tile_5 location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -8749527647283597157, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_5 is down from tile_8, tile_6 is down from tile_9, tile_3 is down from tile_6, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_1 and holding color white and robot robot1 is at tile_5 and holding color white; tile_2, tile_6, tile_3, and tile_9 are clear; tile_7 is painted black, tile_8 is painted white, and tile_4 is painted white. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot robot1 is at tile_2 location. B. tile_5 is clear. C. Robot robot2 is at tile_2 location. D. Tile tile_2 is painted in white color.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is at tile_2 location", "tile_5 is clear", "Robot robot2 is at tile_2 location", "Tile tile_2 is painted in white color"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 6627747300641254593, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_5 is down from tile_8, tile_6 is down from tile_9, tile_3 is down from tile_6, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_6 and holding color black and robot robot1 is at tile_4 and holding color white; tile_2, tile_1, and tile_3 are clear; tile_7 is painted black, tile_8 is painted white, tile_5 is painted black, and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Tile tile_2 is painted in white color. B. Robot robot2 is at tile_2 location. C. tile_6 is clear. D. Tile tile_4 is painted in black color.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Tile tile_2 is painted in white color", "Robot robot2 is at tile_2 location", "tile_6 is clear", "Tile tile_4 is painted in black color"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -677794193056967264, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 20 tiles. The tiles locations are: tile_4 is to the right of tile_3, tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_19 is to the right of tile_18, tile_18 is to the right of tile_17, tile_12 is to the right of tile_11, tile_9 is to the right of tile_8, tile_14 is to the right of tile_13, tile_17 is to the right of tile_16, tile_10 is to the right of tile_9, tile_2 is to the right of tile_1, tile_20 is to the right of tile_19, tile_13 is to the right of tile_12, and tile_8 is to the right of tile_7. Further, tile_8 is down from tile_13, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_9 is down from tile_14, tile_12 is down from tile_17, tile_7 is down from tile_12, tile_10 is down from tile_15, tile_11 is down from tile_16, tile_5 is down from tile_10, tile_6 is down from tile_11, tile_1 is down from tile_6, tile_3 is down from tile_8, tile_14 is down from tile_19, tile_15 is down from tile_20, and tile_13 is down from tile_18 Currently, robot robot1 is at tile_4 and holding color white and robot robot2 is at tile_7 and holding color white; tile_12, tile_14, tile_5, tile_1, tile_9, tile_2, tile_19, and tile_3 are clear; tile_20 is painted white, tile_15 is painted black, tile_6 is painted white, tile_17 is painted black, tile_10 is painted white, tile_13 is painted black, tile_11 is painted black, tile_16 is painted white, tile_8 is painted white, and tile_18 is painted white. The goal is to reach a state where the following facts hold: Tile tile_15 is painted in black color, Tile tile_17 is painted in black color, Tile tile_14 is painted in white color, Tile tile_8 is painted in white color, Tile tile_7 is painted in black color, Tile tile_10 is painted in white color, Tile tile_13 is painted in black color, Tile tile_11 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, Tile tile_12 is painted in white color, Tile tile_16 is painted in white color, Tile tile_19 is painted in black color, Tile tile_20 is painted in white color, and Tile tile_18 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Tile tile_20 is painted in black color. B. Tile tile_5 is painted in white color. C. tile_7 is clear. D. Tile tile_17 is painted in white color.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Tile tile_20 is painted in black color", "Tile tile_5 is painted in white color", "tile_7 is clear", "Tile tile_17 is painted in white color"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -6795096273383498461, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_5 is down from tile_8, tile_6 is down from tile_9, tile_3 is down from tile_6, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_5 and holding color black and robot robot1 is at tile_1 and holding color white; tile_2, tile_4, tile_3, and tile_8 are clear; tile_7 is painted black, tile_6 is painted white, and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot robot2 is at tile_6 location. B. Robot robot1 is at tile_3 location. C. Robot robot2 is at tile_4 location. D. tile_5 is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot2 is at tile_6 location", "Robot robot1 is at tile_3 location", "Robot robot2 is at tile_4 location", "tile_5 is clear"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 6266842166761872358, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 24 tiles. The tiles locations are: tile_3 is to the right of tile_2, tile_23 is to the right of tile_22, tile_22 is to the right of tile_21, tile_10 is to the right of tile_9, tile_20 is to the right of tile_19, tile_8 is to the right of tile_7, tile_18 is to the right of tile_17, tile_9 is to the right of tile_8, tile_14 is to the right of tile_13, tile_6 is to the right of tile_5, tile_17 is to the right of tile_16, tile_21 is to the right of tile_20, tile_4 is to the right of tile_3, tile_12 is to the right of tile_11, tile_11 is to the right of tile_10, tile_24 is to the right of tile_23, tile_2 is to the right of tile_1, tile_16 is to the right of tile_15, tile_5 is to the right of tile_4, and tile_15 is to the right of tile_14. Further, tile_9 is down from tile_15, tile_10 is down from tile_16, tile_15 is down from tile_21, tile_17 is down from tile_23, tile_13 is down from tile_19, tile_5 is down from tile_11, tile_2 is down from tile_8, tile_7 is down from tile_13, tile_6 is down from tile_12, tile_18 is down from tile_24, tile_4 is down from tile_10, tile_12 is down from tile_18, tile_8 is down from tile_14, tile_1 is down from tile_7, tile_3 is down from tile_9, tile_11 is down from tile_17, tile_14 is down from tile_20, and tile_16 is down from tile_22 Currently, robot robot2 is at tile_5 and holding color white and robot robot1 is at tile_11 and holding color white; tile_17, tile_10, tile_6, tile_1, tile_15, tile_4, tile_9, tile_2, and tile_3 are clear; tile_23 is painted white, tile_19 is painted white, tile_24 is painted black, tile_8 is painted black, tile_7 is painted white, tile_20 is painted black, tile_12 is painted black, tile_14 is painted white, tile_13 is painted black, tile_16 is painted white, tile_22 is painted black, tile_21 is painted white, and tile_18 is painted white. The goal is to reach a state where the following facts hold: Tile tile_15 is painted in black color, Tile tile_17 is painted in black color, Tile tile_14 is painted in white color, Tile tile_8 is painted in black color, Tile tile_7 is painted in white color, Tile tile_20 is painted in black color, Tile tile_23 is painted in white color, Tile tile_11 is painted in white color, Tile tile_12 is painted in black color, Tile tile_19 is painted in white color, Tile tile_21 is painted in white color, Tile tile_13 is painted in black color, Tile tile_10 is painted in black color, Tile tile_16 is painted in white color, Tile tile_22 is painted in black color, Tile tile_9 is painted in white color, Tile tile_18 is painted in white color, and Tile tile_24 is painted in black color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. tile_11 is clear. B. Tile tile_2 is painted in black color. C. Robot robot2 is at tile_11 location. D. Robot robot1 is at tile_13 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["tile_11 is clear", "Tile tile_2 is painted in black color", "Robot robot2 is at tile_11 location", "Robot robot1 is at tile_13 location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -7641327919687212661, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 12 tiles. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_12 is to the right of tile_11, tile_11 is to the right of tile_10, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_5 is down from tile_8, tile_9 is down from tile_12, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_7 is down from tile_10, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_9 and holding color white and robot robot1 is at tile_4 and holding color black; tile_7, tile_5, tile_2, tile_1, tile_6, tile_12, tile_11, tile_3, and tile_8 are clear; tile_10 is painted white. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_10 is painted in white color, Tile tile_5 is painted in black color, Tile tile_11 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, Tile tile_12 is painted in white color, and Tile tile_4 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Tile tile_2 is painted in white color. B. Robot robot2 is at tile_12 location. C. Robot robot1 is at tile_11 location. D. tile_9 is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Tile tile_2 is painted in white color", "Robot robot2 is at tile_12 location", "Robot robot1 is at tile_11 location", "tile_9 is clear"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 4059588696135519664, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 20 tiles. The tiles locations are: tile_4 is to the right of tile_3, tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_19 is to the right of tile_18, tile_18 is to the right of tile_17, tile_12 is to the right of tile_11, tile_9 is to the right of tile_8, tile_14 is to the right of tile_13, tile_17 is to the right of tile_16, tile_10 is to the right of tile_9, tile_2 is to the right of tile_1, tile_20 is to the right of tile_19, tile_13 is to the right of tile_12, and tile_8 is to the right of tile_7. Further, tile_8 is down from tile_13, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_9 is down from tile_14, tile_12 is down from tile_17, tile_7 is down from tile_12, tile_10 is down from tile_15, tile_11 is down from tile_16, tile_5 is down from tile_10, tile_6 is down from tile_11, tile_1 is down from tile_6, tile_3 is down from tile_8, tile_14 is down from tile_19, tile_15 is down from tile_20, and tile_13 is down from tile_18 Currently, robot robot1 is at tile_10 and holding color black and robot robot2 is at tile_2 and holding color white; tile_17, tile_6, tile_12, tile_14, tile_5, tile_1, tile_4, tile_9, tile_7, tile_19, tile_11, and tile_3 are clear; tile_20 is painted white, tile_15 is painted black, tile_13 is painted black, tile_16 is painted white, tile_8 is painted white, and tile_18 is painted white. The goal is to reach a state where the following facts hold: Tile tile_15 is painted in black color, Tile tile_17 is painted in black color, Tile tile_14 is painted in white color, Tile tile_8 is painted in white color, Tile tile_7 is painted in black color, Tile tile_10 is painted in white color, Tile tile_13 is painted in black color, Tile tile_11 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, Tile tile_12 is painted in white color, Tile tile_16 is painted in white color, Tile tile_19 is painted in black color, Tile tile_20 is painted in white color, and Tile tile_18 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot robot2 is at tile_10 location. B. Tile tile_11 is painted in white color. C. tile_10 is clear. D. Tile tile_16 is painted in black color.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot2 is at tile_10 location", "Tile tile_11 is painted in white color", "tile_10 is clear", "Tile tile_16 is painted in black color"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -6079728576040432942, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 12 tiles. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_12 is to the right of tile_11, tile_11 is to the right of tile_10, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_5 is down from tile_8, tile_9 is down from tile_12, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_7 is down from tile_10, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_2 and holding color black and robot robot1 is at tile_4 and holding color black; tile_5, tile_1, tile_6, tile_3, tile_9, and tile_8 are clear; tile_7 is painted black, tile_10 is painted white, tile_11 is painted black, and tile_12 is painted white. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_10 is painted in white color, Tile tile_5 is painted in black color, Tile tile_11 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, Tile tile_12 is painted in white color, and Tile tile_4 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot robot1 is at tile_3 location. B. tile_10 is clear. C. Robot robot1 is at tile_10 location. D. tile_4 is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is at tile_3 location", "tile_10 is clear", "Robot robot1 is at tile_10 location", "tile_4 is clear"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 8603784826906026050, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_5 is down from tile_8, tile_6 is down from tile_9, tile_3 is down from tile_6, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_1 and holding color white and robot robot1 is at tile_2 and holding color black; tile_6, tile_4, and tile_3 are clear; tile_7 is painted black, tile_8 is painted white, tile_5 is painted black, and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Tile tile_1 is painted in white color. B. Robot robot1 is at tile_4 location. C. Tile tile_2 is painted in black color. D. Tile tile_4 is painted in white color.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Tile tile_1 is painted in white color", "Robot robot1 is at tile_4 location", "Tile tile_2 is painted in black color", "Tile tile_4 is painted in white color"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 826987187166569861, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 12 tiles. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_12 is to the right of tile_11, tile_11 is to the right of tile_10, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_5 is down from tile_8, tile_9 is down from tile_12, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_7 is down from tile_10, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_1 and holding color black and robot robot1 is at tile_11 and holding color white; tile_10, tile_7, tile_5, tile_2, tile_6, tile_4, tile_12, tile_3, tile_9, and tile_8 are clear. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_10 is painted in white color, Tile tile_5 is painted in black color, Tile tile_11 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, Tile tile_12 is painted in white color, and Tile tile_4 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Tile tile_4 is painted in black color. B. tile_11 is clear. C. Tile tile_11 is painted in white color. D. Robot robot1 is at tile_10 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Tile tile_4 is painted in black color", "tile_11 is clear", "Tile tile_11 is painted in white color", "Robot robot1 is at tile_10 location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 2210628225511974455, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_5 is down from tile_8, tile_6 is down from tile_9, tile_3 is down from tile_6, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_1 and holding color black and robot robot1 is at tile_3 and holding color white; tile_7, tile_2, tile_6, and tile_4 are clear; tile_8 is painted white, tile_5 is painted black, and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Tile tile_5 is painted in white color. B. Robot robot2 is at tile_7 location. C. Robot robot1 is at tile_4 location. D. Tile tile_6 is painted in white color.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Tile tile_5 is painted in white color", "Robot robot2 is at tile_7 location", "Robot robot1 is at tile_4 location", "Tile tile_6 is painted in white color"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -5203146508033736713, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 16 tiles. The tiles locations are: tile_4 is to the right of tile_3, tile_3 is to the right of tile_2, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_12 is to the right of tile_11, tile_11 is to the right of tile_10, tile_14 is to the right of tile_13, tile_6 is to the right of tile_5, tile_10 is to the right of tile_9, tile_2 is to the right of tile_1, tile_16 is to the right of tile_15, and tile_8 is to the right of tile_7. Further, tile_8 is down from tile_12, tile_5 is down from tile_9, tile_2 is down from tile_6, tile_12 is down from tile_16, tile_4 is down from tile_8, tile_9 is down from tile_13, tile_10 is down from tile_14, tile_7 is down from tile_11, tile_3 is down from tile_7, tile_11 is down from tile_15, tile_6 is down from tile_10, and tile_1 is down from tile_5 Currently, robot robot2 is at tile_7 and holding color black and robot robot1 is at tile_1 and holding color white; tile_2, tile_15, tile_4, tile_12, tile_11, tile_3, and tile_8 are clear; tile_14 is painted black, tile_10 is painted white, tile_6 is painted black, tile_9 is painted black, tile_5 is painted white, tile_13 is painted white, and tile_16 is painted black. The goal is to reach a state where the following facts hold: Tile tile_15 is painted in white color, Tile tile_14 is painted in black color, Tile tile_8 is painted in black color, Tile tile_7 is painted in white color, Tile tile_10 is painted in white color, Tile tile_6 is painted in black color, Tile tile_11 is painted in black color, Tile tile_9 is painted in black color, Tile tile_5 is painted in white color, Tile tile_12 is painted in white color, Tile tile_13 is painted in white color, and Tile tile_16 is painted in black color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Tile tile_1 is painted in white color. B. Robot robot1 is at tile_10 location. C. Tile tile_7 is painted in black color. D. tile_7 is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Tile tile_1 is painted in white color", "Robot robot1 is at tile_10 location", "Tile tile_7 is painted in black color", "tile_7 is clear"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -5678293868374759713, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 16 tiles. The tiles locations are: tile_4 is to the right of tile_3, tile_3 is to the right of tile_2, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_12 is to the right of tile_11, tile_11 is to the right of tile_10, tile_14 is to the right of tile_13, tile_6 is to the right of tile_5, tile_10 is to the right of tile_9, tile_2 is to the right of tile_1, tile_16 is to the right of tile_15, and tile_8 is to the right of tile_7. Further, tile_8 is down from tile_12, tile_5 is down from tile_9, tile_2 is down from tile_6, tile_12 is down from tile_16, tile_4 is down from tile_8, tile_9 is down from tile_13, tile_10 is down from tile_14, tile_7 is down from tile_11, tile_3 is down from tile_7, tile_11 is down from tile_15, tile_6 is down from tile_10, and tile_1 is down from tile_5 Currently, robot robot2 is at tile_7 and holding color black and robot robot1 is at tile_6 and holding color white; tile_5, tile_2, tile_1, tile_15, tile_4, tile_12, tile_11, tile_3, tile_9, and tile_8 are clear; tile_14 is painted black, tile_10 is painted white, tile_13 is painted white, and tile_16 is painted black. The goal is to reach a state where the following facts hold: Tile tile_15 is painted in white color, Tile tile_14 is painted in black color, Tile tile_8 is painted in black color, Tile tile_7 is painted in white color, Tile tile_10 is painted in white color, Tile tile_6 is painted in black color, Tile tile_11 is painted in black color, Tile tile_9 is painted in black color, Tile tile_5 is painted in white color, Tile tile_12 is painted in white color, Tile tile_13 is painted in white color, and Tile tile_16 is painted in black color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Tile tile_4 is painted in black color. B. tile_7 is clear. C. Tile tile_11 is painted in white color. D. Tile tile_10 is painted in black color.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Tile tile_4 is painted in black color", "tile_7 is clear", "Tile tile_11 is painted in white color", "Tile tile_10 is painted in black color"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -8413916920799892465, "group": "landmarks_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_5 is down from tile_8, tile_6 is down from tile_9, tile_3 is down from tile_6, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_2 and holding color black and robot robot1 is at tile_3 and holding color white; tile_5 and tile_1 are clear; tile_7 is painted black, tile_8 is painted white, tile_6 is painted white, tile_9 is painted black, and tile_4 is painted white. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Tile tile_1 is painted in white color. B. Robot robot2 is at tile_9 location. C. Tile tile_5 is painted in black color. D. tile_8 is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Tile tile_1 is painted in white color", "Robot robot2 is at tile_9 location", "Tile tile_5 is painted in black color", "tile_8 is clear"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
