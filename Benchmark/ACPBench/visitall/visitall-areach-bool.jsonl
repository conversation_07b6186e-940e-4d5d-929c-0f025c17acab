{"id": 542097817266415445, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y2, loc-x0-y4, and loc-x2-y3. Currently, the robot is in place loc-x3-y2.The following places have been visited: loc-x0-y0, loc-x3-y2, loc-x1-y1, loc-x3-y3, loc-x0-y2, loc-x2-y0, loc-x1-y0, loc-x3-y0, loc-x3-y1, loc-x0-y1, loc-x3-y4, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"push box to place loc-x3-y4 from place loc-x2-y4\" can be applied?", "answer": "no"}
{"id": 5002196130158764572, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x0-y0, loc-x1-y4, loc-x1-y1, loc-x2-y2, loc-x2-y0, loc-x3-y2, loc-x1-y2, loc-x1-y3, loc-x1-y0, loc-x3-y1, loc-x3-y4, loc-x2-y1, loc-x0-y2, loc-x0-y4, loc-x0-y3, loc-x3-y3, loc-x3-y0, loc-x0-y1, and loc-x2-y4.", "question": "Is it possible to transition to a state where the action \"check that position loc-x2-y4 is connected to position loc-x3-y4\" can be applied?", "answer": "no"}
{"id": -676010873499900832, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x0-y4.The following places have been visited: loc-x0-y0, loc-x1-y2, loc-x1-y3, loc-x1-y1, loc-x0-y4, loc-x0-y3, loc-x0-y2, loc-x2-y0, loc-x1-y0, loc-x3-y0, loc-x3-y1, loc-x0-y1, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"travel from loc-x0-y2 to loc-x0-y1\" can be applied?", "answer": "yes"}
{"id": 4730290038882355459, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x0-y3.The following places have been visited: loc-x0-y0, loc-x0-y3, loc-x0-y2, loc-x1-y0, and loc-x0-y1.", "question": "Is it possible to transition to a state where the action \"push box to place loc-x0-y2 from place loc-x1-y2\" can be applied?", "answer": "no"}
{"id": 5813408539699212715, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x0-y2, loc-x1-y0, and loc-x3-y3. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x1-y2, loc-x1-y3, loc-x0-y3, loc-x2-y2, loc-x2-y0, loc-x3-y0, loc-x3-y2, loc-x2-y3, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"push box to place loc-x1-y2 from place loc-x1-y3\" can be applied?", "answer": "no"}
{"id": 7974901258154901219, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x2-y1.The following places have been visited: loc-x2-y0, loc-x3-y0, loc-x1-y0, loc-x3-y1, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"check that position loc-x2-y1 is connected to position loc-x3-y1\" can be applied?", "answer": "no"}
{"id": 3232358558587572451, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x2-y0.The following places have been visited: loc-x1-y2, loc-x1-y3, loc-x1-y1, loc-x3-y3, loc-x2-y2, loc-x2-y0, loc-x3-y0, loc-x3-y2, loc-x2-y3, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"check that position loc-x2-y0 is connected to position loc-x2-y1\" can be applied?", "answer": "no"}
{"id": -7046691313528293343, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y2, loc-x0-y4, and loc-x2-y3. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x0-y0, loc-x1-y1, loc-x2-y2, loc-x0-y2, loc-x2-y0, loc-x1-y0, loc-x3-y0, loc-x3-y1, loc-x0-y1, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"check that position loc-x2-y1 is connected to position loc-x3-y1\" can be applied?", "answer": "no"}
{"id": -7701635755153878241, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x0-y2, loc-x1-y0, and loc-x3-y3. Currently, the robot is in place loc-x2-y3.The following places have been visited: loc-x0-y0, loc-x1-y2, loc-x1-y1, loc-x2-y2, loc-x2-y0, loc-x3-y0, loc-x3-y2, loc-x0-y1, loc-x2-y3, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"Please move to loc-x3-y2 position from loc-x2-y2 position\" can be applied?", "answer": "yes"}
{"id": -5407966406891222591, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x3-y3.The following places have been visited: loc-x0-y0, loc-x1-y2, loc-x1-y3, loc-x3-y2, loc-x1-y1, loc-x0-y3, loc-x3-y3, loc-x0-y2, loc-x2-y0, loc-x1-y0, loc-x3-y0, loc-x3-y1, loc-x0-y1, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"navigate from loc-x1-y3 to loc-x0-y3\" can be applied?", "answer": "yes"}
{"id": -8399703894370865909, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3, loc-x0-y0, and loc-x3-y3. Currently, the robot is in place loc-x1-y2.The following places have been visited: loc-x1-y2, loc-x3-y2, loc-x1-y1, loc-x2-y2, loc-x0-y2, loc-x2-y0, loc-x1-y0, loc-x3-y0, loc-x3-y1, loc-x0-y1, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"go to loc-x0-y1 from loc-x0-y2\" can be applied?", "answer": "yes"}
{"id": -5086241473530925403, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. Currently, the robot is in place loc-x0-y1.The following places have been visited: loc-x0-y0, loc-x1-y2, loc-x1-y3, loc-x3-y2, loc-x1-y1, loc-x0-y3, loc-x3-y3, loc-x2-y2, loc-x0-y2, loc-x2-y0, loc-x1-y0, loc-x3-y0, loc-x0-y1, loc-x2-y3, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"push box to place loc-x0-y2 from place loc-x0-y1\" can be applied?", "answer": "no"}
{"id": 509803783501305044, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y0 and loc-x0-y3. Currently, the robot is in place loc-x2-y3.The following places have been visited: loc-x1-y1, loc-x3-y3, loc-x2-y2, loc-x2-y0, loc-x1-y0, loc-x3-y0, loc-x3-y1, loc-x3-y2, loc-x2-y3, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"transition from the current position loc-x3-y1 to the next position loc-x3-y0\" can be applied?", "answer": "yes"}
{"id": -5822087567347881136, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y0 and loc-x0-y3. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x1-y2, loc-x1-y3, loc-x1-y1, loc-x3-y3, loc-x2-y2, loc-x0-y2, loc-x3-y2, loc-x0-y1, and loc-x2-y3.", "question": "Is it possible to transition to a state where the action \"check that position loc-x1-y2 is connected to position loc-x0-y2\" can be applied?", "answer": "no"}
{"id": -6314723815262668782, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. Currently, the robot is in place loc-x2-y0.The following places have been visited: loc-x1-y3, loc-x0-y3, loc-x3-y3, loc-x2-y2, loc-x2-y0, loc-x3-y2, loc-x2-y3, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"Please move to loc-x2-y0 position from loc-x3-y0 position\" can be applied?", "answer": "yes"}
