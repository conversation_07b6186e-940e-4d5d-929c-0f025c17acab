{"id": -3755988908408204914, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c7 on board. The cars are at locations as follows: c11, c15, c32, c19, c2, c33, and c49 are at l1; c44, c45, c36, c10, c47, c1, c16, c18, c24, c42, c4, c17, and c48 are at l0; c31, c12, c43, c25, c37, c20, c34, c23, c13, and c6 are at l3; c46, c14, c0, c28, c30, c27, c5, c8, c9, c41, c38, and c29 are at l4; c39, c40, c3, c21, c22, c35, and c26 are at l2. The goal is to reach a state where the following facts hold: Car c11 is at location l1, Car c44 is at location l0, Car c45 is at location l0, Car c31 is at location l3, Car c12 is at location l3, Car c36 is at location l0, Car c46 is at location l4, Car c14 is at location l4, Car c39 is at location l2, Car c28 is at location l4, Car c15 is at location l1, Car c25 is at location l3, Car c30 is at location l4, Car c27 is at location l4, Car c5 is at location l4, Car c10 is at location l0, Car c32 is at location l1, Car c37 is at location l3, Car c40 is at location l2, Car c20 is at location l3, Car c19 is at location l1, Car c34 is at location l3, Car c8 is at location l4, Car c9 is at location l4, Car c23 is at location l3, Car c47 is at location l0, Car c3 is at location l2, Car c21 is at location l2, Car c1 is at location l0, Car c41 is at location l4, Car c16 is at location l0, Car c7 is at location l0, Car c22 is at location l2, Car c43 is at location l2, Car c0 is at location l2, Car c2 is at location l1, Car c18 is at location l0, Car c13 is at location l3, Car c33 is at location l1, Car c6 is at location l3, Car c35 is at location l2, Car c26 is at location l2, Car c38 is at location l4, Car c49 is at location l1, Car c24 is at location l0, Car c42 is at location l0, Car c4 is at location l0, Car c29 is at location l4, Car c17 is at location l0, and Car c48 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? There are no cars on the ferry", "answer": "yes"}
{"id": 3617573445680283767, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c44, c14, c36, c8, c2, c0, c19, c13, c38, c11, c12, c20, c40, c25, c9, c29, c21, c43, c30, c22, and c26 are at l0; c23, c48, c7, c15, c39, c31, c32, c45, c27, c3, c17, c34, c28, c1, c33, c46, c4, c42, c16, c49, c37, c35, c6, c18, c47, c5, c10, c41, and c24 are at l1. The goal is to reach a state where the following facts hold: Car c45 is at location l0, Car c44 is at location l0, Car c14 is at location l0, Car c23 is at location l1, Car c37 is at location l0, Car c36 is at location l0, Car c48 is at location l1, Car c8 is at location l0, Car c7 is at location l1, Car c15 is at location l1, Car c2 is at location l0, Car c39 is at location l1, Car c46 is at location l0, Car c31 is at location l1, Car c32 is at location l1, Car c0 is at location l0, Car c19 is at location l0, Car c13 is at location l0, Car c38 is at location l0, Car c27 is at location l1, Car c3 is at location l1, Car c17 is at location l1, Car c34 is at location l1, Car c11 is at location l0, Car c12 is at location l0, Car c20 is at location l0, Car c28 is at location l1, Car c40 is at location l0, Car c25 is at location l0, Car c1 is at location l1, Car c9 is at location l0, Car c29 is at location l0, Car c33 is at location l1, Car c21 is at location l0, Car c4 is at location l1, Car c42 is at location l1, Car c43 is at location l0, Car c16 is at location l1, Car c49 is at location l1, Car c35 is at location l1, Car c30 is at location l0, Car c6 is at location l1, Car c18 is at location l1, Car c47 is at location l1, Car c5 is at location l1, Car c10 is at location l1, Car c22 is at location l0, Car c41 is at location l1, Car c24 is at location l1, and Car c26 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c30 is on the ferry", "answer": "no"}
{"id": 5442417161932553298, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l4, with the car c1 on board. The cars are at locations as follows: c2 and c0 are at l3. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c0 is at location l3, and Car c1 is at location l3.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The ferry is at l3 location", "answer": "yes"}
{"id": 941512129145842155, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c23, c44, c14, c33, c35, c8, c3, c2, c0, c19, c6, c13, c11, c16, c12, c20, c9, c15, c29, c18, c30, and c17 are at l0; c38, c36, c48, c5, c7, c26, c39, c40, c31, c25, c32, c43, c27, c22, c34, c28, c1, c46, c4, c42, c49, c21, c37, c47, c10, c41, c24, and c45 are at l1. The goal is to reach a state where the following facts hold: Car c45 is at location l0, Car c44 is at location l0, Car c14 is at location l0, Car c23 is at location l1, Car c37 is at location l0, Car c36 is at location l0, Car c48 is at location l1, Car c8 is at location l0, Car c7 is at location l1, Car c15 is at location l1, Car c2 is at location l0, Car c39 is at location l1, Car c46 is at location l0, Car c31 is at location l1, Car c32 is at location l1, Car c0 is at location l0, Car c19 is at location l0, Car c13 is at location l0, Car c38 is at location l0, Car c27 is at location l1, Car c3 is at location l1, Car c17 is at location l1, Car c34 is at location l1, Car c11 is at location l0, Car c12 is at location l0, Car c20 is at location l0, Car c28 is at location l1, Car c40 is at location l0, Car c25 is at location l0, Car c1 is at location l1, Car c9 is at location l0, Car c29 is at location l0, Car c33 is at location l1, Car c21 is at location l0, Car c4 is at location l1, Car c42 is at location l1, Car c43 is at location l0, Car c16 is at location l1, Car c49 is at location l1, Car c35 is at location l1, Car c30 is at location l0, Car c6 is at location l1, Car c18 is at location l1, Car c47 is at location l1, Car c5 is at location l1, Car c10 is at location l1, Car c22 is at location l0, Car c41 is at location l1, Car c24 is at location l1, and Car c26 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c14 is on board the ferry", "answer": "no"}
{"id": 7589887814401972365, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c46 on board. The cars are at locations as follows: c45, c44, c14, c37, c36, c8, c2, c0, c13, c38, c11, c12, c20, c40, c25, c9, c29, c21, c43, c30, c22, and c26 are at l0; c23, c48, c7, c15, c39, c31, c32, c19, c27, c3, c17, c34, c28, c1, c33, c4, c42, c16, c49, c35, c6, c18, c47, c5, c10, c41, and c24 are at l1. The goal is to reach a state where the following facts hold: Car c45 is at location l0, Car c44 is at location l0, Car c14 is at location l0, Car c23 is at location l1, Car c37 is at location l0, Car c36 is at location l0, Car c48 is at location l1, Car c8 is at location l0, Car c7 is at location l1, Car c15 is at location l1, Car c2 is at location l0, Car c39 is at location l1, Car c46 is at location l0, Car c31 is at location l1, Car c32 is at location l1, Car c0 is at location l0, Car c19 is at location l0, Car c13 is at location l0, Car c38 is at location l0, Car c27 is at location l1, Car c3 is at location l1, Car c17 is at location l1, Car c34 is at location l1, Car c11 is at location l0, Car c12 is at location l0, Car c20 is at location l0, Car c28 is at location l1, Car c40 is at location l0, Car c25 is at location l0, Car c1 is at location l1, Car c9 is at location l0, Car c29 is at location l0, Car c33 is at location l1, Car c21 is at location l0, Car c4 is at location l1, Car c42 is at location l1, Car c43 is at location l0, Car c16 is at location l1, Car c49 is at location l1, Car c35 is at location l1, Car c30 is at location l0, Car c6 is at location l1, Car c18 is at location l1, Car c47 is at location l1, Car c5 is at location l1, Car c10 is at location l1, Car c22 is at location l0, Car c41 is at location l1, Car c24 is at location l1, and Car c26 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c2 is on board the ferry", "answer": "no"}
{"id": -9036743003168256916, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1, with the car c1 on board. The cars are at locations as follows: c0 is at l0; c2 is at l4. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c0 is at location l3, and Car c1 is at location l3.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c0 is on the ferry", "answer": "yes"}
{"id": -7882800440783655287, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c45, c44, c14, c37, c8, c2, c46, c0, c19, c13, c38, c11, c12, c20, c40, c25, c9, c29, c21, c43, c30, c22, and c26 are at l0; c23, c36, c48, c7, c15, c39, c31, c32, c27, c3, c17, c34, c28, c1, c33, c4, c42, c16, c49, c35, c6, c18, c47, c5, c10, c41, and c24 are at l1. The goal is to reach a state where the following facts hold: Car c45 is at location l0, Car c44 is at location l0, Car c14 is at location l0, Car c23 is at location l1, Car c37 is at location l0, Car c36 is at location l0, Car c48 is at location l1, Car c8 is at location l0, Car c7 is at location l1, Car c15 is at location l1, Car c2 is at location l0, Car c39 is at location l1, Car c46 is at location l0, Car c31 is at location l1, Car c32 is at location l1, Car c0 is at location l0, Car c19 is at location l0, Car c13 is at location l0, Car c38 is at location l0, Car c27 is at location l1, Car c3 is at location l1, Car c17 is at location l1, Car c34 is at location l1, Car c11 is at location l0, Car c12 is at location l0, Car c20 is at location l0, Car c28 is at location l1, Car c40 is at location l0, Car c25 is at location l0, Car c1 is at location l1, Car c9 is at location l0, Car c29 is at location l0, Car c33 is at location l1, Car c21 is at location l0, Car c4 is at location l1, Car c42 is at location l1, Car c43 is at location l0, Car c16 is at location l1, Car c49 is at location l1, Car c35 is at location l1, Car c30 is at location l0, Car c6 is at location l1, Car c18 is at location l1, Car c47 is at location l1, Car c5 is at location l1, Car c10 is at location l1, Car c22 is at location l0, Car c41 is at location l1, Car c24 is at location l1, and Car c26 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c48 is at location l0", "answer": "no"}
{"id": -1207328381549894196, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1, with the car c8 on board. The cars are at locations as follows: c1, c2, and c7 are at l1; c9, c6, and c4 are at l0; c0, c3, and c5 are at l2. The goal is to reach a state where the following facts hold: Car c8 is at location l2, Car c1 is at location l1, Car c9 is at location l0, Car c0 is at location l2, Car c4 is at location l0, Car c2 is at location l1, Car c3 is at location l2, Car c5 is at location l2, Car c6 is at location l2, and Car c7 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c6 is at location l1", "answer": "no"}
{"id": 6580681051203913382, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c11, c15, c32, c19, c2, c33, and c49 are at l1; c44, c45, c36, c10, c47, c1, c16, c18, c24, c42, c4, c17, and c48 are at l0; c31, c12, c7, c25, c37, c20, c34, c23, c22, c13, and c6 are at l3; c46, c14, c0, c28, c30, c27, c5, c8, c9, c41, c38, and c29 are at l4; c39, c40, c3, c21, c43, c35, and c26 are at l2. The goal is to reach a state where the following facts hold: Car c11 is at location l1, Car c44 is at location l0, Car c45 is at location l0, Car c31 is at location l3, Car c12 is at location l3, Car c36 is at location l0, Car c46 is at location l4, Car c14 is at location l4, Car c39 is at location l2, Car c28 is at location l4, Car c15 is at location l1, Car c25 is at location l3, Car c30 is at location l4, Car c27 is at location l4, Car c5 is at location l4, Car c10 is at location l0, Car c32 is at location l1, Car c37 is at location l3, Car c40 is at location l2, Car c20 is at location l3, Car c19 is at location l1, Car c34 is at location l3, Car c8 is at location l4, Car c9 is at location l4, Car c23 is at location l3, Car c47 is at location l0, Car c3 is at location l2, Car c21 is at location l2, Car c1 is at location l0, Car c41 is at location l4, Car c16 is at location l0, Car c7 is at location l0, Car c22 is at location l2, Car c43 is at location l2, Car c0 is at location l2, Car c2 is at location l1, Car c18 is at location l0, Car c13 is at location l3, Car c33 is at location l1, Car c6 is at location l3, Car c35 is at location l2, Car c26 is at location l2, Car c38 is at location l4, Car c49 is at location l1, Car c24 is at location l0, Car c42 is at location l0, Car c4 is at location l0, Car c29 is at location l4, Car c17 is at location l0, and Car c48 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The ferry is at l3 location", "answer": "yes"}
{"id": 3211016090457128984, "group": "landmarks_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l3 location and it is empty. The cars are at locations as follows: c11, c48, c15, c32, c19, c44, c2, c33, and c46 are at l1; c31, c12, c39, c43, c7, c5, c37, c20, c34, c23, c13, c3, c49, c42, and c4 are at l3; c36, c14, c0, c28, c26, c30, c27, c45, c47, c38, c35, and c29 are at l4; c10, c6, c1, c16, c25, c9, c18, c24, c17, and c41 are at l0; c40, c21, c22, and c8 are at l2. The goal is to reach a state where the following facts hold: Car c11 is at location l1, Car c44 is at location l0, Car c45 is at location l0, Car c31 is at location l3, Car c12 is at location l3, Car c36 is at location l0, Car c46 is at location l4, Car c14 is at location l4, Car c39 is at location l2, Car c28 is at location l4, Car c15 is at location l1, Car c25 is at location l3, Car c30 is at location l4, Car c27 is at location l4, Car c5 is at location l4, Car c10 is at location l0, Car c32 is at location l1, Car c37 is at location l3, Car c40 is at location l2, Car c20 is at location l3, Car c19 is at location l1, Car c34 is at location l3, Car c8 is at location l4, Car c9 is at location l4, Car c23 is at location l3, Car c47 is at location l0, Car c3 is at location l2, Car c21 is at location l2, Car c1 is at location l0, Car c41 is at location l4, Car c16 is at location l0, Car c7 is at location l0, Car c22 is at location l2, Car c43 is at location l2, Car c0 is at location l2, Car c2 is at location l1, Car c18 is at location l0, Car c13 is at location l3, Car c33 is at location l1, Car c6 is at location l3, Car c35 is at location l2, Car c26 is at location l2, Car c38 is at location l4, Car c49 is at location l1, Car c24 is at location l0, Car c42 is at location l0, Car c4 is at location l0, Car c29 is at location l4, Car c17 is at location l0, and Car c48 is at location l0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Car c45 is on the ferry", "answer": "yes"}
{"id": 4760393164654511652, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l2-3, l2-6, l2-1, l2-2, l2-8, l2-0, l2-7, l2-5, l2-4, and l2-9 are in c2; l4-1, l4-9, l4-8, l4-7, l4-5, l4-0, l4-4, l4-2, l4-6, and l4-3 are in c4; l1-4, l1-6, l1-3, l1-8, l1-2, l1-9, l1-1, l1-7, l1-5, and l1-0 are in c1; l3-4, l3-9, l3-5, l3-6, l3-0, l3-7, l3-8, l3-1, l3-2, and l3-3 are in c3; l0-7, l0-2, l0-1, l0-9, l0-0, l0-3, l0-8, l0-6, l0-5, and l0-4 are in c0. Currently, t3 is at l3-0, t1 and a0 are at l1-0, p2 and t4 are at l4-8, t0 is at l0-2, p1 and t2 are at l2-7, p0 and p3 are in t1. The goal is to reach a state where the following facts hold: p3 is at l1-1, p1 is at l2-7, p2 is at l4-8, and p0 is at l1-5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p0 is at l0-8", "answer": "no"}
{"id": 6140912279185871593, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-0 and l0-1 are in c0. Currently, t1, p1, and p0 are at l1-1, a0 is at l1-0, t0 is at l0-0, p3 is in t1, p2 is in a0. The goal is to reach a state where the following facts hold: p3 is at l1-1, p2 is at l1-0, p0 is at l1-1, and p1 is at l1-1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p2 is at l1-0", "answer": "yes"}
{"id": -7002266798889710210, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l3-1, l3-2, and l3-0 are in c3; l4-2, l4-1, and l4-0 are in c4; l0-0, l0-2, and l0-1 are in c0; l2-2, l2-0, and l2-1 are in c2; l1-1, l1-0, and l1-2 are in c1. Currently, t2 is at l2-0, a0 and t4 are at l4-0, t3 is at l3-0, t0 is at l0-1, t1 is at l1-2, p1 is at l3-1, p3 is in t4, p2 is in t2, p0 is in a0. The goal is to reach a state where the following facts hold: p2 is at l2-2, p1 is at l3-2, p3 is at l3-2, and p0 is at l3-0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? t4 is at l4-1", "answer": "no"}
{"id": 7824270701713909489, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-0 and l0-1 are in c0. Currently, t1, p1, and p3 are at l1-1, a0 is at l1-0, t0 is at l0-0, p2 and p0 are in a0. The goal is to reach a state where the following facts hold: p3 is at l1-1, p2 is at l1-0, p0 is at l1-1, and p1 is at l1-1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p3 is in t1", "answer": "no"}
{"id": -4176113523658852727, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l2-2, l2-0, and l2-1 are in c2; l1-1, l1-0, and l1-2 are in c1. Currently, p1, t1, and a0 are at l1-0, t0 is at l0-1, t2 is at l2-1, p2 is in t0, p4 is in a0, p0 and p3 are in t1. The goal is to reach a state where the following facts hold: p1 is at l1-0, p0 is at l1-2, p3 is at l1-2, p2 is at l1-2, and p4 is at l0-0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p2 is at l0-0", "answer": "yes"}
{"id": 4949058973911648679, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l2-3, l2-6, l2-1, l2-2, l2-8, l2-0, l2-7, l2-5, l2-4, and l2-9 are in c2; l4-1, l4-9, l4-8, l4-7, l4-5, l4-0, l4-4, l4-2, l4-6, and l4-3 are in c4; l1-4, l1-6, l1-3, l1-8, l1-2, l1-9, l1-1, l1-7, l1-5, and l1-0 are in c1; l3-4, l3-9, l3-5, l3-6, l3-0, l3-7, l3-8, l3-1, l3-2, and l3-3 are in c3; l0-7, l0-2, l0-1, l0-9, l0-0, l0-3, l0-8, l0-6, l0-5, and l0-4 are in c0. Currently, t1 is at l1-1, t2, p1, and a0 are at l2-0, t3 is at l3-0, p2 and t4 are at l4-8, p0 is at l1-5, t0 is at l0-2, p3 is in t1. The goal is to reach a state where the following facts hold: p3 is at l1-1, p1 is at l2-7, p2 is at l4-8, and p0 is at l1-5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p3 is at l3-0", "answer": "no"}
{"id": -3824349965901793719, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l3-1, l3-2, and l3-0 are in c3; l4-2, l4-1, and l4-0 are in c4; l0-0, l0-2, and l0-1 are in c0; l2-2, l2-0, and l2-1 are in c2; l1-1, l1-0, and l1-2 are in c1. Currently, p3, a0, and t4 are at l4-0, t3 is at l3-0, t0 is at l0-2, t2 and p2 are at l2-1, t1 is at l1-2, p1 is at l3-1, p0 is in a0. The goal is to reach a state where the following facts hold: p2 is at l2-2, p1 is at l3-2, p3 is at l3-2, and p0 is at l3-0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? a0 is at l3-0", "answer": "yes"}
{"id": -1463532256608449579, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l2-3, l2-6, l2-1, l2-2, l2-8, l2-0, l2-7, l2-5, l2-4, and l2-9 are in c2; l4-1, l4-9, l4-8, l4-7, l4-5, l4-0, l4-4, l4-2, l4-6, and l4-3 are in c4; l1-4, l1-6, l1-3, l1-8, l1-2, l1-9, l1-1, l1-7, l1-5, and l1-0 are in c1; l3-4, l3-9, l3-5, l3-6, l3-0, l3-7, l3-8, l3-1, l3-2, and l3-3 are in c3; l0-7, l0-2, l0-1, l0-9, l0-0, l0-3, l0-8, l0-6, l0-5, and l0-4 are in c0. Currently, t1 and p3 are at l1-1, t3 is at l3-0, p2 and t4 are at l4-8, a0 is at l1-0, t0 is at l0-2, p1 and t2 are at l2-7, p0 is in t1. The goal is to reach a state where the following facts hold: p3 is at l1-1, p1 is at l2-7, p2 is at l4-8, and p0 is at l1-5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? t1 is at l1-5", "answer": "yes"}
{"id": 6508986716181259099, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-1, l1-0, and l1-2 are in c1. Currently, t1 is at l1-1, p3 and p1 are at l0-2, a0 and p2 are at l1-0, p0 and t0 are at l0-0. The goal is to reach a state where the following facts hold: p1 is at l0-1, p0 is at l0-1, p2 is at l0-1, and p3 is at l0-1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p2 is in a0", "answer": "yes"}
{"id": 4506251736750648730, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l2-2, l2-0, and l2-1 are in c2; l1-1, l1-0, and l1-2 are in c1. Currently, t1 and p4 are at l1-0, p1 and p3 are at l2-0, p2 is at l0-1, p0, t0, and a0 are at l0-0, t2 is at l2-1. The goal is to reach a state where the following facts hold: p1 is at l1-0, p0 is at l1-2, p3 is at l1-2, p2 is at l1-2, and p4 is at l0-0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p1 is at l2-1", "answer": "no"}
{"id": -3302321995949387375, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_15, block_20, block_6, block_13, block_9, and block_10. The following block(s) are stacked on top of another block: block_2 is on block_14, block_17 is on block_12, block_1 is on block_17, block_19 is on block_16, block_16 is on block_11, block_5 is on block_1, block_11 is on block_8, block_18 is on block_5, block_3 is on block_13, block_7 is on block_10, block_12 is on block_7, block_14 is on block_20, block_4 is on block_6, and block_8 is on block_4. The goal is to reach a state where the following facts hold: The block block_10 is currently situated under the block block_7, The block block_19 is on top of block block_16, The block block_2 is on top of block block_14, The block block_5 is on top of block block_1, The block block_12 is on top of block block_7, The block block_8 is currently situated under the block block_11, The block block_17 is currently situated above the block block_12, The block block_14 is currently situated above the block block_20, The block block_4 is on top of block block_6, The block block_5 is currently situated under the block block_18, The block block_16 is on top of block block_11, The block block_8 is currently situated above the block block_4, The block block_2 is currently situated under the block block_9, The block block_9 is currently situated under the block block_15, The block block_17 is currently situated under the block block_1, and The block block_13 is currently situated under the block block_3.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robotic arm is holding block_8", "answer": "no"}
{"id": -7137910774579353729, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_5, block_2, and block_4. The following block(s) are stacked on top of another block: block_1 is on block_4 and block_3 is on block_1. The goal is to reach a state where the following facts hold: The block block_3 is currently situated under the block block_1, The block block_2 is currently situated under the block block_4, The block block_5 is currently situated above the block block_1, and The block block_2 is currently situated above the block block_5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robotic arm is holding block_2", "answer": "yes"}
{"id": -7254553642265898425, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_3 and block_1. The following block(s) are stacked on top of another block: block_2 is on block_1 and block_5 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is on top of block block_3, The block block_2 is currently situated under the block block_4, The block block_5 is currently situated above the block block_1, and The block block_2 is on top of block block_5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The block block_2 is on top of block block_3", "answer": "no"}
{"id": -5317635039746009446, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_1. The following block(s) are on the table: block_2 and block_4. The following block(s) are stacked on top of another block: block_3 is on block_2 and block_5 is on block_4. The goal is to reach a state where the following facts hold: The block block_3 is currently situated under the block block_1, The block block_4 is on top of block block_2, The block block_5 is currently situated above the block block_1, and The block block_2 is currently situated above the block block_5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The block block_5 is currently situated under the block block_3", "answer": "no"}
{"id": 6224798673497953201, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_2, block_20, block_16, block_6, block_18, block_13, block_14, and block_10. The following block(s) are stacked on top of another block: block_17 is on block_12, block_15 is on block_9, block_1 is on block_17, block_19 is on block_16, block_9 is on block_2, block_11 is on block_8, block_3 is on block_13, block_7 is on block_10, block_12 is on block_7, block_4 is on block_6, and block_8 is on block_4. The goal is to reach a state where the following facts hold: The block block_7 is currently situated above the block block_10, The block block_16 is currently situated under the block block_19, The block block_2 is on top of block block_14, The block block_1 is currently situated under the block block_5, The block block_12 is currently situated above the block block_7, The block block_8 is currently situated under the block block_11, The block block_17 is on top of block block_12, The block block_14 is currently situated above the block block_20, The block block_4 is currently situated above the block block_6, The block block_18 is on top of block block_5, The block block_16 is currently situated above the block block_11, The block block_8 is on top of block block_4, The block block_9 is currently situated above the block block_2, The block block_9 is currently situated under the block block_15, The block block_17 is currently situated under the block block_1, and The block block_3 is currently situated above the block block_13.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The block block_16 is currently being held by the robotic arm", "answer": "yes"}
{"id": 7085451526155926061, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_1. The following block(s) are on the table: block_10 and block_2. The following block(s) are stacked on top of another block: block_9 is on block_5, block_4 is on block_9, block_8 is on block_10, block_3 is on block_4, block_7 is on block_3, block_5 is on block_2, and block_6 is on block_7. The goal is to reach a state where the following facts hold: The block block_9 is currently situated above the block block_1, The block block_7 is on top of block block_10, The block block_4 is currently situated above the block block_2, The block block_8 is currently situated under the block block_1, The block block_5 is on top of block block_9, The block block_3 is currently situated under the block block_6, The block block_3 is on top of block block_7, and The block block_2 is on top of block block_5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? No blocks are placed on top of block_5", "answer": "yes"}
{"id": 2682874459393992293, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_2. The following block(s) are on the table: block_5, block_1, and block_4. The following block(s) is stacked on top of another block: block_3 is on block_4. The goal is to reach a state where the following facts hold: The block block_4 is currently situated under the block block_5, The block block_4 is currently situated above the block block_1, and The block block_3 is currently situated above the block block_5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robotic arm is holding block_5", "answer": "yes"}
{"id": 8261394128363890052, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_3, block_5, block_1, and block_2. The goal is to reach a state where the following facts hold: The block block_1 is on top of block block_3, The block block_2 is currently situated under the block block_4, The block block_5 is on top of block block_1, and The block block_5 is currently situated under the block block_2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robotic arm is not holding anything", "answer": "yes"}
{"id": 3496520136928626784, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_5 and block_4. The following block(s) are stacked on top of another block: block_1 is on block_2 and block_2 is on block_4. The goal is to reach a state where the following facts hold: The block block_1 is currently situated above the block block_3, The block block_2 is currently situated under the block block_4, The block block_5 is on top of block block_1, and The block block_2 is currently situated above the block block_5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? block_4 is not obstructed by any other blocks", "answer": "yes"}
{"id": -389379424263951733, "group": "landmarks_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_2 and block_1. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_3.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The block block_1 is currently situated above the block block_1", "answer": "no"}
{"id": 6597636164923934416, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f2-4f and is holding key0-1. All the positions are open except the following: f4-4f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-0 is at position f2-2f. Key key0-2 is at position f1-4f. The goal is to reach a state where the following facts hold: Key key0-2 is at f1-4f location, Key key0-1 is at f3-2f location, and Key key0-0 is at f2-2f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f3-2f location", "answer": "yes"}
{"id": -8221057251287060871, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f3-0f and is holding key0-1. All the positions are open except the following: f4-4f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-0 is at position f2-2f. Key key0-2 is at position f1-4f. The goal is to reach a state where the following facts hold: Key key0-2 is at f1-4f location, Key key0-1 is at f3-2f location, and Key key0-0 is at f2-2f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f3-2f location", "answer": "yes"}
{"id": 6753197458102839486, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-3 is of shape shape0, Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f4-1f and its arm is empty. All the positions are open except the following: f0-2f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-2 is at position f4-0f. Key key0-1 is at position f4-2f. Key key0-0 is at position f0-1f. Key key0-3 is at position f0-1f. The goal is to reach a state where the following facts hold: Key key0-0 is at f0-1f location, Key key0-1 is at f1-4f location, Key key0-2 is at f4-0f location, and Key key0-3 is at f0-1f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is holding key0-1", "answer": "yes"}
{"id": 2514193642834254210, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f2-4f and its arm is empty. All the positions are open except the following: f2-1f has shape1 shaped lock, f3-4f has shape1 shaped lock. Key key1-0 is at position f1-0f. Key key1-1 is at position f3-2f. The goal is to reach a state where the following facts hold: Key key1-0 is at f1-0f location and Key key1-1 is at f4-4f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f3-2f location", "answer": "yes"}
{"id": -2150187257571845619, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f3-3f and is holding key1-1. All the positions are open except the following: f3-4f has shape1 shaped lock. Key key1-0 is at position f1-0f. The goal is to reach a state where the following facts hold: Key key1-0 is at f1-0f location and Key key1-1 is at f4-4f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f4-4f location", "answer": "yes"}
{"id": 4628971803681032817, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f1-0f and its arm is empty. All the positions are open except the following: f2-1f has shape1 shaped lock, f3-4f has shape1 shaped lock. Key key1-1 is at position f3-0f. Key key1-0 is at position f1-1f. The goal is to reach a state where the following facts hold: Key key1-0 is at f1-0f location and Key key1-1 is at f4-4f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f1-1f location", "answer": "yes"}
{"id": 335322009804562048, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f3-3f and is holding key0-0. All the positions are open except the following: f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-2 is at position f0-4f. Key key0-1 is at position f2-2f. The goal is to reach a state where the following facts hold: Key key0-0 is at f3-3f location, Key key0-2 is at f3-3f location, and Key key0-1 is at f1-2f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot is at f1-4f location", "answer": "no"}
{"id": 7288279625544347787, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-3f and its arm is empty. All the positions are open except the following: f0-1f has shape0 shaped lock. Key key0-1 is at position f4-1f. Key key0-0 is at position f2-3f. The goal is to reach a state where the following facts hold: Key key0-0 is at f3-1f location and Key key0-1 is at f4-1f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Key key0-0 is at f1-3f location", "answer": "no"}
{"id": -8940300753236745654, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f1-2f and its arm is empty. All the positions are open except the following: f4-4f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-2 is at position f4-3f. Key key0-0 is at position f0-1f. Key key0-1 is at position f1-2f. The goal is to reach a state where the following facts hold: Key key0-2 is at f1-4f location, Key key0-1 is at f3-2f location, and Key key0-0 is at f2-2f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Key key0-1 is at f4-3f location", "answer": "no"}
{"id": -5625793508802672460, "group": "landmarks_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f2-2f and is holding key0-2. All the positions are open except the following: f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-0 is at position f1-3f. Key key0-1 is at position f2-2f. The goal is to reach a state where the following facts hold: Key key0-0 is at f3-3f location, Key key0-2 is at f3-3f location, and Key key0-1 is at f1-2f location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Key key0-0 is at f4-0f location", "answer": "no"}
{"id": -190862746056098394, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 24 tiles and 2 robots. The tiles locations are: tile_14 is to the right of tile_13, tile_6 is to the right of tile_5, tile_4 is to the right of tile_3, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_20 is to the right of tile_19, tile_5 is to the right of tile_4, tile_16 is to the right of tile_15, tile_17 is to the right of tile_16, tile_9 is to the right of tile_8, tile_21 is to the right of tile_20, tile_24 is to the right of tile_23, tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_22 is to the right of tile_21, tile_15 is to the right of tile_14, tile_18 is to the right of tile_17, tile_10 is to the right of tile_9, tile_23 is to the right of tile_22, and tile_12 is to the right of tile_11. Further, tile_17 is down from tile_23, tile_5 is down from tile_11, tile_8 is down from tile_14, tile_2 is down from tile_8, tile_1 is down from tile_7, tile_18 is down from tile_24, tile_13 is down from tile_19, tile_15 is down from tile_21, tile_10 is down from tile_16, tile_4 is down from tile_10, tile_14 is down from tile_20, tile_3 is down from tile_9, tile_7 is down from tile_13, tile_11 is down from tile_17, tile_9 is down from tile_15, tile_16 is down from tile_22, tile_12 is down from tile_18, and tile_6 is down from tile_12 Currently, robot robot2 is at tile_1 and holding color white and robot robot1 is at tile_2 and holding color white; tile_4, tile_3, tile_6, tile_5, and tile_7 are clear; tile_21 is painted white, tile_13 is painted black, tile_24 is painted black, tile_18 is painted white, tile_9 is painted white, tile_20 is painted black, tile_16 is painted white, tile_11 is painted white, tile_19 is painted white, tile_10 is painted black, tile_8 is painted black, tile_14 is painted white, tile_23 is painted white, tile_12 is painted black, tile_17 is painted black, tile_22 is painted black, and tile_15 is painted black. The goal is to reach a state where the following facts hold: Tile tile_21 is painted in white color, Tile tile_14 is painted in white color, Tile tile_13 is painted in black color, Tile tile_24 is painted in black color, Tile tile_18 is painted in white color, Tile tile_20 is painted in black color, Tile tile_16 is painted in white color, Tile tile_11 is painted in white color, Tile tile_19 is painted in white color, Tile tile_23 is painted in white color, Tile tile_10 is painted in black color, Tile tile_12 is painted in black color, Tile tile_17 is painted in black color, Tile tile_9 is painted in white color, Tile tile_22 is painted in black color, Tile tile_7 is painted in white color, Tile tile_15 is painted in black color, and Tile tile_8 is painted in black color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Tile tile_7 is painted in white color", "answer": "yes"}
{"id": 2737400362836965353, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 24 tiles and 2 robots. The tiles locations are: tile_14 is to the right of tile_13, tile_6 is to the right of tile_5, tile_4 is to the right of tile_3, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_20 is to the right of tile_19, tile_5 is to the right of tile_4, tile_16 is to the right of tile_15, tile_17 is to the right of tile_16, tile_9 is to the right of tile_8, tile_21 is to the right of tile_20, tile_24 is to the right of tile_23, tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_22 is to the right of tile_21, tile_15 is to the right of tile_14, tile_18 is to the right of tile_17, tile_10 is to the right of tile_9, tile_23 is to the right of tile_22, and tile_12 is to the right of tile_11. Further, tile_17 is down from tile_23, tile_5 is down from tile_11, tile_8 is down from tile_14, tile_2 is down from tile_8, tile_1 is down from tile_7, tile_18 is down from tile_24, tile_13 is down from tile_19, tile_15 is down from tile_21, tile_10 is down from tile_16, tile_4 is down from tile_10, tile_14 is down from tile_20, tile_3 is down from tile_9, tile_7 is down from tile_13, tile_11 is down from tile_17, tile_9 is down from tile_15, tile_16 is down from tile_22, tile_12 is down from tile_18, and tile_6 is down from tile_12 Currently, robot robot1 is at tile_9 and holding color black and robot robot2 is at tile_6 and holding color white; tile_10, tile_13, tile_4, tile_3, tile_2, tile_11, tile_17, tile_5, tile_12, tile_16, tile_1, and tile_7 are clear; tile_21 is painted white, tile_24 is painted black, tile_18 is painted white, tile_20 is painted black, tile_19 is painted white, tile_8 is painted black, tile_14 is painted white, tile_23 is painted white, tile_22 is painted black, and tile_15 is painted black. The goal is to reach a state where the following facts hold: Tile tile_21 is painted in white color, Tile tile_14 is painted in white color, Tile tile_13 is painted in black color, Tile tile_24 is painted in black color, Tile tile_18 is painted in white color, Tile tile_20 is painted in black color, Tile tile_16 is painted in white color, Tile tile_11 is painted in white color, Tile tile_19 is painted in white color, Tile tile_23 is painted in white color, Tile tile_10 is painted in black color, Tile tile_12 is painted in black color, Tile tile_17 is painted in black color, Tile tile_9 is painted in white color, Tile tile_22 is painted in black color, Tile tile_7 is painted in white color, Tile tile_15 is painted in black color, and Tile tile_8 is painted in black color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? tile_9 is clear", "answer": "yes"}
{"id": 3453803709505290290, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_7 is down from tile_10, tile_1 is down from tile_4, and tile_9 is down from tile_12 Currently, robot robot2 is at tile_7 and holding color white and robot robot1 is at tile_9 and holding color white; tile_5, tile_12, tile_4, tile_8, tile_1, tile_2, tile_3, tile_11, and tile_6 are clear; tile_10 is painted white. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? tile_9 is clear", "answer": "yes"}
{"id": 8682061298127632426, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 24 tiles and 2 robots. The tiles locations are: tile_14 is to the right of tile_13, tile_6 is to the right of tile_5, tile_4 is to the right of tile_3, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_20 is to the right of tile_19, tile_5 is to the right of tile_4, tile_16 is to the right of tile_15, tile_17 is to the right of tile_16, tile_9 is to the right of tile_8, tile_21 is to the right of tile_20, tile_24 is to the right of tile_23, tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_22 is to the right of tile_21, tile_15 is to the right of tile_14, tile_18 is to the right of tile_17, tile_10 is to the right of tile_9, tile_23 is to the right of tile_22, and tile_12 is to the right of tile_11. Further, tile_17 is down from tile_23, tile_5 is down from tile_11, tile_8 is down from tile_14, tile_2 is down from tile_8, tile_1 is down from tile_7, tile_18 is down from tile_24, tile_13 is down from tile_19, tile_15 is down from tile_21, tile_10 is down from tile_16, tile_4 is down from tile_10, tile_14 is down from tile_20, tile_3 is down from tile_9, tile_7 is down from tile_13, tile_11 is down from tile_17, tile_9 is down from tile_15, tile_16 is down from tile_22, tile_12 is down from tile_18, and tile_6 is down from tile_12 Currently, robot robot2 is at tile_5 and holding color white and robot robot1 is at tile_9 and holding color white; tile_10, tile_15, tile_4, tile_23, tile_3, tile_2, tile_11, tile_6, tile_17, tile_16, and tile_1 are clear; tile_21 is painted white, tile_13 is painted black, tile_24 is painted black, tile_18 is painted white, tile_20 is painted black, tile_7 is painted white, tile_19 is painted white, tile_8 is painted black, tile_14 is painted white, tile_12 is painted black, and tile_22 is painted black. The goal is to reach a state where the following facts hold: Tile tile_21 is painted in white color, Tile tile_14 is painted in white color, Tile tile_13 is painted in black color, Tile tile_24 is painted in black color, Tile tile_18 is painted in white color, Tile tile_20 is painted in black color, Tile tile_16 is painted in white color, Tile tile_11 is painted in white color, Tile tile_19 is painted in white color, Tile tile_23 is painted in white color, Tile tile_10 is painted in black color, Tile tile_12 is painted in black color, Tile tile_17 is painted in black color, Tile tile_9 is painted in white color, Tile tile_22 is painted in black color, Tile tile_7 is painted in white color, Tile tile_15 is painted in black color, and Tile tile_8 is painted in black color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Tile tile_17 is painted in white color", "answer": "no"}
{"id": 7908210324502434104, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 24 tiles and 2 robots. The tiles locations are: tile_14 is to the right of tile_13, tile_6 is to the right of tile_5, tile_4 is to the right of tile_3, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_20 is to the right of tile_19, tile_5 is to the right of tile_4, tile_16 is to the right of tile_15, tile_17 is to the right of tile_16, tile_9 is to the right of tile_8, tile_21 is to the right of tile_20, tile_24 is to the right of tile_23, tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_22 is to the right of tile_21, tile_15 is to the right of tile_14, tile_18 is to the right of tile_17, tile_10 is to the right of tile_9, tile_23 is to the right of tile_22, and tile_12 is to the right of tile_11. Further, tile_17 is down from tile_23, tile_5 is down from tile_11, tile_8 is down from tile_14, tile_2 is down from tile_8, tile_1 is down from tile_7, tile_18 is down from tile_24, tile_13 is down from tile_19, tile_15 is down from tile_21, tile_10 is down from tile_16, tile_4 is down from tile_10, tile_14 is down from tile_20, tile_3 is down from tile_9, tile_7 is down from tile_13, tile_11 is down from tile_17, tile_9 is down from tile_15, tile_16 is down from tile_22, tile_12 is down from tile_18, and tile_6 is down from tile_12 Currently, robot robot1 is at tile_3 and holding color white and robot robot2 is at tile_1 and holding color white; tile_24, tile_10, tile_4, tile_2, tile_22, tile_11, tile_6, tile_5, tile_12, tile_16, and tile_18 are clear; tile_21 is painted white, tile_13 is painted black, tile_9 is painted white, tile_20 is painted black, tile_7 is painted white, tile_19 is painted white, tile_8 is painted black, tile_14 is painted white, tile_23 is painted white, tile_17 is painted black, and tile_15 is painted black. The goal is to reach a state where the following facts hold: Tile tile_21 is painted in white color, Tile tile_14 is painted in white color, Tile tile_13 is painted in black color, Tile tile_24 is painted in black color, Tile tile_18 is painted in white color, Tile tile_20 is painted in black color, Tile tile_16 is painted in white color, Tile tile_11 is painted in white color, Tile tile_19 is painted in white color, Tile tile_23 is painted in white color, Tile tile_10 is painted in black color, Tile tile_12 is painted in black color, Tile tile_17 is painted in black color, Tile tile_9 is painted in white color, Tile tile_22 is painted in black color, Tile tile_7 is painted in white color, Tile tile_15 is painted in black color, and Tile tile_8 is painted in black color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Tile tile_22 is painted in black color", "answer": "yes"}
{"id": -7745550363688375104, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 9 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, and tile_5 is to the right of tile_4. Further, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_5 is down from tile_8, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_1 and holding color black and robot robot1 is at tile_6 and holding color black; tile_5, tile_8, tile_2, tile_3, and tile_4 are clear; tile_7 is painted black and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is at tile_8 location", "answer": "no"}
{"id": 301051975412578273, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_14 is to the right of tile_13, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_18 is to the right of tile_17, tile_10 is to the right of tile_9, tile_13 is to the right of tile_12, tile_8 is to the right of tile_7, tile_4 is to the right of tile_3, tile_19 is to the right of tile_18, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_20 is to the right of tile_19, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_12 is down from tile_17, tile_6 is down from tile_11, tile_8 is down from tile_13, tile_2 is down from tile_7, tile_10 is down from tile_15, tile_13 is down from tile_18, tile_1 is down from tile_6, tile_14 is down from tile_19, tile_4 is down from tile_9, tile_11 is down from tile_16, tile_15 is down from tile_20, tile_7 is down from tile_12, tile_9 is down from tile_14, tile_5 is down from tile_10, and tile_3 is down from tile_8 Currently, robot robot1 is at tile_8 and holding color white and robot robot2 is at tile_12 and holding color black; tile_10, tile_15, tile_13, tile_4, tile_14, tile_3, tile_2, tile_11, tile_6, tile_17, tile_5, tile_19, tile_1, tile_7, and tile_9 are clear; tile_18 is painted white, tile_16 is painted white, and tile_20 is painted white. The goal is to reach a state where the following facts hold: Tile tile_14 is painted in white color, Tile tile_13 is painted in black color, Tile tile_18 is painted in white color, Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_16 is painted in white color, Tile tile_9 is painted in black color, Tile tile_7 is painted in black color, Tile tile_19 is painted in black color, Tile tile_20 is painted in white color, Tile tile_17 is painted in black color, Tile tile_8 is painted in white color, and Tile tile_15 is painted in black color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? tile_12 is clear", "answer": "yes"}
{"id": 8271286163224714230, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 3 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_14 is to the right of tile_13, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_18 is to the right of tile_17, tile_10 is to the right of tile_9, tile_13 is to the right of tile_12, tile_8 is to the right of tile_7, tile_4 is to the right of tile_3, tile_19 is to the right of tile_18, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_20 is to the right of tile_19, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_12 is down from tile_17, tile_6 is down from tile_11, tile_8 is down from tile_13, tile_2 is down from tile_7, tile_10 is down from tile_15, tile_13 is down from tile_18, tile_1 is down from tile_6, tile_14 is down from tile_19, tile_4 is down from tile_9, tile_11 is down from tile_16, tile_15 is down from tile_20, tile_7 is down from tile_12, tile_9 is down from tile_14, tile_5 is down from tile_10, and tile_3 is down from tile_8 Currently, robot robot2 is at tile_2 and holding color black, robot robot3 is at tile_4 and holding color white, and robot robot1 is at tile_1 and holding color white; tile_14, tile_3, tile_5, tile_7, and tile_9 are clear; tile_13 is painted black, tile_18 is painted white, tile_19 is painted black, tile_6 is painted white, tile_10 is painted white, tile_16 is painted white, tile_11 is painted black, tile_12 is painted white, tile_20 is painted white, tile_17 is painted black, tile_8 is painted white, and tile_15 is painted black. The goal is to reach a state where the following facts hold: Tile tile_14 is painted in white color, Tile tile_13 is painted in black color, Tile tile_18 is painted in white color, Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_16 is painted in white color, Tile tile_9 is painted in black color, Tile tile_7 is painted in black color, Tile tile_19 is painted in black color, Tile tile_20 is painted in white color, Tile tile_17 is painted in black color, Tile tile_8 is painted in white color, and Tile tile_15 is painted in black color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot2 is at tile_11 location", "answer": "no"}
{"id": -7132194892185575922, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 9 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, and tile_5 is to the right of tile_4. Further, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_5 is down from tile_8, and tile_1 is down from tile_4 Currently, robot robot1 is at tile_5 and holding color white and robot robot2 is at tile_6 and holding color white; tile_1, tile_2, tile_7, tile_3, and tile_4 are clear; tile_9 is painted black and tile_8 is painted white. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? tile_5 is clear", "answer": "yes"}
{"id": 2724697049619125819, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_14 is to the right of tile_13, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_18 is to the right of tile_17, tile_10 is to the right of tile_9, tile_13 is to the right of tile_12, tile_8 is to the right of tile_7, tile_4 is to the right of tile_3, tile_19 is to the right of tile_18, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_20 is to the right of tile_19, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_12 is down from tile_17, tile_6 is down from tile_11, tile_8 is down from tile_13, tile_2 is down from tile_7, tile_10 is down from tile_15, tile_13 is down from tile_18, tile_1 is down from tile_6, tile_14 is down from tile_19, tile_4 is down from tile_9, tile_11 is down from tile_16, tile_15 is down from tile_20, tile_7 is down from tile_12, tile_9 is down from tile_14, tile_5 is down from tile_10, and tile_3 is down from tile_8 Currently, robot robot2 is at tile_2 and holding color black and robot robot1 is at tile_4 and holding color white; tile_14, tile_3, tile_5, tile_19, tile_1, and tile_9 are clear; tile_13 is painted black, tile_18 is painted white, tile_6 is painted white, tile_10 is painted white, tile_16 is painted white, tile_11 is painted black, tile_12 is painted white, tile_7 is painted black, tile_20 is painted white, tile_17 is painted black, tile_8 is painted white, and tile_15 is painted black. The goal is to reach a state where the following facts hold: Tile tile_14 is painted in white color, Tile tile_13 is painted in black color, Tile tile_18 is painted in white color, Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_16 is painted in white color, Tile tile_9 is painted in black color, Tile tile_7 is painted in black color, Tile tile_19 is painted in black color, Tile tile_20 is painted in white color, Tile tile_17 is painted in black color, Tile tile_8 is painted in white color, and Tile tile_15 is painted in black color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot2 is at tile_11 location", "answer": "no"}
{"id": -7341033865075530299, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1 and both grippers are free. Additionally, ball2 is at room1, ball3 and ball1 are at room4, ball4 is at room5. The goal is to reach a state where the following facts hold: Ball ball4 is at room5 location, Ball ball3 is at room4 location, Ball ball2 is in room room3, and Ball ball1 is at room4 location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is at room3 location", "answer": "yes"}
{"id": -8804551041508343667, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball4 and ball2 are at room2, ball1 is at room4, ball3 is at room10. The goal is to reach a state where the following facts hold: Ball ball1 is at room8 location, Ball ball2 is in room room6, Ball ball4 is at room9 location, and Ball ball3 is in room room8.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is in room room4", "answer": "yes"}
{"id": 797541817915744269, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball15, and right gripper is carrying the ball ball6. Additionally, ball5, ball11, ball2, ball4, ball10, ball9, and ball14 are at room1, ball12, ball1, ball13, and ball3 are at room2, ball8 and ball7 are at room3. The goal is to reach a state where the following facts hold: Ball ball5 is in room room1, Ball ball14 is in room room3, Ball ball8 is in room room1, Ball ball12 is in room room2, Ball ball4 is at room1 location, Ball ball2 is at room1 location, Ball ball6 is at room1 location, Ball ball15 is in room room1, Ball ball1 is at room2 location, Ball ball9 is at room3 location, Ball ball10 is at room3 location, Ball ball11 is at room3 location, Ball ball7 is at room3 location, Ball ball13 is at room2 location, and Ball ball3 is in room room2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is in room room3", "answer": "yes"}
{"id": 8094631974392802645, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball10, and right gripper is carrying the ball ball15. Additionally, ball5, ball11, ball2, ball4, ball6, ball9, and ball14 are at room1, ball12, ball1, ball13, and ball3 are at room2, ball8 and ball7 are at room3. The goal is to reach a state where the following facts hold: Ball ball5 is at room1 location, Ball ball14 is at room3 location, Ball ball8 is at room1 location, Ball ball12 is in room room2, Ball ball4 is in room room1, Ball ball2 is in room room1, Ball ball6 is at room1 location, Ball ball15 is in room room1, Ball ball1 is in room room2, Ball ball9 is in room room3, Ball ball10 is in room room3, Ball ball11 is at room3 location, Ball ball7 is in room room3, Ball ball13 is in room room2, and Ball ball3 is at room2 location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is in room room3", "answer": "yes"}
{"id": 2248249855581234856, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room6, right gripper is free, and left gripper is carrying the ball ball3. Additionally, ball4 is at room9, ball1 is at room4, ball2 is at room6. The goal is to reach a state where the following facts hold: Ball ball1 is in room room8, Ball ball2 is at room6 location, Ball ball4 is in room room9, and Ball ball3 is in room room8.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is in room room8", "answer": "yes"}
{"id": -2876733251208662020, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball4, ball2, and ball3 are at room2. The goal is to reach a state where the following facts hold: Ball ball2 is at room2 location, Ball ball1 is at room2 location, Ball ball4 is at room2 location, and Ball ball3 is in room room2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Ball ball1 is in room room1", "answer": "no"}
{"id": -8530413640210327926, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball4, and right gripper is carrying the ball ball2. Additionally, ball1 and ball3 are at room2. The goal is to reach a state where the following facts hold: Ball ball2 is in room room2, Ball ball1 is at room2 location, Ball ball4 is in room room2, and Ball ball3 is at room2 location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Ball ball2 is in room room2", "answer": "yes"}
{"id": -4501150682243790741, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball3, and right gripper is carrying the ball ball4. Additionally, ball1 is at room1, ball2 is at room2. The goal is to reach a state where the following facts hold: Ball ball2 is at room2 location, Ball ball1 is at room2 location, Ball ball4 is at room2 location, and Ball ball3 is at room2 location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is in room room2", "answer": "yes"}
{"id": -462891615075826088, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball10. Additionally, ball11, ball8, ball2, ball15, ball4, ball6, ball9, and ball14 are at room1, ball12, ball1, ball13, and ball3 are at room2, ball7 and ball5 are at room3. The goal is to reach a state where the following facts hold: Ball ball5 is at room1 location, Ball ball14 is at room3 location, Ball ball8 is at room1 location, Ball ball12 is at room2 location, Ball ball4 is in room room1, Ball ball2 is in room room1, Ball ball6 is at room1 location, Ball ball15 is in room room1, Ball ball1 is at room2 location, Ball ball9 is at room3 location, Ball ball10 is at room3 location, Ball ball11 is at room3 location, Ball ball7 is in room room3, Ball ball13 is in room room2, and Ball ball3 is in room room2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Ball ball14 is at room2 location", "answer": "no"}
{"id": -793582170106558144, "group": "landmarks_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room5, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball1 is at room4, ball2 is at room6, ball3 is at room10. The goal is to reach a state where the following facts hold: Ball ball1 is at room8 location, Ball ball2 is at room6 location, Ball ball4 is at room9 location, and Ball ball3 is at room8 location.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Ball ball4 is in room room7", "answer": "no"}
{"id": -3525627785900515512, "group": "landmarks_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 supports colour and low_res. Camera camera0 supports high_res and low_res and colour. Camera camera1 supports colour. Rover rover0 can traverse from waypoint2 to waypoint5, waypoint6 to waypoint5, waypoint5 to waypoint2, waypoint5 to waypoint4, waypoint0 to waypoint5, waypoint1 to waypoint5, waypoint5 to waypoint1, waypoint3 to waypoint0, waypoint0 to waypoint3, waypoint5 to waypoint6, waypoint4 to waypoint5, waypoint5 to waypoint0. Rover rover1 can traverse from waypoint3 to waypoint0, waypoint4 to waypoint2, waypoint0 to waypoint3, waypoint1 to waypoint6, waypoint6 to waypoint0, waypoint5 to waypoint0, waypoint0 to waypoint6, waypoint2 to waypoint0, waypoint2 to waypoint4, waypoint6 to waypoint1, waypoint0 to waypoint2, waypoint0 to waypoint5. Waypoint(s) are visible from waypoint5: waypoint3, waypoint6, waypoint1, waypoint2, waypoint0, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint5, waypoint6, waypoint3, waypoint2, and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1, waypoint0, waypoint4, waypoint3, and waypoint5. Waypoint(s) are visible from waypoint1: waypoint3, waypoint5, waypoint6, waypoint2, and waypoint4. Waypoint(s) are visible from waypoint3: waypoint4, waypoint5, waypoint6, waypoint2, waypoint1, and waypoint0. Waypoint(s) are visible from waypoint6: waypoint4, waypoint5, waypoint3, waypoint1, and waypoint0. Waypoint(s) are visible from waypoint0: waypoint3, waypoint5, waypoint6, and waypoint2. Objective objective1 is visible from waypoint5, waypoint2, waypoint0, and waypoint4. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint5. Rocks can be sampled at the following location(s): waypoint5, waypoint2, waypoint4, waypoint6, and waypoint3. Soil can be sampled at the following location(s): waypoint3, waypoint1, waypoint4, and waypoint6. Rovers rover1 and rover0 are available. Rover rover1 has image objective0 in mode high_res. Rover rover0 has its camera camera2 calibrated. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Image objective1 was communicated in mode colour, Rock data was communicated from waypoint waypoint4;, Image objective0 was communicated in mode high_res, Rock data was communicated from waypoint waypoint3;, Rock data was communicated from waypoint waypoint5;, and Image objective1 was communicated in mode high_res.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Rover rover0 has rock analyzed in waypoint waypoint2", "answer": "no"}
{"id": -4930564680182736176, "group": "landmarks_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera2 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint2 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective2 is visible from waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint2. Rovers rover1 and rover0 are available. Soil data was communicated from waypoint waypoint0; Image objective1 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode colour. Rover rover1 has image objective1 in mode colour. Store(s) store1 is empty. Store(s) store0 is full. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode colour, Rock data was communicated from waypoint waypoint0;, and Soil data was communicated from waypoint waypoint0;.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Image objective0 was communicated in mode high_res", "answer": "no"}
{"id": -4828387400211380666, "group": "landmarks_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera2 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint2 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective2 is visible from waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint2. Rovers rover1 and rover0 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode colour. Rover rover1 has its camera camera2 calibrated. Store(s) store0 and store1 are full. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode colour, Rock data was communicated from waypoint waypoint0;, and Soil data was communicated from waypoint waypoint0;.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Image objective1 was communicated in mode colour", "answer": "yes"}
{"id": 4750981850813750807, "group": "landmarks_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 7 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera0 can be calibrated on objective4. Camera camera2 can be calibrated on objective1. Camera camera1 can be calibrated on objective1. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint2 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective6 is visible from waypoint0 and waypoint1. Objective objective5 is visible from waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective2 is visible from waypoint1. Objective objective1 is visible from waypoint2. Objective objective3 is visible from waypoint2. Objective objective4 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Rock data was communicated from waypoint waypoint2; Soil data was communicated from waypoint waypoint2; Image objective3 was communicated in mode high_res. Rover rover0 has soil analyzed in waypoint waypoint2. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has rock analyzed in waypoint waypoint2. Rover rover1 has image objective3 in mode high_res. Rover rover0 has image objective4 in mode low_res. Rover rover0 has image objective0 in mode colour. Store(s) store0 is empty. Store(s) store1 is full. The goal is to reach a state where the following facts hold: Image objective6 was communicated in mode low_res, Image objective3 was communicated in mode high_res, Image objective0 was communicated in mode colour, Rock data was communicated from waypoint waypoint2;, Rock data was communicated from waypoint waypoint1;, Image objective4 was communicated in mode low_res, Soil data was communicated from waypoint waypoint1;, Rock data was communicated from waypoint waypoint0;, Image objective0 was communicated in mode low_res, and Soil data was communicated from waypoint waypoint2;.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Rover rover0 has image objective0 in mode low_res", "answer": "yes"}
{"id": -8314659591435475377, "group": "landmarks_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera2 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint2 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective2 is visible from waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint2. Rovers rover1 and rover0 are available. Image objective1 was communicated in mode colour. Rover rover1 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode colour. Store(s) store0 is empty. Store(s) store1 is full. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode colour, Rock data was communicated from waypoint waypoint0;, and Soil data was communicated from waypoint waypoint0;.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Image objective1 was communicated in mode high_res", "answer": "no"}
{"id": 6949209248306983184, "group": "landmarks_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera1 supports colour and low_res. Camera camera0 supports low_res and colour. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint1 to waypoint4, waypoint0 to waypoint1, waypoint4 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0, waypoint2, and waypoint4. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint4, waypoint3, waypoint1, and waypoint2. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1, waypoint3, and waypoint0. Objective objective0 is visible from waypoint2 and waypoint1. Objective objective1 is visible from waypoint4. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint4. Rocks can be sampled at the following location(s): waypoint1. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode low_res. Rover rover1 has its camera camera1 calibrated. Store(s) store0 is empty. Store(s) store1 is full. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode colour, Rock data was communicated from waypoint waypoint1;, Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint0;, and Image objective0 was communicated in mode low_res.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Rover rover1 is at waypoint1", "answer": "yes"}
{"id": 230722108409223916, "group": "landmarks_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera0 supports low_res. Camera camera1 supports low_res and high_res. Rover rover0 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective1 is visible from waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover1 and rover0 are available. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has its camera camera1 calibrated. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective0 was communicated in mode low_res, Image objective1 was communicated in mode low_res, Soil data was communicated from waypoint waypoint1;, and Rock data was communicated from waypoint waypoint1;.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Rover rover0 has image objective0 in mode high_res", "answer": "no"}
{"id": 6552383347815092369, "group": "landmarks_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 supports colour and low_res. Camera camera0 supports high_res and low_res and colour. Camera camera1 supports colour. Rover rover0 can traverse from waypoint2 to waypoint5, waypoint6 to waypoint5, waypoint5 to waypoint2, waypoint5 to waypoint4, waypoint0 to waypoint5, waypoint1 to waypoint5, waypoint5 to waypoint1, waypoint3 to waypoint0, waypoint0 to waypoint3, waypoint5 to waypoint6, waypoint4 to waypoint5, waypoint5 to waypoint0. Rover rover1 can traverse from waypoint3 to waypoint0, waypoint4 to waypoint2, waypoint0 to waypoint3, waypoint1 to waypoint6, waypoint6 to waypoint0, waypoint5 to waypoint0, waypoint0 to waypoint6, waypoint2 to waypoint0, waypoint2 to waypoint4, waypoint6 to waypoint1, waypoint0 to waypoint2, waypoint0 to waypoint5. Waypoint(s) are visible from waypoint5: waypoint3, waypoint6, waypoint1, waypoint2, waypoint0, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint5, waypoint6, waypoint3, waypoint2, and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1, waypoint0, waypoint4, waypoint3, and waypoint5. Waypoint(s) are visible from waypoint1: waypoint3, waypoint5, waypoint6, waypoint2, and waypoint4. Waypoint(s) are visible from waypoint3: waypoint4, waypoint5, waypoint6, waypoint2, waypoint1, and waypoint0. Waypoint(s) are visible from waypoint6: waypoint4, waypoint5, waypoint3, waypoint1, and waypoint0. Waypoint(s) are visible from waypoint0: waypoint3, waypoint5, waypoint6, and waypoint2. Objective objective1 is visible from waypoint5, waypoint2, waypoint0, and waypoint4. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint4. Rover rover1 is at waypoint3. Rocks can be sampled at the following location(s): waypoint6, waypoint2, and waypoint4. Soil can be sampled at the following location(s): waypoint3, waypoint4, waypoint6, and waypoint1. Rovers rover1 and rover0 are available. Image objective0 was communicated in mode high_res. Rover rover0 has rock analyzed in waypoint waypoint5. Rover rover1 has rock analyzed in waypoint waypoint3. Rover rover1 has image objective0 in mode high_res. Rover rover0 has its camera camera2 calibrated. Store(s) store0 and store1 are full. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Image objective1 was communicated in mode colour, Rock data was communicated from waypoint waypoint4;, Image objective0 was communicated in mode high_res, Rock data was communicated from waypoint waypoint3;, Rock data was communicated from waypoint waypoint5;, and Image objective1 was communicated in mode high_res.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Rover rover1 has its camera camera0 calibrated", "answer": "yes"}
{"id": -6710851884691497454, "group": "landmarks_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera1 supports colour and low_res. Camera camera0 supports low_res and colour. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint1 to waypoint4, waypoint0 to waypoint1, waypoint4 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0, waypoint2, and waypoint4. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint4, waypoint3, waypoint1, and waypoint2. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1, waypoint3, and waypoint0. Objective objective0 is visible from waypoint2 and waypoint1. Objective objective1 is visible from waypoint4. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint4. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint1. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Rock data was communicated from waypoint waypoint0; Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode low_res. Rover rover1 has its camera camera1 calibrated. Store(s) store0 is empty. Store(s) store1 is full. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode colour, Rock data was communicated from waypoint waypoint1;, Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint0;, and Image objective0 was communicated in mode low_res.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Rover rover1 is at waypoint1", "answer": "yes"}
{"id": 7504138218977085100, "group": "landmarks_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera0 supports low_res. Camera camera1 supports low_res and high_res. Rover rover0 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective1 is visible from waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint2. Rovers rover1 and rover0 are available. Rock data was communicated from waypoint waypoint1; Soil data was communicated from waypoint waypoint1; Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective1 in mode low_res. Rover rover0 has its camera camera1 calibrated. Store(s) store1 is empty. Store(s) store0 is full. The goal is to reach a state where the following facts hold: Image objective0 was communicated in mode low_res, Image objective1 was communicated in mode low_res, Soil data was communicated from waypoint waypoint1;, and Rock data was communicated from waypoint waypoint1;.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Image objective1 was communicated in mode low_res", "answer": "yes"}
{"id": 9049837613513031125, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y3, loc-x3-y1, loc-x1-y0, and loc-x0-y2. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x2-y0, loc-x1-y1, loc-x2-y1, and loc-x3-y0. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? the robot is in place loc-x2-y2", "answer": "yes"}
{"id": 3523378070750313112, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x3-y0.The following places have been visited: loc-x3-y1, loc-x2-y1, loc-x2-y2, loc-x3-y3, loc-x1-y4, loc-x0-y1, loc-x2-y4, loc-x1-y3, loc-x1-y0, loc-x3-y2, loc-x3-y4, loc-x0-y2, loc-x0-y0, loc-x1-y2, loc-x1-y1, loc-x0-y4, loc-x3-y0, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y3 has been visited, Place loc-x1-y4 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y4 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y4 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y4 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? the robot is in place loc-x0-y0", "answer": "no"}
{"id": -3588711518378217174, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x3-y2.The following places have been visited: loc-x2-y2, loc-x0-y1, loc-x0-y2, loc-x0-y0, loc-x2-y0, loc-x1-y2, loc-x1-y1, loc-x1-y3, loc-x1-y0, loc-x3-y1, loc-x2-y1, loc-x3-y2, loc-x3-y0, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Place loc-x2-y3 has been visited", "answer": "yes"}
{"id": 4380079466326278459, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x0-y0.The following places have been visited: loc-x0-y1, loc-x0-y2, loc-x0-y0, loc-x1-y2, and loc-x1-y1. The goal is to reach a state where the following facts hold: Place loc-x2-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y3 has been visited, Place loc-x1-y4 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y4 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y4 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y4 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Place loc-x0-y3 has been visited", "answer": "yes"}
{"id": 3587444392823179703, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y3, loc-x3-y1, loc-x1-y0, and loc-x0-y2. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x2-y2, loc-x2-y3, loc-x1-y2, loc-x1-y1, loc-x1-y3, loc-x2-y1, loc-x3-y2, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? the robot is in place loc-x2-y3", "answer": "no"}
{"id": 4991027463510983589, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y0 and loc-x3-y1. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x2-y2, loc-x0-y1, loc-x0-y2, loc-x0-y0, loc-x2-y0, loc-x1-y2, loc-x1-y1, loc-x1-y3, loc-x2-y1, loc-x3-y0, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Place loc-x2-y3 has been visited", "answer": "yes"}
{"id": -8173230092299897472, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x1-y0.The following places have been visited: loc-x0-y1, loc-x0-y2, loc-x0-y0, and loc-x1-y0. The goal is to reach a state where the following facts hold: Place loc-x2-y0 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x2-y2 has been visited, Place loc-x3-y3 has been visited, Place loc-x1-y4 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y4 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y4 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y4 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? the robot is in place loc-x0-y2", "answer": "no"}
{"id": 2792000296749103800, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y4, loc-x1-y2, and loc-x2-y3. Currently, the robot is in place loc-x2-y4.The following places have been visited: loc-x3-y4, loc-x0-y2, loc-x2-y4, loc-x1-y3, loc-x1-y4, and loc-x0-y3. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x3-y4 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y1 has been visited, Place loc-x2-y4 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x1-y4 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Place loc-x3-y2 has been visited", "answer": "yes"}
{"id": -7342651258682572140, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x0-y1, loc-x0-y0, loc-x1-y1, and loc-x1-y0. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Place loc-x3-y2 has been visited", "answer": "yes"}
{"id": -1383135437718035770, "group": "landmarks_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x3-y2.The following places have been visited: loc-x0-y1, loc-x2-y3, loc-x0-y0, loc-x2-y0, loc-x1-y1, loc-x1-y0, loc-x3-y3, loc-x3-y1, loc-x2-y1, loc-x3-y2, loc-x3-y0, and loc-x2-y2. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x0-y1 has been visited, Place loc-x2-y3 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y0 has been visited, and Place loc-x0-y3 has been visited.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Place loc-x0-y3 has been visited", "answer": "yes"}
{"id": 8165825906630188856, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 12 pallets, 10 depots, 12 hoists, numbered consecutively. Currently, pallet1, pallet7, pallet11, pallet5, pallet10, crate1, pallet9, pallet3, pallet4, pallet6, pallet8, and pallet0 are clear; hoist3, hoist1, hoist5, hoist4, hoist2, hoist7, hoist9, hoist10, hoist0, hoist11, and hoist6 are available; hoist5 is at depot5, truck1 is at depot4, pallet4 is at depot4, pallet6 is at depot6, hoist4 is at depot4, hoist2 is at depot2, pallet0 is at depot0, crate1 is at depot2, pallet1 is at depot1, hoist10 is at distributor0, pallet2 is at depot2, hoist8 is at depot8, pallet8 is at depot8, pallet9 is at depot9, hoist7 is at depot7, hoist0 is at depot0, pallet11 is at distributor1, hoist11 is at distributor1, pallet3 is at depot3, pallet5 is at depot5, pallet10 is at distributor0, pallet7 is at depot7, truck0 is at depot0, hoist6 is at depot6, hoist9 is at depot9, hoist1 is at depot1, and hoist3 is at depot3; crate1 is on pallet2; hoist8 is lifting crate0. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? hoist2 is lifting crate1", "answer": "yes"}
{"id": 2758005415289797972, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 5 pallets, 3 depots, 5 hoists, numbered consecutively. Currently, pallet2, pallet3, pallet4, crate0, and pallet0 are clear; hoist3, hoist1, hoist4, hoist2, and hoist0 are available; truck1 is at distributor0, hoist2 is at depot2, crate0 is at depot1, pallet0 is at depot0, pallet3 is at distributor0, pallet1 is at depot1, pallet2 is at depot2, hoist0 is at depot0, pallet4 is at distributor1, truck0 is at distributor0, hoist4 is at distributor1, hoist1 is at depot1, and hoist3 is at distributor0; crate0 is on pallet1; crate1 is in truck1. The goal is to reach a state where the following facts hold: crate1 is on pallet2 and crate0 is on pallet1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? crate0 is on crate1", "answer": "no"}
{"id": -2588131594349645260, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 9 pallets, 7 depots, 9 hoists, numbered consecutively. Currently, pallet2, pallet7, pallet5, crate1, pallet4, crate0, pallet6, pallet8, and pallet0 are clear; hoist3, hoist1, hoist8, hoist5, hoist4, hoist2, hoist7, hoist0, and hoist6 are available; hoist5 is at depot5, pallet4 is at depot4, pallet6 is at depot6, hoist4 is at depot4, hoist2 is at depot2, crate0 is at depot1, pallet0 is at depot0, hoist8 is at distributor1, hoist7 is at distributor0, pallet1 is at depot1, pallet2 is at depot2, pallet8 is at distributor1, hoist0 is at depot0, pallet3 is at depot3, pallet5 is at depot5, pallet7 is at distributor0, truck0 is at distributor0, hoist6 is at depot6, truck1 is at depot2, hoist1 is at depot1, crate1 is at depot3, and hoist3 is at depot3; crate0 is on pallet1 and crate1 is on pallet3. The goal is to reach a state where the following facts hold: crate1 is on pallet3 and crate0 is on pallet8.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? hoist8 is lifting crate0", "answer": "yes"}
{"id": -7868129010590336624, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 12 pallets, 10 depots, 12 hoists, numbered consecutively. Currently, pallet1, pallet2, pallet7, pallet11, pallet5, pallet10, pallet9, pallet3, pallet4, pallet6, pallet8, and pallet0 are clear; hoist3, hoist1, hoist5, hoist4, hoist2, hoist7, hoist9, hoist10, hoist0, hoist11, and hoist6 are available; hoist5 is at depot5, pallet4 is at depot4, pallet6 is at depot6, hoist4 is at depot4, hoist2 is at depot2, pallet0 is at depot0, pallet1 is at depot1, hoist10 is at distributor0, pallet2 is at depot2, hoist8 is at depot8, pallet8 is at depot8, pallet9 is at depot9, truck1 is at depot9, hoist7 is at depot7, hoist0 is at depot0, pallet11 is at distributor1, hoist11 is at distributor1, pallet3 is at depot3, pallet5 is at depot5, pallet10 is at distributor0, pallet7 is at depot7, truck0 is at depot0, hoist6 is at depot6, hoist9 is at depot9, hoist1 is at depot1, and hoist3 is at depot3; crate1 is in truck1; hoist8 is lifting crate0. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? truck0 is at depot5", "answer": "no"}
{"id": 588373741395195971, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 12 pallets, 10 depots, 12 hoists, numbered consecutively. Currently, pallet1, pallet7, pallet11, pallet5, pallet10, crate1, pallet3, pallet9, pallet4, crate0, pallet6, and pallet0 are clear; hoist3, hoist1, hoist8, hoist5, hoist4, hoist2, hoist7, hoist9, hoist10, hoist0, hoist11, and hoist6 are available; hoist5 is at depot5, pallet4 is at depot4, pallet6 is at depot6, hoist4 is at depot4, hoist2 is at depot2, pallet0 is at depot0, crate1 is at depot2, crate0 is at depot8, pallet1 is at depot1, hoist10 is at distributor0, pallet2 is at depot2, hoist8 is at depot8, pallet8 is at depot8, pallet9 is at depot9, hoist7 is at depot7, hoist0 is at depot0, pallet11 is at distributor1, hoist11 is at distributor1, truck1 is at depot7, pallet3 is at depot3, pallet5 is at depot5, pallet10 is at distributor0, pallet7 is at depot7, truck0 is at depot0, hoist6 is at depot6, hoist9 is at depot9, hoist1 is at depot1, and hoist3 is at depot3; crate0 is on pallet8 and crate1 is on pallet2. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? truck1 is at depot6", "answer": "no"}
{"id": 3023192143043428847, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 5 pallets, 3 depots, 5 hoists, numbered consecutively. Currently, pallet2, pallet3, pallet4, crate0, and pallet0 are clear; hoist3, hoist1, hoist4, hoist2, and hoist0 are available; truck1 is at depot1, hoist2 is at depot2, crate0 is at depot1, pallet0 is at depot0, pallet3 is at distributor0, pallet1 is at depot1, pallet2 is at depot2, hoist0 is at depot0, pallet4 is at distributor1, truck0 is at distributor0, hoist4 is at distributor1, hoist1 is at depot1, and hoist3 is at distributor0; crate0 is on pallet1; crate1 is in truck0. The goal is to reach a state where the following facts hold: crate1 is on pallet2 and crate0 is on pallet1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? crate0 is on pallet4", "answer": "no"}
{"id": 5174141819573240570, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 9 pallets, 7 depots, 9 hoists, numbered consecutively. Currently, pallet1, pallet2, pallet7, pallet5, crate1, pallet4, pallet6, pallet8, and pallet0 are clear; hoist3, hoist1, hoist5, hoist4, hoist2, hoist7, hoist0, and hoist6 are available; hoist5 is at depot5, pallet4 is at depot4, pallet6 is at depot6, hoist4 is at depot4, hoist2 is at depot2, pallet0 is at depot0, hoist8 is at distributor1, hoist7 is at distributor0, pallet1 is at depot1, pallet2 is at depot2, pallet8 is at distributor1, truck1 is at depot3, hoist0 is at depot0, pallet3 is at depot3, pallet5 is at depot5, pallet7 is at distributor0, hoist6 is at depot6, truck0 is at depot6, hoist1 is at depot1, crate1 is at depot3, and hoist3 is at depot3; crate1 is on pallet3; hoist8 is lifting crate0. The goal is to reach a state where the following facts hold: crate1 is on pallet3 and crate0 is on pallet8.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? crate0 is on pallet8", "answer": "yes"}
{"id": -7442729827958268158, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 4 pallets, 2 depots, 4 hoists, numbered consecutively. Currently, pallet1, pallet2, pallet3, and crate0 are clear; hoist3, hoist1, hoist2, and hoist0 are available; hoist2 is at distributor0, pallet0 is at depot0, crate0 is at depot0, pallet1 is at depot1, pallet2 is at distributor0, hoist0 is at depot0, truck1 is at distributor1, truck0 is at depot0, pallet3 is at distributor1, hoist1 is at depot1, and hoist3 is at distributor1; crate0 is on pallet0; crate1 is in truck0. The goal is to reach a state where the following facts hold: crate1 is on pallet0 and crate0 is on crate1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? hoist1 is lifting crate0", "answer": "no"}
{"id": 7526694284247197983, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 9 pallets, 7 depots, 9 hoists, numbered consecutively. Currently, pallet1, pallet2, pallet7, pallet5, crate1, pallet4, pallet6, pallet8, and pallet0 are clear; hoist3, hoist1, hoist8, hoist5, hoist4, hoist2, hoist7, hoist0, and hoist6 are available; hoist5 is at depot5, pallet4 is at depot4, pallet6 is at depot6, hoist4 is at depot4, hoist2 is at depot2, pallet0 is at depot0, hoist8 is at distributor1, hoist7 is at distributor0, pallet1 is at depot1, pallet2 is at depot2, pallet8 is at distributor1, hoist0 is at depot0, pallet3 is at depot3, pallet5 is at depot5, pallet7 is at distributor0, truck1 is at depot0, hoist6 is at depot6, hoist1 is at depot1, crate1 is at depot3, hoist3 is at depot3, and truck0 is at depot1; crate1 is on pallet3; crate0 is in truck0. The goal is to reach a state where the following facts hold: crate1 is on pallet3 and crate0 is on pallet8.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? hoist4 is lifting crate0", "answer": "no"}
{"id": -5415575682295523374, "group": "landmarks_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 2 trucks, 5 pallets, 3 depots, 5 hoists, numbered consecutively. Currently, pallet2, crate1, pallet3, pallet4, and pallet0 are clear; hoist3, hoist1, hoist4, hoist2, and hoist0 are available; crate1 is at depot1, hoist2 is at depot2, crate0 is at depot1, pallet0 is at depot0, pallet3 is at distributor0, pallet1 is at depot1, pallet2 is at depot2, hoist0 is at depot0, pallet4 is at distributor1, hoist4 is at distributor1, truck1 is at depot2, hoist1 is at depot1, hoist3 is at distributor0, and truck0 is at depot1; crate0 is on pallet1 and crate1 is on crate0. The goal is to reach a state where the following facts hold: crate1 is on pallet2 and crate0 is on pallet1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? crate1 is on crate1", "answer": "no"}
{"id": 280253941723595118, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have hard rock: f1-4f, f1-1f, and f1-3f. The following locations have soft rock: f0-1f, f1-2f, f2-2f, f0-2f, f0-3f, f2-4f, f0-4f, f2-1f, and f2-3f. The gold is at f0-4f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robot is at position f0-4f", "answer": "yes"}
{"id": -8617844163438410652, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and its arm is empty. The following locations have hard rock: f2-1f, f2-2f, and f0-3f. The following locations have soft rock: f1-3f, f0-1f, f0-2f, and f2-3f. The gold is at f1-3f location. The laser is at f1-1f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The laser is at f0-0f location", "answer": "no"}
{"id": 6897451989597429681, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have hard rock: f1-4f and f1-3f. The following locations have soft rock: f1-2f, f2-2f, f2-4f, f0-4f, f2-1f, and f2-3f. The gold is at f0-4f location. The laser is at f0-1f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The laser is at f1-1f location", "answer": "no"}
{"id": -8407702394168503875, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and is holding a bomb. The following locations have hard rock: f1-2f, f1-3f, f3-3f, and f3-1f. The following locations have soft rock: f2-2f, f3-2f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robot is at position f0-3f", "answer": "yes"}
{"id": -5388907887471418014, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-2f and its arm is empty. The following locations have hard rock: f1-4f, f1-1f, and f1-3f. The following locations have soft rock: f1-2f, f2-2f, f2-4f, f2-1f, and f2-3f. The gold is at f0-4f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robot is at position f0-4f", "answer": "yes"}
{"id": -4135187869449008489, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x3 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and is holding a laser. The following locations have hard rock: f1-2f. The following locations have soft rock: f2-1f, f2-2f, f0-2f, and f1-1f. The gold is at f0-2f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robot is at position f0-2f", "answer": "yes"}
{"id": -1530896123572976710, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-2f and its arm is empty. The following locations have hard rock: f1-2f, f1-1f, f1-3f, f3-3f, and f3-1f. The following locations have soft rock: f2-2f, f3-2f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Location(s) f0-3f is clear", "answer": "yes"}
{"id": 2024454633805047539, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x3 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and its arm is empty. The following locations have hard rock: f1-2f. The following locations have soft rock: f2-1f, f2-2f, f0-2f, and f1-1f. The gold is at f0-2f location. The laser is at f0-1f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robot is at position f1-2f", "answer": "no"}
{"id": 6667684931946719687, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and is holding a bomb. The following locations have hard rock: f1-2f, f1-1f, f1-3f, f3-3f, and f3-1f. The following locations have soft rock: f2-2f, f3-2f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-1f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? The robot's arm is empty", "answer": "yes"}
{"id": 7068634563046551094, "group": "landmarks_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-2f, f1-1f, f1-3f, f3-3f, and f3-1f. The following locations have soft rock: f2-2f, f3-2f, f0-3f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Soft rock at f2-1f", "answer": "no"}
{"id": -5040640315998551261, "group": "landmarks_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation4, groundstation5, groundstation6, star9, star10, groundstation3, star0, star2, planet8, planet7, groundstation1. There are 3 image mode(s): spectrograph0, thermograph2, infrared1. There are 11 instrument(s), numbered consecutively.  Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite1 has following instruments onboard: instrument2. Satellite satellite2 has following instruments onboard: instrument5, instrument3, instrument4. Satellite satellite3 has following instruments onboard: instrument6. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument1 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument6 supports image of mode thermograph2 and its calibration target is star0. Instrument instrument2 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument4 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument9 supports image of mode infrared1 and its calibration target is groundstation6. Instrument instrument3 supports image of mode spectrograph0 and its calibration target is groundstation1. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite1 is pointing to groundstation4. Satellite satellite0 is pointing to star10. Satellite satellite5 is pointing to star2. Satellite satellite2 is pointing to star0. Satellite satellite3 is pointing to star10. Satellite satellite4 is pointing to groundstation3. Power is available on the following satellite(s): satellite3, satellite4, satellite2, satellite0, satellite5. Following instruments are powered on: instrument2. Following instruments are calibrated: instrument2. A infrared1 mode image of target planet8 is available. A thermograph2 mode image of target planet7 is available. The goal is to reach a state where the following facts hold: A infrared1 mode image of target star9 is available, A infrared1 mode image of target star10 is available, A infrared1 mode image of target planet8 is available, Satellite satellite4 is pointing to star10, Satellite satellite5 is pointing to star2, A thermograph2 mode image of target planet7 is available, and Satellite satellite1 is pointing to star9.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Satellite satellite1 is pointing to star9", "answer": "yes"}
{"id": -4476151580692985538, "group": "landmarks_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): planet5, groundstation3, groundstation0, star2, planet6, star4, groundstation1. There are 3 image mode(s): image1, thermograph0, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite4 has following instruments onboard: instrument7, instrument6. Satellite satellite8 has following instruments onboard: instrument13, instrument14. Satellite satellite3 has following instruments onboard: instrument5, instrument4. Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite6 has following instruments onboard: instrument11, instrument10, instrument9. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite5 has following instruments onboard: instrument8. Satellite satellite2 has following instruments onboard: instrument3. Instrument instrument8 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument4 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument12 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument6 supports image of mode thermograph0 and its calibration target is groundstation0. Instrument instrument2 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument7 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument10 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3.  Currently, Satellite satellite0 is pointing to groundstation0. Satellite satellite2 is pointing to planet6. Satellite satellite1 is pointing to groundstation0. Satellite satellite5 is pointing to planet5. Satellite satellite9 is pointing to star4. Satellite satellite4 is pointing to planet6. Satellite satellite6 is pointing to star4. Satellite satellite3 is pointing to planet6. Satellite satellite7 is pointing to star2. Satellite satellite8 is pointing to planet6. Power is available on the following satellite(s): satellite6, satellite4, satellite2, satellite7, satellite1, satellite9, satellite5, satellite0, satellite8. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A infrared2 mode image of target planet6 is available. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available, Satellite satellite8 is pointing to planet6, A thermograph0 mode image of target planet5 is available, and Satellite satellite6 is pointing to star4.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? A image1 mode image of target groundstation0 is available", "answer": "no"}
{"id": -5074405785362499018, "group": "landmarks_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): planet5, groundstation3, groundstation0, star2, planet6, star4, groundstation1. There are 3 image mode(s): image1, thermograph0, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite4 has following instruments onboard: instrument7, instrument6. Satellite satellite8 has following instruments onboard: instrument13, instrument14. Satellite satellite3 has following instruments onboard: instrument5, instrument4. Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite6 has following instruments onboard: instrument11, instrument10, instrument9. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite5 has following instruments onboard: instrument8. Satellite satellite2 has following instruments onboard: instrument3. Instrument instrument8 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument4 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument12 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument6 supports image of mode thermograph0 and its calibration target is groundstation0. Instrument instrument2 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument7 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument10 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3.  Currently, Satellite satellite3 is pointing to groundstation1. Satellite satellite2 is pointing to planet6. Satellite satellite1 is pointing to groundstation0. Satellite satellite9 is pointing to star4. Satellite satellite4 is pointing to planet6. Satellite satellite0 is pointing to groundstation1. Satellite satellite7 is pointing to star2. Satellite satellite5 is pointing to groundstation3. Satellite satellite6 is pointing to planet5. Satellite satellite8 is pointing to planet6. Power is available on the following satellite(s): satellite6, satellite4, satellite2, satellite7, satellite1, satellite9, satellite3, satellite5, satellite0, satellite8. Following instruments are calibrated: instrument10. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available, Satellite satellite8 is pointing to planet6, A thermograph0 mode image of target planet5 is available, and Satellite satellite6 is pointing to star4.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? A thermograph0 mode image of target star2 is available", "answer": "no"}
{"id": -5550727107576898670, "group": "landmarks_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, star3, groundstation0, groundstation2, phenomenon5, star6, star1. There are 3 image mode(s): image1, image0, image2. There are 18 instrument(s), numbered consecutively.  Satellite satellite5 has following instruments onboard: instrument13, instrument12, instrument14. Satellite satellite6 has following instruments onboard: instrument17, instrument16, instrument15. Satellite satellite1 has following instruments onboard: instrument4, instrument3, instrument5. Satellite satellite0 has following instruments onboard: instrument1, instrument0, instrument2. Satellite satellite4 has following instruments onboard: instrument11, instrument10, instrument9. Satellite satellite3 has following instruments onboard: instrument8, instrument7. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument3 supports image of mode image1 and its calibration target is star3. Instrument instrument15 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument2 supports image of mode image2 and its calibration target is star1. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument5 supports image of mode image2 and its calibration target is star3. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument10 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite6 is pointing to groundstation0. Satellite satellite3 is pointing to groundstation4. Satellite satellite4 is pointing to star1. Satellite satellite0 is pointing to groundstation2. Satellite satellite2 is pointing to star1. Satellite satellite1 is pointing to phenomenon5. Satellite satellite5 is pointing to groundstation0. Power is available on the following satellite(s): satellite3, satellite6, satellite4, satellite5, satellite0. Following instruments are powered on: instrument6, instrument4. Following instruments are calibrated: instrument6. A image0 mode image of target phenomenon5 is available. The goal is to reach a state where the following facts hold: A image1 mode image of target star6 is available, Satellite satellite4 is pointing to star1, Satellite satellite5 is pointing to groundstation0, Satellite satellite1 is pointing to phenomenon5, and A image0 mode image of target phenomenon5 is available.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Satellite satellite3 is pointing to groundstation0", "answer": "no"}
{"id": -7936534715464209787, "group": "landmarks_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): planet5, groundstation3, groundstation0, star2, planet6, star4, groundstation1. There are 3 image mode(s): image1, thermograph0, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite4 has following instruments onboard: instrument7, instrument6. Satellite satellite8 has following instruments onboard: instrument13, instrument14. Satellite satellite3 has following instruments onboard: instrument5, instrument4. Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite6 has following instruments onboard: instrument11, instrument10, instrument9. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite5 has following instruments onboard: instrument8. Satellite satellite2 has following instruments onboard: instrument3. Instrument instrument8 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument4 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument12 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument6 supports image of mode thermograph0 and its calibration target is groundstation0. Instrument instrument2 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument7 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument10 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3.  Currently, Satellite satellite2 is pointing to planet6. Satellite satellite1 is pointing to groundstation0. Satellite satellite8 is pointing to star4. Satellite satellite9 is pointing to star4. Satellite satellite4 is pointing to planet6. Satellite satellite3 is pointing to planet5. Satellite satellite0 is pointing to star4. Satellite satellite6 is pointing to star4. Satellite satellite5 is pointing to groundstation3. Satellite satellite7 is pointing to groundstation1. Power is available on the following satellite(s): satellite4, satellite2, satellite7, satellite1, satellite9, satellite3, satellite5, satellite0, satellite8. Following instruments are powered on: instrument10. Following instruments are calibrated: instrument10. A thermograph0 mode image of target planet5 is available. A infrared2 mode image of target planet6 is available. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available, Satellite satellite8 is pointing to planet6, A thermograph0 mode image of target planet5 is available, and Satellite satellite6 is pointing to star4.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Satellite satellite8 is pointing to planet6", "answer": "yes"}
{"id": -6942467935726448902, "group": "landmarks_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation4, groundstation5, groundstation6, star9, star10, groundstation3, star0, star2, planet8, planet7, groundstation1. There are 3 image mode(s): spectrograph0, thermograph2, infrared1. There are 11 instrument(s), numbered consecutively.  Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite1 has following instruments onboard: instrument2. Satellite satellite2 has following instruments onboard: instrument5, instrument3, instrument4. Satellite satellite3 has following instruments onboard: instrument6. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument1 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument6 supports image of mode thermograph2 and its calibration target is star0. Instrument instrument2 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument4 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument9 supports image of mode infrared1 and its calibration target is groundstation6. Instrument instrument3 supports image of mode spectrograph0 and its calibration target is groundstation1. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite5 is pointing to star0. Satellite satellite3 is pointing to planet7. Satellite satellite0 is pointing to star10. Satellite satellite1 is pointing to star2. Satellite satellite2 is pointing to star0. Satellite satellite4 is pointing to planet7. Power is available on the following satellite(s): satellite3, satellite4, satellite1, satellite2, satellite5, satellite0. The goal is to reach a state where the following facts hold: A infrared1 mode image of target star9 is available, A infrared1 mode image of target star10 is available, A infrared1 mode image of target planet8 is available, Satellite satellite4 is pointing to star10, Satellite satellite5 is pointing to star2, A thermograph2 mode image of target planet7 is available, and Satellite satellite1 is pointing to star9.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Following instruments are powered on: instrument3", "answer": "no"}
{"id": -5471277051870733893, "group": "landmarks_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): planet5, groundstation3, groundstation0, star2, planet6, star4, groundstation1. There are 3 image mode(s): image1, thermograph0, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode infrared2 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. Following instruments are calibrated: instrument0. A thermograph0 mode image of target planet5 is available. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available and A thermograph0 mode image of target planet5 is available.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Following instruments are powered on: instrument0", "answer": "yes"}
{"id": 9008713329518435367, "group": "landmarks_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation4, groundstation5, groundstation6, star9, star10, groundstation3, star0, star2, planet8, planet7, groundstation1. There are 3 image mode(s): spectrograph0, thermograph2, infrared1. There are 11 instrument(s), numbered consecutively.  Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite1 has following instruments onboard: instrument2. Satellite satellite2 has following instruments onboard: instrument5, instrument3, instrument4. Satellite satellite3 has following instruments onboard: instrument6. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument1 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument6 supports image of mode thermograph2 and its calibration target is star0. Instrument instrument2 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument4 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument9 supports image of mode infrared1 and its calibration target is groundstation6. Instrument instrument3 supports image of mode spectrograph0 and its calibration target is groundstation1. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to star10. Satellite satellite4 is pointing to star10. Satellite satellite5 is pointing to star2. Satellite satellite2 is pointing to star10. Satellite satellite3 is pointing to star9. Satellite satellite1 is pointing to groundstation3. Power is available on the following satellite(s): satellite3, satellite4, satellite2, satellite0, satellite5. Following instruments are powered on: instrument2. Following instruments are calibrated: instrument2. A infrared1 mode image of target star10 is available. A infrared1 mode image of target planet8 is available. A thermograph2 mode image of target planet7 is available. The goal is to reach a state where the following facts hold: A infrared1 mode image of target star9 is available, A infrared1 mode image of target star10 is available, A infrared1 mode image of target planet8 is available, Satellite satellite4 is pointing to star10, Satellite satellite5 is pointing to star2, A thermograph2 mode image of target planet7 is available, and Satellite satellite1 is pointing to star9.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? A thermograph2 mode image of target star10 is available", "answer": "no"}
{"id": 2925272560276387824, "group": "landmarks_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, planet5, groundstation3, star0, star2, phenomenon6, groundstation1. There are 3 image mode(s): spectrograph0, thermograph2, infrared1. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument1, instrument2. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument4 supports image of mode infrared1 and its calibration target is star2. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument2 supports image of mode infrared1 and its calibration target is groundstation3.  Currently, Satellite satellite1 is pointing to phenomenon6. Satellite satellite2 is pointing to star2. Satellite satellite0 is pointing to star2. Power is available on the following satellite(s): satellite1, satellite0. Following instruments are powered on: instrument4. The goal is to reach a state where the following facts hold: A spectrograph0 mode image of target phenomenon6 is available, A thermograph2 mode image of target planet5 is available, and Satellite satellite1 is pointing to phenomenon6.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Following instruments are calibrated: instrument1", "answer": "no"}
{"id": -3015671225445717270, "group": "landmarks_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): planet5, groundstation3, groundstation0, star2, planet6, star4, groundstation1. There are 3 image mode(s): image1, thermograph0, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite4 has following instruments onboard: instrument7, instrument6. Satellite satellite8 has following instruments onboard: instrument13, instrument14. Satellite satellite3 has following instruments onboard: instrument5, instrument4. Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite6 has following instruments onboard: instrument11, instrument10, instrument9. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite5 has following instruments onboard: instrument8. Satellite satellite2 has following instruments onboard: instrument3. Instrument instrument8 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument4 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument12 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument6 supports image of mode thermograph0 and its calibration target is groundstation0. Instrument instrument2 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument7 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument10 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3.  Currently, Satellite satellite2 is pointing to planet6. Satellite satellite1 is pointing to groundstation0. Satellite satellite9 is pointing to star4. Satellite satellite8 is pointing to star2. Satellite satellite4 is pointing to planet6. Satellite satellite3 is pointing to planet5. Satellite satellite6 is pointing to groundstation1. Satellite satellite0 is pointing to groundstation1. Satellite satellite7 is pointing to star2. Satellite satellite5 is pointing to groundstation3. Power is available on the following satellite(s): satellite6, satellite4, satellite2, satellite7, satellite1, satellite9, satellite5, satellite0, satellite8. Following instruments are powered on: instrument5. Following instruments are calibrated: instrument5. A infrared2 mode image of target planet6 is available. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available, Satellite satellite8 is pointing to planet6, A thermograph0 mode image of target planet5 is available, and Satellite satellite6 is pointing to star4.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Satellite satellite9 is pointing to planet5", "answer": "no"}
{"id": 6812576411098478670, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, alice, zoe, vic, carol, dave, and xena. There are 8 items/roles: iceskates, frisbee, necklace, quadcopter, slinky, guitar, whale, and zebra. Currently, michelle is assigned zebra, xena is assigned slinky, vic is assigned whale, alice is assigned iceskates, carol is assigned frisbee, dave is assigned guitar, zoe is assigned quadcopter, and heidi is assigned necklace. The goal is to reach a state where the following facts hold: xena is assigned slinky, vic is assigned necklace, alice is assigned zebra, zoe is assigned whale, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, and dave is assigned iceskates.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? xena is assigned frisbee", "answer": "no"}
{"id": -2290004044155730302, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: frank, bob, liam, quentin, vic, and xena. There are 6 items/roles: pliers, nibbler, wrench, sander, ratchet, and knead. Currently, vic is assigned wrench, quentin is assigned ratchet, xena is assigned sander, liam is assigned nibbler, bob is assigned pliers, and frank is assigned knead. The goal is to reach a state where the following facts hold: xena is assigned wrench, quentin is assigned knead, bob is assigned nibbler, frank is assigned pliers, liam is assigned sander, and vic is assigned ratchet.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? quentin is assigned sander", "answer": "no"}
{"id": -5434473652666980808, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, alice, zoe, vic, carol, dave, and xena. There are 8 items/roles: iceskates, frisbee, necklace, quadcopter, slinky, guitar, whale, and zebra. Currently, xena is assigned zebra, alice is assigned necklace, carol is assigned whale, zoe is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, vic is assigned slinky, and dave is assigned iceskates. The goal is to reach a state where the following facts hold: xena is assigned slinky, vic is assigned necklace, alice is assigned zebra, zoe is assigned whale, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, and dave is assigned iceskates.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? vic is assigned necklace", "answer": "yes"}
{"id": -5516021290527302588, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: frank, bob, liam, quentin, vic, and xena. There are 6 items/roles: pliers, nibbler, wrench, sander, ratchet, and knead. Currently, bob is assigned nibbler, xena is assigned knead, quentin is assigned ratchet, liam is assigned sander, frank is assigned wrench, and vic is assigned pliers. The goal is to reach a state where the following facts hold: xena is assigned wrench, quentin is assigned knead, bob is assigned nibbler, frank is assigned pliers, liam is assigned sander, and vic is assigned ratchet.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? quentin is assigned sander", "answer": "no"}
{"id": 2049834357869796001, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, alice, zoe, vic, carol, dave, and xena. There are 8 items/roles: iceskates, frisbee, necklace, quadcopter, slinky, guitar, whale, and zebra. Currently, xena is assigned whale, alice is assigned zebra, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, vic is assigned slinky, dave is assigned iceskates, and zoe is assigned necklace. The goal is to reach a state where the following facts hold: xena is assigned slinky, vic is assigned necklace, alice is assigned zebra, zoe is assigned whale, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, and dave is assigned iceskates.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? vic is assigned necklace", "answer": "yes"}
{"id": 6229289268775576392, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: heidi, ted, alice, bob, kevin, dave, and xena. There are 7 items/roles: valerian, yam, parsnip, mushroom, quince, leek, and ulluco. Currently, heidi is assigned quince, kevin is assigned mushroom, dave is assigned parsnip, xena is assigned leek, bob is assigned valerian, ted is assigned ulluco, and alice is assigned yam. The goal is to reach a state where the following facts hold: kevin is assigned yam, bob is assigned parsnip, heidi is assigned mushroom, ted is assigned leek, xena is assigned quince, dave is assigned ulluco, and alice is assigned valerian.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? heidi is assigned mushroom", "answer": "yes"}
{"id": 2212203000903997920, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, alice, zoe, vic, carol, dave, and xena. There are 8 items/roles: iceskates, frisbee, necklace, quadcopter, slinky, guitar, whale, and zebra. Currently, xena is assigned slinky, vic is assigned guitar, carol is assigned quadcopter, heidi is assigned zebra, zoe is assigned whale, michelle is assigned necklace, dave is assigned iceskates, and alice is assigned frisbee. The goal is to reach a state where the following facts hold: xena is assigned slinky, vic is assigned necklace, alice is assigned zebra, zoe is assigned whale, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, and dave is assigned iceskates.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? vic is assigned necklace", "answer": "yes"}
{"id": -5124016585761956714, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: frank, bob, liam, quentin, vic, and xena. There are 6 items/roles: pliers, nibbler, wrench, sander, ratchet, and knead. Currently, frank is assigned nibbler, xena is assigned wrench, quentin is assigned sander, liam is assigned knead, bob is assigned pliers, and vic is assigned ratchet. The goal is to reach a state where the following facts hold: xena is assigned wrench, quentin is assigned knead, bob is assigned nibbler, frank is assigned pliers, liam is assigned sander, and vic is assigned ratchet.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? bob is assigned nibbler", "answer": "yes"}
{"id": -7356083227961421425, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, alice, zoe, vic, carol, dave, and xena. There are 8 items/roles: iceskates, frisbee, necklace, quadcopter, slinky, guitar, whale, and zebra. Currently, michelle is assigned frisbee, vic is assigned quadcopter, xena is assigned slinky, alice is assigned whale, zoe is assigned zebra, dave is assigned iceskates, carol is assigned guitar, and heidi is assigned necklace. The goal is to reach a state where the following facts hold: xena is assigned slinky, vic is assigned necklace, alice is assigned zebra, zoe is assigned whale, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, and dave is assigned iceskates.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? michelle is assigned quadcopter", "answer": "yes"}
{"id": 3735483598263633190, "group": "landmarks_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 4 agents: bob, steve, zoe, and vic. There are 4 items/roles: book04, book02, book03, and book01. Currently, vic is assigned book04, bob is assigned book01, steve is assigned book03, and zoe is assigned book02. The goal is to reach a state where the following facts hold: vic is assigned book04, bob is assigned book02, zoe is assigned book01, and steve is assigned book03.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? bob is assigned book03", "answer": "no"}
{"id": -5324534174348056188, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. shelf6 is at location24. bed1 is at location13. shelf1 is at location20. shelf5 is at location22. drawer3 is at location17. drawer4 and drawer5 are at location12. safe1 is at location6. desk2 is at location10. drawer1 is at location21. garbagecan1 is at location2. laundryhamper1 is at location8. shelf3 is at location11. desk1 is at location3. shelf2 is at location25. shelf4 is at location23. drawer2 is at location18. drawer6 is at location1.  Currently, the objects are at locations as follows. blinds2 is at location15. cellphone3 is at location12. laptop1, laptop2, pillow1, cellphone1, and pillow2 are at location13. mirror1 is at location19. keychain2 and keychain1 are at location6. bowl3 is at location24. basketball1 is at location7. window2 is at location4. cd1, alarmclock1, mug1, bowl1, and pencil1 are at location3. pencil3, cellphone2, mug2, cd3, and pen1 are at location10. alarmclock3, bowl2, and desklamp1 are at location23. blinds1 is at location16. creditcard1 and pencil2 are at location22. window1 is at location5. alarmclock2 is at location11. laundryhamperlid1 is at location8. chair2 is at location26. cd2 is at location2. lightswitch1 is at location14. chair1 is at location21. baseballbat1 is at location9. agent agent1 is at location location25. The objects are in/on receptacle as follows. mug2, cd3, alarmclock3, cellphone2, bowl2, desklamp1, pencil3, and pen1 are on desk2. laptop2, pillow2, laptop1, pillow1, and cellphone1 are in bed1. mug1, pencil1, alarmclock1, cd1, and bowl1 are on desk1. alarmclock2 is on shelf3. alarmclock3, bowl2, and desklamp1 are on shelf4. cd2 is in garbagecan1. keychain2 and keychain1 are in safe1. pencil2 and creditcard1 are on shelf5. bowl3 is on shelf6. cellphone3 is in drawer5. drawer3, drawer1, drawer6, and safe1 are closed. desklamp1 is off. Nothing has been validated. agent1 is holding object book1. The goal is to reach a state where the following facts hold: It has been validated that an object of type booktype is examined under an object of type desklamptype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? keychain2 is in drawer3", "answer": "no"}
{"id": -1959964052239426071, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet6 is at location4. countertop3 is at location3. stoveburner4 and stoveburner2 are at location21. stoveburner3 and stoveburner1 are at location5. cabinet2 is at location22. shelf2 is at location29. sinkbasin1 is at location6. shelf3 is at location26. drawer1 is at location20. drawer2 is at location15. cabinet5 is at location13. toaster1 is at location9. fridge1 is at location10. countertop2 and coffeemachine1 are at location11. cabinet3 is at location16. garbagecan1 is at location8. drawer3 is at location27. cabinet4 is at location14. shelf1 is at location17. cabinet1 is at location19. microwave1 is at location24. countertop1 is at location2.  Currently, the objects are at locations as follows. stoveknob2 and stoveknob3 are at location12. dishsponge1 and fork1 are at location27. vase1 and dishsponge2 are at location13. egg1, plate2, potato1, bowl1, mug1, tomato1, and egg2 are at location10. glassbottle3, potato3, and apple2 are at location8. cellphone1, lettuce1, lettuce2, plate1, peppershaker1, apple1, knife1, and cellphone2 are at location2. bread1, houseplant1, glassbottle1, creditcard1, papertowelroll1, knife2, spoon2, spatula1, cellphone3, plate3, and butterknife1 are at location3. spatula2, potato2, sink1, glassbottle2, and spatula3 are at location6. bowl2 is at location29. soapbottle1 is at location4. vase2 is at location17. window1 is at location30. stoveknob4 is at location7. soapbottle2 and statue1 are at location26. pan1, spoon1, and mug2 are at location11. chair2 is at location1. lightswitch1 is at location25. window2 is at location28. saltshaker1 is at location15. stoveknob1 is at location18. chair1 is at location23. peppershaker3 is at location16. cup1 is at location24. peppershaker2 is at location20. pot1 is at location5. agent agent1 is at location location19. The objects are in/on receptacle as follows. knife2, cellphone3, butterknife1, papertowelroll1, houseplant1, glassbottle1, creditcard1, spatula1, bread1, plate3, and spoon2 are on countertop3. potato1, bowl1, tomato1, mug1, egg2, plate2, and egg1 are in fridge1. pan1 is on stoveburner2. cellphone1, cellphone2, lettuce2, peppershaker1, knife1, apple1, lettuce1, and plate1 are on countertop1. potato3, apple2, and glassbottle3 are in garbagecan1. spoon1 and pan1 are on countertop2. peppershaker3 is in cabinet3. fork1 and dishsponge1 are in drawer3. vase2 is on shelf1. pot1 is on stoveburner1. statue1 and soapbottle2 are on shelf3. cup1 is in microwave1. mug2 is in coffeemachine1. spatula3, potato2, glassbottle2, and spatula2 are in sinkbasin1. saltshaker1 is in drawer2. peppershaker2 is in drawer1. bowl2 is on shelf2. dishsponge2 and vase1 are in cabinet5. soapbottle1 is in cabinet6. pan1 is on stoveburner4. cellphone2 is on plate1. pot1 is on stoveburner3. cabinet2, microwave1, fridge1, drawer1, cabinet1, drawer3, drawer2, and cabinet5 are closed. mug1 is cool. mug2 is hot. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type mugtype is hot and is in a receptacle of type coffeemachinetype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? soapbottle2 is in cabinet4", "answer": "no"}
{"id": -3122691734874038778, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet6 is at location4. countertop3 is at location3. stoveburner4 and stoveburner2 are at location21. stoveburner3 and stoveburner1 are at location5. cabinet2 is at location22. shelf2 is at location29. sinkbasin1 is at location6. shelf3 is at location26. drawer1 is at location20. drawer2 is at location15. cabinet5 is at location13. toaster1 is at location9. fridge1 is at location10. countertop2 and coffeemachine1 are at location11. cabinet3 is at location16. garbagecan1 is at location8. drawer3 is at location27. cabinet4 is at location14. shelf1 is at location17. cabinet1 is at location19. microwave1 is at location24. countertop1 is at location2.  Currently, the objects are at locations as follows. saltshaker1 is at location16. saltshaker2, lettuce3, houseplant1, tomato1, glassbottle3, spoon3, soapbottle1, butterknife1, fork2, and knife1 are at location3. potato2, cup2, potato1, apple1, lettuce1, and cup1 are at location10. peppershaker1, cellphone1, and pan1 are at location11. stoveknob2 and stoveknob3 are at location12. glassbottle2 and bowl1 are at location14. papertowelroll1 and vase2 are at location29. mug1, bread1, lettuce2, statue1, creditcard1, creditcard2, spoon1, and cellphone3 are at location2. spatula2, spoon2, sink1, and fork1 are at location6. saltshaker3 is at location26. glassbottle1 is at location19. window1 is at location30. plate1 is at location4. stoveknob4 is at location7. chair2 is at location1. lightswitch1 is at location25. spatula1 is at location27. window2 is at location28. apple2 and egg1 are at location8. dishsponge1 is at location15. stoveknob1 is at location18. chair1 is at location23. cellphone2 and peppershaker2 are at location20. pot1 is at location5. vase1 is at location17. agent agent1 is at location location6. The objects are in/on receptacle as follows. cup1, potato1, apple1, cup2, lettuce1, and potato2 are in fridge1. fork2, glassbottle3, soapbottle1, butterknife1, lettuce3, houseplant1, tomato1, saltshaker2, spoon3, and knife1 are on countertop3. fork1, spoon2, and spatula2 are in sinkbasin1. bowl1 and glassbottle2 are in cabinet4. cellphone2 and peppershaker2 are in drawer1. pan1 is on stoveburner2. saltshaker1 is in cabinet3. statue1, mug1, bread1, lettuce2, cellphone3, creditcard2, creditcard1, and spoon1 are on countertop1. plate1 is in cabinet6. apple2 and egg1 are in garbagecan1. papertowelroll1 and vase2 are on shelf2. pot1 is on stoveburner1. vase1 is on shelf1. cellphone1, pan1, and peppershaker1 are on countertop2. spatula1 is in drawer3. pan1 is on stoveburner4. dishsponge1 is in drawer2. saltshaker3 is on shelf3. pot1 is on stoveburner3. glassbottle1 is in cabinet1. cabinet2, microwave1, fridge1, drawer1, cabinet1, drawer3, drawer2, and cabinet5 are closed. egg2 is hot. Nothing has been validated. agent1 is holding object egg2. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? plate1 is at location17", "answer": "no"}
{"id": -2867874079787021419, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. shelf6 is at location24. bed1 is at location13. shelf1 is at location20. shelf5 is at location22. drawer3 is at location17. drawer4 and drawer5 are at location12. safe1 is at location6. desk2 is at location10. drawer1 is at location21. garbagecan1 is at location2. laundryhamper1 is at location8. shelf3 is at location11. desk1 is at location3. shelf2 is at location25. shelf4 is at location23. drawer2 is at location18. drawer6 is at location1.  Currently, the objects are at locations as follows. blinds2 is at location15. cellphone3 is at location12. laptop1, book1, laptop2, pillow1, cellphone1, and pillow2 are at location13. mirror1 is at location19. keychain2 and keychain1 are at location6. bowl3 is at location24. basketball1 is at location7. window2 is at location4. cd1, alarmclock1, mug1, bowl1, and pencil1 are at location3. pencil3, cellphone2, mug2, cd3, and pen1 are at location10. alarmclock3, bowl2, and desklamp1 are at location23. blinds1 is at location16. creditcard1 and pencil2 are at location22. window1 is at location5. alarmclock2 is at location11. laundryhamperlid1 is at location8. chair2 is at location26. cd2 is at location2. lightswitch1 is at location14. chair1 is at location21. baseballbat1 is at location9. agent agent1 is at location location2. The objects are in/on receptacle as follows. mug2, cd3, alarmclock3, cellphone2, bowl2, desklamp1, pencil3, and pen1 are on desk2. laptop2, book1, pillow2, laptop1, pillow1, and cellphone1 are in bed1. mug1, pencil1, alarmclock1, cd1, and bowl1 are on desk1. alarmclock2 is on shelf3. alarmclock3, bowl2, and desklamp1 are on shelf4. cd2 is in garbagecan1. keychain2 and keychain1 are in safe1. pencil2 and creditcard1 are on shelf5. bowl3 is on shelf6. cellphone3 is in drawer5. drawer3, drawer1, drawer6, and safe1 are closed. desklamp1 is off. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type booktype is examined under an object of type desklamptype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? agent1 is holding object book1", "answer": "yes"}
{"id": 5615063566305886031, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet6 is at location4. countertop3 is at location3. stoveburner4 and stoveburner2 are at location21. stoveburner3 and stoveburner1 are at location5. cabinet2 is at location22. shelf2 is at location29. sinkbasin1 is at location6. shelf3 is at location26. drawer1 is at location20. drawer2 is at location15. cabinet5 is at location13. toaster1 is at location9. fridge1 is at location10. countertop2 and coffeemachine1 are at location11. cabinet3 is at location16. garbagecan1 is at location8. drawer3 is at location27. cabinet4 is at location14. shelf1 is at location17. cabinet1 is at location19. microwave1 is at location24. countertop1 is at location2.  Currently, the objects are at locations as follows. saltshaker1 is at location16. saltshaker2, lettuce3, houseplant1, tomato1, glassbottle3, spoon3, soapbottle1, butterknife1, fork2, and knife1 are at location3. potato2, cup2, potato1, apple1, lettuce1, and cup1 are at location10. peppershaker1, cellphone1, and pan1 are at location11. stoveknob2 and stoveknob3 are at location12. glassbottle2 and bowl1 are at location14. egg2 and apple2 are at location8. papertowelroll1 and vase2 are at location29. mug1, bread1, lettuce2, statue1, creditcard1, creditcard2, spoon1, and cellphone3 are at location2. spatula2, spoon2, sink1, and fork1 are at location6. saltshaker3 is at location26. glassbottle1 is at location19. window1 is at location30. plate1 is at location4. stoveknob4 is at location7. chair2 is at location1. lightswitch1 is at location25. spatula1 is at location27. window2 is at location28. dishsponge1 is at location15. stoveknob1 is at location18. chair1 is at location23. cellphone2 and peppershaker2 are at location20. pot1 is at location5. vase1 is at location17. agent agent1 is at location location10. The objects are in/on receptacle as follows. cup1, potato1, apple1, cup2, lettuce1, and potato2 are in fridge1. fork2, glassbottle3, soapbottle1, butterknife1, lettuce3, houseplant1, tomato1, saltshaker2, spoon3, and knife1 are on countertop3. fork1, spoon2, and spatula2 are in sinkbasin1. bowl1 and glassbottle2 are in cabinet4. cellphone2 and peppershaker2 are in drawer1. pan1 is on stoveburner2. saltshaker1 is in cabinet3. statue1, mug1, bread1, lettuce2, cellphone3, creditcard2, creditcard1, and spoon1 are on countertop1. plate1 is in cabinet6. apple2 and egg2 are in garbagecan1. papertowelroll1 and vase2 are on shelf2. pot1 is on stoveburner1. vase1 is on shelf1. cellphone1, pan1, and peppershaker1 are on countertop2. spatula1 is in drawer3. pan1 is on stoveburner4. dishsponge1 is in drawer2. saltshaker3 is on shelf3. pot1 is on stoveburner3. glassbottle1 is in cabinet1. cabinet2, microwave1, fridge1, drawer1, cabinet1, drawer3, drawer2, and cabinet5 are closed. egg1 is hot. Nothing has been validated. agent1 is holding object egg1. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? saltshaker1 is at location19", "answer": "no"}
{"id": 5851631183972330216, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet6 is at location4. countertop3 is at location3. stoveburner4 and stoveburner2 are at location21. stoveburner3 and stoveburner1 are at location5. cabinet2 is at location22. shelf2 is at location29. sinkbasin1 is at location6. shelf3 is at location26. drawer1 is at location20. drawer2 is at location15. cabinet5 is at location13. toaster1 is at location9. fridge1 is at location10. countertop2 and coffeemachine1 are at location11. cabinet3 is at location16. garbagecan1 is at location8. drawer3 is at location27. cabinet4 is at location14. shelf1 is at location17. cabinet1 is at location19. microwave1 is at location24. countertop1 is at location2.  Currently, the objects are at locations as follows. saltshaker1 is at location16. saltshaker2, lettuce3, houseplant1, tomato1, glassbottle3, spoon3, soapbottle1, butterknife1, fork2, and knife1 are at location3. potato2, cup2, potato1, apple1, lettuce1, and cup1 are at location10. peppershaker1, cellphone1, and pan1 are at location11. stoveknob2 and stoveknob3 are at location12. glassbottle2 and bowl1 are at location14. egg2 and apple2 are at location8. papertowelroll1 and vase2 are at location29. mug1, bread1, lettuce2, statue1, creditcard1, creditcard2, spoon1, and cellphone3 are at location2. spatula2, spoon2, sink1, and fork1 are at location6. saltshaker3 is at location26. glassbottle1 is at location19. window1 is at location30. plate1 is at location4. stoveknob4 is at location7. chair2 is at location1. lightswitch1 is at location25. spatula1 is at location27. window2 is at location28. dishsponge1 is at location15. stoveknob1 is at location18. chair1 is at location23. cellphone2 and peppershaker2 are at location20. pot1 is at location5. vase1 is at location17. agent agent1 is at location location26. The objects are in/on receptacle as follows. cup1, potato1, apple1, cup2, lettuce1, and potato2 are in fridge1. fork2, glassbottle3, soapbottle1, butterknife1, lettuce3, houseplant1, tomato1, saltshaker2, spoon3, and knife1 are on countertop3. fork1, spoon2, and spatula2 are in sinkbasin1. bowl1 and glassbottle2 are in cabinet4. cellphone2 and peppershaker2 are in drawer1. pan1 is on stoveburner2. saltshaker1 is in cabinet3. statue1, mug1, bread1, lettuce2, cellphone3, creditcard2, creditcard1, and spoon1 are on countertop1. plate1 is in cabinet6. apple2 and egg2 are in garbagecan1. papertowelroll1 and vase2 are on shelf2. pot1 is on stoveburner1. vase1 is on shelf1. cellphone1, pan1, and peppershaker1 are on countertop2. spatula1 is in drawer3. pan1 is on stoveburner4. dishsponge1 is in drawer2. saltshaker3 is on shelf3. pot1 is on stoveburner3. glassbottle1 is in cabinet1. cabinet2, microwave1, fridge1, drawer1, cabinet1, drawer3, drawer2, and cabinet5 are closed. Nothing has been validated. agent1 is holding object egg1. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? spatula2 is at location3", "answer": "no"}
{"id": -1169280393445361824, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet6 is at location4. countertop3 is at location3. stoveburner4 and stoveburner2 are at location21. stoveburner3 and stoveburner1 are at location5. cabinet2 is at location22. shelf2 is at location29. sinkbasin1 is at location6. shelf3 is at location26. drawer1 is at location20. drawer2 is at location15. cabinet5 is at location13. toaster1 is at location9. fridge1 is at location10. countertop2 and coffeemachine1 are at location11. cabinet3 is at location16. garbagecan1 is at location8. drawer3 is at location27. cabinet4 is at location14. shelf1 is at location17. cabinet1 is at location19. microwave1 is at location24. countertop1 is at location2.  Currently, the objects are at locations as follows. saltshaker1 is at location16. saltshaker2, lettuce3, houseplant1, tomato1, glassbottle3, spoon3, soapbottle1, butterknife1, fork2, and knife1 are at location3. potato2, cup2, potato1, apple1, lettuce1, and cup1 are at location10. peppershaker1, cellphone1, and pan1 are at location11. stoveknob2 and stoveknob3 are at location12. glassbottle2 and bowl1 are at location14. egg2, apple2, and egg1 are at location8. papertowelroll1 and vase2 are at location29. mug1, bread1, lettuce2, statue1, creditcard1, creditcard2, spoon1, and cellphone3 are at location2. spatula2, spoon2, sink1, and fork1 are at location6. saltshaker3 is at location26. glassbottle1 is at location19. window1 is at location30. plate1 is at location4. stoveknob4 is at location7. chair2 is at location1. lightswitch1 is at location25. spatula1 is at location27. window2 is at location28. dishsponge1 is at location15. stoveknob1 is at location18. chair1 is at location23. cellphone2 and peppershaker2 are at location20. pot1 is at location5. vase1 is at location17. agent agent1 is at location location8. The objects are in/on receptacle as follows. cup1, potato1, apple1, cup2, lettuce1, and potato2 are in fridge1. fork2, glassbottle3, soapbottle1, butterknife1, lettuce3, houseplant1, tomato1, saltshaker2, spoon3, and knife1 are on countertop3. fork1, spoon2, and spatula2 are in sinkbasin1. bowl1 and glassbottle2 are in cabinet4. cellphone2 and peppershaker2 are in drawer1. pan1 is on stoveburner2. saltshaker1 is in cabinet3. statue1, mug1, bread1, lettuce2, cellphone3, creditcard2, creditcard1, and spoon1 are on countertop1. plate1 is in cabinet6. apple2, egg2, and egg1 are in garbagecan1. papertowelroll1 and vase2 are on shelf2. pot1 is on stoveburner1. vase1 is on shelf1. cellphone1, pan1, and peppershaker1 are on countertop2. spatula1 is in drawer3. pan1 is on stoveburner4. dishsponge1 is in drawer2. saltshaker3 is on shelf3. pot1 is on stoveburner3. glassbottle1 is in cabinet1. cabinet2, microwave1, fridge1, drawer1, cabinet1, drawer3, drawer2, and cabinet5 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? agent agent1 is at location location24", "answer": "yes"}
{"id": -8803000159183432939, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet6 is at location4. countertop3 is at location3. stoveburner4 and stoveburner2 are at location21. stoveburner3 and stoveburner1 are at location5. cabinet2 is at location22. shelf2 is at location29. sinkbasin1 is at location6. shelf3 is at location26. drawer1 is at location20. drawer2 is at location15. cabinet5 is at location13. toaster1 is at location9. fridge1 is at location10. countertop2 and coffeemachine1 are at location11. cabinet3 is at location16. garbagecan1 is at location8. drawer3 is at location27. cabinet4 is at location14. shelf1 is at location17. cabinet1 is at location19. microwave1 is at location24. countertop1 is at location2.  Currently, the objects are at locations as follows. saltshaker1 is at location16. saltshaker2, lettuce3, houseplant1, tomato1, glassbottle3, spoon3, soapbottle1, butterknife1, fork2, and knife1 are at location3. potato2, cup2, potato1, apple1, lettuce1, and cup1 are at location10. peppershaker1, cellphone1, and pan1 are at location11. stoveknob2 and stoveknob3 are at location12. glassbottle2 and bowl1 are at location14. papertowelroll1 and vase2 are at location29. mug1, bread1, lettuce2, statue1, creditcard1, creditcard2, spoon1, and cellphone3 are at location2. spatula2, spoon2, sink1, and fork1 are at location6. saltshaker3 is at location26. glassbottle1 is at location19. window1 is at location30. plate1 is at location4. stoveknob4 is at location7. chair2 is at location1. lightswitch1 is at location25. spatula1 is at location27. window2 is at location28. apple2 and egg1 are at location8. dishsponge1 is at location15. stoveknob1 is at location18. chair1 is at location23. cellphone2 and peppershaker2 are at location20. pot1 is at location5. vase1 is at location17. agent agent1 is at location location19. The objects are in/on receptacle as follows. cup1, potato1, apple1, cup2, lettuce1, and potato2 are in fridge1. fork2, glassbottle3, soapbottle1, butterknife1, lettuce3, houseplant1, tomato1, saltshaker2, spoon3, and knife1 are on countertop3. fork1, spoon2, and spatula2 are in sinkbasin1. bowl1 and glassbottle2 are in cabinet4. cellphone2 and peppershaker2 are in drawer1. pan1 is on stoveburner2. saltshaker1 is in cabinet3. statue1, mug1, bread1, lettuce2, cellphone3, creditcard2, creditcard1, and spoon1 are on countertop1. plate1 is in cabinet6. apple2 and egg1 are in garbagecan1. papertowelroll1 and vase2 are on shelf2. pot1 is on stoveburner1. vase1 is on shelf1. cellphone1, pan1, and peppershaker1 are on countertop2. spatula1 is in drawer3. pan1 is on stoveburner4. dishsponge1 is in drawer2. saltshaker3 is on shelf3. pot1 is on stoveburner3. glassbottle1 is in cabinet1. cabinet2, microwave1, fridge1, drawer1, cabinet1, drawer3, drawer2, and cabinet5 are closed. Nothing has been validated. agent1 is holding object egg2. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? agent agent1 is at location location24", "answer": "yes"}
{"id": -6177815315037371298, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. sinkbasin2 is at location6. cabinet2 is at location11. cabinet4 is at location15. cabinet3 is at location8. toiletpaperhanger1 is at location10. handtowelholder2 is at location17. handtowelholder1 is at location18. garbagecan1 is at location2. towelholder1 is at location5. toilet1 is at location7. cabinet1 is at location4. countertop1 is at location3. sinkbasin1 is at location16.  Currently, the objects are at locations as follows. toiletpaper1 and soapbar1 are at location7. handtowel1 is at location18. scrubbrush1 and plunger1 are at location10. cloth1 and candle1 are at location3. spraybottle3 is at location2. showerdoor1 is at location1. spraybottle2 and soapbottle1 are at location15. handtowel2 is at location17. sink2 is at location14. towel1 and showerglass1 are at location5. sink1 is at location12. soapbottle2 and spraybottle1 are at location4. mirror1 is at location9. toiletpaper2 is at location8. lightswitch1 is at location13. cloth2 is at location11. agent agent1 is at location location3. The objects are in/on receptacle as follows. handtowel2 is on handtowelholder2. cloth2 is in cabinet2. cloth1 and candle1 are on countertop1. towel1 is on towelholder1. toiletpaper1 and soapbar1 are in toilet1. soapbottle2 and spraybottle1 are in cabinet1. spraybottle2 and soapbottle1 are in cabinet4. toiletpaper2 is in cabinet3. spraybottle3 is in garbagecan1. handtowel1 is on handtowelholder1. cabinet2, cabinet4, cabinet1, and cabinet3 are closed. Nothing has been validated. agent1 is holding object soapbar2. The goal is to reach a state where the following facts hold: It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? cloth1 is at location7", "answer": "no"}
{"id": -306709117252800840, "group": "landmarks_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet6 is at location4. countertop3 is at location3. stoveburner4 and stoveburner2 are at location21. stoveburner3 and stoveburner1 are at location5. cabinet2 is at location22. shelf2 is at location29. sinkbasin1 is at location6. shelf3 is at location26. drawer1 is at location20. drawer2 is at location15. cabinet5 is at location13. toaster1 is at location9. fridge1 is at location10. countertop2 and coffeemachine1 are at location11. cabinet3 is at location16. garbagecan1 is at location8. drawer3 is at location27. cabinet4 is at location14. shelf1 is at location17. cabinet1 is at location19. microwave1 is at location24. countertop1 is at location2.  Currently, the objects are at locations as follows. stoveknob2 and stoveknob3 are at location12. dishsponge1 and fork1 are at location27. vase1 and dishsponge2 are at location13. egg1, plate2, potato1, bowl1, mug1, tomato1, and egg2 are at location10. glassbottle3, potato3, and apple2 are at location8. cellphone1, lettuce1, lettuce2, plate1, peppershaker1, apple1, knife1, and cellphone2 are at location2. bread1, houseplant1, glassbottle1, creditcard1, papertowelroll1, knife2, spoon2, spatula1, cellphone3, plate3, and butterknife1 are at location3. spatula2, potato2, sink1, glassbottle2, and spatula3 are at location6. bowl2 is at location29. soapbottle1 is at location4. vase2 is at location17. window1 is at location30. stoveknob4 is at location7. soapbottle2 and statue1 are at location26. pan1 and spoon1 are at location11. chair2 is at location1. lightswitch1 is at location25. window2 is at location28. saltshaker1 is at location15. stoveknob1 is at location18. chair1 is at location23. peppershaker3 is at location16. cup1 is at location24. peppershaker2 is at location20. pot1 is at location5. agent agent1 is at location location19. The objects are in/on receptacle as follows. knife2, cellphone3, butterknife1, papertowelroll1, houseplant1, glassbottle1, creditcard1, spatula1, bread1, plate3, and spoon2 are on countertop3. potato1, bowl1, tomato1, mug1, egg2, plate2, and egg1 are in fridge1. pan1 is on stoveburner2. cellphone1, cellphone2, lettuce2, peppershaker1, knife1, apple1, lettuce1, and plate1 are on countertop1. potato3, apple2, and glassbottle3 are in garbagecan1. spoon1 and pan1 are on countertop2. peppershaker3 is in cabinet3. fork1 and dishsponge1 are in drawer3. vase2 is on shelf1. pot1 is on stoveburner1. statue1 and soapbottle2 are on shelf3. cup1 is in microwave1. spatula3, potato2, glassbottle2, and spatula2 are in sinkbasin1. saltshaker1 is in drawer2. peppershaker2 is in drawer1. bowl2 is on shelf2. dishsponge2 and vase1 are in cabinet5. soapbottle1 is in cabinet6. pan1 is on stoveburner4. cellphone2 is on plate1. pot1 is on stoveburner3. cabinet2, microwave1, fridge1, drawer1, cabinet1, drawer3, drawer2, and cabinet5 are closed. mug1 is cool. Nothing has been validated. agent1 is holding object mug2. The goal is to reach a state where the following facts hold: It has been validated that an object of type mugtype is hot and is in a receptacle of type coffeemachinetype.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? glassbottle1 is at location6", "answer": "no"}
