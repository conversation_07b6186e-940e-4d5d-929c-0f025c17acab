{"id": -8065996215240873535, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c26, c2, c45, c10, c28, c47, c36, c14, c0, c38, c20, and c35 are at l4; c3, c31, c43, c7, c5, c49, c29, c23, c22, c15, c39, c4, c37, and c42 are at l3; c46, c19, c34, c44, c33, c21, c24, c48, c32, and c13 are at l1; c30, c16, c12, and c8 are at l2; c27, c9, c17, c41, c1, c6, c25, c18, c11, and c40 are at l0. The goal is to reach a state where the following facts hold: Car c0 is at location l2, Car c12 is at location l3, Car c49 is at location l1, Car c35 is at location l2, Car c26 is at location l2, Car c6 is at location l3, Car c19 is at location l1, Car c10 is at location l0, Car c24 is at location l0, Car c31 is at location l3, Car c20 is at location l3, Car c21 is at location l2, Car c30 is at location l4, Car c4 is at location l0, Car c41 is at location l4, Car c48 is at location l0, Car c11 is at location l1, Car c9 is at location l4, Car c46 is at location l4, Car c5 is at location l4, Car c17 is at location l0, Car c34 is at location l3, Car c36 is at location l0, Car c33 is at location l1, Car c47 is at location l0, Car c44 is at location l0, Car c28 is at location l4, Car c8 is at location l4, Car c3 is at location l2, Car c23 is at location l3, Car c39 is at location l2, Car c32 is at location l1, Car c22 is at location l2, Car c14 is at location l4, Car c1 is at location l0, Car c15 is at location l1, Car c27 is at location l4, Car c38 is at location l4, Car c16 is at location l0, Car c40 is at location l2, Car c25 is at location l3, Car c18 is at location l0, Car c37 is at location l3, Car c2 is at location l1, Car c43 is at location l2, Car c13 is at location l3, Car c29 is at location l4, Car c45 is at location l0, Car c7 is at location l0, and Car c42 is at location l0.", "question": "Given the plan: \"board the car c13 at location l1 on to the ferry, sail from location l1 to location l3, debark car c13 to location l3 from the ferry, board the car c29 at location l3 on to the ferry, sail from location l3 to location l4, debark car c29 to location l4 from the ferry, board the car c26 at location l4 on to the ferry, sail from location l4 to location l2, debark car c26 to location l2 from the ferry, board the car c16 at location l2 on to the ferry, sail from location l2 to location l0, debark car c16 to location l0 from the ferry, board the car c40 at location l0 on to the ferry, sail from location l0 to location l2, debark car c40 to location l2 from the ferry, board the car c12 at location l2 on to the ferry, sail from location l2 to location l3, debark car c12 to location l3 from the ferry, board the car c15 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l1, debark car c15 to location l1 from the ferry, board the car c21 at location l1 on to the ferry, sail from location l1 to location l2, debark car c21 to location l2 from the ferry, board the car c30 at location l2 on to the ferry, sail from location l2 to location l3, sail from location l3 to location l4, debark car c30 to location l4 from the ferry, board the car c10 at location l4 on to the ferry, sail from location l4 to location l2, sail from location l2 to location l0, debark car c10 to location l0 from the ferry, board the car c11 at location l0 on to the ferry, sail from location l0 to location l1, debark car c11 to location l1 from the ferry, board the car c24 at location l1 on to the ferry, sail from location l1 to location l0, debark car c24 to location l0 from the ferry, board the car c25 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l3, debark car c25 to location l3 from the ferry, board the car c22 at location l3 on to the ferry, sail from location l3 to location l2, debark car c22 to location l2 from the ferry, board the car c8 at location l2 on to the ferry, sail from location l2 to location l3, sail from location l3 to location l4, debark car c8 to location l4 from the ferry, board the car c2 at location l4 on to the ferry, sail from location l4 to location l2, sail from location l2 to location l1, debark car c2 to location l1 from the ferry, board the car c34 at location l1 on to the ferry, sail from location l1 to location l0, sail from location l0 to location l3, debark car c34 to location l3 from the ferry, board the car c3 at location l3 on to the ferry, sail from location l3 to location l2, debark car c3 to location l2 from the ferry, sail from location l2 to location l3, board the car c39 at location l3 on to the ferry, sail from location l3 to location l2, debark car c39 to location l2 from the ferry, sail from location l2 to location l3, board the car c4 at location l3 on to the ferry, sail from location l3 to location l0, debark car c4 to location l0 from the ferry, board the car c27 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l4, debark car c27 to location l4 from the ferry, board the car c20 at location l4 on to the ferry, sail from location l4 to location l2, sail from location l2 to location l3, debark car c20 to location l3 from the ferry, board the car c42 at location l3 on to the ferry, sail from location l3 to location l0, debark car c42 to location l0 from the ferry, board the car c41 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l4, debark car c41 to location l4 from the ferry, board the car c36 at location l4 on to the ferry, sail from location l4 to location l2, sail from location l2 to location l0, debark car c36 to location l0 from the ferry, board the car c6 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l3, debark car c6 to location l3 from the ferry, board the car c43 at location l3 on to the ferry, sail from location l3 to location l2, debark car c43 to location l2 from the ferry, sail from location l2 to location l3, board the car c49 at location l3 on to the ferry, sail from location l3 to location l0, sail from location l0 to location l1, debark car c49 to location l1 from the ferry, board the car c44 at location l1 on to the ferry, sail from location l1 to location l0, debark car c44 to location l0 from the ferry, board the car c9 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l4, debark car c9 to location l4 from the ferry, sail from location l4 to location l1, board the car c46 at location l1 on to the ferry, sail from location l1 to location l4, debark car c46 to location l4 from the ferry, board the car c35 at location l4 on to the ferry, sail from location l4 to location l2, debark car c35 to location l2 from the ferry, sail from location l2 to location l1, board the car c48 at location l1 on to the ferry, sail from location l1 to location l0, debark car c48 to location l0 from the ferry, sail from location l0 to location l3, board the car c5 at location l3 on to the ferry, sail from location l3 to location l4, debark car c5 to location l4 from the ferry, board the car c45 at location l4 on to the ferry, sail from location l4 to location l0, debark car c45 to location l0 from the ferry, sail from location l0 to location l3, board the car c7 at location l3 on to the ferry, sail from location l3 to location l0, debark car c7 to location l0 from the ferry, sail from location l0 to location l4, board the car c0 at location l4 on to the ferry, sail from location l4 to location l2, debark car c0 to location l2 from the ferry, sail from location l2 to location l0, sail from location l0 to location l4, board the car c47 at location l4 on to the ferry, sail from location l4 to location l0, debark car c47 to location l0 from the ferry\"; can the following action be removed from this plan and still have a valid plan: sail from location l1 to location l3?", "answer": "no"}
{"id": -4047479053016228401, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c0 and c1 are at l0; c2 is at l1. The goal is to reach a state where the following facts hold: Car c1 is at location l0, Car c0 is at location l1, and Car c2 is at location l1.", "question": "Given the plan: \"sail from location l1 to location l0, sail from location l0 to location l1, sail from location l1 to location l0, board the car c0 at the location l0, debark the car c0 to location l0 from the ferry, board the car c0 at the location l0, sail from location l0 to location l1, debark the car c0 to location l1 from the ferry, sail from location l1 to location l0\"; can the following action be removed from this plan and still have a valid plan: sail from location l1 to location l0?", "answer": "yes"}
{"id": 9064347322980490596, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c3 is at l2; c9, c2, and c6 are at l0; c1, c7, c8, c5, c4, and c0 are at l1. The goal is to reach a state where the following facts hold: Car c4 is at location l0, Car c0 is at location l2, Car c3 is at location l2, Car c5 is at location l2, Car c9 is at location l0, Car c1 is at location l1, Car c6 is at location l2, Car c2 is at location l1, Car c8 is at location l2, and Car c7 is at location l0.", "question": "Given the plan: \"travel by sea from location l2 to location l0, travel by sea from location l0 to location l1, board the car c7 at the location l1, travel by sea from location l1 to location l0, debark the car c7 to location l0 from the ferry, board the car c2 at the location l0, travel by sea from location l0 to location l1, debark the car c2 to location l1 from the ferry, board the car c8 at the location l1, travel by sea from location l1 to location l2, debark the car c8 to location l2 from the ferry, travel by sea from location l2 to location l1, board the car c5 at the location l1, travel by sea from location l1 to location l2, debark the car c5 to location l2 from the ferry, travel by sea from location l2 to location l1, board the car c4 at the location l1, travel by sea from location l1 to location l0, debark the car c4 to location l0 from the ferry, board the car c6 at the location l0, travel by sea from location l0 to location l2, debark the car c6 to location l2 from the ferry, travel by sea from location l2 to location l1, board the car c0 at the location l1, travel by sea from location l1 to location l2, debark the car c0 to location l2 from the ferry\"; can the following action be removed from this plan and still have a valid plan: travel by sea from location l1 to location l0?", "answer": "no"}
{"id": -2948642499284021664, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c3 is at l2; c9, c2, and c6 are at l0; c1, c7, c8, c5, c4, and c0 are at l1. The goal is to reach a state where the following facts hold: Car c4 is at location l0, Car c0 is at location l2, Car c3 is at location l2, Car c5 is at location l2, Car c9 is at location l0, Car c1 is at location l1, Car c6 is at location l2, Car c2 is at location l1, Car c8 is at location l2, and Car c7 is at location l0.", "question": "Given the plan: \"sail from location l2 to location l1, board car c0 at location l1, sail from location l1 to location l2, debark the car c0 to location l2 from the ferry, sail from location l2 to location l1, board car c5 at location l1, debark the car c5 to location l1 from the ferry, board car c8 at location l1, sail from location l1 to location l2, debark the car c8 to location l2 from the ferry, sail from location l2 to location l1, board car c4 at location l1, sail from location l1 to location l0, debark the car c4 to location l0 from the ferry, board car c2 at location l0, sail from location l0 to location l1, debark the car c2 to location l1 from the ferry, board car c7 at location l1, sail from location l1 to location l0, debark the car c7 to location l0 from the ferry, board car c6 at location l0, sail from location l0 to location l2, debark the car c6 to location l2 from the ferry, sail from location l2 to location l1, board car c5 at location l1, sail from location l1 to location l2, debark the car c5 to location l2 from the ferry\"; can the following action be removed from this plan and still have a valid plan: debark the car c6 to location l2 from the ferry?", "answer": "no"}
{"id": 8307555940186464125, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0 and c2 are at l4; c1 is at l0. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c0 is at location l3, and Car c1 is at location l3.", "question": "Given the plan: \"embark the car c1 at location l0 on to the ferry, travel by sea from location l0 to location l3, debark the car c1 from the ferry to location l3, travel by sea from location l3 to location l0, travel by sea from location l0 to location l4, embark the car c0 at location l4 on to the ferry, travel by sea from location l4 to location l3, debark the car c0 from the ferry to location l3, travel by sea from location l3 to location l4, embark the car c2 at location l4 on to the ferry, travel by sea from location l4 to location l3, debark the car c2 from the ferry to location l3, travel by sea from location l3 to location l2\"; can the following action be removed from this plan and still have a valid plan: debark the car c1 from the ferry to location l3?", "answer": "no"}
{"id": 5370407710788629186, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c26, c2, c45, c10, c28, c47, c36, c14, c0, c38, c20, and c35 are at l4; c3, c31, c43, c7, c5, c49, c29, c23, c22, c15, c39, c4, c37, and c42 are at l3; c46, c19, c34, c44, c33, c21, c24, c48, c32, and c13 are at l1; c30, c16, c12, and c8 are at l2; c27, c9, c17, c41, c1, c6, c25, c18, c11, and c40 are at l0. The goal is to reach a state where the following facts hold: Car c0 is at location l2, Car c12 is at location l3, Car c49 is at location l1, Car c35 is at location l2, Car c26 is at location l2, Car c6 is at location l3, Car c19 is at location l1, Car c10 is at location l0, Car c24 is at location l0, Car c31 is at location l3, Car c20 is at location l3, Car c21 is at location l2, Car c30 is at location l4, Car c4 is at location l0, Car c41 is at location l4, Car c48 is at location l0, Car c11 is at location l1, Car c9 is at location l4, Car c46 is at location l4, Car c5 is at location l4, Car c17 is at location l0, Car c34 is at location l3, Car c36 is at location l0, Car c33 is at location l1, Car c47 is at location l0, Car c44 is at location l0, Car c28 is at location l4, Car c8 is at location l4, Car c3 is at location l2, Car c23 is at location l3, Car c39 is at location l2, Car c32 is at location l1, Car c22 is at location l2, Car c14 is at location l4, Car c1 is at location l0, Car c15 is at location l1, Car c27 is at location l4, Car c38 is at location l4, Car c16 is at location l0, Car c40 is at location l2, Car c25 is at location l3, Car c18 is at location l0, Car c37 is at location l3, Car c2 is at location l1, Car c43 is at location l2, Car c13 is at location l3, Car c29 is at location l4, Car c45 is at location l0, Car c7 is at location l0, and Car c42 is at location l0.", "question": "Given the plan: \"board the car c48 at location l1, debark car c48 to location l1 from the ferry, board the car c13 at location l1, travel by sea from location l1 to location l3, debark car c13 to location l3 from the ferry, board the car c22 at location l3, travel by sea from location l3 to location l2, debark car c22 to location l2 from the ferry, board the car c16 at location l2, travel by sea from location l2 to location l0, debark car c16 to location l0 from the ferry, board the car c27 at location l0, travel by sea from location l0 to location l4, debark car c27 to location l4 from the ferry, board the car c10 at location l4, travel by sea from location l4 to location l0, debark car c10 to location l0 from the ferry, board the car c40 at location l0, travel by sea from location l0 to location l2, debark car c40 to location l2 from the ferry, travel by sea from location l2 to location l4, board the car c2 at location l4, travel by sea from location l4 to location l1, debark car c2 to location l1 from the ferry, board the car c21 at location l1, travel by sea from location l1 to location l2, debark car c21 to location l2 from the ferry, travel by sea from location l2 to location l4, board the car c20 at location l4, travel by sea from location l4 to location l3, debark car c20 to location l3 from the ferry, board the car c15 at location l3, travel by sea from location l3 to location l0, travel by sea from location l0 to location l1, debark car c15 to location l1 from the ferry, board the car c24 at location l1, travel by sea from location l1 to location l0, debark car c24 to location l0 from the ferry, board the car c41 at location l0, travel by sea from location l0 to location l4, debark car c41 to location l4 from the ferry, board the car c36 at location l4, travel by sea from location l4 to location l0, debark car c36 to location l0 from the ferry, board the car c9 at location l0, travel by sea from location l0 to location l4, debark car c9 to location l4 from the ferry, board the car c45 at location l4, travel by sea from location l4 to location l0, debark car c45 to location l0 from the ferry, board the car c11 at location l0, travel by sea from location l0 to location l1, debark car c11 to location l1 from the ferry, board the car c34 at location l1, travel by sea from location l1 to location l0, travel by sea from location l0 to location l3, debark car c34 to location l3 from the ferry, board the car c29 at location l3, travel by sea from location l3 to location l0, travel by sea from location l0 to location l4, debark car c29 to location l4 from the ferry, board the car c47 at location l4, travel by sea from location l4 to location l0, debark car c47 to location l0 from the ferry, board the car c25 at location l0, travel by sea from location l0 to location l1, travel by sea from location l1 to location l3, debark car c25 to location l3 from the ferry, board the car c39 at location l3, travel by sea from location l3 to location l1, travel by sea from location l1 to location l2, debark car c39 to location l2 from the ferry, board the car c12 at location l2, travel by sea from location l2 to location l3, debark car c12 to location l3 from the ferry, board the car c43 at location l3, travel by sea from location l3 to location l1, travel by sea from location l1 to location l2, debark car c43 to location l2 from the ferry, board the car c30 at location l2, travel by sea from location l2 to location l3, travel by sea from location l3 to location l4, debark car c30 to location l4 from the ferry, board the car c26 at location l4, travel by sea from location l4 to location l2, debark car c26 to location l2 from the ferry, board the car c8 at location l2, travel by sea from location l2 to location l3, travel by sea from location l3 to location l4, debark car c8 to location l4 from the ferry, board the car c35 at location l4, travel by sea from location l4 to location l2, debark car c35 to location l2 from the ferry, travel by sea from location l2 to location l3, board the car c4 at location l3, travel by sea from location l3 to location l0, debark car c4 to location l0 from the ferry, board the car c6 at location l0, travel by sea from location l0 to location l1, travel by sea from location l1 to location l3, debark car c6 to location l3 from the ferry, board the car c42 at location l3, travel by sea from location l3 to location l0, debark car c42 to location l0 from the ferry, travel by sea from location l0 to location l1, board the car c44 at location l1, travel by sea from location l1 to location l0, debark car c44 to location l0 from the ferry, travel by sea from location l0 to location l1, board the car c46 at location l1, travel by sea from location l1 to location l2, travel by sea from location l2 to location l4, debark car c46 to location l4 from the ferry, travel by sea from location l4 to location l3, board the car c3 at location l3, travel by sea from location l3 to location l1, debark car c3 to location l1 from the ferry, board the car c48 at location l1, travel by sea from location l1 to location l0, debark car c48 to location l0 from the ferry, travel by sea from location l0 to location l3, board the car c49 at location l3, travel by sea from location l3 to location l1, debark car c49 to location l1 from the ferry, travel by sea from location l1 to location l3, board the car c5 at location l3, travel by sea from location l3 to location l4, debark car c5 to location l4 from the ferry, board the car c0 at location l4, travel by sea from location l4 to location l0, travel by sea from location l0 to location l2, debark car c0 to location l2 from the ferry, travel by sea from location l2 to location l3, board the car c7 at location l3, travel by sea from location l3 to location l0, debark car c7 to location l0 from the ferry, travel by sea from location l0 to location l1, board the car c3 at location l1, travel by sea from location l1 to location l2, debark car c3 to location l2 from the ferry\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: board the car c48 at location l1 and debark car c48 to location l1 from the ferry?", "answer": "yes"}
{"id": -2030480802923632111, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c0, c15, c20, c23, c10, c8, c30, c9, c17, c35, c14, c44, c3, c1, c6, c29, c16, c2, c18, c33, and c11 are at l0; c46, c25, c40, c49, c47, c19, c34, c36, c12, c43, c7, c39, c5, c42, c21, c31, c38, c28, c26, c48, c24, c37, c4, c22, c32, c41, c27, c45, and c13 are at l1. The goal is to reach a state where the following facts hold: Car c0 is at location l0, Car c18 is at location l1, Car c49 is at location l1, Car c20 is at location l0, Car c35 is at location l1, Car c47 is at location l1, Car c8 is at location l0, Car c17 is at location l1, Car c34 is at location l1, Car c10 is at location l1, Car c3 is at location l1, Car c30 is at location l0, Car c21 is at location l0, Car c9 is at location l0, Car c38 is at location l0, Car c7 is at location l1, Car c39 is at location l1, Car c36 is at location l0, Car c5 is at location l1, Car c33 is at location l1, Car c42 is at location l1, Car c22 is at location l0, Car c31 is at location l1, Car c24 is at location l1, Car c14 is at location l0, Car c28 is at location l1, Car c19 is at location l0, Car c44 is at location l0, Car c48 is at location l1, Car c1 is at location l1, Car c43 is at location l0, Car c4 is at location l1, Car c32 is at location l1, Car c6 is at location l1, Car c29 is at location l0, Car c41 is at location l1, Car c15 is at location l1, Car c25 is at location l0, Car c27 is at location l1, Car c23 is at location l1, Car c2 is at location l0, Car c13 is at location l0, Car c46 is at location l0, Car c12 is at location l0, Car c37 is at location l0, Car c45 is at location l0, Car c11 is at location l0, Car c40 is at location l0, Car c16 is at location l1, and Car c26 is at location l0.", "question": "Given the plan: \"board the car c36 at location l1, sail from location l1 to location l0, debark car c36 to location l0 from the ferry, board the car c1 at location l0, sail from location l0 to location l1, debark car c1 to location l1 from the ferry, board the car c12 at location l1, sail from location l1 to location l0, debark car c12 to location l0 from the ferry, board the car c10 at location l0, sail from location l0 to location l1, debark car c10 to location l1 from the ferry, board the car c13 at location l1, sail from location l1 to location l0, debark car c13 to location l0 from the ferry, board the car c15 at location l0, sail from location l0 to location l1, debark car c15 to location l1 from the ferry, board the car c19 at location l1, sail from location l1 to location l0, debark car c19 to location l0 from the ferry, board the car c16 at location l0, sail from location l0 to location l1, debark car c16 to location l1 from the ferry, board the car c21 at location l1, sail from location l1 to location l0, debark car c21 to location l0 from the ferry, board the car c17 at location l0, sail from location l0 to location l1, debark car c17 to location l1 from the ferry, board the car c22 at location l1, sail from location l1 to location l0, debark car c22 to location l0 from the ferry, board the car c18 at location l0, sail from location l0 to location l1, debark car c18 to location l1 from the ferry, board the car c25 at location l1, sail from location l1 to location l0, debark car c25 to location l0 from the ferry, board the car c23 at location l0, sail from location l0 to location l1, debark car c23 to location l1 from the ferry, board the car c26 at location l1, sail from location l1 to location l0, debark car c26 to location l0 from the ferry, board the car c3 at location l0, sail from location l0 to location l1, debark car c3 to location l1 from the ferry, board the car c37 at location l1, sail from location l1 to location l0, debark car c37 to location l0 from the ferry, board the car c33 at location l0, sail from location l0 to location l1, debark car c33 to location l1 from the ferry, board the car c38 at location l1, sail from location l1 to location l0, debark car c38 to location l0 from the ferry, board the car c35 at location l0, sail from location l0 to location l1, debark car c35 to location l1 from the ferry, board the car c40 at location l1, sail from location l1 to location l0, debark car c40 to location l0 from the ferry, board the car c6 at location l0, sail from location l0 to location l1, debark car c6 to location l1 from the ferry, board the car c43 at location l1, sail from location l1 to location l0, debark car c43 to location l0 from the ferry, sail from location l0 to location l1, board the car c45 at location l1, sail from location l1 to location l0, debark car c45 to location l0 from the ferry, sail from location l0 to location l1, board the car c46 at location l1, sail from location l1 to location l0, debark car c46 to location l0 from the ferry, board the car c36 at location l0, debark car c36 to location l0 from the ferry\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: board the car c36 at location l0 and debark car c36 to location l0 from the ferry?", "answer": "yes"}
{"id": 6384988473335147074, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0 and c2 are at l4; c1 is at l0. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c0 is at location l3, and Car c1 is at location l3.", "question": "Given the plan: \"embark the car c1 at location l0 on to the ferry, sail from location l0 to location l3, debark the car c1 from the ferry to location l3, sail from location l3 to location l2, sail from location l2 to location l4, embark the car c0 at location l4 on to the ferry, sail from location l4 to location l3, debark the car c0 from the ferry to location l3, sail from location l3 to location l2, sail from location l2 to location l4, embark the car c2 at location l4 on to the ferry, sail from location l4 to location l3, debark the car c2 from the ferry to location l3, sail from location l3 to location l4\"; can the following action be removed from this plan and still have a valid plan: sail from location l0 to location l3?", "answer": "no"}
{"id": -5337622938030096811, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c26, c2, c45, c10, c28, c47, c36, c14, c0, c38, c20, and c35 are at l4; c3, c31, c43, c7, c5, c49, c29, c23, c22, c15, c39, c4, c37, and c42 are at l3; c46, c19, c34, c44, c33, c21, c24, c48, c32, and c13 are at l1; c30, c16, c12, and c8 are at l2; c27, c9, c17, c41, c1, c6, c25, c18, c11, and c40 are at l0. The goal is to reach a state where the following facts hold: Car c0 is at location l2, Car c12 is at location l3, Car c49 is at location l1, Car c35 is at location l2, Car c26 is at location l2, Car c6 is at location l3, Car c19 is at location l1, Car c10 is at location l0, Car c24 is at location l0, Car c31 is at location l3, Car c20 is at location l3, Car c21 is at location l2, Car c30 is at location l4, Car c4 is at location l0, Car c41 is at location l4, Car c48 is at location l0, Car c11 is at location l1, Car c9 is at location l4, Car c46 is at location l4, Car c5 is at location l4, Car c17 is at location l0, Car c34 is at location l3, Car c36 is at location l0, Car c33 is at location l1, Car c47 is at location l0, Car c44 is at location l0, Car c28 is at location l4, Car c8 is at location l4, Car c3 is at location l2, Car c23 is at location l3, Car c39 is at location l2, Car c32 is at location l1, Car c22 is at location l2, Car c14 is at location l4, Car c1 is at location l0, Car c15 is at location l1, Car c27 is at location l4, Car c38 is at location l4, Car c16 is at location l0, Car c40 is at location l2, Car c25 is at location l3, Car c18 is at location l0, Car c37 is at location l3, Car c2 is at location l1, Car c43 is at location l2, Car c13 is at location l3, Car c29 is at location l4, Car c45 is at location l0, Car c7 is at location l0, and Car c42 is at location l0.", "question": "Given the plan: \"load the car c13 at location l1 on to the ferry, sail from location l1 to location l3, unload the car c13 from the ferry to location l3, load the car c22 at location l3 on to the ferry, sail from location l3 to location l2, unload the car c22 from the ferry to location l2, load the car c16 at location l2 on to the ferry, sail from location l2 to location l0, unload the car c16 from the ferry to location l0, load the car c27 at location l0 on to the ferry, sail from location l0 to location l4, unload the car c27 from the ferry to location l4, load the car c10 at location l4 on to the ferry, sail from location l4 to location l0, unload the car c10 from the ferry to location l0, load the car c40 at location l0 on to the ferry, sail from location l0 to location l2, unload the car c40 from the ferry to location l2, load the car c12 at location l2 on to the ferry, sail from location l2 to location l3, unload the car c12 from the ferry to location l3, sail from location l3 to location l0, load the car c41 at location l0 on to the ferry, sail from location l0 to location l4, unload the car c41 from the ferry to location l4, load the car c2 at location l4 on to the ferry, sail from location l4 to location l1, unload the car c2 from the ferry to location l1, load the car c21 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c21 from the ferry to location l2, load the car c30 at location l2 on to the ferry, sail from location l2 to location l3, sail from location l3 to location l4, unload the car c30 from the ferry to location l4, load the car c20 at location l4 on to the ferry, sail from location l4 to location l3, unload the car c20 from the ferry to location l3, sail from location l3 to location l0, load the car c9 at location l0 on to the ferry, sail from location l0 to location l4, unload the car c9 from the ferry to location l4, load the car c36 at location l4 on to the ferry, sail from location l4 to location l0, unload the car c36 from the ferry to location l0, load the car c11 at location l0 on to the ferry, sail from location l0 to location l1, unload the car c11 from the ferry to location l1, load the car c24 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c24 from the ferry to location l2, load the car c8 at location l2 on to the ferry, sail from location l2 to location l4, unload the car c8 from the ferry to location l4, load the car c26 at location l4 on to the ferry, sail from location l4 to location l2, unload the car c26 from the ferry to location l2, load the car c24 at location l2 on to the ferry, sail from location l2 to location l0, unload the car c24 from the ferry to location l0, load the car c25 at location l0 on to the ferry, sail from location l0 to location l3, unload the car c25 from the ferry to location l3, load the car c15 at location l3 on to the ferry, sail from location l3 to location l1, unload the car c15 from the ferry to location l1, load the car c34 at location l1 on to the ferry, sail from location l1 to location l3, unload the car c34 from the ferry to location l3, load the car c29 at location l3 on to the ferry, sail from location l3 to location l4, unload the car c29 from the ferry to location l4, load the car c35 at location l4 on to the ferry, sail from location l4 to location l2, unload the car c35 from the ferry to location l2, sail from location l2 to location l0, load the car c6 at location l0 on to the ferry, sail from location l0 to location l3, unload the car c6 from the ferry to location l3, load the car c3 at location l3 on to the ferry, sail from location l3 to location l2, unload the car c3 from the ferry to location l2, sail from location l2 to location l1, load the car c44 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c44 from the ferry to location l0, sail from location l0 to location l1, load the car c46 at location l1 on to the ferry, sail from location l1 to location l4, unload the car c46 from the ferry to location l4, load the car c45 at location l4 on to the ferry, sail from location l4 to location l0, unload the car c45 from the ferry to location l0, sail from location l0 to location l1, load the car c48 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c48 from the ferry to location l0, sail from location l0 to location l3, load the car c39 at location l3 on to the ferry, sail from location l3 to location l2, unload the car c39 from the ferry to location l2, sail from location l2 to location l3, load the car c4 at location l3 on to the ferry, sail from location l3 to location l0, unload the car c4 from the ferry to location l0, sail from location l0 to location l3, load the car c42 at location l3 on to the ferry, sail from location l3 to location l0, unload the car c42 from the ferry to location l0, sail from location l0 to location l3, load the car c43 at location l3 on to the ferry, sail from location l3 to location l2, unload the car c43 from the ferry to location l2, sail from location l2 to location l3, load the car c49 at location l3 on to the ferry, sail from location l3 to location l1, unload the car c49 from the ferry to location l1, sail from location l1 to location l0, sail from location l0 to location l3, load the car c5 at location l3 on to the ferry, sail from location l3 to location l4, unload the car c5 from the ferry to location l4, load the car c47 at location l4 on to the ferry, sail from location l4 to location l0, unload the car c47 from the ferry to location l0, sail from location l0 to location l3, load the car c7 at location l3 on to the ferry, sail from location l3 to location l0, unload the car c7 from the ferry to location l0, sail from location l0 to location l2, sail from location l2 to location l4, load the car c0 at location l4 on to the ferry, sail from location l4 to location l2, unload the car c0 from the ferry to location l2\"; can the following action be removed from this plan and still have a valid plan: sail from location l0 to location l2?", "answer": "no"}
{"id": -3378745792146229810, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0 and c2 are at l4; c1 is at l0. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c0 is at location l3, and Car c1 is at location l3.", "question": "Given the plan: \"embark the car c1 at location l0 on to the ferry, travel by sea from location l0 to location l2, travel by sea from location l2 to location l0, travel by sea from location l0 to location l3, debark car c1 to location l3 from the ferry, travel by sea from location l3 to location l4, embark the car c2 at location l4 on to the ferry, travel by sea from location l4 to location l3, debark car c2 to location l3 from the ferry, travel by sea from location l3 to location l4, embark the car c0 at location l4 on to the ferry, travel by sea from location l4 to location l3, debark car c0 to location l3 from the ferry, travel by sea from location l3 to location l1\"; can the following action be removed from this plan and still have a valid plan: travel by sea from location l3 to location l1?", "answer": "yes"}
{"id": 6652934932660258628, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l2-2, l2-1, and l2-0 are in c2; l0-0, l0-2, and l0-1 are in c0. Currently, t0, a0, and p0 are at l0-0, p3 and p1 are at l2-0, p4 is at l1-0, t2 is at l2-1, t1 is at l1-1, p2 is at l0-1. The goal is to reach a state where the following facts hold: p2 is at l1-2, p1 is at l1-0, p0 is at l1-2, p3 is at l1-2, and p4 is at l0-0.", "question": "Given the plan: \"navigate the truck t1 which is in location l1-1 in city c1 to another location l1-0 in the same city, place the object p0 onto the airplane a0 at location l0-0, navigate the truck t0 which is in location l0-0 in city c0 to another location l0-1 in the same city, place the object p2 into the truck t0 at location l0-1, fly the airplane a0 from the airport l0-0 to the airport l2-0, place the object p1 onto the airplane a0 at location l2-0, place the object p3 onto the airplane a0 at location l2-0, fly the airplane a0 from the airport l2-0 to the airport l1-0, offload the object p0 from the airplane a0 at location l1-0, place the object p0 into the truck t1 at location l1-0, offload the object p1 from the airplane a0 at location l1-0, offload the object p3 from the airplane a0 at location l1-0, place the object p3 into the truck t1 at location l1-0, place the object p4 onto the airplane a0 at location l1-0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-2 in the same city, remove the object p0 from the truck t1 and place it on the location l1-2, remove the object p3 from the truck t1 and place it on the location l1-2, navigate the truck t0 which is in location l0-1 in city c0 to another location l0-0 in the same city, remove the object p2 from the truck t0 and place it on the location l0-0, offload the object p4 from the airplane a0 at location l1-0, fly the airplane a0 from the airport l1-0 to the airport l0-0, place the object p2 onto the airplane a0 at location l0-0, fly the airplane a0 from the airport l0-0 to the airport l1-0, offload the object p2 from the airplane a0 at location l1-0, navigate the truck t1 which is in location l1-2 in city c1 to another location l1-0 in the same city, place the object p2 into the truck t1 at location l1-0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-2 in the same city, remove the object p2 from the truck t1 and place it on the location l1-2, navigate the truck t1 which is in location l1-2 in city c1 to another location l1-0 in the same city, place the object p4 into the truck t1 at location l1-0, remove the object p4 from the truck t1 and place it on the location l1-0, place the object p4 onto the airplane a0 at location l1-0, fly the airplane a0 from the airport l1-0 to the airport l0-0, offload the object p4 from the airplane a0 at location l0-0\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: place the object p4 into the truck t1 at location l1-0 and remove the object p4 from the truck t1 and place it on the location l1-0?", "answer": "yes"}
{"id": -3705311142557720045, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l2-2, l2-1, and l2-0 are in c2; l0-0, l0-2, and l0-1 are in c0; l3-2, l3-0, and l3-1 are in c3; l4-1, l4-0, and l4-2 are in c4. Currently, t0 is at l0-2, a0 is at l2-0, p0 and t2 are at l2-2, t1 is at l1-2, t3 is at l3-2, p1 is at l3-1, p2 is at l2-1, p3 is at l4-1, t4 is at l4-0. The goal is to reach a state where the following facts hold: p0 is at l3-0, p2 is at l2-2, p1 is at l3-2, and p3 is at l3-2.", "question": "Given the plan: \"drive truck t3 from location l3-2 in city c3 to location l3-0 in the same city, load the object p0 from location l2-2 into the truck t2, drive truck t2 from location l2-2 in city c2 to location l2-0 in the same city, offload the object p0 from the truck t2 at location l2-0, load object p0 into airplane a0 at location l2-0, fly the airplane a0 from location l2-0 to location l4-0, drive truck t4 from location l4-0 in city c4 to location l4-1 in the same city, load the object p3 from location l4-1 into the truck t4, drive truck t4 from location l4-1 in city c4 to location l4-0 in the same city, offload the object p3 from the truck t4 at location l4-0, load object p3 into airplane a0 at location l4-0, fly the airplane a0 from location l4-0 to location l3-0, unload object p0 from airplane a0 at location l3-0, unload object p3 from airplane a0 at location l3-0, load the object p3 from location l3-0 into the truck t3, drive truck t3 from location l3-0 in city c3 to location l3-1 in the same city, load the object p1 from location l3-1 into the truck t3, drive truck t3 from location l3-1 in city c3 to location l3-2 in the same city, offload the object p3 from the truck t3 at location l3-2, offload the object p1 from the truck t3 at location l3-2, fly the airplane a0 from location l3-0 to location l1-0, drive truck t2 from location l2-0 in city c2 to location l2-1 in the same city, load the object p2 from location l2-1 into the truck t2, drive truck t2 from location l2-1 in city c2 to location l2-2 in the same city, offload the object p2 from the truck t2 at location l2-2\"; can the following action be removed from this plan and still have a valid plan: offload the object p0 from the truck t2 at location l2-0?", "answer": "no"}
{"id": 3419254766225937964, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l2-2, l2-1, and l2-0 are in c2; l0-0, l0-2, and l0-1 are in c0. Currently, t0, a0, and p0 are at l0-0, p3 and p1 are at l2-0, p4 is at l1-0, t2 is at l2-1, t1 is at l1-1, p2 is at l0-1. The goal is to reach a state where the following facts hold: p2 is at l1-2, p1 is at l1-0, p0 is at l1-2, p3 is at l1-2, and p4 is at l0-0.", "question": "Given the plan: \"navigate the truck t1 which is in location l1-1 in city c1 to another location l1-0 in the same city, load object p0 into airplane a0 at location l0-0, navigate the truck t0 which is in location l0-0 in city c0 to another location l0-1 in the same city, place the object p2 into the truck t0 at location l0-1, fly airplane a0 from airport l0-0 to airport l2-0, load object p1 into airplane a0 at location l2-0, load object p3 into airplane a0 at location l2-0, fly airplane a0 from airport l2-0 to airport l1-0, remove the object p0 from the airplane a0 and place it on the location l1-0, place the object p0 into the truck t1 at location l1-0, remove the object p1 from the airplane a0 and place it on the location l1-0, remove the object p3 from the airplane a0 and place it on the location l1-0, place the object p3 into the truck t1 at location l1-0, load object p4 into airplane a0 at location l1-0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-2 in the same city, offload the object p0 from the truck t1 at location l1-2, offload the object p3 from the truck t1 at location l1-2, navigate the truck t0 which is in location l0-1 in city c0 to another location l0-0 in the same city, offload the object p2 from the truck t0 at location l0-0, fly airplane a0 from airport l1-0 to airport l0-0, load object p2 into airplane a0 at location l0-0, remove the object p4 from the airplane a0 and place it on the location l0-0, fly airplane a0 from airport l0-0 to airport l1-0, remove the object p2 from the airplane a0 and place it on the location l1-0, navigate the truck t1 which is in location l1-2 in city c1 to another location l1-0 in the same city, place the object p2 into the truck t1 at location l1-0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-2 in the same city, offload the object p2 from the truck t1 at location l1-2, fly airplane a0 from airport l1-0 to airport l2-0\"; can the following action be removed from this plan and still have a valid plan: offload the object p2 from the truck t0 at location l0-0?", "answer": "no"}
{"id": 370737218325655434, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l4-3, l4-2, l4-4, l4-9, l4-1, l4-8, l4-5, l4-7, l4-0, and l4-6 are in c4; l3-7, l3-9, l3-1, l3-4, l3-6, l3-2, l3-0, l3-8, l3-5, and l3-3 are in c3; l2-1, l2-4, l2-2, l2-3, l2-0, l2-5, l2-9, l2-6, l2-7, and l2-8 are in c2; l0-8, l0-6, l0-5, l0-1, l0-4, l0-0, l0-9, l0-2, l0-3, and l0-7 are in c0; l1-2, l1-3, l1-0, l1-7, l1-6, l1-9, l1-5, l1-8, l1-1, and l1-4 are in c1. Currently, a0 is at l0-0, t2 is at l2-0, t1 is at l1-6, t4 is at l4-7, p0 is at l2-4, p2 and t3 are at l3-1, p1 is at l1-8, t0 is at l0-2, p3 is at l2-9. The goal is to reach a state where the following facts hold: p2 is at l4-8, p0 is at l1-5, p1 is at l2-7, and p3 is at l1-1.", "question": "Given the plan: \"drive truck t4 from location l4-7 in city c4 to location l4-8 in the same city, place the object p2 into the truck t3 at location l3-1, drive truck t3 from location l3-1 in city c3 to location l3-0 in the same city, remove the object p2 from the truck t3 and place it on the location l3-0, drive truck t1 from location l1-6 in city c1 to location l1-8 in the same city, place the object p1 into the truck t1 at location l1-8, drive truck t1 from location l1-8 in city c1 to location l1-0 in the same city, remove the object p1 from the truck t1 and place it on the location l1-0, fly airplane a0 from airport l0-0 to airport l4-0, drive truck t2 from location l2-0 in city c2 to location l2-9 in the same city, place the object p3 into the truck t2 at location l2-9, drive truck t2 from location l2-9 in city c2 to location l2-4 in the same city, place the object p0 into the truck t2 at location l2-4, drive truck t2 from location l2-4 in city c2 to location l2-7 in the same city, fly airplane a0 from airport l4-0 to airport l3-0, load object p2 into airplane a0 at location l3-0, fly airplane a0 from airport l3-0 to airport l1-0, load object p1 into airplane a0 at location l1-0, fly airplane a0 from airport l1-0 to airport l2-0, remove the object p1 from the airplane a0 and place it on the location l2-0, drive truck t2 from location l2-7 in city c2 to location l2-0 in the same city, remove the object p3 from the truck t2 and place it on the location l2-0, place the object p1 into the truck t2 at location l2-0, remove the object p0 from the truck t2 and place it on the location l2-0, load object p3 into airplane a0 at location l2-0, load object p0 into airplane a0 at location l2-0, drive truck t2 from location l2-0 in city c2 to location l2-7 in the same city, remove the object p1 from the truck t2 and place it on the location l2-7, fly airplane a0 from airport l2-0 to airport l1-0, remove the object p3 from the airplane a0 and place it on the location l1-0, place the object p3 into the truck t1 at location l1-0, remove the object p0 from the airplane a0 and place it on the location l1-0, place the object p0 into the truck t1 at location l1-0, drive truck t1 from location l1-0 in city c1 to location l1-1 in the same city, remove the object p3 from the truck t1 and place it on the location l1-1, drive truck t1 from location l1-1 in city c1 to location l1-5 in the same city, remove the object p0 from the truck t1 and place it on the location l1-5, fly airplane a0 from airport l1-0 to airport l4-0, remove the object p2 from the airplane a0 and place it on the location l4-0, drive truck t4 from location l4-8 in city c4 to location l4-0 in the same city, place the object p2 into the truck t4 at location l4-0, drive truck t4 from location l4-0 in city c4 to location l4-8 in the same city, remove the object p2 from the truck t4 and place it on the location l4-8, drive truck t0 from location l0-2 in city c0 to location l0-5 in the same city, drive truck t0 from location l0-5 in city c0 to location l0-7 in the same city\"; can the following action be removed from this plan and still have a valid plan: drive truck t1 from location l1-6 in city c1 to location l1-8 in the same city?", "answer": "no"}
{"id": 6244544779699261366, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l2-2, l2-1, and l2-0 are in c2; l0-0, l0-2, and l0-1 are in c0. Currently, t0, a0, and p0 are at l0-0, p3 and p1 are at l2-0, p4 is at l1-0, t2 is at l2-1, t1 is at l1-1, p2 is at l0-1. The goal is to reach a state where the following facts hold: p2 is at l1-2, p1 is at l1-0, p0 is at l1-2, p3 is at l1-2, and p4 is at l0-0.", "question": "Given the plan: \"navigate the truck t1 from location l1-1 in city c1 to location l1-0 in the same city, place the object p0 onto the airplane a0 at location l0-0, operate the airplane a0 from airport l0-0 to airport l2-0, place the object p1 onto the airplane a0 at location l2-0, place the object p3 onto the airplane a0 at location l2-0, operate the airplane a0 from airport l2-0 to airport l1-0, offload the object p0 from the airplane a0 at location l1-0, load object p0 into truck t1 at location l1-0, offload the object p1 from the airplane a0 at location l1-0, offload the object p3 from the airplane a0 at location l1-0, load object p3 into truck t1 at location l1-0, place the object p4 onto the airplane a0 at location l1-0, navigate the truck t1 from location l1-0 in city c1 to location l1-2 in the same city, offload the object p0 from the truck t1 at location l1-2, offload the object p3 from the truck t1 at location l1-2, navigate the truck t0 from location l0-0 in city c0 to location l0-1 in the same city, load object p2 into truck t0 at location l0-1, navigate the truck t0 from location l0-1 in city c0 to location l0-0 in the same city, offload the object p2 from the truck t0 at location l0-0, operate the airplane a0 from airport l1-0 to airport l0-0, place the object p2 onto the airplane a0 at location l0-0, offload the object p4 from the airplane a0 at location l0-0, operate the airplane a0 from airport l0-0 to airport l1-0, offload the object p2 from the airplane a0 at location l1-0, navigate the truck t1 from location l1-2 in city c1 to location l1-0 in the same city, load object p2 into truck t1 at location l1-0, navigate the truck t1 from location l1-0 in city c1 to location l1-2 in the same city, offload the object p2 from the truck t1 at location l1-2, navigate the truck t1 from location l1-2 in city c1 to location l1-1 in the same city\"; can the following action be removed from this plan and still have a valid plan: navigate the truck t1 from location l1-2 in city c1 to location l1-1 in the same city?", "answer": "yes"}
{"id": 7660916037436323851, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l2-2, l2-1, and l2-0 are in c2; l0-0, l0-2, and l0-1 are in c0. Currently, t0, a0, and p0 are at l0-0, p3 and p1 are at l2-0, p4 is at l1-0, t2 is at l2-1, t1 is at l1-1, p2 is at l0-1. The goal is to reach a state where the following facts hold: p2 is at l1-2, p1 is at l1-0, p0 is at l1-2, p3 is at l1-2, and p4 is at l0-0.", "question": "Given the plan: \"drive the truck t1 in city c1 from location l1-1 to location l1-0, place the object p0 onto the airplane a0 at location l0-0, operate the airplane a0 from airport l0-0 to airport l2-0, place the object p1 onto the airplane a0 at location l2-0, place the object p3 onto the airplane a0 at location l2-0, operate the airplane a0 from airport l2-0 to airport l1-0, unload the object p0 from the airplane a0 at location l1-0, place the object p0 into the truck t1 at location l1-0, unload the object p1 from the airplane a0 at location l1-0, unload the object p3 from the airplane a0 at location l1-0, place the object p3 into the truck t1 at location l1-0, place the object p4 onto the airplane a0 at location l1-0, drive the truck t1 in city c1 from location l1-0 to location l1-2, unload the object p0 from the truck t1 at location l1-2, unload the object p3 from the truck t1 at location l1-2, drive the truck t0 in city c0 from location l0-0 to location l0-1, place the object p2 into the truck t0 at location l0-1, drive the truck t0 in city c0 from location l0-1 to location l0-0, unload the object p2 from the truck t0 at location l0-0, operate the airplane a0 from airport l1-0 to airport l0-0, place the object p2 onto the airplane a0 at location l0-0, unload the object p4 from the airplane a0 at location l0-0, operate the airplane a0 from airport l0-0 to airport l1-0, unload the object p2 from the airplane a0 at location l1-0, drive the truck t1 in city c1 from location l1-2 to location l1-0, place the object p2 into the truck t1 at location l1-0, drive the truck t1 in city c1 from location l1-0 to location l1-2, unload the object p2 from the truck t1 at location l1-2, drive the truck t1 in city c1 from location l1-2 to location l1-1\"; can the following action be removed from this plan and still have a valid plan: drive the truck t1 in city c1 from location l1-2 to location l1-0?", "answer": "no"}
{"id": 2033626071072718835, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, t0, a0, and p3 are at l0-0, p2 and p0 are at l0-1, p1 and t1 are at l1-0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p0 is at l1-1, p2 is at l1-0, and p3 is at l1-1.", "question": "Given the plan: \"load the object p1 from location l1-0 into the truck t1, load object p3 into airplane a0 at location l0-0, navigate the truck t0 from location l0-0 in city c0 to location l0-1 in the same city, load the object p0 from location l0-1 into the truck t0, load the object p2 from location l0-1 into the truck t0, fly the airplane a0 from location l0-0 to location l1-0, unload the object p3 from the airplane a0 at location l1-0, load the object p3 from location l1-0 into the truck t1, navigate the truck t1 from location l1-0 in city c1 to location l1-1 in the same city, offload the object p1 from the truck t1 at location l1-1, offload the object p3 from the truck t1 at location l1-1, navigate the truck t0 from location l0-1 in city c0 to location l0-0 in the same city, offload the object p0 from the truck t0 at location l0-0, load the object p0 from location l0-0 into the truck t0, offload the object p2 from the truck t0 at location l0-0, offload the object p0 from the truck t0 at location l0-0, fly the airplane a0 from location l1-0 to location l0-0, load object p0 into airplane a0 at location l0-0, load object p2 into airplane a0 at location l0-0, fly the airplane a0 from location l0-0 to location l1-0, unload the object p0 from the airplane a0 at location l1-0, unload the object p2 from the airplane a0 at location l1-0, navigate the truck t1 from location l1-1 in city c1 to location l1-0 in the same city, load the object p0 from location l1-0 into the truck t1, navigate the truck t1 from location l1-0 in city c1 to location l1-1 in the same city, offload the object p0 from the truck t1 at location l1-1\"; can the following action be removed from this plan and still have a valid plan: navigate the truck t0 from location l0-0 in city c0 to location l0-1 in the same city?", "answer": "no"}
{"id": -5301120506305124744, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l2-2, l2-1, and l2-0 are in c2; l0-0, l0-2, and l0-1 are in c0. Currently, t0, a0, and p0 are at l0-0, p3 and p1 are at l2-0, p4 is at l1-0, t2 is at l2-1, t1 is at l1-1, p2 is at l0-1. The goal is to reach a state where the following facts hold: p2 is at l1-2, p1 is at l1-0, p0 is at l1-2, p3 is at l1-2, and p4 is at l0-0.", "question": "Given the plan: \"drive the truck t1 in city c1 from location l1-1 to location l1-0, place the object p0 onto the airplane a0 at location l0-0, fly the airplane a0 from location l0-0 to location l2-0, place the object p1 onto the airplane a0 at location l2-0, place the object p3 onto the airplane a0 at location l2-0, fly the airplane a0 from location l2-0 to location l1-0, remove the object p0 from the airplane a0 and place it on the location l1-0, load the object p0 from location l1-0 into the truck t1, remove the object p1 from the airplane a0 and place it on the location l1-0, remove the object p3 from the airplane a0 and place it on the location l1-0, load the object p3 from location l1-0 into the truck t1, place the object p4 onto the airplane a0 at location l1-0, drive the truck t1 in city c1 from location l1-0 to location l1-2, offload the object p0 from the truck t1 at location l1-2, offload the object p3 from the truck t1 at location l1-2, drive the truck t0 in city c0 from location l0-0 to location l0-1, load the object p2 from location l0-1 into the truck t0, drive the truck t0 in city c0 from location l0-1 to location l0-0, offload the object p2 from the truck t0 at location l0-0, fly the airplane a0 from location l1-0 to location l0-0, place the object p2 onto the airplane a0 at location l0-0, remove the object p4 from the airplane a0 and place it on the location l0-0, fly the airplane a0 from location l0-0 to location l1-0, remove the object p2 from the airplane a0 and place it on the location l1-0, drive the truck t1 in city c1 from location l1-2 to location l1-0, load the object p2 from location l1-0 into the truck t1, drive the truck t1 in city c1 from location l1-0 to location l1-2, offload the object p2 from the truck t1 at location l1-2, drive the truck t2 in city c2 from location l2-1 to location l2-2\"; can the following action be removed from this plan and still have a valid plan: fly the airplane a0 from location l2-0 to location l1-0?", "answer": "no"}
{"id": 2735848798350094006, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, t0, a0, and p3 are at l0-0, p2 and p0 are at l0-1, p1 and t1 are at l1-0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p0 is at l1-1, p2 is at l1-0, and p3 is at l1-1.", "question": "Given the plan: \"place the object p1 into the truck t1 at location l1-0, place the object p3 onto the airplane a0 at location l0-0, drive truck t0 from location l0-0 in city c0 to location l0-1 in the same city, place the object p0 into the truck t0 at location l0-1, place the object p2 into the truck t0 at location l0-1, fly the airplane a0 from location l0-0 to location l1-0, unload the object p3 from the airplane a0 at location l1-0, place the object p3 into the truck t1 at location l1-0, drive truck t1 from location l1-0 in city c1 to location l1-1 in the same city, unload object p1 from truck t1 at location l1-1, unload object p3 from truck t1 at location l1-1, drive truck t0 from location l0-1 in city c0 to location l0-0 in the same city, unload object p0 from truck t0 at location l0-0, unload object p2 from truck t0 at location l0-0, drive truck t0 from location l0-0 in city c0 to location l0-1 in the same city, drive truck t0 from location l0-1 in city c0 to location l0-0 in the same city, fly the airplane a0 from location l1-0 to location l0-0, place the object p0 onto the airplane a0 at location l0-0, place the object p2 onto the airplane a0 at location l0-0, fly the airplane a0 from location l0-0 to location l1-0, unload the object p0 from the airplane a0 at location l1-0, unload the object p2 from the airplane a0 at location l1-0, drive truck t1 from location l1-1 in city c1 to location l1-0 in the same city, place the object p0 into the truck t1 at location l1-0, drive truck t1 from location l1-0 in city c1 to location l1-1 in the same city, unload object p0 from truck t1 at location l1-1\"; can the following action be removed from this plan and still have a valid plan: drive truck t0 from location l0-1 in city c0 to location l0-0 in the same city?", "answer": "yes"}
{"id": -7752095510866846986, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l2-2, l2-1, and l2-0 are in c2; l0-0, l0-2, and l0-1 are in c0. Currently, t0, a0, and p0 are at l0-0, p3 and p1 are at l2-0, p4 is at l1-0, t2 is at l2-1, t1 is at l1-1, p2 is at l0-1. The goal is to reach a state where the following facts hold: p2 is at l1-2, p1 is at l1-0, p0 is at l1-2, p3 is at l1-2, and p4 is at l0-0.", "question": "Given the plan: \"navigate the truck t1 from its current location l1-1 in city c1 to the new location l1-0 within the same city, load object p0 into airplane a0 at location l0-0, navigate the truck t0 from its current location l0-0 in city c0 to the new location l0-1 within the same city, load the object p2 from location l0-1 into the truck t0, fly the airplane a0 from location l0-0 to location l2-0, load object p1 into airplane a0 at location l2-0, load object p3 into airplane a0 at location l2-0, fly the airplane a0 from location l2-0 to location l1-0, offload the object p0 from the airplane a0 at location l1-0, load the object p0 from location l1-0 into the truck t1, offload the object p1 from the airplane a0 at location l1-0, offload the object p3 from the airplane a0 at location l1-0, load the object p3 from location l1-0 into the truck t1, load object p4 into airplane a0 at location l1-0, navigate the truck t1 from its current location l1-0 in city c1 to the new location l1-2 within the same city, offload the object p0 from the truck t1 at location l1-2, offload the object p3 from the truck t1 at location l1-2, navigate the truck t0 from its current location l0-1 in city c0 to the new location l0-0 within the same city, offload the object p2 from the truck t0 at location l0-0, fly the airplane a0 from location l1-0 to location l0-0, load object p2 into airplane a0 at location l0-0, offload the object p4 from the airplane a0 at location l0-0, fly the airplane a0 from location l0-0 to location l1-0, offload the object p2 from the airplane a0 at location l1-0, navigate the truck t1 from its current location l1-2 in city c1 to the new location l1-0 within the same city, load the object p2 from location l1-0 into the truck t1, navigate the truck t1 from its current location l1-0 in city c1 to the new location l1-2 within the same city, offload the object p2 from the truck t1 at location l1-2, navigate the truck t2 from its current location l2-1 in city c2 to the new location l2-2 within the same city\"; can the following action be removed from this plan and still have a valid plan: navigate the truck t2 from its current location l2-1 in city c2 to the new location l2-2 within the same city?", "answer": "yes"}
{"id": 1851874060262167436, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_5. The following block(s) are stacked on top of another block: block_2 is on block_1, block_4 is on block_3, and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_5 is on top of block block_1, The block block_5 is currently situated under the block block_2, The block block_4 is on top of block block_2, and The block block_1 is on top of block block_3.", "question": "Given the plan: \"unstack object block_4 from object block_3, put down object block_4, pick up the object block_4 from the table, put down object block_4, unstack object block_3 from object block_2, put down object block_3, unstack object block_2 from object block_1, stack the object block_2 on top of the object block_4, pick up the object block_1 from the table, stack the object block_1 on top of the object block_3, pick up the object block_5 from the table, stack the object block_5 on top of the object block_2, unstack object block_5 from object block_2, stack the object block_5 on top of the object block_1, unstack object block_2 from object block_4, stack the object block_2 on top of the object block_5, pick up the object block_4 from the table, stack the object block_4 on top of the object block_2\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: stack the object block_5 on top of the object block_2 and unstack object block_5 from object block_2?", "answer": "yes"}
{"id": -7634464818431618385, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_5. The following block(s) are stacked on top of another block: block_2 is on block_1, block_4 is on block_3, and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_5, The block block_2 is on top of block block_5, The block block_2 is currently situated under the block block_4, and The block block_1 is on top of block block_3.", "question": "Given the plan: \"remove the object block_4 from on top of the object block_3, put down object block_4, remove the object block_3 from on top of the object block_2, stack the object block_3 on top of the object block_5, remove the object block_3 from on top of the object block_5, put down object block_3, remove the object block_2 from on top of the object block_1, stack the object block_2 on top of the object block_4, collect the object block_1 from the table, stack the object block_1 on top of the object block_3, collect the object block_5 from the table, stack the object block_5 on top of the object block_1, remove the object block_2 from on top of the object block_4, stack the object block_2 on top of the object block_5, collect the object block_4 from the table, stack the object block_4 on top of the object block_2\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: stack the object block_3 on top of the object block_5 and remove the object block_3 from on top of the object block_5?", "answer": "yes"}
{"id": 7246690288083552689, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) is on the table: block_1. The following block(s) are stacked on top of another block: block_2 is on block_1 and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_3 is on top of block block_1.", "question": "Given the plan: \"remove the object block_3 from on top of the object block_2, place the object block_3 on the table, remove the object block_2 from on top of the object block_1, stack the object block_2 on top of the object block_3, remove the object block_2 from on top of the object block_3, place the object block_2 on the table, remove block_3 from table, place the object block_3 on the table, remove block_3 from table, stack the object block_3 on top of the object block_1, remove block_2 from table, stack the object block_2 on top of the object block_3\"; can the following action be removed from this plan and still have a valid plan: stack the object block_2 on top of the object block_3?", "answer": "yes"}
{"id": 1447913331789251765, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_18, block_1, block_6, block_19, block_7, and block_14. The following block(s) are stacked on top of another block: block_20 is on block_8, block_16 is on block_20, block_15 is on block_16, block_17 is on block_3, block_11 is on block_1, block_3 is on block_13, block_10 is on block_19, block_13 is on block_4, block_12 is on block_17, block_2 is on block_18, block_5 is on block_14, block_4 is on block_11, block_9 is on block_15, and block_8 is on block_7. The goal is to reach a state where the following facts hold: The block block_16 is on top of block block_11, The block block_12 is currently situated under the block block_17, The block block_4 is currently situated above the block block_6, The block block_1 is on top of block block_17, The block block_2 is on top of block block_14, The block block_5 is on top of block block_1, The block block_19 is currently situated above the block block_16, The block block_3 is currently situated above the block block_13, The block block_18 is on top of block block_5, The block block_8 is currently situated under the block block_11, The block block_15 is currently situated above the block block_9, The block block_8 is currently situated above the block block_4, The block block_7 is on top of block block_10, The block block_7 is currently situated under the block block_12, The block block_9 is currently situated above the block block_2, and The block block_14 is on top of block block_20.", "question": "Given the plan: \"unstack the object block_10 from the object block_19, put down object block_10, unstack the object block_9 from the object block_15, stack the object block_9 on top of the object block_2, unstack the object block_12 from the object block_17, stack the object block_12 on top of the object block_10, unstack the object block_15 from the object block_16, stack the object block_15 on top of the object block_9, unstack the object block_16 from the object block_20, stack the object block_16 on top of the object block_15, unstack the object block_20 from the object block_8, put down object block_20, unstack the object block_17 from the object block_3, stack the object block_17 on top of the object block_12, remove block_19 from table, stack the object block_19 on top of the object block_16, unstack the object block_5 from the object block_14, stack the object block_5 on top of the object block_6, remove block_14 from table, stack the object block_14 on top of the object block_20, unstack the object block_3 from the object block_13, stack the object block_3 on top of the object block_19, unstack the object block_13 from the object block_4, put down object block_13, unstack the object block_4 from the object block_11, stack the object block_4 on top of the object block_8, unstack the object block_11 from the object block_1, stack the object block_11 on top of the object block_13, remove block_1 from table, stack the object block_1 on top of the object block_17, unstack the object block_5 from the object block_6, stack the object block_5 on top of the object block_1, unstack the object block_4 from the object block_8, stack the object block_4 on top of the object block_6, unstack the object block_8 from the object block_7, stack the object block_8 on top of the object block_4, unstack the object block_11 from the object block_13, stack the object block_11 on top of the object block_8, unstack the object block_3 from the object block_19, stack the object block_3 on top of the object block_13, unstack the object block_5 from the object block_1, stack the object block_5 on top of the object block_11, unstack the object block_1 from the object block_17, stack the object block_1 on top of the object block_19, unstack the object block_17 from the object block_12, stack the object block_17 on top of the object block_1, unstack the object block_12 from the object block_10, stack the object block_12 on top of the object block_14, remove block_7 from table, stack the object block_7 on top of the object block_10, unstack the object block_12 from the object block_14, stack the object block_12 on top of the object block_7, unstack the object block_17 from the object block_1, stack the object block_17 on top of the object block_12, unstack the object block_1 from the object block_19, stack the object block_1 on top of the object block_17, unstack the object block_5 from the object block_11, stack the object block_5 on top of the object block_1, unstack the object block_19 from the object block_16, stack the object block_19 on top of the object block_14, unstack the object block_16 from the object block_15, stack the object block_16 on top of the object block_11, unstack the object block_19 from the object block_14, stack the object block_19 on top of the object block_16, unstack the object block_15 from the object block_9, stack the object block_15 on top of the object block_14, unstack the object block_9 from the object block_2, stack the object block_9 on top of the object block_15, unstack the object block_2 from the object block_18, stack the object block_2 on top of the object block_3, remove block_18 from table, stack the object block_18 on top of the object block_5, unstack the object block_9 from the object block_15, stack the object block_9 on top of the object block_19, unstack the object block_15 from the object block_14, stack the object block_15 on top of the object block_9, unstack the object block_2 from the object block_3, stack the object block_2 on top of the object block_14, unstack the object block_15 from the object block_9, stack the object block_15 on top of the object block_18, unstack the object block_9 from the object block_19, stack the object block_9 on top of the object block_19, unstack the object block_9 from the object block_19, stack the object block_9 on top of the object block_2, unstack the object block_15 from the object block_18, stack the object block_15 on top of the object block_9\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: unstack the object block_9 from the object block_19 and stack the object block_9 on top of the object block_19?", "answer": "yes"}
{"id": 1191412835998500791, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_5. The following block(s) are stacked on top of another block: block_2 is on block_1, block_4 is on block_3, and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_5, The block block_2 is currently situated above the block block_5, The block block_4 is on top of block block_2, and The block block_3 is currently situated under the block block_1.", "question": "Given the plan: \"unstack object block_4 from object block_3, put down the object block_4, unstack object block_3 from object block_2, stack object block_3 on top of object block_5, unstack object block_3 from object block_5, put down the object block_3, unstack object block_2 from object block_1, stack object block_2 on top of object block_4, pick up the object block_1 from the table, stack object block_1 on top of object block_3, pick up the object block_5 from the table, stack object block_5 on top of object block_1, unstack object block_2 from object block_4, stack object block_2 on top of object block_5, pick up the object block_4 from the table, stack object block_4 on top of object block_2\"; can the following action be removed from this plan and still have a valid plan: pick up the object block_5 from the table?", "answer": "no"}
{"id": 5081501847287293561, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_18, block_1, block_6, block_19, block_7, and block_14. The following block(s) are stacked on top of another block: block_20 is on block_8, block_16 is on block_20, block_15 is on block_16, block_17 is on block_3, block_11 is on block_1, block_3 is on block_13, block_10 is on block_19, block_13 is on block_4, block_12 is on block_17, block_2 is on block_18, block_5 is on block_14, block_4 is on block_11, block_9 is on block_15, and block_8 is on block_7. The goal is to reach a state where the following facts hold: The block block_16 is currently situated above the block block_11, The block block_17 is currently situated above the block block_12, The block block_4 is on top of block block_6, The block block_1 is currently situated above the block block_17, The block block_2 is on top of block block_14, The block block_5 is currently situated above the block block_1, The block block_19 is currently situated above the block block_16, The block block_13 is currently situated under the block block_3, The block block_18 is on top of block block_5, The block block_8 is currently situated under the block block_11, The block block_15 is on top of block block_9, The block block_8 is currently situated above the block block_4, The block block_10 is currently situated under the block block_7, The block block_7 is currently situated under the block block_12, The block block_2 is currently situated under the block block_9, and The block block_20 is currently situated under the block block_14.", "question": "Given the plan: \"remove the object block_10 from on top of the object block_19, put down object block_10, remove the object block_9 from on top of the object block_15, stack object block_9 on top of object block_2, remove the object block_12 from on top of the object block_17, stack object block_12 on top of object block_10, remove the object block_15 from on top of the object block_16, stack object block_15 on top of object block_9, remove the object block_16 from on top of the object block_20, stack object block_16 on top of object block_15, remove the object block_20 from on top of the object block_8, put down object block_20, remove the object block_17 from on top of the object block_3, stack object block_17 on top of object block_12, pick up object block_19 from the table, stack object block_19 on top of object block_16, remove the object block_5 from on top of the object block_14, stack object block_5 on top of object block_6, pick up object block_14 from the table, stack object block_14 on top of object block_20, remove the object block_3 from on top of the object block_13, stack object block_3 on top of object block_19, remove the object block_13 from on top of the object block_4, put down object block_13, remove the object block_4 from on top of the object block_11, stack object block_4 on top of object block_8, remove the object block_11 from on top of the object block_1, stack object block_11 on top of object block_13, pick up object block_1 from the table, stack object block_1 on top of object block_17, remove the object block_5 from on top of the object block_6, stack object block_5 on top of object block_1, remove the object block_4 from on top of the object block_8, stack object block_4 on top of object block_6, remove the object block_8 from on top of the object block_7, stack object block_8 on top of object block_4, remove the object block_11 from on top of the object block_13, stack object block_11 on top of object block_8, remove the object block_3 from on top of the object block_19, stack object block_3 on top of object block_13, remove the object block_5 from on top of the object block_1, stack object block_5 on top of object block_11, remove the object block_1 from on top of the object block_17, stack object block_1 on top of object block_19, remove the object block_17 from on top of the object block_12, stack object block_17 on top of object block_1, remove the object block_12 from on top of the object block_10, stack object block_12 on top of object block_14, pick up object block_7 from the table, stack object block_7 on top of object block_10, remove the object block_12 from on top of the object block_14, stack object block_12 on top of object block_7, remove the object block_17 from on top of the object block_1, stack object block_17 on top of object block_12, remove the object block_1 from on top of the object block_19, stack object block_1 on top of object block_17, remove the object block_5 from on top of the object block_11, stack object block_5 on top of object block_1, remove the object block_19 from on top of the object block_16, stack object block_19 on top of object block_14, remove the object block_16 from on top of the object block_15, stack object block_16 on top of object block_11, remove the object block_19 from on top of the object block_14, stack object block_19 on top of object block_16, remove the object block_15 from on top of the object block_9, stack object block_15 on top of object block_14, remove the object block_9 from on top of the object block_2, stack object block_9 on top of object block_15, remove the object block_2 from on top of the object block_18, stack object block_2 on top of object block_19, pick up object block_18 from the table, put down object block_18, pick up object block_18 from the table, put down object block_18, pick up object block_18 from the table, stack object block_18 on top of object block_5, remove the object block_9 from on top of the object block_15, stack object block_9 on top of object block_18, remove the object block_15 from on top of the object block_14, stack object block_15 on top of object block_9, remove the object block_2 from on top of the object block_19, stack object block_2 on top of object block_14, remove the object block_15 from on top of the object block_9, stack object block_15 on top of object block_19, remove the object block_9 from on top of the object block_18, stack object block_9 on top of object block_2, remove the object block_15 from on top of the object block_19, stack object block_15 on top of object block_9\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: put down object block_18 and pick up object block_18 from the table?", "answer": "yes"}
{"id": -1593190598297143480, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) is on the table: block_1. The following block(s) are stacked on top of another block: block_2 is on block_1 and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_3.", "question": "Given the plan: \"unstack the object block_3 from the object block_2, stack object block_3 on top of object block_2, unstack the object block_3 from the object block_2, put down object block_3, unstack the object block_2 from the object block_1, put down object block_2, collect the object block_3 from the table, stack object block_3 on top of object block_1, unstack the object block_3 from the object block_1, put down object block_3, collect the object block_3 from the table, stack object block_3 on top of object block_1\"; can the following action be removed from this plan and still have a valid plan: stack object block_3 on top of object block_2?", "answer": "no"}
{"id": 6888605824248685564, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_18, block_1, block_6, block_19, block_7, and block_14. The following block(s) are stacked on top of another block: block_20 is on block_8, block_16 is on block_20, block_15 is on block_16, block_17 is on block_3, block_11 is on block_1, block_3 is on block_13, block_10 is on block_19, block_13 is on block_4, block_12 is on block_17, block_2 is on block_18, block_5 is on block_14, block_4 is on block_11, block_9 is on block_15, and block_8 is on block_7. The goal is to reach a state where the following facts hold: The block block_11 is currently situated under the block block_16, The block block_17 is currently situated above the block block_12, The block block_4 is on top of block block_6, The block block_1 is currently situated above the block block_17, The block block_14 is currently situated under the block block_2, The block block_1 is currently situated under the block block_5, The block block_16 is currently situated under the block block_19, The block block_3 is currently situated above the block block_13, The block block_18 is on top of block block_5, The block block_11 is currently situated above the block block_8, The block block_9 is currently situated under the block block_15, The block block_8 is on top of block block_4, The block block_7 is on top of block block_10, The block block_12 is on top of block block_7, The block block_9 is currently situated above the block block_2, and The block block_14 is on top of block block_20.", "question": "Given the plan: \"remove the object block_9 from on top of the object block_15, place the object block_9 on top of the object block_2, remove the object block_15 from on top of the object block_16, place the object block_15 on the table, remove the object block_12 from on top of the object block_17, place the object block_12 on the table, remove the object block_16 from on top of the object block_20, place the object block_16 on the table, remove the object block_17 from on top of the object block_3, place the object block_17 on the table, remove the object block_5 from on top of the object block_14, place the object block_5 on top of the object block_17, remove the object block_5 from on top of the object block_17, place the object block_5 on the table, remove the object block_10 from on top of the object block_19, place the object block_10 on the table, remove the object block_20 from on top of the object block_8, place the object block_20 on the table, remove block_15 from table, place the object block_15 on top of the object block_9, remove block_14 from table, place the object block_14 on top of the object block_20, remove block_17 from table, place the object block_17 on top of the object block_12, remove block_19 from table, place the object block_19 on top of the object block_16, remove the object block_8 from on top of the object block_7, place the object block_8 on the table, remove block_7 from table, place the object block_7 on top of the object block_10, remove the object block_17 from on top of the object block_12, place the object block_17 on the table, remove block_12 from table, place the object block_12 on top of the object block_7, remove block_17 from table, place the object block_17 on top of the object block_12, remove the object block_3 from on top of the object block_13, place the object block_3 on the table, remove the object block_13 from on top of the object block_4, place the object block_13 on the table, remove block_3 from table, place the object block_3 on top of the object block_13, remove the object block_4 from on top of the object block_11, place the object block_4 on top of the object block_6, remove the object block_11 from on top of the object block_1, place the object block_11 on top of the object block_8, remove block_1 from table, place the object block_1 on top of the object block_17, remove block_5 from table, place the object block_5 on top of the object block_1, remove the object block_11 from on top of the object block_8, place the object block_11 on the table, remove block_8 from table, place the object block_8 on top of the object block_4, remove block_11 from table, place the object block_11 on top of the object block_8, remove the object block_19 from on top of the object block_16, place the object block_19 on the table, remove block_16 from table, place the object block_16 on top of the object block_11, remove block_19 from table, place the object block_19 on top of the object block_16, remove the object block_15 from on top of the object block_9, place the object block_15 on the table, remove the object block_9 from on top of the object block_2, place the object block_9 on the table, remove block_15 from table, place the object block_15 on top of the object block_9, remove the object block_2 from on top of the object block_18, place the object block_2 on top of the object block_14, remove block_18 from table, place the object block_18 on top of the object block_5, remove the object block_15 from on top of the object block_9, place the object block_15 on the table, remove block_9 from table, place the object block_9 on top of the object block_2, remove block_15 from table, place the object block_15 on top of the object block_9\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: place the object block_5 on top of the object block_17 and remove the object block_5 from on top of the object block_17?", "answer": "yes"}
{"id": -4024253297516936144, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_5. The following block(s) are stacked on top of another block: block_2 is on block_1, block_4 is on block_3, and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_5, The block block_2 is currently situated above the block block_5, The block block_4 is on top of block block_2, and The block block_1 is on top of block block_3.", "question": "Given the plan: \"remove the object block_4 from on top of the object block_3, place the object block_4 on the table, remove the object block_3 from on top of the object block_2, place the object block_3 on the table, remove the object block_2 from on top of the object block_1, place the object block_2 on the table, pick up object block_1 from the table, place the object block_1 on top of the object block_4, remove the object block_1 from on top of the object block_4, place the object block_1 on top of the object block_3, pick up object block_5 from the table, place the object block_5 on top of the object block_1, pick up object block_2 from the table, place the object block_2 on top of the object block_5, pick up object block_4 from the table, place the object block_4 on top of the object block_2\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: place the object block_1 on top of the object block_4 and remove the object block_1 from on top of the object block_4?", "answer": "yes"}
{"id": -3966675097644208513, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) is on the table: block_1. The following block(s) are stacked on top of another block: block_2 is on block_1 and block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_3 is currently situated above the block block_1.", "question": "Given the plan: \"unstack the object block_3 from the object block_2, put down object block_3, unstack the object block_2 from the object block_1, put down object block_2, pick up object block_2 from the table, put down object block_2, pick up object block_1 from the table, place the object block_1 on top of the object block_2, pick up object block_3 from the table, place the object block_3 on top of the object block_1, unstack the object block_3 from the object block_1, place the object block_3 on top of the object block_1\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: place the object block_3 on top of the object block_1 and unstack the object block_3 from the object block_1?", "answer": "yes"}
{"id": 5568815713613709562, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f2-1f has shape1 shaped lock, f3-4f has shape1 shaped lock. Key key1-0 is at position f1-2f. Key key1-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key1-1 is at f4-4f location and Key key1-0 is at f1-0f location.", "question": "Given the plan: \"travel from the current position f0-4f to the next position f0-3f, travel from the current position f0-3f to the next position f1-3f, travel from the current position f1-3f to the next position f1-2f, retrieve the key key1-0 from its current position f1-2f, travel from the current position f1-2f to the next position f1-1f, put down the key key1-0 at the current position f1-1f, retrieve the key key1-0 from its current position f1-1f, travel from the current position f1-1f to the next position f1-0f, put down the key key1-0 at the current position f1-0f, travel from the current position f1-0f to the next position f2-0f, travel from the current position f2-0f to the next position f3-0f, retrieve the key key1-1 from its current position f3-0f, travel from the current position f3-0f to the next position f4-0f, travel from the current position f4-0f to the next position f4-1f, travel from the current position f4-1f to the next position f4-2f, travel from the current position f4-2f to the next position f4-3f, travel from the current position f4-3f to the next position f4-4f, put down the key key1-1 at the current position f4-4f\"; can the following action be removed from this plan and still have a valid plan: retrieve the key key1-0 from its current position f1-1f?", "answer": "no"}
{"id": -4510498081521909201, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-3f and its arm is empty. All the positions are open except the following: f2-0f has shape0 shaped lock, f2-2f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-1 is at position f2-2f. Key key0-0 is at position f1-3f. Key key0-2 is at position f0-4f. The goal is to reach a state where the following facts hold: Key key0-0 is at f3-3f location, Key key0-2 is at f3-3f location, and Key key0-1 is at f1-2f location.", "question": "Given the plan: \"move from f2-3f to f2-4f, move from f2-4f to f1-4f, move from f1-4f to f0-4f, pick up key key0-2 from place f0-4f, move from f0-4f to f1-4f, move from f1-4f to f2-4f, move from f2-4f to f2-3f, unlock the place f2-2f with the key key0-2 of the shape shape0 from the current position place f2-3f, move from f2-3f to f3-3f, put the key key0-2 at the current position place f3-3f, move from f3-3f to f3-2f, move from f3-2f to f2-2f, pick up key key0-1 from place f2-2f, move from f2-2f to f1-2f, put the key key0-1 at the current position place f1-2f, move from f1-2f to f1-3f, pick up key key0-0 from place f1-3f, move from f1-3f to f2-3f, move from f2-3f to f3-3f, pick up the key key0-2 from the current position f3-3f and loose the key key0-0 which is being held, put the key key0-2 at the current position place f3-3f\"; can the following action be removed from this plan and still have a valid plan: move from f2-3f to f3-3f?", "answer": "no"}
{"id": 7346202495984112720, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-3 is of shape shape0.  Currently, the robot is at position f3-1f and its arm is empty. All the positions are open except the following: f0-2f has shape0 shaped lock, f0-0f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-1 is at position f4-2f. Key key0-3 is at position f3-1f. Key key0-0 is at position f0-0f. Key key0-2 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-4f location, Key key0-3 is at f0-1f location, Key key0-0 is at f0-1f location, and Key key0-2 is at f4-0f location.", "question": "Given the plan: \"acquire the key key0-3 from the place f3-1f, put the key key0-3 at the current position place f3-1f, acquire the key key0-3 from the place f3-1f, transition from the current position f3-1f to the next position f2-1f, transition from the current position f2-1f to the next position f1-1f, transition from the current position f1-1f to the next position f0-1f, use the key key0-3 of shape shape0 to unlock the place f0-0f from its current position f0-1f, put the key key0-3 at the current position place f0-1f, transition from the current position f0-1f to the next position f0-0f, acquire the key key0-0 from the place f0-0f, transition from the current position f0-0f to the next position f0-1f, put the key key0-0 at the current position place f0-1f, transition from the current position f0-1f to the next position f1-1f, transition from the current position f1-1f to the next position f2-1f, transition from the current position f2-1f to the next position f3-1f, transition from the current position f3-1f to the next position f3-0f, acquire the key key0-2 from the place f3-0f, use the key key0-2 of shape shape0 to unlock the place f4-0f from its current position f3-0f, transition from the current position f3-0f to the next position f4-0f, put the key key0-2 at the current position place f4-0f, transition from the current position f4-0f to the next position f4-1f, transition from the current position f4-1f to the next position f4-2f, acquire the key key0-1 from the place f4-2f, transition from the current position f4-2f to the next position f3-2f, transition from the current position f3-2f to the next position f2-2f, transition from the current position f2-2f to the next position f2-3f, transition from the current position f2-3f to the next position f2-4f, transition from the current position f2-4f to the next position f1-4f, put the key key0-1 at the current position place f1-4f\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: acquire the key key0-3 from the place f3-1f and put the key key0-3 at the current position place f3-1f?", "answer": "yes"}
{"id": 8530234960789714198, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-3 is of shape shape0.  Currently, the robot is at position f3-1f and its arm is empty. All the positions are open except the following: f0-2f has shape0 shaped lock, f0-0f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-1 is at position f4-2f. Key key0-3 is at position f3-1f. Key key0-0 is at position f0-0f. Key key0-2 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f1-4f location, Key key0-3 is at f0-1f location, Key key0-0 is at f0-1f location, and Key key0-2 is at f4-0f location.", "question": "Given the plan: \"retrieve the key key0-3 from its current position f3-1f, put the key key0-3 at the current position place f3-1f, retrieve the key key0-3 from its current position f3-1f, move from f3-1f to f2-1f, move from f2-1f to f1-1f, move from f1-1f to f0-1f, unlock the place f0-0f with key key0-3 of shape shape0 from the current position place f0-1f, put the key key0-3 at the current position place f0-1f, move from f0-1f to f0-0f, retrieve the key key0-0 from its current position f0-0f, move from f0-0f to f0-1f, put the key key0-0 at the current position place f0-1f, move from f0-1f to f1-1f, move from f1-1f to f2-1f, move from f2-1f to f3-1f, move from f3-1f to f3-0f, retrieve the key key0-2 from its current position f3-0f, unlock the place f4-0f with key key0-2 of shape shape0 from the current position place f3-0f, move from f3-0f to f4-0f, put the key key0-2 at the current position place f4-0f, move from f4-0f to f4-1f, move from f4-1f to f4-2f, retrieve the key key0-1 from its current position f4-2f, move from f4-2f to f4-3f, move from f4-3f to f3-3f, move from f3-3f to f2-3f, move from f2-3f to f1-3f, move from f1-3f to f1-4f, put the key key0-1 at the current position place f1-4f\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: retrieve the key key0-3 from its current position f3-1f and put the key key0-3 at the current position place f3-1f?", "answer": "yes"}
{"id": -7161641395773530309, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f2-1f has shape1 shaped lock, f3-4f has shape1 shaped lock. Key key1-0 is at position f1-2f. Key key1-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key1-1 is at f4-4f location and Key key1-0 is at f1-0f location.", "question": "Given the plan: \"travel from the current position f0-4f to the next position f0-3f, travel from the current position f0-3f to the next position f1-3f, travel from the current position f1-3f to the next position f1-2f, acquire the key key1-0 from the place f1-2f, travel from the current position f1-2f to the next position f1-1f, travel from the current position f1-1f to the next position f1-0f, place the key key1-0 at the current position place f1-0f, travel from the current position f1-0f to the next position f2-0f, travel from the current position f2-0f to the next position f3-0f, acquire the key key1-1 from the place f3-0f, travel from the current position f3-0f to the next position f4-0f, travel from the current position f4-0f to the next position f4-1f, travel from the current position f4-1f to the next position f4-2f, travel from the current position f4-2f to the next position f4-3f, place the key key1-1 at the current position place f4-3f, acquire the key key1-1 from the place f4-3f, travel from the current position f4-3f to the next position f4-4f, place the key key1-1 at the current position place f4-4f\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: place the key key1-1 at the current position place f4-3f and acquire the key key1-1 from the place f4-3f?", "answer": "yes"}
{"id": -1796231726266805070, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f2-1f has shape1 shaped lock, f3-4f has shape1 shaped lock. Key key1-0 is at position f1-2f. Key key1-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key1-1 is at f4-4f location and Key key1-0 is at f1-0f location.", "question": "Given the plan: \"transition from the current position f0-4f to the next position f1-4f, transition from the current position f1-4f to the next position f1-3f, transition from the current position f1-3f to the next position f1-2f, acquire the key key1-0 from the place f1-2f, transition from the current position f1-2f to the next position f1-1f, transition from the current position f1-1f to the next position f1-0f, put down key key1-0 at current position place f1-0f, transition from the current position f1-0f to the next position f2-0f, transition from the current position f2-0f to the next position f3-0f, acquire the key key1-1 from the place f3-0f, transition from the current position f3-0f to the next position f3-1f, transition from the current position f3-1f to the next position f3-2f, transition from the current position f3-2f to the next position f3-3f, transition from the current position f3-3f to the next position f4-3f, transition from the current position f4-3f to the next position f4-4f, put down key key1-1 at current position place f4-4f, acquire the key key1-1 from the place f4-4f, put down key key1-1 at current position place f4-4f\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: acquire the key key1-1 from the place f4-4f and put down key key1-1 at current position place f4-4f?", "answer": "yes"}
{"id": 576121559234460464, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f2-1f has shape1 shaped lock, f3-4f has shape1 shaped lock. Key key1-0 is at position f1-2f. Key key1-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key1-1 is at f4-4f location and Key key1-0 is at f1-0f location.", "question": "Given the plan: \"move from place f0-4f to place f0-3f, move from place f0-3f to place f0-2f, move from place f0-2f to place f1-2f, retrieve the key key1-0 from its current position f1-2f, move from place f1-2f to place f1-1f, move from place f1-1f to place f1-0f, place the key key1-0 at the current position place f1-0f, move from place f1-0f to place f2-0f, move from place f2-0f to place f3-0f, retrieve the key key1-1 from its current position f3-0f, move from place f3-0f to place f3-1f, move from place f3-1f to place f3-2f, move from place f3-2f to place f3-3f, move from place f3-3f to place f4-3f, move from place f4-3f to place f4-4f, use the key key1-1 of shape shape1 to unlock the place f3-4f from its current position f4-4f, place the key key1-1 at the current position place f4-4f, move from place f4-4f to place f4-3f\"; can the following action be removed from this plan and still have a valid plan: move from place f4-3f to place f4-4f?", "answer": "no"}
{"id": 8146825907596388729, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-3f and its arm is empty. All the positions are open except the following: f4-4f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-0 is at position f0-1f. Key key0-1 is at position f1-2f. Key key0-2 is at position f4-3f. The goal is to reach a state where the following facts hold: Key key0-0 is at f2-2f location, Key key0-2 is at f1-4f location, and Key key0-1 is at f3-2f location.", "question": "Given the plan: \"move from place f2-3f to place f3-3f, move from place f3-3f to place f4-3f, pick up key key0-2 from place f4-3f, move from place f4-3f to place f3-3f, move from place f3-3f to place f2-3f, move from place f2-3f to place f2-4f, move from place f2-4f to place f1-4f, place the key key0-2 at the current position place f1-4f, move from place f1-4f to place f1-3f, move from place f1-3f to place f1-2f, move from place f1-2f to place f0-2f, move from place f0-2f to place f0-1f, pick up key key0-0 from place f0-1f, move from place f0-1f to place f0-2f, move from place f0-2f to place f1-2f, move from place f1-2f to place f2-2f, place the key key0-0 at the current position place f2-2f, move from place f2-2f to place f1-2f, pick up key key0-1 from place f1-2f, move from place f1-2f to place f2-2f, move from place f2-2f to place f3-2f, place the key key0-1 at the current position place f3-2f\"; can the following action be removed from this plan and still have a valid plan: move from place f4-3f to place f3-3f?", "answer": "no"}
{"id": 2374419046295360203, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-3f and its arm is empty. All the positions are open except the following: f2-0f has shape0 shaped lock, f2-2f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-1 is at position f2-2f. Key key0-0 is at position f1-3f. Key key0-2 is at position f0-4f. The goal is to reach a state where the following facts hold: Key key0-0 is at f3-3f location, Key key0-2 is at f3-3f location, and Key key0-1 is at f1-2f location.", "question": "Given the plan: \"move from place f2-3f to place f1-3f, move from place f1-3f to place f0-3f, move from place f0-3f to place f0-4f, pick up key key0-2 from place f0-4f, move from place f0-4f to place f0-3f, move from place f0-3f to place f1-3f, move from place f1-3f to place f2-3f, put the key key0-2 at the current position place f2-3f, pick up key key0-2 from place f2-3f, use the key key0-2 of shape shape0 to unlock the place f2-2f from its current position f2-3f, move from place f2-3f to place f3-3f, put the key key0-2 at the current position place f3-3f, move from place f3-3f to place f3-2f, move from place f3-2f to place f2-2f, pick up key key0-1 from place f2-2f, move from place f2-2f to place f1-2f, put the key key0-1 at the current position place f1-2f, move from place f1-2f to place f1-3f, pick up key key0-0 from place f1-3f, move from place f1-3f to place f2-3f, move from place f2-3f to place f3-3f, pick up the key key0-2 from the current position f3-3f and loose the key key0-0 which is being held, put the key key0-2 at the current position place f3-3f\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: put the key key0-2 at the current position place f2-3f and pick up key key0-2 from place f2-3f?", "answer": "yes"}
{"id": -6116387683511340320, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-3f and its arm is empty. All the positions are open except the following: f4-4f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-0 is at position f0-1f. Key key0-1 is at position f1-2f. Key key0-2 is at position f4-3f. The goal is to reach a state where the following facts hold: Key key0-0 is at f2-2f location, Key key0-2 is at f1-4f location, and Key key0-1 is at f3-2f location.", "question": "Given the plan: \"move from place f2-3f to place f2-2f, move from place f2-2f to place f1-2f, move from place f1-2f to place f1-1f, move from place f1-1f to place f0-1f, retrieve the key key0-0 from its current position f0-1f, move from place f0-1f to place f0-2f, move from place f0-2f to place f1-2f, move from place f1-2f to place f2-2f, put down the key key0-0 at the current position f2-2f, move from place f2-2f to place f1-2f, retrieve the key key0-1 from its current position f1-2f, move from place f1-2f to place f2-2f, move from place f2-2f to place f3-2f, put down the key key0-1 at the current position f3-2f, move from place f3-2f to place f3-3f, move from place f3-3f to place f4-3f, retrieve the key key0-2 from its current position f4-3f, unlock place f4-4f with key key0-2 of shape shape0 from current position place f4-3f, move from place f4-3f to place f3-3f, move from place f3-3f to place f2-3f, move from place f2-3f to place f2-4f, move from place f2-4f to place f1-4f, put down the key key0-2 at the current position f1-4f\"; can the following action be removed from this plan and still have a valid plan: unlock place f4-4f with key key0-2 of shape shape0 from current position place f4-3f?", "answer": "yes"}
{"id": 3198820223801183312, "group": "action_justification_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 3 robots. The tiles locations are: tile_19 is to the right of tile_18, tile_10 is to the right of tile_9, tile_3 is to the right of tile_2, tile_15 is to the right of tile_14, tile_4 is to the right of tile_3, tile_9 is to the right of tile_8, tile_20 is to the right of tile_19, tile_18 is to the right of tile_17, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, tile_7 is to the right of tile_6, tile_2 is to the right of tile_1, tile_13 is to the right of tile_12, tile_8 is to the right of tile_7, tile_12 is to the right of tile_11, and tile_14 is to the right of tile_13. Further, tile_8 is down from tile_13, tile_3 is down from tile_8, tile_12 is down from tile_17, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_13 is down from tile_18, tile_9 is down from tile_14, tile_1 is down from tile_6, tile_15 is down from tile_20, tile_11 is down from tile_16, tile_5 is down from tile_10, tile_6 is down from tile_11, tile_10 is down from tile_15, tile_7 is down from tile_12, and tile_14 is down from tile_19 Currently, robot robot3 is at tile_20 and holding color white, robot robot1 is at tile_17 and holding color white, and robot robot2 is at tile_13 and holding color black; tile_14, tile_5, tile_11, tile_18, tile_3, tile_6, tile_1, tile_19, tile_12, tile_10, tile_15, tile_4, tile_7, tile_9, tile_8, tile_2, and tile_16 are clear. The goal is to reach a state where the following facts hold: Tile tile_11 is painted in black color, Tile tile_20 is painted in white color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_6 is painted in white color, Tile tile_19 is painted in black color, Tile tile_18 is painted in white color, Tile tile_16 is painted in white color, Tile tile_9 is painted in black color, Tile tile_14 is painted in white color, Tile tile_7 is painted in black color, Tile tile_15 is painted in black color, Tile tile_13 is painted in black color, Tile tile_17 is painted in black color, and Tile tile_8 is painted in white color.", "question": "Given the plan: \"move robot robot1 from tile tile_17 to the left tile tile tile_16, move robot robot2 down from tile tile_13 to tile tile_8, move robot robot3 down from tile tile_20 to tile tile_15, move robot robot1 down from tile tile_16 to tile tile_11, paint the tile tile_16 above the tile tile_11 with color white using the robot robot1, paint the tile tile_20 above the tile tile_15 with color white using the robot robot3, move robot robot2 down from tile tile_8 to tile tile_3, move robot robot1 down from tile tile_11 to tile tile_6, move robot robot1 down from tile tile_6 to tile tile_1, move the robot robot1 from tile tile_1 to the right tile tile_2, move robot robot3 from tile tile_15 to the left tile tile tile_14, move the robot robot2 from tile tile_3 to the right tile tile_4, move robot robot3 down from tile tile_14 to tile tile_9, move the robot robot2 from tile tile_4 to the right tile tile_5, move the robot robot2 from tile tile_5 to tile tile_10 upwards, paint the tile tile_15 above the tile tile_10 with color black using the robot robot2, alter the color of the robot robot2 from color black to color white, move robot robot2 down from tile tile_10 to tile tile_5, paint the tile tile_10 above the tile tile_5 with color white using the robot robot2, move robot robot2 from tile tile_5 to the left tile tile tile_4, alter the color of the robot robot2 from color white to color black, move the robot robot3 from tile tile_9 to tile tile_14 upwards, move robot robot3 from tile tile_14 to the left tile tile tile_13, paint the tile tile_18 above the tile tile_13 with color white using the robot robot3, alter the color of the robot robot3 from color white to color black, move the robot robot2 from tile tile_4 to tile tile_9 upwards, move the robot robot1 from tile tile_2 to tile tile_7 upwards, move robot robot3 from tile tile_13 to the left tile tile tile_12, move robot robot3 from tile tile_12 to the left tile tile tile_11, move robot robot1 down from tile tile_7 to tile tile_2, move robot robot3 down from tile tile_11 to tile tile_6, paint the tile tile_11 above the tile tile_6 with color black using the robot robot3, move robot robot3 down from tile tile_6 to tile tile_1, alter the color of the robot robot3 from color black to color white, paint the tile tile_6 above the tile tile_1 with color white using the robot robot3, move robot robot2 down from tile tile_9 to tile tile_4, move the robot robot1 from tile tile_2 to the right tile tile_3, move the robot robot3 from tile tile_1 to the right tile tile_2, alter the color of the robot robot3 from color white to color black, move the robot robot1 from tile tile_3 to tile tile_8 upwards, move the robot robot1 from tile tile_8 to the right tile tile_9, move the robot robot1 from tile tile_9 to tile tile_14 upwards, alter the color of the robot robot1 from color white to color black, paint the tile tile_19 above the tile tile_14 with color black using the robot robot1, move robot robot1 down from tile tile_14 to tile tile_9, move robot robot1 from tile tile_9 to the left tile tile tile_8, paint the tile tile_13 above the tile tile_8 with color black using the robot robot1, alter the color of the robot robot1 from color black to color white, move the robot robot1 from tile tile_8 to the right tile tile_9, paint the tile tile_14 above the tile tile_9 with color white using the robot robot1, move robot robot1 from tile tile_9 to the left tile tile tile_8, paint the tile tile_9 above the tile tile_4 with color black using the robot robot2, move robot robot1 down from tile tile_8 to tile tile_3, paint the tile tile_8 above the tile tile_3 with color white using the robot robot1, move the robot robot3 from tile tile_2 to tile tile_7 upwards, move the robot robot3 from tile tile_7 to tile tile_12 upwards, paint the tile tile_17 above the tile tile_12 with color black using the robot robot3, alter the color of the robot robot3 from color black to color white, alter the color of the robot robot1 from color white to color black, move robot robot1 from tile tile_3 to the left tile tile tile_2, move robot robot3 down from tile tile_12 to tile tile_7, move the robot robot3 from tile tile_7 to tile tile_12 upwards, move robot robot3 down from tile tile_12 to tile tile_7, move robot robot2 from tile tile_4 to the left tile tile tile_3, move the robot robot2 from tile tile_3 to the right tile tile_4, move robot robot2 from tile tile_4 to the left tile tile tile_3, move the robot robot2 from tile tile_3 to the right tile tile_4, move robot robot2 from tile tile_4 to the left tile tile tile_3, move the robot robot2 from tile tile_3 to the right tile tile_4, move robot robot2 from tile tile_4 to the left tile tile tile_3, move the robot robot2 from tile tile_3 to the right tile tile_4, move robot robot2 from tile tile_4 to the left tile tile tile_3, move the robot robot2 from tile tile_3 to the right tile tile_4, move robot robot2 from tile tile_4 to the left tile tile tile_3, move the robot robot2 from tile tile_3 to the right tile tile_4, move robot robot2 from tile tile_4 to the left tile tile tile_3, move the robot robot2 from tile tile_3 to the right tile tile_4, move robot robot2 from tile tile_4 to the left tile tile tile_3, move the robot robot2 from tile tile_3 to the right tile tile_4, move robot robot2 from tile tile_4 to the left tile tile tile_3, move the robot robot2 from tile tile_3 to the right tile tile_4, move robot robot2 from tile tile_4 to the left tile tile tile_3, move the robot robot2 from tile tile_3 to the right tile tile_4, move robot robot2 from tile tile_4 to the left tile tile tile_3, move the robot robot2 from tile tile_3 to the right tile tile_4, move robot robot2 from tile tile_4 to the left tile tile tile_3, move the robot robot2 from tile tile_3 to the right tile tile_4, move robot robot2 from tile tile_4 to the left tile tile tile_3, move the robot robot2 from tile tile_3 to the right tile tile_4, move robot robot2 from tile tile_4 to the left tile tile tile_3, paint the tile tile_12 above the tile tile_7 with color white using the robot robot3, move robot robot1 from tile tile_2 to the left tile tile tile_1, move robot robot3 down from tile tile_7 to tile tile_2, alter the color of the robot robot3 from color white to color black, paint the tile tile_7 above the tile tile_2 with color black using the robot robot3\"; can the following action be removed from this plan and still have a valid plan: move robot robot2 from tile tile_4 to the left tile tile tile_3?", "answer": "yes"}
{"id": -7864826938362465668, "group": "action_justification_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 3 robots. The tiles locations are: tile_19 is to the right of tile_18, tile_10 is to the right of tile_9, tile_3 is to the right of tile_2, tile_15 is to the right of tile_14, tile_4 is to the right of tile_3, tile_9 is to the right of tile_8, tile_20 is to the right of tile_19, tile_18 is to the right of tile_17, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, tile_7 is to the right of tile_6, tile_2 is to the right of tile_1, tile_13 is to the right of tile_12, tile_8 is to the right of tile_7, tile_12 is to the right of tile_11, and tile_14 is to the right of tile_13. Further, tile_8 is down from tile_13, tile_3 is down from tile_8, tile_12 is down from tile_17, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_13 is down from tile_18, tile_9 is down from tile_14, tile_1 is down from tile_6, tile_15 is down from tile_20, tile_11 is down from tile_16, tile_5 is down from tile_10, tile_6 is down from tile_11, tile_10 is down from tile_15, tile_7 is down from tile_12, and tile_14 is down from tile_19 Currently, robot robot3 is at tile_20 and holding color white, robot robot1 is at tile_17 and holding color white, and robot robot2 is at tile_13 and holding color black; tile_14, tile_5, tile_11, tile_18, tile_3, tile_6, tile_1, tile_19, tile_12, tile_10, tile_15, tile_4, tile_7, tile_9, tile_8, tile_2, and tile_16 are clear. The goal is to reach a state where the following facts hold: Tile tile_11 is painted in black color, Tile tile_20 is painted in white color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_6 is painted in white color, Tile tile_19 is painted in black color, Tile tile_18 is painted in white color, Tile tile_16 is painted in white color, Tile tile_9 is painted in black color, Tile tile_14 is painted in white color, Tile tile_7 is painted in black color, Tile tile_15 is painted in black color, Tile tile_13 is painted in black color, Tile tile_17 is painted in black color, and Tile tile_8 is painted in white color.", "question": "Given the plan: \"move robot robot1 from tile tile_17 to the left tile tile tile_16, move the robot robot2 from the tile tile_13 to the tile tile_8 going downwards, move the robot robot3 from the tile tile_20 to the tile tile_15 going downwards, move the robot robot1 from the tile tile_16 to the tile tile_11 going downwards, apply color white to tile tile_16 above tile tile_11 using robot robot1, apply color white to tile tile_20 above tile tile_15 using robot robot3, move the robot robot2 from the tile tile_8 to the tile tile_3 going downwards, move the robot robot1 from the tile tile_11 to the tile tile_6 going downwards, move the robot robot1 from the tile tile_6 to the tile tile_1 going downwards, move the robot robot1 from tile tile_1 to the right tile tile_2, move robot robot3 from tile tile_15 to the left tile tile tile_14, move the robot robot2 from tile tile_3 to the right tile tile_4, move the robot robot3 from the tile tile_14 to the tile tile_9 going downwards, move the robot robot2 from tile tile_4 to the right tile tile_5, move the robot robot2 up from tile tile_5 to tile tile_10, apply color black to tile tile_15 above tile tile_10 using robot robot2, modify the color of the robot robot2 from black to white, move the robot robot2 from the tile tile_10 to the tile tile_5 going downwards, apply color white to tile tile_10 above tile tile_5 using robot robot2, move robot robot2 from tile tile_5 to the left tile tile tile_4, modify the color of the robot robot2 from white to black, move the robot robot3 up from tile tile_9 to tile tile_14, move robot robot3 from tile tile_14 to the left tile tile tile_13, apply color white to tile tile_18 above tile tile_13 using robot robot3, modify the color of the robot robot3 from white to black, move the robot robot2 up from tile tile_4 to tile tile_9, move the robot robot1 up from tile tile_2 to tile tile_7, move robot robot3 from tile tile_13 to the left tile tile tile_12, move robot robot3 from tile tile_12 to the left tile tile tile_11, move the robot robot1 from the tile tile_7 to the tile tile_2 going downwards, move the robot robot3 from the tile tile_11 to the tile tile_6 going downwards, apply color black to tile tile_11 above tile tile_6 using robot robot3, move the robot robot3 from the tile tile_6 to the tile tile_1 going downwards, modify the color of the robot robot3 from black to white, apply color white to tile tile_6 above tile tile_1 using robot robot3, move the robot robot2 from the tile tile_9 to the tile tile_4 going downwards, move the robot robot1 from tile tile_2 to the right tile tile_3, move the robot robot3 from tile tile_1 to the right tile tile_2, modify the color of the robot robot3 from white to black, move the robot robot1 up from tile tile_3 to tile tile_8, move the robot robot1 from tile tile_8 to the right tile tile_9, move the robot robot1 up from tile tile_9 to tile tile_14, modify the color of the robot robot1 from white to black, apply color black to tile tile_19 above tile tile_14 using robot robot1, move the robot robot1 from the tile tile_14 to the tile tile_9 going downwards, move robot robot1 from tile tile_9 to the left tile tile tile_8, apply color black to tile tile_13 above tile tile_8 using robot robot1, modify the color of the robot robot1 from black to white, move the robot robot1 from tile tile_8 to the right tile tile_9, apply color white to tile tile_14 above tile tile_9 using robot robot1, move robot robot1 from tile tile_9 to the left tile tile tile_8, apply color black to tile tile_9 above tile tile_4 using robot robot2, move the robot robot1 from the tile tile_8 to the tile tile_3 going downwards, apply color white to tile tile_8 above tile tile_3 using robot robot1, move the robot robot3 up from tile tile_2 to tile tile_7, move the robot robot3 up from tile tile_7 to tile tile_12, apply color black to tile tile_17 above tile tile_12 using robot robot3, modify the color of the robot robot3 from black to white, modify the color of the robot robot1 from white to black, move robot robot1 from tile tile_3 to the left tile tile tile_2, move the robot robot3 from the tile tile_12 to the tile tile_7 going downwards, move the robot robot3 up from tile tile_7 to tile tile_12, move the robot robot3 from the tile tile_12 to the tile tile_7 going downwards, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, move the robot robot2 from tile tile_4 to the right tile tile_5, apply color white to tile tile_12 above tile tile_7 using robot robot3, move robot robot2 from tile tile_5 to the left tile tile tile_4, move robot robot1 from tile tile_2 to the left tile tile tile_1, move the robot robot3 from the tile tile_7 to the tile tile_2 going downwards, modify the color of the robot robot3 from white to black, apply color black to tile tile_7 above tile tile_2 using robot robot3\"; can the following action be removed from this plan and still have a valid plan: modify the color of the robot robot1 from white to black?", "answer": "yes"}
{"id": -7731598351378542868, "group": "action_justification_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 2 robots. The tiles locations are: tile_19 is to the right of tile_18, tile_10 is to the right of tile_9, tile_3 is to the right of tile_2, tile_15 is to the right of tile_14, tile_4 is to the right of tile_3, tile_9 is to the right of tile_8, tile_20 is to the right of tile_19, tile_18 is to the right of tile_17, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, tile_7 is to the right of tile_6, tile_2 is to the right of tile_1, tile_13 is to the right of tile_12, tile_8 is to the right of tile_7, tile_12 is to the right of tile_11, and tile_14 is to the right of tile_13. Further, tile_8 is down from tile_13, tile_3 is down from tile_8, tile_12 is down from tile_17, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_13 is down from tile_18, tile_9 is down from tile_14, tile_1 is down from tile_6, tile_15 is down from tile_20, tile_11 is down from tile_16, tile_5 is down from tile_10, tile_6 is down from tile_11, tile_10 is down from tile_15, tile_7 is down from tile_12, and tile_14 is down from tile_19 Currently, robot robot2 is at tile_20 and holding color black and robot robot1 is at tile_9 and holding color white; tile_14, tile_5, tile_11, tile_18, tile_17, tile_3, tile_6, tile_1, tile_12, tile_19, tile_10, tile_13, tile_15, tile_4, tile_7, tile_8, tile_2, and tile_16 are clear. The goal is to reach a state where the following facts hold: Tile tile_11 is painted in black color, Tile tile_20 is painted in white color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_6 is painted in white color, Tile tile_19 is painted in black color, Tile tile_18 is painted in white color, Tile tile_16 is painted in white color, Tile tile_9 is painted in black color, Tile tile_14 is painted in white color, Tile tile_7 is painted in black color, Tile tile_15 is painted in black color, Tile tile_13 is painted in black color, Tile tile_17 is painted in black color, and Tile tile_8 is painted in white color.", "question": "Given the plan: \"modify the color of the robot robot2 from black to white, move the robot robot1 from the tile tile_9 to the tile tile_4 going downwards, move the robot robot2 from the tile tile_20 to the tile tile_15 going downwards, move the robot robot1 from the tile tile_4 to the tile on its left tile_3, move the robot robot1 from the tile tile_3 to the tile on its left tile_2, move the robot robot1 from the tile tile_2 to the tile on its left tile_1, move robot robot1 up from tile tile_1 to tile tile_6, move robot robot1 up from tile tile_6 to tile tile_11, paint the tile tile_16 above the tile tile_11 with color white using the robot robot1, paint the tile tile_20 above the tile tile_15 with color white using the robot robot2, navigate robot robot1 from tile tile_11 to tile tile_12 to the right, move the robot robot1 from the tile tile_12 to the tile tile_7 going downwards, navigate robot robot1 from tile tile_7 to tile tile_8 to the right, move the robot robot2 from the tile tile_15 to the tile on its left tile_14, move the robot robot2 from the tile tile_14 to the tile on its left tile_13, paint the tile tile_18 above the tile tile_13 with color white using the robot robot2, modify the color of the robot robot2 from white to black, move the robot robot2 from the tile tile_13 to the tile on its left tile_12, paint the tile tile_17 above the tile tile_12 with color black using the robot robot2, move the robot robot1 from the tile tile_8 to the tile tile_3 going downwards, navigate robot robot1 from tile tile_3 to tile tile_4 to the right, navigate robot robot1 from tile tile_4 to tile tile_5 to the right, move the robot robot2 from the tile tile_12 to the tile tile_7 going downwards, navigate robot robot2 from tile tile_7 to tile tile_8 to the right, paint the tile tile_13 above the tile tile_8 with color black using the robot robot2, move the robot robot2 from the tile tile_8 to the tile tile_3 going downwards, modify the color of the robot robot1 from white to black, move robot robot1 up from tile tile_5 to tile tile_10, paint the tile tile_15 above the tile tile_10 with color black using the robot robot1, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move the robot robot1 from the tile tile_10 to the tile tile_5 going downwards, modify the color of the robot robot1 from black to white, paint the tile tile_10 above the tile tile_5 with color white using the robot robot1, move the robot robot1 from the tile tile_5 to the tile on its left tile_4, move the robot robot1 from the tile tile_4 to the tile on its left tile_3, paint the tile tile_8 above the tile tile_3 with color white using the robot robot1, navigate robot robot1 from tile tile_3 to tile tile_4 to the right, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, move robot robot2 up from tile tile_1 to tile tile_6, paint the tile tile_11 above the tile tile_6 with color black using the robot robot2, move the robot robot2 from the tile tile_6 to the tile tile_1 going downwards, modify the color of the robot robot2 from black to white, paint the tile tile_6 above the tile tile_1 with color white using the robot robot2, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, move robot robot2 up from tile tile_2 to tile tile_7, paint the tile tile_12 above the tile tile_7 with color white using the robot robot2, modify the color of the robot robot2 from white to black, modify the color of the robot robot1 from white to black, move the robot robot2 from the tile tile_7 to the tile tile_2 going downwards, paint the tile tile_7 above the tile tile_2 with color black using the robot robot2, move robot robot1 up from tile tile_4 to tile tile_9, move robot robot1 up from tile tile_9 to tile tile_14, paint the tile tile_19 above the tile tile_14 with color black using the robot robot1, move the robot robot1 from the tile tile_14 to the tile tile_9 going downwards, move the robot robot1 from the tile tile_9 to the tile tile_4 going downwards, modify the color of the robot robot1 from black to white, navigate robot robot2 from tile tile_2 to tile tile_3 to the right, move robot robot1 up from tile tile_4 to tile tile_9, paint the tile tile_14 above the tile tile_9 with color white using the robot robot1, modify the color of the robot robot1 from white to black, move the robot robot1 from the tile tile_9 to the tile tile_4 going downwards, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, paint the tile tile_9 above the tile tile_4 with color black using the robot robot1, move the robot robot2 from the tile tile_2 to the tile on its left tile_1\"; can the following action be removed from this plan and still have a valid plan: move the robot robot2 from the tile tile_2 to the tile on its left tile_1?", "answer": "yes"}
{"id": 2823843415990556348, "group": "action_justification_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_5 is to the right of tile_4, tile_2 is to the right of tile_1, tile_8 is to the right of tile_7, tile_12 is to the right of tile_11, and tile_11 is to the right of tile_10. Further, tile_7 is down from tile_10, tile_8 is down from tile_11, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_5 is down from tile_8, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_9 is down from tile_12, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_7 and holding color black and robot robot1 is at tile_8 and holding color white; tile_9, tile_3, tile_2, tile_5, tile_11, tile_12, tile_6, tile_1, tile_4, and tile_10 are clear. The goal is to reach a state where the following facts hold: Tile tile_11 is painted in black color, Tile tile_5 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, and Tile tile_8 is painted in white color.", "question": "Given the plan: \"move robot robot2 down from tile tile_7 to tile tile_4, move robot robot1 from tile tile_8 to the left tile tile tile_7, paint the tile tile_10 above the tile tile_7 with color white using the robot robot1, move robot robot1 from tile tile_7 to the right tile tile tile_8, move robot robot2 down from tile tile_4 to tile tile_1, move robot robot1 from tile tile_8 to the right tile tile tile_9, paint the tile tile_12 above the tile tile_9 with color white using the robot robot1, move robot robot1 down from tile tile_9 to tile tile_6, move robot robot1 down from tile tile_6 to tile tile_3, move robot robot2 from tile tile_1 to the right tile tile tile_2, move the robot robot2 up from tile tile_2 to tile tile_5, move the robot robot2 up from tile tile_5 to tile tile_8, paint the tile tile_11 above the tile tile_8 with color black using the robot robot2, move robot robot2 from tile tile_8 to the left tile tile tile_7, move robot robot2 down from tile tile_7 to tile tile_4, paint the tile tile_7 above the tile tile_4 with color black using the robot robot2, change the color of robot robot2 from color black to color white, move robot robot2 down from tile tile_4 to tile tile_1, paint the tile tile_4 above the tile tile_1 with color white using the robot robot2, move robot robot2 from tile tile_1 to the right tile tile tile_2, change the color of robot robot2 from color white to color black, move the robot robot1 up from tile tile_3 to tile tile_6, change the color of robot robot1 from color white to color black, paint the tile tile_9 above the tile tile_6 with color black using the robot robot1, change the color of robot robot1 from color black to color white, move robot robot1 from tile tile_6 to the left tile tile tile_5, paint the tile tile_8 above the tile tile_5 with color white using the robot robot1, move robot robot1 from tile tile_5 to the right tile tile tile_6, move robot robot1 down from tile tile_6 to tile tile_3, paint the tile tile_6 above the tile tile_3 with color white using the robot robot1, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, change the color of robot robot1 from color white to color black, change the color of robot robot1 from color black to color white, paint the tile tile_5 above the tile tile_2 with color black using the robot robot2, change the color of robot robot1 from color white to color black\"; can the following action be removed from this plan and still have a valid plan: change the color of robot robot1 from color white to color black?", "answer": "yes"}
{"id": -7927295032897152526, "group": "action_justification_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_5 is to the right of tile_4, tile_2 is to the right of tile_1, tile_8 is to the right of tile_7, tile_12 is to the right of tile_11, and tile_11 is to the right of tile_10. Further, tile_7 is down from tile_10, tile_8 is down from tile_11, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_5 is down from tile_8, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_9 is down from tile_12, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_7 and holding color black and robot robot1 is at tile_8 and holding color white; tile_9, tile_3, tile_2, tile_5, tile_11, tile_12, tile_6, tile_1, tile_4, and tile_10 are clear. The goal is to reach a state where the following facts hold: Tile tile_11 is painted in black color, Tile tile_5 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, and Tile tile_8 is painted in white color.", "question": "Given the plan: \"move the robot robot2 from the tile tile_7 to the tile tile_4 going downwards, navigate robot robot1 from tile tile_8 to tile tile_7 to its left, apply color white to tile tile_10 above tile tile_7 using robot robot1, move the robot robot1 from the tile tile_7 to the tile tile_8 which is to the right of the tile tile_7, move the robot robot2 from the tile tile_4 to the tile tile_1 going downwards, move the robot robot1 from the tile tile_8 to the tile tile_9 which is to the right of the tile tile_8, apply color white to tile tile_12 above tile tile_9 using robot robot1, move the robot robot1 from the tile tile_9 to the tile tile_6 going downwards, move the robot robot1 from the tile tile_6 to the tile tile_3 going downwards, move the robot robot2 from the tile tile_1 to the tile tile_2 which is to the right of the tile tile_1, move the robot robot2 up from tile tile_2 to tile tile_5, move the robot robot2 up from tile tile_5 to tile tile_8, apply color black to tile tile_11 above tile tile_8 using robot robot2, navigate robot robot2 from tile tile_8 to tile tile_7 to its left, move the robot robot2 from the tile tile_7 to the tile tile_4 going downwards, apply color black to tile tile_7 above tile tile_4 using robot robot2, modify the color of the robot robot2 from black to white, move the robot robot2 from the tile tile_4 to the tile tile_1 going downwards, apply color white to tile tile_4 above tile tile_1 using robot robot2, move the robot robot2 from the tile tile_1 to the tile tile_2 which is to the right of the tile tile_1, move the robot robot2 up from tile tile_2 to tile tile_5, apply color white to tile tile_8 above tile tile_5 using robot robot2, modify the color of the robot robot2 from white to black, move the robot robot2 from the tile tile_5 to the tile tile_6 which is to the right of the tile tile_5, apply color black to tile tile_9 above tile tile_6 using robot robot2, navigate robot robot2 from tile tile_6 to tile tile_5 to its left, move the robot robot2 from the tile tile_5 to the tile tile_2 going downwards, apply color black to tile tile_5 above tile tile_2 using robot robot2, modify the color of the robot robot2 from black to white, modify the color of the robot robot2 from white to black, modify the color of the robot robot2 from black to white, modify the color of the robot robot2 from white to black, modify the color of the robot robot2 from black to white, modify the color of the robot robot2 from white to black, modify the color of the robot robot2 from black to white, modify the color of the robot robot2 from white to black, modify the color of the robot robot2 from black to white, modify the color of the robot robot2 from white to black, modify the color of the robot robot2 from black to white, modify the color of the robot robot2 from white to black, modify the color of the robot robot2 from black to white, modify the color of the robot robot2 from white to black, modify the color of the robot robot2 from black to white, modify the color of the robot robot2 from white to black, modify the color of the robot robot2 from black to white, modify the color of the robot robot2 from white to black, modify the color of the robot robot2 from black to white, modify the color of the robot robot2 from white to black, modify the color of the robot robot2 from black to white, modify the color of the robot robot2 from white to black, modify the color of the robot robot2 from black to white, modify the color of the robot robot2 from white to black, modify the color of the robot robot2 from black to white, modify the color of the robot robot2 from white to black, apply color white to tile tile_6 above tile tile_3 using robot robot1, modify the color of the robot robot2 from black to white\"; can the following action be removed from this plan and still have a valid plan: modify the color of the robot robot2 from black to white?", "answer": "yes"}
{"id": 6184196112576997450, "group": "action_justification_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 2 robots. The tiles locations are: tile_19 is to the right of tile_18, tile_10 is to the right of tile_9, tile_3 is to the right of tile_2, tile_15 is to the right of tile_14, tile_4 is to the right of tile_3, tile_9 is to the right of tile_8, tile_20 is to the right of tile_19, tile_18 is to the right of tile_17, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, tile_7 is to the right of tile_6, tile_2 is to the right of tile_1, tile_13 is to the right of tile_12, tile_8 is to the right of tile_7, tile_12 is to the right of tile_11, and tile_14 is to the right of tile_13. Further, tile_8 is down from tile_13, tile_3 is down from tile_8, tile_12 is down from tile_17, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_13 is down from tile_18, tile_9 is down from tile_14, tile_1 is down from tile_6, tile_15 is down from tile_20, tile_11 is down from tile_16, tile_5 is down from tile_10, tile_6 is down from tile_11, tile_10 is down from tile_15, tile_7 is down from tile_12, and tile_14 is down from tile_19 Currently, robot robot2 is at tile_20 and holding color black and robot robot1 is at tile_9 and holding color white; tile_14, tile_5, tile_11, tile_18, tile_17, tile_3, tile_6, tile_1, tile_12, tile_19, tile_10, tile_13, tile_15, tile_4, tile_7, tile_8, tile_2, and tile_16 are clear. The goal is to reach a state where the following facts hold: Tile tile_11 is painted in black color, Tile tile_20 is painted in white color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_6 is painted in white color, Tile tile_19 is painted in black color, Tile tile_18 is painted in white color, Tile tile_16 is painted in white color, Tile tile_9 is painted in black color, Tile tile_14 is painted in white color, Tile tile_7 is painted in black color, Tile tile_15 is painted in black color, Tile tile_13 is painted in black color, Tile tile_17 is painted in black color, and Tile tile_8 is painted in white color.", "question": "Given the plan: \"change the color of robot robot2 from color black to color white, move robot robot1 down from tile tile_9 to tile tile_4, move robot robot2 down from tile tile_20 to tile tile_15, move the robot robot1 from the tile tile_4 to the tile on its left tile_3, move the robot robot1 from the tile tile_3 to the tile on its left tile_2, move the robot robot1 from the tile tile_2 to the tile on its left tile_1, move the robot robot1 up from tile tile_1 to tile tile_6, move the robot robot1 up from tile tile_6 to tile tile_11, paint tile tile_16 above tile tile_11 with color white using robot robot1, paint tile tile_20 above tile tile_15 with color white using robot robot2, move the robot robot1 from tile tile_11 to the right tile tile_12, move robot robot1 down from tile tile_12 to tile tile_7, move the robot robot1 from tile tile_7 to the right tile tile_8, move the robot robot2 from the tile tile_15 to the tile on its left tile_14, move the robot robot2 from the tile tile_14 to the tile on its left tile_13, paint tile tile_18 above tile tile_13 with color white using robot robot2, change the color of robot robot2 from color white to color black, move the robot robot2 from the tile tile_13 to the tile on its left tile_12, paint tile tile_17 above tile tile_12 with color black using robot robot2, move robot robot1 down from tile tile_8 to tile tile_3, move the robot robot1 from tile tile_3 to the right tile tile_4, move the robot robot1 from tile tile_4 to the right tile tile_5, move robot robot2 down from tile tile_12 to tile tile_7, move the robot robot2 from tile tile_7 to the right tile tile_8, paint tile tile_13 above tile tile_8 with color black using robot robot2, move robot robot2 down from tile tile_8 to tile tile_3, change the color of robot robot1 from color white to color black, move the robot robot1 up from tile tile_5 to tile tile_10, paint tile tile_15 above tile tile_10 with color black using robot robot1, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move robot robot1 down from tile tile_10 to tile tile_5, change the color of robot robot1 from color black to color white, paint tile tile_10 above tile tile_5 with color white using robot robot1, move the robot robot1 from the tile tile_5 to the tile on its left tile_4, move the robot robot1 from the tile tile_4 to the tile on its left tile_3, paint tile tile_8 above tile tile_3 with color white using robot robot1, move the robot robot1 from tile tile_3 to the right tile tile_4, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, move the robot robot2 up from tile tile_1 to tile tile_6, paint tile tile_11 above tile tile_6 with color black using robot robot2, move robot robot2 down from tile tile_6 to tile tile_1, change the color of robot robot2 from color black to color white, paint tile tile_6 above tile tile_1 with color white using robot robot2, move the robot robot2 from tile tile_1 to the right tile tile_2, move the robot robot2 up from tile tile_2 to tile tile_7, paint tile tile_12 above tile tile_7 with color white using robot robot2, change the color of robot robot2 from color white to color black, change the color of robot robot1 from color white to color black, move robot robot2 down from tile tile_7 to tile tile_2, paint tile tile_7 above tile tile_2 with color black using robot robot2, move the robot robot1 up from tile tile_4 to tile tile_9, move the robot robot1 up from tile tile_9 to tile tile_14, paint tile tile_19 above tile tile_14 with color black using robot robot1, move robot robot1 down from tile tile_14 to tile tile_9, move robot robot1 down from tile tile_9 to tile tile_4, change the color of robot robot1 from color black to color white, move the robot robot2 from tile tile_2 to the right tile tile_3, move the robot robot1 up from tile tile_4 to tile tile_9, paint tile tile_14 above tile tile_9 with color white using robot robot1, change the color of robot robot1 from color white to color black, move robot robot1 down from tile tile_9 to tile tile_4, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move the robot robot2 from tile tile_2 to the right tile tile_3, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move the robot robot2 from tile tile_2 to the right tile tile_3, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move the robot robot2 from tile tile_2 to the right tile tile_3, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move the robot robot2 from tile tile_2 to the right tile tile_3, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move the robot robot2 from tile tile_2 to the right tile tile_3, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move the robot robot2 from tile tile_2 to the right tile tile_3, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move the robot robot2 from tile tile_2 to the right tile tile_3, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move the robot robot2 from tile tile_2 to the right tile tile_3, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move the robot robot2 from tile tile_2 to the right tile tile_3, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move the robot robot2 from tile tile_2 to the right tile tile_3, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move the robot robot2 from tile tile_2 to the right tile tile_3, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move the robot robot2 from tile tile_2 to the right tile tile_3, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move the robot robot2 from tile tile_2 to the right tile tile_3, paint tile tile_9 above tile tile_4 with color black using robot robot1, move the robot robot1 from tile tile_4 to the right tile tile_5\"; can the following action be removed from this plan and still have a valid plan: move the robot robot2 from tile tile_2 to the right tile tile_3?", "answer": "no"}
{"id": 3600949274434931234, "group": "action_justification_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 3 robots. The tiles locations are: tile_19 is to the right of tile_18, tile_10 is to the right of tile_9, tile_3 is to the right of tile_2, tile_15 is to the right of tile_14, tile_4 is to the right of tile_3, tile_9 is to the right of tile_8, tile_20 is to the right of tile_19, tile_18 is to the right of tile_17, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, tile_7 is to the right of tile_6, tile_2 is to the right of tile_1, tile_13 is to the right of tile_12, tile_8 is to the right of tile_7, tile_12 is to the right of tile_11, and tile_14 is to the right of tile_13. Further, tile_8 is down from tile_13, tile_3 is down from tile_8, tile_12 is down from tile_17, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_13 is down from tile_18, tile_9 is down from tile_14, tile_1 is down from tile_6, tile_15 is down from tile_20, tile_11 is down from tile_16, tile_5 is down from tile_10, tile_6 is down from tile_11, tile_10 is down from tile_15, tile_7 is down from tile_12, and tile_14 is down from tile_19 Currently, robot robot3 is at tile_20 and holding color white, robot robot1 is at tile_17 and holding color white, and robot robot2 is at tile_13 and holding color black; tile_14, tile_5, tile_11, tile_18, tile_3, tile_6, tile_1, tile_19, tile_12, tile_10, tile_15, tile_4, tile_7, tile_9, tile_8, tile_2, and tile_16 are clear. The goal is to reach a state where the following facts hold: Tile tile_11 is painted in black color, Tile tile_20 is painted in white color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_6 is painted in white color, Tile tile_19 is painted in black color, Tile tile_18 is painted in white color, Tile tile_16 is painted in white color, Tile tile_9 is painted in black color, Tile tile_14 is painted in white color, Tile tile_7 is painted in black color, Tile tile_15 is painted in black color, Tile tile_13 is painted in black color, Tile tile_17 is painted in black color, and Tile tile_8 is painted in white color.", "question": "Given the plan: \"move the robot robot1 from the tile tile_17 to the tile on its left tile_16, move robot robot2 down from tile tile_13 to tile tile_8, move robot robot3 down from tile tile_20 to tile tile_15, move robot robot1 down from tile tile_16 to tile tile_11, apply color white to tile tile_16 above tile tile_11 using robot robot1, apply color white to tile tile_20 above tile tile_15 using robot robot3, move robot robot2 down from tile tile_8 to tile tile_3, move robot robot1 down from tile tile_11 to tile tile_6, move robot robot1 down from tile tile_6 to tile tile_1, move robot robot1 from tile tile_1 to the right tile tile tile_2, move the robot robot3 from the tile tile_15 to the tile on its left tile_14, move robot robot2 from tile tile_3 to the right tile tile tile_4, move robot robot3 down from tile tile_14 to tile tile_9, move robot robot2 from tile tile_4 to the right tile tile tile_5, move the robot robot2 up from tile tile_5 to tile tile_10, apply color black to tile tile_15 above tile tile_10 using robot robot2, change the color of robot robot2 from color black to color white, move robot robot2 down from tile tile_10 to tile tile_5, apply color white to tile tile_10 above tile tile_5 using robot robot2, move the robot robot2 from the tile tile_5 to the tile on its left tile_4, change the color of robot robot2 from color white to color black, move the robot robot3 up from tile tile_9 to tile tile_14, move the robot robot3 from the tile tile_14 to the tile on its left tile_13, apply color white to tile tile_18 above tile tile_13 using robot robot3, change the color of robot robot3 from color white to color black, move the robot robot2 up from tile tile_4 to tile tile_9, move the robot robot1 up from tile tile_2 to tile tile_7, move the robot robot3 from the tile tile_13 to the tile on its left tile_12, move the robot robot3 from the tile tile_12 to the tile on its left tile_11, move robot robot1 down from tile tile_7 to tile tile_2, move robot robot3 down from tile tile_11 to tile tile_6, apply color black to tile tile_11 above tile tile_6 using robot robot3, move robot robot3 down from tile tile_6 to tile tile_1, change the color of robot robot3 from color black to color white, apply color white to tile tile_6 above tile tile_1 using robot robot3, move robot robot2 down from tile tile_9 to tile tile_4, move robot robot1 from tile tile_2 to the right tile tile tile_3, move robot robot3 from tile tile_1 to the right tile tile tile_2, change the color of robot robot3 from color white to color black, move the robot robot1 up from tile tile_3 to tile tile_8, move robot robot1 from tile tile_8 to the right tile tile tile_9, move the robot robot1 up from tile tile_9 to tile tile_14, change the color of robot robot1 from color white to color black, apply color black to tile tile_19 above tile tile_14 using robot robot1, move robot robot1 down from tile tile_14 to tile tile_9, move the robot robot1 from the tile tile_9 to the tile on its left tile_8, apply color black to tile tile_13 above tile tile_8 using robot robot1, change the color of robot robot1 from color black to color white, move robot robot1 from tile tile_8 to the right tile tile tile_9, apply color white to tile tile_14 above tile tile_9 using robot robot1, move the robot robot1 from the tile tile_9 to the tile on its left tile_8, apply color black to tile tile_9 above tile tile_4 using robot robot2, move robot robot1 down from tile tile_8 to tile tile_3, apply color white to tile tile_8 above tile tile_3 using robot robot1, move the robot robot3 up from tile tile_2 to tile tile_7, move the robot robot3 up from tile tile_7 to tile tile_12, apply color black to tile tile_17 above tile tile_12 using robot robot3, change the color of robot robot3 from color black to color white, change the color of robot robot1 from color white to color black, move the robot robot1 from the tile tile_3 to the tile on its left tile_2, move robot robot3 down from tile tile_12 to tile tile_7, move the robot robot3 up from tile tile_7 to tile tile_12, move robot robot3 down from tile tile_12 to tile tile_7, change the color of robot robot2 from color black to color white, change the color of robot robot2 from color white to color black, change the color of robot robot2 from color black to color white, change the color of robot robot2 from color white to color black, change the color of robot robot2 from color black to color white, change the color of robot robot2 from color white to color black, change the color of robot robot2 from color black to color white, change the color of robot robot2 from color white to color black, change the color of robot robot2 from color black to color white, change the color of robot robot2 from color white to color black, change the color of robot robot2 from color black to color white, change the color of robot robot2 from color white to color black, move the robot robot2 from the tile tile_4 to the tile on its left tile_3, change the color of robot robot2 from color black to color white, apply color white to tile tile_12 above tile tile_7 using robot robot3, move the robot robot1 from the tile tile_2 to the tile on its left tile_1, move robot robot3 down from tile tile_7 to tile tile_2, change the color of robot robot3 from color white to color black, apply color black to tile tile_7 above tile tile_2 using robot robot3\"; can the following action be removed from this plan and still have a valid plan: change the color of robot robot1 from color white to color black?", "answer": "yes"}
{"id": 7162586187685194246, "group": "action_justification_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 3 robots. The tiles locations are: tile_19 is to the right of tile_18, tile_10 is to the right of tile_9, tile_3 is to the right of tile_2, tile_15 is to the right of tile_14, tile_4 is to the right of tile_3, tile_9 is to the right of tile_8, tile_20 is to the right of tile_19, tile_18 is to the right of tile_17, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, tile_7 is to the right of tile_6, tile_2 is to the right of tile_1, tile_13 is to the right of tile_12, tile_8 is to the right of tile_7, tile_12 is to the right of tile_11, and tile_14 is to the right of tile_13. Further, tile_8 is down from tile_13, tile_3 is down from tile_8, tile_12 is down from tile_17, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_13 is down from tile_18, tile_9 is down from tile_14, tile_1 is down from tile_6, tile_15 is down from tile_20, tile_11 is down from tile_16, tile_5 is down from tile_10, tile_6 is down from tile_11, tile_10 is down from tile_15, tile_7 is down from tile_12, and tile_14 is down from tile_19 Currently, robot robot3 is at tile_20 and holding color white, robot robot1 is at tile_17 and holding color white, and robot robot2 is at tile_13 and holding color black; tile_14, tile_5, tile_11, tile_18, tile_3, tile_6, tile_1, tile_19, tile_12, tile_10, tile_15, tile_4, tile_7, tile_9, tile_8, tile_2, and tile_16 are clear. The goal is to reach a state where the following facts hold: Tile tile_11 is painted in black color, Tile tile_20 is painted in white color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_6 is painted in white color, Tile tile_19 is painted in black color, Tile tile_18 is painted in white color, Tile tile_16 is painted in white color, Tile tile_9 is painted in black color, Tile tile_14 is painted in white color, Tile tile_7 is painted in black color, Tile tile_15 is painted in black color, Tile tile_13 is painted in black color, Tile tile_17 is painted in black color, and Tile tile_8 is painted in white color.", "question": "Given the plan: \"navigate robot robot1 from tile tile_17 to tile tile_16 to its left, move robot robot2 down from tile tile_13 to tile tile_8, move robot robot3 down from tile tile_20 to tile tile_15, move robot robot1 down from tile tile_16 to tile tile_11, use robot robot1 to paint the tile tile_16 above the tile tile_11 with the color white, use robot robot3 to paint the tile tile_20 above the tile tile_15 with the color white, move robot robot2 down from tile tile_8 to tile tile_3, move robot robot1 down from tile tile_11 to tile tile_6, move robot robot1 down from tile tile_6 to tile tile_1, move the robot robot1 from tile tile_1 to the right tile tile_2, navigate robot robot3 from tile tile_15 to tile tile_14 to its left, move the robot robot2 from tile tile_3 to the right tile tile_4, move robot robot3 down from tile tile_14 to tile tile_9, move the robot robot2 from tile tile_4 to the right tile tile_5, move the robot robot2 from tile tile_5 to tile tile_10 upwards, use robot robot2 to paint the tile tile_15 above the tile tile_10 with the color black, alter the color of the robot robot2 from color black to color white, move robot robot2 down from tile tile_10 to tile tile_5, use robot robot2 to paint the tile tile_10 above the tile tile_5 with the color white, navigate robot robot2 from tile tile_5 to tile tile_4 to its left, alter the color of the robot robot2 from color white to color black, move the robot robot3 from tile tile_9 to tile tile_14 upwards, navigate robot robot3 from tile tile_14 to tile tile_13 to its left, use robot robot3 to paint the tile tile_18 above the tile tile_13 with the color white, alter the color of the robot robot3 from color white to color black, move the robot robot2 from tile tile_4 to tile tile_9 upwards, move the robot robot1 from tile tile_2 to tile tile_7 upwards, navigate robot robot3 from tile tile_13 to tile tile_12 to its left, navigate robot robot3 from tile tile_12 to tile tile_11 to its left, move robot robot1 down from tile tile_7 to tile tile_2, move robot robot3 down from tile tile_11 to tile tile_6, use robot robot3 to paint the tile tile_11 above the tile tile_6 with the color black, move robot robot3 down from tile tile_6 to tile tile_1, alter the color of the robot robot3 from color black to color white, use robot robot3 to paint the tile tile_6 above the tile tile_1 with the color white, move robot robot2 down from tile tile_9 to tile tile_4, move the robot robot1 from tile tile_2 to the right tile tile_3, move the robot robot3 from tile tile_1 to the right tile tile_2, alter the color of the robot robot3 from color white to color black, move the robot robot1 from tile tile_3 to tile tile_8 upwards, move the robot robot1 from tile tile_8 to the right tile tile_9, move the robot robot1 from tile tile_9 to tile tile_14 upwards, alter the color of the robot robot1 from color white to color black, use robot robot1 to paint the tile tile_19 above the tile tile_14 with the color black, move robot robot1 down from tile tile_14 to tile tile_9, navigate robot robot1 from tile tile_9 to tile tile_8 to its left, use robot robot1 to paint the tile tile_13 above the tile tile_8 with the color black, alter the color of the robot robot1 from color black to color white, move the robot robot1 from tile tile_8 to the right tile tile_9, use robot robot1 to paint the tile tile_14 above the tile tile_9 with the color white, navigate robot robot1 from tile tile_9 to tile tile_8 to its left, use robot robot2 to paint the tile tile_9 above the tile tile_4 with the color black, move robot robot1 down from tile tile_8 to tile tile_3, use robot robot1 to paint the tile tile_8 above the tile tile_3 with the color white, move the robot robot3 from tile tile_2 to tile tile_7 upwards, move the robot robot3 from tile tile_7 to tile tile_12 upwards, use robot robot3 to paint the tile tile_17 above the tile tile_12 with the color black, alter the color of the robot robot3 from color black to color white, alter the color of the robot robot1 from color white to color black, navigate robot robot1 from tile tile_3 to tile tile_2 to its left, move robot robot3 down from tile tile_12 to tile tile_7, move the robot robot3 from tile tile_7 to tile tile_12 upwards, move robot robot3 down from tile tile_12 to tile tile_7, navigate robot robot2 from tile tile_4 to tile tile_3 to its left, move the robot robot2 from tile tile_3 to the right tile tile_4, navigate robot robot2 from tile tile_4 to tile tile_3 to its left, move the robot robot2 from tile tile_3 to the right tile tile_4, navigate robot robot2 from tile tile_4 to tile tile_3 to its left, move the robot robot2 from tile tile_3 to the right tile tile_4, navigate robot robot2 from tile tile_4 to tile tile_3 to its left, move the robot robot2 from tile tile_3 to the right tile tile_4, navigate robot robot2 from tile tile_4 to tile tile_3 to its left, move the robot robot2 from tile tile_3 to the right tile tile_4, navigate robot robot2 from tile tile_4 to tile tile_3 to its left, move the robot robot2 from tile tile_3 to the right tile tile_4, navigate robot robot2 from tile tile_4 to tile tile_3 to its left, move the robot robot2 from tile tile_3 to the right tile tile_4, navigate robot robot2 from tile tile_4 to tile tile_3 to its left, move the robot robot2 from tile tile_3 to the right tile tile_4, navigate robot robot2 from tile tile_4 to tile tile_3 to its left, move the robot robot2 from tile tile_3 to the right tile tile_4, navigate robot robot2 from tile tile_4 to tile tile_3 to its left, move the robot robot2 from tile tile_3 to the right tile tile_4, navigate robot robot2 from tile tile_4 to tile tile_3 to its left, move the robot robot2 from tile tile_3 to the right tile tile_4, navigate robot robot2 from tile tile_4 to tile tile_3 to its left, move the robot robot2 from tile tile_3 to the right tile tile_4, navigate robot robot2 from tile tile_4 to tile tile_3 to its left, move the robot robot2 from tile tile_3 to the right tile tile_4, navigate robot robot2 from tile tile_4 to tile tile_3 to its left, move the robot robot2 from tile tile_3 to the right tile tile_4, navigate robot robot2 from tile tile_4 to tile tile_3 to its left, use robot robot3 to paint the tile tile_12 above the tile tile_7 with the color white, move the robot robot2 from tile tile_3 to the right tile tile_4, navigate robot robot1 from tile tile_2 to tile tile_1 to its left, move robot robot3 down from tile tile_7 to tile tile_2, alter the color of the robot robot3 from color white to color black, use robot robot3 to paint the tile tile_7 above the tile tile_2 with the color black\"; can the following action be removed from this plan and still have a valid plan: move the robot robot2 from tile tile_3 to the right tile tile_4?", "answer": "yes"}
{"id": -3532041470494770014, "group": "action_justification_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 3 robots. The tiles locations are: tile_19 is to the right of tile_18, tile_10 is to the right of tile_9, tile_3 is to the right of tile_2, tile_15 is to the right of tile_14, tile_4 is to the right of tile_3, tile_9 is to the right of tile_8, tile_20 is to the right of tile_19, tile_18 is to the right of tile_17, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, tile_7 is to the right of tile_6, tile_2 is to the right of tile_1, tile_13 is to the right of tile_12, tile_8 is to the right of tile_7, tile_12 is to the right of tile_11, and tile_14 is to the right of tile_13. Further, tile_8 is down from tile_13, tile_3 is down from tile_8, tile_12 is down from tile_17, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_13 is down from tile_18, tile_9 is down from tile_14, tile_1 is down from tile_6, tile_15 is down from tile_20, tile_11 is down from tile_16, tile_5 is down from tile_10, tile_6 is down from tile_11, tile_10 is down from tile_15, tile_7 is down from tile_12, and tile_14 is down from tile_19 Currently, robot robot3 is at tile_20 and holding color white, robot robot1 is at tile_17 and holding color white, and robot robot2 is at tile_13 and holding color black; tile_14, tile_5, tile_11, tile_18, tile_3, tile_6, tile_1, tile_19, tile_12, tile_10, tile_15, tile_4, tile_7, tile_9, tile_8, tile_2, and tile_16 are clear. The goal is to reach a state where the following facts hold: Tile tile_11 is painted in black color, Tile tile_20 is painted in white color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_6 is painted in white color, Tile tile_19 is painted in black color, Tile tile_18 is painted in white color, Tile tile_16 is painted in white color, Tile tile_9 is painted in black color, Tile tile_14 is painted in white color, Tile tile_7 is painted in black color, Tile tile_15 is painted in black color, Tile tile_13 is painted in black color, Tile tile_17 is painted in black color, and Tile tile_8 is painted in white color.", "question": "Given the plan: \"move the robot robot1 from tile tile_17 to the left tile tile_16, move robot robot2 down from tile tile_13 to tile tile_8, move robot robot3 down from tile tile_20 to tile tile_15, move robot robot1 down from tile tile_16 to tile tile_11, use robot robot1 to paint the tile tile_16 above the tile tile_11 with the color white, use robot robot3 to paint the tile tile_20 above the tile tile_15 with the color white, move robot robot2 down from tile tile_8 to tile tile_3, move robot robot1 down from tile tile_11 to tile tile_6, move robot robot1 down from tile tile_6 to tile tile_1, move robot robot1 from tile tile_1 to the right tile tile tile_2, move the robot robot3 from tile tile_15 to the left tile tile_14, move robot robot2 from tile tile_3 to the right tile tile tile_4, move robot robot3 down from tile tile_14 to tile tile_9, move robot robot2 from tile tile_4 to the right tile tile tile_5, move the robot robot2 up from tile tile_5 to tile tile_10, use robot robot2 to paint the tile tile_15 above the tile tile_10 with the color black, alter the color of the robot robot2 from color black to color white, move robot robot2 down from tile tile_10 to tile tile_5, use robot robot2 to paint the tile tile_10 above the tile tile_5 with the color white, move the robot robot2 from tile tile_5 to the left tile tile_4, alter the color of the robot robot2 from color white to color black, move the robot robot3 up from tile tile_9 to tile tile_14, move the robot robot3 from tile tile_14 to the left tile tile_13, use robot robot3 to paint the tile tile_18 above the tile tile_13 with the color white, alter the color of the robot robot3 from color white to color black, move the robot robot2 up from tile tile_4 to tile tile_9, move the robot robot1 up from tile tile_2 to tile tile_7, move the robot robot3 from tile tile_13 to the left tile tile_12, move the robot robot3 from tile tile_12 to the left tile tile_11, move robot robot1 down from tile tile_7 to tile tile_2, move robot robot3 down from tile tile_11 to tile tile_6, use robot robot3 to paint the tile tile_11 above the tile tile_6 with the color black, move robot robot3 down from tile tile_6 to tile tile_1, alter the color of the robot robot3 from color black to color white, use robot robot3 to paint the tile tile_6 above the tile tile_1 with the color white, move robot robot2 down from tile tile_9 to tile tile_4, move robot robot1 from tile tile_2 to the right tile tile tile_3, move robot robot3 from tile tile_1 to the right tile tile tile_2, alter the color of the robot robot3 from color white to color black, move the robot robot1 up from tile tile_3 to tile tile_8, move robot robot1 from tile tile_8 to the right tile tile tile_9, move the robot robot1 up from tile tile_9 to tile tile_14, alter the color of the robot robot1 from color white to color black, use robot robot1 to paint the tile tile_19 above the tile tile_14 with the color black, move robot robot1 down from tile tile_14 to tile tile_9, move the robot robot1 from tile tile_9 to the left tile tile_8, use robot robot1 to paint the tile tile_13 above the tile tile_8 with the color black, alter the color of the robot robot1 from color black to color white, move robot robot1 from tile tile_8 to the right tile tile tile_9, use robot robot1 to paint the tile tile_14 above the tile tile_9 with the color white, move the robot robot1 from tile tile_9 to the left tile tile_8, use robot robot2 to paint the tile tile_9 above the tile tile_4 with the color black, move robot robot1 down from tile tile_8 to tile tile_3, use robot robot1 to paint the tile tile_8 above the tile tile_3 with the color white, move the robot robot3 up from tile tile_2 to tile tile_7, move the robot robot3 up from tile tile_7 to tile tile_12, use robot robot3 to paint the tile tile_17 above the tile tile_12 with the color black, alter the color of the robot robot3 from color black to color white, alter the color of the robot robot1 from color white to color black, move the robot robot1 from tile tile_3 to the left tile tile_2, move robot robot3 down from tile tile_12 to tile tile_7, move the robot robot3 up from tile tile_7 to tile tile_12, move robot robot3 down from tile tile_12 to tile tile_7, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot2 from color black to color white, use robot robot3 to paint the tile tile_12 above the tile tile_7 with the color white, alter the color of the robot robot2 from color white to color black, move the robot robot1 from tile tile_2 to the left tile tile_1, move robot robot3 down from tile tile_7 to tile tile_2, alter the color of the robot robot3 from color white to color black, use robot robot3 to paint the tile tile_7 above the tile tile_2 with the color black\"; can the following action be removed from this plan and still have a valid plan: alter the color of the robot robot1 from color white to color black?", "answer": "yes"}
{"id": 377715856028767411, "group": "action_justification_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 24 tiles and 2 robots. The tiles locations are: tile_23 is to the right of tile_22, tile_9 is to the right of tile_8, tile_20 is to the right of tile_19, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, tile_10 is to the right of tile_9, tile_15 is to the right of tile_14, tile_4 is to the right of tile_3, tile_24 is to the right of tile_23, tile_16 is to the right of tile_15, tile_18 is to the right of tile_17, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_12 is to the right of tile_11, tile_11 is to the right of tile_10, tile_3 is to the right of tile_2, tile_21 is to the right of tile_20, tile_22 is to the right of tile_21, tile_2 is to the right of tile_1, and tile_14 is to the right of tile_13. Further, tile_17 is down from tile_23, tile_9 is down from tile_15, tile_6 is down from tile_12, tile_18 is down from tile_24, tile_15 is down from tile_21, tile_3 is down from tile_9, tile_4 is down from tile_10, tile_10 is down from tile_16, tile_11 is down from tile_17, tile_1 is down from tile_7, tile_8 is down from tile_14, tile_13 is down from tile_19, tile_14 is down from tile_20, tile_7 is down from tile_13, tile_16 is down from tile_22, tile_5 is down from tile_11, tile_12 is down from tile_18, and tile_2 is down from tile_8 Currently, robot robot1 is at tile_24 and holding color white and robot robot2 is at tile_14 and holding color black; tile_22, tile_5, tile_11, tile_20, tile_18, tile_17, tile_3, tile_6, tile_1, tile_19, tile_12, tile_10, tile_13, tile_15, tile_4, tile_7, tile_23, tile_9, tile_8, tile_2, tile_16, and tile_21 are clear. The goal is to reach a state where the following facts hold: Tile tile_8 is painted in black color, Tile tile_22 is painted in black color, Tile tile_10 is painted in black color, Tile tile_23 is painted in white color, Tile tile_24 is painted in black color, Tile tile_7 is painted in white color, Tile tile_18 is painted in white color, Tile tile_16 is painted in white color, Tile tile_20 is painted in black color, Tile tile_14 is painted in white color, Tile tile_12 is painted in black color, Tile tile_11 is painted in white color, Tile tile_13 is painted in black color, Tile tile_15 is painted in black color, Tile tile_21 is painted in white color, Tile tile_19 is painted in white color, Tile tile_17 is painted in black color, and Tile tile_9 is painted in white color.", "question": "Given the plan: \"paint the tile tile_20 above the tile tile_14 with color black using the robot robot2, move the robot robot1 from the tile tile_24 to the tile tile_18 going downwards, move the robot robot2 from the tile tile_14 to the tile tile_8 going downwards, move the robot robot1 from the tile tile_18 to the tile tile_12 going downwards, move the robot robot1 from the tile tile_12 to the tile tile_6 going downwards, move the robot robot1 from the tile tile_6 to the tile on its left tile_5, move the robot robot1 from the tile tile_5 to the tile on its left tile_4, move the robot robot1 from the tile tile_4 to the tile on its left tile_3, move the robot robot1 from the tile tile_3 to the tile on its left tile_2, move the robot robot1 from the tile tile_2 to the tile on its left tile_1, move the robot robot1 up from tile tile_1 to tile tile_7, move the robot robot2 from tile tile_8 to the right tile tile_9, move the robot robot1 up from tile tile_7 to tile tile_13, paint the tile tile_19 above the tile tile_13 with color white using the robot robot1, move the robot robot1 from the tile tile_13 to the tile tile_7 going downwards, move the robot robot1 from the tile tile_7 to the tile tile_1 going downwards, move the robot robot2 from tile tile_9 to the right tile tile_10, move the robot robot2 up from tile tile_10 to tile tile_16, paint the tile tile_22 above the tile tile_16 with color black using the robot robot2, move the robot robot2 from the tile tile_16 to the tile tile_10 going downwards, move the robot robot2 from tile tile_10 to the right tile tile_11, move the robot robot2 from tile tile_11 to the right tile tile_12, move the robot robot2 from the tile tile_12 to the tile tile_6 going downwards, move the robot robot1 up from tile tile_1 to tile tile_7, move the robot robot2 up from tile tile_6 to tile tile_12, move the robot robot2 up from tile tile_12 to tile tile_18, paint the tile tile_24 above the tile tile_18 with color black using the robot robot2, move the robot robot1 from tile tile_7 to the right tile tile_8, paint the tile tile_14 above the tile tile_8 with color white using the robot robot1, move the robot robot1 from the tile tile_8 to the tile tile_2 going downwards, move the robot robot1 from tile tile_2 to the right tile tile_3, move the robot robot2 from the tile tile_18 to the tile on its left tile_17, move the robot robot2 from the tile tile_17 to the tile on its left tile_16, alter the color of the robot robot2 from color black to color white, move the robot robot2 from the tile tile_16 to the tile on its left tile_15, paint the tile tile_21 above the tile tile_15 with color white using the robot robot2, move the robot robot1 from the tile tile_3 to the tile on its left tile_2, move the robot robot2 from the tile tile_15 to the tile tile_9 going downwards, move the robot robot1 from the tile tile_2 to the tile on its left tile_1, move the robot robot1 up from tile tile_1 to tile tile_7, move the robot robot1 from the tile tile_7 to the tile tile_1 going downwards, move the robot robot2 from tile tile_9 to the right tile tile_10, paint the tile tile_16 above the tile tile_10 with color white using the robot robot2, move the robot robot2 from tile tile_10 to the right tile tile_11, move the robot robot2 from tile tile_11 to the right tile tile_12, paint the tile tile_18 above the tile tile_12 with color white using the robot robot2, move the robot robot2 from the tile tile_12 to the tile tile_6 going downwards, alter the color of the robot robot2 from color white to color black, paint the tile tile_12 above the tile tile_6 with color black using the robot robot2, move the robot robot2 from the tile tile_6 to the tile on its left tile_5, move the robot robot2 from the tile tile_5 to the tile on its left tile_4, paint the tile tile_10 above the tile tile_4 with color black using the robot robot2, move the robot robot2 from the tile tile_4 to the tile on its left tile_3, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move the robot robot2 up from tile tile_2 to tile tile_8, move the robot robot2 from the tile tile_8 to the tile on its left tile_7, paint the tile tile_13 above the tile tile_7 with color black using the robot robot2, move the robot robot2 from tile tile_7 to the right tile tile_8, paint the tile tile_7 above the tile tile_1 with color white using the robot robot1, move the robot robot1 from tile tile_1 to the right tile tile_2, move the robot robot1 from tile tile_2 to the right tile tile_3, move the robot robot2 from the tile tile_8 to the tile tile_2 going downwards, move the robot robot1 from tile tile_3 to the right tile tile_4, move the robot robot1 from tile tile_4 to the right tile tile_5, paint the tile tile_8 above the tile tile_2 with color black using the robot robot2, move the robot robot2 from tile tile_2 to the right tile tile_3, move the robot robot1 up from tile tile_5 to tile tile_11, move the robot robot1 up from tile tile_11 to tile tile_17, paint the tile tile_23 above the tile tile_17 with color white using the robot robot1, move the robot robot1 from the tile tile_17 to the tile tile_11 going downwards, move the robot robot2 up from tile tile_3 to tile tile_9, paint the tile tile_15 above the tile tile_9 with color black using the robot robot2, alter the color of the robot robot2 from color black to color white, move the robot robot2 from the tile tile_9 to the tile tile_3 going downwards, paint the tile tile_9 above the tile tile_3 with color white using the robot robot2, move the robot robot2 from tile tile_3 to the right tile tile_4, alter the color of the robot robot1 from color white to color black, paint the tile tile_17 above the tile tile_11 with color black using the robot robot1, move the robot robot1 from the tile tile_11 to the tile tile_5 going downwards, alter the color of the robot robot1 from color black to color white, move the robot robot2 from the tile tile_4 to the tile on its left tile_3, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, paint the tile tile_11 above the tile tile_5 with color white using the robot robot1, move the robot robot1 from tile tile_5 to the right tile tile_6\"; can the following action be removed from this plan and still have a valid plan: move the robot robot1 from tile tile_5 to the right tile tile_6?", "answer": "yes"}
{"id": -1599985283833254286, "group": "action_justification_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball1, ball14, ball4, ball2, ball11, ball9, and ball10 are at room1, ball15, ball12, ball6, and ball13 are at room2, ball3, ball5, ball8, and ball7 are at room3. The goal is to reach a state where the following facts hold: Ball ball14 is in room room3, Ball ball11 is in room room3, Ball ball15 is in room room1, Ball ball7 is in room room3, Ball ball8 is in room room1, Ball ball12 is in room room2, Ball ball13 is at room2 location, Ball ball4 is at room1 location, Ball ball3 is at room2 location, Ball ball2 is at room1 location, Ball ball10 is at room3 location, Ball ball1 is in room room2, Ball ball6 is at room1 location, Ball ball9 is at room3 location, and Ball ball5 is in room room1.", "question": "Given the plan: \"pick up object ball3 with robot robot1 using left1 gripper from room room3, move robot robot1 from room room3 to room room2, place the object ball3 in the room room2 using the robot robot1 with left1 gripper, pick up object ball15 with robot robot1 using left1 gripper from room room2, move robot robot1 from room room2 to room room1, place the object ball15 in the room room1 using the robot robot1 with left1 gripper, pick up object ball1 with robot robot1 using left1 gripper from room room1, pick up object ball10 with robot robot1 using right1 gripper from room room1, move robot robot1 from room room1 to room room2, place the object ball1 in the room room2 using the robot robot1 with left1 gripper, pick up object ball6 with robot robot1 using left1 gripper from room room2, move robot robot1 from room room2 to room room1, place the object ball6 in the room room1 using the robot robot1 with left1 gripper, pick up object ball14 with robot robot1 using left1 gripper from room room1, move robot robot1 from room room1 to room room3, place the object ball14 in the room room3 using the robot robot1 with left1 gripper, pick up object ball5 with robot robot1 using left1 gripper from room room3, place the object ball5 in the room room3 using the robot robot1 with left1 gripper, pick up object ball5 with robot robot1 using left1 gripper from room room3, place the object ball10 in the room room3 using the robot robot1 with right1 gripper, pick up object ball8 with robot robot1 using right1 gripper from room room3, move robot robot1 from room room3 to room room1, place the object ball5 in the room room1 using the robot robot1 with left1 gripper, place the object ball8 in the room room1 using the robot robot1 with right1 gripper, pick up object ball11 with robot robot1 using left1 gripper from room room1, pick up object ball9 with robot robot1 using right1 gripper from room room1, move robot robot1 from room room1 to room room3, place the object ball11 in the room room3 using the robot robot1 with left1 gripper, place the object ball9 in the room room3 using the robot robot1 with right1 gripper\"; can the following action be removed from this plan and still have a valid plan: pick up object ball9 with robot robot1 using right1 gripper from room room1?", "answer": "no"}
{"id": -1983450415835785045, "group": "action_justification_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room8 and both grippers are free. Additionally, ball4 and ball2 are at room2, ball1 is at room4, ball3 is at room10. The goal is to reach a state where the following facts hold: Ball ball4 is in room room9, Ball ball2 is at room6 location, Ball ball3 is at room8 location, and Ball ball1 is in room room8.", "question": "Given the plan: \"transfer the robot robot1 from the room room8 to the room room1, transfer the robot robot1 from the room room1 to the room room8, transfer the robot robot1 from the room room8 to the room room4, pick up object ball1 with robot robot1 using right1 gripper from room room4, transfer the robot robot1 from the room room4 to the room room8, transfer the robot robot1 from the room room8 to the room room10, pick up object ball3 with robot robot1 using left1 gripper from room room10, transfer the robot robot1 from the room room10 to the room room8, use the right1 gripper of robot robot1 to drop the object ball1 in room room8, use the left1 gripper of robot robot1 to drop the object ball3 in room room8, transfer the robot robot1 from the room room8 to the room room2, pick up object ball2 with robot robot1 using right1 gripper from room room2, pick up object ball4 with robot robot1 using left1 gripper from room room2, transfer the robot robot1 from the room room2 to the room room6, use the right1 gripper of robot robot1 to drop the object ball2 in room room6, transfer the robot robot1 from the room room6 to the room room9, use the left1 gripper of robot robot1 to drop the object ball4 in room room9\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: transfer the robot robot1 from the room room8 to the room room1 and transfer the robot robot1 from the room room1 to the room room8?", "answer": "yes"}
{"id": -2840137019130000801, "group": "action_justification_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room4 and both grippers are free. Additionally, ball3 is at room5, ball4 and ball2 are at room1, ball1 is at room2. The goal is to reach a state where the following facts hold: Ball ball1 is at room4 location, Ball ball2 is at room3 location, Ball ball3 is in room room4, and Ball ball4 is at room5 location.", "question": "Given the plan: \"move the robot robot1 from room room4 to room room3, move the robot robot1 from room room3 to room room4, move the robot robot1 from room room4 to room room1, use the right1 gripper of robot robot1 to pick up the object ball4 from room room1, use the left1 gripper of robot robot1 to pick up the object ball2 from room room1, move the robot robot1 from room room1 to room room3, drop the object ball2 in the left1 gripper of the robot robot1 at the room room3, move the robot robot1 from room room3 to room room4, move the robot robot1 from room room4 to room room5, use the left1 gripper of robot robot1 to pick up the object ball3 from room room5, drop the object ball4 in the right1 gripper of the robot robot1 at the room room5, move the robot robot1 from room room5 to room room2, use the right1 gripper of robot robot1 to pick up the object ball1 from room room2, move the robot robot1 from room room2 to room room4, drop the object ball3 in the left1 gripper of the robot robot1 at the room room4, drop the object ball1 in the right1 gripper of the robot robot1 at the room room4\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: move the robot robot1 from room room4 to room room3 and move the robot robot1 from room room3 to room room4?", "answer": "yes"}
{"id": 5957444152866204413, "group": "action_justification_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball1, ball14, ball4, ball2, ball11, ball9, and ball10 are at room1, ball15, ball12, ball6, and ball13 are at room2, ball3, ball5, ball8, and ball7 are at room3. The goal is to reach a state where the following facts hold: Ball ball14 is at room3 location, Ball ball11 is at room3 location, Ball ball15 is at room1 location, Ball ball7 is in room room3, Ball ball8 is at room1 location, Ball ball12 is at room2 location, Ball ball13 is at room2 location, Ball ball4 is at room1 location, Ball ball3 is at room2 location, Ball ball2 is in room room1, Ball ball10 is in room room3, Ball ball1 is at room2 location, Ball ball6 is at room1 location, Ball ball9 is in room room3, and Ball ball5 is in room room1.", "question": "Given the plan: \"pick up the object ball8 with the robot robot1 using the left1 gripper from the room room3, drop object ball8 in room room3 using left1 gripper of robot robot1, pick up the object ball3 with the robot robot1 using the left1 gripper from the room room3, transfer the robot robot1 from the room room3 to the room room2, drop object ball3 in room room2 using left1 gripper of robot robot1, pick up the object ball15 with the robot robot1 using the left1 gripper from the room room2, transfer the robot robot1 from the room room2 to the room room1, drop object ball15 in room room1 using left1 gripper of robot robot1, pick up the object ball1 with the robot robot1 using the left1 gripper from the room room1, pick up the object ball10 with the robot robot1 using the right1 gripper from the room room1, transfer the robot robot1 from the room room1 to the room room2, drop object ball1 in room room2 using left1 gripper of robot robot1, pick up the object ball6 with the robot robot1 using the left1 gripper from the room room2, transfer the robot robot1 from the room room2 to the room room1, drop object ball6 in room room1 using left1 gripper of robot robot1, pick up the object ball9 with the robot robot1 using the left1 gripper from the room room1, transfer the robot robot1 from the room room1 to the room room3, drop object ball9 in room room3 using left1 gripper of robot robot1, pick up the object ball5 with the robot robot1 using the left1 gripper from the room room3, drop object ball10 in room room3 using right1 gripper of robot robot1, drop object ball5 in room room3 using left1 gripper of robot robot1, pick up the object ball8 with the robot robot1 using the left1 gripper from the room room3, pick up the object ball5 with the robot robot1 using the right1 gripper from the room room3, transfer the robot robot1 from the room room3 to the room room1, drop object ball8 in room room1 using left1 gripper of robot robot1, drop object ball5 in room room1 using right1 gripper of robot robot1, pick up the object ball14 with the robot robot1 using the left1 gripper from the room room1, pick up the object ball11 with the robot robot1 using the right1 gripper from the room room1, transfer the robot robot1 from the room room1 to the room room3, drop object ball14 in room room3 using left1 gripper of robot robot1, drop object ball11 in room room3 using right1 gripper of robot robot1\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: pick up the object ball8 with the robot robot1 using the left1 gripper from the room room3 and drop object ball8 in room room3 using left1 gripper of robot robot1?", "answer": "yes"}
{"id": 2781210828610139012, "group": "action_justification_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball1, ball4, and ball2 are at room1, ball3, ball5, and ball7 are at room3, ball6 is at room2. The goal is to reach a state where the following facts hold: Ball ball1 is in room room3, Ball ball6 is in room room2, Ball ball5 is at room2 location, Ball ball4 is in room room1, Ball ball3 is in room room1, Ball ball2 is in room room1, and Ball ball7 is in room room1.", "question": "Given the plan: \"pick up object ball3 with robot robot1 using left1 gripper from room room3, pick up object ball7 with robot robot1 using right1 gripper from room room3, move robot robot1 from room room3 to room room1, drop the object ball7 in the right1 gripper of the robot robot1 at the room room1, drop the object ball3 in the left1 gripper of the robot robot1 at the room room1, pick up object ball1 with robot robot1 using right1 gripper from room room1, move robot robot1 from room room1 to room room3, drop the object ball1 in the right1 gripper of the robot robot1 at the room room3, pick up object ball5 with robot robot1 using left1 gripper from room room3, move robot robot1 from room room3 to room room2, drop the object ball5 in the left1 gripper of the robot robot1 at the room room2, pick up object ball5 with robot robot1 using left1 gripper from room room2, drop the object ball5 in the left1 gripper of the robot robot1 at the room room2\"; can the following action be removed from this plan and still have a valid plan: drop the object ball1 in the right1 gripper of the robot robot1 at the room room3?", "answer": "no"}
{"id": 6349345229676076162, "group": "action_justification_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball1, ball14, ball4, ball2, ball11, ball9, and ball10 are at room1, ball15, ball12, ball6, and ball13 are at room2, ball3, ball5, ball8, and ball7 are at room3. The goal is to reach a state where the following facts hold: Ball ball14 is at room3 location, Ball ball11 is at room3 location, Ball ball15 is in room room1, Ball ball7 is in room room3, Ball ball8 is at room1 location, Ball ball12 is in room room2, Ball ball13 is at room2 location, Ball ball4 is in room room1, Ball ball3 is in room room2, Ball ball2 is at room1 location, Ball ball10 is in room room3, Ball ball1 is in room room2, Ball ball6 is at room1 location, Ball ball9 is in room room3, and Ball ball5 is at room1 location.", "question": "Given the plan: \"use the left1 gripper of robot robot1 to pick up the object ball3 from room room3, transfer the robot robot1 from room room3 to room room2, drop object ball3 in room room2 using left1 gripper of robot robot1, use the left1 gripper of robot robot1 to pick up the object ball15 from room room2, transfer the robot robot1 from room room2 to room room1, drop object ball15 in room room1 using left1 gripper of robot robot1, use the left1 gripper of robot robot1 to pick up the object ball1 from room room1, use the right1 gripper of robot robot1 to pick up the object ball10 from room room1, transfer the robot robot1 from room room1 to room room3, drop object ball10 in room room3 using right1 gripper of robot robot1, use the right1 gripper of robot robot1 to pick up the object ball5 from room room3, transfer the robot robot1 from room room3 to room room1, drop object ball5 in room room1 using right1 gripper of robot robot1, use the right1 gripper of robot robot1 to pick up the object ball11 from room room1, transfer the robot robot1 from room room1 to room room2, drop object ball1 in room room2 using left1 gripper of robot robot1, use the left1 gripper of robot robot1 to pick up the object ball6 from room room2, transfer the robot robot1 from room room2 to room room1, drop object ball6 in room room1 using left1 gripper of robot robot1, transfer the robot robot1 from room room1 to room room3, use the left1 gripper of robot robot1 to pick up the object ball8 from room room3, drop object ball11 in room room3 using right1 gripper of robot robot1, transfer the robot robot1 from room room3 to room room1, drop object ball8 in room room1 using left1 gripper of robot robot1, use the left1 gripper of robot robot1 to pick up the object ball14 from room room1, use the right1 gripper of robot robot1 to pick up the object ball9 from room room1, transfer the robot robot1 from room room1 to room room3, drop object ball14 in room room3 using left1 gripper of robot robot1, drop object ball9 in room room3 using right1 gripper of robot robot1, use the left1 gripper of robot robot1 to pick up the object ball7 from room room3, drop object ball7 in room room3 using left1 gripper of robot robot1\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: use the left1 gripper of robot robot1 to pick up the object ball7 from room room3 and drop object ball7 in room room3 using left1 gripper of robot robot1?", "answer": "yes"}
{"id": 964023992075976907, "group": "action_justification_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room8 and both grippers are free. Additionally, ball4 and ball2 are at room2, ball1 is at room4, ball3 is at room10. The goal is to reach a state where the following facts hold: Ball ball4 is at room9 location, Ball ball2 is in room room6, Ball ball3 is at room8 location, and Ball ball1 is in room room8.", "question": "Given the plan: \"transfer the robot robot1 from the room room8 to the room room1, transfer the robot robot1 from the room room1 to the room room8, transfer the robot robot1 from the room room8 to the room room2, grasp the object ball2 from room room2 with the left1 gripper of robot robot1, grasp the object ball4 from room room2 with the right1 gripper of robot robot1, transfer the robot robot1 from the room room2 to the room room6, use robot robot1 with left1 gripper to place the object ball2 in room room6, transfer the robot robot1 from the room room6 to the room room9, use robot robot1 with right1 gripper to place the object ball4 in room room9, transfer the robot robot1 from the room room9 to the room room10, grasp the object ball3 from room room10 with the right1 gripper of robot robot1, transfer the robot robot1 from the room room10 to the room room2, transfer the robot robot1 from the room room2 to the room room4, grasp the object ball1 from room room4 with the left1 gripper of robot robot1, transfer the robot robot1 from the room room4 to the room room8, use robot robot1 with left1 gripper to place the object ball1 in room room8, use robot robot1 with right1 gripper to place the object ball3 in room room8\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: transfer the robot robot1 from the room room8 to the room room1 and transfer the robot robot1 from the room room1 to the room room8?", "answer": "yes"}
{"id": 2797213626234796365, "group": "action_justification_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball1, ball4, and ball2 are at room1, ball3, ball5, and ball7 are at room3, ball6 is at room2. The goal is to reach a state where the following facts hold: Ball ball1 is in room room3, Ball ball6 is at room2 location, Ball ball5 is at room2 location, Ball ball4 is at room1 location, Ball ball3 is in room room1, Ball ball2 is at room1 location, and Ball ball7 is at room1 location.", "question": "Given the plan: \"pick up the object ball3 with robot robot1 using left1 gripper from room room3, pick up the object ball7 with robot robot1 using right1 gripper from room room3, move the robot robot1 from room room3 to room room1, use robot robot1 with left1 gripper to place the object ball3 in room room1, use robot robot1 with right1 gripper to place the object ball7 in room room1, pick up the object ball3 with robot robot1 using left1 gripper from room room1, use robot robot1 with left1 gripper to place the object ball3 in room room1, pick up the object ball1 with robot robot1 using right1 gripper from room room1, move the robot robot1 from room room1 to room room3, use robot robot1 with right1 gripper to place the object ball1 in room room3, pick up the object ball5 with robot robot1 using right1 gripper from room room3, move the robot robot1 from room room3 to room room2, use robot robot1 with right1 gripper to place the object ball5 in room room2\"; can the following action be removed from this plan and still have a valid plan: pick up the object ball3 with robot robot1 using left1 gripper from room room3?", "answer": "no"}
{"id": 8025364418721601391, "group": "action_justification_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball1, ball4, and ball2 are at room1, ball3, ball5, and ball7 are at room3, ball6 is at room2. The goal is to reach a state where the following facts hold: Ball ball1 is in room room3, Ball ball6 is at room2 location, Ball ball5 is at room2 location, Ball ball4 is in room room1, Ball ball3 is at room1 location, Ball ball2 is at room1 location, and Ball ball7 is at room1 location.", "question": "Given the plan: \"pick up the object ball3 with robot robot1 using left1 gripper from room room3, pick up the object ball5 with robot robot1 using right1 gripper from room room3, move the robot robot1 from room room3 to room room1, use the left1 gripper of robot robot1 to drop the object ball3 in room room1, pick up the object ball1 with robot robot1 using left1 gripper from room room1, move the robot robot1 from room room1 to room room2, move the robot robot1 from room room2 to room room3, use the left1 gripper of robot robot1 to drop the object ball1 in room room3, pick up the object ball7 with robot robot1 using left1 gripper from room room3, move the robot robot1 from room room3 to room room2, use the right1 gripper of robot robot1 to drop the object ball5 in room room2, move the robot robot1 from room room2 to room room1, use the left1 gripper of robot robot1 to drop the object ball7 in room room1\"; can the following action be removed from this plan and still have a valid plan: use the left1 gripper of robot robot1 to drop the object ball1 in room room3?", "answer": "no"}
{"id": -5344641974890869231, "group": "action_justification_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room4 and both grippers are free. Additionally, ball3 is at room5, ball4 and ball2 are at room1, ball1 is at room2. The goal is to reach a state where the following facts hold: Ball ball1 is in room room4, Ball ball2 is at room3 location, Ball ball3 is in room room4, and Ball ball4 is in room room5.", "question": "Given the plan: \"move robot robot1 from room room4 to room room1, pick up the object ball4 with the robot robot1 using the right1 gripper from the room room1, pick up the object ball2 with the robot robot1 using the left1 gripper from the room room1, move robot robot1 from room room1 to room room5, drop the object ball4 in the right1 gripper of the robot robot1 at the room room5, move robot robot1 from room room5 to room room3, drop the object ball2 in the left1 gripper of the robot robot1 at the room room3, move robot robot1 from room room3 to room room2, pick up the object ball1 with the robot robot1 using the right1 gripper from the room room2, move robot robot1 from room room2 to room room5, pick up the object ball3 with the robot robot1 using the left1 gripper from the room room5, move robot robot1 from room room5 to room room4, drop the object ball1 in the right1 gripper of the robot robot1 at the room room4, drop the object ball3 in the left1 gripper of the robot robot1 at the room room4\"; can the following action be removed from this plan and still have a valid plan: move robot robot1 from room room4 to room room1?", "answer": "no"}
{"id": -1176293359104985967, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res and colour. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint4 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1, waypoint3, and waypoint0. Waypoint(s) are visible from waypoint0: waypoint3, waypoint4, waypoint1, and waypoint2. Waypoint(s) are visible from waypoint1: waypoint2, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint3: waypoint0 and waypoint2. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint2 and waypoint1. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0 and waypoint1. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode colour, Image objective0 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Rock data was communicated from waypoint waypoint1;.", "question": "Given the plan: \"calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint2, move the rover rover1 from waypoint waypoint2 to waypoint waypoint0, sample the rock at waypoint waypoint0 with rover rover1 and store it in store store1, communicate rock data from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint3 via waypoint waypoint0, move the rover rover0 from waypoint waypoint0 to waypoint waypoint1, calibrate the camera camera0 on rover rover0 for the objective objective0 at the waypoint waypoint1, move the rover rover0 from waypoint waypoint1 to waypoint waypoint4, capture an image of objective objective1 in mode low_res using the camera camera0 on the rover rover0 from waypoint waypoint4, move the rover rover1 from waypoint waypoint0 to waypoint waypoint2, move the rover rover1 from waypoint waypoint2 to waypoint waypoint1, capture an image of objective objective0 in mode low_res using the camera camera1 on the rover rover1 from waypoint waypoint1, calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint1, capture an image of objective objective0 in mode colour using the camera camera1 on the rover rover1 from waypoint waypoint1, move the rover rover0 from waypoint waypoint4 to waypoint waypoint1, move the rover rover1 from waypoint waypoint1 to waypoint waypoint2, communicate the image data of objective objective0 in mode colour from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, communicate the image data of objective objective0 in mode low_res from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, move the rover rover0 from waypoint waypoint1 to waypoint waypoint0, communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3, use the rover rover0 to collect soil samples at waypoint waypoint0 and store them in the store store0, communicate soil data from rover rover0 at waypoint waypoint0 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint3, move the rover rover1 from waypoint waypoint2 to waypoint waypoint1, drop from store store1 of rover rover1, sample the rock at waypoint waypoint1 with rover rover1 and store it in store store1, move the rover rover1 from waypoint waypoint1 to waypoint waypoint2, communicate rock data from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3 via waypoint waypoint1, move the rover rover1 from waypoint waypoint2 to waypoint waypoint0\"; can the following action be removed from this plan and still have a valid plan: move the rover rover1 from waypoint waypoint2 to waypoint waypoint0?", "answer": "yes"}
{"id": -8183749649213245858, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res and high_res. Camera camera0 supports low_res. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Rover rover0 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Soil data was communicated from waypoint waypoint1;, Rock data was communicated from waypoint waypoint1;, and Image objective0 was communicated in mode low_res.", "question": "Given the plan: \"navigate rover rover0 from waypoint waypoint2 to waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective0 at the waypoint waypoint1, take a picture of the objective objective1 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective0 at the waypoint waypoint1, take a picture of the objective objective0 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1, collect a rock sample from waypoint waypoint1 using rover rover0 and store it in store store0, empty the store store0 from rover rover0, sample soil at waypoint waypoint1 with rover rover0 and store in store store0, navigate rover rover0 from waypoint waypoint1 to waypoint waypoint2, communicate image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate rock data from rover rover0 at waypoint waypoint2 about waypoint waypoint1 to lander general at waypoint waypoint1, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint0, communicate image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint1, communicate image data of objective objective0 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: communicate image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1?", "answer": "yes"}
{"id": -6445788600385362326, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 7 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective1. Camera camera0 can be calibrated on objective4. Camera camera2 can be calibrated on objective1. Camera camera1 supports low_res. Camera camera0 supports colour. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective6 is visible from waypoint1 and waypoint0. Objective objective2 is visible from waypoint1. Objective objective5 is visible from waypoint1. Objective objective4 is visible from waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective3 is visible from waypoint2. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0, waypoint1, and waypoint2. Soil can be sampled at the following location(s): waypoint2 and waypoint1. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint1;, Soil data was communicated from waypoint waypoint2;, Image objective0 was communicated in mode colour, Rock data was communicated from waypoint waypoint0;, Image objective3 was communicated in mode high_res, Image objective0 was communicated in mode low_res, Image objective6 was communicated in mode low_res, Rock data was communicated from waypoint waypoint2;, Rock data was communicated from waypoint waypoint1;, and Image objective4 was communicated in mode low_res.", "question": "Given the plan: \"adjust the camera camera2 on the rover rover1 for the objective objective1 at waypoint waypoint2, take an image of the objective objective3 in mode high_res using the camera camera2 on the rover rover1 from the waypoint waypoint2, transmit the image data of the objective objective3 in mode high_res from the rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint1, sample a rock at waypoint waypoint2 using rover rover1, then store it in the storage unit store1, communicate the rock data from the rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint1 via waypoint waypoint2, navigate rover rover1 from waypoint waypoint2 to waypoint waypoint1, navigate rover rover0 from waypoint waypoint0 to waypoint waypoint2, adjust the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint2, sample soil at waypoint waypoint2 with rover rover0 and store in the store store0, communicate the soil data from rover rover0 at waypoint waypoint2 with the soil analysis of waypoint waypoint2 to lander general at waypoint waypoint1, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint0, communicate the soil data from rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint2 to lander general at waypoint waypoint1, drop store store0 of rover rover0, take an image of the objective objective0 in mode low_res using the camera camera1 on the rover rover0 from the waypoint waypoint0, transmit the image data of the objective objective0 in mode low_res from the rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, navigate rover rover0 from waypoint waypoint0 to waypoint waypoint1, sample soil at waypoint waypoint1 with rover rover0 and store in the store store0, adjust the camera camera0 on the rover rover0 for the objective objective4 at waypoint waypoint1, take an image of the objective objective0 in mode colour using the camera camera0 on the rover rover0 from the waypoint waypoint1, navigate rover rover0 from waypoint waypoint1 to waypoint waypoint0, communicate the soil data from rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1, transmit the image data of the objective objective0 in mode colour from the rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, drop store store1 of rover rover1, navigate rover rover1 from waypoint waypoint1 to waypoint waypoint2, navigate rover rover1 from waypoint waypoint2 to waypoint waypoint0, sample a rock at waypoint waypoint0 using rover rover1, then store it in the storage unit store1, communicate the rock data from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint1 via waypoint waypoint0, navigate rover rover1 from waypoint waypoint0 to waypoint waypoint2, drop store store1 of rover rover1, navigate rover rover1 from waypoint waypoint2 to waypoint waypoint1, sample a rock at waypoint waypoint1 using rover rover1, then store it in the storage unit store1, navigate rover rover1 from waypoint waypoint1 to waypoint waypoint2, navigate rover rover0 from waypoint waypoint0 to waypoint waypoint2, communicate the rock data from the rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint1 via waypoint waypoint1, adjust the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint2, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint0, take an image of the objective objective6 in mode low_res using the camera camera1 on the rover rover0 from the waypoint waypoint0, transmit the image data of the objective objective6 in mode low_res from the rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, navigate rover rover0 from waypoint waypoint0 to waypoint waypoint2, adjust the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint2, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint0, navigate rover rover0 from waypoint waypoint0 to waypoint waypoint1, take an image of the objective objective4 in mode low_res using the camera camera1 on the rover rover0 from the waypoint waypoint1, navigate rover rover0 from waypoint waypoint1 to waypoint waypoint0, transmit the image data of the objective objective4 in mode low_res from the rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: communicate the soil data from rover rover0 at waypoint waypoint2 with the soil analysis of waypoint waypoint2 to lander general at waypoint waypoint1?", "answer": "yes"}
{"id": 8481620696822644480, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective0. Camera camera1 supports low_res. Camera camera0 supports colour. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective2 is visible from waypoint1. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode colour, and Rock data was communicated from waypoint waypoint0;.", "question": "Given the plan: \"navigate rover rover1 from waypoint waypoint2 to waypoint waypoint0, adjust the camera camera2 on the rover rover1 for the objective objective0 at waypoint waypoint0, use the rover rover0 to collect soil samples at waypoint waypoint0 and store them in the store store0, communicate the soil data from the rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to the lander general at waypoint waypoint1, sample the rock at waypoint waypoint0 with rover rover1 and store it in store store1, navigate rover rover1 from waypoint waypoint0 to waypoint waypoint2, communicate rock data from rover rover1 at waypoint waypoint2 about waypoint waypoint0 to lander general at waypoint waypoint1, capture an image of the objective objective1 in mode colour with the camera camera2 on the rover rover1 at waypoint waypoint2, communicate image data of objective objective1 in mode colour from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint1, navigate rover rover1 from waypoint waypoint2 to waypoint waypoint1, adjust the camera camera2 on the rover rover1 for the objective objective0 at waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: use the rover rover0 to collect soil samples at waypoint waypoint0 and store them in the store store0?", "answer": "no"}
{"id": 1463742592245775874, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective0. Camera camera1 supports low_res. Camera camera0 supports colour. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective2 is visible from waypoint1. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode colour, and Rock data was communicated from waypoint waypoint0;.", "question": "Given the plan: \"sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store0, communicate the soil data from rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, communicate the soil data from rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, move the rover rover0 from waypoint waypoint0 to waypoint waypoint2, move the rover rover1 from waypoint waypoint2 to waypoint waypoint0, calibrate the camera camera0 on rover rover0 for the objective objective1 at the waypoint waypoint2, take an image of the objective objective1 in mode colour using the camera camera0 on the rover rover0 from the waypoint waypoint2, sample rock at waypoint waypoint0 with rover rover1 and store in store store1, transmit the image data of the objective objective1 in mode colour from the rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, communicate rock data from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint0\"; can the following action be removed from this plan and still have a valid plan: communicate the soil data from rover rover0 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1?", "answer": "yes"}
{"id": -1776480360619521463, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res and high_res. Camera camera0 supports low_res. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Rover rover0 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Soil data was communicated from waypoint waypoint1;, Rock data was communicated from waypoint waypoint1;, and Image objective0 was communicated in mode low_res.", "question": "Given the plan: \"navigate the rover rover0 from waypoint waypoint2 to waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective0 at the waypoint waypoint1, take an image of objective objective0 in mode low_res using camera camera1 on rover rover0 from waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective0 at the waypoint waypoint1, sample a rock at waypoint waypoint1 using rover rover0, then store it in the storage unit store0, empty the store store0 from rover rover0, calibrate the camera camera1 on the rover rover0 for the objective objective0 at the waypoint waypoint1, take an image of objective objective1 in mode low_res using camera camera1 on rover rover0 from waypoint waypoint1, sample soil at waypoint waypoint1 with rover rover0 and store in store store0, navigate the rover rover0 from waypoint waypoint1 to waypoint waypoint2, communicate rock data from rover rover0 at waypoint waypoint2 about waypoint waypoint1 to lander general at waypoint waypoint1, communicate the image data of target objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of target objective0 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, navigate the rover rover0 from waypoint waypoint2 to waypoint waypoint0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: calibrate the camera camera1 on the rover rover0 for the objective objective0 at the waypoint waypoint1?", "answer": "yes"}
{"id": 843876286158284239, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res and colour. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint4 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1, waypoint3, and waypoint0. Waypoint(s) are visible from waypoint0: waypoint3, waypoint4, waypoint1, and waypoint2. Waypoint(s) are visible from waypoint1: waypoint2, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint3: waypoint0 and waypoint2. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint2 and waypoint1. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0 and waypoint1. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode colour, Image objective0 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Rock data was communicated from waypoint waypoint1;.", "question": "Given the plan: \"calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint2, navigate with rover rover0 from waypoint waypoint0 to waypoint waypoint1, calibrate the camera camera0 on rover rover0 for the objective objective0 at the waypoint waypoint1, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint0, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint4, take an image of the objective objective1 in mode low_res using the camera camera0 on the rover rover0 from the waypoint waypoint4, sample the rock at waypoint waypoint0 with rover rover1 and store it in store store1, transmit the rock data from the rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3 using waypoint waypoint0 as a relay, navigate with rover rover1 from waypoint waypoint0 to waypoint waypoint2, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint1, take an image of the objective objective0 in mode low_res using the camera camera1 on the rover rover1 from the waypoint waypoint1, calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint1, take an image of the objective objective0 in mode colour using the camera camera1 on the rover rover1 from the waypoint waypoint1, navigate with rover rover1 from waypoint waypoint1 to waypoint waypoint2, communicate image data of objective objective0 in mode colour from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, communicate image data of objective objective0 in mode low_res from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint1, unload the store store1 from the rover rover1, sample the rock at waypoint waypoint1 with rover rover1 and store it in store store1, navigate with rover rover1 from waypoint waypoint1 to waypoint waypoint2, transmit the rock data from the rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint3 using waypoint waypoint1 as a relay, navigate with rover rover0 from waypoint waypoint4 to waypoint waypoint1, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint0, communicate image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3, sample the soil at waypoint waypoint0 with rover rover0 and store it in store store0, transmit soil data from rover rover0 located at waypoint waypoint0 to lander general located at waypoint waypoint3 using soil analysis of waypoint waypoint0, transmit soil data from rover rover0 located at waypoint waypoint0 to lander general located at waypoint waypoint3 using soil analysis of waypoint waypoint0\"; can the following action be removed from this plan and still have a valid plan: transmit soil data from rover rover0 located at waypoint waypoint0 to lander general located at waypoint waypoint3 using soil analysis of waypoint waypoint0?", "answer": "yes"}
{"id": 1762989238429457397, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res and colour. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint4 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1, waypoint3, and waypoint0. Waypoint(s) are visible from waypoint0: waypoint3, waypoint4, waypoint1, and waypoint2. Waypoint(s) are visible from waypoint1: waypoint2, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint3: waypoint0 and waypoint2. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint2 and waypoint1. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0 and waypoint1. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode colour, Image objective0 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Rock data was communicated from waypoint waypoint1;.", "question": "Given the plan: \"adjust the camera camera1 on the rover rover1 for the objective objective0 at waypoint waypoint2, move the rover rover1 from waypoint waypoint2 to waypoint waypoint0, collect a rock sample from waypoint waypoint0 using rover rover1 and store it in store store1, transmit the rock data from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint3 through waypoint waypoint0, move the rover rover0 from waypoint waypoint0 to waypoint waypoint1, adjust the camera camera0 on the rover rover0 for the objective objective0 at waypoint waypoint1, move the rover rover0 from waypoint waypoint1 to waypoint waypoint4, take a picture of the objective objective1 in mode low_res using the camera camera0 mounted on the rover rover0 from the waypoint waypoint4, move the rover rover1 from waypoint waypoint0 to waypoint waypoint2, move the rover rover1 from waypoint waypoint2 to waypoint waypoint1, take a picture of the objective objective0 in mode low_res using the camera camera1 mounted on the rover rover1 from the waypoint waypoint1, adjust the camera camera1 on the rover rover1 for the objective objective0 at waypoint waypoint1, take a picture of the objective objective0 in mode colour using the camera camera1 mounted on the rover rover1 from the waypoint waypoint1, move the rover rover1 from waypoint waypoint1 to waypoint waypoint2, communicate the image data of objective objective0 in mode colour from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, communicate the image data of objective objective0 in mode low_res from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, move the rover rover1 from waypoint waypoint2 to waypoint waypoint1, if storey is full, drop store store1 of rover rover1, collect a rock sample from waypoint waypoint1 using rover rover1 and store it in store store1, move the rover rover1 from waypoint waypoint1 to waypoint waypoint2, transmit the rock data from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint3 through waypoint waypoint1, move the rover rover0 from waypoint waypoint4 to waypoint waypoint1, move the rover rover0 from waypoint waypoint1 to waypoint waypoint0, communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3, sample soil at waypoint waypoint0 with rover rover0 and store in the store store0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint3, regarding the soil analysis of waypoint waypoint0, move the rover rover0 from waypoint waypoint0 to waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: communicate the image data of objective objective0 in mode low_res from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3?", "answer": "no"}
{"id": 529173937884219804, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective0. Camera camera1 supports low_res. Camera camera0 supports colour. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective2 is visible from waypoint1. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode colour, and Rock data was communicated from waypoint waypoint0;.", "question": "Given the plan: \"navigate rover rover1 from waypoint waypoint2 to waypoint waypoint0, sample rock at waypoint waypoint0 with rover rover1 and store in store store1, transmit the rock data from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint1 through waypoint waypoint0, sample soil at waypoint waypoint0 with rover rover0 and store in store store0, navigate rover rover0 from waypoint waypoint0 to waypoint waypoint2, calibrate the camera camera0 on the rover rover0 for the objective objective1 at the waypoint waypoint2, capture an image of the objective objective1 in mode colour with the camera camera0 on the rover rover0 at waypoint waypoint2, transmit image data of objective objective1 in mode colour from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, transmit image data of objective objective1 in mode colour from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, transmit soil data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, transmit image data of objective objective1 in mode colour from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: calibrate the camera camera0 on the rover rover0 for the objective objective1 at the waypoint waypoint2?", "answer": "no"}
{"id": -915539420950664861, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res and colour. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint4 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1, waypoint3, and waypoint0. Waypoint(s) are visible from waypoint0: waypoint3, waypoint4, waypoint1, and waypoint2. Waypoint(s) are visible from waypoint1: waypoint2, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint3: waypoint0 and waypoint2. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint2 and waypoint1. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0 and waypoint1. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode colour, Image objective0 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Rock data was communicated from waypoint waypoint1;.", "question": "Given the plan: \"adjust the camera camera1 on the rover rover1 for the objective objective0 at waypoint waypoint2, sample soil at waypoint waypoint0 with rover rover0 and store in store store0, move the rover rover1 from waypoint waypoint2 to waypoint waypoint0, communicate soil data from rover rover0 at waypoint waypoint0 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint3, move the rover rover0 from waypoint waypoint0 to waypoint waypoint1, adjust the camera camera0 on the rover rover0 for the objective objective0 at waypoint waypoint1, move the rover rover0 from waypoint waypoint1 to waypoint waypoint4, capture an image of objective objective1 in mode low_res using the camera camera0 on the rover rover0 from waypoint waypoint4, move the rover rover0 from waypoint waypoint4 to waypoint waypoint1, adjust the camera camera0 on the rover rover0 for the objective objective0 at waypoint waypoint1, sample rock at waypoint waypoint0 with rover rover1 and store in store store1, communicate rock data from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint3 via waypoint waypoint0, capture an image of objective objective0 in mode colour using the camera camera0 on the rover rover0 from waypoint waypoint1, move the rover rover0 from waypoint waypoint1 to waypoint waypoint0, communicate image data of objective objective0 in mode colour from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3, communicate image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3, communicate image data of objective objective0 in mode colour from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3, communicate image data of objective objective0 in mode colour from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3, move the rover rover1 from waypoint waypoint0 to waypoint waypoint2, move the rover rover1 from waypoint waypoint2 to waypoint waypoint1, capture an image of objective objective0 in mode low_res using the camera camera1 on the rover rover1 from waypoint waypoint1, move the rover rover1 from waypoint waypoint1 to waypoint waypoint2, communicate image data of objective objective0 in mode low_res from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3, drop the content from store store1 of the rover rover1, move the rover rover1 from waypoint waypoint2 to waypoint waypoint1, sample rock at waypoint waypoint1 with rover rover1 and store in store store1, move the rover rover1 from waypoint waypoint1 to waypoint waypoint2, communicate rock data from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3 via waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: communicate image data of objective objective0 in mode colour from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint3?", "answer": "yes"}
{"id": 2339363515767582098, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x3-y0.Place loc-x3-y0 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"go to loc-x2-y0 from loc-x3-y0, go to loc-x2-y1 from loc-x2-y0, go to loc-x1-y1 from loc-x2-y1, go to loc-x1-y2 from loc-x1-y1, go to loc-x2-y2 from loc-x1-y2, go to loc-x3-y2 from loc-x2-y2, go to loc-x3-y3 from loc-x3-y2, go to loc-x2-y3 from loc-x3-y3, go to loc-x1-y3 from loc-x2-y3, go to loc-x0-y3 from loc-x1-y3, go to loc-x0-y2 from loc-x0-y3, go to loc-x0-y1 from loc-x0-y2, go to loc-x0-y0 from loc-x0-y1, go to loc-x0-y1 from loc-x0-y0, go to loc-x1-y1 from loc-x0-y1\"; can the following action be removed from this plan and still have a valid plan: go to loc-x2-y0 from loc-x3-y0?", "answer": "no"}
{"id": -139889112813956311, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y3, loc-x3-y1, loc-x1-y0, and loc-x0-y2. Currently, the robot is in place loc-x2-y1.Place loc-x2-y1 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"navigate from loc-x2-y1 to loc-x2-y0, navigate from loc-x2-y0 to loc-x3-y0, navigate from loc-x3-y0 to loc-x2-y0, navigate from loc-x2-y0 to loc-x2-y1, navigate from loc-x2-y1 to loc-x2-y2, navigate from loc-x2-y2 to loc-x3-y2, navigate from loc-x3-y2 to loc-x2-y2, navigate from loc-x2-y2 to loc-x2-y3, navigate from loc-x2-y3 to loc-x1-y3, navigate from loc-x1-y3 to loc-x0-y3, navigate from loc-x0-y3 to loc-x1-y3, navigate from loc-x1-y3 to loc-x2-y3, navigate from loc-x2-y3 to loc-x2-y2, navigate from loc-x2-y2 to loc-x1-y2, navigate from loc-x1-y2 to loc-x1-y1, navigate from loc-x1-y1 to loc-x0-y1, navigate from loc-x0-y1 to loc-x0-y0, navigate from loc-x0-y0 to loc-x0-y1\"; can the following action be removed from this plan and still have a valid plan: navigate from loc-x2-y3 to loc-x2-y2?", "answer": "no"}
{"id": 928900863794676562, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x3-y0.Place loc-x3-y0 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"move to place loc-x2-y0 from place loc-x3-y0, move to place loc-x2-y1 from place loc-x2-y0, move to place loc-x2-y2 from place loc-x2-y1, move to place loc-x3-y2 from place loc-x2-y2, move to place loc-x3-y3 from place loc-x3-y2, move to place loc-x2-y3 from place loc-x3-y3, move to place loc-x3-y3 from place loc-x2-y3, move to place loc-x2-y3 from place loc-x3-y3, move to place loc-x1-y3 from place loc-x2-y3, move to place loc-x0-y3 from place loc-x1-y3, move to place loc-x0-y2 from place loc-x0-y3, move to place loc-x1-y2 from place loc-x0-y2, move to place loc-x1-y1 from place loc-x1-y2, move to place loc-x0-y1 from place loc-x1-y1, move to place loc-x0-y0 from place loc-x0-y1\"; can the following action be removed from this plan and still have a valid plan: move to place loc-x2-y1 from place loc-x2-y0?", "answer": "no"}
{"id": 4664708134759721139, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x1-y0.Place loc-x1-y0 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, Place loc-x3-y1 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"move from place loc-x1-y0 to place loc-x2-y0, move from place loc-x2-y0 to place loc-x2-y1, move from place loc-x2-y1 to place loc-x1-y1, move from place loc-x1-y1 to place loc-x1-y0, move from place loc-x1-y0 to place loc-x0-y0, move from place loc-x0-y0 to place loc-x0-y1, move from place loc-x0-y1 to place loc-x0-y2, move from place loc-x0-y2 to place loc-x0-y3, move from place loc-x0-y3 to place loc-x1-y3, move from place loc-x1-y3 to place loc-x1-y2, move from place loc-x1-y2 to place loc-x2-y2, move from place loc-x2-y2 to place loc-x2-y3, move from place loc-x2-y3 to place loc-x3-y3, move from place loc-x3-y3 to place loc-x3-y2, move from place loc-x3-y2 to place loc-x3-y1, move from place loc-x3-y1 to place loc-x3-y0\"; can the following action be removed from this plan and still have a valid plan: move from place loc-x0-y0 to place loc-x0-y1?", "answer": "no"}
{"id": -2286539557956697782, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x1-y0.Place loc-x1-y0 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, Place loc-x3-y1 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"move from place loc-x1-y0 to place loc-x1-y1, move from place loc-x1-y1 to place loc-x1-y2, move from place loc-x1-y2 to place loc-x2-y2, move from place loc-x2-y2 to place loc-x2-y1, move from place loc-x2-y1 to place loc-x2-y0, move from place loc-x2-y0 to place loc-x3-y0, move from place loc-x3-y0 to place loc-x3-y1, move from place loc-x3-y1 to place loc-x3-y2, move from place loc-x3-y2 to place loc-x3-y3, move from place loc-x3-y3 to place loc-x2-y3, move from place loc-x2-y3 to place loc-x1-y3, move from place loc-x1-y3 to place loc-x0-y3, move from place loc-x0-y3 to place loc-x0-y2, move from place loc-x0-y2 to place loc-x0-y1, move from place loc-x0-y1 to place loc-x0-y0, move from place loc-x0-y0 to place loc-x1-y0\"; can the following action be removed from this plan and still have a valid plan: move from place loc-x0-y0 to place loc-x1-y0?", "answer": "yes"}
{"id": 2362222126872726663, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x2-y3, loc-x1-y2, and loc-x0-y4. Currently, the robot is in place loc-x0-y2.Place loc-x0-y2 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y4 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y4 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, Place loc-x1-y4 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"go to loc-x0-y3 from loc-x0-y2, go to loc-x1-y3 from loc-x0-y3, go to loc-x1-y4 from loc-x1-y3, go to loc-x2-y4 from loc-x1-y4, go to loc-x3-y4 from loc-x2-y4, go to loc-x3-y3 from loc-x3-y4, go to loc-x3-y2 from loc-x3-y3, go to loc-x2-y2 from loc-x3-y2, go to loc-x2-y1 from loc-x2-y2, go to loc-x1-y1 from loc-x2-y1, go to loc-x1-y0 from loc-x1-y1, go to loc-x0-y0 from loc-x1-y0, go to loc-x0-y1 from loc-x0-y0, go to loc-x1-y1 from loc-x0-y1, go to loc-x1-y0 from loc-x1-y1, go to loc-x2-y0 from loc-x1-y0, go to loc-x3-y0 from loc-x2-y0, go to loc-x3-y1 from loc-x3-y0\"; can the following action be removed from this plan and still have a valid plan: go to loc-x3-y0 from loc-x2-y0?", "answer": "no"}
{"id": -350564967936887876, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x1-y0.Place loc-x1-y0 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, Place loc-x3-y1 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"transition from the current position loc-x1-y0 to the next position loc-x0-y0, transition from the current position loc-x0-y0 to the next position loc-x0-y1, transition from the current position loc-x0-y1 to the next position loc-x1-y1, transition from the current position loc-x1-y1 to the next position loc-x2-y1, transition from the current position loc-x2-y1 to the next position loc-x2-y0, transition from the current position loc-x2-y0 to the next position loc-x3-y0, transition from the current position loc-x3-y0 to the next position loc-x3-y1, transition from the current position loc-x3-y1 to the next position loc-x3-y2, transition from the current position loc-x3-y2 to the next position loc-x3-y3, transition from the current position loc-x3-y3 to the next position loc-x2-y3, transition from the current position loc-x2-y3 to the next position loc-x2-y2, transition from the current position loc-x2-y2 to the next position loc-x2-y3, transition from the current position loc-x2-y3 to the next position loc-x1-y3, transition from the current position loc-x1-y3 to the next position loc-x0-y3, transition from the current position loc-x0-y3 to the next position loc-x0-y2, transition from the current position loc-x0-y2 to the next position loc-x1-y2\"; can the following action be removed from this plan and still have a valid plan: transition from the current position loc-x0-y0 to the next position loc-x0-y1?", "answer": "no"}
{"id": -7605946517573176589, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x1-y0.Place loc-x1-y0 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, Place loc-x3-y1 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"move to place loc-x0-y0 from place loc-x1-y0, move to place loc-x0-y1 from place loc-x0-y0, move to place loc-x0-y2 from place loc-x0-y1, move to place loc-x0-y3 from place loc-x0-y2, move to place loc-x1-y3 from place loc-x0-y3, move to place loc-x2-y3 from place loc-x1-y3, move to place loc-x2-y2 from place loc-x2-y3, move to place loc-x1-y2 from place loc-x2-y2, move to place loc-x1-y1 from place loc-x1-y2, move to place loc-x2-y1 from place loc-x1-y1, move to place loc-x2-y0 from place loc-x2-y1, move to place loc-x3-y0 from place loc-x2-y0, move to place loc-x3-y1 from place loc-x3-y0, move to place loc-x3-y2 from place loc-x3-y1, move to place loc-x3-y3 from place loc-x3-y2\"; can the following action be removed from this plan and still have a valid plan: move to place loc-x3-y0 from place loc-x2-y0?", "answer": "no"}
{"id": -8002491087609043275, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x2-y3, loc-x1-y2, and loc-x0-y4. Currently, the robot is in place loc-x0-y2.Place loc-x0-y2 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y4 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y4 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, Place loc-x1-y4 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"transition from the current position loc-x0-y2 to the next position loc-x0-y3, transition from the current position loc-x0-y3 to the next position loc-x1-y3, transition from the current position loc-x1-y3 to the next position loc-x1-y4, transition from the current position loc-x1-y4 to the next position loc-x2-y4, transition from the current position loc-x2-y4 to the next position loc-x3-y4, transition from the current position loc-x3-y4 to the next position loc-x3-y3, transition from the current position loc-x3-y3 to the next position loc-x3-y2, transition from the current position loc-x3-y2 to the next position loc-x3-y1, transition from the current position loc-x3-y1 to the next position loc-x3-y0, transition from the current position loc-x3-y0 to the next position loc-x2-y0, transition from the current position loc-x2-y0 to the next position loc-x1-y0, transition from the current position loc-x1-y0 to the next position loc-x0-y0, transition from the current position loc-x0-y0 to the next position loc-x0-y1, transition from the current position loc-x0-y1 to the next position loc-x1-y1, transition from the current position loc-x1-y1 to the next position loc-x2-y1, transition from the current position loc-x2-y1 to the next position loc-x2-y2, transition from the current position loc-x2-y2 to the next position loc-x2-y1\"; can the following action be removed from this plan and still have a valid plan: transition from the current position loc-x3-y1 to the next position loc-x3-y0?", "answer": "no"}
{"id": 9105253077038962047, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x2-y3, loc-x1-y2, and loc-x0-y4. Currently, the robot is in place loc-x0-y2.Place loc-x0-y2 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y4 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y4 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, Place loc-x1-y4 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"navigate from loc-x0-y2 to loc-x0-y3, navigate from loc-x0-y3 to loc-x1-y3, navigate from loc-x1-y3 to loc-x1-y4, navigate from loc-x1-y4 to loc-x2-y4, navigate from loc-x2-y4 to loc-x3-y4, navigate from loc-x3-y4 to loc-x3-y3, navigate from loc-x3-y3 to loc-x3-y2, navigate from loc-x3-y2 to loc-x2-y2, navigate from loc-x2-y2 to loc-x2-y1, navigate from loc-x2-y1 to loc-x3-y1, navigate from loc-x3-y1 to loc-x3-y0, navigate from loc-x3-y0 to loc-x2-y0, navigate from loc-x2-y0 to loc-x1-y0, navigate from loc-x1-y0 to loc-x0-y0, navigate from loc-x0-y0 to loc-x0-y1, navigate from loc-x0-y1 to loc-x1-y1, navigate from loc-x1-y1 to loc-x2-y1, navigate from loc-x2-y1 to loc-x3-y1\"; can the following action be removed from this plan and still have a valid plan: navigate from loc-x2-y1 to loc-x3-y1?", "answer": "yes"}
{"id": -7531224921640943082, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 4 hoists, 2 crates, 2 depots, 2 distributors, 4 pallets, 2 trucks, numbered consecutively. Currently, crate0, pallet2, crate1, and pallet1 are clear; hoist2, hoist0, hoist1, and hoist3 are available; pallet3 is at distributor1, pallet0 is at depot0, hoist3 is at distributor1, truck0 is at distributor0, hoist1 is at depot1, pallet2 is at distributor0, crate1 is at distributor1, pallet1 is at depot1, truck1 is at depot0, hoist0 is at depot0, crate0 is at depot0, and hoist2 is at distributor0; crate0 is on pallet0 and crate1 is on pallet3. The goal is to reach a state where the following facts hold: crate1 is on pallet0 and crate0 is on crate1.", "question": "Given the plan: \"drive the truck truck0 from distributor0 to distributor1, use the hoist hoist3 to lift the crate crate1 from the surface pallet3 at location distributor1, use hoist hoist3 to load crate crate1 into truck truck0 at place distributor1, drive the truck truck0 from distributor1 to depot0, use the hoist hoist0 to lift the crate crate0 from the surface pallet0 at location depot0, use hoist hoist0 to load crate crate0 into truck truck0 at place depot0, use the hoist hoist0 to unload the crate crate1 from the truck truck0 at location depot0, drop the crate crate1 from the hoist hoist0 onto the surface pallet0 at the place depot0, use the hoist hoist0 to unload the crate crate0 from the truck truck0 at location depot0, drive the truck truck0 from depot0 to distributor0, drive the truck truck0 from distributor0 to distributor1, drop the crate crate0 from the hoist hoist0 onto the surface crate1 at the place depot0\"; can the following action be removed from this plan and still have a valid plan: use the hoist hoist0 to lift the crate crate0 from the surface pallet0 at location depot0?", "answer": "no"}
{"id": -3572210206741853911, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 6 hoists, 3 crates, 4 depots, 2 distributors, 6 pallets, 2 trucks, numbered consecutively. Currently, crate2, crate0, pallet2, crate1, pallet4, and pallet0 are clear; hoist5, hoist2, hoist0, hoist1, hoist3, and hoist4 are available; hoist3 is at depot3, pallet0 is at depot0, pallet5 is at distributor1, truck0 is at distributor1, pallet2 is at depot2, hoist4 is at distributor0, hoist1 is at depot1, hoist2 is at depot2, pallet3 is at depot3, crate2 is at depot1, crate1 is at distributor1, crate0 is at depot3, pallet1 is at depot1, truck1 is at depot0, pallet4 is at distributor0, hoist5 is at distributor1, and hoist0 is at depot0; crate0 is on pallet3, crate1 is on pallet5, and crate2 is on pallet1. The goal is to reach a state where the following facts hold: crate1 is on crate2, crate0 is on pallet1, and crate2 is on pallet4.", "question": "Given the plan: \"lift the crate crate2 from the surface pallet1 at place depot1 using the hoist hoist1, lift the crate crate1 from the surface pallet5 at place distributor1 using the hoist hoist5, drive truck truck0 from place distributor1 to place depot3, lift the crate crate0 from the surface pallet3 at place depot3 using the hoist hoist3, load the crate crate0 from place depot3 with hoist hoist3 into the truck truck0, drive truck truck0 from place depot3 to place distributor1, load the crate crate1 from place distributor1 with hoist hoist5 into the truck truck0, drive truck truck0 from place distributor1 to place depot1, load the crate crate2 from place depot1 with hoist hoist1 into the truck truck0, unload the crate crate0 from the truck truck0 at the place depot1 using the hoist hoist1, drive truck truck0 from place depot1 to place distributor0, unload the crate crate2 from the truck truck0 at the place distributor0 using the hoist hoist4, drop the crate crate2 from the hoist hoist4 onto the surface pallet4 at the place distributor0, drop the crate crate0 from the hoist hoist1 onto the surface pallet1 at the place depot1, unload the crate crate1 from the truck truck0 at the place distributor0 using the hoist hoist4, drop the crate crate1 from the hoist hoist4 onto the surface crate2 at the place distributor0\"; can the following action be removed from this plan and still have a valid plan: unload the crate crate0 from the truck truck0 at the place depot1 using the hoist hoist1?", "answer": "no"}
{"id": 1051590874440443652, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 hoists, 2 crates, 10 depots, 2 distributors, 12 pallets, 2 trucks, numbered consecutively. Currently, pallet11, pallet3, pallet7, pallet5, crate0, pallet10, crate1, pallet1, pallet9, pallet4, pallet0, and pallet6 are clear; hoist8, hoist10, hoist5, hoist11, hoist2, hoist7, hoist9, hoist0, hoist4, hoist6, hoist1, and hoist3 are available; hoist3 is at depot3, truck0 is at depot0, hoist6 is at depot6, pallet0 is at depot0, crate0 is at depot8, pallet2 is at depot2, crate1 is at depot2, pallet6 is at depot6, pallet10 is at distributor0, hoist7 is at depot7, hoist1 is at depot1, hoist2 is at depot2, pallet8 is at depot8, pallet3 is at depot3, pallet7 is at depot7, pallet1 is at depot1, hoist5 is at depot5, hoist4 is at depot4, hoist9 is at depot9, pallet9 is at depot9, hoist0 is at depot0, hoist11 is at distributor1, pallet5 is at depot5, pallet11 is at distributor1, hoist8 is at depot8, truck1 is at depot4, hoist10 is at distributor0, and pallet4 is at depot4; crate0 is on pallet8 and crate1 is on pallet2. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Given the plan: \"navigate the truck truck1 from place depot4 to place depot8, lift crate crate0 from surface pallet8 at place depot8 using hoist hoist8, use hoist hoist8 to load crate crate0 into truck truck1 at place depot8, navigate the truck truck1 from place depot8 to place depot2, lift crate crate1 from surface pallet2 at place depot2 using hoist hoist2, use hoist hoist2 to load crate crate1 into truck truck1 at place depot2, navigate the truck truck1 from place depot2 to place distributor1, use the hoist hoist11 to unload the crate crate0 from the truck truck1 at location distributor1, navigate the truck truck1 from place distributor1 to place depot6, navigate the truck truck1 from place depot6 to place depot9, use the hoist hoist9 to unload the crate crate1 from the truck truck1 at location depot9, lower the crate crate1 from the hoist hoist9 onto the surface pallet9 at the place depot9, lower the crate crate0 from the hoist hoist11 onto the surface pallet11 at the place distributor1\"; can the following action be removed from this plan and still have a valid plan: use the hoist hoist9 to unload the crate crate1 from the truck truck1 at location depot9?", "answer": "no"}
{"id": -1423754374928579924, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 hoists, 2 crates, 10 depots, 2 distributors, 12 pallets, 2 trucks, numbered consecutively. Currently, pallet11, pallet3, pallet7, pallet5, crate0, pallet10, crate1, pallet1, pallet9, pallet4, pallet0, and pallet6 are clear; hoist8, hoist10, hoist5, hoist11, hoist2, hoist7, hoist9, hoist0, hoist4, hoist6, hoist1, and hoist3 are available; hoist3 is at depot3, truck0 is at depot0, hoist6 is at depot6, pallet0 is at depot0, crate0 is at depot8, pallet2 is at depot2, crate1 is at depot2, pallet6 is at depot6, pallet10 is at distributor0, hoist7 is at depot7, hoist1 is at depot1, hoist2 is at depot2, pallet8 is at depot8, pallet3 is at depot3, pallet7 is at depot7, pallet1 is at depot1, hoist5 is at depot5, hoist4 is at depot4, hoist9 is at depot9, pallet9 is at depot9, hoist0 is at depot0, hoist11 is at distributor1, pallet5 is at depot5, pallet11 is at distributor1, hoist8 is at depot8, truck1 is at depot4, hoist10 is at distributor0, and pallet4 is at depot4; crate0 is on pallet8 and crate1 is on pallet2. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Given the plan: \"navigate the truck truck0 from place depot0 to place depot1, navigate the truck truck0 from place depot1 to place depot0, navigate the truck truck0 from place depot0 to place depot2, use the hoist hoist2 to lift the crate crate1 from the surface pallet2 at location depot2, use hoist hoist2 to load crate crate1 into truck truck0 at place depot2, navigate the truck truck0 from place depot2 to place depot8, use the hoist hoist8 to lift the crate crate0 from the surface pallet8 at location depot8, use hoist hoist8 to load crate crate0 into truck truck0 at place depot8, navigate the truck truck0 from place depot8 to place depot9, unload the crate crate1 from the truck truck0 at the place depot9 using the hoist hoist9, navigate the truck truck0 from place depot9 to place depot5, navigate the truck truck0 from place depot5 to place distributor1, unload the crate crate0 from the truck truck0 at the place distributor1 using the hoist hoist11, drop the crate crate0 from the hoist hoist11 onto the surface pallet11 at the place distributor1, drop the crate crate1 from the hoist hoist9 onto the surface pallet9 at the place depot9\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: navigate the truck truck0 from place depot0 to place depot1 and navigate the truck truck0 from place depot1 to place depot0?", "answer": "yes"}
{"id": 5220133523941080896, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 hoists, 2 crates, 7 depots, 2 distributors, 9 pallets, 2 trucks, numbered consecutively. Currently, pallet7, pallet5, crate0, pallet2, crate1, pallet8, pallet4, pallet0, and pallet6 are clear; hoist8, hoist5, hoist2, hoist7, hoist0, hoist4, hoist6, hoist1, and hoist3 are available; hoist3 is at depot3, hoist6 is at depot6, pallet0 is at depot0, pallet2 is at depot2, pallet6 is at depot6, hoist1 is at depot1, crate0 is at depot1, hoist2 is at depot2, pallet8 is at distributor1, pallet3 is at depot3, truck0 is at depot2, pallet1 is at depot1, truck1 is at depot0, hoist5 is at depot5, hoist4 is at depot4, hoist0 is at depot0, pallet5 is at depot5, crate1 is at depot3, pallet7 is at distributor0, hoist7 is at distributor0, hoist8 is at distributor1, and pallet4 is at depot4; crate0 is on pallet1 and crate1 is on pallet3. The goal is to reach a state where the following facts hold: crate1 is on pallet3 and crate0 is on pallet8.", "question": "Given the plan: \"use hoist hoist1 to lift crate crate0 from surface pallet1 at place depot1, navigate the truck truck1 from location depot0 to location depot1, load the crate crate0 into the truck truck1 at the place depot1 with the hoist hoist1, navigate the truck truck1 from location depot1 to location distributor1, use the hoist hoist8 to unload the crate crate0 from the truck truck1 at location distributor1, place the crate crate0 on the surface pallet8 at the place distributor1 using the hoist hoist8\"; can the following action be removed from this plan and still have a valid plan: place the crate crate0 on the surface pallet8 at the place distributor1 using the hoist hoist8?", "answer": "no"}
{"id": 9134780014846199492, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 hoists, 2 crates, 7 depots, 2 distributors, 9 pallets, 2 trucks, numbered consecutively. Currently, pallet7, pallet5, crate0, pallet2, crate1, pallet8, pallet4, pallet0, and pallet6 are clear; hoist8, hoist5, hoist2, hoist7, hoist0, hoist4, hoist6, hoist1, and hoist3 are available; hoist3 is at depot3, hoist6 is at depot6, pallet0 is at depot0, pallet2 is at depot2, pallet6 is at depot6, hoist1 is at depot1, crate0 is at depot1, hoist2 is at depot2, pallet8 is at distributor1, pallet3 is at depot3, truck0 is at depot2, pallet1 is at depot1, truck1 is at depot0, hoist5 is at depot5, hoist4 is at depot4, hoist0 is at depot0, pallet5 is at depot5, crate1 is at depot3, pallet7 is at distributor0, hoist7 is at distributor0, hoist8 is at distributor1, and pallet4 is at depot4; crate0 is on pallet1 and crate1 is on pallet3. The goal is to reach a state where the following facts hold: crate1 is on pallet3 and crate0 is on pallet8.", "question": "Given the plan: \"navigate the truck truck0 from the place depot2 to the place depot1, lift crate crate0 from surface pallet1 at place depot1 using hoist hoist1, load the crate crate0 into the truck truck0 at the place depot1 with the hoist hoist1, navigate the truck truck1 from the place depot0 to the place depot5, navigate the truck truck0 from the place depot1 to the place distributor1, navigate the truck truck1 from the place depot5 to the place depot4, unload the crate crate0 from the truck truck0 at the place distributor1 using the hoist hoist8, drop crate crate0 from hoist hoist8 onto surface pallet8 at place distributor1\"; can the following action be removed from this plan and still have a valid plan: navigate the truck truck1 from the place depot5 to the place depot4?", "answer": "yes"}
{"id": 7390257066645710159, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 hoists, 2 crates, 10 depots, 2 distributors, 12 pallets, 2 trucks, numbered consecutively. Currently, pallet11, pallet3, pallet7, pallet5, crate0, pallet10, crate1, pallet1, pallet9, pallet4, pallet0, and pallet6 are clear; hoist8, hoist10, hoist5, hoist11, hoist2, hoist7, hoist9, hoist0, hoist4, hoist6, hoist1, and hoist3 are available; hoist3 is at depot3, truck0 is at depot0, hoist6 is at depot6, pallet0 is at depot0, crate0 is at depot8, pallet2 is at depot2, crate1 is at depot2, pallet6 is at depot6, pallet10 is at distributor0, hoist7 is at depot7, hoist1 is at depot1, hoist2 is at depot2, pallet8 is at depot8, pallet3 is at depot3, pallet7 is at depot7, pallet1 is at depot1, hoist5 is at depot5, hoist4 is at depot4, hoist9 is at depot9, pallet9 is at depot9, hoist0 is at depot0, hoist11 is at distributor1, pallet5 is at depot5, pallet11 is at distributor1, hoist8 is at depot8, truck1 is at depot4, hoist10 is at distributor0, and pallet4 is at depot4; crate0 is on pallet8 and crate1 is on pallet2. The goal is to reach a state where the following facts hold: crate1 is on pallet9 and crate0 is on pallet11.", "question": "Given the plan: \"navigate the truck truck1 from place depot4 to place depot2, lift crate crate1 from surface pallet2 at place depot2 using hoist hoist2, load crate crate1 into truck truck1 at place depot2 with hoist hoist2, navigate the truck truck1 from place depot2 to place depot8, lift crate crate0 from surface pallet8 at place depot8 using hoist hoist8, load crate crate0 into truck truck1 at place depot8 with hoist hoist8, navigate the truck truck1 from place depot8 to place depot9, use the hoist hoist9 to unload the crate crate1 from the truck truck1 at location depot9, lower the crate crate1 from the hoist hoist9 onto the surface pallet9 at the place depot9, navigate the truck truck1 from place depot9 to place distributor1, use the hoist hoist11 to unload the crate crate0 from the truck truck1 at location distributor1, lower the crate crate0 from the hoist hoist11 onto the surface pallet11 at the place distributor1\"; can the following action be removed from this plan and still have a valid plan: load crate crate1 into truck truck1 at place depot2 with hoist hoist2?", "answer": "no"}
{"id": -4170707118614639190, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 5 hoists, 2 crates, 3 depots, 2 distributors, 5 pallets, 2 trucks, numbered consecutively. Currently, pallet3, pallet2, crate1, pallet4, and pallet0 are clear; hoist2, hoist0, hoist1, hoist3, and hoist4 are available; crate1 is at depot1, pallet0 is at depot0, pallet2 is at depot2, hoist1 is at depot1, crate0 is at depot1, hoist2 is at depot2, truck1 is at distributor0, hoist4 is at distributor1, pallet1 is at depot1, hoist0 is at depot0, hoist3 is at distributor0, pallet4 is at distributor1, pallet3 is at distributor0, and truck0 is at depot1; crate1 is on crate0 and crate0 is on pallet1. The goal is to reach a state where the following facts hold: crate1 is on pallet2 and crate0 is on pallet1.", "question": "Given the plan: \"drive the truck truck1 from distributor0 to depot1, drive the truck truck0 from depot1 to distributor0, use the hoist hoist1 to lift the crate crate1 from the surface crate0 at location depot1, load the crate crate1 from place depot1 with hoist hoist1 into the truck truck1, drive the truck truck1 from depot1 to depot2, unload the crate crate1 from the truck truck1 at the place depot2 using the hoist hoist2, drop the crate crate1 from the hoist hoist2 onto the surface pallet2 at the place depot2\"; can the following action be removed from this plan and still have a valid plan: load the crate crate1 from place depot1 with hoist hoist1 into the truck truck1?", "answer": "no"}
{"id": -4192225067679677274, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 5 hoists, 2 crates, 3 depots, 2 distributors, 5 pallets, 2 trucks, numbered consecutively. Currently, pallet3, pallet2, crate1, pallet4, and pallet0 are clear; hoist2, hoist0, hoist1, hoist3, and hoist4 are available; crate1 is at depot1, pallet0 is at depot0, pallet2 is at depot2, hoist1 is at depot1, crate0 is at depot1, hoist2 is at depot2, truck1 is at distributor0, hoist4 is at distributor1, pallet1 is at depot1, hoist0 is at depot0, hoist3 is at distributor0, pallet4 is at distributor1, pallet3 is at distributor0, and truck0 is at depot1; crate1 is on crate0 and crate0 is on pallet1. The goal is to reach a state where the following facts hold: crate1 is on pallet2 and crate0 is on pallet1.", "question": "Given the plan: \"drive truck truck1 from place distributor0 to place distributor1, lift crate crate1 from surface crate0 at place depot1 using hoist hoist1, drive truck truck1 from place distributor1 to place depot1, load the crate crate1 from place depot1 with hoist hoist1 into the truck truck1, drive truck truck1 from place depot1 to place depot2, unload the crate crate1 from the truck truck1 at the place depot2 using the hoist hoist2, lower the crate crate1 from the hoist hoist2 onto the surface pallet2 at the place depot2\"; can the following action be removed from this plan and still have a valid plan: drive truck truck1 from place distributor0 to place distributor1?", "answer": "no"}
{"id": 3653706118193721188, "group": "action_justification_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 5 hoists, 2 crates, 3 depots, 2 distributors, 5 pallets, 2 trucks, numbered consecutively. Currently, pallet3, pallet2, crate1, pallet4, and pallet0 are clear; hoist2, hoist0, hoist1, hoist3, and hoist4 are available; crate1 is at depot1, pallet0 is at depot0, pallet2 is at depot2, hoist1 is at depot1, crate0 is at depot1, hoist2 is at depot2, truck1 is at distributor0, hoist4 is at distributor1, pallet1 is at depot1, hoist0 is at depot0, hoist3 is at distributor0, pallet4 is at distributor1, pallet3 is at distributor0, and truck0 is at depot1; crate1 is on crate0 and crate0 is on pallet1. The goal is to reach a state where the following facts hold: crate1 is on pallet2 and crate0 is on pallet1.", "question": "Given the plan: \"lift the crate crate1 from the surface crate0 at place depot1 using the hoist hoist1, load the crate crate1 from place depot1 with hoist hoist1 into the truck truck0, navigate the truck truck1 from location distributor0 to location depot2, navigate the truck truck0 from location depot1 to location depot0, navigate the truck truck0 from location depot0 to location depot2, navigate the truck truck1 from location depot2 to location distributor1, use the hoist hoist2 to unload the crate crate1 from the truck truck0 at location depot2, lower the crate crate1 from the hoist hoist2 onto the surface pallet2 at the place depot2\"; can the following action be removed from this plan and still have a valid plan: navigate the truck truck0 from location depot0 to location depot2?", "answer": "no"}
{"id": 6830370408662954840, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-4f, f1-1f, and f1-3f. The following locations have soft rock: f2-4f, f1-2f, f0-1f, f2-3f, f0-3f, f0-4f, f2-2f, f0-2f, and f2-1f. The gold is at f0-4f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up the laser at location f0-0f, aim the laser from location f0-0f to location f0-1f, aim the laser from location f0-0f to location f0-1f, move to location f0-1f from location f0-0f, aim the laser from location f0-1f to location f0-2f, move to location f0-2f from location f0-1f, aim the laser from location f0-2f to location f0-3f, place the laser at location f0-2f, move to location f0-1f from location f0-2f, move to location f0-0f from location f0-1f, pick up the bomb at location f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, move to location f0-3f from location f0-2f, trigger the explosion of the bomb at location f0-3f, which is connected to location f0-4f, move to location f0-4f from location f0-3f, retrieve gold from location f0-4f, move to location f0-3f from location f0-4f\"; can the following action be removed from this plan and still have a valid plan: move to location f0-3f from location f0-4f?", "answer": "yes"}
{"id": -1826411954901360631, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f0-2f and f1-1f. The following locations have soft rock: f1-3f, f2-2f, f1-2f, f0-1f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up the laser at loc f0-0f, aim the laser from location f0-0f to location f0-1f, move to location f0-1f from location f0-0f, aim the laser from location f0-1f to location f0-2f, aim the laser from location f0-1f to location f1-1f, move to location f0-0f from location f0-1f, place the laser at location f0-0f, pick up the bomb at loc f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, trigger the explosion of the bomb at location f0-2f, which is connected to location f0-3f, move to location f0-3f from location f0-2f, pick up gold at loc f0-3f\"; can the following action be removed from this plan and still have a valid plan: aim the laser from location f0-1f to location f1-1f?", "answer": "yes"}
{"id": 7196213546310408428, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f0-2f and f1-1f. The following locations have soft rock: f1-3f, f2-2f, f1-2f, f0-1f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move from loc f2-0f to loc f1-0f, move from loc f1-0f to loc f0-0f, pick up the laser at loc f0-0f, fire the laser from location f0-0f to location f0-1f, fire the laser from location f0-0f to location f0-1f, move from loc f0-0f to loc f0-1f, fire the laser from location f0-1f to location f0-2f, place the laser at location f0-1f, move from loc f0-1f to loc f0-0f, pick up the bomb at location f0-0f, move from loc f0-0f to loc f0-1f, move from loc f0-1f to loc f0-2f, trigger the explosion of the bomb at location f0-2f, which is connected to location f0-3f, move from loc f0-2f to loc f0-3f, pick up the gold from location f0-3f, move from loc f0-3f to loc f0-2f, move from loc f0-2f to loc f0-1f\"; can the following action be removed from this plan and still have a valid plan: move from loc f0-0f to loc f0-1f?", "answer": "no"}
{"id": -3270008089837678575, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-1f, f3-3f, f1-2f, f1-3f, and f3-1f. The following locations have soft rock: f3-2f, f0-1f, f2-3f, f0-3f, f2-2f, f0-2f, and f2-1f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up the bomb at loc f0-0f, detonate bomb at loc f0-0f connected to loc f0-1f, pick up laser at loc f0-0f, move to location f0-1f from location f0-0f, aim the laser from location f0-1f to location f0-2f, move to location f0-0f from location f0-1f, put down laser at loc f0-0f, pick up the bomb at loc f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, detonate bomb at loc f0-2f connected to loc f0-3f, move to location f0-3f from location f0-2f, retrieve gold from location f0-3f\"; can the following action be removed from this plan and still have a valid plan: detonate bomb at loc f0-0f connected to loc f0-1f?", "answer": "no"}
{"id": -8291403248385986065, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-1f, f3-3f, f1-2f, f1-3f, and f3-1f. The following locations have soft rock: f3-2f, f0-1f, f2-3f, f0-3f, f2-2f, f0-2f, and f2-1f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move from loc f2-0f to loc f1-0f, move from loc f1-0f to loc f0-0f, pick up laser at loc f0-0f, fire laser from loc f0-0f to loc f0-1f, move from loc f0-0f to loc f0-1f, put down the laser at location f0-1f, pick up laser at loc f0-1f, fire laser from loc f0-1f to loc f0-2f, put down the laser at location f0-1f, move from loc f0-1f to loc f0-0f, pick up the bomb at location f0-0f, move from loc f0-0f to loc f0-1f, move from loc f0-1f to loc f0-2f, trigger the explosion of the bomb at location f0-2f, which is connected to location f0-3f, move from loc f0-2f to loc f0-3f, pick up gold at loc f0-3f\"; can the following action be removed from this plan and still have a valid plan: move from loc f0-1f to loc f0-0f?", "answer": "no"}
{"id": 4582670661047382133, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f0-3f, f2-2f, and f2-1f. The following locations have soft rock: f1-3f, f0-2f, f1-1f, f1-2f, f0-1f, and f2-3f. The gold is at f1-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move from location f2-0f to location f1-0f, move from location f1-0f to location f0-0f, pick up the laser at location f0-0f, move from location f0-0f to location f1-0f, aim the laser from location f1-0f to location f1-1f, move from location f1-0f to location f1-1f, aim the laser from location f1-1f to location f1-2f, aim the laser from location f1-1f to location f0-1f, move from location f1-1f to location f0-1f, move from location f0-1f to location f0-0f, put down the laser at location f0-0f, pick up bomb at loc f0-0f, move from location f0-0f to location f0-1f, move from location f0-1f to location f1-1f, move from location f1-1f to location f1-2f, trigger the explosion of the bomb at location f1-2f, which is connected to location f1-3f, move from location f1-2f to location f1-3f, pick up the gold from location f1-3f\"; can the following action be removed from this plan and still have a valid plan: trigger the explosion of the bomb at location f1-2f, which is connected to location f1-3f?", "answer": "no"}
{"id": 482796750516259182, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f0-2f and f1-1f. The following locations have soft rock: f1-3f, f2-2f, f1-2f, f0-1f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up laser at loc f0-0f, direct the laser from location f0-0f to location f0-1f, move to location f0-1f from location f0-0f, direct the laser from location f0-1f to location f0-2f, direct the laser from location f0-1f to location f1-1f, place the laser at location f0-1f, move to location f0-0f from location f0-1f, pick up the bomb at location f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, detonate bomb at loc f0-2f connected to loc f0-3f, move to location f0-3f from location f0-2f, move to location f0-2f from location f0-3f, move to location f0-3f from location f0-2f, retrieve gold from location f0-3f\"; can the following action be removed from this plan and still have a valid plan: move to location f1-0f from location f2-0f?", "answer": "no"}
{"id": 3382412964919315245, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f0-2f and f1-1f. The following locations have soft rock: f1-3f, f2-2f, f1-2f, f0-1f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f2-0f from location f1-0f, move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, acquire the laser from location f0-0f, direct the laser from location f0-0f to location f0-1f, move to location f0-1f from location f0-0f, direct the laser from location f0-1f to location f0-2f, move to location f0-0f from location f0-1f, place the laser at location f0-0f, pick up the bomb at location f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, trigger the explosion of the bomb at location f0-2f, which is connected to location f0-3f, move to location f0-3f from location f0-2f, pick up gold at location f0-3f, move to location f0-2f from location f0-3f\"; can the following action be removed from this plan and still have a valid plan: move to location f0-2f from location f0-3f?", "answer": "yes"}
{"id": 721763952103877612, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-4f, f1-1f, and f1-3f. The following locations have soft rock: f2-4f, f1-2f, f0-1f, f2-3f, f0-3f, f0-4f, f2-2f, f0-2f, and f2-1f. The gold is at f0-4f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"move to location f1-0f from location f2-0f, move to location f0-0f from location f1-0f, pick up the laser at loc f0-0f, fire the laser from location f0-0f to location f0-1f, move to location f0-1f from location f0-0f, fire the laser from location f0-1f to location f0-2f, move to location f0-2f from location f0-1f, fire the laser from location f0-2f to location f0-3f, fire the laser from location f0-2f to location f1-2f, move to location f0-1f from location f0-2f, move to location f0-0f from location f0-1f, place the laser at location f0-0f, retrieve the bomb from location f0-0f, move to location f0-1f from location f0-0f, move to location f0-2f from location f0-1f, move to location f0-3f from location f0-2f, trigger the explosion of the bomb at location f0-3f, which is connected to location f0-4f, move to location f0-4f from location f0-3f, pick up gold at location f0-4f, move to location f0-3f from location f0-4f\"; can the following action be removed from this plan and still have a valid plan: fire the laser from location f0-2f to location f1-2f?", "answer": "yes"}
{"id": 4145217687300971887, "group": "action_justification_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f0-3f, f2-2f, and f2-1f. The following locations have soft rock: f1-3f, f0-2f, f1-1f, f1-2f, f0-1f, and f2-3f. The gold is at f1-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold.", "question": "Given the plan: \"travel from location f2-0f to location f1-0f, travel from location f1-0f to location f0-0f, pick up laser at loc f0-0f, fire laser from loc f0-0f to loc f0-1f, travel from location f0-0f to location f1-0f, fire laser from loc f1-0f to loc f1-1f, fire laser from loc f1-0f to loc f1-1f, travel from location f1-0f to location f1-1f, fire laser from loc f1-1f to loc f1-2f, put down laser at loc f1-1f, travel from location f1-1f to location f0-1f, travel from location f0-1f to location f0-0f, pick up bomb at loc f0-0f, travel from location f0-0f to location f1-0f, travel from location f1-0f to location f1-1f, travel from location f1-1f to location f1-2f, detonate bomb at loc f1-2f connected to loc f1-3f, travel from location f1-2f to location f1-3f, pick up the gold from location f1-3f\"; can the following action be removed from this plan and still have a valid plan: travel from location f1-0f to location f1-1f?", "answer": "no"}
{"id": -4048612321761238602, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, planet6, star4, planet5, groundstation3, groundstation0. There are 3 image mode(s): thermograph0, image1, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available and A thermograph0 mode image of target planet5 is available.", "question": "Given the plan: \"power on instrument instrument0 on the satellite satellite0, point the satellite satellite0 to direction star4 instead of groundstation1, calibrate instrument instrument0 on the satellite satellite0 for direction star4, point the satellite satellite0 to direction groundstation3 instead of star4, point the satellite satellite0 to direction planet6 instead of groundstation3, capture an image in direction planet6 in mode infrared2 using the instrument instrument0 on the satellite satellite0, point the satellite satellite0 to direction planet5 instead of planet6, capture an image in direction planet5 in mode thermograph0 using the instrument instrument0 on the satellite satellite0, turn off the instrument instrument0 on board the satellite satellite0\"; can the following action be removed from this plan and still have a valid plan: turn off the instrument instrument0 on board the satellite satellite0?", "answer": "yes"}
{"id": 20508163328247991, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, planet6, star4, planet5, groundstation3, groundstation0. There are 3 image mode(s): thermograph0, image1, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available and A thermograph0 mode image of target planet5 is available.", "question": "Given the plan: \"turn satellite satellite0 to point from groundstation1 direction to planet5, turn satellite satellite0 to point from planet5 direction to groundstation1, switch on instrument instrument0 on the satellite satellite0, turn satellite satellite0 to point from groundstation1 direction to planet6, turn satellite satellite0 to point from planet6 direction to star2, turn satellite satellite0 to point from star2 direction to star4, calibrate instrument instrument0 on the satellite satellite0 for direction star4, turn satellite satellite0 to point from star4 direction to planet6, capture an image of direction planet6 in mode infrared2 using the instrument instrument0 on satellite satellite0, turn satellite satellite0 to point from planet6 direction to planet5, capture an image of direction planet5 in mode thermograph0 using the instrument instrument0 on satellite satellite0\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: turn satellite satellite0 to point from groundstation1 direction to planet5 and turn satellite satellite0 to point from planet5 direction to groundstation1?", "answer": "yes"}
{"id": -6290043019660818644, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, planet6, star4, planet5, groundstation3, groundstation0. There are 3 image mode(s): thermograph0, image1, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite6 has following instruments onboard: instrument10, instrument11, instrument9. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite4 has following instruments onboard: instrument6, instrument7. Satellite satellite1 has following instruments onboard: instrument2, instrument1. Satellite satellite5 has following instruments onboard: instrument8. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite8 has following instruments onboard: instrument13, instrument14. Satellite satellite3 has following instruments onboard: instrument4, instrument5. Satellite satellite7 has following instruments onboard: instrument12. Instrument instrument5 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument7 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument12 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument10 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument8 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument4 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument6 supports image of mode infrared2 and its calibration target is groundstation0. Instrument instrument2 supports image of mode infrared2 and its calibration target is star2. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3.  Currently, Satellite satellite6 is pointing to groundstation1. Satellite satellite5 is pointing to groundstation3. Satellite satellite8 is pointing to star2. Satellite satellite3 is pointing to groundstation1. Satellite satellite0 is pointing to groundstation1. Satellite satellite2 is pointing to planet6. Satellite satellite1 is pointing to groundstation0. Satellite satellite4 is pointing to planet6. Satellite satellite7 is pointing to star2. Satellite satellite9 is pointing to star4. Power is available on the following satellite(s): satellite8, satellite2, satellite0, satellite6, satellite9, satellite5, satellite7, satellite1, satellite3, satellite4. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available, A thermograph0 mode image of target planet5 is available, Satellite satellite8 is pointing to planet6, and Satellite satellite6 is pointing to star4.", "question": "Given the plan: \"direct the satellite satellite8 to point in the direction planet6 instead of star2, direct the satellite satellite6 to point in the direction star4 instead of groundstation1, activate the instrument instrument5 which is on the satellite satellite3, adjust the instrument instrument5 on the satellite satellite3 for direction groundstation1, direct the satellite satellite3 to point in the direction planet6 instead of groundstation1, take an image of direction planet6 in mode infrared2 using instrument instrument5 on board satellite satellite3, direct the satellite satellite3 to point in the direction planet5 instead of planet6, take an image of direction planet5 in mode thermograph0 using instrument instrument5 on board satellite satellite3, activate the instrument instrument13 which is on the satellite satellite8\"; can the following action be removed from this plan and still have a valid plan: activate the instrument instrument13 which is on the satellite satellite8?", "answer": "yes"}
{"id": 7046667637904357957, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, planet6, star4, planet5, groundstation3, groundstation0. There are 3 image mode(s): thermograph0, image1, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available and A thermograph0 mode image of target planet5 is available.", "question": "Given the plan: \"turn on the instrument instrument0 on board the satellite satellite0, change the direction of the satellite satellite0 from groundstation1 to planet5, change the direction of the satellite satellite0 from planet5 to star4, adjust the direction for instrument instrument0 on the satellite satellite0 to star4, change the direction of the satellite satellite0 from star4 to planet5, capture an image in direction planet5 in mode thermograph0 using the instrument instrument0 on the satellite satellite0, change the direction of the satellite satellite0 from planet5 to planet6, capture an image in direction planet6 in mode infrared2 using the instrument instrument0 on the satellite satellite0, change the direction of the satellite satellite0 from planet6 to groundstation1\"; can the following action be removed from this plan and still have a valid plan: capture an image in direction planet6 in mode infrared2 using the instrument instrument0 on the satellite satellite0?", "answer": "no"}
{"id": 193306943933099839, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, groundstation4, star0, planet5, phenomenon6, groundstation3. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument4. Satellite satellite0 has following instruments onboard: instrument1, instrument0, instrument2. Satellite satellite1 has following instruments onboard: instrument3. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument4 supports image of mode infrared1 and its calibration target is star2. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to groundstation4. Satellite satellite2 is pointing to star2. Satellite satellite1 is pointing to groundstation3. Power is available on the following satellite(s): satellite2, satellite0, satellite1. The goal is to reach a state where the following facts hold: A spectrograph0 mode image of target phenomenon6 is available, Satellite satellite1 is pointing to phenomenon6, and A thermograph2 mode image of target planet5 is available.", "question": "Given the plan: \"turn satellite satellite1 from direction groundstation3 to direction phenomenon6, turn on the instrument instrument4 on board the satellite satellite2, point the instrument instrument4 on the satellite satellite2 to direction star2, turn satellite satellite2 from direction star2 to direction phenomenon6, capture a image of direction phenomenon6 in mode spectrograph0 using the instrument instrument4 on the satellite satellite2, turn satellite satellite2 from direction phenomenon6 to direction planet5, capture a image of direction planet5 in mode thermograph2 using the instrument instrument4 on the satellite satellite2, turn satellite satellite0 from direction groundstation4 to direction planet5\"; can the following action be removed from this plan and still have a valid plan: capture a image of direction planet5 in mode thermograph2 using the instrument instrument4 on the satellite satellite2?", "answer": "no"}
{"id": 2596369097557016442, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, planet6, star4, planet5, groundstation3, groundstation0. There are 3 image mode(s): thermograph0, image1, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available and A thermograph0 mode image of target planet5 is available.", "question": "Given the plan: \"turn the satellite satellite0 from direction groundstation1 to direction planet5, turn the satellite satellite0 from direction planet5 to direction groundstation1, turn on the instrument instrument0 on board the satellite satellite0, turn the satellite satellite0 from direction groundstation1 to direction star4, point the instrument instrument0 on the satellite satellite0 to direction star4, turn the satellite satellite0 from direction star4 to direction planet5, capture an image of direction planet5 in mode thermograph0 using the instrument instrument0 on satellite satellite0, turn the satellite satellite0 from direction planet5 to direction star4, turn the satellite satellite0 from direction star4 to direction planet6, capture an image of direction planet6 in mode infrared2 using the instrument instrument0 on satellite satellite0\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: turn the satellite satellite0 from direction groundstation1 to direction planet5 and turn the satellite satellite0 from direction planet5 to direction groundstation1?", "answer": "yes"}
{"id": 428595516947545534, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): phenomenon5, star6, star1, groundstation4, groundstation2, star3, groundstation0. There are 3 image mode(s): image2, image0, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite3 has following instruments onboard: instrument8, instrument7. Satellite satellite0 has following instruments onboard: instrument1, instrument0, instrument2. Satellite satellite1 has following instruments onboard: instrument4, instrument5, instrument3. Satellite satellite4 has following instruments onboard: instrument9, instrument10, instrument11. Satellite satellite6 has following instruments onboard: instrument17, instrument16, instrument15. Satellite satellite5 has following instruments onboard: instrument13, instrument12, instrument14. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument14 supports image of mode image1 and its calibration target is groundstation4. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument6 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument8 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument2 supports image of mode image1 and its calibration target is star1. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument5 supports image of mode image1 and its calibration target is star3. Instrument instrument7 supports image of mode image1 and its calibration target is star3. Instrument instrument13 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0.  Currently, Satellite satellite1 is pointing to phenomenon5. Satellite satellite6 is pointing to groundstation0. Satellite satellite2 is pointing to groundstation2. Satellite satellite3 is pointing to star6. Satellite satellite5 is pointing to phenomenon5. Satellite satellite0 is pointing to groundstation2. Satellite satellite4 is pointing to star1. Power is available on the following satellite(s): satellite3, satellite2, satellite0, satellite6, satellite4, satellite1, satellite5. The goal is to reach a state where the following facts hold: A image1 mode image of target star6 is available, A image0 mode image of target phenomenon5 is available, Satellite satellite1 is pointing to phenomenon5, Satellite satellite5 is pointing to groundstation0, and Satellite satellite4 is pointing to star1.", "question": "Given the plan: \"change the direction of the satellite satellite5 from phenomenon5 to groundstation0, switch on instrument instrument6 on board satellite satellite2, adjust the instrument instrument6 on the satellite satellite2 for direction groundstation2, change the direction of the satellite satellite2 from groundstation2 to phenomenon5, take an image of direction phenomenon5 in mode image0 using instrument instrument6 on board satellite satellite2, change the direction of the satellite satellite2 from phenomenon5 to star6, take an image of direction star6 in mode image1 using instrument instrument6 on board satellite satellite2, change the direction of the satellite satellite0 from groundstation2 to groundstation4\"; can the following action be removed from this plan and still have a valid plan: change the direction of the satellite satellite5 from phenomenon5 to groundstation0?", "answer": "no"}
{"id": -8346296507874456290, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, planet6, star4, planet5, groundstation3, groundstation0. There are 3 image mode(s): thermograph0, image1, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available and A thermograph0 mode image of target planet5 is available.", "question": "Given the plan: \"turn on the instrument instrument0 on board the satellite satellite0, point the satellite satellite0 from groundstation1 direction to new direction star4, calibrate instrument instrument0 on the satellite satellite0 for direction star4, calibrate instrument instrument0 on the satellite satellite0 for direction star4, point the satellite satellite0 from star4 direction to new direction planet5, capture an image in direction planet5 in mode thermograph0 using the instrument instrument0 on the satellite satellite0, point the satellite satellite0 from planet5 direction to new direction planet6, capture an image in direction planet6 in mode infrared2 using the instrument instrument0 on the satellite satellite0, point the satellite satellite0 from planet6 direction to new direction planet5\"; can the following action be removed from this plan and still have a valid plan: point the satellite satellite0 from planet6 direction to new direction planet5?", "answer": "yes"}
{"id": -4993312540406227093, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, planet6, star4, planet5, groundstation3, groundstation0. There are 3 image mode(s): thermograph0, image1, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available and A thermograph0 mode image of target planet5 is available.", "question": "Given the plan: \"turn on the instrument instrument0 on the satellite satellite0, turn satellite satellite0 to point from groundstation1 direction to star4, adjust the direction for instrument instrument0 on the satellite satellite0 to star4, turn satellite satellite0 to point from star4 direction to planet6, capture an image of direction planet6 in mode infrared2 using the instrument instrument0 on satellite satellite0, turn satellite satellite0 to point from planet6 direction to planet5, capture an image of direction planet5 in mode thermograph0 using the instrument instrument0 on satellite satellite0, turn satellite satellite0 to point from planet5 direction to star2, turn satellite satellite0 to point from star2 direction to groundstation0\"; can the following action be removed from this plan and still have a valid plan: turn satellite satellite0 to point from planet5 direction to star2?", "answer": "no"}
{"id": -905183918628274492, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, planet6, star4, planet5, groundstation3, groundstation0. There are 3 image mode(s): thermograph0, image1, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available and A thermograph0 mode image of target planet5 is available.", "question": "Given the plan: \"activate the instrument instrument0 which is on the satellite satellite0, turn satellite satellite0 to point from groundstation1 direction to star4, calibrate instrument instrument0 on the satellite satellite0 for direction star4, turn satellite satellite0 to point from star4 direction to planet6, capture a image of direction planet6 in mode infrared2 using the instrument instrument0 on the satellite satellite0, turn satellite satellite0 to point from planet6 direction to planet5, capture a image of direction planet5 in mode thermograph0 using the instrument instrument0 on the satellite satellite0, turn satellite satellite0 to point from planet5 direction to groundstation1, turn satellite satellite0 to point from groundstation1 direction to groundstation3\"; can the following action be removed from this plan and still have a valid plan: turn satellite satellite0 to point from star4 direction to planet6?", "answer": "no"}
{"id": 2102032783078088455, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 4 agents: zoe, bob, steve, and vic. There are 4 items/roles: book03, book04, book01, and book02. Currently, bob is assigned book01, zoe is assigned book04, vic is assigned book03, and steve is assigned book02. The goal is to reach a state where the following facts hold: bob is assigned book02, vic is assigned book04, steve is assigned book03, and zoe is assigned book01.", "question": "Given the plan: \"swap steve:book02 with vic:book03, swap zoe:book04 with steve:book03, swap bob:book01 with vic:book02, swap vic:book01 with steve:book04, swap steve:book01 with zoe:book03\"; can the following action be removed from this plan and still have a valid plan: swap vic:book01 with steve:book04?", "answer": "no"}
{"id": -5891027429848956335, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 4 agents: zoe, bob, steve, and vic. There are 4 items/roles: book03, book04, book01, and book02. Currently, bob is assigned book01, zoe is assigned book04, vic is assigned book03, and steve is assigned book02. The goal is to reach a state where the following facts hold: bob is assigned book02, vic is assigned book04, steve is assigned book03, and zoe is assigned book01.", "question": "Given the plan: \"trade book03 of vic for book04 of zoe, trade book03 of zoe for book04 of vic, trade book02 of steve for book01 of bob, trade book01 of steve for book04 of zoe, trade book03 of vic for book04 of steve\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: trade book03 of vic for book04 of zoe and trade book03 of zoe for book04 of vic?", "answer": "yes"}
{"id": 7531037506289764247, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 3 agents: grace, xena, and judy. There are 3 items/roles: funnel, xactoknife, and sander. Currently, judy is assigned funnel, grace is assigned xactoknife, and xena is assigned sander. The goal is to reach a state where the following facts hold: judy is assigned xactoknife, xena is assigned funnel, and grace is assigned sander.", "question": "Given the plan: \"exchange sander of xena with funnel of judy, exchange funnel of xena with xactoknife of grace, exchange xactoknife of xena with funnel of grace, exchange sander of judy with xactoknife of grace\"; can the following action be removed from this plan and still have a valid plan: exchange sander of judy with xactoknife of grace?", "answer": "no"}
{"id": -4711839525485731131, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: xena, zoe, michelle, alice, carol, dave, vic, and heidi. There are 8 items/roles: necklace, zebra, slinky, guitar, iceskates, quadcopter, whale, and frisbee. Currently, zoe is assigned frisbee, heidi is assigned necklace, dave is assigned slinky, alice is assigned iceskates, vic is assigned quadcopter, xena is assigned whale, michelle is assigned zebra, and carol is assigned guitar. The goal is to reach a state where the following facts hold: zoe is assigned whale, michelle is assigned quadcopter, vic is assigned necklace, carol is assigned frisbee, heidi is assigned guitar, dave is assigned iceskates, xena is assigned slinky, and alice is assigned zebra.", "question": "Given the plan: \"exchange quadcopter of vic with whale of xena, exchange whale of vic with quadcopter of xena, exchange iceskates of alice with slinky of dave, exchange whale of xena with slinky of alice, exchange frisbee of zoe with whale of alice, exchange necklace of heidi with guitar of carol, exchange frisbee of alice with zebra of michelle, exchange necklace of carol with quadcopter of vic, exchange quadcopter of carol with frisbee of michelle\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: exchange quadcopter of vic with whale of xena and exchange whale of vic with quadcopter of xena?", "answer": "yes"}
{"id": 5927907804443383825, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: xena, alice, bob, dave, ted, heidi, and kevin. There are 7 items/roles: mushroom, parsnip, quince, ulluco, leek, yam, and valerian. Currently, heidi is assigned quince, bob is assigned leek, ted is assigned ulluco, xena is assigned parsnip, alice is assigned yam, kevin is assigned mushroom, and dave is assigned valerian. The goal is to reach a state where the following facts hold: bob is assigned parsnip, xena is assigned quince, heidi is assigned mushroom, kevin is assigned yam, alice is assigned valerian, dave is assigned ulluco, and ted is assigned leek.", "question": "Given the plan: \"exchange leek of bob with parsnip of xena, exchange valerian of dave with leek of xena, exchange leek of dave with ulluco of ted, exchange yam of alice with quince of heidi, exchange mushroom of kevin with yam of heidi, exchange valerian of xena with quince of alice\"; can the following action be removed from this plan and still have a valid plan: exchange leek of bob with parsnip of xena?", "answer": "no"}
{"id": 6721424506265291166, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: xena, alice, bob, dave, ted, heidi, and kevin. There are 7 items/roles: mushroom, parsnip, quince, ulluco, leek, yam, and valerian. Currently, heidi is assigned quince, bob is assigned leek, ted is assigned ulluco, xena is assigned parsnip, alice is assigned yam, kevin is assigned mushroom, and dave is assigned valerian. The goal is to reach a state where the following facts hold: bob is assigned parsnip, xena is assigned quince, heidi is assigned mushroom, kevin is assigned yam, alice is assigned valerian, dave is assigned ulluco, and ted is assigned leek.", "question": "Given the plan: \"swap bob with ted, leek for ulluco, swap ted with bob, leek for ulluco, swap dave with alice, valerian for yam, swap bob with dave, leek for yam, swap kevin with heidi, mushroom for quince, swap kevin with bob, quince for yam, swap dave with ted, leek for ulluco, swap bob with xena, quince for parsnip\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: swap bob with ted, leek for ulluco and swap ted with bob, leek for ulluco?", "answer": "yes"}
{"id": -3420810492104366399, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 4 agents: zoe, bob, steve, and vic. There are 4 items/roles: book03, book04, book01, and book02. Currently, bob is assigned book01, zoe is assigned book04, vic is assigned book03, and steve is assigned book02. The goal is to reach a state where the following facts hold: bob is assigned book02, vic is assigned book04, steve is assigned book03, and zoe is assigned book01.", "question": "Given the plan: \"exchange book04 of zoe with book02 of steve, exchange book04 of steve with book02 of zoe, exchange book02 of steve with book03 of vic, exchange book02 of vic with book01 of bob, exchange book04 of zoe with book01 of vic\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: exchange book04 of zoe with book02 of steve and exchange book04 of steve with book02 of zoe?", "answer": "yes"}
{"id": 5113069776237607197, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: xena, liam, bob, vic, frank, and quentin. There are 6 items/roles: wrench, sander, knead, pliers, ratchet, and nibbler. Currently, vic is assigned nibbler, frank is assigned sander, liam is assigned knead, bob is assigned wrench, xena is assigned pliers, and quentin is assigned ratchet. The goal is to reach a state where the following facts hold: bob is assigned nibbler, liam is assigned sander, vic is assigned ratchet, frank is assigned pliers, quentin is assigned knead, and xena is assigned wrench.", "question": "Given the plan: \"trade ratchet of quentin for nibbler of vic, trade nibbler of quentin for knead of liam, trade pliers of xena for wrench of bob, trade sander of frank for pliers of bob, trade nibbler of liam for sander of bob\"; can the following action be removed from this plan and still have a valid plan: trade pliers of xena for wrench of bob?", "answer": "no"}
{"id": 8573929718351450571, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: xena, liam, bob, vic, frank, and quentin. There are 6 items/roles: wrench, sander, knead, pliers, ratchet, and nibbler. Currently, vic is assigned nibbler, frank is assigned sander, liam is assigned knead, bob is assigned wrench, xena is assigned pliers, and quentin is assigned ratchet. The goal is to reach a state where the following facts hold: bob is assigned nibbler, liam is assigned sander, vic is assigned ratchet, frank is assigned pliers, quentin is assigned knead, and xena is assigned wrench.", "question": "Given the plan: \"trade nibbler of vic for ratchet of quentin, trade nibbler of quentin for ratchet of vic, trade knead of liam for nibbler of vic, trade knead of vic for ratchet of quentin, trade pliers of xena for wrench of bob, trade sander of frank for nibbler of liam, trade nibbler of frank for pliers of bob\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: trade nibbler of vic for ratchet of quentin and trade nibbler of quentin for ratchet of vic?", "answer": "yes"}
{"id": -2663677181136765059, "group": "action_justification_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: xena, liam, bob, vic, frank, and quentin. There are 6 items/roles: wrench, sander, knead, pliers, ratchet, and nibbler. Currently, vic is assigned nibbler, frank is assigned sander, liam is assigned knead, bob is assigned wrench, xena is assigned pliers, and quentin is assigned ratchet. The goal is to reach a state where the following facts hold: bob is assigned nibbler, liam is assigned sander, vic is assigned ratchet, frank is assigned pliers, quentin is assigned knead, and xena is assigned wrench.", "question": "Given the plan: \"exchange nibbler of vic with ratchet of quentin, exchange nibbler of quentin with knead of liam, exchange pliers of xena with wrench of bob, exchange sander of frank with pliers of bob, exchange nibbler of liam with sander of bob\"; can the following action be removed from this plan and still have a valid plan: exchange sander of frank with pliers of bob?", "answer": "no"}
{"id": 146672430259489784, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet1 is at location20. stoveburner2 and stoveburner4 are at location22. sinkbasin1 is at location7. coffeemachine1 and countertop2 are at location12. drawer2 is at location16. toaster1 is at location10. shelf1 is at location18. microwave1 is at location25. cabinet3 is at location17. cabinet4 is at location15. shelf2 is at location30. countertop3 is at location4. stoveburner3 and stoveburner1 are at location6. drawer1 is at location21. cabinet6 is at location5. cabinet2 is at location23. fridge1 is at location11. cabinet5 is at location14. shelf3 is at location27. countertop1 is at location3. drawer3 is at location28. garbagecan1 is at location9.  Currently, the objects are at locations as follows. papertowelroll1, soapbottle2, and glassbottle1 are at location9. pot1 is at location22. fork2, soapbottle3, statue2, butterknife1, spoon2, cellphone3, knife1, spatula3, butterknife2, peppershaker2, and houseplant1 are at location4. lettuce1, tomato1, tomato2, potato1, potato2, egg1, cup2, and cup1 are at location11. spatula1 is at location28. creditcard2, glassbottle2, bowl1, and mug2 are at location30. cellphone1, apple3, dishsponge3, apple2, bread1, statue1, apple1, creditcard1, and spatula2 are at location3. fork1, egg2, cup3, sink1, tomato3, and potato3 are at location7. cellphone2 is at location21. lightswitch1 is at location26. dishsponge1 and spoon1 are at location16. window2 is at location29. vase2 is at location18. creditcard3 and saltshaker1 are at location27. soapbottle1 and vase1 are at location5. stoveknob1 is at location19. window1 is at location31. chair1 is at location24. chair2 is at location1. peppershaker1 is at location20. stoveknob3 and stoveknob2 are at location13. pan1 is at location6. plate2 and plate1 are at location14. stoveknob4 is at location8. mug1 is at location25. dishsponge2 is at location2. agent agent1 is at location location32. The objects are in/on receptacle as follows. bowl1, mug2, glassbottle2, and creditcard2 are on shelf2. fork1, egg2, cup3, tomato3, and potato3 are in sinkbasin1. plate1, dishsponge2, and plate2 are in cabinet5. statue1, bread1, spatula2, dishsponge3, cellphone1, apple2, apple3, apple1, and creditcard1 are on countertop1. knife1, peppershaker2, butterknife2, soapbottle3, spatula3, cellphone3, statue2, houseplant1, spoon2, butterknife1, and fork2 are on countertop3. creditcard3 and saltshaker1 are on shelf3. vase2 is on shelf1. peppershaker1 is in cabinet1. spoon1 and dishsponge1 are in drawer2. soapbottle2, papertowelroll1, and glassbottle1 are in garbagecan1. cup1, egg1, cup2, potato2, lettuce1, tomato2, potato1, and tomato1 are in fridge1. dishsponge2 is on plate1. cellphone2 is in drawer1. pan1 is on stoveburner1. spatula1 is in drawer3. pan1 is on stoveburner3. soapbottle1 and vase1 are in cabinet6. mug1 is in microwave1. pot1 is on stoveburner2. pot1 is on stoveburner4. cabinet1, drawer1, drawer2, fridge1, cabinet5, drawer3, microwave1, and cabinet2 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype.", "question": "Given the plan: \"agent agent1 moves from the current position location32 to the next position location21 that has the receptacle drawer1, agent agent1 moves from the current position location21 to the next position location27 that has the receptacle shelf3, agent agent1 grasps object saltshaker1 from receptacle shelf3 while at location location27, agent agent1 moves from the current position location27 to the next position location5 that has the receptacle cabinet6, agent agent1 places object saltshaker1 with type saltshakertype in a receptacle cabinet6 with type cabinettype at location location5, check that object saltshaker1 of type saltshakertype is in a receptacle cabinet6 of type cabinettype\"; can the following action be removed from this plan and still have a valid plan: agent agent1 places object saltshaker1 with type saltshakertype in a receptacle cabinet6 with type cabinettype at location location5?", "answer": "no"}
{"id": *******************, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop2 and coffeemachine1 are at location11. stoveburner4 and stoveburner2 are at location21. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. cabinet1 is at location19. drawer2 is at location15. cabinet6 is at location4. drawer3 is at location27. cabinet4 is at location14. countertop1 is at location2. shelf1 is at location17. shelf2 is at location29. cabinet5 is at location13. stoveburner3 and stoveburner1 are at location5. toaster1 is at location9. shelf3 is at location26. drawer1 is at location20. fridge1 is at location10. countertop3 is at location3. sinkbasin1 is at location6. garbagecan1 is at location8.  Currently, the objects are at locations as follows. pan1 and spoon1 are at location11. apple1, cellphone1, plate1, peppershaker1, cellphone2, lettuce1, knife1, and lettuce2 are at location2. spoon2, mug2, knife2, houseplant1, glassbottle1, bread1, papertowelroll1, plate3, spatula1, butterknife1, cellphone3, and creditcard1 are at location3. mug1, bowl1, egg1, plate2, tomato1, egg2, and potato1 are at location10. stoveknob3 and stoveknob2 are at location12. potato2, sink1, spatula3, spatula2, and glassbottle2 are at location6. fork1 and dishsponge1 are at location27. pot1 is at location5. dishsponge2 and vase1 are at location13. peppershaker2 is at location20. apple2, glassbottle3, and potato3 are at location8. soapbottle2 and statue1 are at location26. lightswitch1 is at location25. saltshaker1 is at location15. window2 is at location28. stoveknob1 is at location18. chair2 is at location1. stoveknob4 is at location7. chair1 is at location23. bowl2 is at location29. vase2 is at location17. cup1 is at location24. peppershaker3 is at location16. soapbottle1 is at location4. window1 is at location30. agent agent1 is at location location31. The objects are in/on receptacle as follows. cellphone2, lettuce1, lettuce2, cellphone1, peppershaker1, apple1, plate1, and knife1 are on countertop1. glassbottle2, spatula2, potato2, and spatula3 are in sinkbasin1. dishsponge2 and vase1 are in cabinet5. saltshaker1 is in drawer2. soapbottle2 and statue1 are on shelf3. egg2, mug1, egg1, plate2, bowl1, potato1, and tomato1 are in fridge1. pan1 is on stoveburner4. spatula1, papertowelroll1, plate3, mug2, knife2, creditcard1, glassbottle1, bread1, cellphone3, houseplant1, spoon2, and butterknife1 are on countertop3. pan1 and spoon1 are on countertop2. peppershaker2 is in drawer1. glassbottle3, potato3, and apple2 are in garbagecan1. pot1 is on stoveburner1. vase2 is on shelf1. fork1 and dishsponge1 are in drawer3. cup1 is in microwave1. bowl2 is on shelf2. soapbottle1 is in cabinet6. peppershaker3 is in cabinet3. pot1 is on stoveburner3. pan1 is on stoveburner2. cellphone2 is on plate1. cabinet1, drawer1, drawer2, fridge1, cabinet5, drawer3, microwave1, and cabinet2 are closed. mug1 is cool. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type mugtype is hot and is in a receptacle of type coffeemachinetype.", "question": "Given the plan: \"agent agent1 navigates to the receptacle microwave1 from the current location location31 to the next location location24, agent agent1 navigates to the receptacle countertop3 from the current location location24 to the next location location3, agent agent1 collects object mug2 from receptacle countertop3 while at location location3, agent agent1 navigates to the receptacle microwave1 from the current location location3 to the next location location24, agent agent1 warms up object mug2 with a microwave microwave1 at location location24, agent agent1 warms up object mug2 with a microwave microwave1 at location location24, agent agent1 warms up object mug2 with a microwave microwave1 at location location24, agent agent1 navigates to the receptacle coffeemachine1 from the current location location24 to the next location location11, agent agent1 puts down an object mug2 of type mugtype in a receptacle coffeemachine1 of type coffeemachinetype that is at location location11, ensure that object mug2 of type mugtype is hot and in receptacle coffeemachine1 of type coffeemachinetype\"; can the following action be removed from this plan and still have a valid plan: agent agent1 warms up object mug2 with a microwave microwave1 at location location24?", "answer": "yes"}
{"id": 1532946796535048058, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. handtowelholder1 is at location18. handtowelholder2 is at location17. garbagecan1 is at location2. cabinet4 is at location15. cabinet3 is at location8. toilet1 is at location7. countertop1 is at location3. towelholder1 is at location5. sinkbasin2 is at location6. sinkbasin1 is at location16. toiletpaperhanger1 is at location10. cabinet2 is at location11. cabinet1 is at location4.  Currently, the objects are at locations as follows. soapbar1 and toiletpaper1 are at location7. plunger1 and scrubbrush1 are at location10. handtowel2 is at location17. spraybottle2 and soapbottle1 are at location15. cloth1, candle1, and soapbar2 are at location3. sink2 is at location14. toiletpaper2 is at location8. sink1 is at location12. mirror1 is at location9. cloth2 is at location11. handtowel1 is at location18. showerdoor1 is at location1. spraybottle1 and soapbottle2 are at location4. showerglass1 and towel1 are at location5. lightswitch1 is at location13. spraybottle3 is at location2. agent agent1 is at location location19. The objects are in/on receptacle as follows. soapbottle1 and spraybottle2 are in cabinet4. soapbottle2 and spraybottle1 are in cabinet1. toiletpaper1 and soapbar1 are in toilet1. candle1, cloth1, and soapbar2 are on countertop1. cloth2 is in cabinet2. towel1 is on towelholder1. handtowel1 is on handtowelholder1. handtowel2 is on handtowelholder2. toiletpaper2 is in cabinet3. spraybottle3 is in garbagecan1. cabinet1, cabinet4, cabinet3, and cabinet2 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type soapbartype is clean and is in a receptacle of type cabinettype.", "question": "Given the plan: \"agent agent1 goes to receptacle countertop1 from the current location location19 to the next location location3, agent agent1 picks up object soapbar2 from receptacle countertop1 that is at location location3, agent agent1 goes to receptacle cabinet2 from the current location location3 to the next location location11, agent agent1 goes to receptacle sinkbasin2 from the current location location11 to the next location location6, agent agent1 goes to receptacle sinkbasin1 from the current location location6 to the next location location16, agent agent1 cleans an object soapbar2 in a sink sinkbasin1 that is in location location16, agent agent1 goes to receptacle cabinet2 from the current location location16 to the next location location11, agent agent1 goes to receptacle cabinet4 from the current location location11 to the next location location15, agent agent1 opens receptacle cabinet4 while at location location15, agent agent1 puts down an object soapbar2 with type soapbartype in a receptacle cabinet4 with type cabinettype at location location15, validate that object soapbar2 of type soapbartype is clean and in receptacle cabinet4 of type cabinettype\"; can the following action be removed from this plan and still have a valid plan: agent agent1 goes to receptacle cabinet2 from the current location location3 to the next location location11?", "answer": "no"}
{"id": -3057328468786367428, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop2 and coffeemachine1 are at location11. stoveburner4 and stoveburner2 are at location21. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. cabinet1 is at location19. drawer2 is at location15. cabinet6 is at location4. drawer3 is at location27. cabinet4 is at location14. countertop1 is at location2. shelf1 is at location17. shelf2 is at location29. cabinet5 is at location13. stoveburner3 and stoveburner1 are at location5. toaster1 is at location9. shelf3 is at location26. drawer1 is at location20. fridge1 is at location10. countertop3 is at location3. sinkbasin1 is at location6. garbagecan1 is at location8.  Currently, the objects are at locations as follows. pan1 and spoon1 are at location11. apple1, cellphone1, plate1, peppershaker1, cellphone2, lettuce1, knife1, and lettuce2 are at location2. spoon2, mug2, knife2, houseplant1, glassbottle1, bread1, papertowelroll1, plate3, spatula1, butterknife1, cellphone3, and creditcard1 are at location3. mug1, bowl1, egg1, plate2, tomato1, egg2, and potato1 are at location10. stoveknob3 and stoveknob2 are at location12. potato2, sink1, spatula3, spatula2, and glassbottle2 are at location6. fork1 and dishsponge1 are at location27. pot1 is at location5. dishsponge2 and vase1 are at location13. peppershaker2 is at location20. apple2, glassbottle3, and potato3 are at location8. soapbottle2 and statue1 are at location26. lightswitch1 is at location25. saltshaker1 is at location15. window2 is at location28. stoveknob1 is at location18. chair2 is at location1. stoveknob4 is at location7. chair1 is at location23. bowl2 is at location29. vase2 is at location17. cup1 is at location24. peppershaker3 is at location16. soapbottle1 is at location4. window1 is at location30. agent agent1 is at location location31. The objects are in/on receptacle as follows. cellphone2, lettuce1, lettuce2, cellphone1, peppershaker1, apple1, plate1, and knife1 are on countertop1. glassbottle2, spatula2, potato2, and spatula3 are in sinkbasin1. dishsponge2 and vase1 are in cabinet5. saltshaker1 is in drawer2. soapbottle2 and statue1 are on shelf3. egg2, mug1, egg1, plate2, bowl1, potato1, and tomato1 are in fridge1. pan1 is on stoveburner4. spatula1, papertowelroll1, plate3, mug2, knife2, creditcard1, glassbottle1, bread1, cellphone3, houseplant1, spoon2, and butterknife1 are on countertop3. pan1 and spoon1 are on countertop2. peppershaker2 is in drawer1. glassbottle3, potato3, and apple2 are in garbagecan1. pot1 is on stoveburner1. vase2 is on shelf1. fork1 and dishsponge1 are in drawer3. cup1 is in microwave1. bowl2 is on shelf2. soapbottle1 is in cabinet6. peppershaker3 is in cabinet3. pot1 is on stoveburner3. pan1 is on stoveburner2. cellphone2 is on plate1. cabinet1, drawer1, drawer2, fridge1, cabinet5, drawer3, microwave1, and cabinet2 are closed. mug1 is cool. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type mugtype is hot and is in a receptacle of type coffeemachinetype.", "question": "Given the plan: \"agent agent1 moves from the current position location31 to the next position location11 that has the receptacle coffeemachine1, agent agent1 moves from the current position location11 to the next position location3 that has the receptacle countertop3, agent agent1 moves from the current position location3 to the next position location6 that has the receptacle sinkbasin1, agent agent1 moves from the current position location6 to the next position location3 that has the receptacle countertop3, agent agent1 collects object mug2 from receptacle countertop3 while at location location3, agent agent1 moves from the current position location3 to the next position location11 that has the receptacle coffeemachine1, agent agent1 moves from the current position location11 to the next position location24 that has the receptacle microwave1, agent agent1 heats up object mug2 with a microwave microwave1 at location location24, agent agent1 moves from the current position location24 to the next position location11 that has the receptacle coffeemachine1, agent agent1 places object mug2 with type mugtype in a receptacle coffeemachine1 with type coffeemachinetype at location location11, check that object mug2 of type mugtype is warmed up and in receptacle coffeemachine1 of type coffeemachinetype\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: agent agent1 moves from the current position location3 to the next position location6 that has the receptacle sinkbasin1 and agent agent1 moves from the current position location6 to the next position location3 that has the receptacle countertop3?", "answer": "yes"}
{"id": -5736473591703056007, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. shelf6 is at location24. drawer3 is at location17. shelf4 is at location23. garbagecan1 is at location2. drawer5 and drawer4 are at location12. shelf3 is at location11. drawer2 is at location18. desk1 is at location3. bed1 is at location13. shelf5 is at location22. laundryhamper1 is at location8. desk2 is at location10. shelf2 is at location25. drawer1 is at location21. drawer6 is at location1. safe1 is at location6. shelf1 is at location20.  Currently, the objects are at locations as follows. pencil3, cd3, pen1, mug2, and cellphone2 are at location10. bowl3 is at location24. bowl1, cd1, mug1, alarmclock1, and pencil1 are at location3. alarmclock2 is at location11. pillow1, cellphone1, laptop1, laptop2, pillow2, and book1 are at location13. alarmclock3, desklamp1, and bowl2 are at location23. chair1 is at location21. window1 is at location5. keychain1 and keychain2 are at location6. chair2 is at location26. cellphone3 is at location12. lightswitch1 is at location14. pencil2 and creditcard1 are at location22. baseballbat1 is at location9. blinds1 is at location16. cd2 is at location2. mirror1 is at location19. window2 is at location4. blinds2 is at location15. basketball1 is at location7. laundryhamperlid1 is at location8. agent agent1 is at location location27. The objects are in/on receptacle as follows. cellphone1, laptop2, laptop1, pillow1, book1, and pillow2 are in bed1. creditcard1 and pencil2 are on shelf5. alarmclock2 is on shelf3. alarmclock3, pencil3, pen1, bowl2, mug2, cellphone2, desklamp1, and cd3 are on desk2. cd1, pencil1, bowl1, mug1, and alarmclock1 are on desk1. bowl3 is on shelf6. alarmclock3, bowl2, and desklamp1 are on shelf4. cd2 is in garbagecan1. keychain2 and keychain1 are in safe1. cellphone3 is in drawer5. drawer3, safe1, drawer1, and drawer6 are closed. desklamp1 is off. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type booktype is examined under an object of type desklamptype.", "question": "Given the plan: \"agent agent1 navigates to the receptacle bed1 from the current location location27 to the next location location13, agent agent1 navigates to the receptacle shelf1 from the current location location13 to the next location location20, agent agent1 navigates to the receptacle bed1 from the current location location20 to the next location location13, agent agent1 collects object book1 from receptacle bed1 that is at location location13, agent agent1 navigates to the receptacle drawer6 from the current location location13 to the next location location1, agent agent1 navigates to the receptacle desk2 from the current location location1 to the next location location10, agent agent1 navigates to the receptacle shelf4 from the current location location10 to the next location location23, agent agent1 toggles a togglable object desklamp1 that is on the receptacle shelf4 at location location23, validate that togglable object desklamp1 of type desklamptype is toggled and in receptacle shelf4 at location location23 while agent agent1 is holding object book1 of type booktype\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: agent agent1 navigates to the receptacle shelf1 from the current location location13 to the next location location20 and agent agent1 navigates to the receptacle bed1 from the current location location20 to the next location location13?", "answer": "yes"}
{"id": 6625409156980340616, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop2 and coffeemachine1 are at location11. stoveburner4 and stoveburner2 are at location21. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. cabinet1 is at location19. drawer2 is at location15. cabinet6 is at location4. drawer3 is at location27. cabinet4 is at location14. countertop1 is at location2. shelf1 is at location17. shelf2 is at location29. cabinet5 is at location13. stoveburner3 and stoveburner1 are at location5. toaster1 is at location9. shelf3 is at location26. drawer1 is at location20. fridge1 is at location10. countertop3 is at location3. sinkbasin1 is at location6. garbagecan1 is at location8.  Currently, the objects are at locations as follows. spoon3, lettuce3, knife1, saltshaker2, soapbottle1, tomato1, houseplant1, glassbottle3, butterknife1, and fork2 are at location3. dishsponge1 is at location15. pan1, peppershaker1, and cellphone1 are at location11. creditcard1, creditcard2, statue1, bread1, spoon1, lettuce2, mug1, and cellphone3 are at location2. cellphone2 and peppershaker2 are at location20. stoveknob3 and stoveknob2 are at location12. papertowelroll1 and vase2 are at location29. saltshaker3 is at location26. apple1, lettuce1, potato2, potato1, cup2, and cup1 are at location10. plate1 is at location4. pot1 is at location5. sink1, fork1, spatula2, and spoon2 are at location6. apple2, egg1, and egg2 are at location8. spatula1 is at location27. lightswitch1 is at location25. window2 is at location28. stoveknob1 is at location18. chair2 is at location1. stoveknob4 is at location7. chair1 is at location23. glassbottle1 is at location19. saltshaker1 is at location16. glassbottle2 and bowl1 are at location14. vase1 is at location17. window1 is at location30. agent agent1 is at location location31. The objects are in/on receptacle as follows. fork1, spatula2, and spoon2 are in sinkbasin1. soapbottle1, knife1, spoon3, saltshaker2, houseplant1, lettuce3, tomato1, butterknife1, fork2, and glassbottle3 are on countertop3. vase1 is on shelf1. statue1, bread1, lettuce2, creditcard2, cellphone3, spoon1, mug1, and creditcard1 are on countertop1. pan1 is on stoveburner4. cellphone1, pan1, and peppershaker1 are on countertop2. peppershaker2 and cellphone2 are in drawer1. vase2 and papertowelroll1 are on shelf2. pot1 is on stoveburner1. saltshaker1 is in cabinet3. egg1, egg2, and apple2 are in garbagecan1. plate1 is in cabinet6. cup1, cup2, potato2, apple1, lettuce1, and potato1 are in fridge1. glassbottle2 and bowl1 are in cabinet4. spatula1 is in drawer3. saltshaker3 is on shelf3. pot1 is on stoveburner3. pan1 is on stoveburner2. dishsponge1 is in drawer2. glassbottle1 is in cabinet1. cabinet1, drawer1, drawer2, fridge1, cabinet5, drawer3, cabinet2, and microwave1 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Given the plan: \"agent agent1 navigates to the receptacle garbagecan1 from the current location location31 to the next location location8, agent agent1 collects object egg1 from receptacle garbagecan1 that is at location location8, agent agent1 navigates to the receptacle drawer1 from the current location location8 to the next location location20, agent agent1 navigates to the receptacle coffeemachine1 from the current location location20 to the next location location11, agent agent1 navigates to the receptacle garbagecan1 from the current location location11 to the next location location8, agent agent1 navigates to the receptacle microwave1 from the current location location8 to the next location location24, agent agent1 heats up object egg1 with a microwave microwave1 that is in location location24, agent agent1 navigates to the receptacle garbagecan1 from the current location location24 to the next location location8, agent agent1 places object egg1 with type eggtype in a receptacle garbagecan1 with type garbagecantype at location location8, check that object egg1 of type eggtype is hot and in receptacle garbagecan1 of type garbagecantype\"; can the following action be removed from this plan and still have a valid plan: agent agent1 navigates to the receptacle microwave1 from the current location location8 to the next location location24?", "answer": "no"}
{"id": -6264628563946099607, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. shelf6 is at location24. drawer3 is at location17. shelf4 is at location23. garbagecan1 is at location2. drawer5 and drawer4 are at location12. shelf3 is at location11. drawer2 is at location18. desk1 is at location3. bed1 is at location13. shelf5 is at location22. laundryhamper1 is at location8. desk2 is at location10. shelf2 is at location25. drawer1 is at location21. drawer6 is at location1. safe1 is at location6. shelf1 is at location20.  Currently, the objects are at locations as follows. pencil3, cd3, pen1, mug2, and cellphone2 are at location10. bowl3 is at location24. bowl1, cd1, mug1, alarmclock1, and pencil1 are at location3. alarmclock2 is at location11. pillow1, cellphone1, laptop1, laptop2, pillow2, and book1 are at location13. alarmclock3, desklamp1, and bowl2 are at location23. chair1 is at location21. window1 is at location5. keychain1 and keychain2 are at location6. chair2 is at location26. cellphone3 is at location12. lightswitch1 is at location14. pencil2 and creditcard1 are at location22. baseballbat1 is at location9. blinds1 is at location16. cd2 is at location2. mirror1 is at location19. window2 is at location4. blinds2 is at location15. basketball1 is at location7. laundryhamperlid1 is at location8. agent agent1 is at location location27. The objects are in/on receptacle as follows. cellphone1, laptop2, laptop1, pillow1, book1, and pillow2 are in bed1. creditcard1 and pencil2 are on shelf5. alarmclock2 is on shelf3. alarmclock3, pencil3, pen1, bowl2, mug2, cellphone2, desklamp1, and cd3 are on desk2. cd1, pencil1, bowl1, mug1, and alarmclock1 are on desk1. bowl3 is on shelf6. alarmclock3, bowl2, and desklamp1 are on shelf4. cd2 is in garbagecan1. keychain2 and keychain1 are in safe1. cellphone3 is in drawer5. drawer3, safe1, drawer1, and drawer6 are closed. desklamp1 is off. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type booktype is examined under an object of type desklamptype.", "question": "Given the plan: \"agent agent1 navigates from the current position location27 to the next position location13 that has a receptacle bed1, agent agent1 grasps object book1 from receptacle bed1 while at location location13, agent agent1 navigates from the current position location13 to the next position location23 that has a receptacle shelf4, agent agent1 turns on object desklamp1 that is on the receptacle shelf4 at location location23, agent agent1 turns off object desklamp1 that is on the receptacle shelf4 at location location23, agent agent1 turns on object desklamp1 that is on the receptacle shelf4 at location location23, agent agent1 turns off object desklamp1 that is on the receptacle shelf4 at location location23, ensure that object desklamp1 of type desklamptype is toggled and in receptacle shelf4 at location location23 while agent agent1 is holding object book1 of type booktype\"; can the following action be removed from this plan and still have a valid plan: agent agent1 grasps object book1 from receptacle bed1 while at location location13?", "answer": "no"}
{"id": 2038181437345155360, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet1 is at location20. stoveburner2 and stoveburner4 are at location22. sinkbasin1 is at location7. coffeemachine1 and countertop2 are at location12. drawer2 is at location16. toaster1 is at location10. shelf1 is at location18. microwave1 is at location25. cabinet3 is at location17. cabinet4 is at location15. shelf2 is at location30. countertop3 is at location4. stoveburner3 and stoveburner1 are at location6. drawer1 is at location21. cabinet6 is at location5. cabinet2 is at location23. fridge1 is at location11. cabinet5 is at location14. shelf3 is at location27. countertop1 is at location3. drawer3 is at location28. garbagecan1 is at location9.  Currently, the objects are at locations as follows. papertowelroll1, soapbottle2, and glassbottle1 are at location9. pot1 is at location22. fork2, soapbottle3, statue2, butterknife1, spoon2, cellphone3, knife1, spatula3, butterknife2, peppershaker2, and houseplant1 are at location4. lettuce1, tomato1, tomato2, potato1, potato2, egg1, cup2, and cup1 are at location11. spatula1 is at location28. creditcard2, glassbottle2, bowl1, and mug2 are at location30. cellphone1, apple3, dishsponge3, apple2, bread1, statue1, apple1, creditcard1, and spatula2 are at location3. fork1, egg2, cup3, sink1, tomato3, and potato3 are at location7. cellphone2 is at location21. lightswitch1 is at location26. dishsponge1 and spoon1 are at location16. window2 is at location29. vase2 is at location18. creditcard3 and saltshaker1 are at location27. soapbottle1 and vase1 are at location5. stoveknob1 is at location19. window1 is at location31. chair1 is at location24. chair2 is at location1. peppershaker1 is at location20. stoveknob3 and stoveknob2 are at location13. pan1 is at location6. plate2 and plate1 are at location14. stoveknob4 is at location8. mug1 is at location25. dishsponge2 is at location2. agent agent1 is at location location32. The objects are in/on receptacle as follows. bowl1, mug2, glassbottle2, and creditcard2 are on shelf2. fork1, egg2, cup3, tomato3, and potato3 are in sinkbasin1. plate1, dishsponge2, and plate2 are in cabinet5. statue1, bread1, spatula2, dishsponge3, cellphone1, apple2, apple3, apple1, and creditcard1 are on countertop1. knife1, peppershaker2, butterknife2, soapbottle3, spatula3, cellphone3, statue2, houseplant1, spoon2, butterknife1, and fork2 are on countertop3. creditcard3 and saltshaker1 are on shelf3. vase2 is on shelf1. peppershaker1 is in cabinet1. spoon1 and dishsponge1 are in drawer2. soapbottle2, papertowelroll1, and glassbottle1 are in garbagecan1. cup1, egg1, cup2, potato2, lettuce1, tomato2, potato1, and tomato1 are in fridge1. dishsponge2 is on plate1. cellphone2 is in drawer1. pan1 is on stoveburner1. spatula1 is in drawer3. pan1 is on stoveburner3. soapbottle1 and vase1 are in cabinet6. mug1 is in microwave1. pot1 is on stoveburner2. pot1 is on stoveburner4. cabinet1, drawer1, drawer2, fridge1, cabinet5, drawer3, microwave1, and cabinet2 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type saltshakertype is in a receptacle of type cabinettype.", "question": "Given the plan: \"agent agent1 navigates to the receptacle fridge1 from the current location location32 to the next location location11, agent agent1 navigates to the receptacle drawer1 from the current location location11 to the next location location21, agent agent1 navigates to the receptacle shelf3 from the current location location21 to the next location location27, agent agent1 picks up object saltshaker1 from receptacle shelf3 while at location location27, agent agent1 navigates to the receptacle cabinet4 from the current location location27 to the next location location15, agent agent1 places object saltshaker1 with type saltshakertype in a receptacle cabinet4 with type cabinettype at location location15, ensure that object saltshaker1 of type saltshakertype is in a receptacle cabinet4 of type cabinettype\"; can the following action be removed from this plan and still have a valid plan: agent agent1 places object saltshaker1 with type saltshakertype in a receptacle cabinet4 with type cabinettype at location location15?", "answer": "no"}
{"id": -*******************, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. shelf6 is at location24. drawer3 is at location17. shelf4 is at location23. garbagecan1 is at location2. drawer5 and drawer4 are at location12. shelf3 is at location11. drawer2 is at location18. desk1 is at location3. bed1 is at location13. shelf5 is at location22. laundryhamper1 is at location8. desk2 is at location10. shelf2 is at location25. drawer1 is at location21. drawer6 is at location1. safe1 is at location6. shelf1 is at location20.  Currently, the objects are at locations as follows. pencil3, cd3, pen1, mug2, and cellphone2 are at location10. bowl3 is at location24. bowl1, cd1, mug1, alarmclock1, and pencil1 are at location3. alarmclock2 is at location11. pillow1, cellphone1, laptop1, laptop2, pillow2, and book1 are at location13. alarmclock3, desklamp1, and bowl2 are at location23. chair1 is at location21. window1 is at location5. keychain1 and keychain2 are at location6. chair2 is at location26. cellphone3 is at location12. lightswitch1 is at location14. pencil2 and creditcard1 are at location22. baseballbat1 is at location9. blinds1 is at location16. cd2 is at location2. mirror1 is at location19. window2 is at location4. blinds2 is at location15. basketball1 is at location7. laundryhamperlid1 is at location8. agent agent1 is at location location27. The objects are in/on receptacle as follows. cellphone1, laptop2, laptop1, pillow1, book1, and pillow2 are in bed1. creditcard1 and pencil2 are on shelf5. alarmclock2 is on shelf3. alarmclock3, pencil3, pen1, bowl2, mug2, cellphone2, desklamp1, and cd3 are on desk2. cd1, pencil1, bowl1, mug1, and alarmclock1 are on desk1. bowl3 is on shelf6. alarmclock3, bowl2, and desklamp1 are on shelf4. cd2 is in garbagecan1. keychain2 and keychain1 are in safe1. cellphone3 is in drawer5. drawer3, safe1, drawer1, and drawer6 are closed. desklamp1 is off. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type booktype is examined under an object of type desklamptype.", "question": "Given the plan: \"agent agent1 goes to receptacle bed1 from the current location location27 to the next location location13, agent agent1 goes to receptacle shelf1 from the current location location13 to the next location location20, agent agent1 goes to receptacle bed1 from the current location location20 to the next location location13, agent agent1 collects object book1 from receptacle bed1 while at location location13, agent agent1 goes to receptacle drawer1 from the current location location13 to the next location location21, agent agent1 goes to receptacle desk2 from the current location location21 to the next location location10, agent agent1 turns on object desklamp1 that is on the receptacle desk2 at location location10, validate that togglable object desklamp1 of type desklamptype is toggled and in receptacle desk2 at location location10 while agent agent1 is holding object book1 of type booktype\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: agent agent1 goes to receptacle shelf1 from the current location location13 to the next location location20 and agent agent1 goes to receptacle bed1 from the current location location20 to the next location location13?", "answer": "yes"}
{"id": 8897837736915869020, "group": "action_justification_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop2 and coffeemachine1 are at location11. stoveburner4 and stoveburner2 are at location21. microwave1 is at location24. cabinet3 is at location16. cabinet2 is at location22. cabinet1 is at location19. drawer2 is at location15. cabinet6 is at location4. drawer3 is at location27. cabinet4 is at location14. countertop1 is at location2. shelf1 is at location17. shelf2 is at location29. cabinet5 is at location13. stoveburner3 and stoveburner1 are at location5. toaster1 is at location9. shelf3 is at location26. drawer1 is at location20. fridge1 is at location10. countertop3 is at location3. sinkbasin1 is at location6. garbagecan1 is at location8.  Currently, the objects are at locations as follows. spoon3, lettuce3, knife1, saltshaker2, soapbottle1, tomato1, houseplant1, glassbottle3, butterknife1, and fork2 are at location3. dishsponge1 is at location15. pan1, peppershaker1, and cellphone1 are at location11. creditcard1, creditcard2, statue1, bread1, spoon1, lettuce2, mug1, and cellphone3 are at location2. cellphone2 and peppershaker2 are at location20. stoveknob3 and stoveknob2 are at location12. papertowelroll1 and vase2 are at location29. saltshaker3 is at location26. apple1, lettuce1, potato2, potato1, cup2, and cup1 are at location10. plate1 is at location4. pot1 is at location5. sink1, fork1, spatula2, and spoon2 are at location6. apple2, egg1, and egg2 are at location8. spatula1 is at location27. lightswitch1 is at location25. window2 is at location28. stoveknob1 is at location18. chair2 is at location1. stoveknob4 is at location7. chair1 is at location23. glassbottle1 is at location19. saltshaker1 is at location16. glassbottle2 and bowl1 are at location14. vase1 is at location17. window1 is at location30. agent agent1 is at location location31. The objects are in/on receptacle as follows. fork1, spatula2, and spoon2 are in sinkbasin1. soapbottle1, knife1, spoon3, saltshaker2, houseplant1, lettuce3, tomato1, butterknife1, fork2, and glassbottle3 are on countertop3. vase1 is on shelf1. statue1, bread1, lettuce2, creditcard2, cellphone3, spoon1, mug1, and creditcard1 are on countertop1. pan1 is on stoveburner4. cellphone1, pan1, and peppershaker1 are on countertop2. peppershaker2 and cellphone2 are in drawer1. vase2 and papertowelroll1 are on shelf2. pot1 is on stoveburner1. saltshaker1 is in cabinet3. egg1, egg2, and apple2 are in garbagecan1. plate1 is in cabinet6. cup1, cup2, potato2, apple1, lettuce1, and potato1 are in fridge1. glassbottle2 and bowl1 are in cabinet4. spatula1 is in drawer3. saltshaker3 is on shelf3. pot1 is on stoveburner3. pan1 is on stoveburner2. dishsponge1 is in drawer2. glassbottle1 is in cabinet1. cabinet1, drawer1, drawer2, fridge1, cabinet5, drawer3, cabinet2, and microwave1 are closed. Nothing has been validated. agent1's hands are empty. The goal is to reach a state where the following facts hold: It has been validated that an object of type eggtype is hot and is in a receptacle of type garbagecantype.", "question": "Given the plan: \"agent agent1 goes to receptacle garbagecan1 from the current location location31 to the next location location8, agent agent1 collects object egg1 from receptacle garbagecan1 that is at location location8, agent agent1 goes to receptacle coffeemachine1 from the current location location8 to the next location location11, agent agent1 goes to receptacle garbagecan1 from the current location location11 to the next location location8, agent agent1 goes to receptacle coffeemachine1 from the current location location8 to the next location location11, agent agent1 goes to receptacle garbagecan1 from the current location location11 to the next location location8, agent agent1 goes to receptacle microwave1 from the current location location8 to the next location location24, agent agent1 heats up object egg1 with a microwave microwave1 at location location24, agent agent1 goes to receptacle garbagecan1 from the current location location24 to the next location location8, agent agent1 places object egg1 of type eggtype in a receptacle garbagecan1 of type garbagecantype while at location location8, validate that object egg1 of type eggtype is hot and in receptacle garbagecan1 of type garbagecantype\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: agent agent1 goes to receptacle garbagecan1 from the current location location11 to the next location location8 and agent agent1 goes to receptacle coffeemachine1 from the current location location8 to the next location location11?", "answer": "yes"}
