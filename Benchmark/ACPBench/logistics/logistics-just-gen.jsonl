{"id": 660232409932127055, "group": "action_justification_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l2-1, l2-0, and l2-2 are in c2; l3-1, l3-2, and l3-0 are in c3; l4-1, l4-2, and l4-0 are in c4; l1-0, l1-1, and l1-2 are in c1; l0-2, l0-0, and l0-1 are in c0. Currently, p3 is at l4-1, p2 is at l2-1, t1 is at l1-2, t0 is at l0-2, t3 is at l3-2, p0 and t2 are at l2-2, t4 is at l4-0, p1 is at l3-1, a0 is at l2-0. The available actions are: (load-truck ?obj ?truck ?loc) - load object ?obj into truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - place the object ?obj onto the airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - remove the object ?obj from the truck ?truck and place it on the location ?loc, (unload-airplane ?obj ?airplane ?loc) - remove the object ?obj from the airplane ?airplane and place it on the location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive truck ?truck from location ?loc-from in city ?city to location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from location ?loc-from to location ?loc-to. The goal is to reach a state where the following facts hold: p3 is at l3-2, p0 is at l3-0, p2 is at l2-2, and p1 is at l3-2.", "question": "Simplify the plan \"(DRIVE-TRUCK t1 l1-2 l1-1 c1) (DRIVE-TRUCK t1 l1-1 l1-2 c1) (DRIVE-TRUCK t3 l3-2 l3-1 c3) (LOAD-TRUCK p1 t3 l3-1) (DRIVE-TRUCK t3 l3-1 l3-0 c3) (LOAD-TRUCK p0 t2 l2-2) (DRIVE-TRUCK t2 l2-2 l2-1 c2) (LOAD-TRUCK p2 t2 l2-1) (DRIVE-TRUCK t2 l2-1 l2-0 c2) (UNLOAD-TRUCK p0 t2 l2-0) (LOAD-AIRPLANE p0 a0 l2-0) (FLY-AIRPLANE a0 l2-0 l4-0) (DRIVE-TRUCK t4 l4-0 l4-1 c4) (LOAD-TRUCK p3 t4 l4-1) (DRIVE-TRUCK t4 l4-1 l4-0 c4) (UNLOAD-TRUCK p3 t4 l4-0) (LOAD-AIRPLANE p3 a0 l4-0) (FLY-AIRPLANE a0 l4-0 l0-0) (FLY-AIRPLANE a0 l0-0 l3-0) (UNLOAD-AIRPLANE p3 a0 l3-0) (LOAD-TRUCK p3 t3 l3-0) (DRIVE-TRUCK t3 l3-0 l3-2 c3) (UNLOAD-TRUCK p1 t3 l3-2) (UNLOAD-TRUCK p3 t3 l3-2) (DRIVE-TRUCK t2 l2-0 l2-2 c2) (UNLOAD-TRUCK p2 t2 l2-2) (UNLOAD-AIRPLANE p0 a0 l3-0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(DRIVE-TRUCK t1 l1-2 l1-1 c1)", "(DRIVE-TRUCK t1 l1-1 l1-2 c1)", "-1"]], "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 l3-1 l3-2 l4-1 l4-2 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l2-0) (at p0 l2-2) (at p1 l3-1) (at p2 l2-1) (at p3 l4-1) (at t0 l0-2) (at t1 l1-2) (at t2 l2-2) (at t3 l3-2) (at t4 l4-0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4))\n    (:goal (and (at p0 l3-0) (at p1 l3-2) (at p2 l2-2) (at p3 l3-2)))\n)"}
{"id": -1894923116739782933, "group": "action_justification_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l0-2, l0-0, and l0-1 are in c0. Currently, p1 and p3 are at l0-2, p0 and t0 are at l0-0, t1 is at l1-1, p2 and a0 are at l1-0. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load object ?obj into airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - remove the object ?obj from the airplane ?airplane and place it on the location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive truck ?truck from location ?loc-from in city ?city to location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - operate the airplane ?airplane from airport ?loc-from to airport ?loc-to. The goal is to reach a state where the following facts hold: p2 is at l0-1, p1 is at l0-1, p3 is at l0-1, and p0 is at l0-1.", "question": "Simplify the plan \"(DRIVE-TRUCK t1 l1-1 l1-2 c1) (LOAD-AIRPLANE p2 a0 l1-0) (FLY-AIRPLANE a0 l1-0 l0-0) (UNLOAD-AIRPLANE p2 a0 l0-0) (LOAD-TRUCK p0 t0 l0-0) (LOAD-TRUCK p2 t0 l0-0) (DRIVE-TRUCK t0 l0-0 l0-2 c0) (LOAD-TRUCK p1 t0 l0-2) (LOAD-TRUCK p3 t0 l0-2) (DRIVE-TRUCK t0 l0-2 l0-1 c0) (UNLOAD-TRUCK p2 t0 l0-1) (UNLOAD-TRUCK p0 t0 l0-1) (UNLOAD-TRUCK p1 t0 l0-1) (UNLOAD-TRUCK p3 t0 l0-1) (DRIVE-TRUCK t1 l1-2 l1-0 c1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(DRIVE-TRUCK t1 l1-2 l1-0 c1)", "1"]], "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l1-0) (at p0 l0-0) (at p1 l0-2) (at p2 l1-0) (at p3 l0-2) (at t0 l0-0) (at t1 l1-1) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l0-1) (at p1 l0-1) (at p2 l0-1) (at p3 l0-1)))\n)"}
{"id": 5236807843879344993, "group": "action_justification_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l0-2, l0-0, and l0-1 are in c0. Currently, p1 and p3 are at l0-2, p0 and t0 are at l0-0, t1 is at l1-1, p2 and a0 are at l1-0. The available actions are: (load-truck ?obj ?truck ?loc) - load object ?obj into truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - place the object ?obj onto the airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload object ?obj from airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive the truck ?truck in city ?city from location ?loc-from to location ?loc-to, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from airport ?loc-from to airport ?loc-to. The goal is to reach a state where the following facts hold: p2 is at l0-1, p1 is at l0-1, p3 is at l0-1, and p0 is at l0-1.", "question": "Simplify the plan \"(LOAD-AIRPLANE p2 a0 l1-0) (FLY-AIRPLANE a0 l1-0 l0-0) (UNLOAD-AIRPLANE p2 a0 l0-0) (LOAD-TRUCK p2 t0 l0-0) (LOAD-TRUCK p0 t0 l0-0) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (UNLOAD-TRUCK p2 t0 l0-1) (DRIVE-TRUCK t0 l0-1 l0-2 c0) (LOAD-TRUCK p3 t0 l0-2) (LOAD-TRUCK p1 t0 l0-2) (DRIVE-TRUCK t0 l0-2 l0-1 c0) (UNLOAD-TRUCK p3 t0 l0-1) (UNLOAD-TRUCK p0 t0 l0-1) (UNLOAD-TRUCK p1 t0 l0-1) (DRIVE-TRUCK t0 l0-1 l0-0 c0) (DRIVE-TRUCK t1 l1-1 l1-0 c1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(DRIVE-TRUCK t0 l0-1 l0-0 c0)", "1"], ["(DRIVE-TRUCK t1 l1-1 l1-0 c1)", "1"], ["(DRIVE-TRUCK t0 l0-1 l0-0 c0)", "(DRIVE-TRUCK t1 l1-1 l1-0 c1)", "-1"]], "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l1-0) (at p0 l0-0) (at p1 l0-2) (at p2 l1-0) (at p3 l0-2) (at t0 l0-0) (at t1 l1-1) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l0-1) (at p1 l0-1) (at p2 l0-1) (at p3 l0-1)))\n)"}
{"id": 7699146924930504746, "group": "action_justification_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-0 and l0-1 are in c0. Currently, t1 and p1 are at l1-0, a0, t0, and p3 are at l0-0, p2 and p0 are at l0-1. The available actions are: (load-truck ?obj ?truck ?loc) - load object ?obj into truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - load object ?obj into airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - remove the object ?obj from the airplane ?airplane and place it on the location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive truck ?truck from location ?loc-from in city ?city to location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from the airport ?loc-from to the airport ?loc-to. The goal is to reach a state where the following facts hold: p1 is at l1-1, p0 is at l1-1, p3 is at l1-1, and p2 is at l1-0.", "question": "Simplify the plan \"(LOAD-TRUCK p1 t1 l1-0) (LOAD-AIRPLANE p3 a0 l0-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p1 t1 l1-1) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (LOAD-TRUCK p0 t0 l0-1) (LOAD-TRUCK p2 t0 l0-1) (DRIVE-TRUCK t0 l0-1 l0-0 c0) (UNLOAD-TRUCK p0 t0 l0-0) (UNLOAD-TRUCK p2 t0 l0-0) (LOAD-AIRPLANE p0 a0 l0-0) (LOAD-AIRPLANE p2 a0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (UNLOAD-AIRPLANE p0 a0 l1-0) (UNLOAD-AIRPLANE p2 a0 l1-0) (LOAD-TRUCK p1 t1 l1-1) (UNLOAD-AIRPLANE p3 a0 l1-0) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (LOAD-TRUCK p0 t1 l1-0) (UNLOAD-TRUCK p1 t1 l1-0) (LOAD-TRUCK p3 t1 l1-0) (LOAD-TRUCK p1 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p0 t1 l1-1) (UNLOAD-TRUCK p3 t1 l1-1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (UNLOAD-TRUCK p1 t1 l1-0) (LOAD-TRUCK p1 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p1 t1 l1-1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(UNLOAD-TRUCK p1 t1 l1-0)", "(LOAD-TRUCK p1 t1 l1-0)", "-1"]], "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l0-1) (at p1 l1-0) (at p2 l0-1) (at p3 l0-0) (at t0 l0-0) (at t1 l1-0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-1)))\n)"}
{"id": 5623733796636229031, "group": "action_justification_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l2-1, l2-0, and l2-2 are in c2; l1-0, l1-1, and l1-2 are in c1; l0-2, l0-0, and l0-1 are in c0. Currently, p0, a0, and t0 are at l0-0, t1 is at l1-1, t2 is at l2-1, p4 is at l1-0, p2 is at l0-1, p3 and p1 are at l2-0. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load object ?obj into airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - unload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - offload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from location ?loc-from in city ?city to location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly airplane ?airplane from airport ?loc-from to airport ?loc-to. The goal is to reach a state where the following facts hold: p2 is at l1-2, p3 is at l1-2, p1 is at l1-0, p0 is at l1-2, and p4 is at l0-0.", "question": "Simplify the plan \"(DRIVE-TRUCK t1 l1-1 l1-0 c1) (LOAD-AIRPLANE p0 a0 l0-0) (FLY-AIRPLANE a0 l0-0 l2-0) (LOAD-AIRPLANE p1 a0 l2-0) (LOAD-AIRPLANE p3 a0 l2-0) (FLY-AIRPLANE a0 l2-0 l1-0) (UNLOAD-AIRPLANE p0 a0 l1-0) (LOAD-TRUCK p0 t1 l1-0) (UNLOAD-AIRPLANE p1 a0 l1-0) (UNLOAD-AIRPLANE p3 a0 l1-0) (LOAD-TRUCK p3 t1 l1-0) (LOAD-AIRPLANE p4 a0 l1-0) (DRIVE-TRUCK t1 l1-0 l1-2 c1) (UNLOAD-TRUCK p0 t1 l1-2) (UNLOAD-TRUCK p3 t1 l1-2) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (LOAD-TRUCK p2 t0 l0-1) (DRIVE-TRUCK t0 l0-1 l0-0 c0) (FLY-AIRPLANE a0 l1-0 l0-0) (UNLOAD-AIRPLANE p4 a0 l0-0) (DRIVE-TRUCK t0 l0-0 l0-2 c0) (UNLOAD-TRUCK p2 t0 l0-2) (LOAD-TRUCK p2 t0 l0-2) (DRIVE-TRUCK t0 l0-2 l0-0 c0) (UNLOAD-TRUCK p2 t0 l0-0) (LOAD-AIRPLANE p2 a0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (UNLOAD-AIRPLANE p2 a0 l1-0) (DRIVE-TRUCK t1 l1-2 l1-0 c1) (LOAD-TRUCK p2 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-2 c1) (UNLOAD-TRUCK p2 t1 l1-2)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(UNLOAD-TRUCK p2 t0 l0-2)", "(LOAD-TRUCK p2 t0 l0-2)", "-1"]], "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c3-s3-p5-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 - airport c0 c1 c2 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 - location p0 p1 p2 p3 p4 - package t0 t1 t2 - truck)\n    (:init (at a0 l0-0) (at p0 l0-0) (at p1 l2-0) (at p2 l0-1) (at p3 l2-0) (at p4 l1-0) (at t0 l0-0) (at t1 l1-1) (at t2 l2-1) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2))\n    (:goal (and (at p0 l1-2) (at p1 l1-0) (at p2 l1-2) (at p3 l1-2) (at p4 l0-0)))\n)"}
{"id": -7987764247229666976, "group": "action_justification_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l2-1, l2-0, and l2-2 are in c2; l3-1, l3-2, and l3-0 are in c3; l4-1, l4-2, and l4-0 are in c4; l1-0, l1-1, and l1-2 are in c1; l0-2, l0-0, and l0-1 are in c0. Currently, p3 is at l4-1, p2 is at l2-1, t1 is at l1-2, t0 is at l0-2, t3 is at l3-2, p0 and t2 are at l2-2, t4 is at l4-0, p1 is at l3-1, a0 is at l2-0. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc into the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - remove the object ?obj from the truck ?truck and place it on the location ?loc, (unload-airplane ?obj ?airplane ?loc) - offload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck which is in location ?loc-from in city ?city to another location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from the airport ?loc-from to the airport ?loc-to. The goal is to reach a state where the following facts hold: p3 is at l3-2, p0 is at l3-0, p2 is at l2-2, and p1 is at l3-2.", "question": "Simplify the plan \"(DRIVE-TRUCK t3 l3-2 l3-1 c3) (LOAD-TRUCK p1 t3 l3-1) (DRIVE-TRUCK t3 l3-1 l3-0 c3) (LOAD-TRUCK p0 t2 l2-2) (DRIVE-TRUCK t2 l2-2 l2-1 c2) (LOAD-TRUCK p2 t2 l2-1) (DRIVE-TRUCK t2 l2-1 l2-0 c2) (UNLOAD-TRUCK p0 t2 l2-0) (LOAD-AIRPLANE p0 a0 l2-0) (DRIVE-TRUCK t2 l2-0 l2-2 c2) (UNLOAD-TRUCK p2 t2 l2-2) (FLY-AIRPLANE a0 l2-0 l4-0) (DRIVE-TRUCK t4 l4-0 l4-1 c4) (LOAD-TRUCK p3 t4 l4-1) (DRIVE-TRUCK t4 l4-1 l4-0 c4) (UNLOAD-TRUCK p3 t4 l4-0) (LOAD-AIRPLANE p3 a0 l4-0) (FLY-AIRPLANE a0 l4-0 l3-0) (UNLOAD-AIRPLANE p0 a0 l3-0) (UNLOAD-AIRPLANE p3 a0 l3-0) (LOAD-TRUCK p3 t3 l3-0) (DRIVE-TRUCK t3 l3-0 l3-2 c3) (UNLOAD-TRUCK p1 t3 l3-2) (UNLOAD-TRUCK p3 t3 l3-2) (FLY-AIRPLANE a0 l3-0 l0-0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(FLY-AIRPLANE a0 l3-0 l0-0)", "1"]], "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 l3-1 l3-2 l4-1 l4-2 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l2-0) (at p0 l2-2) (at p1 l3-1) (at p2 l2-1) (at p3 l4-1) (at t0 l0-2) (at t1 l1-2) (at t2 l2-2) (at t3 l3-2) (at t4 l4-0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4))\n    (:goal (and (at p0 l3-0) (at p1 l3-2) (at p2 l2-2) (at p3 l3-2)))\n)"}
{"id": -184357375001553292, "group": "action_justification_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-0 and l0-1 are in c0. Currently, t1 and p1 are at l1-0, a0, t0, and p3 are at l0-0, p2 and p0 are at l0-1. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - place the object ?obj onto the airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - unload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - remove the object ?obj from the airplane ?airplane and place it on the location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from location ?loc-from in city ?city to location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from the airport ?loc-from to the airport ?loc-to. The goal is to reach a state where the following facts hold: p1 is at l1-1, p0 is at l1-1, p3 is at l1-1, and p2 is at l1-0.", "question": "Simplify the plan \"(LOAD-TRUCK p1 t1 l1-0) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (LOAD-TRUCK p0 t0 l0-1) (LOAD-TRUCK p2 t0 l0-1) (LOAD-AIRPLANE p3 a0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (UNLOAD-AIRPLANE p3 a0 l1-0) (LOAD-TRUCK p3 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p1 t1 l1-1) (UNLOAD-TRUCK p3 t1 l1-1) (DRIVE-TRUCK t0 l0-1 l0-0 c0) (UNLOAD-TRUCK p0 t0 l0-0) (UNLOAD-TRUCK p2 t0 l0-0) (FLY-AIRPLANE a0 l1-0 l0-0) (LOAD-AIRPLANE p0 a0 l0-0) (LOAD-AIRPLANE p2 a0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (UNLOAD-AIRPLANE p0 a0 l1-0) (UNLOAD-AIRPLANE p2 a0 l1-0) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (LOAD-TRUCK p0 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p0 t1 l1-1) (LOAD-TRUCK p1 t1 l1-1) (UNLOAD-TRUCK p1 t1 l1-1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(LOAD-TRUCK p1 t1 l1-1)", "(UNLOAD-TRUCK p1 t1 l1-1)", "-1"]], "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l0-1) (at p1 l1-0) (at p2 l0-1) (at p3 l0-0) (at t0 l0-0) (at t1 l1-0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-1)))\n)"}
{"id": 5976487916940318016, "group": "action_justification_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l2-1, l2-0, and l2-2 are in c2; l3-1, l3-2, and l3-0 are in c3; l4-1, l4-2, and l4-0 are in c4; l1-0, l1-1, and l1-2 are in c1; l0-2, l0-0, and l0-1 are in c0. Currently, p3 is at l4-1, p2 is at l2-1, t1 is at l1-2, t0 is at l0-2, t3 is at l3-2, p0 and t2 are at l2-2, t4 is at l4-0, p1 is at l3-1, a0 is at l2-0. The available actions are: (load-truck ?obj ?truck ?loc) - load object ?obj into truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - place the object ?obj onto the airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from its current location ?loc-from in city ?city to the new location ?loc-to within the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from airport ?loc-from to airport ?loc-to. The goal is to reach a state where the following facts hold: p3 is at l3-2, p0 is at l3-0, p2 is at l2-2, and p1 is at l3-2.", "question": "Simplify the plan \"(DRIVE-TRUCK t3 l3-2 l3-1 c3) (LOAD-TRUCK p1 t3 l3-1) (DRIVE-TRUCK t3 l3-1 l3-0 c3) (LOAD-TRUCK p0 t2 l2-2) (DRIVE-TRUCK t2 l2-2 l2-0 c2) (UNLOAD-TRUCK p0 t2 l2-0) (DRIVE-TRUCK t2 l2-0 l2-1 c2) (LOAD-AIRPLANE p0 a0 l2-0) (LOAD-TRUCK p2 t2 l2-1) (DRIVE-TRUCK t2 l2-1 l2-2 c2) (UNLOAD-TRUCK p2 t2 l2-2) (FLY-AIRPLANE a0 l2-0 l4-0) (DRIVE-TRUCK t4 l4-0 l4-1 c4) (LOAD-TRUCK p3 t4 l4-1) (DRIVE-TRUCK t4 l4-1 l4-0 c4) (UNLOAD-TRUCK p3 t4 l4-0) (LOAD-AIRPLANE p3 a0 l4-0) (FLY-AIRPLANE a0 l4-0 l3-0) (UNLOAD-AIRPLANE p0 a0 l3-0) (UNLOAD-AIRPLANE p3 a0 l3-0) (LOAD-TRUCK p3 t3 l3-0) (DRIVE-TRUCK t3 l3-0 l3-2 c3) (UNLOAD-TRUCK p1 t3 l3-2) (UNLOAD-TRUCK p3 t3 l3-2) (DRIVE-TRUCK t4 l4-0 l4-1 c4)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(DRIVE-TRUCK t4 l4-0 l4-1 c4)", "2"]], "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 l3-1 l3-2 l4-1 l4-2 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l2-0) (at p0 l2-2) (at p1 l3-1) (at p2 l2-1) (at p3 l4-1) (at t0 l0-2) (at t1 l1-2) (at t2 l2-2) (at t3 l3-2) (at t4 l4-0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4))\n    (:goal (and (at p0 l3-0) (at p1 l3-2) (at p2 l2-2) (at p3 l3-2)))\n)"}
{"id": 4201968872468191257, "group": "action_justification_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-0 and l0-1 are in c0. Currently, t1 and p1 are at l1-0, a0, t0, and p3 are at l0-0, p2 and p0 are at l0-1. The available actions are: (load-truck ?obj ?truck ?loc) - load object ?obj into truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - load object ?obj into airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - unload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from its current location ?loc-from in city ?city to the new location ?loc-to within the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from the airport ?loc-from to the airport ?loc-to. The goal is to reach a state where the following facts hold: p1 is at l1-1, p0 is at l1-1, p3 is at l1-1, and p2 is at l1-0.", "question": "Simplify the plan \"(LOAD-TRUCK p1 t1 l1-0) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (LOAD-TRUCK p0 t0 l0-1) (LOAD-TRUCK p2 t0 l0-1) (LOAD-AIRPLANE p3 a0 l0-0) (DRIVE-TRUCK t0 l0-1 l0-0 c0) (UNLOAD-TRUCK p0 t0 l0-0) (UNLOAD-TRUCK p2 t0 l0-0) (LOAD-AIRPLANE p0 a0 l0-0) (LOAD-AIRPLANE p2 a0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (UNLOAD-AIRPLANE p0 a0 l1-0) (LOAD-TRUCK p0 t1 l1-0) (UNLOAD-AIRPLANE p2 a0 l1-0) (UNLOAD-AIRPLANE p3 a0 l1-0) (UNLOAD-TRUCK p1 t1 l1-0) (LOAD-TRUCK p3 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p0 t1 l1-1) (UNLOAD-TRUCK p3 t1 l1-1) (LOAD-AIRPLANE p1 a0 l1-0) (UNLOAD-AIRPLANE p1 a0 l1-0) (LOAD-AIRPLANE p1 a0 l1-0) (UNLOAD-AIRPLANE p1 a0 l1-0) (LOAD-AIRPLANE p1 a0 l1-0) (UNLOAD-AIRPLANE p1 a0 l1-0) (LOAD-AIRPLANE p1 a0 l1-0) (UNLOAD-AIRPLANE p1 a0 l1-0) (LOAD-AIRPLANE p1 a0 l1-0) (UNLOAD-AIRPLANE p1 a0 l1-0) (LOAD-AIRPLANE p1 a0 l1-0) (UNLOAD-AIRPLANE p1 a0 l1-0) (LOAD-AIRPLANE p1 a0 l1-0) (UNLOAD-AIRPLANE p1 a0 l1-0) (LOAD-AIRPLANE p1 a0 l1-0) (UNLOAD-AIRPLANE p1 a0 l1-0) (LOAD-AIRPLANE p1 a0 l1-0) (UNLOAD-AIRPLANE p1 a0 l1-0) (LOAD-AIRPLANE p1 a0 l1-0) (UNLOAD-AIRPLANE p1 a0 l1-0) (LOAD-AIRPLANE p1 a0 l1-0) (UNLOAD-AIRPLANE p1 a0 l1-0) (LOAD-AIRPLANE p1 a0 l1-0) (UNLOAD-AIRPLANE p1 a0 l1-0) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (LOAD-TRUCK p1 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (UNLOAD-TRUCK p1 t1 l1-1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(LOAD-AIRPLANE p1 a0 l1-0)", "(UNLOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(UNLOAD-AIRPLANE p1 a0 l1-0)", "(LOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(LOAD-AIRPLANE p1 a0 l1-0)", "(UNLOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(UNLOAD-AIRPLANE p1 a0 l1-0)", "(LOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(LOAD-AIRPLANE p1 a0 l1-0)", "(UNLOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(UNLOAD-AIRPLANE p1 a0 l1-0)", "(LOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(LOAD-AIRPLANE p1 a0 l1-0)", "(UNLOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(UNLOAD-AIRPLANE p1 a0 l1-0)", "(LOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(LOAD-AIRPLANE p1 a0 l1-0)", "(UNLOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(UNLOAD-AIRPLANE p1 a0 l1-0)", "(LOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(LOAD-AIRPLANE p1 a0 l1-0)", "(UNLOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(UNLOAD-AIRPLANE p1 a0 l1-0)", "(LOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(LOAD-AIRPLANE p1 a0 l1-0)", "(UNLOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(UNLOAD-AIRPLANE p1 a0 l1-0)", "(LOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(LOAD-AIRPLANE p1 a0 l1-0)", "(UNLOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(UNLOAD-AIRPLANE p1 a0 l1-0)", "(LOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(LOAD-AIRPLANE p1 a0 l1-0)", "(UNLOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(UNLOAD-AIRPLANE p1 a0 l1-0)", "(LOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(LOAD-AIRPLANE p1 a0 l1-0)", "(UNLOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(UNLOAD-AIRPLANE p1 a0 l1-0)", "(LOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(LOAD-AIRPLANE p1 a0 l1-0)", "(UNLOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(UNLOAD-AIRPLANE p1 a0 l1-0)", "(LOAD-AIRPLANE p1 a0 l1-0)", "-1"], ["(LOAD-AIRPLANE p1 a0 l1-0)", "(UNLOAD-AIRPLANE p1 a0 l1-0)", "-1"]], "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l0-1) (at p1 l1-0) (at p2 l0-1) (at p3 l0-0) (at t0 l0-0) (at t1 l1-0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-1)))\n)"}
{"id": 8924320766076088633, "group": "action_justification_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l2-1, l2-0, and l2-2 are in c2; l3-1, l3-2, and l3-0 are in c3; l4-1, l4-2, and l4-0 are in c4; l1-0, l1-1, and l1-2 are in c1; l0-2, l0-0, and l0-1 are in c0. Currently, p3 is at l4-1, p2 is at l2-1, t1 is at l1-2, t0 is at l0-2, t3 is at l3-2, p0 and t2 are at l2-2, t4 is at l4-0, p1 is at l3-1, a0 is at l2-0. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc into the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - remove the object ?obj from the truck ?truck and place it on the location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload object ?obj from airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - drive the truck ?truck in city ?city from location ?loc-from to location ?loc-to, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly airplane ?airplane from airport ?loc-from to airport ?loc-to. The goal is to reach a state where the following facts hold: p3 is at l3-2, p0 is at l3-0, p2 is at l2-2, and p1 is at l3-2.", "question": "Simplify the plan \"(DRIVE-TRUCK t3 l3-2 l3-1 c3) (LOAD-TRUCK p1 t3 l3-1) (DRIVE-TRUCK t3 l3-1 l3-0 c3) (LOAD-TRUCK p0 t2 l2-2) (DRIVE-TRUCK t2 l2-2 l2-1 c2) (LOAD-TRUCK p2 t2 l2-1) (DRIVE-TRUCK t2 l2-1 l2-0 c2) (UNLOAD-TRUCK p0 t2 l2-0) (LOAD-AIRPLANE p0 a0 l2-0) (DRIVE-TRUCK t2 l2-0 l2-2 c2) (UNLOAD-TRUCK p2 t2 l2-2) (FLY-AIRPLANE a0 l2-0 l4-0) (DRIVE-TRUCK t4 l4-0 l4-1 c4) (LOAD-TRUCK p3 t4 l4-1) (DRIVE-TRUCK t4 l4-1 l4-0 c4) (UNLOAD-TRUCK p3 t4 l4-0) (LOAD-AIRPLANE p3 a0 l4-0) (FLY-AIRPLANE a0 l4-0 l3-0) (UNLOAD-AIRPLANE p0 a0 l3-0) (UNLOAD-AIRPLANE p3 a0 l3-0) (LOAD-TRUCK p3 t3 l3-0) (DRIVE-TRUCK t3 l3-0 l3-2 c3) (UNLOAD-TRUCK p1 t3 l3-2) (UNLOAD-TRUCK p3 t3 l3-2) (DRIVE-TRUCK t0 l0-2 l0-1 c0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(DRIVE-TRUCK t0 l0-2 l0-1 c0)", "1"]], "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 l3-1 l3-2 l4-1 l4-2 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l2-0) (at p0 l2-2) (at p1 l3-1) (at p2 l2-1) (at p3 l4-1) (at t0 l0-2) (at t1 l1-2) (at t2 l2-2) (at t3 l3-2) (at t4 l4-0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4))\n    (:goal (and (at p0 l3-0) (at p1 l3-2) (at p2 l2-2) (at p3 l3-2)))\n)"}
{"id": 8439603267446618471, "group": "action_justification_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. \nThe locations are in cities as follows: l1-0 and l1-1 are in c1; l0-0 and l0-1 are in c0. \nCurrently, t1 and p0 are at l1-1, t0 is at l0-1, p1, p2, and p3 are at l1-0, a0 is at l0-0. The available actions are: (load-truck ?obj ?truck ?loc) - place the object ?obj into the truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc into the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - remove the object ?obj from the truck ?truck and place it on the location ?loc, (unload-airplane ?obj ?airplane ?loc) - remove the object ?obj from the airplane ?airplane and place it on the location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from location ?loc-from in city ?city to location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from airport ?loc-from to airport ?loc-to. The goal is to reach a state where the following facts hold: p0 is at l0-0, p3 is at l0-1, p1 is at l1-0, and p2 is at l1-0.", "question": "Simplify the plan \"(DRIVE-TRUCK t0 l0-1 l0-0 c0) (FLY-AIRPLANE a0 l0-0 l1-0) (LOAD-TRUCK p0 t1 l1-1) (LOAD-AIRPLANE p2 a0 l1-0) (UNLOAD-AIRPLANE p2 a0 l1-0) (LOAD-AIRPLANE p3 a0 l1-0) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (UNLOAD-TRUCK p0 t1 l1-0) (LOAD-AIRPLANE p0 a0 l1-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (FLY-AIRPLANE a0 l1-0 l0-0) (UNLOAD-AIRPLANE p0 a0 l0-0) (UNLOAD-AIRPLANE p3 a0 l0-0) (LOAD-TRUCK p3 t0 l0-0) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (UNLOAD-TRUCK p3 t0 l0-1) (DRIVE-TRUCK t0 l0-1 l0-0 c0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(DRIVE-TRUCK t1 l1-0 l1-1 c1)", "1"], ["(DRIVE-TRUCK t0 l0-1 l0-0 c0)", "2"], ["(LOAD-AIRPLANE p2 a0 l1-0)", "(UNLOAD-AIRPLANE p2 a0 l1-0)", "-1"]], "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l1-1) (at p1 l1-0) (at p2 l1-0) (at p3 l1-0) (at t0 l0-1) (at t1 l1-1) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l0-0) (at p1 l1-0) (at p2 l1-0) (at p3 l0-1)))\n)"}
{"id": -363044819230586968, "group": "action_justification_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. \nThe locations are in cities as follows: l1-0 and l1-1 are in c1; l0-0 and l0-1 are in c0. \nCurrently, p0, t0, and p3 are at l0-0, t1 and a0 are at l1-0, p1 is at l0-1, p2 is at l1-1. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc onto the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - unload object ?obj from truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from its current location ?loc-from in city ?city to the new location ?loc-to within the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - operate the airplane ?airplane from airport ?loc-from to airport ?loc-to. The goal is to reach a state where the following facts hold: p2 is at l0-0, p1 is at l0-1, p3 is at l0-0, and p0 is at l0-1.", "question": "Simplify the plan \"(DRIVE-TRUCK t1 l1-0 l1-1 c1) (LOAD-TRUCK p2 t1 l1-1) (UNLOAD-TRUCK p2 t1 l1-1) (LOAD-TRUCK p2 t1 l1-1) (LOAD-TRUCK p0 t0 l0-0) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (UNLOAD-TRUCK p2 t1 l1-0) (UNLOAD-TRUCK p0 t0 l0-1) (LOAD-AIRPLANE p2 a0 l1-0) (FLY-AIRPLANE a0 l1-0 l0-0) (UNLOAD-AIRPLANE p2 a0 l0-0) (LOAD-TRUCK p1 t0 l0-1) (UNLOAD-TRUCK p1 t0 l0-1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(LOAD-TRUCK p2 t1 l1-1)", "(UNLOAD-TRUCK p2 t1 l1-1)", "-1"], ["(UNLOAD-TRUCK p2 t1 l1-1)", "(LOAD-TRUCK p2 t1 l1-1)", "-1"], ["(LOAD-TRUCK p1 t0 l0-1)", "(UNLOAD-TRUCK p1 t0 l0-1)", "-1"]], "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l1-0) (at p0 l0-0) (at p1 l0-1) (at p2 l1-1) (at p3 l0-0) (at t0 l0-0) (at t1 l1-0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l0-1) (at p1 l0-1) (at p2 l0-0) (at p3 l0-0)))\n)"}
{"id": -3595929840207846012, "group": "action_justification_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 3 trucks and 1 airplane, as well as 4 packages. There are 9 locations across 3 cities. \nThe locations are in cities as follows: l2-1, l2-0, and l2-2 are in c2; l1-0, l1-1, and l1-2 are in c1; l0-2, l0-0, and l0-1 are in c0. \nCurrently, t1 and p1 are at l1-2, p2 is at l0-1, t0 is at l0-2, p3 is at l2-1, t2 is at l2-2, a0 and p0 are at l2-0. The available actions are: (load-truck ?obj ?truck ?loc) - load object ?obj into truck ?truck at location ?loc, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc onto the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - unload object ?obj from truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload object ?obj from airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from location ?loc-from in city ?city to location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from the airport ?loc-from to the airport ?loc-to. The goal is to reach a state where the following facts hold: p3 is at l1-2, p0 is at l2-1, p1 is at l2-0, and p2 is at l2-2.", "question": "Simplify the plan \"(LOAD-TRUCK p1 t1 l1-2) (DRIVE-TRUCK t0 l0-2 l0-1 c0) (LOAD-TRUCK p2 t0 l0-1) (DRIVE-TRUCK t0 l0-1 l0-0 c0) (UNLOAD-TRUCK p2 t0 l0-0) (DRIVE-TRUCK t2 l2-2 l2-0 c2) (LOAD-TRUCK p0 t2 l2-0) (DRIVE-TRUCK t2 l2-0 l2-1 c2) (UNLOAD-TRUCK p0 t2 l2-1) (LOAD-TRUCK p3 t2 l2-1) (DRIVE-TRUCK t1 l1-2 l1-0 c1) (UNLOAD-TRUCK p1 t1 l1-0) (FLY-AIRPLANE a0 l2-0 l0-0) (LOAD-AIRPLANE p2 a0 l0-0) (FLY-AIRPLANE a0 l0-0 l1-0) (LOAD-AIRPLANE p1 a0 l1-0) (DRIVE-TRUCK t2 l2-1 l2-0 c2) (UNLOAD-TRUCK p3 t2 l2-0) (FLY-AIRPLANE a0 l1-0 l2-0) (UNLOAD-AIRPLANE p1 a0 l2-0) (UNLOAD-AIRPLANE p2 a0 l2-0) (LOAD-TRUCK p2 t2 l2-0) (LOAD-AIRPLANE p3 a0 l2-0) (DRIVE-TRUCK t2 l2-0 l2-2 c2) (UNLOAD-TRUCK p2 t2 l2-2) (FLY-AIRPLANE a0 l2-0 l1-0) (UNLOAD-AIRPLANE p3 a0 l1-0) (LOAD-TRUCK p3 t1 l1-0) (FLY-AIRPLANE a0 l1-0 l2-0) (UNLOAD-TRUCK p3 t1 l1-0) (LOAD-TRUCK p3 t1 l1-0) (UNLOAD-TRUCK p3 t1 l1-0) (LOAD-TRUCK p3 t1 l1-0) (UNLOAD-TRUCK p3 t1 l1-0) (LOAD-TRUCK p3 t1 l1-0) (DRIVE-TRUCK t1 l1-0 l1-2 c1) (UNLOAD-TRUCK p3 t1 l1-2)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(FLY-AIRPLANE a0 l1-0 l2-0)", "2"], ["(UNLOAD-TRUCK p3 t1 l1-0)", "(LOAD-TRUCK p3 t1 l1-0)", "-1"], ["(LOAD-TRUCK p3 t1 l1-0)", "(UNLOAD-TRUCK p3 t1 l1-0)", "-1"], ["(UNLOAD-TRUCK p3 t1 l1-0)", "(LOAD-TRUCK p3 t1 l1-0)", "-1"], ["(LOAD-TRUCK p3 t1 l1-0)", "(UNLOAD-TRUCK p3 t1 l1-0)", "-1"], ["(UNLOAD-TRUCK p3 t1 l1-0)", "(LOAD-TRUCK p3 t1 l1-0)", "-1"]], "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c3-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 - airport c0 c1 c2 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 - location p0 p1 p2 p3 - package t0 t1 t2 - truck)\n    (:init (at a0 l2-0) (at p0 l2-0) (at p1 l1-2) (at p2 l0-1) (at p3 l2-1) (at t0 l0-2) (at t1 l1-2) (at t2 l2-2) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2))\n    (:goal (and (at p0 l2-1) (at p1 l2-0) (at p2 l2-2) (at p3 l1-2)))\n)"}
{"id": -4801236149786500326, "group": "action_justification_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. \nThe locations are in cities as follows: l1-0 and l1-1 are in c1; l0-0 and l0-1 are in c0. \nCurrently, p0, t0, and p3 are at l0-0, t1 and a0 are at l1-0, p1 is at l0-1, p2 is at l1-1. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load object ?obj into airplane ?airplane at location ?loc, (unload-truck ?obj ?truck ?loc) - offload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - remove the object ?obj from the airplane ?airplane and place it on the location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from its current location ?loc-from in city ?city to the new location ?loc-to within the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - operate the airplane ?airplane from airport ?loc-from to airport ?loc-to. The goal is to reach a state where the following facts hold: p2 is at l0-0, p1 is at l0-1, p3 is at l0-0, and p0 is at l0-1.", "question": "Simplify the plan \"(LOAD-TRUCK p0 t0 l0-0) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (LOAD-TRUCK p2 t1 l1-1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (UNLOAD-TRUCK p2 t1 l1-0) (LOAD-AIRPLANE p2 a0 l1-0) (LOAD-TRUCK p3 t0 l0-0) (UNLOAD-TRUCK p3 t0 l0-0) (LOAD-TRUCK p3 t0 l0-0) (UNLOAD-TRUCK p3 t0 l0-0) (FLY-AIRPLANE a0 l1-0 l0-0) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (UNLOAD-AIRPLANE p2 a0 l0-0) (UNLOAD-TRUCK p0 t0 l0-1)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(LOAD-TRUCK p3 t0 l0-0)", "(UNLOAD-TRUCK p3 t0 l0-0)", "-1"], ["(UNLOAD-TRUCK p3 t0 l0-0)", "(LOAD-TRUCK p3 t0 l0-0)", "-1"], ["(LOAD-TRUCK p3 t0 l0-0)", "(UNLOAD-TRUCK p3 t0 l0-0)", "-1"]], "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l1-0) (at p0 l0-0) (at p1 l0-1) (at p2 l1-1) (at p3 l0-0) (at t0 l0-0) (at t1 l1-0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l0-1) (at p1 l0-1) (at p2 l0-0) (at p3 l0-0)))\n)"}
{"id": -5785284809616541947, "group": "action_justification_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. \nThe locations are in cities as follows: l1-0 and l1-1 are in c1; l0-0 and l0-1 are in c0. \nCurrently, p0, t0, and p3 are at l0-0, t1 and a0 are at l1-0, p1 is at l0-1, p2 is at l1-1. The available actions are: (load-truck ?obj ?truck ?loc) - load the object ?obj from location ?loc into the truck ?truck, (load-airplane ?obj ?airplane ?loc) - load the object ?obj from location ?loc into the airplane ?airplane, (unload-truck ?obj ?truck ?loc) - unload the object ?obj from the truck ?truck at location ?loc, (unload-airplane ?obj ?airplane ?loc) - unload the object ?obj from the airplane ?airplane at location ?loc, (drive-truck ?truck ?loc-from ?loc-to ?city) - navigate the truck ?truck from location ?loc-from in city ?city to location ?loc-to in the same city, and (fly-airplane ?airplane ?loc-from ?loc-to) - fly the airplane ?airplane from location ?loc-from to location ?loc-to. The goal is to reach a state where the following facts hold: p2 is at l0-0, p1 is at l0-1, p3 is at l0-0, and p0 is at l0-1.", "question": "Simplify the plan \"(LOAD-TRUCK p0 t0 l0-0) (DRIVE-TRUCK t0 l0-0 l0-1 c0) (UNLOAD-TRUCK p0 t0 l0-1) (DRIVE-TRUCK t1 l1-0 l1-1 c1) (LOAD-TRUCK p2 t1 l1-1) (DRIVE-TRUCK t1 l1-1 l1-0 c1) (UNLOAD-TRUCK p2 t1 l1-0) (LOAD-AIRPLANE p2 a0 l1-0) (FLY-AIRPLANE a0 l1-0 l0-0) (UNLOAD-AIRPLANE p2 a0 l0-0) (LOAD-AIRPLANE p2 a0 l0-0) (UNLOAD-AIRPLANE p2 a0 l0-0)\" by removing either a single action or a pair of consecutive actions, while still maintaining a valid plan. Provide the resulting simplified plan.", "answer": [["(UNLOAD-AIRPLANE p2 a0 l0-0)", "(LOAD-AIRPLANE p2 a0 l0-0)", "-1"], ["(LOAD-AIRPLANE p2 a0 l0-0)", "(UNLOAD-AIRPLANE p2 a0 l0-0)", "-1"]], "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l1-0) (at p0 l0-0) (at p1 l0-1) (at p2 l1-1) (at p3 l0-0) (at t0 l0-0) (at t1 l1-0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l0-1) (at p1 l0-1) (at p2 l0-0) (at p3 l0-0)))\n)"}
