{"id": -1474170633806229538, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l4-2, l4-0, and l4-1 are in c4; l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l3-1, l3-0, and l3-2 are in c3; l0-0, l0-1, and l0-2 are in c0. Currently, p2 and t2 are at l2-1, t3 is at l3-0, t4 and a0 are at l4-0, t0 is at l0-2, t1 is at l1-2, p1 is at l3-1, p0 and p3 are in a0.", "question": "Which of the following actions can eventually be applied? A. load object a0 into truck t0 at location p1. B. unload object l4-1 from truck l2-1 at location c2. C. remove the object p3 from the airplane a0 and place it on the location l1-0. D. load object l4-0 into truck c2 at location l4-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load object a0 into truck t0 at location p1", "unload object l4-1 from truck l2-1 at location c2", "remove the object p3 from the airplane a0 and place it on the location l1-0", "load object l4-0 into truck c2 at location l4-1"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 5550655872393039993, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p4 and t1 are at l1-0, a0 and t0 are at l0-0, t2 is at l2-1, p1 and p3 are at l2-0, p2 is at l0-1, p0 is in a0.", "question": "Which of the following actions can eventually be applied? A. remove the object l1-0 from the airplane p4 and place it on the location p0. B. load the object p3 from location l2-0 onto the airplane a0. C. navigate the truck l0-1 from location t2 in city p0 to location l1-0 in the same city. D. fly the airplane p4 from airport p1 to airport l1-2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["remove the object l1-0 from the airplane p4 and place it on the location p0", "load the object p3 from location l2-0 onto the airplane a0", "navigate the truck l0-1 from location t2 in city p0 to location l1-0 in the same city", "fly the airplane p4 from airport p1 to airport l1-2"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 4008075837916923612, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p0, p3, t1, and p2 are at l1-2, t2 is at l2-1, a0 and p1 are at l1-0, t0 and p4 are at l0-0.", "question": "Which of the following actions can eventually be applied? A. offload the object t2 from the airplane l1-2 at location p2. B. drive truck t2 from location l2-0 in city c2 to location l2-2 in the same city. C. drive truck l0-2 from location c0 in city l0-1 to location l1-2 in the same city. D. operate the airplane l2-0 from airport t1 to airport a0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["offload the object t2 from the airplane l1-2 at location p2", "drive truck t2 from location l2-0 in city c2 to location l2-2 in the same city", "drive truck l0-2 from location c0 in city l0-1 to location l1-2 in the same city", "operate the airplane l2-0 from airport t1 to airport a0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -8142953292802760641, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l1-3, l1-0, l1-5, l1-7, l1-6, l1-9, l1-2, l1-1, l1-4, and l1-8 are in c1; l4-0, l4-4, l4-6, l4-2, l4-3, l4-5, l4-9, l4-1, l4-8, and l4-7 are in c4; l3-3, l3-5, l3-6, l3-1, l3-4, l3-8, l3-7, l3-9, l3-2, and l3-0 are in c3; l2-3, l2-1, l2-9, l2-4, l2-8, l2-6, l2-0, l2-7, l2-2, and l2-5 are in c2; l0-5, l0-7, l0-3, l0-9, l0-0, l0-1, l0-6, l0-4, l0-8, and l0-2 are in c0. Currently, p3 is at l2-9, t3 and p2 are at l3-0, t1 is at l1-1, t4 is at l4-0, t0 is at l0-2, a0 is at l0-0, p1 is at l1-8, p0 is at l2-4, t2 is at l2-0.", "question": "Which of the following actions can eventually be applied? A. drive the truck t4 in city c4 from location l4-8 to location l4-2. B. place the object c1 into the truck l3-1 at location l1-1. C. place the object l4-0 into the truck c1 at location l3-1. D. unload object t3 from airplane l2-6 at location l1-4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["drive the truck t4 in city c4 from location l4-8 to location l4-2", "place the object c1 into the truck l3-1 at location l1-1", "place the object l4-0 into the truck c1 at location l3-1", "unload object t3 from airplane l2-6 at location l1-4"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -6563286363467899485, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p2 and a0 are at l1-0, t1 is at l1-1, t0 is at l0-2, p0, p1, and p3 are in t0.", "question": "Which of the following actions can eventually be applied? A. navigate the truck l0-0 from its current location l1-1 in city p0 to the new location l0-2 within the same city. B. load the object p0 from location t0 onto the airplane p3. C. remove the object p1 from the truck t0 and place it on the location l0-1. D. place the object p0 into the truck l0-1 at location p1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["navigate the truck l0-0 from its current location l1-1 in city p0 to the new location l0-2 within the same city", "load the object p0 from location t0 onto the airplane p3", "remove the object p1 from the truck t0 and place it on the location l0-1", "place the object p0 into the truck l0-1 at location p1"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -8594491423422192622, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p4 is at l1-0, p0, a0, and t0 are at l0-0, t2 is at l2-1, p1 and p3 are at l2-0, t1 is at l1-2, p2 is at l0-1.", "question": "Which of the following actions can eventually be applied? A. drive truck t2 from location l2-2 in city c2 to location l2-0 in the same city. B. unload object l1-1 from airplane p3 at location l1-0. C. drive truck c1 from location t0 in city l0-1 to location p1 in the same city. D. fly airplane l1-1 from airport l1-2 to airport t0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["drive truck t2 from location l2-2 in city c2 to location l2-0 in the same city", "unload object l1-1 from airplane p3 at location l1-0", "drive truck c1 from location t0 in city l0-1 to location p1 in the same city", "fly airplane l1-1 from airport l1-2 to airport t0"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -4624695102354256075, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l4-2, l4-0, and l4-1 are in c4; l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l3-1, l3-0, and l3-2 are in c3; l0-0, l0-1, and l0-2 are in c0. Currently, p0 and a0 are at l3-0, t3 and p3 are at l3-2, t4 is at l4-0, t0 is at l0-2, t1 is at l1-2, p2 and t2 are at l2-2, p1 is in t3.", "question": "Which of the following actions can eventually be applied? A. load object c0 into airplane l0-2 at location a0. B. remove the object l1-0 from the truck c2 and place it on the location t4. C. fly the airplane a0 from location l0-0 to location l2-0. D. remove the object l0-0 from the truck l0-2 and place it on the location l3-2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load object c0 into airplane l0-2 at location a0", "remove the object l1-0 from the truck c2 and place it on the location t4", "fly the airplane a0 from location l0-0 to location l2-0", "remove the object l0-0 from the truck l0-2 and place it on the location l3-2"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -6673225145075556987, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l1-3, l1-0, l1-5, l1-7, l1-6, l1-9, l1-2, l1-1, l1-4, and l1-8 are in c1; l4-0, l4-4, l4-6, l4-2, l4-3, l4-5, l4-9, l4-1, l4-8, and l4-7 are in c4; l3-3, l3-5, l3-6, l3-1, l3-4, l3-8, l3-7, l3-9, l3-2, and l3-0 are in c3; l2-3, l2-1, l2-9, l2-4, l2-8, l2-6, l2-0, l2-7, l2-2, and l2-5 are in c2; l0-5, l0-7, l0-3, l0-9, l0-0, l0-1, l0-6, l0-4, l0-8, and l0-2 are in c0. Currently, p3 is at l2-9, a0 and t2 are at l2-0, t3 is at l3-0, t4 is at l4-0, t0 is at l0-2, t1 is at l1-0, p0 is at l2-4, p1 is in t2, p2 is in t4.", "question": "Which of the following actions can eventually be applied? A. load the object p2 from location l3-2 into the truck l3-8. B. fly airplane l3-5 from airport c2 to airport l2-5. C. drive truck t4 from location l4-1 in city c4 to location l4-8 in the same city. D. unload object l4-5 from airplane l1-8 at location l4-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load the object p2 from location l3-2 into the truck l3-8", "fly airplane l3-5 from airport c2 to airport l2-5", "drive truck t4 from location l4-1 in city c4 to location l4-8 in the same city", "unload object l4-5 from airplane l1-8 at location l4-0"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 12915953209085223, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l4-2, l4-0, and l4-1 are in c4; l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l3-1, l3-0, and l3-2 are in c3; l0-0, l0-1, and l0-2 are in c0. Currently, p2 and t2 are at l2-1, p3 is at l4-1, t3 is at l3-0, a0 is at l0-0, t4 is at l4-2, t1 is at l1-2, p1 is at l3-1, t0 is at l0-1, p0 is in a0.", "question": "Which of the following actions can eventually be applied? A. operate the airplane l4-1 from airport c4 to airport l0-1. B. unload the object l0-2 from the airplane l2-2 at location c0. C. navigate the truck t1 which is in location l1-1 in city c1 to another location l1-0 in the same city. D. operate the airplane p2 from airport a0 to airport l0-2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["operate the airplane l4-1 from airport c4 to airport l0-1", "unload the object l0-2 from the airplane l2-2 at location c0", "navigate the truck t1 which is in location l1-1 in city c1 to another location l1-0 in the same city", "operate the airplane p2 from airport a0 to airport l0-2"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -6324340464747286456, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-1 and l1-0 are in c1. Currently, p0 is at l1-1, p2, t1, and a0 are at l1-0, t0 is at l0-1, p3 and p1 are in t1.", "question": "Which of the following actions can eventually be applied? A. remove the object c0 from the truck p0 and place it on the location a0. B. load the object p3 from location l1-1 into the truck t1. C. remove the object l1-1 from the airplane t0 and place it on the location l1-0. D. navigate the truck l0-0 from its current location t1 in city a0 to the new location p0 within the same city.", "choices": {"label": ["A", "B", "C", "D"], "text": ["remove the object c0 from the truck p0 and place it on the location a0", "load the object p3 from location l1-1 into the truck t1", "remove the object l1-1 from the airplane t0 and place it on the location l1-0", "navigate the truck l0-0 from its current location t1 in city a0 to the new location p0 within the same city"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -3447395839344558671, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-1 and l1-0 are in c1. Currently, p2 and t1 are at l1-0, a0 is at l0-0, p0 and t0 are at l0-1, p3 is in a0, p1 is in t0.", "question": "Which of the following actions can eventually be applied? A. load object l1-0 into truck t1 at location p1. B. navigate the truck c0 from its current location p1 in city p0 to the new location t1 within the same city. C. navigate the truck l0-0 from its current location p0 in city t0 to the new location c0 within the same city. D. operate the airplane a0 from airport l1-0 to airport l0-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load object l1-0 into truck t1 at location p1", "navigate the truck c0 from its current location p1 in city p0 to the new location t1 within the same city", "navigate the truck l0-0 from its current location p0 in city t0 to the new location c0 within the same city", "operate the airplane a0 from airport l1-0 to airport l0-0"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -4547965318315934485, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-1 and l1-0 are in c1. Currently, t1 is at l1-1, p0 and p1 are at l0-1, a0 is at l1-0, t0 is at l0-0, p3 is in a0, p2 is in t1.", "question": "Which of the following actions can eventually be applied? A. remove the object p1 from the airplane l0-0 and place it on the location p3. B. drive truck t0 from location l0-1 in city c0 to location l0-0 in the same city. C. remove the object c1 from the truck p0 and place it on the location l1-1. D. load the object p0 from location c0 into the airplane l1-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["remove the object p1 from the airplane l0-0 and place it on the location p3", "drive truck t0 from location l0-1 in city c0 to location l0-0 in the same city", "remove the object c1 from the truck p0 and place it on the location l1-1", "load the object p0 from location c0 into the airplane l1-0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -4723507971934032784, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 4 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, a0 and p0 are at l2-0, t1 is at l1-2, t2 is at l2-2, p3 is at l2-1, t0 is at l0-1, p2 is in t0, p1 is in t1.", "question": "Which of the following actions can eventually be applied? A. offload the object l0-1 from the airplane c2 at location l1-1. B. unload object p2 from truck t0 at location l0-0. C. unload object l1-0 from truck t2 at location l0-1. D. navigate the truck l0-0 from location a0 in city p2 to location c2 in the same city.", "choices": {"label": ["A", "B", "C", "D"], "text": ["offload the object l0-1 from the airplane c2 at location l1-1", "unload object p2 from truck t0 at location l0-0", "unload object l1-0 from truck t2 at location l0-1", "navigate the truck l0-0 from location a0 in city p2 to location c2 in the same city"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 3682615470677242793, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p1 and t1 are at l1-2, p0 is at l0-2, a0 is at l0-0, t0 is at l0-1, p2 is in t1, p3 is in t0.", "question": "Which of the following actions can eventually be applied? A. fly the airplane l0-2 from airport l1-0 to airport p2. B. offload the object l1-0 from the truck p1 at location t0. C. offload the object p3 from the truck t0 at location l0-0. D. offload the object l0-1 from the airplane l1-1 at location p3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["fly the airplane l0-2 from airport l1-0 to airport p2", "offload the object l1-0 from the truck p1 at location t0", "offload the object p3 from the truck t0 at location l0-0", "offload the object l0-1 from the airplane l1-1 at location p3"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 8212084537195039397, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p0 and t1 are at l1-1, a0 is at l1-0, t0 is at l0-0, p3 is in a0, p2 and p1 are in t1.", "question": "Which of the following actions can eventually be applied? A. place the object p2 into the truck t1 at location l1-1. B. remove the object c0 from the airplane t1 and place it on the location l0-1. C. navigate the truck l0-0 which is in location p3 in city t1 to another location l1-2 in the same city. D. offload the object l1-0 from the truck l0-1 at location l1-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the object p2 into the truck t1 at location l1-1", "remove the object c0 from the airplane t1 and place it on the location l0-1", "navigate the truck l0-0 which is in location p3 in city t1 to another location l1-2 in the same city", "offload the object l1-0 from the truck l0-1 at location l1-1"]}, "query": "Which action is reachable from this state?", "answer": "A"}
