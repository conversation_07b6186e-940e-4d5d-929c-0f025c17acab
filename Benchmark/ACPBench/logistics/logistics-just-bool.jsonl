{"id": 6652934932660258628, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l2-2, l2-1, and l2-0 are in c2; l0-0, l0-2, and l0-1 are in c0. Currently, t0, a0, and p0 are at l0-0, p3 and p1 are at l2-0, p4 is at l1-0, t2 is at l2-1, t1 is at l1-1, p2 is at l0-1. The goal is to reach a state where the following facts hold: p2 is at l1-2, p1 is at l1-0, p0 is at l1-2, p3 is at l1-2, and p4 is at l0-0.", "question": "Given the plan: \"navigate the truck t1 which is in location l1-1 in city c1 to another location l1-0 in the same city, place the object p0 onto the airplane a0 at location l0-0, navigate the truck t0 which is in location l0-0 in city c0 to another location l0-1 in the same city, place the object p2 into the truck t0 at location l0-1, fly the airplane a0 from the airport l0-0 to the airport l2-0, place the object p1 onto the airplane a0 at location l2-0, place the object p3 onto the airplane a0 at location l2-0, fly the airplane a0 from the airport l2-0 to the airport l1-0, offload the object p0 from the airplane a0 at location l1-0, place the object p0 into the truck t1 at location l1-0, offload the object p1 from the airplane a0 at location l1-0, offload the object p3 from the airplane a0 at location l1-0, place the object p3 into the truck t1 at location l1-0, place the object p4 onto the airplane a0 at location l1-0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-2 in the same city, remove the object p0 from the truck t1 and place it on the location l1-2, remove the object p3 from the truck t1 and place it on the location l1-2, navigate the truck t0 which is in location l0-1 in city c0 to another location l0-0 in the same city, remove the object p2 from the truck t0 and place it on the location l0-0, offload the object p4 from the airplane a0 at location l1-0, fly the airplane a0 from the airport l1-0 to the airport l0-0, place the object p2 onto the airplane a0 at location l0-0, fly the airplane a0 from the airport l0-0 to the airport l1-0, offload the object p2 from the airplane a0 at location l1-0, navigate the truck t1 which is in location l1-2 in city c1 to another location l1-0 in the same city, place the object p2 into the truck t1 at location l1-0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-2 in the same city, remove the object p2 from the truck t1 and place it on the location l1-2, navigate the truck t1 which is in location l1-2 in city c1 to another location l1-0 in the same city, place the object p4 into the truck t1 at location l1-0, remove the object p4 from the truck t1 and place it on the location l1-0, place the object p4 onto the airplane a0 at location l1-0, fly the airplane a0 from the airport l1-0 to the airport l0-0, offload the object p4 from the airplane a0 at location l0-0\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: place the object p4 into the truck t1 at location l1-0 and remove the object p4 from the truck t1 and place it on the location l1-0?", "answer": "yes"}
{"id": -3705311142557720045, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l2-2, l2-1, and l2-0 are in c2; l0-0, l0-2, and l0-1 are in c0; l3-2, l3-0, and l3-1 are in c3; l4-1, l4-0, and l4-2 are in c4. Currently, t0 is at l0-2, a0 is at l2-0, p0 and t2 are at l2-2, t1 is at l1-2, t3 is at l3-2, p1 is at l3-1, p2 is at l2-1, p3 is at l4-1, t4 is at l4-0. The goal is to reach a state where the following facts hold: p0 is at l3-0, p2 is at l2-2, p1 is at l3-2, and p3 is at l3-2.", "question": "Given the plan: \"drive truck t3 from location l3-2 in city c3 to location l3-0 in the same city, load the object p0 from location l2-2 into the truck t2, drive truck t2 from location l2-2 in city c2 to location l2-0 in the same city, offload the object p0 from the truck t2 at location l2-0, load object p0 into airplane a0 at location l2-0, fly the airplane a0 from location l2-0 to location l4-0, drive truck t4 from location l4-0 in city c4 to location l4-1 in the same city, load the object p3 from location l4-1 into the truck t4, drive truck t4 from location l4-1 in city c4 to location l4-0 in the same city, offload the object p3 from the truck t4 at location l4-0, load object p3 into airplane a0 at location l4-0, fly the airplane a0 from location l4-0 to location l3-0, unload object p0 from airplane a0 at location l3-0, unload object p3 from airplane a0 at location l3-0, load the object p3 from location l3-0 into the truck t3, drive truck t3 from location l3-0 in city c3 to location l3-1 in the same city, load the object p1 from location l3-1 into the truck t3, drive truck t3 from location l3-1 in city c3 to location l3-2 in the same city, offload the object p3 from the truck t3 at location l3-2, offload the object p1 from the truck t3 at location l3-2, fly the airplane a0 from location l3-0 to location l1-0, drive truck t2 from location l2-0 in city c2 to location l2-1 in the same city, load the object p2 from location l2-1 into the truck t2, drive truck t2 from location l2-1 in city c2 to location l2-2 in the same city, offload the object p2 from the truck t2 at location l2-2\"; can the following action be removed from this plan and still have a valid plan: offload the object p0 from the truck t2 at location l2-0?", "answer": "no"}
{"id": 3419254766225937964, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l2-2, l2-1, and l2-0 are in c2; l0-0, l0-2, and l0-1 are in c0. Currently, t0, a0, and p0 are at l0-0, p3 and p1 are at l2-0, p4 is at l1-0, t2 is at l2-1, t1 is at l1-1, p2 is at l0-1. The goal is to reach a state where the following facts hold: p2 is at l1-2, p1 is at l1-0, p0 is at l1-2, p3 is at l1-2, and p4 is at l0-0.", "question": "Given the plan: \"navigate the truck t1 which is in location l1-1 in city c1 to another location l1-0 in the same city, load object p0 into airplane a0 at location l0-0, navigate the truck t0 which is in location l0-0 in city c0 to another location l0-1 in the same city, place the object p2 into the truck t0 at location l0-1, fly airplane a0 from airport l0-0 to airport l2-0, load object p1 into airplane a0 at location l2-0, load object p3 into airplane a0 at location l2-0, fly airplane a0 from airport l2-0 to airport l1-0, remove the object p0 from the airplane a0 and place it on the location l1-0, place the object p0 into the truck t1 at location l1-0, remove the object p1 from the airplane a0 and place it on the location l1-0, remove the object p3 from the airplane a0 and place it on the location l1-0, place the object p3 into the truck t1 at location l1-0, load object p4 into airplane a0 at location l1-0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-2 in the same city, offload the object p0 from the truck t1 at location l1-2, offload the object p3 from the truck t1 at location l1-2, navigate the truck t0 which is in location l0-1 in city c0 to another location l0-0 in the same city, offload the object p2 from the truck t0 at location l0-0, fly airplane a0 from airport l1-0 to airport l0-0, load object p2 into airplane a0 at location l0-0, remove the object p4 from the airplane a0 and place it on the location l0-0, fly airplane a0 from airport l0-0 to airport l1-0, remove the object p2 from the airplane a0 and place it on the location l1-0, navigate the truck t1 which is in location l1-2 in city c1 to another location l1-0 in the same city, place the object p2 into the truck t1 at location l1-0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-2 in the same city, offload the object p2 from the truck t1 at location l1-2, fly airplane a0 from airport l1-0 to airport l2-0\"; can the following action be removed from this plan and still have a valid plan: offload the object p2 from the truck t0 at location l0-0?", "answer": "no"}
{"id": 370737218325655434, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l4-3, l4-2, l4-4, l4-9, l4-1, l4-8, l4-5, l4-7, l4-0, and l4-6 are in c4; l3-7, l3-9, l3-1, l3-4, l3-6, l3-2, l3-0, l3-8, l3-5, and l3-3 are in c3; l2-1, l2-4, l2-2, l2-3, l2-0, l2-5, l2-9, l2-6, l2-7, and l2-8 are in c2; l0-8, l0-6, l0-5, l0-1, l0-4, l0-0, l0-9, l0-2, l0-3, and l0-7 are in c0; l1-2, l1-3, l1-0, l1-7, l1-6, l1-9, l1-5, l1-8, l1-1, and l1-4 are in c1. Currently, a0 is at l0-0, t2 is at l2-0, t1 is at l1-6, t4 is at l4-7, p0 is at l2-4, p2 and t3 are at l3-1, p1 is at l1-8, t0 is at l0-2, p3 is at l2-9. The goal is to reach a state where the following facts hold: p2 is at l4-8, p0 is at l1-5, p1 is at l2-7, and p3 is at l1-1.", "question": "Given the plan: \"drive truck t4 from location l4-7 in city c4 to location l4-8 in the same city, place the object p2 into the truck t3 at location l3-1, drive truck t3 from location l3-1 in city c3 to location l3-0 in the same city, remove the object p2 from the truck t3 and place it on the location l3-0, drive truck t1 from location l1-6 in city c1 to location l1-8 in the same city, place the object p1 into the truck t1 at location l1-8, drive truck t1 from location l1-8 in city c1 to location l1-0 in the same city, remove the object p1 from the truck t1 and place it on the location l1-0, fly airplane a0 from airport l0-0 to airport l4-0, drive truck t2 from location l2-0 in city c2 to location l2-9 in the same city, place the object p3 into the truck t2 at location l2-9, drive truck t2 from location l2-9 in city c2 to location l2-4 in the same city, place the object p0 into the truck t2 at location l2-4, drive truck t2 from location l2-4 in city c2 to location l2-7 in the same city, fly airplane a0 from airport l4-0 to airport l3-0, load object p2 into airplane a0 at location l3-0, fly airplane a0 from airport l3-0 to airport l1-0, load object p1 into airplane a0 at location l1-0, fly airplane a0 from airport l1-0 to airport l2-0, remove the object p1 from the airplane a0 and place it on the location l2-0, drive truck t2 from location l2-7 in city c2 to location l2-0 in the same city, remove the object p3 from the truck t2 and place it on the location l2-0, place the object p1 into the truck t2 at location l2-0, remove the object p0 from the truck t2 and place it on the location l2-0, load object p3 into airplane a0 at location l2-0, load object p0 into airplane a0 at location l2-0, drive truck t2 from location l2-0 in city c2 to location l2-7 in the same city, remove the object p1 from the truck t2 and place it on the location l2-7, fly airplane a0 from airport l2-0 to airport l1-0, remove the object p3 from the airplane a0 and place it on the location l1-0, place the object p3 into the truck t1 at location l1-0, remove the object p0 from the airplane a0 and place it on the location l1-0, place the object p0 into the truck t1 at location l1-0, drive truck t1 from location l1-0 in city c1 to location l1-1 in the same city, remove the object p3 from the truck t1 and place it on the location l1-1, drive truck t1 from location l1-1 in city c1 to location l1-5 in the same city, remove the object p0 from the truck t1 and place it on the location l1-5, fly airplane a0 from airport l1-0 to airport l4-0, remove the object p2 from the airplane a0 and place it on the location l4-0, drive truck t4 from location l4-8 in city c4 to location l4-0 in the same city, place the object p2 into the truck t4 at location l4-0, drive truck t4 from location l4-0 in city c4 to location l4-8 in the same city, remove the object p2 from the truck t4 and place it on the location l4-8, drive truck t0 from location l0-2 in city c0 to location l0-5 in the same city, drive truck t0 from location l0-5 in city c0 to location l0-7 in the same city\"; can the following action be removed from this plan and still have a valid plan: drive truck t1 from location l1-6 in city c1 to location l1-8 in the same city?", "answer": "no"}
{"id": 6244544779699261366, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l2-2, l2-1, and l2-0 are in c2; l0-0, l0-2, and l0-1 are in c0. Currently, t0, a0, and p0 are at l0-0, p3 and p1 are at l2-0, p4 is at l1-0, t2 is at l2-1, t1 is at l1-1, p2 is at l0-1. The goal is to reach a state where the following facts hold: p2 is at l1-2, p1 is at l1-0, p0 is at l1-2, p3 is at l1-2, and p4 is at l0-0.", "question": "Given the plan: \"navigate the truck t1 from location l1-1 in city c1 to location l1-0 in the same city, place the object p0 onto the airplane a0 at location l0-0, operate the airplane a0 from airport l0-0 to airport l2-0, place the object p1 onto the airplane a0 at location l2-0, place the object p3 onto the airplane a0 at location l2-0, operate the airplane a0 from airport l2-0 to airport l1-0, offload the object p0 from the airplane a0 at location l1-0, load object p0 into truck t1 at location l1-0, offload the object p1 from the airplane a0 at location l1-0, offload the object p3 from the airplane a0 at location l1-0, load object p3 into truck t1 at location l1-0, place the object p4 onto the airplane a0 at location l1-0, navigate the truck t1 from location l1-0 in city c1 to location l1-2 in the same city, offload the object p0 from the truck t1 at location l1-2, offload the object p3 from the truck t1 at location l1-2, navigate the truck t0 from location l0-0 in city c0 to location l0-1 in the same city, load object p2 into truck t0 at location l0-1, navigate the truck t0 from location l0-1 in city c0 to location l0-0 in the same city, offload the object p2 from the truck t0 at location l0-0, operate the airplane a0 from airport l1-0 to airport l0-0, place the object p2 onto the airplane a0 at location l0-0, offload the object p4 from the airplane a0 at location l0-0, operate the airplane a0 from airport l0-0 to airport l1-0, offload the object p2 from the airplane a0 at location l1-0, navigate the truck t1 from location l1-2 in city c1 to location l1-0 in the same city, load object p2 into truck t1 at location l1-0, navigate the truck t1 from location l1-0 in city c1 to location l1-2 in the same city, offload the object p2 from the truck t1 at location l1-2, navigate the truck t1 from location l1-2 in city c1 to location l1-1 in the same city\"; can the following action be removed from this plan and still have a valid plan: navigate the truck t1 from location l1-2 in city c1 to location l1-1 in the same city?", "answer": "yes"}
{"id": 7660916037436323851, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l2-2, l2-1, and l2-0 are in c2; l0-0, l0-2, and l0-1 are in c0. Currently, t0, a0, and p0 are at l0-0, p3 and p1 are at l2-0, p4 is at l1-0, t2 is at l2-1, t1 is at l1-1, p2 is at l0-1. The goal is to reach a state where the following facts hold: p2 is at l1-2, p1 is at l1-0, p0 is at l1-2, p3 is at l1-2, and p4 is at l0-0.", "question": "Given the plan: \"drive the truck t1 in city c1 from location l1-1 to location l1-0, place the object p0 onto the airplane a0 at location l0-0, operate the airplane a0 from airport l0-0 to airport l2-0, place the object p1 onto the airplane a0 at location l2-0, place the object p3 onto the airplane a0 at location l2-0, operate the airplane a0 from airport l2-0 to airport l1-0, unload the object p0 from the airplane a0 at location l1-0, place the object p0 into the truck t1 at location l1-0, unload the object p1 from the airplane a0 at location l1-0, unload the object p3 from the airplane a0 at location l1-0, place the object p3 into the truck t1 at location l1-0, place the object p4 onto the airplane a0 at location l1-0, drive the truck t1 in city c1 from location l1-0 to location l1-2, unload the object p0 from the truck t1 at location l1-2, unload the object p3 from the truck t1 at location l1-2, drive the truck t0 in city c0 from location l0-0 to location l0-1, place the object p2 into the truck t0 at location l0-1, drive the truck t0 in city c0 from location l0-1 to location l0-0, unload the object p2 from the truck t0 at location l0-0, operate the airplane a0 from airport l1-0 to airport l0-0, place the object p2 onto the airplane a0 at location l0-0, unload the object p4 from the airplane a0 at location l0-0, operate the airplane a0 from airport l0-0 to airport l1-0, unload the object p2 from the airplane a0 at location l1-0, drive the truck t1 in city c1 from location l1-2 to location l1-0, place the object p2 into the truck t1 at location l1-0, drive the truck t1 in city c1 from location l1-0 to location l1-2, unload the object p2 from the truck t1 at location l1-2, drive the truck t1 in city c1 from location l1-2 to location l1-1\"; can the following action be removed from this plan and still have a valid plan: drive the truck t1 in city c1 from location l1-2 to location l1-0?", "answer": "no"}
{"id": 2033626071072718835, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, t0, a0, and p3 are at l0-0, p2 and p0 are at l0-1, p1 and t1 are at l1-0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p0 is at l1-1, p2 is at l1-0, and p3 is at l1-1.", "question": "Given the plan: \"load the object p1 from location l1-0 into the truck t1, load object p3 into airplane a0 at location l0-0, navigate the truck t0 from location l0-0 in city c0 to location l0-1 in the same city, load the object p0 from location l0-1 into the truck t0, load the object p2 from location l0-1 into the truck t0, fly the airplane a0 from location l0-0 to location l1-0, unload the object p3 from the airplane a0 at location l1-0, load the object p3 from location l1-0 into the truck t1, navigate the truck t1 from location l1-0 in city c1 to location l1-1 in the same city, offload the object p1 from the truck t1 at location l1-1, offload the object p3 from the truck t1 at location l1-1, navigate the truck t0 from location l0-1 in city c0 to location l0-0 in the same city, offload the object p0 from the truck t0 at location l0-0, load the object p0 from location l0-0 into the truck t0, offload the object p2 from the truck t0 at location l0-0, offload the object p0 from the truck t0 at location l0-0, fly the airplane a0 from location l1-0 to location l0-0, load object p0 into airplane a0 at location l0-0, load object p2 into airplane a0 at location l0-0, fly the airplane a0 from location l0-0 to location l1-0, unload the object p0 from the airplane a0 at location l1-0, unload the object p2 from the airplane a0 at location l1-0, navigate the truck t1 from location l1-1 in city c1 to location l1-0 in the same city, load the object p0 from location l1-0 into the truck t1, navigate the truck t1 from location l1-0 in city c1 to location l1-1 in the same city, offload the object p0 from the truck t1 at location l1-1\"; can the following action be removed from this plan and still have a valid plan: navigate the truck t0 from location l0-0 in city c0 to location l0-1 in the same city?", "answer": "no"}
{"id": -5301120506305124744, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l2-2, l2-1, and l2-0 are in c2; l0-0, l0-2, and l0-1 are in c0. Currently, t0, a0, and p0 are at l0-0, p3 and p1 are at l2-0, p4 is at l1-0, t2 is at l2-1, t1 is at l1-1, p2 is at l0-1. The goal is to reach a state where the following facts hold: p2 is at l1-2, p1 is at l1-0, p0 is at l1-2, p3 is at l1-2, and p4 is at l0-0.", "question": "Given the plan: \"drive the truck t1 in city c1 from location l1-1 to location l1-0, place the object p0 onto the airplane a0 at location l0-0, fly the airplane a0 from location l0-0 to location l2-0, place the object p1 onto the airplane a0 at location l2-0, place the object p3 onto the airplane a0 at location l2-0, fly the airplane a0 from location l2-0 to location l1-0, remove the object p0 from the airplane a0 and place it on the location l1-0, load the object p0 from location l1-0 into the truck t1, remove the object p1 from the airplane a0 and place it on the location l1-0, remove the object p3 from the airplane a0 and place it on the location l1-0, load the object p3 from location l1-0 into the truck t1, place the object p4 onto the airplane a0 at location l1-0, drive the truck t1 in city c1 from location l1-0 to location l1-2, offload the object p0 from the truck t1 at location l1-2, offload the object p3 from the truck t1 at location l1-2, drive the truck t0 in city c0 from location l0-0 to location l0-1, load the object p2 from location l0-1 into the truck t0, drive the truck t0 in city c0 from location l0-1 to location l0-0, offload the object p2 from the truck t0 at location l0-0, fly the airplane a0 from location l1-0 to location l0-0, place the object p2 onto the airplane a0 at location l0-0, remove the object p4 from the airplane a0 and place it on the location l0-0, fly the airplane a0 from location l0-0 to location l1-0, remove the object p2 from the airplane a0 and place it on the location l1-0, drive the truck t1 in city c1 from location l1-2 to location l1-0, load the object p2 from location l1-0 into the truck t1, drive the truck t1 in city c1 from location l1-0 to location l1-2, offload the object p2 from the truck t1 at location l1-2, drive the truck t2 in city c2 from location l2-1 to location l2-2\"; can the following action be removed from this plan and still have a valid plan: fly the airplane a0 from location l2-0 to location l1-0?", "answer": "no"}
{"id": 2735848798350094006, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, t0, a0, and p3 are at l0-0, p2 and p0 are at l0-1, p1 and t1 are at l1-0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p0 is at l1-1, p2 is at l1-0, and p3 is at l1-1.", "question": "Given the plan: \"place the object p1 into the truck t1 at location l1-0, place the object p3 onto the airplane a0 at location l0-0, drive truck t0 from location l0-0 in city c0 to location l0-1 in the same city, place the object p0 into the truck t0 at location l0-1, place the object p2 into the truck t0 at location l0-1, fly the airplane a0 from location l0-0 to location l1-0, unload the object p3 from the airplane a0 at location l1-0, place the object p3 into the truck t1 at location l1-0, drive truck t1 from location l1-0 in city c1 to location l1-1 in the same city, unload object p1 from truck t1 at location l1-1, unload object p3 from truck t1 at location l1-1, drive truck t0 from location l0-1 in city c0 to location l0-0 in the same city, unload object p0 from truck t0 at location l0-0, unload object p2 from truck t0 at location l0-0, drive truck t0 from location l0-0 in city c0 to location l0-1 in the same city, drive truck t0 from location l0-1 in city c0 to location l0-0 in the same city, fly the airplane a0 from location l1-0 to location l0-0, place the object p0 onto the airplane a0 at location l0-0, place the object p2 onto the airplane a0 at location l0-0, fly the airplane a0 from location l0-0 to location l1-0, unload the object p0 from the airplane a0 at location l1-0, unload the object p2 from the airplane a0 at location l1-0, drive truck t1 from location l1-1 in city c1 to location l1-0 in the same city, place the object p0 into the truck t1 at location l1-0, drive truck t1 from location l1-0 in city c1 to location l1-1 in the same city, unload object p0 from truck t1 at location l1-1\"; can the following action be removed from this plan and still have a valid plan: drive truck t0 from location l0-1 in city c0 to location l0-0 in the same city?", "answer": "yes"}
{"id": -7752095510866846986, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l2-2, l2-1, and l2-0 are in c2; l0-0, l0-2, and l0-1 are in c0. Currently, t0, a0, and p0 are at l0-0, p3 and p1 are at l2-0, p4 is at l1-0, t2 is at l2-1, t1 is at l1-1, p2 is at l0-1. The goal is to reach a state where the following facts hold: p2 is at l1-2, p1 is at l1-0, p0 is at l1-2, p3 is at l1-2, and p4 is at l0-0.", "question": "Given the plan: \"navigate the truck t1 from its current location l1-1 in city c1 to the new location l1-0 within the same city, load object p0 into airplane a0 at location l0-0, navigate the truck t0 from its current location l0-0 in city c0 to the new location l0-1 within the same city, load the object p2 from location l0-1 into the truck t0, fly the airplane a0 from location l0-0 to location l2-0, load object p1 into airplane a0 at location l2-0, load object p3 into airplane a0 at location l2-0, fly the airplane a0 from location l2-0 to location l1-0, offload the object p0 from the airplane a0 at location l1-0, load the object p0 from location l1-0 into the truck t1, offload the object p1 from the airplane a0 at location l1-0, offload the object p3 from the airplane a0 at location l1-0, load the object p3 from location l1-0 into the truck t1, load object p4 into airplane a0 at location l1-0, navigate the truck t1 from its current location l1-0 in city c1 to the new location l1-2 within the same city, offload the object p0 from the truck t1 at location l1-2, offload the object p3 from the truck t1 at location l1-2, navigate the truck t0 from its current location l0-1 in city c0 to the new location l0-0 within the same city, offload the object p2 from the truck t0 at location l0-0, fly the airplane a0 from location l1-0 to location l0-0, load object p2 into airplane a0 at location l0-0, offload the object p4 from the airplane a0 at location l0-0, fly the airplane a0 from location l0-0 to location l1-0, offload the object p2 from the airplane a0 at location l1-0, navigate the truck t1 from its current location l1-2 in city c1 to the new location l1-0 within the same city, load the object p2 from location l1-0 into the truck t1, navigate the truck t1 from its current location l1-0 in city c1 to the new location l1-2 within the same city, offload the object p2 from the truck t1 at location l1-2, navigate the truck t2 from its current location l2-1 in city c2 to the new location l2-2 within the same city\"; can the following action be removed from this plan and still have a valid plan: navigate the truck t2 from its current location l2-1 in city c2 to the new location l2-2 within the same city?", "answer": "yes"}
{"id": 5868471440508966448, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, t0, p3, and p0 are at l0-0, p1 is at l0-1, a0 and t1 are at l1-0, p2 is at l1-1. The goal is to reach a state where the following facts hold: p1 is at l0-1, p2 is at l0-0, p3 is at l0-0, and p0 is at l0-1.", "question": "Given the plan: \"place the object p0 into the truck t0 at location l0-0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-1 in the same city, place the object p2 into the truck t1 at location l1-1, navigate the truck t1 which is in location l1-1 in city c1 to another location l1-0 in the same city, remove the object p2 from the truck t1 and place it on the location l1-0, place the object p2 onto the airplane a0 at location l1-0, fly airplane a0 from airport l1-0 to airport l0-0, offload the object p2 from the airplane a0 at location l0-0, place the object p2 into the truck t0 at location l0-0, remove the object p2 from the truck t0 and place it on the location l0-0, navigate the truck t0 which is in location l0-0 in city c0 to another location l0-1 in the same city, remove the object p0 from the truck t0 and place it on the location l0-1\"; can the following action be removed from this plan and still have a valid plan: remove the object p2 from the truck t1 and place it on the location l1-0?", "answer": "no"}
{"id": 4319533793500658709, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 4 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l2-2, l2-1, and l2-0 are in c2; l0-0, l0-2, and l0-1 are in c0. Currently, t0 is at l0-2, p0 and a0 are at l2-0, p3 is at l2-1, t1 and p1 are at l1-2, p2 is at l0-1, t2 is at l2-2. The goal is to reach a state where the following facts hold: p1 is at l2-0, p2 is at l2-2, p0 is at l2-1, and p3 is at l1-2.", "question": "Given the plan: \"place the object p1 into the truck t1 at location l1-2, drive the truck t0 in city c0 from location l0-2 to location l0-1, place the object p2 into the truck t0 at location l0-1, drive the truck t0 in city c0 from location l0-1 to location l0-0, offload the object p2 from the truck t0 at location l0-0, drive the truck t2 in city c2 from location l2-2 to location l2-0, place the object p0 into the truck t2 at location l2-0, drive the truck t2 in city c2 from location l2-0 to location l2-1, offload the object p0 from the truck t2 at location l2-1, place the object p3 into the truck t2 at location l2-1, drive the truck t1 in city c1 from location l1-2 to location l1-0, offload the object p1 from the truck t1 at location l1-0, operate the airplane a0 from airport l2-0 to airport l0-0, load object p2 into airplane a0 at location l0-0, operate the airplane a0 from airport l0-0 to airport l1-0, load object p1 into airplane a0 at location l1-0, drive the truck t2 in city c2 from location l2-1 to location l2-0, offload the object p3 from the truck t2 at location l2-0, operate the airplane a0 from airport l1-0 to airport l2-0, unload the object p1 from the airplane a0 at location l2-0, unload the object p2 from the airplane a0 at location l2-0, place the object p2 into the truck t2 at location l2-0, load object p3 into airplane a0 at location l2-0, drive the truck t2 in city c2 from location l2-0 to location l2-2, offload the object p2 from the truck t2 at location l2-2, operate the airplane a0 from airport l2-0 to airport l1-0, unload the object p3 from the airplane a0 at location l1-0, place the object p3 into the truck t1 at location l1-0, drive the truck t1 in city c1 from location l1-0 to location l1-2, offload the object p3 from the truck t1 at location l1-2, drive the truck t2 in city c2 from location l2-2 to location l2-1\"; can the following action be removed from this plan and still have a valid plan: drive the truck t2 in city c2 from location l2-0 to location l2-1?", "answer": "no"}
{"id": -5132796709481820963, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, t0, p3, and p0 are at l0-0, p1 is at l0-1, a0 and t1 are at l1-0, p2 is at l1-1. The goal is to reach a state where the following facts hold: p1 is at l0-1, p2 is at l0-0, p3 is at l0-0, and p0 is at l0-1.", "question": "Given the plan: \"drive the truck t1 in city c1 from location l1-0 to location l1-1, load object p2 into truck t1 at location l1-1, drive the truck t1 in city c1 from location l1-1 to location l1-0, offload the object p2 from the truck t1 at location l1-0, load object p2 into truck t1 at location l1-0, load object p0 into truck t0 at location l0-0, drive the truck t0 in city c0 from location l0-0 to location l0-1, offload the object p2 from the truck t1 at location l1-0, load the object p2 from location l1-0 into the airplane a0, offload the object p0 from the truck t0 at location l0-1, fly the airplane a0 from the airport l1-0 to the airport l0-0, unload object p2 from airplane a0 at location l0-0, drive the truck t0 in city c0 from location l0-1 to location l0-0\"; can the following action be removed from this plan and still have a valid plan: load the object p2 from location l1-0 into the airplane a0?", "answer": "no"}
{"id": 3083207491224888048, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, a0 is at l0-0, t1, p3, and p0 are at l1-2, p1 is at l1-1, t0 is at l0-2, p2 is at l1-0. The goal is to reach a state where the following facts hold: p2 is at l1-2, p1 is at l1-2, p3 is at l0-1, and p0 is at l0-2.", "question": "Given the plan: \"place the object p0 into the truck t1 at location l1-2, place the object p3 into the truck t1 at location l1-2, drive truck t1 from location l1-2 in city c1 to location l1-0 in the same city, unload object p0 from truck t1 at location l1-0, place the object p2 into the truck t1 at location l1-0, unload object p3 from truck t1 at location l1-0, drive truck t1 from location l1-0 in city c1 to location l1-1 in the same city, place the object p1 into the truck t1 at location l1-1, fly the airplane a0 from location l0-0 to location l1-0, load the object p0 from location l1-0 into the airplane a0, load the object p3 from location l1-0 into the airplane a0, drive truck t1 from location l1-1 in city c1 to location l1-2 in the same city, unload object p1 from truck t1 at location l1-2, unload object p2 from truck t1 at location l1-2, fly the airplane a0 from location l1-0 to location l0-0, offload the object p0 from the airplane a0 at location l0-0, offload the object p3 from the airplane a0 at location l0-0, drive truck t0 from location l0-2 in city c0 to location l0-0 in the same city, place the object p0 into the truck t0 at location l0-0, place the object p3 into the truck t0 at location l0-0, drive truck t0 from location l0-0 in city c0 to location l0-1 in the same city, unload object p3 from truck t0 at location l0-1, drive truck t0 from location l0-1 in city c0 to location l0-2 in the same city, unload object p0 from truck t0 at location l0-2, place the object p2 into the truck t1 at location l1-2, drive truck t1 from location l1-2 in city c1 to location l1-1 in the same city, unload object p2 from truck t1 at location l1-1, place the object p2 into the truck t1 at location l1-1, unload object p2 from truck t1 at location l1-1, place the object p2 into the truck t1 at location l1-1, unload object p2 from truck t1 at location l1-1, place the object p2 into the truck t1 at location l1-1, drive truck t1 from location l1-1 in city c1 to location l1-2 in the same city, unload object p2 from truck t1 at location l1-2\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: unload object p2 from truck t1 at location l1-1 and place the object p2 into the truck t1 at location l1-1?", "answer": "yes"}
{"id": 5404878503955522396, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, a0 and p0 are at l0-0, p3 and t0 are at l0-2, t1 is at l1-2, p1 is at l1-1, p2 is at l0-1. The goal is to reach a state where the following facts hold: p1 is at l1-1, p0 is at l1-1, p2 is at l1-0, and p3 is at l1-2.", "question": "Given the plan: \"navigate the truck t1 from location l1-2 in city c1 to location l1-0 in the same city, load object p0 into airplane a0 at location l0-0, place the object p3 into the truck t0 at location l0-2, navigate the truck t0 from location l0-2 in city c0 to location l0-1 in the same city, place the object p2 into the truck t0 at location l0-1, navigate the truck t0 from location l0-1 in city c0 to location l0-0 in the same city, offload the object p2 from the truck t0 at location l0-0, load object p2 into airplane a0 at location l0-0, offload the object p3 from the truck t0 at location l0-0, load object p3 into airplane a0 at location l0-0, fly the airplane a0 from airport l0-0 to airport l1-0, remove the object p0 from the airplane a0 and place it on the location l1-0, place the object p0 into the truck t1 at location l1-0, remove the object p3 from the airplane a0 and place it on the location l1-0, place the object p3 into the truck t1 at location l1-0, navigate the truck t1 from location l1-0 in city c1 to location l1-1 in the same city, navigate the truck t1 from location l1-1 in city c1 to location l1-2 in the same city, offload the object p3 from the truck t1 at location l1-2, navigate the truck t1 from location l1-2 in city c1 to location l1-0 in the same city, navigate the truck t1 from location l1-0 in city c1 to location l1-1 in the same city, remove the object p2 from the airplane a0 and place it on the location l1-0, offload the object p0 from the truck t1 at location l1-1\"; can the following action be removed from this plan and still have a valid plan: navigate the truck t1 from location l1-2 in city c1 to location l1-0 in the same city?", "answer": "no"}
