{"id": 6835978133384204769, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l3-0, l3-2, and l3-1 are in c3; l4-1, l4-2, and l4-0 are in c4; l0-1, l0-2, and l0-0 are in c0; l2-1, l2-2, and l2-0 are in c2. Currently, t4 is at l4-0, t3, a0, and p0 are at l3-0, p1 is at l3-1, t1 is at l1-0, t0 is at l0-2, t2 is at l2-1, p3 is in t3, p2 is in t2. The goal is to reach a state where the following facts hold: p0 is at l3-0, p3 is at l3-2, p2 is at l2-2, and p1 is at l3-2.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p2 is at l0-0. B. t0 is at l0-0. C. t2 is at l2-2. D. p3 is at l4-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p2 is at l0-0", "t0 is at l0-0", "t2 is at l2-2", "p3 is at l4-0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 4921639997979964023, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l3-0, l3-2, and l3-1 are in c3; l4-1, l4-2, and l4-0 are in c4; l0-1, l0-2, and l0-0 are in c0; l2-1, l2-2, and l2-0 are in c2. Currently, t4 is at l4-0, t3 is at l3-0, p3 is at l4-1, p0 and a0 are at l2-0, p2 and t2 are at l2-1, t0 is at l0-2, t1 is at l1-2, p1 is in t3. The goal is to reach a state where the following facts hold: p0 is at l3-0, p3 is at l3-2, p2 is at l2-2, and p1 is at l3-2.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p1 is at l4-2. B. a0 is at l4-0. C. p3 is at l1-2. D. t3 is at l3-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p1 is at l4-2", "a0 is at l4-0", "p3 is at l1-2", "t3 is at l3-1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 1658489319248825054, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l0-1, l0-2, and l0-0 are in c0; l2-1, l2-2, and l2-0 are in c2. Currently, p3, t1, and p0 are at l1-2, p2 and a0 are at l0-0, t2 is at l2-1, t0 is at l0-1, p1 is at l1-0, p4 is in a0. The goal is to reach a state where the following facts hold: p3 is at l1-2, p4 is at l0-0, p2 is at l1-2, p0 is at l1-2, and p1 is at l1-0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p3 is at l1-0. B. p2 is at l2-2. C. t1 is at l1-0. D. p4 is in t0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p3 is at l1-0", "p2 is at l2-2", "t1 is at l1-0", "p4 is in t0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 5690629149065232131, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l0-1, l0-2, and l0-0 are in c0. Currently, p3 and p1 are at l0-2, t0 is at l0-1, t1 is at l1-2, a0 is at l1-0, p0 is in t0, p2 is in a0. The goal is to reach a state where the following facts hold: p3 is at l0-1, p1 is at l0-1, p0 is at l0-1, and p2 is at l0-1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p0 is at l0-0. B. p3 is in t0. C. p1 is at l1-0. D. p1 is at l0-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p0 is at l0-0", "p3 is in t0", "p1 is at l1-0", "p1 is at l0-0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -1264303715999081777, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l0-1, l0-2, and l0-0 are in c0; l2-1, l2-2, and l2-0 are in c2. Currently, p3 and p1 are at l2-0, t0, p2, and a0 are at l0-0, t2 is at l2-1, t1 is at l1-1, p4 is at l1-0, p0 is in a0. The goal is to reach a state where the following facts hold: p3 is at l1-2, p4 is at l0-0, p2 is at l1-2, p0 is at l1-2, and p1 is at l1-0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p0 is at l2-1. B. t0 is at l0-2. C. p4 is at l1-1. D. p2 is in a0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p0 is at l2-1", "t0 is at l0-2", "p4 is at l1-1", "p2 is in a0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -4380647070619639126, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l0-1, l0-2, and l0-0 are in c0. Currently, p2 and a0 are at l1-0, t0 is at l0-2, t1 is at l1-1, p3, p0, and p1 are in t0. The goal is to reach a state where the following facts hold: p3 is at l0-1, p1 is at l0-1, p0 is at l0-1, and p2 is at l0-1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p3 is in a0. B. p0 is at l0-2. C. t0 is at l0-1. D. p3 is at l0-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p3 is in a0", "p0 is at l0-2", "t0 is at l0-1", "p3 is at l0-0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 1212016093787866341, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l0-4, l0-9, l0-3, l0-6, l0-1, l0-2, l0-8, l0-7, l0-5, and l0-0 are in c0; l1-0, l1-9, l1-5, l1-3, l1-2, l1-6, l1-8, l1-7, l1-1, and l1-4 are in c1; l3-0, l3-5, l3-3, l3-9, l3-1, l3-8, l3-2, l3-7, l3-4, and l3-6 are in c3; l4-1, l4-4, l4-2, l4-7, l4-8, l4-9, l4-0, l4-6, l4-3, and l4-5 are in c4; l2-9, l2-3, l2-6, l2-2, l2-0, l2-8, l2-1, l2-7, l2-5, and l2-4 are in c2. Currently, t4 is at l4-0, t3 and p2 are at l3-0, p3 is at l2-9, t2 is at l2-0, t0 is at l0-2, a0 is at l0-0, t1 is at l1-8, p0 is at l2-4, p1 is in t1. The goal is to reach a state where the following facts hold: p2 is at l4-8, p1 is at l2-7, p3 is at l1-1, and p0 is at l1-5.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p0 is in t1. B. p3 is at l1-3. C. p2 is at l1-8. D. p3 is at l1-2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p0 is in t1", "p3 is at l1-3", "p2 is at l1-8", "p3 is at l1-2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 1455248976243101813, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l0-1, l0-2, and l0-0 are in c0; l2-1, l2-2, and l2-0 are in c2. Currently, t0 is at l0-0, p2 is at l0-1, t2 is at l2-1, t1 and p0 are at l1-2, a0 and p1 are at l1-0, p3 and p4 are in a0. The goal is to reach a state where the following facts hold: p3 is at l1-2, p4 is at l0-0, p2 is at l1-2, p0 is at l1-2, and p1 is at l1-0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p2 is at l2-0. B. p0 is at l1-1. C. a0 is at l0-0. D. p0 is at l2-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p2 is at l2-0", "p0 is at l1-1", "a0 is at l0-0", "p0 is at l2-1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 8009344041751059892, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l3-0, l3-2, and l3-1 are in c3; l4-1, l4-2, and l4-0 are in c4; l0-1, l0-2, and l0-0 are in c0; l2-1, l2-2, and l2-0 are in c2. Currently, t3 is at l3-0, p3 is at l4-1, p1 is at l3-1, t0 is at l0-0, a0 is at l2-0, t4 is at l4-2, t1 is at l1-2, t2 is at l2-2, p0 is in a0, p2 is in t2. The goal is to reach a state where the following facts hold: p0 is at l3-0, p3 is at l3-2, p2 is at l2-2, and p1 is at l3-2.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p2 is at l4-1. B. p3 is at l1-2. C. p0 is at l1-0. D. t4 is at l4-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p2 is at l4-1", "p3 is at l1-2", "p0 is at l1-0", "t4 is at l4-1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 6138221031130765187, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l0-4, l0-9, l0-3, l0-6, l0-1, l0-2, l0-8, l0-7, l0-5, and l0-0 are in c0; l1-0, l1-9, l1-5, l1-3, l1-2, l1-6, l1-8, l1-7, l1-1, and l1-4 are in c1; l3-0, l3-5, l3-3, l3-9, l3-1, l3-8, l3-2, l3-7, l3-4, and l3-6 are in c3; l4-1, l4-4, l4-2, l4-7, l4-8, l4-9, l4-0, l4-6, l4-3, and l4-5 are in c4; l2-9, l2-3, l2-6, l2-2, l2-0, l2-8, l2-1, l2-7, l2-5, and l2-4 are in c2. Currently, t3 is at l3-0, t2 is at l2-7, t1 is at l1-0, a0 is at l2-0, t0 is at l0-2, t4 is at l4-2, p2 is at l4-8, p1 is in t2, p0 and p3 are in a0. The goal is to reach a state where the following facts hold: p2 is at l4-8, p1 is at l2-7, p3 is at l1-1, and p0 is at l1-5.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p1 is at l2-9. B. t1 is at l1-5. C. p0 is in t4. D. p3 is at l4-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p1 is at l2-9", "t1 is at l1-5", "p0 is in t4", "p3 is at l4-0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 4854602878089259828, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 4 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l0-1, l0-2, and l0-0 are in c0; l2-1, l2-2, and l2-0 are in c2. Currently, t2 is at l2-2, t0 is at l0-0, t1, a0, and p1 are at l1-0, p0 is at l2-1, p3 is in t2, p2 is in a0. The goal is to reach a state where the following facts hold: p3 is at l1-2, p0 is at l2-1, p2 is at l2-2, and p1 is at l2-0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p1 is at l1-2. B. p2 is at l2-0. C. p3 is at l0-1. D. p0 is in a0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p1 is at l1-2", "p2 is at l2-0", "p3 is at l0-1", "p0 is in a0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 5470290453309629164, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, t0 is at l0-0, t1, a0, and p2 are at l1-0, p1 is in t1, p3 and p0 are in t0. The goal is to reach a state where the following facts hold: p3 is at l0-1, p0 is at l0-0, p1 is at l1-0, and p2 is at l1-0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p2 is at l1-1. B. p3 is at l1-1. C. p2 is in t0. D. t0 is at l0-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p2 is at l1-1", "p3 is at l1-1", "p2 is in t0", "t0 is at l0-1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -3014630304077399433, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l0-1, l0-2, and l0-0 are in c0. Currently, p1, p2, and t1 are at l1-2, t0, p3, and a0 are at l0-0, p0 is in t0. The goal is to reach a state where the following facts hold: p0 is at l0-2, p3 is at l0-1, p1 is at l1-2, and p2 is at l1-2.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p0 is at l1-1. B. p0 is at l0-0. C. t0 is at l0-2. D. p3 is in t1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p0 is at l1-1", "p0 is at l0-0", "t0 is at l0-2", "p3 is in t1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 183656925876721434, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, t1 and p2 are at l1-0, t0 is at l0-1, a0 is at l0-0, p1 is in t1, p3 is in t0, p0 is in a0. The goal is to reach a state where the following facts hold: p3 is at l0-1, p0 is at l0-0, p1 is at l1-0, and p2 is at l1-0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. p2 is at l0-0. B. p0 is in t1. C. p1 is at l1-0. D. p3 is in a0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p2 is at l0-0", "p0 is in t1", "p1 is at l1-0", "p3 is in a0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 3682953426833486922, "group": "landmarks_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l0-1, l0-2, and l0-0 are in c0. Currently, t0, p0, and a0 are at l0-0, t1 is at l1-0, p1 is at l1-1, p3 is in a0, p2 is in t0. The goal is to reach a state where the following facts hold: p2 is at l1-0, p0 is at l1-1, p3 is at l1-2, and p1 is at l1-1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. t0 is at l0-2. B. a0 is at l1-0. C. p2 is at l1-1. D. p3 is at l0-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["t0 is at l0-2", "a0 is at l1-0", "p2 is at l1-1", "p3 is at l0-0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
