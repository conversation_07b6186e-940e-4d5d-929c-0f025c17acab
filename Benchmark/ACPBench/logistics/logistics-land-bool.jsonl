{"id": 4760393164654511652, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l2-3, l2-6, l2-1, l2-2, l2-8, l2-0, l2-7, l2-5, l2-4, and l2-9 are in c2; l4-1, l4-9, l4-8, l4-7, l4-5, l4-0, l4-4, l4-2, l4-6, and l4-3 are in c4; l1-4, l1-6, l1-3, l1-8, l1-2, l1-9, l1-1, l1-7, l1-5, and l1-0 are in c1; l3-4, l3-9, l3-5, l3-6, l3-0, l3-7, l3-8, l3-1, l3-2, and l3-3 are in c3; l0-7, l0-2, l0-1, l0-9, l0-0, l0-3, l0-8, l0-6, l0-5, and l0-4 are in c0. Currently, t3 is at l3-0, t1 and a0 are at l1-0, p2 and t4 are at l4-8, t0 is at l0-2, p1 and t2 are at l2-7, p0 and p3 are in t1. The goal is to reach a state where the following facts hold: p3 is at l1-1, p1 is at l2-7, p2 is at l4-8, and p0 is at l1-5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p0 is at l0-8", "answer": "no"}
{"id": 6140912279185871593, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-0 and l0-1 are in c0. Currently, t1, p1, and p0 are at l1-1, a0 is at l1-0, t0 is at l0-0, p3 is in t1, p2 is in a0. The goal is to reach a state where the following facts hold: p3 is at l1-1, p2 is at l1-0, p0 is at l1-1, and p1 is at l1-1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p2 is at l1-0", "answer": "yes"}
{"id": -7002266798889710210, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l3-1, l3-2, and l3-0 are in c3; l4-2, l4-1, and l4-0 are in c4; l0-0, l0-2, and l0-1 are in c0; l2-2, l2-0, and l2-1 are in c2; l1-1, l1-0, and l1-2 are in c1. Currently, t2 is at l2-0, a0 and t4 are at l4-0, t3 is at l3-0, t0 is at l0-1, t1 is at l1-2, p1 is at l3-1, p3 is in t4, p2 is in t2, p0 is in a0. The goal is to reach a state where the following facts hold: p2 is at l2-2, p1 is at l3-2, p3 is at l3-2, and p0 is at l3-0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? t4 is at l4-1", "answer": "no"}
{"id": 7824270701713909489, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-0 and l0-1 are in c0. Currently, t1, p1, and p3 are at l1-1, a0 is at l1-0, t0 is at l0-0, p2 and p0 are in a0. The goal is to reach a state where the following facts hold: p3 is at l1-1, p2 is at l1-0, p0 is at l1-1, and p1 is at l1-1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p3 is in t1", "answer": "no"}
{"id": -4176113523658852727, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l2-2, l2-0, and l2-1 are in c2; l1-1, l1-0, and l1-2 are in c1. Currently, p1, t1, and a0 are at l1-0, t0 is at l0-1, t2 is at l2-1, p2 is in t0, p4 is in a0, p0 and p3 are in t1. The goal is to reach a state where the following facts hold: p1 is at l1-0, p0 is at l1-2, p3 is at l1-2, p2 is at l1-2, and p4 is at l0-0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p2 is at l0-0", "answer": "yes"}
{"id": 4949058973911648679, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l2-3, l2-6, l2-1, l2-2, l2-8, l2-0, l2-7, l2-5, l2-4, and l2-9 are in c2; l4-1, l4-9, l4-8, l4-7, l4-5, l4-0, l4-4, l4-2, l4-6, and l4-3 are in c4; l1-4, l1-6, l1-3, l1-8, l1-2, l1-9, l1-1, l1-7, l1-5, and l1-0 are in c1; l3-4, l3-9, l3-5, l3-6, l3-0, l3-7, l3-8, l3-1, l3-2, and l3-3 are in c3; l0-7, l0-2, l0-1, l0-9, l0-0, l0-3, l0-8, l0-6, l0-5, and l0-4 are in c0. Currently, t1 is at l1-1, t2, p1, and a0 are at l2-0, t3 is at l3-0, p2 and t4 are at l4-8, p0 is at l1-5, t0 is at l0-2, p3 is in t1. The goal is to reach a state where the following facts hold: p3 is at l1-1, p1 is at l2-7, p2 is at l4-8, and p0 is at l1-5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p3 is at l3-0", "answer": "no"}
{"id": -3824349965901793719, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l3-1, l3-2, and l3-0 are in c3; l4-2, l4-1, and l4-0 are in c4; l0-0, l0-2, and l0-1 are in c0; l2-2, l2-0, and l2-1 are in c2; l1-1, l1-0, and l1-2 are in c1. Currently, p3, a0, and t4 are at l4-0, t3 is at l3-0, t0 is at l0-2, t2 and p2 are at l2-1, t1 is at l1-2, p1 is at l3-1, p0 is in a0. The goal is to reach a state where the following facts hold: p2 is at l2-2, p1 is at l3-2, p3 is at l3-2, and p0 is at l3-0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? a0 is at l3-0", "answer": "yes"}
{"id": -1463532256608449579, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l2-3, l2-6, l2-1, l2-2, l2-8, l2-0, l2-7, l2-5, l2-4, and l2-9 are in c2; l4-1, l4-9, l4-8, l4-7, l4-5, l4-0, l4-4, l4-2, l4-6, and l4-3 are in c4; l1-4, l1-6, l1-3, l1-8, l1-2, l1-9, l1-1, l1-7, l1-5, and l1-0 are in c1; l3-4, l3-9, l3-5, l3-6, l3-0, l3-7, l3-8, l3-1, l3-2, and l3-3 are in c3; l0-7, l0-2, l0-1, l0-9, l0-0, l0-3, l0-8, l0-6, l0-5, and l0-4 are in c0. Currently, t1 and p3 are at l1-1, t3 is at l3-0, p2 and t4 are at l4-8, a0 is at l1-0, t0 is at l0-2, p1 and t2 are at l2-7, p0 is in t1. The goal is to reach a state where the following facts hold: p3 is at l1-1, p1 is at l2-7, p2 is at l4-8, and p0 is at l1-5.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? t1 is at l1-5", "answer": "yes"}
{"id": 6508986716181259099, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-1, l1-0, and l1-2 are in c1. Currently, t1 is at l1-1, p3 and p1 are at l0-2, a0 and p2 are at l1-0, p0 and t0 are at l0-0. The goal is to reach a state where the following facts hold: p1 is at l0-1, p0 is at l0-1, p2 is at l0-1, and p3 is at l0-1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p2 is in a0", "answer": "yes"}
{"id": 4506251736750648730, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l2-2, l2-0, and l2-1 are in c2; l1-1, l1-0, and l1-2 are in c1. Currently, t1 and p4 are at l1-0, p1 and p3 are at l2-0, p2 is at l0-1, p0, t0, and a0 are at l0-0, t2 is at l2-1. The goal is to reach a state where the following facts hold: p1 is at l1-0, p0 is at l1-2, p3 is at l1-2, p2 is at l1-2, and p4 is at l0-0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p1 is at l2-1", "answer": "no"}
{"id": -4022268800635228721, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-0 and l0-1 are in c0. Currently, p1, t1, p0, and p2 are at l1-0, a0 and t0 are at l0-0, p3 is in t1. The goal is to reach a state where the following facts hold: p3 is at l0-1, p0 is at l0-0, p2 is at l1-0, and p1 is at l1-0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p2 is at l0-1", "answer": "no"}
{"id": 3315077574652305966, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-0 and l0-1 are in c0. Currently, p1, t1, and p2 are at l1-0, t0 is at l0-1, a0 is at l0-0, p3 is in t0, p0 is in a0. The goal is to reach a state where the following facts hold: p3 is at l0-1, p0 is at l0-0, p2 is at l1-0, and p1 is at l1-0.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p1 is in a0", "answer": "no"}
{"id": -2702806323296001186, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-1, l1-0, and l1-2 are in c1. Currently, p3 is at l0-1, t0 and p0 are at l0-2, t1 and p2 are at l1-2, a0 is at l0-0, p1 is in t1. The goal is to reach a state where the following facts hold: p3 is at l0-1, p2 is at l1-2, p1 is at l1-2, and p0 is at l0-2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p2 is at l0-0", "answer": "no"}
{"id": 9058216856046031582, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-1, l1-0, and l1-2 are in c1. Currently, t1 is at l1-1, p0, p3, a0, and t0 are at l0-0, p1 and p2 are in t1. The goal is to reach a state where the following facts hold: p3 is at l0-1, p2 is at l1-2, p1 is at l1-2, and p0 is at l0-2.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p2 is at l0-0", "answer": "no"}
{"id": 619848753361065374, "group": "landmarks_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-2, and l0-1 are in c0; l1-1, l1-0, and l1-2 are in c1. Currently, p1 is at l1-1, t1, a0, p2, and p3 are at l1-0, t0 is at l0-1, p0 is in t1. The goal is to reach a state where the following facts hold: p3 is at l1-2, p2 is at l1-0, p0 is at l1-1, and p1 is at l1-1.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? p1 is at l0-2", "answer": "no"}
