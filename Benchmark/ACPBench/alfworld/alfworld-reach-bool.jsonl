{"id": -2019104209955033347, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. stoveburner4 and stoveburner2 are at location21. drawer1 is at location20. microwave1 is at location24. cabinet5 is at location13. countertop3 is at location3. countertop2 and coffeemachine1 are at location11. garbagecan1 is at location8. drawer3 is at location27. countertop1 is at location2. cabinet3 is at location16. cabinet6 is at location4. cabinet1 is at location19. shelf2 is at location29. shelf1 is at location17. fridge1 is at location10. shelf3 is at location26. cabinet4 is at location14. sinkbasin1 is at location6. drawer2 is at location15. cabinet2 is at location22. toaster1 is at location9.  Currently, the objects are at locations as follows. pan1 and spoon1 are at location11. glassbottle3, potato3, and apple2 are at location8. pot1 is at location5. tomato1, potato1, egg2, mug1, plate2, bowl1, and egg1 are at location10. dishsponge1 and fork1 are at location27. papertowelroll1, cellphone3, spoon2, glassbottle1, butterknife1, knife2, bread1, plate3, creditcard1, spatula1, mug2, and houseplant1 are at location3. window1 is at location30. plate1, peppershaker1, lettuce1, knife1, lettuce2, cellphone1, cellphone2, and apple1 are at location2. stoveknob1 is at location18. lightswitch1 is at location25. window2 is at location28. chair2 is at location1. potato2, spatula2, glassbottle2, spatula3, and sink1 are at location6. peppershaker3 is at location16. statue1 and soapbottle2 are at location26. bowl2 is at location29. saltshaker1 is at location15. chair1 is at location23. dishsponge2 and vase1 are at location13. peppershaker2 is at location20. soapbottle1 is at location4. cup1 is at location24. stoveknob4 is at location7. vase2 is at location17. stoveknob2 and stoveknob3 are at location12. agent agent1 is at location location3. The objects are in/on receptacle as follows. lettuce1, lettuce2, knife1, plate1, cellphone2, cellphone1, apple1, and peppershaker1 are on countertop1. mug2, butterknife1, creditcard1, cellphone3, spoon2, glassbottle1, houseplant1, bread1, spatula1, plate3, knife2, and papertowelroll1 are on countertop3. mug1, potato1, tomato1, egg1, plate2, egg2, and bowl1 are in fridge1. saltshaker1 is in drawer2. dishsponge2 and vase1 are in cabinet5. peppershaker2 is in drawer1. pan1 is on stoveburner2. spatula2, spatula3, glassbottle2, and potato2 are in sinkbasin1. pan1 and spoon1 are on countertop2. soapbottle2 and statue1 are on shelf3. pot1 is on stoveburner3. glassbottle3, apple2, and potato3 are in garbagecan1. pan1 is on stoveburner4. vase2 is on shelf1. soapbottle1 is in cabinet6. fork1 and dishsponge1 are in drawer3. peppershaker3 is in cabinet3. cup1 is in microwave1. pot1 is on stoveburner1. bowl2 is on shelf2. cellphone2 is on plate1. drawer2, drawer3, microwave1, cabinet5, cabinet2, drawer1, fridge1, and cabinet1 are closed. mug1 is cool. Nothing has been validated. agent1's hands are empty. ", "question": "Is it possible to transition to a state where the following holds: It has been validated that an object of type papertowelrolltype is examined under an object of type plungertype?", "answer": "no"}
{"id": -3858097941701578166, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. stoveburner4 and stoveburner2 are at location21. drawer1 is at location20. microwave1 is at location24. cabinet5 is at location13. countertop3 is at location3. countertop2 and coffeemachine1 are at location11. garbagecan1 is at location8. drawer3 is at location27. countertop1 is at location2. cabinet3 is at location16. cabinet6 is at location4. cabinet1 is at location19. shelf2 is at location29. shelf1 is at location17. fridge1 is at location10. shelf3 is at location26. cabinet4 is at location14. sinkbasin1 is at location6. drawer2 is at location15. cabinet2 is at location22. toaster1 is at location9.  Currently, the objects are at locations as follows. pan1 and spoon1 are at location11. glassbottle3, potato3, and apple2 are at location8. pot1 is at location5. tomato1, potato1, egg2, mug1, plate2, bowl1, and egg1 are at location10. dishsponge1 and fork1 are at location27. papertowelroll1, cellphone3, spoon2, glassbottle1, butterknife1, knife2, bread1, plate3, creditcard1, spatula1, mug2, and houseplant1 are at location3. window1 is at location30. plate1, peppershaker1, lettuce1, knife1, lettuce2, cellphone1, cellphone2, and apple1 are at location2. stoveknob1 is at location18. lightswitch1 is at location25. window2 is at location28. chair2 is at location1. potato2, spatula2, glassbottle2, spatula3, and sink1 are at location6. peppershaker3 is at location16. statue1 and soapbottle2 are at location26. bowl2 is at location29. saltshaker1 is at location15. chair1 is at location23. dishsponge2 and vase1 are at location13. peppershaker2 is at location20. soapbottle1 is at location4. cup1 is at location24. stoveknob4 is at location7. vase2 is at location17. stoveknob2 and stoveknob3 are at location12. agent agent1 is at location location15. The objects are in/on receptacle as follows. lettuce1, lettuce2, knife1, plate1, cellphone2, cellphone1, apple1, and peppershaker1 are on countertop1. mug2, butterknife1, creditcard1, cellphone3, spoon2, glassbottle1, houseplant1, bread1, spatula1, plate3, knife2, and papertowelroll1 are on countertop3. mug1, potato1, tomato1, egg1, plate2, egg2, and bowl1 are in fridge1. saltshaker1 is in drawer2. dishsponge2 and vase1 are in cabinet5. peppershaker2 is in drawer1. pan1 is on stoveburner2. spatula2, spatula3, glassbottle2, and potato2 are in sinkbasin1. pan1 and spoon1 are on countertop2. soapbottle2 and statue1 are on shelf3. pot1 is on stoveburner3. glassbottle3, apple2, and potato3 are in garbagecan1. pan1 is on stoveburner4. vase2 is on shelf1. soapbottle1 is in cabinet6. fork1 and dishsponge1 are in drawer3. peppershaker3 is in cabinet3. cup1 is in microwave1. pot1 is on stoveburner1. bowl2 is on shelf2. cellphone2 is on plate1. drawer2, drawer3, microwave1, cabinet5, cabinet2, drawer1, fridge1, and cabinet1 are closed. mug1 is cool. Nothing has been validated. agent1's hands are empty. ", "question": "Is it possible to transition to a state where the following holds: agent agent1 is at location location19 and It has been validated that an object of type bowltype is in a receptacle of type fridgetype?", "answer": "yes"}
{"id": -2316437304382535862, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet2 is at location11. toilet1 is at location7. cabinet3 is at location8. garbagecan1 is at location2. countertop1 is at location3. cabinet4 is at location15. sinkbasin2 is at location6. handtowelholder1 is at location18. handtowelholder2 is at location17. towelholder1 is at location5. sinkbasin1 is at location16. cabinet1 is at location4. toiletpaperhanger1 is at location10.  Currently, the objects are at locations as follows. spraybottle1 and soapbottle2 are at location4. toiletpaper1 and soapbar1 are at location7. plunger1 and scrubbrush1 are at location10. soapbottle1 and spraybottle2 are at location15. spraybottle3 is at location2. mirror1 is at location9. cloth2 is at location11. sink2 is at location14. handtowel1 is at location18. lightswitch1 is at location13. cloth1 and candle1 are at location3. sink1 is at location12. towel1 and showerglass1 are at location5. showerdoor1 is at location1. toiletpaper2 is at location8. handtowel2 is at location17. agent agent1 is at location location18. The objects are in/on receptacle as follows. toiletpaper1 and soapbar1 are in toilet1. spraybottle1 and soapbottle2 are in cabinet1. toiletpaper2 is in cabinet3. candle1 and cloth1 are on countertop1. spraybottle3 is in garbagecan1. soapbottle1 and spraybottle2 are in cabinet4. handtowel2 is on handtowelholder2. handtowel1 is on handtowelholder1. cloth2 is in cabinet2. towel1 is on towelholder1. cabinet2, cabinet1, cabinet4, and cabinet3 are closed. soapbar2 is clean. It has been validated that an object of type toiletpapertype is in a receptacle of type toilettype. agent1 is holding object soapbar2. ", "question": "Is it possible to transition to a state where the following holds: Nothing has been validated?", "answer": "no"}
{"id": -5684355342343410798, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. drawer3 is at location17. drawer2 is at location18. shelf2 is at location25. desk2 is at location10. shelf5 is at location22. shelf3 is at location11. garbagecan1 is at location2. drawer6 is at location1. desk1 is at location3. drawer1 is at location21. safe1 is at location6. drawer5 and drawer4 are at location12. shelf1 is at location20. bed1 is at location13. laundryhamper1 is at location8. shelf6 is at location24. shelf4 is at location23.  Currently, the objects are at locations as follows. pillow1, book1, pillow2, laptop2, and laptop1 are at location13. laundryhamperlid1 is at location8. keychain1 and keychain2 are at location6. mug2, cellphone2, cd3, pen1, and pencil3 are at location10. window1 is at location5. blinds1 is at location16. desklamp1, alarmclock3, and bowl2 are at location23. lightswitch1 is at location14. creditcard1 and pencil2 are at location22. cd1, bowl1, alarmclock1, pencil1, and mug1 are at location3. chair2 is at location26. blinds2 is at location15. baseballbat1 is at location9. cd2 is at location2. chair1 is at location21. cellphone3 is at location12. mirror1 is at location19. window2 is at location4. alarmclock2 is at location11. bowl3 is at location24. basketball1 is at location7. agent agent1 is at location location13. The objects are in/on receptacle as follows. pencil1, mug1, alarmclock1, bowl1, and cd1 are on desk1. laptop2, laptop1, book1, pillow2, and pillow1 are in bed1. cd2 is in garbagecan1. creditcard1 and pencil2 are on shelf5. desklamp1, alarmclock3, cd3, pen1, pencil3, mug2, cellphone2, and bowl2 are on desk2. alarmclock3, desklamp1, and bowl2 are on shelf4. cellphone3 is in drawer5. keychain1 and keychain2 are in safe1. bowl3 is on shelf6. alarmclock2 is on shelf3. drawer1, drawer3, safe1, and drawer6 are closed. desklamp1 is off. Nothing has been validated. agent1 is holding object cellphone1. ", "question": "Is it possible to transition to a state where the following holds: It has been validated that an object of type pillowtype is in a receptacle of type bedtype?", "answer": "yes"}
{"id": -2381222796914070965, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. stoveburner4 and stoveburner2 are at location21. drawer1 is at location20. microwave1 is at location24. cabinet5 is at location13. countertop3 is at location3. countertop2 and coffeemachine1 are at location11. garbagecan1 is at location8. drawer3 is at location27. countertop1 is at location2. cabinet3 is at location16. cabinet6 is at location4. cabinet1 is at location19. shelf2 is at location29. shelf1 is at location17. fridge1 is at location10. shelf3 is at location26. cabinet4 is at location14. sinkbasin1 is at location6. drawer2 is at location15. cabinet2 is at location22. toaster1 is at location9.  Currently, the objects are at locations as follows. pan1 and spoon1 are at location11. glassbottle3, potato3, and apple2 are at location8. pot1 is at location5. tomato1, potato1, egg2, mug1, plate2, bowl1, and egg1 are at location10. dishsponge1 and fork1 are at location27. papertowelroll1, cellphone3, spoon2, glassbottle1, butterknife1, knife2, bread1, plate3, creditcard1, spatula1, and houseplant1 are at location3. window1 is at location30. plate1, peppershaker1, lettuce1, knife1, lettuce2, cellphone1, cellphone2, and apple1 are at location2. stoveknob1 is at location18. lightswitch1 is at location25. window2 is at location28. chair2 is at location1. potato2, spatula2, glassbottle2, spatula3, and sink1 are at location6. peppershaker3 is at location16. statue1 and soapbottle2 are at location26. bowl2 is at location29. saltshaker1 is at location15. chair1 is at location23. dishsponge2 and vase1 are at location13. peppershaker2 is at location20. soapbottle1 is at location4. cup1 is at location24. stoveknob4 is at location7. vase2 is at location17. stoveknob2 and stoveknob3 are at location12. agent agent1 is at location location5. The objects are in/on receptacle as follows. lettuce1, lettuce2, knife1, plate1, cellphone2, cellphone1, apple1, and peppershaker1 are on countertop1. mug1, potato1, tomato1, egg1, plate2, egg2, and bowl1 are in fridge1. saltshaker1 is in drawer2. butterknife1, creditcard1, cellphone3, spoon2, glassbottle1, houseplant1, bread1, spatula1, plate3, knife2, and papertowelroll1 are on countertop3. dishsponge2 and vase1 are in cabinet5. peppershaker2 is in drawer1. pan1 is on stoveburner2. spatula2, spatula3, glassbottle2, and potato2 are in sinkbasin1. pan1 and spoon1 are on countertop2. soapbottle2 and statue1 are on shelf3. pot1 is on stoveburner3. glassbottle3, apple2, and potato3 are in garbagecan1. pan1 is on stoveburner4. vase2 is on shelf1. soapbottle1 is in cabinet6. fork1 and dishsponge1 are in drawer3. peppershaker3 is in cabinet3. cup1 is in microwave1. pot1 is on stoveburner1. bowl2 is on shelf2. cellphone2 is on plate1. drawer2, drawer3, microwave1, cabinet5, cabinet2, drawer1, fridge1, and cabinet1 are closed. mug1 is cool. It has been validated that an object of type dishspongetype is in a receptacle of type drawertype. agent1 is holding object mug2. ", "question": "Is it possible to transition to a state where the following holds: houseplant1 is at location3 and coffeetabletype is off?", "answer": "no"}
{"id": -6242863931191345145, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. stoveburner4 and stoveburner2 are at location21. drawer1 is at location20. microwave1 is at location24. cabinet5 is at location13. countertop3 is at location3. countertop2 and coffeemachine1 are at location11. garbagecan1 is at location8. drawer3 is at location27. countertop1 is at location2. cabinet3 is at location16. cabinet6 is at location4. cabinet1 is at location19. shelf2 is at location29. shelf1 is at location17. fridge1 is at location10. shelf3 is at location26. cabinet4 is at location14. sinkbasin1 is at location6. drawer2 is at location15. cabinet2 is at location22. toaster1 is at location9.  Currently, the objects are at locations as follows. pan1 and spoon1 are at location11. glassbottle3, potato3, and apple2 are at location8. pot1 is at location5. tomato1, potato1, egg2, mug1, plate2, bowl1, and egg1 are at location10. dishsponge1 and fork1 are at location27. papertowelroll1, cellphone3, spoon2, glassbottle1, butterknife1, knife2, bread1, plate3, creditcard1, spatula1, mug2, and houseplant1 are at location3. window1 is at location30. plate1, peppershaker1, lettuce1, knife1, lettuce2, cellphone1, cellphone2, and apple1 are at location2. stoveknob1 is at location18. lightswitch1 is at location25. window2 is at location28. chair2 is at location1. potato2, spatula2, glassbottle2, spatula3, and sink1 are at location6. peppershaker3 is at location16. statue1 and soapbottle2 are at location26. bowl2 is at location29. saltshaker1 is at location15. chair1 is at location23. dishsponge2 and vase1 are at location13. peppershaker2 is at location20. soapbottle1 is at location4. cup1 is at location24. stoveknob4 is at location7. vase2 is at location17. stoveknob2 and stoveknob3 are at location12. agent agent1 is at location location16. The objects are in/on receptacle as follows. lettuce1, lettuce2, knife1, plate1, cellphone2, cellphone1, apple1, and peppershaker1 are on countertop1. mug2, butterknife1, creditcard1, cellphone3, spoon2, glassbottle1, houseplant1, bread1, spatula1, plate3, knife2, and papertowelroll1 are on countertop3. mug1, potato1, tomato1, egg1, plate2, egg2, and bowl1 are in fridge1. saltshaker1 is in drawer2. dishsponge2 and vase1 are in cabinet5. peppershaker2 is in drawer1. pan1 is on stoveburner2. spatula2, spatula3, glassbottle2, and potato2 are in sinkbasin1. pan1 and spoon1 are on countertop2. soapbottle2 and statue1 are on shelf3. pot1 is on stoveburner3. glassbottle3, apple2, and potato3 are in garbagecan1. pan1 is on stoveburner4. vase2 is on shelf1. soapbottle1 is in cabinet6. fork1 and dishsponge1 are in drawer3. peppershaker3 is in cabinet3. cup1 is in microwave1. pot1 is on stoveburner1. bowl2 is on shelf2. cellphone2 is on plate1. drawer2, drawer3, microwave1, cabinet5, cabinet2, drawer1, fridge1, and cabinet1 are closed. mug1 is cool. Nothing has been validated. agent1's hands are empty. ", "question": "Is it possible to transition to a state where the following holds: houseplant1 is on countertop3 and It has been validated that an object of type pottype is in a receptacle of type laundryhampertype?", "answer": "no"}
{"id": *******************, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. stoveburner4 and stoveburner2 are at location21. drawer1 is at location20. microwave1 is at location24. cabinet5 is at location13. countertop3 is at location3. countertop2 and coffeemachine1 are at location11. garbagecan1 is at location8. drawer3 is at location27. countertop1 is at location2. cabinet3 is at location16. cabinet6 is at location4. cabinet1 is at location19. shelf2 is at location29. shelf1 is at location17. fridge1 is at location10. shelf3 is at location26. cabinet4 is at location14. sinkbasin1 is at location6. drawer2 is at location15. cabinet2 is at location22. toaster1 is at location9.  Currently, the objects are at locations as follows. pan1, cellphone1, and peppershaker1 are at location11. bowl1 and glassbottle2 are at location14. spoon1, creditcard1, bread1, lettuce2, statue1, mug1, cellphone3, and creditcard2 are at location2. pot1 is at location5. tomato1, soapbottle1, saltshaker2, butterknife1, glassbottle3, lettuce3, knife1, fork2, spoon3, and houseplant1 are at location3. dishsponge1 is at location15. window1 is at location30. spoon2, fork1, spatula2, and sink1 are at location6. plate1 is at location4. potato1, apple1, potato2, lettuce1, cup1, and cup2 are at location10. stoveknob1 is at location18. lightswitch1 is at location25. window2 is at location28. chair2 is at location1. papertowelroll1 and vase2 are at location29. egg1 and apple2 are at location8. vase1 is at location17. saltshaker3 is at location26. chair1 is at location23. peppershaker2 and cellphone2 are at location20. spatula1 is at location27. stoveknob4 is at location7. glassbottle1 is at location19. saltshaker1 is at location16. stoveknob2 and stoveknob3 are at location12. agent agent1 is at location location8. The objects are in/on receptacle as follows. glassbottle2 and bowl1 are in cabinet4. lettuce3, soapbottle1, butterknife1, saltshaker2, tomato1, houseplant1, fork2, spoon3, glassbottle3, and knife1 are on countertop3. papertowelroll1 and vase2 are on shelf2. spatula1 is in drawer3. peppershaker1, pan1, and cellphone1 are on countertop2. plate1 is in cabinet6. potato1, potato2, cup1, lettuce1, apple1, and cup2 are in fridge1. creditcard1, lettuce2, mug1, bread1, cellphone3, creditcard2, statue1, and spoon1 are on countertop1. saltshaker3 is on shelf3. peppershaker2 and cellphone2 are in drawer1. pan1 is on stoveburner2. spatula2, spoon2, and fork1 are in sinkbasin1. pot1 is on stoveburner3. apple2 and egg1 are in garbagecan1. vase1 is on shelf1. pan1 is on stoveburner4. dishsponge1 is in drawer2. glassbottle1 is in cabinet1. pot1 is on stoveburner1. saltshaker1 is in cabinet3. drawer2, drawer3, microwave1, cabinet5, cabinet2, drawer1, fridge1, and cabinet1 are closed. Nothing has been validated. agent1 is holding object egg2. ", "question": "Is it possible to transition to a state where the following holds: agent agent1 is at location location5?", "answer": "yes"}
{"id": -3664968030174976659, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. stoveburner4 and stoveburner2 are at location21. drawer1 is at location20. microwave1 is at location24. cabinet5 is at location13. countertop3 is at location3. countertop2 and coffeemachine1 are at location11. garbagecan1 is at location8. drawer3 is at location27. countertop1 is at location2. cabinet3 is at location16. cabinet6 is at location4. cabinet1 is at location19. shelf2 is at location29. shelf1 is at location17. fridge1 is at location10. shelf3 is at location26. cabinet4 is at location14. sinkbasin1 is at location6. drawer2 is at location15. cabinet2 is at location22. toaster1 is at location9.  Currently, the objects are at locations as follows. pan1, cellphone1, and peppershaker1 are at location11. bowl1 and glassbottle2 are at location14. spoon1, creditcard1, bread1, lettuce2, statue1, mug1, cellphone3, and creditcard2 are at location2. pot1 is at location5. tomato1, soapbottle1, saltshaker2, butterknife1, glassbottle3, lettuce3, knife1, fork2, spoon3, and houseplant1 are at location3. dishsponge1 is at location15. window1 is at location30. spoon2, fork1, spatula2, and sink1 are at location6. plate1 is at location4. potato1, apple1, potato2, lettuce1, cup1, and cup2 are at location10. stoveknob1 is at location18. lightswitch1 is at location25. window2 is at location28. chair2 is at location1. papertowelroll1 and vase2 are at location29. egg2 and apple2 are at location8. vase1 is at location17. saltshaker3 is at location26. chair1 is at location23. peppershaker2 and cellphone2 are at location20. spatula1 is at location27. stoveknob4 is at location7. glassbottle1 is at location19. saltshaker1 is at location16. stoveknob2 and stoveknob3 are at location12. agent agent1 is at location location8. The objects are in/on receptacle as follows. glassbottle2 and bowl1 are in cabinet4. lettuce3, soapbottle1, butterknife1, saltshaker2, tomato1, houseplant1, fork2, spoon3, glassbottle3, and knife1 are on countertop3. papertowelroll1 and vase2 are on shelf2. spatula1 is in drawer3. peppershaker1, pan1, and cellphone1 are on countertop2. plate1 is in cabinet6. potato1, potato2, cup1, lettuce1, apple1, and cup2 are in fridge1. creditcard1, lettuce2, mug1, bread1, cellphone3, creditcard2, statue1, and spoon1 are on countertop1. saltshaker3 is on shelf3. peppershaker2 and cellphone2 are in drawer1. pan1 is on stoveburner2. spatula2, spoon2, and fork1 are in sinkbasin1. pot1 is on stoveburner3. apple2 and egg2 are in garbagecan1. vase1 is on shelf1. pan1 is on stoveburner4. dishsponge1 is in drawer2. glassbottle1 is in cabinet1. pot1 is on stoveburner1. saltshaker1 is in cabinet3. drawer2, drawer3, microwave1, cabinet5, cabinet2, drawer1, fridge1, and cabinet1 are closed. egg1 is hot. It has been validated that an object of type soapbottletype is in a receptacle of type countertoptype. agent1 is holding object egg1. ", "question": "Is it possible to transition to a state where the following holds: Nothing has been validated?", "answer": "no"}
{"id": 8455360622799739225, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. drawer3 is at location17. drawer2 is at location18. shelf2 is at location25. desk2 is at location10. shelf5 is at location22. shelf3 is at location11. garbagecan1 is at location2. drawer6 is at location1. desk1 is at location3. drawer1 is at location21. safe1 is at location6. drawer5 and drawer4 are at location12. shelf1 is at location20. bed1 is at location13. laundryhamper1 is at location8. shelf6 is at location24. shelf4 is at location23.  Currently, the objects are at locations as follows. pillow1, cellphone1, book1, pillow2, laptop2, and laptop1 are at location13. laundryhamperlid1 is at location8. keychain1 and keychain2 are at location6. mug2, cellphone2, pen1, cd3, and pencil3 are at location10. window1 is at location5. blinds1 is at location16. desklamp1, alarmclock3, and bowl2 are at location23. lightswitch1 is at location14. creditcard1 and pencil2 are at location22. cd1, bowl1, alarmclock1, pencil1, and mug1 are at location3. chair2 is at location26. blinds2 is at location15. baseballbat1 is at location9. cd2 is at location2. chair1 is at location21. cellphone3 is at location12. mirror1 is at location19. window2 is at location4. alarmclock2 is at location11. bowl3 is at location24. basketball1 is at location7. agent agent1 is at location location2. The objects are in/on receptacle as follows. pencil1, mug1, alarmclock1, bowl1, and cd1 are on desk1. laptop2, laptop1, book1, pillow2, cellphone1, and pillow1 are in bed1. cd2 is in garbagecan1. creditcard1 and pencil2 are on shelf5. desklamp1, alarmclock3, cd3, pen1, pencil3, mug2, cellphone2, and bowl2 are on desk2. alarmclock3, desklamp1, and bowl2 are on shelf4. cellphone3 is in drawer5. keychain1 and keychain2 are in safe1. bowl3 is on shelf6. alarmclock2 is on shelf3. drawer1, drawer3, safe1, and drawer6 are closed. desklamp1 is off. Nothing has been validated. agent1's hands are empty. ", "question": "Is it possible to transition to a state where the following holds: agent agent1 is at location location19 and pillow2 is at location13?", "answer": "no"}
{"id": -582553878352808518, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. toaster1 is at location10. cabinet3 is at location17. countertop1 is at location3. garbagecan1 is at location9. microwave1 is at location25. stoveburner1 and stoveburner3 are at location6. cabinet6 is at location5. sinkbasin1 is at location7. countertop2 and coffeemachine1 are at location12. drawer1 is at location21. cabinet5 is at location14. cabinet1 is at location20. stoveburner2 and stoveburner4 are at location22. shelf1 is at location18. fridge1 is at location11. countertop3 is at location4. cabinet2 is at location23. cabinet4 is at location15. shelf3 is at location27. drawer3 is at location28. shelf2 is at location30. drawer2 is at location16.  Currently, the objects are at locations as follows. sink1, fork1, egg2, cup3, tomato3, and potato3 are at location7. mug2, glassbottle2, bowl1, and creditcard2 are at location30. cellphone3, statue2, spatula3, butterknife1, butterknife2, knife1, fork2, peppershaker2, houseplant1, soapbottle3, and spoon2 are at location4. window1 is at location31. egg1, cup1, tomato1, potato2, lettuce1, cup2, tomato2, and potato1 are at location11. spoon1 and dishsponge1 are at location16. dishsponge3, apple1, bread1, apple3, creditcard1, cellphone1, spatula2, apple2, and statue1 are at location3. stoveknob1 is at location19. vase2 is at location18. creditcard3 and saltshaker1 are at location27. chair2 is at location1. vase1 and soapbottle1 are at location5. cellphone2 is at location21. lightswitch1 is at location26. glassbottle1, soapbottle2, and papertowelroll1 are at location9. plate2 and plate1 are at location14. spatula1 is at location28. chair1 is at location24. dishsponge2 is at location2. stoveknob3 and stoveknob2 are at location13. mug1 is at location25. window2 is at location29. pan1 is at location6. stoveknob4 is at location8. peppershaker1 is at location20. pot1 is at location22. agent agent1 is at location location21. The objects are in/on receptacle as follows. statue2, butterknife1, cellphone3, spoon2, houseplant1, peppershaker2, fork2, soapbottle3, knife1, butterknife2, and spatula3 are on countertop3. potato3, tomato3, egg2, cup3, and fork1 are in sinkbasin1. apple3, creditcard1, apple2, dishsponge3, cellphone1, apple1, spatula2, bread1, and statue1 are on countertop1. spatula1 is in drawer3. mug1 is in microwave1. potato1, tomato1, potato2, cup1, lettuce1, tomato2, egg1, and cup2 are in fridge1. glassbottle1, papertowelroll1, and soapbottle2 are in garbagecan1. dishsponge2, plate1, and plate2 are in cabinet5. pan1 is on stoveburner3. pot1 is on stoveburner2. pot1 is on stoveburner4. mug2, glassbottle2, bowl1, and creditcard2 are on shelf2. creditcard3 and saltshaker1 are on shelf3. vase1 and soapbottle1 are in cabinet6. dishsponge2 is on plate1. pan1 is on stoveburner1. vase2 is on shelf1. spoon1 and dishsponge1 are in drawer2. cellphone2 is in drawer1. peppershaker1 is in cabinet1. drawer2, drawer3, microwave1, cabinet5, cabinet2, drawer1, fridge1, and cabinet1 are closed. It has been validated that an object of type papertowelrolltype is in a receptacle of type garbagecantype. agent1's hands are empty. ", "question": "Is it possible to transition to a state where the following holds: teddybeartype is toggled?", "answer": "no"}
