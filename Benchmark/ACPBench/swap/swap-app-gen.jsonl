{"id": -8186652679340685416, "group": "applicable_actions_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: heidi, alice, kevin, dave, xena, ted, and bob. There are 7 items/roles: leek, quince, ulluco, valerian, mushroom, yam, and parsnip. Currently, kevin is assigned quince, xena is assigned yam, dave is assigned valerian, bob is assigned parsnip, heidi is assigned ulluco, ted is assigned leek, and alice is assigned mushroom. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1 with ?a2, ?r1 for ?r2.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(swap dave dave valerian valerian)", "(swap heidi ted ulluco leek)", "(swap ted heidi leek ulluco)", "(swap alice ted mushroom leek)", "(swap bob ted parsnip leek)", "(swap kevin bob quince parsnip)", "(swap bob alice parsnip mushroom)", "(swap bob heidi parsnip ulluco)", "(swap ted ted leek leek)", "(swap alice bob mushroom parsnip)", "(swap ted bob leek parsnip)", "(swap alice xena mushroom yam)", "(swap dave alice valerian mushroom)", "(swap heidi heidi ulluco ulluco)", "(swap xena heidi yam ulluco)", "(swap alice heidi mushroom ulluco)", "(swap xena dave yam valerian)", "(swap alice dave mushroom valerian)", "(swap dave ted valerian leek)", "(swap kevin xena quince yam)", "(swap xena xena yam yam)", "(swap bob dave parsnip valerian)", "(swap kevin dave quince valerian)", "(swap heidi bob ulluco parsnip)", "(swap bob xena parsnip yam)", "(swap kevin kevin quince quince)", "(swap ted kevin leek quince)", "(swap xena alice yam mushroom)", "(swap dave heidi valerian ulluco)", "(swap dave xena valerian yam)", "(swap xena bob yam parsnip)", "(swap bob bob parsnip parsnip)", "(swap kevin ted quince leek)", "(swap heidi xena ulluco yam)", "(swap kevin alice quince mushroom)", "(swap alice alice mushroom mushroom)", "(swap dave bob valerian parsnip)", "(swap xena ted yam leek)", "(swap xena kevin yam quince)", "(swap ted xena leek yam)", "(swap heidi kevin ulluco quince)", "(swap ted dave leek valerian)", "(swap heidi dave ulluco valerian)", "(swap dave kevin valerian quince)", "(swap bob kevin parsnip quince)", "(swap ted alice leek mushroom)", "(swap heidi alice ulluco mushroom)", "(swap kevin heidi quince ulluco)", "(swap alice kevin mushroom quince)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Bob Dave Heidi Kevin Ted Xena - agent Leek Mushroom Parsnip Quince Ulluco Valerian Yam - role)\n    (:init (assigned Alice Mushroom) (assigned Bob Parsnip) (assigned Dave Valerian) (assigned Heidi Ulluco) (assigned Kevin Quince) (assigned Ted Leek) (assigned Xena Yam))\n    (:goal (and (assigned Xena Quince) (assigned Heidi Mushroom) (assigned Kevin Yam) (assigned Alice Valerian) (assigned Dave Ulluco) (assigned Ted Leek) (assigned Bob Parsnip)))\n)"}
{"id": -1932915878134328270, "group": "applicable_actions_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: frank, xena, vic, liam, bob, and quentin. There are 6 items/roles: knead, nibbler, wrench, pliers, ratchet, and sander. Currently, frank is assigned ratchet, quentin is assigned sander, liam is assigned knead, xena is assigned pliers, vic is assigned wrench, and bob is assigned nibbler. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1:?r1 with ?a2:?r2.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(swap frank quentin ratchet sander)", "(swap vic frank wrench ratchet)", "(swap liam vic knead wrench)", "(swap xena quentin pliers sander)", "(swap bob frank nibbler ratchet)", "(swap liam frank knead ratchet)", "(swap vic bob wrench nibbler)", "(swap quentin liam sander knead)", "(swap liam bob knead nibbler)", "(swap frank liam ratchet knead)", "(swap quentin vic sander wrench)", "(swap vic vic wrench wrench)", "(swap liam quentin knead sander)", "(swap liam liam knead knead)", "(swap xena vic pliers wrench)", "(swap quentin bob sander nibbler)", "(swap xena xena pliers pliers)", "(swap xena frank pliers ratchet)", "(swap quentin xena sander pliers)", "(swap frank vic ratchet wrench)", "(swap xena liam pliers knead)", "(swap vic quentin wrench sander)", "(swap quentin frank sander ratchet)", "(swap frank frank ratchet ratchet)", "(swap xena bob pliers nibbler)", "(swap vic liam wrench knead)", "(swap bob vic nibbler wrench)", "(swap bob bob nibbler nibbler)", "(swap liam xena knead pliers)", "(swap quentin quentin sander sander)", "(swap bob xena nibbler pliers)", "(swap bob quentin nibbler sander)", "(swap frank xena ratchet pliers)", "(swap vic xena wrench pliers)", "(swap frank bob ratchet nibbler)", "(swap bob liam nibbler knead)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Frank Liam Quentin Vic Xena - agent Knead Nibbler Pliers Ratchet Sander Wrench - role)\n    (:init (assigned Bob Nibbler) (assigned Frank Ratchet) (assigned Liam Knead) (assigned Quentin Sander) (assigned Vic Wrench) (assigned Xena Pliers))\n    (:goal (and (assigned Quentin Knead) (assigned Liam Sander) (assigned Frank Pliers) (assigned Xena Wrench) (assigned Bob Nibbler) (assigned Vic Ratchet)))\n)"}
{"id": -3858219394181257916, "group": "applicable_actions_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: heidi, alice, kevin, dave, xena, ted, and bob. There are 7 items/roles: leek, quince, ulluco, valerian, mushroom, yam, and parsnip. Currently, bob is assigned quince, ted is assigned mushroom, heidi is assigned valerian, xena is assigned parsnip, kevin is assigned ulluco, dave is assigned leek, and alice is assigned yam. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - trade ?r1 of ?a1 for ?r2 of ?a2.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(swap ted alice mushroom yam)", "(swap ted bob mushroom quince)", "(swap heidi heidi valerian valerian)", "(swap dave bob leek quince)", "(swap kevin alice ulluco yam)", "(swap ted ted mushroom mushroom)", "(swap xena heidi parsnip valerian)", "(swap bob bob quince quince)", "(swap alice dave yam leek)", "(swap dave dave leek leek)", "(swap xena dave parsnip leek)", "(swap heidi bob valerian quince)", "(swap bob xena quince parsnip)", "(swap ted heidi mushroom valerian)", "(swap xena xena parsnip parsnip)", "(swap heidi dave valerian leek)", "(swap xena kevin parsnip ulluco)", "(swap heidi kevin valerian ulluco)", "(swap bob dave quince leek)", "(swap ted kevin mushroom ulluco)", "(swap kevin dave ulluco leek)", "(swap ted dave mushroom leek)", "(swap ted xena mushroom parsnip)", "(swap dave xena leek parsnip)", "(swap kevin xena ulluco parsnip)", "(swap kevin bob ulluco quince)", "(swap dave kevin leek ulluco)", "(swap kevin ted ulluco mushroom)", "(swap heidi alice valerian yam)", "(swap heidi xena valerian parsnip)", "(swap alice bob yam quince)", "(swap dave heidi leek valerian)", "(swap xena bob parsnip quince)", "(swap dave ted leek mushroom)", "(swap bob heidi quince valerian)", "(swap bob ted quince mushroom)", "(swap alice heidi yam valerian)", "(swap heidi ted valerian mushroom)", "(swap xena alice parsnip yam)", "(swap kevin kevin ulluco ulluco)", "(swap dave alice leek yam)", "(swap bob kevin quince ulluco)", "(swap alice kevin yam ulluco)", "(swap xena ted parsnip mushroom)", "(swap alice xena yam parsnip)", "(swap kevin heidi ulluco valerian)", "(swap alice alice yam yam)", "(swap alice ted yam mushroom)", "(swap bob alice quince yam)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Bob Dave Heidi Kevin Ted Xena - agent Leek Mushroom Parsnip Quince Ulluco Valerian Yam - role)\n    (:init (assigned Alice Yam) (assigned Bob Quince) (assigned Dave Leek) (assigned Heidi Valerian) (assigned Kevin Ulluco) (assigned Ted Mushroom) (assigned Xena Parsnip))\n    (:goal (and (assigned Xena Quince) (assigned Heidi Mushroom) (assigned Kevin Yam) (assigned Alice Valerian) (assigned Dave Ulluco) (assigned Ted Leek) (assigned Bob Parsnip)))\n)"}
{"id": 6890425273031762748, "group": "applicable_actions_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: heidi, alice, kevin, dave, xena, ted, and bob. There are 7 items/roles: leek, quince, ulluco, valerian, mushroom, yam, and parsnip. Currently, alice is assigned quince, heidi is assigned mushroom, ted is assigned leek, bob is assigned parsnip, xena is assigned valerian, kevin is assigned yam, and dave is assigned ulluco. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1:?r1 with ?a2:?r2.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(swap dave dave ulluco ulluco)", "(swap xena dave valerian ulluco)", "(swap alice heidi quince mushroom)", "(swap ted kevin leek yam)", "(swap heidi kevin mushroom yam)", "(swap dave xena ulluco valerian)", "(swap dave bob ulluco parsnip)", "(swap xena kevin valerian yam)", "(swap bob ted parsnip leek)", "(swap bob xena parsnip valerian)", "(swap xena bob valerian parsnip)", "(swap kevin dave yam ulluco)", "(swap heidi ted mushroom leek)", "(swap alice kevin quince yam)", "(swap kevin bob yam parsnip)", "(swap bob kevin parsnip yam)", "(swap alice alice quince quince)", "(swap ted dave leek ulluco)", "(swap bob dave parsnip ulluco)", "(swap ted ted leek leek)", "(swap ted xena leek valerian)", "(swap xena alice valerian quince)", "(swap ted bob leek parsnip)", "(swap heidi heidi mushroom mushroom)", "(swap dave ted ulluco leek)", "(swap bob alice parsnip quince)", "(swap xena xena valerian valerian)", "(swap heidi dave mushroom ulluco)", "(swap kevin ted yam leek)", "(swap alice ted quince leek)", "(swap dave alice ulluco quince)", "(swap bob heidi parsnip mushroom)", "(swap alice bob quince parsnip)", "(swap kevin heidi yam mushroom)", "(swap kevin kevin yam yam)", "(swap bob bob parsnip parsnip)", "(swap kevin xena yam valerian)", "(swap ted alice leek quince)", "(swap alice xena quince valerian)", "(swap heidi bob mushroom parsnip)", "(swap dave kevin ulluco yam)", "(swap heidi xena mushroom valerian)", "(swap dave heidi ulluco mushroom)", "(swap alice dave quince ulluco)", "(swap xena ted valerian leek)", "(swap xena heidi valerian mushroom)", "(swap ted heidi leek mushroom)", "(swap heidi alice mushroom quince)", "(swap kevin alice yam quince)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Bob Dave Heidi Kevin Ted Xena - agent Leek Mushroom Parsnip Quince Ulluco Valerian Yam - role)\n    (:init (assigned Alice Quince) (assigned Bob Parsnip) (assigned Dave Ulluco) (assigned Heidi Mushroom) (assigned Kevin Yam) (assigned Ted Leek) (assigned Xena Valerian))\n    (:goal (and (assigned Xena Quince) (assigned Heidi Mushroom) (assigned Kevin Yam) (assigned Alice Valerian) (assigned Dave Ulluco) (assigned Ted Leek) (assigned Bob Parsnip)))\n)"}
{"id": 5635388963417874290, "group": "applicable_actions_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 4 agents: vic, steve, bob, and zoe. There are 4 items/roles: book02, book04, book03, and book01. Currently, bob is assigned book03, vic is assigned book01, steve is assigned book04, and zoe is assigned book02. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - trade ?r1 of ?a1 for ?r2 of ?a2.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(swap vic vic book01 book01)", "(swap vic zoe book01 book02)", "(swap steve zoe book04 book02)", "(swap bob steve book03 book04)", "(swap steve bob book04 book03)", "(swap steve steve book04 book04)", "(swap vic steve book01 book04)", "(swap bob vic book03 book01)", "(swap zoe zoe book02 book02)", "(swap zoe steve book02 book04)", "(swap vic bob book01 book03)", "(swap bob zoe book03 book02)", "(swap steve vic book04 book01)", "(swap zoe vic book02 book01)", "(swap bob bob book03 book03)", "(swap zoe bob book02 book03)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-4-12)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Steve Vic Zoe - agent book01 book02 book03 book04 - role)\n    (:init (assigned Bob book03) (assigned Steve book04) (assigned Vic book01) (assigned Zoe book02))\n    (:goal (and (assigned Bob book02) (assigned Steve book03) (assigned Vic book04) (assigned Zoe book01)))\n)"}
{"id": 755980571681442243, "group": "applicable_actions_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: frank, xena, vic, liam, bob, and quentin. There are 6 items/roles: knead, nibbler, wrench, pliers, ratchet, and sander. Currently, liam is assigned ratchet, xena is assigned nibbler, frank is assigned knead, quentin is assigned pliers, bob is assigned wrench, and vic is assigned sander. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1 with ?a2, ?r1 for ?r2.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(swap frank bob knead wrench)", "(swap bob vic wrench sander)", "(swap frank liam knead ratchet)", "(swap frank vic knead sander)", "(swap xena quentin nibbler pliers)", "(swap vic liam sander ratchet)", "(swap liam xena ratchet nibbler)", "(swap liam vic ratchet sander)", "(swap frank xena knead nibbler)", "(swap quentin liam pliers ratchet)", "(swap frank frank knead knead)", "(swap liam frank ratchet knead)", "(swap quentin frank pliers knead)", "(swap xena xena nibbler nibbler)", "(swap bob quentin wrench pliers)", "(swap bob liam wrench ratchet)", "(swap liam quentin ratchet pliers)", "(swap quentin vic pliers sander)", "(swap quentin xena pliers nibbler)", "(swap xena bob nibbler wrench)", "(swap frank quentin knead pliers)", "(swap xena frank nibbler knead)", "(swap xena liam nibbler ratchet)", "(swap xena vic nibbler sander)", "(swap liam liam ratchet ratchet)", "(swap vic frank sander knead)", "(swap vic quentin sander pliers)", "(swap bob xena wrench nibbler)", "(swap bob frank wrench knead)", "(swap vic bob sander wrench)", "(swap quentin quentin pliers pliers)", "(swap quentin bob pliers wrench)", "(swap vic vic sander sander)", "(swap bob bob wrench wrench)", "(swap liam bob ratchet wrench)", "(swap vic xena sander nibbler)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Frank Liam Quentin Vic Xena - agent Knead Nibbler Pliers Ratchet Sander Wrench - role)\n    (:init (assigned Bob Wrench) (assigned Frank Knead) (assigned Liam Ratchet) (assigned Quentin Pliers) (assigned Vic Sander) (assigned Xena Nibbler))\n    (:goal (and (assigned Quentin Knead) (assigned Liam Sander) (assigned Frank Pliers) (assigned Xena Wrench) (assigned Bob Nibbler) (assigned Vic Ratchet)))\n)"}
{"id": -4877614391834765940, "group": "applicable_actions_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 3 agents: judy, grace, and xena. There are 3 items/roles: xactoknife, funnel, and sander. Currently, judy is assigned xactoknife, grace is assigned sander, and xena is assigned funnel. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - trade ?r1 of ?a1 for ?r2 of ?a2.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(swap grace xena sander funnel)", "(swap judy judy xactoknife xactoknife)", "(swap grace grace sander sander)", "(swap judy grace xactoknife sander)", "(swap grace judy sander xactoknife)", "(swap judy xena xactoknife funnel)", "(swap xena judy funnel xactoknife)", "(swap xena xena funnel funnel)", "(swap xena grace funnel sander)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-3-4)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Grace Judy Xena - agent Funnel Sander XActoKnife - role)\n    (:init (assigned Grace Sander) (assigned Judy XActoKnife) (assigned Xena Funnel))\n    (:goal (and (assigned Judy XActoKnife) (assigned Grace Sander) (assigned Xena Funnel)))\n)"}
{"id": -6869567996786111742, "group": "applicable_actions_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: frank, xena, vic, liam, bob, and quentin. There are 6 items/roles: knead, nibbler, wrench, pliers, ratchet, and sander. Currently, frank is assigned ratchet, bob is assigned wrench, vic is assigned sander, xena is assigned pliers, quentin is assigned nibbler, and liam is assigned knead. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - trade ?r1 of ?a1 for ?r2 of ?a2.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(swap vic liam sander knead)", "(swap liam bob knead wrench)", "(swap quentin quentin nibbler nibbler)", "(swap bob vic wrench sander)", "(swap liam frank knead ratchet)", "(swap xena bob pliers wrench)", "(swap vic quentin sander nibbler)", "(swap quentin bob nibbler wrench)", "(swap vic frank sander ratchet)", "(swap frank liam ratchet knead)", "(swap frank quentin ratchet nibbler)", "(swap quentin xena nibbler pliers)", "(swap frank vic ratchet sander)", "(swap liam liam knead knead)", "(swap quentin frank nibbler ratchet)", "(swap xena quentin pliers nibbler)", "(swap xena xena pliers pliers)", "(swap bob quentin wrench nibbler)", "(swap xena frank pliers ratchet)", "(swap bob frank wrench ratchet)", "(swap frank bob ratchet wrench)", "(swap xena liam pliers knead)", "(swap quentin liam nibbler knead)", "(swap frank frank ratchet ratchet)", "(swap vic xena sander pliers)", "(swap liam quentin knead nibbler)", "(swap xena vic pliers sander)", "(swap bob liam wrench knead)", "(swap liam vic knead sander)", "(swap vic bob sander wrench)", "(swap liam xena knead pliers)", "(swap quentin vic nibbler sander)", "(swap vic vic sander sander)", "(swap bob bob wrench wrench)", "(swap frank xena ratchet pliers)", "(swap bob xena wrench pliers)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Frank Liam Quentin Vic Xena - agent Knead Nibbler Pliers Ratchet Sander Wrench - role)\n    (:init (assigned Bob Wrench) (assigned Frank Ratchet) (assigned Liam Knead) (assigned Quentin Nibbler) (assigned Vic Sander) (assigned Xena Pliers))\n    (:goal (and (assigned Quentin Knead) (assigned Liam Sander) (assigned Frank Pliers) (assigned Xena Wrench) (assigned Bob Nibbler) (assigned Vic Ratchet)))\n)"}
{"id": 6815083145480074076, "group": "applicable_actions_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: heidi, alice, kevin, dave, xena, ted, and bob. There are 7 items/roles: leek, quince, ulluco, valerian, mushroom, yam, and parsnip. Currently, heidi is assigned valerian, bob is assigned mushroom, xena is assigned parsnip, ted is assigned yam, alice is assigned leek, dave is assigned quince, and kevin is assigned ulluco. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - exchange ?r1 of ?a1 with ?r2 of ?a2.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(swap xena alice parsnip leek)", "(swap xena ted parsnip yam)", "(swap dave ted quince yam)", "(swap bob xena mushroom parsnip)", "(swap alice dave leek quince)", "(swap alice ted leek yam)", "(swap heidi heidi valerian valerian)", "(swap bob ted mushroom yam)", "(swap bob heidi mushroom valerian)", "(swap xena heidi parsnip valerian)", "(swap ted ted yam yam)", "(swap ted bob yam mushroom)", "(swap xena xena parsnip parsnip)", "(swap bob alice mushroom leek)", "(swap kevin alice ulluco leek)", "(swap xena kevin parsnip ulluco)", "(swap xena bob parsnip mushroom)", "(swap heidi kevin valerian ulluco)", "(swap bob kevin mushroom ulluco)", "(swap bob dave mushroom quince)", "(swap ted dave yam quince)", "(swap heidi dave valerian quince)", "(swap dave dave quince quince)", "(swap dave alice quince leek)", "(swap kevin ted ulluco yam)", "(swap alice heidi leek valerian)", "(swap kevin dave ulluco quince)", "(swap alice alice leek leek)", "(swap alice kevin leek ulluco)", "(swap ted alice yam leek)", "(swap kevin xena ulluco parsnip)", "(swap dave kevin quince ulluco)", "(swap heidi alice valerian leek)", "(swap heidi xena valerian parsnip)", "(swap bob bob mushroom mushroom)", "(swap alice xena leek parsnip)", "(swap ted kevin yam ulluco)", "(swap xena dave parsnip quince)", "(swap dave xena quince parsnip)", "(swap dave heidi quince valerian)", "(swap heidi ted valerian yam)", "(swap kevin kevin ulluco ulluco)", "(swap alice bob leek mushroom)", "(swap ted xena yam parsnip)", "(swap heidi bob valerian mushroom)", "(swap kevin bob ulluco mushroom)", "(swap ted heidi yam valerian)", "(swap kevin heidi ulluco valerian)", "(swap dave bob quince mushroom)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Bob Dave Heidi Kevin Ted Xena - agent Leek Mushroom Parsnip Quince Ulluco Valerian Yam - role)\n    (:init (assigned Alice Leek) (assigned Bob Mushroom) (assigned Dave Quince) (assigned Heidi Valerian) (assigned Kevin Ulluco) (assigned Ted Yam) (assigned Xena Parsnip))\n    (:goal (and (assigned Xena Quince) (assigned Heidi Mushroom) (assigned Kevin Yam) (assigned Alice Valerian) (assigned Dave Ulluco) (assigned Ted Leek) (assigned Bob Parsnip)))\n)"}
{"id": -713925123901920711, "group": "applicable_actions_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: heidi, alice, kevin, dave, xena, ted, and bob. There are 7 items/roles: leek, quince, ulluco, valerian, mushroom, yam, and parsnip. Currently, heidi is assigned quince, kevin is assigned parsnip, xena is assigned yam, bob is assigned leek, alice is assigned valerian, dave is assigned mushroom, and ted is assigned ulluco. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1:?r1 with ?a2:?r2.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(swap heidi ted quince ulluco)", "(swap dave dave mushroom mushroom)", "(swap heidi xena quince yam)", "(swap ted bob ulluco leek)", "(swap dave ted mushroom ulluco)", "(swap kevin bob parsnip leek)", "(swap ted dave ulluco mushroom)", "(swap heidi kevin quince parsnip)", "(swap xena ted yam ulluco)", "(swap dave bob mushroom leek)", "(swap bob kevin leek parsnip)", "(swap heidi dave quince mushroom)", "(swap ted alice ulluco valerian)", "(swap dave kevin mushroom parsnip)", "(swap alice ted valerian ulluco)", "(swap kevin dave parsnip mushroom)", "(swap ted xena ulluco yam)", "(swap bob ted leek ulluco)", "(swap alice xena valerian yam)", "(swap ted ted ulluco ulluco)", "(swap alice alice valerian valerian)", "(swap xena alice yam valerian)", "(swap alice kevin valerian parsnip)", "(swap xena xena yam yam)", "(swap alice dave valerian mushroom)", "(swap xena kevin yam parsnip)", "(swap bob heidi leek quince)", "(swap kevin kevin parsnip parsnip)", "(swap ted kevin ulluco parsnip)", "(swap bob bob leek leek)", "(swap heidi bob quince leek)", "(swap dave alice mushroom valerian)", "(swap kevin heidi parsnip quince)", "(swap alice bob valerian leek)", "(swap heidi alice quince valerian)", "(swap bob xena leek yam)", "(swap xena heidi yam quince)", "(swap dave heidi mushroom quince)", "(swap bob alice leek valerian)", "(swap dave xena mushroom yam)", "(swap heidi heidi quince quince)", "(swap kevin alice parsnip valerian)", "(swap xena dave yam mushroom)", "(swap kevin xena parsnip yam)", "(swap alice heidi valerian quince)", "(swap xena bob yam leek)", "(swap bob dave leek mushroom)", "(swap ted heidi ulluco quince)", "(swap kevin ted parsnip ulluco)"], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Bob Dave Heidi Kevin Ted Xena - agent Leek Mushroom Parsnip Quince Ulluco Valerian Yam - role)\n    (:init (assigned Alice Valerian) (assigned Bob Leek) (assigned Dave Mushroom) (assigned Heidi Quince) (assigned Kevin Parsnip) (assigned Ted Ulluco) (assigned Xena Yam))\n    (:goal (and (assigned Xena Quince) (assigned Heidi Mushroom) (assigned Kevin Yam) (assigned Alice Valerian) (assigned Dave Ulluco) (assigned Ted Leek) (assigned Bob Parsnip)))\n)"}
