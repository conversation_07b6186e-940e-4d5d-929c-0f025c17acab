{"id": -3872528514435257182, "group": "progression_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: carol, vic, alice, xena, michelle, heidi, zoe, and dave. There are 8 items/roles: necklace, guitar, zebra, iceskates, frisbee, slinky, whale, and quadcopter. Currently, vic is assigned quadcopter, carol is assigned guitar, michelle is assigned whale, heidi is assigned necklace, alice is assigned zebra, zoe is assigned frisbee, xena is assigned slinky, and dave is assigned iceskates. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Break down the outcomes of performing the action \"swap carol with michelle, guitar for whale\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(assigned carol guitar)", "(assigned michelle whale)"], "pos": ["(assigned carol whale)", "(assigned michelle guitar)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Carol Dave Heidi Michelle Vic Xena Zoe - agent Frisbee Guitar IceSkates Necklace Quadcopter Slinky Whale Zebra - role)\n    (:init (assigned Alice Zebra) (assigned Carol Guitar) (assigned Dave IceSkates) (assigned Heidi Necklace) (assigned Michelle Whale) (assigned Vic Quadcopter) (assigned Xena Slinky) (assigned Zoe Frisbee))\n    (:goal (and (assigned Xena Slinky) (assigned Dave IceSkates) (assigned Alice Zebra) (assigned Michelle Quadcopter) (assigned Vic Necklace) (assigned Heidi Guitar) (assigned Carol Frisbee) (assigned Zoe Whale)))\n)"}
{"id": -1623708223155038018, "group": "progression_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: carol, vic, alice, xena, michelle, heidi, zoe, and dave. There are 8 items/roles: necklace, guitar, zebra, iceskates, frisbee, slinky, whale, and quadcopter. Currently, michelle is assigned zebra, carol is assigned frisbee, alice is assigned quadcopter, heidi is assigned iceskates, vic is assigned whale, zoe is assigned guitar, dave is assigned necklace, and xena is assigned slinky. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Break down the outcomes of performing the action \"swap dave:necklace with zoe:guitar\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(assigned zoe guitar)", "(assigned dave necklace)"], "pos": ["(assigned dave guitar)", "(assigned zoe necklace)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Carol Dave Heidi Michelle Vic Xena Zoe - agent Frisbee Guitar IceSkates Necklace Quadcopter Slinky Whale Zebra - role)\n    (:init (assigned Alice Quadcopter) (assigned Carol Frisbee) (assigned Dave Necklace) (assigned Heidi IceSkates) (assigned Michelle Zebra) (assigned Vic Whale) (assigned Xena Slinky) (assigned Zoe Guitar))\n    (:goal (and (assigned Xena Slinky) (assigned Dave IceSkates) (assigned Alice Zebra) (assigned Michelle Quadcopter) (assigned Vic Necklace) (assigned Heidi Guitar) (assigned Carol Frisbee) (assigned Zoe Whale)))\n)"}
{"id": -9151083334277741598, "group": "progression_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: quentin, bob, frank, liam, vic, and xena. There are 6 items/roles: nibbler, knead, sander, wrench, ratchet, and pliers. Currently, quentin is assigned sander, bob is assigned nibbler, liam is assigned wrench, frank is assigned pliers, vic is assigned ratchet, and xena is assigned knead. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Break down the outcomes of performing the action \"swap frank:pliers with vic:ratchet\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(assigned frank pliers)", "(assigned vic ratchet)"], "pos": ["(assigned frank ratchet)", "(assigned vic pliers)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Frank Liam Quentin Vic Xena - agent Knead Nibbler Pliers Ratchet Sander Wrench - role)\n    (:init (assigned Bob Nibbler) (assigned Frank Pliers) (assigned Liam Wrench) (assigned Quentin Sander) (assigned Vic Ratchet) (assigned Xena Knead))\n    (:goal (and (assigned Quentin Knead) (assigned Liam Sander) (assigned Frank Pliers) (assigned Xena Wrench) (assigned Bob Nibbler) (assigned Vic Ratchet)))\n)"}
{"id": -1809335217787271783, "group": "progression_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: quentin, bob, frank, liam, vic, and xena. There are 6 items/roles: nibbler, knead, sander, wrench, ratchet, and pliers. Currently, frank is assigned ratchet, quentin is assigned sander, vic is assigned knead, xena is assigned wrench, bob is assigned pliers, and liam is assigned nibbler. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Break down the outcomes of performing the action \"trade sander of quentin for wrench of xena\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(assigned quentin sander)", "(assigned xena wrench)"], "pos": ["(assigned xena sander)", "(assigned quentin wrench)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Frank Liam Quentin Vic Xena - agent Knead Nibbler Pliers Ratchet Sander Wrench - role)\n    (:init (assigned Bob Pliers) (assigned Frank Ratchet) (assigned Liam Nibbler) (assigned Quentin Sander) (assigned Vic Knead) (assigned Xena Wrench))\n    (:goal (and (assigned Quentin Knead) (assigned Liam Sander) (assigned Frank Pliers) (assigned Xena Wrench) (assigned Bob Nibbler) (assigned Vic Ratchet)))\n)"}
{"id": -5415126855679066881, "group": "progression_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: quentin, bob, frank, liam, vic, and xena. There are 6 items/roles: nibbler, knead, sander, wrench, ratchet, and pliers. Currently, bob is assigned sander, xena is assigned wrench, quentin is assigned knead, frank is assigned pliers, liam is assigned ratchet, and vic is assigned nibbler. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Break down the outcomes of performing the action \"swap xena with quentin, wrench for knead\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(assigned quentin knead)", "(assigned xena wrench)"], "pos": ["(assigned quentin wrench)", "(assigned xena knead)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Frank Liam Quentin Vic Xena - agent Knead Nibbler Pliers Ratchet Sander Wrench - role)\n    (:init (assigned Bob Sander) (assigned Frank Pliers) (assigned Liam Ratchet) (assigned Quentin Knead) (assigned Vic Nibbler) (assigned Xena Wrench))\n    (:goal (and (assigned Quentin Knead) (assigned Liam Sander) (assigned Frank Pliers) (assigned Xena Wrench) (assigned Bob Nibbler) (assigned Vic Ratchet)))\n)"}
{"id": 8890031107389313010, "group": "progression_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: ted, bob, alice, xena, heidi, kevin, and dave. There are 7 items/roles: quince, parsnip, yam, mushroom, ulluco, leek, and valerian. Currently, dave is assigned yam, heidi is assigned mushroom, kevin is assigned parsnip, bob is assigned ulluco, alice is assigned valerian, xena is assigned quince, and ted is assigned leek. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Break down the outcomes of performing the action \"swap ted with xena, leek for quince\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(assigned xena quince)", "(assigned ted leek)"], "pos": ["(assigned xena leek)", "(assigned ted quince)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Bob Dave Heidi Kevin Ted Xena - agent Leek Mushroom Parsnip Quince Ulluco Valerian Yam - role)\n    (:init (assigned Alice Valerian) (assigned Bob Ulluco) (assigned Dave Yam) (assigned Heidi Mushroom) (assigned Kevin Parsnip) (assigned Ted Leek) (assigned Xena Quince))\n    (:goal (and (assigned Xena Quince) (assigned Heidi Mushroom) (assigned Kevin Yam) (assigned Alice Valerian) (assigned Dave Ulluco) (assigned Ted Leek) (assigned Bob Parsnip)))\n)"}
{"id": -5123401370893052017, "group": "progression_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: quentin, bob, frank, liam, vic, and xena. There are 6 items/roles: nibbler, knead, sander, wrench, ratchet, and pliers. Currently, bob is assigned ratchet, frank is assigned wrench, xena is assigned sander, quentin is assigned pliers, liam is assigned knead, and vic is assigned nibbler. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Break down the outcomes of performing the action \"trade knead of liam for nibbler of vic\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(assigned liam knead)", "(assigned vic nibbler)"], "pos": ["(assigned vic knead)", "(assigned liam nibbler)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Frank Liam Quentin Vic Xena - agent Knead Nibbler Pliers Ratchet Sander Wrench - role)\n    (:init (assigned Bob Ratchet) (assigned Frank Wrench) (assigned Liam Knead) (assigned Quentin Pliers) (assigned Vic Nibbler) (assigned Xena Sander))\n    (:goal (and (assigned Quentin Knead) (assigned Liam Sander) (assigned Frank Pliers) (assigned Xena Wrench) (assigned Bob Nibbler) (assigned Vic Ratchet)))\n)"}
{"id": 1522280979663922064, "group": "progression_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: quentin, bob, frank, liam, vic, and xena. There are 6 items/roles: nibbler, knead, sander, wrench, ratchet, and pliers. Currently, frank is assigned sander, vic is assigned knead, xena is assigned wrench, bob is assigned pliers, liam is assigned nibbler, and quentin is assigned ratchet. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Break down the outcomes of performing the action \"swap xena with vic, wrench for knead\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(assigned vic knead)", "(assigned xena wrench)"], "pos": ["(assigned vic wrench)", "(assigned xena knead)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Frank Liam Quentin Vic Xena - agent Knead Nibbler Pliers Ratchet Sander Wrench - role)\n    (:init (assigned Bob Pliers) (assigned Frank Sander) (assigned Liam Nibbler) (assigned Quentin Ratchet) (assigned Vic Knead) (assigned Xena Wrench))\n    (:goal (and (assigned Quentin Knead) (assigned Liam Sander) (assigned Frank Pliers) (assigned Xena Wrench) (assigned Bob Nibbler) (assigned Vic Ratchet)))\n)"}
{"id": 8672038897362465923, "group": "progression_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: quentin, bob, frank, liam, vic, and xena. There are 6 items/roles: nibbler, knead, sander, wrench, ratchet, and pliers. Currently, frank is assigned sander, xena is assigned wrench, liam is assigned nibbler, quentin is assigned pliers, vic is assigned ratchet, and bob is assigned knead. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Break down the outcomes of performing the action \"trade knead of bob for wrench of xena\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(assigned bob knead)", "(assigned xena wrench)"], "pos": ["(assigned bob wrench)", "(assigned xena knead)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Frank Liam Quentin Vic Xena - agent Knead Nibbler Pliers Ratchet Sander Wrench - role)\n    (:init (assigned Bob Knead) (assigned Frank Sander) (assigned Liam Nibbler) (assigned Quentin Pliers) (assigned Vic Ratchet) (assigned Xena Wrench))\n    (:goal (and (assigned Quentin Knead) (assigned Liam Sander) (assigned Frank Pliers) (assigned Xena Wrench) (assigned Bob Nibbler) (assigned Vic Ratchet)))\n)"}
{"id": 9123001983365089491, "group": "progression_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: quentin, bob, frank, liam, vic, and xena. There are 6 items/roles: nibbler, knead, sander, wrench, ratchet, and pliers. Currently, xena is assigned ratchet, vic is assigned wrench, bob is assigned pliers, frank is assigned knead, quentin is assigned nibbler, and liam is assigned sander. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "Break down the outcomes of performing the action \"swap bob with frank, pliers for knead\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(assigned bob pliers)", "(assigned frank knead)"], "pos": ["(assigned frank pliers)", "(assigned bob knead)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Frank Liam Quentin Vic Xena - agent Knead Nibbler Pliers Ratchet Sander Wrench - role)\n    (:init (assigned Bob Pliers) (assigned Frank Knead) (assigned Liam Sander) (assigned Quentin Nibbler) (assigned Vic Wrench) (assigned Xena Ratchet))\n    (:goal (and (assigned Quentin Knead) (assigned Liam Sander) (assigned Frank Pliers) (assigned Xena Wrench) (assigned Bob Nibbler) (assigned Vic Ratchet)))\n)"}
