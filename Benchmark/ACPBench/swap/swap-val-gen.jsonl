{"id": -8342114175427231804, "group": "validation_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 4 agents: zoe, bob, steve, and vic. There are 4 items/roles: book02, book03, book01, and book04. Currently, bob is assigned book01, steve is assigned book02, vic is assigned book03, and zoe is assigned book04. The goal is to reach a state where the following facts hold: zoe is assigned book01, steve is assigned book03, bob is assigned book02, and vic is assigned book04. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1 with ?a2, ?r1 for ?r2.", "question": "What is the first inapplicable action in the next sequence of actions: \"(swap steve vic book02 book03) (swap bob zoe book01 book04) (swap steve zoe book01 book04)\"?", "answer": 2, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-4-12)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Steve Vic Zoe - agent book01 book02 book03 book04 - role)\n    (:init (assigned Bob book01) (assigned Steve book02) (assigned Vic book03) (assigned Zoe book04))\n    (:goal (and (assigned Bob book02) (assigned Steve book03) (assigned Vic book04) (assigned Zoe book01)))\n)"}
{"id": 191584266151682189, "group": "validation_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: carol, zoe, xena, dave, heidi, vic, alice, and michelle. There are 8 items/roles: whale, guitar, quadcopter, necklace, slinky, frisbee, zebra, and iceskates. Currently, heidi is assigned necklace, xena is assigned whale, zoe is assigned frisbee, vic is assigned quadcopter, alice is assigned iceskates, carol is assigned guitar, dave is assigned slinky, and michelle is assigned zebra. The goal is to reach a state where the following facts hold: alice is assigned zebra, xena is assigned slinky, zoe is assigned whale, carol is assigned frisbee, heidi is assigned guitar, dave is assigned iceskates, michelle is assigned quadcopter, and vic is assigned necklace. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1:?r1 with ?a2:?r2.", "question": "What is the first inapplicable action in the next sequence of actions: \"(swap dave alice slinky iceskates) (swap alice xena slinky whale) (swap alice zoe whale frisbee) (swap alice carol frisbee guitar) (swap michelle alice zebra guitar) (swap michelle vic guitar quadcopter) (swap vic heidi iceskates iceskates)\"?", "answer": 6, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Carol Dave Heidi Michelle Vic Xena Zoe - agent Frisbee Guitar IceSkates Necklace Quadcopter Slinky Whale Zebra - role)\n    (:init (assigned Alice IceSkates) (assigned Carol Guitar) (assigned Dave Slinky) (assigned Heidi Necklace) (assigned Michelle Zebra) (assigned Vic Quadcopter) (assigned Xena Whale) (assigned Zoe Frisbee))\n    (:goal (and (assigned Xena Slinky) (assigned Dave IceSkates) (assigned Alice Zebra) (assigned Michelle Quadcopter) (assigned Vic Necklace) (assigned Heidi Guitar) (assigned Carol Frisbee) (assigned Zoe Whale)))\n)"}
{"id": -6869567996786111742, "group": "validation_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: bob, xena, frank, quentin, liam, and vic. There are 6 items/roles: ratchet, wrench, nibbler, pliers, sander, and knead. Currently, xena is assigned pliers, vic is assigned nibbler, liam is assigned knead, bob is assigned wrench, frank is assigned sander, and quentin is assigned ratchet. The goal is to reach a state where the following facts hold: vic is assigned ratchet, xena is assigned wrench, frank is assigned pliers, quentin is assigned knead, bob is assigned nibbler, and liam is assigned sander. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - trade ?r1 of ?a1 for ?r2 of ?a2.", "question": "What is the first inapplicable action in the next sequence of actions: \"(swap liam vic knead nibbler) (swap quentin vic ratchet knead) (swap liam frank nibbler wrench) (swap bob liam pliers nibbler) (swap liam frank pliers sander)\"?", "answer": 2, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Frank Liam Quentin Vic Xena - agent Knead Nibbler Pliers Ratchet Sander Wrench - role)\n    (:init (assigned Bob Wrench) (assigned Frank Sander) (assigned Liam Knead) (assigned Quentin Ratchet) (assigned Vic Nibbler) (assigned Xena Pliers))\n    (:goal (and (assigned Quentin Knead) (assigned Liam Sander) (assigned Frank Pliers) (assigned Xena Wrench) (assigned Bob Nibbler) (assigned Vic Ratchet)))\n)"}
{"id": -1794830626871222676, "group": "validation_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: bob, xena, frank, quentin, liam, and vic. There are 6 items/roles: ratchet, wrench, nibbler, pliers, sander, and knead. Currently, xena is assigned pliers, vic is assigned nibbler, liam is assigned knead, bob is assigned wrench, frank is assigned sander, and quentin is assigned ratchet. The goal is to reach a state where the following facts hold: vic is assigned ratchet, xena is assigned wrench, frank is assigned pliers, quentin is assigned knead, bob is assigned nibbler, and liam is assigned sander. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - exchange ?r1 of ?a1 with ?r2 of ?a2.", "question": "What is the first inapplicable action in the next sequence of actions: \"(swap quentin liam ratchet knead) (swap frank liam sander ratchet) (swap bob vic wrench nibbler) (swap xena vic knead nibbler) (swap vic frank pliers ratchet)\"?", "answer": 3, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Frank Liam Quentin Vic Xena - agent Knead Nibbler Pliers Ratchet Sander Wrench - role)\n    (:init (assigned Bob Wrench) (assigned Frank Sander) (assigned Liam Knead) (assigned Quentin Ratchet) (assigned Vic Nibbler) (assigned Xena Pliers))\n    (:goal (and (assigned Quentin Knead) (assigned Liam Sander) (assigned Frank Pliers) (assigned Xena Wrench) (assigned Bob Nibbler) (assigned Vic Ratchet)))\n)"}
{"id": -713925123901920711, "group": "validation_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: kevin, ted, bob, xena, dave, heidi, and alice. There are 7 items/roles: quince, leek, parsnip, ulluco, mushroom, valerian, and yam. Currently, dave is assigned valerian, heidi is assigned quince, alice is assigned yam, xena is assigned parsnip, kevin is assigned mushroom, bob is assigned leek, and ted is assigned ulluco. The goal is to reach a state where the following facts hold: heidi is assigned mushroom, ted is assigned leek, bob is assigned parsnip, dave is assigned ulluco, kevin is assigned yam, xena is assigned quince, and alice is assigned valerian. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - exchange ?r1 of ?a1 with ?r2 of ?a2.", "question": "What is the first inapplicable action in the next sequence of actions: \"(swap dave alice valerian yam) (swap bob dave leek yam) (swap heidi kevin quince mushroom) (swap kevin bob quince yam) (swap dave ted yam ulluco) (swap bob xena quince parsnip)\"?", "answer": 4, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Bob Dave Heidi Kevin Ted Xena - agent Leek Mushroom Parsnip Quince Ulluco Valerian Yam - role)\n    (:init (assigned Alice Yam) (assigned Bob Leek) (assigned Dave Valerian) (assigned Heidi Quince) (assigned Kevin Mushroom) (assigned Ted Ulluco) (assigned Xena Parsnip))\n    (:goal (and (assigned Xena Quince) (assigned Heidi Mushroom) (assigned Kevin Yam) (assigned Alice Valerian) (assigned Dave Ulluco) (assigned Ted Leek) (assigned Bob Parsnip)))\n)"}
{"id": 4590011998694604859, "group": "validation_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 4 agents: zoe, bob, steve, and vic. There are 4 items/roles: book02, book03, book01, and book04. Currently, bob is assigned book01, steve is assigned book02, vic is assigned book03, and zoe is assigned book04. The goal is to reach a state where the following facts hold: zoe is assigned book01, steve is assigned book03, bob is assigned book02, and vic is assigned book04. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - trade ?r1 of ?a1 for ?r2 of ?a2.", "question": "What is the first inapplicable action in the next sequence of actions: \"(swap bob steve book01 book02) (swap vic steve book03 book01) (swap zoe vic book03 book02)\"?", "answer": 2, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-4-12)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Steve Vic Zoe - agent book01 book02 book03 book04 - role)\n    (:init (assigned Bob book01) (assigned Steve book02) (assigned Vic book03) (assigned Zoe book04))\n    (:goal (and (assigned Bob book02) (assigned Steve book03) (assigned Vic book04) (assigned Zoe book01)))\n)"}
{"id": -9182757486984277460, "group": "validation_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 4 agents: zoe, bob, steve, and vic. There are 4 items/roles: book02, book03, book01, and book04. Currently, bob is assigned book01, steve is assigned book02, vic is assigned book03, and zoe is assigned book04. The goal is to reach a state where the following facts hold: zoe is assigned book01, steve is assigned book03, bob is assigned book02, and vic is assigned book04. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1 with ?a2, ?r1 for ?r2.", "question": "What is the first inapplicable action in the next sequence of actions: \"(swap steve bob book02 book01) (swap zoe vic book04 book03) (swap steve bob book01 book04)\"?", "answer": 2, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-4-12)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Steve Vic Zoe - agent book01 book02 book03 book04 - role)\n    (:init (assigned Bob book01) (assigned Steve book02) (assigned Vic book03) (assigned Zoe book04))\n    (:goal (and (assigned Bob book02) (assigned Steve book03) (assigned Vic book04) (assigned Zoe book01)))\n)"}
{"id": -756491844613269515, "group": "validation_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 3 agents: grace, xena, and judy. There are 3 items/roles: xactoknife, funnel, and sander. Currently, grace is assigned xactoknife, judy is assigned funnel, and xena is assigned sander. The goal is to reach a state where the following facts hold: grace is assigned sander, judy is assigned xactoknife, and xena is assigned funnel. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - trade ?r1 of ?a1 for ?r2 of ?a2.", "question": "What is the first inapplicable action in the next sequence of actions: \"(swap grace xena xactoknife sander) (swap judy xena funnel xactoknife) (swap grace grace xactoknife sander) (swap xena grace sander funnel)\"?", "answer": 2, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-3-4)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Grace Judy Xena - agent Funnel Sander XActoKnife - role)\n    (:init (assigned Grace XActoKnife) (assigned Judy Funnel) (assigned Xena Sander))\n    (:goal (and (assigned Judy XActoKnife) (assigned Grace Sander) (assigned Xena Funnel)))\n)"}
{"id": 7051898622506541315, "group": "validation_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: bob, xena, frank, quentin, liam, and vic. There are 6 items/roles: ratchet, wrench, nibbler, pliers, sander, and knead. Currently, xena is assigned pliers, vic is assigned nibbler, liam is assigned knead, bob is assigned wrench, frank is assigned sander, and quentin is assigned ratchet. The goal is to reach a state where the following facts hold: vic is assigned ratchet, xena is assigned wrench, frank is assigned pliers, quentin is assigned knead, bob is assigned nibbler, and liam is assigned sander. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - trade ?r1 of ?a1 for ?r2 of ?a2.", "question": "What is the first inapplicable action in the next sequence of actions: \"(swap quentin liam ratchet knead) (swap xena frank pliers sander) (swap bob vic wrench nibbler) (swap xena vic sander knead) (swap xena liam sander wrench)\"?", "answer": 3, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Frank Liam Quentin Vic Xena - agent Knead Nibbler Pliers Ratchet Sander Wrench - role)\n    (:init (assigned Bob Wrench) (assigned Frank Sander) (assigned Liam Knead) (assigned Quentin Ratchet) (assigned Vic Nibbler) (assigned Xena Pliers))\n    (:goal (and (assigned Quentin Knead) (assigned Liam Sander) (assigned Frank Pliers) (assigned Xena Wrench) (assigned Bob Nibbler) (assigned Vic Ratchet)))\n)"}
{"id": 6752237292828795330, "group": "validation_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 4 agents: zoe, bob, steve, and vic. There are 4 items/roles: book02, book03, book01, and book04. Currently, bob is assigned book01, steve is assigned book02, vic is assigned book03, and zoe is assigned book04. The goal is to reach a state where the following facts hold: zoe is assigned book01, steve is assigned book03, bob is assigned book02, and vic is assigned book04. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1 with ?a2, ?r1 for ?r2.", "question": "What is the first inapplicable action in the next sequence of actions: \"(swap zoe bob book04 book01) (swap steve zoe book02 book01) (swap vic vic book01 book04) (swap steve bob book01 book03) (swap zoe bob book02 book01)\"?", "answer": 2, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-4-12)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Steve Vic Zoe - agent book01 book02 book03 book04 - role)\n    (:init (assigned Bob book01) (assigned Steve book02) (assigned Vic book03) (assigned Zoe book04))\n    (:goal (and (assigned Bob book02) (assigned Steve book03) (assigned Vic book04) (assigned Zoe book01)))\n)"}
