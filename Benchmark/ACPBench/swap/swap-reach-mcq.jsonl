{"id": 2508909918848501120, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: alice, michelle, heidi, vic, zoe, carol, xena, and dave. There are 8 items/roles: slinky, zebra, iceskates, guitar, whale, frisbee, necklace, and quadcopter. Currently, zoe is assigned frisbee, xena is assigned zebra, alice is assigned iceskates, heidi is assigned necklace, carol is assigned slinky, dave is assigned whale, michelle is assigned quadcopter, and vic is assigned guitar.", "question": "Which of the following options can hold in a state that can potentially be reached? A. dave is assigned slinky and zoe is assigned slinky. <PERSON>. zoe is assigned whale and alice is assigned quadcopter. C. carol is assigned guitar and carol is assigned slinky. <PERSON>. zoe is assigned guitar and zoe is assigned iceskates.", "choices": {"label": ["A", "B", "C", "D"], "text": ["dave is assigned slinky and zoe is assigned slinky", "zoe is assigned whale and alice is assigned quadcopter", "carol is assigned guitar and carol is assigned slinky", "zoe is assigned guitar and zoe is assigned iceskates"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 7533023007811137076, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: alice, michelle, heidi, vic, zoe, carol, xena, and dave. There are 8 items/roles: slinky, zebra, iceskates, guitar, whale, frisbee, necklace, and quadcopter. Currently, zoe is assigned whale, xena is assigned quadcopter, dave is assigned slinky, alice is assigned zebra, carol is assigned guitar, michelle is assigned iceskates, heidi is assigned frisbee, and vic is assigned necklace.", "question": "Which of the following options can hold in a state that can potentially be reached? A. michelle is assigned frisbee and michelle is assigned slinky. B. dave is assigned whale and dave is assigned zebra. C. heidi is assigned guitar and alice is assigned frisbee. D. carol is assigned iceskates and carol is assigned whale.", "choices": {"label": ["A", "B", "C", "D"], "text": ["michelle is assigned frisbee and michelle is assigned slinky", "dave is assigned whale and dave is assigned zebra", "heidi is assigned guitar and alice is assigned frisbee", "carol is assigned iceskates and carol is assigned whale"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 8268272490497195989, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: alice, michelle, heidi, vic, zoe, carol, xena, and dave. There are 8 items/roles: slinky, zebra, iceskates, guitar, whale, frisbee, necklace, and quadcopter. Currently, zoe is assigned whale, michelle is assigned zebra, dave is assigned slinky, xena is assigned iceskates, alice is assigned quadcopter, heidi is assigned guitar, vic is assigned necklace, and carol is assigned frisbee.", "question": "Which of the following options can hold in a state that can potentially be reached? A. heidi is assigned iceskates and heidi is assigned zebra. B. alice is assigned slinky and alice is assigned necklace. C. dave is assigned iceskates and carol is assigned zebra. D. vic is assigned frisbee and xena is assigned frisbee.", "choices": {"label": ["A", "B", "C", "D"], "text": ["heidi is assigned iceskates and heidi is assigned zebra", "alice is assigned slinky and alice is assigned necklace", "dave is assigned iceskates and carol is assigned zebra", "vic is assigned frisbee and xena is assigned frisbee"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -1293079638875164679, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: bob, quentin, vic, liam, xena, and frank. There are 6 items/roles: ratchet, sander, nibbler, wrench, knead, and pliers. Currently, liam is assigned knead, quentin is assigned pliers, bob is assigned wrench, xena is assigned ratchet, frank is assigned nibbler, and vic is assigned sander.", "question": "Which of the following options can hold in a state that can potentially be reached? A. quentin is assigned wrench and quentin is assigned knead. B. vic is assigned pliers and liam is assigned ratchet. C. vic is assigned ratchet and xena is assigned ratchet. D. xena is assigned pliers and xena is assigned nibbler.", "choices": {"label": ["A", "B", "C", "D"], "text": ["quentin is assigned wrench and quentin is assigned knead", "vic is assigned pliers and liam is assigned ratchet", "vic is assigned ratchet and xena is assigned ratchet", "xena is assigned pliers and xena is assigned nibbler"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -9082535533151561432, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: alice, michelle, heidi, vic, zoe, carol, xena, and dave. There are 8 items/roles: slinky, zebra, iceskates, guitar, whale, frisbee, necklace, and quadcopter. Currently, michelle is assigned zebra, zoe is assigned frisbee, xena is assigned iceskates, vic is assigned quadcopter, carol is assigned slinky, heidi is assigned guitar, alice is assigned whale, and dave is assigned necklace.", "question": "Which of the following options can hold in a state that can potentially be reached? A. vic is assigned quadcopter and vic is assigned guitar. B. alice is assigned iceskates and carol is assigned iceskates. C. xena is assigned necklace and xena is assigned zebra. D. carol is assigned whale and dave is assigned slinky.", "choices": {"label": ["A", "B", "C", "D"], "text": ["vic is assigned quadcopter and vic is assigned guitar", "alice is assigned iceskates and carol is assigned iceskates", "xena is assigned necklace and xena is assigned zebra", "carol is assigned whale and dave is assigned slinky"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 7950987861608913085, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: bob, quentin, vic, liam, xena, and frank. There are 6 items/roles: ratchet, sander, nibbler, wrench, knead, and pliers. Currently, liam is assigned nibbler, xena is assigned pliers, bob is assigned wrench, frank is assigned knead, quentin is assigned ratchet, and vic is assigned sander.", "question": "Which of the following options can hold in a state that can potentially be reached? A. liam is assigned wrench and xena is assigned wrench. B. liam is assigned knead and frank is assigned ratchet. C. bob is assigned ratchet and vic is assigned ratchet. D. quentin is assigned nibbler and xena is assigned nibbler.", "choices": {"label": ["A", "B", "C", "D"], "text": ["liam is assigned wrench and xena is assigned wrench", "liam is assigned knead and frank is assigned ratchet", "bob is assigned ratchet and vic is assigned ratchet", "quentin is assigned nibbler and xena is assigned nibbler"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -661948525496087502, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: bob, quentin, vic, liam, xena, and frank. There are 6 items/roles: ratchet, sander, nibbler, wrench, knead, and pliers. Currently, xena is assigned sander, vic is assigned ratchet, liam is assigned pliers, bob is assigned wrench, frank is assigned nibbler, and quentin is assigned knead.", "question": "Which of the following options can hold in a state that can potentially be reached? A. xena is assigned nibbler and xena is assigned sander. B. bob is assigned nibbler and quentin is assigned nibbler. C. xena is assigned knead and vic is assigned knead. D. vic is assigned nibbler and frank is assigned ratchet.", "choices": {"label": ["A", "B", "C", "D"], "text": ["xena is assigned nibbler and xena is assigned sander", "bob is assigned nibbler and quentin is assigned nibbler", "xena is assigned knead and vic is assigned knead", "vic is assigned nibbler and frank is assigned ratchet"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -3661908220997215533, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: alice, michelle, heidi, vic, zoe, carol, xena, and dave. There are 8 items/roles: slinky, zebra, iceskates, guitar, whale, frisbee, necklace, and quadcopter. Currently, xena is assigned quadcopter, michelle is assigned zebra, dave is assigned slinky, zoe is assigned frisbee, alice is assigned iceskates, carol is assigned whale, heidi is assigned necklace, and vic is assigned guitar.", "question": "Which of the following options can hold in a state that can potentially be reached? A. michelle is assigned slinky and vic is assigned frisbee. B. dave is assigned whale and dave is assigned iceskates. C. alice is assigned necklace and carol is assigned necklace. D. carol is assigned frisbee and carol is assigned necklace.", "choices": {"label": ["A", "B", "C", "D"], "text": ["michelle is assigned slinky and vic is assigned frisbee", "dave is assigned whale and dave is assigned iceskates", "alice is assigned necklace and carol is assigned necklace", "carol is assigned frisbee and carol is assigned necklace"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 6697203447928983700, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: bob, quentin, vic, liam, xena, and frank. There are 6 items/roles: ratchet, sander, nibbler, wrench, knead, and pliers. Currently, vic is assigned ratchet, xena is assigned knead, frank is assigned pliers, liam is assigned wrench, quentin is assigned nibbler, and bob is assigned sander.", "question": "Which of the following options can hold in a state that can potentially be reached? A. vic is assigned nibbler and vic is assigned ratchet. B. quentin is assigned ratchet and frank is assigned ratchet. C. liam is assigned sander and bob is assigned wrench. D. bob is assigned sander and xena is assigned sander.", "choices": {"label": ["A", "B", "C", "D"], "text": ["vic is assigned nibbler and vic is assigned ratchet", "quentin is assigned ratchet and frank is assigned ratchet", "liam is assigned sander and bob is assigned wrench", "bob is assigned sander and xena is assigned sander"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 6124153306201193586, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: bob, heidi, alice, ted, kevin, xena, and dave. There are 7 items/roles: quince, leek, valerian, parsnip, yam, ulluco, and mushroom. Currently, kevin is assigned mushroom, ted is assigned leek, bob is assigned parsnip, alice is assigned valerian, heidi is assigned quince, xena is assigned ulluco, and dave is assigned yam.", "question": "Which of the following options can hold in a state that can potentially be reached? A. bob is assigned leek and alice is assigned leek. B. bob is assigned ulluco and xena is assigned parsnip. C. xena is assigned quince and xena is assigned leek. D. bob is assigned quince and alice is assigned quince.", "choices": {"label": ["A", "B", "C", "D"], "text": ["bob is assigned leek and alice is assigned leek", "bob is assigned ulluco and xena is assigned parsnip", "xena is assigned quince and xena is assigned leek", "bob is assigned quince and alice is assigned quince"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
