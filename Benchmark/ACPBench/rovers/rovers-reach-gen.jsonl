{"id": 6156520384713597334, "group": "reachable_atom_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera2 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Camera camera0 supports colour. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint1. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Image objective1 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode colour. Store(s) store1 is empty. Store(s) store0 is full. The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at_rock_sample waypoint2", "calibrated camera1 rover1", "have_rock_analysis rover1 waypoint2", "have_image rover0 objective0 high_res", "have_soil_analysis rover0 waypoint1", "have_rock_analysis rover0 waypoint2", "have_rock_analysis rover0 waypoint0", "have_image rover1 objective1 low_res", "at_soil_sample waypoint2", "at_rock_sample waypoint1", "have_soil_analysis rover1 waypoint2", "have_image rover1 objective2 low_res", "have_rock_analysis rover0 waypoint1", "at_soil_sample waypoint1", "have_image rover1 objective0 low_res", "have_image rover0 objective2 high_res", "calibrated camera2 rover0", "communicated_soil_data waypoint2", "have_soil_analysis rover1 waypoint1", "communicated_rock_data waypoint2", "have_rock_analysis rover1 waypoint1", "communicated_soil_data waypoint1", "at_soil_sample waypoint0", "have_soil_analysis rover0 waypoint2", "have_image rover0 objective1 high_res", "communicated_rock_data waypoint1", "calibrated camera0 rover1", "at_rock_sample waypoint0"], "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-3-2-3-2021)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 objective2 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint2) (at rover1 waypoint1) (at_lander general waypoint1) (available rover0) (available rover1) (calibration_target camera0 objective1) (calibration_target camera1 objective0) (calibration_target camera2 objective0) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint0 waypoint2) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint2 waypoint0) (can_traverse rover1 waypoint0 waypoint2) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint0) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (communicated_image_data objective1 colour) (communicated_rock_data waypoint0) (communicated_soil_data waypoint0) (empty store1) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover1) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (full store0) (have_image rover0 objective1 colour) (have_rock_analysis rover1 waypoint0) (have_soil_analysis rover0 waypoint0) (on_board camera0 rover0) (on_board camera1 rover0) (on_board camera2 rover1) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera1 low_res) (supports camera2 colour) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint0) (visible_from objective0 waypoint1) (visible_from objective1 waypoint2) (visible_from objective2 waypoint1))\n    (:goal (and (communicated_soil_data waypoint0) (communicated_rock_data waypoint0) (communicated_image_data objective1 colour)))\n)"}
{"id": 3254531895673580741, "group": "reachable_atom_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera2 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Camera camera0 supports colour. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Rover rover1 has its camera camera2 calibrated. Rover rover0 has its camera camera0 calibrated. Store(s) store0 and store1 are empty. The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at_rock_sample waypoint2", "have_image rover1 objective0 low_res", "calibrated camera1 rover1", "have_rock_analysis rover1 waypoint2", "have_image rover0 objective0 high_res", "have_image rover0 objective2 high_res", "calibrated camera2 rover0", "have_soil_analysis rover0 waypoint1", "communicated_soil_data waypoint2", "have_rock_analysis rover0 waypoint2", "have_rock_analysis rover0 waypoint0", "have_soil_analysis rover1 waypoint1", "communicated_rock_data waypoint2", "have_rock_analysis rover1 waypoint1", "have_image rover1 objective1 low_res", "at_soil_sample waypoint2", "communicated_soil_data waypoint1", "have_soil_analysis rover0 waypoint2", "have_image rover0 objective1 high_res", "at_rock_sample waypoint1", "have_soil_analysis rover1 waypoint2", "communicated_rock_data waypoint1", "have_image rover1 objective2 low_res", "calibrated camera0 rover1", "have_rock_analysis rover0 waypoint1", "at_soil_sample waypoint1"], "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-3-2-3-2021)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 objective2 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint2) (at rover1 waypoint0) (at_lander general waypoint1) (at_rock_sample waypoint0) (at_soil_sample waypoint0) (available rover0) (available rover1) (calibrated camera0 rover0) (calibrated camera2 rover1) (calibration_target camera0 objective1) (calibration_target camera1 objective0) (calibration_target camera2 objective0) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint0 waypoint2) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint2 waypoint0) (can_traverse rover1 waypoint0 waypoint2) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint0) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (empty store0) (empty store1) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover1) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (on_board camera0 rover0) (on_board camera1 rover0) (on_board camera2 rover1) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera1 low_res) (supports camera2 colour) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint0) (visible_from objective0 waypoint1) (visible_from objective1 waypoint2) (visible_from objective2 waypoint1))\n    (:goal (and (communicated_soil_data waypoint0) (communicated_rock_data waypoint0) (communicated_image_data objective1 colour)))\n)"}
{"id": 6782102831992146811, "group": "reachable_atom_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera2 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Camera camera0 supports colour. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint0. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Image objective1 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode colour. Store(s) store0 and store1 are full. The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at_rock_sample waypoint2", "calibrated camera1 rover1", "have_rock_analysis rover1 waypoint2", "have_image rover0 objective0 high_res", "have_soil_analysis rover0 waypoint1", "have_rock_analysis rover0 waypoint2", "have_rock_analysis rover0 waypoint0", "have_image rover1 objective1 low_res", "at_soil_sample waypoint2", "at_rock_sample waypoint1", "have_soil_analysis rover1 waypoint2", "have_image rover1 objective2 low_res", "have_rock_analysis rover0 waypoint1", "at_soil_sample waypoint1", "have_image rover1 objective0 low_res", "have_image rover0 objective2 high_res", "calibrated camera2 rover0", "communicated_soil_data waypoint2", "have_soil_analysis rover1 waypoint1", "communicated_rock_data waypoint2", "have_rock_analysis rover1 waypoint1", "communicated_soil_data waypoint1", "at_soil_sample waypoint0", "have_soil_analysis rover0 waypoint2", "have_image rover0 objective1 high_res", "communicated_rock_data waypoint1", "calibrated camera0 rover1", "at_rock_sample waypoint0"], "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-3-2-3-2021)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 objective2 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint2) (at rover1 waypoint0) (at_lander general waypoint1) (available rover0) (available rover1) (calibration_target camera0 objective1) (calibration_target camera1 objective0) (calibration_target camera2 objective0) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint0 waypoint2) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint2 waypoint0) (can_traverse rover1 waypoint0 waypoint2) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint0) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (communicated_image_data objective1 colour) (communicated_rock_data waypoint0) (communicated_soil_data waypoint0) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover1) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (full store0) (full store1) (have_image rover0 objective1 colour) (have_rock_analysis rover1 waypoint0) (have_soil_analysis rover0 waypoint0) (on_board camera0 rover0) (on_board camera1 rover0) (on_board camera2 rover1) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera1 low_res) (supports camera2 colour) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint0) (visible_from objective0 waypoint1) (visible_from objective1 waypoint2) (visible_from objective2 waypoint1))\n    (:goal (and (communicated_soil_data waypoint0) (communicated_rock_data waypoint0) (communicated_image_data objective1 colour)))\n)"}
{"id": -1132770160776581242, "group": "reachable_atom_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 7 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera0 can be calibrated on objective4. Camera camera2 can be calibrated on objective1. Camera camera1 can be calibrated on objective1. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Camera camera0 supports colour. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective3 is visible from waypoint2. Objective objective6 is visible from waypoint1 and waypoint0. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Objective objective5 is visible from waypoint1. Objective objective4 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint1 and waypoint0. Soil can be sampled at the following location(s): waypoint2 and waypoint1. Rovers rover0 and rover1 are available. Rover rover1 has rock analyzed in waypoint waypoint2. Rover rover0 has its camera camera1 calibrated. Rover rover1 has its camera camera2 calibrated. Store(s) store0 is empty. Store(s) store1 is full. The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at_rock_sample waypoint2", "have_image rover1 objective0 low_res", "calibrated camera1 rover1", "have_image rover1 objective3 low_res", "have_image rover0 objective3 high_res", "have_image rover0 objective0 high_res", "have_image rover1 objective4 low_res", "have_image rover0 objective2 high_res", "calibrated camera2 rover0", "have_soil_analysis rover1 waypoint0", "have_image rover0 objective5 high_res", "have_rock_analysis rover0 waypoint2", "have_image rover0 objective4 high_res", "have_rock_analysis rover0 waypoint0", "have_soil_analysis rover0 waypoint0", "at_soil_sample waypoint0", "have_image rover1 objective1 low_res", "have_image rover1 objective5 low_res", "have_image rover1 objective6 low_res", "have_image rover0 objective1 high_res", "communicated_soil_data waypoint0", "have_image rover0 objective6 high_res", "calibrated camera0 rover1", "have_image rover1 objective2 low_res", "have_rock_analysis rover0 waypoint1"], "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-7-2-7-2021)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 objective2 objective3 objective4 objective5 objective6 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint2) (at rover1 waypoint2) (at_lander general waypoint1) (at_rock_sample waypoint0) (at_rock_sample waypoint1) (at_soil_sample waypoint1) (at_soil_sample waypoint2) (available rover0) (available rover1) (calibrated camera1 rover0) (calibrated camera2 rover1) (calibration_target camera0 objective4) (calibration_target camera1 objective1) (calibration_target camera2 objective1) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint0 waypoint2) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint2 waypoint0) (can_traverse rover1 waypoint0 waypoint2) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint0) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (empty store0) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover1) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (full store1) (have_rock_analysis rover1 waypoint2) (on_board camera0 rover0) (on_board camera1 rover0) (on_board camera2 rover1) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera1 low_res) (supports camera2 colour) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint0) (visible_from objective0 waypoint1) (visible_from objective1 waypoint2) (visible_from objective2 waypoint1) (visible_from objective3 waypoint2) (visible_from objective4 waypoint1) (visible_from objective5 waypoint1) (visible_from objective6 waypoint0) (visible_from objective6 waypoint1))\n    (:goal (and (communicated_soil_data waypoint1) (communicated_soil_data waypoint2) (communicated_rock_data waypoint2) (communicated_rock_data waypoint0) (communicated_rock_data waypoint1) (communicated_image_data objective6 low_res) (communicated_image_data objective3 high_res) (communicated_image_data objective0 colour) (communicated_image_data objective4 low_res) (communicated_image_data objective0 low_res)))\n)"}
{"id": -6649609271720637307, "group": "reachable_atom_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res and colour. Camera camera0 supports colour and low_res. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint4 to waypoint1, waypoint1 to waypoint0. Waypoint(s) are visible from waypoint0: waypoint3, waypoint2, waypoint1, and waypoint4. Waypoint(s) are visible from waypoint1: waypoint0, waypoint4, and waypoint2. Waypoint(s) are visible from waypoint2: waypoint3, waypoint1, and waypoint0. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint3: waypoint0 and waypoint2. Objective objective0 is visible from waypoint2 and waypoint1. Objective objective1 is visible from waypoint4. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint4. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Image objective0 was communicated in mode colour. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has image objective0 in mode low_res. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode colour. Store(s) store0 is empty. Store(s) store1 is full. The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at rover0 waypoint2", "at_rock_sample waypoint2", "have_rock_analysis rover0 waypoint3", "have_image rover1 objective0 high_res", "communicated_soil_data waypoint4", "communicated_image_data objective1 high_res", "communicated_rock_data waypoint4", "have_rock_analysis rover1 waypoint2", "have_image rover0 objective0 high_res", "have_soil_analysis rover0 waypoint4", "have_soil_analysis rover0 waypoint3", "have_image rover1 objective1 high_res", "at_soil_sample waypoint3", "have_soil_analysis rover0 waypoint1", "have_rock_analysis rover0 waypoint2", "have_rock_analysis rover0 waypoint0", "at_soil_sample waypoint4", "have_image rover1 objective1 low_res", "at_soil_sample waypoint2", "at_rock_sample waypoint1", "have_soil_analysis rover1 waypoint2", "communicated_soil_data waypoint3", "have_rock_analysis rover0 waypoint4", "at_rock_sample waypoint4", "have_rock_analysis rover0 waypoint1", "at_soil_sample waypoint1", "at_rock_sample waypoint3", "calibrated camera1 rover0", "communicated_rock_data waypoint3", "have_rock_analysis rover1 waypoint3", "have_soil_analysis rover1 waypoint4", "at rover0 waypoint3", "at rover1 waypoint3", "at rover1 waypoint4", "communicated_soil_data waypoint2", "have_soil_analysis rover1 waypoint1", "communicated_rock_data waypoint2", "communicated_soil_data waypoint1", "have_soil_analysis rover1 waypoint3", "have_soil_analysis rover0 waypoint2", "have_image rover0 objective1 high_res", "communicated_image_data objective0 high_res", "calibrated camera0 rover1", "have_image rover1 objective1 colour", "have_rock_analysis rover1 waypoint4"], "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-5-2-2-5-2020)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 - camera general - lander colour high_res low_res - mode objective0 objective1 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 waypoint3 waypoint4 - waypoint)\n    (:init (at rover0 waypoint4) (at rover1 waypoint2) (at_lander general waypoint3) (at_rock_sample waypoint0) (at_soil_sample waypoint0) (available rover0) (available rover1) (calibration_target camera0 objective0) (calibration_target camera1 objective0) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint1 waypoint4) (can_traverse rover0 waypoint4 waypoint1) (can_traverse rover1 waypoint0 waypoint2) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint0) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (communicated_image_data objective0 colour) (empty store0) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover1) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (full store1) (have_image rover0 objective1 low_res) (have_image rover1 objective0 colour) (have_image rover1 objective0 low_res) (have_rock_analysis rover1 waypoint1) (on_board camera0 rover0) (on_board camera1 rover1) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera0 low_res) (supports camera1 colour) (supports camera1 low_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint0 waypoint3) (visible waypoint0 waypoint4) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint1 waypoint4) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible waypoint2 waypoint3) (visible waypoint3 waypoint0) (visible waypoint3 waypoint2) (visible waypoint4 waypoint0) (visible waypoint4 waypoint1) (visible_from objective0 waypoint1) (visible_from objective0 waypoint2) (visible_from objective1 waypoint4))\n    (:goal (and (communicated_soil_data waypoint0) (communicated_rock_data waypoint1) (communicated_rock_data waypoint0) (communicated_image_data objective0 low_res) (communicated_image_data objective0 colour) (communicated_image_data objective1 low_res)))\n)"}
{"id": 3348236759441841348, "group": "reachable_atom_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 and camera1 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera0 supports high_res and low_res and colour. Camera camera1 supports colour. Camera camera2 supports low_res and colour. Rover rover0 can traverse from waypoint5 to waypoint6, waypoint5 to waypoint4, waypoint0 to waypoint5, waypoint5 to waypoint2, waypoint3 to waypoint0, waypoint0 to waypoint3, waypoint5 to waypoint0, waypoint6 to waypoint5, waypoint1 to waypoint5, waypoint4 to waypoint5, waypoint5 to waypoint1, waypoint2 to waypoint5. Rover rover1 can traverse from waypoint0 to waypoint3, waypoint0 to waypoint6, waypoint2 to waypoint4, waypoint3 to waypoint0, waypoint6 to waypoint0, waypoint5 to waypoint0, waypoint4 to waypoint2, waypoint6 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0, waypoint1 to waypoint6, waypoint0 to waypoint5. Waypoint(s) are visible from waypoint6: waypoint3, waypoint5, waypoint1, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint3: waypoint5, waypoint6, waypoint1, waypoint2, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint1: waypoint6, waypoint2, waypoint4, waypoint3, and waypoint5. Waypoint(s) are visible from waypoint5: waypoint2, waypoint4, waypoint0, waypoint3, waypoint6, and waypoint1. Waypoint(s) are visible from waypoint4: waypoint3, waypoint5, waypoint6, waypoint1, and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, waypoint5, waypoint1, and waypoint4. Waypoint(s) are visible from waypoint0: waypoint2, waypoint3, waypoint5, and waypoint6. Objective objective1 is visible from waypoint0, waypoint2, waypoint5, and waypoint4. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint4. Rover rover1 is at waypoint0. Rocks can be sampled at the following location(s): waypoint2, waypoint4, and waypoint6. Soil can be sampled at the following location(s): waypoint3, waypoint4, waypoint1, and waypoint6. Rovers rover0 and rover1 are available. Rock data were communicated from the following waypoints: waypoint5, waypoint3. Image objective1 was communicated in mode high_res. Image objective1 was communicated in mode low_res. Image objective1 was communicated in mode colour. Image objective0 was communicated in mode high_res. Rover rover1 has rock analyzed in waypoint waypoint3. Rover rover0 has rock analyzed in waypoint waypoint5. Rover rover1 has image objective0 in mode high_res. Rover rover0 has image objective1 in mode colour. Rover rover1 has image objective1 in mode high_res. Rover rover1 has image objective1 in mode low_res. Store(s) store0 and store1 are empty. The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["have_soil_analysis rover0 waypoint6", "have_soil_analysis rover0 waypoint5", "communicated_soil_data waypoint4", "communicated_soil_data waypoint6", "have_image rover0 objective0 high_res", "have_soil_analysis rover0 waypoint4", "have_soil_analysis rover0 waypoint3", "have_soil_analysis rover1 waypoint0", "have_soil_analysis rover0 waypoint1", "have_rock_analysis rover0 waypoint0", "communicated_soil_data waypoint5", "have_soil_analysis rover0 waypoint0", "at_soil_sample waypoint2", "at_rock_sample waypoint1", "have_soil_analysis rover1 waypoint2", "communicated_soil_data waypoint3", "at_soil_sample waypoint5", "calibrated camera0 rover0", "have_rock_analysis rover0 waypoint1", "at_rock_sample waypoint3", "calibrated camera1 rover0", "have_soil_analysis rover1 waypoint4", "have_soil_analysis rover1 waypoint6", "communicated_soil_data waypoint2", "at_rock_sample waypoint5", "have_soil_analysis rover1 waypoint1", "have_rock_analysis rover1 waypoint1", "communicated_soil_data waypoint1", "at_soil_sample waypoint0", "have_soil_analysis rover1 waypoint3", "calibrated camera2 rover1", "have_soil_analysis rover0 waypoint2", "have_image rover0 objective1 high_res", "communicated_soil_data waypoint0", "communicated_rock_data waypoint0", "communicated_rock_data waypoint1", "at_rock_sample waypoint0", "have_soil_analysis rover1 waypoint5", "have_rock_analysis rover1 waypoint0"], "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-7-2-2-7-2022)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 waypoint3 waypoint4 waypoint5 waypoint6 - waypoint)\n    (:init (at rover0 waypoint4) (at rover1 waypoint0) (at_lander general waypoint3) (at_rock_sample waypoint2) (at_rock_sample waypoint4) (at_rock_sample waypoint6) (at_soil_sample waypoint1) (at_soil_sample waypoint3) (at_soil_sample waypoint4) (at_soil_sample waypoint6) (available rover0) (available rover1) (calibration_target camera0 objective1) (calibration_target camera1 objective0) (calibration_target camera2 objective1) (can_traverse rover0 waypoint0 waypoint3) (can_traverse rover0 waypoint0 waypoint5) (can_traverse rover0 waypoint1 waypoint5) (can_traverse rover0 waypoint2 waypoint5) (can_traverse rover0 waypoint3 waypoint0) (can_traverse rover0 waypoint4 waypoint5) (can_traverse rover0 waypoint5 waypoint0) (can_traverse rover0 waypoint5 waypoint1) (can_traverse rover0 waypoint5 waypoint2) (can_traverse rover0 waypoint5 waypoint4) (can_traverse rover0 waypoint5 waypoint6) (can_traverse rover0 waypoint6 waypoint5) (can_traverse rover1 waypoint0 waypoint2) (can_traverse rover1 waypoint0 waypoint3) (can_traverse rover1 waypoint0 waypoint5) (can_traverse rover1 waypoint0 waypoint6) (can_traverse rover1 waypoint1 waypoint6) (can_traverse rover1 waypoint2 waypoint0) (can_traverse rover1 waypoint2 waypoint4) (can_traverse rover1 waypoint3 waypoint0) (can_traverse rover1 waypoint4 waypoint2) (can_traverse rover1 waypoint5 waypoint0) (can_traverse rover1 waypoint6 waypoint0) (can_traverse rover1 waypoint6 waypoint1) (channel_free general) (communicated_image_data objective0 high_res) (communicated_image_data objective1 colour) (communicated_image_data objective1 high_res) (communicated_image_data objective1 low_res) (communicated_rock_data waypoint3) (communicated_rock_data waypoint5) (empty store0) (empty store1) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover0) (equipped_for_rock_analysis rover1) (have_image rover0 objective1 colour) (have_image rover1 objective0 high_res) (have_image rover1 objective1 high_res) (have_image rover1 objective1 low_res) (have_rock_analysis rover0 waypoint5) (have_rock_analysis rover1 waypoint3) (on_board camera0 rover1) (on_board camera1 rover1) (on_board camera2 rover0) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera0 high_res) (supports camera0 low_res) (supports camera1 colour) (supports camera2 colour) (supports camera2 low_res) (visible waypoint0 waypoint2) (visible waypoint0 waypoint3) (visible waypoint0 waypoint5) (visible waypoint0 waypoint6) (visible waypoint1 waypoint2) (visible waypoint1 waypoint3) (visible waypoint1 waypoint4) (visible waypoint1 waypoint5) (visible waypoint1 waypoint6) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible waypoint2 waypoint3) (visible waypoint2 waypoint4) (visible waypoint2 waypoint5) (visible waypoint3 waypoint0) (visible waypoint3 waypoint1) (visible waypoint3 waypoint2) (visible waypoint3 waypoint4) (visible waypoint3 waypoint5) (visible waypoint3 waypoint6) (visible waypoint4 waypoint1) (visible waypoint4 waypoint2) (visible waypoint4 waypoint3) (visible waypoint4 waypoint5) (visible waypoint4 waypoint6) (visible waypoint5 waypoint0) (visible waypoint5 waypoint1) (visible waypoint5 waypoint2) (visible waypoint5 waypoint3) (visible waypoint5 waypoint4) (visible waypoint5 waypoint6) (visible waypoint6 waypoint0) (visible waypoint6 waypoint1) (visible waypoint6 waypoint3) (visible waypoint6 waypoint4) (visible waypoint6 waypoint5) (visible_from objective0 waypoint0) (visible_from objective1 waypoint0) (visible_from objective1 waypoint2) (visible_from objective1 waypoint4) (visible_from objective1 waypoint5))\n    (:goal (and (communicated_rock_data waypoint3) (communicated_rock_data waypoint4) (communicated_rock_data waypoint5) (communicated_image_data objective1 colour) (communicated_image_data objective0 high_res) (communicated_image_data objective1 low_res) (communicated_image_data objective1 high_res)))\n)"}
{"id": 3760274113903906388, "group": "reachable_atom_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 7 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera0 can be calibrated on objective4. Camera camera2 can be calibrated on objective1. Camera camera1 can be calibrated on objective1. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Camera camera0 supports colour. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective3 is visible from waypoint2. Objective objective6 is visible from waypoint1 and waypoint0. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Objective objective5 is visible from waypoint1. Objective objective4 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rock data were communicated from the following waypoints: waypoint2, waypoint0. Soil data was communicated from waypoint waypoint2; Image objective3 was communicated in mode high_res. Rover rover0 has soil analyzed in waypoint waypoint2. Rover rover1 has rock analyzed in waypoint waypoint2. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has image objective3 in mode high_res. Rover rover0 has its camera camera1 calibrated. Store(s) store0 is empty. Store(s) store1 is full. The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at_rock_sample waypoint2", "have_image rover1 objective0 low_res", "calibrated camera1 rover1", "have_image rover1 objective3 low_res", "have_image rover0 objective3 high_res", "have_image rover0 objective0 high_res", "have_image rover1 objective4 low_res", "have_image rover0 objective2 high_res", "calibrated camera2 rover0", "have_soil_analysis rover1 waypoint0", "have_image rover0 objective5 high_res", "have_rock_analysis rover0 waypoint2", "have_image rover0 objective4 high_res", "have_rock_analysis rover0 waypoint0", "have_soil_analysis rover0 waypoint0", "at_soil_sample waypoint0", "at_soil_sample waypoint2", "have_image rover1 objective1 low_res", "have_image rover1 objective5 low_res", "have_image rover1 objective6 low_res", "have_image rover0 objective1 high_res", "communicated_soil_data waypoint0", "have_image rover0 objective6 high_res", "calibrated camera0 rover1", "have_image rover1 objective2 low_res", "at_rock_sample waypoint0", "have_rock_analysis rover0 waypoint1"], "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-7-2-7-2021)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 objective2 objective3 objective4 objective5 objective6 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint0) (at rover1 waypoint2) (at_lander general waypoint1) (at_rock_sample waypoint1) (at_soil_sample waypoint1) (available rover0) (available rover1) (calibrated camera1 rover0) (calibration_target camera0 objective4) (calibration_target camera1 objective1) (calibration_target camera2 objective1) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint0 waypoint2) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint2 waypoint0) (can_traverse rover1 waypoint0 waypoint2) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint0) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (communicated_image_data objective3 high_res) (communicated_rock_data waypoint0) (communicated_rock_data waypoint2) (communicated_soil_data waypoint2) (empty store0) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover1) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (full store1) (have_image rover1 objective3 high_res) (have_rock_analysis rover1 waypoint0) (have_rock_analysis rover1 waypoint2) (have_soil_analysis rover0 waypoint2) (on_board camera0 rover0) (on_board camera1 rover0) (on_board camera2 rover1) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera1 low_res) (supports camera2 colour) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint0) (visible_from objective0 waypoint1) (visible_from objective1 waypoint2) (visible_from objective2 waypoint1) (visible_from objective3 waypoint2) (visible_from objective4 waypoint1) (visible_from objective5 waypoint1) (visible_from objective6 waypoint0) (visible_from objective6 waypoint1))\n    (:goal (and (communicated_soil_data waypoint1) (communicated_soil_data waypoint2) (communicated_rock_data waypoint2) (communicated_rock_data waypoint0) (communicated_rock_data waypoint1) (communicated_image_data objective6 low_res) (communicated_image_data objective3 high_res) (communicated_image_data objective0 colour) (communicated_image_data objective4 low_res) (communicated_image_data objective0 low_res)))\n)"}
{"id": -7902794509197002930, "group": "reachable_atom_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 7 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera0 can be calibrated on objective4. Camera camera2 can be calibrated on objective1. Camera camera1 can be calibrated on objective1. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Camera camera0 supports colour. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective3 is visible from waypoint2. Objective objective6 is visible from waypoint1 and waypoint0. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Objective objective5 is visible from waypoint1. Objective objective4 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint1. Rover rover1 is at waypoint0. Rocks can be sampled at the following location(s): waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rock data were communicated from the following waypoints: waypoint2, waypoint0. Soil data was communicated from waypoint waypoint2; Image objective0 was communicated in mode colour. Image objective3 was communicated in mode high_res. Rover rover0 has soil analyzed in waypoint waypoint2. Rover rover1 has rock analyzed in waypoint waypoint2. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has image objective3 in mode high_res. Rover rover1 has image objective0 in mode colour. Rover rover0 has its camera camera1 calibrated. Store(s) store0 and store1 are full. The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at_rock_sample waypoint2", "have_image rover1 objective0 low_res", "calibrated camera1 rover1", "have_image rover1 objective3 low_res", "have_image rover0 objective3 high_res", "have_image rover0 objective0 high_res", "have_image rover1 objective4 low_res", "have_image rover0 objective2 high_res", "calibrated camera2 rover0", "have_soil_analysis rover1 waypoint0", "have_image rover0 objective5 high_res", "have_rock_analysis rover0 waypoint2", "have_image rover0 objective4 high_res", "have_rock_analysis rover0 waypoint0", "have_soil_analysis rover0 waypoint0", "at_soil_sample waypoint0", "at_soil_sample waypoint2", "have_image rover1 objective1 low_res", "have_image rover1 objective5 low_res", "have_image rover1 objective6 low_res", "have_image rover0 objective1 high_res", "communicated_soil_data waypoint0", "have_image rover0 objective6 high_res", "calibrated camera0 rover1", "have_image rover1 objective2 low_res", "at_rock_sample waypoint0", "have_rock_analysis rover0 waypoint1"], "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-7-2-7-2021)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 objective2 objective3 objective4 objective5 objective6 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint1) (at rover1 waypoint0) (at_lander general waypoint1) (at_rock_sample waypoint1) (at_soil_sample waypoint1) (available rover0) (available rover1) (calibrated camera1 rover0) (calibration_target camera0 objective4) (calibration_target camera1 objective1) (calibration_target camera2 objective1) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint0 waypoint2) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint2 waypoint0) (can_traverse rover1 waypoint0 waypoint2) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint0) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (communicated_image_data objective0 colour) (communicated_image_data objective3 high_res) (communicated_rock_data waypoint0) (communicated_rock_data waypoint2) (communicated_soil_data waypoint2) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover1) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (full store0) (full store1) (have_image rover1 objective0 colour) (have_image rover1 objective3 high_res) (have_rock_analysis rover1 waypoint0) (have_rock_analysis rover1 waypoint2) (have_soil_analysis rover0 waypoint2) (on_board camera0 rover0) (on_board camera1 rover0) (on_board camera2 rover1) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera1 low_res) (supports camera2 colour) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint0) (visible_from objective0 waypoint1) (visible_from objective1 waypoint2) (visible_from objective2 waypoint1) (visible_from objective3 waypoint2) (visible_from objective4 waypoint1) (visible_from objective5 waypoint1) (visible_from objective6 waypoint0) (visible_from objective6 waypoint1))\n    (:goal (and (communicated_soil_data waypoint1) (communicated_soil_data waypoint2) (communicated_rock_data waypoint2) (communicated_rock_data waypoint0) (communicated_rock_data waypoint1) (communicated_image_data objective6 low_res) (communicated_image_data objective3 high_res) (communicated_image_data objective0 colour) (communicated_image_data objective4 low_res) (communicated_image_data objective0 low_res)))\n)"}
{"id": -2240685937796735625, "group": "reachable_atom_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res and high_res. Camera camera0 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint1. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint1; Image objective1 was communicated in mode low_res. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective0 in mode low_res. Rover rover0 has image objective1 in mode low_res. Rover rover0 has its camera camera1 calibrated. Store(s) store1 is empty. Store(s) store0 is full. The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at_rock_sample waypoint2", "communicated_image_data objective0 colour", "have_image rover1 objective0 high_res", "calibrated camera1 rover1", "have_rock_analysis rover1 waypoint2", "have_image rover0 objective1 colour", "have_soil_analysis rover1 waypoint0", "have_image rover1 objective1 high_res", "have_rock_analysis rover0 waypoint2", "have_rock_analysis rover0 waypoint0", "have_soil_analysis rover0 waypoint0", "at_soil_sample waypoint2", "at_rock_sample waypoint1", "have_soil_analysis rover1 waypoint2", "calibrated camera0 rover0", "at_soil_sample waypoint1", "have_image rover1 objective0 colour", "communicated_soil_data waypoint2", "have_image rover0 objective0 colour", "have_soil_analysis rover1 waypoint1", "communicated_rock_data waypoint2", "at_soil_sample waypoint0", "have_soil_analysis rover0 waypoint2", "communicated_image_data objective1 colour", "communicated_soil_data waypoint0", "communicated_rock_data waypoint0", "at_rock_sample waypoint0", "have_image rover1 objective1 colour", "have_rock_analysis rover1 waypoint0"], "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-2-2-3-2022)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 - camera general - lander colour high_res low_res - mode objective0 objective1 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint0) (at rover1 waypoint1) (at_lander general waypoint1) (available rover0) (available rover1) (calibrated camera1 rover0) (calibration_target camera0 objective1) (calibration_target camera1 objective0) (can_traverse rover0 waypoint0 waypoint2) (can_traverse rover0 waypoint1 waypoint2) (can_traverse rover0 waypoint2 waypoint0) (can_traverse rover0 waypoint2 waypoint1) (can_traverse rover1 waypoint0 waypoint2) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint0) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (communicated_image_data objective1 low_res) (communicated_rock_data waypoint1) (empty store1) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover0) (equipped_for_rock_analysis rover1) (equipped_for_soil_analysis rover0) (full store0) (have_image rover0 objective0 low_res) (have_image rover0 objective1 low_res) (have_rock_analysis rover0 waypoint1) (have_soil_analysis rover0 waypoint1) (on_board camera0 rover1) (on_board camera1 rover0) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 low_res) (supports camera1 high_res) (supports camera1 low_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint0) (visible_from objective0 waypoint1) (visible_from objective1 waypoint1))\n    (:goal (and (communicated_soil_data waypoint1) (communicated_rock_data waypoint1) (communicated_image_data objective0 low_res) (communicated_image_data objective1 low_res)))\n)"}
{"id": -2424006624763962247, "group": "reachable_atom_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res and high_res. Camera camera0 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint1. Rover rover1 is at waypoint2. Soil can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint1; Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode low_res. Store(s) store0 is empty. Store(s) store1 is full. The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at_rock_sample waypoint2", "communicated_image_data objective0 colour", "have_image rover1 objective0 high_res", "calibrated camera1 rover1", "have_rock_analysis rover1 waypoint2", "have_image rover0 objective1 colour", "have_soil_analysis rover1 waypoint0", "have_image rover1 objective1 high_res", "have_rock_analysis rover0 waypoint2", "have_rock_analysis rover0 waypoint0", "have_soil_analysis rover0 waypoint0", "at_soil_sample waypoint2", "at_rock_sample waypoint1", "have_soil_analysis rover1 waypoint2", "calibrated camera0 rover0", "have_image rover1 objective0 colour", "communicated_soil_data waypoint2", "have_image rover0 objective0 colour", "have_soil_analysis rover1 waypoint1", "communicated_rock_data waypoint2", "at_soil_sample waypoint0", "have_soil_analysis rover0 waypoint2", "communicated_image_data objective1 colour", "communicated_soil_data waypoint0", "communicated_rock_data waypoint0", "at_rock_sample waypoint0", "have_image rover1 objective1 colour", "have_rock_analysis rover1 waypoint0"], "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-2-2-3-2022)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 - camera general - lander colour high_res low_res - mode objective0 objective1 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint1) (at rover1 waypoint2) (at_lander general waypoint1) (at_soil_sample waypoint1) (available rover0) (available rover1) (calibration_target camera0 objective1) (calibration_target camera1 objective0) (can_traverse rover0 waypoint0 waypoint2) (can_traverse rover0 waypoint1 waypoint2) (can_traverse rover0 waypoint2 waypoint0) (can_traverse rover0 waypoint2 waypoint1) (can_traverse rover1 waypoint0 waypoint2) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint0) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (communicated_rock_data waypoint1) (empty store0) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover0) (equipped_for_rock_analysis rover1) (equipped_for_soil_analysis rover0) (full store1) (have_image rover1 objective0 low_res) (have_image rover1 objective1 low_res) (have_rock_analysis rover1 waypoint1) (on_board camera0 rover1) (on_board camera1 rover0) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 low_res) (supports camera1 high_res) (supports camera1 low_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint0) (visible_from objective0 waypoint1) (visible_from objective1 waypoint1))\n    (:goal (and (communicated_soil_data waypoint1) (communicated_rock_data waypoint1) (communicated_image_data objective0 low_res) (communicated_image_data objective1 low_res)))\n)"}
{"id": -5864070665391174627, "group": "reachable_atom_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. \nThere are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  \nRover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera2 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Camera camera0 supports colour. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  \nCurrently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Image objective0 was communicated in mode low_res. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective0 in mode low_res. Rover rover1 has its camera camera2 calibrated. Store(s) store0 and store1 are empty.  The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at_rock_sample waypoint2", "have_image rover1 objective0 low_res", "calibrated camera1 rover1", "have_rock_analysis rover1 waypoint2", "have_rock_analysis rover1 waypoint0", "have_image rover0 objective0 high_res", "calibrated camera2 rover0", "have_soil_analysis rover1 waypoint0", "communicated_soil_data waypoint2", "have_rock_analysis rover0 waypoint2", "have_rock_analysis rover0 waypoint0", "communicated_rock_data waypoint2", "have_soil_analysis rover0 waypoint0", "at_soil_sample waypoint0", "at_soil_sample waypoint2", "have_image rover1 objective1 low_res", "have_soil_analysis rover0 waypoint2", "have_image rover0 objective1 high_res", "communicated_soil_data waypoint0", "at_rock_sample waypoint1", "have_soil_analysis rover1 waypoint2", "communicated_rock_data waypoint0", "calibrated camera0 rover1", "at_rock_sample waypoint0", "have_rock_analysis rover0 waypoint1"], "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-2-2-3-2021)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint0) (at rover1 waypoint1) (at_lander general waypoint1) (at_soil_sample waypoint1) (available rover0) (available rover1) (calibrated camera2 rover1) (calibration_target camera0 objective1) (calibration_target camera1 objective0) (calibration_target camera2 objective0) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint0 waypoint2) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint2 waypoint0) (can_traverse rover1 waypoint0 waypoint2) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint0) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (communicated_image_data objective0 low_res) (empty store0) (empty store1) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover1) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (have_image rover0 objective0 low_res) (have_rock_analysis rover1 waypoint1) (on_board camera0 rover0) (on_board camera1 rover0) (on_board camera2 rover1) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera1 low_res) (supports camera2 colour) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint0) (visible_from objective0 waypoint1) (visible_from objective1 waypoint2))\n    (:goal (and (communicated_soil_data waypoint1) (communicated_rock_data waypoint1) (communicated_image_data objective0 low_res) (communicated_image_data objective1 colour)))\n)"}
{"id": 7904832910131460516, "group": "reachable_atom_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. \nThere are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  \nRover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera2 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Camera camera0 supports colour. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  \nCurrently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint1; Soil data was communicated from waypoint waypoint1; Image objective0 was communicated in mode low_res. Rover rover1 has soil analyzed in waypoint waypoint1. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective0 in mode low_res. Rover rover1 has image objective1 in mode colour. Store(s) store0 is empty. Store(s) store1 is full.  The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at_rock_sample waypoint2", "have_image rover1 objective0 low_res", "calibrated camera1 rover1", "have_rock_analysis rover1 waypoint2", "have_rock_analysis rover1 waypoint0", "have_image rover0 objective0 high_res", "calibrated camera2 rover0", "have_soil_analysis rover1 waypoint0", "communicated_soil_data waypoint2", "have_rock_analysis rover0 waypoint2", "have_rock_analysis rover0 waypoint0", "communicated_rock_data waypoint2", "have_soil_analysis rover0 waypoint0", "at_soil_sample waypoint0", "at_soil_sample waypoint2", "have_image rover1 objective1 low_res", "have_soil_analysis rover0 waypoint2", "have_image rover0 objective1 high_res", "communicated_soil_data waypoint0", "at_rock_sample waypoint1", "have_soil_analysis rover1 waypoint2", "communicated_rock_data waypoint0", "calibrated camera0 rover1", "at_rock_sample waypoint0", "have_rock_analysis rover0 waypoint1", "at_soil_sample waypoint1"], "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-2-2-3-2021)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint0) (at rover1 waypoint2) (at_lander general waypoint1) (available rover0) (available rover1) (calibration_target camera0 objective1) (calibration_target camera1 objective0) (calibration_target camera2 objective0) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint0 waypoint2) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint2 waypoint0) (can_traverse rover1 waypoint0 waypoint2) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint0) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (communicated_image_data objective0 low_res) (communicated_rock_data waypoint1) (communicated_soil_data waypoint1) (empty store0) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover1) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (full store1) (have_image rover0 objective0 low_res) (have_image rover1 objective1 colour) (have_rock_analysis rover1 waypoint1) (have_soil_analysis rover1 waypoint1) (on_board camera0 rover0) (on_board camera1 rover0) (on_board camera2 rover1) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera1 low_res) (supports camera2 colour) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint0) (visible_from objective0 waypoint1) (visible_from objective1 waypoint2))\n    (:goal (and (communicated_soil_data waypoint1) (communicated_rock_data waypoint1) (communicated_image_data objective0 low_res) (communicated_image_data objective1 colour)))\n)"}
{"id": 292871256448321981, "group": "reachable_atom_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. \nThere are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  \nRover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 and camera1 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective2. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera2 supports high_res. Camera camera1 supports high_res. Camera camera0 supports low_res and colour. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint1 to waypoint2, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective0 is visible from waypoint2. Objective objective1 is visible from waypoint0 and waypoint1. Objective objective2 is visible from waypoint0. Lander general is at waypoint waypoint1.  \nCurrently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint1. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Rover rover0 has rock analyzed in waypoint waypoint0. Rover rover1 has its camera camera1 calibrated. Rover rover1 has its camera camera0 calibrated. Store(s) store1 is empty. Store(s) store0 is full.  The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at_rock_sample waypoint2", "have_image rover0 objective2 colour", "have_rock_analysis rover1 waypoint2", "have_rock_analysis rover1 waypoint0", "have_image rover0 objective1 colour", "have_image rover0 objective2 low_res", "have_soil_analysis rover0 waypoint1", "communicated_soil_data waypoint2", "have_image rover0 objective0 colour", "have_rock_analysis rover0 waypoint2", "have_soil_analysis rover1 waypoint1", "communicated_rock_data waypoint2", "have_rock_analysis rover1 waypoint1", "communicated_soil_data waypoint1", "at_soil_sample waypoint2", "calibrated camera2 rover1", "have_soil_analysis rover0 waypoint2", "at_rock_sample waypoint1", "have_soil_analysis rover1 waypoint2", "have_image rover0 objective0 low_res", "communicated_rock_data waypoint1", "at_rock_sample waypoint0", "calibrated camera0 rover0", "have_rock_analysis rover0 waypoint1", "at_soil_sample waypoint1", "calibrated camera1 rover0", "have_image rover0 objective1 low_res"], "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-3-2-3-2020)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 objective2 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint0) (at rover1 waypoint1) (at_lander general waypoint1) (at_soil_sample waypoint0) (available rover0) (available rover1) (calibrated camera0 rover1) (calibrated camera1 rover1) (calibration_target camera0 objective0) (calibration_target camera1 objective0) (calibration_target camera2 objective2) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint1 waypoint2) (can_traverse rover0 waypoint2 waypoint1) (can_traverse rover1 waypoint0 waypoint1) (can_traverse rover1 waypoint1 waypoint0) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (communicated_rock_data waypoint0) (empty store1) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover0) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (full store0) (have_rock_analysis rover0 waypoint0) (on_board camera0 rover1) (on_board camera1 rover1) (on_board camera2 rover0) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera0 low_res) (supports camera1 high_res) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint2) (visible_from objective1 waypoint0) (visible_from objective1 waypoint1) (visible_from objective2 waypoint0))\n    (:goal (and (communicated_soil_data waypoint0) (communicated_rock_data waypoint0) (communicated_image_data objective1 high_res) (communicated_image_data objective1 low_res)))\n)"}
{"id": -4482287115605685338, "group": "reachable_atom_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. \nThere are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  \nRover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 and camera1 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective2. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera2 supports high_res. Camera camera1 supports high_res. Camera camera0 supports low_res and colour. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint1 to waypoint2, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective0 is visible from waypoint2. Objective objective1 is visible from waypoint0 and waypoint1. Objective objective2 is visible from waypoint0. Lander general is at waypoint waypoint1.  \nCurrently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Image objective1 was communicated in mode low_res. Rover rover0 has rock analyzed in waypoint waypoint0. Rover rover1 has image objective1 in mode low_res. Rover rover0 has its camera camera2 calibrated. Store(s) store1 is empty. Store(s) store0 is full.  The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at_rock_sample waypoint2", "have_image rover0 objective2 colour", "have_rock_analysis rover1 waypoint2", "have_rock_analysis rover1 waypoint0", "have_image rover0 objective1 colour", "have_image rover0 objective2 low_res", "have_soil_analysis rover0 waypoint1", "communicated_soil_data waypoint2", "have_image rover0 objective0 colour", "have_rock_analysis rover0 waypoint2", "have_soil_analysis rover1 waypoint1", "communicated_rock_data waypoint2", "have_rock_analysis rover1 waypoint1", "communicated_soil_data waypoint1", "at_soil_sample waypoint2", "calibrated camera2 rover1", "have_soil_analysis rover0 waypoint2", "at_rock_sample waypoint1", "have_soil_analysis rover1 waypoint2", "have_image rover0 objective0 low_res", "communicated_rock_data waypoint1", "at_rock_sample waypoint0", "calibrated camera0 rover0", "have_rock_analysis rover0 waypoint1", "at_soil_sample waypoint1", "calibrated camera1 rover0", "have_image rover0 objective1 low_res"], "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-3-2-3-2020)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 objective2 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint0) (at rover1 waypoint0) (at_lander general waypoint1) (at_soil_sample waypoint0) (available rover0) (available rover1) (calibrated camera2 rover0) (calibration_target camera0 objective0) (calibration_target camera1 objective0) (calibration_target camera2 objective2) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint1 waypoint2) (can_traverse rover0 waypoint2 waypoint1) (can_traverse rover1 waypoint0 waypoint1) (can_traverse rover1 waypoint1 waypoint0) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (communicated_image_data objective1 low_res) (empty store1) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover0) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (full store0) (have_image rover1 objective1 low_res) (have_rock_analysis rover0 waypoint0) (on_board camera0 rover1) (on_board camera1 rover1) (on_board camera2 rover0) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera0 low_res) (supports camera1 high_res) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint2) (visible_from objective1 waypoint0) (visible_from objective1 waypoint1) (visible_from objective2 waypoint0))\n    (:goal (and (communicated_soil_data waypoint0) (communicated_rock_data waypoint0) (communicated_image_data objective1 high_res) (communicated_image_data objective1 low_res)))\n)"}
{"id": -4494301231534346942, "group": "reachable_atom_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. \nThere are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  \nRover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 and camera1 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective2. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera2 supports high_res. Camera camera1 supports high_res. Camera camera0 supports low_res and colour. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint1 to waypoint2, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Objective objective0 is visible from waypoint2. Objective objective1 is visible from waypoint0 and waypoint1. Objective objective2 is visible from waypoint0. Lander general is at waypoint waypoint1.  \nCurrently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Rover rover0 has rock analyzed in waypoint waypoint0. Rover rover1 has image objective0 in mode low_res. Rover rover1 has image objective0 in mode colour. Rover rover1 has its camera camera1 calibrated. Store(s) store1 is empty. Store(s) store0 is full.  The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "What proposition can never hold in any potentially reachable state?", "answer": ["at_rock_sample waypoint2", "have_image rover0 objective2 colour", "have_rock_analysis rover1 waypoint2", "have_rock_analysis rover1 waypoint0", "have_image rover0 objective1 colour", "have_image rover0 objective2 low_res", "have_soil_analysis rover0 waypoint1", "communicated_soil_data waypoint2", "have_image rover0 objective0 colour", "have_rock_analysis rover0 waypoint2", "have_soil_analysis rover1 waypoint1", "communicated_rock_data waypoint2", "have_rock_analysis rover1 waypoint1", "communicated_soil_data waypoint1", "at_soil_sample waypoint2", "calibrated camera2 rover1", "have_soil_analysis rover0 waypoint2", "at_rock_sample waypoint1", "have_soil_analysis rover1 waypoint2", "have_image rover0 objective0 low_res", "communicated_rock_data waypoint1", "at_rock_sample waypoint0", "calibrated camera0 rover0", "have_rock_analysis rover0 waypoint1", "at_soil_sample waypoint1", "calibrated camera1 rover0", "have_image rover0 objective1 low_res"], "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-3-2-3-2020)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 objective2 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint0) (at rover1 waypoint2) (at_lander general waypoint1) (at_soil_sample waypoint0) (available rover0) (available rover1) (calibrated camera1 rover1) (calibration_target camera0 objective0) (calibration_target camera1 objective0) (calibration_target camera2 objective2) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint1 waypoint2) (can_traverse rover0 waypoint2 waypoint1) (can_traverse rover1 waypoint0 waypoint1) (can_traverse rover1 waypoint1 waypoint0) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (communicated_rock_data waypoint0) (empty store1) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover0) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (full store0) (have_image rover1 objective0 colour) (have_image rover1 objective0 low_res) (have_rock_analysis rover0 waypoint0) (on_board camera0 rover1) (on_board camera1 rover1) (on_board camera2 rover0) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera0 low_res) (supports camera1 high_res) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint2) (visible_from objective1 waypoint0) (visible_from objective1 waypoint1) (visible_from objective2 waypoint0))\n    (:goal (and (communicated_soil_data waypoint0) (communicated_rock_data waypoint0) (communicated_image_data objective1 high_res) (communicated_image_data objective1 low_res)))\n)"}
