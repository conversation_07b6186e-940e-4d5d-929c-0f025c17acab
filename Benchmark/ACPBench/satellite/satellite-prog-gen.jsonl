{"id": 4614260838945328948, "group": "progression_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, planet6, groundstation3, groundstation1, star2, planet5, star4. There are 3 image mode(s): image1, infrared2, thermograph0. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode image1 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation3. Following instruments are powered on: instrument0. Following instruments are calibrated: instrument0. A infrared2 mode image of target planet6 is available. A image1 mode image of target star2 is available. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Break down the outcomes of performing the action \"turn the satellite satellite0 from direction groundstation3 to direction star4\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(pointing satellite0 groundstation3)"], "pos": ["(pointing satellite0 star4)"]}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-1-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation1 groundstation3 planet5 planet6 star2 star4 - direction instrument0 - instrument image1 infrared2 thermograph0 - mode satellite0 - satellite)\n    (:init (calibrated instrument0) (calibration_target instrument0 star4) (have_image planet6 infrared2) (have_image star2 image1) (on_board instrument0 satellite0) (pointing satellite0 groundstation3) (power_on instrument0) (supports instrument0 image1) (supports instrument0 infrared2) (supports instrument0 thermograph0))\n    (:goal (and (have_image planet5 thermograph0) (have_image planet6 infrared2)))\n)"}
{"id": 8666405117640935817, "group": "progression_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, phenomenon6, groundstation3, star2, groundstation1, planet5, star0. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite1 is pointing to groundstation4. Satellite satellite2 is pointing to phenomenon6. Satellite satellite0 is pointing to groundstation4. Power is available on the following satellite(s): satellite0. Following instruments are powered on: instrument4, instrument3. Following instruments are calibrated: instrument4. A thermograph2 mode image of target planet5 is available. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Break down the outcomes of performing the action \"power on instrument instrument1 on the satellite satellite0\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(power_avail satellite0)"], "pos": ["(power_on instrument1)"]}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-3-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 phenomenon6 planet5 star0 star2 - direction instrument0 instrument1 instrument2 instrument3 instrument4 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 - satellite)\n    (:init (calibrated instrument4) (calibration_target instrument0 star0) (calibration_target instrument1 star2) (calibration_target instrument2 groundstation3) (calibration_target instrument3 groundstation3) (calibration_target instrument4 star2) (have_image planet5 thermograph2) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite2) (pointing satellite0 groundstation4) (pointing satellite1 groundstation4) (pointing satellite2 phenomenon6) (power_avail satellite0) (power_on instrument3) (power_on instrument4) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 spectrograph0) (supports instrument2 infrared1) (supports instrument2 spectrograph0) (supports instrument3 infrared1) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2))\n    (:goal (and (pointing satellite1 phenomenon6) (have_image planet5 thermograph2) (have_image phenomenon6 spectrograph0)))\n)"}
{"id": -1506707161266886196, "group": "progression_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, phenomenon6, groundstation3, star2, groundstation1, planet5, star0. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to planet5. Satellite satellite1 is pointing to planet5. Satellite satellite2 is pointing to star2. Power is available on the following satellite(s): satellite0, satellite1. Following instruments are powered on: instrument4. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Break down the outcomes of performing the action \"calibrate instrument instrument4 on the satellite satellite2 for direction star2\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": [], "pos": ["(calibrated instrument4)"]}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-3-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 phenomenon6 planet5 star0 star2 - direction instrument0 instrument1 instrument2 instrument3 instrument4 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 - satellite)\n    (:init (calibration_target instrument0 star0) (calibration_target instrument1 star2) (calibration_target instrument2 groundstation3) (calibration_target instrument3 groundstation3) (calibration_target instrument4 star2) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite2) (pointing satellite0 planet5) (pointing satellite1 planet5) (pointing satellite2 star2) (power_avail satellite0) (power_avail satellite1) (power_on instrument4) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 spectrograph0) (supports instrument2 infrared1) (supports instrument2 spectrograph0) (supports instrument3 infrared1) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2))\n    (:goal (and (pointing satellite1 phenomenon6) (have_image planet5 thermograph2) (have_image phenomenon6 spectrograph0)))\n)"}
{"id": 513029911651892882, "group": "progression_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, phenomenon6, groundstation3, star2, groundstation1, planet5, star0. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to groundstation1. Satellite satellite2 is pointing to star0. Satellite satellite1 is pointing to planet5. Power is available on the following satellite(s): satellite1. Following instruments are powered on: instrument2, instrument4. Following instruments are calibrated: instrument4. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Break down the outcomes of performing the action \"take an image of direction star0 in mode infrared1 using instrument instrument4 on board satellite satellite2\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": [], "pos": ["(have_image star0 infrared1)"]}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-3-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 phenomenon6 planet5 star0 star2 - direction instrument0 instrument1 instrument2 instrument3 instrument4 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 - satellite)\n    (:init (calibrated instrument4) (calibration_target instrument0 star0) (calibration_target instrument1 star2) (calibration_target instrument2 groundstation3) (calibration_target instrument3 groundstation3) (calibration_target instrument4 star2) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite2) (pointing satellite0 groundstation1) (pointing satellite1 planet5) (pointing satellite2 star0) (power_avail satellite1) (power_on instrument2) (power_on instrument4) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 spectrograph0) (supports instrument2 infrared1) (supports instrument2 spectrograph0) (supports instrument3 infrared1) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2))\n    (:goal (and (pointing satellite1 phenomenon6) (have_image planet5 thermograph2) (have_image phenomenon6 spectrograph0)))\n)"}
{"id": 1160073485677101626, "group": "progression_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation4, planet8, groundstation3, groundstation6, groundstation1, star2, planet7, star0, star9, groundstation5, star10. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 11 instrument(s), numbered consecutively.  Satellite satellite5 has following instruments onboard: instrument9, instrument10. Satellite satellite1 has following instruments onboard: instrument2. Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite4 has following instruments onboard: instrument8, instrument7. Satellite satellite2 has following instruments onboard: instrument3, instrument4, instrument5. Satellite satellite3 has following instruments onboard: instrument6. Instrument instrument2 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument6 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument9 supports image of mode thermograph2 and its calibration target is groundstation6. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3.  Currently, Satellite satellite3 is pointing to star9. Satellite satellite1 is pointing to star9. Satellite satellite2 is pointing to star0. Satellite satellite0 is pointing to star10. Satellite satellite5 is pointing to star2. Satellite satellite4 is pointing to star10. Power is available on the following satellite(s): satellite5, satellite4, satellite0, satellite1, satellite3. Following instruments are powered on: instrument4. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Break down the outcomes of performing the action \"turn on the instrument instrument2 on the satellite satellite1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(power_avail satellite1)"], "pos": ["(power_on instrument2)"]}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-6-3-3-7-4)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 groundstation5 groundstation6 planet7 planet8 star0 star10 star2 star9 - direction instrument0 instrument1 instrument10 instrument2 instrument3 instrument4 instrument5 instrument6 instrument7 instrument8 instrument9 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 satellite3 satellite4 satellite5 - satellite)\n    (:init (calibration_target instrument0 star0) (calibration_target instrument1 groundstation5) (calibration_target instrument10 groundstation4) (calibration_target instrument2 star2) (calibration_target instrument3 groundstation1) (calibration_target instrument3 groundstation3) (calibration_target instrument4 groundstation5) (calibration_target instrument5 star2) (calibration_target instrument6 groundstation4) (calibration_target instrument6 star0) (calibration_target instrument7 groundstation3) (calibration_target instrument8 groundstation3) (calibration_target instrument8 star2) (calibration_target instrument9 groundstation1) (calibration_target instrument9 groundstation6) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument10 satellite5) (on_board instrument2 satellite1) (on_board instrument3 satellite2) (on_board instrument4 satellite2) (on_board instrument5 satellite2) (on_board instrument6 satellite3) (on_board instrument7 satellite4) (on_board instrument8 satellite4) (on_board instrument9 satellite5) (pointing satellite0 star10) (pointing satellite1 star9) (pointing satellite2 star0) (pointing satellite3 star9) (pointing satellite4 star10) (pointing satellite5 star2) (power_avail satellite0) (power_avail satellite1) (power_avail satellite3) (power_avail satellite4) (power_avail satellite5) (power_on instrument4) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 infrared1) (supports instrument1 spectrograph0) (supports instrument1 thermograph2) (supports instrument10 thermograph2) (supports instrument2 infrared1) (supports instrument2 thermograph2) (supports instrument3 infrared1) (supports instrument3 spectrograph0) (supports instrument3 thermograph2) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2) (supports instrument5 infrared1) (supports instrument6 infrared1) (supports instrument6 spectrograph0) (supports instrument6 thermograph2) (supports instrument7 spectrograph0) (supports instrument8 spectrograph0) (supports instrument9 infrared1) (supports instrument9 spectrograph0) (supports instrument9 thermograph2))\n    (:goal (and (pointing satellite1 star9) (pointing satellite4 star10) (pointing satellite5 star2) (have_image planet7 thermograph2) (have_image planet8 infrared1) (have_image star9 infrared1) (have_image star10 infrared1)))\n)"}
{"id": -8061478107855688516, "group": "progression_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation4, planet8, groundstation3, groundstation6, groundstation1, star2, planet7, star0, star9, groundstation5, star10. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 11 instrument(s), numbered consecutively.  Satellite satellite5 has following instruments onboard: instrument9, instrument10. Satellite satellite1 has following instruments onboard: instrument2. Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite4 has following instruments onboard: instrument8, instrument7. Satellite satellite2 has following instruments onboard: instrument3, instrument4, instrument5. Satellite satellite3 has following instruments onboard: instrument6. Instrument instrument2 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument6 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument9 supports image of mode thermograph2 and its calibration target is groundstation6. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3.  Currently, Satellite satellite3 is pointing to star9. Satellite satellite1 is pointing to star9. Satellite satellite2 is pointing to star0. Satellite satellite0 is pointing to star10. Satellite satellite5 is pointing to star2. Satellite satellite4 is pointing to star10. Power is available on the following satellite(s): satellite5, satellite4, satellite0, satellite2, satellite3. Following instruments are powered on: instrument2. Following instruments are calibrated: instrument2. A infrared1 mode image of target star10 is available. A infrared1 mode image of target star9 is available. A thermograph2 mode image of target planet7 is available. A infrared1 mode image of target planet8 is available. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Break down the outcomes of performing the action \"turn on the instrument instrument9 on the satellite satellite5\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(power_avail satellite5)"], "pos": ["(power_on instrument9)"]}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-6-3-3-7-4)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 groundstation5 groundstation6 planet7 planet8 star0 star10 star2 star9 - direction instrument0 instrument1 instrument10 instrument2 instrument3 instrument4 instrument5 instrument6 instrument7 instrument8 instrument9 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 satellite3 satellite4 satellite5 - satellite)\n    (:init (calibrated instrument2) (calibration_target instrument0 star0) (calibration_target instrument1 groundstation5) (calibration_target instrument10 groundstation4) (calibration_target instrument2 star2) (calibration_target instrument3 groundstation1) (calibration_target instrument3 groundstation3) (calibration_target instrument4 groundstation5) (calibration_target instrument5 star2) (calibration_target instrument6 groundstation4) (calibration_target instrument6 star0) (calibration_target instrument7 groundstation3) (calibration_target instrument8 groundstation3) (calibration_target instrument8 star2) (calibration_target instrument9 groundstation1) (calibration_target instrument9 groundstation6) (have_image planet7 thermograph2) (have_image planet8 infrared1) (have_image star10 infrared1) (have_image star9 infrared1) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument10 satellite5) (on_board instrument2 satellite1) (on_board instrument3 satellite2) (on_board instrument4 satellite2) (on_board instrument5 satellite2) (on_board instrument6 satellite3) (on_board instrument7 satellite4) (on_board instrument8 satellite4) (on_board instrument9 satellite5) (pointing satellite0 star10) (pointing satellite1 star9) (pointing satellite2 star0) (pointing satellite3 star9) (pointing satellite4 star10) (pointing satellite5 star2) (power_avail satellite0) (power_avail satellite2) (power_avail satellite3) (power_avail satellite4) (power_avail satellite5) (power_on instrument2) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 infrared1) (supports instrument1 spectrograph0) (supports instrument1 thermograph2) (supports instrument10 thermograph2) (supports instrument2 infrared1) (supports instrument2 thermograph2) (supports instrument3 infrared1) (supports instrument3 spectrograph0) (supports instrument3 thermograph2) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2) (supports instrument5 infrared1) (supports instrument6 infrared1) (supports instrument6 spectrograph0) (supports instrument6 thermograph2) (supports instrument7 spectrograph0) (supports instrument8 spectrograph0) (supports instrument9 infrared1) (supports instrument9 spectrograph0) (supports instrument9 thermograph2))\n    (:goal (and (pointing satellite1 star9) (pointing satellite4 star10) (pointing satellite5 star2) (have_image planet7 thermograph2) (have_image planet8 infrared1) (have_image star9 infrared1) (have_image star10 infrared1)))\n)"}
{"id": -5868391275058816661, "group": "progression_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, phenomenon6, groundstation3, star2, groundstation1, planet5, star0. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to groundstation3. Satellite satellite1 is pointing to groundstation1. Satellite satellite2 is pointing to groundstation3. Power is available on the following satellite(s): satellite0, satellite1. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Break down the outcomes of performing the action \"turn off the instrument instrument4 which is on the satellite satellite2\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(power_on instrument4)"], "pos": ["(power_avail satellite2)"]}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-3-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 phenomenon6 planet5 star0 star2 - direction instrument0 instrument1 instrument2 instrument3 instrument4 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 - satellite)\n    (:init (calibrated instrument4) (calibration_target instrument0 star0) (calibration_target instrument1 star2) (calibration_target instrument2 groundstation3) (calibration_target instrument3 groundstation3) (calibration_target instrument4 star2) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite2) (pointing satellite0 groundstation3) (pointing satellite1 groundstation1) (pointing satellite2 groundstation3) (power_avail satellite0) (power_avail satellite1) (power_on instrument4) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 spectrograph0) (supports instrument2 infrared1) (supports instrument2 spectrograph0) (supports instrument3 infrared1) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2))\n    (:goal (and (pointing satellite1 phenomenon6) (have_image planet5 thermograph2) (have_image phenomenon6 spectrograph0)))\n)"}
{"id": 9117345481740947952, "group": "progression_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation4, planet8, groundstation3, groundstation6, groundstation1, star2, planet7, star0, star9, groundstation5, star10. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 11 instrument(s), numbered consecutively.  Satellite satellite5 has following instruments onboard: instrument9, instrument10. Satellite satellite1 has following instruments onboard: instrument2. Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite4 has following instruments onboard: instrument8, instrument7. Satellite satellite2 has following instruments onboard: instrument3, instrument4, instrument5. Satellite satellite3 has following instruments onboard: instrument6. Instrument instrument2 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument6 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument9 supports image of mode thermograph2 and its calibration target is groundstation6. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3.  Currently, Satellite satellite3 is pointing to star9. Satellite satellite2 is pointing to star0. Satellite satellite0 is pointing to star10. Satellite satellite5 is pointing to star2. Satellite satellite4 is pointing to star10. Satellite satellite1 is pointing to star10. Power is available on the following satellite(s): satellite5, satellite4, satellite0, satellite2, satellite3. Following instruments are powered on: instrument2. Following instruments are calibrated: instrument2. A thermograph2 mode image of target planet7 is available. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Break down the outcomes of performing the action \"power on instrument instrument6 on the satellite satellite3\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(power_avail satellite3)"], "pos": ["(power_on instrument6)"]}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-6-3-3-7-4)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 groundstation5 groundstation6 planet7 planet8 star0 star10 star2 star9 - direction instrument0 instrument1 instrument10 instrument2 instrument3 instrument4 instrument5 instrument6 instrument7 instrument8 instrument9 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 satellite3 satellite4 satellite5 - satellite)\n    (:init (calibrated instrument2) (calibration_target instrument0 star0) (calibration_target instrument1 groundstation5) (calibration_target instrument10 groundstation4) (calibration_target instrument2 star2) (calibration_target instrument3 groundstation1) (calibration_target instrument3 groundstation3) (calibration_target instrument4 groundstation5) (calibration_target instrument5 star2) (calibration_target instrument6 groundstation4) (calibration_target instrument6 star0) (calibration_target instrument7 groundstation3) (calibration_target instrument8 groundstation3) (calibration_target instrument8 star2) (calibration_target instrument9 groundstation1) (calibration_target instrument9 groundstation6) (have_image planet7 thermograph2) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument10 satellite5) (on_board instrument2 satellite1) (on_board instrument3 satellite2) (on_board instrument4 satellite2) (on_board instrument5 satellite2) (on_board instrument6 satellite3) (on_board instrument7 satellite4) (on_board instrument8 satellite4) (on_board instrument9 satellite5) (pointing satellite0 star10) (pointing satellite1 star10) (pointing satellite2 star0) (pointing satellite3 star9) (pointing satellite4 star10) (pointing satellite5 star2) (power_avail satellite0) (power_avail satellite2) (power_avail satellite3) (power_avail satellite4) (power_avail satellite5) (power_on instrument2) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 infrared1) (supports instrument1 spectrograph0) (supports instrument1 thermograph2) (supports instrument10 thermograph2) (supports instrument2 infrared1) (supports instrument2 thermograph2) (supports instrument3 infrared1) (supports instrument3 spectrograph0) (supports instrument3 thermograph2) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2) (supports instrument5 infrared1) (supports instrument6 infrared1) (supports instrument6 spectrograph0) (supports instrument6 thermograph2) (supports instrument7 spectrograph0) (supports instrument8 spectrograph0) (supports instrument9 infrared1) (supports instrument9 spectrograph0) (supports instrument9 thermograph2))\n    (:goal (and (pointing satellite1 star9) (pointing satellite4 star10) (pointing satellite5 star2) (have_image planet7 thermograph2) (have_image planet8 infrared1) (have_image star9 infrared1) (have_image star10 infrared1)))\n)"}
{"id": 4127293649978295362, "group": "progression_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, planet6, groundstation3, groundstation1, star2, planet5, star4. There are 3 image mode(s): image1, infrared2, thermograph0. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode image1 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to star4. Power is available on the following satellite(s): satellite0. Following instruments are calibrated: instrument0. A infrared2 mode image of target planet6 is available. A thermograph0 mode image of target planet5 is available. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Break down the outcomes of performing the action \"activate the instrument instrument0 on satellite satellite0\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(calibrated instrument0)", "(power_avail satellite0)"], "pos": ["(power_on instrument0)"]}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-1-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation1 groundstation3 planet5 planet6 star2 star4 - direction instrument0 - instrument image1 infrared2 thermograph0 - mode satellite0 - satellite)\n    (:init (calibrated instrument0) (calibration_target instrument0 star4) (have_image planet5 thermograph0) (have_image planet6 infrared2) (on_board instrument0 satellite0) (pointing satellite0 star4) (power_avail satellite0) (supports instrument0 image1) (supports instrument0 infrared2) (supports instrument0 thermograph0))\n    (:goal (and (have_image planet5 thermograph0) (have_image planet6 infrared2)))\n)"}
{"id": 1218019303879552541, "group": "progression_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, planet6, groundstation3, groundstation1, star2, planet5, star4. There are 3 image mode(s): image1, infrared2, thermograph0. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode image1 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. Following instruments are calibrated: instrument0. A infrared2 mode image of target planet6 is available. A thermograph0 mode image of target planet5 is available. The available propositions are: (pointing ?s ?d) - Satellite ?s is pointing to ?d, (power_avail ?s) - Power is available on the following satellite(s): ?s, (power_on ?i) - Following instruments are powered on: ?i, (calibrated ?i) - Following instruments are calibrated: ?i, and (have_image ?d ?m) - A ?m mode image of target ?d is available.", "question": "Break down the outcomes of performing the action \"activate the instrument instrument0 on satellite satellite0\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(calibrated instrument0)", "(power_avail satellite0)"], "pos": ["(power_on instrument0)"]}, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-1-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation1 groundstation3 planet5 planet6 star2 star4 - direction instrument0 - instrument image1 infrared2 thermograph0 - mode satellite0 - satellite)\n    (:init (calibrated instrument0) (calibration_target instrument0 star4) (have_image planet5 thermograph0) (have_image planet6 infrared2) (on_board instrument0 satellite0) (pointing satellite0 groundstation1) (power_avail satellite0) (supports instrument0 image1) (supports instrument0 infrared2) (supports instrument0 thermograph0))\n    (:goal (and (have_image planet5 thermograph0) (have_image planet6 infrared2)))\n)"}
