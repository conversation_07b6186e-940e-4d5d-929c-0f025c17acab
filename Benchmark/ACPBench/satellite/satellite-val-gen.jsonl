{"id": 1969404516362856190, "group": "validation_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, planet5, groundstation0, groundstation1, groundstation3, planet6, star4. There are 3 image mode(s): infrared2, thermograph0, image1. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. The goal is to reach a state where the following facts hold: A thermograph0 mode image of target planet5 is available and A infrared2 mode image of target planet6 is available. The available actions are: (turn_to ?s ?d_new ?d_prev) - turn satellite ?s to point from ?d_prev direction to ?d_new, (switch_on ?i ?s) - turn on the instrument ?i on board the satellite ?s, (switch_off ?i ?s) - turn off the instrument ?i which is on the satellite ?s, (calibrate ?s ?i ?d) - calibrate instrument ?i on the satellite ?s for direction ?d, and (take_image ?s ?d ?i ?m) - capture a image of direction ?d in mode ?m using the instrument ?i on the satellite ?s.", "question": "What is the first inapplicable action in the next sequence of actions: \"(switch_on instrument0 satellite0) (turn_to satellite0 groundstation0 groundstation1) (turn_to satellite0 star4 groundstation0) (calibrate satellite0 instrument0 star4) (turn_to satellite0 planet5 star4) (take_image satellite0 groundstation3 instrument0 infrared2) (turn_to satellite0 planet6 planet5) (take_image satellite0 planet6 instrument0 infrared2)\"?", "answer": 5, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-1-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation1 groundstation3 planet5 planet6 star2 star4 - direction instrument0 - instrument image1 infrared2 thermograph0 - mode satellite0 - satellite)\n    (:init (calibration_target instrument0 star4) (on_board instrument0 satellite0) (pointing satellite0 groundstation1) (power_avail satellite0) (supports instrument0 image1) (supports instrument0 infrared2) (supports instrument0 thermograph0))\n    (:goal (and (have_image planet5 thermograph0) (have_image planet6 infrared2)))\n)"}
{"id": -5674870467855313723, "group": "validation_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, planet5, groundstation0, groundstation1, groundstation3, planet6, star4. There are 3 image mode(s): thermograph0, image1, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite3 has following instruments onboard: instrument5, instrument4. Satellite satellite5 has following instruments onboard: instrument8. Satellite satellite6 has following instruments onboard: instrument10, instrument9, instrument11. Satellite satellite4 has following instruments onboard: instrument6, instrument7. Satellite satellite8 has following instruments onboard: instrument13, instrument14. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite7 has following instruments onboard: instrument12. Instrument instrument12 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument7 supports image of mode infrared2 and its calibration target is star2. Instrument instrument8 supports image of mode image1 and its calibration target is star2. Instrument instrument4 supports image of mode image1 and its calibration target is groundstation1. Instrument instrument0 supports image of mode image1 and its calibration target is star4. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument10 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument2 supports image of mode infrared2 and its calibration target is star2. Instrument instrument6 supports image of mode infrared2 and its calibration target is groundstation0. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument14 supports image of mode image1 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to groundstation1. Satellite satellite6 is pointing to groundstation1. Satellite satellite2 is pointing to planet6. Satellite satellite4 is pointing to planet6. Satellite satellite1 is pointing to groundstation0. Satellite satellite9 is pointing to star4. Satellite satellite7 is pointing to star2. Satellite satellite5 is pointing to groundstation3. Satellite satellite8 is pointing to star2. Satellite satellite3 is pointing to groundstation1. Power is available on the following satellite(s): satellite9, satellite5, satellite7, satellite8, satellite0, satellite2, satellite1, satellite6, satellite4, satellite3. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available, A thermograph0 mode image of target planet5 is available, Satellite satellite8 is pointing to planet6, and Satellite satellite6 is pointing to star4. The available actions are: (turn_to ?s ?d_new ?d_prev) - turn the satellite ?s to the direction ?d_new from the direction ?d_prev, (switch_on ?i ?s) - activate the instrument ?i which is on the satellite ?s, (switch_off ?i ?s) - turn off the instrument ?i on board the satellite ?s, (calibrate ?s ?i ?d) - calibrate instrument ?i on the satellite ?s for direction ?d, and (take_image ?s ?d ?i ?m) - capture an image in direction ?d in mode ?m using the instrument ?i on the satellite ?s.", "question": "What is the first inapplicable action in the next sequence of actions: \"(turn_to satellite8 groundstation3 star2) (turn_to satellite8 planet6 groundstation3) (turn_to satellite6 star4 groundstation1) (take_image satellite7 star2 instrument12 image1) (calibrate satellite3 instrument5 groundstation1) (turn_to satellite3 planet5 groundstation1) (take_image satellite3 planet5 instrument5 thermograph0) (turn_to satellite3 planet6 planet5) (take_image satellite3 planet6 instrument5 infrared2)\"?", "answer": 3, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-10-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation1 groundstation3 planet5 planet6 star2 star4 - direction instrument0 instrument1 instrument10 instrument11 instrument12 instrument13 instrument14 instrument15 instrument2 instrument3 instrument4 instrument5 instrument6 instrument7 instrument8 instrument9 - instrument image1 infrared2 thermograph0 - mode satellite0 satellite1 satellite2 satellite3 satellite4 satellite5 satellite6 satellite7 satellite8 satellite9 - satellite)\n    (:init (calibration_target instrument0 star4) (calibration_target instrument1 groundstation3) (calibration_target instrument10 groundstation1) (calibration_target instrument11 groundstation3) (calibration_target instrument12 groundstation3) (calibration_target instrument13 star4) (calibration_target instrument14 star2) (calibration_target instrument15 groundstation3) (calibration_target instrument2 star2) (calibration_target instrument3 groundstation0) (calibration_target instrument4 groundstation1) (calibration_target instrument5 groundstation1) (calibration_target instrument6 groundstation0) (calibration_target instrument7 star2) (calibration_target instrument8 star2) (calibration_target instrument9 star4) (on_board instrument0 satellite0) (on_board instrument1 satellite1) (on_board instrument10 satellite6) (on_board instrument11 satellite6) (on_board instrument12 satellite7) (on_board instrument13 satellite8) (on_board instrument14 satellite8) (on_board instrument15 satellite9) (on_board instrument2 satellite1) (on_board instrument3 satellite2) (on_board instrument4 satellite3) (on_board instrument5 satellite3) (on_board instrument6 satellite4) (on_board instrument7 satellite4) (on_board instrument8 satellite5) (on_board instrument9 satellite6) (pointing satellite0 groundstation1) (pointing satellite1 groundstation0) (pointing satellite2 planet6) (pointing satellite3 groundstation1) (pointing satellite4 planet6) (pointing satellite5 groundstation3) (pointing satellite6 groundstation1) (pointing satellite7 star2) (pointing satellite8 star2) (pointing satellite9 star4) (power_avail satellite0) (power_avail satellite1) (power_avail satellite2) (power_avail satellite3) (power_avail satellite4) (power_avail satellite5) (power_avail satellite6) (power_avail satellite7) (power_avail satellite8) (power_avail satellite9) (supports instrument0 image1) (supports instrument0 thermograph0) (supports instrument1 thermograph0) (supports instrument10 infrared2) (supports instrument10 thermograph0) (supports instrument11 infrared2) (supports instrument12 image1) (supports instrument12 infrared2) (supports instrument12 thermograph0) (supports instrument13 thermograph0) (supports instrument14 image1) (supports instrument15 image1) (supports instrument2 image1) (supports instrument2 infrared2) (supports instrument2 thermograph0) (supports instrument3 image1) (supports instrument4 image1) (supports instrument4 infrared2) (supports instrument4 thermograph0) (supports instrument5 infrared2) (supports instrument5 thermograph0) (supports instrument6 image1) (supports instrument6 infrared2) (supports instrument6 thermograph0) (supports instrument7 image1) (supports instrument7 infrared2) (supports instrument7 thermograph0) (supports instrument8 image1) (supports instrument8 thermograph0) (supports instrument9 thermograph0))\n    (:goal (and (pointing satellite6 star4) (pointing satellite8 planet6) (have_image planet5 thermograph0) (have_image planet6 infrared2)))\n)"}
{"id": 2292766723748993916, "group": "validation_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, star6, groundstation0, star1, star3, phenomenon5, groundstation2. There are 3 image mode(s): image1, image2, image0. There are 18 instrument(s), numbered consecutively.  Satellite satellite5 has following instruments onboard: instrument12, instrument13, instrument14. Satellite satellite4 has following instruments onboard: instrument9, instrument11, instrument10. Satellite satellite0 has following instruments onboard: instrument2, instrument1, instrument0. Satellite satellite1 has following instruments onboard: instrument4, instrument3, instrument5. Satellite satellite6 has following instruments onboard: instrument17, instrument16, instrument15. Satellite satellite2 has following instruments onboard: instrument6. Satellite satellite3 has following instruments onboard: instrument8, instrument7. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument5 supports image of mode image2 and its calibration target is star3. Instrument instrument7 supports image of mode image1 and its calibration target is star3. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument10 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image2 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument2 supports image of mode image2 and its calibration target is star1. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument16 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument14 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument9 supports image of mode image1 and its calibration target is star3.  Currently, Satellite satellite2 is pointing to groundstation2. Satellite satellite6 is pointing to groundstation0. Satellite satellite1 is pointing to phenomenon5. Satellite satellite3 is pointing to star6. Satellite satellite5 is pointing to phenomenon5. Satellite satellite4 is pointing to star1. Satellite satellite0 is pointing to groundstation2. Power is available on the following satellite(s): satellite0, satellite2, satellite1, satellite6, satellite4, satellite5, satellite3. The goal is to reach a state where the following facts hold: Satellite satellite5 is pointing to groundstation0, A image1 mode image of target star6 is available, Satellite satellite1 is pointing to phenomenon5, A image0 mode image of target phenomenon5 is available, and Satellite satellite4 is pointing to star1. The available actions are: (turn_to ?s ?d_new ?d_prev) - point the satellite ?s to direction ?d_new instead of ?d_prev, (switch_on ?i ?s) - turn on the instrument ?i on the satellite ?s, (switch_off ?i ?s) - turn off the instrument ?i on board the satellite ?s, (calibrate ?s ?i ?d) - calibrate instrument ?i on the satellite ?s for direction ?d, and (take_image ?s ?d ?i ?m) - take an image of direction ?d in mode ?m using instrument ?i on board satellite ?s.", "question": "What is the first inapplicable action in the next sequence of actions: \"(turn_to satellite5 star1 phenomenon5) (turn_to satellite5 groundstation0 star1) (switch_on instrument6 satellite2) (take_image satellite6 groundstation0 instrument15 image1) (turn_to satellite2 star6 groundstation2) (take_image satellite2 star6 instrument6 image1) (turn_to satellite2 phenomenon5 star6) (take_image satellite2 phenomenon5 instrument6 image0)\"?", "answer": 3, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-7-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation2 groundstation4 phenomenon5 star1 star3 star6 - direction instrument0 instrument1 instrument10 instrument11 instrument12 instrument13 instrument14 instrument15 instrument16 instrument17 instrument2 instrument3 instrument4 instrument5 instrument6 instrument7 instrument8 instrument9 - instrument image0 image1 image2 - mode satellite0 satellite1 satellite2 satellite3 satellite4 satellite5 satellite6 - satellite)\n    (:init (calibration_target instrument0 groundstation0) (calibration_target instrument1 groundstation4) (calibration_target instrument10 groundstation4) (calibration_target instrument11 star3) (calibration_target instrument12 groundstation0) (calibration_target instrument13 groundstation0) (calibration_target instrument14 groundstation4) (calibration_target instrument15 groundstation2) (calibration_target instrument16 groundstation2) (calibration_target instrument17 groundstation0) (calibration_target instrument2 star1) (calibration_target instrument3 star3) (calibration_target instrument4 groundstation0) (calibration_target instrument5 star3) (calibration_target instrument6 groundstation2) (calibration_target instrument7 star3) (calibration_target instrument8 groundstation0) (calibration_target instrument9 star3) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument10 satellite4) (on_board instrument11 satellite4) (on_board instrument12 satellite5) (on_board instrument13 satellite5) (on_board instrument14 satellite5) (on_board instrument15 satellite6) (on_board instrument16 satellite6) (on_board instrument17 satellite6) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite1) (on_board instrument5 satellite1) (on_board instrument6 satellite2) (on_board instrument7 satellite3) (on_board instrument8 satellite3) (on_board instrument9 satellite4) (pointing satellite0 groundstation2) (pointing satellite1 phenomenon5) (pointing satellite2 groundstation2) (pointing satellite3 star6) (pointing satellite4 star1) (pointing satellite5 phenomenon5) (pointing satellite6 groundstation0) (power_avail satellite0) (power_avail satellite1) (power_avail satellite2) (power_avail satellite3) (power_avail satellite4) (power_avail satellite5) (power_avail satellite6) (supports instrument0 image2) (supports instrument1 image0) (supports instrument10 image0) (supports instrument10 image2) (supports instrument11 image1) (supports instrument11 image2) (supports instrument12 image0) (supports instrument13 image0) (supports instrument13 image2) (supports instrument14 image0) (supports instrument14 image1) (supports instrument14 image2) (supports instrument15 image0) (supports instrument15 image1) (supports instrument15 image2) (supports instrument16 image0) (supports instrument16 image1) (supports instrument17 image1) (supports instrument2 image0) (supports instrument2 image1) (supports instrument2 image2) (supports instrument3 image0) (supports instrument3 image1) (supports instrument3 image2) (supports instrument4 image2) (supports instrument5 image0) (supports instrument5 image1) (supports instrument5 image2) (supports instrument6 image0) (supports instrument6 image1) (supports instrument6 image2) (supports instrument7 image0) (supports instrument7 image1) (supports instrument8 image0) (supports instrument8 image1) (supports instrument8 image2) (supports instrument9 image1))\n    (:goal (and (pointing satellite1 phenomenon5) (pointing satellite4 star1) (pointing satellite5 groundstation0) (have_image phenomenon5 image0) (have_image star6 image1)))\n)"}
{"id": 8989827149288367312, "group": "validation_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, planet5, groundstation0, groundstation1, groundstation3, planet6, star4. There are 3 image mode(s): thermograph0, image1, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite3 has following instruments onboard: instrument5, instrument4. Satellite satellite5 has following instruments onboard: instrument8. Satellite satellite6 has following instruments onboard: instrument10, instrument9, instrument11. Satellite satellite4 has following instruments onboard: instrument6, instrument7. Satellite satellite8 has following instruments onboard: instrument13, instrument14. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite7 has following instruments onboard: instrument12. Instrument instrument12 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument7 supports image of mode infrared2 and its calibration target is star2. Instrument instrument8 supports image of mode image1 and its calibration target is star2. Instrument instrument4 supports image of mode image1 and its calibration target is groundstation1. Instrument instrument0 supports image of mode image1 and its calibration target is star4. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument10 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument2 supports image of mode infrared2 and its calibration target is star2. Instrument instrument6 supports image of mode infrared2 and its calibration target is groundstation0. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument14 supports image of mode image1 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to groundstation1. Satellite satellite6 is pointing to groundstation1. Satellite satellite2 is pointing to planet6. Satellite satellite4 is pointing to planet6. Satellite satellite1 is pointing to groundstation0. Satellite satellite9 is pointing to star4. Satellite satellite7 is pointing to star2. Satellite satellite5 is pointing to groundstation3. Satellite satellite8 is pointing to star2. Satellite satellite3 is pointing to groundstation1. Power is available on the following satellite(s): satellite9, satellite5, satellite7, satellite8, satellite0, satellite2, satellite1, satellite6, satellite4, satellite3. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available, A thermograph0 mode image of target planet5 is available, Satellite satellite8 is pointing to planet6, and Satellite satellite6 is pointing to star4. The available actions are: (turn_to ?s ?d_new ?d_prev) - change the direction of the satellite ?s from ?d_prev to ?d_new, (switch_on ?i ?s) - power on instrument ?i on the satellite ?s, (switch_off ?i ?s) - power off the instrument ?i on the satellite ?s, (calibrate ?s ?i ?d) - adjust the instrument ?i on the satellite ?s for direction ?d, and (take_image ?s ?d ?i ?m) - capture an image in direction ?d in mode ?m using the instrument ?i on the satellite ?s.", "question": "What is the first inapplicable action in the next sequence of actions: \"(turn_to satellite8 groundstation3 star2) (turn_to satellite8 planet6 groundstation3) (turn_to satellite8 groundstation1 star4) (calibrate satellite6 instrument10 groundstation1) (turn_to satellite6 planet6 groundstation1) (take_image satellite6 planet6 instrument10 infrared2) (turn_to satellite6 planet5 planet6) (take_image satellite6 planet5 instrument10 thermograph0) (turn_to satellite6 star4 planet5)\"?", "answer": 2, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-10-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation1 groundstation3 planet5 planet6 star2 star4 - direction instrument0 instrument1 instrument10 instrument11 instrument12 instrument13 instrument14 instrument15 instrument2 instrument3 instrument4 instrument5 instrument6 instrument7 instrument8 instrument9 - instrument image1 infrared2 thermograph0 - mode satellite0 satellite1 satellite2 satellite3 satellite4 satellite5 satellite6 satellite7 satellite8 satellite9 - satellite)\n    (:init (calibration_target instrument0 star4) (calibration_target instrument1 groundstation3) (calibration_target instrument10 groundstation1) (calibration_target instrument11 groundstation3) (calibration_target instrument12 groundstation3) (calibration_target instrument13 star4) (calibration_target instrument14 star2) (calibration_target instrument15 groundstation3) (calibration_target instrument2 star2) (calibration_target instrument3 groundstation0) (calibration_target instrument4 groundstation1) (calibration_target instrument5 groundstation1) (calibration_target instrument6 groundstation0) (calibration_target instrument7 star2) (calibration_target instrument8 star2) (calibration_target instrument9 star4) (on_board instrument0 satellite0) (on_board instrument1 satellite1) (on_board instrument10 satellite6) (on_board instrument11 satellite6) (on_board instrument12 satellite7) (on_board instrument13 satellite8) (on_board instrument14 satellite8) (on_board instrument15 satellite9) (on_board instrument2 satellite1) (on_board instrument3 satellite2) (on_board instrument4 satellite3) (on_board instrument5 satellite3) (on_board instrument6 satellite4) (on_board instrument7 satellite4) (on_board instrument8 satellite5) (on_board instrument9 satellite6) (pointing satellite0 groundstation1) (pointing satellite1 groundstation0) (pointing satellite2 planet6) (pointing satellite3 groundstation1) (pointing satellite4 planet6) (pointing satellite5 groundstation3) (pointing satellite6 groundstation1) (pointing satellite7 star2) (pointing satellite8 star2) (pointing satellite9 star4) (power_avail satellite0) (power_avail satellite1) (power_avail satellite2) (power_avail satellite3) (power_avail satellite4) (power_avail satellite5) (power_avail satellite6) (power_avail satellite7) (power_avail satellite8) (power_avail satellite9) (supports instrument0 image1) (supports instrument0 thermograph0) (supports instrument1 thermograph0) (supports instrument10 infrared2) (supports instrument10 thermograph0) (supports instrument11 infrared2) (supports instrument12 image1) (supports instrument12 infrared2) (supports instrument12 thermograph0) (supports instrument13 thermograph0) (supports instrument14 image1) (supports instrument15 image1) (supports instrument2 image1) (supports instrument2 infrared2) (supports instrument2 thermograph0) (supports instrument3 image1) (supports instrument4 image1) (supports instrument4 infrared2) (supports instrument4 thermograph0) (supports instrument5 infrared2) (supports instrument5 thermograph0) (supports instrument6 image1) (supports instrument6 infrared2) (supports instrument6 thermograph0) (supports instrument7 image1) (supports instrument7 infrared2) (supports instrument7 thermograph0) (supports instrument8 image1) (supports instrument8 thermograph0) (supports instrument9 thermograph0))\n    (:goal (and (pointing satellite6 star4) (pointing satellite8 planet6) (have_image planet5 thermograph0) (have_image planet6 infrared2)))\n)"}
{"id": -8821360778055041846, "group": "validation_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, star2, planet5, groundstation1, groundstation3, star0, phenomenon6. There are 3 image mode(s): spectrograph0, thermograph2, infrared1. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite2 has following instruments onboard: instrument4. Satellite satellite1 has following instruments onboard: instrument3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument4 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument2 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3.  Currently, Satellite satellite2 is pointing to star2. Satellite satellite0 is pointing to groundstation4. Satellite satellite1 is pointing to groundstation3. Power is available on the following satellite(s): satellite0, satellite2, satellite1. The goal is to reach a state where the following facts hold: Satellite satellite1 is pointing to phenomenon6, A spectrograph0 mode image of target phenomenon6 is available, and A thermograph2 mode image of target planet5 is available. The available actions are: (turn_to ?s ?d_new ?d_prev) - direct the satellite ?s to point in the direction ?d_new instead of ?d_prev, (switch_on ?i ?s) - switch on instrument ?i on board satellite ?s, (switch_off ?i ?s) - turn off the instrument ?i on board the satellite ?s, (calibrate ?s ?i ?d) - adjust the instrument ?i on the satellite ?s for direction ?d, and (take_image ?s ?d ?i ?m) - capture a image of direction ?d in mode ?m using the instrument ?i on the satellite ?s.", "question": "What is the first inapplicable action in the next sequence of actions: \"(switch_on instrument4 satellite2) (turn_to satellite2 groundstation4 phenomenon6) (calibrate satellite2 instrument4 star2) (turn_to satellite2 phenomenon6 star2) (take_image satellite2 phenomenon6 instrument4 spectrograph0) (turn_to satellite2 star0 phenomenon6) (turn_to satellite2 planet5 star0) (take_image satellite2 planet5 instrument4 thermograph2)\"?", "answer": 1, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-3-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 phenomenon6 planet5 star0 star2 - direction instrument0 instrument1 instrument2 instrument3 instrument4 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 - satellite)\n    (:init (calibration_target instrument0 star0) (calibration_target instrument1 star2) (calibration_target instrument2 groundstation3) (calibration_target instrument3 groundstation3) (calibration_target instrument4 star2) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite2) (pointing satellite0 groundstation4) (pointing satellite1 groundstation3) (pointing satellite2 star2) (power_avail satellite0) (power_avail satellite1) (power_avail satellite2) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 spectrograph0) (supports instrument2 infrared1) (supports instrument2 spectrograph0) (supports instrument3 infrared1) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2))\n    (:goal (and (pointing satellite1 phenomenon6) (have_image planet5 thermograph2) (have_image phenomenon6 spectrograph0)))\n)"}
{"id": 4753950245381214650, "group": "validation_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation4, star2, groundstation6, groundstation5, star10, planet7, planet8, star9, groundstation1, groundstation3, star0. There are 3 image mode(s): spectrograph0, thermograph2, infrared1. There are 11 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument4, instrument3, instrument5. Satellite satellite5 has following instruments onboard: instrument9, instrument10. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite1 has following instruments onboard: instrument2. Satellite satellite3 has following instruments onboard: instrument6. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument0 supports image of mode thermograph2 and its calibration target is star0. Instrument instrument1 supports image of mode thermograph2 and its calibration target is groundstation5. Instrument instrument3 supports image of mode thermograph2 and its calibration target is groundstation1. Instrument instrument6 supports image of mode infrared1 and its calibration target is star0. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument9 supports image of mode infrared1 and its calibration target is groundstation6. Instrument instrument4 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4.  Currently, Satellite satellite0 is pointing to star10. Satellite satellite3 is pointing to star9. Satellite satellite5 is pointing to planet8. Satellite satellite1 is pointing to star0. Satellite satellite2 is pointing to star0. Satellite satellite4 is pointing to star0. Power is available on the following satellite(s): satellite0, satellite2, satellite1, satellite4, satellite5, satellite3. The goal is to reach a state where the following facts hold: Satellite satellite1 is pointing to star9, Satellite satellite4 is pointing to star10, A infrared1 mode image of target star10 is available, A infrared1 mode image of target planet8 is available, Satellite satellite5 is pointing to star2, A thermograph2 mode image of target planet7 is available, and A infrared1 mode image of target star9 is available. The available actions are: (turn_to ?s ?d_new ?d_prev) - turn satellite ?s from direction ?d_prev to direction ?d_new, (switch_on ?i ?s) - activate the instrument ?i which is on the satellite ?s, (switch_off ?i ?s) - switch off the instrument ?i on board the satellite ?s, (calibrate ?s ?i ?d) - adjust the direction for instrument ?i on the satellite ?s to ?d, and (take_image ?s ?d ?i ?m) - capture an image of direction ?d in mode ?m using the instrument ?i on satellite ?s.", "question": "What is the first inapplicable action in the next sequence of actions: \"(turn_to satellite5 star2 planet8) (turn_to satellite4 star10 star0) (turn_to satellite1 star2 star0) (take_image satellite2 star0 instrument3 infrared1) (calibrate satellite1 instrument2 star2) (turn_to satellite1 star10 star2) (take_image satellite1 star10 instrument2 infrared1) (turn_to satellite1 planet8 star10) (take_image satellite1 planet8 instrument2 infrared1) (turn_to satellite1 planet7 planet8) (take_image satellite1 planet7 instrument2 thermograph2) (turn_to satellite1 star9 planet7) (take_image satellite1 star9 instrument2 infrared1) (turn_to satellite3 groundstation6 star9)\"?", "answer": 3, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-6-3-3-7-4)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 groundstation5 groundstation6 planet7 planet8 star0 star10 star2 star9 - direction instrument0 instrument1 instrument10 instrument2 instrument3 instrument4 instrument5 instrument6 instrument7 instrument8 instrument9 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 satellite3 satellite4 satellite5 - satellite)\n    (:init (calibration_target instrument0 star0) (calibration_target instrument1 groundstation5) (calibration_target instrument10 groundstation4) (calibration_target instrument2 star2) (calibration_target instrument3 groundstation1) (calibration_target instrument3 groundstation3) (calibration_target instrument4 groundstation5) (calibration_target instrument5 star2) (calibration_target instrument6 groundstation4) (calibration_target instrument6 star0) (calibration_target instrument7 groundstation3) (calibration_target instrument8 groundstation3) (calibration_target instrument8 star2) (calibration_target instrument9 groundstation1) (calibration_target instrument9 groundstation6) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument10 satellite5) (on_board instrument2 satellite1) (on_board instrument3 satellite2) (on_board instrument4 satellite2) (on_board instrument5 satellite2) (on_board instrument6 satellite3) (on_board instrument7 satellite4) (on_board instrument8 satellite4) (on_board instrument9 satellite5) (pointing satellite0 star10) (pointing satellite1 star0) (pointing satellite2 star0) (pointing satellite3 star9) (pointing satellite4 star0) (pointing satellite5 planet8) (power_avail satellite0) (power_avail satellite1) (power_avail satellite2) (power_avail satellite3) (power_avail satellite4) (power_avail satellite5) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 infrared1) (supports instrument1 spectrograph0) (supports instrument1 thermograph2) (supports instrument10 thermograph2) (supports instrument2 infrared1) (supports instrument2 thermograph2) (supports instrument3 infrared1) (supports instrument3 spectrograph0) (supports instrument3 thermograph2) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2) (supports instrument5 infrared1) (supports instrument6 infrared1) (supports instrument6 spectrograph0) (supports instrument6 thermograph2) (supports instrument7 spectrograph0) (supports instrument8 spectrograph0) (supports instrument9 infrared1) (supports instrument9 spectrograph0) (supports instrument9 thermograph2))\n    (:goal (and (pointing satellite1 star9) (pointing satellite4 star10) (pointing satellite5 star2) (have_image planet7 thermograph2) (have_image planet8 infrared1) (have_image star9 infrared1) (have_image star10 infrared1)))\n)"}
{"id": -2422202180913625940, "group": "validation_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, planet5, groundstation0, groundstation1, groundstation3, planet6, star4. There are 3 image mode(s): thermograph0, image1, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite3 has following instruments onboard: instrument5, instrument4. Satellite satellite5 has following instruments onboard: instrument8. Satellite satellite6 has following instruments onboard: instrument10, instrument9, instrument11. Satellite satellite4 has following instruments onboard: instrument6, instrument7. Satellite satellite8 has following instruments onboard: instrument13, instrument14. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite7 has following instruments onboard: instrument12. Instrument instrument12 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument7 supports image of mode infrared2 and its calibration target is star2. Instrument instrument8 supports image of mode image1 and its calibration target is star2. Instrument instrument4 supports image of mode image1 and its calibration target is groundstation1. Instrument instrument0 supports image of mode image1 and its calibration target is star4. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument10 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument2 supports image of mode infrared2 and its calibration target is star2. Instrument instrument6 supports image of mode infrared2 and its calibration target is groundstation0. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument14 supports image of mode image1 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to groundstation1. Satellite satellite6 is pointing to groundstation1. Satellite satellite2 is pointing to planet6. Satellite satellite4 is pointing to planet6. Satellite satellite1 is pointing to groundstation0. Satellite satellite9 is pointing to star4. Satellite satellite7 is pointing to star2. Satellite satellite5 is pointing to groundstation3. Satellite satellite8 is pointing to star2. Satellite satellite3 is pointing to groundstation1. Power is available on the following satellite(s): satellite9, satellite5, satellite7, satellite8, satellite0, satellite2, satellite1, satellite6, satellite4, satellite3. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available, A thermograph0 mode image of target planet5 is available, Satellite satellite8 is pointing to planet6, and Satellite satellite6 is pointing to star4. The available actions are: (turn_to ?s ?d_new ?d_prev) - change the direction of the satellite ?s from ?d_prev to ?d_new, (switch_on ?i ?s) - activate the instrument ?i which is on the satellite ?s, (switch_off ?i ?s) - power off the instrument ?i on the satellite ?s, (calibrate ?s ?i ?d) - adjust the direction for instrument ?i on the satellite ?s to ?d, and (take_image ?s ?d ?i ?m) - capture a image of direction ?d in mode ?m using the instrument ?i on the satellite ?s.", "question": "What is the first inapplicable action in the next sequence of actions: \"(turn_to satellite9 groundstation3 planet6) (turn_to satellite6 star4 groundstation1) (switch_on instrument4 satellite3) (calibrate satellite3 instrument4 groundstation1) (turn_to satellite3 planet6 groundstation1) (take_image satellite3 planet6 instrument4 infrared2) (turn_to satellite3 planet5 planet6) (take_image satellite3 planet5 instrument4 thermograph0) (switch_on instrument11 satellite6)\"?", "answer": 0, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-10-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation1 groundstation3 planet5 planet6 star2 star4 - direction instrument0 instrument1 instrument10 instrument11 instrument12 instrument13 instrument14 instrument15 instrument2 instrument3 instrument4 instrument5 instrument6 instrument7 instrument8 instrument9 - instrument image1 infrared2 thermograph0 - mode satellite0 satellite1 satellite2 satellite3 satellite4 satellite5 satellite6 satellite7 satellite8 satellite9 - satellite)\n    (:init (calibration_target instrument0 star4) (calibration_target instrument1 groundstation3) (calibration_target instrument10 groundstation1) (calibration_target instrument11 groundstation3) (calibration_target instrument12 groundstation3) (calibration_target instrument13 star4) (calibration_target instrument14 star2) (calibration_target instrument15 groundstation3) (calibration_target instrument2 star2) (calibration_target instrument3 groundstation0) (calibration_target instrument4 groundstation1) (calibration_target instrument5 groundstation1) (calibration_target instrument6 groundstation0) (calibration_target instrument7 star2) (calibration_target instrument8 star2) (calibration_target instrument9 star4) (on_board instrument0 satellite0) (on_board instrument1 satellite1) (on_board instrument10 satellite6) (on_board instrument11 satellite6) (on_board instrument12 satellite7) (on_board instrument13 satellite8) (on_board instrument14 satellite8) (on_board instrument15 satellite9) (on_board instrument2 satellite1) (on_board instrument3 satellite2) (on_board instrument4 satellite3) (on_board instrument5 satellite3) (on_board instrument6 satellite4) (on_board instrument7 satellite4) (on_board instrument8 satellite5) (on_board instrument9 satellite6) (pointing satellite0 groundstation1) (pointing satellite1 groundstation0) (pointing satellite2 planet6) (pointing satellite3 groundstation1) (pointing satellite4 planet6) (pointing satellite5 groundstation3) (pointing satellite6 groundstation1) (pointing satellite7 star2) (pointing satellite8 star2) (pointing satellite9 star4) (power_avail satellite0) (power_avail satellite1) (power_avail satellite2) (power_avail satellite3) (power_avail satellite4) (power_avail satellite5) (power_avail satellite6) (power_avail satellite7) (power_avail satellite8) (power_avail satellite9) (supports instrument0 image1) (supports instrument0 thermograph0) (supports instrument1 thermograph0) (supports instrument10 infrared2) (supports instrument10 thermograph0) (supports instrument11 infrared2) (supports instrument12 image1) (supports instrument12 infrared2) (supports instrument12 thermograph0) (supports instrument13 thermograph0) (supports instrument14 image1) (supports instrument15 image1) (supports instrument2 image1) (supports instrument2 infrared2) (supports instrument2 thermograph0) (supports instrument3 image1) (supports instrument4 image1) (supports instrument4 infrared2) (supports instrument4 thermograph0) (supports instrument5 infrared2) (supports instrument5 thermograph0) (supports instrument6 image1) (supports instrument6 infrared2) (supports instrument6 thermograph0) (supports instrument7 image1) (supports instrument7 infrared2) (supports instrument7 thermograph0) (supports instrument8 image1) (supports instrument8 thermograph0) (supports instrument9 thermograph0))\n    (:goal (and (pointing satellite6 star4) (pointing satellite8 planet6) (have_image planet5 thermograph0) (have_image planet6 infrared2)))\n)"}
{"id": -7249498608050425912, "group": "validation_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, star2, planet5, groundstation1, groundstation3, star0, phenomenon6. There are 3 image mode(s): spectrograph0, thermograph2, infrared1. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite2 has following instruments onboard: instrument4. Satellite satellite1 has following instruments onboard: instrument3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument4 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument2 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3.  Currently, Satellite satellite2 is pointing to star2. Satellite satellite0 is pointing to groundstation4. Satellite satellite1 is pointing to groundstation3. Power is available on the following satellite(s): satellite0, satellite2, satellite1. The goal is to reach a state where the following facts hold: Satellite satellite1 is pointing to phenomenon6, A spectrograph0 mode image of target phenomenon6 is available, and A thermograph2 mode image of target planet5 is available. The available actions are: (turn_to ?s ?d_new ?d_prev) - point the satellite ?s from ?d_prev direction to new direction ?d_new, (switch_on ?i ?s) - switch on instrument ?i on the satellite ?s, (switch_off ?i ?s) - switch off instrument ?i on board satellite ?s, (calibrate ?s ?i ?d) - adjust the direction for instrument ?i on the satellite ?s to ?d, and (take_image ?s ?d ?i ?m) - capture an image of direction ?d in mode ?m using the instrument ?i on satellite ?s.", "question": "What is the first inapplicable action in the next sequence of actions: \"(take_image satellite0 star2 instrument0 infrared1) (calibrate satellite2 instrument4 star2) (turn_to satellite2 star0 star2) (turn_to satellite2 planet5 star0) (take_image satellite2 planet5 instrument4 thermograph2) (turn_to satellite2 phenomenon6 planet5) (take_image satellite2 phenomenon6 instrument4 spectrograph0) (turn_to satellite1 phenomenon6 groundstation3)\"?", "answer": 0, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-3-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation1 groundstation3 groundstation4 phenomenon6 planet5 star0 star2 - direction instrument0 instrument1 instrument2 instrument3 instrument4 - instrument infrared1 spectrograph0 thermograph2 - mode satellite0 satellite1 satellite2 - satellite)\n    (:init (calibration_target instrument0 star0) (calibration_target instrument1 star2) (calibration_target instrument2 groundstation3) (calibration_target instrument3 groundstation3) (calibration_target instrument4 star2) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite2) (pointing satellite0 groundstation4) (pointing satellite1 groundstation3) (pointing satellite2 star2) (power_avail satellite0) (power_avail satellite1) (power_avail satellite2) (supports instrument0 infrared1) (supports instrument0 spectrograph0) (supports instrument0 thermograph2) (supports instrument1 spectrograph0) (supports instrument2 infrared1) (supports instrument2 spectrograph0) (supports instrument3 infrared1) (supports instrument4 infrared1) (supports instrument4 spectrograph0) (supports instrument4 thermograph2))\n    (:goal (and (pointing satellite1 phenomenon6) (have_image planet5 thermograph2) (have_image phenomenon6 spectrograph0)))\n)"}
{"id": 856190670233917610, "group": "validation_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, planet5, groundstation0, groundstation1, groundstation3, planet6, star4. There are 3 image mode(s): infrared2, thermograph0, image1. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. The goal is to reach a state where the following facts hold: A thermograph0 mode image of target planet5 is available and A infrared2 mode image of target planet6 is available. The available actions are: (turn_to ?s ?d_new ?d_prev) - direct the satellite ?s to point in the direction ?d_new instead of ?d_prev, (switch_on ?i ?s) - activate the instrument ?i on satellite ?s, (switch_off ?i ?s) - power off the instrument ?i on the satellite ?s, (calibrate ?s ?i ?d) - adjust the instrument ?i on the satellite ?s for direction ?d, and (take_image ?s ?d ?i ?m) - capture a image of direction ?d in mode ?m using the instrument ?i on the satellite ?s.", "question": "What is the first inapplicable action in the next sequence of actions: \"(switch_on instrument0 satellite0) (turn_to satellite0 groundstation0 groundstation1) (turn_to satellite0 star4 groundstation0) (calibrate satellite0 instrument0 star4) (take_image satellite0 planet5 instrument0 thermograph0) (take_image satellite0 planet6 instrument0 infrared2) (turn_to satellite0 star2 planet6) (turn_to satellite0 planet5 star2) (take_image satellite0 planet5 instrument0 thermograph0)\"?", "answer": 4, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-1-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation1 groundstation3 planet5 planet6 star2 star4 - direction instrument0 - instrument image1 infrared2 thermograph0 - mode satellite0 - satellite)\n    (:init (calibration_target instrument0 star4) (on_board instrument0 satellite0) (pointing satellite0 groundstation1) (power_avail satellite0) (supports instrument0 image1) (supports instrument0 infrared2) (supports instrument0 thermograph0))\n    (:goal (and (have_image planet5 thermograph0) (have_image planet6 infrared2)))\n)"}
{"id": 4849029280410764721, "group": "validation_gen", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, star6, groundstation0, star1, star3, phenomenon5, groundstation2. There are 3 image mode(s): image1, image2, image0. There are 18 instrument(s), numbered consecutively.  Satellite satellite5 has following instruments onboard: instrument12, instrument13, instrument14. Satellite satellite4 has following instruments onboard: instrument9, instrument11, instrument10. Satellite satellite0 has following instruments onboard: instrument2, instrument1, instrument0. Satellite satellite1 has following instruments onboard: instrument4, instrument3, instrument5. Satellite satellite6 has following instruments onboard: instrument17, instrument16, instrument15. Satellite satellite2 has following instruments onboard: instrument6. Satellite satellite3 has following instruments onboard: instrument8, instrument7. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument5 supports image of mode image2 and its calibration target is star3. Instrument instrument7 supports image of mode image1 and its calibration target is star3. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument10 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image2 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument2 supports image of mode image2 and its calibration target is star1. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument16 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument14 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument9 supports image of mode image1 and its calibration target is star3.  Currently, Satellite satellite2 is pointing to groundstation2. Satellite satellite6 is pointing to groundstation0. Satellite satellite1 is pointing to phenomenon5. Satellite satellite3 is pointing to star6. Satellite satellite5 is pointing to phenomenon5. Satellite satellite4 is pointing to star1. Satellite satellite0 is pointing to groundstation2. Power is available on the following satellite(s): satellite0, satellite2, satellite1, satellite6, satellite4, satellite5, satellite3. The goal is to reach a state where the following facts hold: Satellite satellite5 is pointing to groundstation0, A image1 mode image of target star6 is available, Satellite satellite1 is pointing to phenomenon5, A image0 mode image of target phenomenon5 is available, and Satellite satellite4 is pointing to star1. The available actions are: (turn_to ?s ?d_new ?d_prev) - turn satellite ?s from direction ?d_prev to direction ?d_new, (switch_on ?i ?s) - switch on instrument ?i on board satellite ?s, (switch_off ?i ?s) - switch off instrument ?i on board satellite ?s, (calibrate ?s ?i ?d) - point the instrument ?i on the satellite ?s to direction ?d, and (take_image ?s ?d ?i ?m) - capture an image in direction ?d in mode ?m using the instrument ?i on the satellite ?s.", "question": "What is the first inapplicable action in the next sequence of actions: \"(turn_to satellite5 groundstation0 phenomenon5) (switch_on instrument6 satellite2) (calibrate satellite2 instrument6 groundstation2) (calibrate satellite2 instrument6 groundstation2) (turn_to satellite2 star6 groundstation2) (take_image satellite2 star6 instrument6 image1) (turn_to satellite2 phenomenon5 star6) (take_image satellite0 star3 instrument2 image2)\"?", "answer": 7, "PDDL_domain": "(define (domain satellite)\n    (:requirements :strips :typing)\n    (:types direction instrument mode satellite)\n    (:predicates (calibrated ?i - instrument)  (calibration_target ?i - instrument ?d - direction)  (have_image ?d - direction ?m - mode)  (on_board ?i - instrument ?s - satellite)  (pointing ?s - satellite ?d - direction)  (power_avail ?s - satellite)  (power_on ?i - instrument)  (supports ?i - instrument ?m - mode))\n    (:action calibrate\n        :parameters (?s - satellite ?i - instrument ?d - direction)\n        :precondition (and (on_board ?i ?s) (calibration_target ?i ?d) (pointing ?s ?d) (power_on ?i))\n        :effect (calibrated ?i)\n    )\n     (:action switch_off\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_on ?i))\n        :effect (and (not (power_on ?i)) (power_avail ?s))\n    )\n     (:action switch_on\n        :parameters (?i - instrument ?s - satellite)\n        :precondition (and (on_board ?i ?s) (power_avail ?s))\n        :effect (and (power_on ?i) (not (calibrated ?i)) (not (power_avail ?s)))\n    )\n     (:action take_image\n        :parameters (?s - satellite ?d - direction ?i - instrument ?m - mode)\n        :precondition (and (calibrated ?i) (on_board ?i ?s) (supports ?i ?m) (power_on ?i) (pointing ?s ?d))\n        :effect (have_image ?d ?m)\n    )\n     (:action turn_to\n        :parameters (?s - satellite ?d_new - direction ?d_prev - direction)\n        :precondition (pointing ?s ?d_prev)\n        :effect (and (pointing ?s ?d_new) (not (pointing ?s ?d_prev)))\n    )\n)", "PDDL_problem": "(define (problem prob-7-3-3-5-2)\n    (:domain satellite)\n    (:requirements :strips :typing)\n    (:objects groundstation0 groundstation2 groundstation4 phenomenon5 star1 star3 star6 - direction instrument0 instrument1 instrument10 instrument11 instrument12 instrument13 instrument14 instrument15 instrument16 instrument17 instrument2 instrument3 instrument4 instrument5 instrument6 instrument7 instrument8 instrument9 - instrument image0 image1 image2 - mode satellite0 satellite1 satellite2 satellite3 satellite4 satellite5 satellite6 - satellite)\n    (:init (calibration_target instrument0 groundstation0) (calibration_target instrument1 groundstation4) (calibration_target instrument10 groundstation4) (calibration_target instrument11 star3) (calibration_target instrument12 groundstation0) (calibration_target instrument13 groundstation0) (calibration_target instrument14 groundstation4) (calibration_target instrument15 groundstation2) (calibration_target instrument16 groundstation2) (calibration_target instrument17 groundstation0) (calibration_target instrument2 star1) (calibration_target instrument3 star3) (calibration_target instrument4 groundstation0) (calibration_target instrument5 star3) (calibration_target instrument6 groundstation2) (calibration_target instrument7 star3) (calibration_target instrument8 groundstation0) (calibration_target instrument9 star3) (on_board instrument0 satellite0) (on_board instrument1 satellite0) (on_board instrument10 satellite4) (on_board instrument11 satellite4) (on_board instrument12 satellite5) (on_board instrument13 satellite5) (on_board instrument14 satellite5) (on_board instrument15 satellite6) (on_board instrument16 satellite6) (on_board instrument17 satellite6) (on_board instrument2 satellite0) (on_board instrument3 satellite1) (on_board instrument4 satellite1) (on_board instrument5 satellite1) (on_board instrument6 satellite2) (on_board instrument7 satellite3) (on_board instrument8 satellite3) (on_board instrument9 satellite4) (pointing satellite0 groundstation2) (pointing satellite1 phenomenon5) (pointing satellite2 groundstation2) (pointing satellite3 star6) (pointing satellite4 star1) (pointing satellite5 phenomenon5) (pointing satellite6 groundstation0) (power_avail satellite0) (power_avail satellite1) (power_avail satellite2) (power_avail satellite3) (power_avail satellite4) (power_avail satellite5) (power_avail satellite6) (supports instrument0 image2) (supports instrument1 image0) (supports instrument10 image0) (supports instrument10 image2) (supports instrument11 image1) (supports instrument11 image2) (supports instrument12 image0) (supports instrument13 image0) (supports instrument13 image2) (supports instrument14 image0) (supports instrument14 image1) (supports instrument14 image2) (supports instrument15 image0) (supports instrument15 image1) (supports instrument15 image2) (supports instrument16 image0) (supports instrument16 image1) (supports instrument17 image1) (supports instrument2 image0) (supports instrument2 image1) (supports instrument2 image2) (supports instrument3 image0) (supports instrument3 image1) (supports instrument3 image2) (supports instrument4 image2) (supports instrument5 image0) (supports instrument5 image1) (supports instrument5 image2) (supports instrument6 image0) (supports instrument6 image1) (supports instrument6 image2) (supports instrument7 image0) (supports instrument7 image1) (supports instrument8 image0) (supports instrument8 image1) (supports instrument8 image2) (supports instrument9 image1))\n    (:goal (and (pointing satellite1 phenomenon5) (pointing satellite4 star1) (pointing satellite5 groundstation0) (have_image phenomenon5 image0) (have_image star6 image1)))\n)"}
