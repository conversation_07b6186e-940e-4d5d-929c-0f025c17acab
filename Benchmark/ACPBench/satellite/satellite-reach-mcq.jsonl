{"id": 5232813661151894308, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation0, planet6, groundstation3, star4, planet5, star2. There are 3 image mode(s): thermograph0, image1, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite8 has following instruments onboard: instrument13, instrument14. Satellite satellite6 has following instruments onboard: instrument10, instrument9, instrument11. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite3 has following instruments onboard: instrument4, instrument5. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite5 has following instruments onboard: instrument8. Satellite satellite4 has following instruments onboard: instrument6, instrument7. Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument12 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument6 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument2 supports image of mode infrared2 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument7 supports image of mode infrared2 and its calibration target is star2. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument4 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument8 supports image of mode image1 and its calibration target is star2. Instrument instrument0 supports image of mode image1 and its calibration target is star4. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3.  Currently, Satellite satellite9 is pointing to groundstation1. Satellite satellite4 is pointing to star2. Satellite satellite7 is pointing to star2. Satellite satellite3 is pointing to planet6. Satellite satellite5 is pointing to groundstation3. Satellite satellite0 is pointing to groundstation1. Satellite satellite6 is pointing to groundstation1. Satellite satellite8 is pointing to planet6. Satellite satellite1 is pointing to groundstation0. Satellite satellite2 is pointing to planet6. Power is available on the following satellite(s): satellite7, satellite5, satellite0, satellite4, satellite6, satellite1, satellite3, satellite2, satellite9. Following instruments are powered on: instrument14. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite6 is pointing to groundstation3 and Satellite satellite6 is pointing to star2. B. Satellite satellite8 is pointing to star2 and Satellite satellite8 is pointing to planet6. C. Satellite satellite9 is pointing to star4 and Satellite satellite5 is pointing to planet5. D. Satellite satellite3 is pointing to groundstation0 and Satellite satellite3 is pointing to planet6.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite6 is pointing to groundstation3 and Satellite satellite6 is pointing to star2", "Satellite satellite8 is pointing to star2 and Satellite satellite8 is pointing to planet6", "Satellite satellite9 is pointing to star4 and Satellite satellite5 is pointing to planet5", "Satellite satellite3 is pointing to groundstation0 and Satellite satellite3 is pointing to planet6"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 6188646718296709750, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation3, planet5, star2, star0, phenomenon6, groundstation4. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument4 supports image of mode infrared1 and its calibration target is star2. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to groundstation1. Satellite satellite1 is pointing to groundstation4. Satellite satellite2 is pointing to groundstation1. Power is available on the following satellite(s): satellite1, satellite0. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A thermograph2 mode image of target planet5 is available. A infrared1 mode image of target phenomenon6 is available. A spectrograph0 mode image of target phenomenon6 is available. A thermograph2 mode image of target phenomenon6 is available. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite0 is pointing to star2 and Satellite satellite0 is pointing to planet5. B. Satellite satellite0 is pointing to phenomenon6 and Satellite satellite2 is pointing to star0. C. Satellite satellite0 is pointing to star2 and Satellite satellite0 is pointing to planet5. D. Satellite satellite0 is pointing to planet5 and Satellite satellite0 is pointing to groundstation1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite0 is pointing to star2 and Satellite satellite0 is pointing to planet5", "Satellite satellite0 is pointing to phenomenon6 and Satellite satellite2 is pointing to star0", "Satellite satellite0 is pointing to star2 and Satellite satellite0 is pointing to planet5", "Satellite satellite0 is pointing to planet5 and Satellite satellite0 is pointing to groundstation1"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -8745705955450891044, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation1, groundstation5, star10, groundstation3, planet7, star9, star2, planet8, groundstation6, star0, groundstation4. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite2 has following instruments onboard: instrument3, instrument4, instrument5. Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument9 supports image of mode spectrograph0 and its calibration target is groundstation1. Instrument instrument6 supports image of mode spectrograph0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3.  Currently, Satellite satellite1 is pointing to star0. Satellite satellite3 is pointing to groundstation3. Satellite satellite0 is pointing to star10. Satellite satellite5 is pointing to groundstation3. Satellite satellite2 is pointing to star9. Satellite satellite4 is pointing to star10. Power is available on the following satellite(s): satellite0, satellite3, satellite5, satellite4, satellite1. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A infrared1 mode image of target star10 is available. A infrared1 mode image of target planet8 is available. A spectrograph0 mode image of target star9 is available. A thermograph2 mode image of target planet7 is available. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite3 is pointing to star2 and Satellite satellite3 is pointing to groundstation3. B. Satellite satellite4 is pointing to groundstation3 and Satellite satellite4 is pointing to groundstation4. C. Satellite satellite0 is pointing to groundstation5. D. Satellite satellite1 is pointing to star2 and Satellite satellite1 is pointing to groundstation4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite3 is pointing to star2 and Satellite satellite3 is pointing to groundstation3", "Satellite satellite4 is pointing to groundstation3 and Satellite satellite4 is pointing to groundstation4", "Satellite satellite0 is pointing to groundstation5", "Satellite satellite1 is pointing to star2 and Satellite satellite1 is pointing to groundstation4"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -383194445830517676, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation3, planet5, star2, star0, phenomenon6, groundstation4. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument4 supports image of mode infrared1 and its calibration target is star2. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to star2. Satellite satellite1 is pointing to star2. Satellite satellite2 is pointing to planet5. Power is available on the following satellite(s): satellite1. Following instruments are powered on: instrument4, instrument0. Following instruments are calibrated: instrument4. A thermograph2 mode image of target planet5 is available. A spectrograph0 mode image of target phenomenon6 is available. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite2 is pointing to planet5 and Satellite satellite2 is pointing to star0. B. Satellite satellite2 is pointing to groundstation1 and Satellite satellite2 is pointing to groundstation3. C. Satellite satellite2 is pointing to groundstation1. D. Satellite satellite2 is pointing to star0 and Satellite satellite2 is pointing to phenomenon6.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite2 is pointing to planet5 and Satellite satellite2 is pointing to star0", "Satellite satellite2 is pointing to groundstation1 and Satellite satellite2 is pointing to groundstation3", "Satellite satellite2 is pointing to groundstation1", "Satellite satellite2 is pointing to star0 and Satellite satellite2 is pointing to phenomenon6"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 4369914393841354954, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation1, groundstation5, star10, groundstation3, planet7, star9, star2, planet8, groundstation6, star0, groundstation4. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite2 has following instruments onboard: instrument3, instrument4, instrument5. Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument9 supports image of mode spectrograph0 and its calibration target is groundstation1. Instrument instrument6 supports image of mode spectrograph0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3.  Currently, Satellite satellite1 is pointing to star9. Satellite satellite5 is pointing to star2. Satellite satellite2 is pointing to star0. Satellite satellite3 is pointing to planet8. Satellite satellite0 is pointing to star10. Satellite satellite4 is pointing to star10. Power is available on the following satellite(s): satellite2, satellite5, satellite4, satellite1, satellite0. Following instruments are powered on: instrument6. Following instruments are calibrated: instrument6. A infrared1 mode image of target star10 is available. A infrared1 mode image of target planet8 is available. A thermograph2 mode image of target planet7 is available. A infrared1 mode image of target star9 is available. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite1 is pointing to planet7 and Satellite satellite1 is pointing to star0. B. Satellite satellite3 is pointing to planet7 and Satellite satellite3 is pointing to groundstation5. C. Satellite satellite4 is pointing to groundstation1 and Satellite satellite4 is pointing to planet8. D. Satellite satellite3 is pointing to groundstation6 and Satellite satellite4 is pointing to groundstation3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite1 is pointing to planet7 and Satellite satellite1 is pointing to star0", "Satellite satellite3 is pointing to planet7 and Satellite satellite3 is pointing to groundstation5", "Satellite satellite4 is pointing to groundstation1 and Satellite satellite4 is pointing to planet8", "Satellite satellite3 is pointing to groundstation6 and Satellite satellite4 is pointing to groundstation3"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 4746969715210492395, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation1, groundstation5, star10, groundstation3, planet7, star9, star2, planet8, groundstation6, star0, groundstation4. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite2 has following instruments onboard: instrument3, instrument4, instrument5. Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument9 supports image of mode spectrograph0 and its calibration target is groundstation1. Instrument instrument6 supports image of mode spectrograph0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3.  Currently, Satellite satellite3 is pointing to star9. Satellite satellite5 is pointing to star2. Satellite satellite0 is pointing to star10. Satellite satellite4 is pointing to star10. Satellite satellite2 is pointing to star0. Satellite satellite1 is pointing to groundstation6. Power is available on the following satellite(s): satellite3, satellite2, satellite5, satellite0, satellite4. Following instruments are powered on: instrument2. Following instruments are calibrated: instrument2. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite5 is pointing to star0 and Satellite satellite0 is pointing to planet8. B. Satellite satellite5 is pointing to groundstation5 and Satellite satellite5 is pointing to groundstation6. C. Satellite satellite1 is pointing to groundstation5 and Satellite satellite1 is pointing to groundstation1. D. Satellite satellite1 is pointing to groundstation3 and Satellite satellite1 is pointing to planet7.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite5 is pointing to star0 and Satellite satellite0 is pointing to planet8", "Satellite satellite5 is pointing to groundstation5 and Satellite satellite5 is pointing to groundstation6", "Satellite satellite1 is pointing to groundstation5 and Satellite satellite1 is pointing to groundstation1", "Satellite satellite1 is pointing to groundstation3 and Satellite satellite1 is pointing to planet7"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -3580532247658025952, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, star1, star3, groundstation2, star6, phenomenon5, groundstation4. There are 3 image mode(s): image0, image2, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument6. Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite1 has following instruments onboard: instrument3, instrument5, instrument4. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite6 has following instruments onboard: instrument16, instrument17, instrument15. Satellite satellite4 has following instruments onboard: instrument10, instrument11, instrument9. Satellite satellite5 has following instruments onboard: instrument14, instrument12, instrument13. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument2 supports image of mode image1 and its calibration target is star1. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument6 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode image1 and its calibration target is star3. Instrument instrument5 supports image of mode image1 and its calibration target is star3. Instrument instrument8 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument14 supports image of mode image1 and its calibration target is groundstation4. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument16 supports image of mode image1 and its calibration target is groundstation2.  Currently, Satellite satellite5 is pointing to groundstation0. Satellite satellite0 is pointing to phenomenon5. Satellite satellite1 is pointing to phenomenon5. Satellite satellite2 is pointing to groundstation2. Satellite satellite6 is pointing to groundstation4. Satellite satellite3 is pointing to star6. Satellite satellite4 is pointing to star1. Power is available on the following satellite(s): satellite3, satellite2, satellite5, satellite6, satellite0, satellite4. Following instruments are powered on: instrument4. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite4 is pointing to star3 and Satellite satellite4 is pointing to groundstation4. B. Satellite satellite5 is pointing to groundstation4 and Following instruments are powered on: instrument12. C. Satellite satellite3 is pointing to star3 and Satellite satellite3 is pointing to star1. D. Satellite satellite1 is pointing to groundstation2 and Satellite satellite1 is pointing to star6.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite4 is pointing to star3 and Satellite satellite4 is pointing to groundstation4", "Satellite satellite5 is pointing to groundstation4 and Following instruments are powered on: instrument12", "Satellite satellite3 is pointing to star3 and Satellite satellite3 is pointing to star1", "Satellite satellite1 is pointing to groundstation2 and Satellite satellite1 is pointing to star6"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 3894537783739277292, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation0, planet6, groundstation3, star4, planet5, star2. There are 3 image mode(s): thermograph0, image1, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite8 has following instruments onboard: instrument13, instrument14. Satellite satellite6 has following instruments onboard: instrument10, instrument9, instrument11. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite3 has following instruments onboard: instrument4, instrument5. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite5 has following instruments onboard: instrument8. Satellite satellite4 has following instruments onboard: instrument6, instrument7. Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument12 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument6 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument2 supports image of mode infrared2 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument7 supports image of mode infrared2 and its calibration target is star2. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument4 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument8 supports image of mode image1 and its calibration target is star2. Instrument instrument0 supports image of mode image1 and its calibration target is star4. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3.  Currently, Satellite satellite9 is pointing to star4. Satellite satellite6 is pointing to planet6. Satellite satellite7 is pointing to star2. Satellite satellite3 is pointing to groundstation1. Satellite satellite0 is pointing to groundstation1. Satellite satellite8 is pointing to planet6. Satellite satellite1 is pointing to groundstation0. Satellite satellite5 is pointing to planet6. Satellite satellite4 is pointing to planet6. Satellite satellite2 is pointing to planet6. Power is available on the following satellite(s): satellite7, satellite5, satellite0, satellite4, satellite8, satellite1, satellite2, satellite9. Following instruments are powered on: instrument5, instrument10. Following instruments are calibrated: instrument10. A infrared2 mode image of target planet6 is available. A thermograph0 mode image of target planet5 is available. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite9 is pointing to groundstation3 and Satellite satellite9 is pointing to groundstation0. B. Satellite satellite0 is pointing to groundstation1 and Satellite satellite0 is pointing to planet5. C. Satellite satellite0 is pointing to star2 and Satellite satellite0 is pointing to planet5. D. Satellite satellite6 is pointing to groundstation1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite9 is pointing to groundstation3 and Satellite satellite9 is pointing to groundstation0", "Satellite satellite0 is pointing to groundstation1 and Satellite satellite0 is pointing to planet5", "Satellite satellite0 is pointing to star2 and Satellite satellite0 is pointing to planet5", "Satellite satellite6 is pointing to groundstation1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 7560355384301785610, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation1, groundstation5, star10, groundstation3, planet7, star9, star2, planet8, groundstation6, star0, groundstation4. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite2 has following instruments onboard: instrument3, instrument4, instrument5. Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument9 supports image of mode spectrograph0 and its calibration target is groundstation1. Instrument instrument6 supports image of mode spectrograph0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3.  Currently, Satellite satellite1 is pointing to star9. Satellite satellite3 is pointing to star9. Satellite satellite5 is pointing to star2. Satellite satellite2 is pointing to star0. Satellite satellite0 is pointing to star10. Satellite satellite4 is pointing to star10. Power is available on the following satellite(s): satellite3, satellite2, satellite5, satellite4, satellite0. Following instruments are powered on: instrument2. Following instruments are calibrated: instrument2. A infrared1 mode image of target star10 is available. A thermograph2 mode image of target planet7 is available. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite1 is pointing to groundstation3 and Satellite satellite1 is pointing to groundstation6. B. Satellite satellite2 is pointing to star0 and Satellite satellite2 is pointing to planet8. C. Satellite satellite5 is pointing to star0 and Satellite satellite5 is pointing to groundstation4. D. Power is available on the following satellite(s): satellite1 and Satellite satellite5 is pointing to star9.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite1 is pointing to groundstation3 and Satellite satellite1 is pointing to groundstation6", "Satellite satellite2 is pointing to star0 and Satellite satellite2 is pointing to planet8", "Satellite satellite5 is pointing to star0 and Satellite satellite5 is pointing to groundstation4", "Power is available on the following satellite(s): satellite1 and Satellite satellite5 is pointing to star9"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 2117244684303341158, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation3, planet5, star2, star0, phenomenon6, groundstation4. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument4 supports image of mode infrared1 and its calibration target is star2. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to star0. Satellite satellite2 is pointing to groundstation1. Satellite satellite1 is pointing to groundstation1. Power is available on the following satellite(s): satellite1, satellite0. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A thermograph2 mode image of target planet5 is available. A spectrograph0 mode image of target phenomenon6 is available. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite0 is pointing to groundstation4 and Satellite satellite0 is pointing to groundstation1. B. Satellite satellite2 is pointing to phenomenon6 and Satellite satellite2 is pointing to planet5. C. Satellite satellite2 is pointing to planet5 and Satellite satellite2 is pointing to phenomenon6. D. Following instruments are powered on: instrument0 and Satellite satellite2 is pointing to phenomenon6.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite0 is pointing to groundstation4 and Satellite satellite0 is pointing to groundstation1", "Satellite satellite2 is pointing to phenomenon6 and Satellite satellite2 is pointing to planet5", "Satellite satellite2 is pointing to planet5 and Satellite satellite2 is pointing to phenomenon6", "Following instruments are powered on: instrument0 and Satellite satellite2 is pointing to phenomenon6"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
