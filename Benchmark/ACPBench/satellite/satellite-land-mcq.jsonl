{"id": 2250439443254950490, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, groundstation2, phenomenon5, star3, star6, star1, groundstation0. There are 3 image mode(s): image2, image0, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument5, instrument3, instrument4. Satellite satellite5 has following instruments onboard: instrument12, instrument14, instrument13. Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite6 has following instruments onboard: instrument16, instrument17, instrument15. Satellite satellite3 has following instruments onboard: instrument8, instrument7. Satellite satellite4 has following instruments onboard: instrument10, instrument11, instrument9. Instrument instrument14 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument10 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument5 supports image of mode image1 and its calibration target is star3. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument15 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument7 supports image of mode image1 and its calibration target is star3. Instrument instrument2 supports image of mode image2 and its calibration target is star1. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument16 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4.  Currently, Satellite satellite6 is pointing to groundstation0. Satellite satellite1 is pointing to phenomenon5. Satellite satellite5 is pointing to phenomenon5. Satellite satellite4 is pointing to star1. Satellite satellite0 is pointing to groundstation2. Satellite satellite3 is pointing to star6. Satellite satellite2 is pointing to groundstation2. Power is available on the following satellite(s): satellite4, satellite3, satellite5, satellite6, satellite0. Following instruments are powered on: instrument4, instrument6. The goal is to reach a state where the following facts hold: A image0 mode image of target phenomenon5 is available, Satellite satellite1 is pointing to phenomenon5, Satellite satellite4 is pointing to star1, A image1 mode image of target star6 is available, and Satellite satellite5 is pointing to groundstation0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. A image0 mode image of target phenomenon5 is available. B. Satellite satellite1 is pointing to star3. C. Satellite satellite2 is pointing to groundstation4. D. A image0 mode image of target groundstation0 is available.", "choices": {"label": ["A", "B", "C", "D"], "text": ["A image0 mode image of target phenomenon5 is available", "Satellite satellite1 is pointing to star3", "Satellite satellite2 is pointing to groundstation4", "A image0 mode image of target groundstation0 is available"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 6105003849156979941, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation0, star4, planet5, star2, planet6, groundstation3. There are 3 image mode(s): image1, thermograph0, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite8 has following instruments onboard: instrument14, instrument13. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite6 has following instruments onboard: instrument10, instrument11, instrument9. Satellite satellite4 has following instruments onboard: instrument6, instrument7. Satellite satellite3 has following instruments onboard: instrument5, instrument4. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite5 has following instruments onboard: instrument8. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument10 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument4 supports image of mode image1 and its calibration target is groundstation1. Instrument instrument7 supports image of mode image1 and its calibration target is star2. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument8 supports image of mode image1 and its calibration target is star2. Instrument instrument2 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument6 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument12 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument0 supports image of mode image1 and its calibration target is star4. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0.  Currently, Satellite satellite5 is pointing to groundstation3. Satellite satellite2 is pointing to planet6. Satellite satellite3 is pointing to groundstation1. Satellite satellite6 is pointing to planet6. Satellite satellite8 is pointing to planet6. Satellite satellite7 is pointing to star2. Satellite satellite1 is pointing to groundstation0. Satellite satellite9 is pointing to star4. Satellite satellite4 is pointing to planet6. Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite9, satellite5, satellite1, satellite0, satellite4, satellite3, satellite8, satellite7, satellite2. Following instruments are powered on: instrument10. Following instruments are calibrated: instrument10. A thermograph0 mode image of target planet5 is available. A infrared2 mode image of target planet6 is available. The goal is to reach a state where the following facts hold: Satellite satellite8 is pointing to planet6, A thermograph0 mode image of target planet5 is available, A infrared2 mode image of target planet6 is available, and Satellite satellite6 is pointing to star4.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Satellite satellite7 is pointing to star4. B. A image1 mode image of target groundstation0 is available. C. Satellite satellite6 is pointing to star4. D. A thermograph0 mode image of target star4 is available.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite7 is pointing to star4", "A image1 mode image of target groundstation0 is available", "Satellite satellite6 is pointing to star4", "A thermograph0 mode image of target star4 is available"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -3816485843654493285, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation4, groundstation2, phenomenon5, star3, star6, star1, groundstation0. There are 3 image mode(s): image2, image0, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument5, instrument3, instrument4. Satellite satellite5 has following instruments onboard: instrument12, instrument14, instrument13. Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite6 has following instruments onboard: instrument16, instrument17, instrument15. Satellite satellite3 has following instruments onboard: instrument8, instrument7. Satellite satellite4 has following instruments onboard: instrument10, instrument11, instrument9. Instrument instrument14 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument10 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument5 supports image of mode image1 and its calibration target is star3. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument15 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument7 supports image of mode image1 and its calibration target is star3. Instrument instrument2 supports image of mode image2 and its calibration target is star1. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument16 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4.  Currently, Satellite satellite3 is pointing to groundstation4. Satellite satellite1 is pointing to phenomenon5. Satellite satellite2 is pointing to star6. Satellite satellite0 is pointing to groundstation2. Satellite satellite4 is pointing to star6. Satellite satellite6 is pointing to star3. Satellite satellite5 is pointing to groundstation0. Power is available on the following satellite(s): satellite4, satellite3, satellite0, satellite1, satellite6. Following instruments are powered on: instrument14, instrument6. Following instruments are calibrated: instrument6. A image0 mode image of target phenomenon5 is available. A image1 mode image of target star6 is available. The goal is to reach a state where the following facts hold: A image0 mode image of target phenomenon5 is available, Satellite satellite1 is pointing to phenomenon5, Satellite satellite4 is pointing to star1, A image1 mode image of target star6 is available, and Satellite satellite5 is pointing to groundstation0.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Following instruments are powered on: instrument1. B. Following instruments are calibrated: instrument2. C. Following instruments are calibrated: instrument13. D. Satellite satellite4 is pointing to star1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Following instruments are powered on: instrument1", "Following instruments are calibrated: instrument2", "Following instruments are calibrated: instrument13", "Satellite satellite4 is pointing to star1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 1129428429997905057, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): planet7, groundstation1, star10, groundstation5, groundstation4, star9, planet8, star0, star2, groundstation6, groundstation3. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 11 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite2 has following instruments onboard: instrument5, instrument3, instrument4. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Satellite satellite4 has following instruments onboard: instrument8, instrument7. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument0 supports image of mode thermograph2 and its calibration target is star0. Instrument instrument6 supports image of mode thermograph2 and its calibration target is star0. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument9 supports image of mode thermograph2 and its calibration target is groundstation1. Instrument instrument1 supports image of mode thermograph2 and its calibration target is groundstation5. Instrument instrument2 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2.  Currently, Satellite satellite5 is pointing to star2. Satellite satellite2 is pointing to planet7. Satellite satellite4 is pointing to star10. Satellite satellite0 is pointing to star10. Satellite satellite1 is pointing to star9. Satellite satellite3 is pointing to star9. Power is available on the following satellite(s): satellite4, satellite3, satellite5, satellite1, satellite0. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A thermograph2 mode image of target planet7 is available. The goal is to reach a state where the following facts hold: Satellite satellite5 is pointing to star2, A infrared1 mode image of target star9 is available, Satellite satellite4 is pointing to star10, Satellite satellite1 is pointing to star9, A infrared1 mode image of target planet8 is available, A thermograph2 mode image of target planet7 is available, and A infrared1 mode image of target star10 is available.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Satellite satellite2 is pointing to star0. B. Satellite satellite1 is pointing to star0. C. A infrared1 mode image of target planet8 is available. D. Satellite satellite5 is pointing to planet7.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite2 is pointing to star0", "Satellite satellite1 is pointing to star0", "A infrared1 mode image of target planet8 is available", "Satellite satellite5 is pointing to planet7"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -7538443036864725431, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): planet7, groundstation1, star10, groundstation5, groundstation4, star9, planet8, star0, star2, groundstation6, groundstation3. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 11 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite2 has following instruments onboard: instrument5, instrument3, instrument4. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Satellite satellite4 has following instruments onboard: instrument8, instrument7. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument0 supports image of mode thermograph2 and its calibration target is star0. Instrument instrument6 supports image of mode thermograph2 and its calibration target is star0. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument9 supports image of mode thermograph2 and its calibration target is groundstation1. Instrument instrument1 supports image of mode thermograph2 and its calibration target is groundstation5. Instrument instrument2 supports image of mode thermograph2 and its calibration target is star2. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2.  Currently, Satellite satellite4 is pointing to planet8. Satellite satellite1 is pointing to star10. Satellite satellite2 is pointing to star0. Satellite satellite0 is pointing to star10. Satellite satellite3 is pointing to star9. Satellite satellite5 is pointing to planet8. Power is available on the following satellite(s): satellite4, satellite3, satellite5, satellite2, satellite0. Following instruments are powered on: instrument2. Following instruments are calibrated: instrument2. A infrared1 mode image of target star10 is available. The goal is to reach a state where the following facts hold: Satellite satellite5 is pointing to star2, A infrared1 mode image of target star9 is available, Satellite satellite4 is pointing to star10, Satellite satellite1 is pointing to star9, A infrared1 mode image of target planet8 is available, A thermograph2 mode image of target planet7 is available, and A infrared1 mode image of target star10 is available.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Satellite satellite2 is pointing to star2. B. Satellite satellite2 is pointing to groundstation5. C. A infrared1 mode image of target groundstation5 is available. D. Satellite satellite1 is pointing to star9.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite2 is pointing to star2", "Satellite satellite2 is pointing to groundstation5", "A infrared1 mode image of target groundstation5 is available", "Satellite satellite1 is pointing to star9"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 2269589358458987078, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation0, star4, planet5, star2, planet6, groundstation3. There are 3 image mode(s): image1, thermograph0, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite8 has following instruments onboard: instrument14, instrument13. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite6 has following instruments onboard: instrument10, instrument11, instrument9. Satellite satellite4 has following instruments onboard: instrument6, instrument7. Satellite satellite3 has following instruments onboard: instrument5, instrument4. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite5 has following instruments onboard: instrument8. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument10 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument4 supports image of mode image1 and its calibration target is groundstation1. Instrument instrument7 supports image of mode image1 and its calibration target is star2. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument8 supports image of mode image1 and its calibration target is star2. Instrument instrument2 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument6 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument12 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument0 supports image of mode image1 and its calibration target is star4. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0.  Currently, Satellite satellite5 is pointing to groundstation3. Satellite satellite2 is pointing to planet6. Satellite satellite3 is pointing to groundstation1. Satellite satellite8 is pointing to groundstation1. Satellite satellite6 is pointing to groundstation1. Satellite satellite7 is pointing to star2. Satellite satellite1 is pointing to groundstation0. Satellite satellite9 is pointing to star4. Satellite satellite4 is pointing to planet6. Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite9, satellite6, satellite5, satellite1, satellite0, satellite4, satellite3, satellite8, satellite7, satellite2. The goal is to reach a state where the following facts hold: Satellite satellite8 is pointing to planet6, A thermograph0 mode image of target planet5 is available, A infrared2 mode image of target planet6 is available, and Satellite satellite6 is pointing to star4.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Satellite satellite8 is pointing to planet6. B. Satellite satellite8 is pointing to star4. C. Following instruments are powered on: instrument13. D. Following instruments are calibrated: instrument3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite8 is pointing to planet6", "Satellite satellite8 is pointing to star4", "Following instruments are powered on: instrument13", "Following instruments are calibrated: instrument3"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 6150788479937358022, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation0, star4, planet5, star2, planet6, groundstation3. There are 3 image mode(s): image1, thermograph0, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode image1 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to planet5. Following instruments are powered on: instrument0. Following instruments are calibrated: instrument0. A thermograph0 mode image of target planet5 is available. A infrared2 mode image of target planet5 is available. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available and A thermograph0 mode image of target planet5 is available.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Satellite satellite0 is pointing to planet6. B. A infrared2 mode image of target star4 is available. C. A thermograph0 mode image of target groundstation3 is available. D. A thermograph0 mode image of target star4 is available.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite0 is pointing to planet6", "A infrared2 mode image of target star4 is available", "A thermograph0 mode image of target groundstation3 is available", "A thermograph0 mode image of target star4 is available"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 2426928770476244268, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation4, star0, phenomenon6, planet5, star2, groundstation3. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument4 supports image of mode infrared1 and its calibration target is star2. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3.  Currently, Satellite satellite0 is pointing to groundstation1. Satellite satellite1 is pointing to groundstation3. Satellite satellite2 is pointing to planet5. Power is available on the following satellite(s): satellite1, satellite0. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A thermograph2 mode image of target planet5 is available. The goal is to reach a state where the following facts hold: A thermograph2 mode image of target planet5 is available, A spectrograph0 mode image of target phenomenon6 is available, and Satellite satellite1 is pointing to phenomenon6.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. A spectrograph0 mode image of target groundstation3 is available. B. Following instruments are calibrated: instrument0. C. Satellite satellite0 is pointing to star0. D. Satellite satellite1 is pointing to phenomenon6.", "choices": {"label": ["A", "B", "C", "D"], "text": ["A spectrograph0 mode image of target groundstation3 is available", "Following instruments are calibrated: instrument0", "Satellite satellite0 is pointing to star0", "Satellite satellite1 is pointing to phenomenon6"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 5638491118465662235, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation4, star0, phenomenon6, planet5, star2, groundstation3. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument4 supports image of mode infrared1 and its calibration target is star2. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3.  Currently, Satellite satellite0 is pointing to groundstation1. Satellite satellite1 is pointing to star2. Satellite satellite2 is pointing to planet5. Power is available on the following satellite(s): satellite1. Following instruments are powered on: instrument4, instrument0. Following instruments are calibrated: instrument4. The goal is to reach a state where the following facts hold: A thermograph2 mode image of target planet5 is available, A spectrograph0 mode image of target phenomenon6 is available, and Satellite satellite1 is pointing to phenomenon6.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. A spectrograph0 mode image of target groundstation4 is available. B. A spectrograph0 mode image of target phenomenon6 is available. C. Satellite satellite0 is pointing to star0. D. A spectrograph0 mode image of target star0 is available.", "choices": {"label": ["A", "B", "C", "D"], "text": ["A spectrograph0 mode image of target groundstation4 is available", "A spectrograph0 mode image of target phenomenon6 is available", "Satellite satellite0 is pointing to star0", "A spectrograph0 mode image of target star0 is available"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -4450422086382299668, "group": "landmarks_mcq", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation4, star0, phenomenon6, planet5, star2, groundstation3. There are 3 image mode(s): infrared1, spectrograph0, thermograph2. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument4 supports image of mode infrared1 and its calibration target is star2. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3.  Currently, Satellite satellite2 is pointing to star2. Satellite satellite0 is pointing to groundstation4. Satellite satellite1 is pointing to phenomenon6. Power is available on the following satellite(s): satellite1, satellite0. Following instruments are powered on: instrument4. The goal is to reach a state where the following facts hold: A thermograph2 mode image of target planet5 is available, A spectrograph0 mode image of target phenomenon6 is available, and Satellite satellite1 is pointing to phenomenon6.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Satellite satellite1 is pointing to planet5. B. A thermograph2 mode image of target planet5 is available. C. Satellite satellite2 is pointing to groundstation1. D. A spectrograph0 mode image of target star0 is available.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite1 is pointing to planet5", "A thermograph2 mode image of target planet5 is available", "Satellite satellite2 is pointing to groundstation1", "A spectrograph0 mode image of target star0 is available"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
