{"id": -7391469720762492339, "group": "applicable_actions_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball3, and right gripper is carrying the ball ball1. Additionally, ball5 and ball7 are at room3, ball6 is at room2, ball4 and ball2 are at room1. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - grasp the object ?obj from room ?room with the ?g gripper of robot ?r, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room from the ?g gripper of the robot ?r.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(move robot1 room1 room3)", "(drop robot1 ball3 room1 left1)", "(drop robot1 ball1 room1 right1)", "(move robot1 room1 room2)", "(move robot1 room1 room1)"], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball2 room1) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at ball7 room3) (at-robby robot1 room1) (carry robot1 ball1 right1) (carry robot1 ball3 left1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": 5799009648598760553, "group": "applicable_actions_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball12, ball15, ball6, ball3, and ball13 are at room2, ball9, ball10, ball14, ball5, ball11, ball4, and ball2 are at room1, ball8 and ball7 are at room3. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - drop object ?obj in room ?room using ?g gripper of robot ?r.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(pick robot1 ball13 room2 right1)", "(pick robot1 ball3 room2 right1)", "(pick robot1 ball15 room2 right1)", "(pick robot1 ball6 room2 right1)", "(drop robot1 ball1 room2 left1)", "(move robot1 room2 room1)", "(move robot1 room2 room3)", "(move robot1 room2 room2)", "(pick robot1 ball12 room2 right1)"], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-15)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball10 ball11 ball12 ball13 ball14 ball15 ball2 ball3 ball4 ball5 ball6 ball7 ball8 ball9 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball10 room1) (at ball11 room1) (at ball12 room2) (at ball13 room2) (at ball14 room1) (at ball15 room2) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room2) (at ball7 room3) (at ball8 room3) (at ball9 room1) (at-robby robot1 room2) (carry robot1 ball1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room1) (at ball9 room3) (at ball10 room3) (at ball11 room3) (at ball12 room2) (at ball13 room2) (at ball14 room3) (at ball15 room1)))\n)"}
{"id": -2899086973478982578, "group": "applicable_actions_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3, right gripper is free, and left gripper is carrying the ball ball3. Additionally, ball7, ball4, and ball2 are at room1, ball1 is at room3, ball5 and ball6 are at room2. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room using the robot ?r with ?g gripper.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(drop robot1 ball3 room3 left1)", "(move robot1 room3 room3)", "(pick robot1 ball1 room3 right1)", "(move robot1 room3 room2)", "(move robot1 room3 room1)"], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room3) (at ball2 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1) (at-robby robot1 room3) (carry robot1 ball3 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": 3054995237422392278, "group": "applicable_actions_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1 and both grippers are free. Additionally, ball4 is at room5, ball1 and ball3 are at room4, ball2 is at room1. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - use the robot ?r equipped with ?g gripper to retrieve the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - drop the object ?obj in the ?g gripper of the robot ?r at the room ?room.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(move robot1 room1 room3)", "(move robot1 room1 room4)", "(pick robot1 ball2 room1 right1)", "(move robot1 room1 room5)", "(pick robot1 ball2 room1 left1)", "(move robot1 room1 room2)", "(move robot1 room1 room1)"], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room4) (at ball2 room1) (at ball3 room4) (at ball4 room5) (at-robby robot1 room1) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room4) (at ball2 room3) (at ball3 room4) (at ball4 room5)))\n)"}
{"id": -7431460256981123330, "group": "applicable_actions_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball4. Additionally, ball1 and ball3 are at room2. The available actions are: (move ?r ?from ?to) - move the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - use the robot ?r equipped with ?g gripper to retrieve the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - drop the object ?obj in room ?room using robot ?r with the ?g gripper.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(move robot1 room1 room2)", "(drop robot1 ball2 room1 left1)", "(move robot1 room1 room1)", "(drop robot1 ball4 room1 right1)"], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball1 room2) (at ball3 room2) (at-robby robot1 room1) (carry robot1 ball2 left1) (carry robot1 ball4 right1))\n    (:goal (and (at ball1 room2) (at ball2 room2) (at ball3 room2) (at ball4 room2)))\n)"}
{"id": 5392496691758407236, "group": "applicable_actions_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball3, and right gripper is carrying the ball ball2. Additionally, ball1 is at room2, ball4 is at room5. The available actions are: (move ?r ?from ?to) - move the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room using the robot ?r with ?g gripper.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(move robot1 room1 room3)", "(drop robot1 ball3 room1 left1)", "(drop robot1 ball2 room1 right1)", "(move robot1 room1 room4)", "(move robot1 room1 room5)", "(move robot1 room1 room2)", "(move robot1 room1 room1)"], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room2) (at ball4 room5) (at-robby robot1 room1) (carry robot1 ball2 right1) (carry robot1 ball3 left1))\n    (:goal (and (at ball1 room4) (at ball2 room3) (at ball3 room4) (at ball4 room5)))\n)"}
{"id": 6661842178067187711, "group": "applicable_actions_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball2. Additionally, ball5 and ball7 are at room3, ball1, ball3, and ball4 are at room1, ball6 is at room2. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - pick up object ?obj with robot ?r using ?g gripper from room ?room, and (drop ?r ?obj ?room ?g) - use robot ?r with ?g gripper to place the object ?obj in room ?room.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(pick robot1 ball3 room1 right1)", "(move robot1 room1 room3)", "(pick robot1 ball4 room1 right1)", "(drop robot1 ball2 room1 left1)", "(pick robot1 ball1 room1 right1)", "(move robot1 room1 room2)", "(move robot1 room1 room1)"], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball3 room1) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at ball7 room3) (at-robby robot1 room1) (carry robot1 ball2 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": 2034655123810359105, "group": "applicable_actions_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball4. Additionally, ball1 is at room1, ball3 is at room2. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - pick up the object ?obj with the robot ?r using the ?g gripper from the room ?room, and (drop ?r ?obj ?room ?g) - place the object ?obj in the room ?room from the ?g gripper of the robot ?r.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(move robot1 room2 room1)", "(drop robot1 ball2 room2 left1)", "(move robot1 room2 room2)", "(drop robot1 ball4 room2 right1)"], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball1 room1) (at ball3 room2) (at-robby robot1 room2) (carry robot1 ball2 left1) (carry robot1 ball4 right1))\n    (:goal (and (at ball1 room2) (at ball2 room2) (at ball3 room2) (at ball4 room2)))\n)"}
{"id": -9118839679507291341, "group": "applicable_actions_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball2. Additionally, ball7, ball1, ball3, and ball4 are at room1, ball5 is at room3, ball6 is at room2. The available actions are: (move ?r ?from ?to) - move the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - grasp the object ?obj from room ?room with the ?g gripper of robot ?r, and (drop ?r ?obj ?room ?g) - drop the object ?obj in the ?g gripper of the robot ?r at the room ?room.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(pick robot1 ball4 room1 right1)", "(move robot1 room1 room3)", "(pick robot1 ball3 room1 right1)", "(drop robot1 ball2 room1 left1)", "(pick robot1 ball1 room1 right1)", "(move robot1 room1 room2)", "(pick robot1 ball7 room1 right1)", "(move robot1 room1 room1)"], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball3 room1) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at ball7 room1) (at-robby robot1 room1) (carry robot1 ball2 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": 2165486950241388098, "group": "applicable_actions_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball10, and right gripper is carrying the ball ball14. Additionally, ball12, ball1, ball3, and ball13 are at room2, ball9, ball6, ball15, ball5, ball11, ball4, and ball2 are at room1, ball7 and ball8 are at room3. The available actions are: (move ?r ?from ?to) - move the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - use the robot ?r equipped with ?g gripper to retrieve the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - drop object ?obj in room ?room using ?g gripper of robot ?r.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(drop robot1 ball10 room1 left1)", "(move robot1 room1 room3)", "(drop robot1 ball14 room1 right1)", "(move robot1 room1 room2)", "(move robot1 room1 room1)"], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-15)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball10 ball11 ball12 ball13 ball14 ball15 ball2 ball3 ball4 ball5 ball6 ball7 ball8 ball9 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room2) (at ball11 room1) (at ball12 room2) (at ball13 room2) (at ball15 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room3) (at ball9 room1) (at-robby robot1 room1) (carry robot1 ball10 left1) (carry robot1 ball14 right1))\n    (:goal (and (at ball1 room2) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room1) (at ball9 room3) (at ball10 room3) (at ball11 room3) (at ball12 room2) (at ball13 room2) (at ball14 room3) (at ball15 room1)))\n)"}
{"id": 6771410528143130496, "group": "applicable_actions_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 5 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room3, left gripper is carrying the ball ball4, and right gripper is carrying the ball ball2. Additionally, ball1 is at room2, ball3 is at room3. The available actions are: (move ?r ?from ?to) - move the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - use the robot ?r equipped with ?g gripper to retrieve the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - drop the object ?obj in room ?room using robot ?r with the ?g gripper.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(drop robot1 ball4 room3 left1)", "(drop robot1 ball2 room3 right1)", "(move robot1 room3 room3)", "(move robot1 room3 room2)", "(move robot1 room3 room4)", "(move robot1 room3 room5)", "(move robot1 room3 room1)"], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room2) (at ball3 room3) (at-robby robot1 room3) (carry robot1 ball2 right1) (carry robot1 ball4 left1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
{"id": 2697170750550879317, "group": "applicable_actions_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 3 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room3, left gripper is free, and right gripper is carrying the ball ball2. Additionally, ball1 and ball4 are at room1, ball3 is at room3. The available actions are: (move ?r ?from ?to) - move robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to pick up the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - drop object ?obj in room ?room using ?g gripper of robot ?r.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(drop robot1 ball2 room3 right1)", "(move robot1 room3 room3)", "(move robot1 room3 room2)", "(pick robot1 ball3 room3 left1)", "(move robot1 room3 room1)"], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball3 room3) (at ball4 room1) (at-robby robot1 room3) (carry robot1 ball2 right1) (free robot1 left1))\n    (:goal (and (at ball1 room3) (at ball2 room2) (at ball3 room3) (at ball4 room3)))\n)"}
{"id": 4337409930596904982, "group": "applicable_actions_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 5 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room2, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball4. Additionally, ball1 is at room2, ball3 is at room3. The available actions are: (move ?r ?from ?to) - move the robot ?r from room ?from to room ?to, (pick ?r ?obj ?room ?g) - use the ?g gripper of robot ?r to pick up the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - drop the object ?obj in the ?g gripper of the robot ?r at the room ?room.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(drop robot1 ball4 room2 right1)", "(move robot1 room2 room5)", "(drop robot1 ball2 room2 left1)", "(move robot1 room2 room4)", "(move robot1 room2 room3)", "(move robot1 room2 room1)", "(move robot1 room2 room2)"], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room2) (at ball3 room3) (at-robby robot1 room2) (carry robot1 ball2 left1) (carry robot1 ball4 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
{"id": -5630869085902318824, "group": "applicable_actions_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 3 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball3. Additionally, ball1 and ball4 are at room3, ball2 is at room1. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - use the robot ?r equipped with ?g gripper to retrieve the object ?obj from room ?room, and (drop ?r ?obj ?room ?g) - use robot ?r with ?g gripper to place the object ?obj in room ?room.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(move robot1 room1 room3)", "(drop robot1 ball3 room1 left1)", "(pick robot1 ball2 room1 right1)", "(move robot1 room1 room2)", "(move robot1 room1 room1)"], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room3) (at ball2 room1) (at ball4 room3) (at-robby robot1 room1) (carry robot1 ball3 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1)))\n)"}
{"id": 4238280659919519791, "group": "applicable_actions_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 5 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room4, left gripper is free, and right gripper is carrying the ball ball2. Additionally, ball1 is at room2, ball4 is at room4, ball3 is at room3. The available actions are: (move ?r ?from ?to) - transfer the robot ?r from the room ?from to the room ?to, (pick ?r ?obj ?room ?g) - grasp the object ?obj from room ?room with the ?g gripper of robot ?r, and (drop ?r ?obj ?room ?g) - drop the object ?obj in the ?g gripper of the robot ?r at the room ?room.", "question": "Generate the list of all ground actions that are applicable in this state.", "answer": ["(move robot1 room4 room5)", "(pick robot1 ball4 room4 left1)", "(move robot1 room4 room2)", "(drop robot1 ball2 room4 right1)", "(move robot1 room4 room1)", "(move robot1 room4 room3)", "(move robot1 room4 room4)"], "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room2) (at ball3 room3) (at ball4 room4) (at-robby robot1 room4) (carry robot1 ball2 right1) (free robot1 left1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
