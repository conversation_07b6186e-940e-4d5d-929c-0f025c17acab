{"id": -7963333953880844268, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball7. Additionally, ball3, ball2, and ball4 are at room1, ball6 and ball5 are at room2, ball1 is at room3. The goal is to reach a state where the following facts hold: Ball ball3 is at room1 location, Ball ball6 is in room room2, Ball ball2 is at room1 location, Ball ball1 is in room room3, Ball ball7 is at room1 location, Ball ball4 is in room room1, and Ball ball5 is at room2 location. The available propositions are: (at-robby ?r ?x) - Robot ?r is in room ?x, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(carry robot1 ball6 left1)", "(at ball3 room3)", "(carry robot1 ball3 left1)", "(at-robby robot1 room3)", "(carry robot1 ball3 right1)", "(at ball1 room1)", "(at ball4 room3)", "(carry robot1 ball6 right1)", "(at ball7 room3)", "(at-robby robot1 room2)", "(at ball4 room2)", "(at ball2 room3)", "(at ball5 room1)", "(at ball1 room2)", "(carry robot1 ball5 right1)", "(at ball7 room2)", "(carry robot1 ball2 right1)", "(at ball6 room1)", "(carry robot1 ball4 right1)", "(carry robot1 ball1 left1)", "(carry robot1 ball7 right1)", "(carry robot1 ball4 left1)", "(carry robot1 ball2 left1)", "(at ball6 room3)", "(carry robot1 ball1 right1)", "(at ball3 room2)", "(at ball2 room2)", "(at ball5 room3)", "(carry robot1 ball5 left1)"], "yes": ["(free robot1 left1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at-robby robot1 room1) (carry robot1 ball7 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": 2817307984146376500, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball3. Additionally, ball4 and ball1 are at room2, ball2 is at room1. The goal is to reach a state where the following facts hold: Ball ball3 is at room2 location, Ball ball1 is at room2 location, Ball ball4 is in room room2, and Ball ball2 is at room2 location. The available propositions are: (at-robby ?r ?x) - Robot ?r is in room ?x, (at ?o ?x) - Ball ?o is in room ?x, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(carry robot1 ball4 left1)", "(carry robot1 ball4 right1)", "(at ball3 room1)", "(carry robot1 ball2 left1)", "(carry robot1 ball1 right1)", "(carry robot1 ball3 right1)", "(carry robot1 ball2 right1)", "(at ball4 room1)", "(at ball1 room1)", "(carry robot1 ball1 left1)"], "yes": ["(at-robby robot1 room1)", "(free robot1 left1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball1 room2) (at ball2 room1) (at ball4 room2) (at-robby robot1 room2) (carry robot1 ball3 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room2) (at ball3 room2) (at ball4 room2)))\n)"}
{"id": -4480279621088916499, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is free, and right gripper is carrying the ball ball2. Additionally, ball4 is at room2, ball1 and ball3 are at room8. The goal is to reach a state where the following facts hold: Ball ball4 is at room9 location, Ball ball1 is at room8 location, Ball ball3 is in room room8, and Ball ball2 is at room6 location. The available propositions are: (at-robby ?r ?x) - Robot ?r is in room ?x, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at ball4 room4)", "(at-robby robot1 room10)", "(at ball3 room4)", "(at ball3 room10)", "(carry robot1 ball3 left1)", "(at ball3 room3)", "(at ball4 room5)", "(at-robby robot1 room5)", "(at-robby robot1 room8)", "(at ball1 room1)", "(at ball4 room3)", "(carry robot1 ball3 right1)", "(at-robby robot1 room3)", "(at ball4 room8)", "(at ball3 room6)", "(at ball2 room5)", "(at ball2 room3)", "(at ball2 room9)", "(at ball2 room1)", "(at ball1 room2)", "(at ball1 room6)", "(at ball3 room9)", "(at ball4 room6)", "(at-robby robot1 room7)", "(at ball2 room7)", "(at ball1 room10)", "(at ball4 room7)", "(at ball4 room1)", "(at ball1 room4)", "(carry robot1 ball4 right1)", "(carry robot1 ball1 left1)", "(at-robby robot1 room4)", "(carry robot1 ball4 left1)", "(at ball2 room4)", "(at ball2 room8)", "(at ball3 room7)", "(at ball3 room1)", "(at ball4 room10)", "(at ball3 room5)", "(carry robot1 ball2 left1)", "(at ball1 room7)", "(at ball1 room5)", "(carry robot1 ball1 right1)", "(at ball1 room9)", "(at ball3 room2)", "(at ball2 room10)", "(at ball2 room2)", "(at ball1 room3)"], "yes": ["(at-robby robot1 room6)", "(at-robby robot1 room9)", "(at-robby robot1 room2)", "(free robot1 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-10-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room10 room2 room3 room4 room5 room6 room7 room8 room9 - room)\n    (:init (at ball1 room8) (at ball3 room8) (at ball4 room2) (at-robby robot1 room1) (carry robot1 ball2 right1) (free robot1 left1))\n    (:goal (and (at ball1 room8) (at ball2 room6) (at ball3 room8) (at ball4 room9)))\n)"}
{"id": -5710188051051110527, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball11. Additionally, ball8, ball5, ball2, ball14, ball15, ball6, and ball4 are at room1, ball12, ball13, ball1, and ball3 are at room2, ball10, ball9, and ball7 are at room3. The goal is to reach a state where the following facts hold: Ball ball8 is in room room1, Ball ball12 is in room room2, Ball ball5 is at room1 location, Ball ball13 is at room2 location, Ball ball2 is in room room1, Ball ball3 is in room room2, Ball ball10 is at room3 location, Ball ball1 is in room room2, Ball ball15 is in room room1, Ball ball9 is in room room3, Ball ball6 is in room room1, Ball ball4 is in room room1, Ball ball14 is in room room3, Ball ball11 is at room3 location, and Ball ball7 is at room3 location. The available propositions are: (at-robby ?r ?x) - Robot ?r is in room ?x, (at ?o ?x) - Ball ?o is in room ?x, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at ball9 room2)", "(at ball14 room2)", "(carry robot1 ball15 left1)", "(carry robot1 ball15 right1)", "(carry robot1 ball6 left1)", "(at ball3 room3)", "(carry robot1 ball3 left1)", "(at ball8 room2)", "(at ball11 room1)", "(carry robot1 ball3 right1)", "(at ball1 room1)", "(at ball4 room3)", "(at ball10 room1)", "(carry robot1 ball6 right1)", "(at ball15 room2)", "(carry robot1 ball10 left1)", "(at-robby robot1 room2)", "(at ball4 room2)", "(carry robot1 ball8 left1)", "(carry robot1 ball8 right1)", "(at ball13 room1)", "(at ball2 room3)", "(carry robot1 ball10 right1)", "(carry robot1 ball14 right1)", "(carry robot1 ball12 right1)", "(at ball13 room3)", "(carry robot1 ball14 left1)", "(at ball15 room3)", "(at ball5 room2)", "(at ball12 room3)", "(at ball10 room2)", "(carry robot1 ball13 left1)", "(at ball9 room1)", "(at ball8 room3)", "(carry robot1 ball7 left1)", "(carry robot1 ball5 right1)", "(at ball7 room2)", "(at ball11 room2)", "(at ball7 room1)", "(carry robot1 ball2 right1)", "(carry robot1 ball4 right1)", "(carry robot1 ball12 left1)", "(carry robot1 ball9 left1)", "(carry robot1 ball11 right1)", "(at ball6 room2)", "(carry robot1 ball7 right1)", "(carry robot1 ball1 left1)", "(carry robot1 ball4 left1)", "(at ball12 room1)", "(at ball3 room1)", "(carry robot1 ball9 right1)", "(carry robot1 ball2 left1)", "(at ball6 room3)", "(carry robot1 ball1 right1)", "(at ball1 room3)", "(free robot1 left1)", "(at ball2 room2)", "(at ball5 room3)", "(carry robot1 ball13 right1)", "(carry robot1 ball5 left1)"], "yes": ["(at-robby robot1 room3)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-15)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball10 ball11 ball12 ball13 ball14 ball15 ball2 ball3 ball4 ball5 ball6 ball7 ball8 ball9 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room2) (at ball10 room3) (at ball12 room2) (at ball13 room2) (at ball14 room1) (at ball15 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room1) (at ball9 room3) (at-robby robot1 room1) (carry robot1 ball11 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room1) (at ball9 room3) (at ball10 room3) (at ball11 room3) (at ball12 room2) (at ball13 room2) (at ball14 room3) (at ball15 room1)))\n)"}
{"id": -6787573888982196997, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball4, and right gripper is carrying the ball ball2. Additionally, ball3 and ball1 are at room4. The goal is to reach a state where the following facts hold: Ball ball2 is in room room3, Ball ball1 is at room4 location, Ball ball4 is in room room5, and Ball ball3 is at room4 location. The available propositions are: (at-robby ?r ?x) - Robot ?r is in room ?x, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at ball4 room4)", "(at ball3 room3)", "(carry robot1 ball3 left1)", "(carry robot1 ball3 right1)", "(at ball1 room1)", "(at ball4 room3)", "(at-robby robot1 room2)", "(at ball4 room2)", "(at ball2 room5)", "(at ball2 room1)", "(at ball1 room2)", "(carry robot1 ball4 right1)", "(at ball4 room1)", "(carry robot1 ball1 left1)", "(at-robby robot1 room4)", "(at ball2 room4)", "(at ball3 room1)", "(at ball3 room5)", "(carry robot1 ball2 left1)", "(at ball1 room5)", "(carry robot1 ball1 right1)", "(at ball3 room2)", "(at ball1 room3)", "(at ball2 room2)"], "yes": ["(at-robby robot1 room3)", "(at-robby robot1 room5)", "(free robot1 left1)", "(free robot1 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room4) (at ball3 room4) (at-robby robot1 room1) (carry robot1 ball2 right1) (carry robot1 ball4 left1))\n    (:goal (and (at ball1 room4) (at ball2 room3) (at ball3 room4) (at ball4 room5)))\n)"}
{"id": -1110960719954131725, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room9, left gripper is free, and right gripper is carrying the ball ball1. Additionally, ball4 and ball2 are at room2, ball3 is at room5. The goal is to reach a state where the following facts hold: Ball ball4 is in room room9, Ball ball1 is in room room8, Ball ball3 is at room8 location, and Ball ball2 is at room6 location. The available propositions are: (at-robby ?r ?x) - Robot ?r is in room ?x, (at ?o ?x) - Ball ?o is in room ?x, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at-robby robot1 room10)", "(at ball4 room4)", "(at ball3 room4)", "(at ball3 room3)", "(at ball3 room10)", "(carry robot1 ball3 left1)", "(at-robby robot1 room3)", "(at ball4 room5)", "(carry robot1 ball3 right1)", "(at ball1 room1)", "(at ball4 room3)", "(at ball4 room8)", "(at ball3 room6)", "(at ball2 room5)", "(at ball2 room3)", "(at ball2 room9)", "(at ball2 room1)", "(at ball1 room2)", "(at ball1 room6)", "(at ball3 room9)", "(at ball4 room6)", "(at-robby robot1 room7)", "(at ball2 room7)", "(at ball1 room10)", "(at-robby robot1 room1)", "(carry robot1 ball2 right1)", "(at ball4 room7)", "(at ball4 room1)", "(at ball1 room4)", "(carry robot1 ball4 right1)", "(carry robot1 ball1 left1)", "(at-robby robot1 room4)", "(carry robot1 ball4 left1)", "(at ball2 room4)", "(at ball2 room8)", "(at ball3 room7)", "(at ball3 room1)", "(at ball4 room10)", "(carry robot1 ball2 left1)", "(at ball1 room7)", "(at ball1 room5)", "(at ball1 room9)", "(at ball3 room2)", "(at ball1 room3)", "(at ball2 room10)"], "yes": ["(at-robby robot1 room8)", "(at-robby robot1 room6)", "(at-robby robot1 room2)", "(at-robby robot1 room5)", "(free robot1 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-10-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room10 room2 room3 room4 room5 room6 room7 room8 room9 - room)\n    (:init (at ball2 room2) (at ball3 room5) (at ball4 room2) (at-robby robot1 room9) (carry robot1 ball1 right1) (free robot1 left1))\n    (:goal (and (at ball1 room8) (at ball2 room6) (at ball3 room8) (at ball4 room9)))\n)"}
{"id": -870951553536672556, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball11, and right gripper is carrying the ball ball10. Additionally, ball12, ball13, ball3, and ball1 are at room2, ball9, ball5, ball2, ball14, ball15, ball6, and ball4 are at room1, ball8 and ball7 are at room3. The goal is to reach a state where the following facts hold: Ball ball8 is at room1 location, Ball ball12 is at room2 location, Ball ball5 is at room1 location, Ball ball13 is in room room2, Ball ball2 is in room room1, Ball ball3 is at room2 location, Ball ball10 is at room3 location, Ball ball1 is in room room2, Ball ball15 is in room room1, Ball ball9 is in room room3, Ball ball6 is at room1 location, Ball ball4 is in room room1, Ball ball14 is in room room3, Ball ball11 is in room room3, and Ball ball7 is in room room3. The available propositions are: (at-robby ?r ?x) - Robot ?r is in room ?x, (at ?o ?x) - Ball ?o is in room ?x, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at ball9 room2)", "(at ball14 room2)", "(carry robot1 ball15 left1)", "(carry robot1 ball15 right1)", "(carry robot1 ball6 left1)", "(at ball3 room3)", "(carry robot1 ball3 left1)", "(at ball8 room2)", "(at ball11 room1)", "(carry robot1 ball3 right1)", "(at ball1 room1)", "(at ball4 room3)", "(at ball10 room1)", "(carry robot1 ball6 right1)", "(at ball15 room2)", "(carry robot1 ball10 left1)", "(at-robby robot1 room2)", "(at ball4 room2)", "(carry robot1 ball8 left1)", "(carry robot1 ball8 right1)", "(at ball13 room1)", "(at ball2 room3)", "(carry robot1 ball14 right1)", "(carry robot1 ball12 right1)", "(at ball13 room3)", "(carry robot1 ball14 left1)", "(at ball15 room3)", "(free robot1 right1)", "(at ball5 room2)", "(at ball12 room3)", "(at ball10 room2)", "(carry robot1 ball13 left1)", "(carry robot1 ball7 left1)", "(carry robot1 ball5 right1)", "(at ball7 room2)", "(at ball11 room2)", "(at ball7 room1)", "(carry robot1 ball2 right1)", "(carry robot1 ball4 right1)", "(carry robot1 ball12 left1)", "(carry robot1 ball9 left1)", "(carry robot1 ball11 right1)", "(at ball6 room2)", "(carry robot1 ball7 right1)", "(carry robot1 ball1 left1)", "(carry robot1 ball4 left1)", "(at ball12 room1)", "(at ball3 room1)", "(carry robot1 ball9 right1)", "(carry robot1 ball2 left1)", "(at ball6 room3)", "(carry robot1 ball1 right1)", "(at ball1 room3)", "(free robot1 left1)", "(at ball2 room2)", "(at ball5 room3)", "(carry robot1 ball13 right1)", "(carry robot1 ball5 left1)"], "yes": ["(at-robby robot1 room1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-15)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball10 ball11 ball12 ball13 ball14 ball15 ball2 ball3 ball4 ball5 ball6 ball7 ball8 ball9 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room2) (at ball12 room2) (at ball13 room2) (at ball14 room1) (at ball15 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room3) (at ball9 room1) (at-robby robot1 room3) (carry robot1 ball10 right1) (carry robot1 ball11 left1))\n    (:goal (and (at ball1 room2) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room1) (at ball9 room3) (at ball10 room3) (at ball11 room3) (at ball12 room2) (at ball13 room2) (at ball14 room3) (at ball15 room1)))\n)"}
{"id": -6847555164585677081, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is free, and right gripper is carrying the ball ball2. Additionally, ball4 is at room2, ball3 is at room10, ball1 is at room4. The goal is to reach a state where the following facts hold: Ball ball4 is at room9 location, Ball ball1 is at room8 location, Ball ball3 is in room room8, and Ball ball2 is at room6 location. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at ball4 room4)", "(at ball3 room4)", "(at ball3 room3)", "(carry robot1 ball3 left1)", "(at-robby robot1 room5)", "(at-robby robot1 room3)", "(at ball4 room5)", "(carry robot1 ball3 right1)", "(at ball1 room1)", "(at ball4 room3)", "(at ball4 room8)", "(at ball3 room6)", "(at ball2 room5)", "(at ball2 room3)", "(at ball2 room9)", "(at ball2 room1)", "(at ball1 room2)", "(at ball1 room6)", "(at ball3 room9)", "(at ball4 room6)", "(at-robby robot1 room7)", "(at ball2 room7)", "(at ball1 room10)", "(at ball4 room7)", "(at ball4 room1)", "(carry robot1 ball4 right1)", "(carry robot1 ball1 left1)", "(carry robot1 ball4 left1)", "(at ball2 room4)", "(at ball2 room8)", "(at ball3 room7)", "(at ball3 room1)", "(at ball4 room10)", "(at ball3 room5)", "(carry robot1 ball2 left1)", "(at ball1 room7)", "(at ball1 room5)", "(carry robot1 ball1 right1)", "(at ball1 room9)", "(at ball3 room2)", "(at ball1 room3)", "(at ball2 room10)", "(at ball2 room2)"], "yes": ["(at-robby robot1 room8)", "(at-robby robot1 room6)", "(at-robby robot1 room9)", "(at-robby robot1 room4)", "(at-robby robot1 room10)", "(at-robby robot1 room2)", "(free robot1 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-10-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room10 room2 room3 room4 room5 room6 room7 room8 room9 - room)\n    (:init (at ball1 room4) (at ball3 room10) (at ball4 room2) (at-robby robot1 room1) (carry robot1 ball2 right1) (free robot1 left1))\n    (:goal (and (at ball1 room8) (at ball2 room6) (at ball3 room8) (at ball4 room9)))\n)"}
{"id": 3602961741898926172, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room8, left gripper is carrying the ball ball4, and right gripper is carrying the ball ball2. Additionally, ball1 and ball3 are at room8. The goal is to reach a state where the following facts hold: Ball ball4 is at room9 location, Ball ball1 is in room room8, Ball ball3 is in room room8, and Ball ball2 is at room6 location. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at ball4 room4)", "(at-robby robot1 room10)", "(at ball3 room4)", "(at ball3 room10)", "(carry robot1 ball3 left1)", "(at ball3 room3)", "(at ball4 room5)", "(at-robby robot1 room5)", "(at ball1 room1)", "(at ball4 room3)", "(carry robot1 ball3 right1)", "(at-robby robot1 room3)", "(at-robby robot1 room2)", "(at ball4 room8)", "(at ball4 room2)", "(at ball3 room6)", "(at ball2 room5)", "(at ball2 room3)", "(at ball2 room9)", "(at ball2 room1)", "(at ball1 room2)", "(at ball1 room6)", "(at ball3 room9)", "(at ball4 room6)", "(at-robby robot1 room7)", "(at ball2 room7)", "(at ball1 room10)", "(at-robby robot1 room1)", "(at ball4 room7)", "(at ball4 room1)", "(at ball1 room4)", "(carry robot1 ball4 right1)", "(carry robot1 ball1 left1)", "(at-robby robot1 room4)", "(at ball2 room4)", "(at ball2 room8)", "(at ball3 room7)", "(at ball3 room1)", "(at ball4 room10)", "(at ball3 room5)", "(carry robot1 ball2 left1)", "(at ball1 room7)", "(at ball1 room5)", "(carry robot1 ball1 right1)", "(at ball1 room9)", "(at ball3 room2)", "(free robot1 left1)", "(at ball2 room10)", "(at ball2 room2)", "(at ball1 room3)"], "yes": ["(at-robby robot1 room6)", "(at-robby robot1 room9)", "(free robot1 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-10-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room10 room2 room3 room4 room5 room6 room7 room8 room9 - room)\n    (:init (at ball1 room8) (at ball3 room8) (at-robby robot1 room8) (carry robot1 ball2 right1) (carry robot1 ball4 left1))\n    (:goal (and (at ball1 room8) (at ball2 room6) (at ball3 room8) (at ball4 room9)))\n)"}
{"id": -4529776667343351887, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room8, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball2 is at room6, ball3 is at room10, ball1 is at room4. The goal is to reach a state where the following facts hold: Ball ball4 is at room9 location, Ball ball1 is at room8 location, Ball ball3 is in room room8, and Ball ball2 is at room6 location. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at-robby robot1 room6)", "(at ball4 room4)", "(at ball3 room4)", "(carry robot1 ball3 left1)", "(at ball3 room3)", "(at ball4 room5)", "(at-robby robot1 room5)", "(at ball1 room1)", "(at ball4 room3)", "(carry robot1 ball3 right1)", "(at-robby robot1 room3)", "(at-robby robot1 room2)", "(at ball4 room8)", "(at ball4 room2)", "(at ball3 room6)", "(at ball2 room5)", "(at ball2 room3)", "(at ball2 room9)", "(at ball2 room1)", "(at ball1 room2)", "(at ball1 room6)", "(at ball3 room9)", "(at ball4 room6)", "(at-robby robot1 room7)", "(at ball2 room7)", "(at ball1 room10)", "(at-robby robot1 room1)", "(carry robot1 ball2 right1)", "(at ball4 room7)", "(at ball4 room1)", "(carry robot1 ball1 left1)", "(carry robot1 ball4 left1)", "(at ball2 room4)", "(at ball2 room8)", "(at ball3 room7)", "(at ball3 room1)", "(at ball4 room10)", "(at ball3 room5)", "(carry robot1 ball2 left1)", "(at ball1 room7)", "(at ball1 room5)", "(carry robot1 ball1 right1)", "(at ball1 room9)", "(at ball3 room2)", "(at ball2 room10)", "(at ball2 room2)", "(at ball1 room3)"], "yes": ["(at-robby robot1 room9)", "(at-robby robot1 room4)", "(at-robby robot1 room10)", "(free robot1 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-10-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room10 room2 room3 room4 room5 room6 room7 room8 room9 - room)\n    (:init (at ball1 room4) (at ball2 room6) (at ball3 room10) (at-robby robot1 room8) (carry robot1 ball4 right1) (free robot1 left1))\n    (:goal (and (at ball1 room8) (at ball2 room6) (at ball3 room8) (at ball4 room9)))\n)"}
{"id": 4676800007335087243, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 3 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room2, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball2 is at room1, ball3 is at room2, ball1 is at room3. The goal is to reach a state where the following facts hold: Ball ball3 is in room room2, Ball ball1 is at room1 location, Ball ball4 is at room1 location, and Ball ball2 is in room room1. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(carry robot1 ball4 left1)", "(at ball4 room2)", "(at ball3 room1)", "(at ball2 room3)", "(carry robot1 ball2 left1)", "(carry robot1 ball1 right1)", "(at ball3 room3)", "(carry robot1 ball3 left1)", "(at ball1 room2)", "(carry robot1 ball2 right1)", "(at ball2 room2)", "(carry robot1 ball3 right1)", "(at ball4 room3)", "(carry robot1 ball1 left1)"], "yes": ["(at-robby robot1 room1)", "(at-robby robot1 room3)", "(free robot1 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room3) (at ball2 room1) (at ball3 room2) (at-robby robot1 room2) (carry robot1 ball4 right1) (free robot1 left1))\n    (:goal (and (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1)))\n)"}
{"id": -8680031870502993317, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 7 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room5 and both grippers are free. Additionally, ball3 is at room5, ball2 is at room3, ball1 and ball4 are at room6. The goal is to reach a state where the following facts hold: Ball ball3 is at room3 location, Ball ball4 is in room room1, Ball ball1 is in room room1, and Ball ball2 is in room room2. The available propositions are: (at-robby ?r ?x) - Robot ?r is in room ?x, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at ball2 room6)", "(at ball4 room4)", "(at ball3 room4)", "(carry robot1 ball3 left1)", "(at ball4 room5)", "(carry robot1 ball3 right1)", "(at ball4 room3)", "(at ball3 room6)", "(at ball4 room2)", "(at ball2 room5)", "(at ball2 room1)", "(at ball1 room2)", "(at-robby robot1 room7)", "(at ball2 room7)", "(carry robot1 ball2 right1)", "(at ball4 room7)", "(carry robot1 ball4 right1)", "(at ball1 room4)", "(carry robot1 ball1 left1)", "(at-robby robot1 room4)", "(carry robot1 ball4 left1)", "(at ball2 room4)", "(at ball3 room7)", "(at ball3 room1)", "(carry robot1 ball2 left1)", "(at ball1 room7)", "(at ball1 room5)", "(carry robot1 ball1 right1)", "(at ball3 room2)", "(at ball1 room3)"], "yes": ["(at-robby robot1 room1)", "(at-robby robot1 room2)", "(at-robby robot1 room3)", "(at-robby robot1 room6)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-7-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 room6 room7 - room)\n    (:init (at ball1 room6) (at ball2 room3) (at ball3 room5) (at ball4 room6) (at-robby robot1 room5) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
{"id": 8561521649268952999, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 3 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room2, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball2 is at room3, ball3 is at room2, ball1 is at room1. The goal is to reach a state where the following facts hold: Ball ball3 is in room room2, Ball ball1 is in room room1, Ball ball4 is in room room1, and Ball ball2 is at room1 location. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(carry robot1 ball4 left1)", "(at ball4 room2)", "(at ball3 room1)", "(carry robot1 ball2 left1)", "(carry robot1 ball1 right1)", "(at ball1 room3)", "(at ball3 room3)", "(carry robot1 ball3 left1)", "(at ball1 room2)", "(carry robot1 ball2 right1)", "(at ball2 room2)", "(carry robot1 ball3 right1)", "(at ball4 room3)", "(carry robot1 ball1 left1)"], "yes": ["(at-robby robot1 room1)", "(at-robby robot1 room3)", "(free robot1 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball2 room3) (at ball3 room2) (at-robby robot1 room2) (carry robot1 ball4 right1) (free robot1 left1))\n    (:goal (and (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1)))\n)"}
{"id": 6151988036911325259, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 5 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room5, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball4 is at room4, ball3 is at room3, ball2 is at room2. The goal is to reach a state where the following facts hold: Ball ball3 is at room3 location, Ball ball4 is at room1 location, Ball ball1 is at room1 location, and Ball ball2 is at room2 location. The available propositions are: (at-robby ?r ?x) - Robot ?r is in room ?x, (at ?o ?x) - Ball ?o is in room ?x, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at ball3 room4)", "(carry robot1 ball3 left1)", "(at ball4 room5)", "(at-robby robot1 room3)", "(carry robot1 ball3 right1)", "(at ball4 room3)", "(at-robby robot1 room2)", "(at ball4 room2)", "(at ball2 room5)", "(at ball2 room3)", "(at ball2 room1)", "(at ball1 room2)", "(carry robot1 ball2 right1)", "(at ball1 room4)", "(carry robot1 ball4 right1)", "(carry robot1 ball4 left1)", "(at ball2 room4)", "(at ball3 room1)", "(at ball3 room5)", "(carry robot1 ball2 left1)", "(at ball1 room5)", "(carry robot1 ball1 right1)", "(at ball3 room2)", "(at ball1 room3)"], "yes": ["(at-robby robot1 room1)", "(at-robby robot1 room4)", "(free robot1 left1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball2 room2) (at ball3 room3) (at ball4 room4) (at-robby robot1 room5) (carry robot1 ball1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
{"id": -744620347215734029, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 5 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball2. Additionally, ball4 is at room4, ball3 is at room3, ball1 is at room2. The goal is to reach a state where the following facts hold: Ball ball3 is at room3 location, Ball ball4 is at room1 location, Ball ball1 is in room room1, and Ball ball2 is in room room2. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is in room ?x, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at ball3 room4)", "(carry robot1 ball3 left1)", "(at ball4 room5)", "(at-robby robot1 room5)", "(at-robby robot1 room3)", "(carry robot1 ball3 right1)", "(at ball4 room3)", "(at ball4 room2)", "(at ball2 room5)", "(at ball2 room3)", "(at ball2 room1)", "(carry robot1 ball2 right1)", "(carry robot1 ball4 right1)", "(at ball1 room4)", "(carry robot1 ball1 left1)", "(carry robot1 ball4 left1)", "(at ball2 room4)", "(at ball3 room1)", "(at ball3 room5)", "(at ball1 room5)", "(carry robot1 ball1 right1)", "(at ball3 room2)", "(at ball1 room3)"], "yes": ["(at-robby robot1 room1)", "(at-robby robot1 room4)", "(free robot1 left1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room2) (at ball3 room3) (at ball4 room4) (at-robby robot1 room2) (carry robot1 ball2 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
