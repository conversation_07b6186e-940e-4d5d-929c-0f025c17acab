{"id": -1866930494136301385, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball7, and right gripper is carrying the ball ball5. Additionally, ball1, ball2, and ball4 are at room1, ball3 is at room3, ball6 is at room2. The goal is to reach a state where the following facts hold: Ball ball2 is at room1 location, Ball ball7 is at room1 location, Ball ball3 is at room1 location, Ball ball4 is in room room1, Ball ball1 is at room3 location, Ball ball6 is in room room2, and Ball ball5 is in room room2.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot robot1 is carrying the ball ball1 in the right gripper. B. Robot robot1 is carrying the ball ball3 in the left gripper. C. Robot robot1 is at room1 location. D. Robot robot1 is carrying the ball ball2 in the left gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball ball1 in the right gripper", "Robot robot1 is carrying the ball ball3 in the left gripper", "Robot robot1 is at room1 location", "Robot robot1 is carrying the ball ball2 in the left gripper"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -8393105248375561552, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball3. Additionally, ball1 is at room2, ball4 is at room1. The goal is to reach a state where the following facts hold: Ball ball2 is in room room2, Ball ball1 is at room2 location, Ball ball4 is in room room2, and Ball ball3 is at room2 location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot robot1 is carrying the ball ball4 in the left gripper. B. Robot robot1 is at room1 location. C. Robot robot1 is carrying the ball ball1 in the left gripper. D. Ball ball3 is in room room1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball ball4 in the left gripper", "Robot robot1 is at room1 location", "Robot robot1 is carrying the ball ball1 in the left gripper", "Ball ball3 is in room room1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -7608132875073545005, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is free, and right gripper is carrying the ball ball5. Additionally, ball2, ball7, ball3, and ball4 are at room1, ball1 is at room3, ball6 is at room2. The goal is to reach a state where the following facts hold: Ball ball2 is at room1 location, Ball ball7 is in room room1, Ball ball3 is at room1 location, Ball ball4 is in room room1, Ball ball1 is at room3 location, Ball ball6 is at room2 location, and Ball ball5 is at room2 location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball4 is at room3 location. B. Robot robot1 is carrying the ball ball7 in the right gripper. C. Ball ball1 is in room room2. D. Ball ball5 is in room room2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball4 is at room3 location", "Robot robot1 is carrying the ball ball7 in the right gripper", "Ball ball1 is in room room2", "Ball ball5 is in room room2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -251484871744676983, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball3. Additionally, ball1 and ball4 are at room1, ball5 and ball7 are at room3, ball6 is at room2. The goal is to reach a state where the following facts hold: Ball ball2 is in room room1, Ball ball7 is in room room1, Ball ball3 is in room room1, Ball ball4 is in room room1, Ball ball1 is at room3 location, Ball ball6 is at room2 location, and Ball ball5 is in room room2.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball2 is in room room2. B. Robot robot1 is in room room2. C. Robot robot1 is carrying the ball ball2 in the right gripper. D. Robot robot1 is carrying the ball ball4 in the left gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball2 is in room room2", "Robot robot1 is in room room2", "Robot robot1 is carrying the ball ball2 in the right gripper", "Robot robot1 is carrying the ball ball4 in the left gripper"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 368914849215493310, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3, right gripper is free, and left gripper is carrying the ball ball4. Additionally, ball1 is at room2, ball2 is at room1, ball3 is at room5. The goal is to reach a state where the following facts hold: Ball ball1 is in room room4, Ball ball4 is at room5 location, Ball ball3 is in room room4, and Ball ball2 is in room room3.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball4 is at room2 location. B. Ball ball3 is at room1 location. C. Ball ball4 is at room3 location. D. Robot robot1 is in room room2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball4 is at room2 location", "Ball ball3 is at room1 location", "Ball ball4 is at room3 location", "Robot robot1 is in room room2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -5104728610006852199, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room5, left gripper is free, and right gripper is carrying the ball ball1. Additionally, ball4 and ball3 are at room5, ball2 is at room3. The goal is to reach a state where the following facts hold: Ball ball1 is at room4 location, Ball ball4 is in room room5, Ball ball3 is in room room4, and Ball ball2 is at room3 location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball4 is at room3 location. B. Robot robot1 is at room4 location. C. Robot robot1 is at room2 location. D. Ball ball2 is at room5 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball4 is at room3 location", "Robot robot1 is at room4 location", "Robot robot1 is at room2 location", "Ball ball2 is at room5 location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -5149912426663232676, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball6, and right gripper is carrying the ball ball10. Additionally, ball11, ball9, ball2, ball5, ball4, ball14, and ball15 are at room1, ball1, ball3, ball13, and ball12 are at room2, ball8 and ball7 are at room3. The goal is to reach a state where the following facts hold: Ball ball10 is in room room3, Ball ball14 is in room room3, Ball ball1 is at room2 location, Ball ball3 is at room2 location, Ball ball2 is in room room1, Ball ball9 is at room3 location, Ball ball5 is in room room1, Ball ball4 is in room room1, Ball ball8 is in room room1, Ball ball13 is at room2 location, Ball ball11 is at room3 location, Ball ball7 is in room room3, Ball ball12 is in room room2, Ball ball6 is at room1 location, and Ball ball15 is in room room1.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball4 is in room room3. B. Robot robot1 is carrying the ball ball15 in the left gripper. C. Ball ball3 is in room room1. D. Robot robot1 is at room3 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball4 is in room room3", "Robot robot1 is carrying the ball ball15 in the left gripper", "Ball ball3 is in room room1", "Robot robot1 is at room3 location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -8909197110121940967, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is free, and right gripper is carrying the ball ball1. Additionally, ball2, ball4, and ball3 are at room2. The goal is to reach a state where the following facts hold: Ball ball2 is at room2 location, Ball ball1 is in room room2, Ball ball4 is at room2 location, and Ball ball3 is at room2 location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball1 is at room2 location. B. Robot robot1 is carrying the ball ball4 in the left gripper. C. Robot robot1 is carrying the ball ball2 in the right gripper. D. Robot robot1 is carrying the ball ball1 in the left gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball1 is at room2 location", "Robot robot1 is carrying the ball ball4 in the left gripper", "Robot robot1 is carrying the ball ball2 in the right gripper", "Robot robot1 is carrying the ball ball1 in the left gripper"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -1741791280652155792, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room10, left gripper is carrying the ball ball4, and right gripper is carrying the ball ball3. Additionally, ball1 is at room4, ball2 is at room6. The goal is to reach a state where the following facts hold: Ball ball4 is in room room9, Ball ball1 is at room8 location, Ball ball3 is in room room8, and Ball ball2 is at room6 location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball3 is in room room7. B. Robot robot1 is at room9 location. C. Ball ball2 is at room1 location. D. Ball ball1 is at room3 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball3 is in room room7", "Robot robot1 is at room9 location", "Ball ball2 is at room1 location", "Ball ball1 is at room3 location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -5782485328808204446, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room6 and both grippers are free. Additionally, ball1 is at room4, ball3 and ball2 are at room6, ball4 is at room9. The goal is to reach a state where the following facts hold: Ball ball4 is at room9 location, Ball ball1 is in room room8, Ball ball3 is at room8 location, and Ball ball2 is in room room6.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball2 is at room4 location. B. Robot robot1 is carrying the ball ball3 in the right gripper. C. Robot robot1 is at room4 location. D. Ball ball2 is at room3 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball2 is at room4 location", "Robot robot1 is carrying the ball ball3 in the right gripper", "Robot robot1 is at room4 location", "Ball ball2 is at room3 location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -2334878285180104091, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball1. Additionally, ball3 is at room3, ball4 is at room1. The goal is to reach a state where the following facts hold: Ball ball1 is at room1 location, Ball ball2 is in room room2, Ball ball4 is in room room1, and Ball ball3 is in room room3.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball3 is at room2 location. B. Ball ball1 is at room5 location. C. Robot robot1 is in room room2. D. Ball ball3 is at room1 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball3 is at room2 location", "Ball ball1 is at room5 location", "Robot robot1 is in room room2", "Ball ball3 is at room1 location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -3963414711557454580, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1 and both grippers are free. Additionally, ball2 is at room2, ball3 is at room3, ball1 is at room4, ball4 is at room1. The goal is to reach a state where the following facts hold: Ball ball1 is in room room1, Ball ball2 is in room room2, Ball ball4 is at room1 location, and Ball ball3 is at room3 location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot robot1 is at room4 location. B. Ball ball4 is at room2 location. C. Robot robot1 is in room room3. D. Robot robot1 is carrying the ball ball1 in the right gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is at room4 location", "Ball ball4 is at room2 location", "Robot robot1 is in room room3", "Robot robot1 is carrying the ball ball1 in the right gripper"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 8231539332995603905, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 7 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball3, and right gripper is carrying the ball ball1. Additionally, ball2 is at room2, ball4 is at room6. The goal is to reach a state where the following facts hold: Ball ball1 is at room1 location, Ball ball2 is in room room2, Ball ball4 is in room room1, and Ball ball3 is in room room3.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball3 is in room room7. B. Ball ball2 is at room7 location. C. Robot robot1 is at room6 location. D. Ball ball3 is in room room4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball3 is in room room7", "Ball ball2 is at room7 location", "Robot robot1 is at room6 location", "Ball ball3 is in room room4"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 3301054370614288193, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball1, and right gripper is carrying the ball ball4. Additionally, ball2 is at room2, ball3 is at room3. The goal is to reach a state where the following facts hold: Ball ball1 is at room3 location, Ball ball4 is in room room3, Ball ball2 is at room2 location, and Ball ball3 is at room3 location.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Robot robot1 is in room room3. B. Robot robot1 is carrying the ball ball3 in the right gripper. C. Ball ball2 is in room room1. D. Robot robot1 is carrying the ball ball2 in the left gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is in room room3", "Robot robot1 is carrying the ball ball3 in the right gripper", "Ball ball2 is in room room1", "Robot robot1 is carrying the ball ball2 in the left gripper"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -8592780279916377910, "group": "landmarks_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball3, and right gripper is carrying the ball ball4. Additionally, ball2 is at room1, ball1 is at room3. The goal is to reach a state where the following facts hold: Ball ball1 is at room3 location, Ball ball4 is in room room3, Ball ball2 is at room2 location, and Ball ball3 is in room room3.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Ball ball1 is in room room1. B. Robot robot1 is in room room2. C. Robot robot1 is carrying the ball ball2 in the left gripper. D. Ball ball3 is at room2 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball ball1 is in room room1", "Robot robot1 is in room room2", "Robot robot1 is carrying the ball ball2 in the left gripper", "Ball ball3 is at room2 location"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
