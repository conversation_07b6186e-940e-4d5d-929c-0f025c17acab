{"id": -6882346440064650042, "group": "progression_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball3, and right gripper is carrying the ball ball1. Additionally, ball2 and ball4 are at room2. The available propositions are: (at-robby ?r ?x) - Robot ?r is in room ?x, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Break down the outcomes of performing the action \"place the object ball1 in the room room2 using the robot robot1 with right1 gripper\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(carry robot1 ball1 right1)"], "pos": ["(at ball1 room2)", "(free robot1 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball2 room2) (at ball4 room2) (at-robby robot1 room2) (carry robot1 ball1 right1) (carry robot1 ball3 left1))\n    (:goal (and (at ball1 room2) (at ball2 room2) (at ball3 room2) (at ball4 room2)))\n)"}
{"id": 4591281845313258634, "group": "progression_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball2, ball4, and ball3 are at room2. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is in room ?x, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Break down the outcomes of performing the action \"pick up the object ball2 with the robot robot1 using the right1 gripper from the room room2\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at ball2 room2)", "(free robot1 right1)"], "pos": ["(carry robot1 ball2 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-2-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 - room)\n    (:init (at ball2 room2) (at ball3 room2) (at ball4 room2) (at-robby robot1 room2) (carry robot1 ball1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room2) (at ball3 room2) (at ball4 room2)))\n)"}
{"id": 7421075395044829051, "group": "progression_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball7, and right gripper is carrying the ball ball3. Additionally, ball5 is at room3, ball6 is at room2, ball1, ball4, and ball2 are at room1. The available propositions are: (at-robby ?r ?x) - Robot ?r is in room ?x, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Break down the outcomes of performing the action \"drop object ball3 in room room3 using right1 gripper of robot robot1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(carry robot1 ball3 right1)"], "pos": ["(at ball3 room3)", "(free robot1 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball2 room1) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at-robby robot1 room3) (carry robot1 ball3 right1) (carry robot1 ball7 left1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": 7905879832875367969, "group": "progression_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball5. Additionally, ball7, ball3, ball4, and ball2 are at room1, ball6 is at room2, ball1 is at room3. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Break down the outcomes of performing the action \"use the robot robot1 equipped with right1 gripper to retrieve the object ball3 from room room1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(free robot1 right1)", "(at ball3 room1)"], "pos": ["(carry robot1 ball3 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball6 room2) (at ball7 room1) (at-robby robot1 room1) (carry robot1 ball5 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": -3778331976204036290, "group": "progression_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball4, and right gripper is carrying the ball ball5. Additionally, ball7, ball3, and ball2 are at room1, ball6 is at room2, ball1 is at room3. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Break down the outcomes of performing the action \"drop the object ball4 in room room1 using robot robot1 with the left1 gripper\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(carry robot1 ball4 left1)"], "pos": ["(at ball4 room1)", "(free robot1 left1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball6 room2) (at ball7 room1) (at-robby robot1 room1) (carry robot1 ball4 left1) (carry robot1 ball5 right1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": 1132377106936938047, "group": "progression_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball4. Additionally, ball1 is at room4, ball3 is at room10. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Break down the outcomes of performing the action \"place the object ball2 in the room room2 from the left1 gripper of the robot robot1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(carry robot1 ball2 left1)"], "pos": ["(at ball2 room2)", "(free robot1 left1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-10-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room10 room2 room3 room4 room5 room6 room7 room8 room9 - room)\n    (:init (at ball1 room4) (at ball3 room10) (at-robby robot1 room2) (carry robot1 ball2 left1) (carry robot1 ball4 right1))\n    (:goal (and (at ball1 room8) (at ball2 room6) (at ball3 room8) (at ball4 room9)))\n)"}
{"id": -2387041943444268869, "group": "progression_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room1 and both grippers are free. Additionally, ball5, ball7, and ball1 are at room3, ball6 is at room2, ball3, ball4, and ball2 are at room1. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Break down the outcomes of performing the action \"transfer the robot robot1 from the room room1 to the room room3\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at-robby robot1 room1)"], "pos": ["(at-robby robot1 room3)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-7)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 ball5 ball6 ball7 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room3) (at ball6 room2) (at ball7 room3) (at-robby robot1 room1) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room1) (at ball3 room1) (at ball4 room1) (at ball5 room2) (at ball6 room2) (at ball7 room1)))\n)"}
{"id": -8533830830209087981, "group": "progression_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is free, and right gripper is carrying the ball ball3. Additionally, ball1 is at room2, ball4 and ball2 are at room1. The available propositions are: (at-robby ?r ?x) - Robot ?r is in room ?x, (at ?o ?x) - Ball ?o is in room ?x, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Break down the outcomes of performing the action \"place the object ball3 in the room room2 from the right1 gripper of the robot robot1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(carry robot1 ball3 right1)"], "pos": ["(at ball3 room2)", "(free robot1 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room2) (at ball2 room1) (at ball4 room1) (at-robby robot1 room2) (carry robot1 ball3 right1) (free robot1 left1))\n    (:goal (and (at ball1 room4) (at ball2 room3) (at ball3 room4) (at ball4 room5)))\n)"}
{"id": 6211073843686312714, "group": "progression_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball10. Additionally, ball13, ball1, ball3, and ball12 are at room2, ball15, ball8, ball6, ball9, ball11, ball4, ball14, and ball2 are at room1, ball5 and ball7 are at room3. The available propositions are: (at-robby ?r ?x) - Robot ?r is in room ?x, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Break down the outcomes of performing the action \"drop the object ball10 in room room1 using robot robot1 with the left1 gripper\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(carry robot1 ball10 left1)"], "pos": ["(free robot1 left1)", "(at ball10 room1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-15)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball10 ball11 ball12 ball13 ball14 ball15 ball2 ball3 ball4 ball5 ball6 ball7 ball8 ball9 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room2) (at ball11 room1) (at ball12 room2) (at ball13 room2) (at ball14 room1) (at ball15 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room3) (at ball6 room1) (at ball7 room3) (at ball8 room1) (at ball9 room1) (at-robby robot1 room1) (carry robot1 ball10 left1) (free robot1 right1))\n    (:goal (and (at ball1 room2) (at ball2 room1) (at ball3 room2) (at ball4 room1) (at ball5 room1) (at ball6 room1) (at ball7 room3) (at ball8 room1) (at ball9 room3) (at ball10 room3) (at ball11 room3) (at ball12 room2) (at ball13 room2) (at ball14 room3) (at ball15 room1)))\n)"}
{"id": 3225667408415496397, "group": "progression_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is free, and right gripper is carrying the ball ball2. Additionally, ball1 and ball3 are at room4, ball4 is at room5. The available propositions are: (at-robby ?r ?x) - Robot ?r is in room ?x, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Break down the outcomes of performing the action \"drop the object ball2 in room room2 using robot robot1 with the right1 gripper\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(carry robot1 ball2 right1)"], "pos": ["(at ball2 room2)", "(free robot1 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room4) (at ball3 room4) (at ball4 room5) (at-robby robot1 room2) (carry robot1 ball2 right1) (free robot1 left1))\n    (:goal (and (at ball1 room4) (at ball2 room3) (at ball3 room4) (at ball4 room5)))\n)"}
{"id": 8072706794802941466, "group": "progression_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 5 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room3, left gripper is free, and right gripper is carrying the ball ball3. Additionally, ball2 is at room2, ball4 is at room4, ball1 is at room1. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Break down the outcomes of performing the action \"move the robot robot1 from room room3 to room room4\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at-robby robot1 room3)"], "pos": ["(at-robby robot1 room4)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room1) (at ball2 room2) (at ball4 room4) (at-robby robot1 room3) (carry robot1 ball3 right1) (free robot1 left1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
{"id": 4215102940259487251, "group": "progression_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 3 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball2. Additionally, ball3 and ball1 are at room3, ball4 is at room1. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Break down the outcomes of performing the action \"grasp the object ball4 from room room1 with the right1 gripper of robot robot1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at ball4 room1)", "(free robot1 right1)"], "pos": ["(carry robot1 ball4 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room3) (at ball3 room3) (at ball4 room1) (at-robby robot1 room1) (carry robot1 ball2 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room2) (at ball3 room3) (at ball4 room3)))\n)"}
{"id": -1460713159736238783, "group": "progression_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 3 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room3, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball3 and ball4 are at room3, ball2 is at room2. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Break down the outcomes of performing the action \"move robot robot1 from room room3 to room room1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at-robby robot1 room3)"], "pos": ["(at-robby robot1 room1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball2 room2) (at ball3 room3) (at ball4 room3) (at-robby robot1 room3) (carry robot1 ball1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room3) (at ball2 room2) (at ball3 room3) (at ball4 room3)))\n)"}
{"id": 2034489011792928317, "group": "progression_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 7 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room4 and both grippers are free. Additionally, ball3 and ball1 are at room3, ball2 is at room2, ball4 is at room6. The available propositions are: (at-robby ?r ?x) - Robot ?r is in room ?x, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Break down the outcomes of performing the action \"move the robot robot1 from room room4 to room room7\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(at-robby robot1 room4)"], "pos": ["(at-robby robot1 room7)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-7-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 room6 room7 - room)\n    (:init (at ball1 room3) (at ball2 room2) (at ball3 room3) (at ball4 room6) (at-robby robot1 room4) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
{"id": 1460588650559783798, "group": "progression_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 3 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room3, left gripper is carrying the ball ball4, and right gripper is carrying the ball ball3. Additionally, ball1 is at room3, ball2 is at room1. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Break down the outcomes of performing the action \"drop object ball4 in room room3 using left1 gripper of robot robot1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(carry robot1 ball4 left1)"], "pos": ["(free robot1 left1)", "(at ball4 room3)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room3) (at ball2 room1) (at-robby robot1 room3) (carry robot1 ball3 right1) (carry robot1 ball4 left1))\n    (:goal (and (at ball1 room3) (at ball2 room2) (at ball3 room3) (at ball4 room3)))\n)"}
