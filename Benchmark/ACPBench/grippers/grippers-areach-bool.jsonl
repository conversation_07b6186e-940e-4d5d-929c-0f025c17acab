{"id": -413082499312830855, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball1, and right gripper is carrying the ball ball3. Additionally, ball2 and ball4 are at room1.", "question": "Is it possible to transition to a state where the action \"use robot robot1 with gripper ball3 to place the object right1 in room room2\" can be applied?", "answer": "no"}
{"id": 3276849274604368659, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball5 and ball1 are at room3, ball3, ball2, ball7, and ball4 are at room1, ball6 is at room2.", "question": "Is it possible to transition to a state where the action \"pick up the object right1 with the robot robot1 using the left1 gripper from the room room3\" can be applied?", "answer": "no"}
{"id": -7341678854950350349, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1 and both grippers are free. Additionally, ball1, ball4, and ball2 are at room2, ball3 is at room1.", "question": "Is it possible to transition to a state where the action \"fly drone robot1 from room room1 to room room2\" can be applied?", "answer": "no"}
{"id": 5313842507172118773, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball3, and right gripper is carrying the ball ball2. Additionally, ball1 is at room2, ball4 is at room1.", "question": "Is it possible to transition to a state where the action \"use the right1 gripper of robot robot1 to drop the object ball1 in room room2\" can be applied?", "answer": "yes"}
{"id": 202439922878848245, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is free, and right gripper is carrying the ball ball6. Additionally, ball3, ball12, ball1, and ball13 are at room2, ball7 and ball5 are at room3, ball11, ball8, ball9, ball2, ball15, ball10, ball4, and ball14 are at room1.", "question": "Is it possible to transition to a state where the action \"fly the drone robot1 from room room2 to room room1\" can be applied?", "answer": "no"}
{"id": -5509035351241272419, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball1, and right gripper is carrying the ball ball7. Additionally, ball5 is at room3, ball3, ball2, and ball4 are at room1, ball6 is at room2.", "question": "Is it possible to transition to a state where the action \"drop the object ball1 in room room2 using robot robot1 with the left1 gripper\" can be applied?", "answer": "yes"}
{"id": -395911225839478783, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball6, and right gripper is carrying the ball ball7. Additionally, ball3, ball2, and ball4 are at room1, ball1 is at room3, ball5 is at room2.", "question": "Is it possible to transition to a state where the action \"pick up the object ball2 with robot robot1 using left1 gripper from room room1\" can be applied?", "answer": "yes"}
{"id": -568780221617038387, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1 and both grippers are free. Additionally, ball1 and ball4 are at room2, ball3 and ball2 are at room1.", "question": "Is it possible to transition to a state where the action \"pick up the object ball3 with robot robot1 using left1 gripper from the table room1\" can be applied?", "answer": "no"}
{"id": -2528214975426279629, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball2. Additionally, ball3 and ball1 are at room4, ball4 is at room5.", "question": "Is it possible to transition to a state where the action \"use the robot robot1 equipped with right1 gripper to retrieve the object room4 from room room3\" can be applied?", "answer": "no"}
{"id": 7899061807573419434, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball10, and right gripper is carrying the ball ball3. Additionally, ball12, ball1, and ball13 are at room2, ball7, ball11, and ball5 are at room3, ball8, ball9, ball2, ball15, ball6, ball4, and ball14 are at room1.", "question": "Is it possible to transition to a state where the action \"use the left1 gripper of robot robot1 to drop the object ball10 in room room1\" can be applied?", "answer": "yes"}
{"id": 6734331229334849540, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1 and both grippers are free. Additionally, ball1 and ball2 are at room2, ball3 is at room3, ball4 is at room1.", "question": "Is it possible to transition to a state where the action \"use robot robot1 with right1 gripper to place the object room3 in room room4\" can be applied?", "answer": "no"}
{"id": 1515868292243263346, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball1 and ball2 are at room1, ball3 is at room3.", "question": "Is it possible to transition to a state where the action \"pick up object ball2 with robot robot1 using right1 gripper from room room1\" can be applied?", "answer": "yes"}
{"id": 9032896292019391631, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 7 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room7, left gripper is carrying the ball ball3, and right gripper is carrying the ball ball4. Additionally, ball2 is at room2, ball1 is at room3.", "question": "Is it possible to transition to a state where the action \"use robot robot1 with gripper ball3 to place the object left1 in room room6\" can be applied?", "answer": "no"}
{"id": -1862830603681948986, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 7 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball1, and right gripper is carrying the ball ball4. Additionally, ball3 is at room3, ball2 is at room2.", "question": "Is it possible to transition to a state where the action \"transfer the robot robot1 from the room room3 to the room room5\" can be applied?", "answer": "yes"}
{"id": -8043624263676985927, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball2. Additionally, ball1 is at room1, ball4 and ball3 are at room3.", "question": "Is it possible to transition to a state where the action \"grasp the object right1 from room room2 with the left1 gripper of robot robot1\" can be applied?", "answer": "no"}
