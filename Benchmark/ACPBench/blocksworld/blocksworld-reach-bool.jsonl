{"id": 8256941752428165977, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_19. The following block(s) are on the table: block_13, block_2, block_20, block_10, and block_6. The following block(s) are stacked on top of another block: block_3 is on block_13, block_7 is on block_10, block_1 is on block_17, block_4 is on block_6, block_14 is on block_20, block_17 is on block_12, block_8 is on block_4, block_5 is on block_1, block_12 is on block_7, block_18 is on block_5, block_11 is on block_8, block_15 is on block_9, block_16 is on block_11, and block_9 is on block_2.", "question": "Is it possible to transition to a state where the following holds: The block block_6 is currently being held by the robotic arm and The robotic arm is holding block_4?", "answer": "no"}
{"id": -1138079910160729630, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_10, block_8, and block_6. The following block(s) are stacked on top of another block: block_5 is on block_9, block_7 is on block_10, block_4 is on block_6, block_2 is on block_5, block_1 is on block_8, block_9 is on block_1, and block_3 is on block_7.", "question": "Is it possible to transition to a state where the following holds: No blocks are placed on top of block_7 and The block block_3 is currently situated above the block block_4?", "answer": "yes"}
{"id": -3782707831614811594, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_2. The following block(s) are on the table: block_4, block_3, and block_5. The following block(s) is stacked on top of another block: block_1 is on block_5.", "question": "Is it possible to transition to a state where the following holds: The block block_3 is currently situated under the block block_5 and The block block_3 is on top of block block_3?", "answer": "no"}
{"id": 2544905356212068046, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_10, block_1, block_2, and block_8. The following block(s) are stacked on top of another block: block_6 is on block_8, block_7 is on block_10, block_9 is on block_5, block_3 is on block_1, and block_5 is on block_2.", "question": "Is it possible to transition to a state where the following holds: The block block_5 is on top of block block_6 and The block block_5 is currently being held by the robotic arm?", "answer": "no"}
{"id": -3253610256607851858, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_2. The following block(s) are stacked on top of another block: block_3 is on block_2, block_4 is on block_1, and block_5 is on block_4.", "question": "Is it possible to transition to a state where the following holds: Block block_5 is located on the table and No blocks are placed on top of block_4?", "answer": "yes"}
{"id": -1214666747726733564, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_7, block_14, block_16, block_18, block_20, block_1, block_10, block_19, and block_6. The following block(s) are stacked on top of another block: block_12 is on block_5, block_3 is on block_13, block_17 is on block_12, block_11 is on block_1, block_5 is on block_14, block_13 is on block_4, block_4 is on block_11, block_2 is on block_18, block_8 is on block_7, block_15 is on block_9, and block_9 is on block_2.", "question": "Is it possible to transition to a state where the following holds: block_7 is not obstructed by any other blocks and The block block_8 is on top of block block_15?", "answer": "yes"}
{"id": -3240788156726209860, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_3 and block_1. The following block(s) are stacked on top of another block: block_4 is on block_2 and block_2 is on block_3.", "question": "Is it possible to transition to a state where the following holds: Block block_5 is clear and The block block_1 is currently being held by the robotic arm?", "answer": "yes"}
{"id": 3037388191648938111, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_19. The following block(s) are on the table: block_5, block_13, block_20, block_10, and block_6. The following block(s) are stacked on top of another block: block_3 is on block_13, block_7 is on block_10, block_1 is on block_17, block_4 is on block_6, block_14 is on block_20, block_2 is on block_14, block_17 is on block_12, block_8 is on block_4, block_12 is on block_7, block_18 is on block_5, block_11 is on block_8, block_15 is on block_9, block_16 is on block_11, and block_9 is on block_2.", "question": "Is it possible to transition to a state where the following holds: No blocks are placed on top of block_19 and No blocks are placed on top of block_5?", "answer": "yes"}
{"id": 2669741931983850416, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_3, block_1, and block_5. The following block(s) are stacked on top of another block: block_2 is on block_3 and block_4 is on block_5.", "question": "Is it possible to transition to a state where the following holds: The block block_1 is currently being held by the robotic arm and The block block_3 is currently being held by the robotic arm?", "answer": "no"}
{"id": 3001871075049492593, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_10, block_1, block_2, block_8, and block_6. The following block(s) are stacked on top of another block: block_4 is on block_9, block_7 is on block_10, block_5 is on block_2, block_9 is on block_5, and block_3 is on block_7.", "question": "Is it possible to transition to a state where the following holds: The block block_2 is on top of block block_3 and The robotic arm is holding block_2?", "answer": "no"}
{"id": -2273865423357842495, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_3. The following block(s) is on the table: block_1. The following block(s) are stacked on top of another block: block_2 is on block_5, block_4 is on block_1, and block_5 is on block_4.", "question": "Is it possible to transition to a state where the following holds: Block block_3 is on the table and Block block_3 is clear?", "answer": "yes"}
{"id": -7926943085549188920, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_5 and block_1. The following block(s) are stacked on top of another block: block_3 is on block_2, block_2 is on block_4, and block_4 is on block_5.", "question": "Is it possible to transition to a state where the following holds: The block block_1 is on top of block block_3?", "answer": "yes"}
{"id": -1753703551762064220, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_2. The following block(s) are on the table: block_3 and block_1. The following block(s) are stacked on top of another block: block_4 is on block_3 and block_5 is on block_1.", "question": "Is it possible to transition to a state where the following holds: The block block_5 is currently being held by the robotic arm and The robotic arm is empty?", "answer": "no"}
{"id": -643910057256822341, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_3 and block_2. The following block(s) are stacked on top of another block: block_1 is on block_3, block_5 is on block_2, and block_4 is on block_5.", "question": "Is it possible to transition to a state where the following holds: The block block_4 is currently situated under the block block_1 and The robotic arm is holding block_4?", "answer": "no"}
{"id": 2632670807254420405, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_3 and block_2. The following block(s) are stacked on top of another block: block_4 is on block_3 and block_1 is on block_4.", "question": "Is it possible to transition to a state where the following holds: Block block_5 is clear and The robotic arm is holding block_2?", "answer": "yes"}
