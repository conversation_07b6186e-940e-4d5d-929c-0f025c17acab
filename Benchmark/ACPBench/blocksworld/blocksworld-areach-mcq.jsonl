{"id": 7644848574864135557, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_1, block_3, and block_4. The following block(s) is stacked on top of another block: block_2 is on block_3.", "question": "Which of the following actions can eventually be applied? A. unstack object block_4 from object block_4. B. place the object block_3 on top of the object block_3. C. place the object block_2 on top of the object block_4. D. unstack object block_5 from object block_5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unstack object block_4 from object block_4", "place the object block_3 on top of the object block_3", "place the object block_2 on top of the object block_4", "unstack object block_5 from object block_5"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -2481914697002720276, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_6, block_14, block_2, block_1, block_10, block_13, block_8, block_16, and block_20. The following block(s) are stacked on top of another block: block_18 is on block_5, block_3 is on block_13, block_12 is on block_7, block_9 is on block_2, block_7 is on block_10, block_15 is on block_9, block_5 is on block_14, block_19 is on block_16, block_17 is on block_12, and block_11 is on block_1.", "question": "Which of the following actions can eventually be applied? A. stack the object block_16 on top of the object block_16. B. stack the object block_11 on top of the object block_11. C. stack the object block_17 on top of the object block_19. D. unstack the object block_8 from the object block_8.", "choices": {"label": ["A", "B", "C", "D"], "text": ["stack the object block_16 on top of the object block_16", "stack the object block_11 on top of the object block_11", "stack the object block_17 on top of the object block_19", "unstack the object block_8 from the object block_8"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 5851302287941330142, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_9. The following block(s) are on the table: block_6, block_10, block_13, block_15, and block_20. The following block(s) are stacked on top of another block: block_18 is on block_5, block_2 is on block_14, block_1 is on block_17, block_3 is on block_13, block_5 is on block_1, block_11 is on block_8, block_12 is on block_7, block_14 is on block_20, block_7 is on block_10, block_19 is on block_16, block_17 is on block_12, block_4 is on block_6, block_16 is on block_11, and block_8 is on block_4.", "question": "Which of the following actions can eventually be applied? A. unstack object block_14 from object block_14. B. unstack object block_9 from object block_2. C. place the object block_16 on top of the object block_16. D. unstack object block_1 from object block_1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unstack object block_14 from object block_14", "unstack object block_9 from object block_2", "place the object block_16 on top of the object block_16", "unstack object block_1 from object block_1"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -8305347071817214920, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_6, block_5, block_1, block_10, block_8, block_16, and block_20. The following block(s) are stacked on top of another block: block_18 is on block_5, block_2 is on block_14, block_3 is on block_13, block_12 is on block_7, block_9 is on block_2, block_14 is on block_20, block_13 is on block_4, block_7 is on block_10, block_15 is on block_9, block_19 is on block_16, block_4 is on block_11, block_17 is on block_12, and block_11 is on block_1.", "question": "Which of the following actions can eventually be applied? A. remove the object block_5 from on top of the object block_5. B. remove the object block_2 from on top of the object block_2. C. place the object block_15 on top of the object block_18. D. place the object block_11 on top of the object block_11.", "choices": {"label": ["A", "B", "C", "D"], "text": ["remove the object block_5 from on top of the object block_5", "remove the object block_2 from on top of the object block_2", "place the object block_15 on top of the object block_18", "place the object block_11 on top of the object block_11"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -8146842966636474759, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_10. The following block(s) are on the table: block_18, block_6, block_14, block_1, block_7, and block_12. The following block(s) are stacked on top of another block: block_19 is on block_3, block_3 is on block_13, block_9 is on block_15, block_13 is on block_4, block_16 is on block_20, block_20 is on block_8, block_5 is on block_14, block_4 is on block_11, block_17 is on block_12, block_8 is on block_7, block_11 is on block_1, block_2 is on block_18, and block_15 is on block_16.", "question": "Which of the following actions can eventually be applied? A. unstack object block_14 from object block_14. B. stack object block_7 on top of object block_7. C. unstack object block_9 from object block_15. D. stack object block_3 on top of object block_3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unstack object block_14 from object block_14", "stack object block_7 on top of object block_7", "unstack object block_9 from object block_15", "stack object block_3 on top of object block_3"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -1100319636333095822, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_20. The following block(s) are on the table: block_6, block_14, block_1, block_10, block_19, block_7, and block_16. The following block(s) are stacked on top of another block: block_18 is on block_5, block_3 is on block_13, block_2 is on block_12, block_9 is on block_2, block_13 is on block_4, block_15 is on block_9, block_5 is on block_14, block_12 is on block_17, block_17 is on block_3, block_4 is on block_11, block_8 is on block_7, and block_11 is on block_1.", "question": "Which of the following actions can eventually be applied? A. unstack object block_17 from object block_17. B. stack the object block_10 on top of the object block_18. C. unstack object block_13 from object block_13. D. stack the object block_5 on top of the object block_5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unstack object block_17 from object block_17", "stack the object block_10 on top of the object block_18", "unstack object block_13 from object block_13", "stack the object block_5 on top of the object block_5"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -4041748659253133232, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_7. The following block(s) are on the table: block_3, block_10, block_8, and block_2. The following block(s) are stacked on top of another block: block_1 is on block_8, block_6 is on block_1, block_9 is on block_5, block_5 is on block_2, and block_4 is on block_9.", "question": "Which of the following actions can eventually be applied? A. stack the object block_8 on top of the object block_8. B. stack the object block_3 on top of the object block_6. C. unstack the object block_10 from the object block_10. D. unstack the object block_8 from the object block_8.", "choices": {"label": ["A", "B", "C", "D"], "text": ["stack the object block_8 on top of the object block_8", "stack the object block_3 on top of the object block_6", "unstack the object block_10 from the object block_10", "unstack the object block_8 from the object block_8"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -1582908400704320277, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_3 and block_5. The following block(s) are stacked on top of another block: block_2 is on block_5, block_1 is on block_3, and block_4 is on block_1.", "question": "Which of the following actions can eventually be applied? A. place the object block_1 on top of the object block_1. B. place the object block_2 on top of the object block_2. C. place the object block_5 on top of the object block_2. D. unstack the object block_5 from the object block_5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the object block_1 on top of the object block_1", "place the object block_2 on top of the object block_2", "place the object block_5 on top of the object block_2", "unstack the object block_5 from the object block_5"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 328318336256516274, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_18, block_6, block_14, block_1, block_10, block_19, block_7, and block_12. The following block(s) are stacked on top of another block: block_3 is on block_13, block_9 is on block_2, block_13 is on block_4, block_16 is on block_20, block_20 is on block_8, block_5 is on block_14, block_4 is on block_11, block_17 is on block_12, block_8 is on block_7, block_11 is on block_1, block_2 is on block_18, and block_15 is on block_16.", "question": "Which of the following actions can eventually be applied? A. place the object block_13 on top of the object block_15. B. remove the object block_3 from on top of the object block_3. C. remove the object block_6 from on top of the object block_6. D. remove the object block_18 from on top of the object block_18.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the object block_13 on top of the object block_15", "remove the object block_3 from on top of the object block_3", "remove the object block_6 from on top of the object block_6", "remove the object block_18 from on top of the object block_18"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 111141248671009401, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_10. The following block(s) are on the table: block_1, block_8, and block_2. The following block(s) are stacked on top of another block: block_6 is on block_1, block_9 is on block_5, block_5 is on block_2, block_7 is on block_3, block_3 is on block_4, and block_4 is on block_9.", "question": "Which of the following actions can eventually be applied? A. place the object block_10 on top of the object block_10. B. unstack the object block_5 from the object block_5. C. place the object block_8 on top of the object block_8. D. unstack the object block_7 from the object block_3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the object block_10 on top of the object block_10", "unstack the object block_5 from the object block_5", "place the object block_8 on top of the object block_8", "unstack the object block_7 from the object block_3"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -716059645917413270, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_1. The following block(s) are on the table: block_3 and block_2. The following block(s) are stacked on top of another block: block_5 is on block_3 and block_4 is on block_5.", "question": "Which of the following actions can eventually be applied? A. unstack the object block_4 from the object block_4. B. unstack the object block_3 from the object block_3. C. stack object block_3 on top of object block_3. D. put down object block_4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unstack the object block_4 from the object block_4", "unstack the object block_3 from the object block_3", "stack object block_3 on top of object block_3", "put down object block_4"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 4103672920368924501, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_4 and block_2. The following block(s) are stacked on top of another block: block_1 is on block_5, block_5 is on block_2, and block_3 is on block_4.", "question": "Which of the following actions can eventually be applied? A. remove the object block_5 from on top of the object block_2. B. stack the object block_3 on top of the object block_3. C. stack the object block_1 on top of the object block_1. D. remove the object block_5 from on top of the object block_5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["remove the object block_5 from on top of the object block_2", "stack the object block_3 on top of the object block_3", "stack the object block_1 on top of the object block_1", "remove the object block_5 from on top of the object block_5"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 967973761642248792, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_1 and block_3. The following block(s) are stacked on top of another block: block_2 is on block_4 and block_4 is on block_1.", "question": "Which of the following actions can eventually be applied? A. unstack the object block_2 from the object block_2. B. stack object block_2 on top of object block_2. C. unstack the object block_1 from the object block_1. D. unstack the object block_4 from the object block_1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unstack the object block_2 from the object block_2", "stack object block_2 on top of object block_2", "unstack the object block_1 from the object block_1", "unstack the object block_4 from the object block_1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -2306369457111841398, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_3 and block_2. The following block(s) are stacked on top of another block: block_1 is on block_3 and block_4 is on block_2.", "question": "Which of the following actions can eventually be applied? A. stack object block_5 on top of object block_5. B. unstack object block_2 from object block_2. C. pick up the object block_5 from the table. D. unstack object block_5 from object block_5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["stack object block_5 on top of object block_5", "unstack object block_2 from object block_2", "pick up the object block_5 from the table", "unstack object block_5 from object block_5"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 6169506334283228747, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_2. The following block(s) are stacked on top of another block: block_3 is on block_5, block_5 is on block_2, and block_4 is on block_1.", "question": "Which of the following actions can eventually be applied? A. unstack the object block_1 from the object block_1. B. unstack the object block_3 from the object block_3. C. stack the object block_2 on top of the object block_2. D. stack the object block_1 on top of the object block_4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unstack the object block_1 from the object block_1", "unstack the object block_3 from the object block_3", "stack the object block_2 on top of the object block_2", "stack the object block_1 on top of the object block_4"]}, "query": "Which action is reachable from this state?", "answer": "D"}
