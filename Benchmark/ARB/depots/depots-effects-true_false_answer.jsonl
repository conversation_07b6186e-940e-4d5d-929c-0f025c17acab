{"question_id": "7942849b-57d1-4c41-a706-41c27559fbf0", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck2 is driven to depot0 to reach the current state. In this state, if crate2 is lifted from pallet0 at depot0 by hoist0, is it True or False that crate0 does not have crate0 on it and crate3 is not on pallet1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 is clear of any crates, crate1 is clear of any crates, crate1 is located at depot3, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor1 is where hoist5 is located, distributor1 is where pallet5 is located, distributor2 is where crate0 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is available for work, hoist3 can be found located at depot3, hoist3 is accessible, hoist4 can be found located at distributor0, hoist4 is available, hoist5 is available, hoist6 is accessible, hoist6 is located at distributor2, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 can be found located at depot3, pallet3 has crate1 on it, pallet4 is clear of any crates, pallet6 has crate0 on it, pallet6 is at distributor2, truck0 is located at depot1, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck2 is driven from depot1 to depot0 to reach the current state. In this state, if hoist0 lifts crate2 from pallet0 at depot0, is it True or False that crate0 is not on crate0 and crate3 is not on pallet1?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is empty, crate1 is situated at depot3, crate2 is empty, crate3 has no crates on it, crate3 is positioned on top of pallet5, crate2 is located at depot0, pallet1 is situated at depot1, hoist2 is located at depot2, pallet4 is situated at distributor0, crate3, hoist5, and pallet5 are all located at distributor1, crate0 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available and situated at depot1, hoist2 is available for work, hoist3 is accessible and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available, hoist6 is accessible and located at distributor2, crate2 is on pallet0, pallet0 is situated at depot0, pallet1 is empty, pallet2 is empty and situated at depot2, pallet3 has crate1 on it and is situated at depot3, pallet4 is empty, crate0 is on pallet6, pallet6 is situated at distributor2, truck0 is situated at depot1, truck1 is at distributor0, and truck2 is situated at depot1."}
{"question_id": "ed3fed71-842b-4d12-adf8-8db094df289f", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot2 from depot0 to reach the current state. In this state, if crate3 is lifted from crate2 at depot2 by hoist2, is it True or False that crate2 is clear, crate3 is not clear of any crates and hoist2 is not available for work?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 is clear of any crates, crate0 is located at distributor0, crate1 is located at depot2, crate1 is on pallet2, crate2 has crate3 on it, crate2 is on crate1, crate3 can be found located at depot2, crate3 is clear of any crates, depot1 is where pallet1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available for work, hoist3 is available for work, hoist4 is at distributor1, hoist4 is available for work, hoist5 is at distributor2, hoist5 is available, pallet0 can be found located at depot0, pallet0 is clear, pallet1 is clear of any crates, pallet2 is at depot2, pallet3 has crate0 on it, pallet3 is at distributor0, pallet4 is at distributor1, pallet4 is clear, pallet5 is at distributor2, pallet5 is clear, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck2 is driven to depot2 from depot0 to reach the current state. In this state, if crate3 is lifted from crate2 at depot2 by hoist2, is it True or False that crate2 is clear, crate3 is not clear of any crates and hoist2 is not available for work?\n\nParaphrased text: \nGiven the initial condition, the following actions are performed: truck2 is driven to depot2 from depot0 to reach the current state. In this state, if crate3 is lifted from crate2 at depot2 by hoist2, is it True or False that crate2 is clear, crate3 is not clear of any crates and hoist2 is not available for work?\n\nSince the original text is already clear and concise, the paraphrased text remains the same.", "initial_state_nl_paraphrased": "Crate0 is empty, and it is situated at distributor0. Crate1 is positioned at depot2 and is placed on pallet2. Crate2 has crate3 on top of it and is itself on crate1. Crate3 is located at depot2 and is empty. Depot1 is the location of pallet1, while depot2 is where crate2 is found. Distributor0 is the location of hoist3. Hoist0 is available and is situated at depot0. Hoist1 is at depot1 and is available, while hoist2 is at depot2 and available for work. Hoist3 is available, hoist4 is at distributor1 and available for work, and hoist5 is at distributor2 and available. Pallet0 is located at depot0 and is empty. Pallet1 is empty and pallet2 is at depot2. Pallet3 has crate0 on it and is at distributor0. Pallet4 is at distributor1 and is empty, while pallet5 is at distributor2 and empty. Truck0 is at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "3c5dd254-ee57-4277-80aa-11978740d52e", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0 to reach the current state. In this state, if crate2 is loaded by hoist0 into truck2 at depot0, is it True or False that hoist1 is available?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 is clear of any crates, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear of any crates, crate3 is on pallet5, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot2 is where pallet2 is located, depot2 is where truck0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor1 is where crate0 is located, distributor2 is where crate3 is located, hoist0 is accessible, hoist0 is at depot0, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is available for work, hoist5 is located at distributor2, pallet0 can be found located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet3 is clear, pallet4 can be found located at distributor1, pallet4 has crate0 on it, pallet5 is located at distributor2, truck1 is at distributor2 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0 to reach the current state. In this state, if hoist0 loads crate2 into truck2 at depot0, is it True or False that hoist1 is available?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is situated at depot2, crate1 is empty, crate1 is placed on pallet2, crate2 has no crates on it, crate2 is positioned on pallet0, crate3 has no crates on it, crate3 is placed on pallet5, crate2 is located at depot0, hoist1 is situated at depot1, pallet2 and truck0 are both located at depot2, hoist3 is situated at distributor0, pallet3 is also located at distributor0, crate0 is situated at distributor1, crate3 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available for use, hoist2 is available and situated at depot2, hoist3 is accessible, hoist4 is situated at distributor1 and available, hoist5 is available for use and situated at distributor2, pallet0 is located at depot0, pallet1 has no crates on it and is situated at depot1, pallet3 is empty, pallet4 is situated at distributor1 and has crate0 on it, pallet5 is situated at distributor2, truck1 is at distributor2 and truck2 is situated at depot0."}
{"question_id": "863eb4b0-53e6-4d8c-bae4-0b5b1e2d4544", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1 to reach the current state. In this state, if at depot0, hoist0 lifts crate2 off pallet0, is it True or False that crate2 is not located at depot0, crate2 is not on pallet0 and hoist0 is raising crate2?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 is on top of pallet5, crate1 is at distributor2, crate1 is clear of any crates, crate1 is on top of crate0, crate2 is clear of any crates, crate2 is located at depot0, crate2 is on pallet0, crate3 is clear, crate3 is located at depot2, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor1 is where pallet4 is located, distributor2 is where crate0 is located, distributor2 is where pallet5 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, hoist6 is located at distributor3, pallet0 is located at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet2 has crate3 on it, pallet2 is at depot2, pallet3 can be found located at distributor0, pallet3 is clear of any crates, pallet4 is clear, pallet6 is clear, pallet6 is located at distributor3, truck0 is located at distributor2 and truck2 is located at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck1 is driven from depot1 to depot0 to reach the current state. In this state, if at depot0, hoist0 lifts crate2 off pallet0, is it True or False that crate2 is no longer at depot0, crate2 is no longer on pallet0, and hoist0 is lifting crate2?", "initial_state_nl_paraphrased": "Crate0 is positioned above pallet5, crate1 is situated at distributor2, crate1 has no crates on top of it, crate1 is stacked on top of crate0, crate2 has no crates on it, crate2 is situated at depot0, crate2 is placed on pallet0, crate3 is clear of any obstructions, crate3 is located at depot2, truck1 is currently at depot1, hoist3 is situated at distributor0, pallet4 is located at distributor1, crate0 is situated at distributor2, pallet5 is also located at distributor2, hoist0 is at depot0 and is available for use, hoist1 is located at depot1 and is available for work, hoist2 is available and situated at depot2, hoist3 is accessible and located at distributor0, hoist4 is available for work and situated at distributor1, hoist5 is available and located at distributor2, hoist6 is available and situated at distributor3, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 has no crates on it, pallet2 has crate3 on it and is situated at depot2, pallet3 is located at distributor0 and has no crates on it, pallet4 has no crates on it, pallet6 is clear of any crates and situated at distributor3, truck0 is located at distributor2 and truck2 is situated at depot2."}
{"question_id": "eb137659-f94c-4087-9669-48f2de2a8a4c", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, at depot0, hoist0 lifts crate2 off pallet0, at depot0, hoist0 loads crate2 into truck1, truck1 is driven to depot1 from depot0, crate2 is unloaded by hoist1 from truck1 at depot1, crate2 is dropped on pallet1 at depot1 by hoist1, at depot2, hoist2 lifts crate3 off pallet2, hoist2 loads crate3 into truck2 at depot2, truck2 is driven to distributor3 from depot2 and hoist6 unloads crate3 from truck2 at distributor3 to reach the current state. In this state, if crate3 is dropped on pallet6 at distributor3 by hoist6, is it True or False that crate3 is clear, hoist6 is accessible and pallet6 is not clear?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 is on top of pallet5, crate1 is at distributor2, crate1 is clear of any crates, crate1 is on top of crate0, crate2 is clear of any crates, crate2 is located at depot0, crate2 is on pallet0, crate3 is clear, crate3 is located at depot2, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor1 is where pallet4 is located, distributor2 is where crate0 is located, distributor2 is where pallet5 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, hoist6 is located at distributor3, pallet0 is located at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet2 has crate3 on it, pallet2 is at depot2, pallet3 can be found located at distributor0, pallet3 is clear of any crates, pallet4 is clear, pallet6 is clear, pallet6 is located at distributor3, truck0 is located at distributor2 and truck2 is located at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 travels from depot1 to depot0, where hoist0 removes crate2 from pallet0 and then loads it into truck1, which then heads back to depot1, where hoist1 unloads crate2 from truck1 and places it on pallet1. Meanwhile, at depot2, hoist2 lifts crate3 off pallet2, loads it into truck2, and truck2 transports it to distributor3, where hoist6 unloads crate3. Now, in this state, if hoist6 places crate3 on pallet6 at distributor3, is it True or False that crate3 is clear, hoist6 is accessible, and pallet6 is not clear?", "initial_state_nl_paraphrased": "Crate0 is positioned above pallet5, crate1 is situated at distributor2, crate1 has no crates on it, crate1 is stacked on top of crate0, crate2 has no crates on it, crate2 is situated at depot0, crate2 is placed on pallet0, crate3 is clear of any obstructions, crate3 is located at depot2, truck1 is currently at depot1, hoist3 is situated at distributor0, pallet4 is located at distributor1, crate0 is situated at distributor2, pallet5 is also located at distributor2, hoist0 is positioned at depot0 and is available for use, hoist1 is located at depot1 and is available for work, hoist2 is available and situated at depot2, hoist3 is accessible and located at distributor0, hoist4 is available for work and situated at distributor1, hoist5 is available and located at distributor2, hoist6 is available and situated at distributor3, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 has no crates on it, pallet2 has crate3 on it and is situated at depot2, pallet3 is located at distributor0 and has no crates on it, pallet4 has no crates on it, pallet6 is clear of any obstructions and situated at distributor3, truck0 is located at distributor2 and truck2 is situated at depot2."}
{"question_id": "6a3da076-9459-464c-9eee-ea984a206950", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot2 from depot0, hoist2 lifts crate3 from crate2 at depot2, crate3 is loaded by hoist2 into truck2 at depot2, at depot2, hoist2 lifts crate2 off crate1, hoist2 loads crate2 into truck2 at depot2, at depot2, hoist2 lifts crate1 off pallet2, at depot2, hoist2 loads crate1 into truck2, truck2 is driven from depot2 to distributor0, crate0 is lifted from pallet3 at distributor0 by hoist3 and crate0 is loaded by hoist3 into truck2 at distributor0 to reach the current state. In this state, if crate1 is unloaded by hoist3 from truck2 at distributor0, is it True or False that pallet4 is not at distributor1?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate0 is located at distributor0, crate1 is located at depot2, crate1 is on pallet2, crate2 has crate3 on it, crate2 is on crate1, crate3 can be found located at depot2, crate3 is clear of any crates, depot1 is where pallet1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available for work, hoist3 is available for work, hoist4 is at distributor1, hoist4 is available for work, hoist5 is at distributor2, hoist5 is available, pallet0 can be found located at depot0, pallet0 is clear, pallet1 is clear of any crates, pallet2 is at depot2, pallet3 has crate0 on it, pallet3 is at distributor0, pallet4 is at distributor1, pallet4 is clear, pallet5 is at distributor2, pallet5 is clear, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 travels from depot0 to depot2, hoist2 at depot2 lifts crate3 off crate2, then hoist2 loads crate3 into truck2 at depot2, hoist2 at depot2 lifts crate2 off crate1, hoist2 loads crate2 into truck2 at depot2, hoist2 at depot2 lifts crate1 off pallet2, hoist2 loads crate1 into truck2 at depot2, and truck2 is driven from depot2 to distributor0, where hoist3 lifts crate0 off pallet3 and loads crate0 into truck2 at distributor0, resulting in the current state. In this state, if hoist3 unloads crate1 from truck2 at distributor0, is it True or False that pallet4 is not located at distributor1?", "initial_state_nl_paraphrased": "Crate0 is empty, and it is situated at distributor0. Crate1 is positioned at depot2 and is placed on pallet2. Crate2 has crate3 on top of it and is itself on crate1. Crate3 is located at depot2 and is empty. Depot1 is the location of pallet1, while depot2 is where crate2 is found. Distributor0 is the site of hoist3. Hoist0 is available for use and is situated at depot0. Hoist1 is located at depot1 and is available, while hoist2 is at depot2 and available for work. Hoist3 is also available for work. Hoist4 is at distributor1 and available for work, and hoist5 is at distributor2 and available. Pallet0 is located at depot0 and is empty. Pallet1 is empty and pallet2 is at depot2. Pallet3 has crate0 on it and is at distributor0. Pallet4 is at distributor1 and empty, while pallet5 is at distributor2 and empty. Truck0 is at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "be8fe648-3d69-4167-b0e6-4489bc9d8138", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot0, truck2 is driven to depot2, hoist2 lifts crate3 from crate2 at depot2, crate3 is loaded by hoist2 into truck2 at depot2, hoist2 lifts crate2 from crate1 at depot2, at depot2, hoist2 loads crate2 into truck2, crate1 is lifted from pallet2 at depot2 by hoist2, at depot2, hoist2 loads crate1 into truck2, from depot2, truck2 is driven to distributor0, crate0 is lifted from pallet3 at distributor0 by hoist3 and at distributor0, hoist3 loads crate0 into truck2 to reach the current state. In this state, if at distributor0, hoist3 unloads crate1 from truck2, is it True or False that hoist3 is not accessible?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate0 is located at distributor0, crate1 is located at depot2, crate1 is on pallet2, crate2 has crate3 on it, crate2 is on crate1, crate3 can be found located at depot2, crate3 is clear of any crates, depot1 is where pallet1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available for work, hoist3 is available for work, hoist4 is at distributor1, hoist4 is available for work, hoist5 is at distributor2, hoist5 is available, pallet0 can be found located at depot0, pallet0 is clear, pallet1 is clear of any crates, pallet2 is at depot2, pallet3 has crate0 on it, pallet3 is at distributor0, pallet4 is at distributor1, pallet4 is clear, pallet5 is at distributor2, pallet5 is clear, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: starting from depot0, truck2 is driven to depot2, where hoist2 lifts crate3 from crate2, then loads crate3 into truck2, lifts crate2 from crate1, and loads crate2 into truck2, lifts crate1 from pallet2, and loads crate1 into truck2. Subsequently, truck2 is driven from depot2 to distributor0. At distributor0, hoist3 lifts crate0 from pallet3 and loads crate0 into truck2, resulting in the current state. In this state, if hoist3 unloads crate1 from truck2 at distributor0, is it True or False that hoist3 is inaccessible?", "initial_state_nl_paraphrased": "Crate0 is empty, and it is situated at distributor0. Crate1 is positioned at depot2 and is placed on pallet2. Crate2 has crate3 on top of it and is itself on crate1. Crate3 is located at depot2 and is empty. Depot1 is the location of pallet1, while depot2 is where crate2 is found. Distributor0 is the site of hoist3. Hoist0 is available for use and is situated at depot0. Hoist1 is located at depot1 and is available, while hoist2 is at depot2 and available for work. Hoist3 is also available for work. Hoist4 is positioned at distributor1 and is available for work, and hoist5 is at distributor2 and available. Pallet0 is located at depot0 and is empty. Pallet1 is also empty, and pallet2 is situated at depot2. Pallet3 has crate0 on it and is at distributor0. Pallet4 is at distributor1 and is empty, while pallet5 is at distributor2 and empty. Truck0 is located at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "3748efcf-0d09-467d-b2e3-d78f0629b8a1", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot0 to depot2, at depot2, hoist2 lifts crate3 off crate2, hoist2 loads crate3 into truck2 at depot2, hoist2 lifts crate2 from crate1 at depot2, hoist2 loads crate2 into truck2 at depot2, hoist2 lifts crate1 from pallet2 at depot2, hoist2 loads crate1 into truck2 at depot2, truck2 is driven from depot2 to distributor0, crate0 is lifted from pallet3 at distributor0 by hoist3, at distributor0, hoist3 loads crate0 into truck2, at distributor0, hoist3 unloads crate1 from truck2, truck2 is driven from distributor0 to distributor1, at distributor1, hoist4 unloads crate2 from truck2, truck2 is driven to distributor2 from distributor1, hoist5 unloads crate3 from truck2 at distributor2, hoist3 drops crate1 on pallet3 at distributor0, hoist4 drops crate2 on pallet4 at distributor1, hoist5 drops crate3 on pallet5 at distributor2 and at distributor2, hoist5 unloads crate0 from truck2 to reach the current state. In this state, if at distributor2, hoist5 drops crate0 on crate3, is it True or False that crate1 is at distributor2, crate1 is in truck2 and hoist0 is not elevating crate0?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 is clear of any crates, crate0 is located at distributor0, crate1 is located at depot2, crate1 is on pallet2, crate2 has crate3 on it, crate2 is on crate1, crate3 can be found located at depot2, crate3 is clear of any crates, depot1 is where pallet1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available for work, hoist3 is available for work, hoist4 is at distributor1, hoist4 is available for work, hoist5 is at distributor2, hoist5 is available, pallet0 can be found located at depot0, pallet0 is clear, pallet1 is clear of any crates, pallet2 is at depot2, pallet3 has crate0 on it, pallet3 is at distributor0, pallet4 is at distributor1, pallet4 is clear, pallet5 is at distributor2, pallet5 is clear, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 travels from depot0 to depot2, where hoist2 unloads crate3 from crate2, loads crate3 into truck2, unloads crate2 from crate1, loads crate2 into truck2, unloads crate1 from pallet2, and loads crate1 into truck2. Then, truck2 moves from depot2 to distributor0, where hoist3 unloads crate0 from pallet3 and loads it into truck2, while unloading crate1 from truck2. Next, truck2 travels to distributor1, where hoist4 unloads crate2, and then to distributor2, where hoist5 unloads crate3. Subsequently, hoist3 places crate1 on pallet3 at distributor0, hoist4 places crate2 on pallet4 at distributor1, and hoist5 places crate3 on pallet5 at distributor2. Finally, hoist5 unloads crate0 from truck2 to reach the current state. In this state, if at distributor2, hoist5 places crate0 on crate3, is it True or False that crate1 is at distributor2, crate1 is in truck2, and hoist0 is not lifting crate0?", "initial_state_nl_paraphrased": "Crate0 is empty, and it is situated at distributor0. Crate1 is positioned at depot2 and is placed on pallet2. Crate2 has crate3 on top of it and is itself on crate1. Crate3 is located at depot2 and is empty. Depot1 is the location of pallet1, while depot2 is where crate2 is situated. Distributor0 is the location of hoist3. Hoist0 is available and is located at depot0. Hoist1 is at depot1 and is available, while hoist2 is at depot2 and available for work. Hoist3 is available for work, hoist4 is at distributor1 and available for work, and hoist5 is at distributor2 and available. Pallet0 is located at depot0 and is empty. Pallet1 is empty and pallet2 is at depot2. Pallet3 has crate0 on it and is at distributor0. Pallet4 is at distributor1 and is empty, while pallet5 is at distributor2 and empty. Truck0 is at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "3aa1d64d-b8bc-4214-b324-62805df9985b", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0, crate2 is loaded by hoist0 into truck2 at depot0, truck2 is driven from depot0 to distributor1, crate1 is lifted from pallet2 at depot2 by hoist2, at depot2, hoist2 loads crate1 into truck0, truck0 is driven to distributor0 from depot2, crate1 is unloaded by hoist3 from truck0 at distributor0, crate1 is dropped on pallet3 at distributor0 by hoist3, crate0 is lifted from pallet4 at distributor1 by hoist4 and crate0 is loaded by hoist4 into truck2 at distributor1 to reach the current state. In this state, if crate3 is lifted from pallet5 at distributor2 by hoist5, is it True or False that crate1 is not clear and pallet0 is clear of any crates?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear of any crates, crate3 is on pallet5, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot2 is where pallet2 is located, depot2 is where truck0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor1 is where crate0 is located, distributor2 is where crate3 is located, hoist0 is accessible, hoist0 is at depot0, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is available for work, hoist5 is located at distributor2, pallet0 can be found located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet3 is clear, pallet4 can be found located at distributor1, pallet4 has crate0 on it, pallet5 is located at distributor2, truck1 is at distributor2 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: at depot0, hoist0 first lifts crate2 off pallet0 and then loads it into truck2, which is then driven to distributor1. Meanwhile, at depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 and places it on pallet3 at distributor0. Additionally, hoist4 lifts crate0 from pallet4 at distributor1 and loads it into truck2. Now, considering the current state, if hoist5 lifts crate3 from pallet5 at distributor2, the question arises: is it True or False that crate1 is not clear and pallet0 is clear of any crates?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is situated at depot2, crate1 has no crates on it, crate1 is placed on pallet2, crate2 has no crates on it, crate2 is placed on pallet0, crate3 has no crates on it, crate3 is placed on pallet5, crate2 is located at depot0, hoist1 is located at depot1, pallet2 and truck0 are both located at depot2, hoist3 is located at distributor0, along with pallet3, crate0 is located at distributor1, crate3 is located at distributor2, hoist0 is accessible and situated at depot0, hoist1 is available for work, hoist2 is available and located at depot2, hoist3 is accessible, hoist4 is located at distributor1 and available, hoist5 is available for work and located at distributor2, pallet0 is located at depot0, pallet1 has no crates on it and is located at depot1, pallet3 has no crates on it, pallet4 is located at distributor1 and has crate0 on it, pallet5 is located at distributor2, truck1 is at distributor2 and truck2 is located at depot0."}
{"question_id": "21a062ca-9e09-403f-9f67-3fd1ec345336", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0, at depot0, hoist0 loads crate2 into truck2, from depot0, truck2 is driven to distributor1, crate1 is lifted from pallet2 at depot2 by hoist2, crate1 is loaded by hoist2 into truck0 at depot2, from depot2, truck0 is driven to distributor0, hoist3 unloads crate1 from truck0 at distributor0, hoist3 drops crate1 on pallet3 at distributor0, crate0 is lifted from pallet4 at distributor1 by hoist4, crate0 is loaded by hoist4 into truck2 at distributor1, at distributor2, hoist5 lifts crate3 off pallet5, hoist5 loads crate3 into truck1 at distributor2, truck1 is driven to distributor1 from distributor2, hoist4 unloads crate3 from truck1 at distributor1, at distributor1, hoist4 drops crate3 on pallet4, crate2 is unloaded by hoist4 from truck2 at distributor1, truck2 is driven from distributor1 to depot1, crate0 is unloaded by hoist1 from truck2 at depot1 and crate0 is dropped on pallet1 at depot1 by hoist1 to reach the current state. In this state, if crate2 is dropped on crate3 at distributor1 by hoist4, is it True or False that crate2 is located at distributor1, crate2 is on top of crate3 and hoist4 is not lifting crate2?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 is clear of any crates, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear of any crates, crate3 is on pallet5, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot2 is where pallet2 is located, depot2 is where truck0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor1 is where crate0 is located, distributor2 is where crate3 is located, hoist0 is accessible, hoist0 is at depot0, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is available for work, hoist5 is located at distributor2, pallet0 can be found located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet3 is clear, pallet4 can be found located at distributor1, pallet4 has crate0 on it, pallet5 is located at distributor2, truck1 is at distributor2 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: hoist0 lifts crate2 from pallet0 at depot0 and then loads it into truck2 at depot0. From depot0, truck2 is driven to distributor1. Meanwhile, at depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0. From depot2, truck0 is driven to distributor0. At distributor0, hoist3 unloads crate1 from truck0 and places it on pallet3. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2. At distributor2, hoist5 lifts crate3 from pallet5 and loads it into truck1. Truck1 is then driven from distributor2 to distributor1. At distributor1, hoist4 unloads crate3 from truck1 and places it on pallet4. Additionally, hoist4 unloads crate2 from truck2 at distributor1, and truck2 is driven from distributor1 to depot1. At depot1, hoist1 unloads crate0 from truck2 and places it on pallet1. In this state, if hoist4 drops crate2 on top of crate3 at distributor1, is it True or False that crate2 is located at distributor1, crate2 is on top of crate3, and hoist4 is not lifting crate2?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is situated at depot2, crate1 has no crates, crate1 is positioned on pallet2, crate2 has no crates, crate2 is placed on pallet0, crate3 has no crates, crate3 is positioned on pallet5, depot0 is the location of crate2, depot1 is the location of hoist1, depot2 houses pallet2, depot2 is also the location of truck0, distributor0 is home to hoist3, distributor0 is also the location of pallet3, distributor1 is the location of crate0, distributor2 is the location of crate3, hoist0 is accessible and located at depot0, hoist1 is available for work, hoist2 is available and situated at depot2, hoist3 is accessible, hoist4 is located at distributor1 and available, hoist5 is available for work and located at distributor2, pallet0 is situated at depot0, pallet1 has no crates and is located at depot1, pallet3 is clear of any crates, pallet4 is located at distributor1 and has crate0 on it, pallet5 is situated at distributor2, truck1 is at distributor2 and truck2 is located at depot0."}
{"question_id": "8f171b2e-3250-4678-93a5-d31f3c39e40f", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck2 is driven to depot0 to reach the current state. In this state, if crate2 is lifted from pallet0 at depot0 by hoist0, is it True or False that crate2 is not clear of any crates, hoist0 is not accessible and pallet0 is clear?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 is clear of any crates, crate1 is clear of any crates, crate1 is located at depot3, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor1 is where hoist5 is located, distributor1 is where pallet5 is located, distributor2 is where crate0 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is available for work, hoist3 can be found located at depot3, hoist3 is accessible, hoist4 can be found located at distributor0, hoist4 is available, hoist5 is available, hoist6 is accessible, hoist6 is located at distributor2, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 can be found located at depot3, pallet3 has crate1 on it, pallet4 is clear of any crates, pallet6 has crate0 on it, pallet6 is at distributor2, truck0 is located at depot1, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck2 is driven from depot1 to depot0 to reach the current state. In this state, if hoist0 lifts crate2 from pallet0 at depot0, is it True or False that crate2 is not clear of any crates, hoist0 is not accessible, and pallet0 is clear?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is empty, crate1 is situated at depot3, crate2 is empty, crate3 has no crates on it, crate3 is positioned on top of pallet5, crate2 is located at depot0, pallet1 is situated at depot1, hoist2 is located at depot2, pallet4 is situated at distributor0, crate3, hoist5, and pallet5 are all located at distributor1, crate0 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available and situated at depot1, hoist2 is available for work, hoist3 is accessible and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available, hoist6 is accessible and located at distributor2, crate2 is on pallet0, pallet0 is situated at depot0, pallet1 is empty, pallet2 is empty and situated at depot2, pallet3 has crate1 on it and is located at depot3, pallet4 is empty, crate0 is on pallet6, pallet6 is situated at distributor2, truck0 is situated at depot1, truck1 is at distributor0, and truck2 is situated at depot1."}
{"question_id": "638b48e0-9078-46db-8852-d5964c94b942", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot2 from depot0 to reach the current state. In this state, if crate3 is lifted from crate2 at depot2 by hoist2, is it True or False that crate2 does not have crate3 on it, crate3 cannot be found located at depot2 and hoist2 is raising crate3?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 is clear of any crates, crate0 is located at distributor0, crate1 is located at depot2, crate1 is on pallet2, crate2 has crate3 on it, crate2 is on crate1, crate3 can be found located at depot2, crate3 is clear of any crates, depot1 is where pallet1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available for work, hoist3 is available for work, hoist4 is at distributor1, hoist4 is available for work, hoist5 is at distributor2, hoist5 is available, pallet0 can be found located at depot0, pallet0 is clear, pallet1 is clear of any crates, pallet2 is at depot2, pallet3 has crate0 on it, pallet3 is at distributor0, pallet4 is at distributor1, pallet4 is clear, pallet5 is at distributor2, pallet5 is clear, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck2 is driven to depot2 from depot0 to reach the current state. In this state, if crate3 is lifted from crate2 at depot2 by hoist2, is it True or False that crate2 no longer has crate3 on it, crate3 is no longer located at depot2 and hoist2 is lifting crate3?", "initial_state_nl_paraphrased": "Crate0 is empty, and it is situated at distributor0. Crate1 is positioned at depot2 and is placed on pallet2. Crate2 has crate3 on top of it and is itself on crate1. Crate3 is located at depot2 and is empty. Depot1 is the location of pallet1, while depot2 is where crate2 is found. Distributor0 is the location of hoist3. Hoist0 is available for work and is situated at depot0. Hoist1 is at depot1 and is available, while hoist2 is at depot2 and available for work. Hoist3, hoist4, and hoist5 are all available for work, with hoist4 at distributor1 and hoist5 at distributor2. Pallet0 is located at depot0 and is empty. Pallet1 is empty and pallet2 is at depot2. Pallet3 has crate0 on it and is at distributor0. Pallet4 and pallet5 are empty, with pallet4 at distributor1 and pallet5 at distributor2. Truck0 is at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "ac5aa389-1764-48cd-9481-525b497710de", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck2 is driven to depot0, crate2 is lifted from pallet0 at depot0 by hoist0, at depot0, hoist0 loads crate2 into truck2, truck2 is driven from depot0 to distributor1, hoist3 lifts crate1 from pallet3 at depot3, hoist5 lifts crate3 from pallet5 at distributor1, at distributor1, hoist5 loads crate3 into truck2, at distributor1, hoist5 unloads crate2 from truck2, truck2 is driven to distributor2 from distributor1, hoist5 drops crate2 on pallet5 at distributor1, crate0 is lifted from pallet6 at distributor2 by hoist6, at distributor2, hoist6 loads crate0 into truck2, truck2 is driven from distributor2 to depot3, at depot3, hoist3 loads crate1 into truck2, at depot3, hoist3 unloads crate0 from truck2, truck2 is driven to distributor0 from depot3, hoist4 unloads crate3 from truck2 at distributor0, crate0 is dropped on pallet3 at depot3 by hoist3 and at distributor0, hoist4 drops crate3 on pallet4 to reach the current state. In this state, if hoist4 unloads crate1 from truck2 at distributor0, is it True or False that crate1 is not in truck2 and hoist4 is lifting crate1?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 is clear of any crates, crate1 is clear of any crates, crate1 is located at depot3, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor1 is where hoist5 is located, distributor1 is where pallet5 is located, distributor2 is where crate0 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is available for work, hoist3 can be found located at depot3, hoist3 is accessible, hoist4 can be found located at distributor0, hoist4 is available, hoist5 is available, hoist6 is accessible, hoist6 is located at distributor2, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 can be found located at depot3, pallet3 has crate1 on it, pallet4 is clear of any crates, pallet6 has crate0 on it, pallet6 is at distributor2, truck0 is located at depot1, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 is driven from depot1 to depot0, then crate2 is lifted from pallet0 at depot0 by hoist0, after which hoist0 loads crate2 into truck2 at depot0. Next, truck2 is driven from depot0 to distributor1. Meanwhile, hoist3 lifts crate1 from pallet3 at depot3, and hoist5 lifts crate3 from pallet5 at distributor1. At distributor1, hoist5 loads crate3 into truck2 and unloads crate2 from truck2. Then, truck2 is driven from distributor1 to distributor2. At distributor1, hoist5 drops crate2 on pallet5. At distributor2, crate0 is lifted from pallet6 by hoist6, which then loads crate0 into truck2. Truck2 is then driven from distributor2 to depot3. At depot3, hoist3 loads crate1 into truck2 and unloads crate0 from truck2. Subsequently, truck2 is driven from depot3 to distributor0. At distributor0, hoist4 unloads crate3 from truck2. Finally, hoist3 drops crate0 on pallet3 at depot3, and hoist4 drops crate3 on pallet4 at distributor0, resulting in the current state. In this state, if hoist4 unloads crate1 from truck2 at distributor0, is it True or False that crate1 is not in truck2 and hoist4 is lifting crate1?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is empty, crate1 is situated at depot3, crate2 is empty, crate3 has no crates on it, crate3 is positioned on top of pallet5, crate2 is located at depot0, pallet1 is situated at depot1, hoist2 is located at depot2, pallet4 is situated at distributor0, crate3, hoist5, and pallet5 are all located at distributor1, crate0 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available and situated at depot1, hoist2 is available for work, hoist3 is accessible and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available, hoist6 is accessible and located at distributor2, crate2 is on pallet0, pallet0 is situated at depot0, pallet1 is empty, pallet2 is empty and situated at depot2, pallet3 has crate1 on it and is located at depot3, pallet4 is empty, pallet6 has crate0 on it and is situated at distributor2, truck0 is situated at depot1, truck1 is at distributor0, and truck2 is situated at depot1."}
{"question_id": "52019f8c-5c58-41c1-9ec3-18d3f6b3d556", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven from depot1 to depot0 to reach the current state. In this state, if hoist0 lifts crate2 from pallet0 at depot0, is it True or False that crate1 is clear of any crates, crate3 is clear of any crates and hoist5 is not accessible?", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 is on top of pallet5, crate1 is at distributor2, crate1 is clear of any crates, crate1 is on top of crate0, crate2 is clear of any crates, crate2 is located at depot0, crate2 is on pallet0, crate3 is clear, crate3 is located at depot2, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor1 is where pallet4 is located, distributor2 is where crate0 is located, distributor2 is where pallet5 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, hoist6 is located at distributor3, pallet0 is located at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet2 has crate3 on it, pallet2 is at depot2, pallet3 can be found located at distributor0, pallet3 is clear of any crates, pallet4 is clear, pallet6 is clear, pallet6 is located at distributor3, truck0 is located at distributor2 and truck2 is located at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: truck1 travels from depot1 to depot0 to achieve the current state. In this state, if hoist0 lifts crate2 from pallet0 at depot0, is it True or False that crate1 has no crates on top of it, crate3 has no crates on top of it, and hoist5 is inaccessible?", "initial_state_nl_paraphrased": "Crate0 is positioned above pallet5, crate1 is situated at distributor2, crate1 has no crates on top of it, crate1 is stacked on top of crate0, crate2 has no crates on it, crate2 is situated at depot0, crate2 is placed on pallet0, crate3 is clear of any obstructions, crate3 is located at depot2, truck1 is currently at depot1, hoist3 is situated at distributor0, pallet4 is located at distributor1, crate0 is situated at distributor2, pallet5 is also located at distributor2, hoist0 is at depot0 and is available for use, hoist1 is located at depot1 and is available for work, hoist2 is available and situated at depot2, hoist3 is accessible and located at distributor0, hoist4 is available for work and situated at distributor1, hoist5 is available and located at distributor2, hoist6 is available and situated at distributor3, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 has no crates on it, pallet2 has crate3 on it and is situated at depot2, pallet3 is located at distributor0 and has no crates on it, pallet4 has no crates on it, pallet6 is clear of any crates and situated at distributor3, truck0 is located at distributor2 and truck2 is situated at depot2."}
{"question_id": "dd574fee-5bd0-4a12-9008-92a079c3ce44", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck2 is driven to depot0, at depot0, hoist0 lifts crate2 off pallet0, crate2 is loaded by hoist0 into truck2 at depot0, truck2 is driven from depot0 to distributor1, at depot3, hoist3 lifts crate1 off pallet3, crate3 is lifted from pallet5 at distributor1 by hoist5, hoist5 loads crate3 into truck2 at distributor1, hoist5 unloads crate2 from truck2 at distributor1, from distributor1, truck2 is driven to distributor2 and hoist5 drops crate2 on pallet5 at distributor1 to reach the current state. In this state, if hoist6 lifts crate0 from pallet6 at distributor2, is it True or False that crate0 cannot be found located at distributor2, crate3 is at distributor0 and truck1 contains crate2?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate1 is clear of any crates, crate1 is located at depot3, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor1 is where hoist5 is located, distributor1 is where pallet5 is located, distributor2 is where crate0 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is available for work, hoist3 can be found located at depot3, hoist3 is accessible, hoist4 can be found located at distributor0, hoist4 is available, hoist5 is available, hoist6 is accessible, hoist6 is located at distributor2, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 can be found located at depot3, pallet3 has crate1 on it, pallet4 is clear of any crates, pallet6 has crate0 on it, pallet6 is at distributor2, truck0 is located at depot1, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 is moved from depot1 to depot0, then at depot0, hoist0 removes crate2 from pallet0 and loads it into truck2, after which truck2 is driven to distributor1. Meanwhile, at depot3, hoist3 lifts crate1 off pallet3, and at distributor1, hoist5 lifts crate3 from pallet5 and loads it into truck2, while unloading crate2 from truck2. Then, truck2 is driven from distributor1 to distributor2, and hoist5 places crate2 on pallet5 at distributor1. In this resulting state, if hoist6 lifts crate0 from pallet6 at distributor2, is it True or False that crate0 is not located at distributor2, crate3 is at distributor0, and truck1 contains crate2?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is empty, crate1 is positioned at depot3, crate2 is empty, crate3 has no crates on it, crate3 is stacked on pallet5, crate2 is situated at depot0, pallet1 is located at depot1, hoist2 is situated at depot2, pallet4 is located at distributor0, crate3, hoist5, and pallet5 are all situated at distributor1, crate0 is located at distributor2, hoist0 is accessible and situated at depot0, hoist1 is available and located at depot1, hoist2 is available for work, hoist3 is accessible and situated at depot3, hoist4 is available and located at distributor0, hoist5 is available, hoist6 is accessible and situated at distributor2, crate2 is on pallet0, pallet0 is situated at depot0, pallet1 is empty, pallet2 is empty and situated at depot2, pallet3 has crate1 on it and is situated at depot3, pallet4 is empty, crate0 is on pallet6, pallet6 is situated at distributor2, truck0 is situated at depot1, truck1 is situated at distributor0, and truck2 is situated at depot1."}
{"question_id": "d927de51-8d9b-4c04-9ada-f01c1128ef01", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: crate2 is lifted from pallet0 at depot0 by hoist0, at depot0, hoist0 loads crate2 into truck2, from depot0, truck2 is driven to distributor1, hoist2 lifts crate1 from pallet2 at depot2, at depot2, hoist2 loads crate1 into truck0, from depot2, truck0 is driven to distributor0, at distributor0, hoist3 unloads crate1 from truck0, crate1 is dropped on pallet3 at distributor0 by hoist3, at distributor1, hoist4 lifts crate0 off pallet4, crate0 is loaded by hoist4 into truck2 at distributor1, crate3 is lifted from pallet5 at distributor2 by hoist5, at distributor2, hoist5 loads crate3 into truck1, truck1 is driven to distributor1 from distributor2, at distributor1, hoist4 unloads crate3 from truck1, crate3 is dropped on pallet4 at distributor1 by hoist4, hoist4 unloads crate2 from truck2 at distributor1, from distributor1, truck2 is driven to depot1, hoist1 unloads crate0 from truck2 at depot1 and crate0 is dropped on pallet1 at depot1 by hoist1 to reach the current state. In this state, if hoist4 drops crate2 on crate3 at distributor1, is it True or False that crate3 is not on pallet1 and truck0 can be found located at distributor0?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 is clear of any crates, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear of any crates, crate3 is on pallet5, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot2 is where pallet2 is located, depot2 is where truck0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor1 is where crate0 is located, distributor2 is where crate3 is located, hoist0 is accessible, hoist0 is at depot0, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is available for work, hoist5 is located at distributor2, pallet0 can be found located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet3 is clear, pallet4 can be found located at distributor1, pallet4 has crate0 on it, pallet5 is located at distributor2, truck1 is at distributor2 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2 at depot0, then truck2 is driven from depot0 to distributor1. Meanwhile, hoist2 lifts crate1 from pallet2 at depot2 and loads it into truck0 at depot2, after which truck0 is driven from depot2 to distributor0. At distributor0, hoist3 unloads crate1 from truck0 and places it on pallet3. At distributor1, hoist4 lifts crate0 from pallet4, loads it into truck2, and then unloads crate0 from truck2 at distributor1 is not shown but at depot1 it is shown. At distributor2, hoist5 lifts crate3 from pallet5 and loads it into truck1, which is then driven to distributor1. At distributor1, hoist4 unloads crate3 from truck1 and places it on pallet4, then unloads crate2 from truck2. Truck2 is then driven from distributor1 to depot1, where hoist1 unloads crate0 from truck2 and places it on pallet1. In this resulting state, if hoist4 drops crate2 on crate3 at distributor1, is it True or False that crate3 is not on pallet1 and truck0 is located at distributor0?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is situated at depot2, crate1 is empty, crate1 is placed on pallet2, crate2 has no crates on it, crate2 is positioned on pallet0, crate3 has no crates on it, crate3 is placed on pallet5, crate2 is located at depot0, hoist1 is situated at depot1, pallet2 and truck0 are both located at depot2, hoist3 is situated at distributor0, pallet3 is also located at distributor0, crate0 is situated at distributor1, crate3 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available for use, hoist2 is available and situated at depot2, hoist3 is accessible, hoist4 is situated at distributor1 and available, hoist5 is available for use and situated at distributor2, pallet0 is located at depot0, pallet1 has no crates on it and is situated at depot1, pallet3 is empty, pallet4 is situated at distributor1 and has crate0 on it, pallet5 is situated at distributor2, truck1 is at distributor2 and truck2 is situated at depot0."}
{"question_id": "ab3ff602-bcff-49b8-b4b6-b15bc5961bb9", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0, crate2 is loaded by hoist0 into truck2 at depot0, truck2 is driven to distributor1 from depot0, at depot2, hoist2 lifts crate1 off pallet2, crate1 is loaded by hoist2 into truck0 at depot2, from depot2, truck0 is driven to distributor0, crate1 is unloaded by hoist3 from truck0 at distributor0, at distributor0, hoist3 drops crate1 on pallet3, hoist4 lifts crate0 from pallet4 at distributor1 and crate0 is loaded by hoist4 into truck2 at distributor1 to reach the current state. In this state, if hoist5 lifts crate3 from pallet5 at distributor2, is it True or False that crate0 is not on crate0 and crate1 is not inside truck2?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear of any crates, crate3 is on pallet5, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot2 is where pallet2 is located, depot2 is where truck0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor1 is where crate0 is located, distributor2 is where crate3 is located, hoist0 is accessible, hoist0 is at depot0, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is available for work, hoist5 is located at distributor2, pallet0 can be found located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet3 is clear, pallet4 can be found located at distributor1, pallet4 has crate0 on it, pallet5 is located at distributor2, truck1 is at distributor2 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: hoist0 lifts crate2 from pallet0 at depot0, then loads it into truck2 at depot0, after which truck2 is driven from depot0 to distributor1. At depot2, hoist2 lifts crate1 off pallet2 and loads it into truck0, which is then driven from depot2 to distributor0. Upon arrival, crate1 is unloaded from truck0 by hoist3 at distributor0 and placed on pallet3. Meanwhile, hoist4 lifts crate0 from pallet4 at distributor1 and loads it into truck2 at distributor1, resulting in the current state. In this state, if hoist5 lifts crate3 from pallet5 at distributor2, is it True or False that crate0 is not on crate0 and crate1 is not inside truck2?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is situated at depot2, crate1 is empty, crate1 is placed on pallet2, crate2 has no crates on it, crate2 is positioned on pallet0, crate3 has no crates on it, crate3 is placed on pallet5, depot0 is the location of crate2, depot1 houses hoist1, depot2 is home to pallet2 and truck0, distributor0 is the location of hoist3 and pallet3, distributor1 is where crate0 is situated, distributor2 is where crate3 is located, hoist0 is accessible and situated at depot0, hoist1 is available for work, hoist2 is available and located at depot2, hoist3 is accessible, hoist4 is available and situated at distributor1, hoist5 is available for work and located at distributor2, pallet0 is situated at depot0, pallet1 is empty and located at depot1, pallet3 is clear of any crates, pallet4 is situated at distributor1 and has crate0 on it, pallet5 is located at distributor2, truck1 is at distributor2 and truck2 is situated at depot0."}
{"question_id": "81d123ce-49dd-492d-bf1a-0b68fa6e44ba", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck2 at depot0, from depot0, truck2 is driven to distributor1, crate1 is lifted from pallet2 at depot2 by hoist2, hoist2 loads crate1 into truck0 at depot2, from depot2, truck0 is driven to distributor0, at distributor0, hoist3 unloads crate1 from truck0, hoist3 drops crate1 on pallet3 at distributor0, crate0 is lifted from pallet4 at distributor1 by hoist4 and crate0 is loaded by hoist4 into truck2 at distributor1 to reach the current state. In this state, if hoist5 lifts crate3 from pallet5 at distributor2, is it True or False that crate3 is not located at distributor2, crate3 is not on pallet5 and hoist5 is lifting crate3?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear of any crates, crate3 is on pallet5, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot2 is where pallet2 is located, depot2 is where truck0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor1 is where crate0 is located, distributor2 is where crate3 is located, hoist0 is accessible, hoist0 is at depot0, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is available for work, hoist5 is located at distributor2, pallet0 can be found located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet3 is clear, pallet4 can be found located at distributor1, pallet4 has crate0 on it, pallet5 is located at distributor2, truck1 is at distributor2 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: hoist0 first lifts crate2 from pallet0 at depot0 and then loads it into truck2 at depot0. Truck2 is then driven from depot0 to distributor1. Meanwhile, at depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0. Subsequently, truck0 is driven from depot2 to distributor0. Upon arrival at distributor0, hoist3 unloads crate1 from truck0 and places it on pallet3. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2. This series of actions results in the current state. Now, if hoist5 lifts crate3 from pallet5 at distributor2, is it True or False that crate3 is no longer at distributor2, crate3 is no longer on pallet5, and hoist5 is in the process of lifting crate3?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is situated at depot2, crate1 is empty, crate1 is placed on pallet2, crate2 has no crates on it, crate2 is positioned on pallet0, crate3 is empty, crate3 is placed on pallet5, crate2 is located at depot0, hoist1 is situated at depot1, pallet2 and truck0 are both located at depot2, hoist3 is situated at distributor0, pallet3 is also located at distributor0, crate0 is situated at distributor1, crate3 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available for work, hoist2 is available and situated at depot2, hoist3 is accessible, hoist4 is situated at distributor1 and available, hoist5 is available for work and situated at distributor2, pallet0 is located at depot0, pallet1 has no crates on it and is situated at depot1, pallet3 is empty, pallet4 is situated at distributor1 and has crate0 on it, pallet5 is situated at distributor2, truck1 is at distributor2 and truck2 is situated at depot0."}
{"question_id": "6a17b9d8-372a-40c4-b0a7-fcceb7dba657", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot0 to depot2, crate3 is lifted from crate2 at depot2 by hoist2, at depot2, hoist2 loads crate3 into truck2, hoist2 lifts crate2 from crate1 at depot2, at depot2, hoist2 loads crate2 into truck2, hoist2 lifts crate1 from pallet2 at depot2, crate1 is loaded by hoist2 into truck2 at depot2, truck2 is driven from depot2 to distributor0, at distributor0, hoist3 lifts crate0 off pallet3, at distributor0, hoist3 loads crate0 into truck2, at distributor0, hoist3 unloads crate1 from truck2, truck2 is driven to distributor1 from distributor0, hoist4 unloads crate2 from truck2 at distributor1, from distributor1, truck2 is driven to distributor2, hoist5 unloads crate3 from truck2 at distributor2, at distributor0, hoist3 drops crate1 on pallet3, crate2 is dropped on pallet4 at distributor1 by hoist4, at distributor2, hoist5 drops crate3 on pallet5 and crate0 is unloaded by hoist5 from truck2 at distributor2 to reach the current state. In this state, if hoist5 drops crate0 on crate3 at distributor2, is it True or False that crate0 is clear of any crates, crate3 is not clear of any crates and hoist5 is available?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 is clear of any crates, crate0 is located at distributor0, crate1 is located at depot2, crate1 is on pallet2, crate2 has crate3 on it, crate2 is on crate1, crate3 can be found located at depot2, crate3 is clear of any crates, depot1 is where pallet1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available for work, hoist3 is available for work, hoist4 is at distributor1, hoist4 is available for work, hoist5 is at distributor2, hoist5 is available, pallet0 can be found located at depot0, pallet0 is clear, pallet1 is clear of any crates, pallet2 is at depot2, pallet3 has crate0 on it, pallet3 is at distributor0, pallet4 is at distributor1, pallet4 is clear, pallet5 is at distributor2, pallet5 is clear, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 travels from depot0 to depot2, where hoist2 lifts crate3 from crate2, then loads crate3 into truck2, lifts crate2 from crate1, loads crate2 into truck2, lifts crate1 from pallet2, and loads crate1 into truck2. Subsequently, truck2 is driven to distributor0, where hoist3 lifts crate0 from pallet3, loads crate0 into truck2, and unloads crate1 from truck2. Truck2 then proceeds to distributor1, where hoist4 unloads crate2, and then to distributor2, where hoist5 unloads crate3. Finally, hoist3 places crate1 on pallet3 at distributor0, hoist4 places crate2 on pallet4 at distributor1, and hoist5 places crate3 on pallet5 at distributor2, and also unloads crate0 from truck2 at distributor2, resulting in the current state. In this state, if hoist5 places crate0 on crate3 at distributor2, is it True or False that crate0 is clear of any crates, crate3 is not clear of any crates, and hoist5 is available?", "initial_state_nl_paraphrased": "Crate0 is empty, and it is situated at distributor0. Crate1 is positioned at depot2 and is placed on pallet2. Crate2 has crate3 on top of it and is itself on crate1. Crate3 is located at depot2 and is empty. Depot1 is the location of pallet1, while depot2 is where crate2 is found. Distributor0 is the site of hoist3. Hoist0 is available for work and is located at depot0. Hoist1 is at depot1 and is available, while hoist2 is at depot2 and available for work. Hoist3 is also available for work. Hoist4 is at distributor1 and available for work, and hoist5 is at distributor2 and available. Pallet0 is located at depot0 and is empty. Pallet1 is empty and pallet2 is at depot2. Pallet3 has crate0 on it and is at distributor0. Pallet4 is at distributor1 and empty, while pallet5 is at distributor2 and empty. Truck0 is at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "0b55083a-2020-438a-ae84-208ede179d40", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, at depot0, hoist0 lifts crate2 off pallet0, at depot0, hoist0 loads crate2 into truck1, from depot0, truck1 is driven to depot1, crate2 is unloaded by hoist1 from truck1 at depot1, at depot1, hoist1 drops crate2 on pallet1, crate3 is lifted from pallet2 at depot2 by hoist2, hoist2 loads crate3 into truck2 at depot2, from depot2, truck2 is driven to distributor3, crate3 is unloaded by hoist6 from truck2 at distributor3, crate3 is dropped on pallet6 at distributor3 by hoist6, hoist5 lifts crate1 from crate0 at distributor2, at distributor2, hoist5 loads crate1 into truck0, at distributor2, hoist5 lifts crate0 off pallet5, hoist5 loads crate0 into truck0 at distributor2, crate1 is unloaded by hoist5 from truck0 at distributor2, truck0 is driven to distributor0 from distributor2, hoist3 unloads crate0 from truck0 at distributor0 and hoist3 drops crate0 on pallet3 at distributor0 to reach the current state. In this state, if crate1 is dropped on pallet5 at distributor2 by hoist5, is it True or False that hoist3 is raising crate2 and pallet5 is not located at distributor1?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 is on top of pallet5, crate1 is at distributor2, crate1 is clear of any crates, crate1 is on top of crate0, crate2 is clear of any crates, crate2 is located at depot0, crate2 is on pallet0, crate3 is clear, crate3 is located at depot2, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor1 is where pallet4 is located, distributor2 is where crate0 is located, distributor2 is where pallet5 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, hoist6 is located at distributor3, pallet0 is located at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet2 has crate3 on it, pallet2 is at depot2, pallet3 can be found located at distributor0, pallet3 is clear of any crates, pallet4 is clear, pallet6 is clear, pallet6 is located at distributor3, truck0 is located at distributor2 and truck2 is located at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck1 is driven from depot1 to depot0, where hoist0 unloads crate2 from pallet0 and loads it into truck1, then truck1 is driven back to depot1, and hoist1 unloads crate2 from truck1 and places it on pallet1 at depot1. Meanwhile, hoist2 lifts crate3 from pallet2 at depot2 and loads it into truck2, which is then driven to distributor3, where hoist6 unloads crate3 and places it on pallet6. At distributor2, hoist5 lifts crate1 from crate0 and loads it into truck0, along with crate0, which is lifted from pallet5. After unloading crate1 from truck0 at distributor2, truck0 is driven to distributor0, where hoist3 unloads crate0 and places it on pallet3. Given this final state, if hoist5 drops crate1 on pallet5 at distributor2, is it True or False that hoist3 is lifting crate2 and pallet5 is not located at distributor1?", "initial_state_nl_paraphrased": "Crate0 is positioned above pallet5, crate1 is situated at distributor2, crate1 has no crates on it, crate1 is stacked on top of crate0, crate2 has no crates on it, crate2 is situated at depot0, crate2 is placed on pallet0, crate3 is clear of any obstructions, crate3 is located at depot2, truck1 is currently at depot1, hoist3 is situated at distributor0, pallet4 is located at distributor1, crate0 is situated at distributor2, pallet5 is also located at distributor2, hoist0 is positioned at depot0 and is available for use, hoist1 is located at depot1 and is available for work, hoist2 is available and situated at depot2, hoist3 is accessible and located at distributor0, hoist4 is available for work and situated at distributor1, hoist5 is available and located at distributor2, hoist6 is available and situated at distributor3, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 has no crates on it, pallet2 has crate3 on it and is situated at depot2, pallet3 is located at distributor0 and has no crates on it, pallet4 has no crates on it, pallet6 is clear of any obstructions and situated at distributor3, truck0 is located at distributor2 and truck2 is situated at depot2."}
{"question_id": "ea0bb974-8070-4161-8b9f-f661dae0ee8e", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot0 to depot2, at depot2, hoist2 lifts crate3 off crate2, at depot2, hoist2 loads crate3 into truck2, hoist2 lifts crate2 from crate1 at depot2, crate2 is loaded by hoist2 into truck2 at depot2, crate1 is lifted from pallet2 at depot2 by hoist2, at depot2, hoist2 loads crate1 into truck2, truck2 is driven to distributor0 from depot2, hoist3 lifts crate0 from pallet3 at distributor0 and crate0 is loaded by hoist3 into truck2 at distributor0 to reach the current state. In this state, if crate1 is unloaded by hoist3 from truck2 at distributor0, is it True or False that hoist3 is elevating crate1 and truck2 does not contain crate1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate0 is located at distributor0, crate1 is located at depot2, crate1 is on pallet2, crate2 has crate3 on it, crate2 is on crate1, crate3 can be found located at depot2, crate3 is clear of any crates, depot1 is where pallet1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available for work, hoist3 is available for work, hoist4 is at distributor1, hoist4 is available for work, hoist5 is at distributor2, hoist5 is available, pallet0 can be found located at depot0, pallet0 is clear, pallet1 is clear of any crates, pallet2 is at depot2, pallet3 has crate0 on it, pallet3 is at distributor0, pallet4 is at distributor1, pallet4 is clear, pallet5 is at distributor2, pallet5 is clear, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 travels from depot0 to depot2, where hoist2 unloads crate3 from crate2, then loads crate3 into truck2, next lifts crate2 from crate1, loads crate2 into truck2, lifts crate1 from pallet2, and loads crate1 into truck2, all at depot2. Subsequently, truck2 is driven to distributor0. At distributor0, hoist3 unloads crate0 from pallet3 and loads it into truck2, resulting in the current state. In this state, if crate1 is unloaded from truck2 by hoist3 at distributor0, is it True or False that hoist3 is lifting crate1 and crate1 is no longer in truck2?", "initial_state_nl_paraphrased": "Crate0 is empty, and it is situated at distributor0. Crate1 is positioned at depot2 and is placed on pallet2. Crate2 has crate3 on top of it and is itself on crate1. Crate3 is located at depot2 and is empty. Depot1 is the location of pallet1, while depot2 is where crate2 is found. Distributor0 is the location of hoist3. Hoist0 is available for use and is situated at depot0. Hoist1 is at depot1 and is available, while hoist2 is at depot2 and available for work. Hoist3 is also available for work. Hoist4 is at distributor1 and available for work, and hoist5 is at distributor2 and available. Pallet0 is located at depot0 and is empty. Pallet1 is empty and pallet2 is at depot2. Pallet3 has crate0 on it and is at distributor0. Pallet4 is at distributor1 and empty, while pallet5 is at distributor2 and empty. Truck0 is at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "4cfebfc1-c306-46f4-b1ff-2c78bba61d15", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1, at depot0, hoist0 lifts crate2 off pallet0, at depot0, hoist0 loads crate2 into truck1, truck1 is driven from depot0 to depot1, crate2 is unloaded by hoist1 from truck1 at depot1, hoist1 drops crate2 on pallet1 at depot1, crate3 is lifted from pallet2 at depot2 by hoist2, crate3 is loaded by hoist2 into truck2 at depot2, from depot2, truck2 is driven to distributor3, hoist6 unloads crate3 from truck2 at distributor3, crate3 is dropped on pallet6 at distributor3 by hoist6, at distributor2, hoist5 lifts crate1 off crate0, hoist5 loads crate1 into truck0 at distributor2, at distributor2, hoist5 lifts crate0 off pallet5, hoist5 loads crate0 into truck0 at distributor2, hoist5 unloads crate1 from truck0 at distributor2, truck0 is driven to distributor0 from distributor2, crate0 is unloaded by hoist3 from truck0 at distributor0 and crate0 is dropped on pallet3 at distributor0 by hoist3 to reach the current state. In this state, if hoist5 drops crate1 on pallet5 at distributor2, is it True or False that crate1 can be found located at distributor2, hoist5 is not lifting crate1 and pallet5 has crate1 on it?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 is on top of pallet5, crate1 is at distributor2, crate1 is clear of any crates, crate1 is on top of crate0, crate2 is clear of any crates, crate2 is located at depot0, crate2 is on pallet0, crate3 is clear, crate3 is located at depot2, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor1 is where pallet4 is located, distributor2 is where crate0 is located, distributor2 is where pallet5 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, hoist6 is located at distributor3, pallet0 is located at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet2 has crate3 on it, pallet2 is at depot2, pallet3 can be found located at distributor0, pallet3 is clear of any crates, pallet4 is clear, pallet6 is clear, pallet6 is located at distributor3, truck0 is located at distributor2 and truck2 is located at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 travels from depot1 to depot0, where hoist0 removes crate2 from pallet0 and loads it into truck1. Then, truck1 is driven back to depot1, where hoist1 unloads crate2 and places it on pallet1. Meanwhile, at depot2, hoist2 lifts crate3 from pallet2 and loads it into truck2, which then drives to distributor3. At distributor3, hoist6 unloads crate3 from truck2 and places it on pallet6. At distributor2, hoist5 lifts crate1 off crate0 and loads it into truck0, followed by lifting crate0 off pallet5 and loading it into truck0. However, hoist5 then unloads crate1 from truck0. Truck0 then travels to distributor0, where hoist3 unloads crate0 and places it on pallet3. In this resulting state, if hoist5 were to drop crate1 on pallet5 at distributor2, would it be true or false that crate1 is located at distributor2, hoist5 is not lifting crate1, and pallet5 has crate1 on it?", "initial_state_nl_paraphrased": "Crate0 is positioned above pallet5, crate1 is situated at distributor2, crate1 has no crates on top of it, crate1 is stacked on top of crate0, crate2 has no crates on it, crate2 is situated at depot0, crate2 is placed on pallet0, crate3 is clear of any obstructions, crate3 is located at depot2, truck1 is currently at depot1, hoist3 is situated at distributor0, pallet4 is located at distributor1, crate0 is situated at distributor2, pallet5 is also located at distributor2, hoist0 is positioned at depot0 and is available for use, hoist1 is located at depot1 and is available for work, hoist2 is available and situated at depot2, hoist3 is accessible and located at distributor0, hoist4 is available for work and situated at distributor1, hoist5 is available and located at distributor2, hoist6 is available and situated at distributor3, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 has no crates on it, pallet2 has crate3 on it and is situated at depot2, pallet3 is located at distributor0 and has no crates on it, pallet4 has no crates on it, pallet6 is clear of any crates and situated at distributor3, truck0 is located at distributor2 and truck2 is situated at depot2."}
{"question_id": "e3eea648-e603-4a50-9c5f-9d47631330be", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0 to reach the current state. In this state, if hoist0 loads crate2 into truck2 at depot0, is it True or False that crate2 is in truck2 and hoist0 is not lifting crate2?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 is clear of any crates, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear of any crates, crate3 is on pallet5, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot2 is where pallet2 is located, depot2 is where truck0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor1 is where crate0 is located, distributor2 is where crate3 is located, hoist0 is accessible, hoist0 is at depot0, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is available for work, hoist5 is located at distributor2, pallet0 can be found located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet3 is clear, pallet4 can be found located at distributor1, pallet4 has crate0 on it, pallet5 is located at distributor2, truck1 is at distributor2 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0 to reach the current state. In this state, if hoist0 loads crate2 into truck2 at depot0, is it True or False that crate2 is in truck2 and hoist0 is no longer lifting crate2?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is situated at depot2, crate1 is empty, crate1 is placed on pallet2, crate2 has no crates on it, crate2 is positioned on pallet0, crate3 has no crates on it, crate3 is placed on pallet5, crate2 is located at depot0, hoist1 is situated at depot1, pallet2 and truck0 are both located at depot2, hoist3 is situated at distributor0, along with pallet3, crate0 is located at distributor1, crate3 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available for use, hoist2 is available and situated at depot2, hoist3 is accessible, hoist4 is located at distributor1 and available, hoist5 is available for use and situated at distributor2, pallet0 is located at depot0, pallet1 has no crates on it and is situated at depot1, pallet3 is empty, pallet4 is located at distributor1 and has crate0 on it, pallet5 is situated at distributor2, truck1 is at distributor2 and truck2 is located at depot0."}
{"question_id": "2717e69f-e15f-4479-b06f-ee6969317850", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot1 to depot0, at depot0, hoist0 lifts crate2 off pallet0, crate2 is loaded by hoist0 into truck2 at depot0, from depot0, truck2 is driven to distributor1, crate1 is lifted from pallet3 at depot3 by hoist3, crate3 is lifted from pallet5 at distributor1 by hoist5, at distributor1, hoist5 loads crate3 into truck2, at distributor1, hoist5 unloads crate2 from truck2, from distributor1, truck2 is driven to distributor2 and hoist5 drops crate2 on pallet5 at distributor1 to reach the current state. In this state, if hoist6 lifts crate0 from pallet6 at distributor2, is it True or False that crate0 is not clear of any crates, crate1 is clear of any crates and crate3 is not clear of any crates?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate1 is clear of any crates, crate1 is located at depot3, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor1 is where hoist5 is located, distributor1 is where pallet5 is located, distributor2 is where crate0 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is available for work, hoist3 can be found located at depot3, hoist3 is accessible, hoist4 can be found located at distributor0, hoist4 is available, hoist5 is available, hoist6 is accessible, hoist6 is located at distributor2, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 can be found located at depot3, pallet3 has crate1 on it, pallet4 is clear of any crates, pallet6 has crate0 on it, pallet6 is at distributor2, truck0 is located at depot1, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 travels from depot1 to depot0, where hoist0 removes crate2 from pallet0 and loads it into truck2, then truck2 proceeds to distributor1. Meanwhile, hoist3 lifts crate1 from pallet3 at depot3, and hoist5 lifts crate3 from pallet5 at distributor1. At distributor1, hoist5 loads crate3 into truck2 and unloads crate2, which is then placed on pallet5 by hoist5. Subsequently, truck2 drives to distributor2. In this resulting state, if hoist6 lifts crate0 from pallet6 at distributor2, is it True or False that crate0 is not clear of any crates, crate1 is clear of any crates, and crate3 is not clear of any crates?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 has no crates on it, crate1 is positioned at depot3, crate2 has no crates on it, crate3 has no crates on it, crate3 is positioned on top of pallet5, crate2 is situated at depot0, pallet1 is situated at depot1, hoist2 is situated at depot2, pallet4 is situated at distributor0, crate3 is situated at distributor1, hoist5 is situated at distributor1, pallet5 is situated at distributor1, crate0 is situated at distributor2, hoist0 is accessible and situated at depot0, hoist1 is situated at depot1 and available, hoist2 is available for work, hoist3 is situated at depot3 and accessible, hoist4 is situated at distributor0 and available, hoist5 is available, hoist6 is accessible and situated at distributor2, crate2 is on pallet0, pallet0 is situated at depot0, pallet1 has no crates on it, pallet2 has no crates on it, pallet2 is situated at depot2, pallet3 is situated at depot3 and has crate1 on it, pallet4 has no crates on it, crate0 is on pallet6, pallet6 is situated at distributor2, depot1 is where truck0 is located, distributor0 is where truck1 is located and depot1 is where truck2 is located."}
{"question_id": "f427b505-32af-4dbf-bea0-ae04d5bbc1c6", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: crate2 is lifted from pallet0 at depot0 by hoist0, at depot0, hoist0 loads crate2 into truck2, truck2 is driven from depot0 to distributor1, crate1 is lifted from pallet2 at depot2 by hoist2, at depot2, hoist2 loads crate1 into truck0, truck0 is driven to distributor0 from depot2, at distributor0, hoist3 unloads crate1 from truck0, crate1 is dropped on pallet3 at distributor0 by hoist3, at distributor1, hoist4 lifts crate0 off pallet4 and crate0 is loaded by hoist4 into truck2 at distributor1 to reach the current state. In this state, if at distributor2, hoist5 lifts crate3 off pallet5, is it True or False that crate3 is not clear, hoist5 is not available for work and pallet5 is clear of any crates?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear of any crates, crate3 is on pallet5, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot2 is where pallet2 is located, depot2 is where truck0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor1 is where crate0 is located, distributor2 is where crate3 is located, hoist0 is accessible, hoist0 is at depot0, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is available for work, hoist5 is located at distributor2, pallet0 can be found located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet3 is clear, pallet4 can be found located at distributor1, pallet4 has crate0 on it, pallet5 is located at distributor2, truck1 is at distributor2 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: hoist0 at depot0 lifts crate2 from pallet0 and loads it into truck2, then truck2 is driven to distributor1 from depot0. Meanwhile, at depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 at distributor0 and places it on pallet3. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2 to reach the current state. In this state, if at distributor2, hoist5 lifts crate3 from pallet5, is it True or False that crate3 is not clear, hoist5 is not available for work, and pallet5 is clear of any crates?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is situated at depot2, crate1 is empty, crate1 is placed on pallet2, crate2 has no crates on it, crate2 is positioned on pallet0, crate3 has no crates on it, crate3 is placed on pallet5, depot0 is the location of crate2, depot1 is the location of hoist1, depot2 houses pallet2, depot2 is also the location of truck0, distributor0 is home to hoist3, distributor0 is also the location of pallet3, distributor1 is where crate0 is situated, distributor2 is where crate3 is located, hoist0 is accessible and situated at depot0, hoist1 is available for work, hoist2 is available and located at depot2, hoist3 is accessible, hoist4 is located at distributor1 and available, hoist5 is available for work and situated at distributor2, pallet0 is located at depot0, pallet1 has no crates on it and is situated at depot1, pallet3 is empty, pallet4 is located at distributor1 and has crate0 on it, pallet5 is situated at distributor2, truck1 is at distributor2 and truck2 is located at depot0."}
{"question_id": "8c1ac783-5e1a-4860-9b4d-3130469143c3", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot0, truck2 is driven to depot2, hoist2 lifts crate3 from crate2 at depot2, crate3 is loaded by hoist2 into truck2 at depot2, hoist2 lifts crate2 from crate1 at depot2, at depot2, hoist2 loads crate2 into truck2, at depot2, hoist2 lifts crate1 off pallet2, at depot2, hoist2 loads crate1 into truck2, truck2 is driven to distributor0 from depot2, crate0 is lifted from pallet3 at distributor0 by hoist3 and crate0 is loaded by hoist3 into truck2 at distributor0 to reach the current state. In this state, if crate1 is unloaded by hoist3 from truck2 at distributor0, is it True or False that hoist3 is not available for work?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate0 is located at distributor0, crate1 is located at depot2, crate1 is on pallet2, crate2 has crate3 on it, crate2 is on crate1, crate3 can be found located at depot2, crate3 is clear of any crates, depot1 is where pallet1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available for work, hoist3 is available for work, hoist4 is at distributor1, hoist4 is available for work, hoist5 is at distributor2, hoist5 is available, pallet0 can be found located at depot0, pallet0 is clear, pallet1 is clear of any crates, pallet2 is at depot2, pallet3 has crate0 on it, pallet3 is at distributor0, pallet4 is at distributor1, pallet4 is clear, pallet5 is at distributor2, pallet5 is clear, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: starting from depot0, truck2 is driven to depot2, where hoist2 lifts crate3 from crate2, then loads crate3 into truck2, lifts crate2 from crate1, loads crate2 into truck2, lifts crate1 from pallet2, and loads crate1 into truck2, all at depot2. Subsequently, truck2 is driven from depot2 to distributor0. At distributor0, hoist3 lifts crate0 from pallet3 and loads it into truck2, resulting in the current state. In this state, if crate1 is unloaded from truck2 by hoist3 at distributor0, is it True or False that hoist3 is not available for work?", "initial_state_nl_paraphrased": "Crate0 is empty, and it is situated at distributor0. Crate1 is positioned at depot2 and is placed on pallet2. Crate2 has crate3 on top of it and is itself on crate1. Crate3 is located at depot2 and is empty. Depot1 is the location of pallet1, while depot2 is where crate2 is found. Distributor0 is the location of hoist3. Hoist0 is available and situated at depot0. Hoist1 is at depot1 and available for use. Hoist2 is at depot2 and available for work. Hoist3 is available, while hoist4 is at distributor1 and also available for work. Hoist5 is at distributor2 and available. Pallet0 is located at depot0 and empty. Pallet1 is empty and located at depot1. Pallet2 is at depot2. Pallet3 has crate0 on it and is situated at distributor0. Pallet4 is at distributor1 and empty, while pallet5 is at distributor2 and also empty. Truck0 is at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "a1c9fabc-5c1e-4c3c-9c8b-a1a293b4482c", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck2 is driven to depot0, hoist0 lifts crate2 from pallet0 at depot0, crate2 is loaded by hoist0 into truck2 at depot0, from depot0, truck2 is driven to distributor1, hoist3 lifts crate1 from pallet3 at depot3, at distributor1, hoist5 lifts crate3 off pallet5, hoist5 loads crate3 into truck2 at distributor1, at distributor1, hoist5 unloads crate2 from truck2, truck2 is driven to distributor2 from distributor1 and hoist5 drops crate2 on pallet5 at distributor1 to reach the current state. In this state, if at distributor2, hoist6 lifts crate0 off pallet6, is it True or False that crate0 cannot be found located at distributor2, crate0 is not on pallet6 and hoist6 is lifting crate0?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate1 is clear of any crates, crate1 is located at depot3, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor1 is where hoist5 is located, distributor1 is where pallet5 is located, distributor2 is where crate0 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is available for work, hoist3 can be found located at depot3, hoist3 is accessible, hoist4 can be found located at distributor0, hoist4 is available, hoist5 is available, hoist6 is accessible, hoist6 is located at distributor2, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 can be found located at depot3, pallet3 has crate1 on it, pallet4 is clear of any crates, pallet6 has crate0 on it, pallet6 is at distributor2, truck0 is located at depot1, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 is driven from depot1 to depot0, then hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2. Next, truck2 is driven from depot0 to distributor1. At distributor1, hoist5 unloads crate3 from pallet5 and loads it into truck2, while also unloading crate2 from truck2. Meanwhile, hoist3 lifts crate1 from pallet3 at depot3. Finally, truck2 is driven from distributor1 to distributor2, and hoist5 places crate2 on pallet5 at distributor1. In this resulting state, if hoist6 lifts crate0 off pallet6 at distributor2, is it True or False that crate0 is not located at distributor2, crate0 is not on pallet6, and hoist6 is lifting crate0?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is empty, crate1 is situated at depot3, crate2 is empty, crate3 has no crates on it, crate3 is positioned on top of pallet5, crate2 is located at depot0, pallet1 is situated at depot1, hoist2 is located at depot2, pallet4 is situated at distributor0, crate3, hoist5, and pallet5 are all located at distributor1, crate0 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available and situated at depot1, hoist2 is available for work, hoist3 is accessible and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available, hoist6 is accessible and located at distributor2, crate2 is on pallet0, pallet0 is situated at depot0, pallet1 is empty, pallet2 is empty and situated at depot2, pallet3 has crate1 on it and is located at depot3, pallet4 is empty, crate0 is on pallet6, pallet6 is situated at distributor2, truck0 is situated at depot1, truck1 is at distributor0, and truck2 is situated at depot1."}
{"question_id": "6e2432e6-abc7-4787-a4ba-fe6c38a1b57a", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck2 is driven to depot0, crate2 is lifted from pallet0 at depot0 by hoist0, at depot0, hoist0 loads crate2 into truck2, truck2 is driven to distributor1 from depot0, at depot3, hoist3 lifts crate1 off pallet3, crate3 is lifted from pallet5 at distributor1 by hoist5, crate3 is loaded by hoist5 into truck2 at distributor1, hoist5 unloads crate2 from truck2 at distributor1, truck2 is driven from distributor1 to distributor2, crate2 is dropped on pallet5 at distributor1 by hoist5, crate0 is lifted from pallet6 at distributor2 by hoist6, crate0 is loaded by hoist6 into truck2 at distributor2, truck2 is driven from distributor2 to depot3, at depot3, hoist3 loads crate1 into truck2, hoist3 unloads crate0 from truck2 at depot3, truck2 is driven from depot3 to distributor0, hoist4 unloads crate3 from truck2 at distributor0, at depot3, hoist3 drops crate0 on pallet3 and at distributor0, hoist4 drops crate3 on pallet4 to reach the current state. In this state, if crate1 is unloaded by hoist4 from truck2 at distributor0, is it True or False that hoist4 is not available?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 is clear of any crates, crate1 is clear of any crates, crate1 is located at depot3, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor1 is where hoist5 is located, distributor1 is where pallet5 is located, distributor2 is where crate0 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is available for work, hoist3 can be found located at depot3, hoist3 is accessible, hoist4 can be found located at distributor0, hoist4 is available, hoist5 is available, hoist6 is accessible, hoist6 is located at distributor2, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 can be found located at depot3, pallet3 has crate1 on it, pallet4 is clear of any crates, pallet6 has crate0 on it, pallet6 is at distributor2, truck0 is located at depot1, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 is driven from depot1 to depot0, then hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2, after which truck2 is driven to distributor1. Meanwhile, at depot3, hoist3 lifts crate1 off pallet3, and at distributor1, hoist5 lifts crate3 from pallet5 and loads it into truck2, but then unloads crate2 from truck2. Truck2 is then driven to distributor2, where hoist5 drops crate2 on pallet5, and crate0 is lifted from pallet6 by hoist6 and loaded into truck2. Truck2 is then driven to depot3, where hoist3 loads crate1 into truck2 and unloads crate0, and then truck2 is driven to distributor0. At distributor0, hoist4 unloads crate3 from truck2, and at depot3, hoist3 drops crate0 on pallet3, while at distributor0, hoist4 drops crate3 on pallet4, resulting in the current state. In this state, if crate1 is unloaded by hoist4 from truck2 at distributor0, is it True or False that hoist4 is not available?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is empty, crate1 is situated at depot3, crate2 is empty, crate3 has no crates on it, crate3 is positioned on top of pallet5, crate2 is located at depot0, pallet1 is situated at depot1, hoist2 is located at depot2, pallet4 is situated at distributor0, crate3, hoist5, and pallet5 are all located at distributor1, crate0 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available and situated at depot1, hoist2 is available for work, hoist3 is accessible and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available, hoist6 is accessible and located at distributor2, crate2 is on pallet0, pallet0 is situated at depot0, pallet1 is empty, pallet2 is empty and situated at depot2, pallet3 has crate1 on it and is located at depot3, pallet4 is empty, crate0 is on pallet6, pallet6 is situated at distributor2, truck0 is situated at depot1, truck1 is at distributor0, and truck2 is situated at depot1."}
{"question_id": "46f1b0fc-8cd7-4852-b4f8-bd68f9207f52", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot0 from depot1, crate2 is lifted from pallet0 at depot0 by hoist0, crate2 is loaded by hoist0 into truck2 at depot0, truck2 is driven from depot0 to distributor1, at depot3, hoist3 lifts crate1 off pallet3, at distributor1, hoist5 lifts crate3 off pallet5, hoist5 loads crate3 into truck2 at distributor1, at distributor1, hoist5 unloads crate2 from truck2, truck2 is driven to distributor2 from distributor1, at distributor1, hoist5 drops crate2 on pallet5, hoist6 lifts crate0 from pallet6 at distributor2, crate0 is loaded by hoist6 into truck2 at distributor2, truck2 is driven to depot3 from distributor2, crate1 is loaded by hoist3 into truck2 at depot3, crate0 is unloaded by hoist3 from truck2 at depot3, from depot3, truck2 is driven to distributor0, at distributor0, hoist4 unloads crate3 from truck2, hoist3 drops crate0 on pallet3 at depot3 and crate3 is dropped on pallet4 at distributor0 by hoist4 to reach the current state. In this state, if crate1 is unloaded by hoist4 from truck2 at distributor0, is it True or False that hoist5 is available?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 is clear of any crates, crate1 is clear of any crates, crate1 is located at depot3, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor1 is where hoist5 is located, distributor1 is where pallet5 is located, distributor2 is where crate0 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is available for work, hoist3 can be found located at depot3, hoist3 is accessible, hoist4 can be found located at distributor0, hoist4 is available, hoist5 is available, hoist6 is accessible, hoist6 is located at distributor2, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 can be found located at depot3, pallet3 has crate1 on it, pallet4 is clear of any crates, pallet6 has crate0 on it, pallet6 is at distributor2, truck0 is located at depot1, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck2 at depot0, truck2 then proceeds from depot0 to distributor1, meanwhile at depot3, hoist3 lifts crate1 off pallet3, at distributor1, hoist5 lifts crate3 off pallet5, hoist5 then loads crate3 into truck2 at distributor1, at distributor1, hoist5 unloads crate2 from truck2, truck2 is then driven to distributor2 from distributor1, at distributor1, hoist5 places crate2 on pallet5, hoist6 lifts crate0 from pallet6 at distributor2, crate0 is then loaded by hoist6 into truck2 at distributor2, truck2 is driven to depot3 from distributor2, crate1 is loaded by hoist3 into truck2 at depot3, crate0 is unloaded by hoist3 from truck2 at depot3, truck2 then travels from depot3 to distributor0, at distributor0, hoist4 unloads crate3 from truck2, and finally, hoist3 places crate0 on pallet3 at depot3 and hoist4 places crate3 on pallet4 at distributor0, resulting in the current state. In this state, if crate1 is unloaded by hoist4 from truck2 at distributor0, is it True or False that hoist5 is available?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 has no crates on it, crate1 is positioned at depot3, crate2 is empty, crate3 has no crates on it, crate3 is stacked on pallet5, crate2 is situated at depot0, pallet1 is located at depot1, hoist2 is situated at depot2, pallet4 is located at distributor0, crate3, hoist5, and pallet5 are all situated at distributor1, crate0 is located at distributor2, hoist0 is accessible and situated at depot0, hoist1 is available and located at depot1, hoist2 is available for work, hoist3 is accessible and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available, hoist6 is accessible and located at distributor2, crate2 is on pallet0, pallet0 is situated at depot0, pallet1 has no crates on it, pallet2 has no crates on it, pallet2 is situated at depot2, pallet3 has crate1 on it and is located at depot3, pallet4 has no crates on it, crate0 is on pallet6, pallet6 is situated at distributor2, truck0 is situated at depot1, truck1 is located at distributor0, and truck2 is situated at depot1."}
{"question_id": "36ae1d88-c5c1-4272-a5b5-6dc930f60027", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0 to reach the current state. In this state, if at depot0, hoist0 loads crate2 into truck2, is it True or False that hoist0 is accessible?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 is clear of any crates, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear of any crates, crate3 is on pallet5, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot2 is where pallet2 is located, depot2 is where truck0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor1 is where crate0 is located, distributor2 is where crate3 is located, hoist0 is accessible, hoist0 is at depot0, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is available for work, hoist5 is located at distributor2, pallet0 can be found located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet3 is clear, pallet4 can be found located at distributor1, pallet4 has crate0 on it, pallet5 is located at distributor2, truck1 is at distributor2 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0 to reach the current state. In this state, if at depot0, hoist0 loads crate2 into truck2, is it True or False that hoist0 is accessible?\n\nParaphrased text: \nGiven the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0 to reach the current state. In this state, if at depot0, hoist0 loads crate2 into truck2, is hoist0 accessible or not?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is situated at depot2, crate1 is empty, crate1 is placed on pallet2, crate2 has no crates on it, crate2 is positioned on pallet0, crate3 has no crates on it, crate3 is placed on pallet5, crate2 is located at depot0, hoist1 is situated at depot1, pallet2 and truck0 are both located at depot2, hoist3 is situated at distributor0, pallet3 is also located at distributor0, crate0 is situated at distributor1, crate3 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available for use, hoist2 is available and situated at depot2, hoist3 is accessible, hoist4 is situated at distributor1 and available, hoist5 is available for use and situated at distributor2, pallet0 is located at depot0, pallet1 has no crates on it and is situated at depot1, pallet3 is empty, pallet4 is situated at distributor1 and has crate0 on it, pallet5 is situated at distributor2, truck1 is at distributor2 and truck2 is situated at depot0."}
{"question_id": "49aaf5f3-6a12-4cd4-8e8c-5314ba8d0171", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven from depot1 to depot0, at depot0, hoist0 lifts crate2 off pallet0, crate2 is loaded by hoist0 into truck1 at depot0, from depot0, truck1 is driven to depot1, crate2 is unloaded by hoist1 from truck1 at depot1, crate2 is dropped on pallet1 at depot1 by hoist1, crate3 is lifted from pallet2 at depot2 by hoist2, at depot2, hoist2 loads crate3 into truck2, from depot2, truck2 is driven to distributor3 and at distributor3, hoist6 unloads crate3 from truck2 to reach the current state. In this state, if hoist6 drops crate3 on pallet6 at distributor3, is it True or False that crate2 is not on top of pallet0?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 is on top of pallet5, crate1 is at distributor2, crate1 is clear of any crates, crate1 is on top of crate0, crate2 is clear of any crates, crate2 is located at depot0, crate2 is on pallet0, crate3 is clear, crate3 is located at depot2, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor1 is where pallet4 is located, distributor2 is where crate0 is located, distributor2 is where pallet5 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, hoist6 is located at distributor3, pallet0 is located at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet2 has crate3 on it, pallet2 is at depot2, pallet3 can be found located at distributor0, pallet3 is clear of any crates, pallet4 is clear, pallet6 is clear, pallet6 is located at distributor3, truck0 is located at distributor2 and truck2 is located at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 travels from depot1 to depot0, where hoist0 removes crate2 from pallet0, then loads it into truck1 at depot0. Truck1 then proceeds from depot0 to depot1, where hoist1 unloads crate2 from truck1 and places it on pallet1 at depot1. Meanwhile, at depot2, hoist2 lifts crate3 from pallet2 and loads it into truck2, which then drives to distributor3. Upon arrival, hoist6 unloads crate3 from truck2, resulting in the current state. In this state, if hoist6 places crate3 on pallet6 at distributor3, is it True or False that crate2 is not on top of pallet0?", "initial_state_nl_paraphrased": "Crate0 is positioned above pallet5, crate1 is situated at distributor2, crate1 has no crates on top of it, crate1 is stacked on top of crate0, crate2 has no crates on it, crate2 is situated at depot0, crate2 is placed on pallet0, crate3 is clear of any obstructions, crate3 is located at depot2, truck1 is currently at depot1, hoist3 is situated at distributor0, pallet4 is located at distributor1, crate0 and pallet5 are both located at distributor2, hoist0 is situated at depot0 and is available for use, hoist1 is located at depot1 and is available for work, hoist2 is available and situated at depot2, hoist3 is accessible and located at distributor0, hoist4 is available for work and situated at distributor1, hoist5 is available and located at distributor2, hoist6 is available and situated at distributor3, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 has no crates on it, pallet2 has crate3 on it and is situated at depot2, pallet3 is located at distributor0 and has no crates on it, pallet4 has no crates on it, pallet6 is clear of any crates and situated at distributor3, truck0 is located at distributor2 and truck2 is situated at depot2."}
{"question_id": "f6e9afe8-5239-4b31-a78d-0005e8d3f7b9", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck2 is driven to depot0, crate2 is lifted from pallet0 at depot0 by hoist0, crate2 is loaded by hoist0 into truck2 at depot0, truck2 is driven from depot0 to distributor1, at depot3, hoist3 lifts crate1 off pallet3, crate3 is lifted from pallet5 at distributor1 by hoist5, crate3 is loaded by hoist5 into truck2 at distributor1, crate2 is unloaded by hoist5 from truck2 at distributor1, from distributor1, truck2 is driven to distributor2, at distributor1, hoist5 drops crate2 on pallet5, at distributor2, hoist6 lifts crate0 off pallet6, hoist6 loads crate0 into truck2 at distributor2, truck2 is driven from distributor2 to depot3, at depot3, hoist3 loads crate1 into truck2, hoist3 unloads crate0 from truck2 at depot3, from depot3, truck2 is driven to distributor0, crate3 is unloaded by hoist4 from truck2 at distributor0, hoist3 drops crate0 on pallet3 at depot3 and hoist4 drops crate3 on pallet4 at distributor0 to reach the current state. In this state, if crate1 is unloaded by hoist4 from truck2 at distributor0, is it True or False that hoist2 is lifting crate1?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 is clear of any crates, crate1 is clear of any crates, crate1 is located at depot3, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor1 is where hoist5 is located, distributor1 is where pallet5 is located, distributor2 is where crate0 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is available for work, hoist3 can be found located at depot3, hoist3 is accessible, hoist4 can be found located at distributor0, hoist4 is available, hoist5 is available, hoist6 is accessible, hoist6 is located at distributor2, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 can be found located at depot3, pallet3 has crate1 on it, pallet4 is clear of any crates, pallet6 has crate0 on it, pallet6 is at distributor2, truck0 is located at depot1, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 is driven from depot1 to depot0, where hoist0 lifts crate2 from pallet0, loads it into truck2, and then truck2 is driven to distributor1. At depot3, hoist3 lifts crate1 off pallet3, while at distributor1, hoist5 lifts crate3 from pallet5, loads it into truck2, and unloads crate2 from truck2. Subsequently, truck2 is driven from distributor1 to distributor2, where crate2 is dropped on pallet5 by hoist5. At distributor2, hoist6 lifts crate0 off pallet6 and loads it into truck2, which is then driven to depot3. At depot3, hoist3 loads crate1 into truck2 and unloads crate0 from truck2. Truck2 is then driven from depot3 to distributor0, where crate3 is unloaded by hoist4 from truck2. Finally, hoist3 drops crate0 on pallet3 at depot3, and hoist4 drops crate3 on pallet4 at distributor0, resulting in the current state. In this state, if crate1 is unloaded by hoist4 from truck2 at distributor0, is it True or False that hoist2 is lifting crate1?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 has no crates on it, crate1 is positioned at depot3, crate2 is empty, crate3 has no crates on it, crate3 is stacked on pallet5, crate2 is situated at depot0, pallet1 is located at depot1, hoist2 is situated at depot2, pallet4 is located at distributor0, crate3, hoist5, and pallet5 are all situated at distributor1, crate0 is located at distributor2, hoist0 is accessible and situated at depot0, hoist1 is available and located at depot1, hoist2 is available for work, hoist3 is accessible and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available, hoist6 is accessible and located at distributor2, crate2 is on pallet0, pallet0 is situated at depot0, pallet1 has no crates on it, pallet2 has no crates on it and is situated at depot2, pallet3 has crate1 on it and is located at depot3, pallet4 has no crates on it, crate0 is on pallet6, pallet6 is situated at distributor2, truck0 is situated at depot1, truck1 is at distributor0, and truck2 is situated at depot1."}
{"question_id": "f659c04b-e82d-43b1-8fd8-0f305a7c2696", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven from depot1 to depot0, at depot0, hoist0 lifts crate2 off pallet0, hoist0 loads crate2 into truck1 at depot0, truck1 is driven to depot1 from depot0, at depot1, hoist1 unloads crate2 from truck1, at depot1, hoist1 drops crate2 on pallet1, hoist2 lifts crate3 from pallet2 at depot2, at depot2, hoist2 loads crate3 into truck2, from depot2, truck2 is driven to distributor3 and hoist6 unloads crate3 from truck2 at distributor3 to reach the current state. In this state, if hoist6 drops crate3 on pallet6 at distributor3, is it True or False that hoist1 is available, pallet0 is clear and pallet3 is clear?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 is on top of pallet5, crate1 is at distributor2, crate1 is clear of any crates, crate1 is on top of crate0, crate2 is clear of any crates, crate2 is located at depot0, crate2 is on pallet0, crate3 is clear, crate3 is located at depot2, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor1 is where pallet4 is located, distributor2 is where crate0 is located, distributor2 is where pallet5 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, hoist6 is located at distributor3, pallet0 is located at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet2 has crate3 on it, pallet2 is at depot2, pallet3 can be found located at distributor0, pallet3 is clear of any crates, pallet4 is clear, pallet6 is clear, pallet6 is located at distributor3, truck0 is located at distributor2 and truck2 is located at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 travels from depot1 to depot0, where hoist0 removes crate2 from pallet0 and loads it into truck1, then truck1 proceeds to depot1, where hoist1 unloads crate2 and places it on pallet1. Meanwhile, at depot2, hoist2 lifts crate3 from pallet2, loads it into truck2, and truck2 is driven to distributor3, where hoist6 unloads crate3. To reach the current state, these actions are performed. In this state, if hoist6 places crate3 on pallet6 at distributor3, is it True or False that hoist1 is available, pallet0 is empty, and pallet3 is empty?", "initial_state_nl_paraphrased": "Crate0 is positioned above pallet5, crate1 is situated at distributor2, crate1 has no crates on top of it, crate1 is stacked on top of crate0, crate2 has no crates on it, crate2 is situated at depot0, crate2 is placed on pallet0, crate3 is clear of any obstructions, crate3 is located at depot2, truck1 is currently at depot1, hoist3 is situated at distributor0, pallet4 is located at distributor1, crate0 is situated at distributor2, pallet5 is also located at distributor2, hoist0 is situated at depot0 and is available for use, hoist1 is located at depot1 and is available for work, hoist2 is available and situated at depot2, hoist3 is accessible, hoist4 is available for work and located at distributor1, hoist5 is available and situated at distributor2, hoist6 is available and located at distributor3, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 has no crates on it, pallet2 has crate3 on it and is situated at depot2, pallet3 is located at distributor0 and has no crates on it, pallet4 has no crates on it, pallet6 is clear of any crates and situated at distributor3, truck0 is located at distributor2 and truck2 is situated at depot2."}
{"question_id": "a13bc091-fb7a-4b61-8467-458716f968c2", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1, hoist0 lifts crate2 from pallet0 at depot0, at depot0, hoist0 loads crate2 into truck1, truck1 is driven to depot1 from depot0, hoist1 unloads crate2 from truck1 at depot1, hoist1 drops crate2 on pallet1 at depot1, hoist2 lifts crate3 from pallet2 at depot2, at depot2, hoist2 loads crate3 into truck2, truck2 is driven from depot2 to distributor3, hoist6 unloads crate3 from truck2 at distributor3, at distributor3, hoist6 drops crate3 on pallet6, crate1 is lifted from crate0 at distributor2 by hoist5, hoist5 loads crate1 into truck0 at distributor2, crate0 is lifted from pallet5 at distributor2 by hoist5, crate0 is loaded by hoist5 into truck0 at distributor2, at distributor2, hoist5 unloads crate1 from truck0, truck0 is driven to distributor0 from distributor2, hoist3 unloads crate0 from truck0 at distributor0 and at distributor0, hoist3 drops crate0 on pallet3 to reach the current state. In this state, if crate1 is dropped on pallet5 at distributor2 by hoist5, is it True or False that crate0 is clear and pallet5 is clear of any crates?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 is on top of pallet5, crate1 is at distributor2, crate1 is clear of any crates, crate1 is on top of crate0, crate2 is clear of any crates, crate2 is located at depot0, crate2 is on pallet0, crate3 is clear, crate3 is located at depot2, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor1 is where pallet4 is located, distributor2 is where crate0 is located, distributor2 is where pallet5 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, hoist6 is located at distributor3, pallet0 is located at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet2 has crate3 on it, pallet2 is at depot2, pallet3 can be found located at distributor0, pallet3 is clear of any crates, pallet4 is clear, pallet6 is clear, pallet6 is located at distributor3, truck0 is located at distributor2 and truck2 is located at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck1 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 then loads crate2 onto truck1 at depot0, truck1 is then driven from depot0 to depot1, hoist1 unloads crate2 from truck1 at depot1, and hoist1 places crate2 on pallet1 at depot1. Meanwhile, hoist2 lifts crate3 from pallet2 at depot2, hoist2 loads crate3 into truck2 at depot2, and truck2 is driven from depot2 to distributor3. At distributor3, hoist6 unloads crate3 from truck2 and drops crate3 onto pallet6. At distributor2, hoist5 lifts crate1 from crate0, loads crate1 into truck0, lifts crate0 from pallet5, and loads crate0 into truck0. Then, hoist5 unloads crate1 from truck0 at distributor2, truck0 is driven from distributor2 to distributor0, hoist3 unloads crate0 from truck0 at distributor0, and hoist3 places crate0 on pallet3, resulting in the current state. In this state, if hoist5 drops crate1 on pallet5 at distributor2, is it True or False that crate0 is clear and pallet5 is clear of any crates?", "initial_state_nl_paraphrased": "Crate0 is positioned above pallet5, crate1 is situated at distributor2, crate1 has no crates on top of it, crate1 is stacked on top of crate0, crate2 has no crates on it, crate2 is situated at depot0, crate2 is placed on pallet0, crate3 is clear of any obstructions, crate3 is located at depot2, truck1 is currently at depot1, hoist3 is situated at distributor0, pallet4 is located at distributor1, crate0 is situated at distributor2, pallet5 is also located at distributor2, hoist0 is situated at depot0 and is available for use, hoist1 is located at depot1 and is available for work, hoist2 is available and situated at depot2, hoist3 is accessible and located at distributor0, hoist4 is available for work and situated at distributor1, hoist5 is available and located at distributor2, hoist6 is available and situated at distributor3, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 has no crates on it, pallet2 has crate3 on it and is situated at depot2, pallet3 is located at distributor0 and has no crates on it, pallet4 has no crates on it, pallet6 is clear of any crates and situated at distributor3, truck0 is located at distributor2 and truck2 is situated at depot2."}
{"question_id": "5158ec3c-6391-4203-9304-71d640d22ac5", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot0 to depot2 to reach the current state. In this state, if at depot2, hoist2 lifts crate3 off crate2, is it True or False that distributor1 is where hoist2 is not located and hoist3 is not located at distributor1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 is clear of any crates, crate0 is located at distributor0, crate1 is located at depot2, crate1 is on pallet2, crate2 has crate3 on it, crate2 is on crate1, crate3 can be found located at depot2, crate3 is clear of any crates, depot1 is where pallet1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available for work, hoist3 is available for work, hoist4 is at distributor1, hoist4 is available for work, hoist5 is at distributor2, hoist5 is available, pallet0 can be found located at depot0, pallet0 is clear, pallet1 is clear of any crates, pallet2 is at depot2, pallet3 has crate0 on it, pallet3 is at distributor0, pallet4 is at distributor1, pallet4 is clear, pallet5 is at distributor2, pallet5 is clear, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck2 is driven from depot0 to depot2 to reach the current state. In this state, if at depot2, hoist2 lifts crate3 off crate2, is it True or False that distributor1 is not at the same location as hoist2 and hoist3 is not at the location of distributor1?", "initial_state_nl_paraphrased": "Crate0 is empty, and it is situated at distributor0. Crate1 is positioned at depot2 and is placed on pallet2. Crate2 has crate3 on top of it and is itself on crate1. Crate3 is located at depot2 and is empty. Depot1 is the location of pallet1, while depot2 is where crate2 is found. Distributor0 is the site of hoist3. Hoist0 is available and located at depot0. Hoist1 is at depot1 and available, hoist2 is at depot2 and available for work, and hoist3 is also available for work. Hoist4 is at distributor1 and available for work, and hoist5 is at distributor2 and available. Pallet0 is at depot0 and empty. Pallet1 is empty and located at depot1. Pallet2 is at depot2, pallet3 has crate0 on it and is at distributor0, pallet4 is at distributor1 and empty, and pallet5 is at distributor2 and empty. Truck0 is at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "f746c552-392d-414e-b29a-1456f26cc8fd", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0, crate2 is loaded by hoist0 into truck2 at depot0, from depot0, truck2 is driven to distributor1, hoist2 lifts crate1 from pallet2 at depot2, crate1 is loaded by hoist2 into truck0 at depot2, truck0 is driven to distributor0 from depot2, at distributor0, hoist3 unloads crate1 from truck0, hoist3 drops crate1 on pallet3 at distributor0, at distributor1, hoist4 lifts crate0 off pallet4, crate0 is loaded by hoist4 into truck2 at distributor1, hoist5 lifts crate3 from pallet5 at distributor2, crate3 is loaded by hoist5 into truck1 at distributor2, truck1 is driven to distributor1 from distributor2, at distributor1, hoist4 unloads crate3 from truck1, hoist4 drops crate3 on pallet4 at distributor1, at distributor1, hoist4 unloads crate2 from truck2, from distributor1, truck2 is driven to depot1, at depot1, hoist1 unloads crate0 from truck2 and at depot1, hoist1 drops crate0 on pallet1 to reach the current state. In this state, if hoist4 drops crate2 on crate3 at distributor1, is it True or False that crate2 is clear of any crates, crate3 is not clear and hoist4 is available for work?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 is clear of any crates, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear of any crates, crate3 is on pallet5, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot2 is where pallet2 is located, depot2 is where truck0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor1 is where crate0 is located, distributor2 is where crate3 is located, hoist0 is accessible, hoist0 is at depot0, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is available for work, hoist5 is located at distributor2, pallet0 can be found located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet3 is clear, pallet4 can be found located at distributor1, pallet4 has crate0 on it, pallet5 is located at distributor2, truck1 is at distributor2 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: at depot0, hoist0 first lifts crate2 from pallet0 and then loads it into truck2, after which truck2 is driven from depot0 to distributor1. Meanwhile, at depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 and places it on pallet3 at distributor0. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2, while at distributor2, hoist5 lifts crate3 from pallet5 and loads it into truck1, which is then driven to distributor1. At distributor1, hoist4 unloads crate3 from truck1 and places it on pallet4, and then unloads crate2 from truck2. Truck2 is then driven from distributor1 to depot1, where hoist1 unloads crate0 from truck2 and places it on pallet1, resulting in the current state. In this state, if hoist4 drops crate2 on crate3 at distributor1, is it True or False that crate2 is clear of any crates, crate3 is not clear and hoist4 is available for work?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is situated at depot2, crate1 is empty, crate1 is placed on pallet2, crate2 has no crates on it, crate2 is positioned on pallet0, crate3 has no crates on it, crate3 is placed on pallet5, crate2 is located at depot0, hoist1 is situated at depot1, pallet2 and truck0 are both located at depot2, hoist3 is situated at distributor0, along with pallet3, crate0 is located at distributor1, crate3 is located at distributor2, hoist0 is accessible and situated at depot0, hoist1 is available for use, hoist2 is available and located at depot2, hoist3 is accessible, hoist4 is located at distributor1 and available, hoist5 is available for use and situated at distributor2, pallet0 is located at depot0, pallet1 has no crates on it and is situated at depot1, pallet3 is empty, pallet4 is located at distributor1 and has crate0 on it, pallet5 is situated at distributor2, truck1 is at distributor2 and truck2 is located at depot0."}
{"question_id": "77397d69-a73e-4ccd-87be-6a564d4ad38a", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot1 to depot0 to reach the current state. In this state, if crate2 is lifted from pallet0 at depot0 by hoist0, is it True or False that crate2 is not at depot0, hoist0 is elevating crate2 and pallet0 does not have crate2 on it?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 is clear of any crates, crate1 is clear of any crates, crate1 is located at depot3, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor1 is where hoist5 is located, distributor1 is where pallet5 is located, distributor2 is where crate0 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is available for work, hoist3 can be found located at depot3, hoist3 is accessible, hoist4 can be found located at distributor0, hoist4 is available, hoist5 is available, hoist6 is accessible, hoist6 is located at distributor2, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 can be found located at depot3, pallet3 has crate1 on it, pallet4 is clear of any crates, pallet6 has crate0 on it, pallet6 is at distributor2, truck0 is located at depot1, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck2 is driven from depot1 to depot0 to reach the current state. In this state, if hoist0 lifts crate2 from pallet0 at depot0, is it True or False that crate2 is no longer at depot0, hoist0 is lifting crate2, and crate2 is no longer on pallet0?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is empty, crate1 is situated at depot3, crate2 is empty, crate3 has no crates on it, crate3 is positioned on top of pallet5, crate2 is located at depot0, pallet1 is situated at depot1, hoist2 is located at depot2, pallet4 is situated at distributor0, crate3, hoist5, and pallet5 are all located at distributor1, crate0 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available and situated at depot1, hoist2 is available for work, hoist3 is accessible and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available, hoist6 is accessible and located at distributor2, crate2 is on pallet0, pallet0 is situated at depot0, pallet1 is empty, pallet2 is empty and situated at depot2, pallet3 has crate1 on it and is located at depot3, pallet4 is empty, crate0 is on pallet6, pallet6 is situated at distributor2, truck0 is situated at depot1, truck1 is at distributor0, and truck2 is situated at depot1."}
{"question_id": "7e744f2c-ad17-43b4-8c5d-a25cdb1680d3", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck2 is driven to depot0, crate2 is lifted from pallet0 at depot0 by hoist0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven to distributor1 from depot0, hoist3 lifts crate1 from pallet3 at depot3, crate3 is lifted from pallet5 at distributor1 by hoist5, hoist5 loads crate3 into truck2 at distributor1, at distributor1, hoist5 unloads crate2 from truck2, from distributor1, truck2 is driven to distributor2 and at distributor1, hoist5 drops crate2 on pallet5 to reach the current state. In this state, if crate0 is lifted from pallet6 at distributor2 by hoist6, is it True or False that crate0 is not clear of any crates, hoist6 is not available and pallet6 is clear of any crates?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 is clear of any crates, crate1 is clear of any crates, crate1 is located at depot3, crate2 is clear, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot1 is where pallet1 is located, depot2 is where hoist2 is located, distributor0 is where pallet4 is located, distributor1 is where crate3 is located, distributor1 is where hoist5 is located, distributor1 is where pallet5 is located, distributor2 is where crate0 is located, hoist0 is accessible, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is available for work, hoist3 can be found located at depot3, hoist3 is accessible, hoist4 can be found located at distributor0, hoist4 is available, hoist5 is available, hoist6 is accessible, hoist6 is located at distributor2, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 can be found located at depot3, pallet3 has crate1 on it, pallet4 is clear of any crates, pallet6 has crate0 on it, pallet6 is at distributor2, truck0 is located at depot1, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 is driven from depot1 to depot0, then hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2, which is then driven to distributor1. At distributor1, hoist5 lifts crate3 from pallet5 and loads it into truck2, while hoist5 unloads crate2 from truck2. Meanwhile, hoist3 lifts crate1 from pallet3 at depot3. Finally, truck2 is driven to distributor2, and hoist5 places crate2 on pallet5 at distributor1, resulting in the current state. In this state, if hoist6 lifts crate0 from pallet6 at distributor2, is it True or False that crate0 is not clear of any crates, hoist6 is not available, and pallet6 is clear of any crates?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is empty, crate1 is situated at depot3, crate2 is empty, crate3 has no crates on it, crate3 is positioned on top of pallet5, crate2 is located at depot0, pallet1 is situated at depot1, hoist2 is located at depot2, pallet4 is situated at distributor0, crate3, hoist5, and pallet5 are all located at distributor1, crate0 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available and situated at depot1, hoist2 is available for work, hoist3 is accessible and located at depot3, hoist4 is available and situated at distributor0, hoist5 is available, hoist6 is accessible and located at distributor2, crate2 is on pallet0, pallet0 is situated at depot0, pallet1 is empty, pallet2 is empty and situated at depot2, pallet3 has crate1 on it and is located at depot3, pallet4 is empty, crate0 is on pallet6, pallet6 is situated at distributor2, truck0 is situated at depot1, truck1 is at distributor0, and truck2 is situated at depot1."}
{"question_id": "c1b05ecc-1875-472b-b655-dc78b4800c19", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven from depot0 to distributor1, at depot2, hoist2 lifts crate1 off pallet2, at depot2, hoist2 loads crate1 into truck0, truck0 is driven to distributor0 from depot2, at distributor0, hoist3 unloads crate1 from truck0, at distributor0, hoist3 drops crate1 on pallet3, hoist4 lifts crate0 from pallet4 at distributor1, crate0 is loaded by hoist4 into truck2 at distributor1, at distributor2, hoist5 lifts crate3 off pallet5, crate3 is loaded by hoist5 into truck1 at distributor2, truck1 is driven from distributor2 to distributor1, hoist4 unloads crate3 from truck1 at distributor1, crate3 is dropped on pallet4 at distributor1 by hoist4, crate2 is unloaded by hoist4 from truck2 at distributor1, truck2 is driven from distributor1 to depot1, hoist1 unloads crate0 from truck2 at depot1 and at depot1, hoist1 drops crate0 on pallet1 to reach the current state. In this state, if at distributor1, hoist4 drops crate2 on crate3, is it True or False that pallet2 is clear and pallet4 is not clear?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 is clear of any crates, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear of any crates, crate3 is on pallet5, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot2 is where pallet2 is located, depot2 is where truck0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor1 is where crate0 is located, distributor2 is where crate3 is located, hoist0 is accessible, hoist0 is at depot0, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is available for work, hoist5 is located at distributor2, pallet0 can be found located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet3 is clear, pallet4 can be found located at distributor1, pallet4 has crate0 on it, pallet5 is located at distributor2, truck1 is at distributor2 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: at depot0, hoist0 first lifts crate2 from pallet0 and then loads it into truck2, which is then driven to distributor1. At depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 and places it on pallet3. Meanwhile, at distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2. At distributor2, hoist5 lifts crate3 from pallet5 and loads it into truck1, which is then driven to distributor1. At distributor1, hoist4 unloads crate3 from truck1 and places it on pallet4. Additionally, hoist4 unloads crate2 from truck2. Truck2 is then driven to depot1, where hoist1 unloads crate0 and places it on pallet1, resulting in the current state. In this state, if at distributor1, hoist4 drops crate2 on crate3, is it True or False that pallet2 is clear and pallet4 is not clear?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is situated at depot2, crate1 is empty, crate1 is placed on pallet2, crate2 has no crates on it, crate2 is positioned on pallet0, crate3 has no crates on it, crate3 is placed on pallet5, crate2 is located at depot0, hoist1 is situated at depot1, pallet2 and truck0 are both located at depot2, hoist3 is situated at distributor0, pallet3 is also located at distributor0, crate0 is situated at distributor1, crate3 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available for use, hoist2 is available and situated at depot2, hoist3 is accessible, hoist4 is situated at distributor1 and available, hoist5 is available for use and situated at distributor2, pallet0 is located at depot0, pallet1 has no crates on it and is situated at depot1, pallet3 is empty, pallet4 is situated at distributor1 and has crate0 on it, pallet5 is situated at distributor2, truck1 is at distributor2 and truck2 is situated at depot0."}
{"question_id": "fe51a65c-d5ff-49d9-8038-7d9865900aed", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot0, truck2 is driven to depot2, crate3 is lifted from crate2 at depot2 by hoist2, at depot2, hoist2 loads crate3 into truck2, at depot2, hoist2 lifts crate2 off crate1, hoist2 loads crate2 into truck2 at depot2, hoist2 lifts crate1 from pallet2 at depot2, at depot2, hoist2 loads crate1 into truck2, from depot2, truck2 is driven to distributor0, at distributor0, hoist3 lifts crate0 off pallet3, crate0 is loaded by hoist3 into truck2 at distributor0, at distributor0, hoist3 unloads crate1 from truck2, truck2 is driven to distributor1 from distributor0, at distributor1, hoist4 unloads crate2 from truck2, truck2 is driven to distributor2 from distributor1, hoist5 unloads crate3 from truck2 at distributor2, hoist3 drops crate1 on pallet3 at distributor0, hoist4 drops crate2 on pallet4 at distributor1, crate3 is dropped on pallet5 at distributor2 by hoist5 and hoist5 unloads crate0 from truck2 at distributor2 to reach the current state. In this state, if at distributor2, hoist5 drops crate0 on crate3, is it True or False that crate0 is at distributor2, crate3 has crate0 on it and hoist5 is not raising crate0?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 is clear of any crates, crate0 is located at distributor0, crate1 is located at depot2, crate1 is on pallet2, crate2 has crate3 on it, crate2 is on crate1, crate3 can be found located at depot2, crate3 is clear of any crates, depot1 is where pallet1 is located, depot2 is where crate2 is located, distributor0 is where hoist3 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 is at depot1, hoist1 is available, hoist2 is at depot2, hoist2 is available for work, hoist3 is available for work, hoist4 is at distributor1, hoist4 is available for work, hoist5 is at distributor2, hoist5 is available, pallet0 can be found located at depot0, pallet0 is clear, pallet1 is clear of any crates, pallet2 is at depot2, pallet3 has crate0 on it, pallet3 is at distributor0, pallet4 is at distributor1, pallet4 is clear, pallet5 is at distributor2, pallet5 is clear, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: starting from depot0, truck2 is driven to depot2, where hoist2 lifts crate3 from crate2, then loads crate3 into truck2, lifts crate2 off crate1, loads crate2 into truck2, lifts crate1 from pallet2, and loads crate1 into truck2. Subsequently, truck2 is driven from depot2 to distributor0, where hoist3 lifts crate0 off pallet3 and loads it into truck2, while unloading crate1 from truck2. Truck2 then proceeds to distributor1, where hoist4 unloads crate2, and then to distributor2, where hoist5 unloads crate3. Finally, hoist3 places crate1 on pallet3 at distributor0, hoist4 places crate2 on pallet4 at distributor1, and hoist5 places crate3 on pallet5 at distributor2, ultimately unloading crate0 from truck2. In this resulting state, if at distributor2, hoist5 places crate0 on crate3, is it True or False that crate0 is at distributor2, crate3 has crate0 on it, and hoist5 is not lifting crate0?", "initial_state_nl_paraphrased": "Crate0 is empty, and it is situated at distributor0. Crate1 is positioned at depot2 and is placed on pallet2. Crate2 has crate3 on top of it and is itself on crate1. Crate3 is located at depot2 and is empty. Depot1 is the location of pallet1, while depot2 is where crate2 is found. Distributor0 is the location of hoist3. Hoist0 is available and situated at depot0. Hoist1 is at depot1 and available, hoist2 is at depot2 and available for work, and hoist3 is also available for work. Hoist4 is at distributor1 and available for work, while hoist5 is at distributor2 and available. Pallet0 is at depot0 and empty. Pallet1 is empty and located at depot1. Pallet2 is at depot2, pallet3 has crate0 on it and is at distributor0, pallet4 is at distributor1 and empty, and pallet5 is at distributor2 and empty. Truck0 is at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "31cc5bed-294e-4889-bebc-d1df73cdc527", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1 to reach the current state. In this state, if at depot0, hoist0 lifts crate2 off pallet0, is it True or False that depot2 is where crate1 is located, pallet5 is not at distributor3 and truck2 can be found located at distributor3?", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 is on top of pallet5, crate1 is at distributor2, crate1 is clear of any crates, crate1 is on top of crate0, crate2 is clear of any crates, crate2 is located at depot0, crate2 is on pallet0, crate3 is clear, crate3 is located at depot2, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor1 is where pallet4 is located, distributor2 is where crate0 is located, distributor2 is where pallet5 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, hoist6 is located at distributor3, pallet0 is located at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet2 has crate3 on it, pallet2 is at depot2, pallet3 can be found located at distributor0, pallet3 is clear of any crates, pallet4 is clear, pallet6 is clear, pallet6 is located at distributor3, truck0 is located at distributor2 and truck2 is located at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck1 is driven from depot1 to depot0 to reach the current state. In this state, if at depot0, hoist0 lifts crate2 off pallet0, is it True or False that crate1 is located at depot2, pallet5 is not located at distributor3, and truck2 is located at distributor3?", "initial_state_nl_paraphrased": "Crate0 is positioned above pallet5, crate1 is situated at distributor2, crate1 has no crates on top of it, crate1 is stacked on top of crate0, crate2 has no crates on it, crate2 is situated at depot0, crate2 is placed on pallet0, crate3 is clear of any obstructions, crate3 is located at depot2, truck1 is currently at depot1, hoist3 is situated at distributor0, pallet4 is located at distributor1, crate0 is situated at distributor2, pallet5 is also located at distributor2, hoist0 is at depot0 and is available for use, hoist1 is located at depot1 and is available for work, hoist2 is available and situated at depot2, hoist3 is accessible and located at distributor0, hoist4 is available for work and situated at distributor1, hoist5 is available and located at distributor2, hoist6 is available and situated at distributor3, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 has no crates on it, pallet2 has crate3 on it and is situated at depot2, pallet3 is located at distributor0 and has no crates on it, pallet4 has no crates on it, pallet6 is clear of any crates and situated at distributor3, truck0 is located at distributor2 and truck2 is situated at depot2."}
{"question_id": "60994f4d-a2be-47f9-8696-60789f892fc1", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: crate2 is lifted from pallet0 at depot0 by hoist0 to reach the current state. In this state, if crate2 is loaded by hoist0 into truck2 at depot0, is it True or False that hoist1 is not at distributor2 and hoist4 is raising crate0?", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 is clear of any crates, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate2 is on pallet0, crate3 is clear of any crates, crate3 is on pallet5, depot0 is where crate2 is located, depot1 is where hoist1 is located, depot2 is where pallet2 is located, depot2 is where truck0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor1 is where crate0 is located, distributor2 is where crate3 is located, hoist0 is accessible, hoist0 is at depot0, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is available for work, hoist5 is located at distributor2, pallet0 can be found located at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet3 is clear, pallet4 can be found located at distributor1, pallet4 has crate0 on it, pallet5 is located at distributor2, truck1 is at distributor2 and truck2 can be found located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: crate2 is lifted from pallet0 at depot0 by hoist0 to achieve the current state. In this state, if hoist0 loads crate2 into truck2 at depot0, is it True or False that hoist1 is not located at distributor2 and hoist4 is lifting crate0?", "initial_state_nl_paraphrased": "Crate0 has no crates on it, crate1 is situated at depot2, crate1 is empty, crate1 is placed on pallet2, crate2 has no crates on it, crate2 is positioned on pallet0, crate3 has no crates on it, crate3 is placed on pallet5, crate2 is located at depot0, hoist1 is situated at depot1, pallet2 and truck0 are both located at depot2, hoist3 is situated at distributor0, along with pallet3, crate0 is located at distributor1, crate3 is situated at distributor2, hoist0 is accessible and located at depot0, hoist1 is available for use, hoist2 is available and situated at depot2, hoist3 is accessible, hoist4 is located at distributor1 and available, hoist5 is available for use and situated at distributor2, pallet0 is located at depot0, pallet1 has no crates on it and is situated at depot1, pallet3 is empty, pallet4 is located at distributor1 and has crate0 on it, pallet5 is situated at distributor2, truck1 is at distributor2 and truck2 is located at depot0."}
{"question_id": "5908e91b-a15f-486f-be41-40eec54f0fc9", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1, hoist0 lifts crate2 from pallet0 at depot0, crate2 is loaded by hoist0 into truck1 at depot0, from depot0, truck1 is driven to depot1, at depot1, hoist1 unloads crate2 from truck1, crate2 is dropped on pallet1 at depot1 by hoist1, at depot2, hoist2 lifts crate3 off pallet2, crate3 is loaded by hoist2 into truck2 at depot2, from depot2, truck2 is driven to distributor3 and at distributor3, hoist6 unloads crate3 from truck2 to reach the current state. In this state, if at distributor3, hoist6 drops crate3 on pallet6, is it True or False that crate3 is located at distributor3, hoist6 is not elevating crate3 and pallet6 has crate3 on it?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 is on top of pallet5, crate1 is at distributor2, crate1 is clear of any crates, crate1 is on top of crate0, crate2 is clear of any crates, crate2 is located at depot0, crate2 is on pallet0, crate3 is clear, crate3 is located at depot2, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor1 is where pallet4 is located, distributor2 is where crate0 is located, distributor2 is where pallet5 is located, hoist0 is at depot0, hoist0 is available for work, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is available, hoist2 is located at depot2, hoist3 is accessible, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is available, hoist5 is located at distributor2, hoist6 is available, hoist6 is located at distributor3, pallet0 is located at depot0, pallet1 can be found located at depot1, pallet1 is clear, pallet2 has crate3 on it, pallet2 is at depot2, pallet3 can be found located at distributor0, pallet3 is clear of any crates, pallet4 is clear, pallet6 is clear, pallet6 is located at distributor3, truck0 is located at distributor2 and truck2 is located at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck1 at depot0, truck1 then travels from depot0 to depot1, at depot1, hoist1 unloads crate2 from truck1, and hoist1 places crate2 on pallet1 at depot1. Meanwhile, at depot2, hoist2 lifts crate3 from pallet2, hoist2 loads crate3 into truck2 at depot2, truck2 travels from depot2 to distributor3, and at distributor3, hoist6 unloads crate3 from truck2, resulting in the current state. In this state, if hoist6 drops crate3 onto pallet6 at distributor3, is it True or False that crate3 is at distributor3, hoist6 is no longer lifting crate3, and pallet6 has crate3 on it?", "initial_state_nl_paraphrased": "Crate0 is positioned above pallet5, crate1 is situated at distributor2, crate1 has no crates on top of it, crate1 is stacked on top of crate0, crate2 has no crates on it, crate2 is situated at depot0, crate2 is placed on pallet0, crate3 is clear of any obstructions, crate3 is located at depot2, truck1 is currently at depot1, hoist3 is situated at distributor0, pallet4 is located at distributor1, crate0 is situated at distributor2, pallet5 is also located at distributor2, hoist0 is situated at depot0 and is available for use, hoist1 is located at depot1 and is available for work, hoist2 is available and situated at depot2, hoist3 is accessible, hoist4 is available for work and located at distributor1, hoist5 is available and situated at distributor2, hoist6 is available and located at distributor3, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 has no crates on it, pallet2 has crate3 on it and is situated at depot2, pallet3 is located at distributor0 and has no crates on it, pallet4 has no crates on it, pallet6 is clear of any crates and situated at distributor3, truck0 is located at distributor2 and truck2 is situated at depot2."}
