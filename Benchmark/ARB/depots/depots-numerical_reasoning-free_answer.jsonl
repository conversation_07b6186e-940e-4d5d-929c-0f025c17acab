{"question_id": "c03804b2-4bd4-4b4e-b5aa-a00524d29097", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from depot0, truck2 is driven to depot2, crate3 is lifted from crate2 at depot2 by hoist2, at depot2, hoist2 loads crate3 into truck2, hoist2 lifts crate2 from crate1 at depot2, at depot2, hoist2 loads crate2 into truck2, hoist2 lifts crate1 from pallet2 at depot2, at depot2, hoist2 loads crate1 into truck2, from depot2, truck2 is driven to distributor0, crate0 is lifted from pallet3 at distributor0 by hoist3 and at distributor0, hoist3 loads crate0 into truck2 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "3834", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 can be found located at depot2, crate1 is on top of pallet2, crate2 is located at depot2, crate2 is on top of crate1, crate3 is at depot2, crate3 is clear of any crates, crate3 is on crate2, depot1 is where hoist1 is located, depot1 is where pallet1 is located, distributor1 is where hoist4 is located, hoist0 can be found located at depot0, hoist0 is available, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is available for work, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available, hoist5 can be found located at distributor2, hoist5 is available for work, pallet0 is at depot0, pallet0 is clear of any crates, pallet1 is clear, pallet2 is located at depot2, pallet3 can be found located at distributor0, pallet4 is clear, pallet4 is located at distributor1, pallet5 is clear, pallet5 is located at distributor2, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 is at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 departs from depot0 and arrives at depot2, where hoist2 unloads crate3 from crate2 and loads it into truck2, then hoist2 unloads crate2 from crate1 and loads it into truck2, and finally hoist2 unloads crate1 from pallet2 and loads it into truck2. Subsequently, truck2 travels from depot2 to distributor0, where hoist3 unloads crate0 from pallet3 and loads it into truck2, resulting in the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, with no crates on top of it, and it is positioned on pallet3. Crate1 is located at depot2 and is placed on top of pallet2. Crate2 is also at depot2, and it is positioned on top of crate1. Crate3 is situated at depot2, has no crates on top of it, and is placed on crate2. Depot1 is the location of both hoist1 and pallet1. Distributor1 is where hoist4 is located. Hoist0 is situated at depot0 and is available for use. Hoist1 is accessible, while hoist2 is located at depot2 and is available for work. Hoist3 is available for work and is situated at distributor0. Hoist4 is available, and hoist5 is located at distributor2 and is available for work. Pallet0 is at depot0 and has no crates on it. Pallet1 is clear of any crates. Pallet2 is located at depot2. Pallet3 is situated at distributor0. Pallet4 is clear of any crates and is located at distributor1. Pallet5 is clear of any crates and is situated at distributor2. Truck0 is located at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "5590e5f8-9f64-49c0-9a50-d3e9c13a5482", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: crate2 is lifted from pallet0 at depot0 by hoist0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven to distributor1 from depot0, at depot2, hoist2 lifts crate1 off pallet2, hoist2 loads crate1 into truck0 at depot2, from depot2, truck0 is driven to distributor0, hoist3 unloads crate1 from truck0 at distributor0, crate1 is dropped on pallet3 at distributor0 by hoist3, at distributor1, hoist4 lifts crate0 off pallet4 and crate0 is loaded by hoist4 into truck2 at distributor1 to reach the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "answer": "33", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear of any crates, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on top of pallet2, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is at distributor2, crate3 is clear of any crates, crate3 is on top of pallet5, distributor1 is where hoist4 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available for work, hoist3 is at distributor0, hoist3 is available for work, hoist4 is accessible, hoist5 is accessible, hoist5 is at distributor2, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 can be found located at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 can be found located at distributor2 and truck2 is located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2, which then travels from depot0 to distributor1. Meanwhile, at depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 and places it on pallet3 at distributor0. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2, resulting in the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, with no crates on top of it, and it is positioned on pallet4. Crate1 is located at depot2, clear of any other crates, and is placed on top of pallet2. Crate2 is found at depot0, clear of any crates, and is situated on pallet0. Crate3 is located at distributor2, with no crates on top of it, and is positioned on pallet5. The location of hoist4 is distributor1. Hoist0 is available and located at depot0. Hoist1 is available for work and can be found at depot1. Hoist2 is available for work and situated at depot2. Hoist3 is available for work and located at distributor0. Hoist4 and hoist5 are both accessible, with hoist5 situated at distributor2. Pallet0 is located at depot0. Pallet1 is clear of any crates and situated at depot1. Pallet2 is located at depot2. Pallet3 is clear of any crates and situated at distributor0. Pallet4 is located at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2. Truck1 is situated at distributor2, and truck2 is located at depot0."}
{"question_id": "700b242a-ae6f-4b31-a1a4-dfbd52dccee2", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, crate2 is loaded by hoist0 into truck1 at depot0, from depot0, truck1 is driven to depot1, hoist1 unloads crate2 from truck1 at depot1, hoist1 drops crate2 on pallet1 at depot1, at depot2, hoist2 lifts crate3 off pallet2, crate3 is loaded by hoist2 into truck2 at depot2, from depot2, truck2 is driven to distributor3 and crate3 is unloaded by hoist6 from truck2 at distributor3 to reach the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "answer": "37", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck1 at depot0, truck1 then proceeds from depot0 to depot1, hoist1 unloads crate2 from truck1 at depot1, and hoist1 places crate2 on pallet1 at depot1. Meanwhile, at depot2, hoist2 lifts crate3 from pallet2, hoist2 loads crate3 into truck2 at depot2, truck2 is driven from depot2 to distributor3, and hoist6 unloads crate3 from truck2 at distributor3, resulting in the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is clear of other crates, is also located at distributor2 and is positioned on top of crate0. Crate2 can be found at depot0 and is clear of any obstructions. Crate3 is located at depot2, is clear of other crates, and is positioned on pallet2. \n\nDepot0 is the location of hoist0, depot1 houses hoist1, distributor1 is home to hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3 is available for work and is situated at distributor0, while hoist4 is also available for work. Hoist5 is available and located at distributor2, and hoist6 is accessible.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is located at depot2, pallet3 is clear and situated at distributor0, pallet4 is clear of any crates and located at distributor1, and pallet6 is clear of any crates and situated at distributor3. Lastly, truck0 is located at distributor2, truck1 is at depot1, and truck2 is at depot2."}
{"question_id": "3fd97e62-b473-4dac-b89e-14ea6925bf89", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, at depot0, hoist0 loads crate2 into truck2, from depot0, truck2 is driven to distributor1, at depot3, hoist3 lifts crate1 off pallet3, crate3 is lifted from pallet5 at distributor1 by hoist5, at distributor1, hoist5 loads crate3 into truck2, at distributor1, hoist5 unloads crate2 from truck2, truck2 is driven to distributor2 from distributor1 and crate2 is dropped on pallet5 at distributor1 by hoist5 to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "answer": "22", "plan_length": 10, "initial_state_nl": "Crate0 is at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear, crate1 is located at depot3, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 can be found located at distributor1, crate3 is clear, crate3 is on pallet5, depot1 is where truck2 is located, depot3 is where pallet3 is located, distributor0 is where truck1 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is at depot3, hoist3 is available, hoist4 is available, hoist4 is located at distributor0, hoist5 is accessible, hoist5 is at distributor1, hoist6 can be found located at distributor2, hoist6 is available for work, pallet0 can be found located at depot0, pallet1 is at depot1, pallet1 is clear, pallet2 can be found located at depot2, pallet2 is clear of any crates, pallet3 has crate1 on it, pallet4 can be found located at distributor0, pallet4 is clear of any crates, pallet5 is at distributor1 and truck0 is at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 then loads crate2 into truck2 at depot0, truck2 is then driven from depot0 to distributor1, at depot3, hoist3 lifts crate1 off pallet3, hoist5 lifts crate3 from pallet5 at distributor1, hoist5 loads crate3 into truck2 at distributor1, hoist5 unloads crate2 from truck2 at distributor1, truck2 is then driven from distributor1 to distributor2, and finally, hoist5 drops crate2 onto pallet5 at distributor1 to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 is empty, crate1 is located at depot3, crate2 is at depot0, crate2 is empty, crate2 is placed on top of pallet0, crate3 is located at distributor1, crate3 is empty, crate3 is on pallet5, truck2 is stationed at depot1, pallet3 is located at depot3, truck1 is stationed at distributor0, pallet6 is located at distributor2, hoist0 is ready for work, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available, hoist2 is available, hoist2 is situated at depot2, hoist3 is located at depot3, hoist3 is available, hoist4 is available, hoist4 is situated at distributor0, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is located at distributor2, hoist6 is ready for work, pallet0 is located at depot0, pallet1 is situated at depot1, pallet1 is empty, pallet2 is located at depot2, pallet2 is empty, crate1 is on pallet3, pallet4 is located at distributor0, pallet4 is empty, pallet5 is situated at distributor1 and truck0 is stationed at depot1."}
{"question_id": "eef61a83-6218-4bfe-9379-e6374ed1722e", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot0 to depot2, crate3 is lifted from crate2 at depot2 by hoist2, hoist2 loads crate3 into truck2 at depot2, hoist2 lifts crate2 from crate1 at depot2, crate2 is loaded by hoist2 into truck2 at depot2, crate1 is lifted from pallet2 at depot2 by hoist2, at depot2, hoist2 loads crate1 into truck2, truck2 is driven from depot2 to distributor0, crate0 is lifted from pallet3 at distributor0 by hoist3 and hoist3 loads crate0 into truck2 at distributor0 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "3815", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 can be found located at depot2, crate1 is on top of pallet2, crate2 is located at depot2, crate2 is on top of crate1, crate3 is at depot2, crate3 is clear of any crates, crate3 is on crate2, depot1 is where hoist1 is located, depot1 is where pallet1 is located, distributor1 is where hoist4 is located, hoist0 can be found located at depot0, hoist0 is available, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is available for work, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available, hoist5 can be found located at distributor2, hoist5 is available for work, pallet0 is at depot0, pallet0 is clear of any crates, pallet1 is clear, pallet2 is located at depot2, pallet3 can be found located at distributor0, pallet4 is clear, pallet4 is located at distributor1, pallet5 is clear, pallet5 is located at distributor2, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 is at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 travels from depot0 to depot2, hoist2 at depot2 lifts crate3 from crate2 and loads it into truck2, then hoist2 lifts crate2 from crate1 and loads it into truck2, followed by lifting crate1 from pallet2 and loading it into truck2, after which truck2 is driven to distributor0, where hoist3 lifts crate0 from pallet3 and loads it into truck2 to achieve the current state. In this state, what is the total number of actions that cannot be executed? Provide the answer as an integer, or None if there are no such actions.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, with no crates on top of it, and it is positioned on pallet3. Crate1 is located at depot2 and is placed on top of pallet2. Crate2 is also at depot2, and it is positioned underneath crate1. Crate3 is situated at depot2, has no crates on top of it, and is placed on crate2. Depot1 is the location of both hoist1 and pallet1. Distributor1 is where hoist4 is located. Hoist0 is situated at depot0 and is available for use. Hoist1 is accessible, while hoist2 is located at depot2 and is available for work. Hoist3 is available for work and is situated at distributor0. Hoist4 is available, and hoist5 is located at distributor2 and is available for work. Pallet0 is at depot0 and has no crates on it. Pallet1 is clear of any crates, pallet2 is located at depot2, and pallet3 is situated at distributor0. Pallet4 is clear of any crates and is located at distributor1, while pallet5 is clear and is situated at distributor2. Truck0 is located at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "31bc2904-4d53-423b-b300-44b90e72420c", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "5593", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1 to reach the current state. In this state, what is the total count of actions that cannot be executed? Provide the answer as an integer, or write None if there are no such actions.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is clear of other crates, is also located at distributor2 and positioned on top of crate0. Crate2 can be found at depot0 and is clear of any obstructions. Crate3 is located at depot2, clear of other crates, and is positioned on pallet2. \n\nDepot0 is the location of hoist0, while depot1 houses hoist1. Distributor1 is home to hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3 is available for work and situated at distributor0, while hoist4 is also available for work. Hoist5 is available and located at distributor2, and hoist6 is accessible.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is located at depot2, while pallet3 is clear and situated at distributor0. Pallet4 is clear of any crates and located at distributor1, and pallet6 is clear and situated at distributor3. The locations of the trucks are as follows: truck0 is at distributor2, truck1 is at depot1, and truck2 is at depot2."}
{"question_id": "50e918cb-68b6-4877-bc8c-8ffbd222d1bb", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck2 at depot0, from depot0, truck2 is driven to distributor1, hoist2 lifts crate1 from pallet2 at depot2, hoist2 loads crate1 into truck0 at depot2, truck0 is driven to distributor0 from depot2, at distributor0, hoist3 unloads crate1 from truck0, at distributor0, hoist3 drops crate1 on pallet3, hoist4 lifts crate0 from pallet4 at distributor1 and hoist4 loads crate0 into truck2 at distributor1 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "3834", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear of any crates, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on top of pallet2, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is at distributor2, crate3 is clear of any crates, crate3 is on top of pallet5, distributor1 is where hoist4 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available for work, hoist3 is at distributor0, hoist3 is available for work, hoist4 is accessible, hoist5 is accessible, hoist5 is at distributor2, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 can be found located at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 can be found located at distributor2 and truck2 is located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are executed: hoist0 first lifts crate2 from pallet0 at depot0 and then loads it into truck2 at depot0, truck2 is then driven from depot0 to distributor1, at depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, truck0 is then driven from depot2 to distributor0, upon arrival at distributor0, hoist3 unloads crate1 from truck0 and drops it on pallet3, meanwhile, at distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, with no crates on top of it, and it is positioned on pallet4. Crate1 is located at depot2, clear of any other crates, and is placed on top of pallet2. Crate2 is found at depot0, clear of any crates, and is situated on pallet0. Crate3 is located at distributor2, with no crates on top of it, and is positioned on pallet5. The location of hoist4 is distributor1. Hoist0 is available and located at depot0. Hoist1 is available for work and can be found at depot1. Hoist2 is available for work and situated at depot2. Hoist3 is available for work and located at distributor0. Hoist4 is accessible, and hoist5 is also accessible, with the latter located at distributor2. Pallet0 is situated at depot0. Pallet1 is clear of any crates and located at depot1. Pallet2 is found at depot2. Pallet3 is clear of any crates and located at distributor0. Pallet4 is situated at distributor1. Pallet5 is located at distributor2. Truck0 is at depot2, truck1 is located at distributor2, and truck2 is situated at depot0."}
{"question_id": "1559b1e9-2705-4bfc-8ec7-d9c6b025ed4b", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: crate2 is lifted from pallet0 at depot0 by hoist0, at depot0, hoist0 loads crate2 into truck2, truck2 is driven to distributor1 from depot0, hoist2 lifts crate1 from pallet2 at depot2, at depot2, hoist2 loads crate1 into truck0, from depot2, truck0 is driven to distributor0, at distributor0, hoist3 unloads crate1 from truck0, crate1 is dropped on pallet3 at distributor0 by hoist3, hoist4 lifts crate0 from pallet4 at distributor1, hoist4 loads crate0 into truck2 at distributor1, hoist5 lifts crate3 from pallet5 at distributor2, hoist5 loads crate3 into truck1 at distributor2, at distributor0, hoist5 lifts crate2 off pallet0, crate3 is unloaded by hoist4 from truck1 at distributor1, at distributor1, hoist4 drops crate3 on pallet4, at distributor1, hoist4 unloads crate2 from truck2, truck2 is driven to depot1 from distributor1, at depot1, hoist1 unloads crate0 from truck2 and hoist1 drops crate0 on pallet1 at depot1 to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "answer": "12", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor1, crate0 is clear of any crates, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on top of pallet2, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 is at distributor2, crate3 is clear of any crates, crate3 is on top of pallet5, distributor1 is where hoist4 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available for work, hoist3 is at distributor0, hoist3 is available for work, hoist4 is accessible, hoist5 is accessible, hoist5 is at distributor2, pallet0 is at depot0, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 can be found located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 can be found located at distributor1, pallet5 is located at distributor2, truck0 is at depot2, truck1 can be found located at distributor2 and truck2 is located at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2, then truck2 is driven from depot0 to distributor1. Meanwhile, hoist2 lifts crate1 from pallet2 at depot2 and loads it into truck0, which is then driven from depot2 to distributor0. At distributor0, hoist3 unloads crate1 from truck0 and drops it on pallet3. Additionally, hoist4 lifts crate0 from pallet4 at distributor1 and loads it into truck2, while hoist5 lifts crate3 from pallet5 at distributor2 and loads it into truck1. At distributor0, hoist5 lifts crate2 off pallet0, and at distributor1, hoist4 unloads crate3 from truck1 and drops it on pallet4, then unloads crate2 from truck2. Finally, truck2 is driven to depot1, where hoist1 unloads crate0 from truck2 and drops it on pallet1 to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor1, with no crates on top of it, and it is positioned on pallet4. Crate1 is located at depot2, clear of any other crates, and is placed on top of pallet2. Crate2 is found at depot0, clear of any crates, and is situated on pallet0. Crate3 is located at distributor2, with no crates on top of it, and is positioned on pallet5. The location of hoist4 is distributor1. Hoist0 is available and situated at depot0. Hoist1 is available for work and can be found at depot1. Hoist2 is available for work and located at depot2. Hoist3 is available for work and situated at distributor0. Hoist4 and hoist5 are both accessible, with hoist5 located at distributor2. Pallet0 is situated at depot0. Pallet1 is clear of any crates and located at depot1. Pallet2 is found at depot2. Pallet3 is clear of any crates and situated at distributor0. Pallet4 is located at distributor1. Pallet5 is situated at distributor2. Truck0 is located at depot2. Truck1 is found at distributor2, and truck2 is situated at depot0."}
{"question_id": "f1f9d562-06ac-4dc8-a19c-2c526f4825d5", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot1 to depot0, crate2 is lifted from pallet0 at depot0 by hoist0, crate2 is loaded by hoist0 into truck2 at depot0, from depot0, truck2 is driven to distributor1, crate1 is lifted from pallet3 at depot3 by hoist3, crate3 is lifted from pallet5 at distributor1 by hoist5, crate3 is loaded by hoist5 into truck2 at distributor1, at distributor1, hoist5 unloads crate2 from truck2, truck2 is driven to distributor2 from distributor1 and at distributor1, hoist5 drops crate2 on pallet5 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "5614", "plan_length": 10, "initial_state_nl": "Crate0 is at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear, crate1 is located at depot3, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 can be found located at distributor1, crate3 is clear, crate3 is on pallet5, depot1 is where truck2 is located, depot3 is where pallet3 is located, distributor0 is where truck1 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is at depot3, hoist3 is available, hoist4 is available, hoist4 is located at distributor0, hoist5 is accessible, hoist5 is at distributor1, hoist6 can be found located at distributor2, hoist6 is available for work, pallet0 can be found located at depot0, pallet1 is at depot1, pallet1 is clear, pallet2 can be found located at depot2, pallet2 is clear of any crates, pallet3 has crate1 on it, pallet4 can be found located at distributor0, pallet4 is clear of any crates, pallet5 is at distributor1 and truck0 is at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck2 at depot0, truck2 then proceeds from depot0 to distributor1, hoist3 lifts crate1 from pallet3 at depot3, hoist5 lifts crate3 from pallet5 at distributor1, hoist5 loads crate3 into truck2 at distributor1, at distributor1, hoist5 unloads crate2 from truck2, truck2 is then driven from distributor1 to distributor2, and at distributor1, hoist5 places crate2 on pallet5, resulting in the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 is empty, crate1 is located at depot3, crate2 is at depot0, crate2 is empty, crate2 is placed on top of pallet0, crate3 is located at distributor1, crate3 is empty, crate3 is on pallet5, truck2 is stationed at depot1, pallet3 is located at depot3, truck1 is stationed at distributor0, pallet6 is located at distributor2, hoist0 is ready for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for use, hoist2 is available, hoist2 is situated at depot2, hoist3 is located at depot3, hoist3 is ready for use, hoist4 is available, hoist4 is situated at distributor0, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is located at distributor2, hoist6 is ready for use, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 is empty, pallet2 is located at depot2, pallet2 is empty, crate1 is on pallet3, pallet4 is located at distributor0, pallet4 is empty, pallet5 is located at distributor1 and truck0 is stationed at depot1."}
{"question_id": "2abfe1c9-c40c-4720-99ec-5b265e2895fc", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck2 is driven to depot0 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "5614", "plan_length": 1, "initial_state_nl": "Crate0 is at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear, crate1 is located at depot3, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 can be found located at distributor1, crate3 is clear, crate3 is on pallet5, depot1 is where truck2 is located, depot3 is where pallet3 is located, distributor0 is where truck1 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is at depot3, hoist3 is available, hoist4 is available, hoist4 is located at distributor0, hoist5 is accessible, hoist5 is at distributor1, hoist6 can be found located at distributor2, hoist6 is available for work, pallet0 can be found located at depot0, pallet1 is at depot1, pallet1 is clear, pallet2 can be found located at depot2, pallet2 is clear of any crates, pallet3 has crate1 on it, pallet4 can be found located at distributor0, pallet4 is clear of any crates, pallet5 is at distributor1 and truck0 is at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following steps are taken: truck2 is driven from depot1 to depot0 to achieve the current state. In this state, what is the total count of both executable and non-executable actions? Provide the answer as an integer, or write None if there are no actions.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 is empty, crate1 is located at depot3, crate2 is at depot0, crate2 is empty, crate2 is placed on top of pallet0, crate3 is located at distributor1, crate3 is empty, crate3 is on pallet5, truck2 is stationed at depot1, pallet3 is located at depot3, truck1 is stationed at distributor0, pallet6 is located at distributor2, hoist0 is ready for work, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for use, hoist2 is available, hoist2 is situated at depot2, hoist3 is located at depot3, hoist3 is ready for work, hoist4 is available, hoist4 is situated at distributor0, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is located at distributor2, hoist6 is ready for work, pallet0 is situated at depot0, pallet1 is located at depot1, pallet1 is empty, pallet2 is located at depot2, pallet2 is empty, crate1 is on pallet3, pallet4 is located at distributor0, pallet4 is empty, pallet5 is located at distributor1 and truck0 is stationed at depot1."}
{"question_id": "daaaf496-395b-407f-912c-afc564fe101a", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot2 from depot0 to reach the current state. In this state, what is the total number of valid properties of the state that do not involve negations? Write as an integer. Write None if there are none.", "answer": "35", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 can be found located at depot2, crate1 is on top of pallet2, crate2 is located at depot2, crate2 is on top of crate1, crate3 is at depot2, crate3 is clear of any crates, crate3 is on crate2, depot1 is where hoist1 is located, depot1 is where pallet1 is located, distributor1 is where hoist4 is located, hoist0 can be found located at depot0, hoist0 is available, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is available for work, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available, hoist5 can be found located at distributor2, hoist5 is available for work, pallet0 is at depot0, pallet0 is clear of any crates, pallet1 is clear, pallet2 is located at depot2, pallet3 can be found located at distributor0, pallet4 is clear, pallet4 is located at distributor1, pallet5 is clear, pallet5 is located at distributor2, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 is at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: truck2 is driven from depot0 to depot2 to achieve the current state. In this state, what is the total count of valid state properties that do not include negations? Express the answer as an integer, or write None if there are no such properties.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, with no crates on top of it, and it is positioned on pallet3. Crate1 is located at depot2 and is placed on top of pallet2. Crate2 is also at depot2, and it is positioned underneath crate1. Crate3 is situated at depot2, has no crates on top of it, and is placed on crate2. Depot1 is the location of both hoist1 and pallet1. Distributor1 is where hoist4 is situated. Hoist0 is located at depot0 and is available for use. Hoist1 is accessible, while hoist2 is located at depot2 and is available for work. Hoist3 is available for work and is situated at distributor0. Hoist4 is available, and hoist5 is located at distributor2 and is available for work. Pallet0 is at depot0 and has no crates on it. Pallet1 is clear of any crates, while pallet2 is located at depot2. Pallet3 is situated at distributor0, pallet4 is clear and located at distributor1, and pallet5 is clear and situated at distributor2. Truck0 is located at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "5a1de863-ea90-4369-92e2-2cfd198575b7", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot0 to depot2 to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "answer": "17", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor0, crate0 is clear of any crates, crate0 is on pallet3, crate1 can be found located at depot2, crate1 is on top of pallet2, crate2 is located at depot2, crate2 is on top of crate1, crate3 is at depot2, crate3 is clear of any crates, crate3 is on crate2, depot1 is where hoist1 is located, depot1 is where pallet1 is located, distributor1 is where hoist4 is located, hoist0 can be found located at depot0, hoist0 is available, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is available for work, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available, hoist5 can be found located at distributor2, hoist5 is available for work, pallet0 is at depot0, pallet0 is clear of any crates, pallet1 is clear, pallet2 is located at depot2, pallet3 can be found located at distributor0, pallet4 is clear, pallet4 is located at distributor1, pallet5 is clear, pallet5 is located at distributor2, truck0 can be found located at distributor0, truck1 is at depot1 and truck2 is at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are taken: truck2 is driven from depot0 to depot2 to achieve the current state. In this state, what is the total count of actions that can be executed? Provide the answer as an integer, or None if there are no executable actions.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor0, with no crates on top of it, and it is positioned on pallet3. Crate1 is located at depot2 and is placed on top of pallet2. Crate2 is also at depot2, and it is positioned underneath crate1. Crate3 is situated at depot2, has no crates on top of it, and is placed on crate2. Depot1 is the location of both hoist1 and pallet1. Distributor1 is where hoist4 can be found. Hoist0 is located at depot0 and is available for use. Hoist1 is accessible, while hoist2 is located at depot2 and is available for work. Hoist3 is available for work and is situated at distributor0. Hoist4 is available, and hoist5 is located at distributor2 and is available for work. Pallet0 is at depot0 and has no crates on it. Pallet1 is clear of any crates. Pallet2 is located at depot2. Pallet3 is situated at distributor0. Pallet4 is clear of any crates and is located at distributor1. Pallet5 is clear of any crates and is situated at distributor2. Truck0 is located at distributor0, truck1 is at depot1, and truck2 is at depot0."}
{"question_id": "6edc09af-f1b4-49e2-b9e5-0937ab4a59fe", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot0 from depot1, crate2 is lifted from pallet0 at depot0 by hoist0, crate2 is loaded by hoist0 into truck2 at depot0, truck2 is driven to distributor1 from depot0, crate1 is lifted from pallet3 at depot3 by hoist3, at distributor1, hoist5 lifts crate3 off pallet5, at distributor1, hoist5 loads crate3 into truck2, at distributor1, hoist5 unloads crate2 from truck2, from distributor1, truck2 is driven to distributor2, at distributor1, hoist5 drops crate2 on pallet5, at distributor2, hoist6 lifts crate0 off pallet6, hoist6 loads crate0 into truck2 at distributor2, truck2 is driven to depot3 from distributor2, at depot3, hoist3 loads crate1 into truck2, hoist3 unloads crate0 from truck2 at depot3, truck2 is driven to distributor0 from depot3, hoist4 unloads crate3 from truck2 at distributor0, hoist3 drops crate0 on pallet3 at depot3 and at distributor0, hoist4 drops crate3 on pallet4 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "5614", "plan_length": 19, "initial_state_nl": "Crate0 is at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear, crate1 is located at depot3, crate2 is at depot0, crate2 is clear, crate2 is on top of pallet0, crate3 can be found located at distributor1, crate3 is clear, crate3 is on pallet5, depot1 is where truck2 is located, depot3 is where pallet3 is located, distributor0 is where truck1 is located, distributor2 is where pallet6 is located, hoist0 is available for work, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available, hoist2 is available, hoist2 is located at depot2, hoist3 is at depot3, hoist3 is available, hoist4 is available, hoist4 is located at distributor0, hoist5 is accessible, hoist5 is at distributor1, hoist6 can be found located at distributor2, hoist6 is available for work, pallet0 can be found located at depot0, pallet1 is at depot1, pallet1 is clear, pallet2 can be found located at depot2, pallet2 is clear of any crates, pallet3 has crate1 on it, pallet4 can be found located at distributor0, pallet4 is clear of any crates, pallet5 is at distributor1 and truck0 is at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 travels from depot1 to depot0, then hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2, after which truck2 is driven to distributor1. At distributor1, hoist5 lifts crate3 from pallet5, loads it into truck2, and unloads crate2 from truck2. Meanwhile, truck2 is driven from distributor1 to distributor2. At distributor1, hoist5 places crate2 on pallet5. At distributor2, hoist6 lifts crate0 from pallet6 and loads it into truck2. Then, truck2 is driven from distributor2 to depot3, where hoist3 loads crate1 into truck2 and unloads crate0. Subsequently, truck2 is driven from depot3 to distributor0, where hoist4 unloads crate3 from truck2. Finally, hoist3 places crate0 on pallet3 at depot3, and hoist4 places crate3 on pallet4 at distributor0, resulting in the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is positioned at distributor2, crate0 is empty, crate0 is placed on pallet6, crate1 is empty, crate1 is situated at depot3, crate2 is located at depot0, crate2 is empty, crate2 is positioned on top of pallet0, crate3 is situated at distributor1, crate3 is empty, crate3 is placed on pallet5, truck2 is stationed at depot1, pallet3 is located at depot3, truck1 is stationed at distributor0, pallet6 is located at distributor2, hoist0 is ready for operation, hoist0 is situated at depot0, hoist1 is available and located at depot1, hoist2 is available and situated at depot2, hoist3 is stationed at depot3 and is available, hoist4 is available and located at distributor0, hoist5 is accessible and situated at distributor1, hoist6 is available for operation and located at distributor2, pallet0 is situated at depot0, pallet1 is located at depot1 and is empty, pallet2 is situated at depot2 and is empty, crate1 is placed on pallet3, pallet4 is situated at distributor0 and is empty, pallet5 is located at distributor1, and truck0 is stationed at depot1."}
{"question_id": "3ba0018c-685e-45db-9382-1d4dc725e456", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, crate2 is lifted from pallet0 at depot0 by hoist0, at depot0, hoist0 loads crate2 into truck1, truck1 is driven from depot0 to depot1, at depot1, hoist1 unloads crate2 from truck1, hoist1 drops crate2 on pallet1 at depot1, at depot2, hoist2 lifts crate3 off pallet2, at depot2, hoist2 loads crate3 into truck2, from depot2, truck2 is driven to distributor3, hoist6 unloads crate3 from truck2 at distributor3, at distributor3, hoist6 drops crate3 on pallet6, at distributor2, hoist5 lifts crate1 off crate0, hoist5 loads crate1 into truck0 at distributor2, at distributor2, hoist5 lifts crate0 off pallet5, at distributor2, hoist5 loads crate0 into truck0, crate1 is unloaded by hoist5 from truck0 at distributor2, truck0 is driven to distributor0 from distributor2, hoist3 unloads crate0 from truck0 at distributor0 and crate0 is dropped on pallet3 at distributor0 by hoist3 to reach the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "answer": "212", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate1 is on crate0, crate2 can be found located at depot0, crate2 is clear, crate3 is at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where hoist1 is located, distributor1 is where hoist4 is located, distributor2 is where pallet5 is located, distributor3 is where hoist6 is located, hoist0 is accessible, hoist1 is accessible, hoist2 can be found located at depot2, hoist2 is accessible, hoist3 is available for work, hoist3 is located at distributor0, hoist4 is available for work, hoist5 is available, hoist5 is located at distributor2, hoist6 is accessible, pallet0 can be found located at depot0, pallet0 has crate2 on it, pallet1 is at depot1, pallet1 is clear of any crates, pallet2 is located at depot2, pallet3 is clear, pallet3 is located at distributor0, pallet4 is clear of any crates, pallet4 is located at distributor1, pallet6 is clear of any crates, pallet6 is located at distributor3, truck0 is located at distributor2, truck1 is at depot1 and truck2 is at depot2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck1 is driven from depot1 to depot0, where hoist0 lifts crate2 from pallet0 and loads it into truck1; truck1 then travels back to depot1, where hoist1 unloads crate2 and places it on pallet1. Meanwhile, at depot2, hoist2 lifts crate3 from pallet2, loads it into truck2, and truck2 is driven to distributor3; upon arrival, hoist6 unloads crate3 and drops it on pallet6. At distributor2, hoist5 lifts crate1 from crate0, loads it into truck0, lifts crate0 from pallet5, and loads it into truck0 as well. After unloading crate1 from truck0, truck0 is driven to distributor0, where hoist3 unloads crate0 and drops it on pallet3, resulting in the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2 and is placed on pallet5. Crate1, which is clear of other crates, is also located at distributor2 and positioned on top of crate0. Crate2 can be found at depot0 and is clear of any obstructions. Crate3 is located at depot2, clear of other crates, and is positioned on pallet2. \n\nDepot0 is the location of hoist0, while depot1 houses hoist1. Distributor1 is home to hoist4, distributor2 is where pallet5 is situated, and distributor3 is the location of hoist6. Hoist0, hoist1, and hoist2, which is located at depot2, are all accessible. Hoist3, situated at distributor0, and hoist4 are available for work, while hoist5, located at distributor2, and hoist6 are also available.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is situated at depot1 and is clear of any crates. Pallet2 is at depot2, while pallet3, which is clear, is located at distributor0. Pallet4, also clear of crates, is situated at distributor1, and pallet6, which is clear, is located at distributor3. The locations of the trucks are as follows: truck0 is at distributor2, truck1 is at depot1, and truck2 is at depot2."}
