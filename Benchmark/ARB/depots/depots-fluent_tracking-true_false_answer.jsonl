{"question_id": "657ff89a-aa59-4504-bdac-5500198c3a1e", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_10", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot0 from depot1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: crate0 is not clear of any crates, crate1 is not clear of any crates, crate2 is not clear of any crates, crate3 is not clear, hoist0 is not accessible, hoist1 is not available for work, hoist2 is not available, hoist3 is not available for work, hoist4 is not available for work, hoist5 is not available for work, hoist6 is not accessible, pallet0 is not clear, pallet1 is not clear of any crates, pallet2 is not clear of any crates, pallet3 is not clear of any crates, pallet4 is not clear, pallet5 is not clear and pallet6 is not clear of any crates?", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear of any crates, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on top of pallet0, crate3 is at distributor1, crate3 is clear of any crates, crate3 is on pallet5, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck0 is located, depot3 is where crate1 is located, depot3 is where hoist3 is located, distributor0 is where hoist4 is located, distributor0 is where pallet4 is located, distributor2 is where hoist6 is located, hoist0 can be found located at depot0, hoist0 is accessible, hoist1 is available for work, hoist2 is accessible, hoist2 is at depot2, hoist3 is available for work, hoist4 is available for work, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 is located at depot3, pallet4 is clear of any crates, pallet5 is at distributor1, pallet6 is located at distributor2, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: truck2 is driven from depot1 to depot0 to achieve the current state. In this state, are the following properties that involve negations True or False: crate0 is not clear of crates, crate1 is not clear of crates, crate2 is not clear of crates, crate3 is not clear, hoist0 is inaccessible, hoist1 is not available for work, hoist2 is unavailable, hoist3 is not available for work, hoist4 is not available for work, hoist5 is not available for work, hoist6 is inaccessible, pallet0 is not clear, pallet1 is not clear of crates, pallet2 is not clear of crates, pallet3 is not clear of crates, pallet4 is not clear, pallet5 is not clear, and pallet6 is not clear of crates?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 has no crates on top, crate1 is placed on top of pallet3, crate2 is located at depot0, crate2 has no crates on top, crate2 is positioned on top of pallet0, crate3 is situated at distributor1, crate3 has no crates on top, crate3 is positioned on pallet5, depot1 houses hoist1, depot1 houses pallet1, depot1 houses truck0, depot3 houses crate1, depot3 houses hoist3, distributor0 houses hoist4, distributor0 houses pallet4, distributor2 houses hoist6, hoist0 is situated at depot0, hoist0 is operational, hoist1 is ready for work, hoist2 is operational, hoist2 is situated at depot2, hoist3 is ready for work, hoist4 is ready for work, hoist5 is operational, hoist5 is situated at distributor1, hoist6 is available, pallet0 is situated at depot0, pallet1 has no crates on it, pallet2 has no crates on it, pallet2 is situated at depot2, pallet3 is situated at depot3, pallet4 has no crates on it, pallet5 is situated at distributor1, pallet6 is situated at distributor2, truck1 is situated at distributor0 and truck2 is situated at depot1."}
{"question_id": "445c9b76-0e9f-44e9-a3b8-25df489f0900", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_9", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1, at depot0, hoist0 lifts crate2 off pallet0, at depot0, hoist0 loads crate2 into truck1, truck1 is driven from depot0 to depot1, hoist1 unloads crate2 from truck1 at depot1, at depot1, hoist1 drops crate2 on pallet1, at depot2, hoist2 lifts crate3 off pallet2, crate3 is loaded by hoist2 into truck2 at depot2, truck2 is driven to distributor3 from depot2, crate3 is unloaded by hoist6 from truck2 at distributor3, hoist6 drops crate3 on pallet6 at distributor3, hoist5 lifts crate1 from crate0 at distributor2, hoist5 loads crate1 into truck0 at distributor2, crate0 is lifted from pallet5 at distributor2 by hoist5, hoist5 loads crate0 into truck0 at distributor2, hoist5 unloads crate1 from truck0 at distributor2, from distributor2, truck0 is driven to distributor0, at distributor0, hoist3 unloads crate0 from truck0 and at distributor0, hoist3 drops crate0 on pallet3 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: crate1 is not clear of any crates, hoist5 is not accessible, pallet1 is not clear, pallet3 is not clear of any crates and pallet6 is not clear of any crates?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 has crate1 on it, crate0 is located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate2 is clear of any crates, crate2 is located at depot0, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where hoist5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is accessible, hoist6 is available for work, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 is clear, pallet4 is clear, pallet4 is located at distributor1, pallet5 is at distributor2, pallet6 is clear of any crates, pallet6 is located at distributor3 and truck2 is at depot2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck1 travels from depot1 to depot0, where hoist0 removes crate2 from pallet0 and loads it into truck1; truck1 then proceeds from depot0 to depot1, where hoist1 unloads crate2 and places it on pallet1. Meanwhile, at depot2, hoist2 lifts crate3 from pallet2 and loads it into truck2, which then drives to distributor3; at distributor3, hoist6 unloads crate3 from truck2 and places it on pallet6. At distributor2, hoist5 lifts crate1 from crate0 and loads it into truck0, then lifts crate0 from pallet5 and loads it into truck0 as well. However, hoist5 unloads crate1 from truck0 at distributor2. Subsequently, truck0 drives from distributor2 to distributor0, where hoist3 unloads crate0 and places it on pallet3. In this resulting state, the following properties involving negations are evaluated as True or False: crate1 is not clear of any crates, hoist5 is inaccessible, pallet1 is not clear, pallet3 is not clear of any crates, and pallet6 is not clear of any crates.", "initial_state_nl_paraphrased": "Crate0 has crate1 stacked on top of it, and it is situated at distributor2. Additionally, crate0 is placed on pallet5. Crate1, on the other hand, has no crates on top of it and is also located at distributor2. Similarly, crate2 is clear of any crates and is situated at depot0. Crate3 can be found at depot2, has no crates on top of it, and is placed on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is located. Distributor0 houses both hoist3 and pallet3, and distributor2 is home to both hoist5 and truck0. Distributor3 is where hoist6 is located. \n\nHoist0 is available for work, and hoist1 is available and situated at depot1. Hoist2 is also available and located at depot2. Hoist3 is available for work, while hoist4 is available and located at distributor1. Hoist5 is accessible, and hoist6 is available for work.\n\nPallet0 has crate2 on it and is situated at depot0. Pallet1 is empty and located at depot1. Pallet2 is situated at depot2, and pallet3 is empty. Pallet4 is also empty and located at distributor1. Pallet5 is at distributor2, and pallet6 is empty and located at distributor3. Lastly, truck2 is situated at depot2."}
{"question_id": "1029dc40-d669-41e6-9d51-98db137ee251", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_14", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: crate0 can be found located at depot1, crate0 can be found located at depot2, crate0 has crate1 on it, crate0 is at distributor1, crate0 is in truck2, crate0 is located at depot0, crate0 is located at distributor0, crate0 is located at distributor2, crate0 is on pallet1, crate0 is on pallet5, crate0 is on top of crate0, crate0 is on top of crate1, crate1 can be found located at distributor0, crate1 has crate1 on it, crate1 is at distributor2, crate1 is located at depot1, crate1 is on pallet2, crate1 is on top of crate2, crate1 is on top of pallet0, crate1 is on top of pallet1, crate1 is on top of pallet5, crate1 is on top of pallet6, crate2 can be found located at distributor0, crate2 can be found located at distributor1, crate2 has crate0 on it, crate2 is at depot0, crate2 is in truck1, crate2 is inside truck2, crate2 is located at distributor2, crate2 is located at distributor3, crate2 is on crate1, crate2 is on crate2, crate2 is on pallet0, crate2 is on pallet1, crate2 is on pallet4, crate2 is on pallet6, crate2 is on top of crate0, crate2 is on top of crate3, crate2 is on top of pallet2, crate2 is on top of pallet5, crate3 can be found located at depot2, crate3 has crate0 on it, crate3 has crate1 on it, crate3 is at depot1, crate3 is at distributor1, crate3 is in truck0, crate3 is in truck2, crate3 is inside truck1, crate3 is located at depot0, crate3 is on crate2, crate3 is on crate3, crate3 is on pallet0, crate3 is on pallet1, crate3 is on pallet2, crate3 is on pallet3, crate3 is on top of crate0, crate3 is on top of crate1, crate3 is on top of pallet4, crate3 is on top of pallet5, depot0 is where crate1 is located, depot0 is where hoist0 is located, depot0 is where pallet2 is located, depot0 is where pallet4 is located, depot0 is where pallet5 is located, depot1 is where crate2 is located, depot1 is where hoist0 is located, depot1 is where hoist3 is located, depot1 is where pallet5 is located, depot1 is where truck0 is located, depot1 is where truck2 is located, depot2 is where crate1 is located, depot2 is where crate2 is located, depot2 is where pallet0 is located, depot2 is where pallet2 is located, depot2 is where pallet3 is located, depot2 is where pallet4 is located, depot2 is where truck0 is located, depot2 is where truck2 is located, distributor0 is where crate3 is located, distributor0 is where hoist6 is located, distributor0 is where pallet1 is located, distributor0 is where pallet6 is located, distributor0 is where truck0 is located, distributor0 is where truck1 is located, distributor1 is where crate1 is located, distributor1 is where hoist0 is located, distributor1 is where hoist1 is located, distributor1 is where pallet1 is located, distributor1 is where truck2 is located, distributor2 is where crate3 is located, distributor2 is where hoist4 is located, distributor2 is where hoist6 is located, distributor2 is where pallet3 is located, distributor2 is where pallet4 is located, distributor3 is where crate0 is located, distributor3 is where crate1 is located, distributor3 is where crate3 is located, distributor3 is where hoist2 is located, distributor3 is where hoist3 is located, distributor3 is where hoist5 is located, distributor3 is where pallet1 is located, distributor3 is where pallet2 is located, distributor3 is where pallet3 is located, hoist0 can be found located at distributor0, hoist0 can be found located at distributor2, hoist0 can be found located at distributor3, hoist0 is at depot2, hoist0 is lifting crate0, hoist0 is lifting crate2, hoist0 is raising crate1, hoist0 is raising crate3, hoist1 can be found located at depot1, hoist1 can be found located at distributor0, hoist1 can be found located at distributor2, hoist1 is at depot0, hoist1 is at depot2, hoist1 is at distributor3, hoist1 is elevating crate2, hoist1 is lifting crate0, hoist1 is lifting crate1, hoist1 is lifting crate3, hoist2 can be found located at depot1, hoist2 can be found located at distributor1, hoist2 is at depot2, hoist2 is at distributor0, hoist2 is lifting crate0, hoist2 is lifting crate2, hoist2 is lifting crate3, hoist2 is located at depot0, hoist2 is located at distributor2, hoist2 is raising crate1, hoist3 is at distributor0, hoist3 is at distributor2, hoist3 is elevating crate1, hoist3 is lifting crate0, hoist3 is lifting crate2, hoist3 is located at depot0, hoist3 is located at depot2, hoist3 is located at distributor1, hoist3 is raising crate3, hoist4 is at depot0, hoist4 is at distributor3, hoist4 is elevating crate2, hoist4 is elevating crate3, hoist4 is lifting crate0, hoist4 is located at depot1, hoist4 is located at depot2, hoist4 is located at distributor0, hoist4 is located at distributor1, hoist4 is raising crate1, hoist5 is at depot2, hoist5 is at distributor0, hoist5 is at distributor1, hoist5 is at distributor2, hoist5 is elevating crate2, hoist5 is lifting crate0, hoist5 is lifting crate1, hoist5 is lifting crate3, hoist5 is located at depot0, hoist5 is located at depot1, hoist6 can be found located at depot0, hoist6 can be found located at depot1, hoist6 can be found located at distributor3, hoist6 is at distributor1, hoist6 is lifting crate1, hoist6 is lifting crate2, hoist6 is lifting crate3, hoist6 is located at depot2, hoist6 is raising crate0, pallet0 can be found located at depot0, pallet0 can be found located at distributor0, pallet0 can be found located at distributor2, pallet0 has crate0 on it, pallet0 is at depot1, pallet0 is at distributor1, pallet0 is located at distributor3, pallet1 can be found located at depot0, pallet1 can be found located at depot2, pallet1 is located at depot1, pallet1 is located at distributor2, pallet2 can be found located at depot1, pallet2 has crate0 on it, pallet2 is at distributor0, pallet2 is at distributor2, pallet2 is located at distributor1, pallet3 can be found located at depot0, pallet3 can be found located at depot1, pallet3 can be found located at distributor1, pallet3 has crate0 on it, pallet3 has crate1 on it, pallet3 has crate2 on it, pallet3 is at distributor0, pallet4 can be found located at distributor3, pallet4 has crate0 on it, pallet4 has crate1 on it, pallet4 is at depot1, pallet4 is at distributor0, pallet4 is located at distributor1, pallet5 can be found located at depot2, pallet5 can be found located at distributor1, pallet5 can be found located at distributor2, pallet5 can be found located at distributor3, pallet5 is located at distributor0, pallet6 can be found located at depot0, pallet6 can be found located at depot1, pallet6 can be found located at distributor2, pallet6 can be found located at distributor3, pallet6 has crate0 on it, pallet6 has crate3 on it, pallet6 is located at depot2, pallet6 is located at distributor1, truck0 can be found located at distributor3, truck0 contains crate0, truck0 contains crate1, truck0 contains crate2, truck0 is at depot0, truck0 is at distributor2, truck0 is located at distributor1, truck1 can be found located at distributor1, truck1 can be found located at distributor3, truck1 contains crate0, truck1 contains crate1, truck1 is at depot1, truck1 is at depot2, truck1 is located at depot0, truck1 is located at distributor2, truck2 contains crate1, truck2 is at depot0, truck2 is at distributor2, truck2 is located at distributor0 and truck2 is located at distributor3?", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 has crate1 on it, crate0 is located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate2 is clear of any crates, crate2 is located at depot0, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where hoist5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is accessible, hoist6 is available for work, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 is clear, pallet4 is clear, pallet4 is located at distributor1, pallet5 is at distributor2, pallet6 is clear of any crates, pallet6 is located at distributor3 and truck2 is at depot2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: \n\ncrate0 is situated at depot1, crate0 is situated at depot2, crate1 is on top of crate0, crate0 is at distributor1, crate0 is inside truck2, crate0 is located at depot0, crate0 is located at distributor0, crate0 is located at distributor2, crate0 is on pallet1, crate0 is on pallet5, crate0 is on top of itself, crate0 is on top of crate1, crate1 is situated at distributor0, crate1 is on top of itself, crate1 is at distributor2, crate1 is located at depot1, crate1 is on pallet2, crate1 is on top of crate2, crate1 is on top of pallet0, crate1 is on top of pallet1, crate1 is on top of pallet5, crate1 is on top of pallet6, crate2 is situated at distributor0, crate2 is situated at distributor1, crate0 is on top of crate2, crate2 is at depot0, crate2 is inside truck1, crate2 is inside truck2, crate2 is located at distributor2, crate2 is located at distributor3, crate2 is on crate1, crate2 is on top of itself, crate2 is on pallet0, crate2 is on pallet1, crate2 is on pallet4, crate2 is on pallet6, crate2 is on top of crate0, crate2 is on top of crate3, crate2 is on top of pallet2, crate2 is on top of pallet5, crate3 is situated at depot2, crate0 is on top of crate3, crate1 is on top of crate3, crate3 is at depot1, crate3 is at distributor1, crate3 is inside truck0, crate3 is inside truck2, crate3 is inside truck1, crate3 is located at depot0, crate3 is on crate2, crate3 is on top of itself, crate3 is on pallet0, crate3 is on pallet1, crate3 is on pallet2, crate3 is on pallet3, crate3 is on top of crate0, crate3 is on top of crate1, crate3 is on top of pallet4, crate3 is on top of pallet5, crate1 is located at depot0, hoist0 is located at depot0, pallet2 is located at depot0, pallet4 is located at depot0, pallet5 is located at depot0, crate2 is located at depot1, hoist0 is located at depot1, hoist3 is located at depot1, pallet5 is located at depot1, truck0 is located at depot1, truck2 is located at depot1, crate1 is located at depot2, crate2 is located at depot2, pallet0 is located at depot2, pallet2 is located at depot2, pallet3 is located at depot2, pallet4 is located at depot2, truck0 is located at depot2, truck2 is located at depot2, crate3 is located at distributor0, hoist6 is located at distributor0, pallet1 is located at distributor0, pallet6 is located at distributor0, truck0 is located at distributor0, truck1 is located at distributor0, crate1 is located at distributor1, hoist0 is located at distributor1, hoist1 is located at distributor1, pallet1 is located at distributor1, truck2 is located at distributor1, crate3 is located at distributor2, hoist4 is located at distributor2, hoist6 is located at distributor2, pallet3 is located at distributor2, pallet4 is located at distributor2, crate0 is located at distributor3, crate1 is located at distributor3, crate3 is located at distributor3, hoist2 is located at distributor3, hoist3 is located at distributor3, hoist5 is located at distributor3, pallet1 is located at distributor3, pallet2 is located at distributor3, pallet3 is located at distributor3, hoist0 is situated at distributor0, hoist0 is situated at distributor2, hoist0 is situated at distributor3, hoist0 is at depot2, hoist0 is lifting crate0, hoist0 is lifting crate2, hoist0 is raising crate1, hoist0 is raising crate3, hoist1 is situated at depot1, hoist1 is situated at distributor0, hoist1 is situated at distributor2, hoist1 is at depot0, hoist1 is at depot2, hoist1 is at distributor3, hoist1 is elevating crate2, hoist1 is lifting crate0, hoist1 is lifting crate1, hoist1 is lifting crate3, hoist2 is situated at depot1, hoist2 is situated at distributor1, hoist2 is at depot2, hoist2 is at distributor0, hoist2 is lifting crate0, hoist2 is lifting crate2, hoist2 is lifting crate3, hoist2 is located at depot0, hoist2 is located at distributor2, hoist2 is raising crate1, hoist3 is at distributor0, hoist3 is at distributor2, hoist3 is elevating crate1, hoist3 is lifting crate0, hoist3 is lifting crate2, hoist3 is located at depot0, hoist3 is located at depot2, hoist3 is located at distributor1, hoist3 is raising crate3, hoist4 is at depot0, hoist4 is at distributor3, hoist4 is elevating crate2, hoist4 is elevating crate3, hoist4 is lifting crate0, hoist4 is located at depot1, hoist4 is located at depot2, hoist4 is located at distributor0, hoist4 is located at distributor1, hoist4 is raising crate1, hoist5 is at depot2, hoist5 is at distributor0, hoist5 is at distributor1, hoist5 is at distributor2, hoist5 is elevating crate2, hoist5 is lifting crate0, hoist5 is lifting crate1, hoist5 is lifting crate3, hoist5 is located at depot0, hoist5 is located at depot1, hoist6 is situated at depot0, hoist6 is situated at depot1, hoist6 is situated at distributor3, hoist6 is at distributor1, hoist6 is lifting crate1, hoist6 is lifting crate2, hoist6 is lifting crate3, hoist6 is located at depot2, hoist6 is raising crate0, pallet0 is situated at depot0, pallet0 is situated at distributor0, pallet0 is situated at distributor2, crate0 is on pallet0, pallet0 is at depot1, pallet0 is at distributor1, pallet0 is located at distributor3, pallet1 is situated at depot0, pallet1 is situated at depot2, pallet1 is located at depot1, pallet1 is located at distributor2, pallet2 is situated at depot1, crate0 is on pallet2, pallet2 is at distributor0, pallet2 is at distributor2, pallet2 is located at distributor1, pallet3 is situated at depot0, pallet3 is situated at depot1, pallet3 is situated at distributor1, crate0 is on pallet3, crate1 is on pallet3, crate2 is on pallet3, pallet3 is at distributor0, pallet4 is situated at distributor3, crate0 is on pallet4, crate1 is on pallet4, pallet4 is at depot1, pallet4 is at distributor0, pallet4 is located at distributor1, pallet5 is situated at depot2, pallet5 is situated at distributor1, pallet5 is situated at distributor2, pallet5 is situated at distributor3, pallet5 is located at distributor0, pallet6 is situated at depot0, pallet6 is situated at depot1, pallet6 is situated at distributor2, pallet6 is situated at distributor3, crate0 is on pallet6, crate3 is on pallet6, pallet6 is located at depot2, pallet6 is located at distributor1, truck0 is situated at distributor3, crate0 is inside truck0, crate1 is inside truck0, crate2 is inside truck0, truck0 is at depot0, truck0 is at distributor2, truck0 is located at distributor1, truck1 is situated at distributor1, truck1 is situated at distributor3, crate0 is inside truck1, crate1 is inside truck1, truck1 is at depot1, truck1 is at depot2, truck1 is located at depot0, truck1 is located at distributor2, crate1 is inside truck2, truck2 is at depot0, truck2 is at distributor2, truck2 is located at distributor0 and truck2 is located at distributor3.", "initial_state_nl_paraphrased": "Crate0 has crate1 stacked on top of it, and it is situated at distributor2. Additionally, crate0 is placed on pallet5. Crate1, on the other hand, has no crates on top of it and is also located at distributor2. Similarly, crate2 is clear of any crates and is situated at depot0. Crate3 can be found at depot2, has no crates on top of it, and is placed on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is located. Distributor0 houses both hoist3 and pallet3, and distributor2 is home to both hoist5 and truck0. Distributor3 is where hoist6 is located. \n\nHoist0 is available for work, and hoist1 is available and situated at depot1. Hoist2 is also available and located at depot2. Hoist3 is available for work, while hoist4 is available and located at distributor1. Hoist5 is accessible, and hoist6 is available for work.\n\nPallet0 has crate2 on it and is situated at depot0. Pallet1 is empty and located at depot1. Pallet2 is situated at depot2, and pallet3 is empty. Pallet4 is also empty and located at distributor1. Pallet5 is at distributor2, and pallet6 is empty and located at distributor3. Lastly, truck2 is situated at depot2."}
{"question_id": "bf447efe-aff6-4bcb-a7da-d7ec464da335", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_9", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: crate0 is not clear, pallet0 is not clear, pallet2 is not clear of any crates and pallet5 is not clear?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 has crate1 on it, crate0 is located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate2 is clear of any crates, crate2 is located at depot0, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where hoist5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is accessible, hoist6 is available for work, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 is clear, pallet4 is clear, pallet4 is located at distributor1, pallet5 is at distributor2, pallet6 is clear of any crates, pallet6 is located at distributor3 and truck2 is at depot2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: truck1 is driven from depot1 to depot0 to achieve the current state. In this state, are the following properties that involve negations True or False: crate0 is not clear, pallet0 is not clear, pallet2 has crates on it, and pallet5 is not clear?", "initial_state_nl_paraphrased": "Crate0 has crate1 stacked on top of it, and it is situated at distributor2. Additionally, crate0 is placed on pallet5. Crate1, on the other hand, has no crates on top of it and is also located at distributor2. Similarly, crate2 is clear of any crates and is situated at depot0. Crate3 can be found at depot2, has no crates on top of it, and is placed on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is located. Distributor0 houses both hoist3 and pallet3, and distributor2 is home to both hoist5 and truck0. Distributor3 is where hoist6 is located. \n\nHoist0 is available for work, and hoist1 is available and situated at depot1. Hoist2 is also available and located at depot2. Hoist3 is available for work, while hoist4 is available and located at distributor1. Hoist5 is accessible, and hoist6 is available for work.\n\nPallet0 has crate2 on it and is situated at depot0. Pallet1 is empty and located at depot1. Pallet2 is situated at depot2, and pallet3 is empty. Pallet4 is also empty and located at distributor1. Pallet5 is at distributor2, and pallet6 is empty and located at distributor3. Lastly, truck2 is situated at depot2."}
{"question_id": "7e9dcfc0-ae94-4a92-b9bb-164c369a7dc7", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_9", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven to distributor1 from depot0, crate1 is lifted from pallet2 at depot2 by hoist2, at depot2, hoist2 loads crate1 into truck0, truck0 is driven to distributor0 from depot2, at distributor0, hoist3 unloads crate1 from truck0, at distributor0, hoist3 drops crate1 on pallet3, hoist4 lifts crate0 from pallet4 at distributor1, hoist4 loads crate0 into truck2 at distributor1, crate3 is lifted from pallet5 at distributor2 by hoist5, crate3 is loaded by hoist5 into truck1 at distributor2, from distributor2, truck1 is driven to distributor1, hoist4 unloads crate3 from truck1 at distributor1, hoist4 drops crate3 on pallet4 at distributor1, crate2 is unloaded by hoist4 from truck2 at distributor1, truck2 is driven to depot1 from distributor1, crate0 is unloaded by hoist1 from truck2 at depot1 and at depot1, hoist1 drops crate0 on pallet1 to reach the current state. In this state, is it True or False that hoist4 is elevating crate2?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor1, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot0 is where pallet0 is located, depot0 is where truck2 is located, depot2 is where hoist2 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where crate3 is located, distributor2 is where hoist5 is located, distributor2 is where pallet5 is located, hoist0 is accessible, hoist1 is available for work, hoist1 is located at depot1, hoist2 is available, hoist3 is available for work, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is available for work, pallet0 has crate2 on it, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is at depot2, pallet3 is clear of any crates, pallet4 is located at distributor1, truck0 is located at depot2 and truck1 is at distributor2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: at depot0, hoist0 first lifts crate2 from pallet0 and then loads it into truck2, after which truck2 is driven from depot0 to distributor1. Meanwhile, at depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven from depot2 to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 and places it on pallet3 at distributor0. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2, while at distributor2, hoist5 lifts crate3 from pallet5 and loads it into truck1, which is then driven to distributor1. At distributor1, hoist4 unloads crate3 from truck1 and places it on pallet4, and also unloads crate2 from truck2. Truck2 is then driven to depot1, where hoist1 unloads crate0 from truck2 and places it on pallet1, resulting in the current state. In this state, is it True or False that hoist4 is elevating crate2?", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor1, crate0 is positioned on top of pallet4, crate1 is located at depot2, crate1 has no crates on it, crate1 is placed on pallet2, crate2 has no crates on it, crate3 has no crates on it, crate3 is positioned on top of pallet5, depot0 is the location of crate2, depot0 is the location of hoist0, depot0 is the location of pallet0, depot0 is the location of truck2, depot2 is the location of hoist2, distributor0 is the location of hoist3, distributor0 is the location of pallet3, distributor2 is the location of crate3, distributor2 is the location of hoist5, distributor2 is the location of pallet5, hoist0 is accessible, hoist1 is available for use, hoist1 is situated at depot1, hoist2 is available, hoist3 is available for use, hoist4 is accessible, hoist4 is situated at distributor1, hoist5 is available for use, pallet0 has crate2 on it, pallet1 has no crates on it, pallet1 is situated at depot1, pallet2 is located at depot2, pallet3 has no crates on it, pallet4 is situated at distributor1, truck0 is located at depot2 and truck1 is located at distributor2."}
{"question_id": "bb3f59ca-a90e-499f-b45c-b16b446363b3", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_10", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven from depot1 to depot0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: crate0 is not clear of any crates, crate1 is not clear, crate2 is not clear, crate3 is not clear, hoist0 is not available for work, hoist1 is not accessible, hoist2 is not accessible, hoist3 is not available for work, hoist4 is not available for work, hoist5 is not accessible, hoist6 is not available for work, pallet0 is not clear, pallet1 is not clear, pallet2 is not clear of any crates, pallet3 is not clear, pallet4 is not clear, pallet5 is not clear and pallet6 is not clear of any crates?", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 has crate1 on it, crate0 is located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate2 is clear of any crates, crate2 is located at depot0, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where hoist5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is accessible, hoist6 is available for work, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 is clear, pallet4 is clear, pallet4 is located at distributor1, pallet5 is at distributor2, pallet6 is clear of any crates, pallet6 is located at distributor3 and truck2 is at depot2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: truck1 is driven from depot1 to depot0 to reach the current state. In this state, are the following properties that involve negations True or False: crate0 is not empty, crate1 is not empty, crate2 is not empty, crate3 is not empty, hoist0 is not operational, hoist1 is not reachable, hoist2 is not reachable, hoist3 is not operational, hoist4 is not operational, hoist5 is not reachable, hoist6 is not operational, pallet0 is not empty, pallet1 is not empty, pallet2 is not empty, pallet3 is not empty, pallet4 is not empty, pallet5 is not empty and pallet6 is not empty?", "initial_state_nl_paraphrased": "Crate0 has crate1 stacked on top of it, and it is situated at distributor2. Additionally, crate0 is placed on pallet5. Crate1, on the other hand, has no crates on top of it and is also located at distributor2. Similarly, crate2 is clear of any crates and is situated at depot0. Crate3 can be found at depot2, has no crates on top of it, and is placed on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is located. Distributor0 houses both hoist3 and pallet3, and distributor2 is home to both hoist5 and truck0. Distributor3 is where hoist6 is located. \n\nHoist0 is available for work, and hoist1, which is located at depot1, is also available. Hoist2, situated at depot2, is available as well. Hoist3 is available for work, and hoist4, located at distributor1, is available too. Hoist5 is accessible, and hoist6 is available for work.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is empty and situated at depot1. Pallet2 is at depot2, and pallet3 is empty. Pallet4 is also empty and located at distributor1. Pallet5 is at distributor2, and pallet6, which is empty, is located at distributor3. Lastly, truck2 is situated at depot2."}
{"question_id": "559abcb6-a770-460b-9d0a-57b3b656985e", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_9", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot2 from depot0 to reach the current state. In this state, is it True or False that crate0 is on pallet3?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 is clear, crate0 is on pallet3, crate1 is on pallet2, crate2 can be found located at depot2, crate2 is on top of crate1, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on top of crate2, depot2 is where crate1 is located, depot2 is where pallet2 is located, distributor0 is where crate0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, pallet0 is at depot0, pallet0 is clear, pallet1 can be found located at depot1, pallet1 is clear of any crates, pallet4 is at distributor1, pallet4 is clear of any crates, pallet5 is at distributor2, pallet5 is clear of any crates, truck0 can be found located at distributor0, truck1 is located at depot1 and truck2 is at depot0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: truck2 is driven from depot0 to depot2 to achieve the current state. In this state, is it True or False that crate0 is on pallet3?", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is positioned on pallet3, crate1 is placed on pallet2, crate2 is situated at depot2, crate2 is stacked on top of crate1, crate3 is located at depot2, crate3 has no crates on it, crate3 is positioned on top of crate2, depot2 is the location of crate1, depot2 is also the location of pallet2, distributor0 is the location of crate0, distributor0 is also the location of hoist3, distributor0 is also the location of pallet3, hoist0 is available for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for work, hoist2 is situated at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is located at distributor2, hoist5 is available, pallet0 is situated at depot0, pallet0 is empty, pallet1 is located at depot1, pallet1 has no crates on it, pallet4 is situated at distributor1, pallet4 has no crates on it, pallet5 is situated at distributor2, pallet5 has no crates on it, truck0 is located at distributor0, truck1 is situated at depot1 and truck2 is situated at depot0."}
{"question_id": "7b57ffd8-0a17-419e-8eea-37550e4d2530", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0 to reach the current state. In this state, is it True or False that pallet5 is not clear of any crates?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor1, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot0 is where pallet0 is located, depot0 is where truck2 is located, depot2 is where hoist2 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where crate3 is located, distributor2 is where hoist5 is located, distributor2 is where pallet5 is located, hoist0 is accessible, hoist1 is available for work, hoist1 is located at depot1, hoist2 is available, hoist3 is available for work, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is available for work, pallet0 has crate2 on it, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is at depot2, pallet3 is clear of any crates, pallet4 is located at distributor1, truck0 is located at depot2 and truck1 is at distributor2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0 to reach the current state. In this state, is it True or False that no crates are on pallet5?", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor1, crate0 is positioned on top of pallet4, crate1 is situated at depot2, crate1 has no crates on it, crate1 is placed on pallet2, crate2 has no crates on it, crate3 has no crates on it, crate3 is positioned on top of pallet5, depot0 is the location of crate2, depot0 is the location of hoist0, depot0 is the location of pallet0, depot0 is the location of truck2, depot2 is the location of hoist2, distributor0 is the location of hoist3, distributor0 is the location of pallet3, distributor2 is the location of crate3, distributor2 is the location of hoist5, distributor2 is the location of pallet5, hoist0 is accessible for use, hoist1 is available for work and is located at depot1, hoist2 is available for use, hoist3 is available for work, hoist4 is accessible for use and is located at distributor1, hoist5 is available for work, pallet0 has crate2 placed on it, pallet1 has no crates on it and is located at depot1, pallet2 is situated at depot2, pallet3 has no crates on it, pallet4 is located at distributor1, truck0 is situated at depot2 and truck1 is situated at distributor2."}
{"question_id": "b9bd5cd4-204e-44de-a6b7-26bc63360f30", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_15", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven to distributor1 from depot0, hoist2 lifts crate1 from pallet2 at depot2, crate1 is loaded by hoist2 into truck0 at depot2, from depot2, truck0 is driven to distributor0, hoist3 unloads crate1 from truck0 at distributor0, crate1 is dropped on pallet3 at distributor0 by hoist3, crate0 is lifted from pallet4 at distributor1 by hoist4 and hoist4 loads crate0 into truck2 at distributor1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: crate0 cannot be found located at depot1, crate0 cannot be found located at distributor0, crate0 cannot be found located at distributor2, crate0 does not have crate0 on it, crate0 does not have crate2 on it, crate0 is not at depot2, crate0 is not at distributor1, crate0 is not in truck0, crate0 is not in truck1, crate0 is not located at depot0, crate0 is not on pallet2, crate0 is not on top of crate3, crate0 is not on top of pallet1, crate1 cannot be found located at depot0, crate1 does not have crate0 on it, crate1 does not have crate2 on it, crate1 is not at depot2, crate1 is not at distributor2, crate1 is not in truck1, crate1 is not inside truck0, crate1 is not on crate0, crate1 is not on crate3, crate1 is not on pallet1, crate1 is not on pallet4, crate1 is not on pallet5, crate1 is not on top of crate1, crate1 is not on top of crate2, crate1 is not on top of pallet2, crate2 cannot be found located at depot0, crate2 does not have crate0 on it, crate2 is not at distributor0, crate2 is not at distributor1, crate2 is not at distributor2, crate2 is not inside truck1, crate2 is not located at depot1, crate2 is not located at depot2, crate2 is not on pallet2, crate2 is not on pallet5, crate2 is not on top of crate2, crate2 is not on top of crate3, crate2 is not on top of pallet0, crate2 is not on top of pallet1, crate3 cannot be found located at depot1, crate3 cannot be found located at depot2, crate3 cannot be found located at distributor0, crate3 is not in truck2, crate3 is not inside truck0, crate3 is not inside truck1, crate3 is not located at distributor1, crate3 is not on crate0, crate3 is not on crate2, crate3 is not on crate3, crate3 is not on pallet0, crate3 is not on top of crate1, crate3 is not on top of pallet1, crate3 is not on top of pallet2, crate3 is not on top of pallet4, depot0 is where crate3 is not located, depot0 is where hoist4 is not located, depot0 is where pallet4 is not located, depot0 is where truck0 is not located, depot1 is where crate1 is not located, depot1 is where hoist4 is not located, depot1 is where hoist5 is not located, depot1 is where pallet5 is not located, depot2 is where hoist0 is not located, depot2 is where hoist4 is not located, depot2 is where truck2 is not located, distributor0 is where hoist0 is not located, distributor0 is where hoist2 is not located, distributor0 is where hoist4 is not located, distributor1 is where crate1 is not located, distributor1 is where hoist0 is not located, distributor1 is where pallet2 is not located, distributor2 is where hoist3 is not located, distributor2 is where pallet3 is not located, distributor2 is where truck0 is not located, distributor2 is where truck2 is not located, hoist0 cannot be found located at depot1, hoist0 is not at distributor2, hoist0 is not elevating crate3, hoist0 is not lifting crate1, hoist0 is not raising crate0, hoist0 is not raising crate2, hoist1 cannot be found located at depot0, hoist1 cannot be found located at distributor1, hoist1 is not at depot2, hoist1 is not at distributor0, hoist1 is not at distributor2, hoist1 is not elevating crate1, hoist1 is not lifting crate0, hoist1 is not lifting crate2, hoist1 is not raising crate3, hoist2 is not at depot0, hoist2 is not at depot1, hoist2 is not at distributor2, hoist2 is not elevating crate0, hoist2 is not lifting crate1, hoist2 is not lifting crate2, hoist2 is not lifting crate3, hoist2 is not located at distributor1, hoist3 cannot be found located at depot1, hoist3 cannot be found located at distributor1, hoist3 is not at depot2, hoist3 is not lifting crate1, hoist3 is not lifting crate3, hoist3 is not located at depot0, hoist3 is not raising crate0, hoist3 is not raising crate2, hoist4 is not at distributor2, hoist4 is not elevating crate1, hoist4 is not lifting crate3, hoist4 is not raising crate0, hoist4 is not raising crate2, hoist5 is not at depot2, hoist5 is not elevating crate0, hoist5 is not elevating crate3, hoist5 is not lifting crate1, hoist5 is not lifting crate2, hoist5 is not located at depot0, hoist5 is not located at distributor0, hoist5 is not located at distributor1, pallet0 cannot be found located at depot1, pallet0 cannot be found located at distributor2, pallet0 does not have crate0 on it, pallet0 does not have crate1 on it, pallet0 is not at depot2, pallet0 is not at distributor0, pallet0 is not located at distributor1, pallet1 cannot be found located at depot0, pallet1 cannot be found located at depot2, pallet1 is not at distributor1, pallet1 is not at distributor2, pallet1 is not located at distributor0, pallet2 cannot be found located at distributor0, pallet2 cannot be found located at distributor2, pallet2 is not at depot1, pallet2 is not located at depot0, pallet3 cannot be found located at depot0, pallet3 does not have crate0 on it, pallet3 does not have crate2 on it, pallet3 does not have crate3 on it, pallet3 is not at depot1, pallet3 is not at depot2, pallet3 is not located at distributor1, pallet4 cannot be found located at distributor0, pallet4 does not have crate0 on it, pallet4 does not have crate2 on it, pallet4 is not located at depot1, pallet4 is not located at depot2, pallet4 is not located at distributor2, pallet5 cannot be found located at depot2, pallet5 cannot be found located at distributor1, pallet5 does not have crate0 on it, pallet5 is not at distributor0, pallet5 is not located at depot0, truck0 cannot be found located at depot1, truck0 does not contain crate2, truck0 is not at depot2, truck0 is not located at distributor1, truck1 cannot be found located at depot0, truck1 cannot be found located at depot1, truck1 is not at depot2, truck1 is not at distributor0, truck1 is not located at distributor1, truck2 cannot be found located at depot1, truck2 does not contain crate1, truck2 is not located at depot0 and truck2 is not located at distributor0?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor1, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot0 is where pallet0 is located, depot0 is where truck2 is located, depot2 is where hoist2 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where crate3 is located, distributor2 is where hoist5 is located, distributor2 is where pallet5 is located, hoist0 is accessible, hoist1 is available for work, hoist1 is located at depot1, hoist2 is available, hoist3 is available for work, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is available for work, pallet0 has crate2 on it, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is at depot2, pallet3 is clear of any crates, pallet4 is located at distributor1, truck0 is located at depot2 and truck1 is at distributor2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven to distributor1 from depot0, hoist2 lifts crate1 from pallet2 at depot2, crate1 is loaded by hoist2 into truck0 at depot2, from depot2, truck0 is driven to distributor0, hoist3 unloads crate1 from truck0 at distributor0, crate1 is dropped on pallet3 at distributor0 by hoist3, crate0 is lifted from pallet4 at distributor1 by hoist4 and hoist4 loads crate0 into truck2 at distributor1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: crate0 is not located at depot1, crate0 is not located at distributor0, crate0 is not located at distributor2, crate0 does not have crate0 on top of it, crate0 does not have crate2 on top of it, crate0 is not present at depot2, crate0 is not present at distributor1, crate0 is not inside truck0, crate0 is not inside truck1, crate0 is not located at depot0, crate0 is not on pallet2, crate0 is not on top of crate3, crate0 is not on top of pallet1, crate1 is not located at depot0, crate1 does not have crate0 on top of it, crate1 does not have crate2 on top of it, crate1 is not present at depot2, crate1 is not present at distributor2, crate1 is not inside truck1, crate1 is not inside truck0, crate1 is not on crate0, crate1 is not on crate3, crate1 is not on pallet1, crate1 is not on pallet4, crate1 is not on pallet5, crate1 is not on top of crate1, crate1 is not on top of crate2, crate1 is not on top of pallet2, crate2 is not located at depot0, crate2 does not have crate0 on top of it, crate2 is not present at distributor0, crate2 is not present at distributor1, crate2 is not present at distributor2, crate2 is not inside truck1, crate2 is not located at depot1, crate2 is not located at depot2, crate2 is not on pallet2, crate2 is not on pallet5, crate2 is not on top of crate2, crate2 is not on top of crate3, crate2 is not on top of pallet0, crate2 is not on top of pallet1, crate3 is not located at depot1, crate3 is not located at depot2, crate3 is not located at distributor0, crate3 is not inside truck2, crate3 is not inside truck0, crate3 is not inside truck1, crate3 is not located at distributor1, crate3 is not on crate0, crate3 is not on crate2, crate3 is not on crate3, crate3 is not on pallet0, crate3 is not on top of crate1, crate3 is not on top of pallet1, crate3 is not on top of pallet2, crate3 is not on top of pallet4, depot0 is not the location of crate3, depot0 is not the location of hoist4, depot0 is not the location of pallet4, depot0 is not the location of truck0, depot1 is not the location of crate1, depot1 is not the location of hoist4, depot1 is not the location of hoist5, depot1 is not the location of pallet5, depot2 is not the location of hoist0, depot2 is not the location of hoist4, depot2 is not the location of truck2, distributor0 is not the location of hoist0, distributor0 is not the location of hoist2, distributor0 is not the location of hoist4, distributor1 is not the location of crate1, distributor1 is not the location of hoist0, distributor1 is not the location of pallet2, distributor2 is not the location of hoist3, distributor2 is not the location of pallet3, distributor2 is not the location of truck0, distributor2 is not the location of truck2, hoist0 is not located at depot1, hoist0 is not present at distributor2, hoist0 is not elevating crate3, hoist0 is not lifting crate1, hoist0 is not lifting crate0, hoist0 is not lifting crate2, hoist1 is not located at depot0, hoist1 is not located at distributor1, hoist1 is not present at depot2, hoist1 is not present at distributor0, hoist1 is not present at distributor2, hoist1 is not elevating crate1, hoist1 is not lifting crate0, hoist1 is not lifting crate2, hoist1 is not lifting crate3, hoist2 is not present at depot0, hoist2 is not present at depot1, hoist2 is not present at distributor2, hoist2 is not elevating crate0, hoist2 is not lifting crate1, hoist2 is not lifting crate2, hoist2 is not lifting crate3, hoist2 is not located at distributor1, hoist3 is not located at depot1, hoist3 is not located at distributor1, hoist3 is not present at depot2, hoist3 is not lifting crate1, hoist3 is not lifting crate3, hoist3 is not located at depot0, hoist3 is not lifting crate0, hoist3 is not lifting crate2, hoist4 is not present at distributor2, hoist4 is not elevating crate1, hoist4 is not lifting crate3, hoist4 is not lifting crate0, hoist4 is not lifting crate2, hoist5 is not present at depot2, hoist5 is not elevating crate0, hoist5 is not elevating crate3, hoist5 is not lifting crate1, hoist5 is not lifting crate2, hoist5 is not located at depot0, hoist5 is not located at distributor0, hoist5 is not located at distributor1, pallet0 is not located at depot1, pallet0 is not located at distributor2, pallet0 does not have crate0 on it, pallet0 does not have crate1 on it, pallet0 is not present at depot2, pallet0 is not present at distributor0, pallet0 is not located at distributor1, pallet1 is not located at depot0, pallet1 is not located at depot2, pallet1 is not present at distributor1, pallet1 is not present at distributor2, pallet1 is not located at distributor0, pallet2 is not located at distributor0, pallet2 is not located at distributor2, pallet2 is not present at depot1, pallet2 is not located at depot0, pallet3 is not located at depot0, pallet3 does not have crate0 on it, pallet3 does not have crate2 on it, pallet3 does not have crate3 on it, pallet3 is not present at depot1, pallet3 is not present at depot2, pallet3 is not located at distributor1, pallet4 is not located at distributor0, pallet4 does not have crate0 on it, pallet4 does not have crate2 on it, pallet4 is not located at depot1, pallet4 is not located at depot2, pallet4 is not located at distributor2, pallet5 is not located at depot2, pallet5 is not located at distributor1, pallet5 does not have crate0 on it, pallet5 is not present at distributor0, pallet5 is not located at depot0, truck0 is not located at depot1, truck0 does not contain crate2, truck0 is not present at depot2, truck0 is not located at distributor1, truck1 is not located at depot0, truck1 is not located at depot1, truck1 is not present at depot2, truck1 is not present at distributor0, truck1 is not located at distributor1, truck2 is not located at depot1, truck2 does not contain crate1, truck2 is not located at depot0 and truck2 is not located at distributor0?", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor1, crate0 is positioned on top of pallet4, crate1 is located at depot2, crate1 has no crates on it, crate1 is placed on pallet2, crate2 has no crates on it, crate3 has no crates on it, crate3 is positioned on top of pallet5, depot0 is the location of crate2, depot0 is the location of hoist0, depot0 is the location of pallet0, depot0 is the location of truck2, depot2 is the location of hoist2, distributor0 is the location of hoist3, distributor0 is the location of pallet3, distributor2 is the location of crate3, distributor2 is the location of hoist5, distributor2 is the location of pallet5, hoist0 is accessible for use, hoist1 is available for work and is located at depot1, hoist2 is available for use, hoist3 is available for work, hoist4 is accessible for use and is located at distributor1, hoist5 is available for work, pallet0 has crate2 placed on it, pallet1 has no crates on it and is located at depot1, pallet2 is situated at depot2, pallet3 has no crates on it, pallet4 is located at distributor1, truck0 is situated at depot2 and truck1 is at distributor2."}
{"question_id": "6db5bb27-8d0c-49c8-a94b-a8bb70800edc", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_10", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot0 from depot1, crate2 is lifted from pallet0 at depot0 by hoist0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven to distributor1 from depot0, crate1 is lifted from pallet3 at depot3 by hoist3, at distributor1, hoist5 lifts crate3 off pallet5, crate3 is loaded by hoist5 into truck2 at distributor1, crate2 is unloaded by hoist5 from truck2 at distributor1, truck2 is driven from distributor1 to distributor2, hoist5 drops crate2 on pallet5 at distributor1, crate0 is lifted from pallet6 at distributor2 by hoist6, hoist6 loads crate0 into truck2 at distributor2, truck2 is driven to depot3 from distributor2, at depot3, hoist3 loads crate1 into truck2, hoist3 unloads crate0 from truck2 at depot3, from depot3, truck2 is driven to distributor0, hoist4 unloads crate3 from truck2 at distributor0, at depot3, hoist3 drops crate0 on pallet3 and crate3 is dropped on pallet4 at distributor0 by hoist4 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: crate0 is not clear of any crates, crate1 is not clear, crate2 is not clear of any crates, crate3 is not clear of any crates, hoist0 is not available, hoist1 is not available for work, hoist2 is not accessible, hoist3 is not available for work, hoist4 is not available for work, hoist5 is not accessible, hoist6 is not available, pallet0 is not clear, pallet1 is not clear of any crates, pallet2 is not clear, pallet3 is not clear, pallet4 is not clear of any crates, pallet5 is not clear and pallet6 is not clear of any crates?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear of any crates, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on top of pallet0, crate3 is at distributor1, crate3 is clear of any crates, crate3 is on pallet5, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck0 is located, depot3 is where crate1 is located, depot3 is where hoist3 is located, distributor0 is where hoist4 is located, distributor0 is where pallet4 is located, distributor2 is where hoist6 is located, hoist0 can be found located at depot0, hoist0 is accessible, hoist1 is available for work, hoist2 is accessible, hoist2 is at depot2, hoist3 is available for work, hoist4 is available for work, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 is located at depot3, pallet4 is clear of any crates, pallet5 is at distributor1, pallet6 is located at distributor2, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2, then truck2 proceeds to distributor1 from depot0. At distributor1, hoist5 lifts crate3 off pallet5, loads it into truck2, and unloads crate2 from truck2. Subsequently, truck2 is driven to distributor2, where hoist5 places crate2 on pallet5, and hoist6 lifts crate0 from pallet6 and loads it into truck2. Truck2 then travels to depot3, where hoist3 loads crate1 into truck2 and unloads crate0. From depot3, truck2 is driven to distributor0, where hoist4 unloads crate3 from truck2. At depot3, hoist3 places crate0 on pallet3, and at distributor0, hoist4 places crate3 on pallet4, resulting in the current state. In this state, are the following properties that involve negations True or False: crate0 is not clear of any crates, crate1 is not clear, crate2 is not clear of any crates, crate3 is not clear of any crates, hoist0 is not available, hoist1 is not available for work, hoist2 is not accessible, hoist3 is not available for work, hoist4 is not available for work, hoist5 is not accessible, hoist6 is not available, pallet0 is not clear, pallet1 is not clear of any crates, pallet2 is not clear, pallet3 is not clear, pallet4 is not clear of any crates, pallet5 is not clear, and pallet6 is not clear of any crates?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 has no crates on top, crate1 is placed on top of pallet3, crate2 is located at depot0, crate2 has no crates on top, crate2 is positioned on top of pallet0, crate3 is situated at distributor1, crate3 has no crates on top, crate3 is positioned on pallet5, depot1 houses hoist1, depot1 houses pallet1, depot1 houses truck0, depot3 houses crate1, depot3 houses hoist3, distributor0 houses hoist4, distributor0 houses pallet4, distributor2 houses hoist6, hoist0 is situated at depot0, hoist0 is operational, hoist1 is ready for work, hoist2 is operational, hoist2 is situated at depot2, hoist3 is ready for work, hoist4 is ready for work, hoist5 is operational, hoist5 is situated at distributor1, hoist6 is available, pallet0 is situated at depot0, pallet1 has no crates on it, pallet2 has no crates on it, pallet2 is situated at depot2, pallet3 is situated at depot3, pallet4 has no crates on it, pallet5 is situated at distributor1, pallet6 is situated at distributor2, truck1 is situated at distributor0 and truck2 is situated at depot1."}
{"question_id": "57f2c4bd-db88-4b69-aaee-dd6883120e3c", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_6", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot0 from depot1, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck2 at depot0, from depot0, truck2 is driven to distributor1, at depot3, hoist3 lifts crate1 off pallet3, at distributor1, hoist5 lifts crate3 off pallet5, hoist5 loads crate3 into truck2 at distributor1, crate2 is unloaded by hoist5 from truck2 at distributor1, truck2 is driven from distributor1 to distributor2, at distributor1, hoist5 drops crate2 on pallet5, at distributor2, hoist6 lifts crate0 off pallet6, hoist6 loads crate0 into truck2 at distributor2, from distributor2, truck2 is driven to depot3, crate1 is loaded by hoist3 into truck2 at depot3, hoist3 unloads crate0 from truck2 at depot3, from depot3, truck2 is driven to distributor0, crate3 is unloaded by hoist4 from truck2 at distributor0, at depot3, hoist3 drops crate0 on pallet3 and hoist4 drops crate3 on pallet4 at distributor0 to reach the current state. In this state, is it True or False that pallet3 is clear of any crates?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear of any crates, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on top of pallet0, crate3 is at distributor1, crate3 is clear of any crates, crate3 is on pallet5, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck0 is located, depot3 is where crate1 is located, depot3 is where hoist3 is located, distributor0 is where hoist4 is located, distributor0 is where pallet4 is located, distributor2 is where hoist6 is located, hoist0 can be found located at depot0, hoist0 is accessible, hoist1 is available for work, hoist2 is accessible, hoist2 is at depot2, hoist3 is available for work, hoist4 is available for work, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 is located at depot3, pallet4 is clear of any crates, pallet5 is at distributor1, pallet6 is located at distributor2, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 moves from depot1 to depot0, then hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2. From depot0, truck2 proceeds to distributor1. At depot3, hoist3 lifts crate1 off pallet3, while at distributor1, hoist5 lifts crate3 off pallet5 and loads it into truck2. Crate2 is then unloaded from truck2 by hoist5 at distributor1. Truck2 then travels from distributor1 to distributor2. At distributor1, hoist5 places crate2 on pallet5. At distributor2, hoist6 lifts crate0 off pallet6 and loads it into truck2. From distributor2, truck2 is driven to depot3, where crate1 is loaded into truck2 by hoist3, and crate0 is unloaded from truck2 by hoist3. Truck2 then moves from depot3 to distributor0, where crate3 is unloaded by hoist4. At depot3, hoist3 places crate0 on pallet3, and at distributor0, hoist4 places crate3 on pallet4, resulting in the current state. In this state, is it True or False that pallet3 is clear of any crates?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 has no crates on top, crate1 is placed on top of pallet3, crate2 is located at depot0, crate2 has no crates on top, crate2 is positioned on top of pallet0, crate3 is situated at distributor1, crate3 has no crates on top, crate3 is positioned on pallet5, depot1 houses hoist1, depot1 houses pallet1, depot1 houses truck0, depot3 houses crate1, depot3 houses hoist3, distributor0 houses hoist4, distributor0 houses pallet4, distributor2 houses hoist6, hoist0 is situated at depot0, hoist0 is operational, hoist1 is ready for work, hoist2 is operational, hoist2 is situated at depot2, hoist3 is ready for work, hoist4 is ready for work, hoist5 is operational, hoist5 is situated at distributor1, hoist6 is available, pallet0 is situated at depot0, pallet1 has no crates on it, pallet2 has no crates on it, pallet2 is situated at depot2, pallet3 is situated at depot3, pallet4 has no crates on it, pallet5 is situated at distributor1, pallet6 is situated at distributor2, truck1 is situated at distributor0 and truck2 is situated at depot1."}
{"question_id": "7a9bb8b1-83a6-4d33-88a8-7f75220f355f", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_5", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, at depot0, hoist0 lifts crate2 off pallet0, at depot0, hoist0 loads crate2 into truck1, truck1 is driven to depot1 from depot0, hoist1 unloads crate2 from truck1 at depot1, hoist1 drops crate2 on pallet1 at depot1, at depot2, hoist2 lifts crate3 off pallet2, at depot2, hoist2 loads crate3 into truck2, from depot2, truck2 is driven to distributor3, hoist6 unloads crate3 from truck2 at distributor3, hoist6 drops crate3 on pallet6 at distributor3, at distributor2, hoist5 lifts crate1 off crate0, at distributor2, hoist5 loads crate1 into truck0, at distributor2, hoist5 lifts crate0 off pallet5, hoist5 loads crate0 into truck0 at distributor2, hoist5 unloads crate1 from truck0 at distributor2, from distributor2, truck0 is driven to distributor0, crate0 is unloaded by hoist3 from truck0 at distributor0 and crate0 is dropped on pallet3 at distributor0 by hoist3 to reach the current state. In this state, is it True or False that crate0 is clear of any crates?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 has crate1 on it, crate0 is located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate2 is clear of any crates, crate2 is located at depot0, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where hoist5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is accessible, hoist6 is available for work, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 is clear, pallet4 is clear, pallet4 is located at distributor1, pallet5 is at distributor2, pallet6 is clear of any crates, pallet6 is located at distributor3 and truck2 is at depot2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck1 is driven from depot1 to depot0, where hoist0 removes crate2 from pallet0 and loads it into truck1, which is then driven back to depot1. At depot1, hoist1 unloads crate2 from truck1 and places it on pallet1. Meanwhile, at depot2, hoist2 lifts crate3 off pallet2, loads it into truck2, and truck2 is driven to distributor3. Upon arrival, hoist6 unloads crate3 from truck2 and places it on pallet6. At distributor2, hoist5 lifts crate1 off crate0, loads crate1 into truck0, lifts crate0 off pallet5, and loads crate0 into truck0. Hoist5 then unloads crate1 from truck0, and truck0 is driven to distributor0. At distributor0, hoist3 unloads crate0 from truck0 and places it on pallet3. In this resulting state, is it True or False that crate0 is clear of any crates?", "initial_state_nl_paraphrased": "Crate0 has crate1 stacked on top of it, and it is situated at distributor2. Additionally, crate0 is placed on pallet5. Crate1, on the other hand, has no crates on top of it and is also located at distributor2. Similarly, crate2 is clear of any crates and is situated at depot0. Crate3 can be found at depot2, has no crates on top of it, and is placed on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is located. Distributor0 houses both hoist3 and pallet3, and distributor2 is home to both hoist5 and truck0. Distributor3 is where hoist6 is located. \n\nHoist0 is available for work, and hoist1 is available and situated at depot1. Hoist2 is also available and located at depot2. Hoist3 is available for work, while hoist4 is available and located at distributor1. Hoist5 is accessible, and hoist6 is available for work.\n\nPallet0 has crate2 on it and is situated at depot0. Pallet1 is empty and located at depot1. Pallet2 is situated at depot2, and pallet3 is empty. Pallet4 is also empty and located at distributor1. Pallet5 is at distributor2, and pallet6 is empty and located at distributor3. Lastly, truck2 is situated at depot2."}
{"question_id": "f22e6383-3fc8-4484-8757-563e75879d7c", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_6", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot2 from depot0, crate3 is lifted from crate2 at depot2 by hoist2, at depot2, hoist2 loads crate3 into truck2, crate2 is lifted from crate1 at depot2 by hoist2, crate2 is loaded by hoist2 into truck2 at depot2, crate1 is lifted from pallet2 at depot2 by hoist2, at depot2, hoist2 loads crate1 into truck2, from depot2, truck2 is driven to distributor0, crate0 is lifted from pallet3 at distributor0 by hoist3, at distributor0, hoist3 loads crate0 into truck2, at distributor0, hoist3 unloads crate1 from truck2, truck2 is driven to distributor1 from distributor0, crate2 is unloaded by hoist4 from truck2 at distributor1, from distributor1, truck2 is driven to distributor2, crate3 is unloaded by hoist5 from truck2 at distributor2, crate1 is dropped on pallet3 at distributor0 by hoist3, hoist4 drops crate2 on pallet4 at distributor1, crate3 is dropped on pallet5 at distributor2 by hoist5 and hoist5 unloads crate0 from truck2 at distributor2 to reach the current state. In this state, is it True or False that crate0 is clear of any crates?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 is clear, crate0 is on pallet3, crate1 is on pallet2, crate2 can be found located at depot2, crate2 is on top of crate1, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on top of crate2, depot2 is where crate1 is located, depot2 is where pallet2 is located, distributor0 is where crate0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, pallet0 is at depot0, pallet0 is clear, pallet1 can be found located at depot1, pallet1 is clear of any crates, pallet4 is at distributor1, pallet4 is clear of any crates, pallet5 is at distributor2, pallet5 is clear of any crates, truck0 can be found located at distributor0, truck1 is located at depot1 and truck2 is at depot0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck2 travels from depot0 to depot2, hoist2 lifts crate3 from crate2 at depot2, then hoist2 loads crate3 into truck2 at depot2, hoist2 lifts crate2 from crate1 at depot2, hoist2 loads crate2 into truck2 at depot2, hoist2 lifts crate1 from pallet2 at depot2, and then hoist2 loads crate1 into truck2 at depot2, after which truck2 travels from depot2 to distributor0, hoist3 lifts crate0 from pallet3 at distributor0, hoist3 loads crate0 into truck2 at distributor0, hoist3 unloads crate1 from truck2 at distributor0, truck2 then travels from distributor0 to distributor1, hoist4 unloads crate2 from truck2 at distributor1, truck2 travels from distributor1 to distributor2, hoist5 unloads crate3 from truck2 at distributor2, hoist3 drops crate1 onto pallet3 at distributor0, hoist4 drops crate2 onto pallet4 at distributor1, hoist5 drops crate3 onto pallet5 at distributor2, and finally hoist5 unloads crate0 from truck2 at distributor2 to reach the current state. In this state, is it True or False that crate0 is clear of any crates?", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is positioned on pallet3, crate1 is placed on pallet2, crate2 is situated at depot2, crate2 is stacked on top of crate1, crate3 is also located at depot2, crate3 has no crates on it, crate3 is positioned on top of crate2, depot2 is the location of crate1, depot2 is also the location of pallet2, distributor0 is the location of crate0, distributor0 is also the location of hoist3, distributor0 is the location of pallet3, hoist0 is available for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for work, hoist2 is situated at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is located at distributor2, hoist5 is available, pallet0 is situated at depot0, pallet0 is empty, pallet1 is located at depot1, pallet1 has no crates on it, pallet4 is situated at distributor1, pallet4 has no crates on it, pallet5 is situated at distributor2, pallet5 has no crates on it, truck0 is located at distributor0, truck1 is situated at depot1 and truck2 is situated at depot0."}
{"question_id": "64945340-f3d5-4ee6-b29d-97413eba09c8", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1 to reach the current state. In this state, is it True or False that pallet0 is not clear?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 has crate1 on it, crate0 is located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate2 is clear of any crates, crate2 is located at depot0, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where hoist5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is accessible, hoist6 is available for work, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 is clear, pallet4 is clear, pallet4 is located at distributor1, pallet5 is at distributor2, pallet6 is clear of any crates, pallet6 is located at distributor3 and truck2 is at depot2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1 to reach the current state. In this state, is it True or False that pallet0 is obstructed?", "initial_state_nl_paraphrased": "Crate0 has crate1 stacked on top of it, and it is situated at distributor2. Additionally, crate0 is placed on pallet5. Crate1, on the other hand, has no crates on top of it and is also located at distributor2. Similarly, crate2 is clear of any crates and is situated at depot0. Crate3 can be found at depot2, has no crates on top of it, and is placed on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is located. Distributor0 houses both hoist3 and pallet3, and distributor2 is home to both hoist5 and truck0. Distributor3 is where hoist6 is located. \n\nHoist0 is available for work, and hoist1 is available and situated at depot1. Hoist2 is also available and located at depot2. Hoist3 is available for work, while hoist4 is available and located at distributor1. Hoist5 is accessible, and hoist6 is available for work.\n\nPallet0 has crate2 on it and is located at depot0. Pallet1 is empty and situated at depot1. Pallet2 is located at depot2, and pallet3 is empty. Pallet4 is also empty and located at distributor1. Pallet5 is situated at distributor2, and pallet6 is empty and located at distributor3. Lastly, truck2 is located at depot2."}
{"question_id": "8d4c46e5-c1a4-4a56-b4ab-b42fecd9acf6", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, hoist0 lifts crate2 from pallet0 at depot0, at depot0, hoist0 loads crate2 into truck1, truck1 is driven from depot0 to depot1, hoist1 unloads crate2 from truck1 at depot1, hoist1 drops crate2 on pallet1 at depot1, at depot2, hoist2 lifts crate3 off pallet2, crate3 is loaded by hoist2 into truck2 at depot2, truck2 is driven to distributor3 from depot2, crate3 is unloaded by hoist6 from truck2 at distributor3, hoist6 drops crate3 on pallet6 at distributor3, crate1 is lifted from crate0 at distributor2 by hoist5, hoist5 loads crate1 into truck0 at distributor2, hoist5 lifts crate0 from pallet5 at distributor2, hoist5 loads crate0 into truck0 at distributor2, at distributor2, hoist5 unloads crate1 from truck0, truck0 is driven to distributor0 from distributor2, at distributor0, hoist3 unloads crate0 from truck0 and at distributor0, hoist3 drops crate0 on pallet3 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: crate0 is clear, crate2 is clear of any crates, crate3 is clear of any crates, hoist0 is accessible, hoist1 is accessible, hoist2 is accessible, hoist3 is available for work, hoist4 is available for work, hoist6 is available for work, pallet0 is clear of any crates, pallet2 is clear, pallet4 is clear and pallet5 is clear of any crates?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 has crate1 on it, crate0 is located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate2 is clear of any crates, crate2 is located at depot0, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where hoist5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is accessible, hoist6 is available for work, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 is clear, pallet4 is clear, pallet4 is located at distributor1, pallet5 is at distributor2, pallet6 is clear of any crates, pallet6 is located at distributor3 and truck2 is at depot2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck1 is driven from depot1 to depot0, then hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck1, after which truck1 is driven back to depot1, where hoist1 unloads crate2 and places it on pallet1. At depot2, hoist2 lifts crate3 from pallet2 and loads it into truck2, which is then driven to distributor3, where hoist6 unloads crate3 and places it on pallet6. At distributor2, hoist5 lifts crate1 from crate0 and loads it into truck0, then lifts crate0 from pallet5 and loads it into truck0 as well. After that, hoist5 unloads crate1 from truck0, and truck0 is driven to distributor0, where hoist3 unloads crate0 and places it on pallet3, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: crate0 is clear, crate2 is clear of any crates, crate3 is clear of any crates, hoist0 is accessible, hoist1 is accessible, hoist2 is accessible, hoist3 is available for work, hoist4 is available for work, hoist6 is available for work, pallet0 is clear of any crates, pallet2 is clear, pallet4 is clear, and pallet5 is clear of any crates?", "initial_state_nl_paraphrased": "Crate0 has crate1 stacked on top of it, and it is situated at distributor2. Additionally, crate0 is placed on pallet5. Crate1, on the other hand, has no crates on top of it and is also located at distributor2. Similarly, crate2 is clear of any crates and is situated at depot0. Crate3 can be found at depot2, has no crates on top of it, and is placed on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is located. Distributor0 houses both hoist3 and pallet3, and distributor2 is home to both hoist5 and truck0. Distributor3 is where hoist6 is located. Hoist0 is available for work, and hoist1, which is located at depot1, is also available. Hoist2, situated at depot2, is available as well. Hoist3 is available for work, and hoist4, located at distributor1, is available too. Hoist5 is accessible, and hoist6 is available for work.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is empty and situated at depot1. Pallet2 is at depot2, and pallet3 is empty. Pallet4 is also empty and located at distributor1. Pallet5 is at distributor2, and pallet6, which is empty, is located at distributor3. Lastly, truck2 is situated at depot2."}
{"question_id": "ac3c5ff5-0ded-48e6-8668-473ba28b6b33", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_13", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot0 to depot2, hoist2 lifts crate3 from crate2 at depot2, hoist2 loads crate3 into truck2 at depot2, crate2 is lifted from crate1 at depot2 by hoist2, at depot2, hoist2 loads crate2 into truck2, crate1 is lifted from pallet2 at depot2 by hoist2, crate1 is loaded by hoist2 into truck2 at depot2, from depot2, truck2 is driven to distributor0, hoist3 lifts crate0 from pallet3 at distributor0, crate0 is loaded by hoist3 into truck2 at distributor0, hoist3 unloads crate1 from truck2 at distributor0, truck2 is driven to distributor1 from distributor0, hoist4 unloads crate2 from truck2 at distributor1, truck2 is driven to distributor2 from distributor1, at distributor2, hoist5 unloads crate3 from truck2, at distributor0, hoist3 drops crate1 on pallet3, crate2 is dropped on pallet4 at distributor1 by hoist4, hoist5 drops crate3 on pallet5 at distributor2 and hoist5 unloads crate0 from truck2 at distributor2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: crate1 can be found located at distributor0, crate1 is on pallet3, crate3 is on top of pallet5, depot0 is where hoist0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor0 is where truck0 is located, distributor1 is where crate2 is located, distributor2 is where crate3 is located, hoist1 is located at depot1, hoist2 is located at depot2, hoist4 is located at distributor1, hoist5 is at distributor2, hoist5 is lifting crate0, pallet0 is located at depot0, pallet1 is at depot1, pallet2 can be found located at depot2, pallet4 has crate2 on it, pallet4 is at distributor1, pallet5 is located at distributor2, truck1 is located at depot1 and truck2 is located at distributor2?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 is clear, crate0 is on pallet3, crate1 is on pallet2, crate2 can be found located at depot2, crate2 is on top of crate1, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on top of crate2, depot2 is where crate1 is located, depot2 is where pallet2 is located, distributor0 is where crate0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, pallet0 is at depot0, pallet0 is clear, pallet1 can be found located at depot1, pallet1 is clear of any crates, pallet4 is at distributor1, pallet4 is clear of any crates, pallet5 is at distributor2, pallet5 is clear of any crates, truck0 can be found located at distributor0, truck1 is located at depot1 and truck2 is at depot0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 travels from depot0 to depot2, hoist2 at depot2 lifts crate3 from crate2, then loads crate3 into truck2, hoist2 lifts crate2 from crate1 at depot2 and loads it into truck2, hoist2 lifts crate1 from pallet2 at depot2 and loads it into truck2, truck2 then drives from depot2 to distributor0, hoist3 at distributor0 lifts crate0 from pallet3 and loads it into truck2, hoist3 unloads crate1 from truck2 at distributor0, truck2 drives to distributor1, hoist4 unloads crate2 from truck2 at distributor1, truck2 then drives to distributor2, hoist5 unloads crate3 from truck2 at distributor2, hoist3 drops crate1 onto pallet3 at distributor0, hoist4 drops crate2 onto pallet4 at distributor1, and hoist5 drops crate3 onto pallet5 at distributor2 and unloads crate0 from truck2, resulting in the current state. In this state, are the following properties that do not involve negations True or False: crate1 is located at distributor0, crate1 is on pallet3, crate3 is on top of pallet5, hoist0 is located at depot0, hoist3 is located at distributor0, pallet3 is located at distributor0, truck0 is located at distributor0, crate2 is located at distributor1, crate3 is located at distributor2, hoist1 is located at depot1, hoist2 is located at depot2, hoist4 is located at distributor1, hoist5 is located at distributor2, hoist5 is lifting crate0, pallet0 is located at depot0, pallet1 is located at depot1, pallet2 is located at depot2, crate2 is on pallet4, pallet4 is located at distributor1, pallet5 is located at distributor2, truck1 is located at depot1, and truck2 is located at distributor2?", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is positioned on pallet3, crate1 is placed on pallet2, crate2 is situated at depot2, crate2 is stacked on top of crate1, crate3 is also located at depot2, crate3 has no crates on it, crate3 is positioned on top of crate2, depot2 is the location of crate1, depot2 is also the location of pallet2, distributor0 is the location of crate0, distributor0 is also the location of hoist3, distributor0 is the location of pallet3, hoist0 is available for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for work, hoist2 is situated at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is located at distributor2, hoist5 is available, pallet0 is situated at depot0, pallet0 is empty, pallet1 is located at depot1, pallet1 has no crates on it, pallet4 is situated at distributor1, pallet4 has no crates on it, pallet5 is situated at distributor2, pallet5 has no crates on it, truck0 is located at distributor0, truck1 is situated at depot1 and truck2 is situated at depot0."}
{"question_id": "45386cc8-ef2d-4e3d-8aed-fc4d0ddc6338", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_9", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0, at depot0, hoist0 loads crate2 into truck2, from depot0, truck2 is driven to distributor1, hoist2 lifts crate1 from pallet2 at depot2, at depot2, hoist2 loads crate1 into truck0, truck0 is driven from depot2 to distributor0, at distributor0, hoist3 unloads crate1 from truck0, at distributor0, hoist3 drops crate1 on pallet3, hoist4 lifts crate0 from pallet4 at distributor1, hoist4 loads crate0 into truck2 at distributor1, at distributor2, hoist5 lifts crate3 off pallet5, hoist5 loads crate3 into truck1 at distributor2, truck1 is driven from distributor2 to distributor1, crate3 is unloaded by hoist4 from truck1 at distributor1, at distributor1, hoist4 drops crate3 on pallet4, crate2 is unloaded by hoist4 from truck2 at distributor1, truck2 is driven from distributor1 to depot1, crate0 is unloaded by hoist1 from truck2 at depot1 and at depot1, hoist1 drops crate0 on pallet1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: crate2 is not clear of any crates, hoist4 is not available for work, pallet1 is not clear of any crates, pallet3 is not clear of any crates and pallet4 is not clear of any crates?", "answer": "True", "plan_length": 19, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor1, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot0 is where pallet0 is located, depot0 is where truck2 is located, depot2 is where hoist2 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where crate3 is located, distributor2 is where hoist5 is located, distributor2 is where pallet5 is located, hoist0 is accessible, hoist1 is available for work, hoist1 is located at depot1, hoist2 is available, hoist3 is available for work, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is available for work, pallet0 has crate2 on it, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is at depot2, pallet3 is clear of any crates, pallet4 is located at distributor1, truck0 is located at depot2 and truck1 is at distributor2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: hoist0 picks up crate2 from pallet0 at depot0 and loads it into truck2 at depot0, then truck2 is driven to distributor1. Meanwhile, hoist2 lifts crate1 from pallet2 at depot2 and loads it into truck0, which is then driven from depot2 to distributor0. At distributor0, hoist3 unloads crate1 from truck0 and places it on pallet3. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2, while at distributor2, hoist5 lifts crate3 from pallet5 and loads it into truck1. Truck1 is then driven from distributor2 to distributor1, where hoist4 unloads crate3 from truck1 and places it on pallet4. Additionally, hoist4 unloads crate2 from truck2 at distributor1, and truck2 is driven from distributor1 to depot1. At depot1, crate0 is unloaded from truck2 by hoist1 and placed on pallet1, resulting in the current state. In this state, are the following properties that involve negations True or False: crate2 is not clear of any crates, hoist4 is not available for work, pallet1 is not clear of any crates, pallet3 is not clear of any crates, and pallet4 is not clear of any crates?", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor1, crate0 is positioned on top of pallet4, crate1 is situated at depot2, crate1 has no crates on it, crate1 is placed on pallet2, crate2 has no crates on it, crate3 has no crates on it, crate3 is positioned on top of pallet5, depot0 is the location of crate2, depot0 is the location of hoist0, depot0 is the location of pallet0, depot0 is the location of truck2, depot2 is the location of hoist2, distributor0 is the location of hoist3, distributor0 is the location of pallet3, distributor2 is the location of crate3, distributor2 is the location of hoist5, distributor2 is the location of pallet5, hoist0 is accessible for use, hoist1 is available for work and is located at depot1, hoist2 is available for use, hoist3 is available for work, hoist4 is accessible for use and is located at distributor1, hoist5 is available for work, pallet0 has crate2 placed on it, pallet1 has no crates on it and is located at depot1, pallet2 is situated at depot2, pallet3 has no crates on it, pallet4 is located at distributor1, truck0 is situated at depot2 and truck1 is situated at distributor2."}
{"question_id": "010e0957-b51a-4012-b46a-d7bfda944bbb", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_11", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven from depot1 to depot0 to reach the current state. In this state, is it True or False that distributor0 is where pallet5 is not located?", "answer": "True", "plan_length": 1, "initial_state_nl": "Crate0 has crate1 on it, crate0 is located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate2 is clear of any crates, crate2 is located at depot0, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where hoist5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is accessible, hoist6 is available for work, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 is clear, pallet4 is clear, pallet4 is located at distributor1, pallet5 is at distributor2, pallet6 is clear of any crates, pallet6 is located at distributor3 and truck2 is at depot2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck1 is driven from depot1 to depot0 to reach the current state. In this state, is it True or False that pallet5 is not located at distributor0?", "initial_state_nl_paraphrased": "Crate0 is stacked on top of crate1, and crate0 is situated at distributor2. Additionally, crate0 is placed on pallet5. Crate1, on the other hand, has no crates on it, and it is also located at distributor2. Similarly, crate2 has no crates on it and is situated at depot0. Crate3 is located at depot2, has no crates on it, and is placed on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is situated. Distributor0 houses both hoist3 and pallet3, whereas distributor2 is home to both hoist5 and truck0. Distributor3 is where hoist6 is located. \n\nHoist0 is available for work, and hoist1 is available and situated at depot1. Hoist2 is also available and located at depot2. Hoist3 is available for work, while hoist4 is available and situated at distributor1. Hoist5 is accessible, and hoist6 is available for work.\n\nPallet0 has crate2 on it and is situated at depot0. Pallet1 is empty and located at depot1. Pallet2 is situated at depot2, while pallet3 is empty. Pallet4 is also empty and located at distributor1. Pallet5 is at distributor2, and pallet6 is empty and situated at distributor3. Lastly, truck2 is located at depot2."}
{"question_id": "5031cf49-1645-42b4-b510-64b596aabc7a", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_16", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: crate0 cannot be found located at depot0, crate0 cannot be found located at distributor0, crate0 cannot be found located at distributor1, crate0 does not have crate0 on it, crate0 is not at distributor2, crate0 is not inside truck0, crate0 is not located at depot2, crate0 is not located at distributor3, crate0 is not on pallet2, crate0 is not on pallet3, crate0 is not on pallet5, crate0 is not on pallet6, crate0 is not on top of crate1, crate0 is not on top of crate2, crate0 is not on top of crate3, crate0 is not on top of pallet1, crate0 is not on top of pallet4, crate1 cannot be found located at distributor1, crate1 does not have crate1 on it, crate1 does not have crate2 on it, crate1 is not at depot2, crate1 is not at distributor0, crate1 is not at distributor2, crate1 is not in truck0, crate1 is not in truck1, crate1 is not located at depot0, crate1 is not located at depot1, crate1 is not located at distributor3, crate1 is not on crate0, crate1 is not on crate3, crate1 is not on pallet0, crate1 is not on pallet1, crate1 is not on pallet6, crate1 is not on top of crate2, crate1 is not on top of pallet5, crate2 cannot be found located at depot0, crate2 cannot be found located at distributor0, crate2 is not at depot2, crate2 is not in truck1, crate2 is not inside truck2, crate2 is not located at depot1, crate2 is not located at distributor2, crate2 is not on crate3, crate2 is not on pallet3, crate2 is not on top of crate0, crate2 is not on top of crate2, crate2 is not on top of pallet0, crate2 is not on top of pallet1, crate2 is not on top of pallet4, crate2 is not on top of pallet6, crate3 cannot be found located at depot1, crate3 cannot be found located at distributor1, crate3 does not have crate3 on it, crate3 is not at depot0, crate3 is not at distributor0, crate3 is not at distributor2, crate3 is not in truck0, crate3 is not in truck1, crate3 is not inside truck2, crate3 is not located at depot2, crate3 is not located at distributor3, crate3 is not on crate0, crate3 is not on crate1, crate3 is not on crate2, crate3 is not on pallet0, crate3 is not on pallet6, crate3 is not on top of pallet2, crate3 is not on top of pallet3, crate3 is not on top of pallet4, crate3 is not on top of pallet5, depot0 is where hoist0 is not located, depot0 is where hoist3 is not located, depot0 is where hoist4 is not located, depot0 is where pallet2 is not located, depot1 is where crate0 is not located, depot1 is where hoist0 is not located, depot1 is where hoist1 is not located, depot1 is where pallet0 is not located, depot1 is where truck1 is not located, depot2 is where hoist3 is not located, depot2 is where hoist4 is not located, depot2 is where pallet5 is not located, depot2 is where pallet6 is not located, depot2 is where truck1 is not located, distributor0 is where hoist0 is not located, distributor0 is where pallet2 is not located, distributor0 is where pallet3 is not located, distributor0 is where pallet6 is not located, distributor0 is where truck0 is not located, distributor1 is where crate2 is not located, distributor1 is where hoist3 is not located, distributor1 is where hoist4 is not located, distributor1 is where hoist5 is not located, distributor1 is where pallet1 is not located, distributor1 is where pallet2 is not located, distributor1 is where pallet4 is not located, distributor1 is where truck0 is not located, distributor1 is where truck2 is not located, distributor2 is where hoist3 is not located, distributor2 is where hoist4 is not located, distributor2 is where pallet0 is not located, distributor2 is where pallet3 is not located, distributor2 is where truck2 is not located, distributor3 is where crate2 is not located, distributor3 is where hoist3 is not located, distributor3 is where pallet3 is not located, distributor3 is where pallet5 is not located, hoist0 cannot be found located at distributor1, hoist0 is not at distributor2, hoist0 is not at distributor3, hoist0 is not lifting crate1, hoist0 is not located at depot2, hoist0 is not raising crate0, hoist0 is not raising crate2, hoist0 is not raising crate3, hoist1 cannot be found located at distributor0, hoist1 is not at depot2, hoist1 is not at distributor1, hoist1 is not at distributor2, hoist1 is not at distributor3, hoist1 is not elevating crate3, hoist1 is not lifting crate0, hoist1 is not lifting crate1, hoist1 is not lifting crate2, hoist1 is not located at depot0, hoist2 cannot be found located at depot1, hoist2 cannot be found located at distributor1, hoist2 is not at depot0, hoist2 is not at distributor0, hoist2 is not at distributor3, hoist2 is not elevating crate2, hoist2 is not lifting crate0, hoist2 is not lifting crate1, hoist2 is not lifting crate3, hoist2 is not located at depot2, hoist2 is not located at distributor2, hoist3 is not at depot1, hoist3 is not at distributor0, hoist3 is not elevating crate0, hoist3 is not elevating crate2, hoist3 is not lifting crate1, hoist3 is not lifting crate3, hoist4 cannot be found located at depot1, hoist4 cannot be found located at distributor0, hoist4 is not elevating crate0, hoist4 is not lifting crate1, hoist4 is not lifting crate2, hoist4 is not lifting crate3, hoist4 is not located at distributor3, hoist5 is not at distributor0, hoist5 is not at distributor3, hoist5 is not lifting crate2, hoist5 is not lifting crate3, hoist5 is not located at depot0, hoist5 is not located at depot1, hoist5 is not located at depot2, hoist5 is not located at distributor2, hoist5 is not raising crate0, hoist5 is not raising crate1, hoist6 cannot be found located at depot1, hoist6 cannot be found located at distributor0, hoist6 cannot be found located at distributor1, hoist6 cannot be found located at distributor3, hoist6 is not elevating crate1, hoist6 is not elevating crate2, hoist6 is not lifting crate0, hoist6 is not lifting crate3, hoist6 is not located at depot0, hoist6 is not located at depot2, hoist6 is not located at distributor2, pallet0 cannot be found located at distributor3, pallet0 does not have crate0 on it, pallet0 is not at depot0, pallet0 is not at depot2, pallet0 is not located at distributor0, pallet0 is not located at distributor1, pallet1 does not have crate3 on it, pallet1 is not at depot0, pallet1 is not at distributor0, pallet1 is not at distributor2, pallet1 is not at distributor3, pallet1 is not located at depot1, pallet1 is not located at depot2, pallet2 cannot be found located at depot1, pallet2 cannot be found located at depot2, pallet2 does not have crate1 on it, pallet2 does not have crate2 on it, pallet2 is not at distributor2, pallet2 is not at distributor3, pallet3 cannot be found located at depot0, pallet3 does not have crate1 on it, pallet3 is not at depot1, pallet3 is not at depot2, pallet3 is not located at distributor1, pallet4 does not have crate1 on it, pallet4 is not at depot0, pallet4 is not at distributor0, pallet4 is not at distributor2, pallet4 is not at distributor3, pallet4 is not located at depot1, pallet4 is not located at depot2, pallet5 cannot be found located at distributor0, pallet5 cannot be found located at distributor1, pallet5 cannot be found located at distributor2, pallet5 does not have crate2 on it, pallet5 is not located at depot0, pallet5 is not located at depot1, pallet6 cannot be found located at depot1, pallet6 cannot be found located at distributor3, pallet6 is not at depot0, pallet6 is not at distributor1, pallet6 is not located at distributor2, truck0 cannot be found located at depot0, truck0 cannot be found located at depot1, truck0 does not contain crate2, truck0 is not at distributor2, truck0 is not located at depot2, truck0 is not located at distributor3, truck1 cannot be found located at depot0, truck1 cannot be found located at distributor0, truck1 does not contain crate0, truck1 is not at distributor1, truck1 is not at distributor2, truck1 is not at distributor3, truck2 cannot be found located at distributor0, truck2 does not contain crate0, truck2 does not contain crate1, truck2 is not at depot1, truck2 is not at depot2, truck2 is not at distributor3 and truck2 is not located at depot0?", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 has crate1 on it, crate0 is located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate2 is clear of any crates, crate2 is located at depot0, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where hoist5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is accessible, hoist6 is available for work, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 is clear, pallet4 is clear, pallet4 is located at distributor1, pallet5 is at distributor2, pallet6 is clear of any crates, pallet6 is located at distributor3 and truck2 is at depot2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: \n\ncrate0 is not situated at depot0, crate0 is not situated at distributor0, crate0 is not situated at distributor1, crate0 does not hold crate0, crate0 is not present at distributor2, crate0 is not inside truck0, crate0 is not located at depot2, crate0 is not located at distributor3, crate0 is not positioned on pallet2, crate0 is not positioned on pallet3, crate0 is not positioned on pallet5, crate0 is not positioned on pallet6, crate0 is not stacked on crate1, crate0 is not stacked on crate2, crate0 is not stacked on crate3, crate0 is not stacked on pallet1, crate0 is not stacked on pallet4, crate1 is not situated at distributor1, crate1 does not hold crate1, crate1 does not hold crate2, crate1 is not present at depot2, crate1 is not present at distributor0, crate1 is not present at distributor2, crate1 is not inside truck0, crate1 is not inside truck1, crate1 is not located at depot0, crate1 is not located at depot1, crate1 is not located at distributor3, crate1 is not positioned on crate0, crate1 is not positioned on crate3, crate1 is not positioned on pallet0, crate1 is not positioned on pallet1, crate1 is not positioned on pallet6, crate1 is not stacked on crate2, crate1 is not stacked on pallet5, crate2 is not situated at depot0, crate2 is not situated at distributor0, crate2 is not present at depot2, crate2 is not inside truck1, crate2 is not inside truck2, crate2 is not located at depot1, crate2 is not located at distributor2, crate2 is not positioned on crate3, crate2 is not positioned on pallet3, crate2 is not stacked on crate0, crate2 is not stacked on crate2, crate2 is not stacked on pallet0, crate2 is not stacked on pallet1, crate2 is not stacked on pallet4, crate2 is not stacked on pallet6, crate3 is not situated at depot1, crate3 is not situated at distributor1, crate3 does not hold crate3, crate3 is not present at depot0, crate3 is not present at distributor0, crate3 is not present at distributor2, crate3 is not inside truck0, crate3 is not inside truck1, crate3 is not inside truck2, crate3 is not located at depot2, crate3 is not located at distributor3, crate3 is not positioned on crate0, crate3 is not positioned on crate1, crate3 is not positioned on crate2, crate3 is not positioned on pallet0, crate3 is not positioned on pallet6, crate3 is not stacked on pallet2, crate3 is not stacked on pallet3, crate3 is not stacked on pallet4, crate3 is not stacked on pallet5, depot0 is not the location of hoist0, depot0 is not the location of hoist3, depot0 is not the location of hoist4, depot0 is not the location of pallet2, depot1 is not the location of crate0, depot1 is not the location of hoist0, depot1 is not the location of hoist1, depot1 is not the location of pallet0, depot1 is not the location of truck1, depot2 is not the location of hoist3, depot2 is not the location of hoist4, depot2 is not the location of pallet5, depot2 is not the location of pallet6, depot2 is not the location of truck1, distributor0 is not the location of hoist0, distributor0 is not the location of pallet2, distributor0 is not the location of pallet3, distributor0 is not the location of pallet6, distributor0 is not the location of truck0, distributor1 is not the location of crate2, distributor1 is not the location of hoist3, distributor1 is not the location of hoist4, distributor1 is not the location of hoist5, distributor1 is not the location of pallet1, distributor1 is not the location of pallet2, distributor1 is not the location of pallet4, distributor1 is not the location of truck0, distributor1 is not the location of truck2, distributor2 is not the location of hoist3, distributor2 is not the location of hoist4, distributor2 is not the location of pallet0, distributor2 is not the location of pallet3, distributor2 is not the location of truck2, distributor3 is not the location of crate2, distributor3 is not the location of hoist3, distributor3 is not the location of pallet3, distributor3 is not the location of pallet5, hoist0 is not situated at distributor1, hoist0 is not present at distributor2, hoist0 is not present at distributor3, hoist0 is not lifting crate1, hoist0 is not located at depot2, hoist0 is not raising crate0, hoist0 is not raising crate2, hoist0 is not raising crate3, hoist1 is not situated at distributor0, hoist1 is not present at depot2, hoist1 is not present at distributor1, hoist1 is not present at distributor2, hoist1 is not present at distributor3, hoist1 is not elevating crate3, hoist1 is not lifting crate0, hoist1 is not lifting crate1, hoist1 is not lifting crate2, hoist1 is not located at depot0, hoist2 is not situated at depot1, hoist2 is not situated at distributor1, hoist2 is not present at depot0, hoist2 is not present at distributor0, hoist2 is not present at distributor3, hoist2 is not elevating crate2, hoist2 is not lifting crate0, hoist2 is not lifting crate1, hoist2 is not lifting crate3, hoist2 is not located at depot2, hoist2 is not located at distributor2, hoist3 is not present at depot1, hoist3 is not present at distributor0, hoist3 is not elevating crate0, hoist3 is not elevating crate2, hoist3 is not lifting crate1, hoist3 is not lifting crate3, hoist4 is not situated at depot1, hoist4 is not situated at distributor0, hoist4 is not elevating crate0, hoist4 is not lifting crate1, hoist4 is not lifting crate2, hoist4 is not lifting crate3, hoist4 is not located at distributor3, hoist5 is not present at distributor0, hoist5 is not present at distributor3, hoist5 is not lifting crate2, hoist5 is not lifting crate3, hoist5 is not located at depot0, hoist5 is not located at depot1, hoist5 is not located at depot2, hoist5 is not located at distributor2, hoist5 is not raising crate0, hoist5 is not raising crate1, hoist6 is not situated at depot1, hoist6 is not situated at distributor0, hoist6 is not situated at distributor1, hoist6 is not situated at distributor3, hoist6 is not elevating crate1, hoist6 is not elevating crate2, hoist6 is not lifting crate0, hoist6 is not lifting crate3, hoist6 is not located at depot0, hoist6 is not located at depot2, hoist6 is not located at distributor2, pallet0 is not situated at distributor3, pallet0 does not hold crate0, pallet0 is not present at depot0, pallet0 is not present at depot2, pallet0 is not located at distributor0, pallet0 is not located at distributor1, pallet1 does not hold crate3, pallet1 is not present at depot0, pallet1 is not present at distributor0, pallet1 is not present at distributor2, pallet1 is not present at distributor3, pallet1 is not located at depot1, pallet1 is not located at depot2, pallet2 is not situated at depot1, pallet2 is not situated at depot2, pallet2 does not hold crate1, pallet2 does not hold crate2, pallet2 is not present at distributor2, pallet2 is not present at distributor3, pallet3 is not situated at depot0, pallet3 does not hold crate1, pallet3 is not present at depot1, pallet3 is not present at depot2, pallet3 is not located at distributor1, pallet4 does not hold crate1, pallet4 is not present at depot0, pallet4 is not present at distributor0, pallet4 is not present at distributor2, pallet4 is not present at distributor3, pallet4 is not located at depot1, pallet4 is not located at depot2, pallet5 is not situated at distributor0, pallet5 is not situated at distributor1, pallet5 is not situated at distributor2, pallet5 does not hold crate2, pallet5 is not located at depot0, pallet5 is not located at depot1, pallet6 is not situated at depot1, pallet6 is not situated at distributor3, pallet6 is not present at depot0, pallet6 is not present at distributor1, pallet6 is not located at distributor2, truck0 is not situated at depot0, truck0 is not situated at depot1, truck0 does not contain crate2, truck0 is not present at distributor2, truck0 is not located at depot2, truck0 is not located at distributor3, truck1 is not situated at depot0, truck1 is not situated at distributor0, truck1 does not contain crate0, truck1 is not present at distributor1, truck1 is not present at distributor2, truck1 is not present at distributor3, truck2 is not situated at distributor0, truck2 does not contain crate0, truck2 does not contain crate1, truck2 is not present at depot1, truck2 is not present at depot2, truck2 is not present at distributor3 and truck2 is not located at depot0.", "initial_state_nl_paraphrased": "Crate0 has crate1 stacked on top of it, and it is situated at distributor2. Additionally, crate0 is placed on pallet5. Crate1, on the other hand, has no crates on top of it and is also located at distributor2. Similarly, crate2 is clear of any crates and is situated at depot0. Crate3 can be found at depot2, has no crates on top of it, and is placed on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is situated. Distributor0 houses both hoist3 and pallet3, and distributor2 is home to both hoist5 and truck0. Distributor3 is where hoist6 is located. \n\nHoist0 is available for work, and hoist1, which is located at depot1, is also available. Hoist2, situated at depot2, is available as well. Hoist3 is available for work, and hoist4, located at distributor1, is available too. Hoist5 is accessible, and hoist6 is available for work.\n\nPallet0, which has crate2 on it, is situated at depot0. Pallet1 is empty and located at depot1. Pallet2 is at depot2, and pallet3 is empty. Pallet4 is also empty and situated at distributor1. Pallet5 is at distributor2, and pallet6, which is empty, is located at distributor3. Lastly, truck2 is situated at depot2."}
{"question_id": "a02dea08-4320-493f-9c78-ef4ee17918a6", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_16", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1, at depot0, hoist0 lifts crate2 off pallet0, at depot0, hoist0 loads crate2 into truck1, from depot0, truck1 is driven to depot1, at depot1, hoist1 unloads crate2 from truck1, crate2 is dropped on pallet1 at depot1 by hoist1, crate3 is lifted from pallet2 at depot2 by hoist2, crate3 is loaded by hoist2 into truck2 at depot2, from depot2, truck2 is driven to distributor3, hoist6 unloads crate3 from truck2 at distributor3, hoist6 drops crate3 on pallet6 at distributor3, at distributor2, hoist5 lifts crate1 off crate0, hoist5 loads crate1 into truck0 at distributor2, hoist5 lifts crate0 from pallet5 at distributor2, crate0 is loaded by hoist5 into truck0 at distributor2, hoist5 unloads crate1 from truck0 at distributor2, truck0 is driven from distributor2 to distributor0, hoist3 unloads crate0 from truck0 at distributor0 and crate0 is dropped on pallet3 at distributor0 by hoist3 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: crate0 cannot be found located at distributor3, crate0 does not have crate0 on it, crate0 does not have crate2 on it, crate0 does not have crate3 on it, crate0 is not at depot2, crate0 is not at distributor1, crate0 is not in truck1, crate0 is not in truck2, crate0 is not inside truck0, crate0 is not located at depot0, crate0 is not located at distributor0, crate0 is not on pallet1, crate0 is not on pallet4, crate0 is not on pallet6, crate0 is not on top of pallet0, crate0 is not on top of pallet2, crate0 is not on top of pallet5, crate1 cannot be found located at depot0, crate1 cannot be found located at depot1, crate1 cannot be found located at distributor0, crate1 does not have crate0 on it, crate1 does not have crate1 on it, crate1 is not at distributor1, crate1 is not inside truck1, crate1 is not on crate0, crate1 is not on pallet2, crate1 is not on pallet4, crate1 is not on top of crate2, crate1 is not on top of pallet0, crate1 is not on top of pallet3, crate1 is not on top of pallet5, crate1 is not on top of pallet6, crate2 cannot be found located at depot0, crate2 cannot be found located at distributor1, crate2 cannot be found located at distributor2, crate2 does not have crate0 on it, crate2 does not have crate3 on it, crate2 is not at depot2, crate2 is not at distributor3, crate2 is not inside truck1, crate2 is not inside truck2, crate2 is not located at distributor0, crate2 is not on crate2, crate2 is not on pallet0, crate2 is not on pallet1, crate2 is not on pallet2, crate2 is not on pallet3, crate2 is not on pallet4, crate2 is not on top of crate1, crate2 is not on top of pallet5, crate3 cannot be found located at distributor0, crate3 cannot be found located at distributor1, crate3 does not have crate0 on it, crate3 does not have crate1 on it, crate3 does not have crate2 on it, crate3 does not have crate3 on it, crate3 is not at depot0, crate3 is not at distributor3, crate3 is not inside truck1, crate3 is not located at depot1, crate3 is not on crate1, crate3 is not on top of pallet1, crate3 is not on top of pallet3, crate3 is not on top of pallet6, depot0 is where hoist1 is not located, depot0 is where hoist3 is not located, depot0 is where hoist4 is not located, depot0 is where pallet0 is not located, depot0 is where pallet6 is not located, depot0 is where truck0 is not located, depot1 is where crate0 is not located, depot1 is where crate2 is not located, depot1 is where hoist0 is not located, depot1 is where pallet4 is not located, depot1 is where truck1 is not located, depot2 is where crate1 is not located, depot2 is where crate3 is not located, depot2 is where hoist3 is not located, depot2 is where hoist4 is not located, depot2 is where hoist6 is not located, depot2 is where pallet1 is not located, depot2 is where pallet4 is not located, distributor0 is where hoist0 is not located, distributor0 is where hoist1 is not located, distributor0 is where truck0 is not located, distributor0 is where truck1 is not located, distributor1 is where hoist1 is not located, distributor1 is where hoist3 is not located, distributor1 is where pallet2 is not located, distributor1 is where truck1 is not located, distributor2 is where crate0 is not located, distributor2 is where crate1 is not located, distributor2 is where crate3 is not located, distributor2 is where hoist0 is not located, distributor2 is where hoist2 is not located, distributor2 is where hoist5 is not located, distributor2 is where pallet1 is not located, distributor2 is where truck0 is not located, distributor2 is where truck2 is not located, distributor3 is where crate1 is not located, distributor3 is where hoist3 is not located, distributor3 is where pallet0 is not located, distributor3 is where pallet1 is not located, distributor3 is where truck1 is not located, distributor3 is where truck2 is not located, hoist0 cannot be found located at depot2, hoist0 is not at depot0, hoist0 is not elevating crate0, hoist0 is not lifting crate3, hoist0 is not located at distributor1, hoist0 is not located at distributor3, hoist0 is not raising crate1, hoist0 is not raising crate2, hoist1 cannot be found located at depot2, hoist1 cannot be found located at distributor3, hoist1 is not elevating crate1, hoist1 is not elevating crate2, hoist1 is not elevating crate3, hoist1 is not located at depot1, hoist1 is not located at distributor2, hoist1 is not raising crate0, hoist2 cannot be found located at depot0, hoist2 cannot be found located at depot2, hoist2 cannot be found located at distributor0, hoist2 cannot be found located at distributor3, hoist2 is not at distributor1, hoist2 is not elevating crate3, hoist2 is not lifting crate1, hoist2 is not located at depot1, hoist2 is not raising crate0, hoist2 is not raising crate2, hoist3 cannot be found located at depot1, hoist3 cannot be found located at distributor2, hoist3 is not at distributor0, hoist3 is not elevating crate0, hoist3 is not elevating crate1, hoist3 is not elevating crate3, hoist3 is not raising crate2, hoist4 cannot be found located at distributor0, hoist4 cannot be found located at distributor1, hoist4 cannot be found located at distributor3, hoist4 is not at depot1, hoist4 is not elevating crate0, hoist4 is not elevating crate3, hoist4 is not located at distributor2, hoist4 is not raising crate1, hoist4 is not raising crate2, hoist5 cannot be found located at depot1, hoist5 cannot be found located at depot2, hoist5 cannot be found located at distributor0, hoist5 cannot be found located at distributor3, hoist5 is not at distributor1, hoist5 is not elevating crate3, hoist5 is not located at depot0, hoist5 is not raising crate0, hoist5 is not raising crate1, hoist5 is not raising crate2, hoist6 cannot be found located at distributor0, hoist6 cannot be found located at distributor1, hoist6 is not at depot1, hoist6 is not elevating crate1, hoist6 is not elevating crate3, hoist6 is not lifting crate2, hoist6 is not located at depot0, hoist6 is not located at distributor2, hoist6 is not located at distributor3, hoist6 is not raising crate0, pallet0 cannot be found located at distributor2, pallet0 does not have crate3 on it, pallet0 is not at distributor1, pallet0 is not located at depot1, pallet0 is not located at depot2, pallet0 is not located at distributor0, pallet1 cannot be found located at depot0, pallet1 cannot be found located at depot1, pallet1 cannot be found located at distributor1, pallet1 does not have crate1 on it, pallet1 is not located at distributor0, pallet2 cannot be found located at depot0, pallet2 cannot be found located at distributor2, pallet2 does not have crate3 on it, pallet2 is not at depot1, pallet2 is not at distributor3, pallet2 is not located at depot2, pallet2 is not located at distributor0, pallet3 cannot be found located at depot2, pallet3 cannot be found located at distributor0, pallet3 does not have crate0 on it, pallet3 is not at depot0, pallet3 is not at depot1, pallet3 is not at distributor3, pallet3 is not located at distributor1, pallet3 is not located at distributor2, pallet4 cannot be found located at distributor0, pallet4 cannot be found located at distributor1, pallet4 cannot be found located at distributor2, pallet4 does not have crate3 on it, pallet4 is not at depot0, pallet4 is not at distributor3, pallet5 cannot be found located at depot0, pallet5 cannot be found located at distributor0, pallet5 cannot be found located at distributor3, pallet5 does not have crate3 on it, pallet5 is not at depot2, pallet5 is not located at depot1, pallet5 is not located at distributor1, pallet5 is not located at distributor2, pallet6 cannot be found located at distributor1, pallet6 cannot be found located at distributor3, pallet6 does not have crate2 on it, pallet6 is not at depot1, pallet6 is not at depot2, pallet6 is not at distributor0, pallet6 is not at distributor2, truck0 cannot be found located at depot1, truck0 does not contain crate1, truck0 does not contain crate2, truck0 does not contain crate3, truck0 is not at depot2, truck0 is not at distributor3, truck0 is not located at distributor1, truck1 is not at depot0, truck1 is not at distributor2, truck1 is not located at depot2, truck2 cannot be found located at depot1, truck2 cannot be found located at depot2, truck2 cannot be found located at distributor0, truck2 does not contain crate1, truck2 does not contain crate3, truck2 is not located at depot0 and truck2 is not located at distributor1?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 has crate1 on it, crate0 is located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate2 is clear of any crates, crate2 is located at depot0, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where hoist5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is accessible, hoist6 is available for work, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 is clear, pallet4 is clear, pallet4 is located at distributor1, pallet5 is at distributor2, pallet6 is clear of any crates, pallet6 is located at distributor3 and truck2 is at depot2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck1 is driven from depot1 to depot0, at depot0, hoist0 lifts crate2 off pallet0, at depot0, hoist0 loads crate2 into truck1, truck1 is then driven from depot0 to depot1, at depot1, hoist1 unloads crate2 from truck1, and crate2 is dropped on pallet1 at depot1 by hoist1, meanwhile, crate3 is lifted from pallet2 at depot2 by hoist2, crate3 is loaded by hoist2 into truck2 at depot2, truck2 is then driven from depot2 to distributor3, hoist6 unloads crate3 from truck2 at distributor3, and crate3 is dropped on pallet6 at distributor3, at distributor2, hoist5 lifts crate1 off crate0, hoist5 loads crate1 into truck0 at distributor2, hoist5 lifts crate0 from pallet5 at distributor2, crate0 is loaded by hoist5 into truck0 at distributor2, hoist5 unloads crate1 from truck0 at distributor2, truck0 is then driven from distributor2 to distributor0, hoist3 unloads crate0 from truck0 at distributor0, and crate0 is dropped on pallet3 at distributor0 by hoist3 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: crate0 is not located at distributor3, crate0 does not have crate0 on it, crate0 does not have crate2 on it, crate0 does not have crate3 on it, crate0 is not at depot2, crate0 is not at distributor1, crate0 is not in truck1, crate0 is not in truck2, crate0 is not inside truck0, crate0 is not located at depot0, crate0 is not located at distributor0, crate0 is not on pallet1, crate0 is not on pallet4, crate0 is not on pallet6, crate0 is not on top of pallet0, crate0 is not on top of pallet2, crate0 is not on top of pallet5, crate1 is not located at depot0, crate1 is not located at depot1, crate1 is not located at distributor0, crate1 does not have crate0 on it, crate1 does not have crate1 on it, crate1 is not at distributor1, crate1 is not inside truck1, crate1 is not on crate0, crate1 is not on pallet2, crate1 is not on pallet4, crate1 is not on top of crate2, crate1 is not on top of pallet0, crate1 is not on top of pallet3, crate1 is not on top of pallet5, crate1 is not on top of pallet6, crate2 is not located at depot0, crate2 is not located at distributor1, crate2 is not located at distributor2, crate2 does not have crate0 on it, crate2 does not have crate3 on it, crate2 is not at depot2, crate2 is not at distributor3, crate2 is not inside truck1, crate2 is not inside truck2, crate2 is not located at distributor0, crate2 is not on crate2, crate2 is not on pallet0, crate2 is not on pallet1, crate2 is not on pallet2, crate2 is not on pallet3, crate2 is not on pallet4, crate2 is not on top of crate1, crate2 is not on top of pallet5, crate3 is not located at distributor0, crate3 is not located at distributor1, crate3 does not have crate0 on it, crate3 does not have crate1 on it, crate3 does not have crate2 on it, crate3 does not have crate3 on it, crate3 is not at depot0, crate3 is not at distributor3, crate3 is not inside truck1, crate3 is not located at depot1, crate3 is not on crate1, crate3 is not on top of pallet1, crate3 is not on top of pallet3, crate3 is not on top of pallet6, depot0 is not the location of hoist1, depot0 is not the location of hoist3, depot0 is not the location of hoist4, depot0 is not the location of pallet0, depot0 is not the location of pallet6, depot0 is not the location of truck0, depot1 is not the location of crate0, depot1 is not the location of crate2, depot1 is not the location of hoist0, depot1 is not the location of pallet4, depot1 is not the location of truck1, depot2 is not the location of crate1, depot2 is not the location of crate3, depot2 is not the location of hoist3, depot2 is not the location of hoist4, depot2 is not the location of hoist6, depot2 is not the location of pallet1, depot2 is not the location of pallet4, distributor0 is not the location of hoist0, distributor0 is not the location of hoist1, distributor0 is not the location of truck0, distributor0 is not the location of truck1, distributor1 is not the location of hoist1, distributor1 is not the location of hoist3, distributor1 is not the location of pallet2, distributor1 is not the location of truck1, distributor2 is not the location of crate0, distributor2 is not the location of crate1, distributor2 is not the location of crate3, distributor2 is not the location of hoist0, distributor2 is not the location of hoist2, distributor2 is not the location of hoist5, distributor2 is not the location of pallet1, distributor2 is not the location of truck0, distributor2 is not the location of truck2, distributor3 is not the location of crate1, distributor3 is not the location of hoist3, distributor3 is not the location of pallet0, distributor3 is not the location of pallet1, distributor3 is not the location of truck1, distributor3 is not the location of truck2, hoist0 is not located at depot2, hoist0 is not at depot0, hoist0 is not lifting crate0, hoist0 is not lifting crate3, hoist0 is not located at distributor1, hoist0 is not located at distributor3, hoist0 is not raising crate1, hoist0 is not raising crate2, hoist1 is not located at depot2, hoist1 is not located at distributor3, hoist1 is not lifting crate1, hoist1 is not lifting crate2, hoist1 is not lifting crate3, hoist1 is not located at depot1, hoist1 is not located at distributor2, hoist1 is not raising crate0, hoist2 is not located at depot0, hoist2 is not located at depot2, hoist2 is not located at distributor0, hoist2 is not located at distributor3, hoist2 is not at distributor1, hoist2 is not lifting crate3, hoist2 is not lifting crate1, hoist2 is not located at depot1, hoist2 is not raising crate0, hoist2 is not raising crate2, hoist3 is not located at depot1, hoist3 is not located at distributor2, hoist3 is not at distributor0, hoist3 is not lifting crate0, hoist3 is not lifting crate1, hoist3 is not lifting crate3, hoist3 is not raising crate2, hoist4 is not located at distributor0, hoist4 is not located at distributor1, hoist4 is not located at distributor3, hoist4 is not at depot1, hoist4 is not lifting crate0, hoist4 is not lifting crate3, hoist4 is not located at distributor2, hoist4 is not raising crate1, hoist4 is not raising crate2, hoist5 is not located at depot1, hoist5 is not located at depot2, hoist5 is not located at distributor0, hoist5 is not located at distributor3, hoist5 is not at distributor1, hoist5 is not lifting crate3, hoist5 is not located at depot0, hoist5 is not raising crate0, hoist5 is not raising crate1, hoist5 is not raising crate2, hoist6 is not located at distributor0, hoist6 is not located at distributor1, hoist6 is not at depot1, hoist6 is not lifting crate1, hoist6 is not lifting crate3, hoist6 is not lifting crate2, hoist6 is not located at depot0, hoist6 is not located at distributor2, hoist6 is not located at distributor3, hoist6 is not raising crate0, pallet0 is not located at distributor2, pallet0 does not have crate3 on it, pallet0 is not at distributor1, pallet0 is not located at depot1, pallet0 is not located at depot2, pallet0 is not located at distributor0, pallet1 is not located at depot0, pallet1 is not located at depot1, pallet1 is not located at distributor1, pallet1 does not have crate1 on it, pallet1 is not located at distributor0, pallet2 is not located at depot0, pallet2 is not located at distributor2, pallet2 does not have crate3 on it, pallet2 is not at depot1, pallet2 is not at distributor3, pallet2 is not located at depot2, pallet2 is not located at distributor0, pallet3 is not located at depot2, pallet3 is not located at distributor0, pallet3 does not have crate0 on it, pallet3 is not at depot0, pallet3 is not at depot1, pallet3 is not at distributor3, pallet3 is not located at distributor1, pallet3 is not located at distributor2, pallet4 is not located at distributor0, pallet4 is not located at distributor1, pallet4 is not located at distributor2, pallet4 does not have crate3 on it, pallet4 is not at depot0, pallet4 is not at distributor3, pallet5 is not located at depot0, pallet5 is not located at distributor0, pallet5 is not located at distributor3, pallet5 does not have crate3 on it, pallet5 is not at depot2, pallet5 is not located at depot1, pallet5 is not located at distributor1, pallet5 is not located at distributor2, pallet6 is not located at distributor1, pallet6 is not located at distributor3, pallet6 does not have crate2 on it, pallet6 is not at depot1, pallet6 is not at depot2, pallet6 is not at distributor0, pallet6 is not at distributor2, truck0 is not located at depot1, truck0 does not contain crate1, truck0 does not contain crate2, truck0 does not contain crate3, truck0 is not at depot2, truck0 is not at distributor3, truck0 is not located at distributor1, truck1 is not at depot0, truck1 is not at distributor2, truck1 is not located at depot2, truck2 is not located at depot1, truck2 is not located at depot2, truck2 is not located at distributor0, truck2 does not contain crate1, truck2 does not contain crate3, truck2 is not located at depot0 and truck2 is not located at distributor1?", "initial_state_nl_paraphrased": "Crate0 has crate1 stacked on top of it, and it is situated at distributor2. Additionally, crate0 is placed on pallet5. Crate1, on the other hand, has no crates on top of it, and it is also located at distributor2. Similarly, crate2 is clear of any crates and is situated at depot0. Crate3 can be found at depot2, has no crates on top of it, and is placed on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is located. Distributor0 houses both hoist3 and pallet3, and distributor2 is home to both hoist5 and truck0. Distributor3 is where hoist6 is located. \n\nHoist0 is available for work, and hoist1, which is located at depot1, is also available. Hoist2, situated at depot2, is available as well. Hoist3 is available for work, and hoist4, located at distributor1, is available too. Hoist5 is accessible, and hoist6 is available for work.\n\nPallet0, which is at depot0, has crate2 on it. Pallet1, located at depot1, is empty, and pallet2 is situated at depot2. Pallet3, located at distributor0, is clear, and pallet4, which is at distributor1, is also empty. Pallet5 is at distributor2, and pallet6, located at distributor3, is clear of any crates. Lastly, truck2 is situated at depot2."}
{"question_id": "bcb3b557-2cb1-407c-b1f1-9cf88dd250bc", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0, hoist0 loads crate2 into truck2 at depot0, truck2 is driven from depot0 to distributor1, crate1 is lifted from pallet2 at depot2 by hoist2, crate1 is loaded by hoist2 into truck0 at depot2, truck0 is driven from depot2 to distributor0, hoist3 unloads crate1 from truck0 at distributor0, crate1 is dropped on pallet3 at distributor0 by hoist3, crate0 is lifted from pallet4 at distributor1 by hoist4 and crate0 is loaded by hoist4 into truck2 at distributor1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: crate0 is clear of any crates, crate1 is clear of any crates, crate2 is clear of any crates, crate3 is clear of any crates, hoist0 is available, hoist1 is available, hoist2 is available, hoist3 is available, hoist4 is available, hoist5 is available, pallet0 is clear, pallet1 is clear, pallet2 is clear, pallet3 is clear of any crates, pallet4 is clear and pallet5 is clear of any crates?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor1, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot0 is where pallet0 is located, depot0 is where truck2 is located, depot2 is where hoist2 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where crate3 is located, distributor2 is where hoist5 is located, distributor2 is where pallet5 is located, hoist0 is accessible, hoist1 is available for work, hoist1 is located at depot1, hoist2 is available, hoist3 is available for work, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is available for work, pallet0 has crate2 on it, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is at depot2, pallet3 is clear of any crates, pallet4 is located at distributor1, truck0 is located at depot2 and truck1 is at distributor2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: at depot0, hoist0 first lifts crate2 from pallet0 and then loads it into truck2, after which truck2 is driven to distributor1. Meanwhile, at depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 at distributor0 and places it on pallet3. Additionally, hoist4 lifts crate0 from pallet4 at distributor1 and loads it into truck2. Considering the resulting state, are the following properties, which do not involve negations, True or False: crate0 is not blocked by any other crates, crate1 is not blocked by any other crates, crate2 is not blocked by any other crates, crate3 is not blocked by any other crates, hoist0 is available for use, hoist1 is available for use, hoist2 is available for use, hoist3 is available for use, hoist4 is available for use, hoist5 is available for use, pallet0 is empty, pallet1 is empty, pallet2 is empty, pallet3 is empty of any crates, pallet4 is empty, and pallet5 is empty of any crates?", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor1, crate0 is positioned on top of pallet4, crate1 is situated at depot2, crate1 has no crates on it, crate1 is placed on pallet2, crate2 has no crates on it, crate3 has no crates on it, crate3 is positioned on top of pallet5, depot0 is the location of crate2, depot0 is the location of hoist0, depot0 is the location of pallet0, depot0 is the location of truck2, depot2 is the location of hoist2, distributor0 is the location of hoist3, distributor0 is the location of pallet3, distributor2 is the location of crate3, distributor2 is the location of hoist5, distributor2 is the location of pallet5, hoist0 is accessible for use, hoist1 is available for work and is located at depot1, hoist2 is available for use, hoist3 is available for work, hoist4 is accessible for use and is located at distributor1, hoist5 is available for work, pallet0 has crate2 placed on it, pallet1 has no crates on it and is located at depot1, pallet2 is situated at depot2, pallet3 has no crates on it, pallet4 is located at distributor1, truck0 is situated at depot2 and truck1 is situated at distributor2."}
{"question_id": "81bf3dae-ef21-4c11-9730-50793dbd3163", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: crate2 is lifted from pallet0 at depot0 by hoist0, crate2 is loaded by hoist0 into truck2 at depot0, from depot0, truck2 is driven to distributor1, at depot2, hoist2 lifts crate1 off pallet2, hoist2 loads crate1 into truck0 at depot2, truck0 is driven to distributor0 from depot2, hoist3 unloads crate1 from truck0 at distributor0, hoist3 drops crate1 on pallet3 at distributor0, hoist4 lifts crate0 from pallet4 at distributor1, crate0 is loaded by hoist4 into truck2 at distributor1, at distributor2, hoist5 lifts crate3 off pallet5, hoist5 loads crate3 into truck1 at distributor2, truck1 is driven from distributor2 to distributor1, hoist4 unloads crate3 from truck1 at distributor1, crate3 is dropped on pallet4 at distributor1 by hoist4, crate2 is unloaded by hoist4 from truck2 at distributor1, truck2 is driven from distributor1 to depot1, at depot1, hoist1 unloads crate0 from truck2 and crate0 is dropped on pallet1 at depot1 by hoist1 to reach the current state. In this state, is it True or False that hoist0 is not accessible?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor1, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot0 is where pallet0 is located, depot0 is where truck2 is located, depot2 is where hoist2 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where crate3 is located, distributor2 is where hoist5 is located, distributor2 is where pallet5 is located, hoist0 is accessible, hoist1 is available for work, hoist1 is located at depot1, hoist2 is available, hoist3 is available for work, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is available for work, pallet0 has crate2 on it, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is at depot2, pallet3 is clear of any crates, pallet4 is located at distributor1, truck0 is located at depot2 and truck1 is at distributor2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2, which then departs from depot0 for distributor1. Meanwhile, at depot2, hoist2 lifts crate1 from pallet2 and loads it into truck0, which is then driven to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 and places it on pallet3 at distributor0. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2, while at distributor2, hoist5 lifts crate3 from pallet5 and loads it into truck1, which is then driven to distributor1. At distributor1, hoist4 unloads crate3 from truck1 and places it on pallet4, and also unloads crate2 from truck2. Truck2 then proceeds to depot1, where hoist1 unloads crate0 and places it on pallet1, resulting in the current state. In this state, is it True or False that hoist0 is not accessible?", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor1, crate0 is positioned on top of pallet4, crate1 is situated at depot2, crate1 has no crates on it, crate1 is placed on pallet2, crate2 has no crates on it, crate3 has no crates on it, crate3 is positioned on top of pallet5, depot0 is the location of crate2, depot0 is the location of hoist0, depot0 is the location of pallet0, depot0 is the location of truck2, depot2 is the location of hoist2, distributor0 is the location of hoist3, distributor0 is the location of pallet3, distributor2 is the location of crate3, distributor2 is the location of hoist5, distributor2 is the location of pallet5, hoist0 is accessible for use, hoist1 is available for work and is located at depot1, hoist2 is available for use, hoist3 is available for work, hoist4 is accessible for use and is located at distributor1, hoist5 is available for work, pallet0 has crate2 placed on it, pallet1 has no crates on it and is located at depot1, pallet2 is situated at depot2, pallet3 has no crates on it, pallet4 is located at distributor1, truck0 is situated at depot2 and truck1 is situated at distributor2."}
{"question_id": "5ac9e1b1-4bda-4bcb-b10a-37b3fddcb8d4", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot0, truck2 is driven to depot2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: crate0 is clear, crate1 is clear, crate2 is clear, crate3 is clear, hoist0 is available, hoist1 is available, hoist2 is available, hoist3 is accessible, hoist4 is available for work, hoist5 is accessible, pallet0 is clear, pallet1 is clear of any crates, pallet2 is clear, pallet3 is clear of any crates, pallet4 is clear of any crates and pallet5 is clear of any crates?", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 is clear, crate0 is on pallet3, crate1 is on pallet2, crate2 can be found located at depot2, crate2 is on top of crate1, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on top of crate2, depot2 is where crate1 is located, depot2 is where pallet2 is located, distributor0 is where crate0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, pallet0 is at depot0, pallet0 is clear, pallet1 can be found located at depot1, pallet1 is clear of any crates, pallet4 is at distributor1, pallet4 is clear of any crates, pallet5 is at distributor2, pallet5 is clear of any crates, truck0 can be found located at distributor0, truck1 is located at depot1 and truck2 is at depot0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: truck2 is driven from depot0 to depot2 to achieve the current state. In this state, are the following properties, which do not involve negations, True or False: crate0 is clear, crate1 is clear, crate2 is clear, crate3 is clear, hoist0 is available, hoist1 is available, hoist2 is available, hoist3 is accessible, hoist4 is available for work, hoist5 is accessible, pallet0 is clear, pallet1 has no crates, pallet2 is clear, pallet3 has no crates, pallet4 has no crates and pallet5 has no crates?", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is positioned on pallet3, crate1 is placed on pallet2, crate2 is situated at depot2, crate2 is stacked on top of crate1, crate3 is also located at depot2, crate3 has no crates on it, crate3 is positioned on top of crate2, depot2 is the location of crate1, depot2 is also the location of pallet2, distributor0 is the location of crate0, distributor0 is also the location of hoist3, distributor0 is the location of pallet3, hoist0 is available for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for work, hoist2 is situated at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is located at distributor2, hoist5 is available, pallet0 is situated at depot0, pallet0 is empty, pallet1 is located at depot1, pallet1 has no crates on it, pallet4 is situated at distributor1, pallet4 has no crates on it, pallet5 is situated at distributor2, pallet5 has no crates on it, truck0 is located at distributor0, truck1 is situated at depot1 and truck2 is situated at depot0."}
{"question_id": "2eba7af2-30df-4326-89ea-cbc052bdb345", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at depot0, hoist0 lifts crate2 off pallet0 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: crate0 is clear of any crates, crate1 is clear, crate2 is clear, crate3 is clear, hoist0 is available for work, hoist1 is available, hoist2 is available for work, hoist3 is accessible, hoist4 is available, hoist5 is accessible, pallet0 is clear of any crates, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet3 is clear of any crates, pallet4 is clear and pallet5 is clear?", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor1, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot0 is where pallet0 is located, depot0 is where truck2 is located, depot2 is where hoist2 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where crate3 is located, distributor2 is where hoist5 is located, distributor2 is where pallet5 is located, hoist0 is accessible, hoist1 is available for work, hoist1 is located at depot1, hoist2 is available, hoist3 is available for work, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is available for work, pallet0 has crate2 on it, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is at depot2, pallet3 is clear of any crates, pallet4 is located at distributor1, truck0 is located at depot2 and truck1 is at distributor2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: at depot0, hoist0 lifts crate2 off pallet0 to achieve the current state. In this state, are the following properties, which do not involve negations, True or False: crate0 is not blocked by any crates, crate1 is unobstructed, crate2 is unobstructed, crate3 is unobstructed, hoist0 is ready for work, hoist1 is available for use, hoist2 is ready for work, hoist3 is accessible for use, hoist4 is available for work, hoist5 is accessible for use, pallet0 is empty, pallet1 is empty, pallet2 is empty, pallet3 is empty, pallet4 is clear and pallet5 is clear?", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor1, crate0 is positioned on top of pallet4, crate1 is situated at depot2, crate1 has no crates on it, crate1 is placed on pallet2, crate2 has no crates on it, crate3 has no crates on it, crate3 is positioned on top of pallet5, depot0 is the location of crate2, depot0 is the location of hoist0, depot0 is the location of pallet0, depot0 is the location of truck2, depot2 is the location of hoist2, distributor0 is the location of hoist3, distributor0 is the location of pallet3, distributor2 is the location of crate3, distributor2 is the location of hoist5, distributor2 is the location of pallet5, hoist0 is accessible for use, hoist1 is available for work and is located at depot1, hoist2 is available for use, hoist3 is available for work, hoist4 is accessible for use and is located at distributor1, hoist5 is available for work, pallet0 has crate2 placed on it, pallet1 has no crates on it and is located at depot1, pallet2 is situated at depot2, pallet3 has no crates on it, pallet4 is located at distributor1, truck0 is situated at depot2 and truck1 is situated at distributor2."}
{"question_id": "25188f50-54ec-493e-aaea-4e972f522f52", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_5", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, at depot0, hoist0 lifts crate2 off pallet0, hoist0 loads crate2 into truck1 at depot0, from depot0, truck1 is driven to depot1, hoist1 unloads crate2 from truck1 at depot1, at depot1, hoist1 drops crate2 on pallet1, crate3 is lifted from pallet2 at depot2 by hoist2, hoist2 loads crate3 into truck2 at depot2, from depot2, truck2 is driven to distributor3 and at distributor3, hoist6 unloads crate3 from truck2 to reach the current state. In this state, is it True or False that pallet3 is clear of any crates?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 has crate1 on it, crate0 is located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate2 is clear of any crates, crate2 is located at depot0, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where hoist5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is accessible, hoist6 is available for work, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 is clear, pallet4 is clear, pallet4 is located at distributor1, pallet5 is at distributor2, pallet6 is clear of any crates, pallet6 is located at distributor3 and truck2 is at depot2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 is driven from depot1 to depot0, where hoist0 removes crate2 from pallet0 and loads it into truck1. Then, truck1 is driven from depot0 back to depot1, where hoist1 unloads crate2 from truck1 and places it on pallet1. Meanwhile, at depot2, hoist2 lifts crate3 from pallet2 and loads it into truck2, which is then driven to distributor3. At distributor3, hoist6 unloads crate3 from truck2, resulting in the current state. In this state, is it True or False that pallet3 is clear of any crates?", "initial_state_nl_paraphrased": "Crate0 has crate1 stacked on top of it, and it is situated at distributor2. Additionally, crate0 is placed on pallet5. Crate1, on the other hand, has no crates on top of it and is also located at distributor2. Similarly, crate2 is clear of any crates and is situated at depot0. Crate3 can be found at depot2, has no crates on top of it, and is placed on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is located. Distributor0 houses both hoist3 and pallet3, and distributor2 is home to both hoist5 and truck0. Distributor3 is where hoist6 is located. \n\nHoist0 is available for work, and hoist1 is available and situated at depot1. Hoist2 is also available and located at depot2. Hoist3 is available for work, while hoist4 is available and located at distributor1. Hoist5 is accessible, and hoist6 is available for work.\n\nPallet0 has crate2 on it and is located at depot0. Pallet1 is empty and situated at depot1. Pallet2 is located at depot2, and pallet3 is empty. Pallet4 is also empty and located at distributor1. Pallet5 is situated at distributor2, and pallet6 is empty and located at distributor3. Lastly, truck2 is located at depot2."}
{"question_id": "21ca8a9c-6b59-4c91-881d-cdc6dfc9e9ff", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, crate2 is lifted from pallet0 at depot0 by hoist0, hoist0 loads crate2 into truck1 at depot0, truck1 is driven from depot0 to depot1, hoist1 unloads crate2 from truck1 at depot1, at depot1, hoist1 drops crate2 on pallet1, crate3 is lifted from pallet2 at depot2 by hoist2, hoist2 loads crate3 into truck2 at depot2, from depot2, truck2 is driven to distributor3 and crate3 is unloaded by hoist6 from truck2 at distributor3 to reach the current state. In this state, is it True or False that crate2 is not clear of any crates?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 has crate1 on it, crate0 is located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate2 is clear of any crates, crate2 is located at depot0, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where hoist5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is accessible, hoist6 is available for work, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 is clear, pallet4 is clear, pallet4 is located at distributor1, pallet5 is at distributor2, pallet6 is clear of any crates, pallet6 is located at distributor3 and truck2 is at depot2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 travels from depot1 to depot0, where hoist0 lifts crate2 from pallet0 and loads it into truck1, then truck1 returns to depot1, and hoist1 unloads crate2 from truck1 and places it on pallet1 at depot1. Meanwhile, crate3 is lifted from pallet2 at depot2 by hoist2, loaded into truck2, and then truck2 is driven to distributor3, where hoist6 unloads crate3 from truck2. In the resulting state, is it True or False that crate2 is not clear of any crates?", "initial_state_nl_paraphrased": "Crate0 has crate1 stacked on top of it, and it is situated at distributor2. Additionally, crate0 is placed on pallet5. Crate1, on the other hand, has no crates on top of it and is also located at distributor2. Similarly, crate2 is clear of any crates and is situated at depot0. Crate3 can be found at depot2, has no crates on top of it, and is placed on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is located. Distributor0 houses both hoist3 and pallet3, and distributor2 is home to both hoist5 and truck0. Distributor3 is where hoist6 is located. \n\nHoist0 is available for work, and hoist1, which is located at depot1, is also available. Hoist2, situated at depot2, is available as well. Hoist3 is available for work, and hoist4, located at distributor1, is available too. Hoist5 is accessible, and hoist6 is available for work.\n\nPallet0, which has crate2 on it, is located at depot0. Pallet1 is empty and situated at depot1. Pallet2 is at depot2, and pallet3 is empty. Pallet4 is also empty and located at distributor1. Pallet5 is at distributor2, and pallet6, which is empty, is located at distributor3. Lastly, truck2 is situated at depot2."}
{"question_id": "e3cb4dc0-0aeb-42ce-b977-dee048485b11", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot0, truck2 is driven to depot2, hoist2 lifts crate3 from crate2 at depot2, hoist2 loads crate3 into truck2 at depot2, at depot2, hoist2 lifts crate2 off crate1, at depot2, hoist2 loads crate2 into truck2, hoist2 lifts crate1 from pallet2 at depot2, at depot2, hoist2 loads crate1 into truck2, from depot2, truck2 is driven to distributor0, crate0 is lifted from pallet3 at distributor0 by hoist3 and hoist3 loads crate0 into truck2 at distributor0 to reach the current state. In this state, is it True or False that hoist1 is not available?", "answer": "False", "plan_length": 10, "initial_state_nl": "Crate0 is clear, crate0 is on pallet3, crate1 is on pallet2, crate2 can be found located at depot2, crate2 is on top of crate1, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on top of crate2, depot2 is where crate1 is located, depot2 is where pallet2 is located, distributor0 is where crate0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, pallet0 is at depot0, pallet0 is clear, pallet1 can be found located at depot1, pallet1 is clear of any crates, pallet4 is at distributor1, pallet4 is clear of any crates, pallet5 is at distributor2, pallet5 is clear of any crates, truck0 can be found located at distributor0, truck1 is located at depot1 and truck2 is at depot0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 starts at depot0 and travels to depot2, where hoist2 unloads crate3 from crate2, then loads crate3 into truck2, next hoist2 unloads crate2 from crate1 and loads crate2 into truck2, followed by unloading crate1 from pallet2 and loading crate1 into truck2, all at depot2. Then, truck2 travels from depot2 to distributor0, where hoist3 unloads crate0 from pallet3 and loads crate0 into truck2, resulting in the current state. In this state, is it True or False that hoist1 is not available?", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is positioned on pallet3, crate1 is placed on pallet2, crate2 is situated at depot2, crate2 is stacked on top of crate1, crate3 is also located at depot2, crate3 has no crates on it, crate3 is positioned on top of crate2, depot2 is the location of crate1, depot2 is also the location of pallet2, distributor0 is the location of crate0, distributor0 is also the location of hoist3, distributor0 is the location of pallet3, hoist0 is available for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for work, hoist2 is situated at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is located at distributor2, hoist5 is available, pallet0 is situated at depot0, pallet0 is empty, pallet1 is located at depot1, pallet1 has no crates on it, pallet4 is situated at distributor1, pallet4 has no crates on it, pallet5 is situated at distributor2, pallet5 has no crates on it, truck0 is located at distributor0, truck1 is situated at depot1 and truck2 is situated at depot0."}
{"question_id": "2a5e760c-0ccd-4947-bae9-f6961eb415d6", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot0 from depot1, hoist0 lifts crate2 from pallet0 at depot0, crate2 is loaded by hoist0 into truck2 at depot0, truck2 is driven to distributor1 from depot0, crate1 is lifted from pallet3 at depot3 by hoist3, hoist5 lifts crate3 from pallet5 at distributor1, crate3 is loaded by hoist5 into truck2 at distributor1, crate2 is unloaded by hoist5 from truck2 at distributor1, from distributor1, truck2 is driven to distributor2 and at distributor1, hoist5 drops crate2 on pallet5 to reach the current state. In this state, is it True or False that crate1 is not clear?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear of any crates, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on top of pallet0, crate3 is at distributor1, crate3 is clear of any crates, crate3 is on pallet5, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck0 is located, depot3 is where crate1 is located, depot3 is where hoist3 is located, distributor0 is where hoist4 is located, distributor0 is where pallet4 is located, distributor2 is where hoist6 is located, hoist0 can be found located at depot0, hoist0 is accessible, hoist1 is available for work, hoist2 is accessible, hoist2 is at depot2, hoist3 is available for work, hoist4 is available for work, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 is located at depot3, pallet4 is clear of any crates, pallet5 is at distributor1, pallet6 is located at distributor2, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck2 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck2 at depot0, truck2 then travels from depot0 to distributor1, hoist3 lifts crate1 from pallet3 at depot3, hoist5 lifts crate3 from pallet5 at distributor1, hoist5 loads crate3 into truck2 at distributor1, hoist5 unloads crate2 from truck2 at distributor1, truck2 then travels from distributor1 to distributor2, and at distributor1, hoist5 places crate2 on pallet5 to reach the current state. In this state, is it True or False that crate1 is not clear?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 has no crates on it, crate1 is placed on top of pallet3, crate2 is located at depot0, crate2 has no crates on it, crate2 is positioned on top of pallet0, crate3 is situated at distributor1, crate3 has no crates on it, crate3 is positioned on pallet5, depot1 houses hoist1, depot1 houses pallet1, depot1 houses truck0, depot3 houses crate1, depot3 houses hoist3, distributor0 houses hoist4, distributor0 houses pallet4, distributor2 houses hoist6, hoist0 is situated at depot0, hoist0 is accessible, hoist1 is available for operation, hoist2 is accessible, hoist2 is situated at depot2, hoist3 is available for operation, hoist4 is available for operation, hoist5 is accessible, hoist5 is situated at distributor1, hoist6 is available, pallet0 is situated at depot0, pallet1 has no crates on it, pallet2 has no crates on it, pallet2 is situated at depot2, pallet3 is situated at depot3, pallet4 has no crates on it, pallet5 is situated at distributor1, pallet6 is situated at distributor2, truck1 is situated at distributor0 and truck2 is situated at depot1."}
{"question_id": "2ba8ec5b-9fb9-4420-9ceb-b827c3ec1e14", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_9", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck2 is driven to depot0, crate2 is lifted from pallet0 at depot0 by hoist0, at depot0, hoist0 loads crate2 into truck2, truck2 is driven from depot0 to distributor1, hoist3 lifts crate1 from pallet3 at depot3, hoist5 lifts crate3 from pallet5 at distributor1, at distributor1, hoist5 loads crate3 into truck2, hoist5 unloads crate2 from truck2 at distributor1, truck2 is driven to distributor2 from distributor1 and at distributor1, hoist5 drops crate2 on pallet5 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: crate1 is not clear, crate3 is not clear, hoist3 is not available for work, pallet5 is not clear of any crates and pallet6 is not clear of any crates?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear of any crates, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on top of pallet0, crate3 is at distributor1, crate3 is clear of any crates, crate3 is on pallet5, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck0 is located, depot3 is where crate1 is located, depot3 is where hoist3 is located, distributor0 is where hoist4 is located, distributor0 is where pallet4 is located, distributor2 is where hoist6 is located, hoist0 can be found located at depot0, hoist0 is accessible, hoist1 is available for work, hoist2 is accessible, hoist2 is at depot2, hoist3 is available for work, hoist4 is available for work, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 is located at depot3, pallet4 is clear of any crates, pallet5 is at distributor1, pallet6 is located at distributor2, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 is moved from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 then loads crate2 into truck2 at depot0, truck2 is then driven from depot0 to distributor1, hoist3 lifts crate1 from pallet3 at depot3, hoist5 lifts crate3 from pallet5 at distributor1, hoist5 loads crate3 into truck2 at distributor1, hoist5 unloads crate2 from truck2 at distributor1, truck2 is then driven from distributor1 to distributor2, and finally, hoist5 places crate2 on pallet5 at distributor1, resulting in the current state. In this state, are the following properties that involve negations True or False: crate1 is not clear, crate3 is not clear, hoist3 is not available for work, pallet5 is not clear of any crates, and pallet6 is not clear of any crates?", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 has no crates on it, crate1 is placed on top of pallet3, crate2 is located at depot0, crate2 has no crates on it, crate2 is positioned on top of pallet0, crate3 is situated at distributor1, crate3 has no crates on it, crate3 is positioned on pallet5, depot1 houses hoist1, depot1 houses pallet1, depot1 houses truck0, depot3 houses crate1, depot3 houses hoist3, distributor0 houses hoist4, distributor0 houses pallet4, distributor2 houses hoist6, hoist0 is situated at depot0, hoist0 is accessible, hoist1 is available for operation, hoist2 is accessible, hoist2 is situated at depot2, hoist3 is available for operation, hoist4 is available for operation, hoist5 is accessible, hoist5 is situated at distributor1, hoist6 is available, pallet0 is situated at depot0, pallet1 has no crates on it, pallet2 has no crates on it, pallet2 is situated at depot2, pallet3 is situated at depot3, pallet4 has no crates on it, pallet5 is situated at distributor1, pallet6 is situated at distributor2, truck1 is situated at distributor0 and truck2 is situated at depot1."}
{"question_id": "bb207c8d-7bc3-4eae-8805-421d69c0130b", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_10", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot0 to depot2 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: crate0 is not clear of any crates, crate1 is not clear, crate2 is not clear, crate3 is not clear of any crates, hoist0 is not available, hoist1 is not accessible, hoist2 is not available, hoist3 is not available, hoist4 is not available, hoist5 is not accessible, pallet0 is not clear of any crates, pallet1 is not clear, pallet2 is not clear, pallet3 is not clear, pallet4 is not clear of any crates and pallet5 is not clear of any crates?", "answer": "False", "plan_length": 1, "initial_state_nl": "Crate0 is clear, crate0 is on pallet3, crate1 is on pallet2, crate2 can be found located at depot2, crate2 is on top of crate1, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on top of crate2, depot2 is where crate1 is located, depot2 is where pallet2 is located, distributor0 is where crate0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, pallet0 is at depot0, pallet0 is clear, pallet1 can be found located at depot1, pallet1 is clear of any crates, pallet4 is at distributor1, pallet4 is clear of any crates, pallet5 is at distributor2, pallet5 is clear of any crates, truck0 can be found located at distributor0, truck1 is located at depot1 and truck2 is at depot0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck2 is driven from depot0 to depot2 to reach the current state. In this state, are all of the following properties that involve negations True or False: crate0 has crates on it, crate1 is not empty, crate2 is not empty, crate3 has crates on it, hoist0 is unavailable, hoist1 is inaccessible, hoist2 is unavailable, hoist3 is unavailable, hoist4 is unavailable, hoist5 is inaccessible, pallet0 has crates on it, pallet1 is not empty, pallet2 is not empty, pallet3 is not empty, pallet4 has crates on it and pallet5 has crates on it?", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is positioned on pallet3, crate1 is placed on pallet2, crate2 is situated at depot2, crate2 is stacked on top of crate1, crate3 is also located at depot2, crate3 has no crates on it, crate3 is positioned on top of crate2, depot2 is the location of crate1, depot2 is also the location of pallet2, distributor0 is the location of crate0, distributor0 is also the location of hoist3, distributor0 is the location of pallet3, hoist0 is available for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for work, hoist2 is situated at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is located at distributor2, hoist5 is available, pallet0 is situated at depot0, pallet0 is empty, pallet1 is located at depot1, pallet1 has no crates on it, pallet4 is situated at distributor1, pallet4 has no crates on it, pallet5 is situated at distributor2, pallet5 has no crates on it, truck0 is located at distributor0, truck1 is situated at depot1 and truck2 is situated at depot0."}
{"question_id": "0a2ceef8-380c-4399-a9ff-bce8b2725e83", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_10", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot2 from depot0, hoist2 lifts crate3 from crate2 at depot2, at depot2, hoist2 loads crate3 into truck2, crate2 is lifted from crate1 at depot2 by hoist2, crate2 is loaded by hoist2 into truck2 at depot2, at depot2, hoist2 lifts crate1 off pallet2, crate1 is loaded by hoist2 into truck2 at depot2, truck2 is driven to distributor0 from depot2, hoist3 lifts crate0 from pallet3 at distributor0, crate0 is loaded by hoist3 into truck2 at distributor0, at distributor0, hoist3 unloads crate1 from truck2, truck2 is driven to distributor1 from distributor0, hoist4 unloads crate2 from truck2 at distributor1, truck2 is driven to distributor2 from distributor1, crate3 is unloaded by hoist5 from truck2 at distributor2, at distributor0, hoist3 drops crate1 on pallet3, crate2 is dropped on pallet4 at distributor1 by hoist4, crate3 is dropped on pallet5 at distributor2 by hoist5 and crate0 is unloaded by hoist5 from truck2 at distributor2 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: crate0 is not clear, crate1 is not clear of any crates, crate2 is not clear of any crates, crate3 is not clear of any crates, hoist0 is not available for work, hoist1 is not accessible, hoist2 is not available for work, hoist3 is not available for work, hoist4 is not available for work, hoist5 is not available for work, pallet0 is not clear of any crates, pallet1 is not clear of any crates, pallet2 is not clear, pallet3 is not clear, pallet4 is not clear of any crates and pallet5 is not clear?", "answer": "False", "plan_length": 19, "initial_state_nl": "Crate0 is clear, crate0 is on pallet3, crate1 is on pallet2, crate2 can be found located at depot2, crate2 is on top of crate1, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on top of crate2, depot2 is where crate1 is located, depot2 is where pallet2 is located, distributor0 is where crate0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, pallet0 is at depot0, pallet0 is clear, pallet1 can be found located at depot1, pallet1 is clear of any crates, pallet4 is at distributor1, pallet4 is clear of any crates, pallet5 is at distributor2, pallet5 is clear of any crates, truck0 can be found located at distributor0, truck1 is located at depot1 and truck2 is at depot0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 travels from depot0 to depot2, hoist2 lifts crate3 from crate2 at depot2, hoist2 then loads crate3 into truck2 at depot2, hoist2 lifts crate2 from crate1 at depot2, hoist2 loads crate2 into truck2 at depot2, hoist2 lifts crate1 from pallet2 at depot2, hoist2 loads crate1 into truck2 at depot2, truck2 then travels from depot2 to distributor0, hoist3 lifts crate0 from pallet3 at distributor0, hoist3 loads crate0 into truck2 at distributor0, hoist3 unloads crate1 from truck2 at distributor0, truck2 travels from distributor0 to distributor1, hoist4 unloads crate2 from truck2 at distributor1, truck2 then travels from distributor1 to distributor2, hoist5 unloads crate3 from truck2 at distributor2, hoist3 drops crate1 onto pallet3 at distributor0, hoist4 drops crate2 onto pallet4 at distributor1, hoist5 drops crate3 onto pallet5 at distributor2, and hoist5 unloads crate0 from truck2 at distributor2 to reach the current state. In this state, are the following properties that involve negations True or False: crate0 is not clear of any objects, crate1 is not clear of any objects, crate2 is not clear of any objects, crate3 is not clear of any objects, hoist0 is not available for use, hoist1 is not accessible, hoist2 is not available for use, hoist3 is not available for use, hoist4 is not available for use, hoist5 is not available for use, pallet0 is not clear of any objects, pallet1 is not clear of any objects, pallet2 is not clear, pallet3 is not clear, pallet4 is not clear of any objects, and pallet5 is not clear?", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is positioned on pallet3, crate1 is placed on pallet2, crate2 is situated at depot2, crate2 is stacked on top of crate1, crate3 is also located at depot2, crate3 has no crates on it, crate3 is positioned on top of crate2, depot2 is the location of crate1, depot2 is also the location of pallet2, distributor0 is the location of crate0, distributor0 is also the location of hoist3, distributor0 is the location of pallet3, hoist0 is available for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for work, hoist2 is situated at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is located at distributor2, hoist5 is available, pallet0 is situated at depot0, pallet0 is empty, pallet1 is located at depot1, pallet1 has no crates on it, pallet4 is situated at distributor1, pallet4 has no crates on it, pallet5 is situated at distributor2, pallet5 has no crates on it, truck0 is located at distributor0, truck1 is situated at depot1 and truck2 is situated at depot0."}
{"question_id": "91106c0a-8b7a-4bd1-a37c-87ab9e4f4125", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_9", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot2 from depot0, crate3 is lifted from crate2 at depot2 by hoist2, crate3 is loaded by hoist2 into truck2 at depot2, at depot2, hoist2 lifts crate2 off crate1, hoist2 loads crate2 into truck2 at depot2, at depot2, hoist2 lifts crate1 off pallet2, hoist2 loads crate1 into truck2 at depot2, from depot2, truck2 is driven to distributor0, crate0 is lifted from pallet3 at distributor0 by hoist3 and hoist3 loads crate0 into truck2 at distributor0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: crate0 is not clear of any crates, crate1 is not clear of any crates, crate2 is not clear of any crates and crate3 is not clear?", "answer": "True", "plan_length": 10, "initial_state_nl": "Crate0 is clear, crate0 is on pallet3, crate1 is on pallet2, crate2 can be found located at depot2, crate2 is on top of crate1, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on top of crate2, depot2 is where crate1 is located, depot2 is where pallet2 is located, distributor0 is where crate0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, pallet0 is at depot0, pallet0 is clear, pallet1 can be found located at depot1, pallet1 is clear of any crates, pallet4 is at distributor1, pallet4 is clear of any crates, pallet5 is at distributor2, pallet5 is clear of any crates, truck0 can be found located at distributor0, truck1 is located at depot1 and truck2 is at depot0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck2 travels from depot0 to depot2, hoist2 lifts crate3 from crate2 at depot2 and then loads it into truck2, at depot2, hoist2 lifts crate2 off crate1 and loads it into truck2, and at depot2, hoist2 lifts crate1 off pallet2 and loads it into truck2. Subsequently, truck2 travels from depot2 to distributor0, where hoist3 lifts crate0 from pallet3 and loads it into truck2, resulting in the current state. In this state, are the following properties that involve negations True or False: crate0 is not clear of any crates, crate1 is not clear of any crates, crate2 is not clear of any crates, and crate3 is not clear?", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is positioned on pallet3, crate1 is placed on pallet2, crate2 is situated at depot2, crate2 is stacked on top of crate1, crate3 is also located at depot2, crate3 has no crates on it, crate3 is positioned on top of crate2, depot2 is the location of crate1, depot2 is also the location of pallet2, distributor0 is the location of crate0, distributor0 is also the location of hoist3, distributor0 is the location of pallet3, hoist0 is available for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for work, hoist2 is situated at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is located at distributor2, hoist5 is available, pallet0 is situated at depot0, pallet0 is empty, pallet1 is located at depot1, pallet1 has no crates on it, pallet4 is situated at distributor1, pallet4 has no crates on it, pallet5 is situated at distributor2, pallet5 has no crates on it, truck0 is located at distributor0, truck1 is situated at depot1 and truck2 is situated at depot0."}
