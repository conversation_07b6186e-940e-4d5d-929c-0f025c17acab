{"question_id": "bb8568a9-d741-4cc7-9cce-f2be07e9d90d", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_9", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot2 from depot0, at depot2, hoist2 lifts crate3 off crate2, crate3 is loaded by hoist2 into truck2 at depot2, hoist2 lifts crate2 from crate1 at depot2, crate2 is loaded by hoist2 into truck2 at depot2, crate1 is lifted from pallet2 at depot2 by hoist2, at depot2, hoist2 loads crate1 into truck2, truck2 is driven from depot2 to distributor0, hoist3 lifts crate0 from pallet3 at distributor0, crate0 is loaded by hoist3 into truck2 at distributor0, hoist3 unloads crate1 from truck2 at distributor0, truck2 is driven from distributor0 to distributor1, hoist4 unloads crate2 from truck2 at distributor1, truck2 is driven from distributor1 to distributor2, crate3 is unloaded by hoist5 from truck2 at distributor2, crate1 is dropped on pallet3 at distributor0 by hoist3, hoist4 drops crate2 on pallet4 at distributor1, at distributor2, hoist5 drops crate3 on pallet5 and hoist5 unloads crate0 from truck2 at distributor2 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for crate0? Write None if there are none", "answer": "crate0 cannot be found located at distributor0, crate0 cannot be found located at distributor1, crate0 does not have crate0 on it, crate0 is not clear of any crates, crate0 is not in truck0, crate0 is not in truck1, crate0 is not inside truck2, crate0 is not located at depot1, crate0 is not on pallet3, crate0 is not on top of crate1, crate0 is not on top of pallet1, crate0 is not on top of pallet4, crate0 is not on top of pallet5, crate1 is not on crate0, crate2 does not have crate0 on it, crate2 is not on crate0, crate3 does not have crate0 on it, crate3 is not on crate0, depot0 is where crate0 is not located, depot2 is where crate0 is not located, distributor2 is where crate0 is not located, hoist0 is not lifting crate0, hoist1 is not elevating crate0, hoist2 is not raising crate0, hoist3 is not raising crate0, hoist4 is not raising crate0, hoist5 is elevating crate0, pallet0 does not have crate0 on it and pallet2 does not have crate0 on it", "plan_length": 19, "initial_state_nl": "Crate0 is clear, crate0 is on pallet3, crate1 is on pallet2, crate2 can be found located at depot2, crate2 is on top of crate1, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on top of crate2, depot2 is where crate1 is located, depot2 is where pallet2 is located, distributor0 is where crate0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, pallet0 is at depot0, pallet0 is clear, pallet1 can be found located at depot1, pallet1 is clear of any crates, pallet4 is at distributor1, pallet4 is clear of any crates, pallet5 is at distributor2, pallet5 is clear of any crates, truck0 can be found located at distributor0, truck1 is located at depot1 and truck2 is at depot0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 travels from depot0 to depot2, where hoist2 lifts crate3 off crate2, then loads crate3 into truck2, lifts crate2 from crate1, loads crate2 into truck2, lifts crate1 from pallet2, and loads crate1 into truck2, all at depot2. Subsequently, truck2 is driven to distributor0, where hoist3 lifts crate0 from pallet3 and loads it into truck2, while unloading crate1 from truck2. Truck2 then proceeds to distributor1, where hoist4 unloads crate2, and then to distributor2, where hoist5 unloads crate3. Additionally, hoist3 drops crate1 on pallet3 at distributor0, hoist4 drops crate2 on pallet4 at distributor1, and hoist5 drops crate3 on pallet5 at distributor2, and finally unloads crate0 from truck2 at distributor2, resulting in the current state. In this state, what are the valid properties of the state (both with and without negations) for crate0? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is positioned on pallet3, crate1 is placed on pallet2, crate2 is situated at depot2, crate2 is stacked on top of crate1, crate3 is also located at depot2, crate3 has no crates on it, crate3 is positioned on top of crate2, depot2 is the location of crate1, depot2 is also the location of pallet2, distributor0 is the location of crate0, distributor0 is also the location of hoist3, distributor0 is the location of pallet3, hoist0 is available for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for work, hoist2 is situated at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is located at distributor2, hoist5 is available, pallet0 is situated at depot0, pallet0 is empty, pallet1 is located at depot1, pallet1 has no crates on it, pallet4 is situated at distributor1, pallet4 has no crates on it, pallet5 is situated at distributor2, pallet5 has no crates on it, truck0 is located at distributor0, truck1 is situated at depot1 and truck2 is situated at depot0."}
{"question_id": "1c4c4c7c-9596-4a33-98d9-a63afd2e866f", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_9", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven to depot0 from depot1, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck1 at depot0, from depot0, truck1 is driven to depot1, at depot1, hoist1 unloads crate2 from truck1, crate2 is dropped on pallet1 at depot1 by hoist1, crate3 is lifted from pallet2 at depot2 by hoist2, crate3 is loaded by hoist2 into truck2 at depot2, from depot2, truck2 is driven to distributor3 and hoist6 unloads crate3 from truck2 at distributor3 to reach the current state. In this state, what are the valid properties of the state that involve negations for distributor0? Write None if there are none", "answer": "crate0 cannot be found located at distributor0, crate1 cannot be found located at distributor0, crate2 cannot be found located at distributor0, crate3 is not at distributor0, distributor0 is where hoist5 is not located, distributor0 is where pallet0 is not located, distributor0 is where truck2 is not located, hoist0 cannot be found located at distributor0, hoist1 is not located at distributor0, hoist2 is not located at distributor0, hoist4 cannot be found located at distributor0, hoist6 is not located at distributor0, pallet1 is not at distributor0, pallet2 is not at distributor0, pallet4 cannot be found located at distributor0, pallet5 is not at distributor0, pallet6 is not at distributor0, truck0 is not located at distributor0 and truck1 cannot be found located at distributor0", "plan_length": 10, "initial_state_nl": "Crate0 has crate1 on it, crate0 is located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate2 is clear of any crates, crate2 is located at depot0, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where hoist5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is accessible, hoist6 is available for work, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 is clear, pallet4 is clear, pallet4 is located at distributor1, pallet5 is at distributor2, pallet6 is clear of any crates, pallet6 is located at distributor3 and truck2 is at depot2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 travels from depot1 to depot0, hoist0 picks up crate2 from pallet0 at depot0, hoist0 then loads crate2 onto truck1 at depot0, truck1 is then driven from depot0 to depot1, at depot1, hoist1 unloads crate2 from truck1, and hoist1 places crate2 on pallet1 at depot1, meanwhile, hoist2 lifts crate3 from pallet2 at depot2, hoist2 loads crate3 into truck2 at depot2, truck2 is then driven from depot2 to distributor3, and finally, hoist6 unloads crate3 from truck2 at distributor3, resulting in the current state. In this state, what are the valid properties of the state that involve negations for distributor0? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 has crate1 stacked on top of it, and it is situated at distributor2. Additionally, crate0 is placed on pallet5. Crate1, on the other hand, has no crates on top of it and is also located at distributor2. Similarly, crate2 is clear of any crates and is situated at depot0. Crate3 can be found at depot2, has no crates on top of it, and is placed on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is located. Distributor0 houses both hoist3 and pallet3, and distributor2 is home to both hoist5 and truck0. Distributor3 is where hoist6 is located. \n\nHoist0 is available for work, and hoist1 is available and situated at depot1. Hoist2 is also available and located at depot2. Hoist3 is available for work, while hoist4 is available and located at distributor1. Hoist5 is accessible, and hoist6 is available for work.\n\nPallet0 has crate2 on it and is located at depot0. Pallet1 is empty and situated at depot1. Pallet2 is located at depot2, and pallet3 is empty. Pallet4 is also empty and located at distributor1. Pallet5 is situated at distributor2, and pallet6 is empty and located at distributor3. Lastly, truck2 is located at depot2."}
{"question_id": "c10b3718-05e0-4b44-b515-0297b5159886", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_11", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot0 from depot1, crate2 is lifted from pallet0 at depot0 by hoist0, crate2 is loaded by hoist0 into truck2 at depot0, truck2 is driven from depot0 to distributor1, crate1 is lifted from pallet3 at depot3 by hoist3, crate3 is lifted from pallet5 at distributor1 by hoist5, at distributor1, hoist5 loads crate3 into truck2, hoist5 unloads crate2 from truck2 at distributor1, from distributor1, truck2 is driven to distributor2, at distributor1, hoist5 drops crate2 on pallet5, crate0 is lifted from pallet6 at distributor2 by hoist6, at distributor2, hoist6 loads crate0 into truck2, truck2 is driven from distributor2 to depot3, at depot3, hoist3 loads crate1 into truck2, at depot3, hoist3 unloads crate0 from truck2, truck2 is driven from depot3 to distributor0, at distributor0, hoist4 unloads crate3 from truck2, hoist3 drops crate0 on pallet3 at depot3 and crate3 is dropped on pallet4 at distributor0 by hoist4 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for hoist0? Write None if there are none", "answer": "distributor2 is where hoist0 is not located, hoist0 can be found located at depot0, hoist0 cannot be found located at distributor0, hoist0 is accessible, hoist0 is not at depot1, hoist0 is not at distributor1, hoist0 is not elevating crate2, hoist0 is not elevating crate3, hoist0 is not lifting crate1, hoist0 is not located at depot2, hoist0 is not located at depot3 and hoist0 is not raising crate0", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear of any crates, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on top of pallet0, crate3 is at distributor1, crate3 is clear of any crates, crate3 is on pallet5, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck0 is located, depot3 is where crate1 is located, depot3 is where hoist3 is located, distributor0 is where hoist4 is located, distributor0 is where pallet4 is located, distributor2 is where hoist6 is located, hoist0 can be found located at depot0, hoist0 is accessible, hoist1 is available for work, hoist2 is accessible, hoist2 is at depot2, hoist3 is available for work, hoist4 is available for work, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 is located at depot3, pallet4 is clear of any crates, pallet5 is at distributor1, pallet6 is located at distributor2, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 loads crate2 into truck2 at depot0, truck2 then proceeds from depot0 to distributor1, hoist3 lifts crate1 from pallet3 at depot3, hoist5 lifts crate3 from pallet5 at distributor1, hoist5 loads crate3 into truck2 at distributor1, and unloads crate2 from truck2 at distributor1, after which truck2 is driven from distributor1 to distributor2, hoist5 places crate2 on pallet5 at distributor1, hoist6 lifts crate0 from pallet6 at distributor2, hoist6 loads crate0 into truck2 at distributor2, truck2 then travels from distributor2 to depot3, hoist3 loads crate1 into truck2 at depot3, and unloads crate0 from truck2 at depot3, truck2 is then driven from depot3 to distributor0, hoist4 unloads crate3 from truck2 at distributor0, hoist3 places crate0 on pallet3 at depot3, and hoist4 places crate3 on pallet4 at distributor0, resulting in the current state. In this state, what are the valid properties of the state (both with and without negations) for hoist0? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 has no crates on top, crate1 is placed on top of pallet3, crate2 is located at depot0, crate2 has no crates on top, crate2 is positioned on top of pallet0, crate3 is situated at distributor1, crate3 has no crates on top, crate3 is positioned on pallet5, depot1 houses hoist1, depot1 houses pallet1, depot1 houses truck0, depot3 houses crate1, depot3 houses hoist3, distributor0 houses hoist4, distributor0 houses pallet4, distributor2 houses hoist6, hoist0 is situated at depot0, hoist0 is accessible, hoist1 is available for operation, hoist2 is accessible, hoist2 is situated at depot2, hoist3 is available for operation, hoist4 is available for operation, hoist5 is accessible, hoist5 is situated at distributor1, hoist6 is available, pallet0 is situated at depot0, pallet1 has no crates on it, pallet2 has no crates on it, pallet2 is situated at depot2, pallet3 is situated at depot3, pallet4 has no crates on it, pallet5 is situated at distributor1, pallet6 is situated at distributor2, truck1 is situated at distributor0 and truck2 is situated at depot1."}
{"question_id": "56bd2dcb-fa6e-476a-8a88-3caa23e1e6f2", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_10", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, hoist0 lifts crate2 from pallet0 at depot0, crate2 is loaded by hoist0 into truck1 at depot0, truck1 is driven to depot1 from depot0, crate2 is unloaded by hoist1 from truck1 at depot1, crate2 is dropped on pallet1 at depot1 by hoist1, at depot2, hoist2 lifts crate3 off pallet2, crate3 is loaded by hoist2 into truck2 at depot2, truck2 is driven from depot2 to distributor3, crate3 is unloaded by hoist6 from truck2 at distributor3, crate3 is dropped on pallet6 at distributor3 by hoist6, crate1 is lifted from crate0 at distributor2 by hoist5, at distributor2, hoist5 loads crate1 into truck0, crate0 is lifted from pallet5 at distributor2 by hoist5, hoist5 loads crate0 into truck0 at distributor2, hoist5 unloads crate1 from truck0 at distributor2, from distributor2, truck0 is driven to distributor0, crate0 is unloaded by hoist3 from truck0 at distributor0 and hoist3 drops crate0 on pallet3 at distributor0 to reach the current state. In this state, what are the valid properties of the state that involve negations for pallet6? Write None if there are none", "answer": "crate0 is not on pallet6, crate1 is not on top of pallet6, depot0 is where pallet6 is not located, distributor0 is where pallet6 is not located, pallet6 does not have crate2 on it, pallet6 is not at depot1, pallet6 is not at distributor2, pallet6 is not clear of any crates, pallet6 is not located at depot2 and pallet6 is not located at distributor1", "plan_length": 19, "initial_state_nl": "Crate0 has crate1 on it, crate0 is located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate2 is clear of any crates, crate2 is located at depot0, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where hoist5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is accessible, hoist6 is available for work, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 is clear, pallet4 is clear, pallet4 is located at distributor1, pallet5 is at distributor2, pallet6 is clear of any crates, pallet6 is located at distributor3 and truck2 is at depot2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: truck1 is driven from depot1 to depot0, then hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck1, after which truck1 is driven back to depot1, where crate2 is unloaded by hoist1 and placed on pallet1. At depot2, hoist2 lifts crate3 from pallet2 and loads it into truck2, which is then driven to distributor3, where crate3 is unloaded by hoist6 and placed on pallet6. Meanwhile, at distributor2, hoist5 lifts crate1 from crate0 and loads it into truck0, then lifts crate0 from pallet5 and also loads it into truck0. However, hoist5 then unloads crate1 from truck0. Finally, truck0 is driven from distributor2 to distributor0, where crate0 is unloaded by hoist3 and placed on pallet3. In this state, what are the valid properties of the state that involve negations for pallet6? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 has crate1 stacked on top of it, and it is situated at distributor2. Additionally, crate0 is placed on pallet5. Crate1, on the other hand, has no crates on top of it and is also located at distributor2. Similarly, crate2 is clear of any crates and is situated at depot0. Crate3 is located at depot2, has no crates on top of it, and is placed on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is situated. Distributor0 houses both hoist3 and pallet3, whereas distributor2 is home to both hoist5 and truck0. Distributor3 is the location of hoist6. \n\nHoist0 is available for work, and hoist1, which is located at depot1, is also available. Hoist2, situated at depot2, is available as well. Hoist3 is available for work, while hoist4, located at distributor1, is available. Hoist5 is accessible, and hoist6 is available for work.\n\nPallet0, which has crate2 on it, is situated at depot0. Pallet1 is empty and located at depot1. Pallet2 is situated at depot2, while pallet3 is empty. Pallet4 is also empty and located at distributor1. Pallet5 is at distributor2, and pallet6, which is empty, is located at distributor3. Lastly, truck2 is situated at depot2."}
{"question_id": "1f76c1e1-fcf1-4009-a71a-77ef504abed6", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_13", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck2 is driven to depot0, at depot0, hoist0 lifts crate2 off pallet0, at depot0, hoist0 loads crate2 into truck2, truck2 is driven to distributor1 from depot0, hoist3 lifts crate1 from pallet3 at depot3, hoist5 lifts crate3 from pallet5 at distributor1, crate3 is loaded by hoist5 into truck2 at distributor1, hoist5 unloads crate2 from truck2 at distributor1, truck2 is driven from distributor1 to distributor2, at distributor1, hoist5 drops crate2 on pallet5, crate0 is lifted from pallet6 at distributor2 by hoist6, at distributor2, hoist6 loads crate0 into truck2, truck2 is driven to depot3 from distributor2, at depot3, hoist3 loads crate1 into truck2, crate0 is unloaded by hoist3 from truck2 at depot3, truck2 is driven to distributor0 from depot3, hoist4 unloads crate3 from truck2 at distributor0, at depot3, hoist3 drops crate0 on pallet3 and at distributor0, hoist4 drops crate3 on pallet4 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for truck0? Write None if there are none", "answer": "crate0 is not in truck0, crate1 is not in truck0, crate2 is not in truck0, truck0 cannot be found located at depot3, truck0 cannot be found located at distributor0, truck0 cannot be found located at distributor1, truck0 does not contain crate3, truck0 is at depot1, truck0 is not located at depot0, truck0 is not located at depot2 and truck0 is not located at distributor2", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear of any crates, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on top of pallet0, crate3 is at distributor1, crate3 is clear of any crates, crate3 is on pallet5, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck0 is located, depot3 is where crate1 is located, depot3 is where hoist3 is located, distributor0 is where hoist4 is located, distributor0 is where pallet4 is located, distributor2 is where hoist6 is located, hoist0 can be found located at depot0, hoist0 is accessible, hoist1 is available for work, hoist2 is accessible, hoist2 is at depot2, hoist3 is available for work, hoist4 is available for work, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 is located at depot3, pallet4 is clear of any crates, pallet5 is at distributor1, pallet6 is located at distributor2, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 is driven from depot1 to depot0, then at depot0, hoist0 unloads crate2 from pallet0 and loads it into truck2, after which truck2 is driven to distributor1. Meanwhile, at depot3, hoist3 unloads crate1 from pallet3, and at distributor1, hoist5 unloads crate3 from pallet5 and loads it into truck2, while unloading crate2 from truck2. Subsequently, truck2 is driven to distributor2, where crate2 is dropped on pallet5 by hoist5. At distributor2, hoist6 unloads crate0 from pallet6 and loads it into truck2. Then, truck2 is driven to depot3, where hoist3 loads crate1 into truck2 and unloads crate0 from truck2. Next, truck2 is driven to distributor0, where hoist4 unloads crate3 from truck2. Finally, hoist3 drops crate0 on pallet3 at depot3, and hoist4 drops crate3 on pallet4 at distributor0, resulting in the current state. In this state, what are the valid properties of the state (both with and without negations) for truck0? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 has no crates on top, crate1 is placed on top of pallet3, crate2 is located at depot0, crate2 has no crates on top, crate2 is positioned on top of pallet0, crate3 is situated at distributor1, crate3 has no crates on top, crate3 is positioned on pallet5, depot1 houses hoist1, depot1 houses pallet1, depot1 houses truck0, depot3 houses crate1, depot3 houses hoist3, distributor0 houses hoist4, distributor0 houses pallet4, distributor2 houses hoist6, hoist0 is situated at depot0, hoist0 is operational, hoist1 is ready for work, hoist2 is operational, hoist2 is situated at depot2, hoist3 is ready for work, hoist4 is ready for work, hoist5 is operational, hoist5 is situated at distributor1, hoist6 is available, pallet0 is situated at depot0, pallet1 has no crates on it, pallet2 has no crates on it, pallet2 is situated at depot2, pallet3 is situated at depot3, pallet4 has no crates on it, pallet5 is situated at distributor1, pallet6 is situated at distributor2, truck1 is situated at distributor0 and truck2 is situated at depot1."}
{"question_id": "d590a9e0-55fd-41ba-8115-fb4f4c610c9a", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_8", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: crate2 is lifted from pallet0 at depot0 by hoist0 to reach the current state. In this state, what are the valid properties of the state that involve negations for hoist3? Write None if there are none", "answer": "depot1 is where hoist3 is not located, depot2 is where hoist3 is not located, hoist3 cannot be found located at depot0, hoist3 cannot be found located at distributor1, hoist3 is not elevating crate0, hoist3 is not elevating crate2, hoist3 is not elevating crate3, hoist3 is not lifting crate1 and hoist3 is not located at distributor2", "plan_length": 1, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor1, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot0 is where pallet0 is located, depot0 is where truck2 is located, depot2 is where hoist2 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where crate3 is located, distributor2 is where hoist5 is located, distributor2 is where pallet5 is located, hoist0 is accessible, hoist1 is available for work, hoist1 is located at depot1, hoist2 is available, hoist3 is available for work, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is available for work, pallet0 has crate2 on it, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is at depot2, pallet3 is clear of any crates, pallet4 is located at distributor1, truck0 is located at depot2 and truck1 is at distributor2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: crate2 is lifted from pallet0 at depot0 by hoist0 to achieve the current state. In this state, what are the valid properties that include negations for hoist3, or state None if there are no such properties.", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor1, crate0 is positioned on top of pallet4, crate1 is located at depot2, crate1 has no crates on it, crate1 is placed on pallet2, crate2 has no crates on it, crate3 has no crates on it, crate3 is positioned on top of pallet5, depot0 is the location of crate2, depot0 is the location of hoist0, depot0 is the location of pallet0, depot0 is the location of truck2, depot2 is the location of hoist2, distributor0 is the location of hoist3, distributor0 is the location of pallet3, distributor2 is the location of crate3, distributor2 is the location of hoist5, distributor2 is the location of pallet5, hoist0 is accessible for use, hoist1 is available for work and is located at depot1, hoist2 is available for use, hoist3 is available for work, hoist4 is accessible for use and is located at distributor1, hoist5 is available for work, pallet0 has crate2 placed on it, pallet1 has no crates on it and is located at depot1, pallet2 is situated at depot2, pallet3 has no crates on it, pallet4 is located at distributor1, truck0 is situated at depot2 and truck1 is at distributor2."}
{"question_id": "9d8d25f6-ccf3-42ce-b81d-d4f3a8c6d623", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_7", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from depot1 to depot0 to reach the current state. In this state, what are the valid properties of the state that involve negations for crate2? Write None if there are none", "answer": "crate0 is not on top of crate2, crate1 does not have crate2 on it, crate1 is not on top of crate2, crate2 cannot be found located at depot3, crate2 does not have crate3 on it, crate2 is not at depot1, crate2 is not at distributor1, crate2 is not inside truck0, crate2 is not inside truck1, crate2 is not located at distributor0, crate2 is not on crate0, crate2 is not on crate2, crate2 is not on pallet1, crate2 is not on pallet3, crate2 is not on pallet5, crate2 is not on top of crate3, depot2 is where crate2 is not located, distributor2 is where crate2 is not located, hoist0 is not lifting crate2, hoist1 is not elevating crate2, hoist2 is not elevating crate2, hoist3 is not elevating crate2, hoist4 is not raising crate2, hoist5 is not raising crate2, hoist6 is not lifting crate2, pallet2 does not have crate2 on it, pallet4 does not have crate2 on it, pallet6 does not have crate2 on it and truck2 does not contain crate2", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear of any crates, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on top of pallet0, crate3 is at distributor1, crate3 is clear of any crates, crate3 is on pallet5, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck0 is located, depot3 is where crate1 is located, depot3 is where hoist3 is located, distributor0 is where hoist4 is located, distributor0 is where pallet4 is located, distributor2 is where hoist6 is located, hoist0 can be found located at depot0, hoist0 is accessible, hoist1 is available for work, hoist2 is accessible, hoist2 is at depot2, hoist3 is available for work, hoist4 is available for work, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 is located at depot3, pallet4 is clear of any crates, pallet5 is at distributor1, pallet6 is located at distributor2, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: truck2 is driven from depot1 to depot0 to achieve the current state. In this state, what are the valid state properties involving negations related to crate2? If there are no such properties, write None.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 has no crates on it, crate1 is placed on top of pallet3, crate2 is located at depot0, crate2 has no crates on it, crate2 is positioned on top of pallet0, crate3 is situated at distributor1, crate3 has no crates on it, crate3 is positioned on pallet5, depot1 houses hoist1, depot1 houses pallet1, depot1 houses truck0, depot3 houses crate1, depot3 houses hoist3, distributor0 houses hoist4, distributor0 houses pallet4, distributor2 houses hoist6, hoist0 is situated at depot0, hoist0 is accessible, hoist1 is available for operation, hoist2 is accessible, hoist2 is situated at depot2, hoist3 is available for operation, hoist4 is available for operation, hoist5 is accessible, hoist5 is situated at distributor1, hoist6 is available, pallet0 is situated at depot0, pallet1 has no crates on it, pallet2 has no crates on it, pallet2 is situated at depot2, pallet3 is situated at depot3, pallet4 has no crates on it, pallet5 is situated at distributor1, pallet6 is situated at distributor2, truck1 is situated at distributor0 and truck2 is situated at depot1."}
{"question_id": "5d6dffe8-d07a-4c15-9b0f-f9e10d53d737", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: crate2 is lifted from pallet0 at depot0 by hoist0, crate2 is loaded by hoist0 into truck2 at depot0, truck2 is driven from depot0 to distributor1, crate1 is lifted from pallet2 at depot2 by hoist2, crate1 is loaded by hoist2 into truck0 at depot2, from depot2, truck0 is driven to distributor0, hoist3 unloads crate1 from truck0 at distributor0, hoist3 drops crate1 on pallet3 at distributor0, hoist4 lifts crate0 from pallet4 at distributor1 and at distributor1, hoist4 loads crate0 into truck2 to reach the current state. In this state, what are the valid properties of the state that involve negations for pallet4? Write None if there are none", "answer": "crate0 is not on pallet4, crate3 is not on pallet4, depot0 is where pallet4 is not located, pallet4 cannot be found located at distributor0, pallet4 does not have crate1 on it, pallet4 does not have crate2 on it, pallet4 is not at depot1, pallet4 is not located at depot2 and pallet4 is not located at distributor2", "plan_length": 10, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor1, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot0 is where pallet0 is located, depot0 is where truck2 is located, depot2 is where hoist2 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where crate3 is located, distributor2 is where hoist5 is located, distributor2 is where pallet5 is located, hoist0 is accessible, hoist1 is available for work, hoist1 is located at depot1, hoist2 is available, hoist3 is available for work, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is available for work, pallet0 has crate2 on it, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is at depot2, pallet3 is clear of any crates, pallet4 is located at distributor1, truck0 is located at depot2 and truck1 is at distributor2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2 at depot0, then truck2 is driven from depot0 to distributor1. Meanwhile, hoist2 lifts crate1 from pallet2 at depot2 and loads it into truck0 at depot2, after which truck0 is driven from depot2 to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 at distributor0 and places it on pallet3 at distributor0. Additionally, hoist4 lifts crate0 from pallet4 at distributor1 and loads it into truck2 at distributor1, resulting in the current state. In this state, what are the valid properties of the state that involve negations for pallet4? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor1, crate0 is positioned on top of pallet4, crate1 is situated at depot2, crate1 has no crates on it, crate1 is placed on pallet2, crate2 has no crates on it, crate3 has no crates on it, crate3 is positioned on top of pallet5, depot0 is the location of crate2, depot0 is the location of hoist0, depot0 is the location of pallet0, depot0 is the location of truck2, depot2 is the location of hoist2, distributor0 is the location of hoist3, distributor0 is the location of pallet3, distributor2 is the location of crate3, distributor2 is the location of hoist5, distributor2 is the location of pallet5, hoist0 is accessible for use, hoist1 is available for work and is located at depot1, hoist2 is available for use, hoist3 is available for work, hoist4 is accessible for use and is located at distributor1, hoist5 is available for work, pallet0 has crate2 placed on it, pallet1 has no crates on it and is located at depot1, pallet2 is situated at depot2, pallet3 has no crates on it, pallet4 is located at distributor1, truck0 is situated at depot2 and truck1 is situated at distributor2."}
{"question_id": "95fdbb8e-ddaf-486a-8411-babd5e2d65ff", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0 to reach the current state. In this state, what are the valid properties of the state that involve negations for distributor2? Write None if there are none", "answer": "crate0 is not at distributor2, crate1 is not located at distributor2, crate2 cannot be found located at distributor2, distributor2 is where hoist2 is not located, distributor2 is where pallet2 is not located, distributor2 is where truck0 is not located, hoist0 is not at distributor2, hoist1 is not at distributor2, hoist3 is not located at distributor2, hoist4 is not at distributor2, pallet0 is not located at distributor2, pallet1 is not at distributor2, pallet3 is not at distributor2, pallet4 is not at distributor2 and truck2 is not located at distributor2", "plan_length": 1, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor1, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot0 is where pallet0 is located, depot0 is where truck2 is located, depot2 is where hoist2 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where crate3 is located, distributor2 is where hoist5 is located, distributor2 is where pallet5 is located, hoist0 is accessible, hoist1 is available for work, hoist1 is located at depot1, hoist2 is available, hoist3 is available for work, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is available for work, pallet0 has crate2 on it, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is at depot2, pallet3 is clear of any crates, pallet4 is located at distributor1, truck0 is located at depot2 and truck1 is at distributor2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: hoist0 lifts crate2 from pallet0 at depot0 to achieve the current state. In this state, what are the valid properties that involve negations for distributor2, or state None if there are no such properties.", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor1, crate0 is positioned on top of pallet4, crate1 is situated at depot2, crate1 has no crates on it, crate1 is placed on pallet2, crate2 has no crates on it, crate3 has no crates on it, crate3 is positioned on top of pallet5, depot0 is the location of crate2, depot0 is the location of hoist0, depot0 is the location of pallet0, depot0 is the location of truck2, depot2 is the location of hoist2, distributor0 is the location of hoist3, distributor0 is the location of pallet3, distributor2 is the location of crate3, distributor2 is the location of hoist5, distributor2 is the location of pallet5, hoist0 is accessible for use, hoist1 is available for work and is located at depot1, hoist2 is available for use, hoist3 is available for work, hoist4 is accessible for use and is located at distributor1, hoist5 is available for work, pallet0 has crate2 placed on it, pallet1 has no crates on it and is located at depot1, pallet2 is situated at depot2, pallet3 has no crates on it, pallet4 is located at distributor1, truck0 is situated at depot2 and truck1 is situated at distributor2."}
{"question_id": "5b035407-620b-4627-b19b-8277f33f5f7d", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_8", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck2 is driven to depot0, at depot0, hoist0 lifts crate2 off pallet0, at depot0, hoist0 loads crate2 into truck2, truck2 is driven to distributor1 from depot0, crate1 is lifted from pallet3 at depot3 by hoist3, at distributor1, hoist5 lifts crate3 off pallet5, at distributor1, hoist5 loads crate3 into truck2, crate2 is unloaded by hoist5 from truck2 at distributor1, truck2 is driven from distributor1 to distributor2, at distributor1, hoist5 drops crate2 on pallet5, at distributor2, hoist6 lifts crate0 off pallet6, hoist6 loads crate0 into truck2 at distributor2, truck2 is driven to depot3 from distributor2, crate1 is loaded by hoist3 into truck2 at depot3, hoist3 unloads crate0 from truck2 at depot3, from depot3, truck2 is driven to distributor0, hoist4 unloads crate3 from truck2 at distributor0, at depot3, hoist3 drops crate0 on pallet3 and at distributor0, hoist4 drops crate3 on pallet4 to reach the current state. In this state, what are the valid properties of the state that involve negations for hoist6? Write None if there are none", "answer": "distributor1 is where hoist6 is not located, hoist6 cannot be found located at depot0, hoist6 cannot be found located at depot1, hoist6 cannot be found located at depot2, hoist6 cannot be found located at distributor0, hoist6 is not elevating crate2, hoist6 is not located at depot3, hoist6 is not raising crate0, hoist6 is not raising crate1 and hoist6 is not raising crate3", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear of any crates, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on top of pallet0, crate3 is at distributor1, crate3 is clear of any crates, crate3 is on pallet5, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck0 is located, depot3 is where crate1 is located, depot3 is where hoist3 is located, distributor0 is where hoist4 is located, distributor0 is where pallet4 is located, distributor2 is where hoist6 is located, hoist0 can be found located at depot0, hoist0 is accessible, hoist1 is available for work, hoist2 is accessible, hoist2 is at depot2, hoist3 is available for work, hoist4 is available for work, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 is located at depot3, pallet4 is clear of any crates, pallet5 is at distributor1, pallet6 is located at distributor2, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 is driven from depot1 to depot0, where hoist0 unloads crate2 from pallet0 and then loads it into truck2, which is then driven to distributor1. At distributor1, hoist5 unloads crate3 from pallet5 and loads it into truck2, while also unloading crate2 from truck2. Truck2 then proceeds to distributor2, where hoist5 places crate2 on pallet5. At distributor2, hoist6 unloads crate0 from pallet6 and loads it into truck2, which is then driven to depot3. At depot3, hoist3 loads crate1 into truck2 and unloads crate0 from truck2. Truck2 then heads to distributor0, where hoist4 unloads crate3 from truck2. Finally, hoist3 places crate0 on pallet3 at depot3, and hoist4 places crate3 on pallet4 at distributor0, resulting in the current state. In this state, what are the valid properties that involve negations for hoist6? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 has no crates on top, crate1 is placed on top of pallet3, crate2 is located at depot0, crate2 has no crates on top, crate2 is positioned on top of pallet0, crate3 is situated at distributor1, crate3 has no crates on top, crate3 is positioned on pallet5, depot1 houses hoist1, depot1 houses pallet1, depot1 houses truck0, depot3 houses crate1, depot3 houses hoist3, distributor0 houses hoist4, distributor0 houses pallet4, distributor2 houses hoist6, hoist0 is situated at depot0, hoist0 is accessible, hoist1 is available for use, hoist2 is accessible, hoist2 is situated at depot2, hoist3 is available for use, hoist4 is available for use, hoist5 is accessible, hoist5 is situated at distributor1, hoist6 is available, pallet0 is situated at depot0, pallet1 has no crates on it, pallet2 has no crates on it, pallet2 is situated at depot2, pallet3 is situated at depot3, pallet4 has no crates on it, pallet5 is situated at distributor1, pallet6 is situated at distributor2, truck1 is situated at distributor0 and truck2 is situated at depot1."}
{"question_id": "f7af5100-4d10-4806-898f-f647bac366a7", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot0 from depot1, at depot0, hoist0 lifts crate2 off pallet0, at depot0, hoist0 loads crate2 into truck2, truck2 is driven from depot0 to distributor1, hoist3 lifts crate1 from pallet3 at depot3, hoist5 lifts crate3 from pallet5 at distributor1, at distributor1, hoist5 loads crate3 into truck2, crate2 is unloaded by hoist5 from truck2 at distributor1, from distributor1, truck2 is driven to distributor2, crate2 is dropped on pallet5 at distributor1 by hoist5, crate0 is lifted from pallet6 at distributor2 by hoist6, hoist6 loads crate0 into truck2 at distributor2, truck2 is driven from distributor2 to depot3, crate1 is loaded by hoist3 into truck2 at depot3, hoist3 unloads crate0 from truck2 at depot3, truck2 is driven to distributor0 from depot3, crate3 is unloaded by hoist4 from truck2 at distributor0, crate0 is dropped on pallet3 at depot3 by hoist3 and hoist4 drops crate3 on pallet4 at distributor0 to reach the current state. In this state, what are the valid properties of the state that involve negations for depot2? Write None if there are none", "answer": "crate1 cannot be found located at depot2, crate2 is not located at depot2, depot2 is where crate0 is not located, depot2 is where crate3 is not located, depot2 is where hoist4 is not located, depot2 is where pallet0 is not located, depot2 is where pallet3 is not located, depot2 is where pallet4 is not located, depot2 is where truck1 is not located, depot2 is where truck2 is not located, hoist0 is not at depot2, hoist1 is not located at depot2, hoist3 is not located at depot2, hoist5 cannot be found located at depot2, hoist6 is not located at depot2, pallet1 is not located at depot2, pallet5 cannot be found located at depot2, pallet6 cannot be found located at depot2 and truck0 cannot be found located at depot2", "plan_length": 19, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear of any crates, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on top of pallet0, crate3 is at distributor1, crate3 is clear of any crates, crate3 is on pallet5, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck0 is located, depot3 is where crate1 is located, depot3 is where hoist3 is located, distributor0 is where hoist4 is located, distributor0 is where pallet4 is located, distributor2 is where hoist6 is located, hoist0 can be found located at depot0, hoist0 is accessible, hoist1 is available for work, hoist2 is accessible, hoist2 is at depot2, hoist3 is available for work, hoist4 is available for work, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 is located at depot3, pallet4 is clear of any crates, pallet5 is at distributor1, pallet6 is located at distributor2, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck2 travels from depot1 to depot0, where hoist0 removes crate2 from pallet0 and then loads it into truck2; truck2 then proceeds from depot0 to distributor1. Meanwhile, hoist3 lifts crate1 from pallet3 at depot3, and hoist5 lifts crate3 from pallet5 at distributor1. At distributor1, hoist5 loads crate3 into truck2, and crate2 is unloaded from truck2 by hoist5. Truck2 then drives from distributor1 to distributor2. At distributor1, hoist5 places crate2 on pallet5. At distributor2, hoist6 lifts crate0 from pallet6 and loads it into truck2. Truck2 then travels from distributor2 to depot3, where crate1 is loaded into truck2 by hoist3, and crate0 is unloaded from truck2 by hoist3. From depot3, truck2 drives to distributor0, where crate3 is unloaded from truck2 by hoist4. Finally, hoist3 places crate0 on pallet3 at depot3, and hoist4 places crate3 on pallet4 at distributor0, resulting in the current state. In this state, what are the valid properties that involve negations for depot2? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 has no crates on top, crate1 is placed on top of pallet3, crate2 is located at depot0, crate2 has no crates on top, crate2 is positioned on top of pallet0, crate3 is situated at distributor1, crate3 has no crates on top, crate3 is positioned on pallet5, depot1 houses hoist1, depot1 houses pallet1, depot1 houses truck0, depot3 houses crate1, depot3 houses hoist3, distributor0 houses hoist4, distributor0 houses pallet4, distributor2 houses hoist6, hoist0 is situated at depot0, hoist0 is operational, hoist1 is ready for work, hoist2 is operational, hoist2 is situated at depot2, hoist3 is ready for work, hoist4 is ready for work, hoist5 is operational, hoist5 is situated at distributor1, hoist6 is available, pallet0 is situated at depot0, pallet1 has no crates on it, pallet2 has no crates on it, pallet2 is situated at depot2, pallet3 is situated at depot3, pallet4 has no crates on it, pallet5 is situated at distributor1, pallet6 is situated at distributor2, truck1 is situated at distributor0 and truck2 is situated at depot1."}
{"question_id": "d84d9198-2c88-40cc-8f9b-fd1dcd3c7cf1", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_9", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck2 is driven to depot0 to reach the current state. In this state, what are the valid properties of the state that involve negations for pallet0? Write None if there are none", "answer": "crate0 is not on top of pallet0, crate1 is not on pallet0, depot3 is where pallet0 is not located, pallet0 cannot be found located at distributor1, pallet0 cannot be found located at distributor2, pallet0 does not have crate3 on it, pallet0 is not at depot2, pallet0 is not at distributor0, pallet0 is not clear and pallet0 is not located at depot1", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear of any crates, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on top of pallet0, crate3 is at distributor1, crate3 is clear of any crates, crate3 is on pallet5, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck0 is located, depot3 is where crate1 is located, depot3 is where hoist3 is located, distributor0 is where hoist4 is located, distributor0 is where pallet4 is located, distributor2 is where hoist6 is located, hoist0 can be found located at depot0, hoist0 is accessible, hoist1 is available for work, hoist2 is accessible, hoist2 is at depot2, hoist3 is available for work, hoist4 is available for work, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 is located at depot3, pallet4 is clear of any crates, pallet5 is at distributor1, pallet6 is located at distributor2, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: truck2 is driven from depot1 to depot0 to achieve the current state. In this state, what are the valid state properties involving negations related to pallet0? If there are no such properties, write None.", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 is empty, crate0 is positioned on pallet6, crate1 has no crates on top, crate1 is placed on top of pallet3, crate2 is located at depot0, crate2 has no crates on top, crate2 is positioned on top of pallet0, crate3 is situated at distributor1, crate3 has no crates on top, crate3 is positioned on pallet5, depot1 houses hoist1, depot1 houses pallet1, depot1 houses truck0, depot3 houses crate1, depot3 houses hoist3, distributor0 houses hoist4, distributor0 houses pallet4, distributor2 houses hoist6, hoist0 is situated at depot0, hoist0 is accessible, hoist1 is available for operation, hoist2 is accessible, hoist2 is situated at depot2, hoist3 is available for operation, hoist4 is available for operation, hoist5 is accessible, hoist5 is situated at distributor1, hoist6 is available, pallet0 is situated at depot0, pallet1 has no crates on it, pallet2 has no crates on it, pallet2 is situated at depot2, pallet3 is situated at depot3, pallet4 has no crates on it, pallet5 is situated at distributor1, pallet6 is situated at distributor2, truck1 is situated at distributor0 and truck2 is situated at depot1."}
{"question_id": "33a54323-54f5-47e9-b8ce-cc0134878218", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_11", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: crate2 is lifted from pallet0 at depot0 by hoist0, at depot0, hoist0 loads crate2 into truck2, truck2 is driven to distributor1 from depot0, at depot2, hoist2 lifts crate1 off pallet2, crate1 is loaded by hoist2 into truck0 at depot2, truck0 is driven to distributor0 from depot2, at distributor0, hoist3 unloads crate1 from truck0, crate1 is dropped on pallet3 at distributor0 by hoist3, crate0 is lifted from pallet4 at distributor1 by hoist4 and at distributor1, hoist4 loads crate0 into truck2 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for truck1? Write None if there are none", "answer": "crate0 is not in truck1, crate2 is not in truck1, crate3 is not inside truck1, depot1 is where truck1 is not located, truck1 can be found located at distributor2, truck1 cannot be found located at depot0, truck1 cannot be found located at distributor0, truck1 does not contain crate1, truck1 is not at distributor1 and truck1 is not located at depot2", "plan_length": 10, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor1, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot0 is where pallet0 is located, depot0 is where truck2 is located, depot2 is where hoist2 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where crate3 is located, distributor2 is where hoist5 is located, distributor2 is where pallet5 is located, hoist0 is accessible, hoist1 is available for work, hoist1 is located at depot1, hoist2 is available, hoist3 is available for work, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is available for work, pallet0 has crate2 on it, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is at depot2, pallet3 is clear of any crates, pallet4 is located at distributor1, truck0 is located at depot2 and truck1 is at distributor2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: hoist0 lifts crate2 from pallet0 at depot0 and then loads it into truck2 at the same location, after which truck2 is driven to distributor1 from depot0. Meanwhile, at depot2, hoist2 lifts crate1 off pallet2 and loads it into truck0, which is then driven to distributor0. Upon arrival, hoist3 unloads crate1 from truck0 at distributor0 and places it on pallet3. Additionally, hoist4 lifts crate0 from pallet4 at distributor1 and loads it into truck2, resulting in the current state. In this state, what are the valid properties of the state (both with and without negations) for truck1? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor1, crate0 is positioned on top of pallet4, crate1 is situated at depot2, crate1 has no crates on it, crate1 is placed on pallet2, crate2 has no crates on it, crate3 has no crates on it, crate3 is positioned on top of pallet5, depot0 is the location of crate2, depot0 is the location of hoist0, depot0 is the location of pallet0, depot0 is the location of truck2, depot2 is the location of hoist2, distributor0 is the location of hoist3, distributor0 is the location of pallet3, distributor2 is the location of crate3, distributor2 is the location of hoist5, distributor2 is the location of pallet5, hoist0 is accessible for use, hoist1 is available for work and is located at depot1, hoist2 is available for use, hoist3 is available for work, hoist4 is accessible for use and is located at distributor1, hoist5 is available for work, pallet0 has crate2 placed on it, pallet1 has no crates on it and is located at depot1, pallet2 is situated at depot2, pallet3 has no crates on it, pallet4 is located at distributor1, truck0 is situated at depot2 and truck1 is situated at distributor2."}
{"question_id": "da72ed23-5d7a-460a-ad46-16084663ffd8", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_8", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven from depot1 to depot0, at depot0, hoist0 lifts crate2 off pallet0, hoist0 loads crate2 into truck1 at depot0, truck1 is driven to depot1 from depot0, crate2 is unloaded by hoist1 from truck1 at depot1, crate2 is dropped on pallet1 at depot1 by hoist1, at depot2, hoist2 lifts crate3 off pallet2, crate3 is loaded by hoist2 into truck2 at depot2, truck2 is driven from depot2 to distributor3 and hoist6 unloads crate3 from truck2 at distributor3 to reach the current state. In this state, what are the valid properties of the state that involve negations for crate2? Write None if there are none", "answer": "crate0 does not have crate2 on it, crate0 is not on crate2, crate1 is not on crate2, crate2 cannot be found located at depot0, crate2 does not have crate3 on it, crate2 is not at depot2, crate2 is not at distributor1, crate2 is not inside truck1, crate2 is not located at distributor2, crate2 is not on crate2, crate2 is not on pallet4, crate2 is not on top of crate1, crate2 is not on top of pallet2, crate2 is not on top of pallet6, crate3 does not have crate2 on it, distributor0 is where crate2 is not located, distributor3 is where crate2 is not located, hoist0 is not elevating crate2, hoist1 is not elevating crate2, hoist2 is not elevating crate2, hoist3 is not raising crate2, hoist4 is not raising crate2, hoist5 is not raising crate2, hoist6 is not raising crate2, pallet0 does not have crate2 on it, pallet3 does not have crate2 on it, pallet5 does not have crate2 on it, truck0 does not contain crate2 and truck2 does not contain crate2", "plan_length": 10, "initial_state_nl": "Crate0 has crate1 on it, crate0 is located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate2 is clear of any crates, crate2 is located at depot0, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where hoist5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is accessible, hoist6 is available for work, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 is clear, pallet4 is clear, pallet4 is located at distributor1, pallet5 is at distributor2, pallet6 is clear of any crates, pallet6 is located at distributor3 and truck2 is at depot2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 travels from depot1 to depot0, where hoist0 removes crate2 from pallet0, loads it into truck1, and then truck1 heads back to depot1. Upon arrival, hoist1 unloads crate2 from truck1 and places it on pallet1 at depot1. Meanwhile, at depot2, hoist2 lifts crate3 off pallet2, loads it into truck2, and truck2 is driven to distributor3, where hoist6 unloads crate3 from truck2, resulting in the current state. In this state, what are the valid properties that involve negations for crate2? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 has crate1 stacked on top of it, and it is situated at distributor2. Additionally, crate0 is placed on pallet5. Crate1, on the other hand, has no crates on top of it and is also located at distributor2. Similarly, crate2 is clear of any crates and is situated at depot0. Crate3 can be found at depot2, has no crates on top of it, and is placed on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is located. Distributor0 houses both hoist3 and pallet3, and distributor2 is home to both hoist5 and truck0. Distributor3 is where hoist6 is located. \n\nHoist0 is available for work, and hoist1 is available and situated at depot1. Hoist2 is also available and located at depot2. Hoist3 is available for work, while hoist4 is available and located at distributor1. Hoist5 is accessible, and hoist6 is available for work.\n\nPallet0 has crate2 on it and is situated at depot0. Pallet1 is empty and located at depot1. Pallet2 is situated at depot2, and pallet3 is empty. Pallet4 is also empty and located at distributor1. Pallet5 is at distributor2, and pallet6 is empty and located at distributor3. Lastly, truck2 is situated at depot2."}
{"question_id": "96a17ba5-4cf1-45cb-8bde-28c323497390", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is driven from depot1 to depot0, crate2 is lifted from pallet0 at depot0 by hoist0, hoist0 loads crate2 into truck1 at depot0, from depot0, truck1 is driven to depot1, hoist1 unloads crate2 from truck1 at depot1, crate2 is dropped on pallet1 at depot1 by hoist1, crate3 is lifted from pallet2 at depot2 by hoist2, crate3 is loaded by hoist2 into truck2 at depot2, from depot2, truck2 is driven to distributor3 and at distributor3, hoist6 unloads crate3 from truck2 to reach the current state. In this state, what are the valid properties of the state that involve negations for depot0? Write None if there are none", "answer": "crate0 cannot be found located at depot0, crate1 is not at depot0, crate2 cannot be found located at depot0, crate3 is not at depot0, depot0 is where hoist1 is not located, hoist2 cannot be found located at depot0, hoist3 is not at depot0, hoist4 is not at depot0, hoist5 is not located at depot0, hoist6 is not at depot0, pallet1 cannot be found located at depot0, pallet2 is not located at depot0, pallet3 is not located at depot0, pallet4 is not at depot0, pallet5 is not at depot0, pallet6 cannot be found located at depot0, truck0 cannot be found located at depot0, truck1 is not at depot0 and truck2 is not located at depot0", "plan_length": 10, "initial_state_nl": "Crate0 has crate1 on it, crate0 is located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate2 is clear of any crates, crate2 is located at depot0, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where hoist5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is accessible, hoist6 is available for work, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 is clear, pallet4 is clear, pallet4 is located at distributor1, pallet5 is at distributor2, pallet6 is clear of any crates, pallet6 is located at distributor3 and truck2 is at depot2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 travels from depot1 to depot0, hoist0 lifts crate2 from pallet0 at depot0, hoist0 then loads crate2 into truck1 at depot0, truck1 is then driven from depot0 to depot1, hoist1 unloads crate2 from truck1 at depot1, and crate2 is placed on pallet1 at depot1 by hoist1. Meanwhile, crate3 is lifted from pallet2 at depot2 by hoist2, loaded into truck2 by hoist2 at depot2, truck2 is driven from depot2 to distributor3, and finally, hoist6 unloads crate3 from truck2 at distributor3, resulting in the current state. In this state, what are the valid properties that involve negations for depot0? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is stacked on top of crate1, and crate0 is situated at distributor2. Additionally, crate0 is placed on pallet5. Crate1, on the other hand, has no crates on it, and it is also located at distributor2. Similarly, crate2 is clear of any crates and is situated at depot0. Crate3 can be found at depot2, has no crates on it, and is placed on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is located. Distributor0 houses both hoist3 and pallet3, and distributor2 is home to both hoist5 and truck0. Distributor3 is where hoist6 is located. Hoist0 is available for work, and hoist1, which is located at depot1, is also available. Hoist2 is available and situated at depot2, while hoist3 is available for work. Hoist4 is located at distributor1 and is available, and hoist5 is accessible. Hoist6 is available for work.\n\nPallet0 has crate2 on it and is situated at depot0. Pallet1 is empty and located at depot1. Pallet2 is at depot2, and pallet3 is empty. Pallet4 is also empty and located at distributor1. Pallet5 is at distributor2, and pallet6 is empty and situated at distributor3. Lastly, truck2 is located at depot2."}
{"question_id": "2c67cd90-5e7b-4d3f-ae57-faae69883a1e", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: crate2 is lifted from pallet0 at depot0 by hoist0 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for pallet4? Write None if there are none", "answer": "crate0 is on top of pallet4 and distributor1 is where pallet4 is located", "plan_length": 1, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor1, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot0 is where pallet0 is located, depot0 is where truck2 is located, depot2 is where hoist2 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where crate3 is located, distributor2 is where hoist5 is located, distributor2 is where pallet5 is located, hoist0 is accessible, hoist1 is available for work, hoist1 is located at depot1, hoist2 is available, hoist3 is available for work, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is available for work, pallet0 has crate2 on it, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is at depot2, pallet3 is clear of any crates, pallet4 is located at distributor1, truck0 is located at depot2 and truck1 is at distributor2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: crate2 is lifted from pallet0 at depot0 by hoist0 to achieve the current state. In this state, what are the valid non-negated properties of pallet4, or state None if none exist.", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor1, crate0 is positioned on top of pallet4, crate1 is situated at depot2, crate1 has no crates on it, crate1 is placed on pallet2, crate2 has no crates on it, crate3 has no crates on it, crate3 is positioned on top of pallet5, depot0 is the location of crate2, depot0 is the location of hoist0, depot0 is the location of pallet0, depot0 is the location of truck2, depot2 is the location of hoist2, distributor0 is the location of hoist3, distributor0 is the location of pallet3, distributor2 is the location of crate3, distributor2 is the location of hoist5, distributor2 is the location of pallet5, hoist0 is accessible for use, hoist1 is available for work and is located at depot1, hoist2 is available for use, hoist3 is available for work, hoist4 is accessible for use and is located at distributor1, hoist5 is available for work, pallet0 has crate2 placed on it, pallet1 has no crates on it and is located at depot1, pallet2 is situated at depot2, pallet3 has no crates on it, pallet4 is located at distributor1, truck0 is situated at depot2 and truck1 is situated at distributor2."}
{"question_id": "22bb3c44-7f1f-4873-8df3-aeefedc5342d", "domain_name": "depots", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck2 is driven to depot0 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for depot1? Write None if there are none", "answer": "depot1 is where hoist1 is located, pallet1 is at depot1 and truck0 is at depot1", "plan_length": 1, "initial_state_nl": "Crate0 can be found located at distributor2, crate0 is clear, crate0 is on pallet6, crate1 is clear of any crates, crate1 is on top of pallet3, crate2 can be found located at depot0, crate2 is clear of any crates, crate2 is on top of pallet0, crate3 is at distributor1, crate3 is clear of any crates, crate3 is on pallet5, depot1 is where hoist1 is located, depot1 is where pallet1 is located, depot1 is where truck0 is located, depot3 is where crate1 is located, depot3 is where hoist3 is located, distributor0 is where hoist4 is located, distributor0 is where pallet4 is located, distributor2 is where hoist6 is located, hoist0 can be found located at depot0, hoist0 is accessible, hoist1 is available for work, hoist2 is accessible, hoist2 is at depot2, hoist3 is available for work, hoist4 is available for work, hoist5 is accessible, hoist5 is located at distributor1, hoist6 is available, pallet0 is located at depot0, pallet1 is clear of any crates, pallet2 is clear of any crates, pallet2 is located at depot2, pallet3 is located at depot3, pallet4 is clear of any crates, pallet5 is at distributor1, pallet6 is located at distributor2, truck1 is at distributor0 and truck2 is located at depot1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: truck2 is driven from depot1 to depot0 to achieve the current state. In this state, what are the valid properties of the state that do not involve negations for depot1? Write None if there are none", "initial_state_nl_paraphrased": "Crate0 is situated at distributor2, crate0 has no obstructions, crate0 is positioned on pallet6, crate1 has no crates on top of it, crate1 is placed on top of pallet3, crate2 is located at depot0, crate2 is clear, crate2 is positioned on top of pallet0, crate3 is situated at distributor1, crate3 has no obstructions, crate3 is placed on pallet5, depot1 houses hoist1, depot1 houses pallet1, depot1 houses truck0, depot3 houses crate1, depot3 houses hoist3, distributor0 houses hoist4, distributor0 houses pallet4, distributor2 houses hoist6, hoist0 is situated at depot0, hoist0 is accessible, hoist1 is available for operation, hoist2 is accessible, hoist2 is situated at depot2, hoist3 is available for operation, hoist4 is available for operation, hoist5 is accessible, hoist5 is situated at distributor1, hoist6 is available, pallet0 is situated at depot0, pallet1 has no crates on it, pallet2 has no crates on it, pallet2 is situated at depot2, pallet3 is situated at depot3, pallet4 has no crates on it, pallet5 is situated at distributor1, pallet6 is situated at distributor2, truck1 is situated at distributor0 and truck2 is situated at depot1."}
{"question_id": "05e80040-eb0f-4d11-9004-2c59a7f96c4c", "domain_name": "depots", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from depot1, truck1 is driven to depot0, crate2 is lifted from pallet0 at depot0 by hoist0, hoist0 loads crate2 into truck1 at depot0, from depot0, truck1 is driven to depot1, at depot1, hoist1 unloads crate2 from truck1, hoist1 drops crate2 on pallet1 at depot1, crate3 is lifted from pallet2 at depot2 by hoist2, hoist2 loads crate3 into truck2 at depot2, truck2 is driven from depot2 to distributor3, crate3 is unloaded by hoist6 from truck2 at distributor3, crate3 is dropped on pallet6 at distributor3 by hoist6, crate1 is lifted from crate0 at distributor2 by hoist5, crate1 is loaded by hoist5 into truck0 at distributor2, at distributor2, hoist5 lifts crate0 off pallet5, crate0 is loaded by hoist5 into truck0 at distributor2, at distributor2, hoist5 unloads crate1 from truck0, from distributor2, truck0 is driven to distributor0, hoist3 unloads crate0 from truck0 at distributor0 and at distributor0, hoist3 drops crate0 on pallet3 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for distributor3? Write None if there are none", "answer": "distributor3 is where crate3 is located, distributor3 is where hoist6 is located, pallet6 can be found located at distributor3 and truck2 is located at distributor3", "plan_length": 19, "initial_state_nl": "Crate0 has crate1 on it, crate0 is located at distributor2, crate0 is on pallet5, crate1 is clear of any crates, crate1 is located at distributor2, crate2 is clear of any crates, crate2 is located at depot0, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on pallet2, depot0 is where hoist0 is located, depot1 is where truck1 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where hoist5 is located, distributor2 is where truck0 is located, distributor3 is where hoist6 is located, hoist0 is available for work, hoist1 is available, hoist1 is located at depot1, hoist2 is available, hoist2 is located at depot2, hoist3 is available for work, hoist4 can be found located at distributor1, hoist4 is available, hoist5 is accessible, hoist6 is available for work, pallet0 has crate2 on it, pallet0 is at depot0, pallet1 is clear, pallet1 is located at depot1, pallet2 is located at depot2, pallet3 is clear, pallet4 is clear, pallet4 is located at distributor1, pallet5 is at distributor2, pallet6 is clear of any crates, pallet6 is located at distributor3 and truck2 is at depot2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck1 is driven from depot1 to depot0, where hoist0 lifts crate2 from pallet0 and loads it into truck1; truck1 then proceeds from depot0 to depot1, where hoist1 unloads crate2 and places it on pallet1. Meanwhile, crate3 is lifted from pallet2 at depot2 by hoist2 and loaded into truck2, which is then driven to distributor3; at distributor3, hoist6 unloads crate3 from truck2 and drops it on pallet6. Additionally, hoist5 lifts crate1 from crate0 at distributor2 and loads it into truck0, then lifts crate0 off pallet5 and loads it into truck0 as well. However, hoist5 then unloads crate1 from truck0 at distributor2. Subsequently, truck0 is driven from distributor2 to distributor0, where hoist3 unloads crate0 and places it on pallet3, resulting in the current state. In this state, what are the valid properties that do not involve negations for distributor3? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 has crate1 stacked on top of it, and it is situated at distributor2. Additionally, crate0 is placed on pallet5. Crate1, on the other hand, has no crates on top of it and is also located at distributor2. Similarly, crate2 is clear of any crates and is situated at depot0. Crate3 can be found at depot2, has no crates on top of it, and is placed on pallet2. \n\nDepot0 is the location of hoist0, while depot1 is where truck1 is located. Distributor0 houses both hoist3 and pallet3, and distributor2 is home to both hoist5 and truck0. Distributor3 is where hoist6 is located. Hoist0 is available for work, and hoist1, which is located at depot1, is also available. Hoist2, situated at depot2, is available as well. Hoist3 is available for work, and hoist4, located at distributor1, is available too. Hoist5 is accessible, and hoist6 is available for work.\n\nPallet0, which is at depot0, has crate2 on it. Pallet1, located at depot1, is empty, and pallet2 is situated at depot2. Pallet3, located at distributor0, is clear of crates, and pallet4, which is at distributor1, is also empty. Pallet5 is at distributor2, and pallet6, located at distributor3, is clear of any crates. Lastly, truck2 is situated at depot2."}
{"question_id": "8445fc22-ecda-4e33-8abd-980ba0db2817", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_9", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: hoist0 lifts crate2 from pallet0 at depot0 to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for crate0? Write None if there are none", "answer": "crate0 cannot be found located at depot0, crate0 cannot be found located at distributor0, crate0 cannot be found located at distributor2, crate0 is clear of any crates, crate0 is not in truck0, crate0 is not in truck1, crate0 is not inside truck2, crate0 is not located at depot1, crate0 is not located at depot2, crate0 is not on crate0, crate0 is not on pallet1, crate0 is not on pallet5, crate0 is not on top of crate3, crate0 is not on top of pallet0, crate0 is not on top of pallet2, crate0 is not on top of pallet3, crate0 is on top of pallet4, crate1 does not have crate0 on it, crate1 is not on top of crate0, crate2 does not have crate0 on it, crate2 is not on top of crate0, crate3 is not on top of crate0, distributor1 is where crate0 is located, hoist0 is not raising crate0, hoist1 is not lifting crate0, hoist2 is not elevating crate0, hoist3 is not lifting crate0, hoist4 is not raising crate0 and hoist5 is not lifting crate0", "plan_length": 1, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor1, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot0 is where pallet0 is located, depot0 is where truck2 is located, depot2 is where hoist2 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where crate3 is located, distributor2 is where hoist5 is located, distributor2 is where pallet5 is located, hoist0 is accessible, hoist1 is available for work, hoist1 is located at depot1, hoist2 is available, hoist3 is available for work, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is available for work, pallet0 has crate2 on it, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is at depot2, pallet3 is clear of any crates, pallet4 is located at distributor1, truck0 is located at depot2 and truck1 is at distributor2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: hoist0 lifts crate2 from pallet0 at depot0 to achieve the current state. In this state, what are the valid properties (including both affirmative and negated properties) that apply to crate0? If there are no such properties, state None.", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor1, crate0 is positioned on top of pallet4, crate1 is situated at depot2, crate1 has no crates on it, crate1 is placed on pallet2, crate2 has no crates on it, crate3 has no crates on it, crate3 is positioned on top of pallet5, depot0 is the location of crate2, depot0 is the location of hoist0, depot0 is the location of pallet0, depot0 is the location of truck2, depot2 is the location of hoist2, distributor0 is the location of hoist3, distributor0 is the location of pallet3, distributor2 is the location of crate3, distributor2 is the location of hoist5, distributor2 is the location of pallet5, hoist0 is accessible for use, hoist1 is available for work and is located at depot1, hoist2 is available for use, hoist3 is available for work, hoist4 is accessible for use and is located at distributor1, hoist5 is available for work, pallet0 has crate2 placed on it, pallet1 has no crates on it and is located at depot1, pallet2 is situated at depot2, pallet3 has no crates on it, pallet4 is located at distributor1, truck0 is situated at depot2 and truck1 is situated at distributor2."}
{"question_id": "b7d4b352-4fec-4799-ba2a-f2f253fbc5bf", "domain_name": "depots", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: crate2 is lifted from pallet0 at depot0 by hoist0, hoist0 loads crate2 into truck2 at depot0, from depot0, truck2 is driven to distributor1, hoist2 lifts crate1 from pallet2 at depot2, crate1 is loaded by hoist2 into truck0 at depot2, truck0 is driven from depot2 to distributor0, crate1 is unloaded by hoist3 from truck0 at distributor0, at distributor0, hoist3 drops crate1 on pallet3, hoist4 lifts crate0 from pallet4 at distributor1, at distributor1, hoist4 loads crate0 into truck2, at distributor2, hoist5 lifts crate3 off pallet5, at distributor2, hoist5 loads crate3 into truck1, from distributor2, truck1 is driven to distributor1, crate3 is unloaded by hoist4 from truck1 at distributor1, hoist4 drops crate3 on pallet4 at distributor1, at distributor1, hoist4 unloads crate2 from truck2, from distributor1, truck2 is driven to depot1, hoist1 unloads crate0 from truck2 at depot1 and crate0 is dropped on pallet1 at depot1 by hoist1 to reach the current state. In this state, what are the valid properties of the state that involve negations for crate1? Write None if there are none", "answer": "crate1 cannot be found located at distributor1, crate1 does not have crate0 on it, crate1 does not have crate1 on it, crate1 does not have crate2 on it, crate1 is not at depot0, crate1 is not inside truck0, crate1 is not located at depot2, crate1 is not on pallet4, crate1 is not on top of crate0, crate1 is not on top of crate2, crate1 is not on top of pallet0, crate1 is not on top of pallet1, crate3 does not have crate1 on it, crate3 is not on top of crate1, depot1 is where crate1 is not located, distributor2 is where crate1 is not located, hoist0 is not lifting crate1, hoist1 is not raising crate1, hoist2 is not raising crate1, hoist3 is not raising crate1, hoist4 is not lifting crate1, hoist5 is not elevating crate1, pallet2 does not have crate1 on it, pallet5 does not have crate1 on it, truck1 does not contain crate1 and truck2 does not contain crate1", "plan_length": 19, "initial_state_nl": "Crate0 is clear, crate0 is located at distributor1, crate0 is on top of pallet4, crate1 can be found located at depot2, crate1 is clear of any crates, crate1 is on pallet2, crate2 is clear of any crates, crate3 is clear of any crates, crate3 is on top of pallet5, depot0 is where crate2 is located, depot0 is where hoist0 is located, depot0 is where pallet0 is located, depot0 is where truck2 is located, depot2 is where hoist2 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, distributor2 is where crate3 is located, distributor2 is where hoist5 is located, distributor2 is where pallet5 is located, hoist0 is accessible, hoist1 is available for work, hoist1 is located at depot1, hoist2 is available, hoist3 is available for work, hoist4 is accessible, hoist4 is located at distributor1, hoist5 is available for work, pallet0 has crate2 on it, pallet1 is clear of any crates, pallet1 is located at depot1, pallet2 is at depot2, pallet3 is clear of any crates, pallet4 is located at distributor1, truck0 is located at depot2 and truck1 is at distributor2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: hoist0 lifts crate2 from pallet0 at depot0 and loads it into truck2, which then travels from depot0 to distributor1. Meanwhile, hoist2 lifts crate1 from pallet2 at depot2 and loads it into truck0, which is then driven to distributor0. At distributor0, hoist3 unloads crate1 from truck0 and places it on pallet3. At distributor1, hoist4 lifts crate0 from pallet4 and loads it into truck2. At distributor2, hoist5 lifts crate3 from pallet5 and loads it into truck1, which is then driven to distributor1. Upon arrival, hoist4 unloads crate3 from truck1 and places it on pallet4. Additionally, hoist4 unloads crate2 from truck2 at distributor1, and truck2 is then driven to depot1. Finally, hoist1 unloads crate0 from truck2 at depot1 and places it on pallet1. In this resulting state, what are the valid properties of the state that involve negations for crate1? Write None if there are none.", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is situated at distributor1, crate0 is positioned on top of pallet4, crate1 is situated at depot2, crate1 has no crates on it, crate1 is placed on pallet2, crate2 has no crates on it, crate3 has no crates on it, crate3 is positioned on top of pallet5, depot0 is the location of crate2, depot0 is the location of hoist0, depot0 is the location of pallet0, depot0 is the location of truck2, depot2 is the location of hoist2, distributor0 is the location of hoist3, distributor0 is the location of pallet3, distributor2 is the location of crate3, distributor2 is the location of hoist5, distributor2 is the location of pallet5, hoist0 is accessible for use, hoist1 is available for work and is located at depot1, hoist2 is available for use, hoist3 is available for work, hoist4 is accessible for use and is located at distributor1, hoist5 is available for work, pallet0 has crate2 placed on it, pallet1 has no crates on it and is located at depot1, pallet2 is situated at depot2, pallet3 has no crates on it, pallet4 is located at distributor1, truck0 is situated at depot2 and truck1 is situated at distributor2."}
{"question_id": "563a41ea-6638-4a8e-b6ee-c05d62c2db13", "domain_name": "depots", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven to depot2 from depot0 to reach the current state. In this state, what are the valid properties of the state that do not involve negations for pallet3? Write None if there are none", "answer": "crate0 is on pallet3 and pallet3 is located at distributor0", "plan_length": 1, "initial_state_nl": "Crate0 is clear, crate0 is on pallet3, crate1 is on pallet2, crate2 can be found located at depot2, crate2 is on top of crate1, crate3 can be found located at depot2, crate3 is clear of any crates, crate3 is on top of crate2, depot2 is where crate1 is located, depot2 is where pallet2 is located, distributor0 is where crate0 is located, distributor0 is where hoist3 is located, distributor0 is where pallet3 is located, hoist0 is available, hoist0 is located at depot0, hoist1 can be found located at depot1, hoist1 is available for work, hoist2 is at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 can be found located at distributor2, hoist5 is available, pallet0 is at depot0, pallet0 is clear, pallet1 can be found located at depot1, pallet1 is clear of any crates, pallet4 is at distributor1, pallet4 is clear of any crates, pallet5 is at distributor2, pallet5 is clear of any crates, truck0 can be found located at distributor0, truck1 is located at depot1 and truck2 is at depot0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: truck2 is driven from depot0 to depot2 to achieve the current state. In this state, what are the valid properties of the state that do not involve negations for pallet3? Write None if there are none", "initial_state_nl_paraphrased": "Crate0 is empty, crate0 is positioned on pallet3, crate1 is placed on pallet2, crate2 is situated at depot2, crate2 is stacked on top of crate1, crate3 is also located at depot2, crate3 has no crates on it, crate3 is positioned on top of crate2, depot2 is the location of crate1, depot2 is also the location of pallet2, distributor0 is the location of crate0, distributor0 is also the location of hoist3, distributor0 is the location of pallet3, hoist0 is available for use, hoist0 is situated at depot0, hoist1 is located at depot1, hoist1 is available for work, hoist2 is situated at depot2, hoist2 is available, hoist3 is available for work, hoist4 is available for work, hoist4 is located at distributor1, hoist5 is located at distributor2, hoist5 is available, pallet0 is situated at depot0, pallet0 is empty, pallet1 is located at depot1, pallet1 has no crates on it, pallet4 is situated at distributor1, pallet4 has no crates on it, pallet5 is situated at distributor2, pallet5 has no crates on it, truck0 is located at distributor0, truck1 is situated at depot1 and truck2 is situated at depot0."}
