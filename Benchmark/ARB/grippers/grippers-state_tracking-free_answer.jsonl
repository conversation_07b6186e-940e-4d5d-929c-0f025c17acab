{"question_id": "21598c92-dfe9-4eac-8ccc-e71b898f34fe", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "ball1 is not at room3, ball1 is not being carried by robot1's rgripper1, ball1 is not located at room2, ball1 is not located at room4, ball1 is not located at room5, ball1 is not present at room6, ball1 is present at room1, ball2 is at room2, ball2 is not at room3, ball2 is not at room4, ball2 is not at room5, ball2 is not being carried by robot1's lgripper1, ball2 is not located at room1, ball2 is not present at room6, ball3 is located at room1, ball3 is not at room2, ball3 is not at room6, ball3 is not being carried by robot1's lgripper1, ball3 is not located at room4, ball3 is not present at room3, ball3 is not present at room5, ball4 is at room2, ball4 is not located at room4, ball4 is not located at room6, ball4 is not present at room1, ball4 is not present at room3, ball4 is not present at room5, ball5 is located at room3, ball5 is not at room4, ball5 is not at room6, ball5 is not being carried by robot1's lgripper1, ball5 is not located at room1, ball5 is not present at room2, ball5 is not present at room5, ball6 is at room1, ball6 is not being carried by robot1's rgripper1, ball6 is not located at room5, ball6 is not located at room6, ball6 is not present at room2, ball6 is not present at room3, ball6 is not present at room4, ball7 is not at room5, ball7 is not at room6, ball7 is not located at room1, ball7 is not located at room3, ball7 is not located at room4, ball7 is not present at room2, lgripper1 of robot1 is carrying ball7, lgripper1 of robot1 is not carrying ball6, lgripper1 of robot1 is not free, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball7, robot1 is at room4, robot1 is not at room1, robot1 is not at room2, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not present in room3, robot1 is not present in room5, robot1 is not present in room6 and robot1's rgripper1 is free", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room3, ball6 is at room1, ball7 is located at room4, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are executed: robot1's lgripper1 in room4 picks up ball7 to achieve the current state. In this state, list all the valid properties (including both affirmative and negated properties). If there are no properties, indicate None.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is found in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "9f14af37-7268-43ee-9ae4-906acd678426", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "ball1 is at room1, ball1 is not at room3, ball1 is not at room4, ball1 is not located at room5, ball1 is not present at room2, ball2 is not at room1, ball2 is not at room4, ball2 is not present at room3, ball2 is not present at room5, ball2 is present at room2, ball3 is not at room3, ball3 is not located at room4, ball3 is not located at room5, ball3 is not present at room2, ball3 is present at room1, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room1, ball4 is not located at room5, ball4 is not present at room3, ball4 is not present at room4, ball4 is present at room2, ball5 is at room2, ball5 is not at room4, ball5 is not at room5, ball5 is not present at room1, ball5 is not present at room3, ball6 is located at room1, ball6 is not at room3, ball6 is not at room5, ball6 is not located at room2, ball6 is not present at room4, ball7 is located at room3, ball7 is not at room4, ball7 is not being carried by robot1's lgripper1, ball7 is not located at room2, ball7 is not located at room5, ball7 is not present at room1, lgripper1 of robot1 is free, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball5, robot1 is not at room2, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not located at room5, robot1 is not present in room3, robot1 is not present in room4 and robot1 is present in room1", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is at room2, ball6 is present at room1, ball7 is present at room3, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot1 moves from room4 to room1 to achieve the current state. In this state, list all the valid properties (including both affirmative and negated properties). If there are no valid properties, indicate None.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is in room2, ball6 is also in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and robot1's right gripper is available."}
{"question_id": "9ba081e7-52d9-4d3a-b500-c9ed5f0349ad", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "ball1 is not being carried by robot1's rgripper1, ball1 is not located at room2, ball1 is not located at room3, ball1 is not located at room5, ball1 is not present at room4, ball2 is not at room3, ball2 is not at room4, ball2 is not being carried by robot1's lgripper1, ball2 is not located at room1, ball2 is not present at room5, ball3 is not located at room3, ball3 is not present at room2, ball3 is not present at room4, ball3 is not present at room5, ball4 is not at room4, ball4 is not being carried by robot1's lgripper1, ball4 is not located at room1, ball4 is not located at room5, ball4 is not present at room3, ball5 is not at room1, ball5 is not being carried by robot1's rgripper1, ball5 is not located at room3, ball5 is not located at room4, ball5 is not present at room5, ball6 is not at room4, ball6 is not at room5, ball6 is not located at room2, ball6 is not present at room3, ball7 is not being carried by robot1's lgripper1, ball7 is not being carried by robot1's rgripper1, ball7 is not located at room1, ball7 is not located at room5, ball7 is not present at room2, ball7 is not present at room4, lgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball6, robot1 is not at room2, robot1 is not at room3, robot1 is not at room4, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball6 with lgripper1 and robot1 is not located at room5", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is at room2, ball6 is present at room1, ball7 is present at room3, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot1 moves from room4 to room1 to achieve the current state. In this state, identify all valid properties that include negations and list them; if there are no such properties, indicate None.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is in room2, ball6 is also in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and robot1's right gripper is available."}
{"question_id": "1df7e17b-32ce-4303-a151-a3d33adce277", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, from room1, robot1's lgripper1 picks up ball1, robot1 moves to room2 from room1, rgripper1 of robot1 picks up ball2 in room2, robot1 moves from room2 to room3, lgripper1 of robot1 drops ball1 in room3, from room3, robot1 moves to room4, rgripper1 of robot1 drops ball2 in room4, from room4, robot1 moves to room2, lgripper1 of robot1 picks up ball4 in room2, ball5 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room5, lgripper1 of robot1 drops ball4 in room5, from room5, robot1 moves to room1, ball3 is picked from room1 with lgripper1 by robot1, ball5 is dropped in room1 with rgripper1 by robot1, ball6 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5 and in room5, robot1's lgripper1 drops ball3 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "ball1 is not at room4, ball1 is not being carried by robot1's lgripper1, ball1 is not located at room1, ball1 is not located at room5, ball1 is not present at room2, ball2 is not located at room1, ball2 is not located at room3, ball2 is not located at room5, ball2 is not present at room2, ball3 is not at room3, ball3 is not being carried by robot1's rgripper1, ball3 is not located at room2, ball3 is not located at room4, ball3 is not present at room1, ball4 is not at room2, ball4 is not at room3, ball4 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room4, ball4 is not present at room1, ball5 is not being carried by robot1's rgripper1, ball5 is not located at room2, ball5 is not located at room3, ball5 is not located at room5, ball5 is not present at room4, ball6 is not at room2, ball6 is not located at room1, ball6 is not located at room4, ball6 is not located at room5, ball6 is not present at room3, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not free, robot1 is not carrying ball2 with rgripper1, robot1 is not located at room2, robot1 is not located at room3, robot1 is not located at room4 and robot1 is not present in room1", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is at room1, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 relocates from room4 to room1, then its left gripper (lgripper1) picks up ball1 in room1. Next, robot1 moves to room2 from room1, and its right gripper (rgripper1) picks up ball2 in room2. Robot1 then proceeds to room3 from room2, where lgripper1 drops ball1. From room3, robot1 moves to room4, and in room4, rgripper1 drops ball2. Subsequently, robot1 moves back to room2 from room4, and in room2, lgripper1 picks up ball4, while rgripper1 picks up ball5. Robot1 then moves to room5 from room2, where lgripper1 drops ball4. From room5, robot1 moves to room1, where lgripper1 picks up ball3, and rgripper1 drops ball5. Additionally, rgripper1 picks up ball6 in room1. Finally, robot1 moves to room5 from room1, and in room5, lgripper1 drops ball3, resulting in the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, the rgripper1 of robot1 is available, robot1 is positioned in room4 and robot1's lgripper1 is available."}
{"question_id": "5a2ebca3-2e74-45b9-b06c-6058fbebd546", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball1, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, ball1 is dropped in room2 with lgripper2 by robot2, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball4, rgripper2 of robot2 picks up ball7 in room3 and robot2 moves from room3 to room2 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "ball1 is located at room2, ball1 is not being carried by robot1's lgripper1, ball1 is not being carried by robot2's lgripper2, ball1 is not being carried by robot2's rgripper1, ball1 is not present at room1, ball1 is not present at room3, ball2 is located at room2, ball2 is not at room1, ball2 is not being carried by robot1's lgripper2, ball2 is not being carried by robot2's rgripper1, ball2 is not present at room3, ball3 is at room2, ball3 is not at room3, ball3 is not being carried by robot1's lgripper2, ball3 is not being carried by robot1's rgripper1, ball3 is not being carried by robot1's rgripper2, ball3 is not being carried by robot2's lgripper1, ball3 is not present at room1, ball4 is being carried by robot2's lgripper2, ball4 is not at room3, ball4 is not being carried by robot1's rgripper1, ball4 is not being carried by robot1's rgripper2, ball4 is not being carried by robot2's lgripper1, ball4 is not being carried by robot2's rgripper2, ball4 is not located at room1, ball4 is not located at room2, ball5 is not at room3, ball5 is not being carried by robot1's rgripper1, ball5 is not being carried by robot1's rgripper2, ball5 is not being carried by robot2's lgripper2, ball5 is not being carried by robot2's rgripper1, ball5 is not located at room2, ball5 is present at room1, ball6 is at room1, ball6 is not at room3, ball6 is not being carried by robot1's rgripper1, ball6 is not being carried by robot1's rgripper2, ball6 is not being carried by robot2's lgripper2, ball6 is not present at room2, ball7 is not at room3, ball7 is not being carried by robot1's rgripper1, ball7 is not being carried by robot1's rgripper2, ball7 is not located at room1, ball7 is not present at room2, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball7, lgripper1 of robot2 is not carrying ball1, lgripper1 of robot2 is not carrying ball7, lgripper1 of robot2 is not free, lgripper2 of robot1 is not carrying ball6, lgripper2 of robot1 is not carrying ball7, lgripper2 of robot2 is not carrying ball7, lgripper2 of robot2 is not free, rgripper1 of robot2 is not carrying ball3, rgripper1 of robot2 is not carrying ball7, rgripper1 of robot2 is not free, rgripper2 of robot1 is not carrying ball2, rgripper2 of robot2 is not carrying ball1, rgripper2 of robot2 is not carrying ball6, robot1 is not at room1, robot1 is not carrying ball1 with lgripper2, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball1 with rgripper2, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball4 with lgripper2, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball5 with lgripper2, robot1 is not carrying ball6 with lgripper1, robot1 is not located at room3, robot1 is present in room2, robot1's lgripper1 is free, robot1's lgripper2 is not free, robot1's rgripper1 is available, robot1's rgripper2 is not free, robot2 is at room2, robot2 is carrying ball7 with rgripper2, robot2 is not at room1, robot2 is not carrying ball2 with lgripper1, robot2 is not carrying ball2 with lgripper2, robot2 is not carrying ball2 with rgripper2, robot2 is not carrying ball3 with lgripper2, robot2 is not carrying ball3 with rgripper2, robot2 is not carrying ball4 with rgripper1, robot2 is not carrying ball5 with lgripper1, robot2 is not carrying ball5 with rgripper2, robot2 is not carrying ball6 with lgripper1, robot2 is not carrying ball6 with rgripper1, robot2 is not located at room3 and robot2's rgripper2 is not free", "plan_length": 10, "initial_state_nl": "Ball1 is located at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is present at room1, ball6 is at room1, ball7 is present at room3, robot1 is present in room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 transitions from room2 to room3, then uses its left gripper (lgripper2) to grasp ball1 and its right gripper (rgripper2) to grasp ball2, both in room3. Next, robot2 moves back to room2, where it releases ball1 with lgripper2 and ball2 with rgripper2. Subsequently, robot2 returns to room3, picks up ball4 with lgripper2 and ball7 with rgripper2, and then moves back to room2, resulting in the current state. In this state, list all valid properties (including those with and without negations). If there are no valid properties, indicate None.", "initial_state_nl_paraphrased": "Ball1 is situated in room3, ball2 is also in room3, ball3 is found in room2, ball4 is located at room3, ball5 is situated in room1, ball6 is also in room1, ball7 is located at room3, robot1 is in room2, robot1 has an available lgripper1, robot1 has an available rgripper1, robot2 is situated in room2, robot2 has an available lgripper2 and robot2 has an available rgripper2."}
{"question_id": "d820a64b-5a8d-4ae7-b328-ee4938e1bb09", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1's rgripper1 picks up ball3, robot1 moves from room1 to room5, ball3 is dropped in room5 with rgripper1 by robot1, from room5, robot1 moves to room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves to room1 from room2, in room1, robot1's rgripper1 drops ball4, from room1, robot1's rgripper1 picks up ball6, from room1, robot1 moves to room3, ball6 is dropped in room3 with rgripper1 by robot1, from room3, robot1's rgripper1 picks up ball7, robot1 moves to room4 from room3, ball1 is dropped in room4 with lgripper1 by robot1, ball7 is dropped in room4 with rgripper1 by robot1, robot1 moves to room2 from room4, lgripper1 of robot1 picks up ball2 in room2 and from room2, robot1's rgripper1 picks up ball5 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "ball1 is at room4, ball3 is located at room5, ball4 is present at room1, ball6 is located at room3, ball7 is at room4, lgripper1 of robot1 is carrying ball2, robot1 is carrying ball5 with rgripper1 and robot1 is located at room2", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is at room2, ball6 is present at room1, ball7 is present at room3, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: starting from room4, robot1 proceeds to room1, where its left gripper (lgripper1) grasps ball1, and its right gripper (rgripper1) picks up ball3; robot1 then moves to room5, drops ball3 using rgripper1, and proceeds to room2, where rgripper1 picks up ball4. Next, robot1 moves back to room1, drops ball4 with rgripper1, picks up ball6 with rgripper1, and then moves to room3, where it drops ball6 using rgripper1. In room3, rgripper1 picks up ball7, and robot1 moves to room4, where it drops ball1 using lgripper1 and ball7 using rgripper1. Finally, robot1 moves to room2, where lgripper1 picks up ball2 and rgripper1 picks up ball5, resulting in the current state. In this state, list all valid properties that do not involve negations. If none exist, state 'None'.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is in room2, ball6 is also in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and robot1's right gripper is available."}
{"question_id": "0955a619-a10b-4dcb-a62b-464cc97fca2c", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves from room1 to room2, ball2 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room3, ball1 is dropped in room3 with lgripper1 by robot1, robot1 moves to room4 from room3, in room4, robot1's rgripper1 drops ball2, robot1 moves from room4 to room2 and ball4 is picked from room2 with lgripper1 by robot1 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "ball1 is present at room3, ball2 is located at room4, ball3 is present at room1, ball5 is at room2, ball6 is present at room1, lgripper1 of robot1 is carrying ball4, robot1 is located at room2 and robot1's rgripper1 is free", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is at room1, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: robot1 relocates from room4 to room1, robot1 uses lgripper1 to pick up ball1 in room1, robot1 proceeds from room1 to room2, robot1 uses rgripper1 to pick up ball2 in room2, robot1 moves from room2 to room3, robot1 drops ball1 in room3 using lgripper1, robot1 then moves from room3 to room4, in room4, robot1 releases ball2 with rgripper1, robot1 moves from room4 back to room2, and finally, robot1 picks up ball4 in room2 with lgripper1 to attain the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, the rgripper1 of robot1 is available, robot1 is positioned in room4 and robot1's lgripper1 is available."}
{"question_id": "486f538d-c8e3-47aa-b22e-fbd5332345f3", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "ball1 is at room3, ball1 is not at room2, ball1 is not being carried by robot1's lgripper1, ball1 is not being carried by robot1's rgripper1, ball1 is not being carried by robot1's rgripper2, ball1 is not being carried by robot2's lgripper1, ball1 is not being carried by robot2's rgripper2, ball1 is not located at room1, ball2 is not being carried by robot1's lgripper1, ball2 is not being carried by robot1's lgripper2, ball2 is not being carried by robot2's lgripper2, ball2 is not being carried by robot2's rgripper1, ball2 is not located at room1, ball2 is not present at room2, ball2 is present at room3, ball3 is at room2, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's rgripper2, ball3 is not being carried by robot2's lgripper1, ball3 is not being carried by robot2's lgripper2, ball3 is not being carried by robot2's rgripper2, ball3 is not located at room1, ball3 is not located at room3, ball4 is located at room3, ball4 is not at room2, ball4 is not being carried by robot2's lgripper1, ball4 is not being carried by robot2's rgripper1, ball4 is not located at room1, ball5 is not at room3, ball5 is not being carried by robot1's lgripper1, ball5 is not being carried by robot1's rgripper2, ball5 is not being carried by robot2's rgripper1, ball5 is not present at room2, ball5 is present at room1, ball6 is not at room2, ball6 is not present at room3, ball6 is present at room1, ball7 is located at room3, ball7 is not being carried by robot1's lgripper1, ball7 is not being carried by robot1's lgripper2, ball7 is not being carried by robot1's rgripper2, ball7 is not being carried by robot2's lgripper1, ball7 is not being carried by robot2's lgripper2, ball7 is not being carried by robot2's rgripper1, ball7 is not located at room1, ball7 is not located at room2, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball6, lgripper1 of robot2 is not carrying ball2, lgripper1 of robot2 is not carrying ball6, lgripper2 of robot1 is not carrying ball1, lgripper2 of robot1 is not carrying ball3, lgripper2 of robot1 is not carrying ball4, lgripper2 of robot1 is not carrying ball5, lgripper2 of robot1 is not carrying ball6, lgripper2 of robot2 is free, lgripper2 of robot2 is not carrying ball4, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot2 is not carrying ball1, rgripper1 of robot2 is not carrying ball6, rgripper2 of robot1 is not carrying ball2, rgripper2 of robot1 is not carrying ball6, rgripper2 of robot1 is not free, rgripper2 of robot2 is free, rgripper2 of robot2 is not carrying ball4, rgripper2 of robot2 is not carrying ball6, rgripper2 of robot2 is not carrying ball7, robot1 is located at room2, robot1 is not at room1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with rgripper2, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not present in room3, robot1's lgripper1 is available, robot1's lgripper2 is not free, robot1's rgripper1 is free, robot2 is not carrying ball1 with lgripper2, robot2 is not carrying ball2 with rgripper2, robot2 is not carrying ball3 with rgripper1, robot2 is not carrying ball5 with lgripper1, robot2 is not carrying ball5 with lgripper2, robot2 is not carrying ball5 with rgripper2, robot2 is not carrying ball6 with lgripper2, robot2 is not present in room1, robot2 is not present in room2, robot2 is present in room3, robot2's lgripper1 is not free and robot2's rgripper1 is not available", "plan_length": 1, "initial_state_nl": "Ball1 is located at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is present at room1, ball6 is at room1, ball7 is present at room3, robot1 is present in room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot2 moves from room2 to room3 to achieve the current state. In this state, list all the valid properties (including both affirmative and negated properties). If there are no valid properties, indicate None.", "initial_state_nl_paraphrased": "Ball1 is situated in room3, ball2 is also in room3, ball3 is found in room2, ball4 is located at room3, ball5 is situated in room1, ball6 is also in room1, ball7 is located at room3, robot1 is in room2, the left gripper of robot1 is available, the right gripper of robot1 is available, robot2 is situated in room2, the left gripper of robot2 is available and the right gripper of robot2 is available."}
{"question_id": "e44ac80f-19a9-4d5e-bead-2fb1818349b9", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is at room3, ball5 is at room1, ball6 is present at room1, ball7 is at room3, lgripper1 of robot1 is free, rgripper2 of robot2 is free, robot1 is located at room2, robot1's rgripper1 is free, robot2 is located at room3 and robot2's lgripper2 is free", "plan_length": 1, "initial_state_nl": "Ball1 is located at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is present at room1, ball6 is at room1, ball7 is present at room3, robot1 is present in room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot2 moves from room2 to room3 to achieve the current state. In this state, identify all valid properties that do not include negations, or state None if there are no such properties.", "initial_state_nl_paraphrased": "Ball1 is situated in room3, ball2 is also in room3, ball3 is found in room2, ball4 is located at room3, ball5 is situated in room1, ball6 is also in room1, ball7 is located at room3, robot1 is in room2, robot1 has an available lgripper1, robot1 has an available rgripper1, robot2 is situated in room2, robot2 has an available lgripper2 and robot2 has an available rgripper2."}
{"question_id": "16437474-ff94-433e-be66-295ff123c138", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7, robot1 moves to room5 from room4, lgripper1 of robot1 drops ball7 in room5, from room5, robot1 moves to room1, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1's rgripper1 picks up ball3, robot1 moves to room5 from room1, in room5, robot1's lgripper1 drops ball1, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves to room2 from room5, ball2 is picked from room2 with lgripper1 by robot1, from room2, robot1's rgripper1 picks up ball4, robot1 moves from room2 to room1, in room1, robot1's rgripper1 drops ball4, rgripper1 of robot1 picks up ball6 in room1, from room1, robot1 moves to room3, ball6 is dropped in room3 with rgripper1 by robot1, rgripper1 of robot1 picks up ball5 in room3 and robot1 moves to room6 from room3 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "ball1 is at room5, ball1 is not at room2, ball1 is not at room4, ball1 is not being carried by robot1's lgripper1, ball1 is not located at room1, ball1 is not present at room3, ball1 is not present at room6, ball2 is not at room1, ball2 is not at room4, ball2 is not at room5, ball2 is not located at room3, ball2 is not located at room6, ball2 is not present at room2, ball3 is located at room5, ball3 is not at room6, ball3 is not being carried by robot1's lgripper1, ball3 is not located at room1, ball3 is not present at room2, ball3 is not present at room3, ball3 is not present at room4, ball4 is located at room1, ball4 is not at room4, ball4 is not at room5, ball4 is not at room6, ball4 is not located at room2, ball4 is not located at room3, ball5 is not at room1, ball5 is not being carried by robot1's lgripper1, ball5 is not present at room2, ball5 is not present at room3, ball5 is not present at room4, ball5 is not present at room5, ball5 is not present at room6, ball6 is at room3, ball6 is not at room1, ball6 is not being carried by robot1's rgripper1, ball6 is not located at room2, ball6 is not located at room5, ball6 is not present at room4, ball6 is not present at room6, ball7 is located at room5, ball7 is not at room1, ball7 is not at room2, ball7 is not located at room3, ball7 is not located at room4, ball7 is not located at room6, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball7, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball7, robot1 is carrying ball2 with lgripper1, robot1 is carrying ball5 with rgripper1, robot1 is not at room1, robot1 is not at room4, robot1 is not at room5, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not located at room2, robot1 is not located at room3, robot1 is present in room6, robot1's lgripper1 is not available and robot1's rgripper1 is not available", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room3, ball6 is at room1, ball7 is located at room4, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 starts in room4, where its left gripper (lgripper1) picks up ball7, then robot1 moves to room5, and in room5, lgripper1 drops ball7. Next, robot1 moves to room1, where lgripper1 picks up ball1, and then robot1's right gripper (rgripper1) picks up ball3. Robot1 then moves to room5, drops ball1 with lgripper1, and drops ball3 with rgripper1. From room5, robot1 moves to room2, picks up ball2 with lgripper1, and then picks up ball4 with rgripper1. Robot1 then moves to room1, drops ball4 with rgripper1, and picks up ball6 with rgripper1. Next, robot1 moves to room3, drops ball6 with rgripper1, picks up ball5 with rgripper1, and finally moves to room6. In this resulting state, list all valid properties (including both affirmative and negated properties). If there are none, indicate None.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is found in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "e5bdfe54-0c09-410a-a84e-bf3a881e896f", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, ball1 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball2 in room3, robot2 moves to room2 from room3, ball1 is dropped in room2 with lgripper2 by robot2, rgripper2 of robot2 drops ball2 in room2, robot2 moves from room2 to room3, lgripper2 of robot2 picks up ball4 in room3, ball7 is picked from room3 with rgripper2 by robot2 and from room3, robot2 moves to room2 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "ball1 is at room2, ball2 is at room2, ball3 is present at room2, ball5 is present at room1, ball6 is present at room1, ball7 is being carried by robot2's rgripper2, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is carrying ball4 with lgripper2 and robot2 is present in room2", "plan_length": 10, "initial_state_nl": "Ball1 is located at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is present at room1, ball6 is at room1, ball7 is present at room3, robot1 is present in room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 relocates from room2 to room3, robot2 uses lgripper2 to pick up ball1 in room3, robot2's rgripper2 picks up ball2 in room3, robot2 then moves back to room2 from room3, robot2 drops ball1 in room2 using lgripper2, robot2's rgripper2 drops ball2 in room2, robot2 moves from room2 back to room3, robot2 uses lgripper2 to pick up ball4 in room3, and robot2 uses rgripper2 to pick up ball7 in room3 before moving to room2 to reach the current state. In this state, list all valid properties that do not involve negations. If there are no such properties, state None.", "initial_state_nl_paraphrased": "Ball1 is situated in room3, ball2 is also in room3, ball3 is found in room2, ball4 is located at room3, ball5 is situated in room1, ball6 is also in room1, ball7 is found in room3, robot1 is positioned in room2, the left gripper of robot1 is available, the right gripper of robot1 is available, robot2 is situated in room2, the left gripper of robot2 is available and the right gripper of robot2 is available."}
{"question_id": "cda4720e-5260-4874-ad45-d1da6f3104a4", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, lgripper1 of robot1 picks up ball1 in room1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves to room5 from room1, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves to room2 from room5, rgripper1 of robot1 picks up ball4 in room2, robot1 moves to room1 from room2, rgripper1 of robot1 drops ball4 in room1 and from room1, robot1's rgripper1 picks up ball6 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "ball1 is not at room1, ball1 is not located at room2, ball1 is not located at room3, ball1 is not located at room4, ball1 is not present at room5, ball2 is not being carried by robot1's lgripper1, ball2 is not located at room1, ball2 is not present at room3, ball2 is not present at room4, ball2 is not present at room5, ball3 is not at room1, ball3 is not at room2, ball3 is not being carried by robot1's rgripper1, ball3 is not located at room3, ball3 is not present at room4, ball4 is not at room4, ball4 is not at room5, ball4 is not being carried by robot1's rgripper1, ball4 is not present at room2, ball4 is not present at room3, ball5 is not at room4, ball5 is not being carried by robot1's rgripper1, ball5 is not present at room1, ball5 is not present at room3, ball5 is not present at room5, ball6 is not at room4, ball6 is not at room5, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room3, ball6 is not present at room1, ball6 is not present at room2, ball7 is not being carried by robot1's rgripper1, ball7 is not located at room2, ball7 is not located at room4, ball7 is not located at room5, ball7 is not present at room1, lgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball2, robot1 is not at room2, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not located at room3, robot1 is not located at room4, robot1 is not located at room5, robot1's lgripper1 is not free and robot1's rgripper1 is not free", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is at room2, ball6 is present at room1, ball7 is present at room3, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: starting from room4, robot1 proceeds to room1, where it uses its left gripper (lgripper1) to grasp ball1 and its right gripper (rgripper1) to grasp ball3. Then, robot1 moves to room5 from room1 and releases ball3 in room5 using rgripper1. Next, robot1 moves to room2 from room5, where it uses rgripper1 to pick up ball4. After that, robot1 returns to room1 from room2 and drops ball4 in room1 using rgripper1. Finally, from room1, robot1's rgripper1 picks up ball6, resulting in the current state. In this state, list all valid properties that involve negations. If there are none, indicate None.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is in room2, ball6 is also in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and robot1's right gripper is available."}
{"question_id": "32347d13-d8b5-4639-84e3-dbaa1955cfc2", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is at room1, lgripper1 of robot1 is free, robot1 is present in room1 and robot1's rgripper1 is free", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is at room1, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot1 moves from room4 to room1 to achieve the current state. In this state, identify all valid properties that do not include negations, or state None if there are no such properties.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, the rgripper1 of robot1 is available, robot1 is positioned in room4 and robot1's lgripper1 is available."}
{"question_id": "5743862b-2c74-4ffc-85ef-bb4d7a28bc64", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1, robot1 moves to room5 from room4, lgripper1 of robot1 drops ball7 in room5, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves from room1 to room5, lgripper1 of robot1 drops ball1 in room5, ball3 is dropped in room5 with rgripper1 by robot1 and robot1 moves to room2 from room5 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "ball1 is not being carried by robot1's lgripper1, ball1 is not located at room3, ball1 is not located at room6, ball1 is not present at room1, ball1 is not present at room2, ball1 is not present at room4, ball1 is present at room5, ball2 is at room2, ball2 is not at room4, ball2 is not being carried by robot1's lgripper1, ball2 is not located at room1, ball2 is not located at room3, ball2 is not located at room5, ball2 is not present at room6, ball3 is located at room5, ball3 is not at room2, ball3 is not located at room4, ball3 is not located at room6, ball3 is not present at room1, ball3 is not present at room3, ball4 is not at room3, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room1, ball4 is not located at room5, ball4 is not present at room4, ball4 is not present at room6, ball4 is present at room2, ball5 is located at room3, ball5 is not at room1, ball5 is not at room2, ball5 is not at room4, ball5 is not located at room5, ball5 is not present at room6, ball6 is located at room1, ball6 is not at room2, ball6 is not at room4, ball6 is not located at room3, ball6 is not located at room6, ball6 is not present at room5, ball7 is located at room5, ball7 is not located at room2, ball7 is not located at room4, ball7 is not present at room1, ball7 is not present at room3, ball7 is not present at room6, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not carrying ball6, lgripper1 of robot1 is not carrying ball7, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball2, robot1 is located at room2, robot1 is not at room3, robot1 is not at room5, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not located at room1, robot1 is not present in room4, robot1 is not present in room6 and robot1's lgripper1 is available", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room3, ball6 is at room1, ball7 is located at room4, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 uses lgripper1 to pick up ball7 from room4, then moves from room4 to room5, drops ball7 in room5 using lgripper1, proceeds to room1 from room5, picks up ball1 with lgripper1 and ball3 with rgripper1 in room1, moves back to room5, drops ball1 and ball3 in room5 using lgripper1 and rgripper1 respectively, and finally moves to room2 from room5 to attain the current state. In this state, list all valid properties (including those with and without negations) that hold true. If there are no valid properties, indicate None.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is found in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
