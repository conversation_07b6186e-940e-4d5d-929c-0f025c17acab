{"question_id": "5f950800-e16c-42d0-bf75-67098cbf0a1b", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 9? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is located at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot1 relocates from room4 to room1 to achieve the current state. In this state, does the count of valid properties that do not include negations equal 9? True or False", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is situated in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, robot1 is situated in room4, robot1's left gripper, lgripper1, is available and robot1's right gripper, rgripper1, is available."}
{"question_id": "76a38473-de64-4a2a-807f-7e3c14dd252e", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7, from room4, robot1 moves to room5, in room5, robot1's lgripper1 drops ball7, from room5, robot1 moves to room1, lgripper1 of robot1 picks up ball1 in room1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, lgripper1 of robot1 drops ball1 in room5, rgripper1 of robot1 drops ball3 in room5, robot1 moves to room2 from room5, ball2 is picked from room2 with lgripper1 by robot1, from room2, robot1's rgripper1 picks up ball4, from room2, robot1 moves to room1, ball4 is dropped in room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball6, robot1 moves to room3 from room1, in room3, robot1's rgripper1 drops ball6, ball5 is picked from room3 with rgripper1 by robot1 and robot1 moves to room6 from room3 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 8? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is at room3, ball6 is present at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1's lgripper1 picks up ball7 in room4, then robot1 moves to room5, where it drops ball7, and then proceeds to room1. In room1, robot1's lgripper1 picks up ball1 and its rgripper1 picks up ball3. From room1, robot1 moves to room5, where it drops both ball1 with lgripper1 and ball3 with rgripper1. Then, robot1 moves to room2, picks up ball2 with lgripper1 and ball4 with rgripper1, and then moves to room1, where it drops ball4 with rgripper1. In room1, robot1's rgripper1 picks up ball6, and then robot1 moves to room3, where it drops ball6 with rgripper1 and picks up ball5 with rgripper1, before finally moving to room6 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 8? True or False", "initial_state_nl_paraphrased": "Ball1 is located at room1, ball2 is at room2, ball3 is present in room1, ball4 is present in room2, ball5 is located at room3, ball6 is at room1, ball7 is at room4, the left gripper of robot1 is free, robot1 is at room4 and the right gripper of robot1 is available."}
{"question_id": "3f85830f-c87c-4c6f-9a12-2b16b6e5bdd7", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, lgripper1 of robot1 picks up ball1 in room1, robot1 moves from room1 to room2, from room2, robot1's rgripper1 picks up ball2, robot1 moves from room2 to room3, ball1 is dropped in room3 with lgripper1 by robot1, from room3, robot1 moves to room4, rgripper1 of robot1 drops ball2 in room4, robot1 moves to room2 from room4 and ball4 is picked from room2 with lgripper1 by robot1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 41? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is located at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 relocates from room4 to room1, then uses its left gripper (lgripper1) to grasp ball1 in room1. Next, robot1 proceeds from room1 to room2, where its right gripper (rgripper1) picks up ball2. Robot1 then moves from room2 to room3, drops ball1 in room3 using lgripper1, and subsequently moves from room3 back to room4. In room4, rgripper1 releases ball2. Finally, robot1 returns to room2 from room4 and uses lgripper1 to pick up ball4, resulting in the current state. In this state, is the number of valid properties that involve negations equal to 41? True or False", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is situated in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, robot1 is situated in room4, robot1's left gripper, lgripper1, is available and robot1's right gripper, rgripper1, is available."}
{"question_id": "463c0752-6074-4d43-9471-42e1fb8f6bb0", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, ball1 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 picks up ball3 in room1, from room1, robot1 moves to room5, in room5, robot1's rgripper1 drops ball3, from room5, robot1 moves to room2, from room2, robot1's rgripper1 picks up ball4, robot1 moves to room1 from room2, rgripper1 of robot1 drops ball4 in room1 and from room1, robot1's rgripper1 picks up ball6 to reach the current state. In this state, is the number of inexecutable actions equal to 154? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 relocates from room4 to room1, then uses lgripper1 to pick up ball1 in room1, and subsequently uses rgripper1 to pick up ball3 in the same room. Next, robot1 moves from room1 to room5, where it drops ball3 using rgripper1. From room5, robot1 proceeds to room2, picks up ball4 with rgripper1, and then moves back to room1, where it drops ball4 using rgripper1. Finally, robot1 uses rgripper1 to pick up ball6 in room1, resulting in the current state. In this state, is the number of inexecutable actions equal to 154? True or False", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 resides in room1, ball4 is found in room2, ball5 is positioned in room2, ball6 is situated in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's right gripper is available."}
{"question_id": "5628728a-2964-4cb9-9b07-0e4b3820db00", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1, robot1 moves to room5 from room4, ball7 is dropped in room5 with lgripper1 by robot1, from room5, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves to room5 from room1, ball1 is dropped in room5 with lgripper1 by robot1, ball3 is dropped in room5 with rgripper1 by robot1 and robot1 moves from room5 to room2 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 61? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is at room3, ball6 is present at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 uses lgripper1 to pick up ball7 from room4, then moves from room4 to room5, drops ball7 in room5 using lgripper1, proceeds from room5 to room1, picks up ball1 with lgripper1 and ball3 with rgripper1 from room1, moves back to room5 from room1, drops ball1 and ball3 in room5 using lgripper1 and rgripper1 respectively, and finally moves from room5 to room2 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 61? True or False", "initial_state_nl_paraphrased": "Ball1 is located at room1, ball2 is at room2, ball3 is present in room1, ball4 is present in room2, ball5 is located at room3, ball6 is at room1, ball7 is at room4, the left gripper of robot1 is free, robot1 is at room4 and the right gripper of robot1 is available."}
{"question_id": "59b65b52-7344-4808-8c24-0d10fac64f60", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 10? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is at room3, ball6 is present at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot1's lgripper1 in room4 picks up ball7 to achieve the current state. In this state, does the count of valid properties without negations equal 10? True or False", "initial_state_nl_paraphrased": "Ball1 is located at room1, ball2 is at room2, ball3 is present in room1, ball4 is present in room2, ball5 is located at room3, ball6 is at room1, ball7 is at room4, the left gripper of robot1 is free, robot1 is at room4 and the right gripper of robot1 is available."}
{"question_id": "8f47a477-caf5-452e-8a41-d81033c83afb", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves to room2 from room1, ball2 is picked from room2 with rgripper1 by robot1, robot1 moves to room3 from room2, ball1 is dropped in room3 with lgripper1 by robot1, robot1 moves to room4 from room3, in room4, robot1's rgripper1 drops ball2, robot1 moves from room4 to room2, from room2, robot1's lgripper1 picks up ball4, ball5 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room5, lgripper1 of robot1 drops ball4 in room5, robot1 moves to room1 from room5, lgripper1 of robot1 picks up ball3 in room1, rgripper1 of robot1 drops ball5 in room1, rgripper1 of robot1 picks up ball6 in room1, from room1, robot1 moves to room5 and in room5, robot1's lgripper1 drops ball3 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 41? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is located at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 moves from room4 to room1, then it picks up ball1 in room1 using lgripper1, proceeds to room2 from room1, picks up ball2 in room2 using rgripper1, moves to room3 from room2, drops ball1 in room3 using lgripper1, returns to room4 from room3, and in room4, it drops ball2 using rgripper1. Next, robot1 moves from room4 to room2, picks up ball4 in room2 using lgripper1, and also picks up ball5 in room2 using rgripper1. Then, robot1 moves to room5 from room2, drops ball4 in room5 using lgripper1, and proceeds to room1 from room5. In room1, robot1 picks up ball3 using lgripper1, drops ball5 using rgripper1, and picks up ball6 using rgripper1. Finally, robot1 moves from room1 to room5 and drops ball3 in room5 using lgripper1, resulting in the current state. In this state, is the number of valid properties of the state that involve negations equal to 41? True or False", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is situated in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, robot1 is situated in room4, robot1's left gripper, lgripper1, is available and robot1's right gripper, rgripper1, is available."}
{"question_id": "96740dc5-c445-494a-9add-78bb2f9b5c09", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_11", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, ball1 is picked from room3 with lgripper2 by robot2, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, in room2, robot2's lgripper2 drops ball1, in room2, robot2's rgripper2 drops ball2, robot2 moves to room3 from room2, ball4 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball7 in room3, robot2 moves from room3 to room2, ball7 is dropped in room2 with rgripper2 by robot2, ball3 is picked from room2 with rgripper2 by robot2, robot2 moves to room1 from room2, ball4 is dropped in room1 with lgripper2 by robot2, from room1, robot2's lgripper2 picks up ball5, rgripper2 of robot2 drops ball3 in room1, from room1, robot2's rgripper2 picks up ball6, robot2 moves from room1 to room2 and in room2, robot2's lgripper2 drops ball5 to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 91? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is located at room3, ball5 is present at room1, ball6 is located at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room2, robot1's lgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 relocates from room2 to room3, then uses lgripper2 to pick up ball1 in room3, followed by using rgripper2 to pick up ball2 in room3. Next, robot2 moves back to room2 from room3, and in room2, it drops ball1 with lgripper2 and ball2 with rgripper2. Then, robot2 returns to room3 from room2, picks up ball4 with lgripper2 and ball7 with rgripper2 in room3. After that, robot2 moves back to room2, drops ball7 with rgripper2, and picks up ball3 with rgripper2 in room2. Subsequently, robot2 moves to room1 from room2, drops ball4 with lgripper2, picks up ball5 with lgripper2, drops ball3 with rgripper2, and picks up ball6 with rgripper2 in room1. Finally, robot2 moves back to room2 from room1 and drops ball5 with lgripper2 to reach the current state. In this state, is the total number of valid properties (including both affirmative and negated properties) equal to 91? True or False", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is positioned at room3, ball5 is found in room1, ball6 is situated in room1, ball7 is also in room3, the right gripper of robot1 is available, robot1 is positioned in room2, the left gripper of robot1 is free, robot2 is situated in room2, the left gripper of robot2 is free and the right gripper of robot2 is available."}
{"question_id": "0a3d4e59-8ce9-42a3-92fa-0f6f0ff30f4c", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 46? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot1 moves from room4 to room1 to achieve the current state. In this state, does the number of valid properties involving negations equal 46? True or False", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated at room2, ball3 resides in room1, ball4 is found at room2, ball5 is positioned at room2, ball6 is situated in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's right gripper is available."}
{"question_id": "c53ecfa2-bc24-4881-a6d4-4f04ebb51f1e", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: lgripper1 of robot1 picks up ball7 in room4 to reach the current state. Is it True or False that the number of actions that led to current state in the sequence is equal to 2?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is at room3, ball6 is present at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Considering the initial condition, the following actions are taken: the left gripper of robot1 grasps ball7 in room4 to achieve the current state. Is it True or False that the number of actions in the sequence that resulted in the current state is 2?", "initial_state_nl_paraphrased": "Ball1 is located at room1, ball2 is at room2, ball3 is present in room1, ball4 is present in room2, ball5 is located at room3, ball6 is at room1, ball7 is at room4, the left gripper of robot1 is free, robot1 is at room4 and the right gripper of robot1 is available."}
{"question_id": "bfd51cee-bcb0-4d57-80c1-9c57fee049c3", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves to room5 from room1, in room5, robot1's rgripper1 drops ball3, robot1 moves to room2 from room5, from room2, robot1's rgripper1 picks up ball4, robot1 moves from room2 to room1, in room1, robot1's rgripper1 drops ball4, from room1, robot1's rgripper1 picks up ball6, robot1 moves from room1 to room3, rgripper1 of robot1 drops ball6 in room3, from room3, robot1's rgripper1 picks up ball7, robot1 moves to room4 from room3, in room4, robot1's lgripper1 drops ball1, rgripper1 of robot1 drops ball7 in room4, robot1 moves to room2 from room4, ball2 is picked from room2 with lgripper1 by robot1 and rgripper1 of robot1 picks up ball5 in room2 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 51? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 moves from room4 to room1, then it picks up ball1 in room1 using lgripper1 and ball3 in room1 using rgripper1, after which it moves to room5 from room1. In room5, robot1 drops ball3 using rgripper1, then moves to room2 from room5. From room2, robot1 picks up ball4 using rgripper1, moves to room1 from room2, and drops ball4 in room1 using rgripper1. Next, robot1 picks up ball6 in room1 using rgripper1, moves to room3 from room1, and drops ball6 in room3 using rgripper1. Then, robot1 picks up ball7 in room3 using rgripper1, moves to room4 from room3, and drops ball1 and ball7 in room4 using lgripper1 and rgripper1, respectively. Finally, robot1 moves to room2 from room4, picks up ball2 in room2 using lgripper1, and picks up ball5 in room2 using rgripper1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 51? True or False", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 resides in room1, ball4 is found in room2, ball5 is positioned in room2, ball6 is situated in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's right gripper is available."}
{"question_id": "36db6841-6b76-4559-aaf9-3f83acbf67d3", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3, lgripper2 of robot2 picks up ball1 in room3, ball2 is picked from room3 with rgripper2 by robot2, from room3, robot2 moves to room2, ball1 is dropped in room2 with lgripper2 by robot2, in room2, robot2's rgripper2 drops ball2, robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball4, ball7 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, rgripper2 of robot2 drops ball7 in room2, rgripper2 of robot2 picks up ball3 in room2, from room2, robot2 moves to room1, lgripper2 of robot2 drops ball4 in room1, from room1, robot2's lgripper2 picks up ball5, rgripper2 of robot2 drops ball3 in room1, rgripper2 of robot2 picks up ball6 in room1, from room1, robot2 moves to room2 and in room2, robot2's lgripper2 drops ball5 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 11? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is located at room3, ball5 is present at room1, ball6 is located at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room2, robot1's lgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 moves from room2 to room3, then uses its left gripper (lgripper2) to pick up ball1 in room3, and its right gripper (rgripper2) to pick up ball2 in room3. Next, robot2 moves back to room2, drops ball1 with lgripper2, and drops ball2 with rgripper2 in room2. Then, robot2 returns to room3, picks up ball4 with lgripper2 and ball7 with rgripper2, moves back to room2, drops ball7 with rgripper2, picks up ball3 with rgripper2, and proceeds to room1. In room1, robot2 drops ball4 with lgripper2, picks up ball5 with lgripper2, drops ball3 with rgripper2, and picks up ball6 with rgripper2. Finally, robot2 moves to room2 and drops ball5 with lgripper2 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 11? True or False", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is in room2, ball4 is situated in room3, ball5 is in room1, ball6 is also in room1, ball7 is in room3, the right gripper of robot1 is available, robot1 is positioned in room2, the left gripper of robot1 is free, robot2 is situated in room2, the left gripper of robot2 is free and the right gripper of robot2 is available."}
{"question_id": "31a10415-92b1-4b45-b9d9-348dca998d3b", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, from room1, robot1's lgripper1 picks up ball1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1 moves to room5, in room5, robot1's rgripper1 drops ball3, from room5, robot1 moves to room2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1 and rgripper1 of robot1 picks up ball6 in room1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 48? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 relocates from room4 to room1, then in room1, robot1's left gripper (lgripper1) grasps ball1, and subsequently, robot1's right gripper (rgripper1) picks up ball3, also in room1. Next, robot1 proceeds to room5, where it releases ball3 using rgripper1. From room5, robot1 moves to room2, where it uses rgripper1 to pick up ball4. Then, robot1 returns to room1, drops ball4 using rgripper1, and finally, uses rgripper1 to grasp ball6 in room1, resulting in the current state. In this state, is the count of valid properties involving negations equal to 48? True or False", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 resides in room1, ball4 is found in room2, ball5 is positioned in room2, ball6 is situated in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's right gripper is available."}
{"question_id": "eeeefd94-5b20-4d7d-af1d-fe9e5dd5376a", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2, lgripper2 of robot2 picks up ball1 in room3, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves from room3 to room2, ball1 is dropped in room2 with lgripper2 by robot2, in room2, robot2's rgripper2 drops ball2, robot2 moves to room3 from room2, ball4 is picked from room3 with lgripper2 by robot2, from room3, robot2's rgripper2 picks up ball7, robot2 moves to room2 from room3, rgripper2 of robot2 drops ball7 in room2, ball3 is picked from room2 with rgripper2 by robot2, from room2, robot2 moves to room1, in room1, robot2's lgripper2 drops ball4, from room1, robot2's lgripper2 picks up ball5, ball3 is dropped in room1 with rgripper2 by robot2, rgripper2 of robot2 picks up ball6 in room1, robot2 moves to room2 from room1 and ball5 is dropped in room2 with lgripper2 by robot2 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 349? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is located at room3, ball5 is present at room1, ball6 is located at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room2, robot1's lgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 transitions from room2 to room3, where it uses its left gripper (lgripper2) to grasp ball1 and its right gripper (rgripper2) to pick up ball2, then moves back to room2. In room2, robot2 releases ball1 with lgripper2 and drops ball2 with rgripper2. Next, robot2 returns to room3, picks up ball4 with lgripper2 and ball7 with rgripper2, then moves back to room2, where it drops ball7 with rgripper2 and picks up ball3 with rgripper2. Robot2 then proceeds to room1, drops ball4 with lgripper2, picks up ball5 with lgripper2, drops ball3 with rgripper2, and picks up ball6 with rgripper2. Finally, robot2 moves back to room2 and drops ball5 with lgripper2, reaching the current state. In this state, is the number of executable and inexecutable actions equal to 349? True or False", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is positioned in room3, ball5 is found in room1, ball6 is situated in room1, ball7 is in room3, the right gripper of robot1 is available, robot1 is positioned in room2, the left gripper of robot1 is free, robot2 is situated in room2, the left gripper of robot2 is free and the right gripper of robot2 is available."}
{"question_id": "86bdbee8-66dd-4b7b-ae1a-fd8faa5d38f6", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, lgripper2 of robot2 picks up ball1 in room3, from room3, robot2's rgripper2 picks up ball2, robot2 moves from room3 to room2, in room2, robot2's lgripper2 drops ball1, rgripper2 of robot2 drops ball2 in room2, robot2 moves from room2 to room3, ball4 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball7 in room3, from room3, robot2 moves to room2, rgripper2 of robot2 drops ball7 in room2, rgripper2 of robot2 picks up ball3 in room2, robot2 moves to room1 from room2, ball4 is dropped in room1 with lgripper2 by robot2, ball5 is picked from room1 with lgripper2 by robot2, rgripper2 of robot2 drops ball3 in room1, from room1, robot2's rgripper2 picks up ball6, robot2 moves to room2 from room1 and in room2, robot2's lgripper2 drops ball5 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 81? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is located at room3, ball5 is present at room1, ball6 is located at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room2, robot1's lgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 relocates from room2 to room3, then its left gripper (lgripper2) grasps ball1 in room3, followed by its right gripper (rgripper2) picking up ball2 in room3. Next, robot2 moves back to room2, where it releases ball1 using lgripper2 and drops ball2 using rgripper2. Robot2 then returns to room3, picks up ball4 with lgripper2 and ball7 with rgripper2, and moves back to room2. In room2, rgripper2 drops ball7 and picks up ball3. Subsequently, robot2 proceeds to room1, where it drops ball4 using lgripper2 and picks up ball5 using lgripper2. Then, rgripper2 releases ball3, and robot2's rgripper2 picks up ball6 in room1. Finally, robot2 moves to room2 and drops ball5 using lgripper2, reaching the current state. In this state, is the number of valid properties of the state that involve negations equal to 81? True or False", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is in room2, ball4 is situated in room3, ball5 is in room1, ball6 is also in room1, ball7 is in room3, the right gripper of robot1 is available, robot1 is positioned in room2, the left gripper of robot1 is free, robot2 is situated in room2, the left gripper of robot2 is free and the right gripper of robot2 is available."}
{"question_id": "af09612d-2ddc-4600-94f1-bbcb00b23879", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1, robot1 moves from room4 to room5, in room5, robot1's lgripper1 drops ball7, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, in room5, robot1's lgripper1 drops ball1, rgripper1 of robot1 drops ball3 in room5, from room5, robot1 moves to room2, lgripper1 of robot1 picks up ball2 in room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1, ball6 is picked from room1 with rgripper1 by robot1, robot1 moves to room3 from room1, rgripper1 of robot1 drops ball6 in room3, rgripper1 of robot1 picks up ball5 in room3 and robot1 moves from room3 to room6 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 56? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is at room3, ball6 is present at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 uses lgripper1 to pick up ball7 in room4, then moves to room5, where it drops ball7 using lgripper1, and proceeds to room1. In room1, robot1's lgripper1 picks up ball1, and rgripper1 picks up ball3. Robot1 then moves to room5, where it drops ball1 with lgripper1 and ball3 with rgripper1. Next, robot1 moves to room2, where lgripper1 picks up ball2 and rgripper1 picks up ball4. Robot1 then moves to room1, drops ball4 with rgripper1, and picks up ball6 with rgripper1. After moving to room3, robot1 drops ball6 with rgripper1 and picks up ball5 with rgripper1, before finally moving to room6 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 56? True or False", "initial_state_nl_paraphrased": "Ball1 is located at room1, ball2 is at room2, ball3 is present in room1, ball4 is present in room2, ball5 is located at room3, ball6 is at room1, ball7 is at room4, the left gripper of robot1 is free, robot1 is at room4 and the right gripper of robot1 is available."}
{"question_id": "ac8b9400-00e9-47a2-9bbc-d7fc09de84ae", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 8? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot1 relocates from room4 to room1 to achieve the current state. In this state, does the count of valid properties that do not include negations equal 8? True or False", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 resides in room1, ball4 is found in room2, ball5 is positioned in room2, ball6 is situated in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's right gripper is available."}
{"question_id": "ef2a223e-971d-4c7c-9574-7827d6b8fab4", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_11", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, ball1 is picked from room1 with lgripper1 by robot1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves to room5 from room1, in room5, robot1's rgripper1 drops ball3, robot1 moves to room2 from room5, from room2, robot1's rgripper1 picks up ball4, from room2, robot1 moves to room1, rgripper1 of robot1 drops ball4 in room1, ball6 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room3, in room3, robot1's rgripper1 drops ball6, rgripper1 of robot1 picks up ball7 in room3, robot1 moves to room4 from room3, lgripper1 of robot1 drops ball1 in room4, in room4, robot1's rgripper1 drops ball7, robot1 moves to room2 from room4, from room2, robot1's lgripper1 picks up ball2 and ball5 is picked from room2 with rgripper1 by robot1 to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 56? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 relocates from room4 to room1, then it uses lgripper1 to pick up ball1 in room1, and subsequently uses rgripper1 to pick up ball3 in the same room. Next, robot1 moves to room5 from room1, drops ball3 in room5 using rgripper1, and then proceeds to room2. In room2, robot1's rgripper1 picks up ball4, and then robot1 moves back to room1, where it drops ball4 using rgripper1. Robot1 then picks up ball6 in room1 with rgripper1 and moves to room3, where it drops ball6. In room3, robot1's rgripper1 picks up ball7, and then robot1 moves to room4, where it drops ball1 using lgripper1 and ball7 using rgripper1. Finally, robot1 moves to room2, where it picks up ball2 with lgripper1 and ball5 with rgripper1, resulting in the current state. In this state, is the total number of valid properties (including both affirmative and negated properties) equal to 56? True or False", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated at room2, ball3 resides in room1, ball4 is found at room2, ball5 is positioned at room2, ball6 is situated in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's right gripper is available."}
{"question_id": "9122c0df-82b0-44d7-977f-db35a63e32cd", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, lgripper1 of robot1 picks up ball1 in room1, robot1 moves to room2 from room1, ball2 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room3, lgripper1 of robot1 drops ball1 in room3, from room3, robot1 moves to room4, in room4, robot1's rgripper1 drops ball2, robot1 moves from room4 to room2 and lgripper1 of robot1 picks up ball4 in room2 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 6? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is located at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 moves from room4 to room1, then uses its left gripper (lgripper1) to pick up ball1 in room1, proceeds to room2 from room1, and uses its right gripper (rgripper1) to pick up ball2 in room2. Next, robot1 moves to room3 from room2, drops ball1 in room3 using lgripper1, and then moves back to room4. In room4, robot1 drops ball2 using rgripper1, moves to room2, and finally uses lgripper1 to pick up ball4 in room2, resulting in the current state. In this state, is the number of valid properties that do not involve negations equal to 6? True or False", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is situated in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, robot1 is situated in room4, robot1's left gripper, lgripper1, is available and robot1's right gripper, rgripper1, is available."}
{"question_id": "d296894a-ef67-4125-b4d8-06d64bcdc1c5", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: lgripper1 of robot1 picks up ball7 in room4, robot1 moves to room5 from room4, ball7 is dropped in room5 with lgripper1 by robot1, robot1 moves to room1 from room5, lgripper1 of robot1 picks up ball1 in room1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room5, in room5, robot1's lgripper1 drops ball1, rgripper1 of robot1 drops ball3 in room5, robot1 moves to room2 from room5, from room2, robot1's lgripper1 picks up ball2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves to room1 from room2, ball4 is dropped in room1 with rgripper1 by robot1, rgripper1 of robot1 picks up ball6 in room1, from room1, robot1 moves to room3, ball6 is dropped in room3 with rgripper1 by robot1, from room3, robot1's rgripper1 picks up ball5 and from room3, robot1 moves to room6 to reach the current state. Is it True or False that the number of actions that led to current state in the sequence is equal to 19?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is at room3, ball6 is present at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1's left gripper (lgripper1) picks up ball7 in room4, then robot1 moves from room4 to room5, and drops ball7 in room5 using lgripper1. Next, robot1 moves from room5 to room1, where lgripper1 picks up ball1 and the right gripper (rgripper1) picks up ball3. Robot1 then moves from room1 to room5, drops ball1 with lgripper1, and drops ball3 with rgripper1 in room5. After that, robot1 moves from room5 to room2, picks up ball2 with lgripper1 and ball4 with rgripper1 in room2, and then moves from room2 to room1. In room1, robot1 drops ball4 with rgripper1, picks up ball6 with rgripper1, and then moves from room1 to room3. In room3, robot1 drops ball6 with rgripper1, picks up ball5 with rgripper1, and finally moves from room3 to room6 to reach the current state. Is it True or False that the number of actions in this sequence that led to the current state is 19?", "initial_state_nl_paraphrased": "Ball1 is located at room1, ball2 is at room2, ball3 is present in room1, ball4 is present in room2, ball5 is located at room3, ball6 is at room1, ball7 is at room4, the left gripper of robot1 is free, robot1 is at room4 and the right gripper of robot1 is available."}
{"question_id": "d7276433-67a8-45a9-ae46-fac0b79ac546", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 13? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is located at room3, ball5 is present at room1, ball6 is located at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room2, robot1's lgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot2 moves from room2 to room3 to achieve the current state. In this state, does the number of valid properties without negations equal 13? True or False", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is in room2, ball4 is situated in room3, ball5 is in room1, ball6 is also in room1, ball7 is in room3, the right gripper of robot1 is available, robot1 is positioned in room2, the left gripper of robot1 is free, robot2 is in room2, the left gripper of robot2 is free and the right gripper of robot2 is also available."}
{"question_id": "77e14553-3010-45e4-8e8c-94344fdecd3b", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: lgripper1 of robot1 picks up ball7 in room4, from room4, robot1 moves to room5, in room5, robot1's lgripper1 drops ball7, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves to room5 from room1, lgripper1 of robot1 drops ball1 in room5, ball3 is dropped in room5 with rgripper1 by robot1, from room5, robot1 moves to room2, ball2 is picked from room2 with lgripper1 by robot1, from room2, robot1's rgripper1 picks up ball4, from room2, robot1 moves to room1, in room1, robot1's rgripper1 drops ball4, rgripper1 of robot1 picks up ball6 in room1, from room1, robot1 moves to room3, rgripper1 of robot1 drops ball6 in room3, ball5 is picked from room3 with rgripper1 by robot1 and from room3, robot1 moves to room6 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 47? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is at room3, ball6 is present at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1's left gripper (lgripper1) picks up ball7 in room4, then robot1 moves from room4 to room5, where it drops ball7 using lgripper1. Next, robot1 moves from room5 to room1, picks up ball1 with lgripper1, and simultaneously picks up ball3 with its right gripper (rgripper1) in room1. Robot1 then moves from room1 to room5, drops ball1 using lgripper1, and drops ball3 using rgripper1 in room5. From room5, robot1 proceeds to room2, where it picks up ball2 with lgripper1 and ball4 with rgripper1. Robot1 then moves from room2 to room1, drops ball4 using rgripper1, and picks up ball6 with rgripper1 in room1. Next, robot1 moves from room1 to room3, drops ball6 using rgripper1, picks up ball5 with rgripper1 in room3, and finally moves from room3 to room6 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 47? True or False", "initial_state_nl_paraphrased": "Ball1 is located at room1, ball2 is at room2, ball3 is present in room1, ball4 is present in room2, ball5 is located at room3, ball6 is at room1, ball7 is at room4, the left gripper of robot1 is free, robot1 is at room4 and the right gripper of robot1 is available."}
{"question_id": "5196c03f-6b4b-416f-b928-f8afad0ce940", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, is the number of inexecutable actions equal to 130? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is located at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot1 moves from room4 to room1 to achieve the current state. In this state, does the number of inexecutable actions amount to 130? True or False", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is situated in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, robot1 is situated in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is available."}
{"question_id": "c3255b64-443b-4935-9f21-01a7a0b3e2c8", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 15? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is located at room3, ball5 is present at room1, ball6 is located at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room2, robot1's lgripper1 is free, robot2 is located at room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot2 transitions from room2 to room3 to achieve the current state. In this state, does the count of valid properties that do not involve negations equal 15? True or False", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is in room2, ball4 is situated in room3, ball5 is in room1, ball6 is also in room1, ball7 is in room3, the right gripper of robot1 is available, robot1 is positioned in room2, the left gripper of robot1 is free, robot2 is in room2, the left gripper of robot2 is free and the right gripper of robot2 is also available."}
{"question_id": "82a329f7-9292-4b9b-bc95-a87091ff0b81", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 42? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is located at room2, ball3 is located at room1, ball4 is present at room2, ball5 is at room2, ball6 is located at room1, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot1 moves from room4 to room1 to achieve the current state. In this state, does the number of valid properties involving negations equal 42? True or False", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is situated in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, robot1 is situated in room4, robot1's left gripper, lgripper1, is available and robot1's right gripper, rgripper1, is available."}
