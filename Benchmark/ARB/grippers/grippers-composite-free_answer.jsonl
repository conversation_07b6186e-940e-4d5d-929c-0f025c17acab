{"question_id": "43a53034-5586-46e6-8a47-ce6c50e0368c", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_6_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: in room3, robot1's lgripper1 drops ball3 to reach the current state. What are the valid properties of the state that do not involve negations for lgripper1 before the first infeasible action in the sequence? Write None if there are none", "answer": "robot1's lgripper1 is free", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Considering the initial condition, the following actions are intended: in room3, robot1's lgripper1 releases ball3 to achieve the current state. What are the valid properties of the state that do not involve negations for lgripper1 prior to the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "d1248003-4831-48ad-8978-aeecd48f19f7", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_5_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1's lgripper1 picks up ball7, robot1 moves to room5 from room4, in room5, robot1's lgripper1 drops ball7, robot1 moves from room5 to room1, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, ball1 is dropped in room5 with lgripper1 by robot1, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, ball2 is picked from room2 with lgripper1 by robot1, from room2, robot1's rgripper1 picks up ball4, from room2, robot1 moves to room1, in room1, robot1's rgripper1 drops ball4, ball6 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room3, rgripper1 of robot1 drops ball6 in room3, from room6, robot1 moves to room4 and robot1 moves from room3 to room6 to reach the current state. What valid properties of the state that do not involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is located at room5, ball3 is at room5, ball4 is located at room1, ball5 is present at room3, ball6 is at room3, ball7 is present at room5, robot1 is at room3, robot1 is carrying ball2 with lgripper1 and robot1's rgripper1 is free", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: starting from room4, robot1's left gripper (lgripper1) will pick up ball7, then robot1 will move from room4 to room5, and in room5, robot1's lgripper1 will drop ball7. Next, robot1 will move from room5 to room1, where robot1's lgripper1 will pick up ball1, and robot1's right gripper (rgripper1) will pick up ball3. From room1, robot1 will move to room5, where robot1's lgripper1 will drop ball1, and robot1's rgripper1 will drop ball3. Then, robot1 will move from room5 to room2, where robot1's lgripper1 will pick up ball2, and robot1's rgripper1 will pick up ball4. From room2, robot1 will move to room1, where robot1's rgripper1 will drop ball4, and robot1's rgripper1 will pick up ball6. Next, robot1 will move from room1 to room3, where robot1's rgripper1 will drop ball6. Finally, robot1 will move from room6 to room4 and from room3 to room6 to reach the current state. What valid properties of the state that do not involve negations are true before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "314b9b10-4b05-44f8-9401-c08e2ce25c55", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_5_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 drops ball4 in room2 to reach the current state. What valid properties of the state that involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not located at room4, ball1 is not located at room5, ball1 is not present at room2, ball1 is not present at room3, ball2 is not at room5, ball2 is not being carried by robot1's lgripper1, ball2 is not located at room4, ball2 is not present at room1, ball2 is not present at room3, ball3 is not at room2, ball3 is not at room4, ball3 is not present at room3, ball3 is not present at room5, ball4 is not at room4, ball4 is not located at room3, ball4 is not present at room1, ball4 is not present at room5, ball5 is not at room1, ball5 is not at room3, ball5 is not at room5, ball5 is not being carried by robot1's rgripper1, ball5 is not present at room4, ball6 is not at room2, ball6 is not at room3, ball6 is not at room4, ball6 is not present at room5, ball7 is not at room2, ball7 is not at room5, ball7 is not present at room1, ball7 is not present at room4, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball4, robot1 is not at room5, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not located at room1, robot1 is not located at room2 and robot1 is not located at room3", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Considering the initial condition, the following actions are intended to be executed: robot1's lgripper1 releases ball4 in room2 to achieve the current state. What valid state properties involving negations hold true prior to the first infeasible action in the sequence? If none exist, state None.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "1c0ccb30-71ac-4fad-8c2e-0549cd146e4e", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_5_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room3, robot1's lgripper1 picks up ball5, robot1 moves to room5 from room4, ball7 is dropped in room5 with lgripper1 by robot1, from room5, robot1 moves to room1, lgripper1 of robot1 picks up ball1 in room1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves to room5 from room1, ball1 is dropped in room5 with lgripper1 by robot1, rgripper1 of robot1 drops ball3 in room5 and robot1 moves to room2 from room5 to reach the current state. What valid properties of the state that do not involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is present at room2, ball5 is present at room3, ball6 is at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to be executed: starting from room3, robot1's left gripper (lgripper1) grasps ball5, then robot1 relocates from room4 to room5, drops ball7 in room5 using lgripper1, proceeds to room1 from room5, picks up ball1 in room1 with lgripper1, and simultaneously picks up ball3 in room1 with its right gripper (rgripper1), then moves to room5 from room1, drops ball1 in room5 using lgripper1, and drops ball3 in room5 using rgripper1, before finally moving to room2 from room5 to attain the current state. What valid properties of the state that do not involve negations are true before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "fe86bd09-db7c-436b-a494-120f4e94d18b", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_6_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room4, robot1 moves to room5 from room4, ball7 is dropped in room5 with lgripper1 by robot1, from room5, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 picks up ball3 in room1, from room1, robot1 moves to room5, lgripper1 of robot1 drops ball1 in room5, ball5 is picked from room2 with lgripper1 by robot1 and from room5, robot1 moves to room2 to reach the current state. What are the valid properties of the state that involve negations for room3 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not present at room3, ball2 is not at room3, ball3 is not at room3, ball4 is not located at room3, ball6 is not located at room3, ball7 is not at room3 and robot1 is not at room3", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: robot1's left gripper (lgripper1) picks up ball7 in room4, then robot1 moves from room4 to room5, and subsequently drops ball7 in room5 using lgripper1. Next, robot1 moves from room5 to room1, picks up ball1 with lgripper1, and its right gripper (rgripper1) picks up ball3 in room1. Then, robot1 moves from room1 to room5, drops ball1 in room5 using lgripper1, picks up ball5 from room2 with lgripper1, and finally moves from room5 to room2 to reach the current state. What are the valid properties of the state that involve negations for room3 before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4, and robot1's right gripper is available."}
{"question_id": "82d12315-e790-43ad-bc14-a8071764e180", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_5_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball1 is dropped in room3 with lgripper1 by robot1, ball1 is picked from room3 with lgripper2 by robot2, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves from room3 to room2, lgripper2 of robot2 drops ball1 in room2, in room2, robot2's rgripper2 drops ball2, robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball4, rgripper2 of robot2 picks up ball7 in room3 and from room3, robot2 moves to room2 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is located at room3, ball1 is not at room1, ball1 is not at room2, ball1 is not being carried by robot1's lgripper1, ball1 is not being carried by robot2's rgripper2, ball2 is located at room3, ball2 is not being carried by robot1's lgripper1, ball2 is not being carried by robot2's lgripper2, ball2 is not being carried by robot2's rgripper1, ball2 is not being carried by robot2's rgripper2, ball2 is not located at room2, ball2 is not present at room1, ball3 is not at room1, ball3 is not being carried by robot1's lgripper2, ball3 is not being carried by robot2's lgripper1, ball3 is not present at room3, ball3 is present at room2, ball4 is located at room3, ball4 is not at room1, ball4 is not being carried by robot1's rgripper1, ball4 is not being carried by robot1's rgripper2, ball4 is not being carried by robot2's lgripper1, ball4 is not being carried by robot2's lgripper2, ball4 is not being carried by robot2's rgripper1, ball4 is not being carried by robot2's rgripper2, ball4 is not located at room2, ball5 is at room1, ball5 is not at room3, ball5 is not being carried by robot1's lgripper2, ball5 is not being carried by robot2's lgripper2, ball5 is not being carried by robot2's rgripper2, ball5 is not present at room2, ball6 is at room1, ball6 is not at room2, ball6 is not being carried by robot1's lgripper1, ball6 is not being carried by robot1's rgripper2, ball6 is not being carried by robot2's lgripper2, ball6 is not being carried by robot2's rgripper2, ball6 is not present at room3, ball7 is at room3, ball7 is not at room2, ball7 is not being carried by robot1's rgripper1, ball7 is not being carried by robot1's rgripper2, ball7 is not being carried by robot2's lgripper2, ball7 is not being carried by robot2's rgripper2, ball7 is not present at room1, lgripper1 of robot1 is free, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not carrying ball7, lgripper1 of robot2 is not carrying ball1, lgripper1 of robot2 is not carrying ball2, lgripper1 of robot2 is not carrying ball6, lgripper1 of robot2 is not carrying ball7, lgripper2 of robot1 is not carrying ball2, lgripper2 of robot1 is not carrying ball4, lgripper2 of robot1 is not carrying ball6, lgripper2 of robot1 is not carrying ball7, lgripper2 of robot2 is free, lgripper2 of robot2 is not carrying ball3, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball6, rgripper1 of robot2 is not carrying ball3, rgripper1 of robot2 is not carrying ball5, rgripper1 of robot2 is not carrying ball6, rgripper1 of robot2 is not carrying ball7, rgripper2 of robot1 is not carrying ball1, rgripper2 of robot1 is not carrying ball2, rgripper2 of robot1 is not carrying ball3, rgripper2 of robot1 is not carrying ball5, rgripper2 of robot2 is not carrying ball3, robot1 is at room2, robot1 is not carrying ball1 with lgripper2, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not present in room1, robot1 is not present in room3, robot1's lgripper2 is not available, robot1's rgripper1 is free, robot1's rgripper2 is not available, robot2 is not at room3, robot2 is not carrying ball1 with lgripper2, robot2 is not carrying ball1 with rgripper1, robot2 is not carrying ball5 with lgripper1, robot2 is not present in room1, robot2 is present in room2, robot2's lgripper1 is not free, robot2's rgripper1 is not available and robot2's rgripper2 is free", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: robot1 uses lgripper1 to drop ball1 in room3, then robot2 uses lgripper2 to pick up ball1 from room3, and robot2 uses rgripper2 to pick up ball2 from room3. Next, robot2 moves from room3 to room2, drops ball1 in room2 using lgripper2, and drops ball2 in room2 using rgripper2. After that, robot2 moves back to room3, picks up ball4 with lgripper2, and picks up ball7 with rgripper2 in room3. Finally, robot2 moves from room3 to room2 to reach the current state. What valid properties of the state (including both affirmative and negated properties) hold true before the first infeasible action in the sequence? If none exist, state 'None'.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "ba4a50e0-aecd-421a-b18f-1133237a8823", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_5_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball1, rgripper2 of robot2 picks up ball2 in room3, robot2 moves to room2 from room3, in room2, robot2's lgripper2 drops ball1, ball2 is dropped in room2 with rgripper2 by robot2, from room2, robot2 moves to room3, ball4 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball7 in room3, robot2 moves from room3 to room2, in room2, robot2's rgripper1 drops ball3, rgripper2 of robot2 picks up ball3 in room2, robot2 moves to room1 from room2, ball4 is dropped in room1 with lgripper2 by robot2, lgripper2 of robot2 picks up ball5 in room1, ball3 is dropped in room1 with rgripper2 by robot2, ball6 is picked from room1 with rgripper2 by robot2, robot2 moves to room2 from room1 and in room2, robot2's lgripper2 drops ball5 to reach the current state. What valid properties of the state that involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not being carried by robot1's lgripper1, ball1 is not being carried by robot1's lgripper2, ball1 is not being carried by robot2's lgripper1, ball1 is not being carried by robot2's lgripper2, ball1 is not being carried by robot2's rgripper1, ball1 is not located at room3, ball1 is not present at room1, ball2 is not at room1, ball2 is not at room3, ball2 is not being carried by robot2's lgripper1, ball3 is not being carried by robot1's lgripper2, ball3 is not being carried by robot1's rgripper2, ball3 is not being carried by robot2's rgripper2, ball3 is not present at room1, ball3 is not present at room3, ball4 is not at room2, ball4 is not located at room3, ball4 is not present at room1, ball5 is not being carried by robot1's rgripper2, ball5 is not being carried by robot2's rgripper2, ball5 is not present at room2, ball5 is not present at room3, ball6 is not at room3, ball6 is not being carried by robot1's rgripper2, ball6 is not located at room2, ball7 is not at room2, ball7 is not being carried by robot1's rgripper1, ball7 is not located at room1, ball7 is not located at room3, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball6, lgripper1 of robot1 is not carrying ball7, lgripper1 of robot2 is not carrying ball4, lgripper1 of robot2 is not carrying ball6, lgripper1 of robot2 is not carrying ball7, lgripper2 of robot1 is not carrying ball2, lgripper2 of robot1 is not carrying ball4, lgripper2 of robot1 is not carrying ball7, lgripper2 of robot1 is not free, lgripper2 of robot2 is not carrying ball3, lgripper2 of robot2 is not carrying ball5, lgripper2 of robot2 is not carrying ball7, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot2 is not carrying ball6, rgripper1 of robot2 is not carrying ball7, rgripper2 of robot1 is not carrying ball1, rgripper2 of robot1 is not carrying ball2, rgripper2 of robot1 is not carrying ball7, rgripper2 of robot2 is not carrying ball4, rgripper2 of robot2 is not free, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball4 with rgripper2, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball5 with lgripper2, robot1 is not carrying ball6 with lgripper2, robot1 is not carrying ball6 with rgripper1, robot1 is not located at room1, robot1 is not located at room3, robot1's rgripper2 is not available, robot2 is not carrying ball1 with rgripper2, robot2 is not carrying ball2 with lgripper2, robot2 is not carrying ball2 with rgripper1, robot2 is not carrying ball2 with rgripper2, robot2 is not carrying ball3 with lgripper1, robot2 is not carrying ball3 with rgripper1, robot2 is not carrying ball4 with rgripper1, robot2 is not carrying ball5 with lgripper1, robot2 is not carrying ball5 with rgripper1, robot2 is not carrying ball6 with lgripper2, robot2 is not carrying ball6 with rgripper2, robot2 is not located at room1, robot2 is not located at room3, robot2's lgripper1 is not available, robot2's lgripper2 is not available and robot2's rgripper1 is not available", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot2 moves from room2 to room3, then in room3, robot2's left gripper (lgripper2) grasps ball1, and the right gripper (rgripper2) of robot2 picks up ball2. Next, robot2 moves back to room2 from room3, and in room2, robot2's lgripper2 releases ball1, and ball2 is dropped with rgripper2 by robot2. From room2, robot2 moves to room3 again, where ball4 is picked up with lgripper2 by robot2, and rgripper2 of robot2 grasps ball7. Then, robot2 moves from room3 to room2, but in room2, robot2's rgripper1 does not drop ball3 (as it does not hold it), instead, rgripper2 of robot2 picks up ball3. After that, robot2 moves to room1 from room2, and in room1, ball4 is dropped with lgripper2 by robot2, and lgripper2 of robot2 picks up ball5. Then, ball3 is dropped in room1 with rgripper2 by robot2, and ball6 is picked from room1 with rgripper2 by robot2. Finally, robot2 moves to room2 from room1, and in room2, robot2's lgripper2 drops ball5 to reach the current state. What valid properties of the state that involve negations are true before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is currently in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "2eca5924-330b-4218-b0b7-51f1784ad40e", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_6_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, robot1 moves from room1 to room2, ball2 is picked from room2 with rgripper1 by robot1, robot1 moves to room3 from room2, ball1 is dropped in room3 with lgripper1 by robot1, robot1 moves to room4 from room3, rgripper1 of robot1 drops ball2 in room4, ball3 is picked from room4 with lgripper1 by robot1 and lgripper1 of robot1 picks up ball4 in room2 to reach the current state. What are the valid properties of the state that involve negations for room1 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not present at room1, ball2 is not present at room1, ball4 is not located at room1, ball5 is not located at room1 and robot1 is not present in room1", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: starting from room4, robot1 proceeds to room1, then robot1's left gripper (lgripper1) picks up ball1 in room1, robot1 moves from room1 to room2, robot1's right gripper (rgripper1) picks up ball2 in room2, robot1 moves from room2 to room3, robot1 drops ball1 in room3 using lgripper1, robot1 moves from room3 back to room4, robot1 drops ball2 in room4 using rgripper1, and finally, robot1 picks up ball3 in room4 with lgripper1 and also picks up ball4 in room2 using lgripper1 to reach the current state. What are the valid properties of the state involving negations for room1 before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "ee115228-7226-46d5-99a4-02eb2c62d223", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_8_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves from room1 to room2, from room2, robot1's rgripper1 picks up ball2, from room2, robot1 moves to room3, ball1 is dropped in room3 with lgripper1 by robot1, robot1 moves to room4 from room3, in room4, robot1's rgripper1 drops ball2, from room4, robot1 moves to room2, ball4 is picked from room2 with lgripper1 by robot1, ball5 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room5, lgripper1 of robot1 drops ball4 in room5, robot1 moves to room1 from room5, rgripper1 of robot1 drops ball4 in room2, rgripper1 of robot1 drops ball5 in room1, rgripper1 of robot1 picks up ball6 in room1, from room1, robot1 moves to room5 and lgripper1 of robot1 drops ball3 in room5 to reach the current state. Some of the actions may not be executable. What is the state before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not at room4, ball1 is not being carried by robot1's lgripper1, ball1 is not being carried by robot1's rgripper1, ball1 is not present at room1, ball1 is not present at room2, ball1 is not present at room5, ball1 is present at room3, ball2 is at room4, ball2 is not located at room1, ball2 is not located at room2, ball2 is not located at room5, ball2 is not present at room3, ball3 is located at room1, ball3 is not at room3, ball3 is not being carried by robot1's rgripper1, ball3 is not located at room2, ball3 is not located at room5, ball3 is not present at room4, ball4 is located at room5, ball4 is not at room1, ball4 is not at room4, ball4 is not present at room2, ball4 is not present at room3, ball5 is being carried by robot1's rgripper1, ball5 is not at room2, ball5 is not at room5, ball5 is not located at room1, ball5 is not located at room3, ball5 is not located at room4, ball6 is not at room5, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room3, ball6 is not located at room4, ball6 is not present at room2, ball6 is present at room1, lgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball6, robot1 is not at room2, robot1 is not at room3, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not located at room4, robot1 is not present in room5, robot1 is present in room1, robot1's lgripper1 is available and robot1's rgripper1 is not free", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot1 is set to move from room4 to room1, then pick up ball1 in room1 using lgripper1, followed by moving to room2, where robot1's rgripper1 will pick up ball2. Next, robot1 will proceed to room3, drop ball1 using lgripper1, and then head to room4, where rgripper1 will drop ball2. From room4, robot1 will move to room2, pick up ball4 with lgripper1 and ball5 with rgripper1, and then move to room5. In room5, lgripper1 will drop ball4, and robot1 will return to room1, where rgripper1 will drop ball5. Additionally, rgripper1 will pick up ball6 in room1, and then robot1 will move to room5 to drop ball3 using lgripper1, reaching the current state. However, some actions in the sequence may not be executable. What is the state before the first infeasible action in the sequence? If there are none, write None.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is situated in room1, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "d86f406f-99a7-468a-9603-e7edbc86d00d", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_5_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1 moves to room2, rgripper1 of robot1 picks up ball2 in room2, robot1 moves from room2 to room3, lgripper1 of robot1 drops ball1 in room3, robot1 moves to room4 from room3, rgripper1 of robot1 drops ball2 in room4, from room4, robot1 moves to room2, lgripper1 of robot1 picks up ball4 in room2, rgripper1 of robot1 picks up ball5 in room2, robot1 moves from room2 to room5, in room5, robot1's lgripper1 drops ball4, robot1 moves from room5 to room1, lgripper1 of robot1 picks up ball3 in room1, ball5 is dropped in room1 with rgripper1 by robot1, lgripper1 of robot1 picks up ball1 in room2, robot1 moves from room1 to room5 and lgripper1 of robot1 drops ball3 in room5 to reach the current state. What valid properties of the state that involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not at room1, ball1 is not at room4, ball1 is not being carried by robot1's lgripper1, ball1 is not located at room2, ball1 is not present at room5, ball2 is not at room5, ball2 is not located at room1, ball2 is not located at room2, ball2 is not located at room3, ball3 is not at room3, ball3 is not at room4, ball3 is not at room5, ball3 is not being carried by robot1's rgripper1, ball3 is not located at room1, ball3 is not located at room2, ball4 is not at room1, ball4 is not at room3, ball4 is not located at room2, ball4 is not present at room4, ball5 is not located at room3, ball5 is not located at room4, ball5 is not present at room2, ball5 is not present at room5, ball6 is not at room2, ball6 is not at room4, ball6 is not at room5, ball6 is not being carried by robot1's rgripper1, ball6 is not present at room3, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball2, robot1 is not at room4, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not located at room2, robot1 is not located at room3, robot1 is not present in room5 and robot1's lgripper1 is not free", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1 relocates from room4 to room1, the left gripper of robot1 (lgripper1) collects ball1 in room1, then robot1 proceeds from room1 to room2, the right gripper of robot1 (rgripper1) collects ball2 in room2, robot1 moves from room2 to room3, lgripper1 of robot1 releases ball1 in room3, robot1 then moves from room3 to room4, and rgripper1 of robot1 releases ball2 in room4, from room4, robot1 moves back to room2, lgripper1 of robot1 collects ball4 in room2, and rgripper1 of robot1 collects ball5 in room2, robot1 then moves from room2 to room5, in room5, robot1's lgripper1 releases ball4, robot1 moves from room5 to room1, lgripper1 of robot1 collects ball3 in room1, and ball5 is released in room1 using rgripper1 by robot1, lgripper1 of robot1 collects ball1 in room2, robot1 moves from room1 to room5, and finally, lgripper1 of robot1 releases ball3 in room5 to reach the current state. What valid properties of the state that involve negations are true before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "e6d52dee-c46c-42f8-9ff9-28b731d4aeb0", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_6_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: in room3, robot1's lgripper2 drops ball6 to reach the current state. What are the valid properties of the state that involve negations for room3 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball3 is not located at room3, ball5 is not at room3, ball6 is not at room3, robot1 is not present in room3 and robot2 is not located at room3", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled: in room3, robot1's lgripper2 releases ball6 to achieve the current state. What are the valid properties of the state involving negations for room3 prior to the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "d14712e5-6376-4a6b-8045-a13e82697162", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_6_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, from room1, robot1's lgripper1 picks up ball1, robot1 moves from room1 to room2, rgripper1 of robot1 picks up ball2 in room2, from room2, robot1 moves to room3, from room1, robot1's lgripper1 picks up ball6, from room3, robot1 moves to room4, rgripper1 of robot1 drops ball2 in room4, from room4, robot1 moves to room2 and from room2, robot1's lgripper1 picks up ball4 to reach the current state. What are the valid properties of the state that do not involve negations for room3 before the first infeasible action in the sequence? Write None if there are none", "answer": "robot1 is present in room3", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1 relocates from room4 to room1, then robot1's left gripper (lgripper1) grasps ball1 in room1, robot1 proceeds from room1 to room2, robot1's right gripper (rgripper1) picks up ball2 in room2, robot1 then moves from room2 to room3, robot1's lgripper1 retrieves ball6 from room1, robot1 moves from room3 back to room4, robot1's rgripper1 releases ball2 in room4, robot1 then moves from room4 to room2, and finally robot1's lgripper1 picks up ball4 in room2 to attain the current state. What are the valid properties of the state that do not involve negations for room3 before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "fb4dbea4-08ca-4107-8504-cf96a2ab4753", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_5_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room1, robot2's rgripper1 picks up ball4 to reach the current state. What valid properties of the state that do not involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is at room3, ball2 is at room3, ball3 is located at room2, ball4 is present at room3, ball5 is present at room1, ball6 is at room1, ball7 is located at room3, lgripper2 of robot2 is free, rgripper1 of robot1 is free, rgripper2 of robot2 is free, robot1 is located at room2, robot1's lgripper1 is free and robot2 is located at room2", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are intended: robot2's rgripper1 in room1 is set to pick up ball4 to achieve the current state. What non-negated properties of the state are valid before the first infeasible action in the sequence occurs? If there are no such properties, state None.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is currently in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "054d3dad-55bf-408a-b3f1-a8fa17014a9f", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_5_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball7 is picked from room4 with lgripper1 by robot1, robot1 moves to room5 from room4, ball7 is dropped in room5 with lgripper1 by robot1, robot1 moves to room1 from room5, lgripper1 of robot1 drops ball3 in room6, rgripper1 of robot1 picks up ball3 in room1, robot1 moves to room5 from room1, lgripper1 of robot1 drops ball1 in room5, in room5, robot1's rgripper1 drops ball3, from room5, robot1 moves to room2, from room2, robot1's lgripper1 picks up ball2, from room2, robot1's rgripper1 picks up ball4, robot1 moves to room1 from room2, ball4 is dropped in room1 with rgripper1 by robot1, ball6 is picked from room1 with rgripper1 by robot1, robot1 moves to room3 from room1, ball6 is dropped in room3 with rgripper1 by robot1, ball5 is picked from room3 with rgripper1 by robot1 and robot1 moves to room6 from room3 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is at room1, ball1 is not at room4, ball1 is not at room5, ball1 is not at room6, ball1 is not being carried by robot1's lgripper1, ball1 is not located at room3, ball1 is not present at room2, ball2 is located at room2, ball2 is not at room6, ball2 is not being carried by robot1's lgripper1, ball2 is not located at room3, ball2 is not present at room1, ball2 is not present at room4, ball2 is not present at room5, ball3 is located at room1, ball3 is not at room3, ball3 is not being carried by robot1's rgripper1, ball3 is not located at room5, ball3 is not present at room2, ball3 is not present at room4, ball3 is not present at room6, ball4 is at room2, ball4 is not at room4, ball4 is not at room5, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room1, ball4 is not located at room6, ball4 is not present at room3, ball5 is located at room3, ball5 is not at room2, ball5 is not at room5, ball5 is not being carried by robot1's lgripper1, ball5 is not located at room1, ball5 is not present at room4, ball5 is not present at room6, ball6 is not at room2, ball6 is not being carried by robot1's rgripper1, ball6 is not located at room4, ball6 is not present at room3, ball6 is not present at room5, ball6 is not present at room6, ball6 is present at room1, ball7 is not being carried by robot1's rgripper1, ball7 is not located at room2, ball7 is not located at room6, ball7 is not present at room1, ball7 is not present at room3, ball7 is not present at room4, ball7 is present at room5, lgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball2, robot1 is at room1, robot1 is not at room2, robot1 is not at room5, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not located at room3, robot1 is not located at room4, robot1 is not located at room6, robot1's lgripper1 is free and robot1's rgripper1 is available", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to be executed: robot1 uses lgripper1 to pick up ball7 in room4, then moves from room4 to room5, drops ball7 in room5 using lgripper1, proceeds to room1 from room5, uses lgripper1 to drop ball3 in room6, picks up ball3 in room1 with rgripper1, moves from room1 to room5, drops ball1 in room5 using lgripper1, drops ball3 in room5 with rgripper1, moves from room5 to room2, picks up ball2 in room2 with lgripper1, picks up ball4 in room2 with rgripper1, moves from room2 to room1, drops ball4 in room1 using rgripper1, picks up ball6 in room1 with rgripper1, moves from room1 to room3, drops ball6 in room3 using rgripper1, picks up ball5 in room3 with rgripper1, and finally moves from room3 to room6 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "b5ca854c-1d96-4e9a-b0d4-afd14515229d", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_6_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball4 in room5 to reach the current state. What are the valid properties of the state (both with and without negations) for room2 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not located at room2, ball2 is present at room2, ball3 is not located at room2, ball4 is at room2, ball5 is at room2, ball6 is not at room2 and robot1 is not located at room2", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are intended to be executed: the left gripper of robot1 is to grasp ball4 in room5 to achieve the current state. What are the valid properties (including both affirmative and negated properties) of room2 prior to the first infeasible action in the sequence? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "959f5956-4603-47c5-9573-25c04c9ca56c", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_8_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, from room1, robot1's lgripper1 picks up ball1, robot1 moves from room1 to room2, rgripper1 of robot1 picks up ball2 in room2, robot1 moves from room2 to room3, in room3, robot1's lgripper1 drops ball1, robot1 moves to room4 from room3, in room4, robot1's rgripper1 drops ball3, robot1 moves from room4 to room2 and ball4 is picked from room2 with lgripper1 by robot1 to reach the current state. Some of the actions may not be executable. What is the state before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is located at room3, ball1 is not at room2, ball1 is not being carried by robot1's rgripper1, ball1 is not located at room4, ball1 is not present at room1, ball1 is not present at room5, ball2 is not at room1, ball2 is not at room3, ball2 is not at room4, ball2 is not at room5, ball2 is not present at room2, ball3 is located at room1, ball3 is not located at room3, ball3 is not located at room4, ball3 is not located at room5, ball3 is not present at room2, ball4 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room3, ball4 is not located at room5, ball4 is not present at room1, ball4 is not present at room4, ball4 is present at room2, ball5 is not located at room3, ball5 is not present at room1, ball5 is not present at room4, ball5 is not present at room5, ball5 is present at room2, ball6 is not at room2, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room3, ball6 is not present at room4, ball6 is not present at room5, ball6 is present at room1, lgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball3, robot1 is at room4, robot1 is carrying ball2 with rgripper1, robot1 is not at room1, robot1 is not at room2, robot1 is not at room3, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not present in room5, robot1's lgripper1 is free and robot1's rgripper1 is not free", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot1 is set to move from room4 to room1, then from room1, robot1's left gripper (lgripper1) will grasp ball1, followed by robot1 moving from room1 to room2, where robot1's right gripper (rgripper1) will pick up ball2, then robot1 will proceed from room2 to room3, in room3, robot1's lgripper1 will release ball1, after which robot1 will move from room3 to room4, and in room4, robot1's rgripper1 will drop ball3, then robot1 will move from room4 to room2, and finally, robot1's lgripper1 will pick up ball4 in room2 to achieve the current state. However, some actions in this sequence may not be feasible. What is the state prior to the first infeasible action in the sequence, or None if no such action exists?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "d2698a4f-ecec-4a42-a59d-39ddb6036138", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_6_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room4, robot1 moves to room5 from room4, ball7 is dropped in room5 with lgripper1 by robot1, robot1 moves to room1 from room5, lgripper1 of robot1 picks up ball1 in room1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves from room1 to room5, in room5, robot1's lgripper1 drops ball1, robot1 moves from room3 to room5 and robot1 moves from room5 to room2 to reach the current state. What are the valid properties of the state that do not involve negations for ball6 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball6 is present at room1", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: robot1's left gripper (lgripper1) will grasp ball7 in room4, then robot1 will move from room4 to room5, and subsequently drop ball7 in room5 using lgripper1. Next, robot1 will move from room5 to room1, where lgripper1 will pick up ball1 and the right gripper (rgripper1) will pick up ball3. Then, robot1 will move from room1 to room5, drop ball1 in room5 using lgripper1, move from room3 to room5, and finally move from room5 to room2 to reach the current state. What are the valid properties of the state that do not involve negations for ball6 before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "280e830d-326b-4af3-a707-2bb87fc692a3", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_5_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves from room1 to room2, from room2, robot1's rgripper1 picks up ball2, robot1 moves to room3 from room2, ball1 is dropped in room3 with lgripper1 by robot1, robot1 moves to room4 from room3, in room4, robot1's rgripper1 drops ball2, robot1 moves from room4 to room2 and ball3 is dropped in room4 with lgripper1 by robot1 to reach the current state. What valid properties of the state that involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not at room1, ball1 is not at room4, ball1 is not being carried by robot1's lgripper1, ball1 is not located at room2, ball1 is not located at room5, ball2 is not at room3, ball2 is not at room5, ball2 is not being carried by robot1's lgripper1, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room1, ball2 is not present at room2, ball3 is not at room3, ball3 is not at room4, ball3 is not located at room2, ball3 is not present at room5, ball4 is not being carried by robot1's rgripper1, ball4 is not present at room1, ball4 is not present at room3, ball4 is not present at room4, ball4 is not present at room5, ball5 is not at room4, ball5 is not located at room1, ball5 is not located at room5, ball5 is not present at room3, ball6 is not at room4, ball6 is not being carried by robot1's rgripper1, ball6 is not located at room5, ball6 is not present at room2, ball6 is not present at room3, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not carrying ball3, robot1 is not at room3, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not located at room5, robot1 is not present in room1 and robot1 is not present in room4", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1 relocates from room4 to room1, robot1 uses lgripper1 to grasp ball1 in room1, robot1 proceeds from room1 to room2, then robot1's rgripper1 retrieves ball2 in room2, robot1 moves from room2 to room3, robot1 releases ball1 in room3 using lgripper1, robot1 moves from room3 to room4, in room4, robot1's rgripper1 releases ball2, robot1 moves from room4 to room2, and robot1 drops ball3 in room4 using lgripper1 to reach the current state. What valid properties of the state that involve negations are true before the first infeasible action in the sequence? Write None if there are none", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "9f4848e1-f0cb-4cd9-8106-c5b973869306", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_5_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, lgripper1 of robot1 picks up ball1 in room1, robot1 moves from room1 to room2, from room2, robot1's rgripper1 picks up ball2, from room2, robot1 moves to room3, lgripper1 of robot1 drops ball1 in room3, robot1 moves from room3 to room4, rgripper1 of robot1 drops ball2 in room4, from room4, robot1 moves to room2, ball4 is picked from room2 with lgripper1 by robot1, rgripper1 of robot1 picks up ball5 in room2, robot1 moves from room2 to room5, lgripper1 of robot1 picks up ball1 in room3, robot1 moves from room5 to room1, ball3 is picked from room1 with lgripper1 by robot1, in room1, robot1's rgripper1 drops ball5, from room1, robot1's rgripper1 picks up ball6, robot1 moves to room5 from room1 and lgripper1 of robot1 drops ball3 in room5 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not at room1, ball1 is not being carried by robot1's lgripper1, ball1 is not being carried by robot1's rgripper1, ball1 is not located at room5, ball1 is not present at room2, ball1 is not present at room4, ball1 is present at room3, ball2 is located at room4, ball2 is not at room2, ball2 is not located at room3, ball2 is not present at room1, ball2 is not present at room5, ball3 is at room1, ball3 is not located at room3, ball3 is not located at room4, ball3 is not present at room2, ball3 is not present at room5, ball4 is being carried by robot1's lgripper1, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room1, ball4 is not located at room2, ball4 is not located at room3, ball4 is not located at room4, ball4 is not located at room5, ball5 is not at room1, ball5 is not at room2, ball5 is not at room4, ball5 is not at room5, ball5 is not being carried by robot1's lgripper1, ball5 is not located at room3, ball6 is not at room2, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room4, ball6 is not present at room3, ball6 is not present at room5, ball6 is present at room1, lgripper1 of robot1 is not free, rgripper1 of robot1 is carrying ball5, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not free, robot1 is not at room4, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not located at room3, robot1 is not present in room1, robot1 is not present in room2 and robot1 is present in room5", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1 relocates from room4 to room1, then its left gripper (lgripper1) collects ball1 in room1. Next, robot1 proceeds from room1 to room2, where its right gripper (rgripper1) picks up ball2. From room2, robot1 moves to room3, and in room3, lgripper1 of robot1 releases ball1. Then, robot1 moves from room3 back to room4, and in room4, rgripper1 of robot1 releases ball2. Subsequently, robot1 returns to room2 from room4, and in room2, lgripper1 of robot1 collects ball4, while rgripper1 of robot1 picks up ball5. Robot1 then moves from room2 to room5, but before that, it goes back to room3 to pick up ball1 with lgripper1. After that, robot1 moves from room5 to room1, where it collects ball3 with lgripper1. In room1, rgripper1 of robot1 drops ball5, and then picks up ball6. Finally, robot1 moves from room1 to room5, where lgripper1 of robot1 releases ball3 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "ad0bbf66-0ff0-44d3-8ef3-68b748dee331", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_6_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: in room5, robot1's lgripper1 drops ball5 to reach the current state. What are the valid properties of the state that do not involve negations for ball1 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is present at room1", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled: in room5, robot1's lgripper1 releases ball5 to achieve the current state. What are the valid properties of the state that do not involve negations for ball1 prior to the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "b176fbf5-536d-49f7-9d21-4e9cd2173940", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_5_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: rgripper1 of robot1 picks up ball6 in room5, robot1 moves to room5 from room4, lgripper1 of robot1 drops ball7 in room5, from room5, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, from room1, robot1's rgripper1 picks up ball3, robot1 moves to room5 from room1, lgripper1 of robot1 drops ball1 in room5, ball3 is dropped in room5 with rgripper1 by robot1 and from room5, robot1 moves to room2 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is located at room1, ball1 is not at room3, ball1 is not at room6, ball1 is not located at room5, ball1 is not present at room2, ball1 is not present at room4, ball2 is at room2, ball2 is not at room3, ball2 is not at room4, ball2 is not at room5, ball2 is not located at room1, ball2 is not located at room6, ball3 is located at room1, ball3 is not at room5, ball3 is not located at room4, ball3 is not present at room2, ball3 is not present at room3, ball3 is not present at room6, ball4 is located at room2, ball4 is not at room5, ball4 is not located at room4, ball4 is not present at room1, ball4 is not present at room3, ball4 is not present at room6, ball5 is not at room2, ball5 is not at room6, ball5 is not being carried by robot1's rgripper1, ball5 is not located at room1, ball5 is not present at room4, ball5 is not present at room5, ball5 is present at room3, ball6 is at room1, ball6 is not at room2, ball6 is not at room3, ball6 is not at room6, ball6 is not present at room4, ball6 is not present at room5, ball7 is located at room4, ball7 is not at room1, ball7 is not at room3, ball7 is not at room6, ball7 is not located at room5, ball7 is not present at room2, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball6, robot1 is located at room4, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not located at room1, robot1 is not located at room2, robot1 is not located at room3, robot1 is not located at room5, robot1 is not present in room6, robot1's lgripper1 is free and robot1's rgripper1 is available", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: robot1's right gripper (rgripper1) retrieves ball6 in room5, robot1 relocates from room4 to room5, robot1's left gripper (lgripper1) releases ball7 in room5, then robot1 proceeds from room5 to room1, in room1, robot1's lgripper1 picks up ball1, and in room1, robot1's rgripper1 picks up ball3, robot1 then moves from room1 to room5, in room5, robot1's lgripper1 releases ball1, and robot1's rgripper1 releases ball3 in room5, and finally, robot1 moves from room5 to room2 to attain the current state. What valid properties of the state (both with and without negations) hold true before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "163400a2-d5df-4275-ac19-56bfabe02e11", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_6_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room2, robot2 moves to room3, lgripper2 of robot2 picks up ball1 in room3, from room3, robot2's rgripper2 picks up ball2, robot2 moves from room3 to room2, lgripper2 of robot2 drops ball1 in room2, ball2 is dropped in room2 with rgripper2 by robot2, ball2 is dropped in room1 with lgripper1 by robot1, ball4 is picked from room3 with lgripper2 by robot2, ball7 is picked from room3 with rgripper2 by robot2 and robot2 moves from room3 to room2 to reach the current state. What are the valid properties of the state that do not involve negations for lgripper2 before the first infeasible action in the sequence? Write None if there are none", "answer": "robot2's lgripper2 is free", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: robot2 will move from room2 to room3, then use its left gripper (lgripper2) to pick up ball1 in room3, followed by its right gripper (rgripper2) picking up ball2 in room3, then robot2 will move back to room2, where it will drop ball1 using lgripper2 and ball2 using rgripper2, next, robot1 will drop ball2 in room1 using its left gripper (lgripper1), then robot2 will pick up ball4 and ball7 from room3 using lgripper2 and rgripper2 respectively, and finally move back to room2 to reach the current state. What are the valid properties of the state that do not involve negations for lgripper2 before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "0a43b7e0-948d-4eed-ae74-c6611ab21005", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_8_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1's rgripper1 picks up ball6 to reach the current state. Some of the actions may not be executable. What is the state before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not at room2, ball1 is not at room3, ball1 is not at room5, ball1 is not located at room4, ball1 is present at room1, ball2 is located at room2, ball2 is not at room1, ball2 is not located at room3, ball2 is not located at room4, ball2 is not located at room5, ball3 is at room1, ball3 is not at room2, ball3 is not at room5, ball3 is not present at room3, ball3 is not present at room4, ball4 is located at room2, ball4 is not at room4, ball4 is not located at room1, ball4 is not located at room5, ball4 is not present at room3, ball5 is located at room2, ball5 is not at room3, ball5 is not being carried by robot1's lgripper1, ball5 is not located at room4, ball5 is not located at room5, ball5 is not present at room1, ball6 is at room1, ball6 is not at room3, ball6 is not at room5, ball6 is not being carried by robot1's rgripper1, ball6 is not present at room2, ball6 is not present at room4, ball7 is not at room5, ball7 is not being carried by robot1's lgripper1, ball7 is not being carried by robot1's rgripper1, ball7 is not located at room4, ball7 is not present at room1, ball7 is not present at room2, ball7 is present at room3, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball4, robot1 is at room4, robot1 is not at room1, robot1 is not at room3, robot1 is not at room5, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not present in room2, robot1's lgripper1 is free and robot1's rgripper1 is free", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are intended to be executed: starting from room4, robot1's rgripper1 is supposed to pick up ball6 to achieve the current state. However, some actions in the sequence may not be feasible. What is the state preceding the first infeasible action in the sequence? If there are no infeasible actions, write None.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "86f728d0-d646-432f-9591-38af0f4969c9", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_6_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room4, robot1 moves from room4 to room5, in room5, robot1's lgripper1 drops ball7, robot1 moves to room1 from room5, ball1 is picked from room1 with lgripper1 by robot1, lgripper1 of robot1 drops ball7 in room6, robot1 moves to room5 from room1, ball1 is dropped in room5 with lgripper1 by robot1, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, lgripper1 of robot1 picks up ball2 in room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves to room1 from room2, rgripper1 of robot1 drops ball4 in room1, from room1, robot1's rgripper1 picks up ball6, from room1, robot1 moves to room3, rgripper1 of robot1 drops ball6 in room3, ball5 is picked from room3 with rgripper1 by robot1 and from room3, robot1 moves to room6 to reach the current state. What are the valid properties of the state that involve negations for rgripper1 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball2 is not being carried by robot1's rgripper1, ball5 is not being carried by robot1's rgripper1, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball7, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with rgripper1 and robot1 is not carrying ball6 with rgripper1", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1's left gripper (lgripper1) picks up ball7 in room4, then robot1 moves to room5 from room4, and in room5, robot1's lgripper1 releases ball7. Next, robot1 moves to room1 from room5, picks up ball1 with lgripper1 in room1, and then drops ball7 in room6 using lgripper1. After that, robot1 moves to room5 from room1, drops ball1 in room5 with lgripper1, and robot1's right gripper (rgripper1) releases ball3 in room5. Then, robot1 moves to room2 from room5, picks up ball2 with lgripper1 and ball4 with rgripper1 in room2, and moves to room1 from room2. In room1, rgripper1 of robot1 drops ball4, picks up ball6, and then moves to room3. In room3, rgripper1 of robot1 drops ball6 and picks up ball5, and finally, robot1 moves to room6 to reach the current state. What are the valid properties of the state that involve negations for rgripper1 before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4, and robot1's right gripper is available."}
{"question_id": "71154a61-c953-416c-b681-9fbd28e4c593", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_6_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: in room1, robot1's lgripper1 drops ball4 to reach the current state. What are the valid properties of the state (both with and without negations) for ball6 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball6 is not at room2, ball6 is not being carried by robot1's lgripper1, ball6 is not being carried by robot1's rgripper1, ball6 is not being carried by robot1's rgripper2, ball6 is not being carried by robot2's rgripper2, ball6 is not located at room3, ball6 is present at room1, lgripper1 of robot2 is not carrying ball6, lgripper2 of robot1 is not carrying ball6, robot2 is not carrying ball6 with lgripper2 and robot2 is not carrying ball6 with rgripper1", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled: in room1, robot1's lgripper1 releases ball4 to achieve the current state. What are the valid properties of the state (including both affirmative and negated properties) for ball6 prior to the first infeasible action in the sequence? If there are no valid properties, please state None.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "9dbb51c1-582a-418e-8e02-5a8cb7c8e370", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_8_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, lgripper1 of robot1 picks up ball1 in room1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves to room5 from room1, rgripper1 of robot1 drops ball3 in room5, robot1 moves to room2 from room5, from room2, robot1's rgripper1 picks up ball4, robot1 moves to room1 from room2, ball4 is dropped in room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball6, from room1, robot1 moves to room3, in room3, robot1's rgripper1 drops ball6, from room3, robot1's rgripper1 picks up ball7, from room3, robot1 moves to room4, in room4, robot1's lgripper1 drops ball1, ball7 is dropped in room4 with rgripper1 by robot1, from room4, robot1 moves to room2, lgripper1 of robot1 picks up ball2 in room2 and rgripper1 of robot1 picks up ball5 in room2 to reach the current state. Some of the actions may not be executable. What is the state before the first infeasible action in the sequence? Write None if there are none", "answer": "None", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot1 relocates from room4 to room1, then uses its left gripper (lgripper1) to grasp ball1 in room1, and its right gripper (rgripper1) to grasp ball3 in room1. Next, robot1 moves from room1 to room5, drops ball3 in room5 using rgripper1, and then proceeds to room2. In room2, robot1's rgripper1 picks up ball4, and the robot returns to room1, where it drops ball4 using rgripper1. The robot then uses rgripper1 to pick up ball6 in room1, moves to room3, and drops ball6 in room3. In room3, robot1's rgripper1 picks up ball7, and the robot moves to room4, where it drops ball1 using lgripper1 and ball7 using rgripper1. Finally, robot1 moves to room2, where it picks up ball2 with lgripper1 and ball5 with rgripper1 to reach the current state. However, some actions in this sequence may not be executable. What is the state before the first infeasible action in the sequence? If there are no infeasible actions, write None.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is positioned in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "885788d8-6bda-47ab-a56d-b24c5fa741ac", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_5_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 drops ball6 in room2 to reach the current state. What valid properties of the state that involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not at room2, ball1 is not at room4, ball1 is not at room5, ball1 is not being carried by robot1's lgripper1, ball1 is not being carried by robot1's rgripper1, ball1 is not present at room3, ball2 is not at room1, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room4, ball2 is not located at room5, ball2 is not present at room3, ball3 is not at room2, ball3 is not present at room3, ball3 is not present at room4, ball3 is not present at room5, ball4 is not at room1, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room3, ball4 is not located at room4, ball4 is not located at room5, ball5 is not being carried by robot1's lgripper1, ball5 is not located at room5, ball5 is not present at room1, ball5 is not present at room3, ball5 is not present at room4, ball6 is not being carried by robot1's rgripper1, ball6 is not located at room2, ball6 is not located at room4, ball6 is not located at room5, ball6 is not present at room3, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball3, robot1 is not at room2, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not present in room1, robot1 is not present in room3 and robot1 is not present in room5", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are intended to be executed: robot1's lgripper1 releases ball6 in room2 to achieve the current state. What valid state properties involving negations hold true prior to the first infeasible action in the sequence? If there are none, state None.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "a3ff1384-06aa-4f66-971d-760036e1aa8a", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_6_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball5 is dropped in room2 with lgripper1 by robot1 to reach the current state. What are the valid properties of the state that involve negations for room6 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not at room6, ball2 is not present at room6, ball3 is not located at room6, ball4 is not present at room6, ball5 is not present at room6, ball6 is not located at room6, ball7 is not present at room6 and robot1 is not present in room6", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are intended: robot1 uses lgripper1 to drop ball5 in room2 to achieve the current state. What are the valid state properties involving negations for room6 prior to the first infeasible action in the sequence? If none exist, please state None.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "4dfa29c6-3928-45fe-a6fe-a4d50df5c749", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_5_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, from room1, robot1's lgripper1 picks up ball1, robot1 moves to room2 from room1, ball2 is picked from room2 with rgripper1 by robot1, from room2, robot1 moves to room3, in room3, robot1's lgripper1 drops ball1, robot1 moves from room3 to room4, ball2 is dropped in room4 with rgripper1 by robot1, from room1, robot1's lgripper1 picks up ball1 and lgripper1 of robot1 picks up ball4 in room2 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is at room3, ball1 is not at room2, ball1 is not being carried by robot1's rgripper1, ball1 is not located at room1, ball1 is not present at room4, ball1 is not present at room5, ball2 is not at room5, ball2 is not located at room2, ball2 is not present at room1, ball2 is not present at room3, ball2 is present at room4, ball3 is located at room1, ball3 is not at room3, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's rgripper1, ball3 is not located at room2, ball3 is not located at room4, ball3 is not present at room5, ball4 is at room2, ball4 is not at room5, ball4 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room1, ball4 is not located at room4, ball4 is not present at room3, ball5 is at room2, ball5 is not at room4, ball5 is not being carried by robot1's lgripper1, ball5 is not being carried by robot1's rgripper1, ball5 is not located at room1, ball5 is not present at room3, ball5 is not present at room5, ball6 is at room1, ball6 is not at room2, ball6 is not at room3, ball6 is not at room4, ball6 is not at room5, ball6 is not being carried by robot1's rgripper1, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball2, robot1 is at room4, robot1 is not at room1, robot1 is not at room5, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not located at room2, robot1 is not present in room3, robot1's lgripper1 is available and robot1's rgripper1 is free", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1 relocates from room4 to room1, then robot1's left gripper (lgripper1) grasps ball1 in room1, robot1 proceeds to room2 from room1, and in room2, robot1's right gripper (rgripper1) picks up ball2, robot1 then moves from room2 to room3, in room3, robot1's lgripper1 releases ball1, robot1 moves from room3 back to room4, and in room4, robot1's rgripper1 drops ball2, and from room1, robot1's lgripper1 picks up ball1 again and also picks up ball4 in room2 using its lgripper1 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present at room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "f77f16ce-1acd-4c7f-bfab-09a28d25038d", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_5_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room3 to room4 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is at room1, ball1 is not at room2, ball1 is not located at room3, ball1 is not located at room4, ball1 is not located at room5, ball2 is located at room2, ball2 is not at room1, ball2 is not at room3, ball2 is not at room5, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room4, ball3 is located at room1, ball3 is not being carried by robot1's rgripper1, ball3 is not located at room2, ball3 is not located at room4, ball3 is not present at room3, ball3 is not present at room5, ball4 is at room2, ball4 is not at room3, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room1, ball4 is not present at room4, ball4 is not present at room5, ball5 is not at room1, ball5 is not at room4, ball5 is not at room5, ball5 is not being carried by robot1's lgripper1, ball5 is not located at room3, ball5 is present at room2, ball6 is located at room1, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room2, ball6 is not located at room4, ball6 is not present at room3, ball6 is not present at room5, lgripper1 of robot1 is free, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball6, robot1 is at room4, robot1 is not carrying ball3 with lgripper1, robot1 is not located at room1, robot1 is not located at room3, robot1 is not located at room5 and robot1 is not present in room2", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are intended: robot1 will move from room3 to room4 to achieve the current state. What valid state properties (including both affirmative and negated properties) hold true prior to the first infeasible action in the sequence? If none exist, please state None.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "43d36e28-7248-4031-a45d-2bf9de3cd7fc", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_5_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball5, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room5, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves to room2 from room5, from room2, robot1's rgripper1 picks up ball4, from room2, robot1 moves to room1, rgripper1 of robot1 drops ball4 in room1 and from room1, robot1's rgripper1 picks up ball6 to reach the current state. What valid properties of the state that involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not at room5, ball1 is not being carried by robot1's lgripper1, ball1 is not being carried by robot1's rgripper1, ball1 is not located at room2, ball1 is not located at room3, ball1 is not located at room4, ball2 is not at room3, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room1, ball2 is not located at room5, ball2 is not present at room4, ball3 is not at room4, ball3 is not at room5, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's rgripper1, ball3 is not located at room3, ball3 is not present at room2, ball4 is not located at room4, ball4 is not present at room1, ball4 is not present at room3, ball4 is not present at room5, ball5 is not at room1, ball5 is not being carried by robot1's lgripper1, ball5 is not located at room4, ball5 is not located at room5, ball5 is not present at room3, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room2, ball6 is not located at room3, ball6 is not located at room4, ball6 is not located at room5, ball7 is not at room1, ball7 is not at room4, ball7 is not at room5, ball7 is not located at room2, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball7, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball6, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not located at room3, robot1 is not located at room4, robot1 is not located at room5 and robot1 is not present in room2", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: starting from room4, robot1 proceeds to room1, then robot1's left gripper (lgripper1) picks up ball5 in room1, and simultaneously, robot1's right gripper (rgripper1) picks up ball3 in room1. Next, robot1 moves from room1 to room5, drops ball3 in room5 using rgripper1, and then moves to room2. In room2, robot1's rgripper1 picks up ball4, and then robot1 moves back to room1, where rgripper1 drops ball4. Finally, in room1, robot1's rgripper1 picks up ball6 to reach the current state. What valid properties of the state that involve negations are true before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "f58a5d21-8d0c-4b6b-8ca2-9ddbfdd16a99", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_5_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room2, robot2 moves to room3, from room3, robot2's lgripper2 picks up ball1, rgripper2 of robot2 picks up ball2 in room3, robot2 moves from room3 to room2, ball1 is dropped in room2 with lgripper2 by robot2, in room2, robot2's rgripper2 drops ball2, robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball4, rgripper2 of robot2 picks up ball7 in room3 and in room1, robot2's lgripper2 drops ball2 to reach the current state. What valid properties of the state that involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not being carried by robot1's lgripper1, ball1 is not present at room1, ball1 is not present at room3, ball2 is not being carried by robot2's lgripper1, ball2 is not being carried by robot2's rgripper1, ball2 is not being carried by robot2's rgripper2, ball2 is not located at room1, ball2 is not located at room3, ball3 is not at room1, ball3 is not being carried by robot2's lgripper1, ball3 is not being carried by robot2's lgripper2, ball3 is not being carried by robot2's rgripper1, ball3 is not being carried by robot2's rgripper2, ball3 is not located at room3, ball4 is not at room2, ball4 is not at room3, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room1, ball5 is not at room2, ball5 is not at room3, ball5 is not being carried by robot1's rgripper1, ball5 is not being carried by robot2's lgripper1, ball6 is not at room3, ball6 is not being carried by robot1's lgripper1, ball6 is not being carried by robot1's rgripper1, ball6 is not being carried by robot2's lgripper1, ball6 is not being carried by robot2's rgripper1, ball6 is not present at room2, ball7 is not at room1, ball7 is not at room3, ball7 is not being carried by robot1's lgripper1, ball7 is not being carried by robot2's lgripper1, ball7 is not located at room2, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot2 is not carrying ball1, lgripper1 of robot2 is not carrying ball4, lgripper1 of robot2 is not free, lgripper2 of robot1 is not carrying ball3, lgripper2 of robot1 is not carrying ball4, lgripper2 of robot1 is not carrying ball5, lgripper2 of robot1 is not carrying ball6, lgripper2 of robot1 is not carrying ball7, lgripper2 of robot1 is not free, lgripper2 of robot2 is not carrying ball1, lgripper2 of robot2 is not carrying ball2, lgripper2 of robot2 is not carrying ball6, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot2 is not carrying ball4, rgripper1 of robot2 is not carrying ball5, rgripper1 of robot2 is not free, rgripper2 of robot1 is not carrying ball1, rgripper2 of robot1 is not carrying ball3, rgripper2 of robot1 is not carrying ball4, rgripper2 of robot1 is not carrying ball6, rgripper2 of robot2 is not carrying ball1, rgripper2 of robot2 is not carrying ball5, rgripper2 of robot2 is not free, robot1 is not carrying ball1 with lgripper2, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball2 with lgripper2, robot1 is not carrying ball2 with rgripper2, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball5 with rgripper2, robot1 is not carrying ball7 with rgripper1, robot1 is not carrying ball7 with rgripper2, robot1 is not located at room1, robot1 is not located at room3, robot1's rgripper2 is not free, robot2 is not at room1, robot2 is not carrying ball1 with rgripper1, robot2 is not carrying ball4 with rgripper2, robot2 is not carrying ball5 with lgripper2, robot2 is not carrying ball6 with rgripper2, robot2 is not carrying ball7 with lgripper2, robot2 is not carrying ball7 with rgripper1, robot2 is not present in room2 and robot2's lgripper2 is not free", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot2 moves from room2 to room3, then in room3, robot2's left gripper (lgripper2) picks up ball1 and its right gripper (rgripper2) picks up ball2, after which robot2 moves back to room2, drops ball1 with lgripper2, and drops ball2 with rgripper2 in room2, then robot2 moves to room3 again, picks up ball4 with lgripper2 and ball7 with rgripper2 in room3, and finally, in room1, robot2's lgripper2 drops ball2 to reach the current state. What valid properties of the state that involve negations are true before the first infeasible action in the sequence? Write None if there are none", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "92cc480a-f037-48bd-a3e2-b1be3a6174bc", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_5_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: in room5, robot1's rgripper1 drops ball4 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is at room1, ball1 is not at room2, ball1 is not at room5, ball1 is not present at room3, ball1 is not present at room4, ball2 is not at room5, ball2 is not being carried by robot1's lgripper1, ball2 is not located at room4, ball2 is not present at room1, ball2 is not present at room3, ball2 is present at room2, ball3 is at room1, ball3 is not at room4, ball3 is not located at room3, ball3 is not present at room2, ball3 is not present at room5, ball4 is at room2, ball4 is not at room3, ball4 is not present at room1, ball4 is not present at room4, ball4 is not present at room5, ball5 is at room2, ball5 is not at room5, ball5 is not being carried by robot1's lgripper1, ball5 is not located at room1, ball5 is not located at room4, ball5 is not present at room3, ball6 is not at room5, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room3, ball6 is not present at room2, ball6 is not present at room4, ball6 is present at room1, ball7 is located at room3, ball7 is not at room4, ball7 is not being carried by robot1's lgripper1, ball7 is not present at room1, ball7 is not present at room2, ball7 is not present at room5, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball5, robot1 is located at room4, robot1 is not at room2, robot1 is not at room3, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not located at room5, robot1 is not present in room1, robot1's lgripper1 is free and robot1's rgripper1 is free", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Considering the initial condition, the following actions are intended to be executed: in room5, robot1's rgripper1 releases ball4 to achieve the current state. What valid properties of the state (including both affirmative and negated properties) hold true prior to the first infeasible action in the sequence? If there are no such properties, state None.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "ebba56a1-d390-42f5-8103-c7dff36fbecb", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_6_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves to room3 from room2, lgripper2 of robot2 picks up ball1 in room3, lgripper1 of robot1 picks up ball2 in room2, robot2 moves to room2 from room3, ball1 is dropped in room2 with lgripper2 by robot2, rgripper2 of robot2 drops ball2 in room2, from room2, robot2 moves to room3, lgripper2 of robot2 picks up ball4 in room3, ball7 is picked from room3 with rgripper2 by robot2, from room3, robot2 moves to room2, in room2, robot2's rgripper2 drops ball7, from room2, robot2's rgripper2 picks up ball3, from room2, robot2 moves to room1, ball4 is dropped in room1 with lgripper2 by robot2, lgripper2 of robot2 picks up ball5 in room1, rgripper2 of robot2 drops ball3 in room1, from room1, robot2's rgripper2 picks up ball6, robot2 moves from room1 to room2 and in room2, robot2's lgripper2 drops ball5 to reach the current state. What are the valid properties of the state that do not involve negations for rgripper2 before the first infeasible action in the sequence? Write None if there are none", "answer": "rgripper2 of robot2 is free", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot2 relocates from room2 to room3, then uses its left gripper (lgripper2) to grasp ball1 in room3. Meanwhile, robot1's left gripper (lgripper1) picks up ball2 in room2. Next, robot2 moves back to room2 from room3 and drops ball1 in room2 using lgripper2. However, it is robot2's right gripper (rgripper2) that actually drops ball2 in room2. Then, robot2 returns to room3, where it uses lgripper2 to pick up ball4 and its right gripper (rgripper2) to pick up ball7. After that, robot2 moves back to room2 and drops ball7 using rgripper2. In room2, robot2's rgripper2 picks up ball3. Subsequently, robot2 moves to room1, drops ball4 using lgripper2, and then uses lgripper2 to pick up ball5. Robot2's rgripper2 drops ball3 in room1, picks up ball6, and then moves to room2, where it drops ball5 using lgripper2 to reach the current state. What are the valid properties of the state that do not involve negations for rgripper2 before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "d400b0e3-197f-444d-8e55-56e229835a55", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_6_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, from room1, robot1's lgripper1 picks up ball1, rgripper1 of robot1 picks up ball6 in room3, robot1 moves to room5 from room1, in room5, robot1's rgripper1 drops ball3, from room5, robot1 moves to room2, from room2, robot1's rgripper1 picks up ball4, robot1 moves from room2 to room1, in room1, robot1's rgripper1 drops ball4 and ball6 is picked from room1 with rgripper1 by robot1 to reach the current state. What are the valid properties of the state (both with and without negations) for ball7 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball7 is at room3, ball7 is not at room4, ball7 is not being carried by robot1's lgripper1, ball7 is not located at room1, ball7 is not located at room2, ball7 is not present at room5 and rgripper1 of robot1 is not carrying ball7", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1 relocates from room4 to room1, then robot1's left gripper (lgripper1) grasps ball1 in room1, while robot1's right gripper (rgripper1) picks up ball6 in room3, robot1 proceeds to room5 from room1, in room5, robot1's right gripper (rgripper1) releases ball3, from room5, robot1 moves to room2, then in room2, robot1's right gripper (rgripper1) picks up ball4, robot1 moves back to room1 from room2, and in room1, robot1's right gripper (rgripper1) releases ball4, and finally, robot1's right gripper (rgripper1) picks up ball6 in room1 to reach the current state. What are the valid properties of the state (both with and without negations) for ball7 before the first infeasible action in the sequence? Write None if there are none", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "3ea5c95d-beee-4a83-9f4d-320cccd70f37", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_5_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball5 is picked from room5 with rgripper1 by robot1, lgripper1 of robot1 picks up ball1 in room1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves to room5 from room1, rgripper1 of robot1 drops ball3 in room5, from room5, robot1 moves to room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1 and from room1, robot1's rgripper1 picks up ball6 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not at room2, ball1 is not at room4, ball1 is not being carried by robot1's rgripper1, ball1 is not present at room3, ball1 is not present at room5, ball1 is present at room1, ball2 is at room2, ball2 is not at room1, ball2 is not at room5, ball2 is not located at room4, ball2 is not present at room3, ball3 is not at room2, ball3 is not at room3, ball3 is not being carried by robot1's lgripper1, ball3 is not located at room4, ball3 is not present at room5, ball3 is present at room1, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room3, ball4 is not located at room4, ball4 is not located at room5, ball4 is not present at room1, ball4 is present at room2, ball5 is located at room2, ball5 is not at room3, ball5 is not located at room1, ball5 is not located at room4, ball5 is not present at room5, ball6 is located at room1, ball6 is not located at room2, ball6 is not located at room3, ball6 is not present at room4, ball6 is not present at room5, ball7 is not being carried by robot1's lgripper1, ball7 is not present at room1, ball7 is not present at room2, ball7 is not present at room4, ball7 is not present at room5, ball7 is present at room3, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball7, robot1 is located at room4, robot1 is not at room3, robot1 is not at room5, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not present in room1, robot1 is not present in room2 and robot1's lgripper1 is free", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to be executed: robot1 uses rgripper1 to pick up ball5 from room5, then uses lgripper1 to pick up ball1 in room1, followed by using rgripper1 to pick up ball3 from room1. Next, robot1 moves from room1 to room5, drops ball3 in room5 using rgripper1, proceeds to room2 from room5, picks up ball4 in room2 with rgripper1, moves back to room1 from room2, and drops ball4 in room1 using rgripper1. Finally, robot1 uses rgripper1 to pick up ball6 in room1 to reach the current state. What valid properties of the state (including both affirmative and negated properties) hold true before the first infeasible action in the sequence? If none exist, state 'None'.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "fd45ae5f-f6fa-40dc-9244-9b1187dcfff0", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_8_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball7 is picked from room4 with lgripper1 by robot1, from room4, robot1 moves to room5, ball7 is dropped in room5 with lgripper1 by robot1, ball6 is picked from room1 with lgripper1 by robot1, ball1 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves to room5 from room1, lgripper1 of robot1 drops ball1 in room5, ball3 is dropped in room5 with rgripper1 by robot1 and robot1 moves to room2 from room5 to reach the current state. Some of the actions may not be executable. What is the state before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not at room2, ball1 is not at room3, ball1 is not at room4, ball1 is not being carried by robot1's rgripper1, ball1 is not located at room5, ball1 is not located at room6, ball1 is present at room1, ball2 is not at room1, ball2 is not at room6, ball2 is not located at room3, ball2 is not present at room4, ball2 is not present at room5, ball2 is present at room2, ball3 is at room1, ball3 is not at room2, ball3 is not located at room6, ball3 is not present at room3, ball3 is not present at room4, ball3 is not present at room5, ball4 is not at room1, ball4 is not at room3, ball4 is not located at room4, ball4 is not located at room6, ball4 is not present at room5, ball4 is present at room2, ball5 is located at room3, ball5 is not at room1, ball5 is not at room2, ball5 is not at room4, ball5 is not at room6, ball5 is not present at room5, ball6 is located at room1, ball6 is not located at room2, ball6 is not located at room3, ball6 is not located at room6, ball6 is not present at room4, ball6 is not present at room5, ball7 is at room5, ball7 is not at room3, ball7 is not at room6, ball7 is not being carried by robot1's rgripper1, ball7 is not located at room1, ball7 is not present at room2, ball7 is not present at room4, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball6, lgripper1 of robot1 is not carrying ball7, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball6, robot1 is at room5, robot1 is not at room4, robot1 is not at room6, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not located at room2, robot1 is not present in room1, robot1 is not present in room3, robot1's lgripper1 is available and robot1's rgripper1 is free", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to be executed: robot1 uses lgripper1 to pick up ball7 from room4, then moves from room4 to room5, and drops ball7 in room5 using lgripper1. Next, robot1 picks up ball6 and ball1 from room1 using lgripper1, and also picks up ball3 from room1 using rgripper1. Then, robot1 moves from room1 to room5, drops ball1 in room5 using lgripper1, and drops ball3 in room5 using rgripper1. Finally, robot1 moves from room5 to room2 to reach the current state. Some actions in the sequence may not be executable. What is the state before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "d571e1fe-c41f-4a1c-9deb-d785ec9d9bae", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_6_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1's lgripper1 picks up ball7, rgripper1 of robot1 drops ball1 in room4, lgripper1 of robot1 drops ball7 in room5, robot1 moves to room1 from room5, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1's rgripper1 picks up ball3, robot1 moves to room5 from room1, ball1 is dropped in room5 with lgripper1 by robot1, in room5, robot1's rgripper1 drops ball3 and robot1 moves from room5 to room2 to reach the current state. What are the valid properties of the state (both with and without negations) for ball7 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball7 is being carried by robot1's lgripper1, ball7 is not at room3, ball7 is not at room6, ball7 is not located at room1, ball7 is not located at room4, ball7 is not present at room2, ball7 is not present at room5 and rgripper1 of robot1 is not carrying ball7", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: robot1's left gripper (lgripper1) in room4 picks up ball7, robot1's right gripper (rgripper1) in room4 drops ball1, robot1's lgripper1 drops ball7 in room5, then robot1 moves from room5 to room1, picks up ball1 with lgripper1 in room1, picks up ball3 with rgripper1 in room1, moves from room1 to room5, drops ball1 with lgripper1 in room5, drops ball3 with rgripper1 in room5, and finally moves from room5 to room2 to reach the current state. What are the valid properties of the state (both with and without negations) for ball7 before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "8ac8468d-52c6-44b9-bd4b-f0c9430e9333", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_8_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball1, from room3, robot2's rgripper2 picks up ball2, robot2 moves from room3 to room2, ball1 is dropped in room2 with lgripper2 by robot2, in room2, robot2's rgripper2 drops ball2, robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball4, rgripper2 of robot2 picks up ball7 in room3, from room3, robot2 moves to room2, in room2, robot2's rgripper2 drops ball7, ball6 is picked from room1 with rgripper2 by robot2, from room2, robot2 moves to room1, lgripper2 of robot2 drops ball4 in room1, ball5 is picked from room1 with lgripper2 by robot2, rgripper2 of robot2 drops ball3 in room1, from room1, robot2's rgripper2 picks up ball6, from room1, robot2 moves to room2 and lgripper2 of robot2 drops ball5 in room2 to reach the current state. Some of the actions may not be executable. What is the state before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is at room2, ball1 is not being carried by robot1's lgripper2, ball1 is not located at room1, ball1 is not located at room3, ball2 is not being carried by robot1's rgripper2, ball2 is not being carried by robot2's lgripper1, ball2 is not located at room3, ball2 is not present at room1, ball2 is present at room2, ball3 is at room2, ball3 is not being carried by robot1's rgripper1, ball3 is not being carried by robot1's rgripper2, ball3 is not located at room3, ball3 is not present at room1, ball4 is not at room2, ball4 is not being carried by robot1's lgripper1, ball4 is not located at room3, ball4 is not present at room1, ball5 is at room1, ball5 is not being carried by robot1's lgripper2, ball5 is not being carried by robot1's rgripper2, ball5 is not being carried by robot2's lgripper2, ball5 is not being carried by robot2's rgripper2, ball5 is not located at room3, ball5 is not present at room2, ball6 is at room1, ball6 is not present at room2, ball6 is not present at room3, ball7 is at room2, ball7 is not being carried by robot1's lgripper2, ball7 is not being carried by robot1's rgripper1, ball7 is not being carried by robot1's rgripper2, ball7 is not being carried by robot2's lgripper1, ball7 is not being carried by robot2's rgripper1, ball7 is not being carried by robot2's rgripper2, ball7 is not located at room3, ball7 is not present at room1, lgripper1 of robot1 is free, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot2 is not carrying ball1, lgripper1 of robot2 is not carrying ball3, lgripper1 of robot2 is not carrying ball4, lgripper1 of robot2 is not carrying ball5, lgripper2 of robot1 is not carrying ball2, lgripper2 of robot1 is not carrying ball3, lgripper2 of robot1 is not carrying ball6, lgripper2 of robot1 is not free, lgripper2 of robot2 is carrying ball4, lgripper2 of robot2 is not carrying ball1, lgripper2 of robot2 is not carrying ball6, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball6, rgripper1 of robot2 is not carrying ball1, rgripper1 of robot2 is not carrying ball4, rgripper1 of robot2 is not free, rgripper2 of robot1 is not carrying ball6, rgripper2 of robot2 is not carrying ball2, rgripper2 of robot2 is not carrying ball4, rgripper2 of robot2 is not carrying ball6, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball1 with rgripper2, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball4 with lgripper2, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball4 with rgripper2, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not located at room1, robot1 is not located at room3, robot1 is present in room2, robot1's rgripper1 is available, robot1's rgripper2 is not available, robot2 is not carrying ball1 with rgripper2, robot2 is not carrying ball2 with lgripper2, robot2 is not carrying ball2 with rgripper1, robot2 is not carrying ball3 with lgripper2, robot2 is not carrying ball3 with rgripper1, robot2 is not carrying ball3 with rgripper2, robot2 is not carrying ball5 with rgripper1, robot2 is not carrying ball6 with lgripper1, robot2 is not carrying ball6 with rgripper1, robot2 is not carrying ball7 with lgripper2, robot2 is not located at room1, robot2 is not present in room3, robot2 is present in room2, robot2's lgripper1 is not available, robot2's lgripper2 is not free and robot2's rgripper2 is free", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: robot2 is set to move from room2 to room3, then from room3, robot2's left gripper (lgripper2) will grasp ball1, and its right gripper (rgripper2) will grasp ball2, after which robot2 will move back to room2, where it will release ball1 using lgripper2 and drop ball2 using rgripper2, then robot2 will return to room3, pick up ball4 with lgripper2 and ball7 with rgripper2, move back to room2, drop ball7 using rgripper2, pick up ball6 from room1 using rgripper2, move to room1, drop ball4 using lgripper2, pick up ball5 using lgripper2, drop ball3 using rgripper2, pick up ball6 using rgripper2, move back to room2, and finally drop ball5 using lgripper2 to reach the current state. However, some actions in this sequence may not be executable. What is the state before the first infeasible action in the sequence? If there are no infeasible actions, the answer is None.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "3f94df38-6393-4427-aeac-f863322d6400", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_5_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: in room4, robot1's rgripper1 drops ball5, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1 moves to room2, rgripper1 of robot1 picks up ball2 in room2, from room2, robot1 moves to room3, ball1 is dropped in room3 with lgripper1 by robot1, robot1 moves from room3 to room4, ball2 is dropped in room4 with rgripper1 by robot1, robot1 moves to room2 from room4 and from room2, robot1's lgripper1 picks up ball4 to reach the current state. What valid properties of the state that do not involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is at room2, ball6 is at room1, lgripper1 of robot1 is free, rgripper1 of robot1 is free and robot1 is at room4", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to be executed: in room4, robot1's right gripper (rgripper1) releases ball5, the left gripper (lgripper1) of robot1 grasps ball1 in room1, robot1 then moves from room1 to room2, the right gripper (rgripper1) of robot1 picks up ball2 in room2, robot1 proceeds to move from room2 to room3, ball1 is released in room3 using the left gripper (lgripper1) by robot1, robot1 moves from room3 to room4, ball2 is released in room4 using the right gripper (rgripper1) by robot1, robot1 then moves back to room2 from room4, and finally, the left gripper (lgripper1) of robot1 picks up ball4 in room2 to reach the current state. What valid properties of the state that do not involve negations are true before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "1e52bdd7-24ca-4774-9aae-0ba051cf4ff8", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_5_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, ball1 is picked from room1 with lgripper1 by robot1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room5, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves to room2 from room5, rgripper1 of robot1 picks up ball4 in room2, from room2, robot1 moves to room1, in room1, robot1's rgripper1 drops ball4 and rgripper1 of robot1 picks up ball6 in room1 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none", "answer": "None", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: robot1 relocates from room4 to room1, robot1 uses lgripper1 to grasp ball1 in room1, robot1 uses rgripper1 to grasp ball3 in room1, robot1 moves from room1 to room5, robot1 releases ball3 in room5 using rgripper1, robot1 proceeds from room5 to room2, robot1's rgripper1 picks up ball4 in room2, robot1 then moves from room2 to room1, and in room1, robot1's rgripper1 drops ball4 and then grasps ball6 to reach the current state. What valid properties of the state (both with and without negations) hold true before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "5c6fe712-bc65-4dbf-9314-fcb3bb4023ad", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_5_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball4 is dropped in room1 with lgripper1 by robot1 to reach the current state. What valid properties of the state that involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not at room3, ball1 is not at room6, ball1 is not located at room5, ball1 is not present at room2, ball1 is not present at room4, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room3, ball2 is not located at room5, ball2 is not located at room6, ball2 is not present at room1, ball2 is not present at room4, ball3 is not located at room3, ball3 is not located at room5, ball3 is not located at room6, ball3 is not present at room2, ball3 is not present at room4, ball4 is not at room4, ball4 is not at room6, ball4 is not located at room3, ball4 is not present at room1, ball4 is not present at room5, ball5 is not at room2, ball5 is not at room5, ball5 is not at room6, ball5 is not being carried by robot1's lgripper1, ball5 is not present at room1, ball5 is not present at room4, ball6 is not at room2, ball6 is not at room5, ball6 is not located at room4, ball6 is not located at room6, ball6 is not present at room3, ball7 is not located at room1, ball7 is not located at room3, ball7 is not located at room5, ball7 is not located at room6, ball7 is not present at room2, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not carrying ball7, robot1 is not at room1, robot1 is not at room6, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not located at room3, robot1 is not located at room5 and robot1 is not present in room2", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are intended to be executed: robot1 uses lgripper1 to drop ball4 in room1 to achieve the current state. What valid state properties involving negations hold true prior to the first infeasible action in the sequence? If there are none, state None.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "9b52c72f-6205-485b-96ef-8c4e5e69396f", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_5_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room2 to room1 to reach the current state. What valid properties of the state that do not involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is present at room1, ball2 is present at room2, ball3 is at room1, ball4 is at room2, ball5 is located at room2, ball6 is located at room1, ball7 is located at room3, rgripper1 of robot1 is free, robot1 is present in room4 and robot1's lgripper1 is free", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Considering the initial condition, the following actions are intended: robot1 will move from room2 to room1 to achieve the current state. What valid, non-negated properties of the state hold true prior to the first infeasible action in the sequence? If none exist, state None.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "de783bd9-e5a6-4dc5-ab0f-47ac6b0067e0", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_8_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, from room1, robot1's lgripper1 picks up ball1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves from room1 to room5, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves from room5 to room2, ball4 is picked from room2 with rgripper1 by robot1, from room2, robot1 moves to room1, in room1, robot1's rgripper1 drops ball4 and rgripper1 of robot1 drops ball7 in room5 to reach the current state. Some of the actions may not be executable. What is the state before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not located at room3, ball1 is not present at room1, ball1 is not present at room2, ball1 is not present at room4, ball1 is not present at room5, ball2 is located at room2, ball2 is not at room1, ball2 is not at room3, ball2 is not at room5, ball2 is not present at room4, ball3 is located at room5, ball3 is not located at room4, ball3 is not present at room1, ball3 is not present at room2, ball3 is not present at room3, ball4 is not at room2, ball4 is not at room4, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room3, ball4 is not present at room5, ball4 is present at room1, ball5 is at room2, ball5 is not at room5, ball5 is not located at room1, ball5 is not present at room3, ball5 is not present at room4, ball6 is not at room4, ball6 is not at room5, ball6 is not being carried by robot1's lgripper1, ball6 is not being carried by robot1's rgripper1, ball6 is not present at room2, ball6 is not present at room3, ball6 is present at room1, ball7 is at room3, ball7 is not being carried by robot1's lgripper1, ball7 is not located at room2, ball7 is not located at room5, ball7 is not present at room1, ball7 is not present at room4, lgripper1 of robot1 is carrying ball1, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not free, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball7, robot1 is located at room1, robot1 is not at room5, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not located at room2, robot1 is not located at room3 and robot1 is not present in room4", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1 is set to move from room4 to room1, then from room1, robot1's left gripper (lgripper1) will grasp ball1, and its right gripper (rgripper1) will pick up ball3 in room1. Next, robot1 will move from room1 to room5, where it will release ball3 using rgripper1. After that, robot1 will proceed from room5 to room2, pick up ball4 with rgripper1 in room2, and then move back to room1. In room1, robot1's rgripper1 will drop ball4, and to reach the current state, rgripper1 of robot1 will release ball7 in room5. However, some actions in this sequence may not be feasible. What is the state before the first infeasible action in the sequence? If there are no infeasible actions, write None.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "2bddd14d-bf5c-4512-93f3-f928d4051eff", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_6_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room5, robot1's rgripper1 picks up ball2, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1's rgripper1 picks up ball3, robot1 moves from room1 to room5, rgripper1 of robot1 drops ball3 in room5, from room5, robot1 moves to room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves from room2 to room1, rgripper1 of robot1 drops ball4 in room1 and rgripper1 of robot1 picks up ball6 in room1 to reach the current state. What are the valid properties of the state that involve negations for ball1 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not at room2, ball1 is not at room3, ball1 is not at room4, ball1 is not being carried by robot1's rgripper1, ball1 is not present at room5 and lgripper1 of robot1 is not carrying ball1", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1's right gripper (rgripper1) in room5 picks up ball2, robot1's left gripper (lgripper1) in room1 picks up ball1, then from room1, robot1's rgripper1 picks up ball3, robot1 moves from room1 to room5, drops ball3 in room5 using rgripper1, moves from room5 to room2, picks up ball4 in room2 using rgripper1, moves from room2 to room1, drops ball4 in room1 using rgripper1, and finally picks up ball6 in room1 using rgripper1 to reach the current state. What are the valid properties of the state that involve negations for ball1 before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "91e1a46f-5f3b-4fe0-a58e-e9818b49f658", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_5_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, lgripper1 of robot1 picks up ball1 in room1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room5, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves to room2 from room5, rgripper1 of robot1 picks up ball4 in room2, robot1 moves from room2 to room1, in room1, robot1's rgripper1 drops ball4, ball6 is picked from room1 with rgripper1 by robot1, robot1 moves to room3 from room1, in room3, robot1's rgripper1 drops ball6, ball7 is picked from room3 with rgripper1 by robot1, from room3, robot1 moves to room4, lgripper1 of robot1 drops ball1 in room4, in room4, robot1's rgripper1 drops ball7, from room4, robot1 moves to room2, from room2, robot1's lgripper1 picks up ball2 and robot1 moves from room3 to room4 to reach the current state. What valid properties of the state that involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not at room3, ball1 is not located at room1, ball1 is not present at room2, ball1 is not present at room5, ball2 is not at room1, ball2 is not at room3, ball2 is not at room4, ball2 is not present at room2, ball2 is not present at room5, ball3 is not at room4, ball3 is not located at room1, ball3 is not present at room2, ball3 is not present at room3, ball4 is not being carried by robot1's lgripper1, ball4 is not located at room5, ball4 is not present at room2, ball4 is not present at room3, ball4 is not present at room4, ball5 is not at room3, ball5 is not at room4, ball5 is not located at room5, ball5 is not present at room1, ball6 is not at room2, ball6 is not at room5, ball6 is not located at room4, ball6 is not present at room1, ball7 is not located at room1, ball7 is not located at room5, ball7 is not present at room2, ball7 is not present at room3, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not free, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not carrying ball7, robot1 is not at room1, robot1 is not at room5, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not located at room3 and robot1 is not located at room4", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1 relocates from room4 to room1, robot1's left gripper (lgripper1) grasps ball1 in room1, robot1's right gripper (rgripper1) picks up ball3 in room1, robot1 moves from room1 to room5, robot1's rgripper1 releases ball3 in room5, robot1 proceeds from room5 to room2, robot1's rgripper1 picks up ball4 in room2, robot1 moves from room2 back to room1, in room1, robot1's rgripper1 releases ball4, robot1's rgripper1 then picks up ball6 in room1, robot1 moves from room1 to room3, in room3, robot1's rgripper1 releases ball6, robot1's rgripper1 then picks up ball7 in room3, robot1 moves from room3 to room4, robot1's lgripper1 releases ball1 in room4, in room4, robot1's rgripper1 releases ball7, robot1 then moves from room4 to room2, and finally, robot1's lgripper1 picks up ball2 and robot1 moves from room3 to room4 to reach the current state. What valid properties of the state that involve negations are true before the first infeasible action in the sequence? Write None if there are none", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "0749d758-4ab1-4d5f-83a5-b7f92cbbe63b", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_8_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: in room5, robot1's rgripper1 drops ball2 to reach the current state. Some of the actions may not be executable. What is the state before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is at room1, ball1 is not at room3, ball1 is not at room6, ball1 is not located at room2, ball1 is not present at room4, ball1 is not present at room5, ball2 is at room2, ball2 is not at room1, ball2 is not at room3, ball2 is not at room5, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room4, ball2 is not present at room6, ball3 is not at room2, ball3 is not at room5, ball3 is not being carried by robot1's lgripper1, ball3 is not located at room4, ball3 is not located at room6, ball3 is not present at room3, ball3 is present at room1, ball4 is located at room2, ball4 is not at room3, ball4 is not at room4, ball4 is not at room6, ball4 is not located at room1, ball4 is not present at room5, ball5 is not at room2, ball5 is not located at room4, ball5 is not present at room1, ball5 is not present at room5, ball5 is not present at room6, ball5 is present at room3, ball6 is at room1, ball6 is not at room3, ball6 is not at room4, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room2, ball6 is not located at room6, ball6 is not present at room5, ball7 is not at room1, ball7 is not at room3, ball7 is not located at room5, ball7 is not located at room6, ball7 is not present at room2, ball7 is present at room4, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball7, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball7, robot1 is located at room4, robot1 is not at room3, robot1 is not at room5, robot1 is not at room6, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not located at room1, robot1 is not present in room2, robot1's lgripper1 is free and robot1's rgripper1 is available", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are intended to be executed: in room5, robot1's rgripper1 releases ball2 to achieve the current state. However, some actions may not be feasible. What is the state preceding the first infeasible action in the sequence? If none exist, write None.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "b2a425e8-f365-47de-beb9-b87e6b1b7103", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_6_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, lgripper1 of robot1 picks up ball1 in room1, robot1 moves to room2 from room1, rgripper1 of robot1 picks up ball2 in room2, ball6 is picked from room4 with rgripper1 by robot1, lgripper1 of robot1 drops ball1 in room3, robot1 moves to room4 from room3, in room4, robot1's rgripper1 drops ball2, robot1 moves from room4 to room2, ball4 is picked from room2 with lgripper1 by robot1, from room2, robot1's rgripper1 picks up ball5, robot1 moves to room5 from room2, in room5, robot1's lgripper1 drops ball4, robot1 moves from room5 to room1, lgripper1 of robot1 picks up ball3 in room1, ball5 is dropped in room1 with rgripper1 by robot1, rgripper1 of robot1 picks up ball6 in room1, robot1 moves to room5 from room1 and ball3 is dropped in room5 with lgripper1 by robot1 to reach the current state. What are the valid properties of the state that involve negations for ball5 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball5 is not being carried by robot1's lgripper1, ball5 is not being carried by robot1's rgripper1, ball5 is not located at room3, ball5 is not located at room4, ball5 is not present at room1 and ball5 is not present at room5", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1 relocates from room4 to room1, then uses its left gripper (lgripper1) to grasp ball1 in room1. Next, robot1 moves to room2 from room1 and uses its right gripper (rgripper1) to pick up ball2 in room2. Subsequently, robot1 retrieves ball6 from room4 using rgripper1, then drops ball1 in room3 using lgripper1. Robot1 proceeds to room4, where it releases ball2 using rgripper1. The robot then moves to room2, picks up ball4 with lgripper1, and also picks up ball5 with rgripper1. Robot1 then moves to room5, drops ball4 using lgripper1, and returns to room1. In room1, robot1 picks up ball3 with lgripper1 and drops ball5 using rgripper1. Robot1 then picks up ball6 with rgripper1, moves to room5, and finally drops ball3 using lgripper1 to reach the current state. What are the valid properties of the state that involve negations for ball5 before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "9a94cd0a-4eb2-4f81-a22a-23852509e7ce", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_5_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves to room3 from room2, ball1 is picked from room3 with lgripper2 by robot2, ball2 is picked from room3 with rgripper2 by robot2, from room3, robot2 moves to room2, ball1 is dropped in room2 with lgripper2 by robot2, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves to room3 from room2, lgripper2 of robot2 picks up ball4 in room3, lgripper2 of robot1 picks up ball1 in room3 and from room3, robot2 moves to room2 to reach the current state. What valid properties of the state that do not involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is present at room2, ball2 is present at room2, ball3 is located at room2, ball5 is present at room1, ball6 is present at room1, ball7 is located at room3, rgripper2 of robot2 is free, robot1 is present in room2, robot1's lgripper1 is available, robot1's rgripper1 is free, robot2 is carrying ball4 with lgripper2 and robot2 is located at room3", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: robot2 will move from room2 to room3, then use lgripper2 to pick up ball1 in room3, and use rgripper2 to pick up ball2 in room3. Next, robot2 will move back to room2 from room3, and then drop ball1 and ball2 in room2 using lgripper2 and rgripper2, respectively. After that, robot2 will move back to room3 from room2, where lgripper2 of robot2 will pick up ball4, and simultaneously, lgripper2 of robot1 will pick up ball1 in room3. Finally, robot2 will move back to room2 from room3 to reach the current state. What valid properties of the state that do not involve negations are true before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "d9d87e8f-eb81-47ff-acb9-9df6bd9e4c03", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_8_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room4, robot1 moves to room5 from room4, ball7 is dropped in room5 with lgripper1 by robot1, ball3 is dropped in room2 with lgripper1 by robot1, lgripper1 of robot1 picks up ball1 in room1, rgripper1 of robot1 picks up ball3 in room1, from room1, robot1 moves to room5, lgripper1 of robot1 drops ball1 in room5, in room5, robot1's rgripper1 drops ball3, from room5, robot1 moves to room2, ball2 is picked from room2 with lgripper1 by robot1, rgripper1 of robot1 picks up ball4 in room2, from room2, robot1 moves to room1, ball4 is dropped in room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball6, robot1 moves to room3 from room1, in room3, robot1's rgripper1 drops ball6, from room3, robot1's rgripper1 picks up ball5 and robot1 moves from room3 to room6 to reach the current state. Some of the actions may not be executable. What is the state before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is at room1, ball1 is not at room4, ball1 is not being carried by robot1's rgripper1, ball1 is not located at room5, ball1 is not present at room2, ball1 is not present at room3, ball1 is not present at room6, ball2 is at room2, ball2 is not at room3, ball2 is not at room4, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room1, ball2 is not located at room5, ball2 is not located at room6, ball3 is at room1, ball3 is not at room4, ball3 is not at room6, ball3 is not present at room2, ball3 is not present at room3, ball3 is not present at room5, ball4 is not at room1, ball4 is not at room3, ball4 is not located at room5, ball4 is not located at room6, ball4 is not present at room4, ball4 is present at room2, ball5 is not located at room6, ball5 is not present at room1, ball5 is not present at room2, ball5 is not present at room4, ball5 is not present at room5, ball5 is present at room3, ball6 is not at room3, ball6 is not located at room2, ball6 is not located at room4, ball6 is not present at room5, ball6 is not present at room6, ball6 is present at room1, ball7 is at room5, ball7 is not at room1, ball7 is not at room2, ball7 is not at room4, ball7 is not located at room6, ball7 is not present at room3, lgripper1 of robot1 is free, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not carrying ball7, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball6, robot1 is located at room5, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not located at room2, robot1 is not located at room3, robot1 is not located at room4, robot1 is not present in room1, robot1 is not present in room6 and robot1's rgripper1 is free", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1's left gripper (lgripper1) picks up ball7 in room4, then robot1 moves from room4 to room5, and drops ball7 in room5 using lgripper1. Next, robot1 drops ball3 in room2 using lgripper1, picks up ball1 in room1 with lgripper1, and picks up ball3 in room1 with its right gripper (rgripper1). From room1, robot1 moves to room5, drops ball1 in room5 using lgripper1, and drops ball3 in room5 using rgripper1. Then, robot1 moves from room5 to room2, picks up ball2 in room2 with lgripper1, and picks up ball4 in room2 with rgripper1. From room2, robot1 moves to room1, drops ball4 in room1 using rgripper1, picks up ball6 with rgripper1, and moves from room1 to room3. In room3, robot1 drops ball6 using rgripper1, picks up ball5 with rgripper1, and moves from room3 to room6 to reach the current state. Some actions in the sequence may not be executable. What is the state before the first infeasible action in the sequence? If there are no infeasible actions, write None.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "030fd4dd-a8f5-47c5-a695-af069e2eaa92", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_5_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves from room4 to room1, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, ball6 is dropped in room3 with rgripper1 by robot1, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, ball4 is picked from room2 with rgripper1 by robot1, from room2, robot1 moves to room1, rgripper1 of robot1 drops ball4 in room1 and rgripper1 of robot1 picks up ball6 in room1 to reach the current state. What valid properties of the state that do not involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is being carried by robot1's lgripper1, ball2 is at room2, ball3 is being carried by robot1's rgripper1, ball4 is located at room2, ball5 is present at room2, ball6 is present at room1, ball7 is present at room3 and robot1 is located at room1", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: robot1 will move from room4 to room1, then from room1, robot1's lgripper1 will grasp ball1, robot1's rgripper1 will pick up ball3 from room1, robot1's rgripper1 will release ball6 in room3, robot1's rgripper1 will drop ball3 in room5, robot1 will move from room5 to room2, robot1's rgripper1 will pick up ball4 from room2, robot1 will move from room2 to room1, robot1's rgripper1 will release ball4 in room1, and finally, robot1's rgripper1 will pick up ball6 from room1 to achieve the current state. What valid properties of the state that do not involve negations are true before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "3346a465-1795-4257-a35b-73d0adbf77db", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_8_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: rgripper1 of robot1 picks up ball4 in room3 to reach the current state. Some of the actions may not be executable. What is the state before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is located at room3, ball1 is not at room1, ball1 is not at room2, ball1 is not being carried by robot1's rgripper1, ball1 is not being carried by robot2's lgripper1, ball2 is located at room3, ball2 is not at room2, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room1, ball3 is located at room2, ball3 is not at room3, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's rgripper1, ball3 is not being carried by robot2's lgripper1, ball3 is not being carried by robot2's lgripper2, ball3 is not located at room1, ball4 is at room3, ball4 is not at room1, ball4 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's rgripper2, ball4 is not being carried by robot2's rgripper1, ball4 is not present at room2, ball5 is not being carried by robot1's rgripper2, ball5 is not being carried by robot2's rgripper1, ball5 is not located at room3, ball5 is not present at room2, ball5 is present at room1, ball6 is at room1, ball6 is not at room2, ball6 is not being carried by robot1's lgripper1, ball6 is not being carried by robot1's rgripper1, ball6 is not being carried by robot1's rgripper2, ball6 is not being carried by robot2's rgripper2, ball6 is not present at room3, ball7 is at room3, ball7 is not at room2, ball7 is not being carried by robot1's lgripper1, ball7 is not being carried by robot1's rgripper1, ball7 is not located at room1, lgripper1 of robot1 is free, lgripper1 of robot2 is not carrying ball2, lgripper1 of robot2 is not carrying ball5, lgripper1 of robot2 is not carrying ball6, lgripper1 of robot2 is not free, lgripper2 of robot1 is not carrying ball4, lgripper2 of robot1 is not carrying ball5, lgripper2 of robot1 is not carrying ball6, lgripper2 of robot1 is not carrying ball7, lgripper2 of robot1 is not free, lgripper2 of robot2 is not carrying ball2, lgripper2 of robot2 is not carrying ball5, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot2 is not carrying ball6, rgripper1 of robot2 is not carrying ball7, rgripper2 of robot1 is not carrying ball3, rgripper2 of robot1 is not carrying ball7, rgripper2 of robot1 is not free, rgripper2 of robot2 is not carrying ball1, rgripper2 of robot2 is not carrying ball2, rgripper2 of robot2 is not carrying ball7, robot1 is not at room1, robot1 is not at room3, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball1 with lgripper2, robot1 is not carrying ball1 with rgripper2, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball2 with lgripper2, robot1 is not carrying ball2 with rgripper2, robot1 is not carrying ball3 with lgripper2, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is present in room2, robot1's rgripper1 is free, robot2 is at room2, robot2 is not carrying ball1 with lgripper2, robot2 is not carrying ball1 with rgripper1, robot2 is not carrying ball2 with rgripper1, robot2 is not carrying ball3 with rgripper1, robot2 is not carrying ball3 with rgripper2, robot2 is not carrying ball4 with lgripper1, robot2 is not carrying ball4 with lgripper2, robot2 is not carrying ball4 with rgripper2, robot2 is not carrying ball5 with rgripper2, robot2 is not carrying ball6 with lgripper2, robot2 is not carrying ball7 with lgripper1, robot2 is not carrying ball7 with lgripper2, robot2 is not present in room1, robot2 is not present in room3, robot2's lgripper2 is free, robot2's rgripper1 is not available and robot2's rgripper2 is available", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are intended to be executed: robot1's rgripper1 retrieves ball4 in room3 to achieve the current state. However, some actions may not be feasible. What is the state preceding the first unexecutable action in the sequence? If none exist, write None.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "5223cfad-bf76-4910-ae58-43ff40c8c76e", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_5_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room4, robot1 moves to room5 from room4, in room5, robot1's lgripper1 drops ball7, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room5, in room5, robot1's lgripper1 drops ball1, rgripper1 of robot1 drops ball3 in room5, from room5, robot1 moves to room2, from room2, robot1's lgripper1 picks up ball2, ball4 is dropped in room1 with rgripper1 by robot1, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1, rgripper1 of robot1 picks up ball6 in room1, from room1, robot1 moves to room3, ball6 is dropped in room3 with rgripper1 by robot1, ball5 is picked from room3 with rgripper1 by robot1 and from room3, robot1 moves to room6 to reach the current state. What valid properties of the state that involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not at room2, ball1 is not at room3, ball1 is not at room4, ball1 is not being carried by robot1's rgripper1, ball1 is not located at room1, ball1 is not located at room6, ball2 is not at room2, ball2 is not at room4, ball2 is not at room6, ball2 is not located at room1, ball2 is not present at room3, ball2 is not present at room5, ball3 is not at room3, ball3 is not located at room2, ball3 is not located at room4, ball3 is not located at room6, ball3 is not present at room1, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room4, ball4 is not present at room1, ball4 is not present at room3, ball4 is not present at room5, ball4 is not present at room6, ball5 is not at room1, ball5 is not at room2, ball5 is not at room5, ball5 is not at room6, ball5 is not being carried by robot1's rgripper1, ball5 is not present at room4, ball6 is not at room4, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room3, ball6 is not located at room5, ball6 is not present at room2, ball6 is not present at room6, ball7 is not at room2, ball7 is not at room3, ball7 is not present at room1, ball7 is not present at room4, ball7 is not present at room6, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not free, robot1 is not at room1, robot1 is not at room4, robot1 is not at room5, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not located at room3 and robot1 is not located at room6", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1's left gripper (lgripper1) picks up ball7 in room4, then robot1 moves from room4 to room5, where it drops ball7 using lgripper1, after which robot1 proceeds to room1 from room5, picks up ball1 with lgripper1 and ball3 with its right gripper (rgripper1) in room1, moves to room5, drops ball1 with lgripper1 and ball3 with rgripper1 in room5, then moves to room2, picks up ball2 with lgripper1 in room2, drops ball4 in room1 using rgripper1, moves from room2 to room1, drops ball4 in room1 using rgripper1, picks up ball6 with rgripper1 in room1, moves to room3, drops ball6 with rgripper1 in room3, picks up ball5 with rgripper1 in room3, and finally moves from room3 to room6 to reach the current state. What valid properties of the state that involve negations are true before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "de8a94f3-bb89-4ec1-b5ac-50c10aaeaa45", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_6_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, rgripper1 of robot1 picks up ball7 in room2, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves to room5 from room1, rgripper1 of robot1 drops ball3 in room5, from room5, robot1 moves to room2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves to room1 from room2, rgripper1 of robot1 drops ball4 in room1, rgripper1 of robot1 picks up ball6 in room1, robot1 moves to room3 from room1, in room3, robot1's rgripper1 drops ball6, ball7 is picked from room3 with rgripper1 by robot1, robot1 moves from room3 to room4, lgripper1 of robot1 drops ball1 in room4, rgripper1 of robot1 drops ball7 in room4, robot1 moves to room2 from room4, lgripper1 of robot1 picks up ball2 in room2 and ball5 is picked from room2 with rgripper1 by robot1 to reach the current state. What are the valid properties of the state that do not involve negations for room3 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball7 is located at room3", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1 relocates from room4 to room1, then uses its rgripper1 to grasp ball7 in room2, followed by picking up ball3 from room1 with rgripper1, and then moving to room5 from room1. In room5, robot1's rgripper1 releases ball3, and then robot1 proceeds to room2. In room2, robot1 uses rgripper1 to pick up ball4, moves to room1, and then drops ball4 in room1 using rgripper1. Next, rgripper1 of robot1 picks up ball6 in room1, and robot1 moves to room3. In room3, robot1's rgripper1 releases ball6, and then picks up ball7 with rgripper1. Robot1 then moves from room3 to room4, where lgripper1 drops ball1, and rgripper1 drops ball7. Subsequently, robot1 moves to room2 from room4, and in room2, lgripper1 picks up ball2, and rgripper1 picks up ball5 to reach the current state. What are the valid properties of the state that do not involve negations for room3 before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "8c36a08f-45e8-4dc9-8332-a4822b691f37", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_5_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball1 is picked from room3 with rgripper1 by robot2 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not being carried by robot1's lgripper2, ball1 is not being carried by robot2's lgripper2, ball1 is not being carried by robot2's rgripper1, ball1 is not located at room1, ball1 is not present at room2, ball1 is present at room3, ball2 is located at room3, ball2 is not being carried by robot1's rgripper2, ball2 is not being carried by robot2's lgripper2, ball2 is not located at room1, ball2 is not located at room2, ball3 is located at room2, ball3 is not at room3, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's lgripper2, ball3 is not being carried by robot1's rgripper1, ball3 is not being carried by robot1's rgripper2, ball3 is not present at room1, ball4 is at room3, ball4 is not being carried by robot1's rgripper1, ball4 is not being carried by robot2's lgripper1, ball4 is not being carried by robot2's lgripper2, ball4 is not being carried by robot2's rgripper2, ball4 is not located at room1, ball4 is not located at room2, ball5 is not at room3, ball5 is not being carried by robot1's rgripper2, ball5 is not being carried by robot2's rgripper1, ball5 is not located at room2, ball5 is present at room1, ball6 is not being carried by robot1's lgripper1, ball6 is not being carried by robot1's lgripper2, ball6 is not being carried by robot1's rgripper1, ball6 is not being carried by robot2's lgripper1, ball6 is not being carried by robot2's lgripper2, ball6 is not located at room3, ball6 is not present at room2, ball6 is present at room1, ball7 is not at room1, ball7 is not at room2, ball7 is not being carried by robot2's rgripper1, ball7 is present at room3, lgripper1 of robot1 is free, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball7, lgripper1 of robot2 is not carrying ball1, lgripper1 of robot2 is not carrying ball2, lgripper1 of robot2 is not carrying ball3, lgripper2 of robot1 is not carrying ball2, lgripper2 of robot1 is not carrying ball5, lgripper2 of robot2 is free, lgripper2 of robot2 is not carrying ball5, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot2 is not carrying ball3, rgripper2 of robot1 is not carrying ball6, rgripper2 of robot1 is not carrying ball7, rgripper2 of robot1 is not free, rgripper2 of robot2 is not carrying ball7, robot1 is located at room2, robot1 is not at room1, robot1 is not at room3, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball1 with rgripper2, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball4 with lgripper2, robot1 is not carrying ball4 with rgripper2, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball7 with lgripper2, robot1 is not carrying ball7 with rgripper1, robot1's lgripper2 is not available, robot1's rgripper1 is available, robot2 is not carrying ball1 with rgripper2, robot2 is not carrying ball2 with rgripper1, robot2 is not carrying ball2 with rgripper2, robot2 is not carrying ball3 with lgripper2, robot2 is not carrying ball3 with rgripper2, robot2 is not carrying ball4 with rgripper1, robot2 is not carrying ball5 with lgripper1, robot2 is not carrying ball5 with rgripper2, robot2 is not carrying ball6 with rgripper1, robot2 is not carrying ball6 with rgripper2, robot2 is not carrying ball7 with lgripper1, robot2 is not carrying ball7 with lgripper2, robot2 is not present in room1, robot2 is not present in room3, robot2 is present in room2, robot2's lgripper1 is not available, robot2's rgripper1 is not free and robot2's rgripper2 is available", "plan_length": 1, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are intended to be executed: robot2 uses rgripper1 to pick up ball1 from room3 to achieve the current state. What valid properties of the state (including both affirmative and negated properties) hold true prior to the first infeasible action in the sequence? If none exist, please state None.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "5394dbd6-ce19-46b6-8e1c-a017c4269fec", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_8_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: rgripper1 of robot1 picks up ball6 in room3 to reach the current state. Some of the actions may not be executable. What is the state before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not at room4, ball1 is not located at room2, ball1 is not located at room3, ball1 is not present at room5, ball1 is present at room1, ball2 is located at room2, ball2 is not at room4, ball2 is not at room5, ball2 is not being carried by robot1's lgripper1, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room3, ball2 is not present at room1, ball3 is at room1, ball3 is not at room3, ball3 is not being carried by robot1's lgripper1, ball3 is not located at room2, ball3 is not located at room4, ball3 is not located at room5, ball4 is not at room1, ball4 is not at room3, ball4 is not located at room4, ball4 is not located at room5, ball4 is present at room2, ball5 is at room2, ball5 is not located at room1, ball5 is not located at room4, ball5 is not located at room5, ball5 is not present at room3, ball6 is located at room1, ball6 is not at room2, ball6 is not at room4, ball6 is not located at room3, ball6 is not present at room5, lgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball6, robot1 is at room4, robot1 is not at room3, robot1 is not at room5, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not present in room1, robot1 is not present in room2 and robot1's lgripper1 is free", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, a series of actions are proposed: robot1's rgripper1 is intended to pick up ball6 in room3 to achieve the current state. However, some actions may not be feasible. What is the state preceding the first unexecutable action in the sequence? If none exist, write None.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "a46159b3-62d3-492a-a460-3a7d3d2a833c", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_5_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves from room2 to room3, ball1 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball2 in room3, from room3, robot2 moves to room2, lgripper2 of robot2 drops ball1 in room2, rgripper2 of robot2 drops ball2 in room2, robot2 moves to room3 from room2, lgripper2 of robot2 picks up ball4 in room3, rgripper2 of robot2 picks up ball7 in room3, robot2 moves from room3 to room2, ball7 is dropped in room2 with rgripper2 by robot2, from room2, robot2's rgripper2 picks up ball3, robot2 moves from room2 to room1, lgripper2 of robot2 drops ball4 in room1, from room3, robot2's lgripper1 picks up ball2, rgripper2 of robot2 drops ball3 in room1, ball6 is picked from room1 with rgripper2 by robot2, robot2 moves from room1 to room2 and ball5 is dropped in room2 with lgripper2 by robot2 to reach the current state. What valid properties of the state that do not involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is located at room2, ball2 is present at room2, ball4 is at room1, ball5 is at room1, ball6 is present at room1, ball7 is at room2, lgripper1 of robot1 is free, rgripper2 of robot2 is carrying ball3, robot1 is at room2, robot1's rgripper1 is available, robot2 is at room1 and robot2's lgripper2 is available", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot2 will move from room2 to room3, then use lgripper2 to pick up ball1 in room3, and use rgripper2 to pick up ball2 in room3. Next, robot2 will move back to room2 from room3, and then use lgripper2 and rgripper2 to drop ball1 and ball2 in room2, respectively. After that, robot2 will move back to room3 from room2, and use lgripper2 and rgripper2 to pick up ball4 and ball7 in room3. Then, robot2 will move to room2 from room3, and use rgripper2 to drop ball7 in room2. From room2, robot2's rgripper2 will pick up ball3, and then robot2 will move to room1 from room2. In room1, lgripper2 will drop ball4, and from room3, robot2's lgripper1 will pick up ball2. Additionally, rgripper2 will drop ball3 in room1, and then ball6 will be picked up from room1 with rgripper2 by robot2. Finally, robot2 will move from room1 to room2, and use lgripper2 to drop ball5 in room2 to reach the current state. What valid properties of the state that do not involve negations are true before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "518f7fc8-f9cb-4775-85c1-b7230a339390", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_5_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves from room2 to room3, lgripper2 of robot2 picks up ball1 in room3, ball2 is picked from room3 with rgripper2 by robot2, rgripper1 of robot2 drops ball4 in room3, in room2, robot2's lgripper2 drops ball1, ball2 is dropped in room2 with rgripper2 by robot2, from room2, robot2 moves to room3, ball4 is picked from room3 with lgripper2 by robot2, ball7 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, rgripper2 of robot2 drops ball7 in room2, rgripper2 of robot2 picks up ball3 in room2, robot2 moves to room1 from room2, ball4 is dropped in room1 with lgripper2 by robot2, from room1, robot2's lgripper2 picks up ball5, in room1, robot2's rgripper2 drops ball3, from room1, robot2's rgripper2 picks up ball6, from room1, robot2 moves to room2 and ball5 is dropped in room2 with lgripper2 by robot2 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is being carried by robot2's lgripper2, ball1 is not at room3, ball1 is not being carried by robot1's rgripper1, ball1 is not being carried by robot2's rgripper1, ball1 is not located at room2, ball1 is not present at room1, ball2 is being carried by robot2's rgripper2, ball2 is not at room1, ball2 is not being carried by robot1's lgripper1, ball2 is not located at room2, ball2 is not present at room3, ball3 is located at room2, ball3 is not being carried by robot2's lgripper2, ball3 is not being carried by robot2's rgripper2, ball3 is not located at room1, ball3 is not located at room3, ball4 is located at room3, ball4 is not at room1, ball4 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's lgripper2, ball4 is not being carried by robot2's rgripper2, ball4 is not located at room2, ball5 is not at room3, ball5 is not being carried by robot1's lgripper1, ball5 is not being carried by robot2's lgripper1, ball5 is not being carried by robot2's rgripper2, ball5 is not located at room2, ball5 is present at room1, ball6 is not being carried by robot1's rgripper2, ball6 is not being carried by robot2's lgripper2, ball6 is not being carried by robot2's rgripper2, ball6 is not located at room2, ball6 is not present at room3, ball6 is present at room1, ball7 is at room3, ball7 is not being carried by robot1's lgripper1, ball7 is not being carried by robot1's rgripper1, ball7 is not being carried by robot1's rgripper2, ball7 is not being carried by robot2's lgripper2, ball7 is not located at room1, ball7 is not located at room2, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball6, lgripper1 of robot2 is not carrying ball1, lgripper1 of robot2 is not carrying ball2, lgripper1 of robot2 is not carrying ball3, lgripper1 of robot2 is not carrying ball4, lgripper1 of robot2 is not free, lgripper2 of robot1 is not carrying ball1, lgripper2 of robot1 is not carrying ball6, lgripper2 of robot2 is not carrying ball2, lgripper2 of robot2 is not carrying ball4, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot2 is not carrying ball2, rgripper1 of robot2 is not carrying ball3, rgripper1 of robot2 is not carrying ball6, rgripper1 of robot2 is not carrying ball7, rgripper1 of robot2 is not free, rgripper2 of robot1 is not free, rgripper2 of robot2 is not carrying ball1, robot1 is not carrying ball1 with rgripper2, robot1 is not carrying ball2 with lgripper2, robot1 is not carrying ball2 with rgripper2, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball3 with lgripper2, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball3 with rgripper2, robot1 is not carrying ball4 with rgripper2, robot1 is not carrying ball5 with lgripper2, robot1 is not carrying ball5 with rgripper2, robot1 is not carrying ball6 with rgripper1, robot1 is not carrying ball7 with lgripper2, robot1 is not located at room1, robot1 is not present in room3, robot1 is present in room2, robot1's lgripper1 is available, robot1's lgripper2 is not available, robot1's rgripper1 is free, robot2 is at room3, robot2 is not carrying ball4 with rgripper1, robot2 is not carrying ball5 with lgripper2, robot2 is not carrying ball5 with rgripper1, robot2 is not carrying ball6 with lgripper1, robot2 is not carrying ball7 with lgripper1, robot2 is not carrying ball7 with rgripper2, robot2 is not present in room1, robot2 is not present in room2, robot2's lgripper2 is not available and robot2's rgripper2 is not free", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot2 moves from room2 to room3, robot2's left gripper (lgripper2) picks up ball1 in room3, robot2's right gripper (rgripper2) picks up ball2 in room3, robot2's right gripper (rgripper1) drops ball4 in room3, robot2's left gripper (lgripper2) drops ball1 in room2, robot2's right gripper (rgripper2) drops ball2 in room2, robot2 moves from room2 to room3, robot2's left gripper (lgripper2) picks up ball4 in room3, robot2's right gripper (rgripper2) picks up ball7 in room3, robot2 moves from room3 to room2, robot2's right gripper (rgripper2) drops ball7 in room2, robot2's right gripper (rgripper2) picks up ball3 in room2, robot2 moves from room2 to room1, robot2's left gripper (lgripper2) drops ball4 in room1, robot2's left gripper (lgripper2) picks up ball5 in room1, robot2's right gripper (rgripper2) drops ball3 in room1, robot2's right gripper (rgripper2) picks up ball6 in room1, robot2 moves from room1 to room2, and robot2's left gripper (lgripper2) drops ball5 in room2 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "b02aabc5-8972-4ed2-9a41-41b2e5e0ce31", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_8_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room4 to reach the current state. Some of the actions may not be executable. What is the state before the first infeasible action in the sequence? Write None if there are none", "answer": "None", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, a series of actions are proposed: robot1's lgripper1 is intended to pick up ball7 in room4 to achieve the current state. However, some actions may not be feasible. What is the state preceding the first unexecutable action in the sequence? If none exist, please state None.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "84ff5b61-29e1-408e-9a1e-23b257391d45", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_6_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4, from room1, robot1's lgripper1 picks up ball1, from room1, robot1 moves to room2, rgripper1 of robot1 picks up ball2 in room2, robot1 moves to room3 from room2, in room3, robot1's lgripper1 drops ball1, robot1 moves to room4 from room3, ball2 is dropped in room4 with rgripper1 by robot1, robot1 moves from room4 to room2 and ball4 is picked from room2 with lgripper1 by robot1 to reach the current state. What are the valid properties of the state that involve negations for ball6 before the first infeasible action in the sequence? Write None if there are none", "answer": "None", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1 relocates from room4 to room1, then in room1, robot1's left gripper (lgripper1) grasps ball1, next, robot1 moves from room1 to room2, where robot1's right gripper (rgripper1) picks up ball2, robot1 proceeds to room3 from room2, in room3, robot1's lgripper1 releases ball1, robot1 then moves from room3 to room4, and in room4, robot1's rgripper1 drops ball2, subsequently, robot1 moves from room4 back to room2, and finally, robot1's lgripper1 picks up ball4 in room2 to attain the current state. What are the valid properties of the state that involve negations for ball6 prior to the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "4ae0c162-13e9-4d6f-bb56-7f3b7f4b3682", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_6_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room4, from room4, robot1 moves to room5, ball7 is dropped in room5 with lgripper1 by robot1, lgripper1 of robot1 picks up ball7 in room1, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1 moves to room5, ball1 is dropped in room5 with lgripper1 by robot1, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, lgripper1 of robot1 picks up ball2 in room2, from room2, robot1's rgripper1 picks up ball4, robot1 moves to room1 from room2, ball4 is dropped in room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball6, robot1 moves from room1 to room3, in room3, robot1's rgripper1 drops ball6, rgripper1 of robot1 picks up ball5 in room3 and robot1 moves to room6 from room3 to reach the current state. What are the valid properties of the state that do not involve negations for lgripper1 before the first infeasible action in the sequence? Write None if there are none", "answer": "lgripper1 of robot1 is free", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1's lgripper1 grasps ball7 in room4, then robot1 proceeds from room4 to room5, and subsequently drops ball7 in room5 using lgripper1. Next, robot1's lgripper1 picks up ball7 in room1, followed by picking up ball1 in room1 with the same gripper. Meanwhile, robot1's rgripper1 picks up ball3 in room1, and then robot1 moves from room1 to room5. In room5, robot1 drops ball1 using lgripper1 and ball3 using rgripper1. Robot1 then moves from room5 to room2, where lgripper1 picks up ball2. In room2, robot1's rgripper1 picks up ball4, and then robot1 moves back to room1. In room1, robot1 drops ball4 using rgripper1, and then rgripper1 picks up ball6. Robot1 proceeds from room1 to room3, where rgripper1 drops ball6, and then picks up ball5 in room3. Finally, robot1 moves from room3 to room6 to reach the current state. What are the valid properties of the state that do not involve negations for lgripper1 before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "394a13f6-c8f7-4114-9ad6-74f4897a4987", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_5_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball6 is dropped in room1 with rgripper1 by robot1, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1 moves to room5, in room5, robot1's rgripper1 drops ball3, robot1 moves from room5 to room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves to room1 from room2, rgripper1 of robot1 drops ball4 in room1, from room1, robot1's rgripper1 picks up ball6, robot1 moves to room3 from room1, ball6 is dropped in room3 with rgripper1 by robot1, ball7 is picked from room3 with rgripper1 by robot1, robot1 moves from room3 to room4, in room4, robot1's lgripper1 drops ball1, rgripper1 of robot1 drops ball7 in room4, robot1 moves to room2 from room4, from room2, robot1's lgripper1 picks up ball2 and rgripper1 of robot1 picks up ball5 in room2 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is located at room1, ball1 is not at room2, ball1 is not at room5, ball1 is not located at room3, ball1 is not located at room4, ball2 is at room2, ball2 is not at room1, ball2 is not at room3, ball2 is not at room4, ball2 is not at room5, ball3 is located at room1, ball3 is not at room4, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's rgripper1, ball3 is not present at room2, ball3 is not present at room3, ball3 is not present at room5, ball4 is not at room1, ball4 is not at room3, ball4 is not at room4, ball4 is not being carried by robot1's rgripper1, ball4 is not present at room5, ball4 is present at room2, ball5 is not at room1, ball5 is not being carried by robot1's lgripper1, ball5 is not located at room3, ball5 is not located at room4, ball5 is not located at room5, ball5 is present at room2, ball6 is not at room2, ball6 is not at room3, ball6 is not present at room4, ball6 is not present at room5, ball6 is present at room1, ball7 is located at room3, ball7 is not at room2, ball7 is not being carried by robot1's lgripper1, ball7 is not located at room4, ball7 is not present at room1, ball7 is not present at room5, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball2, robot1 is located at room4, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not located at room5, robot1 is not present in room1, robot1 is not present in room2, robot1 is not present in room3 and robot1's lgripper1 is free", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1 drops ball6 in room1 using rgripper1, then robot1's lgripper1 picks up ball1 in room1, followed by robot1's rgripper1 picking up ball3 in room1, then robot1 moves to room5, and in room5, robot1's rgripper1 drops ball3, after which robot1 moves to room2, and in room2, robot1's rgripper1 picks up ball4, then robot1 moves back to room1, and in room1, robot1's rgripper1 drops ball4, followed by robot1's rgripper1 picking up ball6 in room1, then robot1 moves to room3, and in room3, robot1 drops ball6 using rgripper1, and robot1 picks up ball7 in room3 using rgripper1, then robot1 moves to room4, and in room4, robot1's lgripper1 drops ball1, and robot1's rgripper1 drops ball7, after which robot1 moves to room2, and in room2, robot1's lgripper1 picks up ball2 and robot1's rgripper1 picks up ball5 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper, lgripper1, is available and robot1's right gripper, rgripper1, is unoccupied."}
{"question_id": "6b9933f5-492e-4d44-ab3b-f9f4f9ad9109", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "composite", "question_name": "iter_6_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball3 is dropped in room5 with rgripper1 by robot1 to reach the current state. What are the valid properties of the state (both with and without negations) for ball6 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball6 is located at room1, ball6 is not located at room3, ball6 is not present at room2, ball6 is not present at room4, ball6 is not present at room5, robot1 is not carrying ball6 with lgripper1 and robot1 is not carrying ball6 with rgripper1", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is at room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are intended to be executed: robot1 uses rgripper1 to drop ball3 in room5 to achieve the current state. What are the valid properties (including both affirmative and negated properties) of ball6 prior to the first infeasible action in the sequence? If there are no valid properties, please state None.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is found in room2, ball6 is in room1, ball7 is situated in room3, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "985cbbdd-649d-4588-a0f4-7a2f81cd7c7a", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_5_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball1 is picked from room1 with lgripper1 by robot1 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not at room2, ball1 is not at room4, ball1 is not at room5, ball1 is not being carried by robot1's lgripper1, ball1 is not located at room6, ball1 is not present at room3, ball1 is present at room1, ball2 is at room2, ball2 is not being carried by robot1's lgripper1, ball2 is not located at room1, ball2 is not located at room4, ball2 is not located at room5, ball2 is not present at room3, ball2 is not present at room6, ball3 is at room1, ball3 is not at room2, ball3 is not located at room3, ball3 is not located at room4, ball3 is not located at room6, ball3 is not present at room5, ball4 is not at room3, ball4 is not at room5, ball4 is not at room6, ball4 is not being carried by robot1's lgripper1, ball4 is not located at room1, ball4 is not located at room4, ball4 is present at room2, ball5 is at room3, ball5 is not at room4, ball5 is not at room5, ball5 is not located at room1, ball5 is not present at room2, ball5 is not present at room6, ball6 is at room1, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room6, ball6 is not present at room2, ball6 is not present at room3, ball6 is not present at room4, ball6 is not present at room5, ball7 is located at room4, ball7 is not at room2, ball7 is not being carried by robot1's rgripper1, ball7 is not located at room3, ball7 is not located at room5, ball7 is not located at room6, ball7 is not present at room1, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball6, robot1 is at room4, robot1 is not at room2, robot1 is not at room3, robot1 is not at room5, robot1 is not at room6, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not present in room1 and robot1's lgripper1 is available", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are intended to be executed: robot1 uses lgripper1 to pick up ball1 from room1 to achieve the current state. What valid properties of the state (including both affirmative and negated properties) hold true prior to the first infeasible action in the sequence? If none exist, please state None.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "ce3473d1-6b67-4d9e-bfed-309fe4630533", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_5_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball7 is picked from room4 with lgripper1 by robot1, robot1 moves from room4 to room5, ball7 is dropped in room5 with lgripper1 by robot1, robot1 moves to room1 from room5, ball1 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 picks up ball3 in room1, from room1, robot1 moves to room5, in room5, robot1's lgripper1 drops ball1, in room5, robot1's rgripper1 drops ball3 and robot1 moves from room5 to room2 to reach the current state. What valid properties of the state (both with and without negations) are true before the first infeasible action in the sequence? Write None if there are none", "answer": "None", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: robot1, using lgripper1, retrieves ball7 from room4, then proceeds to room5, where it releases ball7 using lgripper1. Next, robot1 moves to room1, picks up ball1 with lgripper1, and simultaneously uses rgripper1 to pick up ball3 in room1. From room1, robot1 heads to room5, where it drops ball1 using lgripper1 and ball3 using rgripper1, and finally moves to room2 to reach the current state. What valid properties of the state (including both affirmative and negated properties) hold true before the first infeasible action in the sequence? If none exist, please state 'None'.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "ab067073-cca3-498e-a73c-42fa24357076", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_6_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: rgripper1 of robot1 drops ball2 in room4 to reach the current state. What are the valid properties of the state that do not involve negations for ball5 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball5 is at room2", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are intended: robot1's rgripper1 releases ball2 in room4 to achieve the current state. What are the valid, non-negated properties of the state for ball5 prior to the first infeasible action in the sequence? If none exist, state None.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "bed2e87f-6b5f-4cef-aec7-6ee3382bc657", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_8_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves to room3 from room2, ball1 is picked from room3 with lgripper2 by robot2, from room3, robot2's rgripper2 picks up ball2, robot2 moves to room2 from room3, in room2, robot2's lgripper2 drops ball1, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball4, from room3, robot2's rgripper2 picks up ball7 and lgripper2 of robot1 picks up ball4 in room1 to reach the current state. Some of the actions may not be executable. What is the state before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is at room2, ball1 is not at room3, ball1 is not being carried by robot1's lgripper2, ball1 is not being carried by robot1's rgripper1, ball1 is not located at room1, ball2 is at room2, ball2 is not being carried by robot1's lgripper1, ball2 is not being carried by robot2's lgripper2, ball2 is not located at room1, ball2 is not located at room3, ball3 is located at room2, ball3 is not at room1, ball3 is not at room3, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's rgripper1, ball3 is not being carried by robot1's rgripper2, ball3 is not being carried by robot2's lgripper1, ball3 is not being carried by robot2's lgripper2, ball3 is not being carried by robot2's rgripper1, ball3 is not being carried by robot2's rgripper2, ball4 is not at room2, ball4 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's rgripper1, ball4 is not being carried by robot1's rgripper2, ball4 is not being carried by robot2's lgripper1, ball4 is not being carried by robot2's rgripper1, ball4 is not located at room1, ball4 is not located at room3, ball5 is at room1, ball5 is not being carried by robot1's lgripper1, ball5 is not being carried by robot2's lgripper2, ball5 is not located at room3, ball5 is not present at room2, ball6 is not at room2, ball6 is not at room3, ball6 is not being carried by robot1's lgripper2, ball6 is not being carried by robot2's lgripper1, ball6 is not being carried by robot2's lgripper2, ball6 is not being carried by robot2's rgripper1, ball6 is present at room1, ball7 is not at room1, ball7 is not being carried by robot1's lgripper2, ball7 is not being carried by robot2's lgripper2, ball7 is not being carried by robot2's rgripper1, ball7 is not located at room3, ball7 is not present at room2, lgripper1 of robot2 is not carrying ball1, lgripper1 of robot2 is not carrying ball2, lgripper1 of robot2 is not carrying ball5, lgripper1 of robot2 is not carrying ball7, lgripper2 of robot1 is not carrying ball2, lgripper2 of robot1 is not carrying ball4, lgripper2 of robot1 is not free, lgripper2 of robot2 is carrying ball4, lgripper2 of robot2 is not carrying ball1, rgripper1 of robot1 is free, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball6, rgripper1 of robot2 is not carrying ball1, rgripper1 of robot2 is not carrying ball2, rgripper2 of robot1 is not carrying ball2, rgripper2 of robot1 is not carrying ball6, rgripper2 of robot1 is not free, rgripper2 of robot2 is carrying ball7, rgripper2 of robot2 is not carrying ball1, robot1 is located at room2, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball1 with rgripper2, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball3 with lgripper2, robot1 is not carrying ball5 with lgripper2, robot1 is not carrying ball5 with rgripper2, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not carrying ball7 with rgripper1, robot1 is not carrying ball7 with rgripper2, robot1 is not located at room1, robot1 is not present in room3, robot1's lgripper1 is available, robot2 is located at room3, robot2 is not carrying ball2 with rgripper2, robot2 is not carrying ball4 with rgripper2, robot2 is not carrying ball5 with rgripper1, robot2 is not carrying ball5 with rgripper2, robot2 is not carrying ball6 with rgripper2, robot2 is not located at room2, robot2 is not present in room1, robot2's lgripper1 is not free, robot2's lgripper2 is not available, robot2's rgripper1 is not free and robot2's rgripper2 is not available", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: robot2 will move from room2 to room3, then use its left gripper (lgripper2) to pick up ball1 in room3, followed by its right gripper (rgripper2) picking up ball2 in the same room. Next, robot2 will move back to room2 from room3, where it will drop ball1 using lgripper2 and ball2 using rgripper2. After that, robot2 will return to room3 from room2, pick up ball4 with lgripper2, and also pick up ball7 with rgripper2. Meanwhile, robot1's lgripper will pick up ball4 in room1 to achieve the current state. However, some actions in this sequence may not be feasible. What is the state prior to the first unexecutable action in the sequence, or None if all actions are executable?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "3f9d2f84-1e3d-4ce2-8cbb-6421b51caa6a", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_5_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: rgripper1 of robot1 drops ball7 in room6 to reach the current state. What valid properties of the state that do not involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is located at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is present at room3, ball6 is located at room1, ball7 is located at room4, lgripper1 of robot1 is free, rgripper1 of robot1 is free and robot1 is at room4", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Considering the initial condition, the following actions are intended: robot1's rgripper1 releases ball7 in room6 to achieve the current state. What valid, non-negated properties of the state hold true prior to the first infeasible action in the sequence? If none exist, state None.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "fe2f7cb5-3257-4e8f-a6f6-512590075795", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_6_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball1, from room3, robot2's rgripper2 picks up ball2, robot2 moves from room3 to room2, from room3, robot1's rgripper1 picks up ball2, in room2, robot2's rgripper2 drops ball2, from room2, robot2 moves to room3, lgripper2 of robot2 picks up ball4 in room3, from room3, robot2's rgripper2 picks up ball7 and robot2 moves from room3 to room2 to reach the current state. What are the valid properties of the state that involve negations for robot1 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is not being carried by robot1's rgripper1, ball2 is not being carried by robot1's rgripper2, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's lgripper2, ball3 is not being carried by robot1's rgripper2, ball4 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's rgripper1, ball4 is not being carried by robot1's rgripper2, ball5 is not being carried by robot1's rgripper2, ball6 is not being carried by robot1's rgripper1, ball7 is not being carried by robot1's lgripper1, ball7 is not being carried by robot1's rgripper1, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not carrying ball6, lgripper2 of robot1 is not carrying ball2, lgripper2 of robot1 is not carrying ball6, lgripper2 of robot1 is not carrying ball7, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball5, rgripper2 of robot1 is not carrying ball7, rgripper2 of robot1 is not free, robot1 is not at room1, robot1 is not at room3, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball1 with lgripper2, robot1 is not carrying ball1 with rgripper2, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with lgripper2, robot1 is not carrying ball5 with lgripper2, robot1 is not carrying ball6 with rgripper2 and robot1's lgripper2 is not available", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot2 moves from room2 to room3, then from room3, robot2's left gripper (lgripper2) grasps ball1, and from room3, robot2's right gripper (rgripper2) grasps ball2, after which robot2 moves from room3 to room2, then from room3, robot1's right gripper (rgripper1) grasps ball2, in room2, robot2's right gripper (rgripper2) releases ball2, from room2, robot2 moves to room3, robot2's left gripper (lgripper2) picks up ball4 in room3, from room3, robot2's right gripper (rgripper2) picks up ball7, and robot2 moves from room3 to room2 to reach the current state. What are the valid properties of the state that involve negations for robot1 before the first infeasible action in the sequence? Write None if there are none", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "d9935b16-26aa-432f-a9fe-a86fa79fa037", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_6_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball2 is dropped in room4 with rgripper1 by robot1 to reach the current state. What are the valid properties of the state that involve negations for ball3 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball3 is not at room3, ball3 is not being carried by robot1's rgripper1, ball3 is not located at room4, ball3 is not present at room2, ball3 is not present at room5 and lgripper1 of robot1 is not carrying ball3", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled: robot1 uses rgripper1 to drop ball2 in room4 to achieve the current state. What are the valid state properties involving negations for ball3 prior to the first infeasible action in the sequence? If none exist, please state None.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "261d29ef-1a6b-4a68-91ac-46a1d801cc54", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_6_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: rgripper2 of robot2 drops ball6 in room2, from room3, robot2's lgripper2 picks up ball1, rgripper2 of robot2 picks up ball2 in room3, robot2 moves from room3 to room2, lgripper2 of robot2 drops ball1 in room2, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball4, from room3, robot2's rgripper2 picks up ball7, robot2 moves to room2 from room3, rgripper2 of robot2 drops ball7 in room2, rgripper2 of robot2 picks up ball3 in room2, from room2, robot2 moves to room1, in room1, robot2's lgripper2 drops ball4, lgripper2 of robot2 picks up ball5 in room1, ball3 is dropped in room1 with rgripper2 by robot2, rgripper2 of robot2 picks up ball6 in room1, robot2 moves to room2 from room1 and lgripper2 of robot2 drops ball5 in room2 to reach the current state. What are the valid properties of the state that involve negations for ball3 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball3 is not at room1, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's lgripper2, ball3 is not being carried by robot2's lgripper2, ball3 is not being carried by robot2's rgripper2, ball3 is not located at room3, lgripper1 of robot2 is not carrying ball3, rgripper2 of robot1 is not carrying ball3, robot1 is not carrying ball3 with rgripper1 and robot2 is not carrying ball3 with rgripper1", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot2's right gripper (rgripper2) releases ball6 in room2 after moving from room3, then robot2's left gripper (lgripper2) picks up ball1 in room3, followed by rgripper2 of robot2 picking up ball2 in room3, robot2 then moves from room3 to room2, after which lgripper2 of robot2 releases ball1 in room2, and rgripper2 of robot2 drops ball2 in room2, robot2 then moves back to room3, picks up ball4 with lgripper2 and ball7 with rgripper2 in room3, moves to room2, and drops ball7 with rgripper2, picks up ball3 with rgripper2 in room2, moves to room1, drops ball4 with lgripper2, picks up ball5 with lgripper2 in room1, drops ball3 with rgripper2, picks up ball6 with rgripper2 in room1, moves back to room2, and finally drops ball5 with lgripper2 to reach the current state. What are the valid properties of the state that involve negations for ball3 before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "f6e60018-8829-4f6f-8644-7dad6917d7f1", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_6_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball7 is picked from room3 with rgripper1 by robot2, lgripper2 of robot2 picks up ball1 in room3, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves from room3 to room2, in room2, robot2's lgripper2 drops ball1, ball2 is dropped in room2 with rgripper2 by robot2, from room2, robot2 moves to room3, ball4 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball7 in room3 and robot2 moves from room3 to room2 to reach the current state. What are the valid properties of the state (both with and without negations) for ball4 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball4 is not at room2, ball4 is not being carried by robot1's rgripper1, ball4 is not being carried by robot2's lgripper2, ball4 is not present at room1, ball4 is present at room3, lgripper1 of robot2 is not carrying ball4, lgripper2 of robot1 is not carrying ball4, rgripper1 of robot2 is not carrying ball4, rgripper2 of robot2 is not carrying ball4, robot1 is not carrying ball4 with lgripper1 and robot1 is not carrying ball4 with rgripper2", "plan_length": 10, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to be executed: robot2 uses rgripper1 to pick up ball7 in room3, then uses lgripper2 to pick up ball1 in room3, followed by picking up ball2 in room3 with rgripper2, then moves from room3 to room2. In room2, robot2 drops ball1 using lgripper2 and drops ball2 using rgripper2. Next, robot2 moves back to room3, picks up ball4 using lgripper2, and picks up ball7 using rgripper2 before moving to room2 again to reach the current state. What are the valid properties of the state (both with and without negations) for ball4 before the first infeasible action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is positioned in room1, ball6 is found in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is situated in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
{"question_id": "6cdb5944-ae87-45f4-9658-df83d469ed59", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "composite", "question_name": "iter_6_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball7 is picked from room6 with lgripper1 by robot1 to reach the current state. What are the valid properties of the state (both with and without negations) for ball1 before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is located at room1, ball1 is not at room2, ball1 is not at room5, ball1 is not at room6, ball1 is not located at room3, ball1 is not present at room4, rgripper1 of robot1 is not carrying ball1 and robot1 is not carrying ball1 with lgripper1", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room3, ball6 is located at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is present in room4 and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are intended to be executed: robot1 uses lgripper1 to pick up ball7 from room6 to achieve the current state. What are the valid properties (including both affirmative and negated properties) of ball1 prior to the first infeasible action in the sequence? If there are no valid properties, please state None.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, ball7 is in room4, the left gripper of robot1 is available, robot1 is in room4 and the right gripper of robot1 is available."}
{"question_id": "fb99ee60-dd76-4464-93d4-125daa10fcfb", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_6_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1 to reach the current state. What are the valid properties of the state that do not involve negations for room4 before the first infeasible action in the sequence? Write None if there are none", "answer": "None", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking", "action_executability", "object_tracking"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1 to reach the current state. What are the non-negated properties of the state for room4 that hold true before the first infeasible action in the sequence? If none exist, write None.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is present at room2, ball6 is present in room1, robot1 is in room4, robot1 has an available lgripper1 and an available rgripper1."}
{"question_id": "19cf589f-9077-4918-a885-8436718fff2c", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "composite", "question_name": "iter_5_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room3, robot1's rgripper1 picks up ball5 to reach the current state. What valid properties of the state that do not involve negations are true before the first infeasible action in the sequence? Write None if there are none", "answer": "ball1 is located at room1, ball2 is present at room2, ball3 is present at room1, ball4 is at room2, ball5 is at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is free", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is located at room2, ball6 is located at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are intended: robot1's rgripper1 in room3 is set to pick up ball5 to achieve the current state. What non-negated properties of the state are valid prior to the first infeasible action in the sequence? If none exist, state None.", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is situated in room1, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "6c8e9886-640a-44cc-adf3-514bdac3e93a", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "composite", "question_name": "iter_8_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room2, robot2 moves to room3, from room3, robot2's lgripper2 picks up ball1, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves from room3 to room2, in room2, robot2's lgripper2 drops ball1, in room2, robot2's rgripper2 drops ball2, robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball4, rgripper2 of robot2 picks up ball7 in room3, robot2 moves to room2 from room3, in room2, robot2's rgripper2 drops ball7, rgripper2 of robot2 picks up ball3 in room2, robot2 moves from room2 to room1, ball4 is dropped in room1 with lgripper2 by robot2, ball5 is picked from room1 with lgripper2 by robot2, in room1, robot2's rgripper2 drops ball3, rgripper2 of robot2 picks up ball6 in room1, from room1, robot2 moves to room2 and lgripper2 of robot2 drops ball5 in room2 to reach the current state. Some of the actions may not be executable. What is the state before the first infeasible action in the sequence? Write None if there are none", "answer": "None", "plan_length": 19, "initial_state_nl": "Ball1 is at room3, ball2 is present at room3, ball3 is present at room2, ball4 is present at room3, ball5 is at room1, ball6 is located at room1, ball7 is at room3, lgripper1 of robot1 is free, robot1 is present in room2, robot1's rgripper1 is available, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": ["state_tracking", "action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned: starting from room2, robot2 is set to move to room3, then in room3, robot2's left gripper (lgripper2) will pick up ball1 and its right gripper (rgripper2) will pick up ball2, after which robot2 will move back to room2. In room2, robot2 will drop ball1 with lgripper2 and ball2 with rgripper2. Then, robot2 will move back to room3, where it will pick up ball4 with lgripper2 and ball7 with rgripper2. Robot2 will then move to room2, drop ball7 with rgripper2, and pick up ball3 with rgripper2. Next, robot2 will move to room1, drop ball4 with lgripper2, pick up ball5 with lgripper2, drop ball3 with rgripper2, and pick up ball6 with rgripper2. Finally, robot2 will move back to room2 and drop ball5 with lgripper2 to reach the current state. Some actions in the sequence may not be executable. What is the state before the first infeasible action in the sequence? If there are no infeasible actions, write None.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is found in room3, ball5 is positioned in room1, ball6 is situated in room1, ball7 is in room3, the left gripper of robot1 is unoccupied, robot1 is currently in room2, the right gripper of robot1 is available, robot2 is also in room2, the left gripper of robot2 is unoccupied and the right gripper of robot2 is available."}
