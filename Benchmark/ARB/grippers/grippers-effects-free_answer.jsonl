{"question_id": "68d05b0f-d685-4e95-a30b-243a3cb6ce78", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, if ball1 is picked from room1 with lgripper1 by robot1, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "ball1 is not located at room2, ball1 is not located at room4, ball1 is not located at room5, ball1 is not present at room1, ball1 is not present at room3, ball2 is at room2, ball2 is not at room1, ball2 is not at room3, ball2 is not at room5, ball2 is not being carried by robot1's lgripper1, ball2 is not located at room4, ball3 is at room1, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's rgripper1, ball3 is not located at room3, ball3 is not present at room2, ball3 is not present at room4, ball3 is not present at room5, ball4 is at room2, ball4 is not at room4, ball4 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room1, ball4 is not located at room5, ball4 is not present at room3, ball5 is at room2, ball5 is not at room4, ball5 is not at room5, ball5 is not located at room3, ball5 is not present at room1, ball6 is at room1, ball6 is not at room3, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room5, ball6 is not present at room2, ball6 is not present at room4, ball7 is at room3, ball7 is not at room1, ball7 is not at room2, ball7 is not being carried by robot1's lgripper1, ball7 is not being carried by robot1's rgripper1, ball7 is not located at room5, ball7 is not present at room4, lgripper1 of robot1 is carrying ball1, rgripper1 of robot1 is not carrying ball5, robot1 is at room1, robot1 is not at room2, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball5 with lgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not located at room3, robot1 is not present in room4, robot1 is not present in room5, robot1's lgripper1 is not free and robot1's rgripper1 is free", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is present at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot1 relocates from room4 to room1 to achieve the current state. In this state, if robot1 uses lgripper1 to pick up ball1 from room1, what are all the valid properties of the state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is situated in room1, ball4 is found in room2, ball5 is also found in room2, ball6 is situated in room1, ball7 is found in room3, the right gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's left gripper is available."}
{"question_id": "dae44438-a174-4b7c-a66f-a7e5e63c85ed", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, from room1, robot1's lgripper1 picks up ball1, from room1, robot1 moves to room2, rgripper1 of robot1 picks up ball2 in room2, from room2, robot1 moves to room3, lgripper1 of robot1 drops ball1 in room3, robot1 moves from room3 to room4, rgripper1 of robot1 drops ball2 in room4, robot1 moves to room2 from room4, ball4 is picked from room2 with lgripper1 by robot1, ball5 is picked from room2 with rgripper1 by robot1, from room2, robot1 moves to room5, lgripper1 of robot1 drops ball4 in room5, from room5, robot1 moves to room1, lgripper1 of robot1 picks up ball3 in room1, in room1, robot1's rgripper1 drops ball5, rgripper1 of robot1 picks up ball6 in room1, robot1 moves from room1 to room5 and in room5, robot1's lgripper1 drops ball3 to reach the current state. In this state, if rgripper1 of robot1 drops ball6 in room5, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "ball1 is at room3, ball2 is at room4, ball3 is at room5, ball4 is at room5, ball5 is present at room1, ball6 is present at room5, robot1 is located at room5, robot1's lgripper1 is free and robot1's rgripper1 is free", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 relocates from room4 to room1, then its left gripper (lgripper1) collects ball1 from room1. Next, robot1 proceeds to room2, where its right gripper (rgripper1) picks up ball2. From room2, robot1 moves to room3, where lgripper1 releases ball1, and then robot1 returns to room4, dropping ball2 via rgripper1. Subsequently, robot1 travels back to room2, where it uses lgripper1 to pick up ball4 and rgripper1 to pick up ball5. Robot1 then moves to room5, where lgripper1 drops ball4, and from there, it proceeds to room1. In room1, lgripper1 collects ball3, while rgripper1 releases ball5 and then picks up ball6. Finally, robot1 moves to room5, where lgripper1 drops ball3, resulting in the current state. In this state, if robot1's rgripper1 releases ball6 in room5, what are all the valid properties of the state that do not involve negations? If none exist, state 'None'.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, the left gripper of robot1 is unoccupied, robot1 is positioned at room4, and robot1's right gripper is available."}
{"question_id": "d221bf2b-c90c-4d17-8213-81339721f2c4", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3 to reach the current state. In this state, if from room3, robot2's lgripper2 picks up ball1, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "ball2 is present at room3, ball3 is located at room2, ball4 is at room3, ball5 is present at room1, ball6 is located at room1, ball7 is at room3, lgripper2 of robot2 is carrying ball1, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is located at room3 and robot2's rgripper2 is available", "plan_length": 1, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot2 moves from room2 to room3 to achieve the current state. In this state, if robot2's lgripper2 picks up ball1 from room3, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is present in room3, ball5 is found in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is free, the right gripper of robot2 is also free, robot1 is positioned in room2, the right gripper of robot1 is available and robot2 is also in room2."}
{"question_id": "c6eef8fb-867c-4dfa-95fb-ad445a3adb01", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: lgripper1 of robot1 picks up ball7 in room4, robot1 moves from room4 to room5, lgripper1 of robot1 drops ball7 in room5, from room5, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves to room5 from room1, ball1 is dropped in room5 with lgripper1 by robot1, rgripper1 of robot1 drops ball3 in room5 and robot1 moves to room2 from room5 to reach the current state. In this state, if lgripper1 of robot1 picks up ball2 in room2, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "ball1 is at room5, ball3 is present at room5, ball4 is present at room2, ball5 is at room3, ball6 is at room1, ball7 is present at room5, robot1 is at room2, robot1 is carrying ball2 with lgripper1 and robot1's rgripper1 is available", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is present at room3, ball6 is present at room1, ball7 is located at room4, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1's left gripper (lgripper1) picks up ball7 in room4, then robot1 moves to room5, and lgripper1 drops ball7 in room5. Next, robot1 moves to room1, where it picks up ball1 with lgripper1 and its right gripper (rgripper1) picks up ball3. Robot1 then moves to room5, drops ball1 with lgripper1, and rgripper1 drops ball3 in room5. Finally, robot1 moves to room2. In this state, if lgripper1 of robot1 picks up ball2 in room2, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is also in room1, ball4 is also in room2, ball5 is in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is available."}
{"question_id": "5062e6c1-0b93-408d-98f4-148c803c5dd8", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, from room1, robot1's lgripper1 picks up ball1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1 moves to room5, in room5, robot1's rgripper1 drops ball3, robot1 moves to room2 from room5, rgripper1 of robot1 picks up ball4 in room2, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball6, from room1, robot1 moves to room3, ball6 is dropped in room3 with rgripper1 by robot1, from room3, robot1's rgripper1 picks up ball7, from room3, robot1 moves to room4, ball1 is dropped in room4 with lgripper1 by robot1, in room4, robot1's rgripper1 drops ball7, from room4, robot1 moves to room2, ball2 is picked from room2 with lgripper1 by robot1 and from room2, robot1's rgripper1 picks up ball5 to reach the current state. In this state, if robot1 moves to room5 from room2, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "ball1 is at room4, ball1 is not at room3, ball1 is not at room5, ball1 is not being carried by robot1's lgripper1, ball1 is not present at room1, ball1 is not present at room2, ball2 is not at room3, ball2 is not at room4, ball2 is not at room5, ball2 is not present at room1, ball2 is not present at room2, ball3 is at room5, ball3 is not at room2, ball3 is not at room3, ball3 is not being carried by robot1's lgripper1, ball3 is not located at room1, ball3 is not located at room4, ball4 is not being carried by robot1's lgripper1, ball4 is not located at room2, ball4 is not present at room3, ball4 is not present at room4, ball4 is not present at room5, ball4 is present at room1, ball5 is not at room2, ball5 is not at room5, ball5 is not located at room1, ball5 is not located at room3, ball5 is not located at room4, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room2, ball6 is not located at room5, ball6 is not present at room1, ball6 is not present at room4, ball6 is present at room3, ball7 is located at room4, ball7 is not at room2, ball7 is not being carried by robot1's lgripper1, ball7 is not being carried by robot1's rgripper1, ball7 is not located at room3, ball7 is not present at room1, ball7 is not present at room5, lgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not free, robot1 is at room5, robot1 is carrying ball2 with lgripper1, robot1 is carrying ball5 with rgripper1, robot1 is not at room4, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not present in room1, robot1 is not present in room2, robot1 is not present in room3 and robot1's lgripper1 is not available", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is present at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 relocates from room4 to room1, then in room1, robot1's left gripper (lgripper1) grasps ball1, and robot1's right gripper (rgripper1) picks up ball3. Next, robot1 moves from room1 to room5, where it releases ball3 using rgripper1. Robot1 then proceeds to room2 from room5, and in room2, rgripper1 of robot1 picks up ball4. Subsequently, robot1 moves from room2 back to room1, drops ball4 in room1 using rgripper1, and then picks up ball6 in room1 with rgripper1. Robot1 then moves to room3, drops ball6 in room3 using rgripper1, and picks up ball7 in room3 with rgripper1. After that, robot1 moves to room4, drops ball1 in room4 using lgripper1, and releases ball7 in room4 using rgripper1. Finally, robot1 moves to room2, picks up ball2 in room2 using lgripper1, and picks up ball5 in room2 using rgripper1, resulting in the current state. If, from this state, robot1 moves to room5 from room2, what are all the valid properties of the resulting state, including both affirmative and negated properties? If none exist, state 'None'.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is located in room2, ball6 is found in room1, ball7 is present in room3, the right gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's left gripper is available."}
{"question_id": "4dbc7bec-fe44-40ce-be5f-b478a7d5bf71", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball1, rgripper2 of robot2 picks up ball2 in room3, robot2 moves to room2 from room3, lgripper2 of robot2 drops ball1 in room2, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves to room3 from room2, lgripper2 of robot2 picks up ball4 in room3, from room3, robot2's rgripper2 picks up ball7, robot2 moves to room2 from room3, in room2, robot2's rgripper2 drops ball7, from room2, robot2's rgripper2 picks up ball3, from room2, robot2 moves to room1, in room1, robot2's lgripper2 drops ball4, lgripper2 of robot2 picks up ball5 in room1, ball3 is dropped in room1 with rgripper2 by robot2, from room1, robot2's rgripper2 picks up ball6, from room1, robot2 moves to room2 and ball5 is dropped in room2 with lgripper2 by robot2 to reach the current state. In this state, if rgripper2 of robot2 drops ball6 in room2, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "ball1 is located at room2, ball2 is at room2, ball3 is at room1, ball4 is at room1, ball5 is present at room2, ball6 is located at room2, ball7 is present at room2, rgripper2 of robot2 is free, robot1 is located at room2, robot1's lgripper1 is available, robot1's rgripper1 is free, robot2 is at room2 and robot2's lgripper2 is available", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot2 relocates from room2 to room3, then its left gripper (lgripper2) picks up ball1 in room3, while its right gripper (rgripper2) picks up ball2 in the same room. Robot2 then moves back to room2, where it drops ball1 using lgripper2 and ball2 using rgripper2. Next, robot2 returns to room3, where lgripper2 picks up ball4 and rgripper2 picks up ball7. Robot2 then moves back to room2, drops ball7 using rgripper2, picks up ball3 using rgripper2, and proceeds to room1. In room1, lgripper2 drops ball4, picks up ball5, and rgripper2 drops ball3. Then, rgripper2 picks up ball6, and robot2 moves to room2, where it drops ball5 using lgripper2, reaching the current state. In this state, if robot2's rgripper2 drops ball6 in room2, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is present in room3, ball5 is found in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is free, the right gripper of robot2 is also free, robot1 is positioned in room2, the right gripper of robot1 is available, and robot2 is also in room2."}
{"question_id": "bbf87938-50b8-4a56-8f0f-2d060ab5b138", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4 to reach the current state. In this state, if ball1 is picked from room1 with lgripper1 by robot1, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "ball1 is not being carried by robot1's rgripper1, ball1 is not located at room2, ball1 is not located at room3, ball1 is not located at room4, ball1 is not located at room5, ball1 is not present at room1, ball2 is not located at room5, ball2 is not present at room1, ball2 is not present at room3, ball2 is not present at room4, ball3 is not at room2, ball3 is not at room3, ball3 is not being carried by robot1's lgripper1, ball3 is not located at room5, ball3 is not present at room4, ball4 is not being carried by robot1's rgripper1, ball4 is not located at room3, ball4 is not present at room1, ball4 is not present at room4, ball4 is not present at room5, ball5 is not at room1, ball5 is not at room4, ball5 is not located at room3, ball5 is not located at room5, ball6 is not at room4, ball6 is not located at room2, ball6 is not present at room3, ball6 is not present at room5, ball7 is not at room4, ball7 is not being carried by robot1's rgripper1, ball7 is not present at room1, ball7 is not present at room2, ball7 is not present at room5, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not free, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball6, robot1 is not at room3, robot1 is not at room4, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with lgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not located at room5 and robot1 is not present in room2", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is present at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1 relocates from room4 to room1 to achieve the current state. In this state, if robot1 uses lgripper1 to pick up ball1 from room1, what are all the valid state properties that involve negations? If there are no such properties, state None.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is located in room2, ball6 is found in room1, ball7 is situated in room3, the right gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's left gripper is available."}
{"question_id": "60773eaf-988e-46ca-b58a-37696703b40e", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, in room5, robot1's rgripper1 drops ball3, from room5, robot1 moves to room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves to room1 from room2, in room1, robot1's rgripper1 drops ball4 and from room1, robot1's rgripper1 picks up ball6 to reach the current state. In this state, if robot1 moves to room3 from room1, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "ball1 is not at room1, ball1 is not located at room2, ball1 is not located at room4, ball1 is not located at room5, ball1 is not present at room3, ball2 is located at room2, ball2 is not at room1, ball2 is not at room3, ball2 is not located at room4, ball2 is not present at room5, ball3 is at room5, ball3 is not at room2, ball3 is not being carried by robot1's rgripper1, ball3 is not located at room3, ball3 is not located at room4, ball3 is not present at room1, ball4 is not at room4, ball4 is not at room5, ball4 is not being carried by robot1's lgripper1, ball4 is not present at room2, ball4 is not present at room3, ball4 is present at room1, ball5 is located at room2, ball5 is not at room3, ball5 is not at room4, ball5 is not being carried by robot1's lgripper1, ball5 is not located at room1, ball5 is not present at room5, ball6 is not being carried by robot1's lgripper1, ball6 is not located at room1, ball6 is not located at room2, ball6 is not located at room4, ball6 is not located at room5, ball6 is not present at room3, ball7 is at room3, ball7 is not at room4, ball7 is not being carried by robot1's rgripper1, ball7 is not located at room1, ball7 is not present at room2, ball7 is not present at room5, lgripper1 of robot1 is carrying ball1, lgripper1 of robot1 is not carrying ball7, rgripper1 of robot1 is carrying ball6, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not free, robot1 is at room3, robot1 is not carrying ball2 with lgripper1, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not located at room1, robot1 is not located at room5, robot1 is not present in room2, robot1 is not present in room4 and robot1's lgripper1 is not available", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is present at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 moves from room4 to room1, then uses its left gripper (lgripper1) to pick up ball1 in room1, and its right gripper (rgripper1) to pick up ball3 in room1. Next, robot1 moves to room5, where it drops ball3 using rgripper1. From room5, robot1 proceeds to room2, picks up ball4 with rgripper1, and then returns to room1. In room1, robot1 drops ball4 with rgripper1 and then picks up ball6 with the same gripper, resulting in the current state. If robot1 moves from room1 to room3 in this state, what are all the valid properties of the resulting state, including both affirmative and negated properties? If there are no valid properties, indicate None.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is located in room2, ball6 is found in room1, ball7 is situated in room3, the right gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's left gripper is available."}
{"question_id": "d65e7edb-db9c-4f4c-8282-478fb7cf7f93", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, lgripper2 of robot2 picks up ball1 in room3, from room3, robot2's rgripper2 picks up ball2, robot2 moves from room3 to room2, ball1 is dropped in room2 with lgripper2 by robot2, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves to room3 from room2, lgripper2 of robot2 picks up ball4 in room3, rgripper2 of robot2 picks up ball7 in room3 and robot2 moves to room2 from room3 to reach the current state. In this state, if in room2, robot2's rgripper2 drops ball7, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "ball1 is not at room3, ball1 is not being carried by robot2's lgripper2, ball1 is not being carried by robot2's rgripper1, ball1 is not present at room1, ball2 is not being carried by robot1's lgripper2, ball2 is not being carried by robot1's rgripper2, ball2 is not located at room1, ball2 is not located at room3, ball3 is not at room3, ball3 is not being carried by robot1's lgripper2, ball3 is not being carried by robot1's rgripper2, ball3 is not located at room1, ball4 is not at room1, ball4 is not at room3, ball4 is not being carried by robot2's rgripper1, ball4 is not being carried by robot2's rgripper2, ball4 is not present at room2, ball5 is not being carried by robot1's rgripper1, ball5 is not being carried by robot1's rgripper2, ball5 is not being carried by robot2's rgripper1, ball5 is not located at room3, ball5 is not present at room2, ball6 is not at room2, ball6 is not at room3, ball7 is not at room3, ball7 is not being carried by robot1's lgripper2, ball7 is not present at room1, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not carrying ball6, lgripper1 of robot1 is not carrying ball7, lgripper1 of robot2 is not carrying ball2, lgripper1 of robot2 is not carrying ball4, lgripper1 of robot2 is not carrying ball5, lgripper1 of robot2 is not carrying ball6, lgripper1 of robot2 is not carrying ball7, lgripper2 of robot1 is not carrying ball1, lgripper2 of robot1 is not carrying ball5, lgripper2 of robot1 is not free, lgripper2 of robot2 is not carrying ball2, lgripper2 of robot2 is not carrying ball7, lgripper2 of robot2 is not free, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot2 is not carrying ball6, rgripper2 of robot1 is not carrying ball1, rgripper2 of robot2 is not carrying ball2, rgripper2 of robot2 is not carrying ball3, rgripper2 of robot2 is not carrying ball5, rgripper2 of robot2 is not carrying ball7, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball4 with lgripper2, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball4 with rgripper2, robot1 is not carrying ball6 with lgripper2, robot1 is not carrying ball6 with rgripper1, robot1 is not carrying ball6 with rgripper2, robot1 is not carrying ball7 with rgripper1, robot1 is not carrying ball7 with rgripper2, robot1 is not located at room3, robot1 is not present in room1, robot1's rgripper2 is not available, robot2 is not carrying ball1 with lgripper1, robot2 is not carrying ball1 with rgripper2, robot2 is not carrying ball2 with rgripper1, robot2 is not carrying ball3 with lgripper1, robot2 is not carrying ball3 with lgripper2, robot2 is not carrying ball3 with rgripper1, robot2 is not carrying ball5 with lgripper2, robot2 is not carrying ball6 with lgripper2, robot2 is not carrying ball6 with rgripper2, robot2 is not carrying ball7 with rgripper1, robot2 is not located at room3, robot2 is not present in room1, robot2's lgripper1 is not free and robot2's rgripper1 is not available", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot2 relocates from room2 to room3, robot2's left gripper (lgripper2) grasps ball1 in room3, then robot2's right gripper (rgripper2) picks up ball2 in room3, robot2 returns to room2 from room3, and in room2, robot2's lgripper2 releases ball1 and rgripper2 releases ball2. Subsequently, robot2 moves back to room3 from room2, where lgripper2 of robot2 picks up ball4 and rgripper2 of robot2 picks up ball7, and then robot2 moves to room2 from room3, resulting in the current state. In this state, if robot2's rgripper2 drops ball7 in room2, what are all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is present in room3, ball5 is found in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is free, the right gripper of robot2 is also free, robot1 is positioned in room2, the right gripper of robot1 is available and robot2 is also in room2."}
{"question_id": "263de4c4-d357-413a-83a3-c5b4b0784c8c", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1 moves to room2, from room2, robot1's rgripper1 picks up ball2, robot1 moves from room2 to room3, lgripper1 of robot1 drops ball1 in room3, from room3, robot1 moves to room4, ball2 is dropped in room4 with rgripper1 by robot1, from room4, robot1 moves to room2 and from room2, robot1's lgripper1 picks up ball4 to reach the current state. In this state, if ball5 is picked from room2 with rgripper1 by robot1, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "ball1 is not located at room1, ball1 is not located at room5, ball1 is not present at room2, ball1 is not present at room4, ball2 is not at room2, ball2 is not at room3, ball2 is not being carried by robot1's lgripper1, ball2 is not being carried by robot1's rgripper1, ball2 is not located at room1, ball2 is not present at room5, ball3 is not at room2, ball3 is not at room4, ball3 is not present at room3, ball3 is not present at room5, ball4 is not at room1, ball4 is not at room4, ball4 is not located at room3, ball4 is not located at room5, ball4 is not present at room2, ball5 is not at room5, ball5 is not being carried by robot1's lgripper1, ball5 is not located at room1, ball5 is not located at room2, ball5 is not present at room3, ball5 is not present at room4, ball6 is not located at room5, ball6 is not present at room2, ball6 is not present at room3, ball6 is not present at room4, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball6, lgripper1 of robot1 is not free, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not free, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not located at room4, robot1 is not present in room1, robot1 is not present in room3 and robot1 is not present in room5", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: robot1 relocates from room4 to room1, robot1's left gripper (lgripper1) grasps ball1 in room1, then robot1 proceeds from room1 to room2, where robot1's right gripper (rgripper1) picks up ball2, robot1 moves from room2 to room3, and in room3, lgripper1 releases ball1, after which robot1 moves from room3 to room4, and in room4, rgripper1 drops ball2, then robot1 moves from room4 back to room2, and finally, in room2, lgripper1 picks up ball4, resulting in the current state. In this state, if robot1 uses rgripper1 to pick up ball5 in room2, what would be all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, the left gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's right gripper is available."}
{"question_id": "badbcc2e-564e-4590-93f8-cc30340bd0f8", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1 moves to room2, ball2 is picked from room2 with rgripper1 by robot1, robot1 moves to room3 from room2, lgripper1 of robot1 drops ball1 in room3, robot1 moves to room4 from room3, rgripper1 of robot1 drops ball2 in room4, robot1 moves to room2 from room4 and lgripper1 of robot1 picks up ball4 in room2 to reach the current state. In this state, if ball5 is picked from room2 with rgripper1 by robot1, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "ball1 is at room3, ball2 is present at room4, ball3 is present at room1, ball5 is being carried by robot1's rgripper1, ball6 is located at room1, robot1 is at room2 and robot1 is carrying ball4 with lgripper1", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 relocates from room4 to room1, then uses lgripper1 to pick up ball1 in room1, proceeds to room2 from room1, uses rgripper1 to pick up ball2 in room2, moves to room3 from room2, drops ball1 in room3 using lgripper1, returns to room4 from room3, drops ball2 in room4 using rgripper1, and finally moves back to room2 from room4, where it picks up ball4 using lgripper1, resulting in the current state. In this state, if robot1 uses rgripper1 to pick up ball5 in room2, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, the left gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's right gripper is available."}
{"question_id": "6791458c-3463-4577-b1ba-432347da70f5", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves to room2 from room5, rgripper1 of robot1 picks up ball4 in room2, robot1 moves to room1 from room2, in room1, robot1's rgripper1 drops ball4, from room1, robot1's rgripper1 picks up ball6, from room1, robot1 moves to room3, rgripper1 of robot1 drops ball6 in room3, from room3, robot1's rgripper1 picks up ball7, robot1 moves to room4 from room3, in room4, robot1's lgripper1 drops ball1, in room4, robot1's rgripper1 drops ball7, from room4, robot1 moves to room2, lgripper1 of robot1 picks up ball2 in room2 and ball5 is picked from room2 with rgripper1 by robot1 to reach the current state. In this state, if from room2, robot1 moves to room5, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "ball1 is not at room2, ball1 is not being carried by robot1's lgripper1, ball1 is not located at room5, ball1 is not present at room1, ball1 is not present at room3, ball2 is not at room1, ball2 is not at room5, ball2 is not located at room3, ball2 is not present at room2, ball2 is not present at room4, ball3 is not at room3, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot1's rgripper1, ball3 is not located at room2, ball3 is not located at room4, ball3 is not present at room1, ball4 is not at room4, ball4 is not at room5, ball4 is not located at room2, ball4 is not present at room3, ball5 is not at room3, ball5 is not located at room1, ball5 is not located at room2, ball5 is not present at room4, ball5 is not present at room5, ball6 is not at room2, ball6 is not at room5, ball6 is not being carried by robot1's lgripper1, ball6 is not present at room1, ball6 is not present at room4, ball7 is not being carried by robot1's rgripper1, ball7 is not located at room2, ball7 is not located at room3, ball7 is not located at room5, ball7 is not present at room1, lgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball6, robot1 is not at room2, robot1 is not at room4, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball7 with lgripper1, robot1 is not present in room1, robot1 is not present in room3, robot1's lgripper1 is not available and robot1's rgripper1 is not available", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is present at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1 relocates from room4 to room1, then uses its left gripper (lgripper1) to pick up ball1 in room1, and its right gripper (rgripper1) to pick up ball3 in room1. From room1, robot1 proceeds to room5, where it drops ball3 using rgripper1. Next, robot1 moves to room2 from room5, picks up ball4 in room2 with rgripper1, and then returns to room1. In room1, robot1 drops ball4 using rgripper1, picks up ball6 with rgripper1, and then moves to room3. In room3, robot1 drops ball6 using rgripper1, picks up ball7 with rgripper1, and then moves to room4. In room4, robot1 drops ball1 using lgripper1 and ball7 using rgripper1. Finally, robot1 moves to room2, where it picks up ball2 with lgripper1 and ball5 with rgripper1, resulting in the current state. If, from room2, robot1 moves to room5, what would be all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is located in room2, ball6 is found in room1, ball7 is situated in room3, the right gripper of robot1 is unoccupied, robot1 is positioned in room4 and the left gripper of robot1 is available."}
{"question_id": "67183b7d-e6ae-481c-bb0d-6343c8f12732", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, if lgripper1 of robot1 picks up ball1 in room1, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "ball1 is not at room2, ball1 is not at room4, ball1 is not being carried by robot1's rgripper1, ball1 is not located at room1, ball1 is not located at room5, ball1 is not present at room3, ball2 is not at room3, ball2 is not at room5, ball2 is not located at room4, ball2 is not present at room1, ball3 is not at room4, ball3 is not located at room2, ball3 is not located at room5, ball3 is not present at room3, ball4 is not at room1, ball4 is not at room3, ball4 is not being carried by robot1's lgripper1, ball4 is not located at room4, ball4 is not present at room5, ball5 is not at room3, ball5 is not at room5, ball5 is not being carried by robot1's rgripper1, ball5 is not located at room1, ball5 is not present at room4, ball6 is not at room2, ball6 is not at room3, ball6 is not at room4, ball6 is not being carried by robot1's lgripper1, ball6 is not present at room5, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not free, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball4, robot1 is not at room4, robot1 is not at room5, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball6 with rgripper1, robot1 is not located at room3 and robot1 is not present in room2", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1 moves from room4 to room1 to achieve the current state. In this state, if robot1's lgripper1 picks up ball1 in room1, what are all the valid state properties that involve negations? If there are no such properties, write None.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, the left gripper of robot1 is unoccupied, robot1 is positioned at room4, and robot1's right gripper is available."}
