{"question_id": "9d1bafde-4ced-4ecc-a20c-c1c8df01b809", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3, ball1 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball2 in room3, from room3, robot2 moves to room2, lgripper2 of robot2 drops ball1 in room2, ball2 is dropped in room2 with rgripper2 by robot2, from room2, robot2 moves to room3, from room3, robot2's lgripper2 picks up ball4, ball7 is picked from room3 with rgripper2 by robot2 and robot2 moves to room2 from room3 to reach the current state. In this state, if ball7 is dropped in room2 with rgripper2 by robot2, is it True or False that robot2's rgripper1 is not free?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot2 moves from room2 to room3, then uses its lgripper2 to pick up ball1 in room3, and its rgripper2 to pick up ball2 in room3. Next, robot2 moves back to room2, where it drops ball1 with lgripper2 and ball2 with rgripper2. Then, robot2 returns to room3, picks up ball4 with lgripper2 and ball7 with rgripper2, and moves back to room2, resulting in the current state. In this state, if robot2 drops ball7 in room2 using rgripper2, is it True or False that robot2's rgripper1 is not free?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is present in room3, ball5 is found in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is free, the right gripper of robot2 is also free, robot1 is positioned in room2, the right gripper of robot1 is available, and robot2 is also in room2."}
{"question_id": "4cec0366-d024-46dc-9a70-c83c0d5971ef", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1, from room4, robot1 moves to room5, in room5, robot1's lgripper1 drops ball7, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, lgripper1 of robot1 drops ball1 in room5, in room5, robot1's rgripper1 drops ball3, robot1 moves from room5 to room2, from room2, robot1's lgripper1 picks up ball2, rgripper1 of robot1 picks up ball4 in room2, from room2, robot1 moves to room1, ball4 is dropped in room1 with rgripper1 by robot1, ball6 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room3, ball6 is dropped in room3 with rgripper1 by robot1, rgripper1 of robot1 picks up ball5 in room3 and robot1 moves to room6 from room3 to reach the current state. In this state, if lgripper1 of robot1 drops ball2 in room6, is it True or False that rgripper1 of robot1 is not carrying ball6?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is present at room3, ball6 is present at room1, ball7 is located at room4, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1, using lgripper1, picks up ball7 from room4, then moves from room4 to room5, and in room5, robot1's lgripper1 releases ball7. Next, robot1 moves from room5 to room1, where it uses lgripper1 to pick up ball1 and rgripper1 to pick up ball3. Then, robot1 moves from room1 to room5, where it drops ball1 with lgripper1 and ball3 with rgripper1. After that, robot1 moves from room5 to room2, picks up ball2 with lgripper1 and ball4 with rgripper1, and then moves from room2 to room1. In room1, robot1 drops ball4 with rgripper1, picks up ball6 with rgripper1, and then moves to room3. In room3, robot1 drops ball6 with rgripper1, picks up ball5 with rgripper1, and finally moves from room3 to room6 to reach the current state. In this state, if robot1's lgripper1 drops ball2 in room6, is it True or False that robot1's rgripper1 is not holding ball6?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is also in room1, ball4 is also in room2, ball5 is in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is available."}
{"question_id": "abbb6416-fe3d-43b4-85c1-5c6da1347b12", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4 to reach the current state. In this state, if lgripper1 of robot1 picks up ball1 in room1, is it True or False that ball1 is not present at room1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is present at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, if the left gripper of robot1 (lgripper1) picks up ball1 in room1, is it True or False that ball1 is no longer present in room1?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is located in room2, ball6 is found in room1, ball7 is present in room3, the right gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's left gripper is available."}
{"question_id": "ab8e9ba8-0282-4df2-9d17-9f27d012a519", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, if lgripper1 of robot1 picks up ball1 in room1, is it True or False that ball1 is being carried by robot1's lgripper1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, if robot1's lgripper1 picks up ball1 in room1, is it True or False that robot1's lgripper1 is carrying ball1?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, the left gripper of robot1 is unoccupied, robot1 is positioned at room4, and robot1's right gripper is available."}
{"question_id": "18b2eda1-5ed8-49cf-be67-c34c40ed6c4c", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves to room2 from room1, rgripper1 of robot1 picks up ball2 in room2, robot1 moves to room3 from room2, lgripper1 of robot1 drops ball1 in room3, robot1 moves from room3 to room4, rgripper1 of robot1 drops ball2 in room4, robot1 moves from room4 to room2 and from room2, robot1's lgripper1 picks up ball4 to reach the current state. In this state, if rgripper1 of robot1 picks up ball5 in room2, is it True or False that robot1's lgripper1 is not available?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 relocates from room4 to room1, then uses lgripper1 to pick up ball1 in room1, proceeds to room2 from room1, and utilizes rgripper1 to grasp ball2 in room2. Next, robot1 moves to room3 from room2, where lgripper1 releases ball1, and then robot1 returns to room4 from room3. In room4, rgripper1 drops ball2, and robot1 moves back to room2, where lgripper1 picks up ball4, resulting in the current state. In this state, if robot1's rgripper1 picks up ball5 in room2, is it True or False that robot1's lgripper1 is unavailable?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, the left gripper of robot1 is unoccupied, robot1 is positioned at room4, and robot1's right gripper is available."}
{"question_id": "c3916ccd-3079-4e75-bc7f-b9f8fef0e8d4", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1 moves to room2, from room2, robot1's rgripper1 picks up ball2, from room2, robot1 moves to room3, ball1 is dropped in room3 with lgripper1 by robot1, robot1 moves to room4 from room3, rgripper1 of robot1 drops ball2 in room4, robot1 moves to room2 from room4 and lgripper1 of robot1 picks up ball4 in room2 to reach the current state. In this state, if from room2, robot1's rgripper1 picks up ball5, is it True or False that ball4 is present at room1?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 relocates from room4 to room1, then uses lgripper1 to pick up ball1 in room1, proceeds to room2, and uses rgripper1 to pick up ball2 in room2. Next, robot1 moves to room3, drops ball1 using lgripper1, and then heads back to room4, where it drops ball2 using rgripper1. Finally, robot1 returns to room2 and uses lgripper1 to pick up ball4, resulting in the current state. In this state, if robot1's rgripper1 picks up ball5 in room2, is it True or False that ball4 is present in room1?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, the left gripper of robot1 is unoccupied, robot1 is positioned at room4, and robot1's right gripper is available."}
{"question_id": "cc671669-4df5-41f8-8ee3-07c6ae71efa1", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, if lgripper1 of robot1 picks up ball1 in room1, is it True or False that lgripper1 of robot1 is carrying ball1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, if the left gripper of robot1 (lgripper1) picks up ball1 in room1, is it True or False that the left gripper of robot1 (lgripper1) is holding ball1?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, the left gripper of robot1 is unoccupied, robot1 is positioned at room4, and robot1's right gripper is available."}
{"question_id": "0b7790e8-03f2-44e9-82bc-8b71b9284386", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves from room1 to room2, ball2 is picked from room2 with rgripper1 by robot1, from room2, robot1 moves to room3, ball1 is dropped in room3 with lgripper1 by robot1, robot1 moves to room4 from room3, rgripper1 of robot1 drops ball2 in room4, from room4, robot1 moves to room2 and from room2, robot1's lgripper1 picks up ball4 to reach the current state. In this state, if rgripper1 of robot1 picks up ball5 in room2, is it True or False that robot1's rgripper1 is not available?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 relocates from room4 to room1, then uses lgripper1 to pick up ball1 in room1, proceeds to room2, and uses rgripper1 to pick up ball2. From room2, robot1 moves to room3, where it drops ball1 using lgripper1, then moves to room4, and drops ball2 using rgripper1. Finally, robot1 returns to room2 and uses lgripper1 to pick up ball4, resulting in the current state. In this state, if robot1's rgripper1 picks up ball5 in room2, is it True or False that robot1's rgripper1 is not available?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, the left gripper of robot1 is unoccupied, robot1 is positioned at room4, and robot1's right gripper is available."}
{"question_id": "db15873c-842b-45e2-9d01-9bc8f826ab31", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1, from room4, robot1 moves to room5, in room5, robot1's lgripper1 drops ball7, robot1 moves to room1 from room5, ball1 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves to room5 from room1, in room5, robot1's lgripper1 drops ball1, ball3 is dropped in room5 with rgripper1 by robot1, from room5, robot1 moves to room2, from room2, robot1's lgripper1 picks up ball2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room1, in room1, robot1's rgripper1 drops ball4, ball6 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room3, rgripper1 of robot1 drops ball6 in room3, from room3, robot1's rgripper1 picks up ball5 and robot1 moves to room6 from room3 to reach the current state. In this state, if lgripper1 of robot1 drops ball2 in room6, is it True or False that robot1's rgripper1 is available?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is present at room3, ball6 is present at room1, ball7 is located at room4, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1, using lgripper1, picks up ball7 from room4, then moves to room5, where it drops ball7 using lgripper1. Next, robot1 moves to room1, picks up ball1 with lgripper1, and simultaneously uses rgripper1 to pick up ball3. Robot1 then proceeds to room5, drops ball1 with lgripper1, and drops ball3 with rgripper1. From room5, robot1 moves to room2, picks up ball2 with lgripper1, and ball4 with rgripper1. It then moves to room1, drops ball4 with rgripper1, picks up ball6 with rgripper1, and moves to room3. In room3, robot1 drops ball6 with rgripper1, picks up ball5 with rgripper1, and finally moves to room6, reaching the current state. In this state, if robot1's lgripper1 drops ball2 in room6, is it True or False that robot1's rgripper1 is available?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is also in room1, ball4 is also in room2, ball5 is in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is available."}
{"question_id": "531e6599-1fd7-4248-a19a-d81c6e0fe845", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, if from room1, robot1's lgripper1 picks up ball1, is it True or False that robot1's lgripper1 is available?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, if from room1, robot1's lgripper1 picks up ball1, is it True or False that robot1's lgripper1 is available for use?\n\nParaphrased text maintains the same structure and meaning as the original text but with slight rewording for clarity.", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, the left gripper of robot1 is unoccupied, robot1 is positioned at room4, and robot1's right gripper is available."}
{"question_id": "da1363df-dfd7-4d08-83c2-797882ecc1b8", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1 to reach the current state. In this state, if robot1 moves to room5 from room4, is it True or False that robot1 is not at room4 and robot1 is present in room5?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is present at room3, ball6 is present at room1, ball7 is located at room4, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot1 uses lgripper1 to pick up ball7 from room4. In the resulting state, if robot1 moves from room4 to room5, is it True or False that robot1 is no longer in room4 and is now present in room5?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is also in room1, ball4 is also in room2, ball5 is in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is available."}
{"question_id": "ad4f271d-372e-41bd-9058-1051ad6fa574", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3, ball1 is picked from room3 with lgripper2 by robot2, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves from room3 to room2, in room2, robot2's lgripper2 drops ball1, ball2 is dropped in room2 with rgripper2 by robot2, from room2, robot2 moves to room3, lgripper2 of robot2 picks up ball4 in room3, rgripper2 of robot2 picks up ball7 in room3, robot2 moves to room2 from room3, in room2, robot2's rgripper2 drops ball7, from room2, robot2's rgripper2 picks up ball3, from room2, robot2 moves to room1, lgripper2 of robot2 drops ball4 in room1, from room1, robot2's lgripper2 picks up ball5, in room1, robot2's rgripper2 drops ball3, ball6 is picked from room1 with rgripper2 by robot2, robot2 moves from room1 to room2 and in room2, robot2's lgripper2 drops ball5 to reach the current state. In this state, if ball6 is dropped in room2 with rgripper2 by robot2, is it True or False that rgripper2 of robot1 is not carrying ball4?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot2 moves from room2 to room3, then it uses lgripper2 to pick up ball1 in room3 and uses rgripper2 to pick up ball2 in room3, after which robot2 moves back to room2. In room2, robot2 drops ball1 with lgripper2 and drops ball2 with rgripper2. Then, robot2 moves from room2 to room3, picks up ball4 with lgripper2 and ball7 with rgripper2 in room3, and moves back to room2. In room2, robot2 drops ball7 with rgripper2, picks up ball3 with rgripper2, and then moves to room1. In room1, robot2 drops ball4 with lgripper2, picks up ball5 with lgripper2, drops ball3 with rgripper2, and picks up ball6 with rgripper2. Finally, robot2 moves to room2 and drops ball5 with lgripper2 to reach the current state. In this state, if robot2 drops ball6 in room2 with rgripper2, is it True or False that rgripper2 of robot1 is not carrying ball4?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is found in room3, ball5 resides in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is free, the right gripper of robot2 is also free, robot1 is positioned in room2, the right gripper of robot1 is available and robot2 is also in room2."}
{"question_id": "be4b5344-40b6-4d16-b84a-592899197281", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1 moves to room2, ball2 is picked from room2 with rgripper1 by robot1, from room2, robot1 moves to room3, in room3, robot1's lgripper1 drops ball1, robot1 moves to room4 from room3, rgripper1 of robot1 drops ball2 in room4, robot1 moves to room2 from room4 and lgripper1 of robot1 picks up ball4 in room2 to reach the current state. In this state, if rgripper1 of robot1 picks up ball5 in room2, is it True or False that rgripper1 of robot1 is carrying ball5?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 moves from room4 to room1, then uses its left gripper (lgripper1) to pick up ball1 in room1. Next, robot1 moves from room1 to room2, where it uses its right gripper (rgripper1) to pick up ball2. From room2, robot1 proceeds to room3, drops ball1 using lgripper1, and then moves back to room4. In room4, robot1 drops ball2 using rgripper1, then moves to room2 and uses lgripper1 to pick up ball4, resulting in the current state. In this state, if robot1's right gripper (rgripper1) picks up ball5 in room2, is it True or False that rgripper1 of robot1 is carrying ball5?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, the left gripper of robot1 is unoccupied, robot1 is positioned at room4, and robot1's right gripper is available."}
{"question_id": "dea72274-f357-40a6-98fd-6d6344766cff", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7, from room4, robot1 moves to room5, ball7 is dropped in room5 with lgripper1 by robot1, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball1, rgripper1 of robot1 picks up ball3 in room1, from room1, robot1 moves to room5, in room5, robot1's lgripper1 drops ball1, rgripper1 of robot1 drops ball3 in room5 and from room5, robot1 moves to room2 to reach the current state. In this state, if lgripper1 of robot1 picks up ball2 in room2, is it True or False that ball4 is not located at room2?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is present at room3, ball6 is present at room1, ball7 is located at room4, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: robot1's lgripper1 collects ball7 from room4, then robot1 proceeds to room5, where it releases ball7 using lgripper1, after which robot1 moves to room1. In room1, robot1's lgripper1 picks up ball1, and its rgripper1 collects ball3. Robot1 then moves to room5, where it drops ball1 with lgripper1 and ball3 with rgripper1. Finally, robot1 moves from room5 to room2, reaching the current state. In this state, if robot1's lgripper1 picks up ball2 in room2, is it True or False that ball4 is not located in room2?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is also in room1, ball4 is also in room2, ball5 is in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "3359ee63-2742-4f4c-aa13-434c9cb37dd5", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, robot1 moves from room1 to room2, rgripper1 of robot1 picks up ball2 in room2, robot1 moves to room3 from room2, lgripper1 of robot1 drops ball1 in room3, from room3, robot1 moves to room4, rgripper1 of robot1 drops ball2 in room4, robot1 moves to room2 from room4, from room2, robot1's lgripper1 picks up ball4, rgripper1 of robot1 picks up ball5 in room2, from room2, robot1 moves to room5, in room5, robot1's lgripper1 drops ball4, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball3, ball5 is dropped in room1 with rgripper1 by robot1, rgripper1 of robot1 picks up ball6 in room1, robot1 moves to room5 from room1 and ball3 is dropped in room5 with lgripper1 by robot1 to reach the current state. In this state, if rgripper1 of robot1 drops ball6 in room5, is it True or False that ball6 is at room5?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 moves from room4 to room1, then its left gripper (lgripper1) picks up ball1 in room1. Next, robot1 moves to room2, where its right gripper (rgripper1) picks up ball2. Robot1 then proceeds to room3, where it drops ball1 using lgripper1, and subsequently moves back to room4, dropping ball2 with rgripper1. From room4, robot1 returns to room2, picks up ball4 with lgripper1, and also picks up ball5 with rgripper1. Robot1 then moves to room5, drops ball4 with lgripper1, and proceeds to room1, where it picks up ball3 with lgripper1 and drops ball5 with rgripper1. Additionally, rgripper1 picks up ball6 in room1. Finally, robot1 moves to room5 and drops ball3 with lgripper1, reaching the current state. In this state, if robot1's rgripper1 drops ball6 in room5, is it True or False that ball6 is at room5?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, the left gripper of robot1 is unoccupied, robot1 is positioned at room4, and robot1's right gripper is available."}
{"question_id": "b783fc24-4704-4138-b961-90c3455b9297", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, if lgripper1 of robot1 picks up ball1 in room1, is it True or False that lgripper1 of robot1 is not carrying ball5?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is present at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, if the left gripper of robot1 (lgripper1) picks up ball1 in room1, is it True or False that the left gripper of robot1 is not holding ball5?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is located in room2, ball6 is found in room1, ball7 is situated in room3, the right gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's left gripper is available."}
{"question_id": "e8775c28-c002-4dd8-a04e-4f256f32e02e", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: lgripper1 of robot1 picks up ball7 in room4, robot1 moves from room4 to room5, lgripper1 of robot1 drops ball7 in room5, robot1 moves to room1 from room5, ball1 is picked from room1 with lgripper1 by robot1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves to room5 from room1, ball1 is dropped in room5 with lgripper1 by robot1, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, from room2, robot1's lgripper1 picks up ball2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1, ball6 is picked from room1 with rgripper1 by robot1, robot1 moves to room3 from room1, rgripper1 of robot1 drops ball6 in room3, rgripper1 of robot1 picks up ball5 in room3 and robot1 moves from room3 to room6 to reach the current state. In this state, if lgripper1 of robot1 drops ball2 in room6, is it True or False that ball2 is located at room6?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is present at room3, ball6 is present at room1, ball7 is located at room4, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1's left gripper (lgripper1) picks up ball7 in room4, then robot1 moves to room5, and lgripper1 drops ball7 in room5. Next, robot1 moves back to room1, where it picks up ball1 with lgripper1 and ball3 with its right gripper (rgripper1). Robot1 then moves to room5, drops ball1 with lgripper1, and drops ball3 with rgripper1. After that, robot1 moves to room2, picks up ball2 with lgripper1 and ball4 with rgripper1, and then moves to room1. In room1, robot1 drops ball4 with rgripper1, picks up ball6 with rgripper1, and moves to room3. In room3, rgripper1 drops ball6 and picks up ball5. Finally, robot1 moves to room6. Now, if robot1's lgripper1 drops ball2 in room6, is it True or False that ball2 is located in room6?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is also in room1, ball4 is also in room2, ball5 is in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "1dd25a69-6970-41d8-9c8c-0a74c12ba013", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, from room1, robot1's lgripper1 picks up ball1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves from room1 to room5, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves from room5 to room2, from room2, robot1's rgripper1 picks up ball4, robot1 moves to room1 from room2, rgripper1 of robot1 drops ball4 in room1 and rgripper1 of robot1 picks up ball6 in room1 to reach the current state. In this state, if robot1 moves from room1 to room3, is it True or False that ball4 is at room1 and ball6 is not at room3?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is present at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 relocates from room4 to room1, then robot1's left gripper (lgripper1) grasps ball1 in room1, and its right gripper (rgripper1) picks up ball3 in the same room. Next, robot1 moves from room1 to room5, where it releases ball3 using rgripper1. Robot1 then proceeds to room2, where rgripper1 picks up ball4. Subsequently, robot1 returns to room1 from room2, drops ball4 in room1 using rgripper1, and then uses rgripper1 to grasp ball6 in room1, resulting in the current state. In this state, if robot1 moves from room1 to room3, is it True or False that ball4 remains in room1 and ball6 is not in room3?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is situated in room1, ball4 is found in room2, ball5 is also found in room2, ball6 is situated in room1, ball7 is found in room3, the right gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's left gripper is available."}
{"question_id": "995f0d22-8812-4786-9ae1-69efd721adb6", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2 to reach the current state. In this state, if ball1 is picked from room3 with lgripper2 by robot2, is it True or False that robot2 is at room1?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3 to reach the current state. In this state, if robot2 picks up ball1 from room3 using lgripper2, is it True or False that robot2 is located at room1?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is found in room3, ball5 is positioned in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is unoccupied, the right gripper of robot2 is also unoccupied, robot1 is currently in room2, the right gripper of robot1 is available, and robot2 is also in room2."}
{"question_id": "a0d04570-133f-46aa-af48-a4b88b600f24", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2 to reach the current state. In this state, if ball1 is picked from room3 with lgripper2 by robot2, is it True or False that ball1 is not at room3?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3 to reach the current state. In this state, if robot2 picks up ball1 from room3 using lgripper2, is it True or False that ball1 is no longer at room3?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is found in room3, ball5 resides in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is free, the right gripper of robot2 is also free, robot1 is positioned in room2, the right gripper of robot1 is available, and robot2 is also in room2."}
{"question_id": "71816864-397b-4aeb-886e-808a5d95eef1", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, ball1 is picked from room3 with lgripper2 by robot2, ball2 is picked from room3 with rgripper2 by robot2, from room3, robot2 moves to room2, ball1 is dropped in room2 with lgripper2 by robot2, in room2, robot2's rgripper2 drops ball2, from room2, robot2 moves to room3, from room3, robot2's lgripper2 picks up ball4, ball7 is picked from room3 with rgripper2 by robot2, robot2 moves from room3 to room2, ball7 is dropped in room2 with rgripper2 by robot2, from room2, robot2's rgripper2 picks up ball3, robot2 moves to room1 from room2, lgripper2 of robot2 drops ball4 in room1, lgripper2 of robot2 picks up ball5 in room1, in room1, robot2's rgripper2 drops ball3, rgripper2 of robot2 picks up ball6 in room1, robot2 moves to room2 from room1 and in room2, robot2's lgripper2 drops ball5 to reach the current state. In this state, if ball6 is dropped in room2 with rgripper2 by robot2, is it True or False that robot1's rgripper1 is not free?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 relocates from room2 to room3, then it uses lgripper2 to pick up ball1 from room3, and subsequently uses rgripper2 to pick up ball2 from room3. Next, robot2 moves back to room2 from room3, drops ball1 in room2 using lgripper2, and then drops ball2 in room2 using rgripper2. Robot2 then moves back to room3, picks up ball4 with lgripper2 and ball7 with rgripper2, and then relocates to room2. In room2, robot2 drops ball7 using rgripper2, picks up ball3 using rgripper2, and then moves to room1. In room1, robot2 drops ball4 using lgripper2, picks up ball5 using lgripper2, drops ball3 using rgripper2, and picks up ball6 using rgripper2. Finally, robot2 moves back to room2 and drops ball5 using lgripper2 to reach the current state. In this state, if robot2 drops ball6 in room2 using rgripper2, is it True or False that robot1's rgripper1 is not free?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is found in room3, ball5 resides in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is unoccupied, the left gripper of robot2 is unoccupied, the right gripper of robot2 is unoccupied, robot1 is positioned in room2, the right gripper of robot1 is available, and robot2 is also in room2."}
{"question_id": "86a51b21-6dc5-4a54-8272-020961edab99", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: lgripper1 of robot1 picks up ball7 in room4, robot1 moves from room4 to room5, ball7 is dropped in room5 with lgripper1 by robot1, robot1 moves to room1 from room5, ball1 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves to room5 from room1, ball1 is dropped in room5 with lgripper1 by robot1, in room5, robot1's rgripper1 drops ball3 and from room5, robot1 moves to room2 to reach the current state. In this state, if lgripper1 of robot1 picks up ball2 in room2, is it True or False that ball2 is not present at room2?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is present at room3, ball6 is present at room1, ball7 is located at room4, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: robot1's left gripper (lgripper1) grasps ball7 in room4, then robot1 moves to room5 from room4, and subsequently drops ball7 in room5 using lgripper1. Next, robot1 proceeds to room1 from room5, picks up ball1 in room1 with lgripper1, and its right gripper (rgripper1) picks up ball3 in room1. Robot1 then moves to room5 from room1, drops ball1 in room5 using lgripper1, and in room5, drops ball3 using rgripper1. Finally, robot1 moves to room2 from room5, reaching the current state. In this state, if robot1's lgripper1 picks up ball2 in room2, is it True or False that ball2 is no longer present in room2?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is also located in room1, ball4 is also in room2, ball5 is situated in room3, ball6 is also in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is available."}
{"question_id": "e9ec98d1-5dbe-4485-a487-a61fb9ee96ac", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room5, in room5, robot1's rgripper1 drops ball3, robot1 moves from room5 to room2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room1, in room1, robot1's rgripper1 drops ball4, ball6 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room3, in room3, robot1's rgripper1 drops ball6, ball7 is picked from room3 with rgripper1 by robot1, robot1 moves from room3 to room4, lgripper1 of robot1 drops ball1 in room4, in room4, robot1's rgripper1 drops ball7, from room4, robot1 moves to room2, lgripper1 of robot1 picks up ball2 in room2 and from room2, robot1's rgripper1 picks up ball5 to reach the current state. In this state, if robot1 moves to room5 from room2, is it True or False that robot1 is not present in room2 and robot1 is present in room5?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is present at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: starting from room4, robot1 proceeds to room1, where it uses lgripper1 to pick up ball1 and rgripper1 to pick up ball3, then moves to room5, drops ball3 using rgripper1, and continues to room2, where it picks up ball4 with rgripper1, moves back to room1, drops ball4 using rgripper1, picks up ball6 with rgripper1, moves to room3, drops ball6 using rgripper1, picks up ball7 with rgripper1, and then moves to room4, where it drops ball1 using lgripper1 and ball7 using rgripper1, before moving back to room2, where it picks up ball2 with lgripper1 and ball5 with rgripper1, ultimately reaching the current state. In this state, if robot1 moves from room2 to room5, is it True or False that robot1 is no longer in room2 and is now present in room5?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is located in room2, ball6 is found in room1, ball7 is situated in room3, the right gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's left gripper is available."}
{"question_id": "d53a81ae-64c4-4758-b3d6-25e911476106", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1, robot1 moves from room4 to room5, lgripper1 of robot1 drops ball7 in room5, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball1, from room1, robot1's rgripper1 picks up ball3, robot1 moves to room5 from room1, in room5, robot1's lgripper1 drops ball1, rgripper1 of robot1 drops ball3 in room5, from room5, robot1 moves to room2, from room2, robot1's lgripper1 picks up ball2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves to room1 from room2, ball4 is dropped in room1 with rgripper1 by robot1, ball6 is picked from room1 with rgripper1 by robot1, robot1 moves to room3 from room1, ball6 is dropped in room3 with rgripper1 by robot1, rgripper1 of robot1 picks up ball5 in room3 and robot1 moves to room6 from room3 to reach the current state. In this state, if in room6, robot1's lgripper1 drops ball2, is it True or False that lgripper1 of robot1 is not carrying ball2?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is present at room3, ball6 is present at room1, ball7 is located at room4, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: robot1 uses lgripper1 to pick up ball7 from room4, then moves from room4 to room5, and drops ball7 in room5 using lgripper1. Next, robot1 moves from room5 to room1, picks up ball1 with lgripper1, and picks up ball3 with rgripper1 in room1. Then, robot1 moves from room1 to room5, drops ball1 with lgripper1, and drops ball3 with rgripper1 in room5. After that, robot1 moves from room5 to room2, picks up ball2 with lgripper1, and picks up ball4 with rgripper1 in room2. Subsequently, robot1 moves from room2 to room1, drops ball4 with rgripper1, picks up ball6 with rgripper1 in room1, and moves from room1 to room3. In room3, robot1 drops ball6 with rgripper1 and picks up ball5 with rgripper1. Finally, robot1 moves from room3 to room6 to reach the current state. In this state, if robot1 drops ball2 with lgripper1 in room6, is it True or False that lgripper1 of robot1 is not carrying ball2?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is also located in room1, ball4 is also in room2, ball5 is situated in room3, ball6 is also in room1, ball7 is situated in room4, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "430ae46a-be15-4ebd-b680-e5dc5b4a46f1", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1 to reach the current state. In this state, if from room4, robot1 moves to room5, is it True or False that ball7 is present at room4?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is present at room3, ball6 is present at room1, ball7 is located at room4, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1 uses lgripper1 to pick up ball7 from room4, resulting in the current state. In this state, if robot1 moves from room4 to room5, is it True or False that ball7 remains in room4?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is also in room1, ball4 is also in room2, ball5 is in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is available."}
{"question_id": "bfea37e4-ad7d-44db-9833-b3997a4812e8", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3, lgripper2 of robot2 picks up ball1 in room3, from room3, robot2's rgripper2 picks up ball2, robot2 moves to room2 from room3, in room2, robot2's lgripper2 drops ball1, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves from room2 to room3, ball4 is picked from room3 with lgripper2 by robot2, ball7 is picked from room3 with rgripper2 by robot2 and robot2 moves to room2 from room3 to reach the current state. In this state, if rgripper2 of robot2 drops ball7 in room2, is it True or False that ball7 is at room2?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: robot2 moves from room2 to room3, then robot2's left gripper (lgripper2) picks up ball1 in room3, followed by robot2's right gripper (rgripper2) picking up ball2 in room3. Next, robot2 moves back to room2 from room3, and in room2, robot2's lgripper2 releases ball1, and robot2's rgripper2 drops ball2. Then, robot2 moves from room2 to room3 again, where robot2's lgripper2 picks up ball4 and robot2's rgripper2 picks up ball7. Finally, robot2 moves back to room2 from room3, reaching the current state. In this state, if robot2's rgripper2 drops ball7 in room2, is it True or False that ball7 is in room2?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is present in room3, ball5 is found in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is free, the right gripper of robot2 is also free, robot1 is positioned in room2, the right gripper of robot1 is available and robot2 is also in room2."}
{"question_id": "5875f34e-d735-4142-90bf-8e0434a2f1af", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, if ball1 is picked from room1 with lgripper1 by robot1, is it True or False that robot1's lgripper1 is not free?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, if robot1 uses lgripper1 to pick up ball1 from room1, is it True or False that robot1's lgripper1 is not available?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, the left gripper of robot1 is unoccupied, robot1 is positioned at room4, and robot1's right gripper is available."}
{"question_id": "9f2c93d4-aac2-4357-a758-76be81a16e23", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1, robot1 moves to room5 from room4, ball7 is dropped in room5 with lgripper1 by robot1, from room5, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 picks up ball3 in room1, from room1, robot1 moves to room5, lgripper1 of robot1 drops ball1 in room5, in room5, robot1's rgripper1 drops ball3 and from room5, robot1 moves to room2 to reach the current state. In this state, if ball2 is picked from room2 with lgripper1 by robot1, is it True or False that robot1's lgripper1 is not free?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is present at room3, ball6 is present at room1, ball7 is located at room4, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: robot1 uses lgripper1 to pick up ball7 from room4, then moves from room4 to room5, drops ball7 in room5 using lgripper1, proceeds from room5 to room1, picks up ball1 with lgripper1 and ball3 with rgripper1 in room1, moves from room1 to room5, drops ball1 with lgripper1 and ball3 with rgripper1 in room5, and finally moves from room5 to room2 to reach the current state. In this state, if robot1 uses lgripper1 to pick up ball2 from room2, is it True or False that robot1's lgripper1 is not free?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is also in room1, ball4 is also in room2, ball5 is in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is available."}
{"question_id": "06c31fe5-81c0-457a-a1ef-9497de126c8a", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, if from room1, robot1's lgripper1 picks up ball1, is it True or False that robot1 is carrying ball1 with lgripper1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is present at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, if robot1's lgripper1 picks up ball1 from room1, is it True or False that robot1 is holding ball1 with lgripper1?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is located in room2, ball6 is found in room1, ball7 is situated in room3, the right gripper of robot1 is unoccupied, robot1 is positioned in room4 and robot1's left gripper is available."}
{"question_id": "fe6631a7-e401-4a77-860a-aa7226c06b25", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3 to reach the current state. In this state, if from room3, robot2's lgripper2 picks up ball1, is it True or False that rgripper2 of robot1 is not free?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3 to reach the current state. In this state, if from room3, robot2's lgripper2 picks up ball1, is it True or False that the rgripper2 of robot1 is occupied?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is found in room3, ball5 resides in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is unoccupied, the right gripper of robot2 is also unoccupied, robot1 is positioned in room2, the right gripper of robot1 is available and robot2 is also in room2."}
{"question_id": "e96641df-9f6e-4fc8-b73c-049376149247", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3, from room3, robot2's lgripper2 picks up ball1, from room3, robot2's rgripper2 picks up ball2, robot2 moves to room2 from room3, lgripper2 of robot2 drops ball1 in room2, ball2 is dropped in room2 with rgripper2 by robot2, from room2, robot2 moves to room3, ball4 is picked from room3 with lgripper2 by robot2, from room3, robot2's rgripper2 picks up ball7 and robot2 moves to room2 from room3 to reach the current state. In this state, if rgripper2 of robot2 drops ball7 in room2, is it True or False that robot2's rgripper2 is free?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot2 moves from room2 to room3, then its left gripper (lgripper2) picks up ball1 in room3, and its right gripper (rgripper2) picks up ball2 in room3. Next, robot2 moves back to room2 from room3, drops ball1 in room2 using lgripper2, and drops ball2 in room2 using rgripper2. Then, robot2 moves from room2 to room3 again, picks up ball4 in room3 using lgripper2, and picks up ball7 in room3 using rgripper2 before moving back to room2. In this state, if robot2's rgripper2 drops ball7 in room2, is it True or False that robot2's rgripper2 is free?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is found in room3, ball5 resides in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is unoccupied, the right gripper of robot2 is also unoccupied, robot1 is positioned in room2, the right gripper of robot1 is available and robot2 is also in room2."}
{"question_id": "d9a7fdba-3c7e-4331-a655-d5c54f663d7d", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball1, rgripper2 of robot2 picks up ball2 in room3, robot2 moves to room2 from room3, lgripper2 of robot2 drops ball1 in room2, in room2, robot2's rgripper2 drops ball2, robot2 moves to room3 from room2, ball4 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball7 in room3, robot2 moves from room3 to room2, rgripper2 of robot2 drops ball7 in room2, rgripper2 of robot2 picks up ball3 in room2, robot2 moves to room1 from room2, in room1, robot2's lgripper2 drops ball4, lgripper2 of robot2 picks up ball5 in room1, in room1, robot2's rgripper2 drops ball3, from room1, robot2's rgripper2 picks up ball6, robot2 moves to room2 from room1 and lgripper2 of robot2 drops ball5 in room2 to reach the current state. In this state, if ball6 is dropped in room2 with rgripper2 by robot2, is it True or False that ball6 is present at room2?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 relocates from room2 to room3, then its left gripper (lgripper2) picks up ball1 in room3, while its right gripper (rgripper2) picks up ball2 in the same room. Next, robot2 moves back to room2 from room3, where it drops ball1 using lgripper2 and ball2 using rgripper2. Then, robot2 returns to room3, picks up ball4 with lgripper2 and ball7 with rgripper2, and moves back to room2. In room2, rgripper2 drops ball7 and picks up ball3. Robot2 then proceeds to room1, where it drops ball4 using lgripper2, picks up ball5 with lgripper2, and drops ball3 using rgripper2. After that, rgripper2 picks up ball6 in room1, and robot2 moves to room2. Finally, lgripper2 drops ball5 in room2, reaching the current state. In this state, if robot2 drops ball6 in room2 using rgripper2, is it True or False that ball6 is present in room2?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is found in room3, ball5 resides in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is unoccupied, the right gripper of robot2 is also unoccupied, robot1 is positioned in room2, the right gripper of robot1 is available, and robot2 is also in room2."}
{"question_id": "55727c21-cf10-45e9-bd3f-b23e30aa6794", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, ball1 is picked from room3 with lgripper2 by robot2, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves from room3 to room2, ball1 is dropped in room2 with lgripper2 by robot2, rgripper2 of robot2 drops ball2 in room2, robot2 moves to room3 from room2, ball4 is picked from room3 with lgripper2 by robot2, ball7 is picked from room3 with rgripper2 by robot2, robot2 moves from room3 to room2, ball7 is dropped in room2 with rgripper2 by robot2, from room2, robot2's rgripper2 picks up ball3, robot2 moves to room1 from room2, lgripper2 of robot2 drops ball4 in room1, from room1, robot2's lgripper2 picks up ball5, in room1, robot2's rgripper2 drops ball3, ball6 is picked from room1 with rgripper2 by robot2, robot2 moves to room2 from room1 and lgripper2 of robot2 drops ball5 in room2 to reach the current state. In this state, if rgripper2 of robot2 drops ball6 in room2, is it True or False that ball4 is not at room3?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: robot2 relocates from room2 to room3, then uses lgripper2 to pick up ball1 from room3, and uses rgripper2 to pick up ball2 from room3. Next, robot2 moves back to room2, drops ball1 in room2 using lgripper2, and drops ball2 in room2 using rgripper2. Robot2 then returns to room3, picks up ball4 with lgripper2 and ball7 with rgripper2, and moves back to room2. In room2, robot2 drops ball7 using rgripper2, picks up ball3 using rgripper2, and then moves to room1. In room1, robot2 drops ball4 using lgripper2, picks up ball5 using lgripper2, drops ball3 using rgripper2, and picks up ball6 using rgripper2. Finally, robot2 moves to room2 and drops ball5 using lgripper2, reaching the current state. In this state, if robot2's rgripper2 drops ball6 in room2, is it True or False that ball4 is not at room3?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is present in room3, ball5 is found in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is free, the right gripper of robot2 is also free, robot1 is positioned in room2, the right gripper of robot1 is available and robot2 is also in room2."}
{"question_id": "a4c54bf9-8c90-4136-8ff2-990b77332828", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball1, from room3, robot2's rgripper2 picks up ball2, robot2 moves to room2 from room3, lgripper2 of robot2 drops ball1 in room2, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves from room2 to room3, lgripper2 of robot2 picks up ball4 in room3, from room3, robot2's rgripper2 picks up ball7 and from room3, robot2 moves to room2 to reach the current state. In this state, if in room2, robot2's rgripper2 drops ball7, is it True or False that ball2 is not at room2?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot2 relocates from room2 to room3, then from room3, robot2's left gripper (lgripper2) grasps ball1, and from room3, robot2's right gripper (rgripper2) grasps ball2. Next, robot2 moves back to room2 from room3, and upon arrival, lgripper2 releases ball1 in room2, and rgripper2 of robot2 drops ball2 in room2. Subsequently, robot2 moves from room2 to room3, where lgripper2 of robot2 picks up ball4, and rgripper2 of robot2 picks up ball7, both in room3. Finally, robot2 moves from room3 to room2, reaching the current state. In this state, if robot2's rgripper2 drops ball7 in room2, is it True or False that ball2 is not in room2?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is found in room3, ball5 resides in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is unoccupied, the right gripper of robot2 is also unoccupied, robot1 is positioned in room2, the right gripper of robot1 is available, and robot2 is also in room2."}
{"question_id": "4e735748-945f-4681-b02e-45fe2faaacc4", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, robot1 moves to room2 from room1, rgripper1 of robot1 picks up ball2 in room2, robot1 moves from room2 to room3, in room3, robot1's lgripper1 drops ball1, from room3, robot1 moves to room4, rgripper1 of robot1 drops ball2 in room4, robot1 moves to room2 from room4 and lgripper1 of robot1 picks up ball4 in room2 to reach the current state. In this state, if rgripper1 of robot1 picks up ball5 in room2, is it True or False that ball6 is being carried by robot1's lgripper1?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 moves from room4 to room1, then its left gripper (lgripper1) picks up ball1 in room1. Next, robot1 moves to room2 from room1, and its right gripper (rgripper1) picks up ball2 in room2. Robot1 then proceeds to room3, where it drops ball1 using lgripper1. From room3, robot1 moves back to room4, and in room4, it drops ball2 using rgripper1. Subsequently, robot1 moves to room2 from room4 and uses lgripper1 to pick up ball4 in room2, resulting in the current state. In this state, if robot1's rgripper1 picks up ball5 in room2, is it True or False that ball6 is being carried by robot1's lgripper1?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, the left gripper of robot1 is unoccupied, robot1 is positioned at room4, and robot1's right gripper is available."}
{"question_id": "ebc5bd29-9815-42a4-8fa2-8c75cbc5a980", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, lgripper1 of robot1 picks up ball1 in room1, rgripper1 of robot1 picks up ball3 in room1, from room1, robot1 moves to room5, rgripper1 of robot1 drops ball3 in room5, from room5, robot1 moves to room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1, rgripper1 of robot1 picks up ball6 in room1, robot1 moves from room1 to room3, in room3, robot1's rgripper1 drops ball6, rgripper1 of robot1 picks up ball7 in room3, robot1 moves to room4 from room3, ball1 is dropped in room4 with lgripper1 by robot1, rgripper1 of robot1 drops ball7 in room4, robot1 moves from room4 to room2, lgripper1 of robot1 picks up ball2 in room2 and ball5 is picked from room2 with rgripper1 by robot1 to reach the current state. In this state, if robot1 moves to room5 from room2, is it True or False that ball2 is present at room2 and ball7 is located at room1?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is present at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 relocates from room4 to room1, then uses its left gripper (lgripper1) to grasp ball1 in room1, and its right gripper (rgripper1) to grasp ball3 in room1. Next, robot1 moves from room1 to room5, where it releases ball3 using rgripper1. From room5, robot1 proceeds to room2, where it uses rgripper1 to pick up ball4. Robot1 then moves back to room1, drops ball4 using rgripper1, and uses rgripper1 to grasp ball6. Subsequently, robot1 relocates to room3, drops ball6 using rgripper1, and picks up ball7 using rgripper1. Robot1 then moves to room4, drops ball1 using lgripper1, and releases ball7 using rgripper1. Finally, robot1 moves to room2, where it uses lgripper1 to pick up ball2 and rgripper1 to pick up ball5, resulting in the current state. In this state, if robot1 moves from room2 to room5, is it True or False that ball2 remains in room2 and ball7 is located in room1?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is located in room2, ball6 is found in room1, ball7 is situated in room3, the right gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's left gripper is available."}
{"question_id": "221f920f-5db8-4bdd-a3d0-450c9354c787", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1's rgripper1 picks up ball3, robot1 moves from room1 to room5, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, from room2, robot1's rgripper1 picks up ball4, robot1 moves from room2 to room1, in room1, robot1's rgripper1 drops ball4 and ball6 is picked from room1 with rgripper1 by robot1 to reach the current state. In this state, if robot1 moves from room1 to room3, is it True or False that robot1 is located at room3 and robot1 is not at room1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is present at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 relocates from room4 to room1, then its left gripper (lgripper1) grasps ball1 in room1, followed by its right gripper (rgripper1) picking up ball3 in room1. Next, robot1 moves to room5, where its right gripper drops ball3. Robot1 then proceeds to room2, picks up ball4 with its right gripper, and moves back to room1. In room1, robot1's right gripper releases ball4 and picks up ball6. Now, if robot1 moves from room1 to room3, is it True or False that robot1 is located at room3 and not at room1?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is located in room2, ball6 is found in room1, ball7 is present in room3, the right gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's left gripper is available."}
{"question_id": "3e30a0e8-1273-45cf-a27b-32606ef90e27", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: lgripper1 of robot1 picks up ball7 in room4, robot1 moves to room5 from room4, in room5, robot1's lgripper1 drops ball7, robot1 moves from room5 to room1, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1 moves to room5, in room5, robot1's lgripper1 drops ball1, ball3 is dropped in room5 with rgripper1 by robot1 and robot1 moves from room5 to room2 to reach the current state. In this state, if lgripper1 of robot1 picks up ball2 in room2, is it True or False that robot1 is carrying ball2 with lgripper1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is present at room3, ball6 is present at room1, ball7 is located at room4, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1's left gripper (lgripper1) grasps ball7 in room4, then robot1 relocates from room4 to room5, where it releases ball7 using lgripper1. Next, robot1 moves from room5 to room1, picks up ball1 with lgripper1, and simultaneously, its right gripper (rgripper1) picks up ball3 in room1. From room1, robot1 proceeds to room5, where it drops ball1 using lgripper1 and releases ball3 with rgripper1. Finally, robot1 moves from room5 to room2, reaching the current state. In this state, if robot1's lgripper1 picks up ball2 in room2, is it True or False that robot1 is holding ball2 with lgripper1?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is also in room1, ball4 is also in room2, ball5 is in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "e481c54d-e4c6-4d2f-ab8c-b59d1d083c5b", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2, lgripper2 of robot2 picks up ball1 in room3, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, lgripper2 of robot2 drops ball1 in room2, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball4, ball7 is picked from room3 with rgripper2 by robot2 and robot2 moves to room2 from room3 to reach the current state. In this state, if ball7 is dropped in room2 with rgripper2 by robot2, is it True or False that ball7 is not being carried by robot2's rgripper2?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot2 relocates from room2 to room3, then uses its left gripper (lgripper2) to grasp ball1 in room3, and its right gripper (rgripper2) to pick up ball2 in room3. Next, robot2 moves back to room2 from room3, where it releases ball1 using lgripper2 and drops ball2 using rgripper2. Then, robot2 returns to room3 from room2, picks up ball4 with lgripper2, and uses rgripper2 to grasp ball7 in room3 before moving back to room2. In this resulting state, if robot2 drops ball7 in room2 using rgripper2, the question arises: is it True or False that ball7 is no longer being carried by robot2's rgripper2?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is present in room3, ball5 is found in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is free, the right gripper of robot2 is also free, robot1 is positioned in room2, the right gripper of robot1 is available, and robot2 is also in room2."}
{"question_id": "5e098b61-c999-4ed4-98c2-91a154b7e6f4", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3, from room3, robot2's lgripper2 picks up ball1, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, in room2, robot2's lgripper2 drops ball1, in room2, robot2's rgripper2 drops ball2, from room2, robot2 moves to room3, ball4 is picked from room3 with lgripper2 by robot2, from room3, robot2's rgripper2 picks up ball7, robot2 moves to room2 from room3, in room2, robot2's rgripper2 drops ball7, from room2, robot2's rgripper2 picks up ball3, robot2 moves to room1 from room2, ball4 is dropped in room1 with lgripper2 by robot2, ball5 is picked from room1 with lgripper2 by robot2, ball3 is dropped in room1 with rgripper2 by robot2, from room1, robot2's rgripper2 picks up ball6, robot2 moves to room2 from room1 and in room2, robot2's lgripper2 drops ball5 to reach the current state. In this state, if in room2, robot2's rgripper2 drops ball6, is it True or False that robot2's rgripper2 is available?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 moves from room2 to room3, then uses its left gripper (lgripper2) to pick up ball1 and its right gripper (rgripper2) to pick up ball2, both in room3. Next, robot2 returns to room2 and drops ball1 with lgripper2 and ball2 with rgripper2. Then, robot2 moves back to room3, picks up ball4 with lgripper2 and ball7 with rgripper2, and returns to room2, where it drops ball7 with rgripper2. In room2, robot2's rgripper2 picks up ball3, and then robot2 moves to room1, drops ball4 with lgripper2, picks up ball5 with lgripper2, and drops ball3 with rgripper2. Additionally, robot2's rgripper2 picks up ball6 in room1, and then robot2 moves back to room2. Finally, in room2, robot2's lgripper2 drops ball5, reaching the current state. In this state, if in room2, robot2's rgripper2 drops ball6, is it True or False that robot2's rgripper2 is available?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is found in room3, ball5 resides in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is unoccupied, the right gripper of robot2 is also unoccupied, robot1 is positioned in room2, the right gripper of robot1 is available, and robot2 is also in room2."}
{"question_id": "2fad3e6c-d8a1-45c8-8b11-ed41d83ad4a9", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves to room2 from room1, rgripper1 of robot1 picks up ball2 in room2, from room2, robot1 moves to room3, lgripper1 of robot1 drops ball1 in room3, from room3, robot1 moves to room4, in room4, robot1's rgripper1 drops ball2, robot1 moves to room2 from room4, lgripper1 of robot1 picks up ball4 in room2, from room2, robot1's rgripper1 picks up ball5, robot1 moves from room2 to room5, in room5, robot1's lgripper1 drops ball4, from room5, robot1 moves to room1, ball3 is picked from room1 with lgripper1 by robot1, ball5 is dropped in room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball6, robot1 moves from room1 to room5 and in room5, robot1's lgripper1 drops ball3 to reach the current state. In this state, if in room5, robot1's rgripper1 drops ball6, is it True or False that ball2 is not present at room2?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 relocates from room4 to room1, then uses lgripper1 to pick up ball1 in room1, proceeds to room2, and uses rgripper1 to pick up ball2 in room2. From room2, robot1 moves to room3, where it drops ball1 using lgripper1, and then moves to room4. In room4, robot1 drops ball2 using rgripper1, then returns to room2, where it picks up ball4 with lgripper1 and ball5 with rgripper1. Robot1 then moves to room5, drops ball4 using lgripper1, and proceeds to room1. In room1, robot1 picks up ball3 with lgripper1 and drops ball5 with rgripper1. Next, robot1 picks up ball6 with rgripper1 and moves to room5, where it drops ball3 using lgripper1 to reach the current state. In this state, if robot1 drops ball6 in room5 using rgripper1, is it True or False that ball2 is not present in room2?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, the left gripper of robot1 is unoccupied, robot1 is positioned at room4, and robot1's right gripper is available."}
{"question_id": "b00454a7-e77d-4290-8a47-891ae8690cc7", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3 to reach the current state. In this state, if ball1 is picked from room3 with lgripper2 by robot2, is it True or False that lgripper2 of robot2 is carrying ball1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3 to reach the current state. In this state, if robot2 picks up ball1 from room3 using lgripper2, is it True or False that robot2's lgripper2 is holding ball1?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is found in room3, ball5 is positioned in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is free, the right gripper of robot2 is also free, robot1 is positioned in room2, the right gripper of robot1 is available and robot2 is also in room2."}
{"question_id": "47e461eb-c79f-4a19-b01f-270196b3e292", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, if lgripper1 of robot1 picks up ball1 in room1, is it True or False that lgripper1 of robot1 is not free?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is present at room1, ball7 is present at room3, rgripper1 of robot1 is free, robot1 is at room4 and robot1's lgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, if the left gripper of robot1 (lgripper1) picks up ball1 in room1, is it True or False that the left gripper of robot1 (lgripper1) is not available?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is located in room2, ball6 is found in room1, ball7 is present in room3, the right gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's left gripper is available."}
{"question_id": "0ab270bc-d658-4ca9-98e5-055993e3bdf2", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, if from room1, robot1's lgripper1 picks up ball1, is it True or False that ball1 is not located at room1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, if from room1, robot1's lgripper1 picks up ball1, is it True or False that ball1 is no longer located at room1?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, the left gripper of robot1 is unoccupied, robot1 is positioned at room4, and robot1's right gripper is available."}
{"question_id": "d766f8c9-29ff-44eb-8c51-1f823c183224", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball1, rgripper2 of robot2 picks up ball2 in room3, robot2 moves to room2 from room3, lgripper2 of robot2 drops ball1 in room2, rgripper2 of robot2 drops ball2 in room2, robot2 moves to room3 from room2, lgripper2 of robot2 picks up ball4 in room3, ball7 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, in room2, robot2's rgripper2 drops ball7, ball3 is picked from room2 with rgripper2 by robot2, robot2 moves to room1 from room2, lgripper2 of robot2 drops ball4 in room1, from room1, robot2's lgripper2 picks up ball5, in room1, robot2's rgripper2 drops ball3, ball6 is picked from room1 with rgripper2 by robot2, robot2 moves from room1 to room2 and in room2, robot2's lgripper2 drops ball5 to reach the current state. In this state, if in room2, robot2's rgripper2 drops ball6, is it True or False that rgripper2 of robot2 is not carrying ball6?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 relocates from room2 to room3, then robot2's left gripper (lgripper2) grasps ball1 in room3, and robot2's right gripper (rgripper2) picks up ball2 in room3. Next, robot2 moves back to room2 from room3, and in room2, lgripper2 releases ball1 and rgripper2 releases ball2. Then, robot2 returns to room3 from room2, where lgripper2 picks up ball4 and rgripper2 picks up ball7. After that, robot2 moves back to room2, and in room2, rgripper2 drops ball7, then picks up ball3. Subsequently, robot2 proceeds to room1 from room2, where lgripper2 drops ball4, and then lgripper2 picks up ball5. In room1, rgripper2 drops ball3 and picks up ball6. Finally, robot2 moves to room2, and in room2, lgripper2 drops ball5, reaching the current state. In this state, if in room2, robot2's rgripper2 drops ball6, is it True or False that rgripper2 of robot2 is no longer carrying ball6?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is found in room3, ball5 resides in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is unoccupied, the right gripper of robot2 is unoccupied, robot1 is positioned in room2, the right gripper of robot1 is available and robot2 is also in room2."}
{"question_id": "c0f82660-f80d-47ce-826e-6a7ab933cb8d", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1, robot1 moves from room4 to room5, in room5, robot1's lgripper1 drops ball7, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, in room5, robot1's lgripper1 drops ball1, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves from room5 to room2, ball2 is picked from room2 with lgripper1 by robot1, rgripper1 of robot1 picks up ball4 in room2, robot1 moves from room2 to room1, in room1, robot1's rgripper1 drops ball4, ball6 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room3, rgripper1 of robot1 drops ball6 in room3, from room3, robot1's rgripper1 picks up ball5 and robot1 moves to room6 from room3 to reach the current state. In this state, if lgripper1 of robot1 drops ball2 in room6, is it True or False that ball6 is not present at room5?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is present at room3, ball6 is present at room1, ball7 is located at room4, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1, using lgripper1, picks up ball7 from room4, then moves to room5, where it drops ball7 using lgripper1. Next, robot1 proceeds to room1, picks up ball1 with lgripper1, and ball3 with rgripper1. From room1, robot1 moves to room5, drops ball1 using lgripper1, and drops ball3 using rgripper1. Then, robot1 moves to room2, picks up ball2 with lgripper1, and ball4 with rgripper1. After that, robot1 moves to room1, drops ball4 using rgripper1, picks up ball6 with rgripper1, and moves to room3. In room3, robot1 drops ball6 using rgripper1, picks up ball5 with rgripper1, and then moves to room6. Now, if robot1 drops ball2 in room6 using lgripper1, is it True or False that ball6 is not present in room5?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is also in room1, ball4 is also in room2, ball5 is in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is available."}
{"question_id": "8ee7f0e8-3be6-4f89-b912-33f3b7254343", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, if ball1 is picked from room1 with lgripper1 by robot1, is it True or False that ball1 is not present at room5?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is located at room1, ball4 is located at room2, ball5 is located at room2, ball6 is located at room1, lgripper1 of robot1 is free, robot1 is located at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, if robot1 picks up ball1 from room1 using lgripper1, is it True or False that ball1 is not located at room5?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room2, ball6 is situated in room1, the left gripper of robot1 is unoccupied, robot1 is positioned at room4, and robot1's right gripper is available."}
{"question_id": "3e60b4f5-0771-4459-86cf-0dfe6fc3d4a5", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2 to reach the current state. In this state, if lgripper2 of robot2 picks up ball1 in room3, is it True or False that ball3 is being carried by robot1's lgripper2?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3 to reach the current state. In this state, if the left gripper of robot2 picks up ball1 in room3, is it True or False that robot1's left gripper is carrying ball3?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is found in room3, ball5 resides in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is unoccupied, the right gripper of robot2 is also unoccupied, robot1 is positioned in room2, the right gripper of robot1 is available, and robot2 is also in room2."}
{"question_id": "926a0711-69ac-41f2-9e92-b5897299a42f", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, ball1 is picked from room3 with lgripper2 by robot2, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, in room2, robot2's lgripper2 drops ball1, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves to room3 from room2, ball4 is picked from room3 with lgripper2 by robot2, from room3, robot2's rgripper2 picks up ball7 and robot2 moves to room2 from room3 to reach the current state. In this state, if rgripper2 of robot2 drops ball7 in room2, is it True or False that lgripper1 of robot1 is not carrying ball1?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: robot2 relocates from room2 to room3, then uses lgripper2 to pick up ball1 from room3, and subsequently uses rgripper2 to pick up ball2 from room3. Next, robot2 moves back to room2 from room3, and in room2, it drops ball1 using lgripper2 and ball2 using rgripper2. Then, robot2 moves to room3 from room2, picks up ball4 with lgripper2, and uses rgripper2 to pick up ball7 before moving back to room2 from room3, resulting in the current state. In this state, if robot2's rgripper2 drops ball7 in room2, is it True or False that lgripper1 of robot1 is not holding ball1?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is found in room3, ball5 resides in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is unoccupied, the right gripper of robot2 is also unoccupied, robot1 is positioned in room2, the right gripper of robot1 is available, and robot2 is also in room2."}
{"question_id": "e83b9e0e-fe2a-4ee1-8b90-339eabb78baf", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: lgripper1 of robot1 picks up ball7 in room4, from room4, robot1 moves to room5, ball7 is dropped in room5 with lgripper1 by robot1, robot1 moves from room5 to room1, ball1 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 picks up ball3 in room1, from room1, robot1 moves to room5, lgripper1 of robot1 drops ball1 in room5, in room5, robot1's rgripper1 drops ball3 and from room5, robot1 moves to room2 to reach the current state. In this state, if from room2, robot1's lgripper1 picks up ball2, is it True or False that lgripper1 of robot1 is free?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is present at room3, ball6 is present at room1, ball7 is located at room4, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1's lgripper1 picks up ball7 in room4, then robot1 moves from room4 to room5, and drops ball7 in room5 using lgripper1. Next, robot1 moves from room5 to room1, picks up ball1 with lgripper1, and also picks up ball3 with rgripper1 in room1. Then, robot1 moves from room1 to room5, drops ball1 with lgripper1, and drops ball3 with rgripper1 in room5. Finally, robot1 moves from room5 to room2, reaching the current state. In this state, if robot1's lgripper1 picks up ball2 from room2, is it True or False that lgripper1 of robot1 is free?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is also in room1, ball4 is also in room2, ball5 is in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "b1ff5e8e-6e30-4145-a6ec-85032b067491", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: lgripper1 of robot1 picks up ball7 in room4, from room4, robot1 moves to room5, ball7 is dropped in room5 with lgripper1 by robot1, robot1 moves to room1 from room5, lgripper1 of robot1 picks up ball1 in room1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room5, ball1 is dropped in room5 with lgripper1 by robot1, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, lgripper1 of robot1 picks up ball2 in room2, from room2, robot1's rgripper1 picks up ball4, robot1 moves from room2 to room1, rgripper1 of robot1 drops ball4 in room1, rgripper1 of robot1 picks up ball6 in room1, robot1 moves to room3 from room1, ball6 is dropped in room3 with rgripper1 by robot1, rgripper1 of robot1 picks up ball5 in room3 and from room3, robot1 moves to room6 to reach the current state. In this state, if ball2 is dropped in room6 with lgripper1 by robot1, is it True or False that lgripper1 of robot1 is free?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is at room2, ball3 is present at room1, ball4 is present at room2, ball5 is present at room3, ball6 is present at room1, ball7 is located at room4, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1's lgripper1 grasps ball7 in room4, then robot1 proceeds to room5 from room4, and subsequently drops ball7 in room5 using lgripper1. Next, robot1 moves to room1 from room5, where lgripper1 of robot1 picks up ball1, and simultaneously, rgripper1 of robot1 picks up ball3. Robot1 then moves to room5 from room1, drops ball1 in room5 using lgripper1, and also drops ball3 in room5 using rgripper1. After that, robot1 moves to room2 from room5, where lgripper1 of robot1 picks up ball2, and rgripper1 of robot1 picks up ball4. Robot1 then proceeds to room1 from room2, drops ball4 in room1 using rgripper1, and picks up ball6 in room1 using rgripper1. Subsequently, robot1 moves to room3 from room1, drops ball6 in room3 using rgripper1, and picks up ball5 in room3 using rgripper1. Finally, robot1 moves to room6 from room3 to reach the current state. In this state, if robot1 drops ball2 in room6 using lgripper1, is it True or False that lgripper1 of robot1 is free?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is in room2, ball3 is also in room1, ball4 is also in room2, ball5 is in room3, ball6 is in room1, ball7 is situated in room4, robot1 is in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "830526a4-315b-4349-92e0-58e9447c9e4d", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2 to reach the current state. In this state, if from room3, robot2's lgripper2 picks up ball1, is it True or False that lgripper2 of robot2 is not free?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is present at room3, ball2 is at room3, ball3 is at room2, ball4 is present at room3, ball5 is located at room1, ball6 is at room1, ball7 is located at room3, lgripper1 of robot1 is free, lgripper2 of robot2 is free, rgripper2 of robot2 is free, robot1 is at room2, robot1's rgripper1 is available and robot2 is at room2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3 to reach the current state. In this state, if robot2's lgripper2 picks up ball1 from room3, is it True or False that the lgripper2 of robot2 is not free?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is found in room3, ball5 resides in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot1 is available, the left gripper of robot2 is free, the right gripper of robot2 is also free, robot1 is positioned in room2, the right gripper of robot1 is available, and robot2 is also in room2."}
