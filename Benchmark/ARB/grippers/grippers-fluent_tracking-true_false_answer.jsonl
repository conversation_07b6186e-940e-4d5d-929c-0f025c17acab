{"question_id": "e0de6b87-09f2-4910-be5e-1ad6f726f4e7", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_12", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4 to reach the current state. In this state, is it True or False that ball3 is not present at room1?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot1 relocates from room4 to room1 to achieve the current state. In this state, is it True or False that ball3 is absent from room1?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is situated in room3, robot1 is situated in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "cd98a4c0-4729-4781-b3d8-ed0a31cac9e4", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_6", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7 to reach the current state. In this state, is it True or False that lgripper1 of robot1 is free?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is at room1, ball4 is at room2, ball5 is located at room3, ball6 is located at room1, ball7 is at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7 to reach the current state. In this state, is it True or False that the lgripper1 of robot1 is available?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is situated in room3, ball6 is situated in room1, ball7 is in room4, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and robot1's right gripper is available."}
{"question_id": "69897882-6fd7-487d-a37b-d3c9c4cde91f", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves to room2 from room1, ball2 is picked from room2 with rgripper1 by robot1, robot1 moves to room3 from room2, in room3, robot1's lgripper1 drops ball1, from room3, robot1 moves to room4, rgripper1 of robot1 drops ball2 in room4, from room4, robot1 moves to room2 and lgripper1 of robot1 picks up ball4 in room2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: robot1 is carrying ball4 with lgripper1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is present at room1, ball4 is at room2, ball5 is located at room2, ball6 is at room1, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: robot1 relocates from room4 to room1, then uses lgripper1 to pick up ball1 in room1, proceeds to room2 from room1, and uses rgripper1 to pick up ball2 in room2. Next, robot1 moves to room3 from room2, drops ball1 in room3 using lgripper1, and then moves back to room4. In room4, robot1 drops ball2 using rgripper1, and subsequently moves to room2, where it picks up ball4 using lgripper1, resulting in the current state. In this state, are the following properties that do not involve negations True or False: is robot1 holding ball4 with lgripper1?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, robot1 is situated in room4, and both the left gripper (lgripper1) and the right gripper (rgripper1) of robot1 are available."}
{"question_id": "45f5b5c2-29c6-4660-9cc5-0ed52de99799", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_3", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4 to reach the current state. In this state, is it True or False that ball1 is not being carried by robot1's lgripper1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is present at room1, ball4 is at room2, ball5 is located at room2, ball6 is at room1, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4 to reach the current state. In this state, is it True or False that robot1's lgripper1 is not holding ball1?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, robot1 is situated in room4, and both the left gripper (lgripper1) and the right gripper (rgripper1) of robot1 are available."}
{"question_id": "c53e8055-85e4-43a2-a16f-2dbd22ecf878", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2, lgripper2 of robot2 picks up ball1 in room3, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves from room3 to room2, in room2, robot2's lgripper2 drops ball1, rgripper2 of robot2 drops ball2 in room2, robot2 moves from room2 to room3, lgripper2 of robot2 picks up ball4 in room3, rgripper2 of robot2 picks up ball7 in room3 and from room3, robot2 moves to room2 to reach the current state. In this state, is it True or False that robot1's rgripper1 is not free?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: robot2 relocates from room2 to room3, then uses its left gripper (lgripper2) to grasp ball1 in room3, and its right gripper (rgripper2) to grasp ball2 in room3. Next, robot2 moves back to room2, where it releases ball1 with lgripper2 and ball2 with rgripper2. Subsequently, robot2 returns to room3, picks up ball4 with lgripper2 and ball7 with rgripper2, and then moves back to room2, resulting in the current state. In this state, is it True or False that robot1's right gripper (rgripper1) is not free?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is situated in room2, robot1's left gripper is free, robot1's right gripper is free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "c7f5e953-5329-412f-8787-6a105fbc724d", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, from room1, robot1's rgripper1 picks up ball3, robot1 moves to room5 from room1, in room5, robot1's rgripper1 drops ball3, from room5, robot1 moves to room2, from room2, robot1's rgripper1 picks up ball4, robot1 moves from room2 to room1, rgripper1 of robot1 drops ball4 in room1 and rgripper1 of robot1 picks up ball6 in room1 to reach the current state. In this state, is it True or False that lgripper1 of robot1 is not free?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 moves from room4 to room1, then in room1, robot1's left gripper (lgripper1) picks up ball1, and robot1's right gripper (rgripper1) picks up ball3. Next, robot1 moves from room1 to room5, where it drops ball3 using rgripper1. From room5, robot1 proceeds to room2, picks up ball4 with rgripper1, and then moves back to room1. In room1, robot1 drops ball4 using rgripper1 and then uses the same gripper to pick up ball6. In the resulting state, is it True or False that lgripper1 of robot1 is not free?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is situated in room3, robot1 is situated in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "74e5f67b-91a1-4417-95ae-7ab815ba52fb", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_5", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3, from room3, robot2's lgripper2 picks up ball1, from room3, robot2's rgripper2 picks up ball2, from room3, robot2 moves to room2, in room2, robot2's lgripper2 drops ball1, in room2, robot2's rgripper2 drops ball2, robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball4, from room3, robot2's rgripper2 picks up ball7, from room3, robot2 moves to room2, rgripper2 of robot2 drops ball7 in room2, ball3 is picked from room2 with rgripper2 by robot2, from room2, robot2 moves to room1, ball4 is dropped in room1 with lgripper2 by robot2, from room1, robot2's lgripper2 picks up ball5, rgripper2 of robot2 drops ball3 in room1, from room1, robot2's rgripper2 picks up ball6, robot2 moves from room1 to room2 and in room2, robot2's lgripper2 drops ball5 to reach the current state. In this state, is it True or False that robot2's lgripper2 is free?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, then in room3, robot2's left gripper (lgripper2) picks up ball1 and its right gripper (rgripper2) picks up ball2. Next, robot2 moves back to room2, where it drops ball1 with lgripper2 and ball2 with rgripper2. Then, robot2 returns to room3, picks up ball4 with lgripper2 and ball7 with rgripper2, and moves back to room2. In room2, rgripper2 drops ball7, and then picks up ball3. Robot2 then moves to room1, drops ball4 with lgripper2, picks up ball5 with lgripper2, drops ball3 with rgripper2, and picks up ball6 with rgripper2. Finally, robot2 moves to room2 and drops ball5 with lgripper2 to reach the current state. In this state, is it True or False that robot2's lgripper2 is free?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is situated in room2, robot1's left gripper is free, robot1's right gripper is free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "f8df789b-2093-458f-91f4-28881c77d45a", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_5", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: lgripper1 of robot1 picks up ball7 in room4 to reach the current state. In this state, is it True or False that robot1's rgripper1 is available?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is at room1, ball4 is at room2, ball5 is located at room3, ball6 is located at room1, ball7 is at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot1's lgripper1 picks up ball7 in room4 to reach the current state. In this state, is it True or False that the rgripper1 of robot1 is available?", "initial_state_nl_paraphrased": "The location of the balls is as follows: ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room3, ball6 is in room1, and ball7 is in room4. Additionally, the status of robot1 is as follows: its left gripper (lgripper1) is free, it is currently in room4, and its right gripper (rgripper1) is available."}
{"question_id": "dd4f0fa6-3706-4905-bbf3-167f6e4f144e", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, ball1 is picked from room3 with lgripper2 by robot2, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, lgripper2 of robot2 drops ball1 in room2, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves to room3 from room2, lgripper2 of robot2 picks up ball4 in room3, ball7 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, in room2, robot2's rgripper2 drops ball7, ball3 is picked from room2 with rgripper2 by robot2, robot2 moves from room2 to room1, lgripper2 of robot2 drops ball4 in room1, lgripper2 of robot2 picks up ball5 in room1, ball3 is dropped in room1 with rgripper2 by robot2, ball6 is picked from room1 with rgripper2 by robot2, robot2 moves to room2 from room1 and in room2, robot2's lgripper2 drops ball5 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: ball1 is being carried by robot1's lgripper2, ball2 is being carried by robot1's lgripper2, ball2 is being carried by robot1's rgripper1, ball2 is being carried by robot2's lgripper2, ball3 is being carried by robot1's lgripper1, ball3 is being carried by robot1's lgripper2, ball4 is being carried by robot1's rgripper2, ball4 is being carried by robot2's lgripper1, ball4 is being carried by robot2's rgripper2, ball5 is being carried by robot1's rgripper2, ball6 is being carried by robot1's lgripper1, ball6 is being carried by robot1's lgripper2, ball6 is being carried by robot1's rgripper2, ball6 is being carried by robot2's lgripper2, ball7 is being carried by robot2's lgripper2, ball7 is being carried by robot2's rgripper1, lgripper1 of robot1 is carrying ball1, lgripper1 of robot1 is carrying ball5, lgripper1 of robot1 is carrying ball7, lgripper1 of robot2 is carrying ball6, lgripper1 of robot2 is carrying ball7, lgripper2 of robot2 is carrying ball3, rgripper1 of robot1 is carrying ball1, rgripper1 of robot1 is carrying ball4, rgripper1 of robot1 is carrying ball6, rgripper1 of robot1 is carrying ball7, rgripper1 of robot2 is carrying ball2, rgripper1 of robot2 is carrying ball6, rgripper2 of robot1 is carrying ball1, rgripper2 of robot1 is carrying ball2, rgripper2 of robot1 is carrying ball3, rgripper2 of robot1 is carrying ball7, rgripper2 of robot2 is carrying ball2, rgripper2 of robot2 is carrying ball3, rgripper2 of robot2 is carrying ball6, rgripper2 of robot2 is carrying ball7, robot1 is carrying ball2 with lgripper1, robot1 is carrying ball3 with rgripper1, robot1 is carrying ball4 with lgripper1, robot1 is carrying ball4 with lgripper2, robot1 is carrying ball5 with lgripper2, robot1 is carrying ball5 with rgripper1, robot1 is carrying ball7 with lgripper2, robot2 is carrying ball1 with lgripper1, robot2 is carrying ball1 with lgripper2, robot2 is carrying ball1 with rgripper1, robot2 is carrying ball1 with rgripper2, robot2 is carrying ball2 with lgripper1, robot2 is carrying ball3 with lgripper1, robot2 is carrying ball3 with rgripper1, robot2 is carrying ball4 with lgripper2, robot2 is carrying ball4 with rgripper1, robot2 is carrying ball5 with lgripper1, robot2 is carrying ball5 with lgripper2, robot2 is carrying ball5 with rgripper1 and robot2 is carrying ball5 with rgripper2?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 moves from room2 to room3, then it uses lgripper2 to pick up ball1 in room3, and uses rgripper2 to pick up ball2 in room3. Next, robot2 moves back to room2 from room3, drops ball1 in room2 using lgripper2, and drops ball2 in room2 using rgripper2. Then, robot2 moves to room3 from room2, picks up ball4 in room3 using lgripper2, and picks up ball7 in room3 using rgripper2. After that, robot2 moves to room2 from room3, drops ball7 in room2 using rgripper2, picks up ball3 in room2 using rgripper2, and moves to room1 from room2. In room1, robot2 drops ball4 using lgripper2, picks up ball5 using lgripper2, drops ball3 using rgripper2, picks up ball6 using rgripper2, and then moves to room2 from room1. Finally, in room2, robot2 drops ball5 using lgripper2 to reach the current state. In this state, are the following properties that do not involve negations True or False: ball1 is being carried by robot1's lgripper2, ball2 is being carried by robot1's lgripper2, ball2 is being carried by robot1's rgripper1, ball2 is being carried by robot2's lgripper2, ball3 is being carried by robot1's lgripper1, ball3 is being carried by robot1's lgripper2, ball4 is being carried by robot1's rgripper2, ball4 is being carried by robot2's lgripper1, ball4 is being carried by robot2's rgripper2, ball5 is being carried by robot1's rgripper2, ball6 is being carried by robot1's lgripper1, ball6 is being carried by robot1's lgripper2, ball6 is being carried by robot1's rgripper2, ball6 is being carried by robot2's lgripper2, ball7 is being carried by robot2's lgripper2, ball7 is being carried by robot2's rgripper1, lgripper1 of robot1 is carrying ball1, lgripper1 of robot1 is carrying ball5, lgripper1 of robot1 is carrying ball7, lgripper1 of robot2 is carrying ball6, lgripper1 of robot2 is carrying ball7, lgripper2 of robot2 is carrying ball3, rgripper1 of robot1 is carrying ball1, rgripper1 of robot1 is carrying ball4, rgripper1 of robot1 is carrying ball6, rgripper1 of robot1 is carrying ball7, rgripper1 of robot2 is carrying ball2, rgripper1 of robot2 is carrying ball6, rgripper2 of robot1 is carrying ball1, rgripper2 of robot1 is carrying ball2, rgripper2 of robot1 is carrying ball3, rgripper2 of robot1 is carrying ball7, rgripper2 of robot2 is carrying ball2, rgripper2 of robot2 is carrying ball3, rgripper2 of robot2 is carrying ball6, rgripper2 of robot2 is carrying ball7, robot1 is carrying ball2 with lgripper1, robot1 is carrying ball3 with rgripper1, robot1 is carrying ball4 with lgripper1, robot1 is carrying ball4 with lgripper2, robot1 is carrying ball5 with lgripper2, robot1 is carrying ball5 with rgripper1, robot1 is carrying ball7 with lgripper2, robot2 is carrying ball1 with lgripper1, robot2 is carrying ball1 with lgripper2, robot2 is carrying ball1 with rgripper1, robot2 is carrying ball1 with rgripper2, robot2 is carrying ball2 with lgripper1, robot2 is carrying ball3 with lgripper1, robot2 is carrying ball3 with rgripper1, robot2 is carrying ball4 with lgripper2, robot2 is carrying ball4 with rgripper1, robot2 is carrying ball5 with lgripper1, robot2 is carrying ball5 with lgripper2, robot2 is carrying ball5 with rgripper1 and robot2 is carrying ball5 with rgripper2?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is situated in room2, robot1's left gripper is free, robot1's right gripper is free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "9532e243-64ad-4725-9669-e4e0d7c9f9a3", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_13", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: ball1 is at room1, ball2 is at room2, ball3 is present at room1, ball4 is located at room2, ball5 is at room3, ball6 is present at room1 and robot1 is located at room4?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is at room1, ball4 is at room2, ball5 is located at room3, ball6 is located at room1, ball7 is at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: robot1 uses lgripper1 to pick up ball7 from room4, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: ball1 is in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is in room3, ball6 is in room1, and robot1 is in room4?", "initial_state_nl_paraphrased": "The location of the balls is as follows: ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room3, ball6 is in room1, and ball7 is in room4. The status of robot1 is as follows: its left gripper (lgripper1) is free, it is currently in room4, and its right gripper (rgripper1) is available."}
{"question_id": "917dbd35-f5f4-4c3c-b316-65469859a854", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_9", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: robot1's lgripper1 is not available?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is at room1, ball4 is at room2, ball5 is located at room3, ball6 is located at room1, ball7 is at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot1 uses lgripper1 to pick ball7 from room4, resulting in the current state. In this state, are the following properties that involve negations True or False: is robot1's lgripper1 unavailable?", "initial_state_nl_paraphrased": "The location of the balls is as follows: ball1 is in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is situated in room3, ball6 is situated in room1, and ball7 is in room4. Additionally, the status of robot1 is as follows: its left gripper (lgripper1) is free, it is currently in room4, and its right gripper (rgripper1) is available."}
{"question_id": "cee1f33e-559e-435b-89c4-89664042be01", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3, ball1 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball2 in room3, from room3, robot2 moves to room2, lgripper2 of robot2 drops ball1 in room2, in room2, robot2's rgripper2 drops ball2, from room2, robot2 moves to room3, from room3, robot2's lgripper2 picks up ball4, rgripper2 of robot2 picks up ball7 in room3, robot2 moves from room3 to room2, in room2, robot2's rgripper2 drops ball7, rgripper2 of robot2 picks up ball3 in room2, from room2, robot2 moves to room1, lgripper2 of robot2 drops ball4 in room1, from room1, robot2's lgripper2 picks up ball5, in room1, robot2's rgripper2 drops ball3, rgripper2 of robot2 picks up ball6 in room1, robot2 moves to room2 from room1 and in room2, robot2's lgripper2 drops ball5 to reach the current state. In this state, is it True or False that robot2's rgripper2 is not available?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, then it uses lgripper2 to pick up ball1 in room3, and its rgripper2 picks up ball2 in room3. Next, robot2 moves back to room2 from room3, drops ball1 using lgripper2, and drops ball2 using rgripper2 in room2. Then, robot2 moves to room3 again, picks up ball4 with lgripper2 and ball7 with rgripper2 in room3, and moves back to room2. In room2, robot2 drops ball7 using rgripper2, picks up ball3 using rgripper2, and then moves to room1. In room1, robot2 drops ball4 using lgripper2, picks up ball5 using lgripper2, drops ball3 using rgripper2, and picks up ball6 using rgripper2. Finally, robot2 moves to room2 from room1 and drops ball5 using lgripper2 to reach the current state. In this state, is it True or False that robot2's rgripper2 is not available?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is situated in room2, robot1's left gripper is free, robot1's right gripper is free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "43dfb594-5d2e-4d6a-810c-f1462740ab49", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_3", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: ball1 is not being carried by robot1's rgripper1, ball3 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's lgripper1, ball5 is not being carried by robot1's lgripper1, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball4, rgripper1 of robot1 is not carrying ball6, robot1 is not carrying ball5 with rgripper1 and robot1 is not carrying ball6 with lgripper1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is present at room1, ball4 is at room2, ball5 is located at room2, ball6 is at room1, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, are all of the following properties that involve negations True or False: robot1's rgripper1 is not holding ball1, robot1's lgripper1 is not holding ball3, robot1's lgripper1 is not holding ball4, robot1's lgripper1 is not holding ball5, robot1's lgripper1 is not grasping ball1, robot1's lgripper1 is not grasping ball2, robot1's rgripper1 is not grasping ball2, robot1's rgripper1 is not grasping ball3, robot1's rgripper1 is not grasping ball4, robot1's rgripper1 is not grasping ball6, robot1 is not holding ball5 with its rgripper1 and robot1 is not holding ball6 with its lgripper1?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, robot1 is situated in room4, and both the left gripper (lgripper1) and the right gripper (rgripper1) of robot1 are available."}
{"question_id": "faa06603-a0ae-4e67-b159-c9754fd18d88", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_14", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball1, rgripper2 of robot2 picks up ball2 in room3, robot2 moves to room2 from room3, ball1 is dropped in room2 with lgripper2 by robot2, rgripper2 of robot2 drops ball2 in room2, from room2, robot2 moves to room3, from room3, robot2's lgripper2 picks up ball4, from room3, robot2's rgripper2 picks up ball7, robot2 moves to room2 from room3, in room2, robot2's rgripper2 drops ball7, rgripper2 of robot2 picks up ball3 in room2, robot2 moves from room2 to room1, lgripper2 of robot2 drops ball4 in room1, from room1, robot2's lgripper2 picks up ball5, in room1, robot2's rgripper2 drops ball3, rgripper2 of robot2 picks up ball6 in room1, robot2 moves to room2 from room1 and lgripper2 of robot2 drops ball5 in room2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: ball1 is at room2, ball1 is located at room1, ball1 is located at room3, ball2 is at room2, ball2 is at room3, ball2 is present at room1, ball3 is at room1, ball3 is located at room2, ball3 is located at room3, ball4 is at room3, ball4 is located at room1, ball4 is located at room2, ball5 is at room1, ball5 is located at room2, ball5 is located at room3, ball6 is at room1, ball6 is located at room2, ball6 is located at room3, ball7 is at room2, ball7 is located at room3, ball7 is present at room1, robot1 is at room3, robot1 is present in room1, robot1 is present in room2, robot2 is located at room3, robot2 is present in room1 and robot2 is present in room2?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 moves from room2 to room3, then robot2's left gripper (lgripper2) picks up ball1 in room3, and robot2's right gripper (rgripper2) picks up ball2 in room3. Next, robot2 moves back to room2 from room3, drops ball1 in room2 using lgripper2, and drops ball2 in room2 using rgripper2. Then, robot2 moves to room3 again, picks up ball4 with lgripper2 and ball7 with rgripper2 in room3, and moves back to room2. In room2, robot2 drops ball7 using rgripper2, picks up ball3 using rgripper2, and then moves to room1. In room1, robot2 drops ball4 using lgripper2, picks up ball5 using lgripper2, drops ball3 using rgripper2, and picks up ball6 using rgripper2. Finally, robot2 moves to room2 and drops ball5 using lgripper2, reaching the current state. In this state, are the following properties that do not involve negations True or False: ball1 is in room2, ball1 is in room1, ball1 is in room3, ball2 is in room2, ball2 is in room3, ball2 is in room1, ball3 is in room1, ball3 is in room2, ball3 is in room3, ball4 is in room3, ball4 is in room1, ball4 is in room2, ball5 is in room1, ball5 is in room2, ball5 is in room3, ball6 is in room1, ball6 is in room2, ball6 is in room3, ball7 is in room2, ball7 is in room3, ball7 is in room1, robot1 is in room3, robot1 is in room1, robot1 is in room2, robot2 is in room3, robot2 is in room1, and robot2 is in room2?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is situated in room2, robot1's left gripper is free, robot1's right gripper is free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "0768024d-7397-4da1-a04a-ba864e148805", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_3", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3, from room3, robot2's lgripper2 picks up ball1, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves from room3 to room2, lgripper2 of robot2 drops ball1 in room2, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball4, rgripper2 of robot2 picks up ball7 in room3 and robot2 moves to room2 from room3 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: ball1 is not being carried by robot2's lgripper2, ball2 is not being carried by robot1's lgripper1, ball2 is not being carried by robot1's lgripper2, ball2 is not being carried by robot1's rgripper2, ball2 is not being carried by robot2's rgripper1, ball2 is not being carried by robot2's rgripper2, ball3 is not being carried by robot1's lgripper1, ball3 is not being carried by robot2's lgripper2, ball3 is not being carried by robot2's rgripper2, ball4 is not being carried by robot1's lgripper2, ball4 is not being carried by robot1's rgripper2, ball4 is not being carried by robot2's rgripper1, ball4 is not being carried by robot2's rgripper2, ball5 is not being carried by robot1's lgripper2, ball5 is not being carried by robot1's rgripper2, ball5 is not being carried by robot2's lgripper1, ball5 is not being carried by robot2's rgripper1, ball6 is not being carried by robot1's lgripper1, ball6 is not being carried by robot2's lgripper2, ball7 is not being carried by robot1's lgripper1, ball7 is not being carried by robot1's lgripper2, ball7 is not being carried by robot1's rgripper2, lgripper1 of robot1 is not carrying ball1, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot2 is not carrying ball1, lgripper1 of robot2 is not carrying ball2, lgripper1 of robot2 is not carrying ball3, lgripper1 of robot2 is not carrying ball4, lgripper1 of robot2 is not carrying ball7, lgripper2 of robot1 is not carrying ball1, lgripper2 of robot1 is not carrying ball6, lgripper2 of robot2 is not carrying ball2, lgripper2 of robot2 is not carrying ball5, lgripper2 of robot2 is not carrying ball7, rgripper1 of robot1 is not carrying ball1, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot1 is not carrying ball6, rgripper1 of robot2 is not carrying ball1, rgripper1 of robot2 is not carrying ball7, rgripper2 of robot1 is not carrying ball6, rgripper2 of robot2 is not carrying ball1, rgripper2 of robot2 is not carrying ball5, rgripper2 of robot2 is not carrying ball6, robot1 is not carrying ball1 with rgripper2, robot1 is not carrying ball3 with lgripper2, robot1 is not carrying ball3 with rgripper1, robot1 is not carrying ball3 with rgripper2, robot1 is not carrying ball4 with lgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball7 with rgripper1, robot2 is not carrying ball3 with rgripper1, robot2 is not carrying ball6 with lgripper1 and robot2 is not carrying ball6 with rgripper1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot2 moves from room2 to room3, then from room3, robot2's left gripper (lgripper2) picks up ball1 and its right gripper (rgripper2) picks up ball2, after which robot2 moves back to room2, drops ball1 with lgripper2 and ball2 with rgripper2 in room2, then moves back to room3, picks up ball4 with lgripper2 and ball7 with rgripper2, and finally moves to room2 to reach the current state. In this state, are the following properties that involve negations True or False: ball1 is not held by robot2's lgripper2, ball2 is not held by robot1's left gripper (lgripper1), ball2 is not held by robot1's right gripper (rgripper2), ball2 is not held by robot1's lgripper2, ball2 is not held by robot2's rgripper1, ball2 is not held by robot2's rgripper2, ball3 is not held by robot1's lgripper1, ball3 is not held by robot2's lgripper2, ball3 is not held by robot2's rgripper2, ball4 is not held by robot1's lgripper2, ball4 is not held by robot1's rgripper2, ball4 is not held by robot2's rgripper1, ball4 is not held by robot2's rgripper2, ball5 is not held by robot1's lgripper2, ball5 is not held by robot1's rgripper2, ball5 is not held by robot2's lgripper1, ball5 is not held by robot2's rgripper1, ball6 is not held by robot1's lgripper1, ball6 is not held by robot2's lgripper2, ball7 is not held by robot1's lgripper1, ball7 is not held by robot1's lgripper2, ball7 is not held by robot1's rgripper2, robot1's lgripper1 is not holding ball1, robot1's lgripper1 is not holding ball5, robot2's lgripper1 is not holding ball1, robot2's lgripper1 is not holding ball2, robot2's lgripper1 is not holding ball3, robot2's lgripper1 is not holding ball4, robot2's lgripper1 is not holding ball7, robot1's lgripper2 is not holding ball1, robot1's lgripper2 is not holding ball6, robot2's lgripper2 is not holding ball2, robot2's lgripper2 is not holding ball5, robot2's lgripper2 is not holding ball7, robot1's rgripper1 is not holding ball1, robot1's rgripper1 is not holding ball2, robot1's rgripper1 is not holding ball5, robot1's rgripper1 is not holding ball6, robot2's rgripper1 is not holding ball1, robot2's rgripper1 is not holding ball7, robot1's rgripper2 is not holding ball6, robot2's rgripper2 is not holding ball1, robot2's rgripper2 is not holding ball5, robot2's rgripper2 is not holding ball6, robot1 is not holding ball1 with rgripper2, robot1 is not holding ball3 with lgripper2, robot1 is not holding ball3 with rgripper1, robot1 is not holding ball3 with rgripper2, robot1 is not holding ball4 with lgripper1, robot1 is not holding ball4 with rgripper1, robot1 is not holding ball7 with rgripper1, robot2 is not holding ball3 with rgripper1, robot2 is not holding ball6 with lgripper1, and robot2 is not holding ball6 with rgripper1?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is situated in room2, robot1's left gripper is free, robot1's right gripper is free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "f895cba8-0f01-47a1-b50e-a3984026c1d4", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, from room1, robot1's lgripper1 picks up ball1, from room1, robot1 moves to room2, from room2, robot1's rgripper1 picks up ball2, robot1 moves from room2 to room3, lgripper1 of robot1 drops ball1 in room3, robot1 moves to room4 from room3, ball2 is dropped in room4 with rgripper1 by robot1, robot1 moves to room2 from room4 and ball4 is picked from room2 with lgripper1 by robot1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: rgripper1 of robot1 is free?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is present at room1, ball4 is at room2, ball5 is located at room2, ball6 is at room1, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: robot1 relocates from room4 to room1, then from room1, robot1's left gripper (lgripper1) grasps ball1, next, robot1 moves from room1 to room2, from room2, robot1's right gripper (rgripper1) picks up ball2, robot1 proceeds from room2 to room3, where lgripper1 of robot1 releases ball1, robot1 then moves from room3 to room4, and in room4, robot1's rgripper1 drops ball2, subsequently, robot1 moves from room4 back to room2, and finally, robot1's lgripper1 picks up ball4 in room2 to attain the current state. In this state, are the following properties of the state that do not involve negations True or False: is the right gripper (rgripper1) of robot1 free?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, robot1 is situated in room4, and both the left gripper (lgripper1) and the right gripper (rgripper1) of robot1 are available."}
{"question_id": "a33c402d-d25f-4f2d-b819-aa32782e0f3d", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_3", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball1, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves to room2 from room3, ball1 is dropped in room2 with lgripper2 by robot2, rgripper2 of robot2 drops ball2 in room2, from room2, robot2 moves to room3, ball4 is picked from room3 with lgripper2 by robot2, ball7 is picked from room3 with rgripper2 by robot2 and from room3, robot2 moves to room2 to reach the current state. In this state, is it True or False that robot1 is not carrying ball1 with rgripper2?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: robot2 relocates from room2 to room3, then uses its lgripper2 to grasp ball1 in room3, and simultaneously uses rgripper2 to pick up ball2 in room3. Next, robot2 moves back to room2 from room3, and then drops ball1 in room2 using lgripper2, followed by dropping ball2 in room2 with rgripper2. Subsequently, robot2 returns to room3 from room2, picks up ball4 with lgripper2 and ball7 with rgripper2 in room3, and then moves back to room2 to reach the current state. In this state, is it True or False that robot1 is not holding ball1 with rgripper2?", "initial_state_nl_paraphrased": "Ball1 is in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is located in room1, ball6 is also in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is positioned in room2, robot1's left gripper is free, robot1's right gripper is also free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "7b7e22a0-e706-4457-bd60-ec57bdea3687", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7 to reach the current state. In this state, is it True or False that robot1 is carrying ball3 with lgripper1?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is at room1, ball4 is at room2, ball5 is located at room3, ball6 is located at room1, ball7 is at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7 to reach the current state. In this state, is it True or False that robot1's lgripper1 is holding ball3?", "initial_state_nl_paraphrased": "The location of the balls is as follows: ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room3, ball6 is in room1, and ball7 is in room4. Additionally, the status of robot1 is as follows: its left gripper (lgripper1) is free, it is currently in room4, and its right gripper (rgripper1) is available."}
{"question_id": "374caa88-b9f2-4106-9e54-67bdf4561534", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_10", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, ball1 is picked from room3 with lgripper2 by robot2, from room3, robot2's rgripper2 picks up ball2, robot2 moves from room3 to room2, lgripper2 of robot2 drops ball1 in room2, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball4, from room3, robot2's rgripper2 picks up ball7, robot2 moves from room3 to room2, in room2, robot2's rgripper2 drops ball7, ball3 is picked from room2 with rgripper2 by robot2, robot2 moves from room2 to room1, in room1, robot2's lgripper2 drops ball4, lgripper2 of robot2 picks up ball5 in room1, ball3 is dropped in room1 with rgripper2 by robot2, from room1, robot2's rgripper2 picks up ball6, robot2 moves to room2 from room1 and in room2, robot2's lgripper2 drops ball5 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: robot1's lgripper1 is not available, robot1's lgripper2 is not free, robot1's rgripper1 is not available, robot1's rgripper2 is not free, robot2's lgripper1 is not free, robot2's lgripper2 is not available, robot2's rgripper1 is not free and robot2's rgripper2 is not available?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 relocates from room2 to room3, robot2 uses its lgripper2 to pick up ball1 in room3, then robot2's rgripper2 picks up ball2 in room3, robot2 moves back to room2, drops ball1 in room2 using lgripper2, and drops ball2 in room2 using rgripper2, robot2 then moves to room3, picks up ball4 with lgripper2 and ball7 with rgripper2 in room3, moves back to room2, drops ball7 in room2 using rgripper2, picks up ball3 in room2 using rgripper2, moves to room1, drops ball4 in room1 using lgripper2, picks up ball5 in room1 using lgripper2, drops ball3 in room1 using rgripper2, picks up ball6 in room1 using rgripper2, and finally moves to room2 and drops ball5 using lgripper2 to reach the current state. In this state, are the following properties that involve negations True or False: robot1's lgripper1 is not available, robot1's lgripper2 is not free, robot1's rgripper1 is not available, robot1's rgripper2 is not free, robot2's lgripper1 is not free, robot2's lgripper2 is not available, robot2's rgripper1 is not free, and robot2's rgripper2 is not available?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is situated in room2, robot1's left gripper is free, robot1's right gripper is free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "344c22f3-e7f8-49de-851f-85686644ff0a", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_6", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2, from room3, robot2's lgripper2 picks up ball1, rgripper2 of robot2 picks up ball2 in room3, robot2 moves from room3 to room2, in room2, robot2's lgripper2 drops ball1, rgripper2 of robot2 drops ball2 in room2, from room2, robot2 moves to room3, ball4 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball7 in room3 and from room3, robot2 moves to room2 to reach the current state. In this state, is it True or False that lgripper1 of robot2 is free?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot2 moves from room2 to room3, then in room3, robot2's left gripper (lgripper2) grasps ball1 and its right gripper (rgripper2) grasps ball2. Next, robot2 moves back to room2, where it releases ball1 with lgripper2 and ball2 with rgripper2. Subsequently, robot2 returns to room3, picks up ball4 with lgripper2 and ball7 with rgripper2, and then moves back to room2, resulting in the current state. In this state, is it True or False that lgripper1 of robot2 is free?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is situated in room2, robot1's left gripper is free, robot1's right gripper is free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "e68bb824-cb6a-4734-8a9c-0e6b8625e7df", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_6", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1 moves to room2, from room2, robot1's rgripper1 picks up ball2, robot1 moves to room3 from room2, in room3, robot1's lgripper1 drops ball1, robot1 moves to room4 from room3, rgripper1 of robot1 drops ball2 in room4, from room4, robot1 moves to room2 and from room2, robot1's lgripper1 picks up ball4 to reach the current state. In this state, is it True or False that lgripper1 of robot1 is free?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is present at room1, ball4 is at room2, ball5 is located at room2, ball6 is at room1, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 relocates from room4 to room1, where its left gripper (lgripper1) grasps ball1, then robot1 proceeds to room2, where its right gripper (rgripper1) picks up ball2, after which robot1 moves to room3, drops ball1 using lgripper1, and then heads to room4, where rgripper1 releases ball2, subsequently returning to room2 and finally, using lgripper1, robot1 picks up ball4 to attain the current state. In this state, is it True or False that the left gripper (lgripper1) of robot1 is free?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, robot1 is situated in room4, and both the left gripper (lgripper1) and the right gripper (rgripper1) of robot1 are available."}
{"question_id": "cf918da7-a7d3-45c1-ade8-a60dbab5b76e", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_14", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1, robot1 moves to room5 from room4, in room5, robot1's lgripper1 drops ball7, robot1 moves to room1 from room5, ball1 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves to room5 from room1, in room5, robot1's lgripper1 drops ball1, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, from room2, robot1's lgripper1 picks up ball2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1, rgripper1 of robot1 picks up ball6 in room1, robot1 moves to room3 from room1, in room3, robot1's rgripper1 drops ball6, rgripper1 of robot1 picks up ball5 in room3 and robot1 moves to room6 from room3 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: ball1 is at room1, ball1 is at room3, ball1 is located at room2, ball1 is located at room4, ball1 is present at room5, ball1 is present at room6, ball2 is at room3, ball2 is at room4, ball2 is at room6, ball2 is located at room5, ball2 is present at room1, ball2 is present at room2, ball3 is at room3, ball3 is located at room1, ball3 is located at room2, ball3 is present at room4, ball3 is present at room5, ball3 is present at room6, ball4 is at room3, ball4 is at room4, ball4 is at room5, ball4 is at room6, ball4 is located at room1, ball4 is present at room2, ball5 is at room4, ball5 is at room5, ball5 is at room6, ball5 is located at room3, ball5 is present at room1, ball5 is present at room2, ball6 is at room5, ball6 is at room6, ball6 is located at room3, ball6 is located at room4, ball6 is present at room1, ball6 is present at room2, ball7 is at room1, ball7 is at room5, ball7 is located at room3, ball7 is located at room4, ball7 is present at room2, ball7 is present at room6, robot1 is located at room1, robot1 is located at room5, robot1 is located at room6, robot1 is present in room2, robot1 is present in room3 and robot1 is present in room4?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is at room1, ball4 is at room2, ball5 is located at room3, ball6 is located at room1, ball7 is at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 uses lgripper1 to pick up ball7 from room4, then moves from room4 to room5, where it drops ball7 using lgripper1, and then proceeds to room1, where it picks up ball1 with lgripper1 and ball3 with rgripper1, after which it moves to room5, drops ball1 with lgripper1 and ball3 with rgripper1, and then heads to room2, where it picks up ball2 with lgripper1 and ball4 with rgripper1, then moves to room1, drops ball4 with rgripper1, picks up ball6 with rgripper1, moves to room3, drops ball6 with rgripper1, picks up ball5 with rgripper1, and finally moves to room6, reaching the current state. In this state, are the following properties, which do not involve negations, True or False: ball1 is in room1, ball1 is in room3, ball1 is in room2, ball1 is in room4, ball1 is in room5, ball1 is in room6, ball2 is in room3, ball2 is in room4, ball2 is in room6, ball2 is in room5, ball2 is in room1, ball2 is in room2, ball3 is in room3, ball3 is in room1, ball3 is in room2, ball3 is in room4, ball3 is in room5, ball3 is in room6, ball4 is in room3, ball4 is in room4, ball4 is in room5, ball4 is in room6, ball4 is in room1, ball4 is in room2, ball5 is in room4, ball5 is in room5, ball5 is in room6, ball5 is in room3, ball5 is in room1, ball5 is in room2, ball6 is in room5, ball6 is in room6, ball6 is in room3, ball6 is in room4, ball6 is in room1, ball6 is in room2, ball7 is in room1, ball7 is in room5, ball7 is in room3, ball7 is in room4, ball7 is in room2, ball7 is in room6, robot1 is in room1, robot1 is in room5, robot1 is in room6, robot1 is in room2, robot1 is in room3, and robot1 is in room4?", "initial_state_nl_paraphrased": "The location of the balls is as follows: ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room3, ball6 is in room1, and ball7 is in room4. Additionally, the status of robot1 is as follows: its left gripper (lgripper1) is free, it is currently in room4, and its right gripper (rgripper1) is available."}
{"question_id": "84885a03-fb85-4aa3-ba49-76d8aed737b3", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: lgripper1 of robot1 picks up ball7 in room4, from room4, robot1 moves to room5, ball7 is dropped in room5 with lgripper1 by robot1, from room5, robot1 moves to room1, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1 moves to room5, in room5, robot1's lgripper1 drops ball1, ball3 is dropped in room5 with rgripper1 by robot1 and robot1 moves to room2 from room5 to reach the current state. In this state, is it True or False that rgripper1 of robot1 is carrying ball3?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is at room1, ball4 is at room2, ball5 is located at room3, ball6 is located at room1, ball7 is at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1's lgripper1 picks up ball7 in room4, then robot1 moves from room4 to room5, where it drops ball7 using lgripper1, after which robot1 moves to room1, picks up ball1 with lgripper1, and simultaneously, robot1's rgripper1 picks up ball3 in room1, then robot1 moves to room5, drops ball1 with lgripper1, drops ball3 with rgripper1, and finally moves to room2 from room5 to reach the current state. In this state, is it True or False that robot1's rgripper1 is carrying ball3?", "initial_state_nl_paraphrased": "The location of the balls is as follows: ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room3, ball6 is in room1, and ball7 is in room4. The status of robot1 is as follows: its left gripper (lgripper1) is free, it is currently in room4, and its right gripper (rgripper1) is available."}
{"question_id": "fbd71ccd-ffb7-40c3-87df-da1d7bf23f7a", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_4", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, lgripper2 of robot2 picks up ball1 in room3, rgripper2 of robot2 picks up ball2 in room3, robot2 moves from room3 to room2, lgripper2 of robot2 drops ball1 in room2, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves to room3 from room2, lgripper2 of robot2 picks up ball4 in room3, from room3, robot2's rgripper2 picks up ball7, robot2 moves from room3 to room2, rgripper2 of robot2 drops ball7 in room2, ball3 is picked from room2 with rgripper2 by robot2, from room2, robot2 moves to room1, ball4 is dropped in room1 with lgripper2 by robot2, lgripper2 of robot2 picks up ball5 in room1, ball3 is dropped in room1 with rgripper2 by robot2, from room1, robot2's rgripper2 picks up ball6, robot2 moves to room2 from room1 and ball5 is dropped in room2 with lgripper2 by robot2 to reach the current state. In this state, is it True or False that rgripper2 of robot2 is not carrying ball6?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 relocates from room2 to room3, where its left gripper (lgripper2) grasps ball1 and its right gripper (rgripper2) grasps ball2. Then, robot2 moves back to room2, and in this room, it releases ball1 using lgripper2 and drops ball2 using rgripper2. Next, robot2 returns to room3, where lgripper2 picks up ball4 and rgripper2 picks up ball7. Robot2 then moves back to room2, and in this room, it drops ball7 using rgripper2 and picks up ball3 using rgripper2. After that, robot2 proceeds to room1, where it drops ball4 using lgripper2, picks up ball5 using lgripper2, drops ball3 using rgripper2, and picks up ball6 using rgripper2. Finally, robot2 moves to room2 and drops ball5 using lgripper2 to reach the current state. In this state, is it True or False that rgripper2 of robot2 is not holding ball6?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is found in room3, ball5 is positioned in room1, ball6 is also in room1, ball7 is situated in room3, the left gripper of robot2 is available, robot1 is positioned in room2, robot1's left gripper is free, robot1's right gripper is also free, robot2 is situated in room2 and robot2's right gripper is available."}
{"question_id": "2b932dbe-6300-410f-8b89-d73eac174f8b", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_4", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: ball1 is not being carried by robot1's rgripper1, ball2 is not being carried by robot1's lgripper1, ball2 is not being carried by robot1's rgripper1, ball3 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's lgripper1, ball4 is not being carried by robot1's rgripper1, ball5 is not being carried by robot1's lgripper1, ball6 is not being carried by robot1's rgripper1, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball7, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with lgripper1 and robot1 is not carrying ball7 with lgripper1?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is at room1, ball4 is at room2, ball5 is located at room3, ball6 is located at room1, ball7 is at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from room4, robot1's left gripper (lgripper1) picks up ball7 to reach the current state. In this state, are all of the following properties that involve negations True or False: ball1 is not held by robot1's right gripper (rgripper1), ball2 is not held by robot1's left gripper (lgripper1), ball2 is not held by robot1's right gripper (rgripper1), ball3 is not held by robot1's left gripper (lgripper1), ball4 is not held by robot1's left gripper (lgripper1), ball4 is not held by robot1's right gripper (rgripper1), ball5 is not held by robot1's left gripper (lgripper1), ball6 is not held by robot1's right gripper (rgripper1), the right gripper (rgripper1) of robot1 is not holding ball3, the right gripper (rgripper1) of robot1 is not holding ball7, robot1 is not holding ball1 with its left gripper (lgripper1), robot1 is not holding ball5 with its right gripper (rgripper1), robot1 is not holding ball6 with its left gripper (lgripper1), and robot1 is not holding ball7 with its left gripper (lgripper1)?", "initial_state_nl_paraphrased": "The location of the balls is as follows: ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room3, ball6 is in room1, and ball7 is in room4. Additionally, robot1's left gripper (lgripper1) is unoccupied, the robot itself is in room4, and its right gripper (rgripper1) is available for use."}
{"question_id": "df84030b-23cd-4553-be5d-a78e39d000df", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_15", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: ball1 is not located at room4, ball1 is not present at room2, ball1 is not present at room3, ball1 is not present at room5, ball2 is not at room1, ball2 is not at room3, ball2 is not present at room4, ball2 is not present at room5, ball3 is not at room3, ball3 is not at room4, ball3 is not at room5, ball3 is not present at room2, ball4 is not at room3, ball4 is not at room5, ball4 is not present at room1, ball4 is not present at room4, ball5 is not at room1, ball5 is not at room4, ball5 is not at room5, ball5 is not present at room3, ball6 is not at room4, ball6 is not located at room2, ball6 is not located at room3, ball6 is not present at room5, robot1 is not at room5, robot1 is not located at room3, robot1 is not present in room2 and robot1 is not present in room4?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is present at room1, ball4 is at room2, ball5 is located at room2, ball6 is at room1, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: from room4, robot1 moves to room1 to reach the current state. In this state, are all of the following properties that involve negations True or False: ball1 is not in room4, ball1 is not in room2, ball1 is not in room3, ball1 is not in room5, ball2 is not in room1, ball2 is not in room3, ball2 is not in room4, ball2 is not in room5, ball3 is not in room3, ball3 is not in room4, ball3 is not in room5, ball3 is not in room2, ball4 is not in room3, ball4 is not in room5, ball4 is not in room1, ball4 is not in room4, ball5 is not in room1, ball5 is not in room4, ball5 is not in room5, ball5 is not in room3, ball6 is not in room4, ball6 is not in room2, ball6 is not in room3, ball6 is not in room5, robot1 is not in room5, robot1 is not in room3, robot1 is not in room2, and robot1 is not in room4?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, robot1 is situated in room4, and both the left gripper (lgripper1) and the right gripper (rgripper1) of robot1 are available."}
{"question_id": "43de0ba3-2b93-4299-b4a1-d94d041d78e4", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, is it True or False that robot1's rgripper1 is not available?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is present at room1, ball4 is at room2, ball5 is located at room2, ball6 is at room1, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, is it True or False that the availability of robot1's rgripper1 is False?\n\nAlternatively, \nGiven the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, is it True or False that robot1's rgripper1 is unavailable?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, robot1 is situated in room4, and both the left gripper (lgripper1) and the right gripper (rgripper1) of robot1 are available."}
{"question_id": "cc77a7b0-6e5f-4282-a976-a73195438538", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_3", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, is it True or False that robot1 is not carrying ball5 with rgripper1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot1 moves from room4 to room1 to achieve the current state. In this state, is it True or False that robot1 is not holding ball5 with rgripper1?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is situated in room3, robot1 is situated in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "46d0d158-20d9-4798-9a00-724836c2ca72", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7, from room4, robot1 moves to room5, in room5, robot1's lgripper1 drops ball7, robot1 moves to room1 from room5, from room1, robot1's lgripper1 picks up ball1, ball3 is picked from room1 with rgripper1 by robot1, robot1 moves to room5 from room1, lgripper1 of robot1 drops ball1 in room5, in room5, robot1's rgripper1 drops ball3, from room5, robot1 moves to room2, lgripper1 of robot1 picks up ball2 in room2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves to room1 from room2, rgripper1 of robot1 drops ball4 in room1, rgripper1 of robot1 picks up ball6 in room1, from room1, robot1 moves to room3, in room3, robot1's rgripper1 drops ball6, from room3, robot1's rgripper1 picks up ball5 and robot1 moves to room6 from room3 to reach the current state. In this state, is it True or False that robot1's rgripper1 is not free?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is at room1, ball4 is at room2, ball5 is located at room3, ball6 is located at room1, ball7 is at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 starts in room4, where its left gripper (lgripper1) picks up ball7, then robot1 moves to room5, and in room5, lgripper1 drops ball7. Next, robot1 moves to room1, where it uses lgripper1 to pick up ball1 and its right gripper (rgripper1) to pick up ball3. Then, robot1 moves to room5, drops ball1 with lgripper1, and drops ball3 with rgripper1. From room5, robot1 proceeds to room2, where it uses lgripper1 to pick up ball2 and rgripper1 to pick up ball4. After that, robot1 moves to room1, drops ball4 with rgripper1, and then uses rgripper1 to pick up ball6. Subsequently, robot1 moves to room3, drops ball6 with rgripper1, picks up ball5 with rgripper1, and finally moves to room6 from room3, reaching the current state. In this state, is it True or False that robot1's right gripper (rgripper1) is not free?", "initial_state_nl_paraphrased": "The location of the balls is as follows: ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room3, ball6 is in room1, and ball7 is in room4. Additionally, the status of robot1 is as follows: its left gripper (lgripper1) is free, it is currently in room4, and its right gripper (rgripper1) is available."}
{"question_id": "71c66007-d201-41b0-ba99-14cecd95f1d6", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: robot1's lgripper1 is available and robot1's rgripper1 is free?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is present at room1, ball4 is at room2, ball5 is located at room2, ball6 is at room1, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: robot1 moves from room4 to room1 to achieve the current state. In this state, are the following properties, which do not involve negations, True or False: robot1's lgripper1 is available and robot1's rgripper1 is free?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, robot1 is situated in room4, and both the left gripper (lgripper1) and the right gripper (rgripper1) of robot1 are available."}
{"question_id": "923ff389-0c2e-4190-8869-e5221dd2bc39", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_13", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: ball1 is at room3, ball2 is at room3, ball3 is present at room2, ball4 is located at room3, ball5 is located at room1, ball6 is at room1, ball7 is at room3, robot1 is present in room2 and robot2 is at room3?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: ball1 is in room3, ball2 is in room3, ball3 is in room2, ball4 is in room3, ball5 is in room1, ball6 is in room1, ball7 is in room3, robot1 is in room2 and robot2 is in room3?", "initial_state_nl_paraphrased": "Ball1 is in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is located in room1, ball6 is also in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is situated in room2, robot1's left gripper is free, robot1's right gripper is free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "b1167583-c6d4-4528-bdea-5e9ba1d5ce58", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7 to reach the current state. In this state, is it True or False that rgripper1 of robot1 is not free?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is at room1, ball4 is at room2, ball5 is located at room3, ball6 is located at room1, ball7 is at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from room4, robot1's lgripper1 picks up ball7 to reach the current state. In this state, is it True or False that robot1's rgripper1 is not available?", "initial_state_nl_paraphrased": "The location of the balls is as follows: ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room3, ball6 is in room1, and ball7 is in room4. Additionally, the status of robot1 is as follows: its left gripper (lgripper1) is free, it is currently in room4, and its right gripper (rgripper1) is available."}
{"question_id": "488a1cb9-c500-45cc-8274-caae61ee3ce5", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_10", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, from room1, robot1's lgripper1 picks up ball1, from room1, robot1 moves to room2, from room2, robot1's rgripper1 picks up ball2, robot1 moves to room3 from room2, lgripper1 of robot1 drops ball1 in room3, robot1 moves from room3 to room4, rgripper1 of robot1 drops ball2 in room4, from room4, robot1 moves to room2 and from room2, robot1's lgripper1 picks up ball4 to reach the current state. In this state, is it True or False that robot1 is located at room5?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is present at room1, ball4 is at room2, ball5 is located at room2, ball6 is at room1, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: robot1 relocates from room4 to room1, then from room1, robot1's left gripper (lgripper1) grasps ball1, next, robot1 moves from room1 to room2, and from room2, robot1's right gripper (rgripper1) picks up ball2, robot1 then proceeds from room2 to room3, where lgripper1 releases ball1, afterwards, robot1 moves from room3 back to room4, and rgripper1 drops ball2 in room4, subsequently, robot1 moves from room4 to room2, and finally, from room2, lgripper1 of robot1 picks up ball4, resulting in the current state. In this state, is it True or False that robot1 is situated at room5?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, robot1 is situated in room4, and both the left gripper (lgripper1) and the right gripper (rgripper1) of robot1 are available."}
{"question_id": "69a57c41-0b81-470a-b685-8c51cce4d666", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_15", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves to room5 from room1, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves to room2 from room5, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves to room1 from room2, rgripper1 of robot1 drops ball4 in room1 and rgripper1 of robot1 picks up ball6 in room1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: ball1 is not at room1, ball1 is not located at room3, ball1 is not present at room2, ball1 is not present at room4, ball1 is not present at room5, ball2 is not at room4, ball2 is not present at room1, ball2 is not present at room3, ball2 is not present at room5, ball3 is not at room3, ball3 is not located at room1, ball3 is not located at room2, ball3 is not present at room4, ball4 is not at room2, ball4 is not at room4, ball4 is not present at room3, ball4 is not present at room5, ball5 is not at room5, ball5 is not located at room3, ball5 is not located at room4, ball5 is not present at room1, ball6 is not at room1, ball6 is not located at room2, ball6 is not located at room4, ball6 is not present at room3, ball6 is not present at room5, ball7 is not at room5, ball7 is not located at room4, ball7 is not present at room1, ball7 is not present at room2, robot1 is not at room3, robot1 is not at room4, robot1 is not present in room2 and robot1 is not present in room5?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 moves from room4 to room1, then uses its left gripper (lgripper1) to pick up ball1 in room1, and its right gripper (rgripper1) to pick up ball3 in room1. Next, robot1 moves from room1 to room5, drops ball3 in room5 using rgripper1, and then moves from room5 to room2. In room2, robot1 uses rgripper1 to pick up ball4, moves from room2 to room1, drops ball4 in room1 using rgripper1, and finally uses rgripper1 to pick up ball6 in room1, resulting in the current state. In this state, are the following properties that involve negations True or False: ball1 is not in room1, ball1 is not in room3, ball1 is not in room2, ball1 is not in room4, ball1 is not in room5, ball2 is not in room4, ball2 is not in room1, ball2 is not in room3, ball2 is not in room5, ball3 is not in room3, ball3 is not in room1, ball3 is not in room2, ball3 is not in room4, ball4 is not in room2, ball4 is not in room4, ball4 is not in room3, ball4 is not in room5, ball5 is not in room5, ball5 is not in room3, ball5 is not in room4, ball5 is not in room1, ball6 is not in room1, ball6 is not in room2, ball6 is not in room4, ball6 is not in room3, ball6 is not in room5, ball7 is not in room5, ball7 is not in room4, ball7 is not in room1, ball7 is not in room2, robot1 is not in room3, robot1 is not in room4, robot1 is not in room2, and robot1 is not in room5?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is situated in room3, robot1 is situated in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "74495887-c37d-45a4-b732-f9042a9b2d16", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_4", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, lgripper1 of robot1 picks up ball1 in room1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves from room1 to room5, rgripper1 of robot1 drops ball3 in room5, robot1 moves to room2 from room5, from room2, robot1's rgripper1 picks up ball4, from room2, robot1 moves to room1, rgripper1 of robot1 drops ball4 in room1 and from room1, robot1's rgripper1 picks up ball6 to reach the current state. In this state, is it True or False that robot1 is not carrying ball6 with rgripper1?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 relocates from room4 to room1, then the left gripper of robot1 (lgripper1) grasps ball1 in room1, and the right gripper of robot1 (rgripper1) picks up ball3 in room1. Next, robot1 moves from room1 to room5, where rgripper1 releases ball3. Robot1 then proceeds to room2 from room5, picks up ball4 with rgripper1 in room2, and moves back to room1. In room1, rgripper1 drops ball4 and subsequently picks up ball6, resulting in the current state. In this state, is it True or False that robot1 is not holding ball6 with rgripper1?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is situated in room3, robot1 is situated in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "1fc4765b-7ecb-479b-a0a7-1f4ffafd405f", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_3", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: lgripper1 of robot1 picks up ball7 in room4, robot1 moves from room4 to room5, lgripper1 of robot1 drops ball7 in room5, robot1 moves from room5 to room1, from room1, robot1's lgripper1 picks up ball1, from room1, robot1's rgripper1 picks up ball3, robot1 moves to room5 from room1, in room5, robot1's lgripper1 drops ball1, ball3 is dropped in room5 with rgripper1 by robot1, from room5, robot1 moves to room2, lgripper1 of robot1 picks up ball2 in room2, rgripper1 of robot1 picks up ball4 in room2, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball6, from room1, robot1 moves to room3, ball6 is dropped in room3 with rgripper1 by robot1, from room3, robot1's rgripper1 picks up ball5 and robot1 moves from room3 to room6 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: ball3 is not being carried by robot1's rgripper1, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball6, lgripper1 of robot1 is not carrying ball7, rgripper1 of robot1 is not carrying ball2, rgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not carrying ball7, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball1 with rgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball4 with rgripper1 and robot1 is not carrying ball5 with lgripper1?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is at room1, ball4 is at room2, ball5 is located at room3, ball6 is located at room1, ball7 is at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1's left gripper (lgripper1) picks up ball7 in room4, then robot1 moves to room5, where it drops ball7 using lgripper1, and then proceeds to room1. In room1, robot1's lgripper1 picks up ball1, and its right gripper (rgripper1) picks up ball3. Robot1 then moves to room5, where it drops ball1 using lgripper1 and ball3 using rgripper1. From room5, robot1 moves to room2, where its lgripper1 picks up ball2 and its rgripper1 picks up ball4. Robot1 then moves to room1, where it drops ball4 using rgripper1, and its rgripper1 picks up ball6. From room1, robot1 moves to room3, where it drops ball6 using rgripper1, and then its rgripper1 picks up ball5. Finally, robot1 moves from room3 to room6, reaching the current state. In this state, are the following properties that involve negations True or False: robot1's rgripper1 is not holding ball3, robot1's lgripper1 is not holding ball4, robot1's lgripper1 is not holding ball6, robot1's lgripper1 is not holding ball7, robot1's rgripper1 is not holding ball2, robot1's rgripper1 is not holding ball6, robot1's rgripper1 is not holding ball7, robot1 is not holding ball1 with its lgripper1, robot1 is not holding ball1 with its rgripper1, robot1 is not holding ball3 with its lgripper1, robot1 is not holding ball4 with its rgripper1, and robot1 is not holding ball5 with its lgripper1?", "initial_state_nl_paraphrased": "The location of the balls is as follows: ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room3, ball6 is in room1, and ball7 is in room4. Additionally, the status of robot1 is as follows: its left gripper (lgripper1) is free, it is currently in room4, and its right gripper (rgripper1) is available."}
{"question_id": "8e6b3870-6690-4f95-9331-1674caac5f96", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2, lgripper2 of robot2 picks up ball1 in room3, rgripper2 of robot2 picks up ball2 in room3, from room3, robot2 moves to room2, in room2, robot2's lgripper2 drops ball1, in room2, robot2's rgripper2 drops ball2, robot2 moves from room2 to room3, ball4 is picked from room3 with lgripper2 by robot2, from room3, robot2's rgripper2 picks up ball7, from room3, robot2 moves to room2, ball7 is dropped in room2 with rgripper2 by robot2, from room2, robot2's rgripper2 picks up ball3, from room2, robot2 moves to room1, in room1, robot2's lgripper2 drops ball4, ball5 is picked from room1 with lgripper2 by robot2, rgripper2 of robot2 drops ball3 in room1, from room1, robot2's rgripper2 picks up ball6, from room1, robot2 moves to room2 and ball5 is dropped in room2 with lgripper2 by robot2 to reach the current state. In this state, is it True or False that robot2 is carrying ball6 with rgripper2?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 transitions from room2 to room3, where it uses its left gripper (lgripper2) to grasp ball1 and its right gripper (rgripper2) to grasp ball2. Then, robot2 moves back to room2, and in room2, it releases ball1 with lgripper2 and ball2 with rgripper2. Next, robot2 returns to room3, picks up ball4 with lgripper2, and uses rgripper2 to grasp ball7. Robot2 then moves back to room2, where it drops ball7 with rgripper2, picks up ball3 with rgripper2, and proceeds to room1. In room1, robot2 releases ball4 with lgripper2, picks up ball5 with lgripper2, drops ball3 with rgripper2, and then picks up ball6 with rgripper2. Finally, robot2 moves to room2 and drops ball5 with lgripper2, reaching the current state. In this state, is it True or False that robot2 is carrying ball6 with rgripper2?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is positioned in room2, robot1's left gripper is free, robot1's right gripper is free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "d75b563f-fecf-4f34-9768-c502d797c6c0", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_4", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1's rgripper1 picks up ball3, robot1 moves to room5 from room1, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, ball4 is picked from room2 with rgripper1 by robot1, from room2, robot1 moves to room1, in room1, robot1's rgripper1 drops ball4, from room1, robot1's rgripper1 picks up ball6, from room1, robot1 moves to room3, in room3, robot1's rgripper1 drops ball6, rgripper1 of robot1 picks up ball7 in room3, robot1 moves from room3 to room4, in room4, robot1's lgripper1 drops ball1, ball7 is dropped in room4 with rgripper1 by robot1, robot1 moves to room2 from room4, from room2, robot1's lgripper1 picks up ball2 and from room2, robot1's rgripper1 picks up ball5 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: ball1 is not being carried by robot1's rgripper1, ball2 is not being carried by robot1's rgripper1, ball4 is not being carried by robot1's lgripper1, lgripper1 of robot1 is not carrying ball2, lgripper1 of robot1 is not carrying ball3, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot1 is not carrying ball6, rgripper1 of robot1 is not carrying ball3, rgripper1 of robot1 is not carrying ball7, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball5 with rgripper1, robot1 is not carrying ball6 with rgripper1 and robot1 is not carrying ball7 with lgripper1?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1 relocates from room4 to room1, then robot1's lgripper1 grasps ball1 in room1, followed by robot1's rgripper1 picking up ball3 in room1, robot1 then moves from room1 to room5, and in room5, robot1's rgripper1 releases ball3, after which robot1 proceeds to room2 from room5, where robot1's rgripper1 picks up ball4, robot1 then moves back to room1, and in room1, robot1's rgripper1 drops ball4, subsequently, robot1's rgripper1 picks up ball6 in room1, and then robot1 moves to room3, where robot1's rgripper1 releases ball6, robot1's rgripper1 then picks up ball7 in room3, robot1 moves from room3 to room4, and in room4, robot1's lgripper1 drops ball1, and robot1's rgripper1 releases ball7, robot1 then moves to room2 from room4, and from room2, robot1's lgripper1 picks up ball2, and robot1's rgripper1 picks up ball5, resulting in the current state. In this state, are the following properties that involve negations True or False: robot1's rgripper1 is not holding ball1, robot1's rgripper1 is not holding ball2, robot1's lgripper1 is not holding ball4, robot1's lgripper1 is not carrying ball2, robot1's lgripper1 is not carrying ball3, robot1's lgripper1 is not carrying ball5, robot1's lgripper1 is not carrying ball6, robot1's rgripper1 is not carrying ball3, robot1's rgripper1 is not carrying ball7, robot1 is not holding ball1 with its lgripper1, robot1 is not holding ball4 with its rgripper1, robot1 is not holding ball5 with its rgripper1, robot1 is not holding ball6 with its rgripper1, and robot1 is not holding ball7 with its lgripper1?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is situated in room3, robot1 is situated in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "fc04501b-e69d-47cb-b5fb-c1090f5f21fc", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room2, robot2 moves to room3, lgripper2 of robot2 picks up ball1 in room3, from room3, robot2's rgripper2 picks up ball2, from room3, robot2 moves to room2, in room2, robot2's lgripper2 drops ball1, in room2, robot2's rgripper2 drops ball2, robot2 moves from room2 to room3, ball4 is picked from room3 with lgripper2 by robot2, ball7 is picked from room3 with rgripper2 by robot2 and from room3, robot2 moves to room2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: ball7 is being carried by robot2's rgripper2 and robot2 is carrying ball4 with lgripper2?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot2 moves from room2 to room3, then robot2's left gripper (lgripper2) picks up ball1 in room3, followed by robot2's right gripper (rgripper2) picking up ball2 in room3. Next, robot2 moves back to room2, where it drops ball1 with lgripper2 and ball2 with rgripper2. Then, robot2 moves from room2 to room3 again, picks up ball4 with lgripper2 and ball7 with rgripper2, and finally moves back to room2 to reach the current state. In this state, are the following properties, which do not involve negations, True or False: robot2's rgripper2 is carrying ball7 and robot2 is carrying ball4 with lgripper2?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is situated in room2, robot1's left gripper is free, robot1's right gripper is free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "14371783-1819-4e61-9c28-89140d98a18f", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: ball7 is picked from room4 with lgripper1 by robot1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: robot1 is carrying ball7 with lgripper1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is at room1, ball4 is at room2, ball5 is located at room3, ball6 is located at room1, ball7 is at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are executed: robot1 uses lgripper1 to pick up ball7 from room4, resulting in the current state. In this state, are the following properties that do not involve negations True or False: is robot1 holding ball7 with lgripper1?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is in room1, ball4 is in room2, ball5 is situated in room3, ball6 is situated in room1, ball7 is in room4, the left gripper of robot1 is unoccupied, robot1 is positioned in room4, and robot1's right gripper is available."}
{"question_id": "992e6fc1-c8ae-4135-80fd-8733adaa9e15", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: lgripper1 of robot1 picks up ball7 in room4, robot1 moves to room5 from room4, in room5, robot1's lgripper1 drops ball7, robot1 moves from room5 to room1, from room1, robot1's lgripper1 picks up ball1, from room1, robot1's rgripper1 picks up ball3, robot1 moves from room1 to room5, ball1 is dropped in room5 with lgripper1 by robot1, in room5, robot1's rgripper1 drops ball3, robot1 moves from room5 to room2, from room2, robot1's lgripper1 picks up ball2, from room2, robot1's rgripper1 picks up ball4, robot1 moves from room2 to room1, in room1, robot1's rgripper1 drops ball4, from room1, robot1's rgripper1 picks up ball6, robot1 moves to room3 from room1, ball6 is dropped in room3 with rgripper1 by robot1, from room3, robot1's rgripper1 picks up ball5 and from room3, robot1 moves to room6 to reach the current state. In this state, is it True or False that robot1 is carrying ball5 with lgripper1?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is at room1, ball4 is at room2, ball5 is located at room3, ball6 is located at room1, ball7 is at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: robot1's left gripper (lgripper1) picks up ball7 in room4, then robot1 moves from room4 to room5, where it drops ball7 using lgripper1. Next, robot1 moves from room5 to room1, picks up ball1 with lgripper1, and also picks up ball3 with its right gripper (rgripper1) in room1. Robot1 then moves from room1 to room5, drops ball1 using lgripper1, and also drops ball3 using rgripper1 in room5. After that, robot1 moves from room5 to room2, picks up ball2 with lgripper1, and picks up ball4 with rgripper1 in room2. Robot1 then moves from room2 to room1, drops ball4 using rgripper1, picks up ball6 with rgripper1, and moves from room1 to room3. In room3, robot1 drops ball6 using rgripper1, picks up ball5 with rgripper1, and finally moves from room3 to room6 to reach the current state. In this state, is it True or False that robot1 is carrying ball5 with lgripper1?", "initial_state_nl_paraphrased": "The location of the balls is as follows: ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room3, ball6 is in room1, and ball7 is in room4. Additionally, the status of robot1 is as follows: its left gripper (lgripper1) is free, it is currently in room4, and its right gripper (rgripper1) is available."}
{"question_id": "ff279278-625c-4af9-939f-9cb3ad25dd31", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_11", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, is it True or False that ball1 is not present at room3?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: robot1 moves from room4 to room1 to achieve the current state. In this state, is it True or False that ball1 is absent from room3?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is situated in room3, robot1 is situated in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "b47a5c79-06df-46e3-a599-857676a0aaef", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_9", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1's rgripper1 picks up ball3, robot1 moves to room5 from room1, rgripper1 of robot1 drops ball3 in room5, from room5, robot1 moves to room2, from room2, robot1's rgripper1 picks up ball4, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1 and from room1, robot1's rgripper1 picks up ball6 to reach the current state. In this state, is it True or False that ball4 is present at room1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: robot1 relocates from room4 to room1, then uses lgripper1 to pick up ball1 in room1, followed by using rgripper1 to pick up ball3 in the same room. Next, robot1 moves to room5 from room1, where it drops ball3 using rgripper1. From room5, robot1 proceeds to room2, picks up ball4 with rgripper1, and then moves back to room1, where it drops ball4 using rgripper1. Finally, robot1 uses rgripper1 to pick up ball6 in room1, resulting in the current state. In this state, is it True or False that ball4 is present at room1?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is situated in room3, robot1 is situated in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "9ca19a46-edbc-4695-88fe-a64a27df4e03", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_5", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1, ball1 is picked from room1 with lgripper1 by robot1, robot1 moves to room2 from room1, ball2 is picked from room2 with rgripper1 by robot1, from room2, robot1 moves to room3, ball1 is dropped in room3 with lgripper1 by robot1, from room3, robot1 moves to room4, in room4, robot1's rgripper1 drops ball2, from room4, robot1 moves to room2, ball4 is picked from room2 with lgripper1 by robot1, from room2, robot1's rgripper1 picks up ball5, from room2, robot1 moves to room5, ball4 is dropped in room5 with lgripper1 by robot1, robot1 moves from room5 to room1, lgripper1 of robot1 picks up ball3 in room1, rgripper1 of robot1 drops ball5 in room1, rgripper1 of robot1 picks up ball6 in room1, robot1 moves from room1 to room5 and ball3 is dropped in room5 with lgripper1 by robot1 to reach the current state. In this state, is it True or False that lgripper1 of robot1 is free?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is at room2, ball3 is present at room1, ball4 is at room2, ball5 is located at room2, ball6 is at room1, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: robot1 relocates from room4 to room1, robot1 uses lgripper1 to pick up ball1 in room1, then robot1 proceeds to room2 from room1, where robot1 uses rgripper1 to pick up ball2, robot1 then moves from room2 to room3, drops ball1 in room3 using lgripper1, and then moves from room3 to room4, where robot1's rgripper1 releases ball2, after which robot1 moves from room4 to room2, picks up ball4 with lgripper1 in room2, and also uses rgripper1 to pick up ball5 in room2, then moves to room5, drops ball4 in room5 using lgripper1, and then returns to room1, where lgripper1 of robot1 picks up ball3, rgripper1 of robot1 drops ball5, and then picks up ball6, after which robot1 moves from room1 to room5 and drops ball3 in room5 using lgripper1 to reach the current state. In this state, is it True or False that the lgripper1 of robot1 is free?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is in room2, ball3 is also in room1, ball4 is in room2, ball5 is situated in room2, ball6 is in room1, robot1 is situated in room4, and both the left gripper (lgripper1) and the right gripper (rgripper1) of robot1 are available."}
{"question_id": "014bd884-dd52-43a3-ac34-8e08758ac13a", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot1 moves to room1 from room4, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1 moves to room5, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1 and from room1, robot1's rgripper1 picks up ball6 to reach the current state. In this state, is it True or False that robot1 is carrying ball1 with lgripper1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot1 relocates from room4 to room1, then uses lgripper1 to pick up ball1 in room1, subsequently, robot1's rgripper1 picks up ball3 in room1, robot1 proceeds to room5, where rgripper1 drops ball3, then robot1 moves to room2, picks up ball4 with rgripper1, returns to room1, drops ball4 with rgripper1, and finally, robot1's rgripper1 picks up ball6 to attain the current state. In this state, is it True or False that robot1 is holding ball1 with lgripper1?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is situated in room3, robot1 is situated in room4, robot1's left gripper is available and robot1's right gripper is available."}
{"question_id": "eea1c4c6-3f10-465d-9324-f91dc5c140b6", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_16", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves to room3 from room2 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: ball1 is not at room1, ball1 is not at room3, ball1 is not located at room2, ball2 is not at room2, ball2 is not at room3, ball2 is not located at room1, ball3 is not at room3, ball3 is not present at room1, ball3 is not present at room2, ball4 is not located at room2, ball4 is not located at room3, ball4 is not present at room1, ball5 is not at room2, ball5 is not present at room1, ball5 is not present at room3, ball6 is not at room2, ball6 is not located at room1, ball6 is not located at room3, ball7 is not at room2, ball7 is not located at room3, ball7 is not present at room1, robot1 is not located at room1, robot1 is not located at room2, robot1 is not present in room3, robot2 is not at room1, robot2 is not located at room3 and robot2 is not present in room2?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3 to reach the current state. In this state, are the following properties that involve negations True or False: ball1 is not in room1, ball1 is not in room3, ball1 is not in room2, ball2 is not in room2, ball2 is not in room3, ball2 is not in room1, ball3 is not in room3, ball3 is not in room1, ball3 is not in room2, ball4 is not in room2, ball4 is not in room3, ball4 is not in room1, ball5 is not in room2, ball5 is not in room1, ball5 is not in room3, ball6 is not in room2, ball6 is not in room1, ball6 is not in room3, ball7 is not in room2, ball7 is not in room3, ball7 is not in room1, robot1 is not in room1, robot1 is not in room2, robot1 is not in room3, robot2 is not in room1, robot2 is not in room3 and robot2 is not in room2?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated at room2, ball4 is positioned at room3, ball5 is in room1, ball6 is also in room1, ball7 is situated at room3, the left gripper of robot2 is available, robot1 is positioned in room2, robot1's left gripper is free, robot1's right gripper is also free, robot2 is situated in room2 and robot2's right gripper is available."}
{"question_id": "7cab6c42-6610-458c-b525-570617f0666e", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_5", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball1, from room3, robot2's rgripper2 picks up ball2, robot2 moves to room2 from room3, ball1 is dropped in room2 with lgripper2 by robot2, rgripper2 of robot2 drops ball2 in room2, robot2 moves to room3 from room2, ball4 is picked from room3 with lgripper2 by robot2, from room3, robot2's rgripper2 picks up ball7 and robot2 moves from room3 to room2 to reach the current state. In this state, is it True or False that robot1's lgripper1 is available?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: robot2 moves from room2 to room3, then from room3, robot2's left gripper (lgripper2) grasps ball1, and its right gripper (rgripper2) picks up ball2. Next, robot2 returns to room2 from room3, and in room2, it releases ball1 using lgripper2 and drops ball2 using rgripper2. Then, robot2 moves back to room3 from room2, where it picks up ball4 with lgripper2 and ball7 with rgripper2, before finally moving from room3 to room2 to reach the current state. In this state, is it True or False that robot1's left gripper (lgripper1) is available?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated at room2, ball4 is situated in room3, ball5 is in room1, ball6 is also in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is situated in room2, robot1's left gripper is free, robot1's right gripper is free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "9e1005c0-ba40-4a41-9ae8-53503fae076c", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_3", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, ball1 is picked from room3 with lgripper2 by robot2, rgripper2 of robot2 picks up ball2 in room3, robot2 moves from room3 to room2, in room2, robot2's lgripper2 drops ball1, rgripper2 of robot2 drops ball2 in room2, robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball4, ball7 is picked from room3 with rgripper2 by robot2, from room3, robot2 moves to room2, in room2, robot2's rgripper2 drops ball7, rgripper2 of robot2 picks up ball3 in room2, robot2 moves to room1 from room2, lgripper2 of robot2 drops ball4 in room1, ball5 is picked from room1 with lgripper2 by robot2, ball3 is dropped in room1 with rgripper2 by robot2, from room1, robot2's rgripper2 picks up ball6, robot2 moves to room2 from room1 and ball5 is dropped in room2 with lgripper2 by robot2 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: ball1 is not being carried by robot1's rgripper1, ball2 is not being carried by robot1's lgripper1, ball2 is not being carried by robot1's lgripper2, ball2 is not being carried by robot2's lgripper2, ball3 is not being carried by robot1's rgripper1, ball3 is not being carried by robot2's rgripper2, ball4 is not being carried by robot1's rgripper2, ball4 is not being carried by robot2's rgripper1, ball4 is not being carried by robot2's rgripper2, ball5 is not being carried by robot2's lgripper2, ball5 is not being carried by robot2's rgripper2, ball6 is not being carried by robot1's lgripper1, ball6 is not being carried by robot2's lgripper2, ball6 is not being carried by robot2's rgripper1, ball7 is not being carried by robot1's lgripper1, ball7 is not being carried by robot1's rgripper1, ball7 is not being carried by robot2's lgripper1, ball7 is not being carried by robot2's rgripper1, lgripper1 of robot1 is not carrying ball4, lgripper1 of robot1 is not carrying ball5, lgripper1 of robot2 is not carrying ball1, lgripper1 of robot2 is not carrying ball4, lgripper2 of robot1 is not carrying ball3, lgripper2 of robot1 is not carrying ball7, lgripper2 of robot2 is not carrying ball3, rgripper1 of robot1 is not carrying ball5, rgripper1 of robot2 is not carrying ball2, rgripper1 of robot2 is not carrying ball3, rgripper2 of robot1 is not carrying ball2, rgripper2 of robot1 is not carrying ball3, rgripper2 of robot1 is not carrying ball6, rgripper2 of robot2 is not carrying ball7, robot1 is not carrying ball1 with lgripper1, robot1 is not carrying ball1 with lgripper2, robot1 is not carrying ball1 with rgripper2, robot1 is not carrying ball2 with rgripper1, robot1 is not carrying ball3 with lgripper1, robot1 is not carrying ball4 with lgripper2, robot1 is not carrying ball4 with rgripper1, robot1 is not carrying ball5 with lgripper2, robot1 is not carrying ball5 with rgripper2, robot1 is not carrying ball6 with lgripper2, robot1 is not carrying ball6 with rgripper1, robot1 is not carrying ball7 with rgripper2, robot2 is not carrying ball1 with lgripper2, robot2 is not carrying ball1 with rgripper1, robot2 is not carrying ball1 with rgripper2, robot2 is not carrying ball2 with lgripper1, robot2 is not carrying ball2 with rgripper2, robot2 is not carrying ball3 with lgripper1, robot2 is not carrying ball4 with lgripper2, robot2 is not carrying ball5 with lgripper1, robot2 is not carrying ball5 with rgripper1, robot2 is not carrying ball6 with lgripper1 and robot2 is not carrying ball7 with lgripper2?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is present at room3, ball3 is located at room2, ball4 is located at room3, ball5 is at room1, ball6 is located at room1, ball7 is present at room3, lgripper2 of robot2 is free, robot1 is at room2, robot1's lgripper1 is available, robot1's rgripper1 is available, robot2 is present in room2 and robot2's rgripper2 is free.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot2 moves from room2 to room3, robot2 uses lgripper2 to pick up ball1 in room3, robot2's rgripper2 picks up ball2 in room3, robot2 moves from room3 to room2, robot2 drops ball1 in room2 using lgripper2, robot2 drops ball2 in room2 using rgripper2, robot2 moves from room2 to room3, robot2 picks up ball4 in room3 using lgripper2, robot2 picks up ball7 in room3 using rgripper2, robot2 moves from room3 to room2, robot2 drops ball7 in room2 using rgripper2, robot2 picks up ball3 in room2 using rgripper2, robot2 moves from room2 to room1, robot2 drops ball4 in room1 using lgripper2, robot2 picks up ball5 in room1 using lgripper2, robot2 drops ball3 in room1 using rgripper2, robot2 picks up ball6 in room1 using rgripper2, robot2 moves from room1 to room2, and robot2 drops ball5 in room2 using lgripper2 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: ball1 is not held by robot1's rgripper1, ball2 is not held by robot1's lgripper1, ball2 is not held by robot1's lgripper2, ball2 is not held by robot2's lgripper2, ball3 is not held by robot1's rgripper1, ball3 is not held by robot2's rgripper2, ball4 is not held by robot1's rgripper2, ball4 is not held by robot2's rgripper1, ball4 is not held by robot2's rgripper2, ball5 is not held by robot2's lgripper2, ball5 is not held by robot2's rgripper2, ball6 is not held by robot1's lgripper1, ball6 is not held by robot2's lgripper2, ball6 is not held by robot2's rgripper1, ball7 is not held by robot1's lgripper1, ball7 is not held by robot1's rgripper1, ball7 is not held by robot2's lgripper1, ball7 is not held by robot2's rgripper1, robot1's lgripper1 is not holding ball4, robot1's lgripper1 is not holding ball5, robot2's lgripper1 is not holding ball1, robot2's lgripper1 is not holding ball4, robot1's lgripper2 is not holding ball3, robot1's lgripper2 is not holding ball7, robot2's lgripper2 is not holding ball3, robot1's rgripper1 is not holding ball5, robot2's rgripper1 is not holding ball2, robot2's rgripper1 is not holding ball3, robot1's rgripper2 is not holding ball2, robot1's rgripper2 is not holding ball3, robot1's rgripper2 is not holding ball6, robot2's rgripper2 is not holding ball7, robot1 is not holding ball1 with lgripper1, robot1 is not holding ball1 with lgripper2, robot1 is not holding ball1 with rgripper2, robot1 is not holding ball2 with rgripper1, robot1 is not holding ball3 with lgripper1, robot1 is not holding ball4 with lgripper2, robot1 is not holding ball4 with rgripper1, robot1 is not holding ball5 with lgripper2, robot1 is not holding ball5 with rgripper2, robot1 is not holding ball6 with lgripper2, robot1 is not holding ball6 with rgripper1, robot1 is not holding ball7 with rgripper2, robot2 is not holding ball1 with lgripper2, robot2 is not holding ball1 with rgripper1, robot2 is not holding ball1 with rgripper2, robot2 is not holding ball2 with lgripper1, robot2 is not holding ball2 with rgripper2, robot2 is not holding ball3 with lgripper1, robot2 is not holding ball4 with lgripper2, robot2 is not holding ball5 with lgripper1, robot2 is not holding ball5 with rgripper1, robot2 is not holding ball6 with lgripper1, and robot2 is not holding ball7 with lgripper2?", "initial_state_nl_paraphrased": "Ball1 is located in room3, ball2 is also in room3, ball3 is situated in room2, ball4 is in room3, ball5 is situated in room1, ball6 is in room1, ball7 is in room3, the left gripper of robot2 is available, robot1 is situated in room2, robot1's left gripper is free, robot1's right gripper is free, robot2 is in room2 and robot2's right gripper is available."}
{"question_id": "a9c6b6d4-857d-476f-9903-72ee1d32213f", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_5", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from room4, robot1 moves to room1 to reach the current state. In this state, is it True or False that lgripper1 of robot1 is free?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is located at room2, ball3 is present at room1, ball4 is located at room2, ball5 is present at room2, ball6 is at room1, ball7 is located at room3, robot1 is located at room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: robot1 moves from room4 to room1 to reach the current state. In this state, is it True or False that the lgripper1 of robot1 is free?", "initial_state_nl_paraphrased": "Ball1 is in room1, ball2 is situated in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is situated in room3, robot1 is situated in room4, robot1's left gripper is available and robot1's right gripper is available."}
