{"question_id": "bdf504d6-e02e-4d37-90a1-bfe30f7ec8e3", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: robot1 moves from room4 to room1, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1's rgripper1 picks up ball3, robot1 moves from room1 to room5, in room5, robot1's rgripper1 drops ball3, robot1 moves from room5 to room2, from room2, robot1's rgripper1 picks up ball4, from room2, robot1 moves to room1, lgripper1 of robot1 drops ball4 in room3 and rgripper1 of robot1 picks up ball6 in room1. Is the action: ball4 is dropped in room3 with lgripper1 by robot1 executable at step 9, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is at room1, ball7 is present at room3, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: robot1 relocates from room4 to room1, robot1's left gripper (lgripper1) grasps ball1 in room1, robot1's right gripper (rgripper1) then picks up ball3 in room1, robot1 proceeds from room1 to room5, in room5, robot1's right gripper (rgripper1) releases ball3, robot1 moves from room5 to room2, from room2, robot1's right gripper (rgripper1) picks up ball4, robot1 then moves from room2 to room1, and in room1, robot1's left gripper (lgripper1) releases ball4 in room3 while robot1's right gripper (rgripper1) picks up ball6. Is the action: ball4 is dropped in room3 using lgripper1 by robot1 executable at step 9, True or False?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 is in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is in room3, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "277b0a20-89cf-4f5a-99b5-37c36724e3c8", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot1 moves to room1 from room4. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is at room1, ball7 is present at room3, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: robot1 will relocate from room4 to room1. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 is in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is in room3, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "550ac4dc-e743-4fc3-b0c4-a156bb5a7405", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball7 is picked from room4 with lgripper1 by robot1. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is located at room1, ball4 is at room2, ball5 is located at room3, ball6 is at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: robot1 will use lgripper1 to pick up ball7 from room4. Is the execution of this action feasible, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room3, ball6 is situated in room1, ball7 is found in room4, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and the right gripper of robot1 is available."}
{"question_id": "e8448e35-ef35-4b58-9ad1-8ff1f776fb02", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball3 is picked from room1 with lgripper1 by robot1, ball5 is picked from room2 with rgripper1 by robot1, ball6 is picked from room1 with rgripper1 by robot1, from room1, robot1's lgripper1 picks up ball1, from room2, robot1 moves to room3, from room2, robot1's rgripper1 picks up ball2, from room5, robot1 moves to room1, in room1, robot1's rgripper1 drops ball5, in room3, robot1's lgripper1 drops ball1, in room4, robot1's rgripper1 drops ball2, in room5, robot1's lgripper1 drops ball3, in room5, robot1's lgripper1 drops ball4, lgripper1 of robot1 picks up ball4 in room2, robot1 moves from room1 to room2, robot1 moves from room1 to room5, robot1 moves from room3 to room4, robot1 moves from room4 to room1, robot1 moves from room4 to room2 and robot1 moves to room5 from room2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is located at room2, ball3 is located at room1, ball4 is at room2, ball5 is at room2, ball6 is present at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: robot1, using lgripper1, will pick up ball3 from room1, and using rgripper1, will pick up ball5 from room2 and ball6 from room1. Additionally, robot1 will use lgripper1 to pick up ball1 from room1, then move from room2 to room3. From room2, robot1's rgripper1 will pick up ball2, and then robot1 will move from room5 to room1. The following drop actions will be performed: in room1, robot1's rgripper1 will drop ball5, in room3, robot1's lgripper1 will drop ball1, in room4, robot1's rgripper1 will drop ball2, and in room5, robot1's lgripper1 will drop ball3 and ball4. Furthermore, robot1's lgripper1 will pick up ball4 in room2. Robot1 will also move between rooms in the following sequence: from room1 to room2, from room1 to room5, from room3 to room4, from room4 to room1, from room4 to room2, and from room2 to room5. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 resides in room2, ball3 is also in room1, ball4 is positioned in room2, ball5 is located in room2, ball6 is found in room1, robot1 is situated in room4, robot1's left gripper (lgripper1) is available, and robot1's right gripper (rgripper1) is unoccupied."}
{"question_id": "23a368a9-2c7b-444d-8ca6-50ed68261ddb", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: robot1 moves to room1 from room4, from room1, robot1's lgripper1 picks up ball1, robot1 moves from room1 to room2, from room2, robot1's rgripper1 picks up ball2, lgripper1 of robot1 picks up ball1 in room5, ball1 is dropped in room3 with lgripper1 by robot1, robot1 moves to room4 from room3, ball2 is dropped in room4 with rgripper1 by robot1, robot1 moves to room2 from room4, ball4 is picked from room2 with lgripper1 by robot1, rgripper1 of robot1 picks up ball5 in room2, robot1 moves to room5 from room2, ball4 is dropped in room5 with lgripper1 by robot1, robot1 moves to room1 from room5, ball3 is picked from room1 with lgripper1 by robot1, rgripper1 of robot1 drops ball5 in room1, from room1, robot1's rgripper1 picks up ball6, from room1, robot1 moves to room5 and in room5, robot1's lgripper1 drops ball3. Is the action: lgripper1 of robot1 picks up ball1 in room5 executable at step 5, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is located at room2, ball3 is located at room1, ball4 is at room2, ball5 is at room2, ball6 is present at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 19: robot1 will move from room4 to room1, then robot1's left gripper (lgripper1) will pick up ball1 in room1, followed by robot1 moving from room1 to room2, where robot1's right gripper (rgripper1) will pick up ball2, then robot1's lgripper1 will pick up ball1 in room5, robot1 will drop ball1 in room3 using lgripper1, move from room3 to room4, drop ball2 in room4 using rgripper1, move from room4 to room2, pick up ball4 in room2 using lgripper1, robot1's rgripper1 will pick up ball5 in room2, move from room2 to room5, drop ball4 in room5 using lgripper1, move from room5 to room1, pick up ball3 in room1 using lgripper1, drop ball5 in room1 using rgripper1, pick up ball6 in room1 using rgripper1, move from room1 to room5, and finally drop ball3 in room5 using lgripper1. Is the action: robot1's lgripper1 picks up ball1 in room5 executable at step 5, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 resides in room2, ball3 is also in room1, ball4 is positioned in room2, ball5 is located in room2, ball6 is found in room1, robot1 is situated in room4, robot1's left gripper, lgripper1, is available, and robot1's right gripper, rgripper1, is unoccupied."}
{"question_id": "d65881e0-a691-4132-a043-6f0d62bce0da", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball2 is picked from room2 with lgripper1 by robot1, ball6 is dropped in room3 with rgripper1 by robot1, ball6 is picked from room1 with rgripper1 by robot1, from room2, robot1's rgripper1 picks up ball4, from room4, robot1 moves to room1, in room5, robot1's rgripper1 drops ball3, lgripper1 of robot1 drops ball1 in room4, lgripper1 of robot1 picks up ball1 in room1, rgripper1 of robot1 drops ball4 in room1, rgripper1 of robot1 drops ball7 in room4, rgripper1 of robot1 picks up ball3 in room1, rgripper1 of robot1 picks up ball3 in room3, rgripper1 of robot1 picks up ball5 in room2, rgripper1 of robot1 picks up ball7 in room3, robot1 moves from room1 to room3, robot1 moves to room1 from room2, robot1 moves to room2 from room4, robot1 moves to room4 from room3 and robot1 moves to room5 from room1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is at room1, ball7 is present at room3, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to be executed: robot1, using lgripper1, will pick up ball2 from room2, and then robot1 will drop ball6 in room3 using rgripper1. Next, robot1 will pick up ball6 from room1 with rgripper1. From room2, robot1's rgripper1 will pick up ball4. Then, robot1 will move from room4 to room1. In room5, robot1's rgripper1 will drop ball3. Additionally, robot1's lgripper1 will drop ball1 in room4 and pick up ball1 in room1. Furthermore, robot1's rgripper1 will drop ball4 in room1 and ball7 in room4, and pick up ball3 in room1 and room3, as well as ball5 in room2 and ball7 in room3. Robot1 will also move from room1 to room3, from room2 to room1, from room4 to room2, from room3 to room4, and from room1 to room5. Is the execution of these actions possible, True or False?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 is in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is in room3, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "a72a0ead-929f-4f89-81c3-69e7b7fda81b", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room4, from room4, robot1 moves to room5, ball7 is dropped in room5 with lgripper1 by robot1, robot1 moves from room5 to room1, lgripper1 of robot1 picks up ball1 in room1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves to room5 from room1, ball1 is dropped in room5 with lgripper1 by robot1, ball3 is dropped in room5 with rgripper1 by robot1, robot1 moves to room2 from room5, lgripper1 of robot1 picks up ball2 in room2, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves to room1 from room2, rgripper1 of robot1 drops ball4 in room1, rgripper1 of robot1 picks up ball6 in room1, from room1, robot1 moves to room3, in room3, robot1's rgripper1 drops ball6, ball5 is picked from room3 with rgripper1 by robot1 and from room3, robot1 moves to room6. Is the action: ball3 is dropped in room5 with rgripper1 by robot1 executable at step 9, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is located at room1, ball4 is at room2, ball5 is located at room3, ball6 is at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: robot1's lgripper1 is to pick up ball7 in room4, then robot1 will move from room4 to room5, and ball7 will be dropped in room5 using lgripper1 by robot1. Next, robot1 will move from room5 to room1, where lgripper1 of robot1 will pick up ball1 and rgripper1 of robot1 will pick up ball3. Then, robot1 will move from room1 to room5, and both ball1 and ball3 will be dropped in room5 using lgripper1 and rgripper1, respectively, by robot1. After that, robot1 will move from room5 to room2, where lgripper1 of robot1 will pick up ball2 and rgripper1 of robot1 will pick up ball4. Subsequently, robot1 will move from room2 to room1, where rgripper1 of robot1 will drop ball4 and then pick up ball6. Then, robot1 will move from room1 to room3, where rgripper1 of robot1 will drop ball6 and pick up ball5, and finally, robot1 will move from room3 to room6. Is the action: ball3 is dropped in room5 with rgripper1 by robot1 executable at step 9, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room3, ball6 is situated in room1, ball7 is found in room4, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and the right gripper of robot1 is available."}
{"question_id": "d65d2d1b-0fc6-45fd-a55f-8976db7b18e7", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: robot1 moves to room1 from room4. Is the action: from room4, robot1 moves to room1 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is located at room2, ball3 is located at room1, ball4 is at room2, ball5 is at room2, ball6 is present at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are scheduled for steps 1 through 1: robot1 is set to move from room4 to room1. Is the action of moving robot1 from room4 to room1 feasible at step 1, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 resides in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is located in room2, ball6 is found in room1, robot1 is positioned in room4, robot1's left gripper, lgripper1, is available, and robot1's right gripper, rgripper1, is unoccupied."}
{"question_id": "538e9625-5b25-4b3d-bb46-8edc2399fedc", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: from room4, robot1 moves to room1. Is the action: from room4, robot1 moves to room1 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is at room1, ball7 is present at room3, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are scheduled for steps 1 through 1: robot1 is set to move from room4 to room1. Is the action of moving robot1 from room4 to room1 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 is in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is in room3, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "ecf359a7-ffda-400d-a2a2-9ebfd0aa40b4", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: rgripper1 of robot1 picks up ball4 in room1. Is the action: rgripper1 of robot1 picks up ball4 in room1 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is at room1, ball7 is present at room3, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: the rgripper1 of robot1 is to pick up ball4 in room1. Is the action of rgripper1 of robot1 picking up ball4 in room1 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 is in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is in room3, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "d1072737-7139-40f7-93da-540d90310874", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball2 in room3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is located at room1, ball4 is at room2, ball5 is located at room3, ball6 is at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: robot1's lgripper1 is to grasp ball2 in room3. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is situated in room1, ball4 is in room2, ball5 is situated in room3, ball6 is in room1, ball7 is found in room4, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and robot1's right gripper is available."}
{"question_id": "a0d4eadd-2e84-4efb-b1d9-5a044073f086", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball1 is dropped in room2 with lgripper2 by robot2, ball1 is picked from room3 with lgripper2 by robot2, ball3 is dropped in room1 with rgripper2 by robot2, ball4 is dropped in room1 with lgripper2 by robot2, ball5 is picked from room1 with lgripper2 by robot2, ball6 is picked from room1 with rgripper2 by robot2, from room2, robot2 moves to room3, from room2, robot2's rgripper2 picks up ball3, from room3, robot2 moves to room2, from room3, robot2 moves to room2, in room2, robot2's rgripper2 drops ball7, lgripper2 of robot2 drops ball5 in room2, lgripper2 of robot2 picks up ball4 in room3, rgripper2 of robot2 drops ball2 in room2, rgripper2 of robot2 picks up ball2 in room3, rgripper2 of robot2 picks up ball7 in room3, robot2 moves from room2 to room3, robot2 moves to room1 from room2 and robot2 moves to room2 from room1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is located at room3, ball3 is located at room2, ball4 is located at room3, ball5 is present at room1, ball6 is present at room1, ball7 is located at room3, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: robot2 will drop ball1 in room2 using lgripper2, then pick up ball1 from room3 using lgripper2, drop ball3 in room1 using rgripper2, drop ball4 in room1 using lgripper2, pick up ball5 from room1 using lgripper2, and pick up ball6 from room1 using rgripper2. Additionally, robot2 will move from room2 to room3, pick up ball3 from room2 using rgripper2, move from room3 to room2 twice, drop ball7 in room2 using rgripper2, drop ball5 in room2 using lgripper2, pick up ball4 in room3 using lgripper2, drop ball2 in room2 using rgripper2, pick up ball2 in room3 using rgripper2, pick up ball7 in room3 using rgripper2, move from room2 to room3, move from room2 to room1, and finally move from room1 to room2. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Ball1 is at room3, ball2 is also at room3, ball3 is situated in room2, ball4 is at room3, ball5 is in room1, ball6 is also in room1, ball7 is at room3, robot1 is in room2, both of robot1's grippers (lgripper1 and rgripper1) are available, robot2 is in room2, and both of robot2's grippers (lgripper2 and rgripper2) are available."}
{"question_id": "acf64dd1-3528-4da5-a062-f03e95a78958", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball3 is dropped in room5 with rgripper1 by robot1, ball5 is picked from room4 with rgripper1 by robot1, ball7 is picked from room4 with lgripper1 by robot1, from room1, robot1's rgripper1 picks up ball3, from room5, robot1 moves to room1, from room5, robot1 moves to room2, lgripper1 of robot1 drops ball1 in room5, lgripper1 of robot1 picks up ball1 in room1, robot1 moves to room5 from room1 and robot1 moves to room5 from room4. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is located at room1, ball4 is at room2, ball5 is located at room3, ball6 is at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: robot1 uses rgripper1 to drop ball3 in room5 and to pick up ball5 from room4, while also using lgripper1 to pick up ball7 from room4, then robot1's rgripper1 retrieves ball3 from room1, robot1 moves from room5 to room1 and from room5 to room2, robot1's lgripper1 drops ball1 in room5 and picks up ball1 in room1, and robot1 moves from room1 to room5 and from room4 to room5. Is the execution of these actions possible, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room3, ball6 is situated in room1, ball7 is found in room4, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and robot1's right gripper is available."}
{"question_id": "3f8fa5d6-6a98-4ca5-820e-7db7cfcc814f", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room2, robot2 moves to room3. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is present at room3, ball2 is located at room3, ball3 is located at room2, ball4 is located at room3, ball5 is present at room1, ball6 is present at room1, ball7 is located at room3, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: robot2 will move from room2 to room3. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "Ball1 is at room3, ball2 is also at room3, ball3 is situated in room2, ball4 is at room3, ball5 is in room1, ball6 is also in room1, ball7 is at room3, robot1 is in room2, robot1 has an available lgripper1, robot1 has an available rgripper1, robot2 is in room2, robot2 has an available lgripper2 and robot2 has an available rgripper2."}
{"question_id": "96916583-8c8d-4c1a-b33d-8d5402635c1d", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: robot1 moves from room4 to room1, lgripper1 of robot1 picks up ball1 in room1, rgripper1 of robot1 picks up ball3 in room1, robot1 moves to room5 from room1, ball3 is dropped in room5 with rgripper1 by robot1, from room5, robot1 moves to room2, from room2, robot1's rgripper1 picks up ball4, robot1 moves from room2 to room1, ball4 is dropped in room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball6, from room1, robot1 moves to room3, ball6 is dropped in room3 with rgripper1 by robot1, ball7 is picked from room3 with rgripper1 by robot1, robot1 moves from room3 to room4, in room4, robot1's lgripper1 drops ball1, in room4, robot1's rgripper1 drops ball7, robot1 moves from room4 to room2, from room2, robot1's lgripper1 picks up ball2 and ball5 is picked from room2 with rgripper1 by robot1. Is the action: ball4 is dropped in room1 with rgripper1 by robot1 executable at step 9, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is at room1, ball7 is present at room3, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 19: robot1 will move from room4 to room1, then use its left gripper (lgripper1) to pick up ball1 in room1, and its right gripper (rgripper1) to pick up ball3 in room1. Next, robot1 will move to room5 from room1, drop ball3 in room5 using rgripper1, and then proceed to room2. In room2, robot1's rgripper1 will pick up ball4, and then robot1 will move back to room1, where it will drop ball4 using rgripper1. After that, robot1's rgripper1 will pick up ball6 in room1, and then robot1 will move to room3, where it will drop ball6 using rgripper1. In room3, robot1's rgripper1 will pick up ball7, and then robot1 will move to room4. In room4, robot1 will drop ball1 using lgripper1 and ball7 using rgripper1. Finally, robot1 will move to room2, where it will pick up ball2 using lgripper1 and ball5 using rgripper1. Is the action of dropping ball4 in room1 with rgripper1 by robot1 executable at step 9, True or False?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 is in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is in room3, robot1 is positioned in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "22e264cd-7de1-40fc-aaa3-1935ca4b148a", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1 moves to room2, from room2, robot1's lgripper1 picks up ball4, from room2, robot1's rgripper1 picks up ball2, in room3, robot1's lgripper1 drops ball1, rgripper1 of robot1 drops ball2 in room4, robot1 moves from room4 to room2, robot1 moves to room1 from room4, robot1 moves to room3 from room2 and robot1 moves to room4 from room3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is located at room2, ball3 is located at room1, ball4 is at room2, ball5 is at room2, ball6 is present at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: robot1, using lgripper1, will pick up ball1 from room1, then proceed from room1 to room2, where it will use lgripper1 to pick up ball4 and rgripper1 to pick up ball2, subsequently, in room3, robot1's lgripper1 will release ball1, and in room4, robot1's rgripper1 will release ball2, then robot1 will move from room4 to room2, then from room4 to room1, then from room2 to room3, and finally from room3 to room4. Is the execution of this sequence feasible, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 resides in room2, ball3 is also in room1, ball4 is positioned in room2, ball5 is located in room2, ball6 is found in room1, robot1 is situated in room4, robot1's left gripper, lgripper1, is available, and robot1's right gripper, rgripper1, is unoccupied."}
{"question_id": "1e1f665f-e17e-44c7-a205-00b263a7fde7", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: from room2, robot2 moves to room3. Is the action: robot2 moves from room2 to room3 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is present at room3, ball2 is located at room3, ball3 is located at room2, ball4 is located at room3, ball5 is present at room1, ball6 is present at room1, ball7 is located at room3, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: from room2, robot2 moves to room3. Is the action: robot2 moves from room2 to room3 executable at step 1, True or False?\n\nParaphrased text: \nGiven the initial condition, for steps 1 through 1 the following actions are planned to be performed: from room2, robot2 moves to room3. Can the action of robot2 moving from room2 to room3 be executed at step 1, True or False?", "initial_state_nl_paraphrased": "Ball1 is at room3, ball2 is also at room3, ball3 is situated in room2, ball4 is at room3, ball5 is in room1, ball6 is also in room1, ball7 is at room3, robot1 is in room2, both of robot1's grippers (lgripper1 and rgripper1) are available, robot2 is present in room2, and both of robot2's grippers (lgripper2 and rgripper2) are available."}
{"question_id": "edd4e630-c73a-4155-9d6e-d03135a408b0", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: robot1 moves from room4 to room1, lgripper1 of robot1 picks up ball1 in room1, robot1 moves from room1 to room2, rgripper1 of robot1 picks up ball2 in room2, from room2, robot1 moves to room3, in room3, robot1's lgripper1 drops ball1, robot1 moves from room3 to room4, rgripper1 of robot1 drops ball2 in room4, from room4, robot1 moves to room2 and lgripper1 of robot1 picks up ball4 in room2. Is the action: from room1, robot1's lgripper1 picks up ball1 executable at step 2, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is located at room2, ball3 is located at room1, ball4 is at room2, ball5 is at room2, ball6 is present at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 10: robot1 relocates from room4 to room1, then the left gripper of robot1 (lgripper1) grasps ball1 in room1, followed by robot1 moving from room1 to room2, where the right gripper of robot1 (rgripper1) picks up ball2, then robot1 proceeds from room2 to room3, and in room3, robot1's lgripper1 releases ball1, after which robot1 moves from room3 to room4, where rgripper1 of robot1 drops ball2, subsequently, robot1 moves from room4 back to room2, and finally, lgripper1 of robot1 picks up ball4 in room2. Is the action: from room1, robot1's lgripper1 picks up ball1 executable at step 2, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 resides in room2, ball3 is also in room1, ball4 is positioned in room2, ball5 is located in room2, ball6 is found in room1, robot1 is situated in room4, robot1's left gripper, lgripper1, is available, and robot1's right gripper, rgripper1, is unoccupied."}
{"question_id": "70008070-0e15-4f14-aa06-f1cdf56573b0", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: from room4, robot1 moves to room1, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1 moves to room2, rgripper1 of robot1 picks up ball2 in room2, from room2, robot1 moves to room3, ball1 is dropped in room3 with lgripper1 by robot1, from room3, robot1 moves to room4, in room4, robot1's rgripper1 drops ball2, robot1 moves to room2 from room4, ball4 is picked from room2 with lgripper1 by robot1, rgripper1 of robot1 picks up ball5 in room2, from room2, robot1 moves to room5, lgripper1 of robot1 drops ball4 in room5, robot1 moves from room5 to room1, ball3 is picked from room1 with lgripper1 by robot1, in room1, robot1's rgripper1 drops ball5, ball6 is picked from room1 with rgripper1 by robot1, robot1 moves from room1 to room5 and lgripper1 of robot1 drops ball3 in room5. Is the action: in room4, robot1's rgripper1 drops ball2 executable at step 8, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is located at room2, ball3 is located at room1, ball4 is at room2, ball5 is at room2, ball6 is present at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: robot1 starts in room4 and moves to room1, where it uses its left gripper (lgripper1) to pick up ball1, then proceeds to room2, where its right gripper (rgripper1) picks up ball2, and then moves to room3, where it drops ball1 using lgripper1, after which it returns to room4, drops ball2 with rgripper1, and then heads back to room2 to pick up ball4 with lgripper1 and ball5 with rgripper1, then moves to room5, drops ball4 with lgripper1, and proceeds to room1, where it picks up ball3 with lgripper1, drops ball5 with rgripper1, picks up ball6 with rgripper1, and finally moves to room5 to drop ball3 with lgripper1. Is the action of robot1 dropping ball2 with its rgripper1 in room4 executable at step 8, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 resides in room2, ball3 is also in room1, ball4 is positioned in room2, ball5 is located in room2, ball6 is found in room1, robot1 is situated in room4, robot1's left gripper, lgripper1, is available, and robot1's right gripper, rgripper1, is unoccupied."}
{"question_id": "58f0e7b9-5e23-490e-904c-44ce5b9eec8e", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: robot2 moves from room2 to room3, from room3, robot2's lgripper2 picks up ball1, rgripper2 of robot2 picks up ball2 in room3, robot2 moves from room3 to room2, lgripper2 of robot2 drops ball1 in room2, in room2, robot2's rgripper2 drops ball2, robot2 moves to room3 from room2, ball4 is picked from room3 with lgripper2 by robot2, from room3, robot2's rgripper2 picks up ball7 and from room3, robot2 moves to room2. Is the action: robot2 moves from room3 to room2 executable at step 10, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is located at room3, ball3 is located at room2, ball4 is located at room3, ball5 is present at room1, ball6 is present at room1, ball7 is located at room3, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: robot2 is set to move from room2 to room3, then from room3, robot2's left gripper (lgripper2) will grasp ball1, and its right gripper (rgripper2) will grasp ball2 in room3. Next, robot2 will move from room3 back to room2, where lgripper2 will release ball1, and rgripper2 will release ball2. Then, robot2 will move from room2 to room3, pick up ball4 with lgripper2, and pick up ball7 with rgripper2 in room3, before moving from room3 to room2. Is the action of robot2 moving from room3 to room2 executable at step 10, True or False?", "initial_state_nl_paraphrased": "Ball1 is at room3, ball2 is also at room3, ball3 is situated in room2, ball4 is at room3, ball5 is in room1, ball6 is also in room1, ball7 is at room3, robot1 is in room2, both of robot1's grippers (lgripper1 and rgripper1) are available, robot2 is in room2, and both of robot2's grippers (lgripper2 and rgripper2) are available."}
{"question_id": "f5d8c0f2-9e2d-4708-98df-7b9d69207460", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1's rgripper1 picks up ball2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is located at room2, ball3 is located at room1, ball4 is at room2, ball5 is at room2, ball6 is present at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are scheduled: robot1's rgripper1 in room4 is set to pick up ball2. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is present at room2, ball3 is in room1, ball4 is located at room2, ball5 is present at room2, ball6 is in room1, robot1 is in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is free."}
{"question_id": "50f22b46-bbd6-4f2c-8972-2d779f5d0443", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: lgripper1 of robot1 picks up ball6 in room1. Is the action: lgripper1 of robot1 picks up ball6 in room1 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is present at room3, ball2 is located at room3, ball3 is located at room2, ball4 is located at room3, ball5 is present at room1, ball6 is present at room1, ball7 is located at room3, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: the left gripper of robot1 is to grasp ball6 in room1. Is the action - the left gripper of robot1 grasping ball6 in room1 - executable at step 1, True or False?", "initial_state_nl_paraphrased": "Ball1 is at room3, ball2 is also at room3, ball3 is situated in room2, ball4 is at room3, ball5 is in room1, ball6 is also in room1, ball7 is at room3, robot1 is in room2, both of robot1's grippers (lgripper1 and rgripper1) are available, robot2 is in room2, and both of robot2's grippers (lgripper2 and rgripper2) are available."}
{"question_id": "9b2a2799-34a5-427a-baba-4a769a1bed7a", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room4, robot1 moves to room1. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Ball1 is present at room1, ball2 is located at room2, ball3 is located at room1, ball4 is at room2, ball5 is at room2, ball6 is present at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: robot1 is set to move from room4 to room1. Is the execution of this action feasible, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 resides in room2, ball3 is also in room1, ball4 is situated in room2, ball5 is located in room2, ball6 is found in room1, robot1 is positioned in room4, robot1's left gripper, lgripper1, is available, and robot1's right gripper, rgripper1, is unoccupied."}
{"question_id": "a90fbea0-42d0-4c06-a252-38266838c0ff", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: robot1 moves to room1 from room4, lgripper1 of robot1 picks up ball1 in room1, from room1, robot1's rgripper1 picks up ball3, robot1 moves from room1 to room5, rgripper1 of robot1 drops ball3 in room5, robot1 moves from room5 to room2, from room2, robot1's rgripper1 picks up ball4, robot1 moves to room1 from room2, ball4 is dropped in room1 with rgripper1 by robot1, rgripper1 of robot1 picks up ball6 in room1, robot1 moves to room3 from room1, in room3, robot1's rgripper1 drops ball6, ball7 is dropped in room5 with rgripper1 by robot1, robot1 moves from room3 to room4, lgripper1 of robot1 drops ball1 in room4, rgripper1 of robot1 drops ball7 in room4, robot1 moves to room2 from room4, from room2, robot1's lgripper1 picks up ball2 and from room2, robot1's rgripper1 picks up ball5. Is the action: rgripper1 of robot1 drops ball7 in room5 executable at step 13, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is at room1, ball7 is present at room3, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 19: robot1 will move from room4 to room1, then use its left gripper (lgripper1) to pick up ball1 in room1, followed by using its right gripper (rgripper1) to pick up ball3 in room1, then move to room5, drop ball3 in room5 using rgripper1, move to room2, pick up ball4 in room2 using rgripper1, move back to room1, drop ball4 in room1 using rgripper1, pick up ball6 in room1 using rgripper1, move to room3, drop ball6 in room3 using rgripper1, drop ball7 in room5 using rgripper1, move to room4, drop ball1 in room4 using lgripper1, drop ball7 in room4 using rgripper1, move to room2, and finally pick up ball2 using lgripper1 and ball5 using rgripper1 in room2. Is the action: robot1's rgripper1 drops ball7 in room5 executable at step 13, True or False?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 is in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is in room3, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "99d25a79-4729-4fda-aea5-2e239acde85c", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room1, robot1's lgripper1 picks up ball1, from room1, robot1's lgripper1 picks up ball4, lgripper1 of robot1 drops ball1 in room3, lgripper1 of robot1 picks up ball4 in room2, rgripper1 of robot1 drops ball2 in room4, rgripper1 of robot1 picks up ball2 in room2, robot1 moves from room3 to room4, robot1 moves from room4 to room2, robot1 moves to room2 from room1 and robot1 moves to room3 from room2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is located at room2, ball3 is located at room1, ball4 is at room2, ball5 is at room2, ball6 is present at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled: robot1's lgripper1 in room1 will grasp ball1 and then ball4, then it will release ball1 in room3 and pick up ball4 in room2. Meanwhile, robot1's rgripper1 will drop ball2 in room4 and pick it up in room2. Additionally, robot1 will move from room3 to room4, then to room2, from room1 to room2, and from room2 to room3. Can this sequence be executed, True or False?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is present at room2, ball3 is present in room1, ball4 is located at room2, ball5 is present at room2, ball6 is located in room1, robot1 is located in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is free."}
{"question_id": "2467eda7-8e8f-4215-8a00-9acbbd0e5a3e", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: robot2 moves to room3 from room2, ball1 is picked from room3 with lgripper2 by robot2, ball2 is picked from room3 with rgripper2 by robot2, robot2 moves from room3 to room2, lgripper1 of robot2 drops ball4 in room2, ball2 is dropped in room2 with rgripper2 by robot2, robot2 moves to room3 from room2, lgripper2 of robot2 picks up ball4 in room3, ball7 is picked from room3 with rgripper2 by robot2, from room3, robot2 moves to room2, ball7 is dropped in room2 with rgripper2 by robot2, rgripper2 of robot2 picks up ball3 in room2, from room2, robot2 moves to room1, ball4 is dropped in room1 with lgripper2 by robot2, ball5 is picked from room1 with lgripper2 by robot2, ball3 is dropped in room1 with rgripper2 by robot2, from room1, robot2's rgripper2 picks up ball6, from room1, robot2 moves to room2 and in room2, robot2's lgripper2 drops ball5. Is the action: in room2, robot2's lgripper1 drops ball4 executable at step 5, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is located at room3, ball3 is located at room2, ball4 is located at room3, ball5 is present at room1, ball6 is present at room1, ball7 is located at room3, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 19: robot2 will move from room2 to room3, then pick up ball1 from room3 using lgripper2 and ball2 from room3 using rgripper2, both with robot2. Next, robot2 will move back to room2, drop ball4 in room2 using lgripper1, and drop ball2 in room2 using rgripper2. Then, robot2 will move to room3, pick up ball4 using lgripper2 and ball7 using rgripper2, and move back to room2. In room2, robot2 will drop ball7 using rgripper2, pick up ball3 using rgripper2, and then move to room1. In room1, robot2 will drop ball4 using lgripper2, pick up ball5 using lgripper2, drop ball3 using rgripper2, pick up ball6 using rgripper2, and then move to room2. Finally, in room2, robot2 will drop ball5 using lgripper2. Is the action of robot2's lgripper1 dropping ball4 in room2 executable at step 5, True or False?", "initial_state_nl_paraphrased": "Ball1 is at room3, ball2 is also at room3, ball3 is situated in room2, ball4 is at room3, ball5 is present in room1, ball6 is also present in room1, ball7 is at room3, robot1 is in room2, both of robot1's grippers (lgripper1 and rgripper1) are available, robot2 is in room2, and both of robot2's grippers (lgripper2 and rgripper2) are available."}
{"question_id": "151f5869-06c1-4e45-8500-bc868b09b47c", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball1 is picked from room1 with lgripper1 by robot1, ball4 is picked from room2 with rgripper1 by robot1, ball6 is dropped in room4 with lgripper1 by robot1, ball6 is picked from room1 with rgripper1 by robot1, from room1, robot1 moves to room5, from room1, robot1's rgripper1 picks up ball3, from room5, robot1 moves to room2, in room1, robot1's rgripper1 drops ball4, in room5, robot1's rgripper1 drops ball3 and robot1 moves from room2 to room1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is at room1, ball7 is present at room3, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: robot1, using lgripper1, will pick up ball1 from room1, then robot1 will pick up ball4 from room2 using rgripper1, followed by dropping ball6 in room4 with lgripper1, then robot1 will pick up ball6 from room1 using rgripper1. Next, robot1 will move from room1 to room5, pick up ball3 from room1 with rgripper1, move from room5 to room2, drop ball4 in room1 using rgripper1, drop ball3 in room5 using rgripper1, and finally move from room2 back to room1. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 is in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is in room3, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "47985bcc-2fcf-4e54-b2ff-8a2fc578dfd0", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball2 is picked from room2 with lgripper1 by robot1, ball5 is picked from room3 with rgripper1 by robot1, ball7 is picked from room4 with lgripper1 by robot1, from room1, robot1 moves to room3, from room1, robot1's rgripper1 picks up ball6, from room3, robot1 moves to room6, from room4, robot1 moves to room5, from room5, robot1 moves to room2, in room1, robot1's rgripper1 drops ball4, in room5, robot1's rgripper1 drops ball3, lgripper1 of robot1 drops ball1 in room5, lgripper1 of robot1 drops ball7 in room5, lgripper1 of robot1 picks up ball1 in room1, rgripper1 of robot1 drops ball6 in room3, rgripper1 of robot1 picks up ball3 in room1, rgripper1 of robot1 picks up ball4 in room2, robot1 moves from room5 to room1, robot1 moves to room1 from room2 and robot1 moves to room1 from room3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is located at room1, ball4 is at room2, ball5 is located at room3, ball6 is at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: robot1, using lgripper1, will pick up ball2 from room2, and using rgripper1, will pick up ball5 from room3 and ball7 from room4. Then, robot1 will move from room1 to room3, pick up ball6 from room1 using rgripper1, and then move from room3 to room6. Additionally, robot1 will move from room4 to room5 and then to room2. The following drop actions will be performed: in room1, robot1's rgripper1 will drop ball4, in room5, robot1's rgripper1 will drop ball3, and lgripper1 of robot1 will drop ball1 and ball7 in room5. Furthermore, lgripper1 of robot1 will pick up ball1 in room1, rgripper1 of robot1 will drop ball6 in room3, and pick up ball3 and ball4 in room1 and room2 respectively. Finally, robot1 will move from room5 to room1, from room2 to room1, and from room3 to room1. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room3, ball6 is situated in room1, ball7 is found in room4, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and the right gripper of robot1 is available."}
{"question_id": "131b3d4b-4629-4d1e-98d6-a6ab1c6ca62d", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball4 is picked from room3 with lgripper2 by robot2, from room2, robot2 moves to room3, from room2, robot2 moves to room3, from room3, robot2's rgripper2 picks up ball5, lgripper2 of robot2 drops ball1 in room2, lgripper2 of robot2 picks up ball1 in room3, rgripper2 of robot2 drops ball2 in room2, rgripper2 of robot2 picks up ball2 in room3, robot2 moves from room3 to room2 and robot2 moves from room3 to room2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is located at room3, ball3 is located at room2, ball4 is located at room3, ball5 is present at room1, ball6 is present at room1, ball7 is located at room3, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are scheduled to be executed: robot2, using lgripper2, will pick up ball4 from room3, then move from room2 to room3, and again from room2 to room3. Next, robot2's rgripper2 will pick up ball5 from room3. Additionally, robot2's lgripper2 will drop ball1 in room2, then pick up ball1 in room3. Furthermore, robot2's rgripper2 will drop ball2 in room2 and pick up ball2 in room3. Finally, robot2 will move from room3 to room2, and then again from room3 to room2. Is the execution of these actions possible, True or False?", "initial_state_nl_paraphrased": "Ball1 is at room3, ball2 is also at room3, ball3 is situated in room2, ball4 is at room3, ball5 is in room1, ball6 is also in room1, ball7 is at room3, robot1 is in room2, both of robot1's grippers (lgripper1 and rgripper1) are available, robot2 is in room2, and both of robot2's grippers (lgripper2 and rgripper2) are available."}
{"question_id": "9c9838d7-4734-4b04-ab78-1db5aab1e807", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball1 is dropped in room2 with lgripper2 by robot2, ball4 is picked from room3 with lgripper2 by robot2, ball7 is picked from room3 with rgripper2 by robot2, from room3, robot2's rgripper2 picks up ball2, lgripper2 of robot2 picks up ball1 in room3, rgripper2 of robot2 drops ball2 in room2, robot2 moves from room2 to room3, robot2 moves from room2 to room3, robot2 moves from room3 to room2 and robot2 moves from room3 to room2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is present at room3, ball2 is located at room3, ball3 is located at room2, ball4 is located at room3, ball5 is present at room1, ball6 is present at room1, ball7 is located at room3, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to be executed: robot2 uses lgripper2 to drop ball1 in room2, then uses lgripper2 to pick up ball4 in room3, and uses rgripper2 to pick up ball7 in room3. Additionally, robot2's rgripper2 will pick up ball2 in room3, and its lgripper2 will pick up ball1 in room3. Furthermore, robot2's rgripper2 will drop ball2 in room2. The robot will also move from room2 to room3, then back to room2, and repeat this movement. Is the execution of these actions possible, True or False?", "initial_state_nl_paraphrased": "Ball1 is at room3, ball2 is also at room3, ball3 is situated in room2, ball4 is at room3, ball5 is in room1, ball6 is also in room1, ball7 is at room3, robot1 is in room2, robot1 has both lgripper1 and rgripper1 available, robot2 is in room2, and both lgripper2 and rgripper2 of robot2 are available."}
{"question_id": "a77e14f0-8a40-4ed5-ba37-3945c9219aab", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room6. Is the action: lgripper1 of robot1 picks up ball7 in room6 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is located at room1, ball4 is at room2, ball5 is located at room3, ball6 is at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: the lgripper1 of robot1 is set to pick up ball7 in room6. Is the action of lgripper1 of robot1 picking up ball7 in room6 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room3, ball6 is situated in room1, ball7 is found in room4, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and the right gripper of robot1 is available."}
{"question_id": "4356ec90-7dbf-4bac-afce-a00f5f81c154", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball3 is picked from room1 with rgripper1 by robot1, ball4 is picked from room2 with rgripper1 by robot1, from room1, robot1 moves to room5, from room1, robot1's lgripper1 picks up ball1, from room2, robot1 moves to room1, from room2, robot1's lgripper1 picks up ball2, from room2, robot1's rgripper1 picks up ball5, from room3, robot1's rgripper1 picks up ball7, from room5, robot1 moves to room2, in room1, robot1's rgripper1 drops ball4, lgripper1 of robot1 drops ball1 in room4, rgripper1 of robot1 drops ball3 in room5, rgripper1 of robot1 drops ball6 in room3, rgripper1 of robot1 drops ball7 in room4, rgripper1 of robot1 picks up ball6 in room1, robot1 moves from room4 to room2, robot1 moves to room1 from room4, robot1 moves to room3 from room1 and robot1 moves to room4 from room3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is at room1, ball7 is present at room3, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: robot1, using rgripper1, will pick up ball3 from room1 and ball4 from room2, then move from room1 to room5. Next, robot1 will pick up ball1 from room1 using lgripper1, move from room2 to room1, pick up ball2 from room2 using lgripper1, and pick up ball5 from room2 and ball7 from room3 using rgripper1. After that, robot1 will move from room5 to room2, drop ball4 in room1 using rgripper1, drop ball1 in room4 using lgripper1, drop ball3 in room5, ball6 in room3, and ball7 in room4 using rgripper1. Then, robot1 will pick up ball6 in room1 using rgripper1, move from room4 to room2, from room4 to room1, from room1 to room3, and from room3 to room4. Is it possible to execute this sequence of actions, True or False?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 is in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is in room3, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "261845e2-d281-4ddd-b854-2c8a3ec997f0", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room4, from room4, robot1 moves to room5, from room4, robot1's rgripper1 picks up ball5, from room5, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, from room1, robot1's rgripper1 picks up ball3, robot1 moves from room1 to room5, in room5, robot1's lgripper1 drops ball1, rgripper1 of robot1 drops ball3 in room5 and from room5, robot1 moves to room2. Is the action: ball5 is picked from room4 with rgripper1 by robot1 executable at step 3, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is located at room1, ball4 is at room2, ball5 is located at room3, ball6 is at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: robot1's left gripper (lgripper1) picks up ball7 in room4, then robot1 moves from room4 to room5, next robot1's right gripper (rgripper1) picks up ball5 in room4, after that robot1 moves from room5 to room1, then robot1's lgripper1 picks up ball1 in room1, followed by robot1's rgripper1 picking up ball3 in room1, then robot1 moves from room1 to room5, in room5, robot1's lgripper1 drops ball1 and robot1's rgripper1 drops ball3, and finally robot1 moves from room5 to room2. Is the action: robot1's rgripper1 picks up ball5 in room4 executable at step 3, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room3, ball6 is situated in room1, ball7 is found in room4, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and robot1's right gripper is available."}
{"question_id": "f4a61ac2-7bbf-416a-8780-342e9341c920", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball1 is dropped in room3 with lgripper1 by robot1, ball3 is dropped in room5 with lgripper1 by robot1, ball3 is picked from room1 with lgripper1 by robot1, ball5 is dropped in room1 with rgripper1 by robot1, ball5 is picked from room2 with rgripper1 by robot1, from room1, robot1 moves to room2, from room1, robot1 moves to room5, from room1, robot1's lgripper1 picks up ball1, from room1, robot1's rgripper1 picks up ball6, lgripper1 of robot1 drops ball4 in room5, rgripper1 of robot1 drops ball2 in room4, rgripper1 of robot1 drops ball6 in room4, rgripper1 of robot1 picks up ball2 in room2, robot1 moves from room4 to room2, robot1 moves to room1 from room4, robot1 moves to room1 from room5, robot1 moves to room3 from room2, robot1 moves to room4 from room3 and robot1 moves to room5 from room2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is present at room1, ball2 is located at room2, ball3 is located at room1, ball4 is at room2, ball5 is at room2, ball6 is present at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: robot1 uses lgripper1 to drop ball1 in room3 and ball3 in room5, and to pick up ball3 from room1. Meanwhile, robot1 uses rgripper1 to drop ball5 in room1 and pick up ball5 from room2. Additionally, robot1 will move from room1 to room2, from room1 to room5, and perform various other movements between rooms. Robot1's grippers will also be used to pick up and drop various balls in different rooms, including picking up ball1 and ball6 from room1, dropping ball4 in room5, and dropping and picking up ball2 in rooms 4 and 2, respectively. The question remains whether this sequence of actions can be executed successfully, True or False?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is present at room2, ball3 is present in room1, ball4 is located at room2, ball5 is present at room2, ball6 is located in room1, robot1 is located in room4, robot1's left gripper (lgripper1) is available and robot1's right gripper (rgripper1) is free."}
{"question_id": "53537d4e-dd39-4829-95f7-7324ec89484f", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from room1, robot1 moves to room5, from room1, robot1's rgripper1 picks up ball3, from room4, robot1 moves to room5, from room4, robot1's lgripper1 picks up ball7, from room5, robot1 moves to room1, in room5, robot1's lgripper1 drops ball1, lgripper1 of robot1 drops ball7 in room5, lgripper1 of robot1 picks up ball1 in room1, rgripper1 of robot1 drops ball3 in room5 and robot1 moves to room2 from room5. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is located at room1, ball4 is at room2, ball5 is located at room3, ball6 is at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned: robot1 will move from room1 to room5, then use its rgripper1 to pick up ball3 in room1. Next, robot1 will move from room4 to room5, and use its lgripper1 to pick up ball7 in room4. After that, robot1 will move from room5 back to room1, drop ball1 in room5 using its lgripper1, and then drop ball7 in room5 using the same lgripper1. Following this, robot1's lgripper1 will pick up ball1 in room1, and its rgripper1 will drop ball3 in room5. Finally, robot1 will move from room5 to room2. Is it possible to execute this sequence, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room3, ball6 is situated in room1, ball7 is found in room4, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and the right gripper of robot1 is available."}
{"question_id": "7792832e-44d7-4cb8-a7e5-b17a2e93f259", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball4 is dropped in room4 with lgripper1 by robot1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is at room1, ball7 is present at room3, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: robot1 will use lgripper1 to drop ball4 in room4. Is the execution of this action feasible, True or False?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 is in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is in room3, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "2824b4f5-92aa-4f4e-89c2-c76637cb3485", "domain_name": "grippers", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: from room4, robot1 moves to room1, from room1, robot1's lgripper1 picks up ball1, from room1, robot1 moves to room2, from room2, robot1's rgripper1 picks up ball2, from room2, robot1 moves to room3, in room3, robot1's lgripper1 drops ball1, lgripper1 of robot1 picks up ball6 in room5, ball2 is dropped in room4 with rgripper1 by robot1, from room4, robot1 moves to room2 and lgripper1 of robot1 picks up ball4 in room2. Is the action: ball6 is picked from room5 with lgripper1 by robot1 executable at step 7, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is present at room1, ball2 is located at room2, ball3 is located at room1, ball4 is at room2, ball5 is at room2, ball6 is present at room1, robot1 is present in room4, robot1's lgripper1 is available and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: starting from room4, robot1 will move to room1, then from room1, robot1's left gripper (lgripper1) will pick up ball1, next from room1, robot1 will move to room2, from room2, robot1's right gripper (rgripper1) will pick up ball2, then from room2, robot1 will move to room3, in room3, robot1's lgripper1 will drop ball1, robot1's lgripper1 will pick up ball6 in room5, robot1's rgripper1 will drop ball2 in room4, from room4, robot1 will move to room2, and finally robot1's lgripper1 will pick up ball4 in room2. Is the action: robot1 picking up ball6 from room5 with lgripper1 executable at step 7, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 resides in room2, ball3 is also in room1, ball4 is positioned in room2, ball5 is located in room2, ball6 is found in room1, robot1 is situated in room4, robot1's left gripper, lgripper1, is available, and robot1's right gripper, rgripper1, is unoccupied."}
{"question_id": "7a43e767-5517-4fd3-9cab-47e32271785b", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball1 is picked from room3 with lgripper2 by robot2, ball2 is picked from room1 with lgripper2 by robot1, ball3 is dropped in room1 with rgripper2 by robot2, ball4 is dropped in room1 with lgripper2 by robot2, ball5 is picked from room1 with lgripper2 by robot2, ball6 is picked from room1 with rgripper2 by robot2, from room2, robot2 moves to room3, from room3, robot2's lgripper2 picks up ball4, from room3, robot2's rgripper2 picks up ball2, from room3, robot2's rgripper2 picks up ball7, in room2, robot2's lgripper2 drops ball1, in room2, robot2's lgripper2 drops ball5, in room2, robot2's rgripper2 drops ball7, rgripper2 of robot2 drops ball2 in room2, rgripper2 of robot2 picks up ball3 in room2, robot2 moves from room3 to room2, robot2 moves to room1 from room2, robot2 moves to room2 from room3 and robot2 moves to room3 from room2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is present at room3, ball2 is located at room3, ball3 is located at room2, ball4 is located at room3, ball5 is present at room1, ball6 is present at room1, ball7 is located at room3, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: robot2 will use lgripper2 to pick up ball1 from room3, while robot1 will use lgripper2 to pick up ball2 from room1. Meanwhile, robot2 will drop ball3 in room1 using rgripper2 and ball4 in room1 using lgripper2. Additionally, robot2 will pick up ball5 from room1 with lgripper2 and ball6 from room1 with rgripper2. Robot2 will then move from room2 to room3, and from room3, it will use lgripper2 to pick up ball4 and rgripper2 to pick up ball2 and ball7. In room2, robot2 will drop ball1, ball5, and ball7 using lgripper2 and rgripper2, and then drop ball2 in room2 using rgripper2. Furthermore, robot2's rgripper2 will pick up ball3 in room2. Robot2 will also move between rooms: from room3 to room2, from room2 to room1, from room3 to room2, and from room2 to room3. Is the execution of these actions possible, True or False?", "initial_state_nl_paraphrased": "Ball1 is at room3, ball2 is also at room3, ball3 is situated in room2, ball4 is at room3, ball5 is in room1, ball6 is also in room1, ball7 is at room3, robot1 is in room2, both of robot1's grippers (lgripper1 and rgripper1) are available, robot2 is in room2, and both of robot2's grippers (lgripper2 and rgripper2) are available."}
{"question_id": "27476761-9cae-4428-a95b-c8becd39fa4e", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball4 is dropped in room1 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1's rgripper1 picks up ball6, from room3, robot1's rgripper1 picks up ball5, from room5, robot1 moves to room1, in room3, robot1's rgripper1 drops ball6, in room5, robot1's lgripper1 drops ball1, lgripper1 of robot1 drops ball7 in room5, lgripper1 of robot1 picks up ball1 in room1, lgripper1 of robot1 picks up ball2 in room2, lgripper1 of robot1 picks up ball7 in room4, rgripper1 of robot1 drops ball3 in room5, rgripper1 of robot1 picks up ball4 in room2, robot1 moves from room1 to room3, robot1 moves from room1 to room5, robot1 moves from room2 to room1, robot1 moves from room3 to room6, robot1 moves from room5 to room2 and robot1 moves to room5 from room4. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is located at room1, ball4 is at room2, ball5 is located at room3, ball6 is at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: robot1, using rgripper1, drops ball4 in room1, then picks up ball3 in room1, followed by picking up ball6 in room1, and then ball5 in room3. Next, robot1 moves from room5 to room1. In room3, robot1's rgripper1 releases ball6, and in room5, robot1's lgripper1 drops ball1. Additionally, robot1's lgripper1 drops ball7 in room5, then picks up ball1 in room1, ball2 in room2, and ball7 in room4. Furthermore, robot1's rgripper1 drops ball3 in room5 and picks up ball4 in room2. The robot's movements include going from room1 to room3, from room1 to room5, from room2 to room1, from room3 to room6, from room5 to room2, and from room4 to room5. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room3, ball6 is situated in room1, ball7 is found in room4, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and the right gripper of robot1 is available."}
{"question_id": "4e55ad52-375f-4643-9e61-cd32bca31be9", "domain_name": "grippers", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: lgripper1 of robot1 picks up ball7 in room3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Ball1 is present at room3, ball2 is located at room3, ball3 is located at room2, ball4 is located at room3, ball5 is present at room1, ball6 is present at room1, ball7 is located at room3, robot1 is at room2, robot1's lgripper1 is free, robot1's rgripper1 is free, robot2 is present in room2, robot2's lgripper2 is free and robot2's rgripper2 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: robot1's lgripper1 is to grasp ball7 in room3. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "Ball1 is at room3, ball2 is also at room3, ball3 is situated in room2, ball4 is at room3, ball5 is in room1, ball6 is also in room1, ball7 is at room3, robot1 is in room2, both of robot1's grippers (lgripper1 and rgripper1) are available, robot2 is present in room2, and both of robot2's grippers (lgripper2 and rgripper2) are available."}
{"question_id": "75a1daf6-ba42-459e-916c-cde6cf0fddf9", "domain_name": "grippers", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: ball3 is dropped in room5 with rgripper1 by robot1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1's rgripper1 picks up ball6, from room2, robot1 moves to room1, from room2, robot1's rgripper1 picks up ball4, lgripper1 of robot1 picks up ball1 in room1, rgripper1 of robot1 drops ball4 in room1, robot1 moves to room1 from room4, robot1 moves to room2 from room5 and robot1 moves to room5 from room1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Ball1 is at room1, ball2 is present at room2, ball3 is at room1, ball4 is present at room2, ball5 is present at room2, ball6 is at room1, ball7 is present at room3, robot1 is present in room4, robot1's lgripper1 is free and robot1's rgripper1 is free.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: robot1, starting from room1, uses rgripper1 to drop ball3 in room5, then uses rgripper1 to pick up ball3 in room1, followed by picking up ball6 in room1, then moves from room2 to room1, picks up ball4 in room2, uses lgripper1 to pick up ball1 in room1, drops ball4 in room1 using rgripper1, moves from room4 to room1, then from room5 to room2, and finally from room1 to room5. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Ball1 is located in room1, ball2 is situated in room2, ball3 is in room1, ball4 is situated in room2, ball5 is also in room2, ball6 is in room1, ball7 is in room3, robot1 is in room4, and both robot1's left gripper (lgripper1) and right gripper (rgripper1) are available."}
{"question_id": "a5c1b5d8-8d5f-40a8-b180-1b8068c0ceb1", "domain_name": "grippers", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: from room4, robot1's lgripper1 picks up ball7, robot1 moves from room4 to room5, ball7 is dropped in room5 with lgripper1 by robot1, robot1 moves to room1 from room5, ball1 is picked from room1 with lgripper1 by robot1, from room1, robot1's rgripper1 picks up ball3, from room1, robot1 moves to room5, ball1 is dropped in room5 with lgripper1 by robot1, rgripper1 of robot1 drops ball3 in room5, robot1 moves to room2 from room5, ball2 is picked from room2 with lgripper1 by robot1, ball4 is picked from room2 with rgripper1 by robot1, robot1 moves to room1 from room2, in room1, robot1's rgripper1 drops ball4, from room1, robot1's rgripper1 picks up ball6, robot1 moves to room5 from room2, ball6 is dropped in room3 with rgripper1 by robot1, rgripper1 of robot1 picks up ball5 in room3 and robot1 moves to room6 from room3. Is the action: robot1 moves from room2 to room5 executable at step 16, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Ball1 is located at room1, ball2 is present at room2, ball3 is located at room1, ball4 is at room2, ball5 is located at room3, ball6 is at room1, ball7 is present at room4, lgripper1 of robot1 is free, robot1 is at room4 and robot1's rgripper1 is available.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: starting from room4, robot1's left gripper (lgripper1) will pick up ball7, then robot1 will move to room5, drop ball7 in room5 using lgripper1, and proceed to room1. In room1, robot1 will pick up ball1 with lgripper1 and ball3 with its right gripper (rgripper1), then move to room5, drop ball1 and ball3 in room5, and head to room2. In room2, robot1 will pick up ball2 with lgripper1 and ball4 with rgripper1, then return to room1, drop ball4, and pick up ball6 with rgripper1. Next, robot1 will move to room5 (though the text says from room2, it seems to be an error and it should be from room1), drop ball6 in room3, pick up ball5 in room3 with rgripper1, and finally move to room6 from room3. Is the action: robot1 moves from room2 to room5 executable at step 16, True or False?", "initial_state_nl_paraphrased": "Ball1 is situated in room1, ball2 is found in room2, ball3 is situated in room1, ball4 is situated in room2, ball5 is situated in room3, ball6 is situated in room1, ball7 is found in room4, the left gripper of robot1 is unoccupied, robot1 is positioned in room4 and the right gripper of robot1 is available."}
