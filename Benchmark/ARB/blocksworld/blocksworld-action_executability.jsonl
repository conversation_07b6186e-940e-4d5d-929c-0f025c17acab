{"question_id": "69c5f558-b17a-4e9d-9482-c04d7e09ae63", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: block b8 is unstacked from top of block b6. Is the action: block b8 is unstacked from block b6 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is on block b4, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is on block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the planned actions include unstacking block b8 from the top of block b6. Is the action of unstacking block b8 from block b6 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on top of block b6, block b3 is resting on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is placed on the table, block b6 is stacked on top of block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is resting on block b8, and the hand is empty."}
{"question_id": "d6dc4ec5-b6cc-47dc-a985-5ecbf5d37689", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b1 is stacked on top of block b2, block b1 is unstacked from top of block b2, block b2 is picked up, block b2 is put down, block b2 is stacked on top of block b1, block b2 is unstacked from top of block b6, block b3 is unstacked from top of block b5, block b4 is picked up, block b4 is stacked on top of block b5, block b4 is unstacked from block b5, block b5 is unstacked from top of block b7, block b6 is picked up by the hand, block b7 is picked up, block b7 is put down, block b7 is unstacked from top of block b1, from top of block b4, block b1 is unstacked, on top of block b3, block b6 is stacked, on top of block b6, block b5 is stacked and the hand puts down the block b3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: block b1 is placed on top of block b2, then removed from it, block b2 is lifted, then put down, block b2 is stacked on top of block b1, then removed from block b6, block b3 is removed from block b5, block b4 is picked up, then stacked on block b5, then removed from it, block b5 is removed from block b7, block b6 is picked up by the hand, block b7 is picked up, then put down, block b7 is removed from block b1 and block b4, block b1 is removed, block b3 is placed on top of block b6, block b5 is stacked on top of block b6, and the hand puts down block b3. Is it possible to execute it, True or False?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1 and the hand is empty."}
{"question_id": "6a5a0a78-89ee-42e2-8ca2-cf2a1e322339", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8, the hand puts down the block b9, from top of block b6, block b2 is unstacked, on top of block b5, block b2 is stacked, block b6 is unstacked from top of block b3, the hand puts down the block b6, from top of block b4, block b3 is unstacked, block b3 is stacked on top of block b9, from top of block b1, block b8 is unstacked, block b8 is stacked on top of block b4, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b8, from top of block b9, block b3 is unstacked, on top of block b2, block b3 is stacked, from top of block b7, block b1 is unstacked, block b7 is stacked on top of block b5, block b7 is picked up from the table, on top of block b9, block b7 is stacked and block b6 is picked up from the table to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "block b7 is stacked on top of block b5", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is on block b4, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is on block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial state, the following steps are taken: block b9 is removed from the top of block b8, then placed on the table, block b2 is removed from the top of block b6, and placed on top of block b5, block b6 is then removed from the top of block b3 and placed on the table, block b3 is removed from the top of block b4 and placed on top of block b9, block b8 is removed from the top of block b1 and placed on top of block b4, block b2 is removed from the top of block b5 and placed on top of block b8, block b3 is removed from the top of block b9 and placed on top of block b2, block b1 is removed from the top of block b7, block b7 is placed on top of block b5, block b7 is picked up from the table, and then placed on top of block b9, and finally block b6 is picked up from the table to achieve the current state. What is the first action in the sequence that cannot be executed? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is resting on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is placed on the table, block b6 is stacked on top of block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is resting on block b8, and the hand is empty."}
{"question_id": "b20c2517-c939-484a-9e94-245355bd4251", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked, block b9 is put down on the table, block b2 is unstacked from top of block b6, block b2 is stacked on top of block b5, block b6 is unstacked from top of block b3, the hand puts down the block b6, block b3 is unstacked from block b4, block b3 is stacked on top of block b9, from top of block b1, block b8 is unstacked, block b8 is stacked on top of block b4, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b8, block b3 is unstacked from top of block b9, on top of block b2, block b3 is stacked, block b1 is unstacked from block b7, block b1 is stacked on top of block b3, block b7 is picked up by the hand, on top of block b9, block b7 is stacked and block b6 is picked up by the hand to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "block b6 is put down on the table, block b6 is stacked on top of block b5, on top of block b1, block b6 is stacked and block b6 is stacked on top of block b7", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is on block b4, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is on block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b9 is removed from the top of block b8 and placed on the table, then block b2 is unstacked from block b6 and placed on top of block b5. Next, block b6 is unstacked from block b3 and put down by the hand. Block b3 is then unstacked from block b4 and stacked on top of block b9. Block b8 is unstacked from block b1 and placed on top of block b4. Block b2 is then unstacked from block b5 and stacked on top of block b8. Block b3 is unstacked from block b9 and stacked on top of block b2. Block b1 is unstacked from block b7 and placed on top of block b3. Block b7 is picked up by the hand and stacked on top of block b9, and finally, block b6 is picked up by the hand to reach the current state. In this state, list all possible actions that can be executed. If there are no actions, write None.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on top of block b6, block b3 is placed on block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on top of block b3, block b7 is resting on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is placed on block b8, and the hand is empty."}
{"question_id": "86a433e2-183a-46e5-88d4-174a9a635b33", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: from top of block b5, block b3 is unstacked, the hand puts down the block b3, block b2 is unstacked from block b6, block b2 is put down on the table, block b6 is picked up from the table, on top of block b3, block b6 is stacked, block b5 is unstacked from top of block b7, block b5 is stacked on top of block b6, from top of block b1, block b7 is unstacked, block b7 is put down, block b1 is unstacked from block b4, on top of block b2, block b1 is stacked, block b4 is picked up, block b4 is stacked on top of block b5, from top of block b2, block b1 is unstacked, on top of block b4, block b1 is stacked, block b2 is picked up, on top of block b1, block b2 is stacked and block b7 is picked up from the table. Is the action: block b2 is picked up executable at step 17, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: starting with block b5, block b3 is removed and placed on the table, then block b2 is unstacked from block b6 and put down, after which block b6 is picked up and stacked on top of block b3. Next, block b5 is unstacked from block b7 and stacked on top of block b6, followed by unstacking block b7 from block b1 and putting it down. Block b1 is then unstacked from block b4 and stacked on top of block b2, and block b4 is picked up and stacked on top of block b5. Subsequently, block b1 is unstacked from block b2 and stacked on top of block b4, block b2 is picked up and stacked on top of block b1, and finally, block b7 is picked up from the table. Is the action: block b2 is picked up executable at step 17, True or False?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1, and the hand is empty."}
{"question_id": "2310d0f2-e88e-43d2-9f24-7844ab5f995f", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: block b4 is unstacked from block b8. Is the action: block b4 is unstacked from block b8 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the planned actions include unstacking block b4 from block b8. Is the action of unstacking block b4 from block b8 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "2b902c58-3e1c-4f0e-a1c0-8e4e40c54158", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b3 is put down on the table, block b3 is unstacked from top of block b7, block b4 is put down, block b4 is unstacked from top of block b1, block b5 is stacked on top of block b1, block b5 is stacked on top of block b2, block b5 is unstacked from top of block b4, block b7 is stacked on top of block b4, from top of block b2, block b5 is unstacked and from top of block b6, block b7 is unstacked. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: place block b3 on the table, remove block b3 from the top of block b7, place block b4 down, remove block b4 from the top of block b1, place block b5 on top of block b1, place block b5 on top of block b2, remove block b5 from the top of block b4, place block b7 on top of block b4, unstack block b5 from the top of block b2, and unstack block b7 from the top of block b6. Is the execution of these actions possible, True or False?", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 has no blocks on it, block b2 is positioned on the table, block b3 has no blocks on it, block b3 is stacked on top of block b7, block b4 is positioned on top of block b1, block b5 has no blocks on it, block b5 is placed on top of block b4, block b6 is resting on the table, block b7 is placed on block b6, and the hand is currently empty."}
{"question_id": "9360efae-d86f-4bfd-8a38-0b5b79ec04de", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from top of block b1, block b9 is unstacked. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is on block b4, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is on block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: block b9 will be unstacked from the top of block b1. Is the execution of this action possible, True or False?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is resting on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is placed on the table, block b6 is stacked on top of block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is resting on block b8, and the hand is empty."}
{"question_id": "42893609-b943-4632-87f4-87fbbc69adcf", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b3 is unstacked from top of block b7. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: unstack block b3 from the top of block b7. Is the execution of this action feasible, True or False?", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 has no blocks on it, block b2 is positioned on the table, block b3 has no blocks on it, block b3 is stacked on top of block b7, block b4 is positioned on top of block b1, block b5 has no blocks on it, block b5 is placed on top of block b4, block b6 is on the table, block b7 is placed on block b6, and the hand is empty."}
{"question_id": "e6ab1496-bbb2-4c56-a182-9ba91ae8abdc", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b1 is put down on the table, block b2 is stacked on top of block b4, block b2 is unstacked from block b5, block b4 is unstacked from top of block b1, block b6 is put down, block b6 is unstacked from block b7, block b7 is picked up from the table, block b7 is stacked on top of block b2, from top of block b6, block b1 is unstacked and the hand puts down the block b4. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: place block b1 on the table, stack block b2 on top of block b4, unstack block b2 from block b5, unstack block b4 from the top of block b1, place block b6 on the table, unstack block b6 from block b7, pick up block b7 from the table, stack block b7 on top of block b2, unstack block b1 from the top of block b6, and finally, put down block b4. Is the execution of these actions feasible, True or False?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "3d6bd8d3-e271-44b3-9c8a-adfcd8b4f366", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, block b3 is put down on the table, block b5 is unstacked from top of block b4, on top of block b2, block b5 is stacked, block b4 is unstacked from block b1, block b4 is put down, from top of block b2, block b5 is unstacked, on top of block b1, block b5 is stacked, block b7 is unstacked from top of block b6, on top of block b4, block b7 is stacked, block b6 is picked up, on top of block b5, block b6 is stacked, block b2 is picked up, block b2 is stacked on top of block b6, block b3 is picked up by the hand, on top of block b2, block b3 is stacked, from top of block b4, block b7 is unstacked, block b7 is stacked on top of block b3 and block b4 is picked up to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "on top of block b7, block b4 is stacked and block b4 is put down on the table", "plan_length": 19, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b7 and placed on the table, block b5 is unstacked from block b4 and placed on top of block b2, block b4 is then unstacked from block b1 and put down, block b5 is unstacked from block b2 and stacked on top of block b1, block b7 is unstacked from block b6 and stacked on top of block b4, block b6 is picked up and stacked on top of block b5, block b2 is picked up and stacked on top of block b6, block b3 is picked up and stacked on top of block b2, block b7 is unstacked from block b4 and stacked on top of block b3, and finally, block b4 is picked up to reach the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 has no blocks on it, block b2 is positioned on the table, block b3 has no blocks on it, block b3 is stacked on top of block b7, block b4 is positioned on top of block b1, block b5 has no blocks on it, block b5 is placed on top of block b4, block b6 is on the table, block b7 is stacked on block b6, and the hand is empty."}
{"question_id": "df310b02-5ab6-4d00-b7d9-86d287c549c0", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: block b3 is unstacked from top of block b5, block b3 is put down, from top of block b6, block b2 is unstacked, block b2 is put down on the table, block b6 is picked up from the table, on top of block b3, block b6 is stacked, block b5 is unstacked from block b7, on top of block b6, block b5 is stacked, block b7 is unstacked from block b1 and on top of block b7, block b6 is stacked. Is the action: on top of block b7, block b6 is stacked executable at step 10, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial setup, the following sequence of actions is planned for steps 1 through 10: unstack block b3 from block b5, place block b3 on the table, unstack block b2 from block b6, put block b2 on the table, pick up block b6 from the table, stack block b6 on top of block b3, unstack block b5 from block b7, stack block b5 on top of block b6, unstack block b7 from block b1, and stack block b6 on top of block b7. Is the action of stacking block b6 on top of block b7 executable at step 10, True or False?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1 and the hand is empty."}
{"question_id": "8921e2c4-41dd-4f5a-b870-2b4357a079d0", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: from top of block b1, block b4 is unstacked. Is the action: block b4 is unstacked from block b1 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: block b4 is to be unstacked from the top of block b1. Is the action of unstacking block b4 from block b1 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "3ab0dedd-6900-4535-bc60-ad62672666a2", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked, from top of block b6, block b2 is unstacked, from top of block b6, block b1 is unstacked, block b1 is put down, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b4, block b6 is unstacked from top of block b7, block b6 is put down on the table, block b7 is picked up by the hand, on top of block b2, block b7 is stacked, block b3 is unstacked from top of block b8, block b3 is stacked on top of block b7, block b1 is picked up from the table, on top of block b3, block b1 is stacked, block b5 is picked up from the table, on top of block b1, block b5 is stacked, block b8 is picked up by the hand, on top of block b5, block b8 is stacked and block b6 is picked up by the hand to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "block b2 is unstacked from block b6", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b4 is removed from the top of block b1, block b2 is removed from the top of block b6, block b1 is then removed from the top of block b6, block b1 is placed on the table, block b2 is removed from the top of block b5, block b2 is placed on top of block b4, block b6 is removed from the top of block b7, block b6 is placed on the table, block b7 is picked up, block b7 is placed on top of block b2, block b3 is removed from the top of block b8, block b3 is placed on top of block b7, block b1 is picked up from the table, block b1 is placed on top of block b3, block b5 is picked up from the table, block b5 is placed on top of block b1, block b8 is picked up, block b8 is placed on top of block b5, and block b6 is picked up to reach the current state. What is the first action in the sequence that cannot be executed? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "01a9c737-7f1e-492a-a6ce-89cb2d0583cb", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: block b3 is unstacked from block b7, block b3 is put down, from top of block b4, block b5 is unstacked, on top of block b2, block b5 is stacked, block b4 is unstacked from block b1, block b4 is put down on the table, from top of block b2, block b5 is unstacked, on top of block b1, block b5 is stacked, block b7 is unstacked from top of block b6, block b1 is put down on the table, block b6 is picked up, on top of block b5, block b6 is stacked, block b2 is picked up from the table, block b2 is stacked on top of block b6, block b3 is picked up by the hand, on top of block b2, block b3 is stacked, block b7 is unstacked from top of block b4, block b7 is stacked on top of block b3 and block b4 is picked up by the hand. Is the action: block b1 is put down executable at step 10, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: unstack block b3 from block b7, place block b3 on the table, unstack block b5 from the top of block b4, stack block b5 on top of block b2, unstack block b4 from block b1, place block b4 on the table, unstack block b5 from the top of block b2, stack block b5 on top of block b1, unstack block b7 from the top of block b6, place block b1 on the table, pick up block b6, stack block b6 on top of block b5, pick up block b2 from the table, stack block b2 on top of block b6, pick up block b3 with the hand, stack block b3 on top of block b2, unstack block b7 from the top of block b4, stack block b7 on top of block b3, and pick up block b4 with the hand. Is the action: placing block b1 on the table executable at step 10, True or False?", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 is unobstructed, block b2 is positioned on the table, block b3 is unobstructed, block b3 is stacked on top of block b7, block b4 is positioned on top of block b1, block b5 is unobstructed, block b5 is placed on top of block b4, block b6 is on the table, block b7 is resting on block b6, and the hand is empty."}
{"question_id": "c47d2bf4-e1ed-4762-b1b6-e6f09a6b1fcb", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b1 is unstacked from block b7, block b2 is stacked on top of block b8, block b2 is unstacked from top of block b6, block b3 is stacked on top of block b9, block b3 is unstacked from top of block b4, block b3 is unstacked from top of block b9, block b6 is picked up, block b6 is put down, block b6 is unstacked from block b3, block b7 is picked up by the hand, block b7 is stacked on top of block b9, block b9 is put down on the table, block b9 is unstacked from top of block b8, from top of block b1, block b8 is unstacked, from top of block b5, block b2 is unstacked, on top of block b2, block b3 is stacked, on top of block b3, block b1 is stacked, on top of block b4, block b8 is stacked and on top of block b5, block b2 is stacked. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is on block b4, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is on block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: unstack block b1 from block b7, place block b2 on top of block b8, unstack block b2 from block b6, place block b3 on top of block b9, unstack block b3 from block b4 and block b9, pick up block b6 and put it down, unstack block b6 from block b3, pick up block b7 with the hand and stack it on top of block b9, place block b9 on the table, unstack block b9 from block b8 and block b1, unstack block b8 from block b5 and block b2, then stack block b3 on top of block b2, block b1 on top of block b3, block b8 on top of block b4, and block b2 on top of block b5. Is it possible to execute this sequence, True or False?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is resting on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is placed on the table, block b6 is stacked on top of block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is resting on block b8, and the hand is empty."}
{"question_id": "fa479b37-c8c7-48aa-83f0-302059893e8e", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: on top of block b3, block b6 is stacked. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: block b6 will be placed on top of block b3. Is the execution of this action feasible, True or False?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "484fa4cc-58de-4e93-95e3-1c39286d2cdc", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b2 is stacked on top of block b5, block b2 is unstacked from top of block b6, block b3 is unstacked from top of block b4, block b6 is put down, block b6 is unstacked from block b3, block b8 is stacked on top of block b4, block b8 is unstacked from top of block b1, block b9 is unstacked from block b8, on top of block b9, block b3 is stacked and the hand puts down the block b9. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is on block b4, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is on block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: place block b2 on top of block b5, remove block b2 from the top of block b6, unstack block b3 from block b4, put block b6 down, unstack block b6 from block b3, place block b8 on top of block b4, unstack block b8 from block b1, unstack block b9 from block b8, stack block b3 on top of block b9, and finally, put block b9 down. Is the execution of these actions feasible, True or False?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is resting on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is placed on the table, block b6 is stacked on top of block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is resting on block b8, and the hand is empty."}
{"question_id": "6e81df67-2eea-4627-b840-4461d50d4630", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1, block b4 is put down, from top of block b6, block b1 is unstacked, the hand puts down the block b1, block b2 is unstacked from block b5, block b2 is stacked on top of block b4, block b6 is unstacked from block b7, the hand puts down the block b6, block b7 is picked up from the table, on top of block b2, block b7 is stacked, block b3 is unstacked from block b8, on top of block b7, block b3 is stacked, block b1 is picked up by the hand, block b1 is stacked on top of block b3, block b5 is picked up by the hand, block b5 is stacked on top of block b1, block b8 is picked up from the table, on top of block b5, block b8 is stacked and block b6 is picked up to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "block b6 is put down and on top of block b8, block b6 is stacked", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b4 is removed from the top of block b1 and placed down, then block b1 is unstacked from the top of block b6 and put down by the hand. Next, block b2 is unstacked from block b5 and stacked on top of block b4. Block b6 is then unstacked from block b7 and put down by the hand, after which block b7 is picked up from the table and stacked on top of block b2. Block b3 is unstacked from block b8 and stacked on top of block b7. The hand then picks up block b1 and stacks it on top of block b3, followed by picking up block b5 and stacking it on top of block b1. Block b8 is then picked up from the table and stacked on top of block b5, and finally, block b6 is picked up to reach the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "dd355754-d637-4702-bfc5-8bee54fc6d94", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b1 is picked up by the hand, block b1 is stacked on top of block b3, block b1 is unstacked from block b6, block b2 is stacked on top of block b4, block b3 is unstacked from top of block b8, block b4 is put down on the table, block b5 is picked up by the hand, block b6 is picked up by the hand, block b6 is put down, block b7 is picked up by the hand, block b8 is picked up from the table, from top of block b1, block b4 is unstacked, from top of block b5, block b2 is unstacked, from top of block b7, block b6 is unstacked, on top of block b1, block b5 is stacked, on top of block b2, block b7 is stacked, on top of block b5, block b8 is stacked, on top of block b7, block b3 is stacked and the hand puts down the block b1. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: the hand will pick up block b1, place it on top of block b3, unstack it from block b6, stack block b2 on top of block b4, unstack block b3 from block b8, put block b4 on the table, pick up blocks b5, b6, and b7 with the hand, pick up block b8 from the table and from on top of block b1, unstack block b4, unstack block b2 from block b5, and unstack block b6 from block b7, then stack block b5 on top of block b1, block b7 on top of block b2, block b8 on top of block b5, and block b3 on top of block b7, and finally, the hand will put down block b1. Is it possible to execute it, True or False?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b6, block b2 has no blocks on it, block b2 is stacked on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is resting on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "abbe25a7-555d-4c34-988b-5742404ed65f", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked, block b9 is put down, block b2 is unstacked from top of block b6, on top of block b5, block b2 is stacked, block b6 is unstacked from top of block b3, block b6 is put down, from top of block b4, block b3 is unstacked, on top of block b9, block b3 is stacked, block b8 is unstacked from top of block b1 and block b8 is stacked on top of block b4 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "block b3 is unstacked from top of block b9, block b6 is picked up by the hand, from top of block b4, block b8 is unstacked, block b2 is unstacked from top of block b5 and from top of block b7, block b1 is unstacked", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is on block b4, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is on block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b9 is removed from the top of block b8 and placed down, then block b2 is removed from the top of block b6 and placed on top of block b5. Next, block b6 is removed from the top of block b3 and placed down. Block b3 is then removed from the top of block b4 and placed on top of block b9. Finally, block b8 is removed from the top of block b1 and placed on top of block b4, resulting in the current state. In this state, list all possible actions. If there are no actions, write None.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is resting on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is placed on the table, block b6 is stacked on top of block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is resting on block b8, and the hand is empty."}
{"question_id": "f3a5f607-c3a3-4c32-a027-93e2f6104dc9", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked, the hand puts down the block b4, block b1 is unstacked from block b6, block b1 is put down on the table, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b4, block b6 is unstacked from top of block b7, block b6 is put down on the table, block b7 is picked up from the table and on top of block b2, block b8 is stacked to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "block b8 is stacked on top of block b2", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b4 is removed from the top of block b1, then placed on the table, block b1 is then removed from block b6 and placed on the table, next, block b2 is removed from the top of block b5, and stacked on top of block b4, block b6 is then removed from the top of block b7 and placed on the table, block b7 is picked up from the table and placed on top of block b2, and finally, block b8 is stacked to achieve the current state. What is the first action in the sequence that cannot be executed? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "e102c2ad-0a71-4f8d-9bf3-80b831ba4bd6", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: block b4 is unstacked from top of block b1, block b4 is put down on the table, from top of block b6, block b4 is unstacked, the hand puts down the block b1, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b4, block b6 is unstacked from block b7, the hand puts down the block b6, block b7 is picked up and block b7 is stacked on top of block b2. Is the action: block b4 is unstacked from top of block b6 executable at step 3, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial setup, the following sequence of actions is planned for steps 1 through 10: unstack block b4 from the top of block b1, place block b4 on the table, unstack block b4 from the top of block b6, put down block b1, unstack block b2 from the top of block b5, stack block b2 on top of block b4, unstack block b6 from block b7, put down block b6, pick up block b7, and stack block b7 on top of block b2. Is the action of unstacking block b4 from the top of block b6 executable at step 3, True or False?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "ad49c752-1129-4a85-a7b4-0b8706cec76c", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b6 is stacked on top of block b5. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the proposed action is to place block b6 on top of block b5. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "Block b1 is placed on top of block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is positioned on the table, block b5 is stacked on top of block b7, block b6 is situated on the table, block b7 is placed on top of block b1, and the hand is empty."}
{"question_id": "c6af34b5-22be-4e09-ae7e-b48f3b5d5dea", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: from top of block b1, block b4 is unstacked, the hand puts down the block b4, block b1 is unstacked from block b6, block b1 is put down on the table, block b4 is unstacked from block b7, block b2 is stacked on top of block b4, from top of block b7, block b6 is unstacked, the hand puts down the block b6, block b7 is picked up, on top of block b2, block b7 is stacked, block b3 is unstacked from top of block b8, block b3 is stacked on top of block b7, block b1 is picked up by the hand, block b1 is stacked on top of block b3, block b5 is picked up, block b5 is stacked on top of block b1, block b8 is picked up, on top of block b5, block b8 is stacked and block b6 is picked up. Is the action: block b4 is unstacked from block b7 executable at step 5, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: starting from the top of block b1, block b4 is removed and placed down, then block b1 is unstacked from block b6 and put down on the table. Next, block b4 is unstacked from block b7, and block b2 is stacked on top of block b4. From the top of block b7, block b6 is unstacked and placed down, then block b7 is picked up and stacked on top of block b2. Block b3 is then unstacked from the top of block b8 and stacked on top of block b7. Block b1 is picked up and stacked on top of block b3, followed by block b5 being picked up and stacked on top of block b1. Block b8 is then picked up and stacked on top of block b5, and finally, block b6 is picked up. Is the action of unstacking block b4 from block b7 executable at step 5, True or False?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "389639d6-2687-4335-8af1-7c08cc6daede", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b3 is put down, block b4 is unstacked from block b1, block b5 is stacked on top of block b1, block b5 is stacked on top of block b2, block b5 is unstacked from block b2, block b5 is unstacked from top of block b4, from top of block b6, block b7 is unstacked, from top of block b7, block b3 is unstacked, on top of block b2, block b4 is stacked and on top of block b4, block b7 is stacked. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are proposed: block b3 is placed down, block b4 is removed from block b1, block b5 is placed on top of block b1, block b5 is placed on top of block b2, block b5 is removed from block b2, block b5 is removed from the top of block b4, block b7 is removed from the top of block b6, block b3 is removed from the top of block b7, block b4 is placed on top of block b2, and block b7 is placed on top of block b4. Can this sequence be executed, True or False?", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 has no blocks on it, block b2 is positioned on the table, block b3 has no blocks on it, block b3 is stacked on top of block b7, block b4 is positioned on top of block b1, block b5 has no blocks on it, block b5 is placed on top of block b4, block b6 is resting on the table, block b7 is placed on block b6, and the hand is empty."}
{"question_id": "43f7be27-89b7-4d80-ac7f-6907105d4982", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b1 is picked up, block b1 is put down, block b1 is stacked on top of block b3, block b2 is stacked on top of block b4, block b2 is unstacked from top of block b5, block b3 is unstacked from block b8, block b4 is put down, block b5 is picked up by the hand, block b5 is stacked on top of block b1, block b6 is picked up from the table, block b6 is put down, block b6 is unstacked from top of block b7, block b8 is picked up, from top of block b1, block b4 is unstacked, from top of block b2, block b8 is unstacked, from top of block b6, block b1 is unstacked, on top of block b2, block b7 is stacked, on top of block b5, block b8 is stacked and on top of block b7, block b3 is stacked. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned: block b1 will be lifted, then placed down, and then stacked on block b3. Next, block b2 will be stacked on block b4, unstacked from block b5, and block b3 will be unstacked from block b8. Block b4 will be placed down, block b5 will be picked up by the hand, and then stacked on block b1. Block b6 will be lifted from the table, placed down, and unstacked from block b7. Block b8 will be picked up from block b1, block b4 will be unstacked from block b2, block b8 will be unstacked from block b6, block b1 will be unstacked from block b2, block b7 will be stacked on block b5, block b8 will be stacked on block b7, and block b3 will be stacked on block b7. Is it possible to execute it, True or False?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "265df173-57a6-4df5-a6c9-f6e03105bd0b", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1 to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "block b8 is stacked on top of block b1, block b6 is stacked on top of block b2, on top of block b4, block b5 is stacked, block b4 is unstacked from block b3, block b8 is picked up by the hand, block b2 is stacked on top of block b7, on top of block b6, block b7 is stacked, block b1 is unstacked from top of block b7, from top of block b8, block b2 is unstacked, on top of block b8, block b2 is stacked, block b2 is unstacked from block b7, block b3 is unstacked from top of block b8, block b1 is stacked on top of block b8, block b5 is unstacked from top of block b4, on top of block b1, block b5 is stacked, on top of block b1, block b7 is stacked, block b6 is unstacked from block b7, from top of block b3, block b8 is unstacked, block b5 is unstacked from block b2, block b4 is stacked on top of block b8, block b8 is unstacked from top of block b6, block b5 is put down on the table, block b1 is unstacked from top of block b5, on top of block b5, block b6 is stacked, block b7 is picked up from the table, block b2 is stacked on top of block b5, block b6 is unstacked from top of block b1, block b5 is unstacked from block b6, block b8 is stacked on top of block b7, block b7 is unstacked from top of block b3, block b3 is stacked on top of block b7, the hand puts down the block b3, block b5 is unstacked from top of block b1, block b5 is picked up by the hand, block b6 is stacked on top of block b3, block b6 is stacked on top of block b7, block b2 is unstacked from top of block b1, on top of block b8, block b5 is stacked, from top of block b2, block b7 is unstacked, block b3 is unstacked from block b4, block b3 is unstacked from block b1, on top of block b3, block b5 is stacked, block b2 is picked up, block b3 is stacked on top of block b2, block b2 is put down, block b3 is unstacked from top of block b6, block b8 is put down, on top of block b6, block b2 is stacked, block b6 is stacked on top of block b4, from top of block b6, block b1 is unstacked, block b2 is stacked on top of block b4, block b4 is unstacked from top of block b5, block b7 is unstacked from block b8, block b2 is stacked on top of block b3, block b1 is stacked on top of block b7, block b7 is unstacked from block b6, from top of block b2, block b3 is unstacked, block b7 is unstacked from top of block b1, block b6 is put down on the table, block b3 is stacked on top of block b4, block b4 is picked up by the hand, block b5 is unstacked from top of block b8, block b1 is stacked on top of block b4, from top of block b5, block b7 is unstacked, block b1 is put down on the table, block b7 is stacked on top of block b2, block b6 is picked up, block b4 is stacked on top of block b6, block b6 is unstacked from top of block b3, from top of block b7, block b3 is unstacked, block b2 is unstacked from block b5, from top of block b1, block b8 is unstacked, block b4 is stacked on top of block b5, on top of block b2, block b1 is stacked, on top of block b1, block b2 is stacked, block b8 is stacked on top of block b2, block b5 is unstacked from top of block b7, on top of block b6, block b5 is stacked, block b6 is unstacked from top of block b5, from top of block b5, block b8 is unstacked, block b1 is unstacked from top of block b2, from top of block b3, block b5 is unstacked, block b4 is unstacked from block b6, block b8 is unstacked from top of block b4, block b4 is unstacked from top of block b8, block b6 is unstacked from block b4, block b6 is stacked on top of block b1, block b6 is unstacked from top of block b2, block b1 is picked up, on top of block b7, block b4 is stacked, block b3 is stacked on top of block b6, from top of block b3, block b2 is unstacked, block b2 is unstacked from top of block b4, on top of block b6, block b1 is stacked, block b3 is picked up from the table, from top of block b7, block b4 is unstacked, block b3 is stacked on top of block b5, block b4 is unstacked from block b1, on top of block b5, block b1 is stacked, block b8 is stacked on top of block b6, on top of block b8, block b7 is stacked, the hand puts down the block b7, block b7 is stacked on top of block b5, on top of block b7, block b5 is stacked, on top of block b4, block b8 is stacked, block b4 is unstacked from top of block b2, block b1 is unstacked from block b3, block b8 is unstacked from block b2, from top of block b5, block b3 is unstacked, block b8 is stacked on top of block b5, block b1 is unstacked from top of block b4, block b2 is unstacked from top of block b6, from top of block b7, block b8 is unstacked, block b1 is unstacked from block b8, block b6 is stacked on top of block b8, block b1 is stacked on top of block b3, on top of block b3, block b7 is stacked, block b6 is unstacked from block b8, on top of block b1, block b3 is stacked, on top of block b2, block b5 is stacked, block b7 is stacked on top of block b4, from top of block b4, block b7 is unstacked, block b3 is stacked on top of block b8 and on top of block b3, block b8 is stacked", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b4 is removed from the top of block b1 to achieve the current state. In this state, identify all actions that cannot be executed. If there are no such actions, state None.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "2cfb7688-1126-4cb9-9830-71458e6d2cc2", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: from top of block b7, block b3 is unstacked. Is the action: block b3 is unstacked from top of block b7 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: block b3 will be unstacked from the top of block b7. Is the action of unstacking block b3 from the top of block b7 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 has no blocks on it, block b2 is positioned on the table, block b3 has no blocks on it, block b3 is stacked on top of block b7, block b4 is positioned on top of block b1, block b5 has no blocks on it, block b5 is placed on top of block b4, block b6 is resting on the table, block b7 is placed on block b6 and the hand is empty."}
{"question_id": "cf5cbf1c-5416-4dbf-a4e3-73c8a029d288", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b2 is picked up by the hand, block b3 is picked up by the hand, block b3 is unstacked from block b7, block b4 is picked up from the table, block b6 is picked up, from top of block b1, block b4 is unstacked, from top of block b2, block b5 is unstacked, from top of block b4, block b5 is unstacked, from top of block b4, block b7 is unstacked, from top of block b6, block b7 is unstacked, on top of block b1, block b5 is stacked, on top of block b2, block b3 is stacked, on top of block b2, block b5 is stacked, on top of block b3, block b7 is stacked, on top of block b4, block b7 is stacked, on top of block b5, block b6 is stacked, on top of block b6, block b2 is stacked, the hand puts down the block b3 and the hand puts down the block b4. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: the hand will pick up block b2, then block b3, unstack block b3 from block b7, pick up block b4 from the table, pick up block b6 from the top of block b1, unstack block b4 from the top of block b2, unstack block b5 from the top of block b4 (twice), unstack block b7 from the top of block b6, unstack block b7 from the top of block b6, stack block b7 on top of block b1, stack block b5 on top of block b2, stack block b3 on top of block b2, stack block b5 on top of block b3, stack block b7 on top of block b4, stack block b7 on top of block b5, stack block b6 on top of block b6, stack block b2 on top of block b6, and finally, the hand will put down block b3 and block b4. Is this sequence executable, True or False?", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 has no blocks on it, block b2 is positioned on the table, block b3 has no blocks on it, block b3 is stacked on top of block b7, block b4 is positioned on top of block b1, block b5 has no blocks on it, block b5 is placed on top of block b4, block b6 is resting on the table, block b7 is placed on block b6, and the hand is empty."}
{"question_id": "1579d022-e01c-446d-9a8a-67d671b29d68", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: block b9 is unstacked from block b8, the hand puts down the block b9, block b2 is unstacked from block b6, on top of block b5, block b2 is stacked, block b6 is unstacked from top of block b3, block b6 is put down on the table, block b3 is unstacked from block b4, block b3 is stacked on top of block b9, block b8 is unstacked from block b1, on top of block b4, block b8 is stacked, from top of block b5, block b2 is unstacked, block b2 is stacked on top of block b8, block b3 is unstacked from top of block b9, block b3 is stacked on top of block b2, from top of block b7, block b1 is unstacked, on top of block b3, block b1 is stacked, block b7 is picked up, block b7 is stacked on top of block b9 and block b6 is picked up from the table. Is the action: block b6 is put down executable at step 6, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is on block b4, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is on block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: unstack block b9 from block b8, place block b9 on the table, unstack block b2 from block b6 and stack it on top of block b5, unstack block b6 from block b3 and put it down, unstack block b3 from block b4 and stack it on top of block b9, unstack block b8 from block b1 and stack it on top of block b4, unstack block b2 from block b5 and stack it on top of block b8, unstack block b3 from block b9 and stack it on top of block b2, unstack block b1 from block b7 and stack it on top of block b3, pick up block b7 and stack it on top of block b9, and pick up block b6 from the table. Is the action: putting down block b6 executable at step 6, True or False?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is resting on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is placed on the table, block b6 is stacked on top of block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is resting on block b8, and the hand is empty."}
{"question_id": "849f8838-535b-4c5b-bf6e-e7a9aad049bf", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: block b9 is unstacked from top of block b8, the hand puts down the block b9, block b2 is picked up from the table, on top of block b5, block b2 is stacked, block b6 is unstacked from top of block b3, block b6 is put down on the table, block b3 is unstacked from top of block b4, block b3 is stacked on top of block b9, from top of block b1, block b8 is unstacked and on top of block b4, block b8 is stacked. Is the action: block b2 is picked up executable at step 3, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is on block b4, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is on block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial setup, the following sequence of actions is planned for steps 1 through 10: unstack block b9 from block b8, place block b9 on the hand, pick up block b2 from the table, stack block b2 on top of block b5, unstack block b6 from block b3, put block b6 on the table, unstack block b3 from block b4, stack block b3 on top of block b9, unstack block b8 from block b1, and stack block b8 on top of block b4. Is the action of picking up block b2 executable at step 3, True or False?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on top of block b6, block b3 is placed on block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on top of block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is placed on block b8, and the hand is empty."}
{"question_id": "aa667531-99a8-4974-a4c0-002cfacee054", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: on top of block b2, block b6 is stacked, block b3 is put down on the table, from top of block b6, block b2 is unstacked, the hand puts down the block b2, block b6 is picked up by the hand, block b6 is stacked on top of block b3, block b5 is unstacked from block b7, block b5 is stacked on top of block b6, block b7 is unstacked from block b1, the hand puts down the block b7, block b1 is unstacked from block b4, block b1 is stacked on top of block b2, block b4 is picked up, block b4 is stacked on top of block b5, block b1 is unstacked from block b2, block b1 is stacked on top of block b4, block b2 is picked up from the table, block b2 is stacked on top of block b1 and block b7 is picked up from the table. Is the action: block b6 is stacked on top of block b2 executable at step 1, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: block b6 is placed on top of block b2, block b3 is put down on the table, block b2 is removed from the top of block b6, the hand releases block b2, the hand picks up block b6, block b6 is stacked on top of block b3, block b5 is removed from block b7, block b5 is placed on top of block b6, block b7 is removed from block b1, the hand releases block b7, block b1 is removed from block b4, block b1 is stacked on top of block b2, block b4 is picked up, block b4 is stacked on top of block b5, block b1 is removed from block b2, block b1 is stacked on top of block b4, block b2 is picked up from the table, block b2 is stacked on top of block b1, and block b7 is picked up from the table. Is the action: block b6 is stacked on top of block b2 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1 and the hand is empty."}
{"question_id": "cedaaa00-2070-4ceb-837c-c758e8e35e56", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: block b4 is unstacked from block b1, block b4 is put down, block b1 is unstacked from block b6, the hand puts down the block b1, block b2 is unstacked from top of block b5, on top of block b4, block b2 is stacked, block b6 is unstacked from block b7, block b6 is put down, block b7 is picked up, block b7 is stacked on top of block b2, block b3 is unstacked from top of block b8, block b3 is stacked on top of block b7, block b1 is picked up from the table, on top of block b3, block b1 is stacked, block b5 is picked up by the hand, on top of block b1, block b5 is stacked, block b8 is picked up from the table, on top of block b5, block b8 is stacked and block b6 is picked up by the hand. Is the action: block b3 is unstacked from block b8 executable at step 11, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: unstack block b4 from block b1, place block b4 down, unstack block b1 from block b6, put block b1 down, unstack block b2 from the top of block b5, stack block b2 on top of block b4, unstack block b6 from block b7, put block b6 down, pick up block b7, stack block b7 on top of block b2, unstack block b3 from the top of block b8, stack block b3 on top of block b7, pick up block b1 from the table, stack block b1 on top of block b3, pick up block b5 with the hand, stack block b5 on top of block b1, pick up block b8 from the table, stack block b8 on top of block b5, and pick up block b6 with the hand. Is the action of unstacking block b3 from block b8 executable at step 11, True or False?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "8b37a4fa-166d-4c95-827e-4802b78548cf", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked, block b3 is put down, block b5 is unstacked from block b4, on top of block b2, block b5 is stacked, block b4 is unstacked from block b1, the hand puts down the block b4, block b5 is unstacked from block b2, block b5 is stacked on top of block b1, block b7 is unstacked from top of block b6, block b7 is stacked on top of block b4, block b6 is picked up by the hand, block b7 is unstacked from top of block b2, block b2 is picked up from the table, block b2 is stacked on top of block b6, block b3 is picked up, block b3 is stacked on top of block b2, block b7 is unstacked from top of block b4, block b7 is stacked on top of block b3 and block b4 is picked up by the hand to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "block b7 is unstacked from top of block b2", "plan_length": 19, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: starting from the top of block b7, block b3 is removed and placed down, block b5 is removed from block b4, then stacked on top of block b2, block b4 is removed from block b1 and put down by the hand, block b5 is removed from block b2 and stacked on top of block b1, block b7 is removed from the top of block b6 and stacked on top of block b4, block b6 is picked up by the hand, block b7 is removed from the top of block b2, block b2 is picked up from the table and stacked on top of block b6, block b3 is picked up and stacked on top of block b2, block b7 is removed from the top of block b4 and stacked on top of block b3, and finally block b4 is picked up by the hand to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 has no blocks on it, block b2 is positioned on the table, block b3 has no blocks on it, block b3 is stacked on top of block b7, block b4 is positioned on top of block b1, block b5 has no blocks on it, block b5 is placed on top of block b4, block b6 is resting on the table, block b7 is placed on block b6, and the hand is not holding any blocks."}
{"question_id": "4f55d8ba-5778-493b-8290-8931d47e68a9", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "on top of block b2, block b9 is stacked, block b9 is put down on the table, on top of block b5, block b9 is stacked and on top of block b8, block b9 is stacked", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is on block b4, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is on block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: block b9 is removed from the top of block b8 to achieve the current state. In this state, list all possible actions that can be executed. If there are no actions, write None.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is resting on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is placed on the table, block b6 is stacked on top of block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is resting on block b8, and the hand is empty."}
{"question_id": "011d9b35-e682-48d3-a065-27a97245e96e", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: block b3 is unstacked from block b7, block b3 is put down, block b5 is unstacked from block b4, on top of block b2, block b5 is stacked, block b4 is unstacked from top of block b1, block b4 is put down on the table, from top of block b2, block b5 is unstacked, on top of block b1, block b5 is stacked, block b7 is unstacked from block b6 and on top of block b4, block b7 is stacked. Is the action: block b3 is put down executable at step 2, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial setup, the following sequence of actions is planned for steps 1 through 10: unstack block b3 from block b7, place block b3 on the table, unstack block b5 from block b4 and stack it on top of block b2, unstack block b4 from the top of block b1 and put it down on the table, unstack block b5 from the top of block b2 and stack it on top of block b1, and finally unstack block b7 from block b6 and stack it on top of block b4. Is the action of putting block b3 down executable at step 2, True or False?", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 has no blocks on it, block b2 is positioned on the table, block b3 has no blocks on it, block b3 is stacked on top of block b7, block b4 is positioned on top of block b1, block b5 has no blocks on it, block b5 is placed on top of block b4, block b6 is resting on the table, block b7 is placed on block b6, and the hand is currently empty."}
{"question_id": "20ff7c08-d1df-423f-bf9c-40f7ab7c8789", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b4, block b1 is unstacked, the hand puts down the block b9, block b2 is unstacked from top of block b6, on top of block b5, block b2 is stacked, block b6 is unstacked from top of block b3, the hand puts down the block b6, block b3 is unstacked from top of block b4, block b3 is stacked on top of block b9, from top of block b1, block b8 is unstacked and on top of block b4, block b8 is stacked to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "block b1 is unstacked from top of block b4", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is on block b4, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is on block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b1 is removed from the top of block b4, block b9 is placed by the hand, block b2 is removed from the top of block b6, block b2 is then placed on top of block b5, block b6 is removed from the top of block b3, the hand places block b6, block b3 is removed from the top of block b4, block b3 is then stacked on top of block b9, block b8 is removed from the top of block b1 and then placed on top of block b4 to reach the current state. What is the first action in the sequence that cannot be executed? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is resting on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is placed on the table, block b6 is stacked on top of block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is resting on block b8, and the hand is empty."}
{"question_id": "c6c9d0d9-0c82-434e-a4f4-6023380425d2", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: block b3 is unstacked from block b5, the hand puts down the block b3, block b2 is unstacked from top of block b6, block b2 is put down on the table, block b6 is picked up from the table, block b6 is stacked on top of block b3, from top of block b7, block b5 is unstacked, on top of block b6, block b5 is stacked, from top of block b1, block b7 is unstacked and block b7 is put down on the table. Is the action: from top of block b6, block b2 is unstacked executable at step 3, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial setup, the following sequence of actions is planned for steps 1 through 10: unstack block b3 from block b5, place block b3 on the table, unstack block b2 from block b6, put block b2 on the table, pick up block b6 from the table, stack block b6 on top of block b3, unstack block b5 from block b7 and stack it on top of block b6, unstack block b7 from block b1 and put block b7 on the table. Is the action of unstacking block b2 from block b6 executable at step 3, True or False?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1 and the hand is empty."}
{"question_id": "3bdc2c25-bc2c-4931-b117-9499f2c42592", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7 to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "block b4 is unstacked from block b3, block b4 is picked up, block b4 is stacked on top of block b6, on top of block b3, block b1 is stacked, block b4 is stacked on top of block b7, from top of block b4, block b3 is unstacked, block b1 is unstacked from block b4, block b1 is unstacked from top of block b5, block b6 is picked up, block b3 is unstacked from top of block b1, from top of block b5, block b3 is unstacked, block b3 is stacked on top of block b1, block b6 is put down, block b2 is stacked on top of block b4, block b4 is unstacked from top of block b5, from top of block b2, block b7 is unstacked, block b5 is unstacked from block b6, block b3 is unstacked from block b2, block b3 is stacked on top of block b4, from top of block b7, block b6 is unstacked, block b4 is unstacked from top of block b2, block b5 is stacked on top of block b4, block b7 is unstacked from top of block b1, block b4 is put down on the table, block b6 is stacked on top of block b5, block b7 is unstacked from top of block b3, block b5 is stacked on top of block b3, on top of block b3, block b4 is stacked, on top of block b2, block b6 is stacked, on top of block b1, block b5 is stacked, the hand puts down the block b5, block b4 is unstacked from block b1, block b5 is unstacked from block b3, on top of block b7, block b1 is stacked, on top of block b7, block b5 is stacked, block b7 is unstacked from top of block b4, from top of block b5, block b2 is unstacked, block b7 is stacked on top of block b3, from top of block b6, block b2 is unstacked, block b5 is unstacked from top of block b1, on top of block b1, block b7 is stacked, block b7 is unstacked from block b6, block b6 is unstacked from top of block b3, block b4 is unstacked from top of block b6, block b3 is unstacked from block b7, block b2 is stacked on top of block b6, block b5 is unstacked from block b2, on top of block b5, block b2 is stacked, on top of block b2, block b5 is stacked, block b6 is unstacked from top of block b2, block b7 is unstacked from top of block b5, on top of block b5, block b4 is stacked, block b1 is stacked on top of block b6, block b3 is picked up from the table, block b1 is put down, block b1 is unstacked from block b2, block b3 is unstacked from top of block b6, block b2 is picked up, block b1 is picked up by the hand, block b1 is stacked on top of block b2, block b1 is unstacked from top of block b3, block b4 is stacked on top of block b1, on top of block b2, block b4 is stacked, from top of block b4, block b2 is unstacked, from top of block b7, block b2 is unstacked, block b2 is unstacked from block b1, block b5 is picked up by the hand, block b2 is stacked on top of block b3, on top of block b6, block b7 is stacked, block b7 is stacked on top of block b4, from top of block b4, block b5 is unstacked, block b7 is put down on the table, block b5 is stacked on top of block b6, on top of block b5, block b1 is stacked, block b7 is stacked on top of block b2, block b2 is stacked on top of block b1, block b6 is unstacked from top of block b5, block b2 is put down, block b6 is unstacked from top of block b1, block b6 is stacked on top of block b1, block b7 is picked up by the hand, block b5 is unstacked from top of block b7, block b1 is unstacked from top of block b6, on top of block b4, block b1 is stacked, on top of block b6, block b3 is stacked, block b6 is unstacked from top of block b4, from top of block b3, block b2 is unstacked, on top of block b4, block b6 is stacked, block b6 is stacked on top of block b3, block b6 is stacked on top of block b7, block b1 is unstacked from block b7, block b2 is stacked on top of block b7, on top of block b5, block b7 is stacked and block b4 is unstacked from top of block b7", "plan_length": 1, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: block b3 is removed from the top of block b7 to achieve the current state. In this state, identify all actions that cannot be executed. If there are no such actions, state None.", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 has no blocks on it, block b2 is positioned on the table, block b3 has no blocks on it, block b3 is stacked on top of block b7, block b4 is positioned on top of block b1, block b5 has no blocks on it, block b5 is placed on top of block b4, block b6 is resting on the table, block b7 is placed on block b6, and the hand is currently empty."}
{"question_id": "67508e02-80db-4ffb-b921-9e61a7ce80d6", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked, the hand puts down the block b3, from top of block b6, block b2 is unstacked, block b2 is put down on the table, on top of block b7, block b1 is stacked, block b6 is stacked on top of block b3, from top of block b7, block b5 is unstacked, on top of block b6, block b5 is stacked, block b7 is unstacked from top of block b1 and block b7 is put down to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "on top of block b7, block b1 is stacked", "plan_length": 10, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b5 and placed down, block b2 is unstacked from the top of block b6 and put down on the table, block b1 is stacked on top of block b7, block b6 is stacked on top of block b3, block b5 is removed from the top of block b7 and stacked on top of block b6, and finally, block b7 is unstacked from the top of block b1 and put down to reach the current state. What is the first action in the sequence that cannot be executed? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1 and the hand is empty."}
{"question_id": "ab494f9f-4b9b-492e-b106-bfbc54832359", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, the hand puts down the block b3, block b2 is unstacked from block b6, the hand puts down the block b2, block b6 is picked up, block b6 is stacked on top of block b3, block b5 is unstacked from top of block b7, on top of block b6, block b5 is stacked, block b7 is unstacked from top of block b1, block b7 is put down, block b1 is unstacked from top of block b4, on top of block b2, block b1 is stacked, block b4 is picked up from the table, block b4 is stacked on top of block b5, from top of block b2, block b1 is unstacked, block b1 is stacked on top of block b4, block b2 is picked up, on top of block b1, block b2 is stacked and block b7 is picked up to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "None", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from block b5, the hand releases block b3, block b2 is removed from block b6, the hand releases block b2, block b6 is picked up, block b6 is placed on top of block b3, block b5 is removed from the top of block b7 and placed on top of block b6, block b7 is removed from the top of block b1 and put down, block b1 is removed from the top of block b4 and placed on top of block b2, block b4 is picked up from the table and placed on top of block b5, block b1 is removed from the top of block b2 and placed on top of block b4, block b2 is picked up and placed on top of block b1, and block b7 is picked up to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1 and the hand is empty."}
{"question_id": "1af3d8fe-2b37-4685-a6e0-2452a9276c7d", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on top of block b3, block b7 is stacked, the hand puts down the block b3, from top of block b4, block b5 is unstacked, on top of block b2, block b5 is stacked, block b4 is unstacked from top of block b1, the hand puts down the block b4, from top of block b2, block b5 is unstacked, block b5 is stacked on top of block b1, from top of block b6, block b7 is unstacked and on top of block b4, block b7 is stacked to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "block b7 is stacked on top of block b3", "plan_length": 10, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b7 is placed on top of block b3, the hand releases block b3, block b5 is removed from the top of block b4, block b5 is placed on top of block b2, block b4 is removed from the top of block b1, the hand releases block b4, block b5 is removed from the top of block b2, block b5 is placed on top of block b1, block b7 is removed from the top of block b6, and block b7 is placed on top of block b4 to reach the current state. What is the first action in the sequence that cannot be executed? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 has no blocks on it, block b2 is positioned on the table, block b3 has no blocks on it, block b3 is stacked on top of block b7, block b4 is positioned on top of block b1, block b5 has no blocks on it, block b5 is placed on top of block b4, block b6 is resting on the table, block b7 is placed on block b6, and the hand is empty."}
{"question_id": "ced306c1-af4f-404b-9070-310b5187c6a6", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "on top of block b5, block b3 is stacked, block b3 is stacked on top of block b2, block b3 is put down on the table and on top of block b7, block b3 is stacked", "plan_length": 1, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: block b3 is removed from the top of block b7 to achieve the current state. In this state, list all possible actions that can be executed. If there are no actions, write None.", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 has no blocks on it, block b2 is positioned on the table, block b3 has no blocks on it, block b3 is stacked on top of block b7, block b4 is positioned on top of block b1, block b5 has no blocks on it, block b5 is placed on top of block b4, block b6 is resting on the table, block b7 is placed on block b6, and the hand is currently empty."}
{"question_id": "c1ca9c40-12a7-400e-9b3a-e6e57011b14d", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: block b4 is unstacked from block b1, block b4 is put down on the table, block b1 is unstacked from block b6, block b1 is put down on the table, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b4, block b6 is unstacked from top of block b7, the hand puts down the block b6, block b7 is picked up by the hand and block b7 is stacked on top of block b2. Is the action: the hand puts down the block b4 executable at step 2, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial setup, the following sequence of actions is planned for steps 1 through 10: unstack block b4 from block b1, place block b4 on the table, unstack block b1 from block b6, place block b1 on the table, unstack block b2 from the top of block b5, stack block b2 on top of block b4, unstack block b6 from the top of block b7, put block b6 down, pick up block b7 with the hand, and stack block b7 on top of block b2. Is the action: the hand puts down block b4 executable at step 2, True or False?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "8835bce7-c9f7-4b7f-8057-a37423081e26", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: block b9 is unstacked from top of block b8. Is the action: from top of block b8, block b9 is unstacked executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is on block b4, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is on block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: block b9 is to be removed from the top of block b8. Is the action of unstacking block b9 from the top of block b8 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is resting on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is placed on the table, block b6 is stacked on top of block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is resting on block b8, and the hand is empty."}
{"question_id": "76a8966c-040b-4c1a-b309-e07bb64d6410", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b1 is stacked on top of block b3, block b1 is unstacked from top of block b7, block b2 is unstacked from block b6, block b2 is unstacked from top of block b5, block b3 is stacked on top of block b9, block b3 is unstacked from block b4, block b3 is unstacked from top of block b9, block b6 is picked up from the table, block b7 is picked up from the table, block b7 is stacked on top of block b9, block b9 is put down, from top of block b1, block b8 is unstacked, from top of block b3, block b6 is unstacked, from top of block b6, block b1 is unstacked, from top of block b8, block b9 is unstacked, on top of block b2, block b3 is stacked, on top of block b4, block b8 is stacked, on top of block b5, block b2 is stacked and on top of block b8, block b2 is stacked. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is on block b4, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is on block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: block b1 will be placed on top of block b3, block b1 will be removed from the top of block b7, block b2 will be removed from block b6, block b2 will be removed from the top of block b5, block b3 will be placed on top of block b9, block b3 will be removed from block b4, block b3 will be removed from the top of block b9, block b6 will be lifted from the table, block b7 will be lifted from the table, block b7 will be placed on top of block b9, block b9 will be put down, block b8 will be removed from the top of block b1, block b6 will be removed from the top of block b3, block b1 will be removed from the top of block b6, block b9 will be removed from the top of block b8, block b3 will be placed on top of block b2, block b8 will be placed on top of block b4, block b2 will be placed on top of block b5, and block b2 will be placed on top of block b8. Is it possible to execute this sequence, True or False?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b7, block b2 has no blocks on it, block b2 is positioned on top of block b6, block b3 is placed on block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on top of block b3, block b7 is resting on the table, block b8 is stacked on top of block b1, block b9 has no blocks on it, block b9 is placed on block b8, and the hand is empty."}
{"question_id": "8a80f2da-67ab-4ce0-8c58-84cc508ed35c", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b5, block b3 is put down, from top of block b6, block b2 is unstacked, block b2 is put down on the table, block b6 is picked up from the table, on top of block b3, block b6 is stacked, from top of block b7, block b5 is unstacked, on top of block b6, block b5 is stacked, block b7 is unstacked from block b1 and the hand puts down the block b7 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "from top of block b6, block b5 is unstacked, block b7 is picked up by the hand, from top of block b4, block b1 is unstacked and block b2 is picked up from the table", "plan_length": 10, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from the top of block b5, then placed down, block b2 is removed from the top of block b6 and placed on the table, block b6 is picked up from the table and stacked on top of block b3, block b5 is removed from the top of block b7 and stacked on top of block b6, and block b7 is removed from block b1 and placed down by the hand to reach the current state. In this state, list all possible actions that can be executed. If there are no actions, write None.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1 and the hand is empty."}
{"question_id": "1e5da83e-096a-4210-b132-90ff1e70946d", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b4 is unstacked from top of block b1. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: unstack block b4 from the top of block b1. Is the execution of this action feasible, True or False?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "7425dee2-f609-4d95-90b2-13594b83e2aa", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked, block b3 is put down, block b5 is unstacked from block b4, on top of block b2, block b5 is stacked, block b4 is unstacked from block b1, block b4 is put down, from top of block b2, block b5 is unstacked, on top of block b1, block b5 is stacked, block b7 is unstacked from top of block b6, on top of block b4, block b7 is stacked, block b6 is picked up by the hand, on top of block b5, block b6 is stacked, block b2 is picked up from the table, block b2 is stacked on top of block b6, block b3 is picked up from the table, block b3 is stacked on top of block b2, block b7 is unstacked from top of block b4, block b7 is stacked on top of block b3 and block b4 is picked up by the hand to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "block b4 is unstacked from top of block b3, block b4 is picked up from the table, on top of block b6, block b4 is stacked, on top of block b3, block b1 is stacked, from top of block b4, block b3 is unstacked, block b1 is unstacked from block b4, block b1 is unstacked from block b5, block b6 is picked up, from top of block b1, block b3 is unstacked, block b3 is unstacked from block b5, on top of block b1, block b3 is stacked, block b6 is put down on the table, on top of block b4, block b2 is stacked, from top of block b5, block b4 is unstacked, block b7 is unstacked from top of block b2, block b5 is unstacked from top of block b6, block b3 is unstacked from block b2, on top of block b4, block b3 is stacked, from top of block b7, block b6 is unstacked, from top of block b2, block b4 is unstacked, block b5 is stacked on top of block b4, block b7 is unstacked from top of block b1, on top of block b5, block b6 is stacked, from top of block b3, block b7 is unstacked, block b3 is stacked on top of block b5, on top of block b3, block b5 is stacked, block b4 is stacked on top of block b3, on top of block b2, block b6 is stacked, on top of block b1, block b5 is stacked, the hand puts down the block b5, from top of block b1, block b4 is unstacked, block b5 is unstacked from top of block b3, on top of block b2, block b3 is stacked, on top of block b7, block b1 is stacked, on top of block b7, block b5 is stacked, block b7 is unstacked from top of block b4, from top of block b5, block b2 is unstacked, on top of block b3, block b7 is stacked, from top of block b6, block b2 is unstacked, block b5 is unstacked from top of block b1, block b7 is stacked on top of block b1, block b7 is unstacked from top of block b6, block b6 is unstacked from block b3, block b4 is unstacked from block b6, block b3 is unstacked from top of block b7, block b2 is stacked on top of block b6, from top of block b2, block b5 is unstacked, block b2 is stacked on top of block b5, block b5 is stacked on top of block b2, from top of block b2, block b6 is unstacked, block b7 is unstacked from block b5, on top of block b5, block b4 is stacked, on top of block b6, block b1 is stacked, the hand puts down the block b3, block b3 is picked up from the table, block b1 is put down on the table, block b1 is unstacked from block b2, from top of block b6, block b3 is unstacked, block b2 is picked up from the table, block b1 is picked up by the hand, on top of block b2, block b1 is stacked, block b1 is unstacked from block b3, on top of block b1, block b4 is stacked, block b4 is stacked on top of block b2, block b2 is unstacked from top of block b4, from top of block b7, block b2 is unstacked, from top of block b1, block b2 is unstacked, block b5 is picked up from the table, block b2 is stacked on top of block b3, block b7 is stacked on top of block b6, on top of block b4, block b7 is stacked, block b5 is unstacked from block b4, the hand puts down the block b7, on top of block b6, block b5 is stacked, on top of block b5, block b1 is stacked, on top of block b2, block b7 is stacked, on top of block b1, block b2 is stacked, block b6 is unstacked from top of block b5, block b2 is put down, block b6 is unstacked from top of block b1, on top of block b1, block b6 is stacked, block b7 is picked up, block b5 is unstacked from block b7, from top of block b6, block b1 is unstacked, block b1 is stacked on top of block b4, block b3 is stacked on top of block b6, block b6 is unstacked from top of block b4, from top of block b3, block b2 is unstacked, on top of block b4, block b6 is stacked, on top of block b3, block b6 is stacked, block b6 is stacked on top of block b7, from top of block b7, block b1 is unstacked, block b3 is stacked on top of block b7, block b2 is stacked on top of block b7, block b7 is stacked on top of block b5 and block b4 is unstacked from top of block b7", "plan_length": 19, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b3 is removed from the top of block b7 and placed down, then block b5 is unstacked from block b4 and placed on top of block b2. Next, block b4 is unstacked from block b1 and put down. Block b5 is then unstacked from block b2 and stacked on top of block b1. Block b7 is unstacked from the top of block b6 and stacked on top of block b4. Block b6 is picked up and stacked on top of block b5. Block b2 is picked up from the table and stacked on top of block b6, followed by block b3 being picked up from the table and stacked on top of block b2. Block b7 is then unstacked from block b4 and stacked on top of block b3. Finally, block b4 is picked up by the hand to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 has no blocks on it, block b2 is positioned on the table, block b3 has no blocks on it, block b3 is stacked on top of block b7, block b4 is positioned on top of block b1, block b5 has no blocks on it, block b5 is placed on top of block b4, block b6 is resting on the table, block b7 is placed on block b6, and the hand is currently empty."}
{"question_id": "853377a0-5755-4e3c-b6b0-b56256e53828", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: block b6 is unstacked from block b4. Is the action: from top of block b4, block b6 is unstacked executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: block b6 is removed from the top of block b4. Is the action: unstack block b6 from the top of block b4 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1 and the hand is empty."}
{"question_id": "d68b064c-c70e-4e1c-b8bd-ea8ed700ad43", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b3 is unstacked from block b5. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: unstack block b3 from block b5. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1 and the hand is empty."}
{"question_id": "c9fe218b-f586-4173-be47-e27fb60bdcf5", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b2 is put down, block b2 is unstacked from block b6, block b3 is put down, block b3 is unstacked from block b5, block b6 is picked up by the hand, block b6 is stacked on top of block b3, from top of block b1, block b7 is unstacked, from top of block b7, block b5 is unstacked, on top of block b6, block b5 is stacked and the hand puts down the block b7. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are proposed: block b2 is placed down, block b2 is removed from block b6, block b3 is placed down, block b3 is removed from block b5, block b6 is grasped by the hand, block b6 is placed on top of block b3, block b7 is removed from the top of block b1, block b5 is removed from the top of block b7, block b5 is placed on top of block b6, and the hand releases block b7. Is it possible to execute this sequence, True or False?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1 and the hand is empty."}
{"question_id": "6b1a9dad-0837-48eb-b50a-489c2e3d6bcd", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b1 is put down, block b1 is unstacked from top of block b6, block b2 is unstacked from top of block b5, block b4 is put down on the table, block b7 is picked up by the hand, from top of block b1, block b4 is unstacked, from top of block b4, block b3 is unstacked, from top of block b7, block b6 is unstacked, on top of block b2, block b7 is stacked and the hand puts down the block b6. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on block b1, block b5 is located on the table, block b6 is on block b7, block b7 is on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: block b1 is placed down, block b1 is removed from the top of block b6, block b2 is removed from the top of block b5, block b4 is placed on the table, block b7 is picked up by the hand from the top of block b1, block b4 is removed from the top of block b1, block b3 is removed from the top of block b4, block b6 is removed from the top of block b7, block b7 is stacked on top of block b2, and the hand releases block b6. Is it possible to execute this sequence, True or False?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "57d8f9c2-8c15-4dc3-9ab0-1f2dbc0f69ee", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "on top of block b6, block b3 is stacked, block b3 is unstacked from block b5, from top of block b5, block b2 is unstacked, from top of block b4, block b2 is unstacked, on top of block b3, block b2 is stacked, on top of block b2, block b5 is stacked, block b5 is unstacked from top of block b6, block b4 is stacked on top of block b2, block b7 is put down, block b6 is unstacked from top of block b5, on top of block b1, block b2 is stacked, on top of block b2, block b7 is stacked, block b4 is stacked on top of block b3, block b5 is unstacked from block b2, block b7 is unstacked from top of block b2, block b4 is unstacked from top of block b1, block b4 is put down, block b7 is unstacked from block b4, from top of block b7, block b2 is unstacked, block b2 is unstacked from block b1, on top of block b7, block b3 is stacked, on top of block b4, block b6 is stacked, block b3 is stacked on top of block b1, from top of block b2, block b3 is unstacked, block b1 is unstacked from block b2, block b2 is unstacked from block b3, block b6 is picked up, block b7 is picked up, block b1 is stacked on top of block b6, on top of block b2, block b6 is stacked, block b5 is picked up from the table, block b7 is unstacked from top of block b3, block b1 is unstacked from top of block b3, on top of block b3, block b6 is stacked, block b4 is unstacked from top of block b6, block b1 is unstacked from top of block b4, block b2 is put down on the table, block b2 is picked up, block b5 is unstacked from block b1, block b4 is picked up from the table, block b1 is stacked on top of block b7, block b1 is stacked on top of block b3, from top of block b2, block b4 is unstacked, on top of block b7, block b6 is stacked, block b4 is stacked on top of block b6, on top of block b4, block b7 is stacked, on top of block b5, block b2 is stacked, from top of block b5, block b4 is unstacked, from top of block b3, block b5 is unstacked, from top of block b6, block b7 is unstacked, the hand puts down the block b6, on top of block b7, block b2 is stacked, block b4 is stacked on top of block b7, block b4 is unstacked from block b7, on top of block b6, block b2 is stacked, block b3 is picked up, from top of block b6, block b1 is unstacked, block b3 is unstacked from block b7, block b5 is unstacked from top of block b4, block b4 is stacked on top of block b5, block b6 is unstacked from block b1, on top of block b2, block b1 is stacked, on top of block b7, block b5 is stacked, from top of block b6, block b3 is unstacked, on top of block b3, block b7 is stacked, block b7 is unstacked from top of block b5, from top of block b6, block b2 is unstacked, block b5 is stacked on top of block b1, block b2 is stacked on top of block b4, block b1 is unstacked from top of block b7, block b1 is stacked on top of block b4, on top of block b1, block b4 is stacked, block b6 is unstacked from top of block b7, block b1 is stacked on top of block b5, on top of block b4, block b5 is stacked, block b1 is unstacked from top of block b5, block b6 is unstacked from block b2, block b5 is put down on the table, on top of block b4, block b3 is stacked, block b1 is picked up from the table, block b4 is unstacked from block b3, on top of block b6, block b7 is stacked, block b5 is stacked on top of block b3, block b1 is put down on the table, from top of block b1, block b3 is unstacked, block b6 is stacked on top of block b5, from top of block b4, block b3 is unstacked, block b7 is unstacked from top of block b1, on top of block b5, block b7 is stacked, block b5 is unstacked from block b7, on top of block b6, block b5 is stacked, from top of block b4, block b6 is unstacked, block b6 is unstacked from top of block b3, block b7 is stacked on top of block b1 and block b6 is stacked on top of block b1", "plan_length": 1, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Starting from the initial condition, the following steps are taken: block b3 is removed from the top of block b5 to achieve the current state. In this state, identify all actions that cannot be executed. If there are no such actions, list None.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1 and the hand is empty."}
{"question_id": "aec904c6-2704-4afe-9f07-ae2edceb7280", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b1 is stacked on top of block b2, block b2 is picked up, block b2 is put down, block b2 is stacked on top of block b1, block b2 is unstacked from top of block b6, block b3 is unstacked from block b5, block b4 is picked up from the table, block b5 is unstacked from block b7, block b6 is picked up from the table, block b6 is stacked on top of block b3, block b7 is picked up by the hand, block b7 is put down, block b7 is unstacked from block b1, from top of block b2, block b1 is unstacked, from top of block b4, block b1 is unstacked, on top of block b4, block b1 is stacked, on top of block b5, block b4 is stacked, on top of block b6, block b5 is stacked and the hand puts down the block b3. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: place block b1 on top of block b2, lift block b2, put block b2 down, place block b2 on top of block b1, remove block b2 from the top of block b6, unstack block b3 from block b5, pick up block b4 from the table, unstack block b5 from block b7, lift block b6 from the table, place block b6 on top of block b3, pick up block b7 with the hand, put block b7 down, unstack block b7 from block b1, unstack block b1 from the top of block b2, unstack block b1 from the top of block b4, place block b1 on top of block b4, stack block b4 on top of block b5, stack block b5 on top of block b6, and finally, put down block b3 with the hand. Is it possible to execute this sequence, True or False?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1 and the hand is empty."}
{"question_id": "04a3936f-04b8-4281-80fc-43f2742292e6", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: block b3 is unstacked from block b5. Is the action: from top of block b5, block b3 is unstacked executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: block b3 is removed from the top of block b5. Is the action: unstacking block b3 from the top of block b5 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1 and the hand is empty."}
{"question_id": "eaeed21b-7854-4752-a5f9-da3e1cb71208", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b3, block b6 is unstacked to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "from top of block b3, block b6 is unstacked", "plan_length": 1, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: starting from the top of block b3, block b6 is removed to achieve the current state. What is the first action in the sequence that cannot be executed? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1, and the hand is empty."}
{"question_id": "37726036-5f4d-44cd-9293-11661476971f", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b2 is picked up, block b2 is stacked on top of block b6, block b3 is picked up from the table, block b3 is unstacked from block b7, block b4 is picked up from the table, block b4 is put down, block b5 is stacked on top of block b1, block b5 is unstacked from block b2, block b6 is stacked on top of block b5, block b7 is stacked on top of block b3, block b7 is unstacked from top of block b5, block b7 is unstacked from top of block b6, from top of block b1, block b4 is unstacked, from top of block b4, block b5 is unstacked, from top of block b4, block b7 is unstacked, on top of block b2, block b3 is stacked, on top of block b2, block b5 is stacked, on top of block b4, block b7 is stacked and the hand puts down the block b3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: block b2 is lifted, block b2 is placed on top of block b6, block b3 is picked up from the table, block b3 is separated from block b7, block b4 is lifted from the table, block b4 is placed down, block b5 is stacked on top of block b1, block b5 is separated from block b2, block b6 is stacked on top of block b5, block b7 is stacked on top of block b3, block b7 is separated from the top of block b5, block b7 is separated from the top of block b6, block b4 is separated from the top of block b1, block b5 is separated from the top of block b4, block b7 is separated from the top of block b4, block b3 is stacked on top of block b2, block b5 is stacked on top of block b2, block b7 is stacked on top of block b4, and the hand releases block b3. Is it possible to execute it, True or False?", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 has no blocks on it, block b2 is positioned on the table, block b3 has no blocks on it, block b3 is stacked on top of block b7, block b4 is positioned on top of block b1, block b5 has no blocks on it, block b5 is placed on top of block b4, block b6 is resting on the table, block b7 is placed on block b6, and the hand is currently empty."}
{"question_id": "b24890bf-b51a-428b-a42f-4c09a3095b17", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: from top of block b8, block b9 is unstacked, the hand puts down the block b9, from top of block b6, block b2 is unstacked, block b2 is stacked on top of block b5, block b6 is unstacked from block b3, block b6 is put down, block b3 is unstacked from block b4, block b3 is stacked on top of block b9, block b8 is unstacked from block b1 and block b8 is stacked on top of block b4. Is the action: the hand puts down the block b6 executable at step 6, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is on block b4, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is on block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: unstack block b9 from the top of block b8, place block b9 on the table, unstack block b2 from the top of block b6, stack block b2 on top of block b5, unstack block b6 from block b3, put block b6 down, unstack block b3 from block b4, stack block b3 on top of block b9, unstack block b8 from block b1, and stack block b8 on top of block b4. Is the action of putting down block b6 executable at step 6, True or False?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is resting on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is placed on the table, block b6 is stacked on top of block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is resting on block b8, and the hand is empty."}
{"question_id": "cf7cc49f-4f6e-4a7e-b22a-a5cfad9ed4e1", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b2 is put down on the table, block b2 is unstacked from block b7, block b5 is stacked on top of block b6, block b6 is picked up, block b6 is stacked on top of block b3, block b7 is put down, from top of block b5, block b3 is unstacked, from top of block b6, block b2 is unstacked, from top of block b7, block b5 is unstacked and the hand puts down the block b3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are intended to be executed: block b2 is placed on the table, block b2 is removed from block b7, block b5 is placed on top of block b6, block b6 is lifted, block b6 is placed on top of block b3, block b7 is put down, block b3 is removed from the top of block b5, block b2 is removed from the top of block b6, block b5 is removed from the top of block b7, and the hand releases block b3. Is it possible to execute it, True or False?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1, and the hand is empty."}
{"question_id": "e1e82439-d218-47d9-b059-41a7b0c86ae2", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from top of block b3, block b7 is unstacked. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: unstack block b7 from the top of block b3. Is the execution of this action feasible, True or False?", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 has no blocks on it, block b2 is positioned on the table, block b3 has no blocks on it, block b3 is stacked on top of block b7, block b4 is positioned on top of block b1, block b5 has no blocks on it, block b5 is placed on top of block b4, block b6 is resting on the table, block b7 is placed on block b6, and the hand is currently empty."}
{"question_id": "72a123f8-749d-44f7-9fcb-a3b2bb3794b2", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b5, the hand puts down the block b3, block b2 is unstacked from block b6, block b2 is put down on the table, block b6 is picked up by the hand, block b6 is stacked on top of block b3, from top of block b7, block b5 is unstacked, on top of block b6, block b5 is stacked, from top of block b1, block b7 is unstacked and block b7 is put down to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "on top of block b6, block b3 is stacked, from top of block b5, block b3 is unstacked, from top of block b5, block b2 is unstacked, from top of block b4, block b2 is unstacked, block b2 is stacked on top of block b3, on top of block b2, block b5 is stacked, on top of block b2, block b4 is stacked, the hand puts down the block b7, block b6 is unstacked from top of block b5, on top of block b1, block b2 is stacked, block b7 is stacked on top of block b2, block b4 is stacked on top of block b3, block b5 is unstacked from block b2, block b7 is unstacked from block b2, block b4 is unstacked from top of block b1, the hand puts down the block b4, from top of block b4, block b7 is unstacked, block b2 is unstacked from top of block b7, block b3 is stacked on top of block b5, block b2 is unstacked from top of block b1, block b3 is stacked on top of block b7, block b3 is stacked on top of block b2, block b6 is stacked on top of block b4, block b3 is stacked on top of block b1, from top of block b2, block b3 is unstacked, from top of block b2, block b1 is unstacked, block b2 is unstacked from top of block b3, block b6 is picked up from the table, on top of block b6, block b1 is stacked, on top of block b2, block b6 is stacked, block b5 is picked up from the table, block b7 is unstacked from block b3, from top of block b3, block b1 is unstacked, on top of block b3, block b6 is stacked, from top of block b6, block b4 is unstacked, block b2 is put down on the table, from top of block b1, block b5 is unstacked, block b4 is picked up, on top of block b7, block b1 is stacked, on top of block b3, block b1 is stacked, block b4 is unstacked from block b2, block b6 is stacked on top of block b7, block b4 is stacked on top of block b6, block b7 is stacked on top of block b4, on top of block b5, block b2 is stacked, block b4 is unstacked from block b5, from top of block b3, block b5 is unstacked, from top of block b6, block b7 is unstacked, the hand puts down the block b6, block b2 is stacked on top of block b7, on top of block b7, block b4 is stacked, block b4 is unstacked from block b7, block b2 is stacked on top of block b6, block b3 is picked up, block b1 is unstacked from block b6, block b3 is unstacked from top of block b7, block b5 is unstacked from top of block b4, on top of block b5, block b4 is stacked, block b6 is unstacked from top of block b1, block b1 is stacked on top of block b2, block b5 is stacked on top of block b7, from top of block b6, block b3 is unstacked, block b7 is stacked on top of block b3, block b7 is unstacked from top of block b5, from top of block b6, block b2 is unstacked, block b5 is stacked on top of block b1, on top of block b4, block b2 is stacked, block b1 is unstacked from block b7, block b1 is stacked on top of block b4, on top of block b1, block b4 is stacked, block b6 is unstacked from top of block b7, on top of block b5, block b1 is stacked, on top of block b4, block b5 is stacked, block b1 is unstacked from top of block b5, block b6 is unstacked from top of block b2, the hand puts down the block b5, on top of block b4, block b3 is stacked, block b1 is picked up by the hand, block b4 is unstacked from block b3, block b7 is stacked on top of block b6, block b5 is stacked on top of block b3, the hand puts down the block b1, block b3 is unstacked from block b1, block b6 is stacked on top of block b5, block b3 is unstacked from top of block b4, block b7 is unstacked from block b1, the hand puts down the block b3, block b7 is stacked on top of block b5, block b5 is unstacked from top of block b7, on top of block b6, block b5 is stacked, block b6 is unstacked from top of block b4, block b6 is unstacked from block b3, on top of block b1, block b7 is stacked and block b6 is stacked on top of block b1", "plan_length": 10, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b3 is removed from the top of block b5, the hand releases block b3, block b2 is removed from block b6, block b2 is placed on the table, the hand picks up block b6, block b6 is placed on top of block b3, block b5 is removed from the top of block b7, block b5 is stacked on top of block b6, block b7 is removed from the top of block b1, and block b7 is placed down to reach the current state. In this state, list all actions that cannot be executed. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1 and the hand is empty."}
{"question_id": "bebbeb0a-89a7-43cd-8a20-9801cd4ae2c9", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b9 is unstacked from top of block b8. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is on block b4, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is on block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: unstack block b9 from the top of block b8. Is the execution of this action possible, True or False?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is resting on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is placed on the table, block b6 is stacked on top of block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is resting on block b8, and the hand is empty."}
{"question_id": "c17a0b25-7244-42be-b18e-5066dcbaa579", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, block b3 is put down on the table, block b2 is unstacked from top of block b6, block b2 is put down, block b6 is picked up from the table, on top of block b3, block b6 is stacked, block b5 is unstacked from block b7, on top of block b6, block b5 is stacked, block b7 is unstacked from block b1, the hand puts down the block b7, block b1 is unstacked from top of block b4, block b1 is stacked on top of block b2, block b4 is picked up from the table, block b4 is stacked on top of block b5, block b1 is unstacked from top of block b2, block b1 is stacked on top of block b4, block b2 is picked up, on top of block b1, block b2 is stacked and block b7 is picked up to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "the hand puts down the block b7 and on top of block b2, block b7 is stacked", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b3 is removed from block b5, then placed on the table, block b2 is removed from the top of block b6 and put down, block b6 is picked up from the table and placed on top of block b3, block b5 is removed from block b7 and stacked on top of block b6, block b7 is removed from block b1 and put down, block b1 is removed from the top of block b4 and stacked on top of block b2, block b4 is picked up from the table and stacked on top of block b5, block b1 is removed from the top of block b2 and stacked on top of block b4, block b2 is picked up and stacked on top of block b1, and finally block b7 is picked up to reach the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on top of block b7, block b6 is on the table, block b7 is placed on block b1 and the hand is empty."}
