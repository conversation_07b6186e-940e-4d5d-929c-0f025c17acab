{"question_id": "a8bc8e79-e972-46e1-86fb-5fbc57cfa507", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_3", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: block b1 is not located on the table, block b2 is not located on the table, block b3 is not located on the table, block b4 is not located on the table and block b6 is not on the table?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is on the table, block b6 is placed on top of block b7, block b7 is located on the table, block b8 is on the table and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b4 is removed from block b1 to achieve the current state. In this state, are the following properties that involve negations True or False: block b1 is not on the table, block b2 is not on the table, block b3 is not on the table, block b4 is not on the table, and block b6 is not on the table?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b6, block b2 has no blocks on it, block b2 is stacked on block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is resting on the table, block b6 is placed on top of block b7, block b7 is situated on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "0e4f3522-b3ba-4674-a44b-f87e58c3108a", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b5, the hand puts down the block b3, block b2 is unstacked from top of block b6, block b2 is put down, block b6 is picked up, on top of block b3, block b6 is stacked, block b5 is unstacked from top of block b7, on top of block b6, block b5 is stacked, block b7 is unstacked from block b1 and block b7 is put down to reach the current state. In this state, what are the valid properties of the state that do not involve negations for b3? Write None if there are none", "answer": "block b3 is located on the table and block b6 is on top of block b3", "plan_length": 10, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from the top of block b5, block b3 is placed down, block b2 is removed from the top of block b6, block b2 is put down, block b6 is picked up and placed on top of block b3, block b5 is removed from the top of block b7 and stacked on top of block b6, and block b7 is removed from block b1 and put down, resulting in the current state. In this state, what are the valid properties that do not involve negations for b3? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is placed on block b6, block b3 has no blocks on it, block b3 is placed on top of block b5, block b4 is on the table, block b5 is placed on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and the hand is empty."}
{"question_id": "99f829be-c26e-4f46-950c-f521e6808065", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_4", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked, block b4 is put down on the table, block b1 is unstacked from block b6, the hand puts down the block b1, block b2 is unstacked from block b5, on top of block b4, block b2 is stacked, block b6 is unstacked from block b7, block b6 is put down, block b7 is picked up from the table, on top of block b2, block b7 is stacked, block b3 is unstacked from block b8, block b3 is stacked on top of block b7, block b1 is picked up from the table, on top of block b3, block b1 is stacked, block b5 is picked up from the table, block b5 is stacked on top of block b1, block b8 is picked up from the table, on top of block b5, block b8 is stacked and block b6 is picked up by the hand to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: block b1 is not on the table, block b2 is not on the table, block b3 is not on the table, block b4 is not located on the table, block b5 is not located on the table, block b6 is not located on the table, block b7 is not on the table and block b8 is not located on the table?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is on the table, block b6 is placed on top of block b7, block b7 is located on the table, block b8 is on the table and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b4 is removed from the top of block b1 and placed on the table, then block b1 is unstacked from block b6 and put down, block b2 is unstacked from block b5, and stacked on top of block b4, block b6 is unstacked from block b7 and placed on the table, block b7 is picked up and stacked on top of block b2, block b3 is unstacked from block b8 and stacked on top of block b7, block b1 is picked up and stacked on top of block b3, block b5 is picked up and stacked on top of block b1, block b8 is picked up and stacked on top of block b5, and finally, block b6 is picked up by the hand to reach the current state. In this state, are the following properties that involve negations True or False: block b1 is not on the table, block b2 is not on the table, block b3 is not on the table, block b4 is not on the table, block b5 is not on the table, block b6 is not on the table, block b7 is not on the table, and block b8 is not on the table?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b6, block b2 has no blocks on it, block b2 is stacked on block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is resting on the table, block b6 is placed on top of block b7, block b7 is situated on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "d4542b92-5225-4651-a6f9-8cfe87e9b504", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, block b3 is put down, block b2 is unstacked from top of block b6, the hand puts down the block b2, block b6 is picked up from the table, on top of block b3, block b6 is stacked, block b5 is unstacked from block b7, on top of block b6, block b5 is stacked, block b7 is unstacked from block b1, the hand puts down the block b7, block b1 is unstacked from top of block b4, on top of block b2, block b1 is stacked, block b4 is picked up, on top of block b5, block b4 is stacked, from top of block b2, block b1 is unstacked, block b1 is stacked on top of block b4, block b2 is picked up from the table, block b2 is stacked on top of block b1 and block b7 is picked up from the table to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: block b1 is clear, block b2 is clear, block b3 is clear, block b4 is clear, block b5 is clear, block b6 is clear, block b7 is clear and hand is empty?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from block b5, then placed on the table, block b2 is removed from the top of block b6 and put down, block b6 is picked up and placed on top of block b3, block b5 is removed from block b7 and stacked on top of block b6, block b7 is removed from block b1 and placed on the table, block b1 is removed from the top of block b4 and stacked on top of block b2, block b4 is picked up and stacked on top of block b5, block b1 is then removed from the top of block b2 and stacked on top of block b4, block b2 is picked up from the table and stacked on top of block b1, and finally, block b7 is picked up from the table to achieve the current state. In this state, are the following properties, which do not involve negations, True or False: block b1 is clear, block b2 is clear, block b3 is clear, block b4 is clear, block b5 is clear, block b6 is clear, block b7 is clear, and the hand is empty?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is placed on block b6, block b3 has no blocks on it, block b3 is placed on top of block b5, block b4 is on the table, block b5 is placed on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and the hand is empty."}
{"question_id": "1cb14b47-c767-451a-b83b-5f9da4e40d50", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for b7? Write None if there are none", "answer": "block b1 is not on block b7, block b2 is not placed on top of block b7, block b3 is not on block b7, block b4 is not on block b7, block b5 is not on block b7, block b6 is not on top of block b7, block b7 is clear, block b7 is not being held, block b7 is not on the table, block b7 is not on top of block b5, block b7 is not placed on top of block b1, block b7 is not placed on top of block b2, block b7 is not placed on top of block b3, block b7 is not placed on top of block b4 and block b7 is on block b6", "plan_length": 1, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Starting from the initial condition, the following actions are taken: block b3 is unstacked from the top of block b7 to achieve the current state. In this state, what are the valid properties (including both affirmative and negated properties) for b7? If there are no valid properties, write None.", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 is not stacked with any other block, block b2 is also on the table, block b3 is not stacked with any other block, block b3 is positioned on top of block b7, block b4 is stacked on top of block b1, block b5 is not stacked with any other block, block b5 is placed on top of block b4, block b6 is on the table, block b7 is stacked on top of block b6, and the hand is empty."}
{"question_id": "97e1393c-8b1a-4b2b-9cc8-b78f9091cd76", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8, the hand puts down the block b9, from top of block b6, block b2 is unstacked, on top of block b5, block b2 is stacked, block b6 is unstacked from block b3, block b6 is put down, from top of block b4, block b3 is unstacked, on top of block b9, block b3 is stacked, from top of block b1, block b8 is unstacked, on top of block b4, block b8 is stacked, from top of block b5, block b2 is unstacked, block b2 is stacked on top of block b8, block b3 is unstacked from top of block b9, block b3 is stacked on top of block b2, from top of block b7, block b1 is unstacked, block b1 is stacked on top of block b3, block b7 is picked up, block b7 is stacked on top of block b9 and block b6 is picked up from the table to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: block b1 is clear, block b5 is clear and block b7 is clear?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b7, block b2 is clear, block b2 is on top of block b6, block b3 is on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is on top of block b8 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b9 is removed from the top of block b8, then placed on the table, block b2 is unstacked from block b6, and then stacked on top of block b5. Next, block b6 is unstacked from block b3 and placed on the table. Block b3 is then unstacked from block b4 and stacked on top of block b9. Block b8 is unstacked from block b1 and stacked on top of block b4. Block b2 is then unstacked from block b5 and stacked on top of block b8. Block b3 is unstacked from block b9 and stacked on top of block b2. Block b1 is unstacked from block b7 and stacked on top of block b3. Block b7 is then picked up and stacked on top of block b9, and finally, block b6 is picked up from the table to reach the current state. In this state, are the following properties that do not involve negations True or False: block b1 is clear, block b5 is clear, and block b7 is clear?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is positioned on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is stacked on block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "82ac4da2-780b-4315-b3a3-5d35b8a99c0f", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1, block b4 is put down on the table, block b1 is unstacked from top of block b6, block b1 is put down on the table, from top of block b5, block b2 is unstacked, on top of block b4, block b2 is stacked, block b6 is unstacked from block b7, block b6 is put down on the table, block b7 is picked up, block b7 is stacked on top of block b2, from top of block b8, block b3 is unstacked, block b3 is stacked on top of block b7, block b1 is picked up, on top of block b3, block b1 is stacked, block b5 is picked up from the table, on top of block b1, block b5 is stacked, block b8 is picked up by the hand, block b8 is stacked on top of block b5 and block b6 is picked up by the hand to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: block b1 is on the table, block b2 is located on the table, block b3 is on the table, block b4 is on the table, block b5 is located on the table, block b6 is on the table, block b7 is on the table and block b8 is located on the table?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is on the table, block b6 is placed on top of block b7, block b7 is located on the table, block b8 is on the table and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b4 is removed from block b1 and placed on the table, block b1 is then removed from block b6 and also placed on the table. Next, block b2 is removed from block b5 and stacked on top of block b4. Block b6 is then removed from block b7 and placed on the table, after which block b7 is picked up and stacked on top of block b2. Block b3 is then removed from block b8 and stacked on top of block b7. Block b1 is picked up and stacked on top of block b3, followed by block b5 being picked up from the table and stacked on top of block b1. Block b8 is then picked up and stacked on top of block b5, and finally, block b6 is picked up to reach the current state. In this state, are the following properties of the state, which do not involve negations, True or False: block b1 is on the table, block b2 is on the table, block b3 is on the table, block b4 is on the table, block b5 is on the table, block b6 is on the table, block b7 is on the table, and block b8 is on the table?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b6, block b2 has no blocks on it, block b2 is stacked on block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is resting on the table, block b6 is placed on top of block b7, block b7 is situated on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "fbcfe233-119c-4534-8f1d-be9c32416d24", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_9", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked, block b4 is put down on the table, block b1 is unstacked from block b6, the hand puts down the block b1, block b2 is unstacked from top of block b5, on top of block b4, block b2 is stacked, block b6 is unstacked from block b7, block b6 is put down, block b7 is picked up, block b7 is stacked on top of block b2, block b3 is unstacked from top of block b8, on top of block b7, block b3 is stacked, block b1 is picked up, block b1 is stacked on top of block b3, block b5 is picked up from the table, on top of block b1, block b5 is stacked, block b8 is picked up from the table, block b8 is stacked on top of block b5 and block b6 is picked up from the table to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: block b1 is not clear, block b2 is not clear, block b3 is not clear, block b4 is not clear, block b5 is not clear, block b6 is not clear, block b7 is not clear and hand is holding some block?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is on the table, block b6 is placed on top of block b7, block b7 is located on the table, block b8 is on the table and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b4 is removed from the top of block b1 and placed on the table, then block b1 is unstacked from block b6 and put down, block b2 is unstacked from the top of block b5 and stacked on top of block b4, block b6 is unstacked from block b7 and put down, block b7 is picked up and stacked on top of block b2, block b3 is unstacked from the top of block b8 and stacked on top of block b7, block b1 is picked up and stacked on top of block b3, block b5 is picked up from the table and stacked on top of block b1, block b8 is picked up from the table and stacked on top of block b5, and finally block b6 is picked up from the table to reach the current state. In this state, are the following properties that involve negations True or False: block b1 is not clear, block b2 is not clear, block b3 is not clear, block b4 is not clear, block b5 is not clear, block b6 is not clear, block b7 is not clear, and is the hand holding a block?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b6, block b2 has no blocks on it, block b2 is stacked on block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is resting on the table, block b6 is placed on top of block b7, block b7 is situated on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "118f08de-7394-497f-9e52-5fbd5e9c55d5", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_5", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked to reach the current state. In this state, is it True or False that block b5 is clear?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b3 is unstacked from the top of block b7 to reach the current state. In this state, is it True or False that block b5 is clear?", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 is not stacked, block b2 is also on the table, block b3 is not stacked, block b3 is positioned above block b7, block b4 is stacked on top of block b1, block b5 is not stacked, block b5 is placed above block b4, block b6 is on the table, block b7 is stacked on block b6, and the hand is empty."}
{"question_id": "43da89e5-d4ef-4eda-8936-8f8fcf976ae9", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_9", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked to reach the current state. In this state, is it True or False that block b1 is on top of block b6?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is on the table, block b6 is placed on top of block b7, block b7 is located on the table, block b8 is on the table and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b4 is removed from the top of block b1 to reach the current state. In this state, is it True or False that block b1 is on top of block b6?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b6, block b2 has no blocks on it, block b2 is stacked on block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is resting on the table, block b6 is placed on top of block b7, block b7 is situated on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "1cd83c4f-ce1c-4caf-ab97-cb642f3ba7de", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_3", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked, block b4 is put down, block b1 is unstacked from top of block b6, the hand puts down the block b1, block b2 is unstacked from block b5, on top of block b4, block b2 is stacked, block b6 is unstacked from top of block b7, block b6 is put down on the table, block b7 is picked up, block b7 is stacked on top of block b2, block b3 is unstacked from block b8, on top of block b7, block b3 is stacked, block b1 is picked up, block b1 is stacked on top of block b3, block b5 is picked up by the hand, on top of block b1, block b5 is stacked, block b8 is picked up from the table, on top of block b5, block b8 is stacked and block b6 is picked up by the hand to reach the current state. In this state, is it True or False that block b3 is not on the table?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is on the table, block b6 is placed on top of block b7, block b7 is located on the table, block b8 is on the table and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b4 is removed from the top of block b1 and placed on the table, then block b1 is unstacked from the top of block b6 and put down, block b2 is unstacked from block b5 and placed on top of block b4, block b6 is unstacked from the top of block b7 and put down, block b7 is picked up and stacked on top of block b2, block b3 is unstacked from block b8 and placed on top of block b7, block b1 is picked up and stacked on top of block b3, block b5 is picked up and stacked on top of block b1, block b8 is picked up from the table and stacked on top of block b5, and finally block b6 is picked up by the hand to reach the current state. In this state, is it True or False that block b3 is not on the table?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b6, block b2 has no blocks on it, block b2 is stacked on block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is resting on the table, block b6 is placed on top of block b7, block b7 is situated on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "e3349e03-ef33-479a-b85f-9622844da019", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, the hand puts down the block b3, block b5 is unstacked from block b4, block b5 is stacked on top of block b2, from top of block b1, block b4 is unstacked, block b4 is put down on the table, block b5 is unstacked from top of block b2, on top of block b1, block b5 is stacked, block b7 is unstacked from block b6, block b7 is stacked on top of block b4, block b6 is picked up from the table, block b6 is stacked on top of block b5, block b2 is picked up from the table, on top of block b6, block b2 is stacked, block b3 is picked up, block b3 is stacked on top of block b2, block b7 is unstacked from block b4, on top of block b3, block b7 is stacked and block b4 is picked up to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: block b1 is on the table, block b2 is located on the table, block b3 is located on the table, block b4 is located on the table, block b5 is located on the table, block b6 is located on the table and block b7 is located on the table?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b7, block b3 is placed on the table, block b5 is removed from block b4, block b5 is placed on top of block b2, block b4 is removed from the top of block b1 and placed on the table, block b5 is removed from the top of block b2 and placed on top of block b1, block b7 is removed from block b6 and placed on top of block b4, block b6 is picked up from the table and placed on top of block b5, block b2 is picked up from the table and placed on top of block b6, block b3 is picked up and placed on top of block b2, block b7 is removed from block b4 and placed on top of block b3, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: block b1 is on the table, block b2 is on the table, block b3 is on the table, block b4 is on the table, block b5 is on the table, block b6 is on the table, and block b7 is on the table?", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 is not stacked with any other block, block b2 is also on the table, block b3 is not stacked with any other block, block b3 is positioned on top of block b7, block b4 is stacked on top of block b1, block b5 is not stacked with any other block, block b5 is placed on top of block b4, block b6 is on the table, block b7 is stacked on block b6 and the hand is empty."}
{"question_id": "ac348635-1710-49a9-9d9b-b07a519c1986", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_11", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked, block b3 is put down on the table, from top of block b4, block b5 is unstacked, on top of block b2, block b5 is stacked, from top of block b1, block b4 is unstacked, block b4 is put down, block b5 is unstacked from top of block b2, on top of block b1, block b5 is stacked, block b7 is unstacked from block b6, on top of block b4, block b7 is stacked, block b6 is picked up, on top of block b5, block b6 is stacked, block b2 is picked up from the table, block b2 is stacked on top of block b6, block b3 is picked up, on top of block b2, block b3 is stacked, block b7 is unstacked from block b4, on top of block b3, block b7 is stacked and block b4 is picked up by the hand to reach the current state. In this state, is it True or False that block b4 is not placed on top of block b2?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b7 and placed on the table, then block b5 is unstacked from block b4 and placed on top of block b2. Next, block b4 is unstacked from block b1 and put down, followed by unstacking block b5 from block b2 and placing it on top of block b1. Block b7 is then unstacked from block b6 and placed on top of block b4. Block b6 is picked up and stacked on top of block b5, and block b2 is picked up from the table and stacked on top of block b6. Block b3 is then picked up and stacked on top of block b2, and block b7 is unstacked from block b4 and placed on top of block b3. Finally, block b4 is picked up by the hand to reach the current state. In this state, is it True or False that block b4 is not placed on top of block b2?", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 is not obstructed, block b2 is also on the table, block b3 is not obstructed, block b3 is positioned above block b7, block b4 is stacked on top of block b1, block b5 is clear, block b5 is placed above block b4, block b6 is resting on the table, block b7 is positioned on top of block b6, and the hand is currently empty."}
{"question_id": "a1243703-34af-4116-be7d-1efd6fecfd2f", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_6", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8 to reach the current state. In this state, is it True or False that block b3 is clear?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b7, block b2 is clear, block b2 is on top of block b6, block b3 is on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is on top of block b8 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: block b9 is removed from block b8 to achieve the current state. In this state, is it True or False that block b3 is clear?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is positioned on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is stacked on block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "8e8d7866-9019-46fe-8eca-15ed378faa29", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_6", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, the hand puts down the block b3, block b5 is unstacked from block b4, block b5 is stacked on top of block b2, block b4 is unstacked from top of block b1, block b4 is put down on the table, from top of block b2, block b5 is unstacked, block b5 is stacked on top of block b1, block b7 is unstacked from top of block b6, on top of block b4, block b7 is stacked, block b6 is picked up, on top of block b5, block b6 is stacked, block b2 is picked up by the hand, block b2 is stacked on top of block b6, block b3 is picked up by the hand, on top of block b2, block b3 is stacked, from top of block b4, block b7 is unstacked, on top of block b3, block b7 is stacked and block b4 is picked up by the hand to reach the current state. In this state, is it True or False that block b1 is clear?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b7, then placed on the table by the hand, block b5 is unstacked from block b4, and then stacked on top of block b2, block b4 is unstacked from the top of block b1 and placed on the table, block b5 is then unstacked from the top of block b2 and stacked on top of block b1, block b7 is unstacked from the top of block b6 and stacked on top of block b4, block b6 is picked up and stacked on top of block b5, block b2 is picked up by the hand and stacked on top of block b6, block b3 is picked up by the hand and stacked on top of block b2, block b7 is unstacked from the top of block b4 and stacked on top of block b3, and finally, block b4 is picked up by the hand to reach the current state. In this state, is it True or False that block b1 is clear?", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 is not obstructed, block b2 is also on the table, block b3 is not obstructed, block b3 is positioned above block b7, block b4 is stacked on top of block b1, block b5 is clear, block b5 is placed above block b4, block b6 is resting on the table, block b7 is positioned on block b6, and the hand is currently empty."}
{"question_id": "c1087da6-c77d-4eb8-aa52-dd995d982d3f", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_11", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, block b3 is put down on the table, block b2 is unstacked from block b6, block b2 is put down on the table, block b6 is picked up, on top of block b3, block b6 is stacked, from top of block b7, block b5 is unstacked, block b5 is stacked on top of block b6, block b7 is unstacked from top of block b1, the hand puts down the block b7, block b1 is unstacked from block b4, block b1 is stacked on top of block b2, block b4 is picked up from the table, on top of block b5, block b4 is stacked, from top of block b2, block b1 is unstacked, on top of block b4, block b1 is stacked, block b2 is picked up by the hand, on top of block b1, block b2 is stacked and block b7 is picked up from the table to reach the current state. In this state, is it True or False that block b7 is not on block b5?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from block b5 and placed on the table, block b2 is removed from block b6 and placed on the table, block b6 is picked up and placed on top of block b3, block b5 is removed from block b7 and stacked on top of block b6, block b7 is removed from block b1 and placed on the table, block b1 is removed from block b4 and stacked on top of block b2, block b4 is picked up from the table and stacked on top of block b5, block b1 is removed from block b2 and stacked on top of block b4, block b2 is picked up by the hand and stacked on top of block b1, and finally, block b7 is picked up from the table to reach the current state. In this state, is it True or False that block b7 is not on block b5?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is placed on block b6, block b3 has no blocks on it, block b3 is placed on top of block b5, block b4 is on the table, block b5 is placed on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and the hand is empty."}
{"question_id": "7ef9bf90-7ddb-4eca-be3f-b02d7fced451", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_15", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b7, block b3 is put down on the table, from top of block b4, block b5 is unstacked, block b5 is stacked on top of block b2, block b4 is unstacked from block b1, the hand puts down the block b4, from top of block b2, block b5 is unstacked, on top of block b1, block b5 is stacked, from top of block b6, block b7 is unstacked and block b7 is stacked on top of block b4 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: block b1 is not on block b2, block b1 is not on block b3, block b1 is not on block b4, block b1 is not on block b6, block b1 is not on top of block b5, block b1 is not placed on top of block b7, block b2 is not being held, block b2 is not on block b5, block b2 is not on block b7, block b2 is not on top of block b1, block b2 is not on top of block b3, block b2 is not on top of block b4, block b2 is not placed on top of block b6, block b3 is not being held by the hand, block b3 is not on block b7, block b3 is not on top of block b2, block b3 is not on top of block b4, block b3 is not on top of block b5, block b3 is not placed on top of block b1, block b3 is not placed on top of block b6, block b4 is not being held by the hand, block b4 is not on block b1, block b4 is not on block b7, block b4 is not on top of block b3, block b4 is not on top of block b5, block b4 is not on top of block b6, block b4 is not placed on top of block b2, block b5 is not on block b2, block b5 is not on block b3, block b5 is not on top of block b4, block b5 is not on top of block b7, block b5 is not placed on top of block b6, block b6 is not being held, block b6 is not on block b2, block b6 is not on block b3, block b6 is not on block b5, block b6 is not on block b7, block b6 is not placed on top of block b1, block b6 is not placed on top of block b4, block b7 is not being held by the hand, block b7 is not on block b5, block b7 is not on top of block b2, block b7 is not on top of block b3, block b7 is not on top of block b6, block b7 is not placed on top of block b1, the hand is not holding the block b1 and the hand is not holding the block b5?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from block b7, block b3 is placed on the table, block b5 is removed from the top of block b4, block b5 is placed on top of block b2, block b4 is removed from block b1, the hand releases block b4, block b5 is removed from the top of block b2, block b5 is placed on top of block b1, block b7 is removed from the top of block b6 and block b7 is placed on top of block b4 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: block b1 is not positioned on block b2, block b1 is not positioned on block b3, block b1 is not positioned on block b4, block b1 is not positioned on block b6, block b1 is not positioned on top of block b5, block b1 is not placed on top of block b7, block b2 is not being held by the hand, block b2 is not positioned on block b5, block b2 is not positioned on block b7, block b2 is not positioned on top of block b1, block b2 is not positioned on top of block b3, block b2 is not positioned on top of block b4, block b2 is not placed on top of block b6, block b3 is not held by the hand, block b3 is not positioned on block b7, block b3 is not positioned on top of block b2, block b3 is not positioned on top of block b4, block b3 is not positioned on top of block b5, block b3 is not placed on top of block b1, block b3 is not placed on top of block b6, block b4 is not held by the hand, block b4 is not positioned on block b1, block b4 is not positioned on block b7, block b4 is not positioned on top of block b3, block b4 is not positioned on top of block b5, block b4 is not positioned on top of block b6, block b4 is not placed on top of block b2, block b5 is not positioned on block b2, block b5 is not positioned on block b3, block b5 is not positioned on top of block b4, block b5 is not positioned on top of block b7, block b5 is not placed on top of block b6, block b6 is not being held, block b6 is not positioned on block b2, block b6 is not positioned on block b3, block b6 is not positioned on block b5, block b6 is not positioned on block b7, block b6 is not placed on top of block b1, block b6 is not placed on top of block b4, block b7 is not held by the hand, block b7 is not positioned on block b5, block b7 is not positioned on top of block b2, block b7 is not positioned on top of block b3, block b7 is not positioned on top of block b6, block b7 is not placed on top of block b1, the hand is not holding block b1 and the hand is not holding block b5?", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 is not obstructed, block b2 is also on the table, block b3 is not obstructed, block b3 is positioned on top of block b7, block b4 is positioned on top of block b1, block b5 is not obstructed, block b5 is placed on top of block b4, block b6 is on the table, block b7 is positioned on block b6, and the hand is empty."}
{"question_id": "5cec314b-1b4e-43e8-a065-2ea46d689474", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_4", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: block b1 is not located on the table, block b2 is not located on the table, block b3 is not on the table, block b4 is not located on the table, block b5 is not located on the table, block b6 is not located on the table and block b7 is not on the table?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is unstacked from the top of block b5 to reach the current state. In this state, are the following properties that involve negations True or False: block b1 is not on the table, block b2 is not on the table, block b3 is not on the table, block b4 is not on the table, block b5 is not on the table, block b6 is not on the table, and block b7 is not on the table?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is placed on block b6, block b3 has no blocks on it, block b3 is placed on top of block b5, block b4 is positioned on the table, block b5 is positioned on top of block b7, block b6 is positioned on the table, block b7 is placed on top of block b1 and the hand is empty."}
{"question_id": "ce0b4167-3721-4a86-9d98-9e6be62023c7", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_10", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked, block b9 is put down, block b2 is unstacked from top of block b6, on top of block b5, block b2 is stacked, from top of block b3, block b6 is unstacked, block b6 is put down on the table, block b3 is unstacked from block b4, on top of block b9, block b3 is stacked, block b8 is unstacked from block b1 and block b8 is stacked on top of block b4 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: block b1 is not clear, block b2 is not clear, block b3 is not clear, block b4 is not clear, block b5 is not clear, block b6 is not clear, block b7 is not clear, block b8 is not clear, block b9 is not clear and hand is holding some block?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b7, block b2 is clear, block b2 is on top of block b6, block b3 is on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is on top of block b8 and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b9 is removed from the top of block b8 and placed down, then block b2 is removed from the top of block b6 and placed on top of block b5. Next, block b6 is removed from the top of block b3 and placed on the table, and block b3 is removed from block b4. Block b3 is then stacked on top of block b9. Finally, block b8 is removed from block b1 and stacked on top of block b4, resulting in the current state. In this state, are the following properties that involve negations True or False: block b1 is not empty, block b2 is not empty, block b3 is not empty, block b4 is not empty, block b5 is not empty, block b6 is not empty, block b7 is not empty, block b8 is not empty, block b9 is not empty, and the hand is holding a block?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is positioned on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is stacked on block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "6573a5a2-cd8d-4f6b-9e5d-dcdc0479d217", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8, the hand puts down the block b9, block b2 is unstacked from top of block b6, on top of block b5, block b2 is stacked, block b6 is unstacked from block b3, block b6 is put down, block b3 is unstacked from top of block b4, block b3 is stacked on top of block b9, block b8 is unstacked from block b1, block b8 is stacked on top of block b4, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b8, from top of block b9, block b3 is unstacked, block b3 is stacked on top of block b2, block b1 is unstacked from top of block b7, on top of block b3, block b1 is stacked, block b7 is picked up by the hand, block b7 is stacked on top of block b9 and block b6 is picked up by the hand to reach the current state. In this state, what are the valid properties of the state that involve negations for b4? Write None if there are none", "answer": "block b1 is not on top of block b4, block b2 is not on top of block b4, block b3 is not on block b4, block b4 is not clear, block b4 is not on block b1, block b4 is not on block b2, block b4 is not on block b3, block b4 is not on block b5, block b4 is not on block b8, block b4 is not on top of block b6, block b4 is not on top of block b7, block b4 is not placed on top of block b9, block b5 is not on block b4, block b6 is not placed on top of block b4, block b7 is not on top of block b4, block b9 is not on top of block b4 and the hand is not holding the block b4", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b7, block b2 is clear, block b2 is on top of block b6, block b3 is on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is on top of block b8 and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b9 is removed from block b8, then placed by the hand, block b2 is removed from the top of block b6 and placed on top of block b5, block b6 is then removed from block b3 and put down, block b3 is removed from the top of block b4 and stacked on top of block b9, block b8 is removed from block b1 and stacked on top of block b4, block b2 is then removed from the top of block b5 and stacked on top of block b8, block b3 is removed from the top of block b9 and stacked on top of block b2, block b1 is removed from the top of block b7 and stacked on top of block b3, block b7 is picked up by the hand and stacked on top of block b9, and finally block b6 is picked up by the hand to reach the current state. In this state, what are the valid properties of the state that involve negations for b4? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is sitting on the table, block b5 has no blocks on it, block b5 is sitting on the table, block b6 is stacked on block b3, block b7 is sitting on the table, block b8 is stacked on block b1, block b9 has no blocks on it, block b9 is stacked on block b8 and the hand is empty."}
{"question_id": "0c3e06c8-3881-46bf-93fd-e6dd3ac55de9", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1, block b4 is put down, from top of block b6, block b1 is unstacked, the hand puts down the block b1, from top of block b5, block b2 is unstacked, on top of block b4, block b2 is stacked, block b6 is unstacked from block b7, block b6 is put down, block b7 is picked up by the hand and on top of block b2, block b7 is stacked to reach the current state. In this state, is it True or False that block b1 is on the table?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is on the table, block b6 is placed on top of block b7, block b7 is located on the table, block b8 is on the table and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b4 is removed from block b1, then placed on the table, block b1 is then removed from the top of block b6 and put down, block b2 is removed from the top of block b5, placed on top of block b4, block b6 is removed from block b7 and placed on the table, and block b7 is picked up and stacked on top of block b2 to reach the current state. In this state, is it True or False that block b1 is on the table?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b6, block b2 has no blocks on it, block b2 is stacked on block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is resting on the table, block b6 is placed on top of block b7, block b7 is situated on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "9178efa9-a350-4388-bd9c-86971215ba8d", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked to reach the current state. In this state, what are the valid properties of the state that involve negations for b5? Write None if there are none", "answer": "block b1 is not on top of block b5, block b2 is not placed on top of block b5, block b3 is not on top of block b5, block b4 is not placed on top of block b5, block b5 is not being held, block b5 is not on block b1, block b5 is not on block b2, block b5 is not on block b6, block b5 is not on block b7, block b5 is not on top of block b3, block b5 is not on top of block b9, block b5 is not placed on top of block b4, block b5 is not placed on top of block b8, block b6 is not on block b5, block b7 is not on block b5, block b8 is not on top of block b5 and block b9 is not on top of block b5", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b7, block b2 is clear, block b2 is on top of block b6, block b3 is on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is on top of block b8 and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b9 is unstacked from the top of block b8 to reach the current state. In this state, what are the valid properties that involve negations for b5, if any, or None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is positioned on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is stacked on block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "b9efa793-c91d-402d-9223-a60152fc5280", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_4", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8, block b9 is put down, block b2 is unstacked from block b6, block b2 is stacked on top of block b5, from top of block b3, block b6 is unstacked, the hand puts down the block b6, block b3 is unstacked from block b4, on top of block b9, block b3 is stacked, from top of block b1, block b8 is unstacked and block b8 is stacked on top of block b4 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: block b1 is not located on the table, block b2 is not on the table, block b3 is not on the table, block b4 is not located on the table, block b5 is not on the table, block b6 is not located on the table, block b7 is not on the table, block b8 is not on the table and block b9 is not on the table?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b7, block b2 is clear, block b2 is on top of block b6, block b3 is on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is on top of block b8 and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b9 is removed from block b8, block b9 is placed down, block b2 is removed from block b6, block b2 is placed on top of block b5, block b6 is removed from the top of block b3, the hand places block b6 down, block b3 is removed from block b4, block b3 is placed on top of block b9, block b8 is removed from the top of block b1 and block b8 is placed on top of block b4 to achieve the current state. In this state, are the following properties that involve negations True or False: block b1 is not on the table, block b2 is not on the table, block b3 is not on the table, block b4 is not on the table, block b5 is not on the table, block b6 is not on the table, block b7 is not on the table, block b8 is not on the table, and block b9 is not on the table?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is positioned on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is stacked on block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "07e041f8-a91a-443b-ac78-9dd0636ef59b", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, block b3 is put down, block b5 is unstacked from top of block b4, block b5 is stacked on top of block b2, block b4 is unstacked from block b1, block b4 is put down, block b5 is unstacked from block b2, block b5 is stacked on top of block b1, block b7 is unstacked from top of block b6, on top of block b4, block b7 is stacked, block b6 is picked up, block b6 is stacked on top of block b5, block b2 is picked up, block b2 is stacked on top of block b6, block b3 is picked up, on top of block b2, block b3 is stacked, from top of block b4, block b7 is unstacked, on top of block b3, block b7 is stacked and block b4 is picked up to reach the current state. In this state, what are the valid properties of the state that do not involve negations for b7? Write None if there are none", "answer": "block b7 is clear and block b7 is placed on top of block b3", "plan_length": 19, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b7 and placed down, block b5 is removed from the top of block b4, then stacked on top of block b2, block b4 is removed from block b1 and placed down, block b5 is removed from block b2 and stacked on top of block b1, block b7 is removed from the top of block b6 and stacked on top of block b4, block b6 is picked up and stacked on top of block b5, block b2 is picked up and stacked on top of block b6, block b3 is picked up and stacked on top of block b2, block b7 is removed from the top of block b4 and stacked on top of block b3, and block b4 is picked up to reach the current state. In this state, what are the valid properties of the state that do not involve negations for b7? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 is not stacked with any other block, block b2 is also on the table, block b3 is not stacked with any other block, block b3 is positioned on top of block b7, block b4 is stacked on top of block b1, block b5 is not stacked with any other block, block b5 is placed on top of block b4, block b6 is on the table, block b7 is stacked on block b6, and the hand is empty."}
{"question_id": "308d4b72-6541-4cf8-8ea6-bbe126474a8f", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_4", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8 to reach the current state. In this state, is it True or False that block b5 is not located on the table?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b7, block b2 is clear, block b2 is on top of block b6, block b3 is on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is on top of block b8 and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b9 is removed from the top of block b8 to reach the current state. In this state, is it True or False that block b5 is not on the table?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is positioned on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is stacked on block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "e9971bcd-d1da-4181-8ca4-afc85b1aec08", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, the hand puts down the block b3, block b5 is unstacked from block b4, on top of block b2, block b5 is stacked, block b4 is unstacked from block b1, block b4 is put down on the table, from top of block b2, block b5 is unstacked, on top of block b1, block b5 is stacked, block b7 is unstacked from block b6, on top of block b4, block b7 is stacked, block b6 is picked up, on top of block b5, block b6 is stacked, block b2 is picked up by the hand, on top of block b6, block b2 is stacked, block b3 is picked up from the table, block b3 is stacked on top of block b2, block b7 is unstacked from top of block b4, on top of block b3, block b7 is stacked and block b4 is picked up from the table to reach the current state. In this state, what are the valid properties of the state that do not involve negations for b2? Write None if there are none", "answer": "block b2 is on top of block b6 and block b3 is placed on top of block b2", "plan_length": 19, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b7, then placed on the table by the hand, block b5 is removed from block b4 and placed on top of block b2, block b4 is removed from block b1 and placed on the table, block b5 is then removed from block b2 and placed on top of block b1, block b7 is removed from block b6 and placed on top of block b4, block b6 is picked up and placed on top of block b5, block b2 is picked up by the hand and placed on top of block b6, block b3 is picked up from the table and placed on top of block b2, block b7 is removed from the top of block b4 and placed on top of block b3, and finally, block b4 is picked up from the table to reach the current state. In this state, what are the valid properties of the state that do not involve negations for b2? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 is not stacked with any other block, block b2 is also on the table, block b3 is not stacked with any other block, block b3 is positioned on top of block b7, block b4 is stacked on top of block b1, block b5 is not stacked with any other block, block b5 is placed on top of block b4, block b6 is on the table, block b7 is stacked on block b6 and the hand is empty."}
{"question_id": "04382012-bf90-4ab6-b681-4065607818ee", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_4", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1, block b4 is put down on the table, from top of block b6, block b1 is unstacked, block b1 is put down, from top of block b5, block b2 is unstacked, on top of block b4, block b2 is stacked, block b6 is unstacked from block b7, block b6 is put down, block b7 is picked up by the hand, on top of block b2, block b7 is stacked, block b3 is unstacked from top of block b8, on top of block b7, block b3 is stacked, block b1 is picked up from the table, on top of block b3, block b1 is stacked, block b5 is picked up from the table, block b5 is stacked on top of block b1, block b8 is picked up by the hand, block b8 is stacked on top of block b5 and block b6 is picked up to reach the current state. In this state, is it True or False that block b4 is not located on the table?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is on the table, block b6 is placed on top of block b7, block b7 is located on the table, block b8 is on the table and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b4 is removed from the top of block b1, then placed on the table, block b1 is then removed from the top of block b6 and set down, block b2 is removed from the top of block b5, and then stacked on top of block b4, block b6 is removed from block b7 and set down, block b7 is picked up and stacked on top of block b2, block b3 is removed from the top of block b8 and stacked on top of block b7, block b1 is picked up from the table and stacked on top of block b3, block b5 is picked up from the table and stacked on top of block b1, block b8 is picked up and stacked on top of block b5, and finally block b6 is picked up to reach the current state. In this state, is it True or False that block b4 is not located on the table?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b6, block b2 has no blocks on it, block b2 is stacked on block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is resting on the table, block b6 is placed on top of block b7, block b7 is situated on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "0c4a4841-b1ab-4e62-bae4-0617ad6dfcd8", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1, block b4 is put down, block b1 is unstacked from block b6, block b1 is put down on the table, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b4, block b6 is unstacked from block b7, block b6 is put down on the table, block b7 is picked up from the table and on top of block b2, block b7 is stacked to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: block b1 is clear, block b3 is clear, block b5 is clear, block b6 is clear, block b7 is clear and hand is empty?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is on the table, block b6 is placed on top of block b7, block b7 is located on the table, block b8 is on the table and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b4 is removed from the top of block b1, block b4 is placed down, block b1 is removed from block b6, block b1 is placed on the table, block b2 is removed from the top of block b5, block b2 is stacked on top of block b4, block b6 is removed from block b7, block b6 is placed on the table, block b7 is picked up from the table and stacked on top of block b2 to reach the current state. In this state, are the following properties of the state that do not involve negations True or False: block b1 is clear, block b3 is clear, block b5 is clear, block b6 is clear, block b7 is clear, and the hand is empty?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b6, block b2 has no blocks on it, block b2 is stacked on block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is resting on the table, block b6 is placed on top of block b7, block b7 is situated on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "e0e67ff7-8548-453c-92a1-0c1573edd615", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1, the hand puts down the block b4, from top of block b6, block b1 is unstacked, block b1 is put down, from top of block b5, block b2 is unstacked, block b2 is stacked on top of block b4, block b6 is unstacked from top of block b7, block b6 is put down, block b7 is picked up by the hand and block b7 is stacked on top of block b2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: block b1 is on the table, block b2 is located on the table, block b3 is located on the table, block b4 is located on the table, block b5 is on the table, block b6 is on the table, block b7 is located on the table and block b8 is on the table?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is on the table, block b6 is placed on top of block b7, block b7 is located on the table, block b8 is on the table and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is executed: block b4 is removed from the top of block b1, block b4 is placed on the table, block b1 is then removed from the top of block b6 and placed on the table, block b2 is removed from the top of block b5, block b2 is stacked on top of block b4, block b6 is removed from the top of block b7 and placed on the table, block b7 is picked up by the hand and stacked on top of block b2, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: block b1 is on the table, block b2 is on the table, block b3 is on the table, block b4 is on the table, block b5 is on the table, block b6 is on the table, block b7 is on the table, and block b8 is on the table?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b6, block b2 has no blocks on it, block b2 is stacked on block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is resting on the table, block b6 is placed on top of block b7, block b7 is situated on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "1450264d-c8ee-484a-bd00-860be66c267b", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_3_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked, the hand puts down the block b3, from top of block b6, block b2 is unstacked, block b2 is put down, block b6 is picked up, on top of block b3, block b6 is stacked, from top of block b7, block b5 is unstacked, on top of block b6, block b5 is stacked, block b7 is unstacked from top of block b1, block b7 is put down, block b1 is unstacked from block b4, block b1 is stacked on top of block b2, block b4 is picked up by the hand, on top of block b5, block b4 is stacked, from top of block b2, block b1 is unstacked, on top of block b4, block b1 is stacked, block b2 is picked up, on top of block b1, block b2 is stacked and block b7 is picked up by the hand to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for b1? Write None if there are none", "answer": "block b1 is not being held by the hand, block b1 is not clear, block b1 is not located on the table, block b1 is not on block b2, block b1 is not on block b7, block b1 is not placed on top of block b3, block b1 is not placed on top of block b5, block b1 is not placed on top of block b6, block b1 is on top of block b4, block b2 is on top of block b1, block b3 is not on top of block b1, block b4 is not on top of block b1, block b5 is not on block b1, block b6 is not on block b1 and block b7 is not on block b1", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b5 and placed down, then block b2 is removed from the top of block b6 and put down, after which block b6 is picked up and stacked on top of block b3. Next, block b5 is removed from the top of block b7 and stacked on top of block b6, and block b7 is unstacked from block b1 and placed down. Block b1 is then unstacked from block b4 and stacked on top of block b2, and block b4 is picked up and stacked on top of block b5. Subsequently, block b1 is removed from the top of block b2 and stacked on top of block b4, block b2 is picked up and stacked on top of block b1, and finally, block b7 is picked up by the hand to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for b1? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is placed on block b6, block b3 has no blocks on it, block b3 is placed on top of block b5, block b4 is on the table, block b5 is placed on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and the hand is empty."}
{"question_id": "94495803-f29c-4974-976a-a7a6b120b0f9", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_10", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: block b1 is not clear, block b2 is not clear, block b3 is not clear, block b4 is not clear, block b5 is not clear, block b6 is not clear, block b7 is not clear and hand is not empty?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is unstacked from the top of block b5 to reach the current state. In this state, are the following properties that involve negations True or False: block b1 is not clear, block b2 is not clear, block b3 is not clear, block b4 is not clear, block b5 is not clear, block b6 is not clear, block b7 is not clear, and the hand is not empty?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is placed on block b6, block b3 has no blocks on it, block b3 is placed on top of block b5, block b4 is on the table, block b5 is placed on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and the hand is empty."}
{"question_id": "c0832c94-39d5-4f24-9db3-55d5c15d98eb", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked to reach the current state. In this state, is it True or False that block b4 is located on the table?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Starting from the initial condition, the following actions are taken: block b3 is unstacked from the top of block b5 to achieve the current state. In this state, is it True or False that block b4 is on the table?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is placed on block b6, block b3 has no blocks on it, block b3 is placed on top of block b5, block b4 is on the table, block b5 is placed on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and the hand is empty."}
{"question_id": "27fc784d-0751-405d-9a5c-1701c5b8b09a", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked to reach the current state. In this state, is it True or False that block b5 is located on the table?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b7, block b2 is clear, block b2 is on top of block b6, block b3 is on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is on top of block b8 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b9 is removed from the top of block b8 to reach the current state. In this state, is it True or False that block b5 is on the table?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is positioned on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is stacked on block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "905e53e7-04be-4928-858a-e335c530b60f", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_14", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: block b1 is on block b2, block b1 is on block b3, block b1 is on block b7, block b1 is on top of block b4, block b1 is placed on top of block b5, block b1 is placed on top of block b6, block b2 is on block b4, block b2 is on block b5, block b2 is on top of block b1, block b2 is on top of block b6, block b2 is placed on top of block b3, block b2 is placed on top of block b7, block b3 is being held by the hand, block b3 is on block b1, block b3 is on block b2, block b3 is on block b5, block b3 is placed on top of block b4, block b3 is placed on top of block b6, block b3 is placed on top of block b7, block b4 is on block b1, block b4 is on block b2, block b4 is on block b5, block b4 is on block b7, block b4 is on top of block b3, block b4 is on top of block b6, block b5 is being held, block b5 is on block b1, block b5 is on block b2, block b5 is on top of block b3, block b5 is on top of block b4, block b5 is on top of block b6, block b5 is placed on top of block b7, block b6 is on block b3, block b6 is on block b5, block b6 is on top of block b1, block b6 is on top of block b4, block b6 is on top of block b7, block b6 is placed on top of block b2, block b7 is on block b3, block b7 is on block b5, block b7 is on block b6, block b7 is on top of block b2, block b7 is placed on top of block b1, block b7 is placed on top of block b4, the hand is holding the block b1, the hand is holding the block b2, the hand is holding the block b4, the hand is holding the block b6 and the hand is holding the block b7?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: block b1 is on top of block b2, block b1 is on top of block b3, block b1 is on top of block b7, block b1 is on top of block b4, block b1 is placed on top of block b5, block b1 is placed on top of block b6, block b2 is on top of block b4, block b2 is on top of block b5, block b2 is on top of block b1, block b2 is on top of block b6, block b2 is placed on top of block b3, block b2 is placed on top of block b7, block b3 is being held by the hand, block b3 is on top of block b1, block b3 is on top of block b2, block b3 is on top of block b5, block b3 is placed on top of block b4, block b3 is placed on top of block b6, block b3 is placed on top of block b7, block b4 is on top of block b1, block b4 is on top of block b2, block b4 is on top of block b5, block b4 is on top of block b7, block b4 is on top of block b3, block b4 is on top of block b6, block b5 is being held, block b5 is on top of block b1, block b5 is on top of block b2, block b5 is on top of block b3, block b5 is on top of block b4, block b5 is on top of block b6, block b5 is placed on top of block b7, block b6 is on top of block b3, block b6 is on top of block b5, block b6 is on top of block b1, block b6 is on top of block b4, block b6 is on top of block b7, block b6 is placed on top of block b2, block b7 is on top of block b3, block b7 is on top of block b5, block b7 is on top of block b6, block b7 is on top of block b2, block b7 is placed on top of block b1, block b7 is placed on top of block b4, the hand is holding block b1, the hand is holding block b2, the hand is holding block b4, the hand is holding block b6 and the hand is holding block b7?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is placed on block b6, block b3 has no blocks on it, block b3 is placed on top of block b5, block b4 is on the table, block b5 is placed on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and the hand is empty."}
{"question_id": "721305f2-24d9-4e69-bda3-fd6a0a74aa55", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8, block b9 is put down on the table, block b2 is unstacked from top of block b6, on top of block b5, block b2 is stacked, block b6 is unstacked from block b3, block b6 is put down, block b3 is unstacked from top of block b4, on top of block b9, block b3 is stacked, from top of block b1, block b8 is unstacked, block b8 is stacked on top of block b4, block b2 is unstacked from top of block b5, on top of block b8, block b2 is stacked, from top of block b9, block b3 is unstacked, on top of block b2, block b3 is stacked, block b1 is unstacked from top of block b7, block b1 is stacked on top of block b3, block b7 is picked up by the hand, on top of block b9, block b7 is stacked and block b6 is picked up by the hand to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: block b1 is clear, block b2 is clear, block b3 is clear, block b4 is clear, block b5 is clear, block b6 is clear, block b7 is clear, block b8 is clear, block b9 is clear and hand is empty?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b7, block b2 is clear, block b2 is on top of block b6, block b3 is on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is on top of block b8 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b9 is removed from the top of block b8 and placed on the table, block b2 is removed from the top of block b6 and stacked on top of block b5, block b6 is removed from block b3 and placed on the table, block b3 is removed from the top of block b4 and stacked on top of block b9, block b8 is removed from the top of block b1 and stacked on top of block b4, block b2 is removed from the top of block b5 and stacked on top of block b8, block b3 is removed from the top of block b9 and stacked on top of block b2, block b1 is removed from the top of block b7 and stacked on top of block b3, block b7 is picked up by the hand and stacked on top of block b9, and block b6 is picked up by the hand to reach the current state. In this state, are the following properties of the state that do not involve negations True or False: block b1 is clear, block b2 is clear, block b3 is clear, block b4 is clear, block b5 is clear, block b6 is clear, block b7 is clear, block b8 is clear, block b9 is clear, and the hand is empty?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is positioned on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is stacked on block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "a07d88ce-ad36-476c-817e-8b37573f2dfb", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_3_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8, the hand puts down the block b9, block b2 is unstacked from block b6, on top of block b5, block b2 is stacked, from top of block b3, block b6 is unstacked, the hand puts down the block b6, block b3 is unstacked from block b4, on top of block b9, block b3 is stacked, block b8 is unstacked from block b1, block b8 is stacked on top of block b4, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b8, from top of block b9, block b3 is unstacked, on top of block b2, block b3 is stacked, block b1 is unstacked from block b7, on top of block b3, block b1 is stacked, block b7 is picked up from the table, block b7 is stacked on top of block b9 and block b6 is picked up to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for b6? Write None if there are none", "answer": "block b1 is not on block b6, block b2 is not on block b6, block b3 is not on block b6, block b4 is not on top of block b6, block b5 is not on block b6, block b6 is being held by the hand, block b6 is not clear, block b6 is not on block b1, block b6 is not on block b2, block b6 is not on block b3, block b6 is not on block b5, block b6 is not on block b8, block b6 is not on the table, block b6 is not on top of block b4, block b6 is not on top of block b7, block b6 is not on top of block b9, block b7 is not on top of block b6, block b8 is not placed on top of block b6 and block b9 is not on block b6", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b7, block b2 is clear, block b2 is on top of block b6, block b3 is on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is on top of block b8 and hand is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b9 is removed from block b8, the hand places block b9, block b2 is removed from block b6 and placed on top of block b5, block b6 is then removed from the top of block b3, and the hand puts down block b6. Next, block b3 is removed from block b4 and placed on top of block b9, block b8 is removed from block b1 and stacked on top of block b4. Then, block b2 is removed from the top of block b5 and stacked on top of block b8. Block b3 is then removed from the top of block b9 and stacked on top of block b2. Block b1 is removed from block b7 and stacked on top of block b3. Finally, block b7 is picked up from the table and stacked on top of block b9, and block b6 is picked up to reach the current state. In this state, what are the valid properties of the state (both with and without negations) for b6? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is positioned on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is stacked on block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "2488a0ab-31e4-43a6-b81d-45a865168b63", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: block b1 is located on the table, block b2 is on the table, block b3 is located on the table, block b4 is located on the table, block b5 is on the table, block b6 is located on the table, block b7 is on the table and block b8 is on the table?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is on the table, block b6 is placed on top of block b7, block b7 is located on the table, block b8 is on the table and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b4 is unstacked from the top of block b1 to reach the current state. In this state, are all of the following properties of the state that do not involve negations True or False: block b1 is on the table, block b2 is on the table, block b3 is on the table, block b4 is on the table, block b5 is on the table, block b6 is on the table, block b7 is on the table, and block b8 is on the table?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b6, block b2 has no blocks on it, block b2 is stacked on block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is resting on the table, block b6 is placed on top of block b7, block b7 is situated on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "eed2a405-00ed-4bf4-8153-d35710bc1590", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_3", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8 to reach the current state. In this state, is it True or False that block b2 is not located on the table?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b7, block b2 is clear, block b2 is on top of block b6, block b3 is on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is on top of block b8 and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b9 is removed from the top of block b8 to reach the current state. In this state, is it True or False that block b2 is not on the table?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is positioned on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is stacked on block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "df134c2d-dcb7-4be0-a7b9-a4dc7734e1a6", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_12", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, block b3 is put down, from top of block b6, block b2 is unstacked, the hand puts down the block b2, block b6 is picked up by the hand, on top of block b3, block b6 is stacked, from top of block b7, block b5 is unstacked, on top of block b6, block b5 is stacked, block b7 is unstacked from block b1, block b7 is put down on the table, block b1 is unstacked from top of block b4, block b1 is stacked on top of block b2, block b4 is picked up by the hand, on top of block b5, block b4 is stacked, from top of block b2, block b1 is unstacked, on top of block b4, block b1 is stacked, block b2 is picked up from the table, on top of block b1, block b2 is stacked and block b7 is picked up from the table to reach the current state. In this state, is it True or False that block b4 is not on block b5?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from block b5, then placed on the table, block b2 is removed from the top of block b6, and then put down by the hand, block b6 is picked up by the hand and placed on top of block b3, block b5 is removed from the top of block b7 and stacked on top of block b6, block b7 is removed from block b1 and placed on the table, block b1 is removed from the top of block b4 and stacked on top of block b2, block b4 is picked up by the hand and stacked on top of block b5, block b1 is removed from the top of block b2 and stacked on top of block b4, block b2 is picked up from the table and stacked on top of block b1, and finally, block b7 is picked up from the table to reach the current state. In this state, is it True or False that block b4 is not on block b5?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is placed on block b6, block b3 has no blocks on it, block b3 is placed on top of block b5, block b4 is on the table, block b5 is placed on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and the hand is empty."}
{"question_id": "daab7551-9b64-4491-99dc-f5cb3f6bbfc9", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: block b5 is located on the table, block b7 is located on the table and block b8 is located on the table?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is on the table, block b6 is placed on top of block b7, block b7 is located on the table, block b8 is on the table and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b4 is unstacked from the top of block b1 to reach the current state. In this state, are the following properties, which do not involve negations, True or False: block b5 is on the table, block b7 is on the table, and block b8 is on the table?", "initial_state_nl_paraphrased": "Block b1 is positioned above block b6, block b2 has no blocks on top of it, block b2 is stacked on block b5, block b3 has no blocks on top of it, block b3 is placed on top of block b8, block b4 has no blocks on top of it, block b4 is stacked on block b1, block b5 is resting on the table, block b6 is placed on top of block b7, block b7 is situated on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "7bdf2dc6-2066-4831-b010-833f8a841c2b", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked, block b4 is put down, block b1 is unstacked from top of block b6, block b1 is put down on the table, block b2 is unstacked from block b5, block b2 is stacked on top of block b4, block b6 is unstacked from block b7, the hand puts down the block b6, block b7 is picked up, on top of block b2, block b7 is stacked, block b3 is unstacked from top of block b8, block b3 is stacked on top of block b7, block b1 is picked up, on top of block b3, block b1 is stacked, block b5 is picked up, on top of block b1, block b5 is stacked, block b8 is picked up, block b8 is stacked on top of block b5 and block b6 is picked up by the hand to reach the current state. In this state, what are the valid properties of the state that do not involve negations for b6? Write None if there are none", "answer": "the hand is holding the block b6", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is on the table, block b6 is placed on top of block b7, block b7 is located on the table, block b8 is on the table and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b4 is removed from the top of block b1 and placed on the table, then block b1 is unstacked from the top of block b6 and placed on the table. Next, block b2 is unstacked from block b5 and stacked on top of block b4. Block b6 is then unstacked from block b7 and placed on the table, after which block b7 is picked up and stacked on top of block b2. Block b3 is unstacked from the top of block b8 and stacked on top of block b7. Block b1 is then picked up and stacked on top of block b3, followed by block b5 being picked up and stacked on top of block b1. Block b8 is then picked up and stacked on top of block b5, and finally, block b6 is picked up by the hand to reach the current state. In this state, what are the valid properties of the state that do not involve negations for b6? Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b6, block b2 has no blocks on it, block b2 is stacked on block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is resting on the table, block b6 is placed on top of block b7, block b7 is situated on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "2da5176c-ee60-4bb8-8e43-c2f7ad7b1813", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_4", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked to reach the current state. In this state, is it True or False that block b1 is not on the table?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b3 is unstacked from the top of block b7 to reach the current state. In this state, is it True or False that block b1 is not on the table?", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 is not obstructed, block b2 is also on the table, block b3 is not obstructed, block b3 is positioned above block b7, block b4 is positioned above block b1, block b5 is not obstructed, block b5 is placed above block b4, block b6 is on the table, block b7 is positioned on block b6, and the hand is empty."}
{"question_id": "103f0f7a-2a37-4ce1-bb7c-820c29b41305", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, the hand puts down the block b3, block b2 is unstacked from top of block b6, block b2 is put down on the table, block b6 is picked up from the table, block b6 is stacked on top of block b3, block b5 is unstacked from block b7, on top of block b6, block b5 is stacked, from top of block b1, block b7 is unstacked, the hand puts down the block b7, block b1 is unstacked from block b4, block b1 is stacked on top of block b2, block b4 is picked up from the table, on top of block b5, block b4 is stacked, block b1 is unstacked from block b2, block b1 is stacked on top of block b4, block b2 is picked up by the hand, block b2 is stacked on top of block b1 and block b7 is picked up by the hand to reach the current state. In this state, is it True or False that block b3 is located on the table?", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from block b5, then placed on the table by the hand, block b2 is removed from the top of block b6 and put down on the table, block b6 is picked up from the table and stacked on top of block b3, block b5 is removed from block b7 and stacked on top of block b6, block b7 is removed from the top of block b1 and placed on the table, block b1 is removed from block b4 and stacked on top of block b2, block b4 is picked up from the table and stacked on top of block b5, block b1 is then removed from block b2 and stacked on top of block b4, block b2 is picked up by the hand and stacked on top of block b1, and finally, block b7 is picked up by the hand to reach the current state. In this state, is it True or False that block b3 is located on the table?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 has no blocks on it, block b3 is positioned on top of block b5, block b4 is resting on the table, block b5 is positioned on top of block b7, block b6 is resting on the table, block b7 is placed on top of block b1 and the hand is empty."}
{"question_id": "e4296691-99ff-4093-afe2-07679fc5834c", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_16", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: block b1 is not being held, block b1 is not on block b3, block b1 is not on top of block b2, block b1 is not on top of block b4, block b1 is not on top of block b5, block b1 is not on top of block b6, block b1 is not on top of block b7, block b1 is not placed on top of block b8, block b2 is not being held by the hand, block b2 is not on block b3, block b2 is not on block b7, block b2 is not on top of block b6, block b2 is not on top of block b8, block b2 is not placed on top of block b1, block b2 is not placed on top of block b4, block b2 is not placed on top of block b5, block b3 is not on block b4, block b3 is not on block b6, block b3 is not on top of block b8, block b3 is not placed on top of block b1, block b3 is not placed on top of block b2, block b3 is not placed on top of block b5, block b3 is not placed on top of block b7, block b4 is not being held, block b4 is not on block b2, block b4 is not on block b7, block b4 is not on block b8, block b4 is not on top of block b3, block b4 is not placed on top of block b1, block b4 is not placed on top of block b5, block b4 is not placed on top of block b6, block b5 is not being held, block b5 is not on block b4, block b5 is not on top of block b1, block b5 is not on top of block b3, block b5 is not on top of block b6, block b5 is not on top of block b7, block b5 is not on top of block b8, block b5 is not placed on top of block b2, block b6 is not being held by the hand, block b6 is not on block b3, block b6 is not on block b8, block b6 is not on top of block b1, block b6 is not on top of block b2, block b6 is not on top of block b5, block b6 is not placed on top of block b4, block b6 is not placed on top of block b7, block b7 is not being held by the hand, block b7 is not on block b1, block b7 is not on block b3, block b7 is not on block b4, block b7 is not on block b6, block b7 is not on top of block b2, block b7 is not placed on top of block b5, block b7 is not placed on top of block b8, block b8 is not being held by the hand, block b8 is not on block b2, block b8 is not on block b6, block b8 is not on top of block b3, block b8 is not on top of block b5, block b8 is not placed on top of block b1, block b8 is not placed on top of block b4, block b8 is not placed on top of block b7 and the hand is not holding the block b3?", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is on the table, block b6 is placed on top of block b7, block b7 is located on the table, block b8 is on the table and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from the top of block b1, block b4 is unstacked to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: block b1 is not held, block b1 is not on block b3, block b1 is not above block b2, block b1 is not above block b4, block b1 is not above block b5, block b1 is not above block b6, block b1 is not above block b7, block b1 is not on top of block b8, the hand is not holding block b2, block b2 is not on block b3, block b2 is not on block b7, block b2 is not above block b6, block b2 is not above block b8, block b2 is not on top of block b1, block b2 is not on top of block b4, block b2 is not on top of block b5, block b3 is not on block b4, block b3 is not on block b6, block b3 is not above block b8, block b3 is not on top of block b1, block b3 is not on top of block b2, block b3 is not on top of block b5, block b3 is not on top of block b7, block b4 is not held, block b4 is not on block b2, block b4 is not on block b7, block b4 is not on block b8, block b4 is not above block b3, block b4 is not on top of block b1, block b4 is not on top of block b5, block b4 is not on top of block b6, block b5 is not held, block b5 is not on block b4, block b5 is not above block b1, block b5 is not above block b3, block b5 is not above block b6, block b5 is not above block b7, block b5 is not above block b8, block b5 is not on top of block b2, block b6 is not held by the hand, block b6 is not on block b3, block b6 is not on block b8, block b6 is not above block b1, block b6 is not above block b2, block b6 is not above block b5, block b6 is not on top of block b4, block b6 is not on top of block b7, block b7 is not held by the hand, block b7 is not on block b1, block b7 is not on block b3, block b7 is not on block b4, block b7 is not on block b6, block b7 is not above block b2, block b7 is not on top of block b5, block b7 is not on top of block b8, block b8 is not held by the hand, block b8 is not on block b2, block b8 is not on block b6, block b8 is not above block b3, block b8 is not above block b5, block b8 is not on top of block b1, block b8 is not on top of block b4, block b8 is not on top of block b7 and the hand is not holding block b3?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is resting on the table, block b6 is placed on top of block b7, block b7 is situated on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "5f7c73a8-6ac7-4a32-902c-1d93679df587", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_14", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, block b3 is put down, block b2 is unstacked from block b6, block b2 is put down on the table, block b6 is picked up from the table, on top of block b3, block b6 is stacked, block b5 is unstacked from block b7, on top of block b6, block b5 is stacked, block b7 is unstacked from top of block b1 and block b7 is put down to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: block b1 is being held by the hand, block b1 is on block b2, block b1 is on block b3, block b1 is on block b5, block b1 is on block b6, block b1 is on top of block b7, block b1 is placed on top of block b4, block b2 is being held by the hand, block b2 is on block b4, block b2 is on top of block b3, block b2 is on top of block b6, block b2 is on top of block b7, block b2 is placed on top of block b1, block b2 is placed on top of block b5, block b3 is being held by the hand, block b3 is on block b4, block b3 is on block b5, block b3 is on top of block b1, block b3 is on top of block b2, block b3 is on top of block b7, block b3 is placed on top of block b6, block b4 is being held, block b4 is on block b1, block b4 is on block b3, block b4 is on block b5, block b4 is on top of block b6, block b4 is placed on top of block b2, block b4 is placed on top of block b7, block b5 is being held, block b5 is on block b7, block b5 is on top of block b4, block b5 is on top of block b6, block b5 is placed on top of block b1, block b5 is placed on top of block b2, block b5 is placed on top of block b3, block b6 is being held, block b6 is on block b3, block b6 is on block b4, block b6 is on top of block b1, block b6 is on top of block b2, block b6 is on top of block b5, block b6 is on top of block b7, block b7 is being held by the hand, block b7 is on block b1, block b7 is on block b2, block b7 is on top of block b3, block b7 is on top of block b5, block b7 is placed on top of block b4 and block b7 is placed on top of block b6?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from block b5, block b3 is placed on the table, block b2 is removed from block b6, block b2 is placed on the table, block b6 is picked up from the table and placed on top of block b3, block b5 is removed from block b7 and placed on top of block b6, and block b7 is removed from block b1 and placed on the table to reach the current state. In this state, are the following properties of the state that do not involve negations True or False: block b1 is in the hand, block b1 is on top of block b2, block b1 is on top of block b3, block b1 is on top of block b5, block b1 is on top of block b6, block b1 is on top of block b7, block b1 is placed on top of block b4, block b2 is in the hand, block b2 is on top of block b4, block b2 is on top of block b3, block b2 is on top of block b6, block b2 is on top of block b7, block b2 is placed on top of block b1, block b2 is placed on top of block b5, block b3 is in the hand, block b3 is on top of block b4, block b3 is on top of block b5, block b3 is on top of block b1, block b3 is on top of block b2, block b3 is on top of block b7, block b3 is placed on top of block b6, block b4 is in the hand, block b4 is on top of block b1, block b4 is on top of block b3, block b4 is on top of block b5, block b4 is on top of block b6, block b4 is placed on top of block b2, block b4 is placed on top of block b7, block b5 is in the hand, block b5 is on top of block b7, block b5 is on top of block b4, block b5 is on top of block b6, block b5 is placed on top of block b1, block b5 is placed on top of block b2, block b5 is placed on top of block b3, block b6 is in the hand, block b6 is on top of block b3, block b6 is on top of block b4, block b6 is on top of block b1, block b6 is on top of block b2, block b6 is on top of block b5, block b6 is on top of block b7, block b7 is in the hand, block b7 is on top of block b1, block b7 is on top of block b2, block b7 is on top of block b3, block b7 is on top of block b5, block b7 is placed on top of block b4 and block b7 is placed on top of block b6?", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is placed on block b6, block b3 has no blocks on it, block b3 is placed on top of block b5, block b4 is on the table, block b5 is placed on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and the hand is empty."}
{"question_id": "46d9f67c-c15e-4621-a922-29ebbe6bf643", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_10", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b7, block b3 is put down, block b5 is unstacked from top of block b4, on top of block b2, block b5 is stacked, block b4 is unstacked from top of block b1, block b4 is put down on the table, block b5 is unstacked from top of block b2, block b5 is stacked on top of block b1, from top of block b6, block b7 is unstacked, block b7 is stacked on top of block b4, block b6 is picked up from the table, on top of block b5, block b6 is stacked, block b2 is picked up by the hand, block b2 is stacked on top of block b6, block b3 is picked up from the table, block b3 is stacked on top of block b2, block b7 is unstacked from block b4, block b7 is stacked on top of block b3 and block b4 is picked up to reach the current state. In this state, is it True or False that block b1 is placed on top of block b7?", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from block b7, then placed on the table, block b5 is removed from block b4 and placed on top of block b2, block b4 is then removed from block b1 and placed on the table, block b5 is removed from block b2 and stacked on block b1, block b7 is removed from block b6 and stacked on block b4, block b6 is picked up from the table and stacked on block b5, block b2 is picked up and stacked on block b6, block b3 is picked up from the table and stacked on block b2, block b7 is removed from block b4 and stacked on block b3, and finally, block b4 is picked up to reach the current state. In this state, is it True or False that block b1 is placed on top of block b7?", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 is not stacked, block b2 is also on the table, block b3 is not stacked, block b3 is positioned above block b7, block b4 is stacked on top of block b1, block b5 is not stacked, block b5 is placed above block b4, block b6 is on the table, block b7 is stacked on block b6, and the hand is empty."}
{"question_id": "a3ca0234-b3c5-4e13-8eeb-dd0056d6a2e3", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_9", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8, block b9 is put down, block b2 is unstacked from block b6, on top of block b5, block b2 is stacked, from top of block b3, block b6 is unstacked, block b6 is put down on the table, from top of block b4, block b3 is unstacked, block b3 is stacked on top of block b9, block b8 is unstacked from block b1 and on top of block b4, block b8 is stacked to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: block b4 is not clear, block b5 is not clear, block b7 is not clear and block b9 is not clear?", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b7, block b2 is clear, block b2 is on top of block b6, block b3 is on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is on the table, block b6 is on top of block b3, block b7 is located on the table, block b8 is on block b1, block b9 is clear, block b9 is on top of block b8 and hand is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b9 is removed from the top of block b8, then placed down, block b2 is removed from block b6 and placed on top of block b5, block b6 is then removed from the top of block b3 and placed on the table, block b3 is removed from the top of block b4 and stacked on top of block b9, and finally, block b8 is removed from block b1 and stacked on top of block b4 to achieve the current state. In this state, are the following properties that involve negations True or False: block b4 is not clear, block b5 is not clear, block b7 is not clear, and block b9 is not clear?", "initial_state_nl_paraphrased": "Block b1 is stacked on block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is positioned on the table, block b5 has no blocks on it, block b5 is positioned on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is stacked on block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "362d31f8-2629-4388-b8a5-d9695697764a", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked, block b3 is put down, from top of block b4, block b5 is unstacked, on top of block b2, block b5 is stacked, block b4 is unstacked from block b1, block b4 is put down on the table, block b5 is unstacked from block b2, on top of block b1, block b5 is stacked, block b7 is unstacked from top of block b6 and on top of block b4, block b7 is stacked to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: block b1 is clear, block b2 is clear, block b3 is clear, block b4 is clear, block b5 is clear, block b6 is clear, block b7 is clear and hand is not holding anything?", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is placed on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is placed on top of block b4, block b6 is on the table, block b7 is on block b6 and hand is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b7 and placed on the table, block b5 is removed from the top of block b4 and placed on top of block b2, block b4 is removed from block b1 and placed on the table, block b5 is then removed from block b2 and placed on top of block b1, and finally, block b7 is removed from the top of block b6 and placed on top of block b4, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: block b1 is clear, block b2 is clear, block b3 is clear, block b4 is clear, block b5 is clear, block b6 is clear, block b7 is clear, and the hand is empty?", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 is not obstructed, block b2 is also on the table, block b3 is not obstructed, block b3 is positioned above block b7, block b4 is stacked on top of block b1, block b5 is clear, block b5 is placed above block b4, block b6 is resting on the table, block b7 is positioned on block b6, and the hand is currently empty."}
