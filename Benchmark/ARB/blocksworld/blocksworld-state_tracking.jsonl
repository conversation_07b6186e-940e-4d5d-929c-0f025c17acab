{"question_id": "fcf1c93a-4672-4858-92bc-e2c54b4b292c", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "block b1 is on block b4, block b2 is clear, block b2 is placed on top of block b6, block b4 is on the table, block b5 is clear, block b5 is placed on top of block b7, block b6 is on the table, block b7 is placed on top of block b1 and the hand is holding the block b3", "plan_length": 1, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: starting from the top of block b5, block b3 is removed to achieve the current state. In this state, identify all valid properties that do not include negations, or state None if there are no such properties.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has nothing on it, block b2 is positioned above block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is situated on the table, block b5 is resting on block b7, block b6 is also on the table, block b7 is supporting block b1, and the hand is empty."}
{"question_id": "8023a968-9046-460f-8564-fd7e215be949", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8, block b9 is put down on the table, from top of block b6, block b2 is unstacked, on top of block b5, block b2 is stacked, from top of block b3, block b6 is unstacked, the hand puts down the block b6, from top of block b4, block b3 is unstacked, on top of block b9, block b3 is stacked, from top of block b1, block b8 is unstacked and on top of block b4, block b8 is stacked to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "block b1 is not being held, block b1 is not located on the table, block b1 is not on block b9, block b1 is not on top of block b3, block b1 is not on top of block b4, block b1 is not placed on top of block b2, block b1 is not placed on top of block b5, block b1 is not placed on top of block b6, block b1 is not placed on top of block b8, block b2 is not located on the table, block b2 is not on block b1, block b2 is not on block b4, block b2 is not on top of block b6, block b2 is not placed on top of block b3, block b2 is not placed on top of block b7, block b2 is not placed on top of block b8, block b2 is not placed on top of block b9, block b3 is not located on the table, block b3 is not on block b1, block b3 is not on block b2, block b3 is not on block b5, block b3 is not on block b7, block b3 is not on top of block b4, block b3 is not on top of block b8, block b3 is not placed on top of block b6, block b4 is not clear, block b4 is not on block b2, block b4 is not on block b6, block b4 is not on top of block b1, block b4 is not on top of block b3, block b4 is not on top of block b5, block b4 is not on top of block b7, block b4 is not on top of block b8, block b4 is not placed on top of block b9, block b5 is not being held, block b5 is not clear, block b5 is not on block b1, block b5 is not on block b6, block b5 is not on block b7, block b5 is not on top of block b2, block b5 is not on top of block b3, block b5 is not on top of block b4, block b5 is not on top of block b8, block b5 is not on top of block b9, block b6 is not being held, block b6 is not on block b7, block b6 is not on block b8, block b6 is not on block b9, block b6 is not on top of block b1, block b6 is not on top of block b3, block b6 is not on top of block b4, block b6 is not placed on top of block b2, block b6 is not placed on top of block b5, block b7 is not being held by the hand, block b7 is not clear, block b7 is not on block b6, block b7 is not on top of block b2, block b7 is not on top of block b3, block b7 is not on top of block b4, block b7 is not on top of block b9, block b7 is not placed on top of block b1, block b7 is not placed on top of block b5, block b7 is not placed on top of block b8, block b8 is not being held by the hand, block b8 is not located on the table, block b8 is not on block b2, block b8 is not on block b3, block b8 is not on top of block b1, block b8 is not on top of block b6, block b8 is not on top of block b7, block b8 is not placed on top of block b5, block b8 is not placed on top of block b9, block b9 is not clear, block b9 is not on block b1, block b9 is not on block b3, block b9 is not on top of block b6, block b9 is not placed on top of block b2, block b9 is not placed on top of block b4, block b9 is not placed on top of block b5, block b9 is not placed on top of block b7, block b9 is not placed on top of block b8, the hand is not holding the block b2, the hand is not holding the block b3, the hand is not holding the block b4 and the hand is not holding the block b9", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is placed on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b9 is removed from the top of block b8, then placed on the table, block b2 is unstacked from the top of block b6 and stacked on top of block b5, block b6 is unstacked from the top of block b3 and put down, block b3 is unstacked from the top of block b4 and stacked on top of block b9, and finally, block b8 is unstacked from the top of block b1 and stacked on top of block b4 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is also situated on the table, block b6 is stacked on block b3, block b7 is resting on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "366e4e2f-b4b1-404c-a21f-3a25c63e34ab", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is clear, block b1 is not being held, block b1 is not on block b5, block b1 is not on block b7, block b1 is not on the table, block b1 is not on top of block b3, block b1 is not on top of block b8, block b1 is not placed on top of block b2, block b1 is not placed on top of block b4, block b1 is on block b6, block b2 is clear, block b2 is not being held by the hand, block b2 is not on block b8, block b2 is not on the table, block b2 is not on top of block b1, block b2 is not on top of block b3, block b2 is not on top of block b4, block b2 is not on top of block b7, block b2 is not placed on top of block b6, block b2 is on top of block b5, block b3 is clear, block b3 is not being held, block b3 is not on block b2, block b3 is not on block b7, block b3 is not on the table, block b3 is not on top of block b5, block b3 is not placed on top of block b1, block b3 is not placed on top of block b4, block b3 is not placed on top of block b6, block b3 is on block b8, block b4 is being held, block b4 is not clear, block b4 is not on block b3, block b4 is not on block b5, block b4 is not on block b7, block b4 is not on the table, block b4 is not on top of block b1, block b4 is not on top of block b6, block b4 is not on top of block b8, block b4 is not placed on top of block b2, block b5 is not being held by the hand, block b5 is not clear, block b5 is not on block b4, block b5 is not on top of block b1, block b5 is not on top of block b6, block b5 is not placed on top of block b2, block b5 is not placed on top of block b3, block b5 is not placed on top of block b7, block b5 is not placed on top of block b8, block b5 is on the table, block b6 is not clear, block b6 is not on block b4, block b6 is not on block b8, block b6 is not on the table, block b6 is not on top of block b1, block b6 is not on top of block b2, block b6 is not placed on top of block b3, block b6 is not placed on top of block b5, block b6 is on top of block b7, block b7 is not being held, block b7 is not clear, block b7 is not on block b8, block b7 is not on top of block b1, block b7 is not on top of block b2, block b7 is not on top of block b5, block b7 is not on top of block b6, block b7 is not placed on top of block b3, block b7 is not placed on top of block b4, block b7 is on the table, block b8 is not being held, block b8 is not clear, block b8 is not on block b2, block b8 is not on block b3, block b8 is not on block b5, block b8 is not on top of block b1, block b8 is not on top of block b6, block b8 is not placed on top of block b4, block b8 is not placed on top of block b7, block b8 is on the table, hand is not empty and the hand is not holding the block b6. Respond with True or False.", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is clear, block b1 is not being grasped, block b1 is not on block b5, block b1 is not on block b7, block b1 is not on the table, block b1 is not above block b3, block b1 is not above block b8, block b1 is not placed above block b2, block b1 is not placed above block b4, block b1 is on block b6, block b2 is clear, block b2 is not being held by the hand, block b2 is not on block b8, block b2 is not on the table, block b2 is not above block b1, block b2 is not above block b3, block b2 is not above block b4, block b2 is not above block b7, block b2 is not placed above block b6, block b2 is above block b5, block b3 is clear, block b3 is not being grasped, block b3 is not on block b2, block b3 is not on block b7, block b3 is not on the table, block b3 is not above block b5, block b3 is not placed above block b1, block b3 is not placed above block b4, block b3 is not placed above block b6, block b3 is on block b8, block b4 is being grasped, block b4 is not clear, block b4 is not on block b3, block b4 is not on block b5, block b4 is not on block b7, block b4 is not on the table, block b4 is not above block b1, block b4 is not above block b6, block b4 is not above block b8, block b4 is not placed above block b2, block b5 is not being held by the hand, block b5 is not clear, block b5 is not on block b4, block b5 is not above block b1, block b5 is not above block b6, block b5 is not placed above block b2, block b5 is not placed above block b3, block b5 is not placed above block b7, block b5 is not placed above block b8, block b5 is on the table, block b6 is not clear, block b6 is not on block b4, block b6 is not on block b8, block b6 is not on the table, block b6 is not above block b1, block b6 is not above block b2, block b6 is not placed above block b3, block b6 is not placed above block b5, block b6 is above block b7, block b7 is not being grasped, block b7 is not clear, block b7 is not on block b8, block b7 is not above block b1, block b7 is not above block b2, block b7 is not above block b5, block b7 is not above block b6, block b7 is not placed above block b3, block b7 is not placed above block b4, block b7 is on the table, block b8 is not being grasped, block b8 is not clear, block b8 is not on block b2, block b8 is not on block b3, block b8 is not on block b5, block b8 is not above block b1, block b8 is not above block b6, block b8 is not placed above block b4, block b8 is not placed above block b7, block b8 is on the table, the hand is not empty and the hand is not holding block b6. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is sitting on the table, block b8 is sitting on the table, and the hand is empty."}
{"question_id": "7fce4de0-2b23-476f-bacd-45e0c4f3d3c3", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked, block b3 is put down on the table, block b2 is unstacked from top of block b6, block b2 is put down, block b6 is picked up, on top of block b3, block b6 is stacked, block b5 is unstacked from block b7, block b5 is stacked on top of block b6, block b7 is unstacked from block b1 and block b7 is put down on the table to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is clear, block b1 is not located on the table, block b1 is not on block b2, block b1 is not on block b3, block b1 is not on block b5, block b1 is not on top of block b6, block b1 is not on top of block b7, block b1 is placed on top of block b4, block b2 is clear, block b2 is located on the table, block b2 is not being held, block b2 is not on block b3, block b2 is not on block b6, block b2 is not on top of block b7, block b2 is not placed on top of block b1, block b2 is not placed on top of block b4, block b2 is not placed on top of block b5, block b3 is not being held, block b3 is not clear, block b3 is not on block b1, block b3 is not on block b4, block b3 is not on block b6, block b3 is not on top of block b2, block b3 is not on top of block b5, block b3 is not placed on top of block b7, block b3 is on the table, block b4 is located on the table, block b4 is not being held, block b4 is not clear, block b4 is not on block b5, block b4 is not on block b7, block b4 is not on top of block b3, block b4 is not on top of block b6, block b4 is not placed on top of block b1, block b4 is not placed on top of block b2, block b5 is clear, block b5 is not being held, block b5 is not on block b2, block b5 is not on the table, block b5 is not on top of block b7, block b5 is not placed on top of block b1, block b5 is not placed on top of block b3, block b5 is not placed on top of block b4, block b5 is placed on top of block b6, block b6 is not being held by the hand, block b6 is not clear, block b6 is not on the table, block b6 is not on top of block b1, block b6 is not on top of block b7, block b6 is not placed on top of block b2, block b6 is not placed on top of block b4, block b6 is not placed on top of block b5, block b6 is on top of block b3, block b7 is clear, block b7 is not being held by the hand, block b7 is not on block b5, block b7 is not on top of block b1, block b7 is not on top of block b6, block b7 is not placed on top of block b2, block b7 is not placed on top of block b3, block b7 is not placed on top of block b4, block b7 is on the table, hand is empty and the hand is not holding the block b1. Respond with True or False.", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b5, placed on the table, then block b2 is removed from the top of block b6 and placed on the table, block b6 is picked up and stacked on top of block b3, block b5 is removed from block b7 and stacked on top of block b6, and block b7 is removed from block b1 and placed on the table to reach the current state. In this state, are the following properties (both with and without negations) valid? block b1 is clear, block b1 is not on the table, block b1 is not on top of block b2, block b1 is not on top of block b3, block b1 is not on top of block b5, block b1 is not on top of block b6, block b1 is not on top of block b7, block b1 is on top of block b4, block b2 is clear, block b2 is on the table, block b2 is not being held, block b2 is not on top of block b3, block b2 is not on top of block b6, block b2 is not on top of block b7, block b2 is not on top of block b1, block b2 is not on top of block b4, block b2 is not on top of block b5, block b3 is not being held, block b3 is not clear, block b3 is not on top of block b1, block b3 is not on top of block b4, block b3 is not on top of block b6, block b3 is not on top of block b2, block b3 is not on top of block b5, block b3 is not on top of block b7, block b3 is on the table, block b4 is on the table, block b4 is not being held, block b4 is not clear, block b4 is not on top of block b5, block b4 is not on top of block b7, block b4 is not on top of block b3, block b4 is not on top of block b6, block b4 is not on top of block b1, block b4 is not on top of block b2, block b5 is clear, block b5 is not being held, block b5 is not on top of block b2, block b5 is not on the table, block b5 is not on top of block b7, block b5 is not on top of block b1, block b5 is not on top of block b3, block b5 is not on top of block b4, block b5 is on top of block b6, block b6 is not being held, block b6 is not clear, block b6 is not on the table, block b6 is not on top of block b1, block b6 is not on top of block b7, block b6 is not on top of block b2, block b6 is not on top of block b4, block b6 is not on top of block b5, block b6 is on top of block b3, block b7 is clear, block b7 is not being held, block b7 is not on top of block b5, block b7 is not on top of block b1, block b7 is not on top of block b6, block b7 is not on top of block b2, block b7 is not on top of block b3, block b7 is not on top of block b4, block b7 is on the table, the hand is empty and the hand is not holding block b1. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has nothing on it, block b2 is positioned above block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is situated on the table, block b5 is resting on block b7, block b6 is also on the table, block b7 is supporting block b1, and the hand is empty."}
{"question_id": "1fc03397-4a3d-4b21-b71e-8db5ac81d809", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b7 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is being held, block b1 is not clear, block b1 is not on block b3, block b1 is not on top of block b2, block b1 is not on top of block b6, block b1 is not placed on top of block b4, block b1 is not placed on top of block b5, block b1 is on block b7, block b1 is on the table, block b2 is not clear, block b2 is not on block b5, block b2 is not on the table, block b2 is not on top of block b3, block b2 is not on top of block b6, block b2 is not placed on top of block b7, block b2 is on block b1, block b2 is placed on top of block b4, block b3 is clear, block b3 is not on block b6, block b3 is not on the table, block b3 is not placed on top of block b2, block b3 is not placed on top of block b5, block b3 is on block b1, block b3 is on block b4, block b3 is placed on top of block b7, block b4 is clear, block b4 is located on the table, block b4 is not being held by the hand, block b4 is not on block b5, block b4 is not on block b6, block b4 is not placed on top of block b1, block b4 is on block b3, block b4 is on top of block b7, block b4 is placed on top of block b2, block b5 is being held, block b5 is clear, block b5 is not located on the table, block b5 is not on block b1, block b5 is not on block b3, block b5 is not on top of block b4, block b5 is not placed on top of block b2, block b5 is on block b6, block b5 is on top of block b7, block b6 is clear, block b6 is not being held, block b6 is not on block b2, block b6 is not on the table, block b6 is not on top of block b4, block b6 is not placed on top of block b5, block b6 is on block b1, block b6 is on block b3, block b6 is on block b7, block b7 is not clear, block b7 is not on block b1, block b7 is not on top of block b6, block b7 is not placed on top of block b2, block b7 is not placed on top of block b4, block b7 is on the table, block b7 is placed on top of block b3, block b7 is placed on top of block b5, hand is not empty, the hand is holding the block b7, the hand is not holding the block b2 and the hand is not holding the block b3. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on block b4, block b6 is on the table, block b7 is on block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b3 is removed from block b7 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is being grasped, block b1 is not unobstructed, block b1 is not on top of block b3, block b1 is not above block b2, block b1 is not above block b6, block b1 is not positioned on top of block b4, block b1 is not positioned on top of block b5, block b1 is on block b7, block b1 is on the table, block b2 is not unobstructed, block b2 is not on block b5, block b2 is not on the table, block b2 is not above block b3, block b2 is not above block b6, block b2 is not positioned on top of block b7, block b2 is on block b1, block b2 is positioned on top of block b4, block b3 is unobstructed, block b3 is not on block b6, block b3 is not on the table, block b3 is not positioned on top of block b2, block b3 is not positioned on top of block b5, block b3 is on block b1, block b3 is on block b4, block b3 is positioned on top of block b7, block b4 is unobstructed, block b4 is located on the table, block b4 is not being grasped by the hand, block b4 is not on block b5, block b4 is not on block b6, block b4 is not positioned on top of block b1, block b4 is on block b3, block b4 is above block b7, block b4 is positioned on top of block b2, block b5 is being grasped, block b5 is unobstructed, block b5 is not located on the table, block b5 is not on block b1, block b5 is not on block b3, block b5 is not above block b4, block b5 is not positioned on top of block b2, block b5 is on block b6, block b5 is above block b7, block b6 is unobstructed, block b6 is not being grasped, block b6 is not on block b2, block b6 is not on the table, block b6 is not above block b4, block b6 is not positioned on top of block b5, block b6 is on block b1, block b6 is on block b3, block b6 is on block b7, block b7 is not unobstructed, block b7 is not on block b1, block b7 is not above block b6, block b7 is not positioned on top of block b2, block b7 is not positioned on top of block b4, block b7 is on the table, block b7 is positioned on top of block b3, block b7 is positioned on top of block b5, the hand is not empty, the hand is holding block b7, the hand is not holding block b2 and the hand is not holding block b3. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is positioned on the table, block b2 is not stacked, block b2 is also on the table, block b3 is not stacked, block b3 is placed on block b7, block b4 is positioned on top of block b1, block b5 is not stacked, block b5 is placed on block b4, block b6 is on the table, block b7 is positioned on block b6, and the hand is empty."}
{"question_id": "2fbca26b-42d8-4de6-bfbe-d6ac689f3de2", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked, block b3 is put down, block b2 is unstacked from top of block b6, block b2 is put down on the table, block b6 is picked up by the hand, block b6 is stacked on top of block b3, block b5 is unstacked from top of block b7, block b5 is stacked on top of block b6, block b7 is unstacked from top of block b1 and block b7 is put down on the table to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "block b1 is clear, block b1 is on block b4, block b2 is clear, block b2 is located on the table, block b3 is on the table, block b4 is located on the table, block b5 is clear, block b5 is on block b6, block b6 is on block b3, block b7 is clear, block b7 is located on the table and hand is not holding anything", "plan_length": 10, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from the top of block b5 and placed down, then block b2 is removed from the top of block b6 and placed on the table. Next, block b6 is picked up and stacked on top of block b3. Block b5 is then removed from the top of block b7 and stacked on top of block b6. Finally, block b7 is removed from the top of block b1 and placed on the table, resulting in the current state. In this state, list all valid properties that do not involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has nothing on it, block b2 is positioned above block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is situated on the table, block b5 is resting on block b7, block b6 is also on the table, block b7 is supporting block b1, and the hand is empty."}
{"question_id": "46357c76-05ea-4820-8318-aff3fde5c230", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8, the hand puts down the block b9, from top of block b6, block b2 is unstacked, on top of block b5, block b2 is stacked, block b6 is unstacked from block b3, block b6 is put down, block b3 is unstacked from block b4, block b3 is stacked on top of block b9, block b8 is unstacked from top of block b1, block b8 is stacked on top of block b4, block b2 is unstacked from top of block b5, on top of block b8, block b2 is stacked, block b3 is unstacked from block b9, on top of block b2, block b3 is stacked, block b1 is unstacked from top of block b7, block b1 is stacked on top of block b3, block b7 is picked up from the table, on top of block b9, block b7 is stacked and block b6 is picked up from the table to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "block b1 is not located on the table, block b1 is not on block b2, block b1 is not on block b4, block b1 is not on block b6, block b1 is not on top of block b7, block b1 is not on top of block b8, block b1 is not placed on top of block b5, block b1 is not placed on top of block b9, block b2 is not being held by the hand, block b2 is not clear, block b2 is not on block b1, block b2 is not on block b5, block b2 is not on the table, block b2 is not on top of block b6, block b2 is not on top of block b9, block b2 is not placed on top of block b3, block b2 is not placed on top of block b4, block b2 is not placed on top of block b7, block b3 is not clear, block b3 is not located on the table, block b3 is not on block b4, block b3 is not on block b5, block b3 is not on block b8, block b3 is not on block b9, block b3 is not on top of block b1, block b3 is not on top of block b7, block b3 is not placed on top of block b6, block b4 is not being held, block b4 is not clear, block b4 is not on block b2, block b4 is not on block b5, block b4 is not on block b9, block b4 is not on top of block b3, block b4 is not on top of block b8, block b4 is not placed on top of block b1, block b4 is not placed on top of block b6, block b4 is not placed on top of block b7, block b5 is not being held by the hand, block b5 is not on top of block b1, block b5 is not on top of block b2, block b5 is not on top of block b3, block b5 is not on top of block b7, block b5 is not on top of block b8, block b5 is not on top of block b9, block b5 is not placed on top of block b4, block b5 is not placed on top of block b6, block b6 is not clear, block b6 is not located on the table, block b6 is not on block b8, block b6 is not on top of block b1, block b6 is not on top of block b4, block b6 is not on top of block b7, block b6 is not on top of block b9, block b6 is not placed on top of block b2, block b6 is not placed on top of block b3, block b6 is not placed on top of block b5, block b7 is not located on the table, block b7 is not on block b2, block b7 is not on block b5, block b7 is not on block b6, block b7 is not on top of block b3, block b7 is not placed on top of block b1, block b7 is not placed on top of block b4, block b7 is not placed on top of block b8, block b8 is not being held, block b8 is not clear, block b8 is not located on the table, block b8 is not on block b2, block b8 is not on block b6, block b8 is not on block b9, block b8 is not on top of block b3, block b8 is not placed on top of block b1, block b8 is not placed on top of block b5, block b8 is not placed on top of block b7, block b9 is not clear, block b9 is not on block b1, block b9 is not on block b2, block b9 is not on block b3, block b9 is not on top of block b5, block b9 is not on top of block b6, block b9 is not on top of block b7, block b9 is not placed on top of block b4, block b9 is not placed on top of block b8, hand is holding some block, the hand is not holding the block b1, the hand is not holding the block b3, the hand is not holding the block b7 and the hand is not holding the block b9", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is placed on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b9 is removed from the top of block b8, then block b9 is placed down, block b2 is unstacked from the top of block b6, and block b2 is stacked on top of block b5. Next, block b6 is unstacked from block b3, block b6 is put down, block b3 is unstacked from block b4, and block b3 is stacked on top of block b9. Then, block b8 is unstacked from the top of block b1 and stacked on top of block b4. Block b2 is unstacked from the top of block b5 and stacked on top of block b8. Block b3 is unstacked from block b9 and stacked on top of block b2. Block b1 is unstacked from the top of block b7 and stacked on top of block b3. Block b7 is picked up from the table and stacked on top of block b9, and block b6 is picked up from the table to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "a6694e72-f43a-4055-b337-bd6c8eed01eb", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is located on the table, block b1 is not being held by the hand, block b1 is not clear, block b1 is not on block b9, block b1 is not on top of block b6, block b1 is placed on top of block b2, block b1 is placed on top of block b3, block b1 is placed on top of block b4, block b1 is placed on top of block b5, block b1 is placed on top of block b8, block b2 is not located on the table, block b2 is on block b1, block b2 is on block b3, block b2 is on block b7, block b2 is on block b8, block b2 is on top of block b5, block b2 is on top of block b9, block b2 is placed on top of block b4, block b3 is being held, block b3 is located on the table, block b3 is not clear, block b3 is not on top of block b2, block b3 is not on top of block b6, block b3 is not placed on top of block b8, block b3 is on block b1, block b3 is on block b7, block b3 is on block b9, block b3 is placed on top of block b5, block b4 is being held, block b4 is not clear, block b4 is not on block b6, block b4 is not on block b7, block b4 is not on block b8, block b4 is not on top of block b2, block b4 is not placed on top of block b3, block b4 is on block b1, block b4 is placed on top of block b5, block b4 is placed on top of block b9, block b5 is not on block b3, block b5 is not on block b9, block b5 is not on top of block b1, block b5 is not on top of block b6, block b5 is not placed on top of block b4, block b5 is not placed on top of block b8, block b5 is on block b2, block b5 is on top of block b7, block b6 is being held by the hand, block b6 is located on the table, block b6 is not clear, block b6 is not on block b7, block b6 is not on block b9, block b6 is not on top of block b2, block b6 is not on top of block b5, block b6 is not placed on top of block b4, block b6 is on top of block b1, block b6 is placed on top of block b8, block b7 is clear, block b7 is not on block b2, block b7 is not on block b6, block b7 is not on top of block b3, block b7 is not on top of block b9, block b7 is not placed on top of block b1, block b7 is placed on top of block b4, block b7 is placed on top of block b5, block b7 is placed on top of block b8, block b8 is not on block b2, block b8 is not on the table, block b8 is not on top of block b5, block b8 is not placed on top of block b3, block b8 is not placed on top of block b4, block b8 is not placed on top of block b9, block b8 is on top of block b7, block b8 is placed on top of block b6, block b9 is clear, block b9 is located on the table, block b9 is not on block b3, block b9 is not placed on top of block b5, block b9 is not placed on top of block b7, block b9 is not placed on top of block b8, block b9 is on block b6, block b9 is on top of block b1, block b9 is on top of block b4, block b9 is placed on top of block b2, hand is not holding anything, the hand is holding the block b2, the hand is holding the block b7, the hand is not holding the block b5 and the hand is not holding the block b8. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is placed on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from the top of block b8, block b9 is unstacked to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is situated on the table, block b1 is not being grasped by the hand, block b1 is not clear, block b1 is not positioned on block b9, block b1 is not positioned on top of block b6, block b1 is positioned on top of block b2, block b1 is positioned on top of block b3, block b1 is positioned on top of block b4, block b1 is positioned on top of block b5, block b1 is positioned on top of block b8, block b2 is not situated on the table, block b2 is positioned on block b1, block b2 is positioned on block b3, block b2 is positioned on block b7, block b2 is positioned on block b8, block b2 is positioned on top of block b5, block b2 is positioned on top of block b9, block b2 is positioned on top of block b4, block b3 is being grasped, block b3 is situated on the table, block b3 is not clear, block b3 is not positioned on top of block b2, block b3 is not positioned on top of block b6, block b3 is not positioned on top of block b8, block b3 is positioned on block b1, block b3 is positioned on block b7, block b3 is positioned on block b9, block b3 is positioned on top of block b5, block b4 is being grasped, block b4 is not clear, block b4 is not positioned on block b6, block b4 is not positioned on block b7, block b4 is not positioned on block b8, block b4 is not positioned on top of block b2, block b4 is not positioned on top of block b3, block b4 is positioned on block b1, block b4 is positioned on top of block b5, block b4 is positioned on top of block b9, block b5 is not positioned on block b3, block b5 is not positioned on block b9, block b5 is not positioned on top of block b1, block b5 is not positioned on top of block b6, block b5 is not positioned on top of block b4, block b5 is not positioned on top of block b8, block b5 is positioned on block b2, block b5 is positioned on top of block b7, block b6 is being grasped by the hand, block b6 is situated on the table, block b6 is not clear, block b6 is not positioned on block b7, block b6 is not positioned on block b9, block b6 is not positioned on top of block b2, block b6 is not positioned on top of block b5, block b6 is not positioned on top of block b4, block b6 is positioned on top of block b1, block b6 is positioned on top of block b8, block b7 is clear, block b7 is not positioned on block b2, block b7 is not positioned on block b6, block b7 is not positioned on top of block b3, block b7 is not positioned on top of block b9, block b7 is not positioned on top of block b1, block b7 is positioned on top of block b4, block b7 is positioned on top of block b5, block b7 is positioned on top of block b8, block b8 is not positioned on block b2, block b8 is not situated on the table, block b8 is not positioned on top of block b5, block b8 is not positioned on top of block b3, block b8 is not positioned on top of block b4, block b8 is not positioned on top of block b9, block b8 is positioned on top of block b7, block b8 is positioned on top of block b6, block b9 is clear, block b9 is situated on the table, block b9 is not positioned on block b3, block b9 is not positioned on top of block b5, block b9 is not positioned on top of block b7, block b9 is not positioned on top of block b8, block b9 is positioned on block b6, block b9 is positioned on top of block b1, block b9 is positioned on top of block b4, block b9 is positioned on top of block b2, the hand is not grasping anything, the hand is grasping block b2, the hand is grasping block b7, the hand is not grasping block b5 and the hand is not grasping block b8. Respond with True or False.\n\nAnswer: False", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "99e02ff2-be9b-4b59-b0dc-06c450ba7fc5", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, block b3 is put down, block b5 is unstacked from top of block b4, on top of block b2, block b5 is stacked, block b4 is unstacked from top of block b1, the hand puts down the block b4, block b5 is unstacked from block b2, block b5 is stacked on top of block b1, from top of block b6, block b7 is unstacked, on top of block b4, block b7 is stacked, block b6 is picked up, on top of block b5, block b6 is stacked, block b2 is picked up from the table, block b2 is stacked on top of block b6, block b3 is picked up, on top of block b2, block b3 is stacked, from top of block b4, block b7 is unstacked, on top of block b3, block b7 is stacked and block b4 is picked up from the table to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is not located on the table, block b2 is not placed on top of block b6, block b3 is on block b2, block b4 is not being held by the hand, block b5 is on block b1, block b6 is on top of block b5, block b7 is not clear and block b7 is on top of block b3. Respond with True or False.", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on block b4, block b6 is on the table, block b7 is on block b6 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b3 is removed from the top of block b7, block b3 is placed on the table, block b5 is removed from the top of block b4, block b5 is stacked on top of block b2, block b4 is removed from the top of block b1, block b4 is put down by the hand, block b5 is removed from block b2, block b5 is stacked on top of block b1, block b7 is removed from the top of block b6, block b7 is stacked on top of block b4, block b6 is picked up, block b6 is stacked on top of block b5, block b2 is picked up from the table, block b2 is stacked on top of block b6, block b3 is picked up, block b3 is stacked on top of block b2, block b7 is removed from the top of block b4, block b7 is stacked on top of block b3, and block b4 is picked up from the table to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is not on the table, block b2 is not on top of block b6, block b3 is on top of block b2, block b4 is not held by the hand, block b5 is on top of block b1, block b6 is on top of block b5, block b7 is not clear, and block b7 is on top of block b3. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is situated on the table, block b2 is not stacked, block b2 is also on the table, block b3 is not stacked, block b3 is placed on block b7, block b4 is positioned on top of block b1, block b5 is not stacked, block b5 is placed on block b4, block b6 is on the table, block b7 is positioned on block b6, and the hand is empty."}
{"question_id": "4e082e99-20df-4f1b-84f9-01fa8385cf9b", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, block b3 is put down on the table, block b5 is unstacked from block b4, on top of block b2, block b5 is stacked, block b4 is unstacked from block b1, block b4 is put down, from top of block b2, block b5 is unstacked, on top of block b1, block b5 is stacked, block b7 is unstacked from top of block b6 and block b7 is stacked on top of block b4 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "block b1 is not being held, block b1 is not clear, block b1 is not on block b2, block b1 is not on block b5, block b1 is not on block b6, block b1 is not on top of block b4, block b1 is not placed on top of block b3, block b1 is not placed on top of block b7, block b1 is on the table, block b2 is clear, block b2 is not being held by the hand, block b2 is not on block b7, block b2 is not placed on top of block b1, block b2 is not placed on top of block b3, block b2 is not placed on top of block b4, block b2 is not placed on top of block b5, block b2 is not placed on top of block b6, block b2 is on the table, block b3 is clear, block b3 is not being held by the hand, block b3 is not on block b1, block b3 is not on block b2, block b3 is not on block b4, block b3 is not on top of block b6, block b3 is not on top of block b7, block b3 is not placed on top of block b5, block b3 is on the table, block b4 is located on the table, block b4 is not being held, block b4 is not clear, block b4 is not on block b7, block b4 is not on top of block b2, block b4 is not on top of block b3, block b4 is not on top of block b5, block b4 is not placed on top of block b1, block b4 is not placed on top of block b6, block b5 is clear, block b5 is not being held, block b5 is not located on the table, block b5 is not on block b2, block b5 is not on block b7, block b5 is not on top of block b3, block b5 is not on top of block b4, block b5 is not on top of block b6, block b5 is on top of block b1, block b6 is clear, block b6 is not on block b2, block b6 is not on block b3, block b6 is not on block b5, block b6 is not on top of block b1, block b6 is not placed on top of block b4, block b6 is not placed on top of block b7, block b6 is on the table, block b7 is clear, block b7 is not on block b1, block b7 is not on block b3, block b7 is not on the table, block b7 is not on top of block b2, block b7 is not on top of block b5, block b7 is not placed on top of block b6, block b7 is on block b4, hand is not holding anything, the hand is not holding the block b6 and the hand is not holding the block b7", "plan_length": 10, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on block b4, block b6 is on the table, block b7 is on block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from the top of block b7 and placed on the table, block b5 is removed from block b4 and placed on top of block b2, block b4 is removed from block b1 and placed down, block b5 is then removed from block b2 and placed on top of block b1, and finally, block b7 is removed from the top of block b6 and stacked on top of block b4 to achieve the current state. In this state, list all valid properties (including both affirmative and negated properties) of the state. If there are no properties, indicate None.", "initial_state_nl_paraphrased": "Block b1 is positioned on the table, block b2 is not stacked, block b2 is also on the table, block b3 is not stacked, block b3 is placed on block b7, block b4 is positioned on top of block b1, block b5 is not stacked, block b5 is placed on block b4, block b6 is on the table, block b7 is positioned on block b6, and the hand is empty."}
{"question_id": "d0598e21-c9b7-4020-a72e-e2ba658fbd30", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked, block b4 is put down, from top of block b6, block b1 is unstacked, block b1 is put down on the table, from top of block b5, block b2 is unstacked, block b2 is stacked on top of block b4, block b6 is unstacked from block b7, the hand puts down the block b6, block b7 is picked up by the hand, on top of block b2, block b7 is stacked, block b3 is unstacked from block b8, on top of block b7, block b3 is stacked, block b1 is picked up from the table, block b1 is stacked on top of block b3, block b5 is picked up from the table, block b5 is stacked on top of block b1, block b8 is picked up by the hand, on top of block b5, block b8 is stacked and block b6 is picked up by the hand to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is on block b3, block b2 is placed on top of block b4, block b3 is placed on top of block b7, block b4 is located on the table, block b5 is on top of block b1, block b6 is being held by the hand, block b7 is on top of block b2, block b8 is clear and block b8 is on block b5. Respond with True or False.", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b4 is removed from the top of block b1 and placed on the table, then block b1 is removed from the top of block b6 and placed on the table. Next, block b2 is removed from the top of block b5 and stacked on top of block b4. Block b6 is then removed from block b7 and placed on the table, and block b7 is picked up by the hand and stacked on top of block b2. Block b3 is removed from block b8 and stacked on top of block b7. Block b1 is then picked up from the table and stacked on top of block b3, followed by block b5 being picked up from the table and stacked on top of block b1. Block b8 is then picked up by the hand and stacked on top of block b5, and finally, block b6 is picked up by the hand to reach the current state. In this state, are the following properties true without involving negations? block b1 is on top of block b3, block b2 is on top of block b4, block b3 is on top of block b7, block b4 is on the table, block b5 is on top of block b1, block b6 is in the hand, block b7 is on top of block b2, block b8 is clear and block b8 is on top of block b5. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is on the table, block b6 is stacked on block b7, block b7 is on the table, block b8 is on the table, and the hand is empty."}
{"question_id": "387ae617-4d90-40bf-9e10-64366498154f", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is not on the table, block b2 is clear, block b2 is not located on the table, block b3 is not being held, block b4 is on block b1, block b5 is clear, block b5 is not on block b4, block b6 is not on the table, block b7 is clear and block b7 is on block b6. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on block b4, block b6 is on the table, block b7 is on block b6 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b7 to achieve the current state. In this state, are all of the following properties of the state that do not involve negations valid? block b1 is not on the table, block b2 has no blocks on it, block b2 is not on the table, block b3 is not being held, block b4 is stacked on block b1, block b5 has no blocks on it, block b5 is not stacked on block b4, block b6 is not on the table, block b7 has no blocks on it and block b7 is stacked on block b6. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is positioned on the table, block b2 is not stacked, block b2 is also on the table, block b3 is not stacked, block b3 is placed on block b7, block b4 is positioned on top of block b1, block b5 is not stacked, block b5 is placed on block b4, block b6 is on the table, block b7 is positioned on block b6, and the hand is empty."}
{"question_id": "30681951-9bdc-4669-a6c1-676aa3e06b3a", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, block b3 is put down, block b5 is unstacked from top of block b4, block b5 is stacked on top of block b2, block b4 is unstacked from top of block b1, the hand puts down the block b4, from top of block b2, block b5 is unstacked, on top of block b1, block b5 is stacked, block b7 is unstacked from block b6 and block b7 is stacked on top of block b4 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is located on the table, block b1 is not being held by the hand, block b1 is not clear, block b1 is not on block b3, block b1 is not on block b5, block b1 is not on block b7, block b1 is not placed on top of block b2, block b1 is not placed on top of block b4, block b1 is not placed on top of block b6, block b2 is clear, block b2 is not being held, block b2 is not on block b6, block b2 is not on top of block b4, block b2 is not placed on top of block b1, block b2 is not placed on top of block b3, block b2 is not placed on top of block b5, block b2 is not placed on top of block b7, block b2 is on the table, block b3 is clear, block b3 is not being held, block b3 is not on block b1, block b3 is not on block b7, block b3 is not on top of block b2, block b3 is not on top of block b4, block b3 is not on top of block b5, block b3 is not on top of block b6, block b3 is on the table, block b4 is located on the table, block b4 is not being held, block b4 is not clear, block b4 is not on block b3, block b4 is not on block b5, block b4 is not on top of block b2, block b4 is not on top of block b7, block b4 is not placed on top of block b1, block b4 is not placed on top of block b6, block b5 is clear, block b5 is not on block b4, block b5 is not on block b7, block b5 is not on the table, block b5 is not on top of block b3, block b5 is not placed on top of block b2, block b5 is not placed on top of block b6, block b5 is placed on top of block b1, block b6 is clear, block b6 is not being held, block b6 is not on block b7, block b6 is not on top of block b1, block b6 is not on top of block b3, block b6 is not on top of block b4, block b6 is not on top of block b5, block b6 is not placed on top of block b2, block b6 is on the table, block b7 is clear, block b7 is not being held by the hand, block b7 is not located on the table, block b7 is not on block b1, block b7 is not on block b3, block b7 is not on block b5, block b7 is not on top of block b6, block b7 is not placed on top of block b2, block b7 is placed on top of block b4, hand is not holding anything and the hand is not holding the block b5. Respond with True or False.", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on block b4, block b6 is on the table, block b7 is on block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b7, block b3 is placed down, block b5 is removed from the top of block b4, block b5 is stacked on top of block b2, block b4 is removed from the top of block b1, the hand releases block b4, block b5 is then removed from the top of block b2, block b5 is stacked on top of block b1, block b7 is removed from block b6 and block b7 is stacked on top of block b4 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is situated on the table, block b1 is not being grasped by the hand, block b1 is not clear, block b1 is not positioned on block b3, block b1 is not positioned on block b5, block b1 is not positioned on block b7, block b1 is not placed above block b2, block b1 is not placed above block b4, block b1 is not placed above block b6, block b2 is clear, block b2 is not being held, block b2 is not positioned on block b6, block b2 is not positioned above block b4, block b2 is not placed above block b1, block b2 is not placed above block b3, block b2 is not placed above block b5, block b2 is not placed above block b7, block b2 is situated on the table, block b3 is clear, block b3 is not being held, block b3 is not positioned on block b1, block b3 is not positioned on block b7, block b3 is not positioned above block b2, block b3 is not positioned above block b4, block b3 is not positioned above block b5, block b3 is not positioned above block b6, block b3 is situated on the table, block b4 is situated on the table, block b4 is not being held, block b4 is not clear, block b4 is not positioned on block b3, block b4 is not positioned on block b5, block b4 is not positioned above block b2, block b4 is not positioned above block b7, block b4 is not placed above block b1, block b4 is not placed above block b6, block b5 is clear, block b5 is not positioned on block b4, block b5 is not positioned on block b7, block b5 is not situated on the table, block b5 is not positioned above block b3, block b5 is not placed above block b2, block b5 is not placed above block b6, block b5 is placed above block b1, block b6 is clear, block b6 is not being held, block b6 is not positioned on block b7, block b6 is not positioned above block b1, block b6 is not positioned above block b3, block b6 is not positioned above block b4, block b6 is not positioned above block b5, block b6 is not placed above block b2, block b6 is situated on the table, block b7 is clear, block b7 is not being held by the hand, block b7 is not situated on the table, block b7 is not positioned on block b1, block b7 is not positioned on block b3, block b7 is not positioned on block b5, block b7 is not positioned above block b6, block b7 is not placed above block b2, block b7 is placed above block b4, the hand is empty and the hand is not holding block b5. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is positioned on the table, block b2 is not stacked, block b2 is also on the table, block b3 is not stacked, block b3 is placed on block b7, block b4 is positioned on top of block b1, block b5 is not stacked, block b5 is placed on block b4, block b6 is on the table, block b7 is positioned on block b6, and the hand is empty."}
{"question_id": "daff8a81-7ecd-4390-940f-c50eb321c2b3", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b7, the hand puts down the block b3, block b5 is unstacked from top of block b4, block b5 is stacked on top of block b2, from top of block b1, block b4 is unstacked, block b4 is put down, block b5 is unstacked from block b2, on top of block b1, block b5 is stacked, block b7 is unstacked from top of block b6, block b7 is stacked on top of block b4, block b6 is picked up from the table, block b6 is stacked on top of block b5, block b2 is picked up, block b2 is stacked on top of block b6, block b3 is picked up, on top of block b2, block b3 is stacked, from top of block b4, block b7 is unstacked, block b7 is stacked on top of block b3 and block b4 is picked up to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is located on the table, block b1 is not being held by the hand, block b1 is not clear, block b1 is not on block b2, block b1 is not on block b5, block b1 is not on block b6, block b1 is not on block b7, block b1 is not placed on top of block b3, block b1 is not placed on top of block b4, block b2 is not being held by the hand, block b2 is not clear, block b2 is not on block b3, block b2 is not on the table, block b2 is not on top of block b1, block b2 is not on top of block b7, block b2 is not placed on top of block b4, block b2 is not placed on top of block b5, block b2 is on block b6, block b3 is not clear, block b3 is not located on the table, block b3 is not on block b4, block b3 is not on block b5, block b3 is not on top of block b6, block b3 is not placed on top of block b1, block b3 is not placed on top of block b7, block b3 is on block b2, block b4 is being held, block b4 is not clear, block b4 is not on block b5, block b4 is not on block b6, block b4 is not on the table, block b4 is not on top of block b2, block b4 is not on top of block b7, block b4 is not placed on top of block b1, block b4 is not placed on top of block b3, block b5 is not clear, block b5 is not located on the table, block b5 is not on block b6, block b5 is not on top of block b3, block b5 is not on top of block b4, block b5 is not on top of block b7, block b5 is not placed on top of block b2, block b5 is on block b1, block b6 is not being held, block b6 is not clear, block b6 is not on block b1, block b6 is not on block b2, block b6 is not on the table, block b6 is not on top of block b3, block b6 is not on top of block b4, block b6 is not placed on top of block b7, block b6 is placed on top of block b5, block b7 is clear, block b7 is not being held, block b7 is not on block b1, block b7 is not on block b2, block b7 is not on block b4, block b7 is not on the table, block b7 is not on top of block b6, block b7 is not placed on top of block b5, block b7 is on block b3, hand is holding some block, the hand is not holding the block b3 and the hand is not holding the block b5. Respond with True or False.", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on block b4, block b6 is on the table, block b7 is on block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial setup, the following sequence of actions is executed: block b3 is removed from block b7, the hand places block b3 down, block b5 is removed from the top of block b4, block b5 is placed on top of block b2, block b4 is removed from the top of block b1, block b4 is put down, block b5 is removed from block b2, block b5 is placed on top of block b1, block b7 is removed from the top of block b6, block b7 is placed on top of block b4, block b6 is picked up from the table, block b6 is placed on top of block b5, block b2 is picked up, block b2 is placed on top of block b6, block b3 is picked up, block b3 is placed on top of block b2, block b7 is removed from the top of block b4, block b7 is placed on top of block b3, and block b4 is picked up to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is on the table, block b1 is not being held by the hand, block b1 is not clear, block b1 is not on block b2, block b1 is not on block b5, block b1 is not on block b6, block b1 is not on block b7, block b1 is not on top of block b3, block b1 is not on top of block b4, block b2 is not being held by the hand, block b2 is not clear, block b2 is not on block b3, block b2 is not on the table, block b2 is not on top of block b1, block b2 is not on top of block b7, block b2 is not on top of block b4, block b2 is not on top of block b5, block b2 is on block b6, block b3 is not clear, block b3 is not on the table, block b3 is not on block b4, block b3 is not on block b5, block b3 is not on top of block b6, block b3 is not on top of block b1, block b3 is not on top of block b7, block b3 is on block b2, block b4 is being held, block b4 is not clear, block b4 is not on block b5, block b4 is not on block b6, block b4 is not on the table, block b4 is not on top of block b2, block b4 is not on top of block b7, block b4 is not on top of block b1, block b4 is not on top of block b3, block b5 is not clear, block b5 is not on the table, block b5 is not on block b6, block b5 is not on top of block b3, block b5 is not on top of block b4, block b5 is not on top of block b7, block b5 is not on top of block b2, block b5 is on block b1, block b6 is not being held, block b6 is not clear, block b6 is not on block b1, block b6 is not on block b2, block b6 is not on the table, block b6 is not on top of block b3, block b6 is not on top of block b4, block b6 is not on top of block b7, block b6 is on top of block b5, block b7 is clear, block b7 is not being held, block b7 is not on block b1, block b7 is not on block b2, block b7 is not on block b4, block b7 is not on the table, block b7 is not on top of block b6, block b7 is not on top of block b5, block b7 is on block b3, the hand is holding a block, the hand is not holding block b3, and the hand is not holding block b5. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is positioned on the table, block b2 is not stacked, block b2 is also on the table, block b3 is not stacked, block b3 is placed on block b7, block b4 is positioned on top of block b1, block b5 is not stacked, block b5 is placed on block b4, block b6 is on the table, block b7 is positioned on block b6, and the hand is empty."}
{"question_id": "ab5de9a1-a3ed-4f09-9be8-c9d87d499d8d", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked, block b4 is put down, from top of block b6, block b1 is unstacked, block b1 is put down, from top of block b5, block b2 is unstacked, block b2 is stacked on top of block b4, block b6 is unstacked from block b7, block b6 is put down on the table, block b7 is picked up and on top of block b2, block b7 is stacked to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "block b1 is not on block b2, block b1 is not on block b4, block b1 is not on block b6, block b1 is not on top of block b3, block b1 is not on top of block b5, block b1 is not on top of block b7, block b1 is not placed on top of block b8, block b2 is not being held, block b2 is not clear, block b2 is not on block b1, block b2 is not on block b5, block b2 is not on the table, block b2 is not on top of block b3, block b2 is not on top of block b6, block b2 is not on top of block b8, block b2 is not placed on top of block b7, block b3 is not being held by the hand, block b3 is not located on the table, block b3 is not on block b1, block b3 is not on block b2, block b3 is not on top of block b4, block b3 is not on top of block b5, block b3 is not on top of block b6, block b3 is not placed on top of block b7, block b4 is not clear, block b4 is not on block b3, block b4 is not on block b6, block b4 is not on block b8, block b4 is not on top of block b7, block b4 is not placed on top of block b1, block b4 is not placed on top of block b2, block b4 is not placed on top of block b5, block b5 is not on block b2, block b5 is not on block b4, block b5 is not on block b7, block b5 is not on top of block b1, block b5 is not on top of block b3, block b5 is not on top of block b8, block b5 is not placed on top of block b6, block b6 is not being held, block b6 is not on block b2, block b6 is not on block b4, block b6 is not on top of block b5, block b6 is not on top of block b7, block b6 is not on top of block b8, block b6 is not placed on top of block b1, block b6 is not placed on top of block b3, block b7 is not located on the table, block b7 is not on block b1, block b7 is not on block b5, block b7 is not on top of block b3, block b7 is not on top of block b4, block b7 is not on top of block b8, block b7 is not placed on top of block b6, block b8 is not clear, block b8 is not on block b1, block b8 is not on block b3, block b8 is not on block b4, block b8 is not on top of block b5, block b8 is not on top of block b6, block b8 is not placed on top of block b2, block b8 is not placed on top of block b7, the hand is not holding the block b1, the hand is not holding the block b4, the hand is not holding the block b5, the hand is not holding the block b7 and the hand is not holding the block b8", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b4 is removed from the top of block b1 and placed down, then block b1 is removed from the top of block b6 and placed down, next block b2 is removed from the top of block b5, block b2 is stacked on top of block b4, block b6 is removed from block b7 and placed down on the table, block b7 is picked up and stacked on top of block b2 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is on the table, and the hand is empty."}
{"question_id": "54fb2760-b4f1-4245-967d-4677ef71c63c", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked, block b3 is put down on the table, block b2 is unstacked from block b6, block b2 is put down, block b6 is picked up from the table, block b6 is stacked on top of block b3, block b5 is unstacked from block b7, block b5 is stacked on top of block b6, block b7 is unstacked from block b1, the hand puts down the block b7, block b1 is unstacked from top of block b4, block b1 is stacked on top of block b2, block b4 is picked up by the hand, on top of block b5, block b4 is stacked, from top of block b2, block b1 is unstacked, on top of block b4, block b1 is stacked, block b2 is picked up, block b2 is stacked on top of block b1 and block b7 is picked up from the table to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is not on top of block b4, block b2 is not clear, block b2 is on top of block b1, block b3 is on the table, block b4 is not on top of block b5, block b5 is not placed on top of block b6, block b6 is placed on top of block b3 and block b7 is being held by the hand. Respond with True or False.", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b5 and placed on the table, block b2 is removed from block b6 and put down, block b6 is picked up from the table and stacked on top of block b3, block b5 is removed from block b7 and stacked on top of block b6, block b7 is removed from block b1 and put down by the hand, block b1 is removed from the top of block b4 and stacked on top of block b2, block b4 is picked up by the hand and stacked on top of block b5, block b1 is removed from the top of block b2 and stacked on top of block b4, block b2 is picked up and stacked on top of block b1, and block b7 is picked up from the table to reach the current state. In this state, are the following properties of the state valid without involving negations? block b1 is not on top of block b4, block b2 is not clear, block b2 is on top of block b1, block b3 is on the table, block b4 is not on top of block b5, block b5 is not on top of block b6, block b6 is on top of block b3, and block b7 is being held by the hand. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has nothing on it, block b2 is positioned on top of block b6, block b3 has nothing on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on block b7, block b6 is located on the table, block b7 is placed on block b1 and the hand is empty."}
{"question_id": "74b8edf7-492c-46c6-ae5c-4160f5699881", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "block b1 is clear, block b1 is placed on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is on top of block b8, block b4 is being held, block b5 is located on the table, block b6 is on top of block b7, block b7 is on the table and block b8 is located on the table", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: block b4 is removed from the top of block b1 to achieve the current state. In this state, identify all valid properties that do not include negations and list them; if there are no such properties, indicate None.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is on the table, and the hand is empty."}
{"question_id": "f46cf921-e7b6-4116-8cbe-c004d3b059e6", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1, the hand puts down the block b4, from top of block b6, block b1 is unstacked, block b1 is put down, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b4, block b6 is unstacked from top of block b7, the hand puts down the block b6, block b7 is picked up from the table, block b7 is stacked on top of block b2, block b3 is unstacked from top of block b8, block b3 is stacked on top of block b7, block b1 is picked up by the hand, block b1 is stacked on top of block b3, block b5 is picked up by the hand, block b5 is stacked on top of block b1, block b8 is picked up by the hand, block b8 is stacked on top of block b5 and block b6 is picked up by the hand to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is not clear, block b1 is not located on the table, block b1 is not on block b5, block b1 is not on block b6, block b1 is not placed on top of block b2, block b1 is not placed on top of block b4, block b1 is not placed on top of block b7, block b1 is not placed on top of block b8, block b2 is not clear, block b2 is not on block b3, block b2 is not on block b5, block b2 is not on the table, block b2 is not on top of block b1, block b2 is not on top of block b6, block b2 is not on top of block b7, block b2 is not on top of block b8, block b3 is not clear, block b3 is not on block b2, block b3 is not on block b4, block b3 is not on block b5, block b3 is not on the table, block b3 is not on top of block b1, block b3 is not placed on top of block b6, block b3 is not placed on top of block b8, block b4 is not clear, block b4 is not on block b1, block b4 is not on block b2, block b4 is not on block b6, block b4 is not on block b7, block b4 is not on block b8, block b4 is not on top of block b3, block b4 is not placed on top of block b5, block b5 is not being held by the hand, block b5 is not clear, block b5 is not on block b2, block b5 is not on block b8, block b5 is not on the table, block b5 is not on top of block b6, block b5 is not placed on top of block b3, block b5 is not placed on top of block b4, block b5 is not placed on top of block b7, block b6 is not clear, block b6 is not on the table, block b6 is not on top of block b1, block b6 is not on top of block b5, block b6 is not on top of block b8, block b6 is not placed on top of block b2, block b6 is not placed on top of block b3, block b6 is not placed on top of block b4, block b6 is not placed on top of block b7, block b7 is not being held, block b7 is not clear, block b7 is not located on the table, block b7 is not on block b1, block b7 is not on block b5, block b7 is not on top of block b4, block b7 is not placed on top of block b3, block b7 is not placed on top of block b6, block b7 is not placed on top of block b8, block b8 is not being held by the hand, block b8 is not located on the table, block b8 is not on block b2, block b8 is not on top of block b1, block b8 is not on top of block b6, block b8 is not placed on top of block b3, block b8 is not placed on top of block b4, block b8 is not placed on top of block b7, hand is not empty, the hand is not holding the block b1, the hand is not holding the block b2, the hand is not holding the block b3 and the hand is not holding the block b4. Respond with True or False.", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b4 is removed from the top of block b1, the hand places block b4 down, block b1 is removed from the top of block b6, block b1 is put down, block b2 is removed from the top of block b5, block b2 is placed on top of block b4, block b6 is removed from the top of block b7, the hand puts block b6 down, block b7 is picked up from the table, block b7 is placed on top of block b2, block b3 is removed from the top of block b8, block b3 is placed on top of block b7, block b1 is picked up by the hand, block b1 is placed on top of block b3, block b5 is picked up by the hand, block b5 is placed on top of block b1, block b8 is picked up by the hand, block b8 is placed on top of block b5 and block b6 is picked up by the hand to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is not clear, block b1 is not on the table, block b1 is not on block b5, block b1 is not on block b6, block b1 is not on top of block b2, block b1 is not on top of block b4, block b1 is not on top of block b7, block b1 is not on top of block b8, block b2 is not clear, block b2 is not on block b3, block b2 is not on block b5, block b2 is not on the table, block b2 is not on top of block b1, block b2 is not on top of block b6, block b2 is not on top of block b7, block b2 is not on top of block b8, block b3 is not clear, block b3 is not on block b2, block b3 is not on block b4, block b3 is not on block b5, block b3 is not on the table, block b3 is not on top of block b1, block b3 is not on top of block b6, block b3 is not on top of block b8, block b4 is not clear, block b4 is not on block b1, block b4 is not on block b2, block b4 is not on block b6, block b4 is not on block b7, block b4 is not on block b8, block b4 is not on top of block b3, block b4 is not on top of block b5, block b5 is not being held by the hand, block b5 is not clear, block b5 is not on block b2, block b5 is not on block b8, block b5 is not on the table, block b5 is not on top of block b6, block b5 is not on top of block b3, block b5 is not on top of block b4, block b5 is not on top of block b7, block b6 is not clear, block b6 is not on the table, block b6 is not on top of block b1, block b6 is not on top of block b5, block b6 is not on top of block b8, block b6 is not on top of block b2, block b6 is not on top of block b3, block b6 is not on top of block b4, block b6 is not on top of block b7, block b7 is not being held, block b7 is not clear, block b7 is not on the table, block b7 is not on block b1, block b7 is not on block b5, block b7 is not on top of block b4, block b7 is not on top of block b3, block b7 is not on top of block b6, block b7 is not on top of block b8, block b8 is not being held by the hand, block b8 is not on the table, block b8 is not on block b2, block b8 is not on top of block b1, block b8 is not on top of block b6, block b8 is not on top of block b3, block b8 is not on top of block b4, block b8 is not on top of block b7, hand is not empty, the hand is not holding block b1, the hand is not holding block b2, the hand is not holding block b3 and the hand is not holding block b4. Respond with True or False.\n\nAnswer: True", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is sitting on the table, block b8 is sitting on the table, and the hand is empty."}
{"question_id": "d4b14bc2-22fa-41b8-b771-b53305c82691", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked, the hand puts down the block b3, from top of block b6, block b2 is unstacked, block b2 is put down, block b6 is picked up, on top of block b3, block b6 is stacked, block b5 is unstacked from top of block b7, block b5 is stacked on top of block b6, block b7 is unstacked from block b1, the hand puts down the block b7, from top of block b4, block b1 is unstacked, block b1 is stacked on top of block b2, block b4 is picked up by the hand, on top of block b5, block b4 is stacked, block b1 is unstacked from top of block b2, block b1 is stacked on top of block b4, block b2 is picked up, block b2 is stacked on top of block b1 and block b7 is picked up by the hand to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "block b1 is placed on top of block b4, block b2 is clear, block b2 is on block b1, block b3 is located on the table, block b4 is on block b5, block b5 is on block b6, block b6 is on top of block b3 and block b7 is being held", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b3 is removed from the top of block b5 and placed down, then block b2 is unstacked from the top of block b6 and put down. Next, block b6 is picked up and stacked on top of block b3. Block b5 is then unstacked from the top of block b7 and stacked on top of block b6. Block b7 is unstacked from block b1 and placed down. Block b1 is unstacked from the top of block b4 and stacked on top of block b2. Block b4 is picked up and stacked on top of block b5. Block b1 is then unstacked from the top of block b2 and stacked on top of block b4. Block b2 is picked up and stacked on top of block b1, and finally, block b7 is picked up by the hand to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has no blocks on it, block b2 is positioned above block b6, block b3 has no blocks on it, block b3 is placed on block b5, block b4 is situated on the table, block b5 is positioned on block b7, block b6 is on the table, block b7 is placed on block b1 and the hand is empty."}
{"question_id": "e097675a-fb1f-4b86-90ce-9e993bd02929", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is on top of block b7, block b2 is not clear, block b2 is placed on top of block b6, block b3 is not on block b4, block b4 is not on the table, block b5 is not clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is not clear, block b8 is placed on top of block b1 and block b9 is not being held. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is placed on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b9 is removed from the top of block b8 to achieve the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is on top of block b7, block b2 is not clear, block b2 is placed on top of block b6, block b3 is not on block b4, block b4 is not on the table, block b5 is not clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is not clear, block b8 is placed on top of block b1 and block b9 is not being held. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "6627077b-9304-4f30-911a-0fa054d56bbf", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1, block b4 is put down, block b1 is unstacked from top of block b6, block b1 is put down on the table, block b2 is unstacked from block b5, on top of block b4, block b2 is stacked, from top of block b7, block b6 is unstacked, block b6 is put down on the table, block b7 is picked up, on top of block b2, block b7 is stacked, from top of block b8, block b3 is unstacked, on top of block b7, block b3 is stacked, block b1 is picked up by the hand, block b1 is stacked on top of block b3, block b5 is picked up from the table, on top of block b1, block b5 is stacked, block b8 is picked up from the table, on top of block b5, block b8 is stacked and block b6 is picked up from the table to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "block b1 is not being held, block b1 is not clear, block b1 is not on block b2, block b1 is not on block b4, block b1 is not on block b5, block b1 is not on block b8, block b1 is not on the table, block b1 is not on top of block b7, block b1 is not placed on top of block b6, block b1 is on block b3, block b2 is not clear, block b2 is not on the table, block b2 is not on top of block b1, block b2 is not on top of block b3, block b2 is not on top of block b5, block b2 is not on top of block b8, block b2 is not placed on top of block b6, block b2 is not placed on top of block b7, block b2 is on top of block b4, block b3 is not being held by the hand, block b3 is not clear, block b3 is not located on the table, block b3 is not on block b1, block b3 is not on block b2, block b3 is not on top of block b4, block b3 is not on top of block b6, block b3 is not placed on top of block b5, block b3 is not placed on top of block b8, block b3 is on block b7, block b4 is located on the table, block b4 is not clear, block b4 is not on block b7, block b4 is not on top of block b1, block b4 is not on top of block b2, block b4 is not on top of block b5, block b4 is not placed on top of block b3, block b4 is not placed on top of block b6, block b4 is not placed on top of block b8, block b5 is not being held, block b5 is not clear, block b5 is not located on the table, block b5 is not on block b2, block b5 is not on block b3, block b5 is not on block b8, block b5 is not on top of block b4, block b5 is not placed on top of block b6, block b5 is not placed on top of block b7, block b5 is on top of block b1, block b6 is not clear, block b6 is not located on the table, block b6 is not on block b2, block b6 is not on block b3, block b6 is not on top of block b5, block b6 is not on top of block b7, block b6 is not on top of block b8, block b6 is not placed on top of block b1, block b6 is not placed on top of block b4, block b7 is not being held by the hand, block b7 is not clear, block b7 is not located on the table, block b7 is not on block b1, block b7 is not on block b3, block b7 is not on top of block b5, block b7 is not on top of block b8, block b7 is not placed on top of block b4, block b7 is not placed on top of block b6, block b7 is on block b2, block b8 is clear, block b8 is not being held by the hand, block b8 is not located on the table, block b8 is not on block b1, block b8 is not on block b2, block b8 is not on block b6, block b8 is not on top of block b3, block b8 is not placed on top of block b4, block b8 is not placed on top of block b7, block b8 is on block b5, hand is holding some block, the hand is holding the block b6, the hand is not holding the block b2 and the hand is not holding the block b4", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b4 is removed from the top of block b1 and placed down, then block b1 is removed from the top of block b6 and placed on the table. Next, block b2 is removed from block b5 and stacked on top of block b4. Block b6 is then removed from the top of block b7 and placed on the table. Block b7 is picked up and stacked on top of block b2. Block b3 is removed from the top of block b8 and stacked on top of block b7. Block b1 is then picked up and stacked on top of block b3. Block b5 is picked up from the table and stacked on top of block b1, followed by block b8 being picked up from the table and stacked on top of block b5. Finally, block b6 is picked up from the table to achieve the current state. In this state, list all valid properties (both affirmative and negative) that apply, or indicate None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is on the table, and the hand is empty."}
{"question_id": "7f48a4d5-4360-4b04-9a77-98683e1e93fd", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked, block b3 is put down, block b2 is unstacked from block b6, block b2 is put down, block b6 is picked up from the table, block b6 is stacked on top of block b3, block b5 is unstacked from top of block b7, on top of block b6, block b5 is stacked, block b7 is unstacked from top of block b1, block b7 is put down on the table, from top of block b4, block b1 is unstacked, block b1 is stacked on top of block b2, block b4 is picked up, on top of block b5, block b4 is stacked, block b1 is unstacked from block b2, on top of block b4, block b1 is stacked, block b2 is picked up, on top of block b1, block b2 is stacked and block b7 is picked up by the hand to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is not being held, block b1 is not clear, block b1 is not on block b5, block b1 is not on block b7, block b1 is not on the table, block b1 is not on top of block b2, block b1 is not on top of block b6, block b1 is not placed on top of block b3, block b1 is placed on top of block b4, block b2 is clear, block b2 is not located on the table, block b2 is not on block b4, block b2 is not on top of block b3, block b2 is not on top of block b5, block b2 is not on top of block b6, block b2 is not on top of block b7, block b2 is placed on top of block b1, block b3 is located on the table, block b3 is not clear, block b3 is not on block b1, block b3 is not on block b5, block b3 is not on top of block b4, block b3 is not on top of block b6, block b3 is not placed on top of block b2, block b3 is not placed on top of block b7, block b4 is not being held, block b4 is not clear, block b4 is not located on the table, block b4 is not on block b7, block b4 is not placed on top of block b1, block b4 is not placed on top of block b2, block b4 is not placed on top of block b3, block b4 is not placed on top of block b6, block b4 is placed on top of block b5, block b5 is not being held, block b5 is not clear, block b5 is not located on the table, block b5 is not on block b7, block b5 is not on top of block b3, block b5 is not placed on top of block b1, block b5 is not placed on top of block b2, block b5 is not placed on top of block b4, block b5 is placed on top of block b6, block b6 is not being held, block b6 is not clear, block b6 is not on block b1, block b6 is not on block b4, block b6 is not on block b5, block b6 is not on the table, block b6 is not on top of block b2, block b6 is not placed on top of block b7, block b6 is on block b3, block b7 is not clear, block b7 is not located on the table, block b7 is not on block b1, block b7 is not on block b4, block b7 is not on block b5, block b7 is not on top of block b2, block b7 is not on top of block b6, block b7 is not placed on top of block b3, hand is not empty, the hand is holding the block b7, the hand is not holding the block b2 and the hand is not holding the block b3. Respond with True or False.", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial setup, the following steps are taken: block b3 is removed from the top of block b5 and placed on the table, block b2 is removed from block b6 and placed on the table, block b6 is picked up from the table and placed on top of block b3, block b5 is removed from the top of block b7 and placed on top of block b6, block b7 is removed from the top of block b1 and placed on the table, block b1 is removed from the top of block b4 and placed on top of block b2, block b4 is picked up and placed on top of block b5, block b1 is removed from block b2 and placed on top of block b4, block b2 is picked up and placed on top of block b1, and block b7 is picked up by the hand to reach the current state. In this state, are the following properties of the state (both with and without negations) valid? block b1 is not being held by the hand, block b1 is not clear of blocks, block b1 is not on top of block b5, block b1 is not on top of block b7, block b1 is not on the table, block b1 is not on top of block b2, block b1 is not on top of block b6, block b1 is not on top of block b3, block b1 is on top of block b4, block b2 is clear of blocks, block b2 is not on the table, block b2 is not on top of block b4, block b2 is not on top of block b3, block b2 is not on top of block b5, block b2 is not on top of block b6, block b2 is not on top of block b7, block b2 is on top of block b1, block b3 is on the table, block b3 is not clear of blocks, block b3 is not on top of block b1, block b3 is not on top of block b5, block b3 is not on top of block b4, block b3 is not on top of block b6, block b3 is not on top of block b2, block b3 is not on top of block b7, block b4 is not being held by the hand, block b4 is not clear of blocks, block b4 is not on the table, block b4 is not on top of block b7, block b4 is not on top of block b1, block b4 is not on top of block b2, block b4 is not on top of block b3, block b4 is not on top of block b6, block b4 is on top of block b5, block b5 is not being held by the hand, block b5 is not clear of blocks, block b5 is not on the table, block b5 is not on top of block b7, block b5 is not on top of block b3, block b5 is not on top of block b1, block b5 is not on top of block b2, block b5 is not on top of block b4, block b5 is on top of block b6, block b6 is not being held by the hand, block b6 is not clear of blocks, block b6 is not on top of block b1, block b6 is not on top of block b4, block b6 is not on top of block b5, block b6 is not on the table, block b6 is not on top of block b2, block b6 is not on top of block b7, block b6 is on top of block b3, block b7 is not clear of blocks, block b7 is not on the table, block b7 is not on top of block b1, block b7 is not on top of block b4, block b7 is not on top of block b5, block b7 is not on top of block b2, block b7 is not on top of block b6, block b7 is not on top of block b3, the hand is not empty, the hand is holding block b7, the hand is not holding block b2, and the hand is not holding block b3. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has nothing on it, block b2 is positioned above block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is situated on the table, block b5 is resting on block b7, block b6 is also on the table, block b7 is supporting block b1, and the hand is empty."}
{"question_id": "c3a62a85-fddb-44e5-8219-857ba9c81cc4", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, block b3 is put down on the table, block b5 is unstacked from top of block b4, block b5 is stacked on top of block b2, block b4 is unstacked from block b1, block b4 is put down, block b5 is unstacked from block b2, block b5 is stacked on top of block b1, block b7 is unstacked from top of block b6 and on top of block b4, block b7 is stacked to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is located on the table, block b4 is located on the table, block b5 is clear, block b5 is placed on top of block b1, block b6 is clear, block b6 is located on the table, block b7 is clear, block b7 is on block b4 and hand is empty", "plan_length": 10, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on block b4, block b6 is on the table, block b7 is on block b6 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b7, block b3 is placed on the table, block b5 is removed from the top of block b4, block b5 is placed on top of block b2, block b4 is removed from block b1, block b4 is put down, block b5 is removed from block b2, block b5 is placed on top of block b1, block b7 is removed from the top of block b6 and placed on top of block b4, and block b7 is stacked to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is positioned on the table, block b2 is not stacked, block b2 is also on the table, block b3 is not stacked, block b3 is placed on block b7, block b4 is positioned on top of block b1, block b5 is not stacked, block b5 is placed on block b4, block b6 is on the table, block b7 is positioned on block b6, and the hand is empty."}
{"question_id": "f5f1f379-6da9-4523-a336-21000d775ef7", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b5, block b3 is put down on the table, block b2 is unstacked from block b6, the hand puts down the block b2, block b6 is picked up from the table, on top of block b3, block b6 is stacked, from top of block b7, block b5 is unstacked, block b5 is stacked on top of block b6, block b7 is unstacked from block b1, block b7 is put down on the table, block b1 is unstacked from block b4, block b1 is stacked on top of block b2, block b4 is picked up, block b4 is stacked on top of block b5, from top of block b2, block b1 is unstacked, block b1 is stacked on top of block b4, block b2 is picked up by the hand, on top of block b1, block b2 is stacked and block b7 is picked up by the hand to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is located on the table, block b1 is not clear, block b1 is not on top of block b5, block b1 is not placed on top of block b7, block b1 is on block b4, block b1 is on top of block b3, block b1 is on top of block b6, block b1 is placed on top of block b2, block b2 is not clear, block b2 is not on block b3, block b2 is not on the table, block b2 is not placed on top of block b4, block b2 is not placed on top of block b6, block b2 is not placed on top of block b7, block b2 is on top of block b1, block b2 is on top of block b5, block b3 is clear, block b3 is not being held, block b3 is not on top of block b5, block b3 is not placed on top of block b2, block b3 is on the table, block b3 is on top of block b1, block b3 is on top of block b6, block b3 is on top of block b7, block b3 is placed on top of block b4, block b4 is being held by the hand, block b4 is clear, block b4 is not placed on top of block b6, block b4 is on block b1, block b4 is on the table, block b4 is on top of block b2, block b4 is on top of block b5, block b4 is placed on top of block b3, block b4 is placed on top of block b7, block b5 is being held, block b5 is not clear, block b5 is not on the table, block b5 is not on top of block b4, block b5 is not placed on top of block b3, block b5 is on block b6, block b5 is on top of block b1, block b5 is on top of block b2, block b5 is placed on top of block b7, block b6 is being held, block b6 is not clear, block b6 is not located on the table, block b6 is not on top of block b4, block b6 is not on top of block b7, block b6 is not placed on top of block b5, block b6 is on block b1, block b6 is on top of block b2, block b6 is on top of block b3, block b7 is clear, block b7 is not on block b4, block b7 is not placed on top of block b1, block b7 is not placed on top of block b2, block b7 is on block b5, block b7 is on the table, block b7 is on top of block b3, block b7 is on top of block b6, hand is empty, the hand is not holding the block b1, the hand is not holding the block b2 and the hand is not holding the block b7. Respond with True or False.", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b5, block b3 is placed on the table, block b2 is removed from block b6, the hand places block b2 down, block b6 is picked up from the table and placed on top of block b3, block b5 is removed from the top of block b7 and stacked on top of block b6, block b7 is removed from block b1 and placed on the table, block b1 is removed from block b4 and stacked on top of block b2, block b4 is picked up and stacked on top of block b5, block b1 is removed from the top of block b2 and stacked on top of block b4, block b2 is picked up by the hand and stacked on top of block b1, and block b7 is picked up by the hand to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is on the table, block b1 is not clear, block b1 is not on top of block b5, block b1 is not on top of block b7, block b1 is on block b4, block b1 is not on top of block b3, block b1 is not on top of block b6, block b1 is on top of block b2, block b2 is not clear, block b2 is not on block b3, block b2 is not on the table, block b2 is not on top of block b4, block b2 is not on top of block b6, block b2 is not on top of block b7, block b2 is on top of block b1, block b2 is not on top of block b5, block b3 is clear, block b3 is not being held, block b3 is not on top of block b5, block b3 is not on top of block b2, block b3 is on the table, block b3 is not on top of block b1, block b3 is not on top of block b6, block b3 is not on top of block b7, block b3 is not on top of block b4, block b4 is being held by the hand, block b4 is clear, block b4 is not on top of block b6, block b4 is on block b1, block b4 is not on the table, block b4 is on top of block b2, block b4 is on top of block b5, block b4 is not on top of block b3, block b4 is not on top of block b7, block b5 is being held, block b5 is not clear, block b5 is not on the table, block b5 is not on top of block b4, block b5 is not on top of block b3, block b5 is on block b6, block b5 is not on top of block b1, block b5 is not on top of block b2, block b5 is not on top of block b7, block b6 is being held, block b6 is not clear, block b6 is not on the table, block b6 is not on top of block b4, block b6 is not on top of block b7, block b6 is not on top of block b5, block b6 is not on block b1, block b6 is not on top of block b2, block b6 is on top of block b3, block b7 is clear, block b7 is not on block b4, block b7 is not on top of block b1, block b7 is not on top of block b2, block b7 is not on block b5, block b7 is on the table, block b7 is not on top of block b3, block b7 is not on top of block b6, hand is empty, the hand is not holding the block b1, the hand is not holding the block b2 and the hand is not holding the block b7. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has nothing on it, block b2 is positioned above block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is situated on the table, block b5 is resting on block b7, block b6 is also on the table, block b7 is supporting block b1, and the hand is empty."}
{"question_id": "8a23142a-aba1-42d0-ac21-07c29d64a19f", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "block b1 is not being held, block b1 is not clear, block b1 is not on block b5, block b1 is not on block b6, block b1 is not on the table, block b1 is not on top of block b4, block b1 is not on top of block b9, block b1 is not placed on top of block b2, block b1 is not placed on top of block b3, block b1 is not placed on top of block b8, block b1 is placed on top of block b7, block b2 is clear, block b2 is not being held by the hand, block b2 is not located on the table, block b2 is not on block b5, block b2 is not on top of block b1, block b2 is not placed on top of block b3, block b2 is not placed on top of block b4, block b2 is not placed on top of block b7, block b2 is not placed on top of block b8, block b2 is not placed on top of block b9, block b2 is on top of block b6, block b3 is not clear, block b3 is not on the table, block b3 is not on top of block b2, block b3 is not on top of block b5, block b3 is not on top of block b8, block b3 is not placed on top of block b1, block b3 is not placed on top of block b6, block b3 is not placed on top of block b7, block b3 is not placed on top of block b9, block b3 is placed on top of block b4, block b4 is not being held by the hand, block b4 is not clear, block b4 is not on block b3, block b4 is not on block b6, block b4 is not on block b8, block b4 is not on block b9, block b4 is not on top of block b1, block b4 is not on top of block b2, block b4 is not placed on top of block b5, block b4 is not placed on top of block b7, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b5 is not being held, block b5 is not on block b1, block b5 is not on block b4, block b5 is not on block b9, block b5 is not on top of block b6, block b5 is not on top of block b7, block b5 is not placed on top of block b2, block b5 is not placed on top of block b3, block b5 is not placed on top of block b8, block b6 is not being held by the hand, block b6 is not clear, block b6 is not on block b9, block b6 is not on the table, block b6 is not on top of block b8, block b6 is not placed on top of block b1, block b6 is not placed on top of block b2, block b6 is not placed on top of block b4, block b6 is not placed on top of block b5, block b6 is not placed on top of block b7, block b6 is on block b3, block b7 is located on the table, block b7 is not being held by the hand, block b7 is not clear, block b7 is not on block b1, block b7 is not on block b2, block b7 is not on block b5, block b7 is not on top of block b6, block b7 is not on top of block b8, block b7 is not placed on top of block b3, block b7 is not placed on top of block b4, block b7 is not placed on top of block b9, block b8 is clear, block b8 is not being held by the hand, block b8 is not located on the table, block b8 is not on top of block b4, block b8 is not on top of block b7, block b8 is not placed on top of block b2, block b8 is not placed on top of block b3, block b8 is not placed on top of block b5, block b8 is not placed on top of block b6, block b8 is not placed on top of block b9, block b8 is on top of block b1, block b9 is being held by the hand, block b9 is not clear, block b9 is not on block b3, block b9 is not on block b7, block b9 is not on block b8, block b9 is not on the table, block b9 is not on top of block b5, block b9 is not on top of block b6, block b9 is not placed on top of block b1, block b9 is not placed on top of block b2, block b9 is not placed on top of block b4, hand is not empty and the hand is not holding the block b3", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is placed on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: block b9 is removed from the top of block b8 to achieve the current state. In this state, list all the valid properties (including both affirmative and negated properties) of the state. If there are no properties, indicate None.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is also situated on the table, block b6 is stacked on block b3, block b7 is resting on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "0dbeff25-b19d-4ccf-91bd-9bb032b66074", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "block b1 is not being held by the hand, block b1 is not located on the table, block b1 is not on block b3, block b1 is not on top of block b5, block b1 is not on top of block b8, block b1 is not placed on top of block b2, block b1 is not placed on top of block b4, block b1 is not placed on top of block b7, block b2 is not being held, block b2 is not on block b1, block b2 is not on block b8, block b2 is not on the table, block b2 is not on top of block b4, block b2 is not placed on top of block b3, block b2 is not placed on top of block b6, block b2 is not placed on top of block b7, block b3 is not being held by the hand, block b3 is not located on the table, block b3 is not on block b4, block b3 is not on top of block b7, block b3 is not placed on top of block b1, block b3 is not placed on top of block b2, block b3 is not placed on top of block b5, block b3 is not placed on top of block b6, block b4 is not clear, block b4 is not on block b1, block b4 is not on block b3, block b4 is not on block b5, block b4 is not on block b6, block b4 is not on block b7, block b4 is not on the table, block b4 is not on top of block b2, block b4 is not on top of block b8, block b5 is not being held, block b5 is not clear, block b5 is not on block b2, block b5 is not on block b4, block b5 is not on top of block b6, block b5 is not on top of block b7, block b5 is not placed on top of block b1, block b5 is not placed on top of block b3, block b5 is not placed on top of block b8, block b6 is not being held by the hand, block b6 is not clear, block b6 is not located on the table, block b6 is not on block b5, block b6 is not on top of block b1, block b6 is not on top of block b2, block b6 is not on top of block b4, block b6 is not placed on top of block b3, block b6 is not placed on top of block b8, block b7 is not clear, block b7 is not on block b1, block b7 is not on block b2, block b7 is not on block b4, block b7 is not on block b6, block b7 is not on top of block b5, block b7 is not placed on top of block b3, block b7 is not placed on top of block b8, block b8 is not being held by the hand, block b8 is not clear, block b8 is not on top of block b1, block b8 is not on top of block b4, block b8 is not on top of block b6, block b8 is not placed on top of block b2, block b8 is not placed on top of block b3, block b8 is not placed on top of block b5, block b8 is not placed on top of block b7, hand is holding some block and the hand is not holding the block b7", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: block b4 is unstacked from block b1 to achieve the current state. In this state, identify all valid properties that include negations and list them; if there are no such properties, indicate None.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is on the table, and the hand is empty."}
{"question_id": "6d621757-8e0d-4231-90cc-9436acd28cfe", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1, block b4 is put down on the table, block b1 is unstacked from block b6, block b1 is put down on the table, from top of block b5, block b2 is unstacked, on top of block b4, block b2 is stacked, block b6 is unstacked from top of block b7, block b6 is put down, block b7 is picked up by the hand and block b7 is stacked on top of block b2 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is not on block b8, block b1 is not on top of block b2, block b1 is not on top of block b3, block b1 is not on top of block b6, block b1 is not placed on top of block b4, block b1 is not placed on top of block b7, block b1 is on block b5, block b2 is not being held, block b2 is not clear, block b2 is not located on the table, block b2 is not on top of block b1, block b2 is not on top of block b6, block b2 is not placed on top of block b8, block b2 is on block b7, block b2 is on top of block b3, block b2 is on top of block b5, block b3 is not being held by the hand, block b3 is not on the table, block b3 is not on top of block b5, block b3 is on block b1, block b3 is on block b2, block b3 is on top of block b6, block b3 is on top of block b7, block b3 is placed on top of block b4, block b4 is not being held by the hand, block b4 is not clear, block b4 is not on top of block b7, block b4 is not placed on top of block b3, block b4 is on block b1, block b4 is on block b8, block b4 is on top of block b5, block b4 is placed on top of block b2, block b4 is placed on top of block b6, block b5 is not being held, block b5 is not on top of block b1, block b5 is not placed on top of block b4, block b5 is not placed on top of block b6, block b5 is on block b2, block b5 is on block b8, block b5 is placed on top of block b3, block b5 is placed on top of block b7, block b6 is being held by the hand, block b6 is not on top of block b1, block b6 is not on top of block b5, block b6 is not placed on top of block b8, block b6 is on block b2, block b6 is on block b3, block b6 is on top of block b4, block b6 is on top of block b7, block b7 is not on block b3, block b7 is not on top of block b8, block b7 is not placed on top of block b5, block b7 is on block b4, block b7 is on block b6, block b7 is on the table, block b7 is placed on top of block b1, block b8 is clear, block b8 is not being held by the hand, block b8 is not on block b3, block b8 is not on block b5, block b8 is not on top of block b6, block b8 is not on top of block b7, block b8 is on block b1, block b8 is on top of block b2, block b8 is placed on top of block b4, the hand is holding the block b1 and the hand is holding the block b7. Respond with True or False.", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial setup, the following actions are taken: block b4 is removed from the top of block b1 and placed on the table, block b1 is then removed from block b6 and placed on the table, block b2 is removed from the top of block b5 and stacked on top of block b4, block b6 is removed from the top of block b7, block b6 is placed on the table, and block b7 is picked up by the hand and stacked on top of block b2 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is not on block b8, block b1 is not on top of block b2, block b1 is not on top of block b3, block b1 is not on top of block b6, block b1 is not on top of block b4, block b1 is not on top of block b7, block b1 is on block b5, block b2 is not being held, block b2 is not clear, block b2 is not on the table, block b2 is not on top of block b1, block b2 is not on top of block b6, block b2 is not on top of block b8, block b2 is on block b7, block b2 is on top of block b3, block b2 is on top of block b5, block b3 is not being held by the hand, block b3 is not on the table, block b3 is not on top of block b5, block b3 is on block b1, block b3 is on block b2, block b3 is on top of block b6, block b3 is on top of block b7, block b3 is on top of block b4, block b4 is not being held by the hand, block b4 is not clear, block b4 is not on top of block b7, block b4 is not on top of block b3, block b4 is on block b1, block b4 is on block b8, block b4 is on top of block b5, block b4 is on top of block b2, block b4 is on top of block b6, block b5 is not being held, block b5 is not on top of block b1, block b5 is not on top of block b4, block b5 is not on top of block b6, block b5 is on block b2, block b5 is on block b8, block b5 is on top of block b3, block b5 is on top of block b7, block b6 is being held by the hand, block b6 is not on top of block b1, block b6 is not on top of block b5, block b6 is not on top of block b8, block b6 is on block b2, block b6 is on block b3, block b6 is on top of block b4, block b6 is on top of block b7, block b7 is not on block b3, block b7 is not on top of block b8, block b7 is not on top of block b5, block b7 is on block b4, block b7 is on block b6, block b7 is on the table, block b7 is on top of block b1, block b8 is clear, block b8 is not being held by the hand, block b8 is not on block b3, block b8 is not on block b5, block b8 is not on top of block b6, block b8 is not on top of block b7, block b8 is on block b1, block b8 is on top of block b2, block b8 is on top of block b4, the hand is holding block b1 and the hand is holding block b7. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is on the table, and the hand is empty."}
{"question_id": "720ea5a5-274a-48cc-8101-551fb8f7c9cb", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is not being held by the hand, block b1 is not clear, block b1 is not on block b3, block b1 is not on top of block b2, block b1 is not on top of block b4, block b1 is not on top of block b6, block b1 is not placed on top of block b5, block b1 is not placed on top of block b7, block b2 is not being held by the hand, block b2 is not on block b3, block b2 is not on block b5, block b2 is not on block b7, block b2 is not on top of block b1, block b2 is not placed on top of block b4, block b2 is not placed on top of block b6, block b3 is not clear, block b3 is not located on the table, block b3 is not on block b5, block b3 is not on top of block b1, block b3 is not on top of block b2, block b3 is not on top of block b4, block b3 is not on top of block b7, block b3 is not placed on top of block b6, block b4 is not clear, block b4 is not on block b2, block b4 is not on block b3, block b4 is not on block b5, block b4 is not on the table, block b4 is not on top of block b6, block b4 is not on top of block b7, block b5 is not being held, block b5 is not on block b3, block b5 is not on block b6, block b5 is not on the table, block b5 is not on top of block b1, block b5 is not on top of block b7, block b5 is not placed on top of block b2, block b6 is not being held by the hand, block b6 is not clear, block b6 is not on block b4, block b6 is not on top of block b7, block b6 is not placed on top of block b1, block b6 is not placed on top of block b2, block b6 is not placed on top of block b3, block b6 is not placed on top of block b5, block b7 is not being held by the hand, block b7 is not on block b5, block b7 is not on the table, block b7 is not on top of block b1, block b7 is not on top of block b2, block b7 is not on top of block b3, block b7 is not on top of block b4, hand is holding some block and the hand is not holding the block b4. Respond with True or False.", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on block b4, block b6 is on the table, block b7 is on block b6 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b7 to achieve the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is not grasped by the hand, block b1 is not clear, block b1 is not positioned on block b3, block b1 is not positioned on top of block b2, block b1 is not positioned on top of block b4, block b1 is not positioned on top of block b6, block b1 is not placed on top of block b5, block b1 is not placed on top of block b7, block b2 is not grasped by the hand, block b2 is not positioned on block b3, block b2 is not positioned on block b5, block b2 is not positioned on block b7, block b2 is not positioned on top of block b1, block b2 is not placed on top of block b4, block b2 is not placed on top of block b6, block b3 is not clear, block b3 is not situated on the table, block b3 is not positioned on block b5, block b3 is not positioned on top of block b1, block b3 is not positioned on top of block b2, block b3 is not positioned on top of block b4, block b3 is not positioned on top of block b7, block b3 is not placed on top of block b6, block b4 is not clear, block b4 is not positioned on block b2, block b4 is not positioned on block b3, block b4 is not positioned on block b5, block b4 is not situated on the table, block b4 is not positioned on top of block b6, block b4 is not positioned on top of block b7, block b5 is not being held, block b5 is not positioned on block b3, block b5 is not positioned on block b6, block b5 is not situated on the table, block b5 is not positioned on top of block b1, block b5 is not positioned on top of block b7, block b5 is not placed on top of block b2, block b6 is not grasped by the hand, block b6 is not clear, block b6 is not positioned on block b4, block b6 is not positioned on top of block b7, block b6 is not placed on top of block b1, block b6 is not placed on top of block b2, block b6 is not placed on top of block b3, block b6 is not placed on top of block b5, block b7 is not grasped by the hand, block b7 is not positioned on block b5, block b7 is not situated on the table, block b7 is not positioned on top of block b1, block b7 is not positioned on top of block b2, block b7 is not positioned on top of block b3, block b7 is not positioned on top of block b4, the hand is holding some block and the hand is not holding block b4. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is positioned on the table, block b2 is not stacked, block b2 is also on the table, block b3 is not stacked, block b3 is placed on block b7, block b4 is positioned on top of block b1, block b5 is not stacked, block b5 is placed on block b4, block b6 is on the table, block b7 is positioned on block b6, and the hand is empty."}
{"question_id": "84fe82a0-0f8d-431d-a598-b6d6284ded7f", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is not being held by the hand, block b1 is not on block b3, block b1 is not on block b5, block b1 is not on block b7, block b1 is not on block b8, block b1 is not on the table, block b1 is not placed on top of block b2, block b1 is not placed on top of block b4, block b2 is not being held, block b2 is not on the table, block b2 is not on top of block b1, block b2 is not on top of block b3, block b2 is not on top of block b4, block b2 is not on top of block b6, block b2 is not on top of block b8, block b2 is not placed on top of block b7, block b3 is not being held by the hand, block b3 is not on block b2, block b3 is not on the table, block b3 is not on top of block b1, block b3 is not on top of block b5, block b3 is not placed on top of block b4, block b3 is not placed on top of block b6, block b3 is not placed on top of block b7, block b4 is not clear, block b4 is not located on the table, block b4 is not on block b5, block b4 is not on top of block b1, block b4 is not on top of block b2, block b4 is not on top of block b6, block b4 is not on top of block b7, block b4 is not placed on top of block b3, block b4 is not placed on top of block b8, block b5 is not being held, block b5 is not clear, block b5 is not on block b2, block b5 is not on block b3, block b5 is not on top of block b1, block b5 is not on top of block b4, block b5 is not placed on top of block b6, block b5 is not placed on top of block b7, block b5 is not placed on top of block b8, block b6 is not being held, block b6 is not clear, block b6 is not located on the table, block b6 is not on block b1, block b6 is not on block b2, block b6 is not on block b4, block b6 is not on top of block b5, block b6 is not placed on top of block b3, block b6 is not placed on top of block b8, block b7 is not being held by the hand, block b7 is not clear, block b7 is not on block b2, block b7 is not on block b4, block b7 is not on block b6, block b7 is not on top of block b5, block b7 is not on top of block b8, block b7 is not placed on top of block b1, block b7 is not placed on top of block b3, block b8 is not being held, block b8 is not clear, block b8 is not on block b1, block b8 is not on block b5, block b8 is not on top of block b3, block b8 is not on top of block b7, block b8 is not placed on top of block b2, block b8 is not placed on top of block b4, block b8 is not placed on top of block b6 and hand is holding some block. Respond with True or False.", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from the top of block b1, block b4 is unstacked to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is not held by the hand, block b1 is not on top of block b3, block b1 is not on top of block b5, block b1 is not on top of block b7, block b1 is not on top of block b8, block b1 is not on the table, block b1 is not above block b2, block b1 is not above block b4, block b2 is not being held, block b2 is not on the table, block b2 is not above block b1, block b2 is not above block b3, block b2 is not above block b4, block b2 is not above block b6, block b2 is not above block b8, block b2 is not above block b7, block b3 is not held by the hand, block b3 is not on top of block b2, block b3 is not on the table, block b3 is not above block b1, block b3 is not above block b5, block b3 is not above block b4, block b3 is not above block b6, block b3 is not above block b7, block b4 is not clear, block b4 is not on the table, block b4 is not on top of block b5, block b4 is not above block b1, block b4 is not above block b2, block b4 is not above block b6, block b4 is not above block b7, block b4 is not above block b3, block b4 is not above block b8, block b5 is not being held, block b5 is not clear, block b5 is not on top of block b2, block b5 is not on top of block b3, block b5 is not above block b1, block b5 is not above block b4, block b5 is not above block b6, block b5 is not above block b7, block b5 is not above block b8, block b6 is not being held, block b6 is not clear, block b6 is not on the table, block b6 is not on top of block b1, block b6 is not on top of block b2, block b6 is not on top of block b4, block b6 is not above block b5, block b6 is not above block b3, block b6 is not above block b8, block b7 is not being held by the hand, block b7 is not clear, block b7 is not on top of block b2, block b7 is not on top of block b4, block b7 is not on top of block b6, block b7 is not above block b5, block b7 is not above block b8, block b7 is not above block b1, block b7 is not above block b3, block b8 is not being held, block b8 is not clear, block b8 is not on top of block b1, block b8 is not on top of block b5, block b8 is not above block b3, block b8 is not above block b7, block b8 is not above block b2, block b8 is not above block b4, block b8 is not above block b6 and the hand is holding a block. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is on the table, and the hand is empty."}
{"question_id": "799c3474-577a-493d-8c5a-28c1fa1344f9", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "block b1 is not being held by the hand, block b1 is not clear, block b1 is not on block b3, block b1 is not on the table, block b1 is not on top of block b4, block b1 is not on top of block b5, block b1 is not on top of block b6, block b1 is not on top of block b8, block b1 is not placed on top of block b2, block b1 is not placed on top of block b9, block b2 is not located on the table, block b2 is not on block b1, block b2 is not on block b9, block b2 is not on top of block b3, block b2 is not on top of block b5, block b2 is not on top of block b7, block b2 is not on top of block b8, block b2 is not placed on top of block b4, block b3 is not being held by the hand, block b3 is not clear, block b3 is not located on the table, block b3 is not on block b2, block b3 is not on block b7, block b3 is not on top of block b5, block b3 is not on top of block b6, block b3 is not on top of block b9, block b3 is not placed on top of block b1, block b3 is not placed on top of block b8, block b4 is not being held by the hand, block b4 is not clear, block b4 is not on block b3, block b4 is not on block b7, block b4 is not on block b8, block b4 is not on block b9, block b4 is not on top of block b1, block b4 is not on top of block b2, block b4 is not on top of block b5, block b4 is not on top of block b6, block b5 is not being held, block b5 is not on block b2, block b5 is not on block b6, block b5 is not on top of block b1, block b5 is not on top of block b3, block b5 is not on top of block b7, block b5 is not on top of block b8, block b5 is not on top of block b9, block b5 is not placed on top of block b4, block b6 is not clear, block b6 is not on block b1, block b6 is not on block b8, block b6 is not on the table, block b6 is not on top of block b2, block b6 is not on top of block b4, block b6 is not on top of block b5, block b6 is not on top of block b7, block b6 is not on top of block b9, block b7 is not being held, block b7 is not clear, block b7 is not on block b6, block b7 is not on top of block b1, block b7 is not on top of block b2, block b7 is not on top of block b4, block b7 is not on top of block b8, block b7 is not on top of block b9, block b7 is not placed on top of block b3, block b7 is not placed on top of block b5, block b8 is not being held by the hand, block b8 is not located on the table, block b8 is not on block b2, block b8 is not on block b4, block b8 is not on block b6, block b8 is not on top of block b5, block b8 is not on top of block b7, block b8 is not placed on top of block b3, block b8 is not placed on top of block b9, block b9 is not clear, block b9 is not on block b2, block b9 is not on block b5, block b9 is not on the table, block b9 is not on top of block b3, block b9 is not on top of block b6, block b9 is not on top of block b7, block b9 is not placed on top of block b1, block b9 is not placed on top of block b4, block b9 is not placed on top of block b8, hand is not empty, the hand is not holding the block b2 and the hand is not holding the block b6", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is placed on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: block b9 is removed from block b8 to achieve the current state. In this state, identify all valid properties that include negations and list them; if there are no such properties, indicate None.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "751fa7b6-6eb5-4492-948b-7eb7d4d7b337", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked, the hand puts down the block b3, block b2 is unstacked from block b6, block b2 is put down on the table, block b6 is picked up from the table, on top of block b3, block b6 is stacked, from top of block b7, block b5 is unstacked, block b5 is stacked on top of block b6, from top of block b1, block b7 is unstacked, the hand puts down the block b7, from top of block b4, block b1 is unstacked, block b1 is stacked on top of block b2, block b4 is picked up, on top of block b5, block b4 is stacked, from top of block b2, block b1 is unstacked, block b1 is stacked on top of block b4, block b2 is picked up, block b2 is stacked on top of block b1 and block b7 is picked up from the table to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is not clear, block b1 is not located on the table, block b1 is not on block b2, block b1 is not on top of block b6, block b1 is not on top of block b7, block b1 is not placed on top of block b3, block b1 is not placed on top of block b5, block b2 is not being held, block b2 is not located on the table, block b2 is not on block b5, block b2 is not on top of block b3, block b2 is not on top of block b4, block b2 is not on top of block b6, block b2 is not on top of block b7, block b3 is not clear, block b3 is not on block b4, block b3 is not on block b6, block b3 is not on block b7, block b3 is not on top of block b2, block b3 is not on top of block b5, block b3 is not placed on top of block b1, block b4 is not being held, block b4 is not clear, block b4 is not on block b6, block b4 is not on block b7, block b4 is not on the table, block b4 is not on top of block b1, block b4 is not on top of block b3, block b4 is not placed on top of block b2, block b5 is not clear, block b5 is not located on the table, block b5 is not on top of block b1, block b5 is not on top of block b4, block b5 is not on top of block b7, block b5 is not placed on top of block b2, block b5 is not placed on top of block b3, block b6 is not being held, block b6 is not clear, block b6 is not located on the table, block b6 is not on block b1, block b6 is not on block b2, block b6 is not on top of block b4, block b6 is not on top of block b5, block b6 is not on top of block b7, block b7 is not clear, block b7 is not on block b1, block b7 is not on block b2, block b7 is not on block b4, block b7 is not on block b5, block b7 is not on the table, block b7 is not on top of block b3, block b7 is not on top of block b6, hand is holding some block, the hand is not holding the block b1, the hand is not holding the block b3 and the hand is not holding the block b5. Respond with True or False.", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b5, then placed on the table, block b2 is removed from block b6 and placed on the table, block b6 is picked up and stacked on top of block b3, block b5 is removed from the top of block b7 and stacked on top of block b6, block b7 is removed from the top of block b1 and placed on the table, block b1 is removed from the top of block b4 and stacked on top of block b2, block b4 is picked up and stacked on top of block b5, block b1 is removed from the top of block b2 and stacked on top of block b4, block b2 is picked up and stacked on top of block b1, and block b7 is picked up from the table to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is not clear, block b1 is not on the table, block b1 is not on block b2, block b1 is not on top of block b6, block b1 is not on top of block b7, block b1 is not on top of block b3, block b1 is not on top of block b5, block b2 is not being held, block b2 is not on the table, block b2 is not on block b5, block b2 is not on top of block b3, block b2 is not on top of block b4, block b2 is not on top of block b6, block b2 is not on top of block b7, block b3 is not clear, block b3 is not on block b4, block b3 is not on block b6, block b3 is not on block b7, block b3 is not on top of block b2, block b3 is not on top of block b5, block b3 is not on top of block b1, block b4 is not being held, block b4 is not clear, block b4 is not on block b6, block b4 is not on block b7, block b4 is not on the table, block b4 is not on top of block b1, block b4 is not on top of block b3, block b4 is not on top of block b2, block b5 is not clear, block b5 is not on the table, block b5 is not on top of block b1, block b5 is not on top of block b4, block b5 is not on top of block b7, block b5 is not on top of block b2, block b5 is not on top of block b3, block b6 is not being held, block b6 is not clear, block b6 is not on the table, block b6 is not on block b1, block b6 is not on block b2, block b6 is not on top of block b4, block b6 is not on top of block b5, block b6 is not on top of block b7, block b7 is not clear, block b7 is not on block b1, block b7 is not on block b2, block b7 is not on block b4, block b7 is not on block b5, block b7 is not on the table, block b7 is not on top of block b3, block b7 is not on top of block b6, the hand is holding a block, the hand is not holding block b1, the hand is not holding block b3, and the hand is not holding block b5. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has nothing on it, block b2 is positioned above block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is situated on the table, block b5 is resting on block b7, block b6 is also on the table, block b7 is supporting block b1, and the hand is empty."}
{"question_id": "50515ef8-a055-47d9-a96e-484b59af2512", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked, block b3 is put down, block b2 is unstacked from block b6, block b2 is put down on the table, block b6 is picked up, block b6 is stacked on top of block b3, block b5 is unstacked from top of block b7, on top of block b6, block b5 is stacked, block b7 is unstacked from block b1, the hand puts down the block b7, block b1 is unstacked from top of block b4, on top of block b2, block b1 is stacked, block b4 is picked up from the table, on top of block b5, block b4 is stacked, block b1 is unstacked from top of block b2, block b1 is stacked on top of block b4, block b2 is picked up, on top of block b1, block b2 is stacked and block b7 is picked up to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is not clear, block b1 is not located on the table, block b1 is not placed on top of block b2, block b1 is on block b3, block b1 is on block b5, block b1 is on top of block b6, block b1 is on top of block b7, block b2 is not on block b3, block b2 is not on the table, block b2 is not placed on top of block b7, block b2 is on top of block b4, block b2 is on top of block b6, block b2 is placed on top of block b5, block b3 is clear, block b3 is not being held by the hand, block b3 is not on block b4, block b3 is not on top of block b5, block b3 is not placed on top of block b1, block b3 is not placed on top of block b6, block b3 is not placed on top of block b7, block b3 is placed on top of block b2, block b4 is clear, block b4 is located on the table, block b4 is not being held, block b4 is not on block b1, block b4 is not on top of block b6, block b4 is on block b2, block b4 is on block b3, block b4 is on block b7, block b5 is clear, block b5 is not on block b4, block b5 is not on the table, block b5 is not on top of block b3, block b5 is on block b1, block b5 is on block b7, block b5 is on top of block b2, block b6 is clear, block b6 is not being held, block b6 is not on block b2, block b6 is not on block b5, block b6 is not on block b7, block b6 is on the table, block b6 is on top of block b1, block b6 is placed on top of block b4, block b7 is clear, block b7 is not on the table, block b7 is not on top of block b2, block b7 is not on top of block b3, block b7 is on block b4, block b7 is on block b5, block b7 is on top of block b1, block b7 is on top of block b6, hand is holding some block, the hand is holding the block b1, the hand is not holding the block b2 and the hand is not holding the block b5. Respond with True or False.", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b5, then placed on the table, block b2 is removed from block b6 and placed on the table, block b6 is picked up and stacked on top of block b3, block b5 is removed from the top of block b7 and stacked on top of block b6, block b7 is removed from block b1 and placed on the table, block b1 is removed from the top of block b4 and stacked on top of block b2, block b4 is picked up from the table and stacked on top of block b5, block b1 is removed from the top of block b2 and stacked on top of block b4, block b2 is picked up and stacked on top of block b1, and block b7 is picked up to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is not clear, block b1 is not on the table, block b1 is not on top of block b2, block b1 is on block b3, block b1 is on block b5, block b1 is on top of block b6, block b1 is on top of block b7, block b2 is not on block b3, block b2 is not on the table, block b2 is not on top of block b7, block b2 is on top of block b4, block b2 is on top of block b6, block b2 is on top of block b5, block b3 is clear, block b3 is not being held by the hand, block b3 is not on block b4, block b3 is not on top of block b5, block b3 is not on top of block b1, block b3 is not on top of block b6, block b3 is not on top of block b7, block b3 is on top of block b2, block b4 is clear, block b4 is not on the table, block b4 is not being held, block b4 is not on block b1, block b4 is not on top of block b6, block b4 is on block b2, block b4 is on block b3, block b4 is on block b7, block b5 is clear, block b5 is not on block b4, block b5 is not on the table, block b5 is not on top of block b3, block b5 is on block b1, block b5 is on block b7, block b5 is on top of block b2, block b6 is clear, block b6 is not being held, block b6 is not on block b2, block b6 is not on block b5, block b6 is not on block b7, block b6 is on the table, block b6 is on top of block b1, block b6 is on top of block b4, block b7 is clear, block b7 is not on the table, block b7 is not on top of block b2, block b7 is not on top of block b3, block b7 is on block b4, block b7 is on block b5, block b7 is on top of block b1, block b7 is on top of block b6, the hand is holding a block, the hand is holding block b1, the hand is not holding block b2, and the hand is not holding block b5. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has nothing on it, block b2 is positioned above block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is situated on the table, block b5 is resting on block b7, block b6 is also on the table, block b7 is supporting block b1, and the hand is empty."}
{"question_id": "3f82c257-134d-4bd0-9f24-f025ce270ebb", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "block b1 is located on the table, block b1 is not clear, block b1 is not on block b2, block b1 is not on block b3, block b1 is not on block b4, block b1 is not on top of block b6, block b1 is not on top of block b7, block b1 is not placed on top of block b5, block b2 is clear, block b2 is located on the table, block b2 is not being held, block b2 is not on block b7, block b2 is not on top of block b1, block b2 is not placed on top of block b3, block b2 is not placed on top of block b4, block b2 is not placed on top of block b5, block b2 is not placed on top of block b6, block b3 is being held by the hand, block b3 is not clear, block b3 is not located on the table, block b3 is not on block b1, block b3 is not on block b5, block b3 is not on block b6, block b3 is not on top of block b4, block b3 is not placed on top of block b2, block b3 is not placed on top of block b7, block b4 is not clear, block b4 is not located on the table, block b4 is not on block b3, block b4 is not on block b6, block b4 is not on block b7, block b4 is not on top of block b2, block b4 is not on top of block b5, block b4 is placed on top of block b1, block b5 is clear, block b5 is not located on the table, block b5 is not on top of block b6, block b5 is not on top of block b7, block b5 is not placed on top of block b1, block b5 is not placed on top of block b2, block b5 is not placed on top of block b3, block b5 is on block b4, block b6 is not being held by the hand, block b6 is not clear, block b6 is not on block b1, block b6 is not on block b2, block b6 is not on block b4, block b6 is not on block b7, block b6 is not on top of block b3, block b6 is not placed on top of block b5, block b6 is on the table, block b7 is clear, block b7 is not being held, block b7 is not on block b1, block b7 is not on the table, block b7 is not on top of block b2, block b7 is not on top of block b4, block b7 is not on top of block b5, block b7 is not placed on top of block b3, block b7 is on top of block b6, hand is holding some block, the hand is not holding the block b1, the hand is not holding the block b4 and the hand is not holding the block b5", "plan_length": 1, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on block b4, block b6 is on the table, block b7 is on block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: block b3 is removed from the top of block b7 to achieve the current state. In this state, list all the valid properties (including both affirmative and negated properties) of the state. If there are no properties, indicate None.", "initial_state_nl_paraphrased": "Block b1 is positioned on the table, block b2 is not stacked with any other block, block b2 is also on the table, block b3 is not supporting any block, block b3 is placed on top of block b7, block b4 is stacked on block b1, block b5 is clear of any other block, block b5 is positioned on block b4, block b6 is resting on the table, block b7 is placed on block b6, and the hand is empty."}
{"question_id": "a1cf0e43-5548-41b8-b197-f5d77a9bdec7", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1, the hand puts down the block b4, from top of block b6, block b1 is unstacked, the hand puts down the block b1, block b2 is unstacked from top of block b5, on top of block b4, block b2 is stacked, block b6 is unstacked from top of block b7, block b6 is put down on the table, block b7 is picked up by the hand, block b7 is stacked on top of block b2, block b3 is unstacked from block b8, block b3 is stacked on top of block b7, block b1 is picked up by the hand, on top of block b3, block b1 is stacked, block b5 is picked up, on top of block b1, block b5 is stacked, block b8 is picked up from the table, on top of block b5, block b8 is stacked and block b6 is picked up from the table to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "block b1 is not being held by the hand, block b1 is not clear, block b1 is not located on the table, block b1 is not on block b5, block b1 is not on top of block b4, block b1 is not on top of block b6, block b1 is not on top of block b7, block b1 is not on top of block b8, block b1 is not placed on top of block b2, block b2 is not clear, block b2 is not located on the table, block b2 is not on block b6, block b2 is not on top of block b3, block b2 is not on top of block b5, block b2 is not on top of block b7, block b2 is not placed on top of block b1, block b2 is not placed on top of block b8, block b3 is not clear, block b3 is not located on the table, block b3 is not on block b2, block b3 is not on block b4, block b3 is not on block b8, block b3 is not placed on top of block b1, block b3 is not placed on top of block b5, block b3 is not placed on top of block b6, block b4 is not being held by the hand, block b4 is not clear, block b4 is not on block b5, block b4 is not on top of block b2, block b4 is not on top of block b3, block b4 is not on top of block b7, block b4 is not placed on top of block b1, block b4 is not placed on top of block b6, block b4 is not placed on top of block b8, block b5 is not being held by the hand, block b5 is not clear, block b5 is not located on the table, block b5 is not on block b2, block b5 is not on block b3, block b5 is not on block b6, block b5 is not on block b7, block b5 is not on top of block b4, block b5 is not placed on top of block b8, block b6 is not clear, block b6 is not on block b7, block b6 is not on the table, block b6 is not on top of block b1, block b6 is not on top of block b3, block b6 is not placed on top of block b2, block b6 is not placed on top of block b4, block b6 is not placed on top of block b5, block b6 is not placed on top of block b8, block b7 is not being held, block b7 is not clear, block b7 is not on block b1, block b7 is not on block b3, block b7 is not on the table, block b7 is not on top of block b8, block b7 is not placed on top of block b4, block b7 is not placed on top of block b5, block b7 is not placed on top of block b6, block b8 is not being held, block b8 is not located on the table, block b8 is not on block b1, block b8 is not on block b4, block b8 is not on block b6, block b8 is not on top of block b7, block b8 is not placed on top of block b2, block b8 is not placed on top of block b3, hand is not empty, the hand is not holding the block b2 and the hand is not holding the block b3", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b4 is removed from block b1, the hand releases block b4, block b1 is then removed from the top of block b6, the hand releases block b1, block b2 is removed from the top of block b5, block b2 is then placed on top of block b4, block b6 is removed from the top of block b7, block b6 is placed on the table, the hand picks up block b7, block b7 is then stacked on top of block b2, block b3 is removed from block b8, block b3 is then stacked on top of block b7, the hand picks up block b1, block b1 is then stacked on top of block b3, block b5 is picked up, block b5 is then stacked on top of block b1, block b8 is picked up from the table, block b8 is then stacked on top of block b5, and block b6 is picked up from the table to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is on the table, and the hand is empty."}
{"question_id": "6c0f4e3f-6f74-4063-adea-b84d2ac0903d", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, block b3 is put down, block b2 is unstacked from top of block b6, the hand puts down the block b2, block b6 is picked up from the table, block b6 is stacked on top of block b3, block b5 is unstacked from block b7, block b5 is stacked on top of block b6, block b7 is unstacked from block b1 and block b7 is put down to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is clear, block b1 is not on block b6, block b1 is not on the table, block b1 is not placed on top of block b2, block b1 is on block b4, block b1 is on block b7, block b1 is on top of block b3, block b1 is on top of block b5, block b2 is clear, block b2 is not being held, block b2 is not on block b5, block b2 is not on block b7, block b2 is not on the table, block b2 is not placed on top of block b4, block b2 is not placed on top of block b6, block b2 is placed on top of block b1, block b2 is placed on top of block b3, block b3 is being held, block b3 is clear, block b3 is not on block b1, block b3 is not on block b6, block b3 is not on top of block b4, block b3 is not placed on top of block b2, block b3 is not placed on top of block b7, block b3 is on the table, block b3 is placed on top of block b5, block b4 is being held, block b4 is clear, block b4 is not located on the table, block b4 is not on block b6, block b4 is not placed on top of block b3, block b4 is on block b5, block b4 is on block b7, block b4 is placed on top of block b1, block b4 is placed on top of block b2, block b5 is being held, block b5 is clear, block b5 is not on block b1, block b5 is not on top of block b2, block b5 is not on top of block b3, block b5 is on block b7, block b5 is on the table, block b5 is placed on top of block b4, block b5 is placed on top of block b6, block b6 is being held, block b6 is located on the table, block b6 is not clear, block b6 is not on block b2, block b6 is not on block b3, block b6 is not on block b5, block b6 is on block b7, block b6 is on top of block b4, block b6 is placed on top of block b1, block b7 is being held, block b7 is not clear, block b7 is not located on the table, block b7 is not on block b5, block b7 is not on top of block b2, block b7 is not placed on top of block b1, block b7 is on block b6, block b7 is placed on top of block b3, block b7 is placed on top of block b4, hand is empty and the hand is not holding the block b1. Respond with True or False.", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from block b5, block b3 is placed down, block b2 is removed from the top of block b6, the hand releases block b2, block b6 is picked up from the table, block b6 is stacked on block b3, block b5 is removed from block b7, block b5 is stacked on block b6, block b7 is removed from block b1 and block b7 is placed down to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is clear of other blocks, block b1 is not stacked on block b6, block b1 is not on the table, block b1 is not on top of block b2, block b1 is stacked on block b4, block b1 is stacked on block b7, block b1 is on top of block b3, block b1 is on top of block b5, block b2 is clear of other blocks, block b2 is not being held by the hand, block b2 is not stacked on block b5, block b2 is not stacked on block b7, block b2 is not on the table, block b2 is not on top of block b4, block b2 is not on top of block b6, block b2 is on top of block b1, block b2 is on top of block b3, block b3 is being held by the hand, block b3 is clear of other blocks, block b3 is not stacked on block b1, block b3 is not stacked on block b6, block b3 is not on top of block b4, block b3 is not on top of block b2, block b3 is not on top of block b7, block b3 is on the table, block b3 is on top of block b5, block b4 is being held by the hand, block b4 is clear of other blocks, block b4 is not on the table, block b4 is not stacked on block b6, block b4 is not on top of block b3, block b4 is stacked on block b5, block b4 is stacked on block b7, block b4 is on top of block b1, block b4 is on top of block b2, block b5 is being held by the hand, block b5 is clear of other blocks, block b5 is not stacked on block b1, block b5 is not on top of block b2, block b5 is not on top of block b3, block b5 is stacked on block b7, block b5 is on the table, block b5 is on top of block b4, block b5 is on top of block b6, block b6 is being held by the hand, block b6 is on the table, block b6 is not clear of other blocks, block b6 is not stacked on block b2, block b6 is not stacked on block b3, block b6 is not stacked on block b5, block b6 is stacked on block b7, block b6 is on top of block b4, block b6 is on top of block b1, block b7 is being held by the hand, block b7 is not clear of other blocks, block b7 is not on the table, block b7 is not stacked on block b5, block b7 is not on top of block b2, block b7 is not on top of block b1, block b7 is stacked on block b6, block b7 is on top of block b3, block b7 is on top of block b4, the hand is empty and the hand is not holding block b1. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has nothing on it, block b2 is positioned above block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is situated on the table, block b5 is resting on block b7, block b6 is also on the table, block b7 is supporting block b1, and the hand is empty."}
{"question_id": "0f9a654f-07a5-4699-ba1a-1cf10b49628d", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1, the hand puts down the block b4, block b1 is unstacked from block b6, block b1 is put down on the table, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b4, block b6 is unstacked from top of block b7, the hand puts down the block b6, block b7 is picked up from the table and block b7 is stacked on top of block b2 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is clear, block b1 is located on the table, block b1 is not being held by the hand, block b1 is not on top of block b2, block b1 is not on top of block b5, block b1 is not on top of block b8, block b1 is not placed on top of block b3, block b1 is not placed on top of block b4, block b1 is not placed on top of block b6, block b1 is not placed on top of block b7, block b2 is not being held by the hand, block b2 is not clear, block b2 is not located on the table, block b2 is not on top of block b1, block b2 is not on top of block b3, block b2 is not on top of block b5, block b2 is not placed on top of block b6, block b2 is not placed on top of block b7, block b2 is not placed on top of block b8, block b2 is on top of block b4, block b3 is clear, block b3 is not being held, block b3 is not on block b1, block b3 is not on block b4, block b3 is not on the table, block b3 is not on top of block b7, block b3 is not placed on top of block b2, block b3 is not placed on top of block b5, block b3 is not placed on top of block b6, block b3 is placed on top of block b8, block b4 is not clear, block b4 is not on block b2, block b4 is not on block b5, block b4 is not on block b8, block b4 is not placed on top of block b1, block b4 is not placed on top of block b3, block b4 is not placed on top of block b6, block b4 is not placed on top of block b7, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b5 is not on block b1, block b5 is not on block b2, block b5 is not on top of block b3, block b5 is not on top of block b4, block b5 is not on top of block b7, block b5 is not on top of block b8, block b5 is not placed on top of block b6, block b6 is clear, block b6 is located on the table, block b6 is not being held, block b6 is not on block b1, block b6 is not on block b3, block b6 is not on block b7, block b6 is not on top of block b5, block b6 is not placed on top of block b2, block b6 is not placed on top of block b4, block b6 is not placed on top of block b8, block b7 is clear, block b7 is not being held by the hand, block b7 is not on block b1, block b7 is not on block b5, block b7 is not on the table, block b7 is not on top of block b3, block b7 is not on top of block b6, block b7 is not placed on top of block b4, block b7 is not placed on top of block b8, block b7 is on block b2, block b8 is not being held, block b8 is not clear, block b8 is not on block b1, block b8 is not on top of block b3, block b8 is not on top of block b6, block b8 is not placed on top of block b2, block b8 is not placed on top of block b4, block b8 is not placed on top of block b5, block b8 is not placed on top of block b7, block b8 is on the table, hand is not holding anything, the hand is not holding the block b4 and the hand is not holding the block b5. Respond with True or False.", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial setup, the following steps are taken: block b4 is removed from block b1, the hand releases block b4, block b1 is removed from block b6, block b1 is placed on the table, block b2 is removed from the top of block b5, block b2 is stacked on top of block b4, block b6 is removed from the top of block b7, the hand releases block b6, block b7 is picked up from the table and block b7 is stacked on top of block b2 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is clear of other blocks, block b1 is situated on the table, block b1 is not being grasped by the hand, block b1 is not positioned above block b2, block b1 is not positioned above block b5, block b1 is not positioned above block b8, block b1 is not placed above block b3, block b1 is not placed above block b4, block b1 is not placed above block b6, block b1 is not placed above block b7, block b2 is not being grasped by the hand, block b2 is not clear of other blocks, block b2 is not situated on the table, block b2 is not positioned above block b1, block b2 is not positioned above block b3, block b2 is not positioned above block b5, block b2 is not placed above block b6, block b2 is not placed above block b7, block b2 is not placed above block b8, block b2 is positioned above block b4, block b3 is clear of other blocks, block b3 is not being grasped, block b3 is not positioned on block b1, block b3 is not positioned on block b4, block b3 is not situated on the table, block b3 is not positioned above block b7, block b3 is not placed above block b2, block b3 is not placed above block b5, block b3 is not placed above block b6, block b3 is placed above block b8, block b4 is not clear of other blocks, block b4 is not positioned on block b2, block b4 is not positioned on block b5, block b4 is not positioned on block b8, block b4 is not placed above block b1, block b4 is not placed above block b3, block b4 is not placed above block b6, block b4 is not placed above block b7, block b4 is situated on the table, block b5 is clear of other blocks, block b5 is situated on the table, block b5 is not positioned on block b1, block b5 is not positioned on block b2, block b5 is not positioned above block b3, block b5 is not positioned above block b4, block b5 is not positioned above block b7, block b5 is not positioned above block b8, block b5 is not placed above block b6, block b6 is clear of other blocks, block b6 is situated on the table, block b6 is not being grasped, block b6 is not positioned on block b1, block b6 is not positioned on block b3, block b6 is not positioned on block b7, block b6 is not positioned above block b5, block b6 is not placed above block b2, block b6 is not placed above block b4, block b6 is not placed above block b8, block b7 is clear of other blocks, block b7 is not being grasped by the hand, block b7 is not positioned on block b1, block b7 is not positioned on block b5, block b7 is not situated on the table, block b7 is not positioned above block b3, block b7 is not positioned above block b6, block b7 is not placed above block b4, block b7 is not placed above block b8, block b7 is positioned on block b2, block b8 is not being grasped, block b8 is not clear of other blocks, block b8 is not positioned on block b1, block b8 is not positioned above block b3, block b8 is not positioned above block b6, block b8 is not placed above block b2, block b8 is not placed above block b4, block b8 is not placed above block b5, block b8 is not placed above block b7, block b8 is situated on the table, the hand is not holding anything, the hand is not holding block b4 and the hand is not holding block b5. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned on top of block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is sitting on the table, block b8 is sitting on the table, and the hand is empty."}
{"question_id": "63c64dd8-44fc-473b-b9e1-bd99b3870abf", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b7 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is located on the table, block b1 is not being held by the hand, block b1 is not clear, block b1 is not on block b2, block b1 is not on top of block b3, block b1 is not placed on top of block b4, block b1 is not placed on top of block b5, block b1 is not placed on top of block b6, block b1 is not placed on top of block b7, block b2 is clear, block b2 is not being held, block b2 is not on block b3, block b2 is not on top of block b4, block b2 is not on top of block b6, block b2 is not on top of block b7, block b2 is not placed on top of block b1, block b2 is not placed on top of block b5, block b2 is on the table, block b3 is being held, block b3 is not clear, block b3 is not on block b2, block b3 is not on the table, block b3 is not on top of block b5, block b3 is not placed on top of block b1, block b3 is not placed on top of block b4, block b3 is not placed on top of block b6, block b3 is not placed on top of block b7, block b4 is not being held by the hand, block b4 is not clear, block b4 is not located on the table, block b4 is not on block b3, block b4 is not on block b6, block b4 is not on block b7, block b4 is not placed on top of block b2, block b4 is not placed on top of block b5, block b4 is on block b1, block b5 is clear, block b5 is not being held by the hand, block b5 is not on block b7, block b5 is not on the table, block b5 is not on top of block b2, block b5 is not placed on top of block b1, block b5 is not placed on top of block b3, block b5 is not placed on top of block b6, block b5 is placed on top of block b4, block b6 is located on the table, block b6 is not clear, block b6 is not on block b2, block b6 is not on block b4, block b6 is not placed on top of block b1, block b6 is not placed on top of block b3, block b6 is not placed on top of block b5, block b6 is not placed on top of block b7, block b7 is clear, block b7 is not on block b4, block b7 is not on the table, block b7 is not on top of block b2, block b7 is not on top of block b3, block b7 is not placed on top of block b1, block b7 is not placed on top of block b5, block b7 is on block b6, hand is not empty, the hand is not holding the block b6 and the hand is not holding the block b7. Respond with True or False.", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on block b4, block b6 is on the table, block b7 is on block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b7 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is on the table, block b1 is not being grasped by the hand, block b1 is not clear, block b1 is not stacked on block b2, block b1 is not on top of block b3, block b1 is not positioned on top of block b4, block b1 is not positioned on top of block b5, block b1 is not positioned on top of block b6, block b1 is not positioned on top of block b7, block b2 is clear, block b2 is not being grasped, block b2 is not stacked on block b3, block b2 is not on top of block b4, block b2 is not on top of block b6, block b2 is not on top of block b7, block b2 is not positioned on top of block b1, block b2 is not positioned on top of block b5, block b2 is on the table, block b3 is being grasped, block b3 is not clear, block b3 is not stacked on block b2, block b3 is not on the table, block b3 is not on top of block b5, block b3 is not positioned on top of block b1, block b3 is not positioned on top of block b4, block b3 is not positioned on top of block b6, block b3 is not positioned on top of block b7, block b4 is not being grasped by the hand, block b4 is not clear, block b4 is not located on the table, block b4 is not stacked on block b3, block b4 is not stacked on block b6, block b4 is not stacked on block b7, block b4 is not positioned on top of block b2, block b4 is not positioned on top of block b5, block b4 is stacked on block b1, block b5 is clear, block b5 is not being grasped by the hand, block b5 is not stacked on block b7, block b5 is not on the table, block b5 is not on top of block b2, block b5 is not positioned on top of block b1, block b5 is not positioned on top of block b3, block b5 is not positioned on top of block b6, block b5 is positioned on top of block b4, block b6 is located on the table, block b6 is not clear, block b6 is not stacked on block b2, block b6 is not stacked on block b4, block b6 is not positioned on top of block b1, block b6 is not positioned on top of block b3, block b6 is not positioned on top of block b5, block b6 is not positioned on top of block b7, block b7 is clear, block b7 is not stacked on block b4, block b7 is not on the table, block b7 is not on top of block b2, block b7 is not on top of block b3, block b7 is not positioned on top of block b1, block b7 is not positioned on top of block b5, block b7 is stacked on block b6, the hand is not empty, the hand is not grasping block b6 and the hand is not grasping block b7. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is positioned on the table, block b2 is not stacked, block b2 is also on the table, block b3 is not stacked, block b3 is placed on block b7, block b4 is positioned on top of block b1, block b5 is not stacked, block b5 is placed on block b4, block b6 is on the table, block b7 is positioned on block b6, and the hand is empty."}
{"question_id": "757e6028-3ae3-49dd-b387-6a5d0849b678", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8, the hand puts down the block b9, block b2 is unstacked from top of block b6, on top of block b5, block b2 is stacked, block b6 is unstacked from block b3, block b6 is put down on the table, block b3 is unstacked from top of block b4, on top of block b9, block b3 is stacked, block b8 is unstacked from block b1, on top of block b4, block b8 is stacked, from top of block b5, block b2 is unstacked, block b2 is stacked on top of block b8, block b3 is unstacked from block b9, block b3 is stacked on top of block b2, from top of block b7, block b1 is unstacked, block b1 is stacked on top of block b3, block b7 is picked up, block b7 is stacked on top of block b9 and block b6 is picked up from the table to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is not being held, block b1 is not located on the table, block b1 is not on block b4, block b1 is not on block b7, block b1 is not on block b8, block b1 is not on block b9, block b1 is not on top of block b2, block b1 is not placed on top of block b5, block b1 is not placed on top of block b6, block b2 is not being held by the hand, block b2 is not clear, block b2 is not on block b1, block b2 is not on block b6, block b2 is not on the table, block b2 is not on top of block b3, block b2 is not on top of block b5, block b2 is not on top of block b7, block b2 is not placed on top of block b4, block b2 is not placed on top of block b9, block b3 is not being held, block b3 is not clear, block b3 is not located on the table, block b3 is not on block b1, block b3 is not on block b6, block b3 is not on block b8, block b3 is not on block b9, block b3 is not on top of block b4, block b3 is not placed on top of block b5, block b3 is not placed on top of block b7, block b4 is not clear, block b4 is not on block b2, block b4 is not on block b3, block b4 is not on top of block b8, block b4 is not placed on top of block b1, block b4 is not placed on top of block b5, block b4 is not placed on top of block b6, block b4 is not placed on top of block b7, block b4 is not placed on top of block b9, block b5 is not being held, block b5 is not on block b2, block b5 is not on block b4, block b5 is not on block b8, block b5 is not on top of block b1, block b5 is not placed on top of block b3, block b5 is not placed on top of block b6, block b5 is not placed on top of block b7, block b5 is not placed on top of block b9, block b6 is not clear, block b6 is not on block b7, block b6 is not on block b9, block b6 is not on the table, block b6 is not on top of block b2, block b6 is not on top of block b3, block b6 is not on top of block b8, block b6 is not placed on top of block b1, block b6 is not placed on top of block b4, block b6 is not placed on top of block b5, block b7 is not on block b1, block b7 is not on block b4, block b7 is not on block b5, block b7 is not on the table, block b7 is not on top of block b6, block b7 is not placed on top of block b2, block b7 is not placed on top of block b3, block b7 is not placed on top of block b8, block b8 is not clear, block b8 is not on block b1, block b8 is not on block b2, block b8 is not on block b5, block b8 is not on block b6, block b8 is not on block b7, block b8 is not on block b9, block b8 is not on the table, block b8 is not on top of block b3, block b9 is not clear, block b9 is not on block b2, block b9 is not on block b3, block b9 is not on block b6, block b9 is not on block b7, block b9 is not on block b8, block b9 is not on top of block b4, block b9 is not placed on top of block b1, block b9 is not placed on top of block b5, hand is not empty, the hand is not holding the block b4, the hand is not holding the block b7, the hand is not holding the block b8 and the hand is not holding the block b9. Respond with True or False.", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is placed on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: block b9 is removed from the top of block b8, the hand places block b9 down, block b2 is removed from the top of block b6, block b2 is placed on top of block b5, block b6 is removed from block b3, block b6 is put down on the table, block b3 is removed from the top of block b4, block b3 is placed on top of block b9, block b8 is removed from block b1, block b8 is placed on top of block b4, block b2 is removed from the top of block b5, block b2 is placed on top of block b8, block b3 is removed from block b9, block b3 is placed on top of block b2, block b1 is removed from the top of block b7, block b1 is placed on top of block b3, block b7 is picked up, block b7 is placed on top of block b9, and block b6 is picked up from the table to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is not being held, block b1 is not located on the table, block b1 is not on block b4, block b1 is not on block b7, block b1 is not on block b8, block b1 is not on block b9, block b1 is not on top of block b2, block b1 is not placed on top of block b5, block b1 is not placed on top of block b6, block b2 is not being held by the hand, block b2 is not clear, block b2 is not on block b1, block b2 is not on block b6, block b2 is not on the table, block b2 is not on top of block b3, block b2 is not on top of block b5, block b2 is not on top of block b7, block b2 is not placed on top of block b4, block b2 is not placed on top of block b9, block b3 is not being held, block b3 is not clear, block b3 is not located on the table, block b3 is not on block b1, block b3 is not on block b6, block b3 is not on block b8, block b3 is not on block b9, block b3 is not on top of block b4, block b3 is not placed on top of block b5, block b3 is not placed on top of block b7, block b4 is not clear, block b4 is not on block b2, block b4 is not on block b3, block b4 is not on top of block b8, block b4 is not placed on top of block b1, block b4 is not placed on top of block b5, block b4 is not placed on top of block b6, block b4 is not placed on top of block b7, block b4 is not placed on top of block b9, block b5 is not being held, block b5 is not on block b2, block b5 is not on block b4, block b5 is not on block b8, block b5 is not on top of block b1, block b5 is not placed on top of block b3, block b5 is not placed on top of block b6, block b5 is not placed on top of block b7, block b5 is not placed on top of block b9, block b6 is not clear, block b6 is not on block b7, block b6 is not on block b9, block b6 is not on the table, block b6 is not on top of block b2, block b6 is not on top of block b3, block b6 is not on top of block b8, block b6 is not placed on top of block b1, block b6 is not placed on top of block b4, block b6 is not placed on top of block b5, block b7 is not on block b1, block b7 is not on block b4, block b7 is not on block b5, block b7 is not on the table, block b7 is not on top of block b6, block b7 is not placed on top of block b2, block b7 is not placed on top of block b3, block b7 is not placed on top of block b8, block b8 is not clear, block b8 is not on block b1, block b8 is not on block b2, block b8 is not on block b5, block b8 is not on block b6, block b8 is not on block b7, block b8 is not on block b9, block b8 is not on the table, block b8 is not on top of block b3, block b9 is not clear, block b9 is not on block b2, block b9 is not on block b3, block b9 is not on block b6, block b9 is not on block b7, block b9 is not on block b8, block b9 is not on top of block b4, block b9 is not placed on top of block b1, block b9 is not placed on top of block b5, the hand is not empty, the hand is not holding the block b4, the hand is not holding the block b7, the hand is not holding the block b8, and the hand is not holding the block b9. Respond with True or False.\n\nAnswer: True", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on top of block b6, block b3 is stacked on top of block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is also situated on the table, block b6 is stacked on top of block b3, block b7 is resting on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is stacked on top of block b8, and the hand is empty."}
{"question_id": "e73f39e2-7b82-4895-8535-88c16927bf9d", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is not being held, block b1 is not clear, block b1 is not on block b2, block b1 is not on block b3, block b1 is not on the table, block b1 is not placed on top of block b7, block b1 is on block b4, block b1 is on block b8, block b1 is on top of block b5, block b1 is placed on top of block b6, block b1 is placed on top of block b9, block b2 is not being held, block b2 is not clear, block b2 is not on block b3, block b2 is not placed on top of block b8, block b2 is not placed on top of block b9, block b2 is on the table, block b2 is on top of block b1, block b2 is on top of block b4, block b2 is on top of block b6, block b2 is placed on top of block b5, block b2 is placed on top of block b7, block b3 is being held, block b3 is not clear, block b3 is not on block b1, block b3 is not on block b4, block b3 is not on the table, block b3 is not on top of block b8, block b3 is not placed on top of block b5, block b3 is not placed on top of block b9, block b3 is on top of block b6, block b3 is placed on top of block b2, block b3 is placed on top of block b7, block b4 is not being held, block b4 is not clear, block b4 is not on block b2, block b4 is not on top of block b5, block b4 is not on top of block b9, block b4 is not placed on top of block b8, block b4 is on the table, block b4 is on top of block b1, block b4 is on top of block b3, block b4 is on top of block b6, block b4 is placed on top of block b7, block b5 is not clear, block b5 is not on block b6, block b5 is not on the table, block b5 is not on top of block b3, block b5 is not placed on top of block b8, block b5 is on block b4, block b5 is placed on top of block b1, block b5 is placed on top of block b2, block b5 is placed on top of block b7, block b5 is placed on top of block b9, block b6 is being held, block b6 is not clear, block b6 is not located on the table, block b6 is not on block b8, block b6 is not on top of block b3, block b6 is not placed on top of block b2, block b6 is not placed on top of block b4, block b6 is not placed on top of block b7, block b6 is on block b1, block b6 is on block b9, block b6 is on top of block b5, block b7 is not clear, block b7 is not on block b6, block b7 is not on top of block b2, block b7 is not on top of block b9, block b7 is not placed on top of block b5, block b7 is on block b4, block b7 is on the table, block b7 is on top of block b1, block b7 is placed on top of block b3, block b7 is placed on top of block b8, block b8 is being held by the hand, block b8 is not clear, block b8 is not on block b9, block b8 is not on top of block b2, block b8 is not on top of block b7, block b8 is not placed on top of block b5, block b8 is on block b1, block b8 is on block b4, block b8 is on block b6, block b8 is on the table, block b8 is placed on top of block b3, block b9 is being held, block b9 is clear, block b9 is located on the table, block b9 is not on block b1, block b9 is not on block b2, block b9 is not on block b3, block b9 is not on block b4, block b9 is not on block b5, block b9 is on top of block b6, block b9 is placed on top of block b7, block b9 is placed on top of block b8, hand is holding some block, the hand is holding the block b5 and the hand is holding the block b7. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is placed on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from the top of block b8, block b9 is unstacked to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is not being grasped, block b1 is not clear of blocks, block b1 is not stacked on block b2, block b1 is not stacked on block b3, block b1 is not placed on the table, block b1 is not positioned on top of block b7, block b1 is stacked on block b4, block b1 is stacked on block b8, block b1 is positioned on top of block b5, block b1 is placed on top of block b6, block b1 is placed on top of block b9, block b2 is not being grasped, block b2 is not clear of blocks, block b2 is not stacked on block b3, block b2 is not positioned on top of block b8, block b2 is not positioned on top of block b9, block b2 is placed on the table, block b2 is stacked on top of block b1, block b2 is stacked on top of block b4, block b2 is stacked on top of block b6, block b2 is placed on top of block b5, block b2 is placed on top of block b7, block b3 is being grasped, block b3 is not clear of blocks, block b3 is not stacked on block b1, block b3 is not stacked on block b4, block b3 is not placed on the table, block b3 is not stacked on top of block b8, block b3 is not placed on top of block b5, block b3 is not placed on top of block b9, block b3 is stacked on top of block b6, block b3 is placed on top of block b2, block b3 is placed on top of block b7, block b4 is not being grasped, block b4 is not clear of blocks, block b4 is not stacked on block b2, block b4 is not stacked on top of block b5, block b4 is not stacked on top of block b9, block b4 is not placed on top of block b8, block b4 is placed on the table, block b4 is stacked on top of block b1, block b4 is stacked on top of block b3, block b4 is stacked on top of block b6, block b4 is placed on top of block b7, block b5 is not clear of blocks, block b5 is not stacked on block b6, block b5 is not placed on the table, block b5 is not stacked on top of block b3, block b5 is not placed on top of block b8, block b5 is stacked on block b4, block b5 is placed on top of block b1, block b5 is placed on top of block b2, block b5 is placed on top of block b7, block b5 is placed on top of block b9, block b6 is being grasped, block b6 is not clear of blocks, block b6 is not placed on the table, block b6 is not stacked on block b8, block b6 is not stacked on top of block b3, block b6 is not placed on top of block b2, block b6 is not placed on top of block b4, block b6 is not placed on top of block b7, block b6 is stacked on block b1, block b6 is stacked on block b9, block b6 is stacked on top of block b5, block b7 is not clear of blocks, block b7 is not stacked on block b6, block b7 is not stacked on top of block b2, block b7 is not stacked on top of block b9, block b7 is not placed on top of block b5, block b7 is stacked on block b4, block b7 is placed on the table, block b7 is stacked on top of block b1, block b7 is placed on top of block b3, block b7 is placed on top of block b8, block b8 is being held by the hand, block b8 is not clear of blocks, block b8 is not stacked on block b9, block b8 is not stacked on top of block b2, block b8 is not stacked on top of block b7, block b8 is not placed on top of block b5, block b8 is stacked on block b1, block b8 is stacked on block b4, block b8 is stacked on block b6, block b8 is placed on the table, block b8 is placed on top of block b3, block b9 is being grasped, block b9 is clear of blocks, block b9 is placed on the table, block b9 is not stacked on block b1, block b9 is not stacked on block b2, block b9 is not stacked on block b3, block b9 is not stacked on block b4, block b9 is not stacked on block b5, block b9 is stacked on top of block b6, block b9 is placed on top of block b7, block b9 is placed on top of block b8, the hand is holding some block, the hand is holding block b5 and the hand is holding block b7. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "41da95a9-a05f-4daf-b393-9d9f87107f0d", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked, block b4 is put down on the table, from top of block b6, block b1 is unstacked, block b1 is put down on the table, from top of block b5, block b2 is unstacked, on top of block b4, block b2 is stacked, from top of block b7, block b6 is unstacked, block b6 is put down, block b7 is picked up and block b7 is stacked on top of block b2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is not clear, block b1 is not on the table, block b2 is on top of block b4, block b3 is not clear, block b3 is on top of block b8, block b4 is not located on the table, block b5 is clear, block b5 is not located on the table, block b6 is clear, block b6 is not on the table, block b7 is not clear, block b7 is on top of block b2, block b8 is located on the table and hand is not holding anything. Respond with True or False.", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b4 is removed from the top of block b1 and placed on the table, then block b1 is removed from the top of block b6 and placed on the table, next block b2 is removed from the top of block b5 and stacked on top of block b4, after that block b6 is removed from the top of block b7 and placed down, then block b7 is picked up and stacked on top of block b2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is not clear, block b1 is not on the table, block b2 is on top of block b4, block b3 is not clear, block b3 is on top of block b8, block b4 is not located on the table, block b5 is clear, block b5 is not located on the table, block b6 is clear, block b6 is not on the table, block b7 is not clear, block b7 is on top of block b2, block b8 is located on the table and the hand is empty. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is on the table, and the hand is empty."}
{"question_id": "6af22269-547e-4e47-9091-1f4369b386e5", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked, block b3 is put down, block b5 is unstacked from top of block b4, block b5 is stacked on top of block b2, from top of block b1, block b4 is unstacked, block b4 is put down, block b5 is unstacked from top of block b2, on top of block b1, block b5 is stacked, block b7 is unstacked from top of block b6, block b7 is stacked on top of block b4, block b6 is picked up by the hand, on top of block b5, block b6 is stacked, block b2 is picked up by the hand, block b2 is stacked on top of block b6, block b3 is picked up, on top of block b2, block b3 is stacked, block b7 is unstacked from top of block b4, on top of block b3, block b7 is stacked and block b4 is picked up from the table to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is on the table, block b2 is on top of block b6, block b3 is on block b2, block b5 is placed on top of block b1, block b6 is placed on top of block b5, block b7 is clear, block b7 is placed on top of block b3 and the hand is holding the block b4. Respond with True or False.", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on block b4, block b6 is on the table, block b7 is on block b6 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b7 and placed on the table, block b5 is removed from the top of block b4 and stacked on top of block b2, block b4 is removed from the top of block b1 and placed on the table, block b5 is then removed from the top of block b2 and stacked on top of block b1, block b7 is removed from the top of block b6 and stacked on top of block b4, block b6 is picked up by the hand and stacked on top of block b5, block b2 is picked up by the hand and stacked on top of block b6, block b3 is picked up and stacked on top of block b2, block b7 is removed from the top of block b4 and stacked on top of block b3, and finally, block b4 is picked up from the table to reach the current state. In this state, are the following properties of the state valid without involving negations? block b1 is on the table, block b2 is on top of block b6, block b3 is on block b2, block b5 is on top of block b1, block b6 is on top of block b5, block b7 is clear, block b7 is on top of block b3, and the hand is holding block b4. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is positioned on the table, block b2 is not stacked, block b2 is also on the table, block b3 is not stacked, block b3 is placed on block b7, block b4 is positioned on top of block b1, block b5 is not stacked, block b5 is placed on block b4, block b6 is on the table, block b7 is positioned on block b6, and the hand is empty."}
{"question_id": "9440122d-2b33-453c-b1a0-e231fe80feb6", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8, block b9 is put down on the table, from top of block b6, block b2 is unstacked, on top of block b5, block b2 is stacked, block b6 is unstacked from block b3, block b6 is put down on the table, block b3 is unstacked from block b4, block b3 is stacked on top of block b9, block b8 is unstacked from top of block b1 and block b8 is stacked on top of block b4 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is clear, block b1 is placed on top of block b7, block b2 is clear, block b2 is on top of block b5, block b3 is clear, block b3 is on block b9, block b4 is on the table, block b5 is on the table, block b6 is clear, block b6 is on the table, block b7 is on the table, block b8 is clear, block b8 is on block b4, block b9 is located on the table and hand is not holding anything. Respond with True or False.", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is placed on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b9 is removed from block b8, block b9 is placed on the table, block b2 is removed from the top of block b6, block b2 is stacked on top of block b5, block b6 is removed from block b3, block b6 is placed on the table, block b3 is removed from block b4, block b3 is stacked on top of block b9, block b8 is removed from the top of block b1 and block b8 is stacked on top of block b4 to achieve the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is clear, block b1 is placed on top of block b7, block b2 is clear, block b2 is on top of block b5, block b3 is clear, block b3 is on block b9, block b4 is on the table, block b5 is on the table, block b6 is clear, block b6 is on the table, block b7 is on the table, block b8 is clear, block b8 is on block b4, block b9 is located on the table and the hand is empty. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "996f85b3-c85c-4e23-a624-73a36af57563", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked, block b3 is put down, block b5 is unstacked from block b4, on top of block b2, block b5 is stacked, block b4 is unstacked from top of block b1, block b4 is put down on the table, block b5 is unstacked from top of block b2, on top of block b1, block b5 is stacked, block b7 is unstacked from top of block b6, block b7 is stacked on top of block b4, block b6 is picked up by the hand, block b6 is stacked on top of block b5, block b2 is picked up by the hand, on top of block b6, block b2 is stacked, block b3 is picked up by the hand, on top of block b2, block b3 is stacked, from top of block b4, block b7 is unstacked, block b7 is stacked on top of block b3 and block b4 is picked up by the hand to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "block b1 is on the table, block b2 is on block b6, block b3 is on block b2, block b5 is placed on top of block b1, block b6 is on top of block b5, block b7 is clear, block b7 is on top of block b3 and the hand is holding the block b4", "plan_length": 19, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on block b4, block b6 is on the table, block b7 is on block b6 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b3 is removed from the top of block b7 and placed on the table, then block b5 is unstacked from block b4 and placed on top of block b2. Next, block b4 is removed from the top of block b1 and placed on the table. Block b5 is then unstacked from block b2 and stacked on top of block b1. Block b7 is unstacked from the top of block b6 and stacked on top of block b4. Block b6 is picked up and stacked on top of block b5. Block b2 is then picked up and stacked on top of block b6, followed by block b3 being picked up and stacked on top of block b2. Block b7 is then unstacked from block b4 and stacked on top of block b3. Finally, block b4 is picked up to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is positioned on the table, block b2 is not stacked, block b2 is also on the table, block b3 is not stacked, block b3 is placed on block b7, block b4 is positioned on top of block b1, block b5 is not stacked, block b5 is placed on block b4, block b6 is on the table, block b7 is positioned on block b6, and the hand is empty."}
{"question_id": "09239193-de01-4f94-b34f-927cccb191e1", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is clear, block b1 is on top of block b6, block b2 is clear, block b2 is not on top of block b5, block b3 is clear, block b3 is not placed on top of block b8, block b5 is not on the table, block b6 is on top of block b7, block b7 is located on the table, block b8 is not located on the table and the hand is not holding the block b4. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b4 is removed from block b1 to achieve the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is clear, block b1 is on top of block b6, block b2 is clear, block b2 is not on top of block b5, block b3 is clear, block b3 is not placed on top of block b8, block b5 is not on the table, block b6 is on top of block b7, block b7 is located on the table, block b8 is not located on the table and the hand is not holding the block b4. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is on the table, and the hand is empty."}
{"question_id": "41928133-c4a1-48de-a1a5-f33e20d245fb", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, block b3 is put down on the table, from top of block b6, block b2 is unstacked, block b2 is put down on the table, block b6 is picked up, on top of block b3, block b6 is stacked, from top of block b7, block b5 is unstacked, on top of block b6, block b5 is stacked, block b7 is unstacked from top of block b1 and block b7 is put down to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is clear, block b1 is placed on top of block b4, block b2 is clear, block b2 is located on the table, block b3 is located on the table, block b4 is located on the table, block b5 is clear, block b5 is placed on top of block b6, block b6 is on block b3, block b7 is clear, block b7 is on the table and hand is not holding anything. Respond with True or False.", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from block b5, block b3 is placed on the table, block b2 is removed from the top of block b6, block b2 is placed on the table, block b6 is picked up, block b6 is stacked on top of block b3, block b5 is removed from the top of block b7, block b5 is stacked on top of block b6, and block b7 is removed from the top of block b1 and placed on the table to reach the current state. In this state, are the following properties of the state, which do not involve negations, all valid? block b1 is clear, block b1 is on top of block b4, block b2 is clear, block b2 is on the table, block b3 is on the table, block b4 is on the table, block b5 is clear, block b5 is on top of block b6, block b6 is on block b3, block b7 is clear, block b7 is on the table, and the hand is empty. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has nothing on it, block b2 is positioned above block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is situated on the table, block b5 is resting on block b7, block b6 is also on the table, block b7 is supporting block b1, and the hand is empty."}
{"question_id": "716faf87-c03d-4354-88d7-7dac3513e2a1", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, block b3 is put down on the table, block b5 is unstacked from top of block b4, on top of block b2, block b5 is stacked, block b4 is unstacked from top of block b1, the hand puts down the block b4, block b5 is unstacked from block b2, block b5 is stacked on top of block b1, block b7 is unstacked from block b6 and block b7 is stacked on top of block b4 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "block b1 is not being held, block b1 is not clear, block b1 is not on block b2, block b1 is not on block b5, block b1 is not placed on top of block b3, block b1 is not placed on top of block b4, block b1 is not placed on top of block b6, block b1 is not placed on top of block b7, block b2 is not on block b4, block b2 is not on block b5, block b2 is not on block b7, block b2 is not on top of block b1, block b2 is not on top of block b3, block b2 is not on top of block b6, block b3 is not being held by the hand, block b3 is not on block b1, block b3 is not on block b2, block b3 is not on block b4, block b3 is not on block b5, block b3 is not on top of block b6, block b3 is not on top of block b7, block b4 is not clear, block b4 is not on block b3, block b4 is not on block b5, block b4 is not on top of block b7, block b4 is not placed on top of block b1, block b4 is not placed on top of block b2, block b4 is not placed on top of block b6, block b5 is not being held by the hand, block b5 is not located on the table, block b5 is not on block b3, block b5 is not on block b6, block b5 is not on block b7, block b5 is not on top of block b4, block b5 is not placed on top of block b2, block b6 is not on block b1, block b6 is not on block b7, block b6 is not on top of block b3, block b6 is not placed on top of block b2, block b6 is not placed on top of block b4, block b6 is not placed on top of block b5, block b7 is not being held by the hand, block b7 is not located on the table, block b7 is not on block b3, block b7 is not on top of block b1, block b7 is not on top of block b2, block b7 is not on top of block b5, block b7 is not on top of block b6, the hand is not holding the block b2, the hand is not holding the block b4 and the hand is not holding the block b6", "plan_length": 10, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on block b4, block b6 is on the table, block b7 is on block b6 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b7, block b3 is placed on the table, block b5 is removed from the top of block b4, block b5 is then stacked on top of block b2, block b4 is removed from the top of block b1, the hand releases block b4, block b5 is removed from block b2, block b5 is then stacked on top of block b1, block b7 is removed from block b6 and block b7 is stacked on top of block b4 to achieve the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is positioned on the table, block b2 is not stacked, block b2 is also on the table, block b3 is not stacked, block b3 is placed on block b7, block b4 is positioned on top of block b1, block b5 is not stacked, block b5 is placed on block b4, block b6 is on the table, block b7 is positioned on block b6, and the hand is empty."}
{"question_id": "dde32de8-547f-4aec-866f-e5776bae57cb", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked, the hand puts down the block b9, block b2 is unstacked from top of block b6, on top of block b5, block b2 is stacked, from top of block b3, block b6 is unstacked, the hand puts down the block b6, from top of block b4, block b3 is unstacked, block b3 is stacked on top of block b9, block b8 is unstacked from block b1, on top of block b4, block b8 is stacked, block b2 is unstacked from block b5, block b2 is stacked on top of block b8, block b3 is unstacked from top of block b9, on top of block b2, block b3 is stacked, from top of block b7, block b1 is unstacked, block b1 is stacked on top of block b3, block b7 is picked up, block b7 is stacked on top of block b9 and block b6 is picked up to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is clear, block b1 is on block b3, block b2 is on block b8, block b3 is on block b2, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b7 is clear, block b7 is on block b9, block b8 is placed on top of block b4, block b9 is on the table and the hand is holding the block b6. Respond with True or False.", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is placed on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b9 is removed from the top of block b8 and placed on the table, block b2 is removed from the top of block b6 and placed on top of block b5, block b6 is removed from the top of block b3 and placed on the table, block b3 is removed from the top of block b4 and placed on top of block b9, block b8 is removed from block b1 and placed on top of block b4, block b2 is removed from block b5 and placed on top of block b8, block b3 is removed from the top of block b9 and placed on top of block b2, block b1 is removed from the top of block b7 and placed on top of block b3, block b7 is picked up and placed on top of block b9, and block b6 is picked up to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is clear, block b1 is on block b3, block b2 is on block b8, block b3 is on block b2, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b7 is clear, block b7 is on block b9, block b8 is placed on top of block b4, block b9 is on the table and the hand is holding the block b6. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "66a9fdb6-501a-4bb7-a9ba-6448e0dbd56e", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8, block b9 is put down, block b2 is unstacked from block b6, on top of block b5, block b2 is stacked, from top of block b3, block b6 is unstacked, block b6 is put down on the table, from top of block b4, block b3 is unstacked, block b3 is stacked on top of block b9, from top of block b1, block b8 is unstacked, block b8 is stacked on top of block b4, from top of block b5, block b2 is unstacked, on top of block b8, block b2 is stacked, block b3 is unstacked from top of block b9, block b3 is stacked on top of block b2, from top of block b7, block b1 is unstacked, on top of block b3, block b1 is stacked, block b7 is picked up from the table, block b7 is stacked on top of block b9 and block b6 is picked up by the hand to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "block b1 is clear, block b1 is not located on the table, block b1 is not on block b2, block b1 is not on block b4, block b1 is not on top of block b6, block b1 is not placed on top of block b5, block b1 is not placed on top of block b7, block b1 is not placed on top of block b8, block b1 is not placed on top of block b9, block b1 is placed on top of block b3, block b2 is not clear, block b2 is not located on the table, block b2 is not on block b6, block b2 is not on block b9, block b2 is not on top of block b1, block b2 is not on top of block b4, block b2 is not on top of block b7, block b2 is not placed on top of block b3, block b2 is not placed on top of block b5, block b2 is on top of block b8, block b3 is not being held, block b3 is not clear, block b3 is not located on the table, block b3 is not on block b6, block b3 is not on block b7, block b3 is not on block b9, block b3 is not on top of block b1, block b3 is not on top of block b4, block b3 is not on top of block b5, block b3 is not on top of block b8, block b3 is on top of block b2, block b4 is not being held by the hand, block b4 is not clear, block b4 is not on block b2, block b4 is not on top of block b3, block b4 is not on top of block b8, block b4 is not placed on top of block b1, block b4 is not placed on top of block b5, block b4 is not placed on top of block b6, block b4 is not placed on top of block b7, block b4 is not placed on top of block b9, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b5 is not being held by the hand, block b5 is not on block b1, block b5 is not on block b3, block b5 is not on block b4, block b5 is not on block b8, block b5 is not on block b9, block b5 is not on top of block b6, block b5 is not placed on top of block b2, block b5 is not placed on top of block b7, block b6 is being held by the hand, block b6 is not clear, block b6 is not on block b1, block b6 is not on block b4, block b6 is not on block b5, block b6 is not on the table, block b6 is not on top of block b8, block b6 is not placed on top of block b2, block b6 is not placed on top of block b3, block b6 is not placed on top of block b7, block b6 is not placed on top of block b9, block b7 is clear, block b7 is not being held by the hand, block b7 is not located on the table, block b7 is not on top of block b4, block b7 is not on top of block b5, block b7 is not placed on top of block b1, block b7 is not placed on top of block b2, block b7 is not placed on top of block b3, block b7 is not placed on top of block b6, block b7 is not placed on top of block b8, block b7 is on top of block b9, block b8 is not being held, block b8 is not clear, block b8 is not on block b1, block b8 is not on block b5, block b8 is not on block b6, block b8 is not on the table, block b8 is not on top of block b3, block b8 is not placed on top of block b2, block b8 is not placed on top of block b7, block b8 is not placed on top of block b9, block b8 is on block b4, block b9 is located on the table, block b9 is not being held, block b9 is not clear, block b9 is not on block b8, block b9 is not on top of block b1, block b9 is not on top of block b2, block b9 is not on top of block b3, block b9 is not on top of block b4, block b9 is not placed on top of block b5, block b9 is not placed on top of block b6, block b9 is not placed on top of block b7, hand is holding some block, the hand is not holding the block b1 and the hand is not holding the block b2", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is placed on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b9 is removed from the top of block b8, then placed down, block b2 is removed from block b6 and placed on top of block b5, block b6 is then removed from the top of block b3 and placed on the table, block b3 is removed from the top of block b4 and stacked on top of block b9, block b8 is removed from the top of block b1 and stacked on top of block b4, block b2 is then removed from the top of block b5 and stacked on top of block b8, block b3 is removed from the top of block b9 and stacked on top of block b2, block b1 is removed from the top of block b7 and stacked on top of block b3, block b7 is picked up from the table and stacked on top of block b9, and finally, block b6 is picked up by the hand to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "275daa33-9ec3-41de-b59c-56d1a5895b63", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8, the hand puts down the block b9, block b2 is unstacked from block b6, on top of block b5, block b2 is stacked, from top of block b3, block b6 is unstacked, the hand puts down the block b6, block b3 is unstacked from block b4, on top of block b9, block b3 is stacked, block b8 is unstacked from top of block b1, on top of block b4, block b8 is stacked, from top of block b5, block b2 is unstacked, block b2 is stacked on top of block b8, block b3 is unstacked from top of block b9, block b3 is stacked on top of block b2, block b1 is unstacked from top of block b7, block b1 is stacked on top of block b3, block b7 is picked up from the table, block b7 is stacked on top of block b9 and block b6 is picked up to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is not clear, block b1 is not located on the table, block b1 is not on top of block b4, block b1 is not on top of block b8, block b1 is not placed on top of block b5, block b1 is on block b7, block b1 is on top of block b6, block b1 is placed on top of block b2, block b1 is placed on top of block b3, block b1 is placed on top of block b9, block b2 is located on the table, block b2 is not clear, block b2 is not on block b4, block b2 is not on top of block b6, block b2 is not placed on top of block b7, block b2 is on block b1, block b2 is on block b3, block b2 is on top of block b8, block b2 is placed on top of block b5, block b2 is placed on top of block b9, block b3 is clear, block b3 is not being held by the hand, block b3 is not on top of block b5, block b3 is not placed on top of block b4, block b3 is not placed on top of block b6, block b3 is not placed on top of block b9, block b3 is on block b1, block b3 is on the table, block b3 is placed on top of block b2, block b3 is placed on top of block b7, block b3 is placed on top of block b8, block b4 is being held by the hand, block b4 is clear, block b4 is not on top of block b7, block b4 is not placed on top of block b3, block b4 is on block b6, block b4 is on the table, block b4 is on top of block b5, block b4 is placed on top of block b1, block b4 is placed on top of block b2, block b4 is placed on top of block b8, block b4 is placed on top of block b9, block b5 is clear, block b5 is located on the table, block b5 is not being held, block b5 is not on block b1, block b5 is not on block b6, block b5 is not on block b7, block b5 is on block b8, block b5 is on top of block b2, block b5 is on top of block b3, block b5 is on top of block b4, block b5 is placed on top of block b9, block b6 is being held, block b6 is clear, block b6 is not located on the table, block b6 is not on block b4, block b6 is not on top of block b7, block b6 is not on top of block b9, block b6 is not placed on top of block b5, block b6 is on top of block b1, block b6 is placed on top of block b2, block b6 is placed on top of block b3, block b6 is placed on top of block b8, block b7 is being held by the hand, block b7 is not clear, block b7 is not on block b1, block b7 is not on top of block b4, block b7 is not on top of block b6, block b7 is not on top of block b8, block b7 is not placed on top of block b2, block b7 is not placed on top of block b5, block b7 is not placed on top of block b9, block b7 is on the table, block b7 is on top of block b3, block b8 is being held by the hand, block b8 is clear, block b8 is located on the table, block b8 is not on block b6, block b8 is not on top of block b5, block b8 is not placed on top of block b1, block b8 is on block b7, block b8 is on top of block b9, block b8 is placed on top of block b2, block b8 is placed on top of block b3, block b8 is placed on top of block b4, block b9 is clear, block b9 is located on the table, block b9 is not being held, block b9 is not on block b3, block b9 is not on block b6, block b9 is not on top of block b2, block b9 is not on top of block b8, block b9 is not placed on top of block b7, block b9 is on block b1, block b9 is placed on top of block b4, block b9 is placed on top of block b5, hand is not empty, the hand is holding the block b2 and the hand is not holding the block b1. Respond with True or False.", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is placed on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b9 is removed from block b8, the hand places block b9 down, block b2 is removed from block b6, block b2 is placed on top of block b5, block b6 is removed from block b3, the hand places block b6 down, block b3 is removed from block b4, block b3 is placed on top of block b9, block b8 is removed from block b1, block b8 is placed on top of block b4, block b2 is removed from block b5, block b2 is placed on top of block b8, block b3 is removed from block b9, block b3 is placed on top of block b2, block b1 is removed from block b7, block b1 is placed on top of block b3, block b7 is picked up from the table, block b7 is placed on top of block b9, and block b6 is picked up to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is not clear, block b1 is not on the table, block b1 is not on top of block b4, block b1 is not on top of block b8, block b1 is not on top of block b5, block b1 is on block b7, block b1 is on top of block b6, block b1 is on top of block b2, block b1 is on top of block b3, block b1 is on top of block b9, block b2 is on the table, block b2 is not clear, block b2 is not on block b4, block b2 is not on top of block b6, block b2 is not on top of block b7, block b2 is on block b1, block b2 is on block b3, block b2 is on top of block b8, block b2 is on top of block b5, block b2 is on top of block b9, block b3 is clear, block b3 is not being held by the hand, block b3 is not on top of block b5, block b3 is not on top of block b4, block b3 is not on top of block b6, block b3 is not on top of block b9, block b3 is on block b1, block b3 is on the table, block b3 is on top of block b2, block b3 is on top of block b7, block b3 is on top of block b8, block b4 is being held by the hand, block b4 is clear, block b4 is not on top of block b7, block b4 is not on top of block b3, block b4 is on block b6, block b4 is on the table, block b4 is on top of block b5, block b4 is on top of block b1, block b4 is on top of block b2, block b4 is on top of block b8, block b4 is on top of block b9, block b5 is clear, block b5 is on the table, block b5 is not being held, block b5 is not on block b1, block b5 is not on block b6, block b5 is not on block b7, block b5 is on block b8, block b5 is on top of block b2, block b5 is on top of block b3, block b5 is on top of block b4, block b5 is on top of block b9, block b6 is being held, block b6 is clear, block b6 is not on the table, block b6 is not on block b4, block b6 is not on top of block b7, block b6 is not on top of block b9, block b6 is not on top of block b5, block b6 is on top of block b1, block b6 is on top of block b2, block b6 is on top of block b3, block b6 is on top of block b8, block b7 is being held by the hand, block b7 is not clear, block b7 is not on block b1, block b7 is not on top of block b4, block b7 is not on top of block b6, block b7 is not on top of block b8, block b7 is not on top of block b2, block b7 is not on top of block b5, block b7 is not on top of block b9, block b7 is on the table, block b7 is on top of block b3, block b8 is being held by the hand, block b8 is clear, block b8 is on the table, block b8 is not on block b6, block b8 is not on top of block b5, block b8 is not on top of block b1, block b8 is on block b7, block b8 is on top of block b9, block b8 is on top of block b2, block b8 is on top of block b3, block b8 is on top of block b4, block b9 is clear, block b9 is on the table, block b9 is not being held, block b9 is not on block b3, block b9 is not on block b6, block b9 is not on top of block b2, block b9 is not on top of block b8, block b9 is not on top of block b7, block b9 is on block b1, block b9 is on top of block b4, block b9 is on top of block b5, the hand is not empty, the hand is holding block b2, and the hand is not holding block b1. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on top of block b6, block b3 is stacked on top of block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on top of block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is stacked on top of block b8, and the hand is empty."}
{"question_id": "a7119758-d761-4a8e-b315-8e932f81e634", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8, block b9 is put down on the table, block b2 is unstacked from block b6, block b2 is stacked on top of block b5, block b6 is unstacked from block b3, block b6 is put down on the table, block b3 is unstacked from top of block b4, on top of block b9, block b3 is stacked, from top of block b1, block b8 is unstacked, block b8 is stacked on top of block b4, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b8, block b3 is unstacked from block b9, on top of block b2, block b3 is stacked, from top of block b7, block b1 is unstacked, block b1 is stacked on top of block b3, block b7 is picked up by the hand, block b7 is stacked on top of block b9 and block b6 is picked up from the table to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "block b1 is clear, block b1 is on block b3, block b2 is on block b8, block b3 is placed on top of block b2, block b4 is on the table, block b5 is clear, block b5 is located on the table, block b6 is being held by the hand, block b7 is clear, block b7 is on top of block b9, block b8 is on block b4 and block b9 is on the table", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is placed on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b9 is removed from the top of block b8, then placed on the table, block b2 is removed from block b6, and then stacked on top of block b5. Next, block b6 is removed from block b3 and placed on the table. Block b3 is then removed from the top of block b4 and stacked on top of block b9. Block b8 is removed from the top of block b1 and stacked on top of block b4. Block b2 is then removed from the top of block b5 and stacked on top of block b8. Block b3 is removed from block b9 and stacked on top of block b2. Block b1 is removed from the top of block b7 and stacked on top of block b3. Block b7 is picked up and stacked on top of block b9, and finally, block b6 is picked up from the table to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "e3601fbd-17e6-4eb4-b2fd-e618dbeaef94", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, block b3 is put down, block b5 is unstacked from block b4, block b5 is stacked on top of block b2, from top of block b1, block b4 is unstacked, block b4 is put down, from top of block b2, block b5 is unstacked, block b5 is stacked on top of block b1, block b7 is unstacked from top of block b6 and on top of block b4, block b7 is stacked to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is not clear, block b1 is not on block b3, block b1 is not on block b5, block b1 is not on block b7, block b1 is not on top of block b6, block b1 is not placed on top of block b2, block b1 is not placed on top of block b4, block b2 is not being held, block b2 is not on top of block b3, block b2 is not on top of block b5, block b2 is not on top of block b7, block b2 is not placed on top of block b1, block b2 is not placed on top of block b4, block b2 is not placed on top of block b6, block b3 is not on block b4, block b3 is not on block b7, block b3 is not on top of block b1, block b3 is not placed on top of block b2, block b3 is not placed on top of block b5, block b3 is not placed on top of block b6, block b4 is not clear, block b4 is not on top of block b2, block b4 is not on top of block b7, block b4 is not placed on top of block b1, block b4 is not placed on top of block b3, block b4 is not placed on top of block b5, block b4 is not placed on top of block b6, block b5 is not located on the table, block b5 is not on top of block b6, block b5 is not placed on top of block b2, block b5 is not placed on top of block b3, block b5 is not placed on top of block b4, block b5 is not placed on top of block b7, block b6 is not being held by the hand, block b6 is not on block b2, block b6 is not on block b5, block b6 is not placed on top of block b1, block b6 is not placed on top of block b3, block b6 is not placed on top of block b4, block b6 is not placed on top of block b7, block b7 is not being held, block b7 is not located on the table, block b7 is not on block b5, block b7 is not on top of block b1, block b7 is not on top of block b2, block b7 is not on top of block b3, block b7 is not placed on top of block b6, the hand is not holding the block b1, the hand is not holding the block b3, the hand is not holding the block b4 and the hand is not holding the block b5. Respond with True or False.", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is on block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on block b4, block b6 is on the table, block b7 is on block b6 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b7, block b3 is placed down, block b5 is removed from block b4, block b5 is stacked on top of block b2, block b4 is removed from the top of block b1, block b4 is placed down, block b5 is removed from the top of block b2, block b5 is stacked on top of block b1, and block b7 is removed from the top of block b6 and stacked on top of block b4 to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is not clear, block b1 is not on top of block b3, block b1 is not on top of block b5, block b1 is not on top of block b7, block b1 is not on top of block b6, block b1 is not stacked on top of block b2, block b1 is not stacked on top of block b4, block b2 is not being held, block b2 is not on top of block b3, block b2 is not on top of block b5, block b2 is not on top of block b7, block b2 is not stacked on top of block b1, block b2 is not stacked on top of block b4, block b2 is not stacked on top of block b6, block b3 is not on block b4, block b3 is not on block b7, block b3 is not on top of block b1, block b3 is not stacked on top of block b2, block b3 is not stacked on top of block b5, block b3 is not stacked on top of block b6, block b4 is not clear, block b4 is not on top of block b2, block b4 is not on top of block b7, block b4 is not stacked on top of block b1, block b4 is not stacked on top of block b3, block b4 is not stacked on top of block b5, block b4 is not stacked on top of block b6, block b5 is not on the table, block b5 is not on top of block b6, block b5 is not stacked on top of block b2, block b5 is not stacked on top of block b3, block b5 is not stacked on top of block b4, block b5 is not stacked on top of block b7, block b6 is not being held by the hand, block b6 is not on block b2, block b6 is not on block b5, block b6 is not stacked on top of block b1, block b6 is not stacked on top of block b3, block b6 is not stacked on top of block b4, block b6 is not stacked on top of block b7, block b7 is not being held, block b7 is not on the table, block b7 is not on block b5, block b7 is not on top of block b1, block b7 is not on top of block b2, block b7 is not on top of block b3, block b7 is not stacked on top of block b6, the hand is not holding block b1, the hand is not holding block b3, the hand is not holding block b4, and the hand is not holding block b5. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is positioned on the table, block b2 is not stacked, block b2 is also on the table, block b3 is not stacked, block b3 is placed on block b7, block b4 is positioned on top of block b1, block b5 is not stacked, block b5 is placed on block b4, block b6 is on the table, block b7 is positioned on block b6, and the hand is empty."}
{"question_id": "5f98b3c0-bf3e-49b9-a619-899c1b142de0", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1, the hand puts down the block b4, block b1 is unstacked from block b6, the hand puts down the block b1, block b2 is unstacked from block b5, block b2 is stacked on top of block b4, block b6 is unstacked from block b7, the hand puts down the block b6, block b7 is picked up from the table, block b7 is stacked on top of block b2, from top of block b8, block b3 is unstacked, block b3 is stacked on top of block b7, block b1 is picked up from the table, on top of block b3, block b1 is stacked, block b5 is picked up by the hand, block b5 is stacked on top of block b1, block b8 is picked up, block b8 is stacked on top of block b5 and block b6 is picked up to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "block b1 is on top of block b3, block b2 is on top of block b4, block b3 is placed on top of block b7, block b4 is located on the table, block b5 is on block b1, block b6 is being held by the hand, block b7 is on top of block b2, block b8 is clear and block b8 is placed on top of block b5", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: block b4 is removed from block b1, the hand places block b4 down, block b1 is removed from block b6, the hand sets block b1 down, block b2 is removed from block b5, block b2 is placed on top of block b4, block b6 is removed from block b7, the hand sets block b6 down, block b7 is lifted from the table, block b7 is placed on top of block b2, block b3 is removed from the top of block b8, block b3 is placed on top of block b7, block b1 is lifted from the table, block b1 is placed on top of block b3, block b5 is picked up by the hand, block b5 is placed on top of block b1, block b8 is lifted, block b8 is placed on top of block b5, and block b6 is picked up to achieve the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is on the table, and the hand is empty."}
{"question_id": "0234c784-6dfe-4444-bb54-6f267fd4ff7a", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, block b3 is put down on the table, block b2 is unstacked from block b6, block b2 is put down, block b6 is picked up, on top of block b3, block b6 is stacked, from top of block b7, block b5 is unstacked, on top of block b6, block b5 is stacked, block b7 is unstacked from top of block b1, block b7 is put down, from top of block b4, block b1 is unstacked, block b1 is stacked on top of block b2, block b4 is picked up, block b4 is stacked on top of block b5, block b1 is unstacked from top of block b2, on top of block b4, block b1 is stacked, block b2 is picked up, block b2 is stacked on top of block b1 and block b7 is picked up from the table to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is on block b4, block b2 is clear, block b2 is on block b1, block b3 is located on the table, block b4 is on top of block b5, block b5 is on top of block b6, block b6 is on top of block b3 and the hand is holding the block b7. Respond with True or False.", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from block b5 and placed on the table, block b2 is removed from block b6 and put down, block b6 is picked up and placed on top of block b3, block b5 is removed from block b7 and stacked on top of block b6, block b7 is removed from block b1 and put down, block b1 is removed from block b4 and stacked on top of block b2, block b4 is picked up and stacked on top of block b5, block b1 is removed from block b2 and stacked on top of block b4, block b2 is picked up and stacked on top of block b1, and block b7 is picked up from the table to reach the current state. In this state, are the following properties of the state, which do not involve negations, valid? block b1 is on block b4, block b2 is clear, block b2 is on block b1, block b3 is on the table, block b4 is on top of block b5, block b5 is on top of block b6, block b6 is on top of block b3, and the hand is holding block b7. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has nothing on it, block b2 is positioned above block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is situated on the table, block b5 is resting on block b7, block b6 is also on the table, block b7 is supporting block b1, and the hand is empty."}
{"question_id": "e2a5d40c-1d63-457f-bf6c-4f5fcdab2324", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1, block b4 is put down, from top of block b6, block b1 is unstacked, block b1 is put down, from top of block b5, block b2 is unstacked, on top of block b4, block b2 is stacked, block b6 is unstacked from block b7, the hand puts down the block b6, block b7 is picked up from the table, block b7 is stacked on top of block b2, from top of block b8, block b3 is unstacked, on top of block b7, block b3 is stacked, block b1 is picked up by the hand, block b1 is stacked on top of block b3, block b5 is picked up by the hand, block b5 is stacked on top of block b1, block b8 is picked up by the hand, block b8 is stacked on top of block b5 and block b6 is picked up to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is clear, block b1 is not being held, block b1 is not on block b2, block b1 is not on block b6, block b1 is not on the table, block b1 is not on top of block b5, block b1 is not on top of block b8, block b1 is not placed on top of block b4, block b1 is not placed on top of block b7, block b2 is clear, block b2 is located on the table, block b2 is not being held, block b2 is not on block b1, block b2 is not on top of block b5, block b2 is not placed on top of block b3, block b2 is on block b7, block b2 is placed on top of block b6, block b2 is placed on top of block b8, block b3 is not being held, block b3 is not clear, block b3 is not on block b2, block b3 is not on the table, block b3 is not on top of block b1, block b3 is not on top of block b4, block b3 is on block b5, block b3 is on top of block b6, block b3 is on top of block b8, block b4 is being held, block b4 is clear, block b4 is not on block b1, block b4 is not on top of block b6, block b4 is not on top of block b7, block b4 is not placed on top of block b2, block b4 is not placed on top of block b3, block b4 is not placed on top of block b8, block b4 is placed on top of block b5, block b5 is located on the table, block b5 is not clear, block b5 is not on block b3, block b5 is not on top of block b6, block b5 is not placed on top of block b8, block b5 is on block b2, block b5 is on top of block b4, block b5 is placed on top of block b7, block b6 is clear, block b6 is not on block b8, block b6 is on block b2, block b6 is on the table, block b6 is on top of block b1, block b6 is on top of block b4, block b6 is placed on top of block b3, block b6 is placed on top of block b5, block b6 is placed on top of block b7, block b7 is clear, block b7 is not on top of block b6, block b7 is not placed on top of block b1, block b7 is on block b5, block b7 is on block b8, block b7 is on the table, block b7 is on top of block b3, block b7 is on top of block b4, block b8 is located on the table, block b8 is not on block b4, block b8 is not on top of block b2, block b8 is not on top of block b6, block b8 is on top of block b3, block b8 is placed on top of block b1, block b8 is placed on top of block b7, hand is empty, the hand is holding the block b5, the hand is not holding the block b7 and the hand is not holding the block b8. Respond with True or False.", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b4 is removed from the top of block b1 and placed down, block b1 is then removed from the top of block b6 and placed down, block b2 is removed from the top of block b5, and then stacked on top of block b4. Next, block b6 is removed from block b7, and block b6 is placed down. Block b7 is then picked up from the table and stacked on top of block b2. Block b3 is removed from the top of block b8 and stacked on top of block b7. Block b1 is then picked up by the hand and stacked on top of block b3. Block b5 is picked up by the hand and stacked on top of block b1. Block b8 is picked up by the hand and stacked on top of block b5, and block b6 is picked up to reach the current state. In this state, are all of the following valid properties of the state that involve negations? block b1 is clear of other blocks, block b1 is not being held by the hand, block b1 is not on block b2, block b1 is not on block b6, block b1 is not on the table, block b1 is not on top of block b5, block b1 is not on top of block b8, block b1 is not placed on top of block b4, block b1 is not placed on top of block b7, block b2 is clear of other blocks, block b2 is located on the table, block b2 is not being held by the hand, block b2 is not on block b1, block b2 is not on top of block b5, block b2 is not placed on top of block b3, block b2 is on block b7, block b2 is placed on top of block b6, block b2 is placed on top of block b8, block b3 is not being held by the hand, block b3 is not clear of other blocks, block b3 is not on block b2, block b3 is not on the table, block b3 is not on top of block b1, block b3 is not on top of block b4, block b3 is on block b5, block b3 is on top of block b6, block b3 is on top of block b8, block b4 is being held by the hand, block b4 is clear of other blocks, block b4 is not on block b1, block b4 is not on top of block b6, block b4 is not on top of block b7, block b4 is not placed on top of block b2, block b4 is not placed on top of block b3, block b4 is not placed on top of block b8, block b4 is placed on top of block b5, block b5 is located on the table, block b5 is not clear of other blocks, block b5 is not on block b3, block b5 is not on top of block b6, block b5 is not placed on top of block b8, block b5 is on block b2, block b5 is on top of block b4, block b5 is placed on top of block b7, block b6 is clear of other blocks, block b6 is not on block b8, block b6 is on block b2, block b6 is on the table, block b6 is on top of block b1, block b6 is on top of block b4, block b6 is placed on top of block b3, block b6 is placed on top of block b5, block b6 is placed on top of block b7, block b7 is clear of other blocks, block b7 is not on top of block b6, block b7 is not placed on top of block b1, block b7 is on block b5, block b7 is on block b8, block b7 is on the table, block b7 is on top of block b3, block b7 is on top of block b4, block b8 is located on the table, block b8 is not on block b4, block b8 is not on top of block b2, block b8 is not on top of block b6, block b8 is on top of block b3, block b8 is placed on top of block b1, block b8 is placed on top of block b7, the hand is empty, the hand is holding the block b5, the hand is not holding the block b7 and the hand is not holding the block b8. Respond with True or False.\n\nAnswer: False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is on the table, and the hand is empty."}
{"question_id": "53220dba-5c05-48b1-a504-1d325f8097ca", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1, block b4 is put down, block b1 is unstacked from block b6, the hand puts down the block b1, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b4, block b6 is unstacked from block b7, block b6 is put down on the table, block b7 is picked up, block b7 is stacked on top of block b2, block b3 is unstacked from block b8, on top of block b7, block b3 is stacked, block b1 is picked up from the table, block b1 is stacked on top of block b3, block b5 is picked up from the table, block b5 is stacked on top of block b1, block b8 is picked up from the table, on top of block b5, block b8 is stacked and block b6 is picked up by the hand to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is placed on top of block b3, block b2 is placed on top of block b4, block b3 is on top of block b7, block b4 is not on the table, block b5 is not on top of block b1, block b6 is not being held, block b7 is not placed on top of block b2, block b8 is clear and block b8 is on top of block b5. Respond with True or False.", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on top of block b6, block b2 is clear, block b2 is placed on top of block b5, block b3 is clear, block b3 is placed on top of block b8, block b4 is clear, block b4 is on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b4 is removed from the top of block b1 and placed down, block b1 is then removed from block b6 and put down, block b2 is removed from the top of block b5, block b2 is then stacked on top of block b4, block b6 is removed from block b7 and placed down on the table, block b7 is picked up and stacked on top of block b2, block b3 is removed from block b8 and stacked on top of block b7, block b1 is picked up from the table and stacked on top of block b3, block b5 is picked up from the table and stacked on top of block b1, block b8 is picked up from the table and stacked on top of block b5, and block b6 is picked up by the hand to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations? block b1 is on top of block b3, block b2 is on top of block b4, block b3 is on top of block b7, block b4 is not on the table, block b5 is not on top of block b1, block b6 is not being held, block b7 is not on top of block b2, block b8 is clear and block b8 is on top of block b5. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is placed on block b5, block b3 has no blocks on it, block b3 is placed on block b8, block b4 has no blocks on it, block b4 is stacked on block b1, block b5 is on the table, block b6 is on top of block b7, block b7 is on the table, block b8 is on the table, and the hand is empty."}
{"question_id": "bf2d3402-bf2b-43b8-9fd2-ad63ef60177c", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is placed on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is clear, block b8 is placed on top of block b1 and the hand is holding the block b9", "plan_length": 1, "initial_state_nl": "Block b1 is on top of block b7, block b2 is clear, block b2 is placed on top of block b6, block b3 is placed on top of block b4, block b4 is located on the table, block b5 is clear, block b5 is located on the table, block b6 is placed on top of block b3, block b7 is on the table, block b8 is on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: block b9 is removed from block b8 to achieve the current state. In this state, identify all valid properties that do not include negations and list them; if there are no such properties, indicate None.", "initial_state_nl_paraphrased": "Block b1 is positioned above block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is stacked on block b4, block b4 is situated on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is on the table, block b8 is positioned above block b1, block b9 has no blocks on it, block b9 is stacked on block b8, and the hand is empty."}
{"question_id": "d4c61606-4ec6-4157-916a-f7a649c38c1f", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5 to reach the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is clear, block b1 is not being held, block b1 is not located on the table, block b1 is not placed on top of block b6, block b1 is on block b4, block b1 is on block b7, block b1 is on top of block b3, block b1 is on top of block b5, block b1 is placed on top of block b2, block b2 is not clear, block b2 is not located on the table, block b2 is not on block b6, block b2 is not placed on top of block b4, block b2 is not placed on top of block b5, block b2 is on block b3, block b2 is on top of block b1, block b2 is placed on top of block b7, block b3 is not clear, block b3 is not on block b1, block b3 is not on block b2, block b3 is not on the table, block b3 is not on top of block b6, block b3 is not on top of block b7, block b3 is not placed on top of block b4, block b3 is on top of block b5, block b4 is clear, block b4 is not being held, block b4 is not on block b5, block b4 is not on top of block b1, block b4 is on block b3, block b4 is on block b7, block b4 is on the table, block b4 is on top of block b2, block b4 is placed on top of block b6, block b5 is being held by the hand, block b5 is located on the table, block b5 is not clear, block b5 is not placed on top of block b2, block b5 is not placed on top of block b4, block b5 is on block b1, block b5 is on block b6, block b5 is placed on top of block b3, block b5 is placed on top of block b7, block b6 is located on the table, block b6 is not being held by the hand, block b6 is not clear, block b6 is not on block b7, block b6 is not on top of block b4, block b6 is not placed on top of block b2, block b6 is on block b3, block b6 is placed on top of block b1, block b6 is placed on top of block b5, block b7 is being held by the hand, block b7 is not clear, block b7 is not on top of block b1, block b7 is not on top of block b5, block b7 is not placed on top of block b4, block b7 is not placed on top of block b6, block b7 is on the table, block b7 is placed on top of block b2, block b7 is placed on top of block b3, hand is not holding anything, the hand is holding the block b2 and the hand is holding the block b3. Respond with True or False.", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on block b5, block b4 is located on the table, block b5 is on block b7, block b6 is on the table, block b7 is on block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from block b5 to achieve the current state. In this state, are all of the following valid properties of the state (both with and without negations)? block b1 is clear of other blocks, block b1 is not being grasped, block b1 is not situated on the table, block b1 is not positioned on top of block b6, block b1 is positioned on block b4, block b1 is positioned on block b7, block b1 is positioned on top of block b3, block b1 is positioned on top of block b5, block b1 is placed on top of block b2, block b2 is not clear of other blocks, block b2 is not situated on the table, block b2 is not positioned on block b6, block b2 is not positioned on top of block b4, block b2 is not positioned on top of block b5, block b2 is positioned on block b3, block b2 is positioned on top of block b1, block b2 is placed on top of block b7, block b3 is not clear of other blocks, block b3 is not positioned on block b1, block b3 is not positioned on block b2, block b3 is not situated on the table, block b3 is not positioned on top of block b6, block b3 is not positioned on top of block b7, block b3 is not placed on top of block b4, block b3 is positioned on top of block b5, block b4 is clear of other blocks, block b4 is not being grasped, block b4 is not positioned on block b5, block b4 is not positioned on top of block b1, block b4 is positioned on block b3, block b4 is positioned on block b7, block b4 is situated on the table, block b4 is positioned on top of block b2, block b4 is placed on top of block b6, block b5 is being grasped by the hand, block b5 is situated on the table, block b5 is not clear of other blocks, block b5 is not placed on top of block b2, block b5 is not placed on top of block b4, block b5 is positioned on block b1, block b5 is positioned on block b6, block b5 is placed on top of block b3, block b5 is placed on top of block b7, block b6 is situated on the table, block b6 is not being grasped by the hand, block b6 is not clear of other blocks, block b6 is not positioned on block b7, block b6 is not positioned on top of block b4, block b6 is not placed on top of block b2, block b6 is positioned on block b3, block b6 is placed on top of block b1, block b6 is placed on top of block b5, block b7 is being grasped by the hand, block b7 is not clear of other blocks, block b7 is not positioned on top of block b1, block b7 is not positioned on top of block b5, block b7 is not placed on top of block b4, block b7 is not placed on top of block b6, block b7 is situated on the table, block b7 is placed on top of block b2, block b7 is placed on top of block b3, the hand is not holding anything, the hand is holding the block b2 and the hand is holding the block b3. Respond with True or False.", "initial_state_nl_paraphrased": "Block b1 is placed on block b4, block b2 has nothing on it, block b2 is positioned above block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is situated on the table, block b5 is resting on block b7, block b6 is also on the table, block b7 is supporting block b1, and the hand is empty."}
