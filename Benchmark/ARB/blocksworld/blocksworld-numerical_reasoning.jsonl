{"question_id": "ad812cfa-96b2-49ad-8285-e87bef592869", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 54? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is located on the table, block b7 is on top of block b6 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is unstacked from the top of block b7 to reach the current state. In this state, does the number of valid properties involving negations equal 54? True or False", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear of any blocks, block b2 is also on the table, block b3 is clear of any blocks, block b3 is stacked on top of block b7, block b4 is stacked on top of block b1, block b5 is clear of any blocks, block b5 is stacked on top of block b4, block b6 is on the table, block b7 is stacked on top of block b6, and the hand is empty."}
{"question_id": "476a16eb-db46-4361-984f-b4e61ba38739", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, the hand puts down the block b3, from top of block b4, block b5 is unstacked, on top of block b2, block b5 is stacked, block b4 is unstacked from block b1, the hand puts down the block b4, block b5 is unstacked from top of block b2, on top of block b1, block b5 is stacked, block b7 is unstacked from top of block b6 and block b7 is stacked on top of block b4 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "answer": "64", "plan_length": 10, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is located on the table, block b7 is on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are executed: block b3 is removed from the top of block b7, then placed by the hand, block b5 is unstacked from the top of block b4, and then stacked on top of block b2. Next, block b4 is unstacked from block b1 and placed by the hand. Block b5 is then unstacked from the top of block b2 and stacked on top of block b1. Finally, block b7 is unstacked from the top of block b6 and stacked on top of block b4, resulting in the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear of any blocks, block b2 is also on the table, block b3 is clear of any blocks, block b3 is stacked on top of block b7, block b4 is stacked on top of block b1, block b5 is clear of any blocks, block b5 is stacked on top of block b4, block b6 is on the table, block b7 is stacked on top of block b6, and the hand is empty."}
{"question_id": "928fda5d-85e0-4cb6-96af-46f25e8a1be3", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked to reach the current state. Is it True or False that the number of actions that led to current state in the sequence is equal to 1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is located on the table, block b7 is on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Considering the initial condition, the following actions are taken: block b3 is unstacked from the top of block b7 to achieve the current state. Is it True or False that the current state in the sequence resulted from exactly 1 action?", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear, block b2 is on the table, block b3 is clear, block b3 is on block b7, block b4 is on block b1, block b5 is clear, block b5 is on block b4, block b6 is on the table, block b7 is on block b6, and the hand is empty."}
{"question_id": "17c26b09-1f8e-494f-999f-a11b93c1ef5d", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, block b3 is put down, block b2 is unstacked from block b6, block b2 is put down, block b6 is picked up, on top of block b3, block b6 is stacked, from top of block b7, block b5 is unstacked, block b5 is stacked on top of block b6, from top of block b1, block b7 is unstacked, block b7 is put down on the table, block b1 is unstacked from block b4, on top of block b2, block b1 is stacked, block b4 is picked up from the table, on top of block b5, block b4 is stacked, block b1 is unstacked from top of block b2, on top of block b4, block b1 is stacked, block b2 is picked up from the table, on top of block b1, block b2 is stacked and block b7 is picked up from the table to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 8? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from block b5 and placed down, block b2 is removed from block b6 and placed down, block b6 is picked up and placed on top of block b3, block b5 is removed from block b7 and stacked on top of block b6, block b7 is removed from block b1 and placed down on the table, block b1 is removed from block b4 and stacked on top of block b2, block b4 is picked up from the table and stacked on top of block b5, block b1 is removed from block b2 and stacked on top of block b4, block b2 is picked up from the table and stacked on top of block b1, and finally, block b7 is picked up from the table to achieve the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 8? True or False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is placed on the table, block b5 is stacked on block b7, block b6 is placed on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "6f9cb3e1-3653-42f1-a93a-d42dd2c8cbdb", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1 to reach the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "answer": "70", "plan_length": 1, "initial_state_nl": "Block b1 is on block b6, block b2 is clear, block b2 is on top of block b5, block b3 is clear, block b3 is on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: block b4 is removed from the top of block b1 to achieve the current state. In this state, what is the total count of valid state properties that include negations? Express the answer as an integer, or write None if there are no such properties.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned above block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on top of block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is resting on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "d1e295c8-aaed-4baa-967e-2bda7b7f2662", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b7, block b3 is put down, block b5 is unstacked from top of block b4, on top of block b2, block b5 is stacked, from top of block b1, block b4 is unstacked, block b4 is put down, block b5 is unstacked from top of block b2, on top of block b1, block b5 is stacked, block b7 is unstacked from block b6, block b7 is stacked on top of block b4, block b6 is picked up by the hand, on top of block b5, block b6 is stacked, block b2 is picked up, block b2 is stacked on top of block b6, block b3 is picked up, block b3 is stacked on top of block b2, block b7 is unstacked from block b4, block b7 is stacked on top of block b3 and block b4 is picked up by the hand to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 48? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is located on the table, block b7 is on top of block b6 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from block b7 and placed down, then block b5 is removed from block b4 and placed on top of block b2. Next, block b4 is removed from block b1 and placed down, and block b5 is removed from block b2 and placed on top of block b1. Block b7 is then removed from block b6 and placed on top of block b4. The hand picks up block b6 and places it on top of block b5. Block b2 is picked up and placed on top of block b6, followed by block b3 being picked up and placed on top of block b2. Block b7 is then removed from block b4 and placed on top of block b3. Finally, block b4 is picked up by the hand to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 48? True or False", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear of any blocks, block b2 is also on the table, block b3 is clear of any blocks, block b3 is stacked on top of block b7, block b4 is stacked on top of block b1, block b5 is clear of any blocks, block b5 is stacked on top of block b4, block b6 is on the table, block b7 is stacked on top of block b6, and the hand is empty."}
{"question_id": "e9073d29-fc59-4c6f-bd10-167298e1e0a3", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, block b3 is put down, from top of block b4, block b5 is unstacked, block b5 is stacked on top of block b2, block b4 is unstacked from top of block b1, block b4 is put down on the table, from top of block b2, block b5 is unstacked, on top of block b1, block b5 is stacked, block b7 is unstacked from top of block b6 and on top of block b4, block b7 is stacked to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 56? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is located on the table, block b7 is on top of block b6 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from the top of block b7 and placed down, then block b5 is removed from the top of block b4, and placed on top of block b2. Next, block b4 is removed from the top of block b1 and placed on the table. Block b5 is then removed from the top of block b2 and placed on top of block b1. Finally, block b7 is removed from the top of block b6 and placed on top of block b4, resulting in the current state. In this state, is the number of valid properties of the state that involve negations equal to 56? True or False", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear of any blocks, block b2 is also on the table, block b3 is clear of any blocks, block b3 is stacked on top of block b7, block b4 is stacked on top of block b1, block b5 is clear of any blocks, block b5 is stacked on top of block b4, block b6 is on the table, block b7 is stacked on top of block b6, and the hand is empty."}
{"question_id": "52e352fc-a8ae-465a-a1a3-6e9c5ba4c030", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 58? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is located on the table, block b7 is on top of block b6 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is unstacked from the top of block b7 to reach the current state. In this state, does the number of valid properties involving negations equal 58? True or False", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear of any blocks, block b2 is also on the table, block b3 is clear of any blocks, block b3 is stacked on top of block b7, block b4 is stacked on top of block b1, block b5 is clear of any blocks, block b5 is stacked on top of block b4, block b6 is on the table, block b7 is stacked on top of block b6, and the hand is empty."}
{"question_id": "61ee2db3-1730-49ed-bccf-c4338f3aba59", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked, block b9 is put down on the table, block b2 is unstacked from block b6, on top of block b5, block b2 is stacked, from top of block b3, block b6 is unstacked, the hand puts down the block b6, block b3 is unstacked from top of block b4, on top of block b9, block b3 is stacked, block b8 is unstacked from top of block b1 and on top of block b4, block b8 is stacked to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 85? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 is clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is placed on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following steps are taken: block b9 is removed from the top of block b8 and placed on the table, then block b2 is removed from block b6 and stacked on top of block b5. Next, block b6 is removed from the top of block b3 and put down, followed by block b3 being removed from the top of block b4 and stacked on top of block b9. Finally, block b8 is removed from the top of block b1 and stacked on top of block b4, resulting in the current state. In this state, is the number of valid properties of the state that involve negations equal to 85? True or False", "initial_state_nl_paraphrased": "Block b1 is placed on block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on top of block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is placed on top of block b1, block b9 has no blocks on it, block b9 is stacked on top of block b8, and the hand is empty."}
{"question_id": "a2b360cb-b915-4c70-8a8a-1460fbf44ab5", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b5, block b3 is put down on the table, from top of block b6, block b2 is unstacked, block b2 is put down, block b6 is picked up, on top of block b3, block b6 is stacked, from top of block b7, block b5 is unstacked, block b5 is stacked on top of block b6, block b7 is unstacked from block b1, block b7 is put down on the table, block b1 is unstacked from top of block b4, on top of block b2, block b1 is stacked, block b4 is picked up, block b4 is stacked on top of block b5, block b1 is unstacked from block b2, on top of block b4, block b1 is stacked, block b2 is picked up from the table, block b2 is stacked on top of block b1 and block b7 is picked up by the hand to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 58? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on top of block b1 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b5, then placed on the table, block b2 is unstacked from the top of block b6 and put down, block b6 is picked up and stacked on top of block b3, block b5 is unstacked from the top of block b7 and stacked on top of block b6, block b7 is unstacked from block b1 and placed on the table, block b1 is unstacked from the top of block b4 and stacked on top of block b2, block b4 is picked up and stacked on top of block b5, block b1 is unstacked from block b2 and stacked on top of block b4, block b2 is picked up from the table and stacked on top of block b1, and finally, block b7 is picked up by the hand to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 58? True or False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is placed on the table, block b5 is stacked on block b7, block b6 is placed on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "d35b883c-e402-43e1-b96a-c4dde3693ff8", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8, block b9 is put down on the table, from top of block b6, block b2 is unstacked, block b2 is stacked on top of block b5, block b6 is unstacked from top of block b3, block b6 is put down on the table, block b3 is unstacked from block b4, block b3 is stacked on top of block b9, block b8 is unstacked from block b1, block b8 is stacked on top of block b4, block b2 is unstacked from block b5, on top of block b8, block b2 is stacked, block b3 is unstacked from top of block b9, block b3 is stacked on top of block b2, block b1 is unstacked from top of block b7, on top of block b3, block b1 is stacked, block b7 is picked up from the table, on top of block b9, block b7 is stacked and block b6 is picked up by the hand to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 88? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 is clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is placed on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b9 is removed from block b8 and placed on the table, block b2 is removed from the top of block b6, then stacked on top of block b5, block b6 is removed from the top of block b3 and placed on the table, block b3 is removed from block b4 and stacked on top of block b9, block b8 is removed from block b1 and stacked on top of block b4, block b2 is removed from block b5 and stacked on top of block b8, block b3 is removed from the top of block b9 and stacked on top of block b2, block b1 is removed from the top of block b7 and stacked on top of block b3, block b7 is picked up from the table and stacked on top of block b9, and block b6 is picked up by the hand to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 88? True or False", "initial_state_nl_paraphrased": "Block b1 is placed on block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on top of block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is sitting on the table, block b6 is stacked on block b3, block b7 is situated on the table, block b8 is positioned on top of block b1, block b9 has no blocks on it, block b9 is placed on top of block b8, and the hand is empty."}
{"question_id": "12e3980f-b327-4051-8962-6139d24cf727", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 168? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 is clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is placed on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are taken: block b9 is removed from block b8 to achieve the current state. In this state, does the number of executable and inexecutable actions amount to 168? True or False", "initial_state_nl_paraphrased": "Block b1 is placed on block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on top of block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is placed on top of block b1, block b9 has no blocks on it, block b9 is stacked on top of block b8, and the hand is empty."}
{"question_id": "fb1cb240-74bd-472a-a99e-77e664fe81ec", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1, the hand puts down the block b4, from top of block b6, block b1 is unstacked, the hand puts down the block b1, from top of block b5, block b2 is unstacked, block b2 is stacked on top of block b4, block b6 is unstacked from block b7, block b6 is put down on the table, block b7 is picked up, on top of block b2, block b7 is stacked, from top of block b8, block b3 is unstacked, on top of block b7, block b3 is stacked, block b1 is picked up from the table, block b1 is stacked on top of block b3, block b5 is picked up from the table, block b5 is stacked on top of block b1, block b8 is picked up, on top of block b5, block b8 is stacked and block b6 is picked up to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "answer": "2", "plan_length": 19, "initial_state_nl": "Block b1 is on block b6, block b2 is clear, block b2 is on top of block b5, block b3 is clear, block b3 is on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b4 is removed from block b1, then placed on the table, block b1 is then unstacked from block b6 and put down, block b2 is unstacked from block b5, and then stacked on top of block b4. Next, block b6 is unstacked from block b7 and placed on the table, block b7 is picked up and stacked on top of block b2. Block b3 is then unstacked from block b8 and stacked on top of block b7. Block b1 is picked up from the table and stacked on top of block b3, followed by block b5 being picked up and stacked on top of block b1. Block b8 is then picked up and stacked on top of block b5, and finally, block b6 is picked up to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned above block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on top of block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is resting on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "f85a9592-935b-4891-90c0-d2ea59bb8192", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b3 is unstacked from top of block b7, block b3 is put down, from top of block b4, block b5 is unstacked, block b5 is stacked on top of block b2, block b5 is picked up, block b4 is put down on the table, block b5 is unstacked from block b2, block b5 is stacked on top of block b1, block b7 is unstacked from block b6 and on top of block b4, block b7 is stacked to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "answer": "4", "plan_length": 10, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is located on the table, block b7 is on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: unstack block b3 from block b7, place block b3 on the table, unstack block b5 from block b4, stack block b5 on block b2, pick up block b5, place block b4 on the table, unstack block b5 from block b2, stack block b5 on block b1, unstack block b7 from block b6 and stack it on block b4 to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear of any blocks, block b2 is also on the table, block b3 is clear of any blocks, block b3 is stacked on top of block b7, block b4 is stacked on top of block b1, block b5 is clear of any blocks, block b5 is stacked on top of block b4, block b6 is on the table, block b7 is stacked on top of block b6, and the hand is empty."}
{"question_id": "9b378672-13c1-4c81-8037-33ab25b4b1a0", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from top of block b1, block b4 is put down on the table, from top of block b6, block b1 is unstacked, block b1 is put down, block b2 is unstacked from block b5, block b2 is stacked on top of block b4, block b6 is unstacked from top of block b7, block b6 is put down, block b7 is picked up by the hand, on top of block b2, block b7 is stacked, block b3 is unstacked from block b8, on top of block b7, block b3 is stacked, block b1 is picked up, on top of block b3, block b1 is stacked, block b5 is picked up by the hand, on top of block b1, block b5 is stacked, block b8 is picked up from the table, block b8 is stacked on top of block b5 and block b6 is picked up from the table to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 83? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on block b6, block b2 is clear, block b2 is on top of block b5, block b3 is clear, block b3 is on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b4 is removed from the top of block b1 and placed on the table, then block b1 is unstacked from block b6 and put down, block b2 is unstacked from block b5, and stacked on top of block b4. Next, block b6 is unstacked from block b7 and put down, and block b7 is picked up and stacked on top of block b2. Block b3 is then unstacked from block b8 and stacked on top of block b7, followed by block b1 being picked up and stacked on top of block b3. Block b5 is then picked up and stacked on top of block b1, block b8 is picked up from the table and stacked on top of block b5, and finally, block b6 is picked up from the table to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 83? True or False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned above block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on top of block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is resting on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "ca481b24-f239-4559-9b3b-412fac3240a2", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b5 to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "answer": "3", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b5 to achieve the current state. In this state, what is the total number of actions that can be executed? Write as an integer. Write None if there are no executable actions.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is placed on the table, block b5 is stacked on block b7, block b6 is placed on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "c49d79ca-3db0-4bae-97c1-5c6c972a7048", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8, block b9 is put down, block b2 is unstacked from top of block b6, on top of block b5, block b2 is stacked, block b6 is unstacked from top of block b3, the hand puts down the block b6, block b3 is unstacked from top of block b4, block b3 is stacked on top of block b9, from top of block b1, block b8 is unstacked, block b8 is stacked on top of block b4, block b2 is unstacked from block b5, on top of block b8, block b2 is stacked, block b3 is unstacked from top of block b9, on top of block b2, block b3 is stacked, block b1 is unstacked from block b7, on top of block b3, block b1 is stacked, block b7 is picked up from the table, on top of block b9, block b7 is stacked and block b6 is picked up to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 12? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 is clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is placed on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are executed: block b9 is removed from the top of block b8 and placed down, block b2 is removed from the top of block b6 and placed on top of block b5, block b6 is removed from the top of block b3 and put down, block b3 is removed from the top of block b4 and stacked on top of block b9, block b8 is removed from the top of block b1 and stacked on top of block b4, block b2 is removed from block b5 and stacked on top of block b8, block b3 is removed from the top of block b9 and stacked on top of block b2, block b1 is removed from block b7 and stacked on top of block b3, block b7 is picked up from the table and stacked on top of block b9, and block b6 is picked up to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 12? True or False", "initial_state_nl_paraphrased": "Block b1 is placed on block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on top of block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is placed on top of block b1, block b9 has no blocks on it, block b9 is stacked on top of block b8, and the hand is empty."}
{"question_id": "0a46cf8c-7ffc-4c2a-8ff0-9cce73995096", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked, the hand puts down the block b3, block b2 is unstacked from block b6, block b2 is put down on the table, block b6 is picked up from the table, on top of block b3, block b6 is stacked, from top of block b7, block b5 is unstacked, block b5 is stacked on top of block b6, block b7 is unstacked from block b1 and the hand puts down the block b7 to reach the current state. In this state, is the number of executable actions equal to 4? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b5 and placed on the table, then block b2 is unstacked from block b6 and put down, after which block b6 is picked up and stacked on top of block b3. Next, block b5 is unstacked from block b7 and placed on top of block b6, and finally, block b7 is unstacked from block b1 and put down to reach the current state. In this state, is the number of executable actions equal to 4? True or False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is placed on the table, block b5 is stacked on block b7, block b6 is placed on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "ed8706d5-c954-4e39-8669-13e19953ba98", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, the hand puts down the block b3, from top of block b6, block b2 is unstacked, block b2 is put down, block b6 is picked up from the table, block b6 is stacked on top of block b3, block b5 is unstacked from top of block b7, on top of block b6, block b5 is stacked, block b7 is unstacked from block b1 and block b7 is put down to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 12? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from block b5, the hand releases block b3, block b2 is removed from the top of block b6, block b2 is put down, block b6 is picked up from the table, block b6 is placed on top of block b3, block b5 is removed from the top of block b7, block b5 is stacked on top of block b6, block b7 is removed from block b1 and block b7 is put down to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 12? True or False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is placed on the table, block b5 is stacked on block b7, block b6 is placed on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "d8bcb890-f33c-4a2b-9774-65a66a17d146", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b5, the hand puts down the block b3, from top of block b6, block b2 is unstacked, block b2 is put down on the table, block b6 is picked up by the hand, on top of block b3, block b6 is stacked, block b5 is unstacked from top of block b7, on top of block b6, block b5 is stacked, from top of block b1, block b7 is unstacked and block b7 is put down to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 51? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on top of block b1 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b5, then placed on the table by the hand, block b2 is unstacked from the top of block b6 and put down, block b6 is picked up by the hand and stacked on top of block b3, block b5 is unstacked from the top of block b7 and then stacked on top of block b6, and finally, block b7 is unstacked from the top of block b1 and placed on the table to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 51? True or False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is placed on the table, block b5 is stacked on block b7, block b6 is placed on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "a04c84e7-d219-4e12-8207-b2ec99168e7d", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b5, block b3 is put down, block b2 is unstacked from top of block b6, the hand puts down the block b2, block b6 is picked up, block b6 is stacked on top of block b3, block b5 is unstacked from block b7, on top of block b6, block b5 is stacked, block b7 is unstacked from block b1 and block b7 is put down on the table to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "answer": "4", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from the top of block b5, then placed down, block b2 is removed from the top of block b6, and the hand releases block b2, block b6 is picked up, and then placed on top of block b3, block b5 is removed from block b7 and stacked on top of block b6, block b7 is removed from block b1 and placed on the table to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is placed on the table, block b5 is stacked on block b7, block b6 is placed on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "6a236d28-cdd2-405d-b7e4-c7f322913058", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b1 is unstacked from block b2 to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "answer": "0", "plan_length": 1, "initial_state_nl": "Block b1 is on block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 is clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is placed on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are planned to be performed: block b1 is unstacked from block b2 to reach the current state. What is the number of actions preceding the first inexecutable action? Provide the answer as an integer, or None if there are no such actions.", "initial_state_nl_paraphrased": "Block b1 is placed on block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on top of block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is placed on top of block b1, block b9 has no blocks on it, block b9 is positioned on top of block b8, and the hand is empty."}
{"question_id": "136402b3-05db-4db0-b864-e8b69bb7067b", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8 to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "answer": "4", "plan_length": 1, "initial_state_nl": "Block b1 is on block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 is clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is placed on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are taken: block b9 is removed from the top of block b8 to achieve the current state. In this state, what is the total number of actions that can be executed? Write as an integer. Write None if there are no executable actions.", "initial_state_nl_paraphrased": "Block b1 is placed on block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on top of block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is placed on top of block b1, block b9 has no blocks on it, block b9 is stacked on top of block b8, and the hand is empty."}
{"question_id": "6015a97b-397b-4bda-a5e6-ed5b39f3abc0", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1, block b4 is put down on the table, block b1 is unstacked from top of block b6, block b1 is put down, from top of block b5, block b2 is unstacked, on top of block b4, block b2 is stacked, block b6 is unstacked from top of block b7, block b6 is put down on the table, block b7 is picked up by the hand and block b7 is stacked on top of block b2 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 15? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on block b6, block b2 is clear, block b2 is on top of block b5, block b3 is clear, block b3 is on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following steps are taken: block b4 is removed from block b1, then placed on the table, block b1 is then removed from block b6 and placed down, block b2 is removed from block b5, and then stacked on top of block b4, block b6 is removed from block b7 and placed on the table, block b7 is picked up and stacked on top of block b2, resulting in the current state. In this state, does the number of valid properties of the state that do not involve negations equal 15? True or False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned above block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on top of block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is resting on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "b7a7d92f-5fb6-4887-8719-afaa8d914af8", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, block b3 is put down, block b5 is unstacked from top of block b4, block b5 is stacked on top of block b2, block b4 is unstacked from top of block b1, block b4 is put down on the table, from top of block b2, block b5 is unstacked, on top of block b1, block b5 is stacked, block b7 is unstacked from block b6 and on top of block b4, block b7 is stacked to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 14? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is located on the table, block b7 is on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from the top of block b7 and placed down, block b5 is removed from the top of block b4, then stacked on top of block b2, block b4 is removed from the top of block b1 and placed on the table, block b5 is then removed from the top of block b2 and stacked on top of block b1, and finally, block b7 is removed from block b6 and stacked on top of block b4 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 14? True or False", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear of any blocks, block b2 is also on the table, block b3 is clear of any blocks, block b3 is stacked on top of block b7, block b4 is stacked on top of block b1, block b5 is clear of any blocks, block b5 is stacked on top of block b4, block b6 is on the table, block b7 is stacked on top of block b6, and the hand is empty."}
{"question_id": "8d26536f-ca5c-42a0-87e9-16eec5be9b2a", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked, block b9 is put down on the table, from top of block b6, block b2 is unstacked, block b2 is stacked on top of block b5, block b6 is unstacked from top of block b3, block b6 is put down, from top of block b4, block b3 is unstacked, on top of block b9, block b3 is stacked, block b8 is unstacked from block b1, on top of block b4, block b8 is stacked, block b2 is unstacked from top of block b5, on top of block b8, block b2 is stacked, from top of block b9, block b3 is unstacked, block b3 is stacked on top of block b2, block b1 is unstacked from block b7, on top of block b3, block b1 is stacked, block b7 is picked up, block b7 is stacked on top of block b9 and block b6 is picked up to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 106? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 is clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is placed on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: block b9 is removed from the top of block b8 and placed on the table, then block b2 is removed from the top of block b6 and stacked on top of block b5. Next, block b6 is removed from the top of block b3 and put down. Block b3 is then removed from the top of block b4 and stacked on top of block b9. Block b8 is removed from block b1 and stacked on top of block b4. Block b2 is then removed from the top of block b5 and stacked on top of block b8. Block b3 is removed from the top of block b9 and stacked on top of block b2. Block b1 is removed from block b7 and stacked on top of block b3. Block b7 is picked up and stacked on top of block b9, and block b6 is picked up to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 106? True or False", "initial_state_nl_paraphrased": "Block b1 is placed on block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on top of block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is placed on top of block b1, block b9 has no blocks on it, block b9 is stacked on top of block b8, and the hand is empty."}
{"question_id": "93168bd8-7fef-48b4-a47b-fc4495a249d7", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8, block b9 is put down, block b2 is unstacked from top of block b6, block b2 is stacked on top of block b5, block b6 is unstacked from top of block b3, block b6 is put down on the table, block b3 is unstacked from block b4, on top of block b9, block b3 is stacked, block b8 is unstacked from top of block b1 and on top of block b4, block b8 is stacked to reach the current state. In this state, is the number of inexecutable actions equal to 157? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 is clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is placed on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following steps are taken: block b9 is removed from the top of block b8, then placed down, block b2 is removed from the top of block b6, then stacked on top of block b5, block b6 is removed from the top of block b3 and placed on the table, block b3 is removed from block b4 and stacked on top of block b9, and block b8 is removed from the top of block b1 and stacked on top of block b4, resulting in the current state. In this state, is the number of inexecutable actions equal to 157? True or False", "initial_state_nl_paraphrased": "Block b1 is placed on block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on top of block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is placed on top of block b1, block b9 has no blocks on it, block b9 is stacked on top of block b8, and the hand is empty."}
{"question_id": "17079317-7bc7-4bdb-ad70-e38482414be7", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked, block b9 is put down on the table, block b2 is unstacked from block b6, on top of block b5, block b2 is stacked, from top of block b3, block b6 is unstacked, block b6 is put down on the table, from top of block b4, block b3 is unstacked, on top of block b9, block b3 is stacked, block b8 is unstacked from block b1 and block b8 is stacked on top of block b4 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 15? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 is clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is placed on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: block b9 is removed from the top of block b8 and placed on the table, then block b2 is removed from block b6 and stacked on top of block b5. Next, block b6 is removed from the top of block b3 and placed on the table. Block b3 is then removed from the top of block b4 and stacked on top of block b9. Finally, block b8 is removed from block b1 and stacked on top of block b4, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 15? True or False", "initial_state_nl_paraphrased": "Block b1 is placed on block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on top of block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is stacked on top of block b1, block b9 has no blocks on it, block b9 is placed on top of block b8, and the hand is empty."}
{"question_id": "d2adf2a9-79c0-4622-b6f0-62838f0a21dc", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_12", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked, the hand puts down the block b3, block b2 is unstacked from top of block b6, block b2 is put down, block b6 is picked up by the hand, block b6 is stacked on top of block b3, block b5 is unstacked from block b7, on top of block b6, block b5 is stacked, block b7 is unstacked from top of block b1, the hand puts down the block b7, block b1 is unstacked from block b4, block b1 is stacked on top of block b2, block b4 is picked up by the hand, block b4 is stacked on top of block b5, from top of block b2, block b1 is unstacked, on top of block b4, block b1 is stacked, block b2 is picked up by the hand, block b2 is stacked on top of block b1 and block b7 is picked up by the hand to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 52? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b5 and placed down, then block b2 is removed from the top of block b6 and put down. Next, block b6 is picked up and stacked on top of block b3. Block b5 is then unstacked from block b7 and placed on top of block b6. Block b7 is removed from the top of block b1 and put down, followed by block b1 being unstacked from block b4 and stacked on top of block b2. Block b4 is then picked up and stacked on top of block b5. Block b1 is removed from the top of block b2 and stacked on top of block b4. Block b2 is picked up and stacked on top of block b1, and finally, block b7 is picked up to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 52? True or False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is placed on the table, block b5 is stacked on block b7, block b6 is placed on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "66be77d3-6d2a-4179-a28c-12114d2cfcd5", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b7, block b3 is unstacked, block b3 is put down on the table, block b5 is unstacked from block b4, block b5 is stacked on top of block b2, block b4 is unstacked from block b1, block b4 is put down, block b5 is unstacked from top of block b2, block b5 is stacked on top of block b1, block b7 is unstacked from block b6, on top of block b4, block b7 is stacked, block b6 is picked up by the hand, block b6 is stacked on top of block b5, block b2 is picked up by the hand, block b2 is stacked on top of block b6, block b3 is picked up, on top of block b2, block b3 is stacked, from top of block b4, block b7 is unstacked, on top of block b3, block b7 is stacked and block b4 is picked up from the table to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "96", "plan_length": 19, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is located on the table, block b7 is on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b7 and placed on the table, then block b5 is removed from block b4 and stacked on top of block b2. Next, block b4 is removed from block b1 and placed on the table. Block b5 is then removed from the top of block b2 and stacked on top of block b1. Block b7 is removed from block b6 and stacked on top of block b4. Block b6 is picked up and stacked on top of block b5, followed by block b2 being picked up and stacked on top of block b6. Block b3 is then picked up and stacked on top of block b2. Block b7 is removed from the top of block b4 and stacked on top of block b3. Finally, block b4 is picked up from the table to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear of any blocks, block b2 is also on the table, block b3 is clear of any blocks, block b3 is stacked on top of block b7, block b4 is stacked on top of block b1, block b5 is clear of any blocks, block b5 is stacked on top of block b4, block b6 is on the table, block b7 is stacked on top of block b6, and the hand is empty."}
{"question_id": "bed0e924-0583-4ed6-bd0f-20f92cb621e9", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 101? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 is clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is placed on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: block b9 is removed from the top of block b8 to achieve the current state. In this state, does the number of valid properties of the state involving negations equal 101? True or False", "initial_state_nl_paraphrased": "Block b1 is positioned on block b7, block b2 has no blocks on it, block b2 is placed on block b6, block b3 is stacked on top of block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is sitting on the table, block b6 is positioned on block b3, block b7 is situated on the table, block b8 is stacked on top of block b1, block b9 has no blocks on it, block b9 is placed on top of block b8, and the hand is empty."}
{"question_id": "70f88bb6-5160-4cb8-b524-55ab7f58e4f6", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8, block b9 is put down, block b2 is unstacked from block b6, block b2 is stacked on top of block b5, from top of block b3, block b6 is unstacked, block b6 is put down, from top of block b4, block b3 is unstacked, on top of block b9, block b3 is stacked, block b8 is unstacked from block b1 and block b8 is stacked on top of block b4 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 19? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 is clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is placed on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following steps are taken: block b9 is removed from the top of block b8 and placed down, block b2 is removed from block b6 and placed on top of block b5, block b6 is then removed from the top of block b3 and placed down, block b3 is removed from the top of block b4 and stacked on top of block b9, and finally, block b8 is removed from block b1 and stacked on top of block b4 to achieve the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 19? True or False", "initial_state_nl_paraphrased": "Block b1 is placed on block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on top of block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is placed on top of block b1, block b9 has no blocks on it, block b9 is positioned on top of block b8, and the hand is empty."}
{"question_id": "145765c5-db13-4155-9aec-3a35aadc87e0", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1, block b4 is put down, block b1 is unstacked from top of block b6, the hand puts down the block b1, block b2 is unstacked from top of block b5, on top of block b4, block b2 is stacked, from top of block b7, block b6 is unstacked, block b6 is put down, block b7 is picked up by the hand, on top of block b2, block b7 is stacked, from top of block b8, block b3 is unstacked, on top of block b7, block b3 is stacked, block b1 is picked up from the table, block b1 is stacked on top of block b3, block b5 is picked up from the table, on top of block b1, block b5 is stacked, block b8 is picked up by the hand, on top of block b5, block b8 is stacked and block b6 is picked up to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "126", "plan_length": 19, "initial_state_nl": "Block b1 is on block b6, block b2 is clear, block b2 is on top of block b5, block b3 is clear, block b3 is on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b4 is removed from block b1 and placed down, then block b1 is unstacked from block b6 and put down. Next, block b2 is unstacked from block b5 and stacked on top of block b4. Block b6 is then unstacked from block b7, placed down, and block b7 is picked up and stacked on top of block b2. Block b3 is unstacked from block b8 and stacked on top of block b7. Block b1 is then picked up from the table and stacked on top of block b3, followed by block b5 being picked up and stacked on top of block b1. Block b8 is then picked up and stacked on top of block b5, and finally, block b6 is picked up to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is placed on block b6, block b2 has no blocks on it, block b2 is stacked on block b5, block b3 has no blocks on it, block b3 is positioned on top of block b8, block b4 has no blocks on it, block b4 is positioned on top of block b1, block b5 is sitting on the table, block b6 is stacked on block b7, block b7 is resting on the table, block b8 is resting on the table, and the hand is empty."}
{"question_id": "3ec6bb74-2df6-49c5-9972-d2ba16669caa", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked, block b4 is put down on the table, block b1 is unstacked from top of block b6, block b1 is put down on the table, from top of block b5, block b2 is unstacked, block b2 is stacked on top of block b4, from top of block b7, block b6 is unstacked, block b6 is put down on the table, block b7 is picked up and on top of block b2, block b7 is stacked to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "123", "plan_length": 10, "initial_state_nl": "Block b1 is on block b6, block b2 is clear, block b2 is on top of block b5, block b3 is clear, block b3 is on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are executed: block b4 is removed from the top of block b1 and placed on the table, then block b1 is removed from the top of block b6 and placed on the table. Next, block b2 is removed from the top of block b5 and stacked on top of block b4. After that, block b6 is removed from the top of block b7 and placed on the table, and block b7 is picked up and stacked on top of block b2 to reach the current state. In this state, what is the total number of actions that cannot be executed? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned above block b5, block b3 has no blocks on it, block b3 is placed above block b8, block b4 has no blocks on it, block b4 is stacked on top of block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is resting on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "08cb35d3-a0ff-4657-8b5f-a274c57ffe65", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8, block b9 is put down on the table, block b2 is unstacked from top of block b6, block b2 is stacked on top of block b5, from top of block b3, block b6 is unstacked, block b6 is put down on the table, block b3 is unstacked from block b4, block b3 is stacked on top of block b9, block b8 is unstacked from block b1 and block b8 is stacked on top of block b4 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "answer": "100", "plan_length": 10, "initial_state_nl": "Block b1 is on block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 is clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is placed on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are executed: block b9 is removed from block b8, block b9 is placed on the table, block b2 is removed from the top of block b6, block b2 is placed on top of block b5, block b6 is removed from the top of block b3, block b6 is placed on the table, block b3 is removed from block b4, block b3 is placed on top of block b9, block b8 is removed from block b1 and block b8 is placed on top of block b4 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is placed on block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on top of block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is placed on top of block b1, block b9 has no blocks on it, block b9 is stacked on top of block b8, and the hand is empty."}
{"question_id": "07150742-dd9e-4434-a820-64119673a34d", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b5, block b3 is put down on the table, block b2 is unstacked from top of block b6, the hand puts down the block b2, block b6 is picked up from the table, block b6 is stacked on top of block b3, from top of block b7, block b5 is unstacked, block b5 is stacked on top of block b6, block b7 is unstacked from top of block b1, block b7 is put down on the table, block b1 is unstacked from top of block b4, block b1 is stacked on top of block b2, block b4 is picked up by the hand, block b4 is stacked on top of block b5, from top of block b2, block b1 is unstacked, block b1 is stacked on top of block b4, block b2 is picked up, on top of block b1, block b2 is stacked and block b7 is picked up by the hand to reach the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "answer": "56", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b5, then placed on the table, block b2 is removed from the top of block b6, and the hand releases block b2, block b6 is picked up from the table and placed on top of block b3, block b5 is removed from the top of block b7 and stacked on top of block b6, block b7 is removed from the top of block b1 and placed on the table, block b1 is removed from the top of block b4 and stacked on top of block b2, block b4 is picked up by the hand and stacked on top of block b5, block b1 is removed from the top of block b2 and stacked on top of block b4, block b2 is picked up and stacked on top of block b1, and finally, block b7 is picked up by the hand to reach the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is placed on the table, block b5 is stacked on block b7, block b6 is placed on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "3e32951a-6e1b-4e74-a200-8f9cdd2be788", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked, the hand puts down the block b4, from top of block b6, block b1 is unstacked, block b1 is put down, from top of block b5, block b2 is unstacked, block b2 is stacked on top of block b4, block b6 is unstacked from block b7, the hand puts down the block b6, block b7 is picked up by the hand, block b7 is stacked on top of block b2, block b3 is unstacked from block b8, on top of block b7, block b3 is stacked, block b1 is picked up, block b1 is stacked on top of block b3, block b5 is picked up, block b5 is stacked on top of block b1, block b8 is picked up by the hand, on top of block b5, block b8 is stacked and block b6 is picked up from the table to reach the current state. In this state, is the number of executable actions equal to 2? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is on block b6, block b2 is clear, block b2 is on top of block b5, block b3 is clear, block b3 is on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b4 is removed from the top of block b1 and placed down, then block b1 is unstacked from block b6 and put down. Next, block b2 is unstacked from block b5 and stacked on top of block b4. Block b6 is then unstacked from block b7 and placed down, after which block b7 is picked up and stacked on top of block b2. Block b3 is unstacked from block b8 and stacked on top of block b7. Block b1 is then picked up and stacked on top of block b3, followed by block b5 being picked up and stacked on top of block b1. Block b8 is then picked up and stacked on top of block b5, and finally, block b6 is picked up from the table to reach the current state. In this state, is the number of executable actions equal to 2? True or False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned above block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on top of block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is resting on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "ad38936d-228a-4fae-ae2d-666b7b676dbc", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_11", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b5, the hand puts down the block b3, block b2 is unstacked from top of block b6, block b2 is put down, block b6 is picked up from the table, block b6 is stacked on top of block b3, block b5 is unstacked from block b7, on top of block b6, block b5 is stacked, block b7 is unstacked from block b1, block b7 is put down on the table, block b1 is unstacked from top of block b4, on top of block b2, block b1 is stacked, block b4 is picked up, block b4 is stacked on top of block b5, block b1 is unstacked from top of block b2, on top of block b4, block b1 is stacked, block b2 is picked up by the hand, block b2 is stacked on top of block b1 and block b7 is picked up to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 64? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b5, then placed on the table, block b2 is removed from the top of block b6 and put down, block b6 is picked up from the table and stacked on top of block b3, block b5 is removed from block b7 and stacked on top of block b6, block b7 is removed from block b1 and placed on the table, block b1 is removed from the top of block b4 and stacked on top of block b2, block b4 is picked up and stacked on top of block b5, block b1 is then removed from the top of block b2 and stacked on top of block b4, block b2 is picked up by the hand and stacked on top of block b1, and finally, block b7 is picked up to achieve the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 64? True or False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is placed on the table, block b5 is stacked on block b7, block b6 is placed on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "2ab143fc-3360-4cda-9fd6-2980dd481434", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 11? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is unstacked from the top of block b5 to reach the current state. In this state, does the number of valid properties that do not involve negations equal 11? True or False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is placed on the table, block b5 is stacked on block b7, block b6 is placed on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "6daac9b5-a0bf-4a5f-acbe-5c9a85ed0cfd", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from top of block b8, block b9 is unstacked, the hand puts down the block b9, block b2 is unstacked from top of block b6, block b2 is stacked on top of block b5, block b6 is unstacked from block b3, block b6 is put down on the table, block b3 is unstacked from top of block b4, on top of block b9, block b3 is stacked, block b8 is unstacked from top of block b1, block b8 is stacked on top of block b4, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b8, block b3 is unstacked from block b9, block b3 is stacked on top of block b2, block b1 is unstacked from block b7, block b1 is stacked on top of block b3, block b7 is picked up by the hand, on top of block b9, block b7 is stacked and block b6 is picked up from the table to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "158", "plan_length": 19, "initial_state_nl": "Block b1 is on block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 is clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is placed on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are executed: block b9 is removed from the top of block b8 and placed by the hand, block b2 is unstacked from the top of block b6, then stacked on top of block b5, block b6 is unstacked from block b3 and placed on the table, block b3 is unstacked from the top of block b4 and stacked on top of block b9, block b8 is unstacked from the top of block b1 and stacked on top of block b4, block b2 is unstacked from the top of block b5 and stacked on top of block b8, block b3 is unstacked from block b9 and stacked on top of block b2, block b1 is unstacked from block b7 and stacked on top of block b3, block b7 is picked up by the hand and stacked on top of block b9, and block b6 is picked up from the table to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is positioned on block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is placed on top of block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is sitting on the table, block b6 is stacked on block b3, block b7 is situated on the table, block b8 is positioned on top of block b1, block b9 has no blocks on it, block b9 is placed on top of block b8, and the hand is empty."}
{"question_id": "90088c50-039c-4443-9c83-fec4f6f33dd1", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_12", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8 to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 105? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 is clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is placed on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: block b9 is removed from the top of block b8 to achieve the current state. In this state, does the total number of valid properties (including both affirmative and negated properties) equal 105? True or False", "initial_state_nl_paraphrased": "Block b1 is positioned on block b7, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 is placed on top of block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is sitting on the table, block b6 is stacked on block b3, block b7 is situated on the table, block b8 is positioned on top of block b1, block b9 has no blocks on it, block b9 is placed on top of block b8, and the hand is empty."}
{"question_id": "b2476362-9e92-4426-bf4b-507a1684e295", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b7, block b3 is put down, block b5 is unstacked from top of block b4, block b5 is stacked on top of block b2, from top of block b1, block b4 is unstacked, the hand puts down the block b4, from top of block b2, block b5 is unstacked, on top of block b1, block b5 is stacked, block b7 is unstacked from block b6 and on top of block b4, block b7 is stacked to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "93", "plan_length": 10, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is located on the table, block b7 is on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are executed: block b3 is removed from block b7 and placed down, block b5 is removed from the top of block b4 and placed on top of block b2, block b4 is removed from the top of block b1 and put down, block b5 is then removed from the top of block b2 and placed on top of block b1, and finally, block b7 is removed from block b6 and placed on top of block b4 to reach the current state. In this state, what is the total number of actions that cannot be executed? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear of any blocks, block b2 is also on the table, block b3 is clear of any blocks, block b3 is stacked on top of block b7, block b4 is stacked on top of block b1, block b5 is clear of any blocks, block b5 is stacked on top of block b4, block b6 is on the table, block b7 is stacked on top of block b6, and the hand is empty."}
{"question_id": "c7f37c11-fe5f-4e19-aee0-5eb6d2414293", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "answer": "64", "plan_length": 1, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is located on the table, block b7 is on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: block b3 is removed from the top of block b7 to achieve the current state. In this state, what is the total count of valid properties (including both affirmative and negated properties)? Provide the answer as an integer, or write None if there are no valid properties.", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear of any blocks, block b2 is also on the table, block b3 is clear of any blocks, block b3 is stacked on top of block b7, block b4 is stacked on top of block b1, block b5 is clear of any blocks, block b5 is stacked on top of block b4, block b6 is on the table, block b7 is stacked on top of block b6, and the hand is empty."}
{"question_id": "1d55948d-a4a9-4fd8-94eb-96f0cdcb122d", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, block b3 is put down, block b2 is unstacked from block b6, the hand puts down the block b2, block b6 is picked up, on top of block b3, block b6 is stacked, from top of block b7, block b5 is unstacked, block b5 is stacked on top of block b6, block b7 is unstacked from block b1, block b7 is put down, block b1 is unstacked from top of block b4, block b1 is stacked on top of block b2, block b4 is picked up from the table, block b4 is stacked on top of block b5, from top of block b2, block b1 is unstacked, block b1 is stacked on top of block b4, block b2 is picked up, on top of block b1, block b2 is stacked and block b7 is picked up to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 56? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on top of block b1 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from block b5, then placed down, block b2 is removed from block b6, and the hand releases block b2, block b6 is picked up and placed on top of block b3, block b5 is removed from the top of block b7 and stacked on top of block b6, block b7 is removed from block b1 and placed down, block b1 is removed from the top of block b4 and stacked on top of block b2, block b4 is picked up from the table and stacked on top of block b5, block b1 is removed from the top of block b2 and stacked on top of block b4, block b2 is picked up and stacked on top of block b1, and finally, block b7 is picked up to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 56? True or False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is placed on the table, block b5 is stacked on block b7, block b6 is placed on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "799d2bbb-67f0-4506-925f-b1a00abdf54e", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 12? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is located on the table, block b7 is on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b7 to achieve the current state. In this state, does the number of valid properties of the state that do not involve negations equal 12? True or False", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear of any blocks, block b2 is also on the table, block b3 is clear of any blocks, block b3 is stacked on top of block b7, block b4 is stacked on top of block b1, block b5 is clear of any blocks, block b5 is stacked on top of block b4, block b6 is on the table, block b7 is stacked on top of block b6, and the hand is empty."}
{"question_id": "c0466634-fd7b-4f0e-a97e-fe767c120467", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b5, block b3 is put down on the table, block b2 is unstacked from top of block b6, the hand puts down the block b2, block b6 is picked up, on top of block b3, block b6 is stacked, block b5 is unstacked from block b7, on top of block b6, block b5 is stacked, block b7 is unstacked from top of block b1 and block b7 is put down to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "98", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are taken: block b3 is removed from the top of block b5, block b3 is placed on the table, block b2 is removed from the top of block b6, the hand releases block b2, block b6 is picked up and placed on top of block b3, block b5 is removed from block b7 and placed on top of block b6, and block b7 is removed from the top of block b1 and put down to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is placed on the table, block b5 is stacked on block b7, block b6 is placed on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "bf8c67c6-d169-42be-a7f3-247078def6e4", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b1, block b4 is unstacked to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 69? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Block b1 is on block b6, block b2 is clear, block b2 is on top of block b5, block b3 is clear, block b3 is on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: block b4 is unstacked from the top of block b1 to reach the current state. In this state, does the number of valid properties involving negations equal 69? True or False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned above block b5, block b3 has no blocks on it, block b3 is placed above block b8, block b4 has no blocks on it, block b4 is stacked on top of block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is resting on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "8531c36c-fbda-4ddb-858d-007daf21142d", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_12", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from top of block b8, the hand puts down the block b9, block b2 is unstacked from block b6, on top of block b5, block b2 is stacked, from top of block b3, block b6 is unstacked, block b6 is put down on the table, block b3 is unstacked from block b4, on top of block b9, block b3 is stacked, block b8 is unstacked from block b1, on top of block b4, block b8 is stacked, from top of block b5, block b2 is unstacked, block b2 is stacked on top of block b8, block b3 is unstacked from top of block b9, on top of block b2, block b3 is stacked, from top of block b7, block b1 is unstacked, on top of block b3, block b1 is stacked, block b7 is picked up by the hand, block b7 is stacked on top of block b9 and block b6 is picked up by the hand to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 121? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is on block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 is clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is placed on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b9 is removed from the top of block b8, then placed on the table by the hand, block b2 is unstacked from block b6 and placed on top of block b5, block b6 is then unstacked from block b3 and put down on the table, block b3 is unstacked from block b4 and stacked on top of block b9, block b8 is unstacked from block b1 and stacked on top of block b4, block b2 is then unstacked from block b5 and stacked on top of block b8, block b3 is unstacked from block b9 and stacked on top of block b2, block b1 is unstacked from block b7 and stacked on top of block b3, block b7 is picked up by the hand and stacked on top of block b9, and finally block b6 is picked up by the hand to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 121? True or False", "initial_state_nl_paraphrased": "Block b1 is placed on block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on top of block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is stacked on top of block b1, block b9 has no blocks on it, block b9 is placed on top of block b8, and the hand is empty."}
{"question_id": "d4f0e5a3-beeb-4133-b7f6-97cb1418e6ec", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, block b3 is put down on the table, block b2 is unstacked from block b6, the hand puts down the block b2, block b6 is picked up by the hand, block b6 is stacked on top of block b3, block b5 is unstacked from block b7, on top of block b6, block b5 is stacked, from top of block b1, block b7 is unstacked and block b7 is put down to reach the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "answer": "52", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from block b5, block b3 is placed on the table, block b2 is removed from block b6, the hand releases block b2, the hand picks up block b6, block b6 is placed on top of block b3, block b5 is removed from block b7 and then stacked on top of block b6, block b7 is removed from block b1 and placed on the table to reach the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is placed on the table, block b5 is stacked on block b7, block b6 is placed on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "25f6028d-4e10-4cef-a28b-b13b270d30d1", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "162", "plan_length": 1, "initial_state_nl": "Block b1 is on block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 is clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is placed on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are taken: block b9 is unstacked from block b8 to achieve the current state. In this state, what is the total count of actions that can be executed and those that cannot be executed? Provide the answer as an integer, or write None if there are no such actions.", "initial_state_nl_paraphrased": "Block b1 is placed on block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on top of block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is placed on top of block b1, block b9 has no blocks on it, block b9 is stacked on top of block b8, and the hand is empty."}
{"question_id": "9e7509e5-d2e6-42bd-86e4-533ee647a812", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from top of block b5, block b3 is unstacked, the hand puts down the block b3, block b2 is unstacked from block b6, block b2 is put down, block b6 is picked up by the hand, block b6 is stacked on top of block b3, block b5 is unstacked from block b7, block b5 is stacked on top of block b6, from top of block b1, block b7 is unstacked, block b7 is put down on the table, block b1 is unstacked from top of block b4, block b1 is stacked on top of block b2, block b4 is picked up by the hand, on top of block b5, block b4 is stacked, block b1 is unstacked from block b2, on top of block b4, block b1 is stacked, block b2 is picked up, block b2 is stacked on top of block b1 and block b7 is picked up to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 9? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b5 and placed down, then block b2 is unstacked from block b6 and put down, after which block b6 is picked up and stacked on top of block b3. Next, block b5 is unstacked from block b7 and stacked on top of block b6. Block b7 is then unstacked from the top of block b1 and placed on the table. Block b1 is unstacked from the top of block b4 and stacked on top of block b2. Block b4 is picked up and stacked on top of block b5. Block b1 is then unstacked from block b2 and stacked on top of block b4. Block b2 is picked up and stacked on top of block b1, and finally, block b7 is picked up to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 9? True or False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is placed on the table, block b5 is stacked on block b7, block b6 is placed on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "023c9fdc-ca07-44cb-bd8b-95429ca1157d", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1, block b4 is put down, from top of block b6, block b1 is unstacked, block b1 is put down, block b2 is unstacked from top of block b5, block b2 is stacked on top of block b4, from top of block b7, block b6 is unstacked, block b6 is put down, block b7 is picked up from the table and block b7 is stacked on top of block b2 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 14? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is on block b6, block b2 is clear, block b2 is on top of block b5, block b3 is clear, block b3 is on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following steps are taken: block b4 is removed from block b1 and placed down, then block b1 is removed from block b6 and placed down. Next, block b2 is removed from block b5 and stacked on top of block b4. Block b6 is then removed from block b7 and placed down. Finally, block b7 is picked up from the table and stacked on top of block b2, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 14? True or False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned above block b5, block b3 has no blocks on it, block b3 is placed above block b8, block b4 has no blocks on it, block b4 is stacked on top of block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is resting on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "7b2936c5-548c-45de-854e-875eb14e2fef", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from block b5, block b3 is put down on the table, block b2 is unstacked from top of block b6, the hand puts down the block b2, block b6 is picked up by the hand, on top of block b3, block b6 is stacked, from top of block b7, block b5 is unstacked, on top of block b6, block b5 is stacked, from top of block b1, block b7 is unstacked and block b7 is put down to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 52? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on top of block b1 and hand is not holding anything.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following steps are taken: block b3 is removed from block b5, block b3 is placed on the table, block b2 is removed from the top of block b6, the hand places block b2 down, the hand picks up block b6 and places it on top of block b3, block b5 is removed from the top of block b7 and placed on top of block b6, block b7 is removed from the top of block b1 and put down to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 52? True or False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is placed on the table, block b5 is stacked on block b7, block b6 is placed on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "e050743f-c694-4585-86e6-4fa996fb6fdc", "domain_name": "blocksworld", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b4 is unstacked from block b1, block b4 is put down, block b1 is unstacked from block b6, block b1 is put down on the table, block b2 is unstacked from block b5, block b2 is stacked on top of block b4, block b6 is unstacked from block b7, block b6 is put down on the table, block b7 is picked up by the hand and on top of block b2, block b7 is stacked to reach the current state. In this state, is the number of executable actions equal to 6? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Block b1 is on block b6, block b2 is clear, block b2 is on top of block b5, block b3 is clear, block b3 is on top of block b8, block b4 is clear, block b4 is placed on top of block b1, block b5 is located on the table, block b6 is on block b7, block b7 is located on the table, block b8 is located on the table and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following steps are taken: block b4 is removed from block b1, then placed on the table, block b1 is then removed from block b6 and placed on the table, block b2 is removed from block b5, then stacked on top of block b4, block b6 is removed from block b7 and placed on the table, block b7 is picked up and stacked on top of block b2 to reach the current state. In this state, is the number of executable actions equal to 6? True or False", "initial_state_nl_paraphrased": "Block b1 is stacked on block b6, block b2 has no blocks on it, block b2 is positioned above block b5, block b3 has no blocks on it, block b3 is placed on top of block b8, block b4 has no blocks on it, block b4 is stacked on top of block b1, block b5 is sitting on the table, block b6 is on top of block b7, block b7 is resting on the table, block b8 is also on the table, and the hand is empty."}
{"question_id": "ab025c39-4298-442f-8a65-8f0c8c08dfdd", "domain_name": "blocksworld", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: block b9 is unstacked from block b8 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 12? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Block b1 is on block b7, block b2 is clear, block b2 is on block b6, block b3 is on top of block b4, block b4 is on the table, block b5 is clear, block b5 is on the table, block b6 is on block b3, block b7 is located on the table, block b8 is placed on top of block b1, block b9 is clear, block b9 is placed on top of block b8 and hand is not holding anything.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: block b9 is removed from block b8 to achieve the current state. In this state, does the number of valid properties that do not include negations equal 12? True or False", "initial_state_nl_paraphrased": "Block b1 is placed on block b7, block b2 has no blocks on it, block b2 is positioned on block b6, block b3 is stacked on top of block b4, block b4 is resting on the table, block b5 has no blocks on it, block b5 is situated on the table, block b6 is stacked on block b3, block b7 is positioned on the table, block b8 is placed on top of block b1, block b9 has no blocks on it, block b9 is stacked on top of block b8, and the hand is empty."}
{"question_id": "b345cf2e-cf9f-4007-8b0c-5fa23d02a1f1", "domain_name": "blocksworld", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b1 is picked up by the hand to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "answer": "0", "plan_length": 1, "initial_state_nl": "Block b1 is placed on top of block b4, block b2 is clear, block b2 is on top of block b6, block b3 is clear, block b3 is on top of block b5, block b4 is on the table, block b5 is on top of block b7, block b6 is on the table, block b7 is on top of block b1 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are planned to be performed: block b1 is picked up by the hand to reach the current state. What is the number of actions preceding the first inexecutable action? Provide the answer as an integer, or None if no such actions exist.", "initial_state_nl_paraphrased": "Block b1 is stacked on block b4, block b2 has no blocks on it, block b2 is stacked on block b6, block b3 has no blocks on it, block b3 is stacked on block b5, block b4 is placed on the table, block b5 is stacked on block b7, block b6 is placed on the table, block b7 is stacked on block b1 and the hand is empty."}
{"question_id": "ba56ce6a-082d-440d-8518-e45b7093ad51", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: block b3 is unstacked from top of block b7, block b3 is put down on the table, from top of block b4, block b5 is unstacked, on top of block b2, block b5 is stacked, block b4 is unstacked from top of block b1, block b4 is put down, from top of block b2, block b5 is unstacked, block b5 is stacked on top of block b1, from top of block b6, block b7 is unstacked, on top of block b4, block b7 is stacked, block b6 is picked up, on top of block b5, block b6 is stacked, block b2 is picked up by the hand, block b2 is stacked on top of block b6, block b3 is picked up by the hand, on top of block b2, block b3 is stacked, from top of block b4, block b7 is unstacked, on top of block b3, block b7 is stacked and block b4 is picked up from the table to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "answer": "64", "plan_length": 19, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is located on the table, block b7 is on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: block b3 is removed from the top of block b7 and placed on the table, then block b5 is unstacked from block b4 and stacked on top of block b2. Next, block b4 is unstacked from block b1 and put down, followed by unstacking block b5 from block b2 and restacking it on top of block b1. Block b7 is then unstacked from block b6 and stacked on top of block b4. Block b6 is picked up and stacked on top of block b5, and block b2 is picked up and stacked on top of block b6. Block b3 is then picked up and stacked on top of block b2. Finally, block b7 is unstacked from block b4 and stacked on top of block b3, and block b4 is picked up from the table to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear of any blocks, block b2 is also on the table, block b3 is clear of any blocks, block b3 is stacked on top of block b7, block b4 is stacked on top of block b1, block b5 is clear of any blocks, block b5 is stacked on top of block b4, block b6 is on the table, block b7 is stacked on top of block b6, and the hand is empty."}
{"question_id": "d721a75d-0c60-483e-961d-e109c1531d0b", "domain_name": "blocksworld", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: block b3 is unstacked from top of block b7, the hand puts down the block b3, block b5 is unstacked from top of block b4, on top of block b2, block b5 is stacked, block b4 is unstacked from block b1, block b4 is put down, block b5 is unstacked from top of block b2, block b5 is stacked on top of block b1, block b7 is unstacked from top of block b6, on top of block b4, block b7 is stacked, block b6 is picked up, on top of block b5, block b6 is stacked, block b2 is picked up, on top of block b6, block b2 is stacked, on top of block b7, block b4 is stacked, on top of block b2, block b3 is stacked, block b7 is unstacked from top of block b4, on top of block b3, block b7 is stacked and block b4 is picked up to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "answer": "14", "plan_length": 19, "initial_state_nl": "Block b1 is located on the table, block b2 is clear, block b2 is located on the table, block b3 is clear, block b3 is on top of block b7, block b4 is on top of block b1, block b5 is clear, block b5 is on top of block b4, block b6 is located on the table, block b7 is on top of block b6 and hand is not holding anything.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: unstack block b3 from the top of block b7, place block b3 down, unstack block b5 from the top of block b4, stack block b5 on top of block b2, unstack block b4 from block b1, put block b4 down, unstack block b5 from the top of block b2, stack block b5 on top of block b1, unstack block b7 from the top of block b6, stack block b7 on top of block b4, pick up block b6, stack block b6 on top of block b5, pick up block b2, stack block b2 on top of block b6, stack block b4 on top of block b2, stack block b3 on top of block b2, unstack block b7 from the top of block b4, stack block b7 on top of block b3, and pick up block b4 to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Block b1 is on the table, block b2 is clear of any blocks, block b2 is also on the table, block b3 is clear of any blocks, block b3 is stacked on top of block b7, block b4 is stacked on top of block b1, block b5 is clear of any blocks, block b5 is stacked on top of block b4, block b6 is on the table, block b7 is stacked on top of block b6, and the hand is empty."}
