{"question_id": "7eced8f3-9456-489f-a7a9-10ca6b5e29bc", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "calibration of instrument0 for groundstation4 is complete, calibration of instrument0 for star0 is complete, calibration of instrument0 is incomplete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for groundstation5 is incomplete, calibration of instrument1 for planet14 is incomplete, calibration of instrument1 for star13 is incomplete, calibration of instrument1 for star16 is incomplete, calibration of instrument1 for star6 is incomplete, calibration of instrument2 for groundstation5 is incomplete, calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for phenomenon15 is incomplete, calibration of instrument2 for planet14 is incomplete, calibration of instrument2 for star1 is incomplete, calibration of instrument2 for star8 is incomplete, calibration of instrument3 for groundstation4 is incomplete, calibration of instrument3 for groundstation9 is incomplete, calibration of instrument3 for star0 is incomplete, calibration of instrument3 for star1 is incomplete, calibration of instrument3 for star12 is incomplete, calibration of instrument3 for star7 is incomplete, calibration of instrument3 is incomplete, for groundstation2, instrument2 is not calibrated, for groundstation2, instrument3 is not calibrated, for groundstation3, instrument3 is not calibrated, for groundstation4, instrument1 is calibrated, for groundstation5, instrument3 is not calibrated, for groundstation9, instrument1 is not calibrated, for phenomenon15, instrument0 is not calibrated, for phenomenon15, instrument1 is not calibrated, for planet14, instrument0 is not calibrated, for star0, instrument1 is not calibrated, for star0, instrument2 is not calibrated, for star1, instrument0 is not calibrated, for star10, instrument2 is not calibrated, for star10, instrument3 is not calibrated, for star11, instrument3 is not calibrated, for star12, instrument2 is not calibrated, for star13, instrument0 is not calibrated, for star13, instrument3 is not calibrated, for star16, instrument0 is not calibrated, for star16, instrument2 is not calibrated, for star6, instrument3 is calibrated, for star7, instrument1 is not calibrated, for star8, instrument0 is not calibrated, for star8, instrument1 is calibrated, groundstation5 is not where satellite0 is pointed, groundstation5 is not where satellite1 is pointed, groundstation9 is not where satellite0 is pointed, groundstation9 is not where satellite1 is pointed, image of groundstation2 does not exist in spectrograph2, image of groundstation3 does not exist in spectrograph0, image of groundstation3 does not exist in spectrograph1, image of groundstation4 does not exist in infrared3, image of groundstation5 does not exist in thermograph4, image of groundstation9 does not exist in spectrograph1, image of groundstation9 does not exist in thermograph4, image of phenomenon15 does not exist in infrared3, image of phenomenon15 does not exist in spectrograph0, image of phenomenon15 does not exist in spectrograph2, image of phenomenon15 does not exist in thermograph4, image of planet14 does not exist in spectrograph1, image of planet14 does not exist in thermograph4, image of star0 does not exist in infrared3, image of star0 does not exist in spectrograph0, image of star0 does not exist in spectrograph1, image of star0 does not exist in spectrograph2, image of star1 does not exist in infrared3, image of star1 does not exist in thermograph4, image of star10 does not exist in infrared3, image of star10 does not exist in spectrograph0, image of star10 does not exist in spectrograph1, image of star10 does not exist in spectrograph2, image of star10 does not exist in thermograph4, image of star11 does not exist in spectrograph0, image of star11 does not exist in spectrograph1, image of star12 does not exist in spectrograph2, image of star12 does not exist in thermograph4, image of star13 does not exist in infrared3, image of star13 does not exist in spectrograph0, image of star13 does not exist in spectrograph1, image of star16 does not exist in infrared3, image of star16 does not exist in spectrograph0, image of star16 does not exist in spectrograph1, image of star16 does not exist in spectrograph2, image of star6 does not exist in spectrograph2, image of star6 does not exist in thermograph4, image of star7 does not exist in spectrograph0, image of star7 does not exist in thermograph4, image of star8 does not exist in infrared3, image of star8 does not exist in spectrograph2, infrared3 is not compatible with instrument1, infrared3 is not supported by instrument3, instrument0 does not support infrared3, instrument0 is calibrated for groundstation2, instrument0 is not calibrated for groundstation3, instrument0 is not calibrated for groundstation5, instrument0 is not calibrated for groundstation9, instrument0 is not calibrated for star10, instrument0 is not calibrated for star11, instrument0 is not calibrated for star12, instrument0 is not calibrated for star6, instrument0 is not calibrated for star7, instrument0 is not powered on, instrument0 is on board satellite0, instrument1 does not support spectrograph2, instrument1 is not calibrated, instrument1 is not calibrated for groundstation3, instrument1 is not calibrated for star1, instrument1 is not calibrated for star10, instrument1 is not calibrated for star11, instrument1 is not calibrated for star12, instrument1 is not powered on, instrument1 is on board satellite0, instrument1 supports spectrograph0, instrument1 supports spectrograph1, instrument2 does not support thermograph4, instrument2 is calibrated for groundstation4, instrument2 is calibrated for star7, instrument2 is not calibrated, instrument2 is not calibrated for groundstation3, instrument2 is not calibrated for star11, instrument2 is not calibrated for star13, instrument2 is not calibrated for star6, instrument2 is not on board satellite0, instrument2 is not switched on, instrument2 is on board satellite1, instrument2 supports infrared3, instrument3 does not support spectrograph0, instrument3 is not calibrated for phenomenon15, instrument3 is not calibrated for planet14, instrument3 is not calibrated for star16, instrument3 is not calibrated for star8, instrument3 is powered on, phenomenon15 is not where satellite1 is pointed, planet14 is not where satellite0 is pointed, power is available for satellite0, power is not available for satellite1, satellite0 does not have instrument3 on board, satellite0 is not aimed towards groundstation3, satellite0 is not aimed towards groundstation4, satellite0 is not aimed towards star0, satellite0 is not aimed towards star11, satellite0 is not aimed towards star13, satellite0 is not aimed towards star7, satellite0 is not pointing to groundstation2, satellite0 is not pointing to phenomenon15, satellite0 is not pointing to star10, satellite0 is not pointing to star12, satellite0 is not pointing to star16, satellite0 is pointing to star1, satellite1 carries instrument3 on board, satellite1 does not have instrument0 on board, satellite1 does not have instrument1 on board, satellite1 is aimed towards groundstation4, satellite1 is not aimed towards groundstation3, satellite1 is not aimed towards star1, satellite1 is not aimed towards star11, satellite1 is not aimed towards star13, satellite1 is not pointing to groundstation2, satellite1 is not pointing to planet14, satellite1 is not pointing to star10, satellite1 is not pointing to star6, satellite1 is not pointing to star8, spectrograph0 is compatible with instrument0, spectrograph0 is supported by instrument2, spectrograph1 is not compatible with instrument2, spectrograph1 is not supported by instrument0, spectrograph1 is supported by instrument3, spectrograph2 is compatible with instrument2, spectrograph2 is not supported by instrument0, spectrograph2 is supported by instrument3, star0 is not where satellite1 is pointed, star12 is not where satellite1 is pointed, star16 is not where satellite1 is pointed, star6 is not where satellite0 is pointed, star7 is not where satellite1 is pointed, star8 is not where satellite0 is pointed, there is no image of direction groundstation2 in infrared3, there is no image of direction groundstation2 in spectrograph0, there is no image of direction groundstation2 in spectrograph1, there is no image of direction groundstation2 in thermograph4, there is no image of direction groundstation3 in infrared3, there is no image of direction groundstation3 in spectrograph2, there is no image of direction groundstation3 in thermograph4, there is no image of direction groundstation4 in spectrograph0, there is no image of direction groundstation4 in spectrograph1, there is no image of direction groundstation4 in spectrograph2, there is no image of direction groundstation4 in thermograph4, there is no image of direction groundstation5 in infrared3, there is no image of direction groundstation5 in spectrograph0, there is no image of direction groundstation5 in spectrograph1, there is no image of direction groundstation5 in spectrograph2, there is no image of direction groundstation9 in infrared3, there is no image of direction groundstation9 in spectrograph0, there is no image of direction groundstation9 in spectrograph2, there is no image of direction phenomenon15 in spectrograph1, there is no image of direction planet14 in infrared3, there is no image of direction planet14 in spectrograph0, there is no image of direction planet14 in spectrograph2, there is no image of direction star0 in thermograph4, there is no image of direction star1 in spectrograph0, there is no image of direction star1 in spectrograph1, there is no image of direction star1 in spectrograph2, there is no image of direction star11 in infrared3, there is no image of direction star11 in spectrograph2, there is no image of direction star11 in thermograph4, there is no image of direction star12 in infrared3, there is no image of direction star12 in spectrograph0, there is no image of direction star12 in spectrograph1, there is no image of direction star13 in spectrograph2, there is no image of direction star13 in thermograph4, there is no image of direction star16 in thermograph4, there is no image of direction star6 in infrared3, there is no image of direction star6 in spectrograph0, there is no image of direction star6 in spectrograph1, there is no image of direction star7 in infrared3, there is no image of direction star7 in spectrograph1, there is no image of direction star7 in spectrograph2, there is no image of direction star8 in spectrograph0, there is no image of direction star8 in spectrograph1, there is no image of direction star8 in thermograph4, thermograph4 is compatible with instrument0, thermograph4 is not compatible with instrument1 and thermograph4 is not supported by instrument3", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument3 for star6 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, infrared3 is supported by instrument2, instrument1 is calibrated for groundstation2, instrument2 is calibrated for star7, instrument2 is on board satellite1, instrument3 supports spectrograph1, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 has power available, satellite0 is pointing to star1, satellite1 has instrument3 on board, satellite1 has power available, satellite1 is pointing to groundstation4, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument2, spectrograph0 is supported by instrument1, spectrograph1 is compatible with instrument1, spectrograph2 is compatible with instrument3, spectrograph2 is supported by instrument2 and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, enumerate all valid properties (including both affirmative and negated properties). If there are no properties, indicate None.", "initial_state_nl_paraphrased": "The calibration process for instrument0 has been successfully completed for both groundstation2 and star0. Similarly, instrument1's calibration is complete for groundstation4 and star8. Instrument2's calibration for groundstation4 has also been finalized, and instrument3's calibration for star6 is complete. For groundstation4, instrument0 has been calibrated, and for groundstation9, instrument2 has been calibrated. Instrument2 supports infrared3, and instrument1 has been calibrated for groundstation2. Additionally, instrument2 has been calibrated for star7 and is currently on board satellite1. Instrument3 supports spectrograph1. Satellite0 has both instrument0 and instrument1 on board, and it has power available, with its current target being star1. Satellite1 also has instrument3 on board, has power available, and is currently pointing towards groundstation4. Spectrograph0 is compatible with both instrument0 and instrument2, and is also supported by instrument1. Spectrograph1 is compatible with instrument1, and spectrograph2 is compatible with instrument3 and supported by instrument2. Lastly, thermograph4 is compatible with instrument0."}
{"question_id": "97853cf5-2757-4df8-905d-756ced0ebb5c", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite1 is turned on, satellite1 turns from star3 to star1, instrument1 is calibrated on satellite1 to star1, from star1, satellite1 turns to phenomenon10, instrument1 which is on satellite1 takes an image of phenomenon10 in image5, satellite1's instrument1 takes an image of phenomenon10 in spectrograph3, from phenomenon10, satellite1 turns to phenomenon11, instrument1 which is on satellite1 takes an image of phenomenon11 in spectrograph1, satellite1 turns from phenomenon11 to phenomenon5, image of phenomenon5 is taken with instrument1 on satellite1 in image4, instrument1 which is on satellite1 takes an image of phenomenon5 in image5, satellite1 turns from phenomenon5 to phenomenon7, instrument1 which is on satellite1 takes an image of phenomenon7 in image0, instrument1 which is on satellite1 takes an image of phenomenon7 in image4, satellite1 turns from phenomenon7 to phenomenon9, instrument1 which is on satellite1 takes an image of phenomenon9 in image5, image of phenomenon9 is taken with instrument1 on satellite1 in spectrograph1, from phenomenon9, satellite1 turns to planet8 and instrument1 which is on satellite1 takes an image of planet8 in image5 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "calibration of instrument0 for groundstation4 is incomplete, calibration of instrument0 for phenomenon5 is incomplete, calibration of instrument0 for phenomenon9 is incomplete, calibration of instrument0 for star6 is incomplete, calibration of instrument1 for groundstation0 is incomplete, calibration of instrument1 for groundstation2 is incomplete, calibration of instrument1 for groundstation4 is incomplete, calibration of instrument1 for phenomenon10 is incomplete, calibration of instrument1 for phenomenon5 is incomplete, calibration of instrument1 for planet8 is incomplete, calibration of instrument1 for star3 is incomplete, for phenomenon11, instrument0 is not calibrated, for phenomenon11, instrument1 is not calibrated, for phenomenon7, instrument1 is not calibrated, for planet8, instrument0 is not calibrated, for star3, instrument0 is not calibrated, groundstation0 is not where satellite1 is pointed, groundstation2 is not where satellite1 is pointed, groundstation4 is not where satellite1 is pointed, image of groundstation0 does not exist in image2, image of groundstation0 does not exist in image4, image of groundstation0 does not exist in spectrograph1, image of groundstation0 does not exist in spectrograph3, image of groundstation2 does not exist in image2, image of groundstation2 does not exist in image4, image of groundstation2 does not exist in image5, image of groundstation2 does not exist in spectrograph3, image of groundstation4 does not exist in image0, image of groundstation4 does not exist in image2, image of groundstation4 does not exist in image5, image of groundstation4 does not exist in spectrograph1, image of phenomenon10 does not exist in image0, image of phenomenon10 does not exist in image4, image of phenomenon10 does not exist in spectrograph1, image of phenomenon11 does not exist in spectrograph3, image of phenomenon5 does not exist in image0, image of phenomenon5 does not exist in spectrograph1, image of phenomenon5 does not exist in spectrograph3, image of phenomenon7 does not exist in image2, image of phenomenon9 does not exist in image0, image of phenomenon9 does not exist in image2, image of phenomenon9 does not exist in spectrograph3, image of planet8 does not exist in image0, image of planet8 does not exist in image2, image of star1 does not exist in image0, image of star1 does not exist in image4, image of star1 does not exist in image5, image of star3 does not exist in image0, image of star3 does not exist in image5, image of star3 does not exist in spectrograph1, image of star3 does not exist in spectrograph3, image of star6 does not exist in image2, image of star6 does not exist in image4, image of star6 does not exist in image5, image of star6 does not exist in spectrograph1, image of star6 does not exist in spectrograph3, image4 is not supported by instrument0, image5 is not supported by instrument0, instrument0 does not support image0, instrument0 is not calibrated, instrument0 is not calibrated for groundstation0, instrument0 is not calibrated for groundstation2, instrument0 is not calibrated for phenomenon10, instrument0 is not calibrated for phenomenon7, instrument0 is not on board satellite1, instrument0 is not switched on, instrument1 is not calibrated for phenomenon9, instrument1 is not calibrated for star6, phenomenon9 is not where satellite0 is pointed, phenomenon9 is not where satellite1 is pointed, planet8 is not where satellite0 is pointed, satellite0 does not have instrument1 on board, satellite0 is not aimed towards groundstation0, satellite0 is not aimed towards groundstation4, satellite0 is not aimed towards phenomenon10, satellite0 is not aimed towards star1, satellite0 is not aimed towards star6, satellite0 is not pointing to groundstation2, satellite0 is not pointing to phenomenon11, satellite0 is not pointing to phenomenon7, satellite1 does not have power available, satellite1 is not aimed towards phenomenon11, satellite1 is not aimed towards phenomenon5, satellite1 is not aimed towards phenomenon7, satellite1 is not aimed towards star1, satellite1 is not pointing to phenomenon10, satellite1 is not pointing to star3, satellite1 is not pointing to star6, spectrograph1 is not compatible with instrument0, spectrograph3 is not supported by instrument0, star3 is not where satellite0 is pointed, there is no image of direction groundstation0 in image0, there is no image of direction groundstation0 in image5, there is no image of direction groundstation2 in image0, there is no image of direction groundstation2 in spectrograph1, there is no image of direction groundstation4 in image4, there is no image of direction groundstation4 in spectrograph3, there is no image of direction phenomenon10 in image2, there is no image of direction phenomenon11 in image0, there is no image of direction phenomenon11 in image2, there is no image of direction phenomenon11 in image4, there is no image of direction phenomenon11 in image5, there is no image of direction phenomenon5 in image2, there is no image of direction phenomenon7 in image5, there is no image of direction phenomenon7 in spectrograph1, there is no image of direction phenomenon7 in spectrograph3, there is no image of direction phenomenon9 in image4, there is no image of direction planet8 in image4, there is no image of direction planet8 in spectrograph1, there is no image of direction planet8 in spectrograph3, there is no image of direction star1 in image2, there is no image of direction star1 in spectrograph1, there is no image of direction star1 in spectrograph3, there is no image of direction star3 in image2, there is no image of direction star3 in image4 and there is no image of direction star6 in image0", "plan_length": 19, "initial_state_nl": "For star1, instrument1 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument0, image2 is supported by instrument1, image4 is supported by instrument1, instrument0 is calibrated for star1, instrument1 supports image5, power is available for satellite1, satellite0 has instrument0 on board, satellite0 has power available, satellite0 is aimed towards phenomenon5, satellite1 has instrument1 on board, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: the instrument1 on satellite1 is activated, then satellite1 reorients from star3 to star1, followed by the calibration of instrument1 on satellite1 to star1. Satellite1 then turns to phenomenon10, and instrument1 captures an image of phenomenon10 in both image5 and spectrograph3. Next, satellite1 turns to phenomenon11, and instrument1 on satellite1 takes an image of phenomenon11 in spectrograph1. Satellite1 then turns to phenomenon5, and images of phenomenon5 are captured with instrument1 in both image4 and image5. Satellite1 continues to phenomenon7, where instrument1 captures images in both image0 and image4. After that, satellite1 turns to phenomenon9, and instrument1 captures images of phenomenon9 in both image5 and spectrograph1. Finally, satellite1 turns to planet8, and instrument1 captures an image of planet8 in image5, resulting in the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "For star1, instrument1 is calibrated, image0 is instrument1-compatible, image2 is instrument0-compatible, image2 is instrument1-supported, image4 is instrument1-supported, instrument0 is star1-calibrated, instrument1 supports image5, power is available for satellite1, satellite0 has instrument0 onboard, satellite0 has available power, satellite0 is aimed at phenomenon5, satellite1 has instrument1 onboard, satellite1 is pointing to star3, spectrograph1 is instrument1-compatible and spectrograph3 is instrument1-supported."}
{"question_id": "ed229023-f04e-4b17-8a95-71d176e7fee5", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "calibration of instrument0 for groundstation7 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation9 is complete, for groundstation0, instrument1 is calibrated, for star3, instrument0 is calibrated, image0 is compatible with instrument2, image1 is compatible with instrument2, image1 is compatible with instrument3, image5 is compatible with instrument1, image6 is supported by instrument0, instrument1 is calibrated for groundstation6, instrument1 is on board satellite0, instrument1 is turned on, instrument1 supports image6, instrument2 is on board satellite0, instrument3 is on board satellite0, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 is calibrated for groundstation8, instrument4 is on board satellite1, instrument4 supports image1, instrument4 supports infrared7, power is available for satellite1, satellite0 carries instrument0 on board, satellite0 is pointing to groundstation2, satellite1 is aimed towards planet13 and spectrograph2 is compatible with instrument1", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument4 for groundstation8 is complete, for star3, instrument0 is calibrated, image0 is compatible with instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image5 is supported by instrument1, image6 is compatible with instrument1, image6 is supported by instrument0, instrument0 is calibrated for groundstation7, instrument1 is calibrated for groundstation0, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 is calibrated for groundstation9, instrument3 is on board satellite0, instrument3 supports image1, instrument4 is on board satellite1, instrument4 supports infrared7, power is available for satellite0, satellite0 carries instrument2 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is pointing to groundstation2, satellite1 has power, satellite1 is aimed towards planet13, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: instrument1 on satellite0 is activated to achieve the current state. In this state, identify all valid properties that do not include negations and list them; if none exist, indicate None.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation6 has been finalized, and the calibration of instrument4 at groundstation8 is also complete. For star3, instrument0 has been calibrated. Image0 is found to be compatible with instrument2, while image1 is compatible with instrument4 and also supported by instrument2. Image5 is supported by instrument1, and image6 is compatible with instrument1 and supported by instrument0. Instrument0 has been calibrated for groundstation7, and instrument1 has been calibrated for groundstation0. Instrument1 is capable of supporting spectrograph2. Instrument2 has been calibrated for star4, and instrument3 has been calibrated for groundstation9. Instrument3 is currently on board satellite0 and supports image1. Instrument4 is on board satellite1 and supports infrared7. Satellite0 has power available, and it carries instrument2 on board, in addition to having instrument0 and instrument1 on board. Satellite0 is currently pointing towards groundstation2. Satellite1 has power and is aimed towards planet13. Spectrograph4 is compatible with instrument3, and thermograph3 is supported by instrument3."}
{"question_id": "f9ce7c74-f6b2-43bf-bcf7-5d4677416519", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite1 is switched on, satellite1 turns to star1 from star3, instrument1 is calibrated on satellite1 to star1, satellite1 turns from star1 to phenomenon10, image of phenomenon10 is taken with instrument1 on satellite1 in image5, instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3, satellite1 turns to phenomenon11 from phenomenon10, instrument1 which is on satellite1 takes an image of phenomenon11 in spectrograph1, satellite1 turns to phenomenon5 from phenomenon11 and instrument1 which is on satellite1 takes an image of phenomenon5 in image4 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "calibration of instrument1 for star1 is complete, calibration of instrument1 is complete, for star1, instrument0 is calibrated, image of phenomenon10 exists in spectrograph3, image of phenomenon5 exists in image4, image2 is supported by instrument1, image4 is supported by instrument1, image5 is supported by instrument1, instrument0 supports image2, instrument1 is powered on, instrument1 supports image0, phenomenon5 is where satellite0 is pointed, phenomenon5 is where satellite1 is pointed, satellite0 carries instrument0 on board, satellite0 has power, satellite1 carries instrument1 on board, spectrograph1 is compatible with instrument1, spectrograph3 is supported by instrument1, there is an image of phenomenon10 in image5 and there is an image of phenomenon11 in spectrograph1", "plan_length": 10, "initial_state_nl": "For star1, instrument1 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument0, image2 is supported by instrument1, image4 is supported by instrument1, instrument0 is calibrated for star1, instrument1 supports image5, power is available for satellite1, satellite0 has instrument0 on board, satellite0 has power available, satellite0 is aimed towards phenomenon5, satellite1 has instrument1 on board, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 is activated on satellite1, satellite1 reorients from star3 to star1, instrument1 is calibrated on satellite1 with respect to star1, satellite1 then shifts its orientation from star1 to phenomenon10, an image of phenomenon10 is captured using instrument1 on satellite1 and stored in image5, instrument1 on satellite1 also captures an image of phenomenon10 in spectrograph3, satellite1 reorients from phenomenon10 to phenomenon11, instrument1 on satellite1 captures an image of phenomenon11 in spectrograph1, satellite1 then shifts its orientation from phenomenon11 to phenomenon5, and finally, instrument1 on satellite1 captures an image of phenomenon5 in image4, resulting in the current state. In this state, list all valid properties that do not involve negations. If none exist, state None.", "initial_state_nl_paraphrased": "For star1, instrument1 is calibrated, image0 is instrument1-compatible, image2 is instrument0-compatible, image2 is instrument1-supported, image4 is instrument1-supported, instrument0 is star1-calibrated, instrument1 supports image5, power is available for satellite1, satellite0 has instrument0 onboard, satellite0 has available power, satellite0 is aimed at phenomenon5, satellite1 has instrument1 onboard, satellite1 is pointing to star3, spectrograph1 is instrument1-compatible and spectrograph3 is instrument1-supported."}
{"question_id": "18bbeee0-e722-40fa-bc60-80a0905e586c", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is switched on, satellite0 turns to groundstation0 from groundstation2, calibration of instrument1 which is on satellite0 to groundstation0 is complete, from groundstation0, satellite0 turns to planet11, image of planet11 is taken with instrument1 on satellite0 in image5, image of planet11 is taken with instrument1 on satellite0 in image6, satellite0 turns from planet11 to planet13, image of planet13 is taken with instrument1 on satellite0 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2, satellite0 turns to star10 from planet13, image of star10 is taken with instrument1 on satellite0 in image6, satellite0's instrument1 takes an image of star10 in spectrograph2, on satellite0, instrument1 is switched off, instrument2 on satellite0 is switched on, from star10, satellite0 turns to star4, instrument2 is calibrated on satellite0 to star4, satellite0 turns from star4 to star16, instrument2 which is on satellite0 takes an image of star16 in image0 and on satellite0, instrument2 is switched off to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "calibration of instrument0 for star3 is complete, calibration of instrument1 for groundstation6 is complete, calibration of instrument1 is complete, calibration of instrument2 is complete, calibration of instrument3 for groundstation9 is complete, for groundstation0, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, for star4, instrument2 is calibrated, image of planet11 exists in image6, image of planet13 exists in spectrograph2, image of star10 exists in spectrograph2, image of star16 exists in image0, image0 is compatible with instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image5 is compatible with instrument1, image6 is compatible with instrument1, image6 is supported by instrument0, instrument0 is on board satellite0, instrument2 is on board satellite0, instrument3 supports image1, instrument4 is on board satellite1, instrument4 supports infrared7, power is available for satellite0, power is available for satellite1, satellite0 carries instrument3 on board, satellite0 has instrument1 on board, satellite0 is aimed towards star16, satellite1 is pointing to planet13, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3, there is an image of planet11 in image5, there is an image of planet13 in image5, there is an image of star10 in image6 and thermograph3 is compatible with instrument3", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument4 for groundstation8 is complete, for star3, instrument0 is calibrated, image0 is compatible with instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image5 is supported by instrument1, image6 is compatible with instrument1, image6 is supported by instrument0, instrument0 is calibrated for groundstation7, instrument1 is calibrated for groundstation0, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 is calibrated for groundstation9, instrument3 is on board satellite0, instrument3 supports image1, instrument4 is on board satellite1, instrument4 supports infrared7, power is available for satellite0, satellite0 carries instrument2 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is pointing to groundstation2, satellite1 has power, satellite1 is aimed towards planet13, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, satellite0 then reorients from groundstation0 to planet11, an image of planet11 is captured using instrument1 on satellite0 and stored in image5, another image of planet11 is captured using instrument1 on satellite0 and stored in image6, satellite0 reorients from planet11 to planet13, an image of planet13 is captured using instrument1 on satellite0 and stored in image5, instrument1 on satellite0 captures an image of planet13 in spectrograph2, satellite0 reorients from planet13 to star10, an image of star10 is captured using instrument1 on satellite0 and stored in image6, instrument1 on satellite0 captures an image of star10 in spectrograph2, instrument1 on satellite0 is deactivated, instrument2 on satellite0 is activated, satellite0 reorients from star10 to star4, instrument2 on satellite0 is calibrated to star4, satellite0 reorients from star4 to star16, instrument2 on satellite0 captures an image of star16 in image0, and finally, instrument2 on satellite0 is deactivated to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation6 has been finalized, and the calibration of instrument4 at groundstation8 is also complete. For star3, instrument0 has been calibrated. Image0 is found to be compatible with instrument2, while image1 is compatible with instrument4 and also supported by instrument2. Image5 is supported by instrument1, and image6 is both compatible with and supported by instrument1 and instrument0, respectively. Instrument0 has been calibrated for groundstation7, and instrument1 has been calibrated for groundstation0. Instrument1 is also capable of supporting spectrograph2. Instrument2 has been calibrated for star4, and instrument3 has been calibrated for groundstation9. Instrument3 is currently on board satellite0 and supports image1. Instrument4 is on board satellite1 and supports infrared7. Satellite0 has power available, and it carries instrument2, instrument0, and instrument1 on board. Satellite0 is currently pointing towards groundstation2. Satellite1 has power and is aimed at planet13. Additionally, spectrograph4 is compatible with instrument3, and thermograph3 is supported by instrument3."}
{"question_id": "adc6779d-420b-4c4b-826e-df9bb42732f7", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on, on satellite0, instrument0 is switched on, satellite1 turns to groundstation5 from phenomenon10, instrument3 is calibrated on satellite1 to groundstation5, from groundstation5, satellite1 turns to phenomenon16, image of phenomenon16 is taken with instrument3 on satellite1 in image3, satellite1 turns to phenomenon17 from phenomenon16, image of phenomenon17 is taken with instrument3 on satellite1 in image3, from phenomenon17, satellite1 turns to planet11, satellite1's instrument3 takes an image of planet11 in image3, satellite1 turns from planet11 to planet13, instrument3 which is on satellite1 takes an image of planet13 in image0, satellite1 turns to planet14 from planet13, satellite1's instrument3 takes an image of planet14 in image0, from planet14, satellite1 turns to star15, image of star15 is taken with instrument3 on satellite1 in image2, from groundstation3, satellite0 turns to star1, calibration of instrument0 which is on satellite0 to star1 is complete and from star1, satellite0 turns to phenomenon10 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for groundstation5 is complete, calibration of instrument3 is complete, for groundstation0, instrument1 is calibrated, for star1, instrument0 is calibrated, for star9, instrument0 is calibrated, image of phenomenon16 exists in image3, image of planet11 exists in image3, image of planet14 exists in image0, image0 is supported by instrument1, image2 is compatible with instrument2, image2 is compatible with instrument3, image3 is compatible with instrument0, image3 is supported by instrument2, instrument0 is calibrated, instrument0 is turned on, instrument0 supports infrared1, instrument1 is on board satellite0, instrument1 supports image3, instrument1 supports infrared1, instrument2 is calibrated for groundstation5, instrument3 is calibrated for star6, instrument3 is calibrated for star8, instrument3 is powered on, instrument3 supports image0, instrument3 supports image3, satellite0 has instrument0 on board, satellite0 has instrument2 on board, satellite0 is pointing to phenomenon10, satellite1 carries instrument3 on board, star15 is where satellite1 is pointed, there is an image of phenomenon17 in image3, there is an image of planet13 in image0 and there is an image of star15 in image2", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for star1 is complete, calibration of instrument2 for star9 is complete, for groundstation7, instrument2 is calibrated, for star6, instrument3 is calibrated, for star9, instrument0 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is compatible with instrument2, image3 is compatible with instrument0, image3 is compatible with instrument1, image3 is compatible with instrument2, image3 is compatible with instrument3, infrared1 is compatible with instrument1, infrared1 is supported by instrument0, instrument1 is calibrated for groundstation0, instrument2 is calibrated for groundstation5, instrument2 is on board satellite0, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star8, instrument3 supports image2, power is available for satellite0, satellite0 carries instrument0 on board, satellite0 has instrument1 on board, satellite0 is aimed towards groundstation3, satellite1 carries instrument3 on board, satellite1 has power available and satellite1 is aimed towards phenomenon10.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: instrument3 is activated on satellite1, and instrument0 is activated on satellite0. Satellite1 reorients from phenomenon10 to groundstation5, and instrument3 on satellite1 is calibrated to groundstation5. Then, satellite1 reorients from groundstation5 to phenomenon16, and an image of phenomenon16 is captured using instrument3 on satellite1, stored in image3. Satellite1 then reorients from phenomenon16 to phenomenon17, and an image of phenomenon17 is captured using instrument3 on satellite1, also stored in image3. From phenomenon17, satellite1 reorients to planet11, and instrument3 on satellite1 captures an image of planet11 in image3. Satellite1 then reorients from planet11 to planet13, and instrument3 on satellite1 captures an image of planet13 in image0. Next, satellite1 reorients from planet13 to planet14, and instrument3 on satellite1 captures an image of planet14 in image0. From planet14, satellite1 reorients to star15, and an image of star15 is captured using instrument3 on satellite1 in image2. Meanwhile, from groundstation3, satellite0 reorients to star1, and the calibration of instrument0 on satellite0 to star1 is completed. Finally, satellite0 reorients from star1 to phenomenon10 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star1 has been finalized, and the calibration of instrument2 for star9 is also complete. At groundstation7, instrument2 has been calibrated, while instrument3 has been calibrated for star6. Additionally, instrument0 has been calibrated for star9. Image0 is found to be compatible with instrument3 and is supported by instrument1. Image2 is compatible with instrument2, and image3 is compatible with multiple instruments, including instrument0, instrument1, instrument2, and instrument3. Infrared1 is compatible with instrument1 and is supported by instrument0. Instrument1 has been calibrated for groundstation0, while instrument2 has been calibrated for groundstation5. Instrument2 is currently on board satellite0, and instrument3 has been calibrated for both groundstation5 and star8. Instrument3 also supports image2. Satellite0 has power available, and it carries instrument0 and instrument1 on board, with its aim directed towards groundstation3. Satellite1, which also has power available, carries instrument3 on board and is aimed towards phenomenon10."}
{"question_id": "44b98eb8-9ac8-4ab1-bd93-18f730676a85", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "calibration of instrument0 for groundstation0 is incomplete, calibration of instrument0 for groundstation1 is incomplete, calibration of instrument0 for groundstation2 is incomplete, calibration of instrument0 for phenomenon14 is incomplete, calibration of instrument0 for planet13 is incomplete, calibration of instrument0 for star16 is incomplete, calibration of instrument0 for star3 is complete, calibration of instrument0 is incomplete, calibration of instrument1 for groundstation5 is incomplete, calibration of instrument1 for groundstation7 is incomplete, calibration of instrument1 for groundstation9 is incomplete, calibration of instrument1 for planet11 is incomplete, calibration of instrument1 for planet13 is incomplete, calibration of instrument1 for star12 is incomplete, calibration of instrument1 for star3 is incomplete, calibration of instrument2 for groundstation6 is incomplete, calibration of instrument2 for groundstation9 is incomplete, calibration of instrument2 for phenomenon14 is incomplete, calibration of instrument2 for phenomenon15 is incomplete, calibration of instrument2 for planet11 is incomplete, calibration of instrument2 for star16 is incomplete, calibration of instrument2 for star3 is incomplete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation2 is incomplete, calibration of instrument3 for groundstation5 is incomplete, calibration of instrument3 for phenomenon14 is incomplete, calibration of instrument3 for phenomenon15 is incomplete, calibration of instrument3 for star12 is incomplete, calibration of instrument3 for star16 is incomplete, calibration of instrument3 for star4 is incomplete, calibration of instrument4 for groundstation6 is incomplete, calibration of instrument4 for phenomenon14 is incomplete, calibration of instrument4 for planet11 is incomplete, calibration of instrument4 for star12 is incomplete, calibration of instrument4 for star16 is incomplete, calibration of instrument4 is incomplete, for groundstation0, instrument1 is calibrated, for groundstation0, instrument3 is not calibrated, for groundstation0, instrument4 is not calibrated, for groundstation1, instrument1 is not calibrated, for groundstation1, instrument2 is not calibrated, for groundstation1, instrument4 is not calibrated, for groundstation2, instrument1 is not calibrated, for groundstation2, instrument2 is not calibrated, for groundstation5, instrument0 is not calibrated, for groundstation6, instrument3 is not calibrated, for groundstation7, instrument0 is calibrated, for groundstation7, instrument2 is not calibrated, for groundstation7, instrument4 is not calibrated, for groundstation8, instrument0 is not calibrated, for groundstation8, instrument2 is not calibrated, for groundstation8, instrument3 is not calibrated, for groundstation8, instrument4 is calibrated, for groundstation9, instrument3 is calibrated, for groundstation9, instrument4 is not calibrated, for phenomenon14, instrument1 is not calibrated, for phenomenon15, instrument1 is not calibrated, for phenomenon15, instrument4 is not calibrated, for planet13, instrument2 is not calibrated, for star10, instrument0 is not calibrated, for star10, instrument2 is not calibrated, for star10, instrument4 is not calibrated, for star12, instrument0 is not calibrated, for star16, instrument1 is not calibrated, for star3, instrument3 is not calibrated, for star3, instrument4 is not calibrated, for star4, instrument0 is not calibrated, for star4, instrument1 is not calibrated, groundstation0 is not where satellite1 is pointed, groundstation1 is not where satellite0 is pointed, groundstation2 is where satellite0 is pointed, groundstation6 is not where satellite1 is pointed, groundstation7 is not where satellite1 is pointed, image of groundstation0 does not exist in image5, image of groundstation0 does not exist in thermograph3, image of groundstation1 does not exist in image6, image of groundstation1 does not exist in infrared7, image of groundstation1 does not exist in thermograph3, image of groundstation2 does not exist in image5, image of groundstation2 does not exist in image6, image of groundstation2 does not exist in spectrograph2, image of groundstation2 does not exist in spectrograph4, image of groundstation2 does not exist in thermograph3, image of groundstation5 does not exist in image0, image of groundstation5 does not exist in infrared7, image of groundstation5 does not exist in thermograph3, image of groundstation6 does not exist in image0, image of groundstation6 does not exist in image1, image of groundstation6 does not exist in infrared7, image of groundstation6 does not exist in spectrograph2, image of groundstation6 does not exist in spectrograph4, image of groundstation6 does not exist in thermograph3, image of groundstation7 does not exist in image0, image of groundstation7 does not exist in image6, image of groundstation7 does not exist in spectrograph2, image of groundstation7 does not exist in spectrograph4, image of groundstation8 does not exist in image5, image of groundstation8 does not exist in spectrograph4, image of groundstation9 does not exist in image0, image of groundstation9 does not exist in image1, image of groundstation9 does not exist in image5, image of groundstation9 does not exist in image6, image of groundstation9 does not exist in infrared7, image of groundstation9 does not exist in spectrograph2, image of groundstation9 does not exist in spectrograph4, image of groundstation9 does not exist in thermograph3, image of phenomenon14 does not exist in image1, image of phenomenon14 does not exist in image5, image of phenomenon15 does not exist in image0, image of phenomenon15 does not exist in image1, image of phenomenon15 does not exist in image6, image of phenomenon15 does not exist in thermograph3, image of planet11 does not exist in image0, image of planet11 does not exist in image1, image of planet11 does not exist in image5, image of planet11 does not exist in image6, image of planet11 does not exist in infrared7, image of planet11 does not exist in spectrograph2, image of planet11 does not exist in spectrograph4, image of planet11 does not exist in thermograph3, image of planet13 does not exist in image0, image of planet13 does not exist in image1, image of planet13 does not exist in spectrograph2, image of star10 does not exist in image0, image of star10 does not exist in image5, image of star10 does not exist in image6, image of star10 does not exist in spectrograph4, image of star12 does not exist in image0, image of star12 does not exist in image1, image of star12 does not exist in spectrograph2, image of star12 does not exist in spectrograph4, image of star16 does not exist in image0, image of star16 does not exist in image1, image of star16 does not exist in thermograph3, image of star3 does not exist in image0, image of star3 does not exist in image1, image of star3 does not exist in image5, image of star3 does not exist in infrared7, image of star3 does not exist in spectrograph2, image of star4 does not exist in image0, image of star4 does not exist in image1, image of star4 does not exist in spectrograph4, image0 is compatible with instrument2, image0 is not compatible with instrument1, image0 is not compatible with instrument4, image0 is not supported by instrument0, image0 is not supported by instrument3, image1 is compatible with instrument3, image1 is compatible with instrument4, image1 is not compatible with instrument0, image1 is not compatible with instrument1, image5 is not compatible with instrument4, image5 is not supported by instrument0, image5 is supported by instrument1, image6 is not compatible with instrument2, image6 is not supported by instrument3, image6 is not supported by instrument4, infrared7 is not compatible with instrument1, infrared7 is not supported by instrument0, infrared7 is supported by instrument4, instrument0 does not support spectrograph4, instrument0 is not calibrated for groundstation6, instrument0 is not calibrated for groundstation9, instrument0 is not calibrated for phenomenon15, instrument0 is not calibrated for planet11, instrument0 is not powered on, instrument0 is on board satellite0, instrument0 supports image6, instrument1 is calibrated for groundstation6, instrument1 is not calibrated, instrument1 is not calibrated for groundstation8, instrument1 is not calibrated for star10, instrument1 is not on board satellite1, instrument1 is switched on, instrument1 supports image6, instrument1 supports spectrograph2, instrument2 does not support image5, instrument2 does not support infrared7, instrument2 does not support spectrograph4, instrument2 is not calibrated, instrument2 is not calibrated for groundstation0, instrument2 is not calibrated for groundstation5, instrument2 is not calibrated for star12, instrument2 is not on board satellite1, instrument2 is not switched on, instrument2 supports image1, instrument3 does not support image5, instrument3 does not support infrared7, instrument3 is not calibrated, instrument3 is not calibrated for groundstation1, instrument3 is not calibrated for groundstation7, instrument3 is not calibrated for planet11, instrument3 is not calibrated for planet13, instrument3 is not calibrated for star10, instrument3 is not turned on, instrument3 supports spectrograph4, instrument4 does not support spectrograph2, instrument4 does not support thermograph3, instrument4 is not calibrated for groundstation2, instrument4 is not calibrated for groundstation5, instrument4 is not calibrated for planet13, instrument4 is not calibrated for star4, instrument4 is not switched on, phenomenon14 is not where satellite0 is pointed, planet13 is not where satellite0 is pointed, satellite0 does not carry instrument4 on board, satellite0 does not have power available, satellite0 has instrument1 on board, satellite0 has instrument2 on board, satellite0 has instrument3 on board, satellite0 is not aimed towards groundstation0, satellite0 is not aimed towards groundstation5, satellite0 is not aimed towards groundstation6, satellite0 is not aimed towards groundstation7, satellite0 is not aimed towards phenomenon15, satellite0 is not pointing to groundstation8, satellite0 is not pointing to groundstation9, satellite0 is not pointing to planet11, satellite0 is not pointing to star3, satellite1 carries instrument4 on board, satellite1 does not carry instrument0 on board, satellite1 does not have instrument3 on board, satellite1 has power available, satellite1 is not aimed towards groundstation8, satellite1 is not aimed towards phenomenon14, satellite1 is not pointing to groundstation1, satellite1 is not pointing to groundstation2, satellite1 is not pointing to groundstation5, satellite1 is not pointing to groundstation9, satellite1 is not pointing to phenomenon15, satellite1 is not pointing to planet11, satellite1 is not pointing to star10, satellite1 is not pointing to star3, satellite1 is pointing to planet13, spectrograph2 is not supported by instrument0, spectrograph2 is not supported by instrument2, spectrograph2 is not supported by instrument3, spectrograph4 is not compatible with instrument1, spectrograph4 is not compatible with instrument4, star10 is not where satellite0 is pointed, star12 is not where satellite0 is pointed, star12 is not where satellite1 is pointed, star16 is not where satellite0 is pointed, star16 is not where satellite1 is pointed, star4 is not where satellite0 is pointed, star4 is not where satellite1 is pointed, there is no image of direction groundstation0 in image0, there is no image of direction groundstation0 in image1, there is no image of direction groundstation0 in image6, there is no image of direction groundstation0 in infrared7, there is no image of direction groundstation0 in spectrograph2, there is no image of direction groundstation0 in spectrograph4, there is no image of direction groundstation1 in image0, there is no image of direction groundstation1 in image1, there is no image of direction groundstation1 in image5, there is no image of direction groundstation1 in spectrograph2, there is no image of direction groundstation1 in spectrograph4, there is no image of direction groundstation2 in image0, there is no image of direction groundstation2 in image1, there is no image of direction groundstation2 in infrared7, there is no image of direction groundstation5 in image1, there is no image of direction groundstation5 in image5, there is no image of direction groundstation5 in image6, there is no image of direction groundstation5 in spectrograph2, there is no image of direction groundstation5 in spectrograph4, there is no image of direction groundstation6 in image5, there is no image of direction groundstation6 in image6, there is no image of direction groundstation7 in image1, there is no image of direction groundstation7 in image5, there is no image of direction groundstation7 in infrared7, there is no image of direction groundstation7 in thermograph3, there is no image of direction groundstation8 in image0, there is no image of direction groundstation8 in image1, there is no image of direction groundstation8 in image6, there is no image of direction groundstation8 in infrared7, there is no image of direction groundstation8 in spectrograph2, there is no image of direction groundstation8 in thermograph3, there is no image of direction phenomenon14 in image0, there is no image of direction phenomenon14 in image6, there is no image of direction phenomenon14 in infrared7, there is no image of direction phenomenon14 in spectrograph2, there is no image of direction phenomenon14 in spectrograph4, there is no image of direction phenomenon14 in thermograph3, there is no image of direction phenomenon15 in image5, there is no image of direction phenomenon15 in infrared7, there is no image of direction phenomenon15 in spectrograph2, there is no image of direction phenomenon15 in spectrograph4, there is no image of direction planet13 in image5, there is no image of direction planet13 in image6, there is no image of direction planet13 in infrared7, there is no image of direction planet13 in spectrograph4, there is no image of direction planet13 in thermograph3, there is no image of direction star10 in image1, there is no image of direction star10 in infrared7, there is no image of direction star10 in spectrograph2, there is no image of direction star10 in thermograph3, there is no image of direction star12 in image5, there is no image of direction star12 in image6, there is no image of direction star12 in infrared7, there is no image of direction star12 in thermograph3, there is no image of direction star16 in image5, there is no image of direction star16 in image6, there is no image of direction star16 in infrared7, there is no image of direction star16 in spectrograph2, there is no image of direction star16 in spectrograph4, there is no image of direction star3 in image6, there is no image of direction star3 in spectrograph4, there is no image of direction star3 in thermograph3, there is no image of direction star4 in image5, there is no image of direction star4 in image6, there is no image of direction star4 in infrared7, there is no image of direction star4 in spectrograph2, there is no image of direction star4 in thermograph3, thermograph3 is not compatible with instrument0, thermograph3 is not supported by instrument1, thermograph3 is not supported by instrument2 and thermograph3 is supported by instrument3", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument4 for groundstation8 is complete, for star3, instrument0 is calibrated, image0 is compatible with instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image5 is supported by instrument1, image6 is compatible with instrument1, image6 is supported by instrument0, instrument0 is calibrated for groundstation7, instrument1 is calibrated for groundstation0, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 is calibrated for groundstation9, instrument3 is on board satellite0, instrument3 supports image1, instrument4 is on board satellite1, instrument4 supports infrared7, power is available for satellite0, satellite0 carries instrument2 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is pointing to groundstation2, satellite1 has power, satellite1 is aimed towards planet13, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: instrument1 on satellite0 is activated to achieve the current state. In this state, enumerate all valid properties (including both affirmative and negated properties). If there are no properties, indicate None.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation6 has been finalized, and the calibration of instrument4 at groundstation8 is also complete. For star3, instrument0 has been calibrated. Image0 is found to be compatible with instrument2, while image1 is compatible with instrument4 and also supported by instrument2. Image5 is supported by instrument1, and image6 is compatible with instrument1 and supported by instrument0. Instrument0 has been calibrated for groundstation7, and instrument1 has been calibrated for groundstation0. Instrument1 is capable of supporting spectrograph2. Instrument2 has been calibrated for star4, and instrument3 has been calibrated for groundstation9. Instrument3 is currently on board satellite0 and supports image1. Instrument4 is on board satellite1 and supports infrared7. Satellite0 has power available, and it carries instrument2 on board, in addition to having instrument0 and instrument1 on board. Satellite0 is currently pointing towards groundstation2. Satellite1 has power and is aimed towards planet13. Spectrograph4 is compatible with instrument3, and thermograph3 is supported by instrument3."}
{"question_id": "f6d36892-b884-4d85-af08-2c838368f4ad", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, on satellite0, instrument0 is switched on, satellite1 turns from phenomenon10 to groundstation5, instrument3 is calibrated on satellite1 to groundstation5, from groundstation5, satellite1 turns to phenomenon16, satellite1's instrument3 takes an image of phenomenon16 in image3, satellite1 turns to phenomenon17 from phenomenon16, satellite1's instrument3 takes an image of phenomenon17 in image3, from phenomenon17, satellite1 turns to planet11, image of planet11 is taken with instrument3 on satellite1 in image3, from planet11, satellite1 turns to planet13, image of planet13 is taken with instrument3 on satellite1 in image0, satellite1 turns to planet14 from planet13, image of planet14 is taken with instrument3 on satellite1 in image0, satellite1 turns from planet14 to star15, instrument3 which is on satellite1 takes an image of star15 in image2, satellite0 turns to star1 from groundstation3, instrument0 that is on satellite0 is calibrated to star1 and from star1, satellite0 turns to phenomenon10 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "calibration of instrument0 for groundstation0 is incomplete, calibration of instrument0 for star15 is incomplete, calibration of instrument0 for star8 is incomplete, calibration of instrument1 for groundstation0 is complete, calibration of instrument1 for groundstation3 is incomplete, calibration of instrument1 for groundstation4 is incomplete, calibration of instrument1 for groundstation5 is incomplete, calibration of instrument1 for star9 is incomplete, calibration of instrument2 for groundstation2 is incomplete, calibration of instrument2 for groundstation3 is incomplete, calibration of instrument2 for groundstation4 is incomplete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for phenomenon10 is incomplete, calibration of instrument2 for planet14 is incomplete, calibration of instrument2 for star6 is incomplete, calibration of instrument3 for groundstation2 is incomplete, calibration of instrument3 for groundstation4 is incomplete, calibration of instrument3 for groundstation5 is complete, calibration of instrument3 for planet11 is incomplete, calibration of instrument3 for planet13 is incomplete, calibration of instrument3 for planet14 is incomplete, for groundstation0, instrument2 is not calibrated, for groundstation0, instrument3 is not calibrated, for groundstation2, instrument1 is not calibrated, for groundstation3, instrument3 is not calibrated, for groundstation4, instrument0 is not calibrated, for groundstation5, instrument0 is not calibrated, for groundstation5, instrument2 is calibrated, for groundstation7, instrument1 is not calibrated, for groundstation7, instrument3 is not calibrated, for phenomenon10, instrument0 is not calibrated, for phenomenon10, instrument3 is not calibrated, for phenomenon16, instrument0 is not calibrated, for phenomenon16, instrument1 is not calibrated, for phenomenon16, instrument3 is not calibrated, for phenomenon17, instrument0 is not calibrated, for phenomenon17, instrument1 is not calibrated, for phenomenon17, instrument2 is not calibrated, for planet11, instrument0 is not calibrated, for planet11, instrument2 is not calibrated, for planet12, instrument0 is not calibrated, for planet12, instrument1 is not calibrated, for planet12, instrument2 is not calibrated, for planet13, instrument0 is not calibrated, for planet13, instrument1 is not calibrated, for planet14, instrument0 is not calibrated, for planet14, instrument1 is not calibrated, for star1, instrument2 is not calibrated, for star15, instrument1 is not calibrated, for star15, instrument3 is not calibrated, for star6, instrument1 is not calibrated, for star6, instrument3 is calibrated, for star8, instrument1 is not calibrated, for star8, instrument3 is calibrated, for star9, instrument2 is calibrated, groundstation2 is not where satellite1 is pointed, groundstation4 is not where satellite0 is pointed, groundstation7 is not where satellite0 is pointed, image of groundstation0 does not exist in infrared1, image of groundstation2 does not exist in image3, image of groundstation3 does not exist in infrared1, image of groundstation4 does not exist in image0, image of groundstation4 does not exist in image2, image of groundstation4 does not exist in image3, image of groundstation5 does not exist in image0, image of groundstation5 does not exist in image2, image of groundstation7 does not exist in image0, image of groundstation7 does not exist in infrared1, image of phenomenon10 does not exist in image2, image of phenomenon10 does not exist in image3, image of phenomenon16 does not exist in image2, image of phenomenon16 does not exist in infrared1, image of phenomenon16 exists in image3, image of phenomenon17 does not exist in infrared1, image of phenomenon17 exists in image3, image of planet11 does not exist in image0, image of planet11 does not exist in infrared1, image of planet12 does not exist in image0, image of planet12 does not exist in image3, image of planet13 does not exist in image2, image of planet13 does not exist in image3, image of planet13 does not exist in infrared1, image of planet14 does not exist in image3, image of planet14 does not exist in infrared1, image of star15 does not exist in image3, image of star15 does not exist in infrared1, image of star6 does not exist in image2, image of star6 does not exist in infrared1, image of star8 does not exist in image0, image of star8 does not exist in image2, image of star8 does not exist in image3, image of star9 does not exist in image0, image of star9 does not exist in image3, image of star9 does not exist in infrared1, image0 is compatible with instrument1, image0 is not compatible with instrument2, image0 is not supported by instrument0, image0 is supported by instrument3, image2 is not compatible with instrument0, image2 is not compatible with instrument1, image3 is supported by instrument1, image3 is supported by instrument3, infrared1 is compatible with instrument1, infrared1 is not supported by instrument2, infrared1 is supported by instrument0, instrument0 is calibrated, instrument0 is calibrated for star1, instrument0 is calibrated for star9, instrument0 is not calibrated for groundstation2, instrument0 is not calibrated for groundstation3, instrument0 is not calibrated for groundstation7, instrument0 is not calibrated for star6, instrument0 is not on board satellite1, instrument0 is powered on, instrument0 supports image3, instrument1 is not calibrated, instrument1 is not calibrated for phenomenon10, instrument1 is not calibrated for planet11, instrument1 is not calibrated for star1, instrument1 is not turned on, instrument2 is not calibrated, instrument2 is not calibrated for phenomenon16, instrument2 is not calibrated for planet13, instrument2 is not calibrated for star15, instrument2 is not calibrated for star8, instrument2 is not on board satellite1, instrument2 is not turned on, instrument2 supports image2, instrument2 supports image3, instrument3 does not support infrared1, instrument3 is calibrated, instrument3 is not calibrated for phenomenon17, instrument3 is not calibrated for planet12, instrument3 is not calibrated for star1, instrument3 is not calibrated for star9, instrument3 is on board satellite1, instrument3 is switched on, instrument3 supports image2, phenomenon10 is not where satellite1 is pointed, phenomenon16 is not where satellite1 is pointed, planet13 is not where satellite1 is pointed, power is not available for satellite1, satellite0 carries instrument0 on board, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 does not have instrument3 on board, satellite0 does not have power available, satellite0 is not aimed towards groundstation0, satellite0 is not aimed towards groundstation2, satellite0 is not aimed towards phenomenon17, satellite0 is not aimed towards planet11, satellite0 is not aimed towards planet13, satellite0 is not aimed towards planet14, satellite0 is not aimed towards star6, satellite0 is not pointing to groundstation3, satellite0 is not pointing to groundstation5, satellite0 is not pointing to phenomenon16, satellite0 is not pointing to planet12, satellite0 is not pointing to star8, satellite0 is pointing to phenomenon10, satellite1 does not carry instrument1 on board, satellite1 is not aimed towards groundstation4, satellite1 is not aimed towards groundstation7, satellite1 is not aimed towards phenomenon17, satellite1 is not pointing to groundstation0, satellite1 is not pointing to groundstation3, satellite1 is not pointing to groundstation5, satellite1 is not pointing to planet11, satellite1 is not pointing to planet12, satellite1 is not pointing to planet14, satellite1 is not pointing to star1, satellite1 is not pointing to star6, satellite1 is not pointing to star9, star1 is not where satellite0 is pointed, star15 is not where satellite0 is pointed, star15 is where satellite1 is pointed, star8 is not where satellite1 is pointed, star9 is not where satellite0 is pointed, there is an image of planet11 in image3, there is an image of planet13 in image0, there is an image of planet14 in image0, there is an image of star15 in image2, there is no image of direction groundstation0 in image0, there is no image of direction groundstation0 in image2, there is no image of direction groundstation0 in image3, there is no image of direction groundstation2 in image0, there is no image of direction groundstation2 in image2, there is no image of direction groundstation2 in infrared1, there is no image of direction groundstation3 in image0, there is no image of direction groundstation3 in image2, there is no image of direction groundstation3 in image3, there is no image of direction groundstation4 in infrared1, there is no image of direction groundstation5 in image3, there is no image of direction groundstation5 in infrared1, there is no image of direction groundstation7 in image2, there is no image of direction groundstation7 in image3, there is no image of direction phenomenon10 in image0, there is no image of direction phenomenon10 in infrared1, there is no image of direction phenomenon16 in image0, there is no image of direction phenomenon17 in image0, there is no image of direction phenomenon17 in image2, there is no image of direction planet11 in image2, there is no image of direction planet12 in image2, there is no image of direction planet12 in infrared1, there is no image of direction planet14 in image2, there is no image of direction star1 in image0, there is no image of direction star1 in image2, there is no image of direction star1 in image3, there is no image of direction star1 in infrared1, there is no image of direction star15 in image0, there is no image of direction star6 in image0, there is no image of direction star6 in image3, there is no image of direction star8 in infrared1 and there is no image of direction star9 in image2", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for star1 is complete, calibration of instrument2 for star9 is complete, for groundstation7, instrument2 is calibrated, for star6, instrument3 is calibrated, for star9, instrument0 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is compatible with instrument2, image3 is compatible with instrument0, image3 is compatible with instrument1, image3 is compatible with instrument2, image3 is compatible with instrument3, infrared1 is compatible with instrument1, infrared1 is supported by instrument0, instrument1 is calibrated for groundstation0, instrument2 is calibrated for groundstation5, instrument2 is on board satellite0, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star8, instrument3 supports image2, power is available for satellite0, satellite0 carries instrument0 on board, satellite0 has instrument1 on board, satellite0 is aimed towards groundstation3, satellite1 carries instrument3 on board, satellite1 has power available and satellite1 is aimed towards phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 is activated on satellite1, instrument0 is activated on satellite0, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated to groundstation5, then satellite1 reorients from groundstation5 to phenomenon16, an image of phenomenon16 is captured by instrument3 on satellite1 and stored in image3, satellite1 then reorients from phenomenon16 to phenomenon17, and another image of phenomenon17 is captured by instrument3 on satellite1 and stored in image3, from phenomenon17, satellite1 reorients to planet11, an image of planet11 is captured by instrument3 on satellite1 and stored in image3, satellite1 then reorients to planet13, an image of planet13 is captured by instrument3 on satellite1 and stored in image0, satellite1 reorients from planet13 to planet14, an image of planet14 is captured by instrument3 on satellite1 and stored in image0, satellite1 then reorients from planet14 to star15, and an image of star15 is captured by instrument3 on satellite1 and stored in image2, meanwhile, satellite0 reorients from groundstation3 to star1, instrument0 on satellite0 is calibrated to star1, and then satellite0 reorients from star1 to phenomenon10, resulting in the current state. In this state, list all valid properties (including both affirmative and negative properties). If there are none, indicate None.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star1 has been finalized, and the calibration of instrument2 for star9 is also complete. At groundstation7, instrument2 has been calibrated, while instrument3 has been calibrated for star6. Furthermore, instrument0 has been calibrated for star9. In terms of compatibility, image0 is supported by instrument3 and instrument1. Image2 is compatible with instrument2, and image3 is compatible with multiple instruments, including instrument0, instrument1, instrument2, and instrument3. Additionally, infrared1 is compatible with instrument1 and supported by instrument0. Calibration has been completed for instrument1 at groundstation0, instrument2 at groundstation5, and instrument3 at both groundstation5 and star8. Instrument3 also supports image2. Satellite0 has power available and carries instrument0 and instrument1 on board, with its aim directed towards groundstation3. Satellite1, which also has power available, carries instrument3 on board and is aimed towards phenomenon10."}
{"question_id": "3cca24c2-0104-4e21-8a6c-e483487c0ab8", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, instrument0 that is on satellite0 is turned on, from phenomenon10, satellite1 turns to groundstation5, instrument3 that is on satellite1 is calibrated to groundstation5, from groundstation5, satellite1 turns to phenomenon16, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, satellite1 turns from phenomenon16 to phenomenon17, satellite1's instrument3 takes an image of phenomenon17 in image3, from phenomenon17, satellite1 turns to planet11, satellite1's instrument3 takes an image of planet11 in image3, satellite1 turns to planet13 from planet11, image of planet13 is taken with instrument3 on satellite1 in image0, from planet13, satellite1 turns to planet14, satellite1's instrument3 takes an image of planet14 in image0, from planet14, satellite1 turns to star15, image of star15 is taken with instrument3 on satellite1 in image2, from groundstation3, satellite0 turns to star1, instrument0 that is on satellite0 is calibrated to star1 and satellite0 turns to phenomenon10 from star1 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "calibration of instrument0 for groundstation0 is incomplete, calibration of instrument0 for planet11 is incomplete, calibration of instrument0 for star6 is incomplete, calibration of instrument0 for star8 is incomplete, calibration of instrument1 for groundstation4 is incomplete, calibration of instrument1 for phenomenon10 is incomplete, calibration of instrument1 for phenomenon16 is incomplete, calibration of instrument1 for phenomenon17 is incomplete, calibration of instrument1 for planet14 is incomplete, calibration of instrument1 for star15 is incomplete, calibration of instrument1 for star6 is incomplete, calibration of instrument2 for phenomenon17 is incomplete, calibration of instrument2 for planet14 is incomplete, calibration of instrument3 for groundstation2 is incomplete, calibration of instrument3 for groundstation7 is incomplete, calibration of instrument3 for planet12 is incomplete, calibration of instrument3 for star1 is incomplete, for groundstation0, instrument2 is not calibrated, for groundstation2, instrument0 is not calibrated, for groundstation2, instrument2 is not calibrated, for groundstation3, instrument0 is not calibrated, for groundstation3, instrument3 is not calibrated, for groundstation4, instrument0 is not calibrated, for groundstation4, instrument2 is not calibrated, for groundstation4, instrument3 is not calibrated, for groundstation7, instrument0 is not calibrated, for phenomenon10, instrument0 is not calibrated, for phenomenon10, instrument3 is not calibrated, for phenomenon16, instrument3 is not calibrated, for phenomenon17, instrument0 is not calibrated, for phenomenon17, instrument3 is not calibrated, for planet11, instrument1 is not calibrated, for planet12, instrument0 is not calibrated, for planet13, instrument3 is not calibrated, for planet14, instrument0 is not calibrated, for planet14, instrument3 is not calibrated, for star1, instrument1 is not calibrated, for star8, instrument1 is not calibrated, for star9, instrument1 is not calibrated, for star9, instrument3 is not calibrated, groundstation0 is not where satellite0 is pointed, groundstation2 is not where satellite1 is pointed, groundstation3 is not where satellite0 is pointed, groundstation7 is not where satellite1 is pointed, image of groundstation0 does not exist in image2, image of groundstation0 does not exist in infrared1, image of groundstation2 does not exist in image2, image of groundstation3 does not exist in image0, image of groundstation3 does not exist in image3, image of groundstation4 does not exist in image0, image of groundstation4 does not exist in image3, image of groundstation4 does not exist in infrared1, image of groundstation5 does not exist in image0, image of groundstation5 does not exist in image3, image of groundstation7 does not exist in image0, image of groundstation7 does not exist in image3, image of phenomenon10 does not exist in image2, image of phenomenon16 does not exist in image2, image of phenomenon16 does not exist in infrared1, image of phenomenon17 does not exist in image0, image of planet11 does not exist in image0, image of planet12 does not exist in image2, image of planet12 does not exist in image3, image of planet12 does not exist in infrared1, image of planet13 does not exist in image2, image of planet13 does not exist in infrared1, image of planet14 does not exist in infrared1, image of star1 does not exist in image0, image of star1 does not exist in image2, image of star1 does not exist in infrared1, image of star15 does not exist in image0, image of star15 does not exist in image3, image of star6 does not exist in image0, image of star6 does not exist in image3, image of star6 does not exist in infrared1, image of star8 does not exist in image2, image of star8 does not exist in image3, image of star9 does not exist in image2, image0 is not compatible with instrument0, image0 is not supported by instrument2, image2 is not compatible with instrument0, image2 is not compatible with instrument1, infrared1 is not supported by instrument2, infrared1 is not supported by instrument3, instrument0 is not calibrated for groundstation5, instrument0 is not calibrated for phenomenon16, instrument0 is not calibrated for planet13, instrument0 is not calibrated for star15, instrument1 is not calibrated, instrument1 is not calibrated for groundstation2, instrument1 is not calibrated for groundstation3, instrument1 is not calibrated for groundstation5, instrument1 is not calibrated for groundstation7, instrument1 is not calibrated for planet12, instrument1 is not calibrated for planet13, instrument1 is not turned on, instrument2 is not calibrated, instrument2 is not calibrated for groundstation3, instrument2 is not calibrated for phenomenon10, instrument2 is not calibrated for phenomenon16, instrument2 is not calibrated for planet11, instrument2 is not calibrated for planet12, instrument2 is not calibrated for planet13, instrument2 is not calibrated for star1, instrument2 is not calibrated for star15, instrument2 is not calibrated for star6, instrument2 is not calibrated for star8, instrument2 is not powered on, instrument3 is not calibrated for groundstation0, instrument3 is not calibrated for planet11, instrument3 is not calibrated for star15, planet11 is not where satellite0 is pointed, planet13 is not where satellite0 is pointed, planet14 is not where satellite0 is pointed, satellite0 does not carry instrument3 on board, satellite0 does not have power available, satellite0 is not aimed towards groundstation7, satellite0 is not aimed towards phenomenon16, satellite0 is not aimed towards phenomenon17, satellite0 is not aimed towards star6, satellite0 is not aimed towards star8, satellite0 is not pointing to groundstation2, satellite0 is not pointing to groundstation4, satellite0 is not pointing to groundstation5, satellite0 is not pointing to planet12, satellite0 is not pointing to star15, satellite0 is not pointing to star9, satellite1 does not have instrument0 on board, satellite1 does not have instrument1 on board, satellite1 does not have instrument2 on board, satellite1 does not have power, satellite1 is not aimed towards groundstation4, satellite1 is not aimed towards phenomenon10, satellite1 is not aimed towards phenomenon16, satellite1 is not aimed towards phenomenon17, satellite1 is not aimed towards planet11, satellite1 is not aimed towards star1, satellite1 is not aimed towards star6, satellite1 is not aimed towards star8, satellite1 is not aimed towards star9, satellite1 is not pointing to groundstation0, satellite1 is not pointing to groundstation3, satellite1 is not pointing to groundstation5, satellite1 is not pointing to planet12, satellite1 is not pointing to planet13, satellite1 is not pointing to planet14, star1 is not where satellite0 is pointed, there is no image of direction groundstation0 in image0, there is no image of direction groundstation0 in image3, there is no image of direction groundstation2 in image0, there is no image of direction groundstation2 in image3, there is no image of direction groundstation2 in infrared1, there is no image of direction groundstation3 in image2, there is no image of direction groundstation3 in infrared1, there is no image of direction groundstation4 in image2, there is no image of direction groundstation5 in image2, there is no image of direction groundstation5 in infrared1, there is no image of direction groundstation7 in image2, there is no image of direction groundstation7 in infrared1, there is no image of direction phenomenon10 in image0, there is no image of direction phenomenon10 in image3, there is no image of direction phenomenon10 in infrared1, there is no image of direction phenomenon16 in image0, there is no image of direction phenomenon17 in image2, there is no image of direction phenomenon17 in infrared1, there is no image of direction planet11 in image2, there is no image of direction planet11 in infrared1, there is no image of direction planet12 in image0, there is no image of direction planet13 in image3, there is no image of direction planet14 in image2, there is no image of direction planet14 in image3, there is no image of direction star1 in image3, there is no image of direction star15 in infrared1, there is no image of direction star6 in image2, there is no image of direction star8 in image0, there is no image of direction star8 in infrared1, there is no image of direction star9 in image0, there is no image of direction star9 in image3 and there is no image of direction star9 in infrared1", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for star1 is complete, calibration of instrument2 for star9 is complete, for groundstation7, instrument2 is calibrated, for star6, instrument3 is calibrated, for star9, instrument0 is calibrated, image0 is compatible with instrument3, image0 is supported by instrument1, image2 is compatible with instrument2, image3 is compatible with instrument0, image3 is compatible with instrument1, image3 is compatible with instrument2, image3 is compatible with instrument3, infrared1 is compatible with instrument1, infrared1 is supported by instrument0, instrument1 is calibrated for groundstation0, instrument2 is calibrated for groundstation5, instrument2 is on board satellite0, instrument3 is calibrated for groundstation5, instrument3 is calibrated for star8, instrument3 supports image2, power is available for satellite0, satellite0 carries instrument0 on board, satellite0 has instrument1 on board, satellite0 is aimed towards groundstation3, satellite1 carries instrument3 on board, satellite1 has power available and satellite1 is aimed towards phenomenon10.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the instrument3 on satellite1 is activated, and the instrument0 on satellite0 is also activated. Satellite1 then reorients from phenomenon10 to groundstation5, and instrument3 on satellite1 is calibrated to groundstation5. Next, satellite1 reorients from groundstation5 to phenomenon16, and instrument3 on satellite1 captures an image of phenomenon16, storing it in image3. Satellite1 then reorients from phenomenon16 to phenomenon17, and instrument3 on satellite1 captures an image of phenomenon17, also storing it in image3. Satellite1 continues by reorienting from phenomenon17 to planet11, and instrument3 on satellite1 captures an image of planet11, storing it in image3. Satellite1 then reorients from planet11 to planet13, and an image of planet13 is captured using instrument3 on satellite1, storing it in image0. Satellite1 reorients from planet13 to planet14, and instrument3 on satellite1 captures an image of planet14, storing it in image0. Satellite1 then reorients from planet14 to star15, and an image of star15 is captured using instrument3 on satellite1, storing it in image2. Meanwhile, from groundstation3, satellite0 reorients to star1, instrument0 on satellite0 is calibrated to star1, and then satellite0 reorients from star1 to phenomenon10 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star1 has been finalized, and the calibration of instrument2 for star9 is also complete. At groundstation7, instrument2 has been calibrated, while instrument3 has been calibrated for star6. Furthermore, instrument0 has been calibrated for star9. In terms of compatibility, image0 is supported by instrument3 and instrument1. Image2 is compatible with instrument2, and image3 is compatible with multiple instruments, including instrument0, instrument1, instrument2, and instrument3. Additionally, infrared1 is compatible with instrument1 and supported by instrument0. Calibration has been completed for instrument1 at groundstation0, instrument2 at groundstation5, and instrument3 at both groundstation5 and star8. Instrument3 also supports image2. Satellite0 has power available and carries instrument0 and instrument1 on board, with its aim directed towards groundstation3. Satellite1, which also has power available, carries instrument3 on board and is aimed towards phenomenon10."}
{"question_id": "b8167e2b-e727-41c0-8a01-a27f9a497c17", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, instrument0 on satellite0 is switched on, from groundstation4, satellite1 turns to star6, calibration of instrument3 which is on satellite1 to star6 is complete, satellite1 turns from star6 to planet14, satellite1's instrument3 takes an image of planet14 in spectrograph1, satellite1 turns to star10 from planet14, satellite1's instrument3 takes an image of star10 in spectrograph1, from star10, satellite1 turns to star12, satellite1's instrument3 takes an image of star12 in spectrograph1, satellite1 turns from star12 to star0, from star1, satellite0 turns to groundstation2, instrument0 that is on satellite0 is calibrated to groundstation2, satellite0 turns from groundstation2 to phenomenon15, satellite0's instrument0 takes an image of phenomenon15 in spectrograph0, satellite0 turns from phenomenon15 to star11, satellite0's instrument0 takes an image of star11 in thermograph4, from star11, satellite0 turns to star13 and satellite0's instrument0 takes an image of star13 in spectrograph0 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "calibration of instrument0 for groundstation9 is incomplete, calibration of instrument0 for phenomenon15 is incomplete, calibration of instrument0 for planet14 is incomplete, calibration of instrument0 for star1 is incomplete, calibration of instrument0 for star11 is incomplete, calibration of instrument0 for star13 is incomplete, calibration of instrument0 for star8 is incomplete, calibration of instrument1 for star1 is incomplete, calibration of instrument1 for star6 is incomplete, calibration of instrument1 for star7 is incomplete, calibration of instrument2 for groundstation2 is incomplete, calibration of instrument2 for groundstation5 is incomplete, calibration of instrument2 for star10 is incomplete, calibration of instrument2 for star13 is incomplete, calibration of instrument2 for star16 is incomplete, calibration of instrument3 for groundstation3 is incomplete, calibration of instrument3 for groundstation4 is incomplete, calibration of instrument3 for groundstation9 is incomplete, calibration of instrument3 for star10 is incomplete, calibration of instrument3 for star13 is incomplete, calibration of instrument3 for star16 is incomplete, calibration of instrument3 for star7 is incomplete, for groundstation3, instrument0 is not calibrated, for groundstation5, instrument0 is not calibrated, for phenomenon15, instrument2 is not calibrated, for planet14, instrument1 is not calibrated, for planet14, instrument2 is not calibrated, for planet14, instrument3 is not calibrated, for star0, instrument2 is not calibrated, for star10, instrument0 is not calibrated, for star11, instrument3 is not calibrated, for star12, instrument0 is not calibrated, for star13, instrument1 is not calibrated, for star16, instrument0 is not calibrated, for star6, instrument0 is not calibrated, for star6, instrument2 is not calibrated, for star8, instrument3 is not calibrated, groundstation2 is not where satellite1 is pointed, groundstation5 is not where satellite0 is pointed, groundstation5 is not where satellite1 is pointed, groundstation9 is not where satellite0 is pointed, image of groundstation2 does not exist in spectrograph1, image of groundstation2 does not exist in spectrograph2, image of groundstation2 does not exist in thermograph4, image of groundstation3 does not exist in infrared3, image of groundstation3 does not exist in spectrograph1, image of groundstation3 does not exist in spectrograph2, image of groundstation3 does not exist in thermograph4, image of groundstation4 does not exist in spectrograph0, image of groundstation4 does not exist in thermograph4, image of groundstation5 does not exist in infrared3, image of groundstation5 does not exist in spectrograph0, image of groundstation5 does not exist in spectrograph1, image of groundstation5 does not exist in spectrograph2, image of groundstation5 does not exist in thermograph4, image of groundstation9 does not exist in spectrograph0, image of groundstation9 does not exist in spectrograph2, image of phenomenon15 does not exist in infrared3, image of phenomenon15 does not exist in spectrograph1, image of phenomenon15 does not exist in spectrograph2, image of planet14 does not exist in infrared3, image of planet14 does not exist in spectrograph0, image of planet14 does not exist in spectrograph2, image of star0 does not exist in spectrograph0, image of star0 does not exist in spectrograph1, image of star1 does not exist in infrared3, image of star1 does not exist in spectrograph0, image of star1 does not exist in spectrograph1, image of star1 does not exist in thermograph4, image of star10 does not exist in spectrograph0, image of star11 does not exist in spectrograph0, image of star11 does not exist in spectrograph1, image of star12 does not exist in infrared3, image of star12 does not exist in spectrograph0, image of star13 does not exist in spectrograph1, image of star13 does not exist in spectrograph2, image of star13 does not exist in thermograph4, image of star16 does not exist in infrared3, image of star16 does not exist in thermograph4, image of star7 does not exist in infrared3, image of star7 does not exist in spectrograph0, image of star7 does not exist in spectrograph1, image of star7 does not exist in spectrograph2, image of star7 does not exist in thermograph4, image of star8 does not exist in spectrograph0, image of star8 does not exist in spectrograph1, image of star8 does not exist in thermograph4, infrared3 is not compatible with instrument1, infrared3 is not supported by instrument0, infrared3 is not supported by instrument3, instrument0 does not support spectrograph2, instrument0 is not calibrated for star7, instrument1 does not support thermograph4, instrument1 is not calibrated, instrument1 is not calibrated for groundstation3, instrument1 is not calibrated for groundstation5, instrument1 is not calibrated for groundstation9, instrument1 is not calibrated for phenomenon15, instrument1 is not calibrated for star0, instrument1 is not calibrated for star10, instrument1 is not calibrated for star11, instrument1 is not calibrated for star12, instrument1 is not calibrated for star16, instrument1 is not on board satellite1, instrument1 is not turned on, instrument2 is not calibrated, instrument2 is not calibrated for groundstation3, instrument2 is not calibrated for star1, instrument2 is not calibrated for star11, instrument2 is not calibrated for star12, instrument2 is not calibrated for star8, instrument2 is not turned on, instrument3 does not support spectrograph0, instrument3 is not calibrated for groundstation2, instrument3 is not calibrated for groundstation5, instrument3 is not calibrated for phenomenon15, instrument3 is not calibrated for star0, instrument3 is not calibrated for star1, instrument3 is not calibrated for star12, phenomenon15 is not where satellite1 is pointed, planet14 is not where satellite0 is pointed, satellite0 does not have instrument2 on board, satellite0 does not have instrument3 on board, satellite0 does not have power available, satellite0 is not aimed towards groundstation3, satellite0 is not aimed towards groundstation4, satellite0 is not pointing to groundstation2, satellite0 is not pointing to phenomenon15, satellite0 is not pointing to star1, satellite0 is not pointing to star10, satellite0 is not pointing to star11, satellite0 is not pointing to star8, satellite1 does not carry instrument0 on board, satellite1 does not have power available, satellite1 is not aimed towards groundstation3, satellite1 is not aimed towards groundstation4, satellite1 is not aimed towards planet14, satellite1 is not aimed towards star13, satellite1 is not aimed towards star6, satellite1 is not pointing to groundstation9, satellite1 is not pointing to star1, satellite1 is not pointing to star11, satellite1 is not pointing to star16, satellite1 is not pointing to star8, spectrograph1 is not supported by instrument0, spectrograph1 is not supported by instrument2, spectrograph2 is not compatible with instrument1, star0 is not where satellite0 is pointed, star10 is not where satellite1 is pointed, star12 is not where satellite0 is pointed, star12 is not where satellite1 is pointed, star16 is not where satellite0 is pointed, star6 is not where satellite0 is pointed, star7 is not where satellite0 is pointed, star7 is not where satellite1 is pointed, there is no image of direction groundstation2 in infrared3, there is no image of direction groundstation2 in spectrograph0, there is no image of direction groundstation3 in spectrograph0, there is no image of direction groundstation4 in infrared3, there is no image of direction groundstation4 in spectrograph1, there is no image of direction groundstation4 in spectrograph2, there is no image of direction groundstation9 in infrared3, there is no image of direction groundstation9 in spectrograph1, there is no image of direction groundstation9 in thermograph4, there is no image of direction phenomenon15 in thermograph4, there is no image of direction planet14 in thermograph4, there is no image of direction star0 in infrared3, there is no image of direction star0 in spectrograph2, there is no image of direction star0 in thermograph4, there is no image of direction star1 in spectrograph2, there is no image of direction star10 in infrared3, there is no image of direction star10 in spectrograph2, there is no image of direction star10 in thermograph4, there is no image of direction star11 in infrared3, there is no image of direction star11 in spectrograph2, there is no image of direction star12 in spectrograph2, there is no image of direction star12 in thermograph4, there is no image of direction star13 in infrared3, there is no image of direction star16 in spectrograph0, there is no image of direction star16 in spectrograph1, there is no image of direction star16 in spectrograph2, there is no image of direction star6 in infrared3, there is no image of direction star6 in spectrograph0, there is no image of direction star6 in spectrograph1, there is no image of direction star6 in spectrograph2, there is no image of direction star6 in thermograph4, there is no image of direction star8 in infrared3, there is no image of direction star8 in spectrograph2, thermograph4 is not compatible with instrument2 and thermograph4 is not supported by instrument3", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument3 for star6 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, infrared3 is supported by instrument2, instrument1 is calibrated for groundstation2, instrument2 is calibrated for star7, instrument2 is on board satellite1, instrument3 supports spectrograph1, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 has power available, satellite0 is pointing to star1, satellite1 has instrument3 on board, satellite1 has power available, satellite1 is pointing to groundstation4, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument2, spectrograph0 is supported by instrument1, spectrograph1 is compatible with instrument1, spectrograph2 is compatible with instrument3, spectrograph2 is supported by instrument2 and thermograph4 is compatible with instrument0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is activated, satellite1 rotates from groundstation4 to star6, the calibration of instrument3 on satellite1 for star6 is completed, satellite1 then rotates from star6 to planet14, an image of planet14 is captured by instrument3 on satellite1 using spectrograph1, satellite1 rotates from planet14 to star10, an image of star10 is captured by instrument3 on satellite1 using spectrograph1, satellite1 then rotates from star10 to star12, an image of star12 is captured by instrument3 on satellite1 using spectrograph1, satellite1 rotates from star12 to star0, from star1, satellite0 rotates to groundstation2, instrument0 on satellite0 is calibrated for groundstation2, satellite0 rotates from groundstation2 to phenomenon15, an image of phenomenon15 is captured by instrument0 on satellite0 using spectrograph0, satellite0 rotates from phenomenon15 to star11, an image of star11 is captured by instrument0 on satellite0 using thermograph4, and finally, satellite0 rotates from star11 to star13 and captures an image of star13 using spectrograph0, resulting in the current state. In this state, list all valid properties that involve negations; if none exist, indicate None.", "initial_state_nl_paraphrased": "The calibration process for instrument0 has been successfully completed for both groundstation2 and star0. Similarly, instrument1's calibration is complete for groundstation4 and star8. Instrument2's calibration for groundstation4 has also been finalized, and instrument3's calibration for star6 is complete. For groundstation4, instrument0 is now calibrated, and for groundstation9, instrument2 is calibrated. Instrument2 provides support for infrared3, and instrument1 is calibrated for groundstation2. Additionally, instrument2 is calibrated for star7 and is currently on board satellite1. Instrument3 supports spectrograph1, and satellite0 has both instrument0 and instrument1 on board, with power available and currently pointing to star1. Satellite1 also has instrument3 on board, has power available, and is pointing to groundstation4. Spectrograph0 is compatible with both instrument0 and instrument2, and is supported by instrument1. Spectrograph1 is compatible with instrument1, and spectrograph2 is compatible with instrument3 and supported by instrument2. Lastly, thermograph4 is compatible with instrument0."}
{"question_id": "0d4d51de-89d1-481d-8487-a58727eddb03", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "calibration of instrument1 for star8 is complete, for groundstation2, instrument1 is calibrated, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, for star7, instrument2 is calibrated, infrared3 is supported by instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for star0, instrument0 is on board satellite0, instrument0 supports thermograph4, instrument1 is calibrated for groundstation4, instrument1 supports spectrograph0, instrument2 is calibrated for groundstation4, instrument2 is on board satellite1, instrument2 supports spectrograph0, instrument3 is calibrated for star6, instrument3 is powered on, instrument3 supports spectrograph1, power is available for satellite0, satellite0 has instrument1 on board, satellite1 carries instrument3 on board, satellite1 is pointing to groundstation4, spectrograph0 is compatible with instrument0, spectrograph1 is supported by instrument1, spectrograph2 is supported by instrument2, spectrograph2 is supported by instrument3 and star1 is where satellite0 is pointed", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument3 for star6 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, infrared3 is supported by instrument2, instrument1 is calibrated for groundstation2, instrument2 is calibrated for star7, instrument2 is on board satellite1, instrument3 supports spectrograph1, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 has power available, satellite0 is pointing to star1, satellite1 has instrument3 on board, satellite1 has power available, satellite1 is pointing to groundstation4, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument2, spectrograph0 is supported by instrument1, spectrograph1 is compatible with instrument1, spectrograph2 is compatible with instrument3, spectrograph2 is supported by instrument2 and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, identify all valid properties that do not include negations and list them; if there are no such properties, indicate None.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with groundstation2 has been finalized, the calibration process for instrument0 with star0 has been finalized, the calibration process for instrument1 with groundstation4 has been finalized, the calibration process for instrument1 with star8 has been finalized, the calibration process for instrument2 with groundstation4 has been finalized, the calibration process for instrument3 with star6 has been finalized. Groundstation4 has a calibrated instrument0, while groundstation9 has a calibrated instrument2. Instrument2 supports infrared3, instrument1 is calibrated for groundstation2, instrument2 is calibrated for star7, and instrument2 is installed on satellite1. Instrument3 supports spectrograph1, satellite0 has instrument0 and instrument1 on board, and satellite0 has available power. Satellite0 is currently pointing towards star1, satellite1 has instrument3 on board, satellite1 has available power, and satellite1 is pointing towards groundstation4. Spectrograph0 is compatible with both instrument0 and instrument2, and is also supported by instrument1. Spectrograph1 is compatible with instrument1, spectrograph2 is compatible with instrument3, and spectrograph2 is supported by instrument2. Additionally, thermograph4 is compatible with instrument0."}
{"question_id": "3a48c0fc-5f27-44f9-bd19-caea18b940fa", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is switched on to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "calibration of instrument0 for star1 is complete, for star1, instrument1 is calibrated, image0 is supported by instrument1, image2 is supported by instrument1, image4 is supported by instrument1, image5 is compatible with instrument1, instrument0 supports image2, instrument1 is turned on, instrument1 supports spectrograph1, power is available for satellite0, satellite0 has instrument0 on board, satellite0 is pointing to phenomenon5, satellite1 has instrument1 on board, satellite1 is aimed towards star3 and spectrograph3 is compatible with instrument1", "plan_length": 1, "initial_state_nl": "For star1, instrument1 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument0, image2 is supported by instrument1, image4 is supported by instrument1, instrument0 is calibrated for star1, instrument1 supports image5, power is available for satellite1, satellite0 has instrument0 on board, satellite0 has power available, satellite0 is aimed towards phenomenon5, satellite1 has instrument1 on board, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: instrument1 on satellite1 is activated to achieve the current state. In this state, identify all valid properties that do not include negations and list them; if there are no such properties, indicate None.", "initial_state_nl_paraphrased": "For star1, instrument1 is calibrated, image0 is instrument1-compatible, image2 is instrument0-compatible, image2 is instrument1-supported, image4 is instrument1-supported, instrument0 is star1-calibrated, instrument1 supports image5, power is available for satellite1, instrument0 is on board satellite0, satellite0 has available power, satellite0 is aimed at phenomenon5, instrument1 is on board satellite1, satellite1 is pointing to star3, spectrograph1 is instrument1-compatible and spectrograph3 is instrument1-supported."}
{"question_id": "cdf590bd-50ae-4165-9ef7-bada547d85bb", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, instrument0 on satellite0 is switched on, satellite1 turns from groundstation4 to star6, instrument3 is calibrated on satellite1 to star6, satellite1 turns from star6 to planet14, satellite1's instrument3 takes an image of planet14 in spectrograph1, satellite1 turns to star10 from planet14, satellite1's instrument3 takes an image of star10 in spectrograph1, from star10, satellite1 turns to star12 and satellite1's instrument3 takes an image of star12 in spectrograph1 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "calibration of instrument0 for groundstation3 is incomplete, calibration of instrument0 for groundstation9 is incomplete, calibration of instrument0 for phenomenon15 is incomplete, calibration of instrument0 for star10 is incomplete, calibration of instrument0 is incomplete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star0 is incomplete, calibration of instrument1 for star1 is incomplete, calibration of instrument1 for star10 is incomplete, calibration of instrument1 for star12 is incomplete, calibration of instrument1 for star16 is incomplete, calibration of instrument2 for phenomenon15 is incomplete, calibration of instrument2 for star0 is incomplete, calibration of instrument2 for star1 is incomplete, calibration of instrument2 for star10 is incomplete, calibration of instrument2 for star11 is incomplete, calibration of instrument2 for star6 is incomplete, calibration of instrument3 for groundstation2 is incomplete, calibration of instrument3 for groundstation3 is incomplete, calibration of instrument3 for groundstation4 is incomplete, calibration of instrument3 for planet14 is incomplete, calibration of instrument3 is complete, for groundstation2, instrument0 is calibrated, for groundstation4, instrument0 is calibrated, for groundstation4, instrument1 is calibrated, for groundstation4, instrument2 is calibrated, for groundstation5, instrument1 is not calibrated, for groundstation9, instrument1 is not calibrated, for groundstation9, instrument2 is calibrated, for groundstation9, instrument3 is not calibrated, for phenomenon15, instrument3 is not calibrated, for planet14, instrument1 is not calibrated, for planet14, instrument2 is not calibrated, for star0, instrument3 is not calibrated, for star1, instrument0 is not calibrated, for star1, instrument3 is not calibrated, for star11, instrument1 is not calibrated, for star11, instrument3 is not calibrated, for star12, instrument0 is not calibrated, for star12, instrument3 is not calibrated, for star13, instrument0 is not calibrated, for star16, instrument0 is not calibrated, for star16, instrument2 is not calibrated, for star16, instrument3 is not calibrated, for star6, instrument0 is not calibrated, for star7, instrument1 is not calibrated, for star8, instrument0 is not calibrated, groundstation2 is not where satellite1 is pointed, groundstation3 is not where satellite0 is pointed, groundstation5 is not where satellite0 is pointed, groundstation9 is not where satellite1 is pointed, image of groundstation2 does not exist in spectrograph2, image of groundstation2 does not exist in thermograph4, image of groundstation3 does not exist in infrared3, image of groundstation3 does not exist in spectrograph1, image of groundstation3 does not exist in spectrograph2, image of groundstation3 does not exist in thermograph4, image of groundstation4 does not exist in infrared3, image of groundstation4 does not exist in spectrograph0, image of groundstation4 does not exist in thermograph4, image of groundstation5 does not exist in infrared3, image of groundstation5 does not exist in spectrograph2, image of groundstation9 does not exist in spectrograph1, image of groundstation9 does not exist in spectrograph2, image of phenomenon15 does not exist in spectrograph1, image of phenomenon15 does not exist in spectrograph2, image of phenomenon15 does not exist in thermograph4, image of planet14 exists in spectrograph1, image of star0 does not exist in infrared3, image of star0 does not exist in spectrograph2, image of star0 does not exist in thermograph4, image of star1 does not exist in infrared3, image of star1 does not exist in spectrograph0, image of star1 does not exist in spectrograph1, image of star11 does not exist in infrared3, image of star11 does not exist in thermograph4, image of star12 does not exist in infrared3, image of star12 does not exist in spectrograph0, image of star12 does not exist in spectrograph2, image of star12 does not exist in thermograph4, image of star13 does not exist in infrared3, image of star13 does not exist in spectrograph0, image of star13 does not exist in spectrograph2, image of star16 does not exist in infrared3, image of star6 does not exist in spectrograph2, image of star7 does not exist in spectrograph1, image of star8 does not exist in spectrograph1, image of star8 does not exist in spectrograph2, infrared3 is compatible with instrument2, infrared3 is not compatible with instrument1, infrared3 is not compatible with instrument3, instrument0 does not support infrared3, instrument0 is calibrated for star0, instrument0 is not calibrated for groundstation5, instrument0 is not calibrated for planet14, instrument0 is not calibrated for star11, instrument0 is not calibrated for star7, instrument0 is powered on, instrument0 supports spectrograph0, instrument1 is calibrated for star8, instrument1 is not calibrated, instrument1 is not calibrated for groundstation3, instrument1 is not calibrated for phenomenon15, instrument1 is not calibrated for star13, instrument1 is not calibrated for star6, instrument1 is not turned on, instrument1 supports spectrograph1, instrument2 does not support spectrograph1, instrument2 is calibrated for star7, instrument2 is not calibrated, instrument2 is not calibrated for groundstation2, instrument2 is not calibrated for groundstation3, instrument2 is not calibrated for groundstation5, instrument2 is not calibrated for star12, instrument2 is not calibrated for star13, instrument2 is not calibrated for star8, instrument2 is not powered on, instrument2 supports spectrograph2, instrument3 does not support spectrograph0, instrument3 is calibrated for star6, instrument3 is not calibrated for groundstation5, instrument3 is not calibrated for star10, instrument3 is not calibrated for star13, instrument3 is not calibrated for star7, instrument3 is not calibrated for star8, instrument3 is switched on, instrument3 supports spectrograph2, phenomenon15 is not where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 carries instrument1 on board, satellite0 does not carry instrument2 on board, satellite0 does not have instrument3 on board, satellite0 does not have power available, satellite0 is not aimed towards groundstation2, satellite0 is not aimed towards star0, satellite0 is not aimed towards star10, satellite0 is not aimed towards star11, satellite0 is not aimed towards star12, satellite0 is not aimed towards star6, satellite0 is not aimed towards star8, satellite0 is not pointing to groundstation4, satellite0 is not pointing to groundstation9, satellite0 is not pointing to planet14, satellite0 is not pointing to star13, satellite0 is pointing to star1, satellite1 carries instrument3 on board, satellite1 does not have instrument0 on board, satellite1 does not have instrument1 on board, satellite1 does not have power available, satellite1 has instrument2 on board, satellite1 is aimed towards star12, satellite1 is not aimed towards groundstation3, satellite1 is not aimed towards groundstation4, satellite1 is not aimed towards groundstation5, satellite1 is not aimed towards star0, satellite1 is not aimed towards star11, satellite1 is not aimed towards star13, satellite1 is not aimed towards star8, satellite1 is not pointing to phenomenon15, satellite1 is not pointing to planet14, satellite1 is not pointing to star1, satellite1 is not pointing to star16, spectrograph0 is compatible with instrument1, spectrograph0 is compatible with instrument2, spectrograph1 is compatible with instrument3, spectrograph1 is not supported by instrument0, spectrograph2 is not supported by instrument0, spectrograph2 is not supported by instrument1, star10 is not where satellite1 is pointed, star16 is not where satellite0 is pointed, star6 is not where satellite1 is pointed, star7 is not where satellite0 is pointed, star7 is not where satellite1 is pointed, there is an image of star10 in spectrograph1, there is an image of star12 in spectrograph1, there is no image of direction groundstation2 in infrared3, there is no image of direction groundstation2 in spectrograph0, there is no image of direction groundstation2 in spectrograph1, there is no image of direction groundstation3 in spectrograph0, there is no image of direction groundstation4 in spectrograph1, there is no image of direction groundstation4 in spectrograph2, there is no image of direction groundstation5 in spectrograph0, there is no image of direction groundstation5 in spectrograph1, there is no image of direction groundstation5 in thermograph4, there is no image of direction groundstation9 in infrared3, there is no image of direction groundstation9 in spectrograph0, there is no image of direction groundstation9 in thermograph4, there is no image of direction phenomenon15 in infrared3, there is no image of direction phenomenon15 in spectrograph0, there is no image of direction planet14 in infrared3, there is no image of direction planet14 in spectrograph0, there is no image of direction planet14 in spectrograph2, there is no image of direction planet14 in thermograph4, there is no image of direction star0 in spectrograph0, there is no image of direction star0 in spectrograph1, there is no image of direction star1 in spectrograph2, there is no image of direction star1 in thermograph4, there is no image of direction star10 in infrared3, there is no image of direction star10 in spectrograph0, there is no image of direction star10 in spectrograph2, there is no image of direction star10 in thermograph4, there is no image of direction star11 in spectrograph0, there is no image of direction star11 in spectrograph1, there is no image of direction star11 in spectrograph2, there is no image of direction star13 in spectrograph1, there is no image of direction star13 in thermograph4, there is no image of direction star16 in spectrograph0, there is no image of direction star16 in spectrograph1, there is no image of direction star16 in spectrograph2, there is no image of direction star16 in thermograph4, there is no image of direction star6 in infrared3, there is no image of direction star6 in spectrograph0, there is no image of direction star6 in spectrograph1, there is no image of direction star6 in thermograph4, there is no image of direction star7 in infrared3, there is no image of direction star7 in spectrograph0, there is no image of direction star7 in spectrograph2, there is no image of direction star7 in thermograph4, there is no image of direction star8 in infrared3, there is no image of direction star8 in spectrograph0, there is no image of direction star8 in thermograph4, thermograph4 is compatible with instrument0, thermograph4 is not compatible with instrument3, thermograph4 is not supported by instrument1 and thermograph4 is not supported by instrument2", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation4 is complete, calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument3 for star6 is complete, for groundstation4, instrument0 is calibrated, for groundstation9, instrument2 is calibrated, infrared3 is supported by instrument2, instrument1 is calibrated for groundstation2, instrument2 is calibrated for star7, instrument2 is on board satellite1, instrument3 supports spectrograph1, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 has power available, satellite0 is pointing to star1, satellite1 has instrument3 on board, satellite1 has power available, satellite1 is pointing to groundstation4, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument2, spectrograph0 is supported by instrument1, spectrograph1 is compatible with instrument1, spectrograph2 is compatible with instrument3, spectrograph2 is supported by instrument2 and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is activated, satellite1 reorients from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, satellite1 then reorients from star6 to planet14, an image of planet14 is captured by instrument3 on satellite1 using spectrograph1, satellite1 reorients from planet14 to star10, an image of star10 is captured by instrument3 on satellite1 using spectrograph1, and finally, satellite1 reorients from star10 to star12, capturing an image of star12 using spectrograph1, resulting in the current state. In this state, list all valid properties (including both affirmative and negated properties). If there are no valid properties, indicate None.", "initial_state_nl_paraphrased": "The calibration process for instrument0 has been successfully completed for both groundstation2 and star0. Similarly, instrument1's calibration is complete for groundstation4 and star8. Instrument2's calibration for groundstation4 has also been finalized, and instrument3's calibration for star6 is complete. For groundstation4, instrument0 is now calibrated, and for groundstation9, instrument2 is calibrated. Instrument2 provides support for infrared3, and instrument1 is calibrated for groundstation2. Additionally, instrument2 is calibrated for star7 and is currently on board satellite1. Instrument3 supports spectrograph1, and satellite0 has both instrument0 and instrument1 on board, with power available and pointing towards star1. Satellite1 also has instrument3 on board, has power available, and is pointing towards groundstation4. Spectrograph0 is compatible with both instrument0 and instrument2, and is supported by instrument1. Spectrograph1 is compatible with instrument1, and spectrograph2 is compatible with instrument3 and supported by instrument2. Lastly, thermograph4 is compatible with instrument0."}
{"question_id": "10abdb85-8612-48d1-8cc8-c45e42da1400", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is switched on, satellite0 turns to groundstation0 from groundstation2, instrument1 that is on satellite0 is calibrated to groundstation0, satellite0 turns to planet11 from groundstation0, satellite0's instrument1 takes an image of planet11 in image5, image of planet11 is taken with instrument1 on satellite0 in image6, satellite0 turns to planet13 from planet11, instrument1 which is on satellite0 takes an image of planet13 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2 and from planet13, satellite0 turns to star10 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "calibration of instrument0 for planet11 is incomplete, calibration of instrument0 for star10 is incomplete, calibration of instrument0 for star12 is incomplete, calibration of instrument0 for star4 is incomplete, calibration of instrument0 is incomplete, calibration of instrument1 for groundstation7 is incomplete, calibration of instrument1 for planet11 is incomplete, calibration of instrument1 for planet13 is incomplete, calibration of instrument2 for groundstation0 is incomplete, calibration of instrument2 for groundstation6 is incomplete, calibration of instrument2 for groundstation7 is incomplete, calibration of instrument2 for star10 is incomplete, calibration of instrument2 for star16 is incomplete, calibration of instrument2 for star3 is incomplete, calibration of instrument3 for groundstation2 is incomplete, calibration of instrument3 for groundstation8 is incomplete, calibration of instrument3 for phenomenon15 is incomplete, calibration of instrument3 for planet11 is incomplete, calibration of instrument3 for star12 is incomplete, calibration of instrument3 for star16 is incomplete, calibration of instrument3 is incomplete, calibration of instrument4 for groundstation1 is incomplete, calibration of instrument4 for groundstation2 is incomplete, calibration of instrument4 for groundstation5 is incomplete, calibration of instrument4 for groundstation6 is incomplete, calibration of instrument4 for groundstation9 is incomplete, calibration of instrument4 for phenomenon14 is incomplete, calibration of instrument4 for phenomenon15 is incomplete, calibration of instrument4 for planet13 is incomplete, calibration of instrument4 for star10 is incomplete, calibration of instrument4 for star4 is incomplete, for groundstation1, instrument3 is not calibrated, for groundstation2, instrument0 is not calibrated, for groundstation2, instrument1 is not calibrated, for groundstation5, instrument0 is not calibrated, for groundstation5, instrument1 is not calibrated, for groundstation6, instrument3 is not calibrated, for groundstation7, instrument4 is not calibrated, for groundstation8, instrument0 is not calibrated, for groundstation8, instrument2 is not calibrated, for groundstation9, instrument0 is not calibrated, for phenomenon15, instrument0 is not calibrated, for phenomenon15, instrument1 is not calibrated, for planet11, instrument2 is not calibrated, for planet11, instrument4 is not calibrated, for planet13, instrument0 is not calibrated, for planet13, instrument2 is not calibrated, for star10, instrument3 is not calibrated, for star12, instrument1 is not calibrated, for star16, instrument0 is not calibrated, for star16, instrument4 is not calibrated, for star3, instrument1 is not calibrated, for star4, instrument1 is not calibrated, for star4, instrument3 is not calibrated, groundstation0 is not where satellite0 is pointed, groundstation0 is not where satellite1 is pointed, groundstation1 is not where satellite0 is pointed, groundstation1 is not where satellite1 is pointed, groundstation8 is not where satellite1 is pointed, groundstation9 is not where satellite1 is pointed, image of groundstation0 does not exist in image1, image of groundstation0 does not exist in image5, image of groundstation0 does not exist in infrared7, image of groundstation1 does not exist in image0, image of groundstation1 does not exist in image5, image of groundstation1 does not exist in image6, image of groundstation1 does not exist in infrared7, image of groundstation1 does not exist in spectrograph2, image of groundstation2 does not exist in image1, image of groundstation2 does not exist in image5, image of groundstation2 does not exist in image6, image of groundstation2 does not exist in infrared7, image of groundstation2 does not exist in spectrograph2, image of groundstation5 does not exist in image0, image of groundstation5 does not exist in image5, image of groundstation5 does not exist in infrared7, image of groundstation6 does not exist in infrared7, image of groundstation6 does not exist in spectrograph4, image of groundstation7 does not exist in image0, image of groundstation7 does not exist in image1, image of groundstation7 does not exist in image6, image of groundstation7 does not exist in infrared7, image of groundstation7 does not exist in spectrograph2, image of groundstation7 does not exist in spectrograph4, image of groundstation8 does not exist in image1, image of groundstation8 does not exist in image5, image of groundstation8 does not exist in infrared7, image of groundstation8 does not exist in spectrograph2, image of groundstation8 does not exist in thermograph3, image of groundstation9 does not exist in image0, image of groundstation9 does not exist in image5, image of groundstation9 does not exist in image6, image of groundstation9 does not exist in spectrograph4, image of phenomenon14 does not exist in spectrograph2, image of phenomenon14 does not exist in thermograph3, image of phenomenon15 does not exist in image0, image of phenomenon15 does not exist in image6, image of planet11 does not exist in spectrograph4, image of planet11 does not exist in thermograph3, image of planet13 does not exist in image0, image of planet13 does not exist in image6, image of planet13 does not exist in infrared7, image of planet13 does not exist in spectrograph4, image of planet13 does not exist in thermograph3, image of star10 does not exist in image6, image of star10 does not exist in infrared7, image of star10 does not exist in spectrograph2, image of star12 does not exist in image0, image of star12 does not exist in image5, image of star12 does not exist in infrared7, image of star12 does not exist in spectrograph2, image of star12 does not exist in thermograph3, image of star16 does not exist in image0, image of star16 does not exist in image6, image of star16 does not exist in infrared7, image of star16 does not exist in spectrograph4, image of star16 does not exist in thermograph3, image of star3 does not exist in image0, image of star3 does not exist in spectrograph2, image of star3 does not exist in spectrograph4, image of star3 does not exist in thermograph3, image of star4 does not exist in image0, image of star4 does not exist in image1, image of star4 does not exist in spectrograph2, image of star4 does not exist in spectrograph4, image of star4 does not exist in thermograph3, image0 is not supported by instrument0, image0 is not supported by instrument1, image0 is not supported by instrument4, image1 is not compatible with instrument0, image5 is not compatible with instrument2, image5 is not compatible with instrument4, image6 is not compatible with instrument3, image6 is not compatible with instrument4, infrared7 is not compatible with instrument0, infrared7 is not compatible with instrument1, infrared7 is not compatible with instrument3, instrument0 does not support image5, instrument0 is not calibrated for groundstation0, instrument0 is not calibrated for groundstation1, instrument0 is not calibrated for groundstation6, instrument0 is not calibrated for phenomenon14, instrument0 is not on board satellite1, instrument0 is not turned on, instrument1 does not support image1, instrument1 is not calibrated for groundstation1, instrument1 is not calibrated for groundstation8, instrument1 is not calibrated for groundstation9, instrument1 is not calibrated for phenomenon14, instrument1 is not calibrated for star10, instrument1 is not calibrated for star16, instrument2 does not support image6, instrument2 does not support infrared7, instrument2 does not support spectrograph2, instrument2 does not support thermograph3, instrument2 is not calibrated, instrument2 is not calibrated for groundstation1, instrument2 is not calibrated for groundstation2, instrument2 is not calibrated for groundstation5, instrument2 is not calibrated for groundstation9, instrument2 is not calibrated for phenomenon14, instrument2 is not calibrated for phenomenon15, instrument2 is not calibrated for star12, instrument2 is not powered on, instrument3 does not support image0, instrument3 does not support image5, instrument3 is not calibrated for groundstation0, instrument3 is not calibrated for groundstation5, instrument3 is not calibrated for groundstation7, instrument3 is not calibrated for phenomenon14, instrument3 is not calibrated for planet13, instrument3 is not calibrated for star3, instrument3 is not on board satellite1, instrument3 is not powered on, instrument4 is not calibrated, instrument4 is not calibrated for groundstation0, instrument4 is not calibrated for star12, instrument4 is not calibrated for star3, instrument4 is not switched on, phenomenon15 is not where satellite0 is pointed, planet11 is not where satellite1 is pointed, planet13 is not where satellite0 is pointed, power is not available for satellite0, satellite0 does not have instrument4 on board, satellite0 is not aimed towards groundstation2, satellite0 is not aimed towards groundstation9, satellite0 is not aimed towards phenomenon14, satellite0 is not aimed towards star12, satellite0 is not pointing to groundstation5, satellite0 is not pointing to groundstation6, satellite0 is not pointing to groundstation7, satellite0 is not pointing to groundstation8, satellite0 is not pointing to planet11, satellite1 does not carry instrument1 on board, satellite1 does not carry instrument2 on board, satellite1 is not aimed towards groundstation2, satellite1 is not aimed towards groundstation6, satellite1 is not aimed towards groundstation7, satellite1 is not aimed towards phenomenon14, satellite1 is not aimed towards phenomenon15, satellite1 is not pointing to groundstation5, satellite1 is not pointing to star16, spectrograph2 is not compatible with instrument3, spectrograph2 is not compatible with instrument4, spectrograph2 is not supported by instrument0, spectrograph4 is not compatible with instrument0, spectrograph4 is not compatible with instrument4, spectrograph4 is not supported by instrument1, spectrograph4 is not supported by instrument2, star10 is not where satellite1 is pointed, star12 is not where satellite1 is pointed, star16 is not where satellite0 is pointed, star3 is not where satellite0 is pointed, star3 is not where satellite1 is pointed, star4 is not where satellite0 is pointed, star4 is not where satellite1 is pointed, there is no image of direction groundstation0 in image0, there is no image of direction groundstation0 in image6, there is no image of direction groundstation0 in spectrograph2, there is no image of direction groundstation0 in spectrograph4, there is no image of direction groundstation0 in thermograph3, there is no image of direction groundstation1 in image1, there is no image of direction groundstation1 in spectrograph4, there is no image of direction groundstation1 in thermograph3, there is no image of direction groundstation2 in image0, there is no image of direction groundstation2 in spectrograph4, there is no image of direction groundstation2 in thermograph3, there is no image of direction groundstation5 in image1, there is no image of direction groundstation5 in image6, there is no image of direction groundstation5 in spectrograph2, there is no image of direction groundstation5 in spectrograph4, there is no image of direction groundstation5 in thermograph3, there is no image of direction groundstation6 in image0, there is no image of direction groundstation6 in image1, there is no image of direction groundstation6 in image5, there is no image of direction groundstation6 in image6, there is no image of direction groundstation6 in spectrograph2, there is no image of direction groundstation6 in thermograph3, there is no image of direction groundstation7 in image5, there is no image of direction groundstation7 in thermograph3, there is no image of direction groundstation8 in image0, there is no image of direction groundstation8 in image6, there is no image of direction groundstation8 in spectrograph4, there is no image of direction groundstation9 in image1, there is no image of direction groundstation9 in infrared7, there is no image of direction groundstation9 in spectrograph2, there is no image of direction groundstation9 in thermograph3, there is no image of direction phenomenon14 in image0, there is no image of direction phenomenon14 in image1, there is no image of direction phenomenon14 in image5, there is no image of direction phenomenon14 in image6, there is no image of direction phenomenon14 in infrared7, there is no image of direction phenomenon14 in spectrograph4, there is no image of direction phenomenon15 in image1, there is no image of direction phenomenon15 in image5, there is no image of direction phenomenon15 in infrared7, there is no image of direction phenomenon15 in spectrograph2, there is no image of direction phenomenon15 in spectrograph4, there is no image of direction phenomenon15 in thermograph3, there is no image of direction planet11 in image0, there is no image of direction planet11 in image1, there is no image of direction planet11 in infrared7, there is no image of direction planet11 in spectrograph2, there is no image of direction planet13 in image1, there is no image of direction star10 in image0, there is no image of direction star10 in image1, there is no image of direction star10 in image5, there is no image of direction star10 in spectrograph4, there is no image of direction star10 in thermograph3, there is no image of direction star12 in image1, there is no image of direction star12 in image6, there is no image of direction star12 in spectrograph4, there is no image of direction star16 in image1, there is no image of direction star16 in image5, there is no image of direction star16 in spectrograph2, there is no image of direction star3 in image1, there is no image of direction star3 in image5, there is no image of direction star3 in image6, there is no image of direction star3 in infrared7, there is no image of direction star4 in image5, there is no image of direction star4 in image6, there is no image of direction star4 in infrared7, thermograph3 is not compatible with instrument1, thermograph3 is not compatible with instrument4 and thermograph3 is not supported by instrument0", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument4 for groundstation8 is complete, for star3, instrument0 is calibrated, image0 is compatible with instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image5 is supported by instrument1, image6 is compatible with instrument1, image6 is supported by instrument0, instrument0 is calibrated for groundstation7, instrument1 is calibrated for groundstation0, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 is calibrated for groundstation9, instrument3 is on board satellite0, instrument3 supports image1, instrument4 is on board satellite1, instrument4 supports infrared7, power is available for satellite0, satellite0 carries instrument2 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is pointing to groundstation2, satellite1 has power, satellite1 is aimed towards planet13, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, satellite0 then reorients from groundstation0 to planet11, an image of planet11 is captured by instrument1 on satellite0 and stored in image5, another image of planet11 is taken using instrument1 on satellite0 and stored in image6, satellite0 reorients from planet11 to planet13, instrument1 on satellite0 captures an image of planet13 and stores it in image5, satellite0's instrument1 also captures an image of planet13 in spectrograph2, and finally, satellite0 reorients from planet13 to star10 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation6 has been finalized, and the calibration of instrument4 at groundstation8 is also complete. For star3, instrument0 has been calibrated. Image0 is found to be compatible with instrument2, while image1 is compatible with instrument4 and also supported by instrument2. Image5 is supported by instrument1, and image6 is both compatible with and supported by instrument1 and instrument0, respectively. Instrument0 has been calibrated for groundstation7, and instrument1 has been calibrated for groundstation0. Instrument1 is also capable of supporting spectrograph2. Instrument2 has been calibrated for star4, and instrument3 has been calibrated for groundstation9. Instrument3 is currently on board satellite0 and supports image1. Instrument4 is on board satellite1 and supports infrared7. Satellite0 has power available, and it carries instrument2, instrument0, and instrument1 on board. Satellite0 is currently pointing towards groundstation2. Satellite1 has power and is aimed at planet13. Additionally, spectrograph4 is compatible with instrument3, and thermograph3 is supported by instrument3."}
{"question_id": "706425b9-ce78-4f8c-8d6b-b1d28a30994d", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is switched on, from groundstation2, satellite0 turns to groundstation0, calibration of instrument1 which is on satellite0 to groundstation0 is complete, from groundstation0, satellite0 turns to planet11, satellite0's instrument1 takes an image of planet11 in image5, image of planet11 is taken with instrument1 on satellite0 in image6, from planet11, satellite0 turns to planet13, image of planet13 is taken with instrument1 on satellite0 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2, satellite0 turns from planet13 to star10, image of star10 is taken with instrument1 on satellite0 in image6, image of star10 is taken with instrument1 on satellite0 in spectrograph2, on satellite0, instrument1 is switched off, instrument2 that is on satellite0 is turned on, satellite0 turns from star10 to star4, instrument2 is calibrated on satellite0 to star4, satellite0 turns to star16 from star4, satellite0's instrument2 takes an image of star16 in image0 and instrument2 on satellite0 is switched off to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "calibration of instrument0 for groundstation1 is incomplete, calibration of instrument0 for groundstation2 is incomplete, calibration of instrument0 for planet11 is incomplete, calibration of instrument0 for star12 is incomplete, calibration of instrument0 for star16 is incomplete, calibration of instrument0 for star3 is complete, calibration of instrument0 is incomplete, calibration of instrument1 for groundstation0 is complete, calibration of instrument1 for groundstation1 is incomplete, calibration of instrument1 for groundstation2 is incomplete, calibration of instrument1 for phenomenon15 is incomplete, calibration of instrument1 for star4 is incomplete, calibration of instrument2 for groundstation6 is incomplete, calibration of instrument2 for groundstation7 is incomplete, calibration of instrument2 for star16 is incomplete, calibration of instrument2 for star3 is incomplete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation2 is incomplete, calibration of instrument3 for groundstation6 is incomplete, calibration of instrument3 for groundstation7 is incomplete, calibration of instrument3 for groundstation9 is complete, calibration of instrument3 for star4 is incomplete, calibration of instrument4 for groundstation5 is incomplete, calibration of instrument4 for groundstation7 is incomplete, calibration of instrument4 for phenomenon14 is incomplete, calibration of instrument4 for star3 is incomplete, for groundstation1, instrument3 is not calibrated, for groundstation2, instrument2 is not calibrated, for groundstation5, instrument0 is not calibrated, for groundstation5, instrument1 is not calibrated, for groundstation5, instrument3 is not calibrated, for groundstation7, instrument0 is calibrated, for groundstation8, instrument0 is not calibrated, for groundstation8, instrument1 is not calibrated, for groundstation8, instrument3 is not calibrated, for groundstation9, instrument1 is not calibrated, for groundstation9, instrument2 is not calibrated, for phenomenon14, instrument0 is not calibrated, for phenomenon14, instrument1 is not calibrated, for phenomenon14, instrument3 is not calibrated, for phenomenon15, instrument4 is not calibrated, for planet11, instrument1 is not calibrated, for planet13, instrument0 is not calibrated, for planet13, instrument2 is not calibrated, for planet13, instrument4 is not calibrated, for star10, instrument3 is not calibrated, for star16, instrument1 is not calibrated, for star3, instrument1 is not calibrated, groundstation0 is not where satellite1 is pointed, groundstation1 is not where satellite1 is pointed, groundstation5 is not where satellite1 is pointed, groundstation8 is not where satellite0 is pointed, groundstation8 is not where satellite1 is pointed, image of groundstation0 does not exist in image0, image of groundstation0 does not exist in image1, image of groundstation0 does not exist in image5, image of groundstation0 does not exist in spectrograph2, image of groundstation1 does not exist in image0, image of groundstation1 does not exist in image1, image of groundstation1 does not exist in image6, image of groundstation1 does not exist in spectrograph2, image of groundstation1 does not exist in spectrograph4, image of groundstation2 does not exist in image1, image of groundstation2 does not exist in image5, image of groundstation2 does not exist in spectrograph2, image of groundstation2 does not exist in spectrograph4, image of groundstation5 does not exist in image0, image of groundstation5 does not exist in thermograph3, image of groundstation6 does not exist in image0, image of groundstation6 does not exist in image1, image of groundstation6 does not exist in image6, image of groundstation6 does not exist in infrared7, image of groundstation6 does not exist in spectrograph2, image of groundstation6 does not exist in spectrograph4, image of groundstation7 does not exist in image1, image of groundstation7 does not exist in image5, image of groundstation7 does not exist in image6, image of groundstation7 does not exist in infrared7, image of groundstation7 does not exist in spectrograph2, image of groundstation7 does not exist in spectrograph4, image of groundstation7 does not exist in thermograph3, image of groundstation8 does not exist in image1, image of groundstation8 does not exist in infrared7, image of groundstation8 does not exist in spectrograph2, image of groundstation8 does not exist in spectrograph4, image of groundstation8 does not exist in thermograph3, image of groundstation9 does not exist in image0, image of phenomenon14 does not exist in image1, image of phenomenon14 does not exist in image5, image of phenomenon14 does not exist in image6, image of phenomenon14 does not exist in infrared7, image of phenomenon14 does not exist in spectrograph2, image of phenomenon14 does not exist in spectrograph4, image of phenomenon14 does not exist in thermograph3, image of phenomenon15 does not exist in image1, image of phenomenon15 does not exist in image5, image of phenomenon15 does not exist in infrared7, image of phenomenon15 does not exist in thermograph3, image of planet11 does not exist in infrared7, image of planet11 does not exist in spectrograph4, image of planet11 does not exist in thermograph3, image of planet11 exists in image6, image of planet13 does not exist in image0, image of planet13 does not exist in image1, image of planet13 does not exist in image6, image of planet13 does not exist in infrared7, image of planet13 does not exist in spectrograph4, image of planet13 exists in image5, image of planet13 exists in spectrograph2, image of star10 does not exist in image0, image of star10 does not exist in image5, image of star10 does not exist in infrared7, image of star10 does not exist in spectrograph4, image of star10 exists in image6, image of star10 exists in spectrograph2, image of star12 does not exist in image6, image of star12 does not exist in infrared7, image of star12 does not exist in spectrograph4, image of star16 does not exist in image5, image of star16 does not exist in image6, image of star16 does not exist in thermograph3, image of star16 exists in image0, image of star3 does not exist in image0, image of star3 does not exist in image1, image of star3 does not exist in image6, image of star3 does not exist in spectrograph4, image of star3 does not exist in thermograph3, image of star4 does not exist in image0, image of star4 does not exist in spectrograph4, image0 is not compatible with instrument1, image0 is not compatible with instrument4, image0 is not supported by instrument3, image1 is compatible with instrument2, image1 is not supported by instrument1, image1 is supported by instrument3, image5 is not compatible with instrument2, image5 is not supported by instrument0, image5 is supported by instrument1, image6 is compatible with instrument0, image6 is compatible with instrument1, image6 is not compatible with instrument2, image6 is not compatible with instrument3, infrared7 is not compatible with instrument2, infrared7 is not compatible with instrument3, infrared7 is not supported by instrument1, instrument0 does not support image0, instrument0 does not support image1, instrument0 does not support infrared7, instrument0 does not support spectrograph4, instrument0 does not support thermograph3, instrument0 is not calibrated for groundstation0, instrument0 is not calibrated for groundstation6, instrument0 is not calibrated for groundstation9, instrument0 is not calibrated for phenomenon15, instrument0 is not calibrated for star10, instrument0 is not calibrated for star4, instrument0 is not powered on, instrument1 does not support spectrograph4, instrument1 does not support thermograph3, instrument1 is calibrated, instrument1 is calibrated for groundstation6, instrument1 is not calibrated for groundstation7, instrument1 is not calibrated for planet13, instrument1 is not calibrated for star10, instrument1 is not calibrated for star12, instrument1 is not on board satellite1, instrument1 is not turned on, instrument2 is calibrated, instrument2 is not calibrated for groundstation0, instrument2 is not calibrated for groundstation1, instrument2 is not calibrated for groundstation5, instrument2 is not calibrated for groundstation8, instrument2 is not calibrated for phenomenon14, instrument2 is not calibrated for phenomenon15, instrument2 is not calibrated for planet11, instrument2 is not calibrated for star10, instrument2 is not calibrated for star12, instrument2 is not on board satellite1, instrument2 is not turned on, instrument2 is on board satellite0, instrument2 supports image0, instrument3 does not support image5, instrument3 is not calibrated, instrument3 is not calibrated for groundstation0, instrument3 is not calibrated for phenomenon15, instrument3 is not calibrated for planet11, instrument3 is not calibrated for planet13, instrument3 is not calibrated for star12, instrument3 is not calibrated for star16, instrument3 is not calibrated for star3, instrument3 is not on board satellite1, instrument3 is not powered on, instrument3 supports spectrograph4, instrument3 supports thermograph3, instrument4 does not support image5, instrument4 does not support image6, instrument4 is calibrated for groundstation8, instrument4 is not calibrated, instrument4 is not calibrated for groundstation0, instrument4 is not calibrated for groundstation1, instrument4 is not calibrated for groundstation2, instrument4 is not calibrated for groundstation6, instrument4 is not calibrated for groundstation9, instrument4 is not calibrated for planet11, instrument4 is not calibrated for star10, instrument4 is not calibrated for star12, instrument4 is not calibrated for star16, instrument4 is not calibrated for star4, instrument4 is not switched on, instrument4 is on board satellite1, instrument4 supports image1, instrument4 supports infrared7, phenomenon14 is not where satellite1 is pointed, planet11 is not where satellite0 is pointed, satellite0 carries instrument3 on board, satellite0 does not carry instrument4 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 has power, satellite0 is aimed towards star16, satellite0 is not aimed towards groundstation6, satellite0 is not aimed towards groundstation7, satellite0 is not aimed towards groundstation9, satellite0 is not aimed towards phenomenon15, satellite0 is not aimed towards planet13, satellite0 is not aimed towards star10, satellite0 is not aimed towards star4, satellite0 is not pointing to groundstation0, satellite0 is not pointing to groundstation1, satellite0 is not pointing to groundstation2, satellite0 is not pointing to groundstation5, satellite0 is not pointing to phenomenon14, satellite0 is not pointing to star12, satellite1 does not have instrument0 on board, satellite1 has power available, satellite1 is aimed towards planet13, satellite1 is not aimed towards groundstation6, satellite1 is not aimed towards phenomenon15, satellite1 is not aimed towards planet11, satellite1 is not aimed towards star10, satellite1 is not aimed towards star12, satellite1 is not pointing to groundstation2, satellite1 is not pointing to groundstation7, satellite1 is not pointing to groundstation9, satellite1 is not pointing to star3, spectrograph2 is compatible with instrument1, spectrograph2 is not compatible with instrument2, spectrograph2 is not compatible with instrument3, spectrograph2 is not compatible with instrument4, spectrograph2 is not supported by instrument0, spectrograph4 is not compatible with instrument4, spectrograph4 is not supported by instrument2, star16 is not where satellite1 is pointed, star3 is not where satellite0 is pointed, star4 is not where satellite1 is pointed, there is an image of planet11 in image5, there is no image of direction groundstation0 in image6, there is no image of direction groundstation0 in infrared7, there is no image of direction groundstation0 in spectrograph4, there is no image of direction groundstation0 in thermograph3, there is no image of direction groundstation1 in image5, there is no image of direction groundstation1 in infrared7, there is no image of direction groundstation1 in thermograph3, there is no image of direction groundstation2 in image0, there is no image of direction groundstation2 in image6, there is no image of direction groundstation2 in infrared7, there is no image of direction groundstation2 in thermograph3, there is no image of direction groundstation5 in image1, there is no image of direction groundstation5 in image5, there is no image of direction groundstation5 in image6, there is no image of direction groundstation5 in infrared7, there is no image of direction groundstation5 in spectrograph2, there is no image of direction groundstation5 in spectrograph4, there is no image of direction groundstation6 in image5, there is no image of direction groundstation6 in thermograph3, there is no image of direction groundstation7 in image0, there is no image of direction groundstation8 in image0, there is no image of direction groundstation8 in image5, there is no image of direction groundstation8 in image6, there is no image of direction groundstation9 in image1, there is no image of direction groundstation9 in image5, there is no image of direction groundstation9 in image6, there is no image of direction groundstation9 in infrared7, there is no image of direction groundstation9 in spectrograph2, there is no image of direction groundstation9 in spectrograph4, there is no image of direction groundstation9 in thermograph3, there is no image of direction phenomenon14 in image0, there is no image of direction phenomenon15 in image0, there is no image of direction phenomenon15 in image6, there is no image of direction phenomenon15 in spectrograph2, there is no image of direction phenomenon15 in spectrograph4, there is no image of direction planet11 in image0, there is no image of direction planet11 in image1, there is no image of direction planet11 in spectrograph2, there is no image of direction planet13 in thermograph3, there is no image of direction star10 in image1, there is no image of direction star10 in thermograph3, there is no image of direction star12 in image0, there is no image of direction star12 in image1, there is no image of direction star12 in image5, there is no image of direction star12 in spectrograph2, there is no image of direction star12 in thermograph3, there is no image of direction star16 in image1, there is no image of direction star16 in infrared7, there is no image of direction star16 in spectrograph2, there is no image of direction star16 in spectrograph4, there is no image of direction star3 in image5, there is no image of direction star3 in infrared7, there is no image of direction star3 in spectrograph2, there is no image of direction star4 in image1, there is no image of direction star4 in image5, there is no image of direction star4 in image6, there is no image of direction star4 in infrared7, there is no image of direction star4 in spectrograph2, there is no image of direction star4 in thermograph3, thermograph3 is not supported by instrument2 and thermograph3 is not supported by instrument4", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument4 for groundstation8 is complete, for star3, instrument0 is calibrated, image0 is compatible with instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image5 is supported by instrument1, image6 is compatible with instrument1, image6 is supported by instrument0, instrument0 is calibrated for groundstation7, instrument1 is calibrated for groundstation0, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 is calibrated for groundstation9, instrument3 is on board satellite0, instrument3 supports image1, instrument4 is on board satellite1, instrument4 supports infrared7, power is available for satellite0, satellite0 carries instrument2 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is pointing to groundstation2, satellite1 has power, satellite1 is aimed towards planet13, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated with respect to groundstation0, satellite0 then reorients from groundstation0 to planet11, an image of planet11 is captured by instrument1 on satellite0 and stored in image5 and image6, satellite0 reorients from planet11 to planet13, an image of planet13 is captured by instrument1 on satellite0 and stored in image5 and spectrograph2, satellite0 then reorients from planet13 to star10, an image of star10 is captured by instrument1 on satellite0 and stored in image6 and spectrograph2, instrument1 on satellite0 is deactivated, instrument2 on satellite0 is activated, satellite0 reorients from star10 to star4, instrument2 on satellite0 is calibrated with respect to star4, satellite0 then reorients from star4 to star16, an image of star16 is captured by instrument2 on satellite0 and stored in image0, and finally instrument2 on satellite0 is deactivated to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation6 has been finalized, and the calibration of instrument4 at groundstation8 is also complete. For star3, instrument0 has been successfully calibrated. Image0 is found to be compatible with instrument2, while image1 is compatible with instrument4 and also supported by instrument2. Image5 is supported by instrument1, and image6 is both compatible with and supported by instrument1 and instrument0, respectively. Instrument0 has been calibrated for groundstation7, and instrument1 has been calibrated for groundstation0. Instrument1 is also capable of supporting spectrograph2. Instrument2 has been calibrated for star4, and instrument3 has been calibrated for groundstation9. Instrument3 is currently on board satellite0 and supports image1. Instrument4 is on board satellite1 and supports infrared7. Satellite0 has power available, and it carries instrument2 on board, in addition to having instrument0 and instrument1. Satellite0 is currently pointing towards groundstation2. Satellite1 has power and is aimed towards planet13. Furthermore, spectrograph4 is compatible with instrument3, and thermograph3 is supported by instrument3."}
{"question_id": "47c580cf-03bc-4a1a-b1a6-39b287c6eab0", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite1 is turned on, satellite1 turns to star1 from star3, instrument1 that is on satellite1 is calibrated to star1, satellite1 turns to phenomenon10 from star1, image of phenomenon10 is taken with instrument1 on satellite1 in image5, instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3, satellite1 turns from phenomenon10 to phenomenon11, image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, satellite1 turns to phenomenon5 from phenomenon11 and image of phenomenon5 is taken with instrument1 on satellite1 in image4 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "calibration of instrument0 for groundstation2 is incomplete, calibration of instrument0 for phenomenon10 is incomplete, calibration of instrument0 for phenomenon11 is incomplete, calibration of instrument0 for phenomenon5 is incomplete, calibration of instrument0 for planet8 is incomplete, calibration of instrument0 for star6 is incomplete, calibration of instrument1 for groundstation0 is incomplete, calibration of instrument1 for phenomenon5 is incomplete, calibration of instrument1 for star1 is complete, calibration of instrument1 for star3 is incomplete, calibration of instrument1 for star6 is incomplete, for groundstation2, instrument1 is not calibrated, for groundstation4, instrument0 is not calibrated, for phenomenon11, instrument1 is not calibrated, for phenomenon7, instrument1 is not calibrated, for phenomenon9, instrument1 is not calibrated, for star1, instrument0 is calibrated, groundstation0 is not where satellite0 is pointed, groundstation0 is not where satellite1 is pointed, image of groundstation0 does not exist in image0, image of groundstation0 does not exist in image2, image of groundstation0 does not exist in image5, image of groundstation0 does not exist in spectrograph1, image of groundstation0 does not exist in spectrograph3, image of groundstation2 does not exist in image0, image of groundstation2 does not exist in image5, image of groundstation4 does not exist in image0, image of groundstation4 does not exist in image4, image of groundstation4 does not exist in image5, image of phenomenon10 does not exist in image0, image of phenomenon10 exists in spectrograph3, image of phenomenon11 does not exist in image0, image of phenomenon11 does not exist in image2, image of phenomenon11 does not exist in spectrograph3, image of phenomenon5 does not exist in image5, image of phenomenon5 does not exist in spectrograph1, image of phenomenon5 does not exist in spectrograph3, image of phenomenon7 does not exist in spectrograph3, image of phenomenon9 does not exist in image2, image of phenomenon9 does not exist in image4, image of phenomenon9 does not exist in image5, image of phenomenon9 does not exist in spectrograph3, image of planet8 does not exist in image0, image of planet8 does not exist in image2, image of planet8 does not exist in image4, image of planet8 does not exist in image5, image of planet8 does not exist in spectrograph1, image of planet8 does not exist in spectrograph3, image of star1 does not exist in image0, image of star1 does not exist in image4, image of star1 does not exist in image5, image of star1 does not exist in spectrograph3, image of star3 does not exist in image2, image of star3 does not exist in image4, image of star3 does not exist in spectrograph3, image of star6 does not exist in image5, image of star6 does not exist in spectrograph3, image0 is not compatible with instrument0, image0 is supported by instrument1, image2 is compatible with instrument0, image5 is supported by instrument1, instrument0 does not support image4, instrument0 does not support image5, instrument0 is not calibrated, instrument0 is not calibrated for groundstation0, instrument0 is not calibrated for phenomenon7, instrument0 is not calibrated for phenomenon9, instrument0 is not calibrated for star3, instrument0 is not powered on, instrument0 is on board satellite0, instrument1 is calibrated, instrument1 is not calibrated for groundstation4, instrument1 is not calibrated for phenomenon10, instrument1 is not calibrated for planet8, instrument1 is powered on, instrument1 supports image2, instrument1 supports image4, phenomenon10 is not where satellite1 is pointed, phenomenon11 is not where satellite0 is pointed, phenomenon9 is not where satellite0 is pointed, phenomenon9 is not where satellite1 is pointed, planet8 is not where satellite0 is pointed, satellite0 does not carry instrument1 on board, satellite0 has power available, satellite0 is not aimed towards groundstation4, satellite0 is not aimed towards phenomenon10, satellite0 is not aimed towards star1, satellite0 is not aimed towards star3, satellite0 is not pointing to groundstation2, satellite0 is not pointing to phenomenon7, satellite0 is pointing to phenomenon5, satellite1 carries instrument1 on board, satellite1 does not have instrument0 on board, satellite1 does not have power, satellite1 is not aimed towards groundstation4, satellite1 is not aimed towards planet8, satellite1 is not aimed towards star1, satellite1 is not pointing to groundstation2, satellite1 is not pointing to phenomenon11, satellite1 is not pointing to phenomenon7, satellite1 is not pointing to star3, satellite1 is not pointing to star6, satellite1 is pointing to phenomenon5, spectrograph1 is compatible with instrument1, spectrograph1 is not supported by instrument0, spectrograph3 is compatible with instrument1, spectrograph3 is not compatible with instrument0, star6 is not where satellite0 is pointed, there is an image of phenomenon10 in image5, there is an image of phenomenon11 in spectrograph1, there is an image of phenomenon5 in image4, there is no image of direction groundstation0 in image4, there is no image of direction groundstation2 in image2, there is no image of direction groundstation2 in image4, there is no image of direction groundstation2 in spectrograph1, there is no image of direction groundstation2 in spectrograph3, there is no image of direction groundstation4 in image2, there is no image of direction groundstation4 in spectrograph1, there is no image of direction groundstation4 in spectrograph3, there is no image of direction phenomenon10 in image2, there is no image of direction phenomenon10 in image4, there is no image of direction phenomenon10 in spectrograph1, there is no image of direction phenomenon11 in image4, there is no image of direction phenomenon11 in image5, there is no image of direction phenomenon5 in image0, there is no image of direction phenomenon5 in image2, there is no image of direction phenomenon7 in image0, there is no image of direction phenomenon7 in image2, there is no image of direction phenomenon7 in image4, there is no image of direction phenomenon7 in image5, there is no image of direction phenomenon7 in spectrograph1, there is no image of direction phenomenon9 in image0, there is no image of direction phenomenon9 in spectrograph1, there is no image of direction star1 in image2, there is no image of direction star1 in spectrograph1, there is no image of direction star3 in image0, there is no image of direction star3 in image5, there is no image of direction star3 in spectrograph1, there is no image of direction star6 in image0, there is no image of direction star6 in image2, there is no image of direction star6 in image4 and there is no image of direction star6 in spectrograph1", "plan_length": 10, "initial_state_nl": "For star1, instrument1 is calibrated, image0 is compatible with instrument1, image2 is compatible with instrument0, image2 is supported by instrument1, image4 is supported by instrument1, instrument0 is calibrated for star1, instrument1 supports image5, power is available for satellite1, satellite0 has instrument0 on board, satellite0 has power available, satellite0 is aimed towards phenomenon5, satellite1 has instrument1 on board, satellite1 is pointing to star3, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the instrument1 on satellite1 is activated, satellite1 reorients from star3 to star1, instrument1 on satellite1 is calibrated to star1, satellite1 then reorients from star1 to phenomenon10, an image of phenomenon10 is captured using instrument1 on satellite1 and stored in image5, instrument1 on satellite1 also captures an image of phenomenon10 in spectrograph3, satellite1 reorients from phenomenon10 to phenomenon11, an image of phenomenon11 is captured using instrument1 on satellite1 and stored in spectrograph1, satellite1 then reorients from phenomenon11 to phenomenon5, and an image of phenomenon5 is captured using instrument1 on satellite1 and stored in image4, resulting in the current state. In this state, list all valid properties (including both affirmative and negated properties). If there are no valid properties, indicate None.", "initial_state_nl_paraphrased": "For star1, instrument1 is calibrated, image0 is instrument1-compatible, image2 is instrument0-compatible, image2 is instrument1-supported, image4 is instrument1-supported, instrument0 is star1-calibrated, instrument1 supports image5, power is available for satellite1, satellite0 has instrument0 onboard, satellite0 has available power, satellite0 is aimed at phenomenon5, satellite1 has instrument1 onboard, satellite1 is pointing to star3, spectrograph1 is instrument1-compatible and spectrograph3 is instrument1-supported."}
{"question_id": "28935ee6-9f56-4cf6-b052-c1b520405984", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is switched on to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "calibration of instrument0 for groundstation0 is incomplete, calibration of instrument0 for groundstation2 is incomplete, calibration of instrument0 for groundstation5 is incomplete, calibration of instrument0 for phenomenon14 is incomplete, calibration of instrument0 for phenomenon15 is incomplete, calibration of instrument0 for planet11 is incomplete, calibration of instrument0 for planet13 is incomplete, calibration of instrument0 for star10 is incomplete, calibration of instrument0 is incomplete, calibration of instrument1 for groundstation7 is incomplete, calibration of instrument1 for phenomenon14 is incomplete, calibration of instrument1 for star12 is incomplete, calibration of instrument1 for star16 is incomplete, calibration of instrument1 is incomplete, calibration of instrument2 for groundstation0 is incomplete, calibration of instrument2 for groundstation1 is incomplete, calibration of instrument2 for groundstation2 is incomplete, calibration of instrument2 for groundstation7 is incomplete, calibration of instrument2 for groundstation9 is incomplete, calibration of instrument2 for star10 is incomplete, calibration of instrument2 for star3 is incomplete, calibration of instrument2 is incomplete, calibration of instrument3 for groundstation0 is incomplete, calibration of instrument3 for groundstation2 is incomplete, calibration of instrument3 for groundstation8 is incomplete, calibration of instrument3 for phenomenon14 is incomplete, calibration of instrument3 for phenomenon15 is incomplete, calibration of instrument4 for groundstation7 is incomplete, calibration of instrument4 for groundstation9 is incomplete, calibration of instrument4 for planet13 is incomplete, calibration of instrument4 for star16 is incomplete, for groundstation1, instrument1 is not calibrated, for groundstation1, instrument4 is not calibrated, for groundstation5, instrument2 is not calibrated, for groundstation6, instrument2 is not calibrated, for groundstation6, instrument3 is not calibrated, for groundstation6, instrument4 is not calibrated, for groundstation7, instrument3 is not calibrated, for groundstation8, instrument2 is not calibrated, for groundstation9, instrument1 is not calibrated, for phenomenon15, instrument1 is not calibrated, for phenomenon15, instrument2 is not calibrated, for planet11, instrument1 is not calibrated, for planet13, instrument2 is not calibrated, for planet13, instrument3 is not calibrated, for star10, instrument1 is not calibrated, for star12, instrument0 is not calibrated, for star12, instrument2 is not calibrated, for star12, instrument4 is not calibrated, for star16, instrument2 is not calibrated, for star16, instrument3 is not calibrated, for star3, instrument3 is not calibrated, for star4, instrument0 is not calibrated, for star4, instrument3 is not calibrated, for star4, instrument4 is not calibrated, groundstation0 is not where satellite0 is pointed, groundstation1 is not where satellite1 is pointed, groundstation5 is not where satellite0 is pointed, groundstation7 is not where satellite0 is pointed, groundstation8 is not where satellite1 is pointed, image of groundstation0 does not exist in image0, image of groundstation0 does not exist in image5, image of groundstation0 does not exist in spectrograph2, image of groundstation0 does not exist in spectrograph4, image of groundstation0 does not exist in thermograph3, image of groundstation1 does not exist in image1, image of groundstation1 does not exist in image5, image of groundstation1 does not exist in infrared7, image of groundstation1 does not exist in spectrograph2, image of groundstation1 does not exist in spectrograph4, image of groundstation1 does not exist in thermograph3, image of groundstation2 does not exist in image0, image of groundstation2 does not exist in image1, image of groundstation2 does not exist in image5, image of groundstation2 does not exist in spectrograph4, image of groundstation2 does not exist in thermograph3, image of groundstation5 does not exist in image0, image of groundstation5 does not exist in image1, image of groundstation5 does not exist in image6, image of groundstation5 does not exist in infrared7, image of groundstation5 does not exist in spectrograph2, image of groundstation5 does not exist in spectrograph4, image of groundstation6 does not exist in image1, image of groundstation6 does not exist in image5, image of groundstation6 does not exist in image6, image of groundstation6 does not exist in spectrograph4, image of groundstation6 does not exist in thermograph3, image of groundstation7 does not exist in image0, image of groundstation7 does not exist in image1, image of groundstation7 does not exist in image5, image of groundstation7 does not exist in image6, image of groundstation7 does not exist in thermograph3, image of groundstation8 does not exist in image0, image of groundstation8 does not exist in image5, image of groundstation8 does not exist in image6, image of groundstation8 does not exist in infrared7, image of groundstation8 does not exist in spectrograph4, image of groundstation9 does not exist in image5, image of groundstation9 does not exist in image6, image of groundstation9 does not exist in spectrograph2, image of phenomenon14 does not exist in image6, image of phenomenon14 does not exist in infrared7, image of phenomenon14 does not exist in spectrograph4, image of phenomenon14 does not exist in thermograph3, image of phenomenon15 does not exist in infrared7, image of phenomenon15 does not exist in spectrograph4, image of phenomenon15 does not exist in thermograph3, image of planet11 does not exist in image0, image of planet11 does not exist in image6, image of planet11 does not exist in spectrograph2, image of planet11 does not exist in thermograph3, image of planet13 does not exist in image1, image of planet13 does not exist in image5, image of planet13 does not exist in image6, image of planet13 does not exist in infrared7, image of planet13 does not exist in spectrograph2, image of star10 does not exist in image1, image of star10 does not exist in image5, image of star10 does not exist in image6, image of star10 does not exist in infrared7, image of star10 does not exist in spectrograph4, image of star12 does not exist in image5, image of star12 does not exist in infrared7, image of star12 does not exist in spectrograph4, image of star16 does not exist in image1, image of star16 does not exist in image6, image of star16 does not exist in infrared7, image of star16 does not exist in spectrograph2, image of star16 does not exist in spectrograph4, image of star16 does not exist in thermograph3, image of star3 does not exist in image0, image of star3 does not exist in image1, image of star3 does not exist in image5, image of star3 does not exist in image6, image of star3 does not exist in spectrograph4, image of star3 does not exist in thermograph3, image of star4 does not exist in image1, image of star4 does not exist in spectrograph4, image of star4 does not exist in thermograph3, image0 is not compatible with instrument4, image0 is not supported by instrument0, image0 is not supported by instrument1, image1 is not supported by instrument0, image5 is not compatible with instrument2, image6 is not compatible with instrument4, image6 is not supported by instrument2, image6 is not supported by instrument3, infrared7 is not compatible with instrument1, infrared7 is not compatible with instrument3, infrared7 is not supported by instrument0, infrared7 is not supported by instrument2, instrument0 does not support image5, instrument0 is not calibrated for groundstation1, instrument0 is not calibrated for groundstation6, instrument0 is not calibrated for groundstation8, instrument0 is not calibrated for groundstation9, instrument0 is not calibrated for star16, instrument0 is not powered on, instrument1 does not support image1, instrument1 is not calibrated for groundstation2, instrument1 is not calibrated for groundstation5, instrument1 is not calibrated for groundstation8, instrument1 is not calibrated for planet13, instrument1 is not calibrated for star3, instrument1 is not calibrated for star4, instrument2 is not calibrated for phenomenon14, instrument2 is not calibrated for planet11, instrument2 is not switched on, instrument3 does not support image0, instrument3 does not support image5, instrument3 is not calibrated, instrument3 is not calibrated for groundstation1, instrument3 is not calibrated for groundstation5, instrument3 is not calibrated for planet11, instrument3 is not calibrated for star10, instrument3 is not calibrated for star12, instrument3 is not turned on, instrument4 does not support image5, instrument4 does not support spectrograph2, instrument4 is not calibrated, instrument4 is not calibrated for groundstation0, instrument4 is not calibrated for groundstation2, instrument4 is not calibrated for groundstation5, instrument4 is not calibrated for phenomenon14, instrument4 is not calibrated for phenomenon15, instrument4 is not calibrated for planet11, instrument4 is not calibrated for star10, instrument4 is not calibrated for star3, instrument4 is not powered on, phenomenon14 is not where satellite0 is pointed, phenomenon14 is not where satellite1 is pointed, phenomenon15 is not where satellite1 is pointed, satellite0 does not have instrument4 on board, satellite0 does not have power available, satellite0 is not aimed towards groundstation9, satellite0 is not aimed towards star10, satellite0 is not pointing to groundstation1, satellite0 is not pointing to groundstation6, satellite0 is not pointing to groundstation8, satellite0 is not pointing to phenomenon15, satellite0 is not pointing to planet11, satellite0 is not pointing to planet13, satellite0 is not pointing to star12, satellite0 is not pointing to star16, satellite0 is not pointing to star3, satellite0 is not pointing to star4, satellite1 does not carry instrument2 on board, satellite1 does not have instrument0 on board, satellite1 does not have instrument1 on board, satellite1 does not have instrument3 on board, satellite1 is not aimed towards groundstation2, satellite1 is not aimed towards groundstation5, satellite1 is not aimed towards groundstation6, satellite1 is not aimed towards planet11, satellite1 is not aimed towards star12, satellite1 is not aimed towards star4, satellite1 is not pointing to groundstation0, satellite1 is not pointing to groundstation7, satellite1 is not pointing to groundstation9, satellite1 is not pointing to star10, satellite1 is not pointing to star3, spectrograph2 is not compatible with instrument2, spectrograph2 is not supported by instrument0, spectrograph2 is not supported by instrument3, spectrograph4 is not compatible with instrument0, spectrograph4 is not compatible with instrument1, spectrograph4 is not compatible with instrument2, spectrograph4 is not supported by instrument4, star16 is not where satellite1 is pointed, there is no image of direction groundstation0 in image1, there is no image of direction groundstation0 in image6, there is no image of direction groundstation0 in infrared7, there is no image of direction groundstation1 in image0, there is no image of direction groundstation1 in image6, there is no image of direction groundstation2 in image6, there is no image of direction groundstation2 in infrared7, there is no image of direction groundstation2 in spectrograph2, there is no image of direction groundstation5 in image5, there is no image of direction groundstation5 in thermograph3, there is no image of direction groundstation6 in image0, there is no image of direction groundstation6 in infrared7, there is no image of direction groundstation6 in spectrograph2, there is no image of direction groundstation7 in infrared7, there is no image of direction groundstation7 in spectrograph2, there is no image of direction groundstation7 in spectrograph4, there is no image of direction groundstation8 in image1, there is no image of direction groundstation8 in spectrograph2, there is no image of direction groundstation8 in thermograph3, there is no image of direction groundstation9 in image0, there is no image of direction groundstation9 in image1, there is no image of direction groundstation9 in infrared7, there is no image of direction groundstation9 in spectrograph4, there is no image of direction groundstation9 in thermograph3, there is no image of direction phenomenon14 in image0, there is no image of direction phenomenon14 in image1, there is no image of direction phenomenon14 in image5, there is no image of direction phenomenon14 in spectrograph2, there is no image of direction phenomenon15 in image0, there is no image of direction phenomenon15 in image1, there is no image of direction phenomenon15 in image5, there is no image of direction phenomenon15 in image6, there is no image of direction phenomenon15 in spectrograph2, there is no image of direction planet11 in image1, there is no image of direction planet11 in image5, there is no image of direction planet11 in infrared7, there is no image of direction planet11 in spectrograph4, there is no image of direction planet13 in image0, there is no image of direction planet13 in spectrograph4, there is no image of direction planet13 in thermograph3, there is no image of direction star10 in image0, there is no image of direction star10 in spectrograph2, there is no image of direction star10 in thermograph3, there is no image of direction star12 in image0, there is no image of direction star12 in image1, there is no image of direction star12 in image6, there is no image of direction star12 in spectrograph2, there is no image of direction star12 in thermograph3, there is no image of direction star16 in image0, there is no image of direction star16 in image5, there is no image of direction star3 in infrared7, there is no image of direction star3 in spectrograph2, there is no image of direction star4 in image0, there is no image of direction star4 in image5, there is no image of direction star4 in image6, there is no image of direction star4 in infrared7, there is no image of direction star4 in spectrograph2, thermograph3 is not compatible with instrument0, thermograph3 is not compatible with instrument2, thermograph3 is not supported by instrument1 and thermograph3 is not supported by instrument4", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument4 for groundstation8 is complete, for star3, instrument0 is calibrated, image0 is compatible with instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image5 is supported by instrument1, image6 is compatible with instrument1, image6 is supported by instrument0, instrument0 is calibrated for groundstation7, instrument1 is calibrated for groundstation0, instrument1 supports spectrograph2, instrument2 is calibrated for star4, instrument3 is calibrated for groundstation9, instrument3 is on board satellite0, instrument3 supports image1, instrument4 is on board satellite1, instrument4 supports infrared7, power is available for satellite0, satellite0 carries instrument2 on board, satellite0 has instrument0 on board, satellite0 has instrument1 on board, satellite0 is pointing to groundstation2, satellite1 has power, satellite1 is aimed towards planet13, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: instrument1 on satellite0 is activated to achieve the current state. In this state, identify and list all valid properties that include negations; if none exist, indicate None.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation6 has been finalized, and the calibration of instrument4 at groundstation8 is also complete. For star3, instrument0 has been calibrated. Image0 is found to be compatible with instrument2, while image1 is compatible with instrument4 and also supported by instrument2. Image5 is supported by instrument1, and image6 is compatible with instrument1 and supported by instrument0. Instrument0 has been calibrated for groundstation7, and instrument1 has been calibrated for groundstation0. Instrument1 is capable of supporting spectrograph2. Instrument2 has been calibrated for star4, and instrument3 has been calibrated for groundstation9. Instrument3 is currently on board satellite0 and supports image1. Instrument4 is on board satellite1 and supports infrared7. Satellite0 has power available, and it carries instrument2 on board, in addition to having instrument0 and instrument1 on board. Satellite0 is currently pointing towards groundstation2. Satellite1 has power and is aimed towards planet13. Spectrograph4 is compatible with instrument3, and thermograph3 is supported by instrument3."}
