{"question_id": "a4f8f30c-f07c-45fb-9bce-8bbe73910660", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on, instrument0 on satellite0 is switched on, from phenomenon10, satellite1 turns to groundstation5, instrument3 is calibrated on satellite1 to groundstation5, satellite1 turns from groundstation5 to phenomenon16, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, satellite1 turns to phenomenon17 from phenomenon16, instrument3 which is on satellite1 takes an image of phenomenon17 in image3, satellite1 turns from phenomenon17 to planet11 and image of planet11 is taken with instrument3 on satellite1 in image3 to reach the current state. In this state, if satellite1 turns to planet13 from planet11, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "calibration of instrument1 for groundstation0 is complete, calibration of instrument3 for star8 is complete, calibration of instrument3 is complete, for groundstation5, instrument3 is calibrated, for star1, instrument0 is calibrated, image of phenomenon16 exists in image3, image of phenomenon17 exists in image3, image0 is supported by instrument1, image0 is supported by instrument3, image2 is supported by instrument2, image3 is compatible with instrument0, image3 is compatible with instrument2, image3 is compatible with instrument3, infrared1 is supported by instrument1, instrument0 is calibrated for star9, instrument0 is on board satellite0, instrument0 is powered on, instrument0 supports infrared1, instrument1 supports image3, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument2 is calibrated for star9, instrument3 is calibrated for star6, instrument3 is switched on, instrument3 supports image2, satellite0 carries instrument1 on board, satellite0 has instrument2 on board, satellite0 is pointing to groundstation3, satellite1 has instrument3 on board, satellite1 is pointing to planet13 and there is an image of planet11 in image3", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image2 is compatible with instrument2, image2 is supported by instrument3, image3 is compatible with instrument3, image3 is supported by instrument2, infrared1 is compatible with instrument1, instrument0 is on board satellite0, instrument0 supports image3, instrument0 supports infrared1, instrument1 supports image3, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 is activated on satellite1, instrument0 is activated on satellite0, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated with respect to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, instrument3 on satellite1 captures an image of phenomenon16 in image3, satellite1 reorients from phenomenon16 to phenomenon17, instrument3 on satellite1 captures an image of phenomenon17 in image3, and finally, satellite1 reorients from phenomenon17 to planet11, capturing an image of planet11 with instrument3 in image3, resulting in the current state. In this state, if satellite1 reorients from planet11 to planet13, what are all the valid properties of the resulting state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is now complete, and instrument3's calibration for star6 is also complete. For star1, instrument0 has been calibrated, while instrument3 has been calibrated for star8, and instrument0 for star9. The satellite0 is currently directed towards groundstation3. Image0 is compatible with instrument1, image2 is compatible with instrument2, and also supported by instrument3. Image3 is compatible with instrument3 and supported by instrument2. Infrared1 is compatible with instrument1. Instrument0 is installed on satellite0, and it supports both image3 and infrared1. Instrument1 also supports image3. Instrument2 has been calibrated for groundstation5 and groundstation7, and instrument3 has been calibrated for groundstation5. Instrument3 supports image0. Satellite0 has instrument1 and instrument2 on board, and it has power available. Satellite1 has instrument3 on board, has power available, and is currently pointing towards phenomenon10."}
{"question_id": "c2a5ff85-c202-4bd1-a708-6c56a99150ca", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, instrument0 that is on satellite0 is turned on, satellite1 turns to groundstation5 from phenomenon10, instrument3 that is on satellite1 is calibrated to groundstation5, satellite1 turns from groundstation5 to phenomenon16, image of phenomenon16 is taken with instrument3 on satellite1 in image3, satellite1 turns from phenomenon16 to phenomenon17, image of phenomenon17 is taken with instrument3 on satellite1 in image3, from phenomenon17, satellite1 turns to planet11, satellite1's instrument3 takes an image of planet11 in image3, from planet11, satellite1 turns to planet13, satellite1's instrument3 takes an image of planet13 in image0, satellite1 turns from planet13 to planet14, instrument3 which is on satellite1 takes an image of planet14 in image0, from planet14, satellite1 turns to star15, satellite1's instrument3 takes an image of star15 in image2, satellite0 turns to star1 from groundstation3, instrument0 is calibrated on satellite0 to star1 and satellite0 turns to phenomenon10 from star1 to reach the current state. In this state, if satellite0's instrument0 takes an image of phenomenon10 in infrared1, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "calibration of instrument0 for groundstation0 is incomplete, calibration of instrument0 for star8 is incomplete, calibration of instrument1 for groundstation3 is incomplete, calibration of instrument1 for groundstation5 is incomplete, calibration of instrument1 for groundstation7 is incomplete, calibration of instrument1 for phenomenon16 is incomplete, calibration of instrument1 for phenomenon17 is incomplete, calibration of instrument1 for planet11 is incomplete, calibration of instrument1 for planet12 is incomplete, calibration of instrument1 for planet14 is incomplete, calibration of instrument1 for star6 is incomplete, calibration of instrument2 for groundstation0 is incomplete, calibration of instrument2 for planet11 is incomplete, calibration of instrument2 for planet13 is incomplete, calibration of instrument2 for star1 is incomplete, calibration of instrument2 is incomplete, calibration of instrument3 for groundstation4 is incomplete, calibration of instrument3 for phenomenon10 is incomplete, for groundstation0, instrument3 is not calibrated, for groundstation2, instrument0 is not calibrated, for groundstation3, instrument2 is not calibrated, for groundstation5, instrument0 is not calibrated, for phenomenon16, instrument0 is not calibrated, for phenomenon17, instrument2 is not calibrated, for planet11, instrument0 is not calibrated, for planet12, instrument3 is not calibrated, for planet13, instrument1 is not calibrated, for planet14, instrument3 is not calibrated, for star15, instrument0 is not calibrated, for star15, instrument1 is not calibrated, for star15, instrument3 is not calibrated, for star6, instrument0 is not calibrated, for star8, instrument2 is not calibrated, for star9, instrument3 is not calibrated, groundstation2 is not where satellite0 is pointed, groundstation4 is not where satellite1 is pointed, groundstation5 is not where satellite0 is pointed, image of groundstation0 does not exist in image2, image of groundstation0 does not exist in image3, image of groundstation2 does not exist in image0, image of groundstation2 does not exist in image2, image of groundstation3 does not exist in image0, image of groundstation3 does not exist in image3, image of groundstation4 does not exist in image2, image of groundstation4 does not exist in infrared1, image of groundstation5 does not exist in image0, image of groundstation5 does not exist in image2, image of groundstation5 does not exist in image3, image of groundstation5 does not exist in infrared1, image of groundstation7 does not exist in image0, image of phenomenon17 does not exist in image0, image of phenomenon17 does not exist in image2, image of phenomenon17 does not exist in infrared1, image of planet11 does not exist in image0, image of planet12 does not exist in image0, image of planet12 does not exist in image3, image of planet13 does not exist in image2, image of planet13 does not exist in image3, image of planet13 does not exist in infrared1, image of planet14 does not exist in image2, image of planet14 does not exist in infrared1, image of star1 does not exist in image0, image of star15 does not exist in image0, image of star15 does not exist in image3, image of star15 does not exist in infrared1, image of star6 does not exist in image0, image of star6 does not exist in image3, image of star6 does not exist in infrared1, image of star8 does not exist in image3, image of star9 does not exist in image0, image of star9 does not exist in image2, image of star9 does not exist in infrared1, image0 is not compatible with instrument0, image0 is not supported by instrument2, image2 is not supported by instrument1, infrared1 is not supported by instrument2, infrared1 is not supported by instrument3, instrument0 does not support image2, instrument0 is not calibrated for groundstation3, instrument0 is not calibrated for groundstation4, instrument0 is not calibrated for groundstation7, instrument0 is not calibrated for phenomenon10, instrument0 is not calibrated for phenomenon17, instrument0 is not calibrated for planet12, instrument0 is not calibrated for planet13, instrument0 is not calibrated for planet14, instrument1 is not calibrated, instrument1 is not calibrated for groundstation2, instrument1 is not calibrated for groundstation4, instrument1 is not calibrated for phenomenon10, instrument1 is not calibrated for star1, instrument1 is not calibrated for star8, instrument1 is not calibrated for star9, instrument1 is not on board satellite1, instrument1 is not switched on, instrument2 is not calibrated for groundstation2, instrument2 is not calibrated for groundstation4, instrument2 is not calibrated for phenomenon10, instrument2 is not calibrated for phenomenon16, instrument2 is not calibrated for planet12, instrument2 is not calibrated for planet14, instrument2 is not calibrated for star15, instrument2 is not calibrated for star6, instrument2 is not powered on, instrument3 is not calibrated for groundstation2, instrument3 is not calibrated for groundstation3, instrument3 is not calibrated for groundstation7, instrument3 is not calibrated for phenomenon16, instrument3 is not calibrated for phenomenon17, instrument3 is not calibrated for planet11, instrument3 is not calibrated for planet13, instrument3 is not calibrated for star1, phenomenon10 is not where satellite1 is pointed, phenomenon16 is not where satellite1 is pointed, phenomenon17 is not where satellite0 is pointed, planet11 is not where satellite0 is pointed, power is not available for satellite0, satellite0 does not carry instrument3 on board, satellite0 is not aimed towards groundstation3, satellite0 is not aimed towards planet13, satellite0 is not aimed towards planet14, satellite0 is not aimed towards star6, satellite0 is not aimed towards star8, satellite0 is not pointing to groundstation0, satellite0 is not pointing to groundstation4, satellite0 is not pointing to groundstation7, satellite0 is not pointing to phenomenon16, satellite0 is not pointing to planet12, satellite0 is not pointing to star15, satellite1 does not have instrument0 on board, satellite1 does not have instrument2 on board, satellite1 does not have power available, satellite1 is not aimed towards groundstation2, satellite1 is not aimed towards groundstation5, satellite1 is not aimed towards planet11, satellite1 is not aimed towards planet13, satellite1 is not aimed towards star1, satellite1 is not aimed towards star6, satellite1 is not aimed towards star8, satellite1 is not pointing to groundstation0, satellite1 is not pointing to groundstation3, satellite1 is not pointing to groundstation7, satellite1 is not pointing to phenomenon17, satellite1 is not pointing to planet12, satellite1 is not pointing to planet14, satellite1 is not pointing to star9, star1 is not where satellite0 is pointed, star9 is not where satellite0 is pointed, there is no image of direction groundstation0 in image0, there is no image of direction groundstation0 in infrared1, there is no image of direction groundstation2 in image3, there is no image of direction groundstation2 in infrared1, there is no image of direction groundstation3 in image2, there is no image of direction groundstation3 in infrared1, there is no image of direction groundstation4 in image0, there is no image of direction groundstation4 in image3, there is no image of direction groundstation7 in image2, there is no image of direction groundstation7 in image3, there is no image of direction groundstation7 in infrared1, there is no image of direction phenomenon10 in image0, there is no image of direction phenomenon10 in image2, there is no image of direction phenomenon10 in image3, there is no image of direction phenomenon16 in image0, there is no image of direction phenomenon16 in image2, there is no image of direction phenomenon16 in infrared1, there is no image of direction planet11 in image2, there is no image of direction planet11 in infrared1, there is no image of direction planet12 in image2, there is no image of direction planet12 in infrared1, there is no image of direction planet14 in image3, there is no image of direction star1 in image2, there is no image of direction star1 in image3, there is no image of direction star1 in infrared1, there is no image of direction star6 in image2, there is no image of direction star8 in image0, there is no image of direction star8 in image2, there is no image of direction star8 in infrared1 and there is no image of direction star9 in image3", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image2 is compatible with instrument2, image2 is supported by instrument3, image3 is compatible with instrument3, image3 is supported by instrument2, infrared1 is compatible with instrument1, instrument0 is on board satellite0, instrument0 supports image3, instrument0 supports infrared1, instrument1 supports image3, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the instrument3 on satellite1 is activated, the instrument0 on satellite0 is activated, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, an image of phenomenon16 is captured using instrument3 on satellite1 and stored in image3, satellite1 reorients from phenomenon16 to phenomenon17, an image of phenomenon17 is captured using instrument3 on satellite1 and stored in image3, satellite1 then reorients from phenomenon17 to planet11, an image of planet11 is captured using instrument3 on satellite1 and stored in image3, satellite1 reorients from planet11 to planet13, an image of planet13 is captured using instrument3 on satellite1 and stored in image0, satellite1 reorients from planet13 to planet14, an image of planet14 is captured using instrument3 on satellite1 and stored in image0, satellite1 then reorients from planet14 to star15, an image of star15 is captured using instrument3 on satellite1 and stored in image2, satellite0 reorients from groundstation3 to star1, instrument0 on satellite0 is calibrated to star1, and satellite0 reorients from star1 to phenomenon10 to reach the current state. In this state, if satellite0's instrument0 captures an image of phenomenon10 in infrared1, what would be all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is also complete, and instrument3's calibration for star6 is finished. Additionally, instrument0 has been calibrated for star1, instrument3 for star8, and instrument0 for star9. Satellite0 is currently directed towards groundstation3. Image0 is compatible with instrument1, while image2 is compatible with instrument2 and also supported by instrument3. Image3 is compatible with instrument3 and supported by instrument2. Infrared1 is compatible with instrument1. Instrument0 is installed on satellite0, and it supports both image3 and infrared1. Instrument1 also supports image3. Instrument2 has been calibrated for groundstation5 and groundstation7, and instrument3 has been calibrated for groundstation5. Instrument3 supports image0. Satellite0 is equipped with instrument1 and instrument2 on board. Both satellite0 and satellite1 have available power, with satellite1 carrying instrument3 on board and currently pointing towards phenomenon10."}
{"question_id": "478df695-b2a6-4d58-b3e1-b735d521143f", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on, satellite0 turns from groundstation2 to groundstation0, calibration of instrument1 which is on satellite0 to groundstation0 is complete, satellite0 turns from groundstation0 to planet11, image of planet11 is taken with instrument1 on satellite0 in image5, image of planet11 is taken with instrument1 on satellite0 in image6, from planet11, satellite0 turns to planet13, image of planet13 is taken with instrument1 on satellite0 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2 and satellite0 turns to star10 from planet13 to reach the current state. In this state, if instrument1 which is on satellite0 takes an image of star10 in image6, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "calibration of instrument0 for groundstation5 is incomplete, calibration of instrument0 for groundstation7 is complete, calibration of instrument0 for phenomenon15 is incomplete, calibration of instrument0 for planet13 is incomplete, calibration of instrument0 for star10 is incomplete, calibration of instrument0 for star3 is complete, calibration of instrument0 for star4 is incomplete, calibration of instrument0 is incomplete, calibration of instrument1 for planet11 is incomplete, calibration of instrument1 for star12 is incomplete, calibration of instrument1 is complete, calibration of instrument2 for groundstation0 is incomplete, calibration of instrument2 for groundstation6 is incomplete, calibration of instrument2 for phenomenon14 is incomplete, calibration of instrument2 for phenomenon15 is incomplete, calibration of instrument2 for star10 is incomplete, calibration of instrument2 for star12 is incomplete, calibration of instrument2 for star16 is incomplete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation0 is incomplete, calibration of instrument3 for groundstation1 is incomplete, calibration of instrument3 for groundstation8 is incomplete, calibration of instrument3 for planet11 is incomplete, calibration of instrument3 for planet13 is incomplete, calibration of instrument3 for star4 is incomplete, calibration of instrument4 for groundstation0 is incomplete, calibration of instrument4 for groundstation5 is incomplete, calibration of instrument4 for groundstation6 is incomplete, calibration of instrument4 for phenomenon15 is incomplete, calibration of instrument4 for planet11 is incomplete, calibration of instrument4 for star4 is incomplete, for groundstation1, instrument4 is not calibrated, for groundstation2, instrument0 is not calibrated, for groundstation2, instrument1 is not calibrated, for groundstation2, instrument2 is not calibrated, for groundstation6, instrument0 is not calibrated, for groundstation6, instrument1 is calibrated, for groundstation8, instrument1 is not calibrated, for groundstation8, instrument4 is calibrated, for groundstation9, instrument0 is not calibrated, for groundstation9, instrument1 is not calibrated, for phenomenon14, instrument0 is not calibrated, for phenomenon15, instrument1 is not calibrated, for planet11, instrument0 is not calibrated, for planet11, instrument2 is not calibrated, for planet13, instrument1 is not calibrated, for planet13, instrument2 is not calibrated, for star10, instrument1 is not calibrated, for star12, instrument4 is not calibrated, for star16, instrument0 is not calibrated, for star16, instrument1 is not calibrated, for star16, instrument3 is not calibrated, for star16, instrument4 is not calibrated, for star3, instrument2 is not calibrated, for star3, instrument3 is not calibrated, for star3, instrument4 is not calibrated, groundstation5 is not where satellite1 is pointed, groundstation7 is not where satellite0 is pointed, groundstation8 is not where satellite0 is pointed, groundstation8 is not where satellite1 is pointed, image of groundstation0 does not exist in image1, image of groundstation0 does not exist in image6, image of groundstation0 does not exist in spectrograph4, image of groundstation1 does not exist in thermograph3, image of groundstation2 does not exist in image0, image of groundstation2 does not exist in image1, image of groundstation2 does not exist in infrared7, image of groundstation2 does not exist in spectrograph4, image of groundstation2 does not exist in thermograph3, image of groundstation5 does not exist in image1, image of groundstation5 does not exist in image5, image of groundstation5 does not exist in image6, image of groundstation5 does not exist in spectrograph4, image of groundstation6 does not exist in image1, image of groundstation6 does not exist in image6, image of groundstation6 does not exist in infrared7, image of groundstation6 does not exist in spectrograph4, image of groundstation7 does not exist in image1, image of groundstation7 does not exist in image6, image of groundstation7 does not exist in infrared7, image of groundstation7 does not exist in spectrograph2, image of groundstation7 does not exist in thermograph3, image of groundstation8 does not exist in image5, image of groundstation8 does not exist in thermograph3, image of groundstation9 does not exist in image1, image of groundstation9 does not exist in image5, image of groundstation9 does not exist in spectrograph2, image of phenomenon14 does not exist in image0, image of phenomenon14 does not exist in image5, image of phenomenon15 does not exist in image0, image of phenomenon15 does not exist in image1, image of phenomenon15 does not exist in image5, image of phenomenon15 does not exist in image6, image of phenomenon15 does not exist in infrared7, image of planet11 does not exist in image1, image of planet11 does not exist in infrared7, image of planet11 does not exist in spectrograph2, image of planet11 exists in image6, image of planet13 does not exist in image1, image of planet13 does not exist in image6, image of planet13 does not exist in infrared7, image of planet13 exists in image5, image of planet13 exists in spectrograph2, image of star10 does not exist in image0, image of star10 does not exist in image1, image of star10 exists in image6, image of star12 does not exist in image0, image of star12 does not exist in image5, image of star12 does not exist in image6, image of star12 does not exist in spectrograph2, image of star12 does not exist in thermograph3, image of star16 does not exist in image0, image of star16 does not exist in image1, image of star16 does not exist in image5, image of star16 does not exist in infrared7, image of star16 does not exist in spectrograph2, image of star16 does not exist in spectrograph4, image of star16 does not exist in thermograph3, image of star3 does not exist in image1, image of star3 does not exist in image5, image of star3 does not exist in spectrograph2, image of star3 does not exist in thermograph3, image of star4 does not exist in image0, image of star4 does not exist in image1, image of star4 does not exist in infrared7, image of star4 does not exist in spectrograph2, image0 is not compatible with instrument3, image0 is not supported by instrument4, image1 is compatible with instrument3, image1 is compatible with instrument4, image1 is not compatible with instrument0, image1 is not compatible with instrument1, image5 is compatible with instrument1, image5 is not compatible with instrument0, image5 is not compatible with instrument2, image5 is not supported by instrument3, image6 is not compatible with instrument2, image6 is not compatible with instrument3, image6 is supported by instrument0, image6 is supported by instrument1, infrared7 is not compatible with instrument1, infrared7 is not supported by instrument0, infrared7 is not supported by instrument2, infrared7 is not supported by instrument3, instrument0 does not support image0, instrument0 is not calibrated for groundstation0, instrument0 is not calibrated for groundstation1, instrument0 is not calibrated for groundstation8, instrument0 is not calibrated for star12, instrument0 is not on board satellite1, instrument0 is not turned on, instrument0 is on board satellite0, instrument1 does not support image0, instrument1 does not support spectrograph4, instrument1 is calibrated for groundstation0, instrument1 is not calibrated for groundstation1, instrument1 is not calibrated for groundstation5, instrument1 is not calibrated for groundstation7, instrument1 is not calibrated for phenomenon14, instrument1 is not calibrated for star3, instrument1 is not calibrated for star4, instrument1 is not on board satellite1, instrument1 is turned on, instrument2 does not support spectrograph2, instrument2 does not support thermograph3, instrument2 is not calibrated, instrument2 is not calibrated for groundstation1, instrument2 is not calibrated for groundstation5, instrument2 is not calibrated for groundstation7, instrument2 is not calibrated for groundstation8, instrument2 is not calibrated for groundstation9, instrument2 is not switched on, instrument2 is on board satellite0, instrument2 supports image0, instrument2 supports image1, instrument3 does not support spectrograph2, instrument3 is calibrated for groundstation9, instrument3 is not calibrated, instrument3 is not calibrated for groundstation2, instrument3 is not calibrated for groundstation5, instrument3 is not calibrated for groundstation6, instrument3 is not calibrated for groundstation7, instrument3 is not calibrated for phenomenon14, instrument3 is not calibrated for phenomenon15, instrument3 is not calibrated for star10, instrument3 is not calibrated for star12, instrument3 is not switched on, instrument4 does not support image5, instrument4 does not support image6, instrument4 does not support spectrograph2, instrument4 does not support thermograph3, instrument4 is not calibrated, instrument4 is not calibrated for groundstation2, instrument4 is not calibrated for groundstation7, instrument4 is not calibrated for groundstation9, instrument4 is not calibrated for phenomenon14, instrument4 is not calibrated for planet13, instrument4 is not calibrated for star10, instrument4 is not on board satellite0, instrument4 is not switched on, instrument4 is on board satellite1, instrument4 supports infrared7, planet11 is not where satellite1 is pointed, satellite0 carries instrument1 on board, satellite0 does not have power available, satellite0 has instrument3 on board, satellite0 is aimed towards star10, satellite0 is not aimed towards groundstation1, satellite0 is not aimed towards groundstation5, satellite0 is not aimed towards groundstation6, satellite0 is not aimed towards phenomenon15, satellite0 is not aimed towards planet11, satellite0 is not aimed towards planet13, satellite0 is not aimed towards star3, satellite0 is not aimed towards star4, satellite0 is not pointing to groundstation0, satellite0 is not pointing to groundstation2, satellite0 is not pointing to groundstation9, satellite0 is not pointing to phenomenon14, satellite1 does not carry instrument2 on board, satellite1 does not have instrument3 on board, satellite1 has power available, satellite1 is aimed towards planet13, satellite1 is not aimed towards groundstation0, satellite1 is not aimed towards groundstation1, satellite1 is not aimed towards groundstation2, satellite1 is not aimed towards groundstation6, satellite1 is not aimed towards groundstation9, satellite1 is not aimed towards phenomenon14, satellite1 is not aimed towards phenomenon15, satellite1 is not aimed towards star10, satellite1 is not pointing to groundstation7, satellite1 is not pointing to star16, satellite1 is not pointing to star3, spectrograph2 is not supported by instrument0, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3, spectrograph4 is not compatible with instrument0, spectrograph4 is not compatible with instrument4, spectrograph4 is not supported by instrument2, star12 is not where satellite0 is pointed, star12 is not where satellite1 is pointed, star16 is not where satellite0 is pointed, star4 is not where satellite1 is pointed, there is an image of planet11 in image5, there is no image of direction groundstation0 in image0, there is no image of direction groundstation0 in image5, there is no image of direction groundstation0 in infrared7, there is no image of direction groundstation0 in spectrograph2, there is no image of direction groundstation0 in thermograph3, there is no image of direction groundstation1 in image0, there is no image of direction groundstation1 in image1, there is no image of direction groundstation1 in image5, there is no image of direction groundstation1 in image6, there is no image of direction groundstation1 in infrared7, there is no image of direction groundstation1 in spectrograph2, there is no image of direction groundstation1 in spectrograph4, there is no image of direction groundstation2 in image5, there is no image of direction groundstation2 in image6, there is no image of direction groundstation2 in spectrograph2, there is no image of direction groundstation5 in image0, there is no image of direction groundstation5 in infrared7, there is no image of direction groundstation5 in spectrograph2, there is no image of direction groundstation5 in thermograph3, there is no image of direction groundstation6 in image0, there is no image of direction groundstation6 in image5, there is no image of direction groundstation6 in spectrograph2, there is no image of direction groundstation6 in thermograph3, there is no image of direction groundstation7 in image0, there is no image of direction groundstation7 in image5, there is no image of direction groundstation7 in spectrograph4, there is no image of direction groundstation8 in image0, there is no image of direction groundstation8 in image1, there is no image of direction groundstation8 in image6, there is no image of direction groundstation8 in infrared7, there is no image of direction groundstation8 in spectrograph2, there is no image of direction groundstation8 in spectrograph4, there is no image of direction groundstation9 in image0, there is no image of direction groundstation9 in image6, there is no image of direction groundstation9 in infrared7, there is no image of direction groundstation9 in spectrograph4, there is no image of direction groundstation9 in thermograph3, there is no image of direction phenomenon14 in image1, there is no image of direction phenomenon14 in image6, there is no image of direction phenomenon14 in infrared7, there is no image of direction phenomenon14 in spectrograph2, there is no image of direction phenomenon14 in spectrograph4, there is no image of direction phenomenon14 in thermograph3, there is no image of direction phenomenon15 in spectrograph2, there is no image of direction phenomenon15 in spectrograph4, there is no image of direction phenomenon15 in thermograph3, there is no image of direction planet11 in image0, there is no image of direction planet11 in spectrograph4, there is no image of direction planet11 in thermograph3, there is no image of direction planet13 in image0, there is no image of direction planet13 in spectrograph4, there is no image of direction planet13 in thermograph3, there is no image of direction star10 in image5, there is no image of direction star10 in infrared7, there is no image of direction star10 in spectrograph2, there is no image of direction star10 in spectrograph4, there is no image of direction star10 in thermograph3, there is no image of direction star12 in image1, there is no image of direction star12 in infrared7, there is no image of direction star12 in spectrograph4, there is no image of direction star16 in image6, there is no image of direction star3 in image0, there is no image of direction star3 in image6, there is no image of direction star3 in infrared7, there is no image of direction star3 in spectrograph4, there is no image of direction star4 in image5, there is no image of direction star4 in image6, there is no image of direction star4 in spectrograph4, there is no image of direction star4 in thermograph3, thermograph3 is not compatible with instrument0, thermograph3 is not supported by instrument1 and thermograph3 is supported by instrument3", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument2 for star4 is complete, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, for groundstation9, instrument3 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image1 is supported by instrument3, image5 is compatible with instrument1, image6 is supported by instrument0, image6 is supported by instrument1, instrument0 is calibrated for star3, instrument1 is calibrated for groundstation0, instrument1 is on board satellite0, instrument4 supports infrared7, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has power available, satellite0 is pointing to groundstation2, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is aimed towards planet13, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, satellite0 then reorients from groundstation0 to planet11, an image of planet11 is captured using instrument1 on satellite0 and stored in image5 and image6, satellite0 reorients from planet11 to planet13, an image of planet13 is captured using instrument1 on satellite0 and stored in image5, instrument1 on satellite0 captures an image of planet13 in spectrograph2, and finally, satellite0 reorients from planet13 to star10 to reach the current state. In this state, if instrument1 on satellite0 captures an image of star10 in image6, what are all the valid properties of the state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "The following calibrations have been completed: instrument1 for groundstation6, instrument2 for star4, instrument0 for groundstation7, instrument4 for groundstation8, and instrument3 for groundstation9. Additionally, the following instrument-image compatibility has been established: instrument2 supports image0, image1 is compatible with instrument4, and also supported by instrument2 and instrument3, image5 is compatible with instrument1, image6 is supported by instrument0 and instrument1. Furthermore, the following instrument calibrations have been completed: instrument0 for star3, instrument1 for groundstation0. The satellite configuration is as follows: instrument0, instrument1, and instrument3 are on board satellite0, while instrument4 is on board satellite1. Satellite0 has power available and is currently pointing towards groundstation2, whereas satellite1 has power and is aimed towards planet13. Instrument capabilities include: instrument4 supports infrared7, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is supported by instrument3."}
{"question_id": "bc992105-18c6-4c0a-9cb5-c1f3438c902d", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on to reach the current state. In this state, if satellite0 turns from groundstation2 to groundstation0, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "calibration of instrument0 for groundstation5 is incomplete, calibration of instrument0 for groundstation6 is incomplete, calibration of instrument0 for groundstation8 is incomplete, calibration of instrument0 for phenomenon14 is incomplete, calibration of instrument0 is incomplete, calibration of instrument1 for groundstation5 is incomplete, calibration of instrument1 for groundstation7 is incomplete, calibration of instrument1 for phenomenon14 is incomplete, calibration of instrument1 for star10 is incomplete, calibration of instrument1 for star3 is incomplete, calibration of instrument2 for star12 is incomplete, calibration of instrument3 for groundstation6 is incomplete, calibration of instrument3 for groundstation8 is incomplete, calibration of instrument3 for phenomenon15 is incomplete, calibration of instrument3 for planet11 is incomplete, calibration of instrument3 for star10 is incomplete, calibration of instrument3 for star3 is incomplete, calibration of instrument3 for star4 is incomplete, calibration of instrument4 for groundstation2 is incomplete, calibration of instrument4 for groundstation9 is incomplete, calibration of instrument4 for planet11 is incomplete, calibration of instrument4 for planet13 is incomplete, calibration of instrument4 for star10 is incomplete, calibration of instrument4 for star12 is incomplete, calibration of instrument4 is incomplete, for groundstation1, instrument4 is not calibrated, for groundstation2, instrument1 is not calibrated, for groundstation2, instrument2 is not calibrated, for groundstation2, instrument3 is not calibrated, for groundstation5, instrument4 is not calibrated, for groundstation6, instrument2 is not calibrated, for groundstation6, instrument4 is not calibrated, for groundstation8, instrument1 is not calibrated, for groundstation9, instrument1 is not calibrated, for groundstation9, instrument2 is not calibrated, for phenomenon14, instrument3 is not calibrated, for phenomenon14, instrument4 is not calibrated, for phenomenon15, instrument2 is not calibrated, for planet11, instrument2 is not calibrated, for planet13, instrument2 is not calibrated, for planet13, instrument3 is not calibrated, for star12, instrument0 is not calibrated, for star12, instrument1 is not calibrated, for star16, instrument0 is not calibrated, for star16, instrument1 is not calibrated, for star16, instrument2 is not calibrated, for star3, instrument2 is not calibrated, groundstation0 is not where satellite1 is pointed, groundstation1 is not where satellite1 is pointed, groundstation5 is not where satellite0 is pointed, groundstation9 is not where satellite0 is pointed, image of groundstation0 does not exist in image0, image of groundstation0 does not exist in image1, image of groundstation0 does not exist in infrared7, image of groundstation0 does not exist in spectrograph2, image of groundstation1 does not exist in image1, image of groundstation1 does not exist in image5, image of groundstation1 does not exist in spectrograph4, image of groundstation2 does not exist in image5, image of groundstation2 does not exist in infrared7, image of groundstation2 does not exist in thermograph3, image of groundstation5 does not exist in image0, image of groundstation5 does not exist in image1, image of groundstation5 does not exist in image6, image of groundstation5 does not exist in infrared7, image of groundstation5 does not exist in spectrograph2, image of groundstation6 does not exist in image5, image of groundstation6 does not exist in image6, image of groundstation6 does not exist in infrared7, image of groundstation7 does not exist in image0, image of groundstation7 does not exist in image6, image of groundstation7 does not exist in spectrograph2, image of groundstation8 does not exist in image0, image of groundstation8 does not exist in image1, image of groundstation8 does not exist in image5, image of groundstation8 does not exist in image6, image of groundstation8 does not exist in spectrograph2, image of groundstation9 does not exist in image1, image of groundstation9 does not exist in infrared7, image of groundstation9 does not exist in spectrograph2, image of phenomenon14 does not exist in image0, image of phenomenon14 does not exist in image1, image of phenomenon14 does not exist in image5, image of phenomenon14 does not exist in infrared7, image of phenomenon14 does not exist in spectrograph2, image of phenomenon14 does not exist in thermograph3, image of phenomenon15 does not exist in image1, image of phenomenon15 does not exist in image5, image of phenomenon15 does not exist in spectrograph2, image of phenomenon15 does not exist in spectrograph4, image of planet11 does not exist in image0, image of planet11 does not exist in image1, image of planet11 does not exist in image5, image of planet11 does not exist in spectrograph2, image of planet11 does not exist in spectrograph4, image of planet13 does not exist in image1, image of planet13 does not exist in image5, image of planet13 does not exist in spectrograph4, image of star10 does not exist in image0, image of star10 does not exist in image5, image of star10 does not exist in image6, image of star10 does not exist in infrared7, image of star10 does not exist in spectrograph2, image of star10 does not exist in spectrograph4, image of star12 does not exist in image6, image of star12 does not exist in spectrograph4, image of star16 does not exist in image1, image of star16 does not exist in image5, image of star16 does not exist in infrared7, image of star16 does not exist in spectrograph2, image of star16 does not exist in thermograph3, image of star3 does not exist in image5, image of star3 does not exist in image6, image of star3 does not exist in spectrograph2, image of star4 does not exist in image0, image of star4 does not exist in image1, image of star4 does not exist in spectrograph4, image0 is not compatible with instrument3, image0 is not supported by instrument4, image1 is not compatible with instrument0, image5 is not compatible with instrument3, image5 is not supported by instrument2, image5 is not supported by instrument4, image6 is not compatible with instrument3, image6 is not supported by instrument2, infrared7 is not compatible with instrument1, infrared7 is not compatible with instrument2, infrared7 is not compatible with instrument3, infrared7 is not supported by instrument0, instrument0 does not support image0, instrument0 does not support image5, instrument0 is not calibrated for groundstation0, instrument0 is not calibrated for groundstation1, instrument0 is not calibrated for groundstation2, instrument0 is not calibrated for groundstation9, instrument0 is not calibrated for phenomenon15, instrument0 is not calibrated for planet11, instrument0 is not calibrated for planet13, instrument0 is not calibrated for star10, instrument0 is not calibrated for star4, instrument0 is not powered on, instrument1 does not support image0, instrument1 does not support image1, instrument1 does not support spectrograph4, instrument1 does not support thermograph3, instrument1 is not calibrated, instrument1 is not calibrated for groundstation1, instrument1 is not calibrated for phenomenon15, instrument1 is not calibrated for planet11, instrument1 is not calibrated for planet13, instrument1 is not calibrated for star4, instrument2 does not support spectrograph2, instrument2 is not calibrated, instrument2 is not calibrated for groundstation0, instrument2 is not calibrated for groundstation1, instrument2 is not calibrated for groundstation5, instrument2 is not calibrated for groundstation7, instrument2 is not calibrated for groundstation8, instrument2 is not calibrated for phenomenon14, instrument2 is not calibrated for star10, instrument2 is not switched on, instrument3 is not calibrated, instrument3 is not calibrated for groundstation0, instrument3 is not calibrated for groundstation1, instrument3 is not calibrated for groundstation5, instrument3 is not calibrated for groundstation7, instrument3 is not calibrated for star12, instrument3 is not calibrated for star16, instrument3 is not on board satellite1, instrument3 is not turned on, instrument4 does not support image6, instrument4 does not support thermograph3, instrument4 is not calibrated for groundstation0, instrument4 is not calibrated for groundstation7, instrument4 is not calibrated for phenomenon15, instrument4 is not calibrated for star16, instrument4 is not calibrated for star3, instrument4 is not calibrated for star4, instrument4 is not on board satellite0, instrument4 is not switched on, phenomenon14 is not where satellite0 is pointed, satellite0 does not have power available, satellite0 is not aimed towards groundstation2, satellite0 is not aimed towards groundstation7, satellite0 is not aimed towards groundstation8, satellite0 is not aimed towards phenomenon15, satellite0 is not aimed towards planet13, satellite0 is not aimed towards star12, satellite0 is not aimed towards star16, satellite0 is not aimed towards star3, satellite0 is not aimed towards star4, satellite0 is not pointing to groundstation1, satellite0 is not pointing to groundstation6, satellite0 is not pointing to planet11, satellite0 is not pointing to star10, satellite1 does not carry instrument0 on board, satellite1 does not carry instrument2 on board, satellite1 does not have instrument1 on board, satellite1 is not aimed towards groundstation6, satellite1 is not aimed towards groundstation9, satellite1 is not aimed towards phenomenon14, satellite1 is not aimed towards planet11, satellite1 is not aimed towards star16, satellite1 is not aimed towards star3, satellite1 is not pointing to groundstation2, satellite1 is not pointing to groundstation5, satellite1 is not pointing to groundstation7, satellite1 is not pointing to groundstation8, satellite1 is not pointing to phenomenon15, satellite1 is not pointing to star12, spectrograph2 is not compatible with instrument0, spectrograph2 is not compatible with instrument3, spectrograph2 is not compatible with instrument4, spectrograph4 is not compatible with instrument0, spectrograph4 is not supported by instrument2, spectrograph4 is not supported by instrument4, star10 is not where satellite1 is pointed, star4 is not where satellite1 is pointed, there is no image of direction groundstation0 in image5, there is no image of direction groundstation0 in image6, there is no image of direction groundstation0 in spectrograph4, there is no image of direction groundstation0 in thermograph3, there is no image of direction groundstation1 in image0, there is no image of direction groundstation1 in image6, there is no image of direction groundstation1 in infrared7, there is no image of direction groundstation1 in spectrograph2, there is no image of direction groundstation1 in thermograph3, there is no image of direction groundstation2 in image0, there is no image of direction groundstation2 in image1, there is no image of direction groundstation2 in image6, there is no image of direction groundstation2 in spectrograph2, there is no image of direction groundstation2 in spectrograph4, there is no image of direction groundstation5 in image5, there is no image of direction groundstation5 in spectrograph4, there is no image of direction groundstation5 in thermograph3, there is no image of direction groundstation6 in image0, there is no image of direction groundstation6 in image1, there is no image of direction groundstation6 in spectrograph2, there is no image of direction groundstation6 in spectrograph4, there is no image of direction groundstation6 in thermograph3, there is no image of direction groundstation7 in image1, there is no image of direction groundstation7 in image5, there is no image of direction groundstation7 in infrared7, there is no image of direction groundstation7 in spectrograph4, there is no image of direction groundstation7 in thermograph3, there is no image of direction groundstation8 in infrared7, there is no image of direction groundstation8 in spectrograph4, there is no image of direction groundstation8 in thermograph3, there is no image of direction groundstation9 in image0, there is no image of direction groundstation9 in image5, there is no image of direction groundstation9 in image6, there is no image of direction groundstation9 in spectrograph4, there is no image of direction groundstation9 in thermograph3, there is no image of direction phenomenon14 in image6, there is no image of direction phenomenon14 in spectrograph4, there is no image of direction phenomenon15 in image0, there is no image of direction phenomenon15 in image6, there is no image of direction phenomenon15 in infrared7, there is no image of direction phenomenon15 in thermograph3, there is no image of direction planet11 in image6, there is no image of direction planet11 in infrared7, there is no image of direction planet11 in thermograph3, there is no image of direction planet13 in image0, there is no image of direction planet13 in image6, there is no image of direction planet13 in infrared7, there is no image of direction planet13 in spectrograph2, there is no image of direction planet13 in thermograph3, there is no image of direction star10 in image1, there is no image of direction star10 in thermograph3, there is no image of direction star12 in image0, there is no image of direction star12 in image1, there is no image of direction star12 in image5, there is no image of direction star12 in infrared7, there is no image of direction star12 in spectrograph2, there is no image of direction star12 in thermograph3, there is no image of direction star16 in image0, there is no image of direction star16 in image6, there is no image of direction star16 in spectrograph4, there is no image of direction star3 in image0, there is no image of direction star3 in image1, there is no image of direction star3 in infrared7, there is no image of direction star3 in spectrograph4, there is no image of direction star3 in thermograph3, there is no image of direction star4 in image5, there is no image of direction star4 in image6, there is no image of direction star4 in infrared7, there is no image of direction star4 in spectrograph2, there is no image of direction star4 in thermograph3, thermograph3 is not compatible with instrument2 and thermograph3 is not supported by instrument0", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument2 for star4 is complete, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, for groundstation9, instrument3 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image1 is supported by instrument3, image5 is compatible with instrument1, image6 is supported by instrument0, image6 is supported by instrument1, instrument0 is calibrated for star3, instrument1 is calibrated for groundstation0, instrument1 is on board satellite0, instrument4 supports infrared7, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has power available, satellite0 is pointing to groundstation2, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is aimed towards planet13, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument1 on satellite0 is activated to achieve the current state. In this state, if satellite0 switches from groundstation2 to groundstation0, what are all the valid state properties that involve negations? If there are no such properties, write None.", "initial_state_nl_paraphrased": "The following calibrations have been completed: instrument1 for groundstation6, instrument2 for star4, instrument0 for groundstation7, instrument4 for groundstation8, and instrument3 for groundstation9. Additionally, the following instrument-image compatibility has been established: instrument2 supports image0, image1 is compatible with instrument4, and image1 is also supported by both instrument2 and instrument3. Furthermore, image5 is compatible with instrument1, and image6 is supported by both instrument0 and instrument1. The calibration status of instruments for specific celestial bodies is as follows: instrument0 is calibrated for star3, and instrument1 is calibrated for groundstation0. The satellite0 is equipped with instruments instrument0, instrument1, instrument2, and instrument3 on board, and it has power available while pointing towards groundstation2. Satellite1, on the other hand, carries instrument4 on board, has power, and is aimed towards planet13. Lastly, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is supported by instrument3, and instrument4 supports infrared7."}
{"question_id": "254a02cc-9ef8-4f15-86e1-c13887c06c3d", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on to reach the current state. In this state, if instrument0 that is on satellite0 is turned on, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "calibration of instrument0 for groundstation4 is complete, calibration of instrument0 for groundstation5 is incomplete, calibration of instrument0 for groundstation9 is incomplete, calibration of instrument0 for star0 is complete, calibration of instrument0 is incomplete, calibration of instrument1 for groundstation9 is incomplete, calibration of instrument1 for phenomenon15 is incomplete, calibration of instrument1 for star0 is incomplete, calibration of instrument1 is incomplete, calibration of instrument2 for groundstation2 is incomplete, calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for star0 is incomplete, calibration of instrument2 for star1 is incomplete, calibration of instrument2 for star11 is incomplete, calibration of instrument2 for star13 is incomplete, calibration of instrument2 for star7 is complete, calibration of instrument2 is incomplete, calibration of instrument3 for groundstation2 is incomplete, calibration of instrument3 for groundstation3 is incomplete, calibration of instrument3 for phenomenon15 is incomplete, calibration of instrument3 for planet14 is incomplete, calibration of instrument3 for star10 is incomplete, for groundstation2, instrument1 is calibrated, for groundstation3, instrument0 is not calibrated, for groundstation3, instrument2 is not calibrated, for groundstation4, instrument1 is calibrated, for groundstation5, instrument2 is not calibrated, for groundstation5, instrument3 is not calibrated, for phenomenon15, instrument2 is not calibrated, for planet14, instrument1 is not calibrated, for star0, instrument3 is not calibrated, for star1, instrument0 is not calibrated, for star10, instrument0 is not calibrated, for star10, instrument2 is not calibrated, for star12, instrument2 is not calibrated, for star13, instrument0 is not calibrated, for star13, instrument1 is not calibrated, for star13, instrument3 is not calibrated, for star16, instrument0 is not calibrated, for star16, instrument2 is not calibrated, for star16, instrument3 is not calibrated, for star6, instrument2 is not calibrated, for star6, instrument3 is calibrated, for star7, instrument1 is not calibrated, for star8, instrument0 is not calibrated, for star8, instrument2 is not calibrated, for star8, instrument3 is not calibrated, groundstation2 is not where satellite1 is pointed, groundstation4 is where satellite1 is pointed, groundstation5 is not where satellite1 is pointed, groundstation9 is not where satellite0 is pointed, groundstation9 is not where satellite1 is pointed, image of groundstation2 does not exist in spectrograph0, image of groundstation2 does not exist in spectrograph1, image of groundstation3 does not exist in infrared3, image of groundstation3 does not exist in spectrograph0, image of groundstation3 does not exist in spectrograph1, image of groundstation3 does not exist in spectrograph2, image of groundstation3 does not exist in thermograph4, image of groundstation4 does not exist in spectrograph0, image of groundstation4 does not exist in spectrograph1, image of groundstation4 does not exist in spectrograph2, image of groundstation5 does not exist in spectrograph0, image of groundstation5 does not exist in spectrograph2, image of groundstation9 does not exist in infrared3, image of groundstation9 does not exist in spectrograph0, image of groundstation9 does not exist in spectrograph1, image of groundstation9 does not exist in spectrograph2, image of groundstation9 does not exist in thermograph4, image of phenomenon15 does not exist in infrared3, image of phenomenon15 does not exist in spectrograph0, image of phenomenon15 does not exist in spectrograph1, image of planet14 does not exist in spectrograph1, image of planet14 does not exist in thermograph4, image of star0 does not exist in spectrograph0, image of star0 does not exist in spectrograph1, image of star0 does not exist in spectrograph2, image of star1 does not exist in spectrograph1, image of star1 does not exist in spectrograph2, image of star1 does not exist in thermograph4, image of star10 does not exist in thermograph4, image of star11 does not exist in spectrograph0, image of star11 does not exist in thermograph4, image of star12 does not exist in spectrograph0, image of star12 does not exist in spectrograph1, image of star12 does not exist in spectrograph2, image of star12 does not exist in thermograph4, image of star13 does not exist in infrared3, image of star13 does not exist in spectrograph0, image of star13 does not exist in spectrograph1, image of star13 does not exist in spectrograph2, image of star13 does not exist in thermograph4, image of star16 does not exist in infrared3, image of star6 does not exist in spectrograph0, image of star7 does not exist in spectrograph1, image of star7 does not exist in spectrograph2, image of star8 does not exist in infrared3, image of star8 does not exist in spectrograph0, infrared3 is compatible with instrument2, infrared3 is not compatible with instrument0, infrared3 is not supported by instrument1, infrared3 is not supported by instrument3, instrument0 does not support spectrograph1, instrument0 does not support spectrograph2, instrument0 is calibrated for groundstation2, instrument0 is not calibrated for phenomenon15, instrument0 is not calibrated for planet14, instrument0 is not calibrated for star11, instrument0 is not calibrated for star12, instrument0 is not calibrated for star6, instrument0 is not calibrated for star7, instrument0 is not on board satellite1, instrument0 is turned on, instrument0 supports thermograph4, instrument1 does not support thermograph4, instrument1 is calibrated for star8, instrument1 is not calibrated for groundstation3, instrument1 is not calibrated for groundstation5, instrument1 is not calibrated for star1, instrument1 is not calibrated for star10, instrument1 is not calibrated for star11, instrument1 is not calibrated for star12, instrument1 is not calibrated for star16, instrument1 is not calibrated for star6, instrument1 is not switched on, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is not calibrated for planet14, instrument2 is not on board satellite0, instrument2 is not turned on, instrument2 supports spectrograph2, instrument3 is not calibrated, instrument3 is not calibrated for groundstation4, instrument3 is not calibrated for groundstation9, instrument3 is not calibrated for star1, instrument3 is not calibrated for star11, instrument3 is not calibrated for star12, instrument3 is not calibrated for star7, instrument3 is turned on, instrument3 supports spectrograph1, planet14 is not where satellite0 is pointed, planet14 is not where satellite1 is pointed, power is not available for satellite0, power is not available for satellite1, satellite0 carries instrument0 on board, satellite0 does not carry instrument3 on board, satellite0 is not aimed towards groundstation5, satellite0 is not aimed towards phenomenon15, satellite0 is not aimed towards star0, satellite0 is not aimed towards star13, satellite0 is not aimed towards star16, satellite0 is not aimed towards star6, satellite0 is not pointing to groundstation2, satellite0 is not pointing to groundstation3, satellite0 is not pointing to groundstation4, satellite0 is not pointing to star10, satellite0 is not pointing to star12, satellite0 is not pointing to star7, satellite0 is pointing to star1, satellite1 does not carry instrument1 on board, satellite1 has instrument2 on board, satellite1 has instrument3 on board, satellite1 is not aimed towards phenomenon15, satellite1 is not aimed towards star10, satellite1 is not aimed towards star11, satellite1 is not aimed towards star16, satellite1 is not pointing to groundstation3, satellite1 is not pointing to star1, satellite1 is not pointing to star13, satellite1 is not pointing to star7, spectrograph0 is compatible with instrument0, spectrograph0 is not supported by instrument3, spectrograph0 is supported by instrument1, spectrograph0 is supported by instrument2, spectrograph1 is not supported by instrument2, spectrograph2 is compatible with instrument3, spectrograph2 is not supported by instrument1, star0 is not where satellite1 is pointed, star11 is not where satellite0 is pointed, star12 is not where satellite1 is pointed, star6 is not where satellite1 is pointed, star8 is not where satellite0 is pointed, star8 is not where satellite1 is pointed, there is no image of direction groundstation2 in infrared3, there is no image of direction groundstation2 in spectrograph2, there is no image of direction groundstation2 in thermograph4, there is no image of direction groundstation4 in infrared3, there is no image of direction groundstation4 in thermograph4, there is no image of direction groundstation5 in infrared3, there is no image of direction groundstation5 in spectrograph1, there is no image of direction groundstation5 in thermograph4, there is no image of direction phenomenon15 in spectrograph2, there is no image of direction phenomenon15 in thermograph4, there is no image of direction planet14 in infrared3, there is no image of direction planet14 in spectrograph0, there is no image of direction planet14 in spectrograph2, there is no image of direction star0 in infrared3, there is no image of direction star0 in thermograph4, there is no image of direction star1 in infrared3, there is no image of direction star1 in spectrograph0, there is no image of direction star10 in infrared3, there is no image of direction star10 in spectrograph0, there is no image of direction star10 in spectrograph1, there is no image of direction star10 in spectrograph2, there is no image of direction star11 in infrared3, there is no image of direction star11 in spectrograph1, there is no image of direction star11 in spectrograph2, there is no image of direction star12 in infrared3, there is no image of direction star16 in spectrograph0, there is no image of direction star16 in spectrograph1, there is no image of direction star16 in spectrograph2, there is no image of direction star16 in thermograph4, there is no image of direction star6 in infrared3, there is no image of direction star6 in spectrograph1, there is no image of direction star6 in spectrograph2, there is no image of direction star6 in thermograph4, there is no image of direction star7 in infrared3, there is no image of direction star7 in spectrograph0, there is no image of direction star7 in thermograph4, there is no image of direction star8 in spectrograph1, there is no image of direction star8 in spectrograph2, there is no image of direction star8 in thermograph4, thermograph4 is not compatible with instrument2 and thermograph4 is not compatible with instrument3", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for star7 is complete, for groundstation4, instrument2 is calibrated, for star0, instrument0 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is compatible with instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for groundstation4, instrument0 is on board satellite0, instrument1 is calibrated for groundstation2, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument3 is calibrated for star6, satellite0 has power, satellite0 is aimed towards star1, satellite1 carries instrument3 on board, satellite1 has power, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph0 is compatible with instrument2, spectrograph1 is supported by instrument3, spectrograph2 is supported by instrument2, spectrograph2 is supported by instrument3 and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, if instrument0 on satellite0 is activated, what are all the possible valid properties of the state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star8 has been completed, instrument2's calibration for groundstation9 is also complete, and instrument2's calibration for star7 is finished. Instrument2 has been calibrated for groundstation4, and instrument0 has been calibrated for star0. The satellite1 is currently directed towards groundstation4. Infrared3 is found to be compatible with instrument2. Instrument0 has been calibrated for both groundstation2 and groundstation4, and it is located on board satellite0. Similarly, instrument1 has been calibrated for groundstation2 and groundstation4, and it is also on board satellite0, with the added capability of supporting spectrograph1. Instrument2 is on board satellite1. Instrument3 has been calibrated for star6. Satellite0 is operational with power and is aimed at star1. Satellite1 is equipped with instrument3 on board and has power. Spectrograph0 is compatible with instruments 0, 1, and 2. Spectrograph1 is supported by instrument3, and spectrograph2 is supported by both instruments 2 and 3. Additionally, thermograph4 is compatible with instrument0."}
{"question_id": "7c9e4c1a-5b4a-4f6e-90bd-c21e8e80c1d4", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is switched on, satellite0 turns from groundstation2 to groundstation0, instrument1 is calibrated on satellite0 to groundstation0, from groundstation0, satellite0 turns to planet11, satellite0's instrument1 takes an image of planet11 in image5, satellite0's instrument1 takes an image of planet11 in image6, satellite0 turns from planet11 to planet13, satellite0's instrument1 takes an image of planet13 in image5, image of planet13 is taken with instrument1 on satellite0 in spectrograph2 and satellite0 turns to star10 from planet13 to reach the current state. In this state, if instrument1 which is on satellite0 takes an image of star10 in image6, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "calibration of instrument0 for groundstation1 is incomplete, calibration of instrument0 for groundstation2 is incomplete, calibration of instrument0 for phenomenon14 is incomplete, calibration of instrument0 for planet11 is incomplete, calibration of instrument0 for star12 is incomplete, calibration of instrument0 for star16 is incomplete, calibration of instrument0 for star4 is incomplete, calibration of instrument0 is incomplete, calibration of instrument1 for groundstation1 is incomplete, calibration of instrument1 for groundstation5 is incomplete, calibration of instrument1 for groundstation7 is incomplete, calibration of instrument1 for groundstation9 is incomplete, calibration of instrument1 for phenomenon14 is incomplete, calibration of instrument1 for planet11 is incomplete, calibration of instrument1 for star10 is incomplete, calibration of instrument2 for groundstation1 is incomplete, calibration of instrument2 for groundstation2 is incomplete, calibration of instrument2 for groundstation6 is incomplete, calibration of instrument2 for phenomenon15 is incomplete, calibration of instrument2 for star12 is incomplete, calibration of instrument2 for star16 is incomplete, calibration of instrument3 for groundstation2 is incomplete, calibration of instrument3 for phenomenon14 is incomplete, calibration of instrument3 for phenomenon15 is incomplete, calibration of instrument3 for star10 is incomplete, calibration of instrument3 for star3 is incomplete, calibration of instrument4 for groundstation0 is incomplete, calibration of instrument4 for groundstation1 is incomplete, calibration of instrument4 for groundstation9 is incomplete, calibration of instrument4 for planet11 is incomplete, calibration of instrument4 for star3 is incomplete, for groundstation5, instrument2 is not calibrated, for groundstation5, instrument4 is not calibrated, for groundstation6, instrument4 is not calibrated, for groundstation7, instrument3 is not calibrated, for groundstation8, instrument0 is not calibrated, for groundstation8, instrument2 is not calibrated, for phenomenon14, instrument2 is not calibrated, for phenomenon14, instrument4 is not calibrated, for phenomenon15, instrument1 is not calibrated, for phenomenon15, instrument4 is not calibrated, for planet13, instrument2 is not calibrated, for planet13, instrument3 is not calibrated, for star12, instrument1 is not calibrated, for star4, instrument1 is not calibrated, for star4, instrument3 is not calibrated, for star4, instrument4 is not calibrated, groundstation0 is not where satellite0 is pointed, groundstation0 is not where satellite1 is pointed, groundstation1 is not where satellite1 is pointed, groundstation2 is not where satellite1 is pointed, groundstation5 is not where satellite0 is pointed, groundstation5 is not where satellite1 is pointed, groundstation6 is not where satellite0 is pointed, groundstation7 is not where satellite0 is pointed, groundstation7 is not where satellite1 is pointed, groundstation8 is not where satellite1 is pointed, image of groundstation0 does not exist in image0, image of groundstation0 does not exist in image1, image of groundstation0 does not exist in image6, image of groundstation0 does not exist in infrared7, image of groundstation0 does not exist in spectrograph2, image of groundstation1 does not exist in image0, image of groundstation1 does not exist in image5, image of groundstation1 does not exist in infrared7, image of groundstation1 does not exist in spectrograph2, image of groundstation2 does not exist in image0, image of groundstation2 does not exist in image1, image of groundstation2 does not exist in image6, image of groundstation2 does not exist in infrared7, image of groundstation2 does not exist in spectrograph4, image of groundstation2 does not exist in thermograph3, image of groundstation5 does not exist in image0, image of groundstation5 does not exist in image5, image of groundstation5 does not exist in image6, image of groundstation5 does not exist in infrared7, image of groundstation6 does not exist in image1, image of groundstation6 does not exist in image6, image of groundstation6 does not exist in spectrograph4, image of groundstation6 does not exist in thermograph3, image of groundstation7 does not exist in image5, image of groundstation7 does not exist in image6, image of groundstation7 does not exist in spectrograph2, image of groundstation7 does not exist in spectrograph4, image of groundstation8 does not exist in image0, image of groundstation8 does not exist in image5, image of groundstation8 does not exist in image6, image of groundstation8 does not exist in infrared7, image of groundstation8 does not exist in spectrograph2, image of groundstation8 does not exist in spectrograph4, image of groundstation8 does not exist in thermograph3, image of groundstation9 does not exist in image1, image of phenomenon14 does not exist in image0, image of phenomenon14 does not exist in image1, image of phenomenon14 does not exist in image6, image of phenomenon14 does not exist in spectrograph2, image of phenomenon15 does not exist in image0, image of phenomenon15 does not exist in image1, image of phenomenon15 does not exist in infrared7, image of phenomenon15 does not exist in spectrograph2, image of phenomenon15 does not exist in spectrograph4, image of phenomenon15 does not exist in thermograph3, image of planet11 does not exist in image0, image of planet11 does not exist in image1, image of planet11 does not exist in infrared7, image of planet11 does not exist in thermograph3, image of planet13 does not exist in image0, image of planet13 does not exist in image6, image of planet13 does not exist in infrared7, image of planet13 does not exist in thermograph3, image of star10 does not exist in image0, image of star10 does not exist in image5, image of star10 does not exist in infrared7, image of star12 does not exist in image1, image of star12 does not exist in thermograph3, image of star16 does not exist in image0, image of star16 does not exist in image5, image of star16 does not exist in image6, image of star16 does not exist in spectrograph4, image of star3 does not exist in infrared7, image of star3 does not exist in spectrograph2, image of star4 does not exist in image1, image of star4 does not exist in infrared7, image of star4 does not exist in spectrograph2, image0 is not compatible with instrument0, image0 is not compatible with instrument3, image0 is not compatible with instrument4, image0 is not supported by instrument1, image1 is not compatible with instrument0, image1 is not supported by instrument1, image5 is not compatible with instrument0, image5 is not compatible with instrument2, image5 is not supported by instrument3, image5 is not supported by instrument4, image6 is not compatible with instrument3, image6 is not compatible with instrument4, infrared7 is not compatible with instrument3, infrared7 is not supported by instrument0, infrared7 is not supported by instrument1, infrared7 is not supported by instrument2, instrument0 is not calibrated for groundstation0, instrument0 is not calibrated for groundstation5, instrument0 is not calibrated for groundstation6, instrument0 is not calibrated for groundstation9, instrument0 is not calibrated for phenomenon15, instrument0 is not calibrated for planet13, instrument0 is not calibrated for star10, instrument0 is not turned on, instrument1 does not support spectrograph4, instrument1 is not calibrated for groundstation2, instrument1 is not calibrated for groundstation8, instrument1 is not calibrated for planet13, instrument1 is not calibrated for star16, instrument1 is not calibrated for star3, instrument2 does not support image6, instrument2 does not support spectrograph4, instrument2 is not calibrated, instrument2 is not calibrated for groundstation0, instrument2 is not calibrated for groundstation7, instrument2 is not calibrated for groundstation9, instrument2 is not calibrated for planet11, instrument2 is not calibrated for star10, instrument2 is not calibrated for star3, instrument2 is not powered on, instrument3 is not calibrated, instrument3 is not calibrated for groundstation0, instrument3 is not calibrated for groundstation1, instrument3 is not calibrated for groundstation5, instrument3 is not calibrated for groundstation6, instrument3 is not calibrated for groundstation8, instrument3 is not calibrated for planet11, instrument3 is not calibrated for star12, instrument3 is not calibrated for star16, instrument3 is not on board satellite1, instrument3 is not powered on, instrument4 is not calibrated, instrument4 is not calibrated for groundstation2, instrument4 is not calibrated for groundstation7, instrument4 is not calibrated for planet13, instrument4 is not calibrated for star10, instrument4 is not calibrated for star12, instrument4 is not calibrated for star16, instrument4 is not powered on, phenomenon14 is not where satellite1 is pointed, phenomenon15 is not where satellite0 is pointed, planet11 is not where satellite0 is pointed, planet11 is not where satellite1 is pointed, planet13 is not where satellite0 is pointed, power is not available for satellite0, satellite0 does not have instrument4 on board, satellite0 is not aimed towards star12, satellite0 is not aimed towards star16, satellite0 is not pointing to groundstation1, satellite0 is not pointing to groundstation2, satellite0 is not pointing to groundstation8, satellite0 is not pointing to groundstation9, satellite0 is not pointing to phenomenon14, satellite0 is not pointing to star3, satellite0 is not pointing to star4, satellite1 does not have instrument0 on board, satellite1 does not have instrument1 on board, satellite1 does not have instrument2 on board, satellite1 is not aimed towards groundstation9, satellite1 is not aimed towards phenomenon15, satellite1 is not aimed towards star12, satellite1 is not pointing to groundstation6, satellite1 is not pointing to star16, satellite1 is not pointing to star3, satellite1 is not pointing to star4, spectrograph2 is not compatible with instrument0, spectrograph2 is not compatible with instrument2, spectrograph2 is not compatible with instrument4, spectrograph2 is not supported by instrument3, spectrograph4 is not supported by instrument0, spectrograph4 is not supported by instrument4, star10 is not where satellite1 is pointed, there is no image of direction groundstation0 in image5, there is no image of direction groundstation0 in spectrograph4, there is no image of direction groundstation0 in thermograph3, there is no image of direction groundstation1 in image1, there is no image of direction groundstation1 in image6, there is no image of direction groundstation1 in spectrograph4, there is no image of direction groundstation1 in thermograph3, there is no image of direction groundstation2 in image5, there is no image of direction groundstation2 in spectrograph2, there is no image of direction groundstation5 in image1, there is no image of direction groundstation5 in spectrograph2, there is no image of direction groundstation5 in spectrograph4, there is no image of direction groundstation5 in thermograph3, there is no image of direction groundstation6 in image0, there is no image of direction groundstation6 in image5, there is no image of direction groundstation6 in infrared7, there is no image of direction groundstation6 in spectrograph2, there is no image of direction groundstation7 in image0, there is no image of direction groundstation7 in image1, there is no image of direction groundstation7 in infrared7, there is no image of direction groundstation7 in thermograph3, there is no image of direction groundstation8 in image1, there is no image of direction groundstation9 in image0, there is no image of direction groundstation9 in image5, there is no image of direction groundstation9 in image6, there is no image of direction groundstation9 in infrared7, there is no image of direction groundstation9 in spectrograph2, there is no image of direction groundstation9 in spectrograph4, there is no image of direction groundstation9 in thermograph3, there is no image of direction phenomenon14 in image5, there is no image of direction phenomenon14 in infrared7, there is no image of direction phenomenon14 in spectrograph4, there is no image of direction phenomenon14 in thermograph3, there is no image of direction phenomenon15 in image5, there is no image of direction phenomenon15 in image6, there is no image of direction planet11 in spectrograph2, there is no image of direction planet11 in spectrograph4, there is no image of direction planet13 in image1, there is no image of direction planet13 in spectrograph4, there is no image of direction star10 in image1, there is no image of direction star10 in spectrograph2, there is no image of direction star10 in spectrograph4, there is no image of direction star10 in thermograph3, there is no image of direction star12 in image0, there is no image of direction star12 in image5, there is no image of direction star12 in image6, there is no image of direction star12 in infrared7, there is no image of direction star12 in spectrograph2, there is no image of direction star12 in spectrograph4, there is no image of direction star16 in image1, there is no image of direction star16 in infrared7, there is no image of direction star16 in spectrograph2, there is no image of direction star16 in thermograph3, there is no image of direction star3 in image0, there is no image of direction star3 in image1, there is no image of direction star3 in image5, there is no image of direction star3 in image6, there is no image of direction star3 in spectrograph4, there is no image of direction star3 in thermograph3, there is no image of direction star4 in image0, there is no image of direction star4 in image5, there is no image of direction star4 in image6, there is no image of direction star4 in spectrograph4, there is no image of direction star4 in thermograph3, thermograph3 is not compatible with instrument0, thermograph3 is not compatible with instrument1, thermograph3 is not compatible with instrument4 and thermograph3 is not supported by instrument2", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument2 for star4 is complete, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, for groundstation9, instrument3 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image1 is supported by instrument3, image5 is compatible with instrument1, image6 is supported by instrument0, image6 is supported by instrument1, instrument0 is calibrated for star3, instrument1 is calibrated for groundstation0, instrument1 is on board satellite0, instrument4 supports infrared7, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has power available, satellite0 is pointing to groundstation2, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is aimed towards planet13, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated with respect to groundstation0, then satellite0 reorients from groundstation0 to planet11, captures an image of planet11 using instrument1 in image5, captures another image of planet11 using instrument1 in image6, reorients from planet11 to planet13, captures an image of planet13 using instrument1 in image5, and captures a spectrograph of planet13 using instrument1 on satellite0 in spectrograph2, before finally reorienting to star10 from planet13 to reach the current state. In this state, if instrument1 on satellite0 captures an image of star10 in image6, what are all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "The following calibrations have been completed: instrument1 for groundstation6, instrument2 for star4, instrument0 for groundstation7, instrument4 for groundstation8, and instrument3 for groundstation9. Additionally, the following instrument-image compatibility has been established: instrument2 supports image0, image1, and image5 is compatible with instrument1, image1 is also supported by instrument2 and instrument3, and image6 is supported by instrument0 and instrument1. Furthermore, the calibration status of instruments on satellites is as follows: instrument0 is calibrated for star3, instrument1 is calibrated for groundstation0 and is on board satellite0, and satellite0 also carries instrument2 and instrument3 on board. Satellite0 has power available and is currently pointing to groundstation2. Satellite1, which carries instrument4 on board, has power and is aimed towards planet13. Instrument4 supports infrared7. Lastly, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is supported by instrument3."}
{"question_id": "92d9f371-4278-44cd-b77d-5d74cd95ce78", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite1 is turned on, from star3, satellite1 turns to star1, calibration of instrument1 which is on satellite1 to star1 is complete, satellite1 turns from star1 to phenomenon10, satellite1's instrument1 takes an image of phenomenon10 in image5, image of phenomenon10 is taken with instrument1 on satellite1 in spectrograph3, from phenomenon10, satellite1 turns to phenomenon11, instrument1 which is on satellite1 takes an image of phenomenon11 in spectrograph1, from phenomenon11, satellite1 turns to phenomenon5, image of phenomenon5 is taken with instrument1 on satellite1 in image4, instrument1 which is on satellite1 takes an image of phenomenon5 in image5, satellite1 turns to phenomenon7 from phenomenon5, image of phenomenon7 is taken with instrument1 on satellite1 in image0, satellite1's instrument1 takes an image of phenomenon7 in image4, satellite1 turns to phenomenon9 from phenomenon7, instrument1 which is on satellite1 takes an image of phenomenon9 in image5, instrument1 which is on satellite1 takes an image of phenomenon9 in spectrograph1, from phenomenon9, satellite1 turns to planet8 and instrument1 which is on satellite1 takes an image of planet8 in image5 to reach the current state. In this state, if satellite1 turns to star6 from planet8, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "calibration of instrument0 for groundstation0 is incomplete, calibration of instrument0 for phenomenon11 is incomplete, calibration of instrument0 for star3 is incomplete, calibration of instrument1 for groundstation0 is incomplete, calibration of instrument1 for groundstation4 is incomplete, calibration of instrument1 for phenomenon5 is incomplete, calibration of instrument1 for star1 is complete, calibration of instrument1 for star3 is incomplete, calibration of instrument1 is complete, for groundstation2, instrument0 is not calibrated, for groundstation2, instrument1 is not calibrated, for phenomenon11, instrument1 is not calibrated, for phenomenon7, instrument0 is not calibrated, for phenomenon7, instrument1 is not calibrated, for phenomenon9, instrument0 is not calibrated, for planet8, instrument0 is not calibrated, for planet8, instrument1 is not calibrated, for star1, instrument0 is calibrated, groundstation0 is not where satellite0 is pointed, groundstation2 is not where satellite1 is pointed, groundstation4 is not where satellite1 is pointed, image of groundstation0 does not exist in image5, image of groundstation2 does not exist in image0, image of groundstation2 does not exist in spectrograph1, image of groundstation4 does not exist in image0, image of groundstation4 does not exist in image5, image of groundstation4 does not exist in spectrograph1, image of groundstation4 does not exist in spectrograph3, image of phenomenon10 does not exist in image0, image of phenomenon10 does not exist in image2, image of phenomenon10 does not exist in image4, image of phenomenon11 does not exist in image0, image of phenomenon11 does not exist in image2, image of phenomenon5 does not exist in image2, image of phenomenon5 does not exist in spectrograph1, image of phenomenon5 does not exist in spectrograph3, image of phenomenon7 does not exist in spectrograph3, image of phenomenon9 does not exist in image0, image of phenomenon9 does not exist in image4, image of planet8 does not exist in image0, image of planet8 does not exist in spectrograph1, image of planet8 exists in image5, image of star1 does not exist in image0, image of star1 does not exist in image2, image of star3 does not exist in image0, image of star3 does not exist in image4, image of star3 does not exist in image5, image of star3 does not exist in spectrograph1, image of star6 does not exist in image2, image of star6 does not exist in image5, image of star6 does not exist in spectrograph1, image of star6 does not exist in spectrograph3, image0 is compatible with instrument1, image0 is not supported by instrument0, image2 is compatible with instrument1, image2 is supported by instrument0, image4 is compatible with instrument1, image4 is not compatible with instrument0, image5 is not compatible with instrument0, image5 is supported by instrument1, instrument0 does not support spectrograph3, instrument0 is not calibrated, instrument0 is not calibrated for groundstation4, instrument0 is not calibrated for phenomenon10, instrument0 is not calibrated for phenomenon5, instrument0 is not calibrated for star6, instrument0 is not on board satellite1, instrument0 is not turned on, instrument0 is on board satellite0, instrument1 is not calibrated for phenomenon10, instrument1 is not calibrated for phenomenon9, instrument1 is not calibrated for star6, instrument1 is not on board satellite0, instrument1 is on board satellite1, instrument1 is switched on, instrument1 supports spectrograph3, phenomenon10 is not where satellite1 is pointed, phenomenon5 is not where satellite1 is pointed, phenomenon5 is where satellite0 is pointed, phenomenon7 is not where satellite1 is pointed, phenomenon9 is not where satellite0 is pointed, power is available for satellite0, power is not available for satellite1, satellite0 is not aimed towards phenomenon10, satellite0 is not aimed towards planet8, satellite0 is not pointing to groundstation2, satellite0 is not pointing to groundstation4, satellite0 is not pointing to phenomenon11, satellite0 is not pointing to phenomenon7, satellite1 is not aimed towards phenomenon9, satellite1 is not aimed towards planet8, satellite1 is not pointing to groundstation0, satellite1 is not pointing to phenomenon11, satellite1 is not pointing to star1, satellite1 is pointing to star6, spectrograph1 is compatible with instrument1, spectrograph1 is not supported by instrument0, star1 is not where satellite0 is pointed, star3 is not where satellite0 is pointed, star3 is not where satellite1 is pointed, star6 is not where satellite0 is pointed, there is an image of phenomenon10 in image5, there is an image of phenomenon10 in spectrograph3, there is an image of phenomenon11 in spectrograph1, there is an image of phenomenon5 in image4, there is an image of phenomenon5 in image5, there is an image of phenomenon7 in image0, there is an image of phenomenon7 in image4, there is an image of phenomenon9 in image5, there is an image of phenomenon9 in spectrograph1, there is no image of direction groundstation0 in image0, there is no image of direction groundstation0 in image2, there is no image of direction groundstation0 in image4, there is no image of direction groundstation0 in spectrograph1, there is no image of direction groundstation0 in spectrograph3, there is no image of direction groundstation2 in image2, there is no image of direction groundstation2 in image4, there is no image of direction groundstation2 in image5, there is no image of direction groundstation2 in spectrograph3, there is no image of direction groundstation4 in image2, there is no image of direction groundstation4 in image4, there is no image of direction phenomenon10 in spectrograph1, there is no image of direction phenomenon11 in image4, there is no image of direction phenomenon11 in image5, there is no image of direction phenomenon11 in spectrograph3, there is no image of direction phenomenon5 in image0, there is no image of direction phenomenon7 in image2, there is no image of direction phenomenon7 in image5, there is no image of direction phenomenon7 in spectrograph1, there is no image of direction phenomenon9 in image2, there is no image of direction phenomenon9 in spectrograph3, there is no image of direction planet8 in image2, there is no image of direction planet8 in image4, there is no image of direction planet8 in spectrograph3, there is no image of direction star1 in image4, there is no image of direction star1 in image5, there is no image of direction star1 in spectrograph1, there is no image of direction star1 in spectrograph3, there is no image of direction star3 in image2, there is no image of direction star3 in spectrograph3, there is no image of direction star6 in image0 and there is no image of direction star6 in image4", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for star1 is complete, image0 is compatible with instrument1, image2 is compatible with instrument0, image2 is compatible with instrument1, image5 is supported by instrument1, instrument1 is calibrated for star1, instrument1 supports image4, instrument1 supports spectrograph3, satellite0 carries instrument0 on board, satellite0 has power available, satellite0 is pointing to phenomenon5, satellite1 has instrument1 on board, satellite1 has power available, satellite1 is pointing to star3 and spectrograph1 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the instrument1 on satellite1 is activated, satellite1 rotates from star3 to star1, instrument1 on satellite1 is calibrated to star1, then satellite1 turns from star1 to phenomenon10, and captures an image of phenomenon10 in image5 and spectrograph3 using instrument1, next satellite1 turns from phenomenon10 to phenomenon11, and instrument1 on satellite1 captures an image of phenomenon11 in spectrograph1, then satellite1 turns from phenomenon11 to phenomenon5, and images of phenomenon5 are taken with instrument1 on satellite1 in image4 and image5, subsequently satellite1 turns from phenomenon5 to phenomenon7, and images of phenomenon7 are captured with instrument1 on satellite1 in image0 and image4, then satellite1 turns from phenomenon7 to phenomenon9, and instrument1 on satellite1 captures images of phenomenon9 in image5 and spectrograph1, finally satellite1 turns from phenomenon9 to planet8 and captures an image of planet8 in image5, resulting in the current state. In this state, if satellite1 turns to star6 from planet8, what are all the valid properties of the state (including both affirmative and negated properties)? If there are none, state None.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star1 has been completed. Image0 is found to be compatible with instrument1, while image2 is compatible with both instrument0 and instrument1. Additionally, image5 is supported by instrument1. Instrument1 has also been calibrated for star1 and provides support for image4 and spectrograph3. Satellite0 is equipped with instrument0 and has sufficient power, with its current focus on phenomenon5. Satellite1, on the other hand, is equipped with instrument1, has available power, and is currently pointing towards star3. Furthermore, spectrograph1 is supported by instrument1."}
{"question_id": "998b30f4-15b9-4b49-ba05-c8231637af38", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite0, instrument1 is switched on, satellite0 turns to groundstation0 from groundstation2, instrument1 is calibrated on satellite0 to groundstation0, from groundstation0, satellite0 turns to planet11, instrument1 which is on satellite0 takes an image of planet11 in image5, satellite0's instrument1 takes an image of planet11 in image6, satellite0 turns to planet13 from planet11, image of planet13 is taken with instrument1 on satellite0 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2, satellite0 turns to star10 from planet13, satellite0's instrument1 takes an image of star10 in image6, instrument1 which is on satellite0 takes an image of star10 in spectrograph2, instrument1 on satellite0 is switched off, instrument2 that is on satellite0 is turned on, satellite0 turns from star10 to star4, instrument2 is calibrated on satellite0 to star4, satellite0 turns from star4 to star16, image of star16 is taken with instrument2 on satellite0 in image0 and instrument2 that is on satellite0 is turned off to reach the current state. In this state, if on satellite0, instrument3 is switched on, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "calibration of instrument0 for groundstation5 is incomplete, calibration of instrument0 for groundstation8 is incomplete, calibration of instrument0 for phenomenon14 is incomplete, calibration of instrument0 for planet11 is incomplete, calibration of instrument0 for star4 is incomplete, calibration of instrument0 is incomplete, calibration of instrument1 for groundstation2 is incomplete, calibration of instrument1 for star16 is incomplete, calibration of instrument1 for star4 is incomplete, calibration of instrument2 for groundstation1 is incomplete, calibration of instrument2 for phenomenon14 is incomplete, calibration of instrument2 for phenomenon15 is incomplete, calibration of instrument2 for planet11 is incomplete, calibration of instrument2 for planet13 is incomplete, calibration of instrument2 for star12 is incomplete, calibration of instrument3 for groundstation2 is incomplete, calibration of instrument3 for groundstation5 is incomplete, calibration of instrument3 for star10 is incomplete, calibration of instrument3 is incomplete, calibration of instrument4 for groundstation0 is incomplete, calibration of instrument4 for groundstation2 is incomplete, calibration of instrument4 for groundstation5 is incomplete, calibration of instrument4 for groundstation7 is incomplete, calibration of instrument4 for star12 is incomplete, calibration of instrument4 is incomplete, for groundstation0, instrument0 is not calibrated, for groundstation0, instrument2 is not calibrated, for groundstation0, instrument3 is not calibrated, for groundstation1, instrument0 is not calibrated, for groundstation1, instrument1 is not calibrated, for groundstation2, instrument0 is not calibrated, for groundstation2, instrument2 is not calibrated, for groundstation5, instrument1 is not calibrated, for groundstation6, instrument0 is not calibrated, for groundstation6, instrument3 is not calibrated, for groundstation7, instrument2 is not calibrated, for groundstation8, instrument1 is not calibrated, for groundstation8, instrument3 is not calibrated, for groundstation9, instrument1 is not calibrated, for phenomenon15, instrument1 is not calibrated, for phenomenon15, instrument3 is not calibrated, for phenomenon15, instrument4 is not calibrated, for planet11, instrument1 is not calibrated, for planet13, instrument0 is not calibrated, for planet13, instrument1 is not calibrated, for star12, instrument0 is not calibrated, for star12, instrument3 is not calibrated, for star16, instrument0 is not calibrated, for star16, instrument2 is not calibrated, for star3, instrument3 is not calibrated, for star3, instrument4 is not calibrated, groundstation1 is not where satellite0 is pointed, groundstation5 is not where satellite0 is pointed, groundstation6 is not where satellite1 is pointed, image of groundstation0 does not exist in image1, image of groundstation0 does not exist in image6, image of groundstation0 does not exist in infrared7, image of groundstation1 does not exist in image1, image of groundstation1 does not exist in infrared7, image of groundstation1 does not exist in spectrograph4, image of groundstation2 does not exist in image5, image of groundstation2 does not exist in image6, image of groundstation2 does not exist in infrared7, image of groundstation2 does not exist in spectrograph2, image of groundstation2 does not exist in spectrograph4, image of groundstation2 does not exist in thermograph3, image of groundstation5 does not exist in image1, image of groundstation5 does not exist in image5, image of groundstation5 does not exist in image6, image of groundstation5 does not exist in infrared7, image of groundstation6 does not exist in image0, image of groundstation6 does not exist in spectrograph4, image of groundstation6 does not exist in thermograph3, image of groundstation7 does not exist in image0, image of groundstation7 does not exist in image5, image of groundstation7 does not exist in spectrograph2, image of groundstation8 does not exist in image5, image of groundstation8 does not exist in image6, image of groundstation8 does not exist in spectrograph2, image of groundstation9 does not exist in image5, image of groundstation9 does not exist in image6, image of groundstation9 does not exist in infrared7, image of groundstation9 does not exist in spectrograph4, image of groundstation9 does not exist in thermograph3, image of phenomenon14 does not exist in image0, image of phenomenon14 does not exist in image5, image of phenomenon14 does not exist in infrared7, image of phenomenon14 does not exist in spectrograph4, image of phenomenon15 does not exist in image6, image of phenomenon15 does not exist in spectrograph4, image of planet11 does not exist in image0, image of planet11 does not exist in image1, image of planet11 does not exist in spectrograph2, image of planet11 does not exist in spectrograph4, image of planet13 does not exist in image1, image of planet13 does not exist in image6, image of star10 does not exist in image0, image of star10 does not exist in image5, image of star10 does not exist in infrared7, image of star10 does not exist in spectrograph4, image of star12 does not exist in image1, image of star12 does not exist in image5, image of star12 does not exist in image6, image of star12 does not exist in spectrograph2, image of star12 does not exist in spectrograph4, image of star16 does not exist in image1, image of star16 does not exist in image6, image of star16 does not exist in spectrograph4, image of star3 does not exist in image1, image of star3 does not exist in infrared7, image of star3 does not exist in thermograph3, image of star4 does not exist in image0, image of star4 does not exist in image1, image of star4 does not exist in image6, image of star4 does not exist in infrared7, image of star4 does not exist in spectrograph4, image0 is not compatible with instrument0, image0 is not compatible with instrument4, image1 is not compatible with instrument1, image5 is not compatible with instrument4, image5 is not supported by instrument0, image5 is not supported by instrument2, image6 is not compatible with instrument3, image6 is not supported by instrument2, image6 is not supported by instrument4, infrared7 is not compatible with instrument0, infrared7 is not compatible with instrument2, infrared7 is not supported by instrument3, instrument0 does not support image1, instrument0 does not support spectrograph4, instrument0 is not calibrated for groundstation9, instrument0 is not calibrated for phenomenon15, instrument0 is not calibrated for star10, instrument0 is not powered on, instrument1 does not support image0, instrument1 does not support infrared7, instrument1 is not calibrated for groundstation7, instrument1 is not calibrated for phenomenon14, instrument1 is not calibrated for star10, instrument1 is not calibrated for star12, instrument1 is not calibrated for star3, instrument1 is not on board satellite1, instrument1 is not turned on, instrument2 does not support spectrograph2, instrument2 does not support spectrograph4, instrument2 is not calibrated for groundstation5, instrument2 is not calibrated for groundstation6, instrument2 is not calibrated for groundstation8, instrument2 is not calibrated for groundstation9, instrument2 is not calibrated for star10, instrument2 is not calibrated for star3, instrument2 is not on board satellite1, instrument2 is not turned on, instrument3 does not support image0, instrument3 does not support image5, instrument3 does not support spectrograph2, instrument3 is not calibrated for groundstation1, instrument3 is not calibrated for groundstation7, instrument3 is not calibrated for phenomenon14, instrument3 is not calibrated for planet11, instrument3 is not calibrated for planet13, instrument3 is not calibrated for star16, instrument3 is not calibrated for star4, instrument4 does not support spectrograph2, instrument4 is not calibrated for groundstation1, instrument4 is not calibrated for groundstation6, instrument4 is not calibrated for groundstation9, instrument4 is not calibrated for phenomenon14, instrument4 is not calibrated for planet11, instrument4 is not calibrated for planet13, instrument4 is not calibrated for star10, instrument4 is not calibrated for star16, instrument4 is not calibrated for star4, instrument4 is not turned on, phenomenon14 is not where satellite0 is pointed, phenomenon15 is not where satellite0 is pointed, phenomenon15 is not where satellite1 is pointed, planet11 is not where satellite1 is pointed, satellite0 does not carry instrument4 on board, satellite0 does not have power, satellite0 is not aimed towards groundstation0, satellite0 is not aimed towards groundstation7, satellite0 is not aimed towards groundstation8, satellite0 is not aimed towards groundstation9, satellite0 is not aimed towards planet13, satellite0 is not aimed towards star10, satellite0 is not aimed towards star12, satellite0 is not pointing to groundstation2, satellite0 is not pointing to groundstation6, satellite0 is not pointing to planet11, satellite0 is not pointing to star3, satellite1 does not carry instrument0 on board, satellite1 does not have instrument3 on board, satellite1 is not aimed towards groundstation5, satellite1 is not aimed towards groundstation7, satellite1 is not aimed towards groundstation9, satellite1 is not aimed towards star16, satellite1 is not pointing to groundstation0, satellite1 is not pointing to groundstation1, satellite1 is not pointing to groundstation2, satellite1 is not pointing to groundstation8, satellite1 is not pointing to phenomenon14, satellite1 is not pointing to star12, satellite1 is not pointing to star3, spectrograph2 is not compatible with instrument0, spectrograph4 is not supported by instrument1, spectrograph4 is not supported by instrument4, star10 is not where satellite1 is pointed, star4 is not where satellite0 is pointed, star4 is not where satellite1 is pointed, there is no image of direction groundstation0 in image0, there is no image of direction groundstation0 in image5, there is no image of direction groundstation0 in spectrograph2, there is no image of direction groundstation0 in spectrograph4, there is no image of direction groundstation0 in thermograph3, there is no image of direction groundstation1 in image0, there is no image of direction groundstation1 in image5, there is no image of direction groundstation1 in image6, there is no image of direction groundstation1 in spectrograph2, there is no image of direction groundstation1 in thermograph3, there is no image of direction groundstation2 in image0, there is no image of direction groundstation2 in image1, there is no image of direction groundstation5 in image0, there is no image of direction groundstation5 in spectrograph2, there is no image of direction groundstation5 in spectrograph4, there is no image of direction groundstation5 in thermograph3, there is no image of direction groundstation6 in image1, there is no image of direction groundstation6 in image5, there is no image of direction groundstation6 in image6, there is no image of direction groundstation6 in infrared7, there is no image of direction groundstation6 in spectrograph2, there is no image of direction groundstation7 in image1, there is no image of direction groundstation7 in image6, there is no image of direction groundstation7 in infrared7, there is no image of direction groundstation7 in spectrograph4, there is no image of direction groundstation7 in thermograph3, there is no image of direction groundstation8 in image0, there is no image of direction groundstation8 in image1, there is no image of direction groundstation8 in infrared7, there is no image of direction groundstation8 in spectrograph4, there is no image of direction groundstation8 in thermograph3, there is no image of direction groundstation9 in image0, there is no image of direction groundstation9 in image1, there is no image of direction groundstation9 in spectrograph2, there is no image of direction phenomenon14 in image1, there is no image of direction phenomenon14 in image6, there is no image of direction phenomenon14 in spectrograph2, there is no image of direction phenomenon14 in thermograph3, there is no image of direction phenomenon15 in image0, there is no image of direction phenomenon15 in image1, there is no image of direction phenomenon15 in image5, there is no image of direction phenomenon15 in infrared7, there is no image of direction phenomenon15 in spectrograph2, there is no image of direction phenomenon15 in thermograph3, there is no image of direction planet11 in infrared7, there is no image of direction planet11 in thermograph3, there is no image of direction planet13 in image0, there is no image of direction planet13 in infrared7, there is no image of direction planet13 in spectrograph4, there is no image of direction planet13 in thermograph3, there is no image of direction star10 in image1, there is no image of direction star10 in thermograph3, there is no image of direction star12 in image0, there is no image of direction star12 in infrared7, there is no image of direction star12 in thermograph3, there is no image of direction star16 in image5, there is no image of direction star16 in infrared7, there is no image of direction star16 in spectrograph2, there is no image of direction star16 in thermograph3, there is no image of direction star3 in image0, there is no image of direction star3 in image5, there is no image of direction star3 in image6, there is no image of direction star3 in spectrograph2, there is no image of direction star3 in spectrograph4, there is no image of direction star4 in image5, there is no image of direction star4 in spectrograph2, there is no image of direction star4 in thermograph3, thermograph3 is not compatible with instrument0, thermograph3 is not compatible with instrument1, thermograph3 is not supported by instrument2 and thermograph3 is not supported by instrument4", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for groundstation6 is complete, calibration of instrument2 for star4 is complete, for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, for groundstation9, instrument3 is calibrated, image0 is supported by instrument2, image1 is compatible with instrument4, image1 is supported by instrument2, image1 is supported by instrument3, image5 is compatible with instrument1, image6 is supported by instrument0, image6 is supported by instrument1, instrument0 is calibrated for star3, instrument1 is calibrated for groundstation0, instrument1 is on board satellite0, instrument4 supports infrared7, satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has power available, satellite0 is pointing to groundstation2, satellite1 carries instrument4 on board, satellite1 has power, satellite1 is aimed towards planet13, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: on satellite0, instrument1 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, then satellite0 reorients from groundstation0 to planet11, instrument1 on satellite0 captures an image of planet11 in image5, another image of planet11 is taken by satellite0's instrument1 in image6, satellite0 reorients from planet11 to planet13, an image of planet13 is captured with instrument1 on satellite0 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2, satellite0 reorients from planet13 to star10, satellite0's instrument1 captures an image of star10 in image6, instrument1 on satellite0 takes an image of star10 in spectrograph2, instrument1 on satellite0 is deactivated, instrument2 on satellite0 is activated, satellite0 reorients from star10 to star4, instrument2 is calibrated on satellite0 to star4, satellite0 reorients from star4 to star16, an image of star16 is captured with instrument2 on satellite0 in image0, and instrument2 on satellite0 is deactivated to reach the current state. In this state, if instrument3 is activated on satellite0, what would be all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "The following calibrations have been completed: instrument1 for groundstation6, instrument2 for star4, instrument0 for groundstation7, instrument4 for groundstation8, and instrument3 for groundstation9. Additionally, the following instrument-image compatibility has been established: instrument2 supports image0, image1 is compatible with instrument4, and image1 is also supported by instrument2 and instrument3. Furthermore, image5 is compatible with instrument1, and image6 is supported by both instrument0 and instrument1. The calibration status of instruments for specific targets is as follows: instrument0 is calibrated for star3, and instrument1 is calibrated for groundstation0. The satellite configuration is as follows: instrument0, instrument1, and instrument3 are on board satellite0, while instrument4 is on board satellite1. Satellite0 has power available and is currently pointing towards groundstation2, whereas satellite1 has power and is aimed towards planet13. Lastly, spectrograph2 is supported by instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is supported by instrument3."}
{"question_id": "5f7e69dd-06ed-42dc-bc34-43e689a2a26f", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on to reach the current state. In this state, if instrument0 that is on satellite0 is turned on, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "calibration of instrument0 for star9 is complete, calibration of instrument2 for groundstation5 is complete, calibration of instrument3 for star6 is complete, for groundstation5, instrument3 is calibrated, groundstation3 is where satellite0 is pointed, image0 is supported by instrument1, image2 is compatible with instrument2, image2 is compatible with instrument3, image3 is supported by instrument0, image3 is supported by instrument2, infrared1 is compatible with instrument0, infrared1 is supported by instrument1, instrument0 is calibrated for star1, instrument0 is on board satellite0, instrument0 is switched on, instrument1 is calibrated for groundstation0, instrument1 supports image3, instrument2 is calibrated for groundstation7, instrument2 is calibrated for star9, instrument3 is calibrated for star8, instrument3 is powered on, instrument3 supports image0, instrument3 supports image3, satellite0 carries instrument2 on board, satellite0 has instrument1 on board, satellite1 carries instrument3 on board and satellite1 is aimed towards phenomenon10", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image2 is compatible with instrument2, image2 is supported by instrument3, image3 is compatible with instrument3, image3 is supported by instrument2, infrared1 is compatible with instrument1, instrument0 is on board satellite0, instrument0 supports image3, instrument0 supports infrared1, instrument1 supports image3, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, if instrument0 on satellite0 is activated, what are all the valid properties of the state that do not include negations? If there are no such properties, state None.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is also complete, and instrument3's calibration for star6 is finished. Additionally, instrument0 has been calibrated for star1, instrument3 for star8, and instrument0 for star9. Satellite0 is currently directed towards groundstation3. Image0 is compatible with instrument1, while image2 is compatible with instrument2 and supported by instrument3. Image3 is compatible with instrument3 and supported by instrument2. Infrared1 is compatible with instrument1. Instrument0 is installed on satellite0, supporting both image3 and infrared1. Instrument1 also supports image3. Instrument2 has been calibrated for groundstation5 and groundstation7, and instrument3 has been calibrated for groundstation5. Instrument3 supports image0. Satellite0 is equipped with instrument1 and instrument2 on board, and it has available power. Satellite1 has instrument3 on board, available power, and is currently pointing towards phenomenon10."}
{"question_id": "b23e4ad5-d3a8-401a-b5df-4536e6025843", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, on satellite0, instrument0 is switched on, satellite1 turns to star6 from groundstation4, instrument3 that is on satellite1 is calibrated to star6, from star6, satellite1 turns to planet14, instrument3 which is on satellite1 takes an image of planet14 in spectrograph1, satellite1 turns from planet14 to star10, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, satellite1 turns from star10 to star12 and instrument3 which is on satellite1 takes an image of star12 in spectrograph1 to reach the current state. In this state, if satellite1 turns to star0 from star12, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "calibration of instrument0 for groundstation5 is incomplete, calibration of instrument0 for star12 is incomplete, calibration of instrument0 for star13 is incomplete, calibration of instrument0 for star16 is incomplete, calibration of instrument0 for star6 is incomplete, calibration of instrument0 is incomplete, calibration of instrument1 for star0 is incomplete, calibration of instrument1 for star1 is incomplete, calibration of instrument1 for star10 is incomplete, calibration of instrument1 for star11 is incomplete, calibration of instrument1 for star12 is incomplete, calibration of instrument1 for star13 is incomplete, calibration of instrument1 for star16 is incomplete, calibration of instrument2 for groundstation5 is incomplete, calibration of instrument2 for phenomenon15 is incomplete, calibration of instrument2 for star1 is incomplete, calibration of instrument2 for star13 is incomplete, calibration of instrument2 is incomplete, calibration of instrument3 for groundstation2 is incomplete, calibration of instrument3 for groundstation5 is incomplete, calibration of instrument3 for groundstation9 is incomplete, calibration of instrument3 for star0 is incomplete, calibration of instrument3 for star10 is incomplete, calibration of instrument3 for star16 is incomplete, calibration of instrument3 for star7 is incomplete, calibration of instrument3 for star8 is incomplete, for groundstation3, instrument2 is not calibrated, for groundstation4, instrument3 is not calibrated, for groundstation9, instrument1 is not calibrated, for phenomenon15, instrument3 is not calibrated, for planet14, instrument0 is not calibrated, for planet14, instrument1 is not calibrated, for planet14, instrument2 is not calibrated, for star1, instrument0 is not calibrated, for star1, instrument3 is not calibrated, for star10, instrument0 is not calibrated, for star11, instrument0 is not calibrated, for star11, instrument2 is not calibrated, for star11, instrument3 is not calibrated, for star12, instrument3 is not calibrated, for star13, instrument3 is not calibrated, for star7, instrument1 is not calibrated, groundstation4 is not where satellite1 is pointed, groundstation5 is not where satellite1 is pointed, image of groundstation2 does not exist in infrared3, image of groundstation2 does not exist in spectrograph0, image of groundstation2 does not exist in spectrograph2, image of groundstation2 does not exist in thermograph4, image of groundstation3 does not exist in infrared3, image of groundstation3 does not exist in spectrograph0, image of groundstation3 does not exist in spectrograph2, image of groundstation4 does not exist in infrared3, image of groundstation4 does not exist in spectrograph1, image of groundstation5 does not exist in spectrograph0, image of groundstation5 does not exist in spectrograph2, image of groundstation5 does not exist in thermograph4, image of groundstation9 does not exist in spectrograph0, image of groundstation9 does not exist in spectrograph1, image of phenomenon15 does not exist in infrared3, image of phenomenon15 does not exist in thermograph4, image of planet14 does not exist in infrared3, image of planet14 does not exist in spectrograph0, image of planet14 does not exist in spectrograph2, image of planet14 does not exist in thermograph4, image of star0 does not exist in spectrograph0, image of star0 does not exist in spectrograph1, image of star0 does not exist in spectrograph2, image of star0 does not exist in thermograph4, image of star1 does not exist in spectrograph0, image of star1 does not exist in spectrograph1, image of star1 does not exist in spectrograph2, image of star1 does not exist in thermograph4, image of star10 does not exist in infrared3, image of star10 does not exist in spectrograph0, image of star10 does not exist in spectrograph2, image of star11 does not exist in infrared3, image of star11 does not exist in spectrograph0, image of star11 does not exist in spectrograph1, image of star11 does not exist in spectrograph2, image of star12 does not exist in spectrograph2, image of star12 does not exist in thermograph4, image of star13 does not exist in infrared3, image of star13 does not exist in spectrograph1, image of star13 does not exist in thermograph4, image of star16 does not exist in spectrograph0, image of star16 does not exist in spectrograph1, image of star16 does not exist in thermograph4, image of star6 does not exist in infrared3, image of star6 does not exist in spectrograph0, image of star6 does not exist in spectrograph1, image of star6 does not exist in spectrograph2, image of star6 does not exist in thermograph4, image of star7 does not exist in infrared3, image of star7 does not exist in spectrograph1, image of star7 does not exist in spectrograph2, image of star8 does not exist in spectrograph1, image of star8 does not exist in thermograph4, infrared3 is not supported by instrument1, instrument0 does not support infrared3, instrument0 is not calibrated for groundstation3, instrument0 is not calibrated for groundstation9, instrument0 is not calibrated for phenomenon15, instrument0 is not calibrated for star7, instrument0 is not calibrated for star8, instrument0 is not on board satellite1, instrument1 does not support spectrograph2, instrument1 is not calibrated, instrument1 is not calibrated for groundstation3, instrument1 is not calibrated for groundstation5, instrument1 is not calibrated for phenomenon15, instrument1 is not calibrated for star6, instrument1 is not on board satellite1, instrument1 is not turned on, instrument2 does not support spectrograph1, instrument2 is not calibrated for groundstation2, instrument2 is not calibrated for star0, instrument2 is not calibrated for star10, instrument2 is not calibrated for star12, instrument2 is not calibrated for star16, instrument2 is not calibrated for star6, instrument2 is not calibrated for star8, instrument2 is not turned on, instrument3 does not support infrared3, instrument3 does not support thermograph4, instrument3 is not calibrated for groundstation3, instrument3 is not calibrated for planet14, planet14 is not where satellite1 is pointed, satellite0 does not carry instrument2 on board, satellite0 does not have instrument3 on board, satellite0 does not have power available, satellite0 is not aimed towards groundstation2, satellite0 is not aimed towards groundstation5, satellite0 is not aimed towards groundstation9, satellite0 is not aimed towards planet14, satellite0 is not aimed towards star0, satellite0 is not aimed towards star10, satellite0 is not aimed towards star8, satellite0 is not pointing to groundstation3, satellite0 is not pointing to groundstation4, satellite0 is not pointing to phenomenon15, satellite0 is not pointing to star11, satellite0 is not pointing to star6, satellite1 does not have power, satellite1 is not aimed towards groundstation3, satellite1 is not aimed towards star1, satellite1 is not aimed towards star10, satellite1 is not aimed towards star13, satellite1 is not aimed towards star7, satellite1 is not pointing to groundstation2, satellite1 is not pointing to groundstation9, satellite1 is not pointing to phenomenon15, satellite1 is not pointing to star11, satellite1 is not pointing to star12, satellite1 is not pointing to star16, satellite1 is not pointing to star6, spectrograph0 is not compatible with instrument3, spectrograph1 is not compatible with instrument0, spectrograph2 is not compatible with instrument0, star12 is not where satellite0 is pointed, star13 is not where satellite0 is pointed, star16 is not where satellite0 is pointed, star7 is not where satellite0 is pointed, star8 is not where satellite1 is pointed, there is no image of direction groundstation2 in spectrograph1, there is no image of direction groundstation3 in spectrograph1, there is no image of direction groundstation3 in thermograph4, there is no image of direction groundstation4 in spectrograph0, there is no image of direction groundstation4 in spectrograph2, there is no image of direction groundstation4 in thermograph4, there is no image of direction groundstation5 in infrared3, there is no image of direction groundstation5 in spectrograph1, there is no image of direction groundstation9 in infrared3, there is no image of direction groundstation9 in spectrograph2, there is no image of direction groundstation9 in thermograph4, there is no image of direction phenomenon15 in spectrograph0, there is no image of direction phenomenon15 in spectrograph1, there is no image of direction phenomenon15 in spectrograph2, there is no image of direction star0 in infrared3, there is no image of direction star1 in infrared3, there is no image of direction star10 in thermograph4, there is no image of direction star11 in thermograph4, there is no image of direction star12 in infrared3, there is no image of direction star12 in spectrograph0, there is no image of direction star13 in spectrograph0, there is no image of direction star13 in spectrograph2, there is no image of direction star16 in infrared3, there is no image of direction star16 in spectrograph2, there is no image of direction star7 in spectrograph0, there is no image of direction star7 in thermograph4, there is no image of direction star8 in infrared3, there is no image of direction star8 in spectrograph0, there is no image of direction star8 in spectrograph2, thermograph4 is not supported by instrument1 and thermograph4 is not supported by instrument2", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for star7 is complete, for groundstation4, instrument2 is calibrated, for star0, instrument0 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is compatible with instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for groundstation4, instrument0 is on board satellite0, instrument1 is calibrated for groundstation2, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument3 is calibrated for star6, satellite0 has power, satellite0 is aimed towards star1, satellite1 carries instrument3 on board, satellite1 has power, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph0 is compatible with instrument2, spectrograph1 is supported by instrument3, spectrograph2 is supported by instrument2, spectrograph2 is supported by instrument3 and thermograph4 is compatible with instrument0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 is activated on satellite1, instrument0 is activated on satellite0, satellite1 reorients from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, then satellite1 reorients from star6 to planet14, instrument3 on satellite1 captures an image of planet14 using spectrograph1, satellite1 reorients from planet14 to star10, instrument3 on satellite1 captures an image of star10 using spectrograph1, and finally, satellite1 reorients from star10 to star12 and instrument3 on satellite1 captures an image of star12 using spectrograph1, resulting in the current state. In this state, if satellite1 reorients from star12 to star0, what are all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star8 has been completed, instrument2's calibration for groundstation9 is also complete, and instrument2's calibration for star7 is finished. Instrument2 has been calibrated for groundstation4, and instrument0 has been calibrated for star0. Satellite1 is currently directed towards groundstation4. Infrared3 is found to be compatible with instrument2. Instrument0 has been calibrated for both groundstation2 and groundstation4. Instrument0 is installed on satellite0, and instrument1 has also been calibrated for groundstation2 and groundstation4, with instrument1 being on board satellite0 and supporting spectrograph1. Instrument2 is installed on satellite1. Instrument3 has been calibrated for star6. Satellite0 is operational and is currently aimed at star1. Satellite1 is equipped with instrument3 and has power. Spectrograph0 is compatible with instruments 0, 1, and 2. Spectrograph1 is supported by instrument3, and spectrograph2 is supported by both instruments 2 and 3. Additionally, thermograph4 is compatible with instrument0."}
{"question_id": "3124994c-4e21-4634-bfd6-04ffe6c20b5f", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, on satellite0, instrument0 is switched on, from groundstation4, satellite1 turns to star6, instrument3 is calibrated on satellite1 to star6, satellite1 turns to planet14 from star6, satellite1's instrument3 takes an image of planet14 in spectrograph1, from planet14, satellite1 turns to star10, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, from star10, satellite1 turns to star12 and instrument3 which is on satellite1 takes an image of star12 in spectrograph1 to reach the current state. In this state, if from star12, satellite1 turns to star0, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "calibration of instrument1 for groundstation4 is complete, calibration of instrument2 for groundstation4 is complete, for groundstation2, instrument0 is calibrated, for groundstation2, instrument1 is calibrated, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, for star8, instrument1 is calibrated, image of planet14 exists in spectrograph1, image of star12 exists in spectrograph1, infrared3 is compatible with instrument2, instrument0 is calibrated for groundstation4, instrument0 is calibrated for star0, instrument0 is powered on, instrument0 supports spectrograph0, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 supports spectrograph2, instrument3 is calibrated, instrument3 is turned on, satellite0 has instrument0 on board, satellite0 is pointing to star1, satellite1 carries instrument2 on board, satellite1 carries instrument3 on board, satellite1 is aimed towards star0, spectrograph0 is supported by instrument1, spectrograph0 is supported by instrument2, spectrograph1 is compatible with instrument3, spectrograph2 is compatible with instrument3, there is an image of star10 in spectrograph1 and thermograph4 is supported by instrument0", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for star7 is complete, for groundstation4, instrument2 is calibrated, for star0, instrument0 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is compatible with instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for groundstation4, instrument0 is on board satellite0, instrument1 is calibrated for groundstation2, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument3 is calibrated for star6, satellite0 has power, satellite0 is aimed towards star1, satellite1 carries instrument3 on board, satellite1 has power, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph0 is compatible with instrument2, spectrograph1 is supported by instrument3, spectrograph2 is supported by instrument2, spectrograph2 is supported by instrument3 and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 is activated on satellite1, instrument0 is activated on satellite0, satellite1 reorients from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, satellite1 then reorients from star6 to planet14, and instrument3 on satellite1 captures an image of planet14 using spectrograph1. Next, satellite1 reorients from planet14 to star10, and instrument3 on satellite1 captures an image of star10 using spectrograph1. Finally, satellite1 reorients from star10 to star12, and instrument3 on satellite1 captures an image of star12 using spectrograph1, resulting in the current state. If, from this state, satellite1 reorients from star12 to star0, what are all the valid properties of the resulting state that do not involve negations? If none exist, state 'None'.", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star8 has been completed, instrument2's calibration for groundstation9 is also complete, and instrument2's calibration for star7 is finished. Instrument2 has been calibrated for groundstation4, and instrument0 has been calibrated for star0. Satellite1 is currently directed towards groundstation4. In terms of compatibility, infrared3 is suitable for use with instrument2. Instrument0 has been calibrated for both groundstation2 and groundstation4. Additionally, instrument0 is installed on satellite0. Instrument1 has also been calibrated for groundstation2 and groundstation4, and it is also on board satellite0. Furthermore, instrument1 supports spectrograph1. Instrument2 is installed on satellite1. Instrument3 has been calibrated for star6. Satellite0 is operational and has power, and it is currently aimed at star1. Satellite1 is also operational and has power, and it carries instrument3 on board. Spectrograph0 is compatible with both instrument0 and instrument1, as well as instrument2. Spectrograph1 is supported by instrument3, and spectrograph2 is supported by both instrument2 and instrument3. Lastly, thermograph4 is compatible with instrument0."}
{"question_id": "6f843222-a300-43e3-b59e-1bc9cb59bd37", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is switched on to reach the current state. In this state, if satellite1 turns from star3 to star1, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "calibration of instrument0 for groundstation0 is incomplete, calibration of instrument0 for groundstation4 is incomplete, calibration of instrument0 for planet8 is incomplete, calibration of instrument0 is incomplete, calibration of instrument1 for phenomenon5 is incomplete, calibration of instrument1 for phenomenon7 is incomplete, for groundstation0, instrument1 is not calibrated, for groundstation2, instrument0 is not calibrated, for groundstation2, instrument1 is not calibrated, for groundstation4, instrument1 is not calibrated, for phenomenon10, instrument0 is not calibrated, for phenomenon11, instrument0 is not calibrated, for phenomenon7, instrument0 is not calibrated, for planet8, instrument1 is not calibrated, for star6, instrument0 is not calibrated, groundstation2 is not where satellite1 is pointed, image of groundstation0 does not exist in image2, image of groundstation0 does not exist in image4, image of groundstation0 does not exist in spectrograph3, image of groundstation2 does not exist in image0, image of groundstation2 does not exist in spectrograph1, image of groundstation4 does not exist in image4, image of groundstation4 does not exist in image5, image of groundstation4 does not exist in spectrograph1, image of groundstation4 does not exist in spectrograph3, image of phenomenon10 does not exist in image0, image of phenomenon10 does not exist in image2, image of phenomenon10 does not exist in spectrograph1, image of phenomenon10 does not exist in spectrograph3, image of phenomenon11 does not exist in image0, image of phenomenon11 does not exist in image4, image of phenomenon5 does not exist in image2, image of phenomenon5 does not exist in image5, image of phenomenon5 does not exist in spectrograph1, image of phenomenon7 does not exist in image5, image of phenomenon9 does not exist in image0, image of phenomenon9 does not exist in image2, image of phenomenon9 does not exist in image4, image of phenomenon9 does not exist in image5, image of phenomenon9 does not exist in spectrograph1, image of planet8 does not exist in image0, image of planet8 does not exist in image4, image of planet8 does not exist in image5, image of planet8 does not exist in spectrograph1, image of planet8 does not exist in spectrograph3, image of star1 does not exist in image0, image of star1 does not exist in image4, image of star1 does not exist in image5, image of star3 does not exist in image2, image of star3 does not exist in image5, image of star3 does not exist in spectrograph1, image of star6 does not exist in image0, image of star6 does not exist in image2, image of star6 does not exist in image5, image of star6 does not exist in spectrograph1, image of star6 does not exist in spectrograph3, image0 is not supported by instrument0, instrument0 does not support image4, instrument0 does not support image5, instrument0 is not calibrated for phenomenon5, instrument0 is not calibrated for phenomenon9, instrument0 is not calibrated for star3, instrument0 is not on board satellite1, instrument0 is not powered on, instrument1 is not calibrated, instrument1 is not calibrated for phenomenon10, instrument1 is not calibrated for phenomenon11, instrument1 is not calibrated for phenomenon9, instrument1 is not calibrated for star3, instrument1 is not calibrated for star6, phenomenon10 is not where satellite0 is pointed, phenomenon11 is not where satellite0 is pointed, phenomenon5 is not where satellite1 is pointed, phenomenon7 is not where satellite1 is pointed, satellite0 does not carry instrument1 on board, satellite0 is not aimed towards star3, satellite0 is not pointing to groundstation0, satellite0 is not pointing to groundstation2, satellite0 is not pointing to groundstation4, satellite0 is not pointing to phenomenon7, satellite0 is not pointing to phenomenon9, satellite0 is not pointing to planet8, satellite1 does not have power available, satellite1 is not aimed towards groundstation0, satellite1 is not aimed towards phenomenon10, satellite1 is not aimed towards phenomenon11, satellite1 is not pointing to groundstation4, satellite1 is not pointing to phenomenon9, satellite1 is not pointing to planet8, spectrograph1 is not supported by instrument0, spectrograph3 is not supported by instrument0, star1 is not where satellite0 is pointed, star3 is not where satellite1 is pointed, star6 is not where satellite0 is pointed, star6 is not where satellite1 is pointed, there is no image of direction groundstation0 in image0, there is no image of direction groundstation0 in image5, there is no image of direction groundstation0 in spectrograph1, there is no image of direction groundstation2 in image2, there is no image of direction groundstation2 in image4, there is no image of direction groundstation2 in image5, there is no image of direction groundstation2 in spectrograph3, there is no image of direction groundstation4 in image0, there is no image of direction groundstation4 in image2, there is no image of direction phenomenon10 in image4, there is no image of direction phenomenon10 in image5, there is no image of direction phenomenon11 in image2, there is no image of direction phenomenon11 in image5, there is no image of direction phenomenon11 in spectrograph1, there is no image of direction phenomenon11 in spectrograph3, there is no image of direction phenomenon5 in image0, there is no image of direction phenomenon5 in image4, there is no image of direction phenomenon5 in spectrograph3, there is no image of direction phenomenon7 in image0, there is no image of direction phenomenon7 in image2, there is no image of direction phenomenon7 in image4, there is no image of direction phenomenon7 in spectrograph1, there is no image of direction phenomenon7 in spectrograph3, there is no image of direction phenomenon9 in spectrograph3, there is no image of direction planet8 in image2, there is no image of direction star1 in image2, there is no image of direction star1 in spectrograph1, there is no image of direction star1 in spectrograph3, there is no image of direction star3 in image0, there is no image of direction star3 in image4, there is no image of direction star3 in spectrograph3 and there is no image of direction star6 in image4", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star1 is complete, image0 is compatible with instrument1, image2 is compatible with instrument0, image2 is compatible with instrument1, image5 is supported by instrument1, instrument1 is calibrated for star1, instrument1 supports image4, instrument1 supports spectrograph3, satellite0 carries instrument0 on board, satellite0 has power available, satellite0 is pointing to phenomenon5, satellite1 has instrument1 on board, satellite1 has power available, satellite1 is pointing to star3 and spectrograph1 is supported by instrument1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument1 on satellite1 is activated to achieve the current state. In this state, if satellite1 transitions from star3 to star1, what are all the valid state properties that involve negations? If there are no such properties, state None.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star1 has been completed. Image0 is found to be compatible with instrument1, while image2 is compatible with both instrument0 and instrument1. Additionally, image5 is supported by instrument1. Instrument1 has been calibrated for star1 and is capable of supporting image4 and spectrograph3. Satellite0 is equipped with instrument0 and has sufficient power, with its current focus on phenomenon5. Satellite1, on the other hand, is equipped with instrument1, has available power, and is currently pointing towards star3. Furthermore, spectrograph1 is supported by instrument1."}
{"question_id": "47469979-0677-4ab9-bbb9-e5c9bcbf001f", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite1 is switched on to reach the current state. In this state, if satellite1 turns to star1 from star3, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "calibration of instrument0 for star1 is complete, calibration of instrument1 for star1 is complete, image0 is supported by instrument1, image2 is supported by instrument0, image4 is supported by instrument1, instrument1 is on board satellite1, instrument1 is switched on, instrument1 supports image2, instrument1 supports image5, instrument1 supports spectrograph3, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power available, satellite1 is pointing to star1 and spectrograph1 is supported by instrument1", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star1 is complete, image0 is compatible with instrument1, image2 is compatible with instrument0, image2 is compatible with instrument1, image5 is supported by instrument1, instrument1 is calibrated for star1, instrument1 supports image4, instrument1 supports spectrograph3, satellite0 carries instrument0 on board, satellite0 has power available, satellite0 is pointing to phenomenon5, satellite1 has instrument1 on board, satellite1 has power available, satellite1 is pointing to star3 and spectrograph1 is supported by instrument1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: instrument1 on satellite1 is activated to achieve the current state. In this state, if satellite1 shifts its orientation from star3 to star1, what are all the valid properties of the state that do not include negations? If there are no such properties, state None.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star1 has been completed. Image0 is found to be compatible with instrument1, while image2 is compatible with both instrument0 and instrument1. Additionally, image5 is supported by instrument1. Instrument1 has been calibrated for star1 and is capable of supporting image4 and spectrograph3. Satellite0 is equipped with instrument0 and has sufficient power, with its current focus on phenomenon5. Satellite1, on the other hand, has instrument1 on board, also with available power, and is currently pointing towards star3. Furthermore, spectrograph1 is supported by instrument1."}
{"question_id": "71c947cf-9973-4057-aac9-5c7f4f6bb434", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on to reach the current state. In this state, if instrument0 on satellite0 is switched on, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "calibration of instrument0 for star0 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for groundstation4 is complete, calibration of instrument1 for star8 is complete, for groundstation9, instrument2 is calibrated, for star6, instrument3 is calibrated, for star7, instrument2 is calibrated, infrared3 is compatible with instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for groundstation4, instrument0 is switched on, instrument0 supports spectrograph0, instrument0 supports thermograph4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is calibrated for groundstation4, instrument2 is on board satellite1, instrument2 supports spectrograph2, instrument3 is switched on, satellite0 has instrument0 on board, satellite1 has instrument3 on board, satellite1 is pointing to groundstation4, spectrograph0 is supported by instrument1, spectrograph0 is supported by instrument2, spectrograph1 is supported by instrument3, spectrograph2 is supported by instrument3 and star1 is where satellite0 is pointed", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for star7 is complete, for groundstation4, instrument2 is calibrated, for star0, instrument0 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is compatible with instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for groundstation4, instrument0 is on board satellite0, instrument1 is calibrated for groundstation2, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument3 is calibrated for star6, satellite0 has power, satellite0 is aimed towards star1, satellite1 carries instrument3 on board, satellite1 has power, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph0 is compatible with instrument2, spectrograph1 is supported by instrument3, spectrograph2 is supported by instrument2, spectrograph2 is supported by instrument3 and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, if instrument0 on satellite0 is activated, what are all the valid properties of the state that do not include negations? If there are no such properties, state None.", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star8 has been completed, instrument2's calibration for groundstation9 is also complete, and instrument2's calibration for star7 is finished. Instrument2 has been calibrated for groundstation4, and instrument0 has been calibrated for star0. Satellite1 is currently directed at groundstation4. In terms of compatibility, infrared3 is suitable for use with instrument2. Instrument0 has been calibrated for both groundstation2 and groundstation4. Additionally, instrument0 is installed on satellite0. Instrument1 has also been calibrated for groundstation2 and groundstation4, and it is also on board satellite0. Furthermore, instrument1 supports spectrograph1. Instrument2 is installed on satellite1. Instrument3 has been calibrated for star6. Satellite0 is operational, and it is currently aimed at star1. Satellite1 has instrument3 on board and is also operational. Spectrograph0 is compatible with instruments 0, 1, and 2. Spectrograph1 is supported by instrument3, and spectrograph2 is supported by both instruments 2 and 3. Lastly, thermograph4 is compatible with instrument0."}
{"question_id": "a4c2e0fb-00a9-4829-9cd5-fe9e726f998e", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, instrument0 on satellite0 is switched on, satellite1 turns from groundstation4 to star6, instrument3 that is on satellite1 is calibrated to star6, satellite1 turns to planet14 from star6, satellite1's instrument3 takes an image of planet14 in spectrograph1, satellite1 turns from planet14 to star10, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, satellite1 turns from star10 to star12, image of star12 is taken with instrument3 on satellite1 in spectrograph1, satellite1 turns from star12 to star0, satellite0 turns from star1 to groundstation2, calibration of instrument0 which is on satellite0 to groundstation2 is complete, from groundstation2, satellite0 turns to phenomenon15, image of phenomenon15 is taken with instrument0 on satellite0 in spectrograph0, from phenomenon15, satellite0 turns to star11, instrument0 which is on satellite0 takes an image of star11 in thermograph4, satellite0 turns from star11 to star13 and image of star13 is taken with instrument0 on satellite0 in spectrograph0 to reach the current state. In this state, if satellite0 turns from star13 to star16, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "calibration of instrument0 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for star7 is complete, calibration of instrument3 for star6 is complete, for groundstation4, instrument1 is calibrated, image of phenomenon15 exists in spectrograph0, image of planet14 exists in spectrograph1, image of star12 exists in spectrograph1, infrared3 is compatible with instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for groundstation4, instrument0 is calibrated for star0, instrument0 is on board satellite0, instrument0 is switched on, instrument1 is calibrated for star8, instrument1 is on board satellite0, instrument2 supports spectrograph0, instrument3 is calibrated, instrument3 is on board satellite1, instrument3 is switched on, instrument3 supports spectrograph2, satellite0 is aimed towards star16, satellite1 carries instrument2 on board, satellite1 is aimed towards star0, spectrograph0 is compatible with instrument0, spectrograph0 is supported by instrument1, spectrograph1 is compatible with instrument1, spectrograph1 is compatible with instrument3, spectrograph2 is compatible with instrument2, there is an image of star10 in spectrograph1, there is an image of star11 in thermograph4, there is an image of star13 in spectrograph0 and thermograph4 is supported by instrument0", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for star8 is complete, calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for star7 is complete, for groundstation4, instrument2 is calibrated, for star0, instrument0 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is compatible with instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for groundstation4, instrument0 is on board satellite0, instrument1 is calibrated for groundstation2, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, instrument2 is on board satellite1, instrument3 is calibrated for star6, satellite0 has power, satellite0 is aimed towards star1, satellite1 carries instrument3 on board, satellite1 has power, spectrograph0 is compatible with instrument0, spectrograph0 is compatible with instrument1, spectrograph0 is compatible with instrument2, spectrograph1 is supported by instrument3, spectrograph2 is supported by instrument2, spectrograph2 is supported by instrument3 and thermograph4 is compatible with instrument0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is activated, satellite1 reorients from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, satellite1 then reorients from star6 to planet14, an image of planet14 is captured by instrument3 on satellite1 using spectrograph1, satellite1 reorients from planet14 to star10, instrument3 on satellite1 captures an image of star10 using spectrograph1, satellite1 reorients from star10 to star12, an image of star12 is captured by instrument3 on satellite1 using spectrograph1, satellite1 reorients from star12 to star0, satellite0 reorients from star1 to groundstation2, instrument0 on satellite0 is calibrated to groundstation2, satellite0 reorients from groundstation2 to phenomenon15, an image of phenomenon15 is captured by instrument0 on satellite0 using spectrograph0, satellite0 reorients from phenomenon15 to star11, instrument0 on satellite0 captures an image of star11 using thermograph4, satellite0 reorients from star11 to star13, and an image of star13 is captured by instrument0 on satellite0 using spectrograph0, resulting in the current state. In this state, if satellite0 reorients from star13 to star16, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star8 has been completed, instrument2's calibration for groundstation9 is also complete, and instrument2's calibration for star7 is finished. Instrument2 has been calibrated for groundstation4, and instrument0 has been calibrated for star0. The satellite1 is currently directed towards groundstation4. In terms of compatibility, infrared3 is suitable for use with instrument2. Instrument0 has been calibrated for both groundstation2 and groundstation4. Additionally, instrument0 is installed on satellite0. Instrument1 has also been calibrated for groundstation2 and groundstation4, and it is also on board satellite0. Furthermore, instrument1 supports spectrograph1. Instrument2 is installed on satellite1. Instrument3 has been calibrated for star6. Satellite0 is operational and is currently aimed at star1. Satellite1 is also operational and carries instrument3 on board. Spectrograph0 is compatible with instruments 0, 1, and 2. Instrument3 supports spectrograph1, and both instrument2 and instrument3 support spectrograph2. Lastly, thermograph4 is compatible with instrument0."}
{"question_id": "4a42f904-581a-4886-8373-47b568561479", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite1 is switched on, from star3, satellite1 turns to star1, calibration of instrument1 which is on satellite1 to star1 is complete, from star1, satellite1 turns to phenomenon10, satellite1's instrument1 takes an image of phenomenon10 in image5, satellite1's instrument1 takes an image of phenomenon10 in spectrograph3, satellite1 turns from phenomenon10 to phenomenon11, satellite1's instrument1 takes an image of phenomenon11 in spectrograph1, satellite1 turns to phenomenon5 from phenomenon11, instrument1 which is on satellite1 takes an image of phenomenon5 in image4, instrument1 which is on satellite1 takes an image of phenomenon5 in image5, satellite1 turns to phenomenon7 from phenomenon5, satellite1's instrument1 takes an image of phenomenon7 in image0, image of phenomenon7 is taken with instrument1 on satellite1 in image4, satellite1 turns from phenomenon7 to phenomenon9, satellite1's instrument1 takes an image of phenomenon9 in image5, satellite1's instrument1 takes an image of phenomenon9 in spectrograph1, from phenomenon9, satellite1 turns to planet8 and satellite1's instrument1 takes an image of planet8 in image5 to reach the current state. In this state, if satellite1 turns from planet8 to star6, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "for star1, instrument0 is calibrated, for star1, instrument1 is calibrated, image of phenomenon10 exists in spectrograph3, image of phenomenon9 exists in image5, image0 is supported by instrument1, image2 is supported by instrument0, image4 is compatible with instrument1, image5 is compatible with instrument1, instrument0 is on board satellite0, instrument1 is calibrated, instrument1 is on board satellite1, instrument1 is turned on, instrument1 supports image2, instrument1 supports spectrograph1, instrument1 supports spectrograph3, satellite0 has power available, satellite0 is pointing to phenomenon5, satellite1 is aimed towards star6, there is an image of phenomenon10 in image5, there is an image of phenomenon11 in spectrograph1, there is an image of phenomenon5 in image4, there is an image of phenomenon5 in image5, there is an image of phenomenon7 in image0, there is an image of phenomenon7 in image4, there is an image of phenomenon9 in spectrograph1 and there is an image of planet8 in image5", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for star1 is complete, image0 is compatible with instrument1, image2 is compatible with instrument0, image2 is compatible with instrument1, image5 is supported by instrument1, instrument1 is calibrated for star1, instrument1 supports image4, instrument1 supports spectrograph3, satellite0 carries instrument0 on board, satellite0 has power available, satellite0 is pointing to phenomenon5, satellite1 has instrument1 on board, satellite1 has power available, satellite1 is pointing to star3 and spectrograph1 is supported by instrument1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 on satellite1 is activated, satellite1 reorients from star3 to star1, instrument1 on satellite1 is calibrated to star1, then satellite1 reorients from star1 to phenomenon10, captures an image of phenomenon10 using instrument1 in image5 and spectrograph3, reorients from phenomenon10 to phenomenon11, captures an image of phenomenon11 using instrument1 in spectrograph1, reorients from phenomenon11 to phenomenon5, captures an image of phenomenon5 using instrument1 in image4 and image5, reorients from phenomenon5 to phenomenon7, captures an image of phenomenon7 using instrument1 in image0 and image4, reorients from phenomenon7 to phenomenon9, captures an image of phenomenon9 using instrument1 in image5 and spectrograph1, and finally reorients from phenomenon9 to planet8, capturing an image of planet8 using instrument1 in image5, resulting in the current state. In this state, if satellite1 reorients from planet8 to star6, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star1 has been completed. Image0 is found to be compatible with instrument1, while image2 is compatible with both instrument0 and instrument1. Additionally, image5 is supported by instrument1. Instrument1 has been calibrated for star1 and is capable of supporting image4 and spectrograph3. Satellite0 is equipped with instrument0 and has sufficient power, with its current focus on phenomenon5. Satellite1, on the other hand, is equipped with instrument1, has available power, and is currently pointing towards star3. Furthermore, spectrograph1 is supported by instrument1."}
{"question_id": "a8f57e34-2b27-4b04-8a5b-9fe1367d69f6", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, instrument0 that is on satellite0 is turned on, satellite1 turns from phenomenon10 to groundstation5, instrument3 that is on satellite1 is calibrated to groundstation5, from groundstation5, satellite1 turns to phenomenon16, image of phenomenon16 is taken with instrument3 on satellite1 in image3, from phenomenon16, satellite1 turns to phenomenon17, instrument3 which is on satellite1 takes an image of phenomenon17 in image3, satellite1 turns to planet11 from phenomenon17 and image of planet11 is taken with instrument3 on satellite1 in image3 to reach the current state. In this state, if satellite1 turns from planet11 to planet13, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "calibration of instrument0 for groundstation0 is incomplete, calibration of instrument0 for groundstation2 is incomplete, calibration of instrument0 for groundstation3 is incomplete, calibration of instrument0 for groundstation4 is incomplete, calibration of instrument0 for planet12 is incomplete, calibration of instrument0 for star15 is incomplete, calibration of instrument0 for star6 is incomplete, calibration of instrument0 is incomplete, calibration of instrument1 for groundstation0 is complete, calibration of instrument1 for groundstation4 is incomplete, calibration of instrument1 for groundstation5 is incomplete, calibration of instrument1 for phenomenon10 is incomplete, calibration of instrument1 for planet11 is incomplete, calibration of instrument1 for planet12 is incomplete, calibration of instrument1 for star15 is incomplete, calibration of instrument1 for star9 is incomplete, calibration of instrument2 for phenomenon17 is incomplete, calibration of instrument3 for groundstation0 is incomplete, calibration of instrument3 for groundstation2 is incomplete, calibration of instrument3 for groundstation4 is incomplete, calibration of instrument3 for planet14 is incomplete, calibration of instrument3 for star1 is incomplete, calibration of instrument3 for star15 is incomplete, calibration of instrument3 for star6 is complete, for groundstation2, instrument1 is not calibrated, for groundstation3, instrument1 is not calibrated, for groundstation3, instrument3 is not calibrated, for groundstation5, instrument3 is calibrated, for groundstation7, instrument0 is not calibrated, for groundstation7, instrument1 is not calibrated, for groundstation7, instrument3 is not calibrated, for phenomenon10, instrument2 is not calibrated, for phenomenon16, instrument1 is not calibrated, for phenomenon17, instrument0 is not calibrated, for planet11, instrument0 is not calibrated, for planet11, instrument2 is not calibrated, for planet13, instrument1 is not calibrated, for planet13, instrument2 is not calibrated, for planet13, instrument3 is not calibrated, for planet14, instrument0 is not calibrated, for star1, instrument0 is calibrated, for star1, instrument1 is not calibrated, for star1, instrument2 is not calibrated, for star15, instrument2 is not calibrated, for star8, instrument2 is not calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, groundstation7 is not where satellite1 is pointed, image of groundstation0 does not exist in image0, image of groundstation0 does not exist in image2, image of groundstation0 does not exist in image3, image of groundstation0 does not exist in infrared1, image of groundstation2 does not exist in image2, image of groundstation3 does not exist in image2, image of groundstation3 does not exist in infrared1, image of groundstation4 does not exist in image2, image of groundstation5 does not exist in image0, image of groundstation5 does not exist in image3, image of groundstation7 does not exist in image2, image of phenomenon10 does not exist in image0, image of phenomenon10 does not exist in image2, image of phenomenon10 does not exist in infrared1, image of phenomenon16 does not exist in image0, image of phenomenon16 exists in image3, image of phenomenon17 does not exist in image0, image of phenomenon17 does not exist in image2, image of phenomenon17 does not exist in infrared1, image of planet11 does not exist in image0, image of planet11 does not exist in image2, image of planet11 does not exist in infrared1, image of planet12 does not exist in image0, image of planet12 does not exist in image2, image of planet12 does not exist in image3, image of planet12 does not exist in infrared1, image of planet13 does not exist in image2, image of planet14 does not exist in image2, image of star15 does not exist in image0, image of star6 does not exist in image0, image of star6 does not exist in image2, image of star6 does not exist in image3, image of star6 does not exist in infrared1, image of star8 does not exist in image0, image of star9 does not exist in image0, image of star9 does not exist in infrared1, image0 is not supported by instrument0, image0 is supported by instrument1, image2 is compatible with instrument2, image2 is supported by instrument3, image3 is compatible with instrument0, image3 is compatible with instrument1, image3 is compatible with instrument3, image3 is supported by instrument2, infrared1 is not compatible with instrument2, infrared1 is not compatible with instrument3, instrument0 does not support image2, instrument0 is not calibrated for groundstation5, instrument0 is not calibrated for phenomenon10, instrument0 is not calibrated for phenomenon16, instrument0 is not calibrated for planet13, instrument0 is not calibrated for star8, instrument0 is on board satellite0, instrument0 is switched on, instrument0 supports infrared1, instrument1 does not support image2, instrument1 is not calibrated, instrument1 is not calibrated for phenomenon17, instrument1 is not calibrated for planet14, instrument1 is not calibrated for star6, instrument1 is not calibrated for star8, instrument1 is not switched on, instrument1 supports infrared1, instrument2 does not support image0, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument2 is calibrated for star9, instrument2 is not calibrated, instrument2 is not calibrated for groundstation0, instrument2 is not calibrated for groundstation2, instrument2 is not calibrated for groundstation3, instrument2 is not calibrated for groundstation4, instrument2 is not calibrated for phenomenon16, instrument2 is not calibrated for planet12, instrument2 is not calibrated for planet14, instrument2 is not calibrated for star6, instrument2 is not turned on, instrument3 is calibrated, instrument3 is calibrated for star8, instrument3 is not calibrated for phenomenon10, instrument3 is not calibrated for phenomenon16, instrument3 is not calibrated for phenomenon17, instrument3 is not calibrated for planet11, instrument3 is not calibrated for planet12, instrument3 is not calibrated for star9, instrument3 is not on board satellite0, instrument3 is powered on, instrument3 supports image0, phenomenon10 is not where satellite1 is pointed, phenomenon17 is not where satellite0 is pointed, planet13 is not where satellite0 is pointed, planet14 is not where satellite0 is pointed, planet14 is not where satellite1 is pointed, satellite0 carries instrument2 on board, satellite0 does not have power available, satellite0 has instrument1 on board, satellite0 is not aimed towards groundstation0, satellite0 is not aimed towards groundstation5, satellite0 is not aimed towards groundstation7, satellite0 is not aimed towards phenomenon10, satellite0 is not aimed towards phenomenon16, satellite0 is not aimed towards planet11, satellite0 is not pointing to groundstation2, satellite0 is not pointing to groundstation4, satellite0 is not pointing to planet12, satellite0 is not pointing to star15, satellite0 is not pointing to star6, satellite1 carries instrument3 on board, satellite1 does not carry instrument0 on board, satellite1 does not carry instrument1 on board, satellite1 does not carry instrument2 on board, satellite1 does not have power, satellite1 is aimed towards planet13, satellite1 is not aimed towards groundstation2, satellite1 is not aimed towards groundstation3, satellite1 is not aimed towards phenomenon16, satellite1 is not aimed towards planet11, satellite1 is not aimed towards planet12, satellite1 is not aimed towards star1, satellite1 is not aimed towards star15, satellite1 is not aimed towards star6, satellite1 is not pointing to groundstation0, satellite1 is not pointing to groundstation4, satellite1 is not pointing to groundstation5, satellite1 is not pointing to phenomenon17, star1 is not where satellite0 is pointed, star8 is not where satellite0 is pointed, star8 is not where satellite1 is pointed, star9 is not where satellite0 is pointed, star9 is not where satellite1 is pointed, there is an image of phenomenon17 in image3, there is an image of planet11 in image3, there is no image of direction groundstation2 in image0, there is no image of direction groundstation2 in image3, there is no image of direction groundstation2 in infrared1, there is no image of direction groundstation3 in image0, there is no image of direction groundstation3 in image3, there is no image of direction groundstation4 in image0, there is no image of direction groundstation4 in image3, there is no image of direction groundstation4 in infrared1, there is no image of direction groundstation5 in image2, there is no image of direction groundstation5 in infrared1, there is no image of direction groundstation7 in image0, there is no image of direction groundstation7 in image3, there is no image of direction groundstation7 in infrared1, there is no image of direction phenomenon10 in image3, there is no image of direction phenomenon16 in image2, there is no image of direction phenomenon16 in infrared1, there is no image of direction planet13 in image0, there is no image of direction planet13 in image3, there is no image of direction planet13 in infrared1, there is no image of direction planet14 in image0, there is no image of direction planet14 in image3, there is no image of direction planet14 in infrared1, there is no image of direction star1 in image0, there is no image of direction star1 in image2, there is no image of direction star1 in image3, there is no image of direction star1 in infrared1, there is no image of direction star15 in image2, there is no image of direction star15 in image3, there is no image of direction star15 in infrared1, there is no image of direction star8 in image2, there is no image of direction star8 in image3, there is no image of direction star8 in infrared1, there is no image of direction star9 in image2 and there is no image of direction star9 in image3", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, for star1, instrument0 is calibrated, for star8, instrument3 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image0 is compatible with instrument1, image2 is compatible with instrument2, image2 is supported by instrument3, image3 is compatible with instrument3, image3 is supported by instrument2, infrared1 is compatible with instrument1, instrument0 is on board satellite0, instrument0 supports image3, instrument0 supports infrared1, instrument1 supports image3, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 on satellite1 is activated, instrument0 on satellite0 is activated, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated to groundstation5, then satellite1 reorients from groundstation5 to phenomenon16, an image of phenomenon16 is captured using instrument3 on satellite1 and stored in image3, satellite1 then reorients from phenomenon16 to phenomenon17, instrument3 on satellite1 captures an image of phenomenon17 in image3, and finally, satellite1 reorients from phenomenon17 to planet11, capturing an image of planet11 with instrument3 on satellite1 in image3, resulting in the current state. In this state, if satellite1 reorients from planet11 to planet13, what are all the valid properties of the resulting state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is also complete, and instrument3's calibration for star6 is finished. Additionally, instrument0 has been calibrated for star1, instrument3 for star8, and instrument0 for star9. Satellite0 is currently directed towards groundstation3. Image0 is compatible with instrument1, while image2 is compatible with instrument2 and supported by instrument3. Image3 is compatible with instrument3 and supported by instrument2. Infrared1 is compatible with instrument1. Instrument0 is installed on satellite0, supporting both image3 and infrared1. Instrument1 also supports image3. Instrument2 has been calibrated for groundstation5 and groundstation7, and instrument3 has been calibrated for groundstation5. Instrument3 supports image0. Satellite0 is equipped with instrument1 and instrument2 on board, and it has available power. Satellite1 has instrument3 on board, available power, and is currently pointing towards phenomenon10."}
