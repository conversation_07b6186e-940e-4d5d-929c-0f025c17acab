{"question_id": "9dd184bd-dd89-495a-9d6d-c3b0357f294c", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on, from groundstation2, satellite0 turns to groundstation0, calibration of instrument1 which is on satellite0 to groundstation0 is complete, satellite0 turns from groundstation0 to planet11, image of planet11 is taken with instrument1 on satellite0 in image5, image of planet11 is taken with instrument1 on satellite0 in image6, satellite0 turns from planet11 to planet13, satellite0's instrument1 takes an image of planet13 in image5, instrument1 which is on satellite0 takes an image of planet13 in spectrograph2, satellite0 turns from planet13 to star10, instrument1 which is on satellite0 takes an image of star10 in image6, satellite0's instrument1 takes an image of star10 in spectrograph2, on satellite0, instrument1 is switched off, instrument2 on satellite0 is switched on, satellite0 turns from star10 to star4, instrument2 that is on satellite0 is calibrated to star4, satellite0 turns from star4 to star16, image of star16 is taken with instrument2 on satellite0 in image0 and on satellite0, instrument2 is switched off to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "2057", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for star3 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation9 is complete, for groundstation0, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, image6 is compatible with instrument0, image6 is compatible with instrument1, instrument1 is calibrated for groundstation6, instrument4 is calibrated for groundstation8, instrument4 is on board satellite1, instrument4 supports image1, instrument4 supports infrared7, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has power, satellite1 has power, satellite1 is pointing to planet13, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, then satellite0 reorients from groundstation0 to planet11, an image of planet11 is captured using instrument1 on satellite0 and stored in image5, another image of planet11 is captured using instrument1 on satellite0 and stored in image6, satellite0 reorients from planet11 to planet13, instrument1 on satellite0 captures an image of planet13 and stores it in image5, instrument1 on satellite0 also captures an image of planet13 and stores it in spectrograph2, satellite0 reorients from planet13 to star10, instrument1 on satellite0 captures an image of star10 and stores it in image6, instrument1 on satellite0 also captures an image of star10 and stores it in spectrograph2, instrument1 on satellite0 is then deactivated, instrument2 on satellite0 is activated, satellite0 reorients from star10 to star4, instrument2 on satellite0 is calibrated to star4, satellite0 reorients from star4 to star16, an image of star16 is captured using instrument2 on satellite0 and stored in image0, and finally instrument2 on satellite0 is deactivated to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star3 has been finalized, instrument2's calibration for star4 is also complete, and instrument3's calibration for groundstation9 is finished. Groundstation0 has instrument1 calibrated, while groundstation7 has instrument0 calibrated. Satellite0 is currently directed towards groundstation2. Image0 is compatible for use with instrument2, and image1 is also compatible with instrument2. Additionally, image1 is supported by instrument3, and image5 is supported by instrument1. Image6 is compatible with both instrument0 and instrument1. Instrument1 has been calibrated for groundstation6, and instrument4 has been calibrated for groundstation8. Instrument4 is installed on satellite1 and supports both image1 and infrared7. Satellite0 is equipped with instruments 0, 1, 2, and 3 on board and has power. Satellite1 also has power and is currently pointing towards planet13. Furthermore, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is compatible with instrument3."}
{"question_id": "2dfc25cf-d522-46be-b182-a480b076dec0", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on, instrument0 on satellite0 is switched on, satellite1 turns to groundstation5 from phenomenon10, instrument3 is calibrated on satellite1 to groundstation5, satellite1 turns to phenomenon16 from groundstation5, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, satellite1 turns from phenomenon16 to phenomenon17, instrument3 which is on satellite1 takes an image of phenomenon17 in image3, satellite1 turns to planet11 from phenomenon17, image of planet11 is taken with instrument3 on satellite1 in image3, satellite1 turns to planet13 from planet11, image of planet13 is taken with instrument3 on satellite1 in image0, satellite1 turns from planet13 to planet14, image of planet14 is taken with instrument3 on satellite1 in image0, satellite1 turns to star15 from planet14, satellite1's instrument3 takes an image of star15 in image2, satellite0 turns from groundstation3 to star1, instrument0 that is on satellite0 is calibrated to star1 and from star1, satellite0 turns to phenomenon10 to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "answer": "41", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, calibration of instrument3 for star8 is complete, for star1, instrument0 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image2 is supported by instrument2, image2 is supported by instrument3, image3 is compatible with instrument0, image3 is compatible with instrument1, image3 is supported by instrument3, infrared1 is supported by instrument0, instrument1 is on board satellite0, instrument1 supports image0, instrument1 supports infrared1, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument2 supports image3, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 has instrument0 on board, satellite0 has instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 is activated on satellite1, and instrument0 is activated on satellite0. Satellite1 then reorients from phenomenon10 to groundstation5, followed by the calibration of instrument3 on satellite1 to groundstation5. Next, satellite1 reorients from groundstation5 to phenomenon16, and instrument3 on satellite1 captures an image of phenomenon16 in image3. Satellite1 then reorients from phenomenon16 to phenomenon17, and instrument3 on satellite1 captures an image of phenomenon17 in image3. Satellite1 continues by reorienting from phenomenon17 to planet11, capturing an image of planet11 with instrument3 in image3. The satellite then reorients from planet11 to planet13, capturing an image of planet13 with instrument3 in image0. Satellite1 reorients from planet13 to planet14, capturing an image of planet14 with instrument3 in image0. Finally, satellite1 reorients from planet14 to star15, capturing an image of star15 with instrument3 in image2. Meanwhile, satellite0 reorients from groundstation3 to star1, calibrates instrument0 to star1, and then reorients from star1 to phenomenon10 to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is now complete, and instrument3's calibration for both star6 and star8 is also complete. For star1 and star9, instrument0 has been successfully calibrated. The current position of satellite0 is directed towards groundstation3. Instrument2 and instrument3 both support image2, while image3 is compatible with instrument0 and instrument1, and is also supported by instrument3. Instrument0 supports infrared1. Instrument1, which is on board satellite0, supports both image0 and infrared1. Instrument2 has been calibrated for groundstations 5 and 7, and supports image3. Instrument3, calibrated for groundstation5, supports image0. Satellite0 is equipped with instruments 0, 1, and 2, and has power available. Satellite1, which has instrument3 on board, has power and is currently pointing towards phenomenon10."}
{"question_id": "f6d0532c-2008-4e5c-b614-9d2e9a99d9a2", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite0, instrument1 is switched on, satellite0 turns to groundstation0 from groundstation2, instrument1 is calibrated on satellite0 to groundstation0, satellite0 turns to planet11 from groundstation0, instrument1 which is on satellite0 takes an image of planet11 in image5, image of planet11 is taken with instrument1 on satellite0 in image6, from planet11, satellite0 turns to planet13, instrument1 which is on satellite0 takes an image of planet13 in image5, image of planet13 is taken with instrument1 on satellite0 in spectrograph2 and satellite0 turns from planet13 to star10 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "2057", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star3 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation9 is complete, for groundstation0, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, image6 is compatible with instrument0, image6 is compatible with instrument1, instrument1 is calibrated for groundstation6, instrument4 is calibrated for groundstation8, instrument4 is on board satellite1, instrument4 supports image1, instrument4 supports infrared7, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has power, satellite1 has power, satellite1 is pointing to planet13, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are executed: on satellite0, instrument1 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, satellite0 then reorients from groundstation0 to planet11, instrument1 on satellite0 captures an image of planet11 in image5, another image of planet11 is captured using instrument1 on satellite0 in image6, from planet11, satellite0 reorients to planet13, instrument1 on satellite0 captures an image of planet13 in image5, and another image of planet13 is captured using instrument1 on satellite0 in spectrograph2, and finally, satellite0 reorients from planet13 to star10 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star3 has been finalized, instrument2's calibration for star4 is also complete, and instrument3's calibration for groundstation9 is finished. Groundstation0 has instrument1 calibrated, while groundstation7 has instrument0 calibrated. Satellite0 is currently directed towards groundstation2. Image0 is compatible for use with instrument2, and image1 is also compatible with instrument2. Additionally, image1 is supported by instrument3, and image5 is supported by instrument1. Image6 is compatible with both instrument0 and instrument1. Instrument1 has been calibrated for groundstation6, and instrument4 has been calibrated for groundstation8. Instrument4 is installed on satellite1 and supports both image1 and infrared7. Satellite0 is equipped with instruments 0, 1, 2, and 3 on board and has power. Satellite1 also has power and is currently pointing towards planet13. Furthermore, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is compatible with instrument3."}
{"question_id": "aafafe30-210c-4d2f-b8b4-bf3f4fb21b86", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "1376", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for groundstation4 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument3 for star6 is complete, for groundstation9, instrument2 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is supported by instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for star0, instrument0 is on board satellite0, instrument0 supports thermograph4, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph0, instrument2 is calibrated for groundstation4, instrument2 is calibrated for star7, instrument2 supports spectrograph2, instrument3 supports spectrograph1, power is available for satellite1, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has instrument2 on board, spectrograph0 is supported by instrument0, spectrograph0 is supported by instrument2, spectrograph1 is compatible with instrument1, spectrograph2 is supported by instrument3 and star1 is where satellite0 is pointed.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is activated to reach the current state. In this state, what is the total count of executable and non-executable actions? Provide the answer as an integer, or None if there are no actions.", "initial_state_nl_paraphrased": "The calibration process for instrument0 at groundstation4 has been finalized, instrument1's calibration at groundstation2 is also complete, and instrument1's calibration for star8 has been finished. Additionally, instrument3's calibration for star6 is complete. For groundstation9, the calibration of instrument2 is done. Satellite1 is currently directed towards groundstation4. Instrument2 supports infrared3, and instrument0 has been calibrated for groundstation2 and star0. Instrument0 is installed on satellite0 and supports thermograph4. Instrument1, also on satellite0, supports spectrograph0 and has been calibrated for groundstation4. Instrument2 has been calibrated for groundstation4 and star7, and it supports spectrograph2. Instrument3 supports spectrograph1. Satellite1 has power available, and satellite0 also has power. Satellite1 is equipped with instrument3 on board and has instrument2 installed. Spectrograph0 is supported by both instrument0 and instrument2, and spectrograph1 is compatible with instrument1. Spectrograph2 is supported by instrument3, and satellite0 is currently pointed towards star1."}
{"question_id": "786a470b-df98-4dd7-823a-9d376845a8df", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is switched on, satellite1 turns to star1 from star3, instrument1 that is on satellite1 is calibrated to star1, satellite1 turns from star1 to phenomenon10, image of phenomenon10 is taken with instrument1 on satellite1 in image5, satellite1's instrument1 takes an image of phenomenon10 in spectrograph3, from phenomenon10, satellite1 turns to phenomenon11, image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, satellite1 turns to phenomenon5 from phenomenon11 and instrument1 which is on satellite1 takes an image of phenomenon5 in image4 to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "answer": "30", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image2 is compatible with instrument0, image2 is compatible with instrument1, instrument1 supports image0, instrument1 supports image4, instrument1 supports image5, instrument1 supports spectrograph3, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is aimed towards star3 and spectrograph1 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 is activated on satellite1, then satellite1 reorients from star3 to star1, followed by the calibration of instrument1 on satellite1 to star1. Next, satellite1 shifts its orientation from star1 to phenomenon10, capturing an image of phenomenon10 using instrument1 on satellite1 in image5. Additionally, satellite1's instrument1 takes a spectrograph of phenomenon10 in spectrograph3. Satellite1 then reorients from phenomenon10 to phenomenon11, capturing an image of phenomenon11 using instrument1 on satellite1 in spectrograph1. Finally, satellite1 reorients from phenomenon11 to phenomenon5 and captures an image of phenomenon5 using instrument1 on satellite1 in image4, resulting in the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image2 is found to be compatible with both instrument0 and instrument1. Additionally, instrument1 is capable of supporting image0, image4, image5, and spectrograph3. The satellite0 is currently focused on phenomenon5. Satellite0 is equipped with instrument0 and has a power supply. Similarly, satellite1 is equipped with instrument1 and also has a power supply. Satellite1 is currently directed towards star3, and instrument1 provides support for spectrograph1."}
{"question_id": "3bd6fef0-a7d3-4f32-aed2-d266788066cf", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, instrument0 on satellite0 is switched on, from phenomenon10, satellite1 turns to groundstation5, instrument3 is calibrated on satellite1 to groundstation5, satellite1 turns from groundstation5 to phenomenon16, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, satellite1 turns from phenomenon16 to phenomenon17, satellite1's instrument3 takes an image of phenomenon17 in image3, satellite1 turns from phenomenon17 to planet11 and image of planet11 is taken with instrument3 on satellite1 in image3 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "answer": "214", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, calibration of instrument3 for star8 is complete, for star1, instrument0 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image2 is supported by instrument2, image2 is supported by instrument3, image3 is compatible with instrument0, image3 is compatible with instrument1, image3 is supported by instrument3, infrared1 is supported by instrument0, instrument1 is on board satellite0, instrument1 supports image0, instrument1 supports infrared1, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument2 supports image3, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 has instrument0 on board, satellite0 has instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated with respect to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, an image of phenomenon16 is captured by instrument3 on satellite1 and stored in image3, satellite1 reorients from phenomenon16 to phenomenon17, an image of phenomenon17 is captured by instrument3 on satellite1 and stored in image3, and finally, satellite1 reorients from phenomenon17 to planet11, capturing an image of planet11 with instrument3 on satellite1 in image3, resulting in the current state. In this state, what is the total number of valid properties of the state (including both affirmative and negated properties)? Provide the answer as an integer, or None if there are no valid properties.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is now complete, and instrument3's calibration for both star6 and star8 is also complete. For star1 and star9, instrument0 has been calibrated. Satellite0 is currently directed towards groundstation3. Instrument2 and instrument3 both support image2, while image3 is compatible with instrument0 and instrument1, and is also supported by instrument3. Instrument0 supports infrared1. Instrument1, which is on board satellite0, supports both image0 and infrared1. Instrument2 has been calibrated for groundstations 5 and 7, and supports image3. Instrument3, calibrated for groundstation5, supports image0. Satellite0 has instruments 0 and 2 on board, and has available power. Satellite1, which has instrument3 on board, has power and is currently pointing towards phenomenon10."}
{"question_id": "ef93b4d2-388b-48fd-b8d9-034cb74a377c", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite1 is turned on to reach the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "answer": "127", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image2 is compatible with instrument0, image2 is compatible with instrument1, instrument1 supports image0, instrument1 supports image4, instrument1 supports image5, instrument1 supports spectrograph3, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is aimed towards star3 and spectrograph1 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument1 on satellite1 is activated to achieve the current state. In this state, what is the total count of valid state properties that include negations? Express the answer as an integer, or None if there are no such properties.", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image2 is found to be compatible with both instrument0 and instrument1. Additionally, instrument1 is capable of supporting image0, image4, and image5, as well as spectrograph3. The satellite0 is currently focused on phenomenon5. Satellite0 is equipped with instrument0 and has a power source. Similarly, satellite1 is equipped with instrument1 and also has a power source. Satellite1 is currently directed towards star3, and instrument1 provides support for spectrograph1."}
{"question_id": "c99e2ac3-1fab-4aa3-a891-c54b98a19d8d", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on, instrument0 that is on satellite0 is turned on, satellite1 turns to groundstation5 from phenomenon10, instrument3 that is on satellite1 is calibrated to groundstation5, from groundstation5, satellite1 turns to phenomenon16, image of phenomenon16 is taken with instrument3 on satellite1 in image3, satellite1 turns from phenomenon16 to phenomenon17, satellite1's instrument3 takes an image of phenomenon17 in image3, satellite1 turns from phenomenon17 to planet11, image of planet11 is taken with instrument3 on satellite1 in image3, from planet11, satellite1 turns to planet13, instrument3 which is on satellite1 takes an image of planet13 in image0, satellite1 turns from planet13 to planet14, instrument3 which is on satellite1 takes an image of planet14 in image0, from planet14, satellite1 turns to star15, instrument3 which is on satellite1 takes an image of star15 in image2, satellite0 turns to star1 from groundstation3, calibration of instrument0 which is on satellite0 to star1 is complete and from star1, satellite0 turns to phenomenon10 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "1307", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, calibration of instrument3 for star8 is complete, for star1, instrument0 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image2 is supported by instrument2, image2 is supported by instrument3, image3 is compatible with instrument0, image3 is compatible with instrument1, image3 is supported by instrument3, infrared1 is supported by instrument0, instrument1 is on board satellite0, instrument1 supports image0, instrument1 supports infrared1, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument2 supports image3, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 has instrument0 on board, satellite0 has instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated to groundstation5, then satellite1 reorients from groundstation5 to phenomenon16, an image of phenomenon16 is captured using instrument3 on satellite1 and stored in image3, satellite1 reorients from phenomenon16 to phenomenon17, instrument3 on satellite1 captures an image of phenomenon17 in image3, satellite1 then reorients from phenomenon17 to planet11, an image of planet11 is captured using instrument3 on satellite1 and stored in image3, from planet11, satellite1 reorients to planet13, instrument3 on satellite1 captures an image of planet13 in image0, satellite1 reorients from planet13 to planet14, instrument3 on satellite1 captures an image of planet14 in image0, from planet14, satellite1 reorients to star15, instrument3 on satellite1 captures an image of star15 in image2, satellite0 reorients from groundstation3 to star1, calibration of instrument0 on satellite0 to star1 is completed, and satellite0 reorients from star1 to phenomenon10 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is now complete, and instrument3's calibration for both star6 and star8 is also complete. For star1 and star9, instrument0 has been calibrated. Satellite0 is currently directed towards groundstation3. Instrument2 and instrument3 both support image2, while instrument3 and instrument0 are compatible with image3, and instrument3 also supports it. Instrument0 supports infrared1. Instrument1 is installed on satellite0, and it supports both image0 and infrared1. Instrument2 has been calibrated for groundstations 5 and 7, and it supports image3. Instrument3 has been calibrated for groundstation5 and supports image0. Satellite0 is equipped with instruments 0, 1, and 2, and it has power available. Satellite1 has instrument3 on board, has power, and is currently pointing towards phenomenon10."}
{"question_id": "863efb23-1738-4389-a53e-4f7edccc5ce4", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: instrument3 on satellite1 is switched on, from star12, satellite0 turns to star16, from groundstation4, satellite1 turns to star6, instrument3 that is on satellite1 is calibrated to star6, satellite1 turns from star6 to planet14, instrument3 which is on satellite1 takes an image of planet14 in spectrograph1, satellite1 turns to star10 from planet14, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, satellite1 turns to star12 from star10, image of star12 is taken with instrument3 on satellite1 in spectrograph1, from star12, satellite1 turns to star0, satellite0 turns to groundstation2 from star1, calibration of instrument0 which is on satellite0 to groundstation2 is complete, satellite0 turns from groundstation2 to phenomenon15, image of phenomenon15 is taken with instrument0 on satellite0 in spectrograph0, from phenomenon15, satellite0 turns to star11, instrument0 which is on satellite0 takes an image of star11 in thermograph4, satellite0 turns to star13 from star11 and image of star13 is taken with instrument0 on satellite0 in spectrograph0 to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "answer": "1", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for groundstation4 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument3 for star6 is complete, for groundstation9, instrument2 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is supported by instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for star0, instrument0 is on board satellite0, instrument0 supports thermograph4, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph0, instrument2 is calibrated for groundstation4, instrument2 is calibrated for star7, instrument2 supports spectrograph2, instrument3 supports spectrograph1, power is available for satellite1, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has instrument2 on board, spectrograph0 is supported by instrument0, spectrograph0 is supported by instrument2, spectrograph1 is compatible with instrument1, spectrograph2 is supported by instrument3 and star1 is where satellite0 is pointed.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned: satellite1's instrument3 is activated, then satellite0 rotates to face star16 from star12, and satellite1 rotates to face star6 from groundstation4. Next, instrument3 on satellite1 is calibrated to star6, followed by satellite1 rotating to planet14 from star6. Instrument3 on satellite1 captures an image of planet14 using spectrograph1, then satellite1 rotates to star10 from planet14 and captures an image of star10 using spectrograph1. Satellite1 then rotates to star12 from star10 and captures an image of star12 using spectrograph1. From star12, satellite1 rotates to star0, and satellite0 rotates to groundstation2 from star1. The calibration of instrument0 on satellite0 to groundstation2 is completed, followed by satellite0 rotating to phenomenon15 from groundstation2 and capturing an image of phenomenon15 using spectrograph0. Satellite0 then rotates to star11 from phenomenon15, captures an image of star11 using thermograph4, and finally rotates to star13 from star11, capturing an image of star13 using spectrograph0 to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 at groundstation4 has been finalized, instrument1's calibration at groundstation2 is also complete, and instrument1's calibration for star8 has been finished. Additionally, instrument3's calibration for star6 is complete. For groundstation9, the calibration of instrument2 is done. Satellite1 is currently directed towards groundstation4. Instrument2 is capable of supporting infrared3. Furthermore, instrument0 has been calibrated for groundstation2 and star0, and it is installed on satellite0, providing support for thermograph4. Instrument1, also on board satellite0, has been calibrated for groundstation4 and supports spectrograph0. Instrument2 has been calibrated for groundstation4 and star7, and it supports spectrograph2. Instrument3 supports spectrograph1. Satellite1 has power available, and satellite0 also has power available. Satellite1 is equipped with instrument3 on board, and it also carries instrument2. Instrument0 and instrument2 both support spectrograph0, and spectrograph1 is compatible with instrument1. Spectrograph2 is supported by instrument3, and satellite0 is currently pointed towards star1."}
{"question_id": "f4777db3-12e0-42f4-ae18-cf71bb8237af", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, on satellite0, instrument0 is switched on, satellite1 turns from groundstation4 to star6, instrument3 that is on satellite1 is calibrated to star6, from star6, satellite1 turns to planet14, image of planet14 is taken with instrument3 on satellite1 in spectrograph1, satellite1 turns to star10 from planet14, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, from star10, satellite1 turns to star12 and image of star12 is taken with instrument3 on satellite1 in spectrograph1 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "answer": "225", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for groundstation4 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument3 for star6 is complete, for groundstation9, instrument2 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is supported by instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for star0, instrument0 is on board satellite0, instrument0 supports thermograph4, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph0, instrument2 is calibrated for groundstation4, instrument2 is calibrated for star7, instrument2 supports spectrograph2, instrument3 supports spectrograph1, power is available for satellite1, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has instrument2 on board, spectrograph0 is supported by instrument0, spectrograph0 is supported by instrument2, spectrograph1 is compatible with instrument1, spectrograph2 is supported by instrument3 and star1 is where satellite0 is pointed.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is activated, satellite1 reorients from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, then satellite1 reorients from star6 to planet14, an image of planet14 is captured using instrument3 on satellite1 in spectrograph1, satellite1 reorients from planet14 to star10, an image of star10 is captured using instrument3 on satellite1 in spectrograph1, and finally, satellite1 reorients from star10 to star12, capturing an image of star12 with instrument3 on satellite1 in spectrograph1, resulting in the current state. In this state, what is the total number of valid properties of the state (including both affirmative and negated properties)? Provide the answer as an integer, or None if there are no valid properties.", "initial_state_nl_paraphrased": "The calibration process for instrument0 at groundstation4 has been finalized, instrument1's calibration at groundstation2 is also complete, and instrument1's calibration for star8 has been finished. Additionally, instrument3's calibration for star6 is complete. For groundstation9, the calibration of instrument2 is confirmed. Satellite1 is currently directed towards groundstation4. Instrument2 is capable of supporting infrared3. Furthermore, instrument0 has been calibrated for groundstation2 and star0, and it is installed on satellite0, providing support for thermograph4. Instrument1, also on board satellite0, has been calibrated for groundstation4 and supports spectrograph0. Instrument2 has been calibrated for groundstation4 and star7, and it supports spectrograph2. Instrument3 supports spectrograph1. Satellite1 has power available, and satellite0 also has power. Satellite1 is equipped with instrument3 on board, and instrument2 is also on board satellite1. Spectrograph0 is supported by both instrument0 and instrument2, while spectrograph1 is compatible with instrument1. Spectrograph2 is supported by instrument3, and satellite0 is currently pointed towards star1."}
{"question_id": "9f8f6ef3-2386-4284-a0df-2517699e44fe", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, instrument0 on satellite0 is switched on, from groundstation4, satellite1 turns to star6, instrument3 is calibrated on satellite1 to star6, satellite1 turns to planet14 from star6, image of planet14 is taken with instrument3 on satellite1 in spectrograph1, satellite1 turns from planet14 to star10, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, satellite1 turns to star12 from star10, satellite1's instrument3 takes an image of star12 in spectrograph1, satellite1 turns to star0 from star12, from star1, satellite0 turns to groundstation2, calibration of instrument0 which is on satellite0 to groundstation2 is complete, satellite0 turns from groundstation2 to phenomenon15, satellite0's instrument0 takes an image of phenomenon15 in spectrograph0, satellite0 turns from phenomenon15 to star11, image of star11 is taken with instrument0 on satellite0 in thermograph4, satellite0 turns to star13 from star11 and satellite0's instrument0 takes an image of star13 in spectrograph0 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "answer": "225", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for groundstation4 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument3 for star6 is complete, for groundstation9, instrument2 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is supported by instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for star0, instrument0 is on board satellite0, instrument0 supports thermograph4, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph0, instrument2 is calibrated for groundstation4, instrument2 is calibrated for star7, instrument2 supports spectrograph2, instrument3 supports spectrograph1, power is available for satellite1, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has instrument2 on board, spectrograph0 is supported by instrument0, spectrograph0 is supported by instrument2, spectrograph1 is compatible with instrument1, spectrograph2 is supported by instrument3 and star1 is where satellite0 is pointed.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is activated, satellite1 rotates from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, satellite1 then rotates from star6 to planet14, an image of planet14 is captured using instrument3 on satellite1 in spectrograph1, satellite1 rotates from planet14 to star10, an image of star10 is taken by instrument3 on satellite1 in spectrograph1, satellite1 then rotates from star10 to star12, an image of star12 is captured by instrument3 on satellite1 in spectrograph1, and satellite1 rotates from star12 to star0, meanwhile, from star1, satellite0 rotates to groundstation2, the calibration of instrument0 on satellite0 to groundstation2 is completed, satellite0 then rotates from groundstation2 to phenomenon15, an image of phenomenon15 is captured by instrument0 on satellite0 in spectrograph0, satellite0 rotates from phenomenon15 to star11, an image of star11 is taken by instrument0 on satellite0 in thermograph4, and finally, satellite0 rotates from star11 to star13, capturing an image of star13 with instrument0 in spectrograph0, resulting in the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 at groundstation4 has been finalized, instrument1's calibration at groundstation2 is also complete, and instrument1's calibration for star8 has been finished. Additionally, instrument3's calibration for star6 is complete. For groundstation9, the calibration of instrument2 is confirmed. Satellite1 is currently directed towards groundstation4. Instrument2 is capable of supporting infrared3. Furthermore, instrument0 has been calibrated for groundstation2 and star0, and it is installed on satellite0, providing support for thermograph4. Instrument1, also on board satellite0, has been calibrated for groundstation4 and supports spectrograph0. Instrument2 has been calibrated for groundstation4 and star7, and it supports spectrograph2. Instrument3 supports spectrograph1. Satellite1 has power available, and satellite0 also has power. Satellite1 is equipped with instrument3 on board, and it also carries instrument2. Instrument0 and instrument2 both support spectrograph0, and spectrograph1 is compatible with instrument1. Spectrograph2 is supported by instrument3, and satellite0 is currently pointed towards star1."}
{"question_id": "08b625bb-5b73-489e-b4bf-4bc60e6a631d", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite0, instrument1 is switched on, from groundstation2, satellite0 turns to groundstation0, instrument1 that is on satellite0 is calibrated to groundstation0, satellite0 turns from groundstation0 to planet11, instrument1 which is on satellite0 takes an image of planet11 in image5, instrument1 which is on satellite0 takes an image of planet11 in image6, satellite0 turns from planet11 to planet13, satellite0's instrument1 takes an image of planet13 in image5, image of planet13 is taken with instrument1 on satellite0 in spectrograph2 and satellite0 turns to star10 from planet13 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "answer": "317", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star3 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation9 is complete, for groundstation0, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, image6 is compatible with instrument0, image6 is compatible with instrument1, instrument1 is calibrated for groundstation6, instrument4 is calibrated for groundstation8, instrument4 is on board satellite1, instrument4 supports image1, instrument4 supports infrared7, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has power, satellite1 has power, satellite1 is pointing to planet13, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are executed: instrument1 is activated on satellite0, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, satellite0 then reorients from groundstation0 to planet11, instrument1 on satellite0 captures an image of planet11 in image5 and another in image6, satellite0 reorients from planet11 to planet13, instrument1 on satellite0 captures an image of planet13 in image5, and an image of planet13 is captured with instrument1 on satellite0 in spectrograph2, before satellite0 reorients to star10 from planet13 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star3 has been finalized, instrument2's calibration for star4 is also complete, and instrument3's calibration for groundstation9 is finished. Groundstation0 has instrument1 calibrated, while groundstation7 has instrument0 calibrated. Satellite0 is currently directed towards groundstation2. Image0 is compatible for use with instrument2, and image1 is also compatible with instrument2. Additionally, image1 is supported by instrument3, and image5 is supported by instrument1. Image6 is compatible with both instrument0 and instrument1. Instrument1 has been calibrated for groundstation6, and instrument4 has been calibrated for groundstation8. Instrument4 is installed on satellite1 and supports both image1 and infrared7. Satellite0 has instruments 0, 1, 2, and 3 on board and is currently operational. Satellite1 also has power and is directed towards planet13. Furthermore, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is compatible with instrument3."}
{"question_id": "eae53911-a14e-4d29-b077-d7a061464113", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is switched on, satellite1 turns to star1 from star3, instrument1 that is on satellite1 is calibrated to star1, from star1, satellite1 turns to phenomenon10, image of phenomenon10 is taken with instrument1 on satellite1 in image5, image of phenomenon10 is taken with instrument1 on satellite1 in spectrograph3, satellite1 turns from phenomenon10 to phenomenon11, instrument1 which is on satellite1 takes an image of phenomenon11 in spectrograph1, satellite1 turns from phenomenon11 to phenomenon5, satellite1's instrument1 takes an image of phenomenon5 in image4, satellite1's instrument1 takes an image of phenomenon5 in image5, from phenomenon5, satellite1 turns to phenomenon7, instrument1 which is on satellite1 takes an image of phenomenon7 in image0, satellite1's instrument1 takes an image of phenomenon7 in image4, satellite1 turns to phenomenon9 from phenomenon7, image of phenomenon9 is taken with instrument1 on satellite1 in image5, satellite1's instrument1 takes an image of phenomenon9 in spectrograph1, from phenomenon9, satellite1 turns to planet8 and image of planet8 is taken with instrument1 on satellite1 in image5 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "578", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image2 is compatible with instrument0, image2 is compatible with instrument1, instrument1 supports image0, instrument1 supports image4, instrument1 supports image5, instrument1 supports spectrograph3, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is aimed towards star3 and spectrograph1 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: on satellite1, instrument1 is activated, then satellite1 reorients from star3 to star1, and instrument1 on satellite1 is calibrated to star1. Satellite1 then turns to face phenomenon10, capturing its image using instrument1 in both image5 and spectrograph3. Next, satellite1 reorients to phenomenon11, and instrument1 on satellite1 captures an image of it in spectrograph1. Satellite1 then turns to phenomenon5, and instrument1 on satellite1 captures images of it in both image4 and image5. From phenomenon5, satellite1 reorients to phenomenon7, and instrument1 on satellite1 captures images of it in image0 and image4. Satellite1 then turns to phenomenon9, and instrument1 on satellite1 captures images of it in image5 and spectrograph1. Finally, satellite1 reorients to planet8, capturing its image in image5, resulting in the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image2 is found to be compatible with both instrument0 and instrument1. Additionally, instrument1 is capable of supporting image0, image4, and image5, as well as spectrograph3. The satellite0 is currently focused on phenomenon5. Satellite0 is equipped with instrument0 and has a power source. Similarly, satellite1 is equipped with instrument1 and also has a power source. Satellite1 is currently directed towards star3, and instrument1 provides support for spectrograph1."}
