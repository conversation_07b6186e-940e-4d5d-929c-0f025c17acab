{"question_id": "d99e3710-bcae-444d-9f25-0ef074155a2e", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, instrument0 on satellite0 is switched on, from phenomenon10, satellite1 turns to groundstation5, calibration of instrument3 which is on satellite1 to groundstation5 is complete, satellite1 turns from groundstation5 to phenomenon16, satellite1's instrument3 takes an image of phenomenon16 in image3, from phenomenon16, satellite1 turns to phenomenon17, image of phenomenon17 is taken with instrument3 on satellite1 in image3, satellite1 turns from phenomenon17 to planet11 and instrument3 which is on satellite1 takes an image of planet11 in image3 to reach the current state. In this state, is the number of executable actions equal to 36? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, calibration of instrument3 for star8 is complete, for star1, instrument0 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image2 is supported by instrument2, image2 is supported by instrument3, image3 is compatible with instrument0, image3 is compatible with instrument1, image3 is supported by instrument3, infrared1 is supported by instrument0, instrument1 is on board satellite0, instrument1 supports image0, instrument1 supports infrared1, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument2 supports image3, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 has instrument0 on board, satellite0 has instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated with respect to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, and captures an image of phenomenon16 using instrument3, which is stored in image3, satellite1 reorients from phenomenon16 to phenomenon17, and captures an image of phenomenon17 using instrument3 on satellite1, also stored in image3, and finally, satellite1 reorients from phenomenon17 to planet11, capturing an image of planet11 using instrument3 on satellite1, which is stored in image3, resulting in the current state. In this state, is the number of executable actions equal to 36? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is now complete, instrument3's calibration for star6 and star8 is also complete. For star1 and star9, instrument0 has been calibrated. Satellite0 is currently directed towards groundstation3. Instrument2 and instrument3 both support image2, while instrument0 and instrument1 are compatible with image3, which is also supported by instrument3. Instrument0 supports infrared1. Instrument1, which is on board satellite0, supports both image0 and infrared1. Instrument2 has been calibrated for groundstations 5 and 7, and it supports image3. Instrument3, calibrated for groundstation5, supports image0. Satellite0 has instruments 0, 1, and 2 on board and has available power. Satellite1, which has instrument3 on board, has power and is currently pointing towards phenomenon10."}
{"question_id": "0373dd6c-f835-40e4-884e-3233b304c07c", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is switched on, from groundstation2, satellite0 turns to groundstation0, instrument1 that is on satellite0 is calibrated to groundstation0, satellite0 turns from groundstation0 to planet11, image of planet11 is taken with instrument1 on satellite0 in image5, instrument1 which is on satellite0 takes an image of planet11 in image6, from planet11, satellite0 turns to planet13, image of planet13 is taken with instrument1 on satellite0 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2, from planet13, satellite0 turns to star10, satellite0's instrument1 takes an image of star10 in image6, satellite0's instrument1 takes an image of star10 in spectrograph2, instrument1 on satellite0 is switched off, instrument2 that is on satellite0 is turned on, satellite0 turns to star4 from star10, calibration of instrument2 which is on satellite0 to star4 is complete, satellite0 turns from star4 to star16, instrument2 which is on satellite0 takes an image of star16 in image0 and on satellite0, instrument2 is switched off to reach the current state. In this state, is the number of executable and inexecutable actions equal to 2215? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for star3 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation9 is complete, for groundstation0, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, image6 is compatible with instrument0, image6 is compatible with instrument1, instrument1 is calibrated for groundstation6, instrument4 is calibrated for groundstation8, instrument4 is on board satellite1, instrument4 supports image1, instrument4 supports infrared7, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has power, satellite1 has power, satellite1 is pointing to planet13, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, then satellite0 reorients from groundstation0 to planet11, an image of planet11 is captured using instrument1 on satellite0 and stored in image5, another image of planet11 is taken by instrument1 on satellite0 and stored in image6, satellite0 then reorients from planet11 to planet13, an image of planet13 is captured using instrument1 on satellite0 and stored in image5, instrument1 on satellite0 captures an image of planet13 in spectrograph2, satellite0 reorients from planet13 to star10, instrument1 on satellite0 captures an image of star10 in image6, and another image of star10 in spectrograph2, instrument1 on satellite0 is deactivated, instrument2 on satellite0 is activated, satellite0 reorients from star10 to star4, instrument2 on satellite0 is calibrated to star4, satellite0 reorients from star4 to star16, instrument2 on satellite0 captures an image of star16 in image0, and finally instrument2 on satellite0 is deactivated to reach the current state. In this state, is the number of executable and inexecutable actions equal to 2215? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star3 has been finalized, instrument2's calibration for star4 is also complete, and instrument3's calibration for groundstation9 is finished. Groundstation0 has instrument1 calibrated, while groundstation7 has instrument0 calibrated. Satellite0 is currently directed towards groundstation2. Image0 is compatible for use with instrument2, and image1 is also compatible with instrument2. Additionally, image1 is supported by instrument3, and image5 is supported by instrument1. Image6 is compatible with both instrument0 and instrument1. Instrument1 has been calibrated for groundstation6, and instrument4 has been calibrated for groundstation8. Instrument4 is installed on satellite1 and supports both image1 and infrared7. Satellite0 has instruments 0, 1, 2, and 3 on board and is currently operational. Satellite1 also has power and is directed towards planet13. Furthermore, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is compatible with instrument3."}
{"question_id": "66490601-f886-4135-af8c-b747123bda9e", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite0, instrument1 is switched on, satellite0 turns to groundstation0 from groundstation2, instrument1 that is on satellite0 is calibrated to groundstation0, satellite0 turns to planet11 from groundstation0, instrument1 which is on satellite0 takes an image of planet11 in image5, image of planet11 is taken with instrument1 on satellite0 in image6, satellite0 turns to planet13 from planet11, satellite0's instrument1 takes an image of planet13 in image5, instrument1 which is on satellite0 takes an image of planet13 in spectrograph2, satellite0 turns from planet13 to star10, satellite0's instrument1 takes an image of star10 in image6, image of star10 is taken with instrument1 on satellite0 in spectrograph2, instrument1 on satellite0 is switched off, on satellite0, instrument2 is switched on, satellite0 turns to star4 from star10, calibration of instrument2 which is on satellite0 to star4 is complete, from star4, satellite0 turns to star16, image of star16 is taken with instrument2 on satellite0 in image0 and on satellite0, instrument2 is switched off to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 36? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for star3 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation9 is complete, for groundstation0, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, image6 is compatible with instrument0, image6 is compatible with instrument1, instrument1 is calibrated for groundstation6, instrument4 is calibrated for groundstation8, instrument4 is on board satellite1, instrument4 supports image1, instrument4 supports infrared7, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has power, satellite1 has power, satellite1 is pointing to planet13, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: on satellite0, instrument1 is activated, then satellite0 reorients from groundstation2 to groundstation0, followed by the calibration of instrument1 on satellite0 to groundstation0. Next, satellite0 reorients from groundstation0 to planet11, and instrument1 on satellite0 captures an image of planet11 in image5, as well as in image6. Satellite0 then reorients from planet11 to planet13, and instrument1 on satellite0 captures an image of planet13 in image5 and in spectrograph2. After that, satellite0 reorients from planet13 to star10, and instrument1 on satellite0 captures an image of star10 in image6 and in spectrograph2. Instrument1 on satellite0 is then deactivated. Subsequently, instrument2 on satellite0 is activated, and satellite0 reorients from star10 to star4, followed by the calibration of instrument2 on satellite0 to star4. Satellite0 then reorients from star4 to star16, and an image of star16 is captured with instrument2 on satellite0 in image0. Finally, instrument2 on satellite0 is deactivated, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 36? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star3 has been finalized, instrument2's calibration for star4 is also complete, and instrument3's calibration for groundstation9 is finished. Groundstation0 has instrument1 calibrated, while groundstation7 has instrument0 calibrated. Satellite0 is currently directed towards groundstation2. Image0 is compatible for use with instrument2, and image1 is also compatible with instrument2. Additionally, image1 is supported by instrument3, and image5 is supported by instrument1. Image6 is compatible with both instrument0 and instrument1. Instrument1 has been calibrated for groundstation6, and instrument4 has been calibrated for groundstation8. Instrument4 is on board satellite1 and supports both image1 and infrared7. Satellite0 has instruments 0, 1, 2, and 3 on board, and it is currently powered. Satellite1 also has power and is directed towards planet13. Furthermore, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is compatible with instrument3."}
{"question_id": "0a36dd1a-e3d0-4fd0-9896-5ab0bea89221", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is switched on to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 27? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star3 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation9 is complete, for groundstation0, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, image6 is compatible with instrument0, image6 is compatible with instrument1, instrument1 is calibrated for groundstation6, instrument4 is calibrated for groundstation8, instrument4 is on board satellite1, instrument4 supports image1, instrument4 supports infrared7, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has power, satellite1 has power, satellite1 is pointing to planet13, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: instrument1 on satellite0 is activated to achieve the current state. In this state, does the count of valid properties that do not include negations equal 27? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star3 has been finalized, instrument2's calibration for star4 is also complete, and instrument3's calibration for groundstation9 is finished. Groundstation0 has instrument1 calibrated, while groundstation7 has instrument0 calibrated. Satellite0 is currently directed towards groundstation2. Image0 is compatible for use with instrument2, and image1 is also compatible with instrument2. Additionally, image1 is supported by instrument3, and image5 is supported by instrument1. Image6 is compatible with both instrument0 and instrument1. Instrument1 has been calibrated for groundstation6, and instrument4 has been calibrated for groundstation8. Instrument4 is installed on satellite1 and supports both image1 and infrared7. Satellite0 is equipped with instruments 0, 1, 2, and 3 on board and has power. Satellite1 also has power and is currently pointing towards planet13. Furthermore, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is compatible with instrument3."}
{"question_id": "8bf07150-ada6-429f-ba71-847258a3064d", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on, satellite0 turns to groundstation0 from groundstation2, calibration of instrument1 which is on satellite0 to groundstation0 is complete, satellite0 turns from groundstation0 to planet11, satellite0's instrument1 takes an image of planet11 in image5, satellite0's instrument1 takes an image of planet11 in image6, satellite0 turns from planet11 to planet13, instrument1 which is on satellite0 takes an image of planet13 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2 and satellite0 turns to star10 from planet13 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 268? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star3 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation9 is complete, for groundstation0, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, image6 is compatible with instrument0, image6 is compatible with instrument1, instrument1 is calibrated for groundstation6, instrument4 is calibrated for groundstation8, instrument4 is on board satellite1, instrument4 supports image1, instrument4 supports infrared7, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has power, satellite1 has power, satellite1 is pointing to planet13, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, satellite0 then reorients from groundstation0 to planet11, captures an image of planet11 in image5 and another in image6 using instrument1, reorients from planet11 to planet13, captures an image of planet13 in image5 using instrument1, and captures another image of planet13 in spectrograph2 using instrument1, before finally reorienting from planet13 to star10 to reach the current state. In this state, is the count of valid state properties involving negations equal to 268? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star3 has been finalized, instrument2's calibration for star4 is also complete, and instrument3's calibration for groundstation9 is finished. Groundstation0 has instrument1 calibrated, while groundstation7 has instrument0 calibrated. Satellite0 is currently directed towards groundstation2. Image0 is compatible for use with instrument2, and image1 is also compatible with instrument2. Additionally, image1 is supported by instrument3, and image5 is supported by instrument1. Image6 is compatible with both instrument0 and instrument1. Instrument1 has been calibrated for groundstation6, and instrument4 has been calibrated for groundstation8. Instrument4 is installed on satellite1 and supports both image1 and infrared7. Satellite0 is equipped with instruments 0, 1, 2, and 3 on board and has power. Satellite1 also has power and is currently pointing towards planet13. Furthermore, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is compatible with instrument3."}
{"question_id": "1e372db7-1f57-4ca5-8250-1b2cb7dc4d5d", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, instrument0 that is on satellite0 is turned on, from groundstation4, satellite1 turns to star6, calibration of instrument3 which is on satellite1 to star6 is complete, satellite1 turns to planet14 from star6, instrument3 which is on satellite1 takes an image of planet14 in spectrograph1, satellite1 turns to star10 from planet14, image of star10 is taken with instrument3 on satellite1 in spectrograph1, satellite1 turns from star10 to star12 and satellite1's instrument3 takes an image of star12 in spectrograph1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 231? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for groundstation4 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument3 for star6 is complete, for groundstation9, instrument2 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is supported by instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for star0, instrument0 is on board satellite0, instrument0 supports thermograph4, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph0, instrument2 is calibrated for groundstation4, instrument2 is calibrated for star7, instrument2 supports spectrograph2, instrument3 supports spectrograph1, power is available for satellite1, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has instrument2 on board, spectrograph0 is supported by instrument0, spectrograph0 is supported by instrument2, spectrograph1 is compatible with instrument1, spectrograph2 is supported by instrument3 and star1 is where satellite0 is pointed.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following sequence of actions occurs: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 rotates to face star6 from groundstation4, instrument3 on satellite1 is calibrated to star6, satellite1 then shifts its orientation from star6 to planet14, an image of planet14 is captured using instrument3 on satellite1 in spectrograph1, satellite1 then turns from planet14 to star10, an image of star10 is taken with instrument3 on satellite1 in spectrograph1, and finally, satellite1 turns from star10 to star12, capturing an image of star12 with instrument3 in spectrograph1, resulting in the current state. In this state, is the count of valid properties that include negations equal to 231? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 at groundstation4 has been finalized, instrument1's calibration at groundstation2 is also complete, and instrument1's calibration for star8 has been finished. Additionally, instrument3's calibration for star6 is complete. For groundstation9, the calibration of instrument2 is confirmed. Satellite1 is currently directed towards groundstation4. Instrument2 is capable of supporting infrared3. Furthermore, instrument0 has been calibrated for groundstation2 and star0, and it is installed on satellite0, providing support for thermograph4. Instrument1, also on board satellite0, has been calibrated for groundstation4 and supports spectrograph0. Instrument2 has been calibrated for groundstation4 and star7, and it supports spectrograph2. Instrument3 supports spectrograph1. Satellite1 has power available, and satellite0 also has power. Satellite1 is equipped with instrument3 on board, and instrument2 is also on board satellite1. Spectrograph0 is supported by both instrument0 and instrument2, while spectrograph1 is compatible with instrument1. Spectrograph2 is supported by instrument3, and satellite0 is currently pointed towards star1."}
{"question_id": "eb094eca-b8e4-4d6f-9a09-ad829c09609c", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, on satellite0, instrument0 is switched on, satellite1 turns from phenomenon10 to groundstation5, instrument3 that is on satellite1 is calibrated to groundstation5, satellite1 turns from groundstation5 to phenomenon16, satellite1's instrument3 takes an image of phenomenon16 in image3, satellite1 turns from phenomenon16 to phenomenon17, satellite1's instrument3 takes an image of phenomenon17 in image3, satellite1 turns from phenomenon17 to planet11 and instrument3 which is on satellite1 takes an image of planet11 in image3 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 183? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, calibration of instrument3 for star8 is complete, for star1, instrument0 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image2 is supported by instrument2, image2 is supported by instrument3, image3 is compatible with instrument0, image3 is compatible with instrument1, image3 is supported by instrument3, infrared1 is supported by instrument0, instrument1 is on board satellite0, instrument1 supports image0, instrument1 supports infrared1, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument2 supports image3, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 has instrument0 on board, satellite0 has instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 is activated on satellite1, instrument0 is activated on satellite0, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, captures an image of phenomenon16 using instrument3 in image3, reorients from phenomenon16 to phenomenon17, captures an image of phenomenon17 using instrument3 in image3, and finally reorients from phenomenon17 to planet11, capturing an image of planet11 using instrument3 in image3, resulting in the current state. In this state, is the count of valid state properties involving negations equal to 183? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is now complete, instrument3's calibration for star6 and star8 is also complete. For star1 and star9, instrument0 has been calibrated. The satellite0 is currently directed towards groundstation3. Instrument2 and instrument3 both support image2, while image3 is compatible with instrument0 and instrument1, and is also supported by instrument3. Instrument0 supports infrared1. Instrument1, which is on board satellite0, supports both image0 and infrared1. Instrument2 has been calibrated for groundstation5 and groundstation7, and supports image3. Instrument3, calibrated for groundstation5, supports image0. Satellite0 has instrument0 and instrument2 on board, and has available power. Satellite1 has instrument3 on board, has power, and is currently pointing towards phenomenon10."}
{"question_id": "9d8a8307-628b-4423-ad45-c6c4869b781f", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, on satellite0, instrument0 is switched on, from groundstation4, satellite1 turns to star6, calibration of instrument3 which is on satellite1 to star6 is complete, from star6, satellite1 turns to planet14, instrument3 which is on satellite1 takes an image of planet14 in spectrograph1, satellite1 turns to star10 from planet14, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, from star10, satellite1 turns to star12 and image of star12 is taken with instrument3 on satellite1 in spectrograph1 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 1386? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for groundstation4 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument3 for star6 is complete, for groundstation9, instrument2 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is supported by instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for star0, instrument0 is on board satellite0, instrument0 supports thermograph4, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph0, instrument2 is calibrated for groundstation4, instrument2 is calibrated for star7, instrument2 supports spectrograph2, instrument3 supports spectrograph1, power is available for satellite1, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has instrument2 on board, spectrograph0 is supported by instrument0, spectrograph0 is supported by instrument2, spectrograph1 is compatible with instrument1, spectrograph2 is supported by instrument3 and star1 is where satellite0 is pointed.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is switched on, satellite1 is directed towards star6 from groundstation4, instrument3 on satellite1 is calibrated to star6, then satellite1 is redirected to planet14 from star6, an image of planet14 is captured using spectrograph1 with instrument3 on satellite1, satellite1 is then redirected to star10 from planet14, an image of star10 is captured using spectrograph1 with instrument3 on satellite1, and finally, satellite1 is redirected to star12 from star10, where an image of star12 is captured using spectrograph1 with instrument3 on satellite1, resulting in the current state. In this state, is the number of executable and inexecutable actions equal to 1386? True or False", "initial_state_nl_paraphrased": "Calibration of instrument0 for groundstation4 has been finalized, instrument1's calibration for groundstation2 is also complete, the calibration process for instrument1 with star8 has been completed, and instrument3's calibration for star6 is finished. For groundstation9, the calibration of instrument2 is complete. Satellite1 is currently directed towards groundstation4. Instrument2 is capable of supporting infrared3. Instrument0 has been calibrated for groundstation2 and star0, and it is installed on satellite0, providing support for thermograph4. Instrument1, also on board satellite0, has been calibrated for groundstation4 and supports spectrograph0. Instrument2 has been calibrated for groundstation4 and star7, and it supports spectrograph2. Instrument3 supports spectrograph1. Satellite1 has power available, and satellite0 also has power. Satellite1 is equipped with instrument3 on board, and it also carries instrument2. Spectrograph0 is supported by both instrument0 and instrument2, while spectrograph1 is compatible with instrument1, and spectrograph2 is supported by instrument3. Satellite0 is currently pointed towards star1."}
{"question_id": "f1e5369b-9377-4d5c-b59a-49ed20be5f71", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on, satellite0 turns to groundstation0 from groundstation2, calibration of instrument1 which is on satellite0 to groundstation0 is complete, from groundstation0, satellite0 turns to planet11, satellite0's instrument1 takes an image of planet11 in image5, instrument1 which is on satellite0 takes an image of planet11 in image6, satellite0 turns to planet13 from planet11, satellite0's instrument1 takes an image of planet13 in image5, image of planet13 is taken with instrument1 on satellite0 in spectrograph2 and satellite0 turns from planet13 to star10 to reach the current state. In this state, is the number of executable actions equal to 33? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star3 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation9 is complete, for groundstation0, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, image6 is compatible with instrument0, image6 is compatible with instrument1, instrument1 is calibrated for groundstation6, instrument4 is calibrated for groundstation8, instrument4 is on board satellite1, instrument4 supports image1, instrument4 supports infrared7, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has power, satellite1 has power, satellite1 is pointing to planet13, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, then satellite0 reorients from groundstation0 to planet11, captures an image of planet11 using instrument1 in image5, and also takes another image of planet11 using instrument1 in image6, after which satellite0 reorients from planet11 to planet13, captures an image of planet13 using instrument1 in image5, and takes a spectrograph of planet13 using instrument1 in spectrograph2, and finally reorients from planet13 to star10 to reach the current state. In this state, is the number of executable actions equal to 33? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star3 has been finalized, instrument2's calibration for star4 is also complete, and instrument3's calibration for groundstation9 is finished. Groundstation0 has instrument1 calibrated, while groundstation7 has instrument0 calibrated. Satellite0 is currently directed towards groundstation2. Image0 is compatible for use with instrument2, and image1 is also compatible with instrument2. Additionally, image1 is supported by instrument3, and image5 is supported by instrument1. Image6 is compatible with both instrument0 and instrument1. Instrument1 has been calibrated for groundstation6, and instrument4 has been calibrated for groundstation8. Instrument4 is installed on satellite1 and supports both image1 and infrared7. Satellite0 has instruments 0, 1, 2, and 3 on board and is currently operational. Satellite1 also has power and is directed towards planet13. Furthermore, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is compatible with instrument3."}
{"question_id": "004077b8-0a25-4cc0-af0c-d49d67a68cd8", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, on satellite0, instrument0 is switched on, from groundstation4, satellite1 turns to star6, instrument3 is calibrated on satellite1 to star6, satellite1 turns to planet14 from star6, instrument3 which is on satellite1 takes an image of planet14 in spectrograph1, satellite1 turns to star10 from planet14, image of star10 is taken with instrument3 on satellite1 in spectrograph1, satellite1 turns from star10 to star12, satellite1's instrument3 takes an image of star12 in spectrograph1, from star12, satellite1 turns to star0, satellite0 turns from star1 to groundstation2, calibration of instrument0 which is on satellite0 to groundstation2 is complete, satellite0 turns to phenomenon15 from groundstation2, instrument0 which is on satellite0 takes an image of phenomenon15 in spectrograph0, satellite0 turns from phenomenon15 to star11, image of star11 is taken with instrument0 on satellite0 in thermograph4, from star11, satellite0 turns to star13 and image of star13 is taken with instrument0 on satellite0 in spectrograph0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 35? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for groundstation4 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument3 for star6 is complete, for groundstation9, instrument2 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is supported by instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for star0, instrument0 is on board satellite0, instrument0 supports thermograph4, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph0, instrument2 is calibrated for groundstation4, instrument2 is calibrated for star7, instrument2 supports spectrograph2, instrument3 supports spectrograph1, power is available for satellite1, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has instrument2 on board, spectrograph0 is supported by instrument0, spectrograph0 is supported by instrument2, spectrograph1 is compatible with instrument1, spectrograph2 is supported by instrument3 and star1 is where satellite0 is pointed.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 is activated on satellite1, instrument0 is activated on satellite0, satellite1 is directed towards star6 from groundstation4, instrument3 on satellite1 is calibrated to star6, satellite1 is then redirected from star6 to planet14, and instrument3 on satellite1 captures an image of planet14 using spectrograph1. Next, satellite1 is redirected from planet14 to star10, and an image of star10 is taken using instrument3 on satellite1 in spectrograph1. Satellite1 is then redirected from star10 to star12, and instrument3 on satellite1 captures an image of star12 in spectrograph1. From star12, satellite1 is redirected to star0. Meanwhile, satellite0 is redirected from star1 to groundstation2, and the calibration of instrument0 on satellite0 to groundstation2 is completed. Satellite0 is then redirected from groundstation2 to phenomenon15, and instrument0 on satellite0 captures an image of phenomenon15 in spectrograph0. Satellite0 is then redirected from phenomenon15 to star11, and an image of star11 is taken using instrument0 on satellite0 in thermograph4. Finally, satellite0 is redirected from star11 to star13, and an image of star13 is taken using instrument0 on satellite0 in spectrograph0, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 35? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 at groundstation4 has been finalized, instrument1's calibration at groundstation2 is also complete, and instrument1's calibration for star8 has been finished. Additionally, instrument3's calibration for star6 is complete. For groundstation9, the calibration of instrument2 is confirmed. Satellite1 is currently directed towards groundstation4. Instrument2 is capable of supporting infrared3. Furthermore, instrument0 has been calibrated for groundstation2 and star0, and it is installed on satellite0, providing support for thermograph4. Instrument1, also on board satellite0, has been calibrated for groundstation4 and supports spectrograph0. Instrument2 has been calibrated for groundstation4 and star7, and it supports spectrograph2. Instrument3 supports spectrograph1. Satellite1 has available power, and satellite0 also has power available. Satellite1 is equipped with instrument3 on board, and it also carries instrument2. Instrument0 and instrument2 both support spectrograph0, and spectrograph1 is compatible with instrument1. Spectrograph2 is supported by instrument3, and satellite0 is currently pointed towards star1."}
{"question_id": "63c79a7e-bb66-42c5-bf45-7c189029b277", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is switched on, satellite0 turns from groundstation2 to groundstation0, calibration of instrument1 which is on satellite0 to groundstation0 is complete, from groundstation0, satellite0 turns to planet11, satellite0's instrument1 takes an image of planet11 in image5, instrument1 which is on satellite0 takes an image of planet11 in image6, from planet11, satellite0 turns to planet13, satellite0's instrument1 takes an image of planet13 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2, satellite0 turns to star10 from planet13, image of star10 is taken with instrument1 on satellite0 in image6, satellite0's instrument1 takes an image of star10 in spectrograph2, instrument1 that is on satellite0 is turned off, on satellite0, instrument2 is switched on, satellite0 turns from star10 to star4, instrument2 is calibrated on satellite0 to star4, satellite0 turns from star4 to star16, image of star16 is taken with instrument2 on satellite0 in image0 and instrument2 that is on satellite0 is turned off to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 33? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for star3 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation9 is complete, for groundstation0, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, image6 is compatible with instrument0, image6 is compatible with instrument1, instrument1 is calibrated for groundstation6, instrument4 is calibrated for groundstation8, instrument4 is on board satellite1, instrument4 supports image1, instrument4 supports infrared7, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has power, satellite1 has power, satellite1 is pointing to planet13, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated with respect to groundstation0, satellite0 then reorients from groundstation0 to planet11, instrument1 on satellite0 captures an image of planet11 in image5, another image of planet11 is taken by instrument1 on satellite0 in image6, satellite0 reorients from planet11 to planet13, instrument1 on satellite0 captures an image of planet13 in image5, and another image of planet13 is taken by instrument1 on satellite0 in spectrograph2, satellite0 then reorients from planet13 to star10, an image of star10 is captured by instrument1 on satellite0 in image6, instrument1 on satellite0 also captures an image of star10 in spectrograph2, instrument1 on satellite0 is then deactivated, instrument2 on satellite0 is activated, satellite0 reorients from star10 to star4, instrument2 on satellite0 is calibrated with respect to star4, satellite0 then reorients from star4 to star16, an image of star16 is captured by instrument2 on satellite0 in image0, and finally, instrument2 on satellite0 is deactivated to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 33? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star3 has been finalized, instrument2's calibration for star4 is also complete, and instrument3's calibration for groundstation9 is finished. Groundstation0 has instrument1 calibrated, while groundstation7 has instrument0 calibrated. Satellite0 is currently directed towards groundstation2. Image0 is compatible for use with instrument2, and image1 is also compatible with instrument2. Additionally, image1 is supported by instrument3, and image5 is supported by instrument1. Image6 is compatible with both instrument0 and instrument1. Instrument1 has been calibrated for groundstation6, and instrument4 has been calibrated for groundstation8. Instrument4 is on board satellite1 and supports both image1 and infrared7. Satellite0 has instruments 0, 1, 2, and 3 on board, and it is currently powered. Satellite1 also has power and is directed towards planet13. Furthermore, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is compatible with instrument3."}
{"question_id": "939e1c5f-7dc6-402e-a2c0-7c7c3a34f90f", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, on satellite0, instrument0 is switched on, satellite1 turns to groundstation5 from phenomenon10, instrument3 that is on satellite1 is calibrated to groundstation5, from groundstation5, satellite1 turns to phenomenon16, image of phenomenon16 is taken with instrument3 on satellite1 in image3, from phenomenon16, satellite1 turns to phenomenon17, satellite1's instrument3 takes an image of phenomenon17 in image3, from phenomenon17, satellite1 turns to planet11, image of planet11 is taken with instrument3 on satellite1 in image3, satellite1 turns to planet13 from planet11, image of planet13 is taken with instrument3 on satellite1 in image0, satellite1 turns to planet14 from planet13, instrument3 which is on satellite1 takes an image of planet14 in image0, from planet14, satellite1 turns to star15, satellite1's instrument3 takes an image of star15 in image2, satellite0 turns to star1 from groundstation3, calibration of instrument0 which is on satellite0 to star1 is complete and from star1, satellite0 turns to phenomenon10 to reach the current state. Is it True or False that the number of actions that led to current state in the sequence is equal to 21?", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, calibration of instrument3 for star8 is complete, for star1, instrument0 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image2 is supported by instrument2, image2 is supported by instrument3, image3 is compatible with instrument0, image3 is compatible with instrument1, image3 is supported by instrument3, infrared1 is supported by instrument0, instrument1 is on board satellite0, instrument1 supports image0, instrument1 supports infrared1, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument2 supports image3, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 has instrument0 on board, satellite0 has instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, an image of phenomenon16 is captured using instrument3 on satellite1 and stored in image3, satellite1 reorients from phenomenon16 to phenomenon17, instrument3 on satellite1 captures an image of phenomenon17 in image3, satellite1 reorients from phenomenon17 to planet11, an image of planet11 is taken with instrument3 on satellite1 in image3, satellite1 reorients from planet11 to planet13, an image of planet13 is captured using instrument3 on satellite1 and stored in image0, satellite1 reorients from planet13 to planet14, instrument3 on satellite1 captures an image of planet14 in image0, satellite1 reorients from planet14 to star15, instrument3 on satellite1 captures an image of star15 in image2, satellite0 reorients from groundstation3 to star1, calibration of instrument0 on satellite0 to star1 is completed, and satellite0 reorients from star1 to phenomenon10 to reach the current state. Is it True or False that the number of actions in the sequence that led to the current state is 21?", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is now complete, and instrument3's calibration for both star6 and star8 is also complete. For star1 and star9, instrument0 has been calibrated. Satellite0 is currently directed towards groundstation3. Instrument2 and instrument3 both support image2, while instrument0 and instrument1 are compatible with image3, which is also supported by instrument3. Instrument0 supports infrared1. Instrument1, which is on board satellite0, supports both image0 and infrared1. Instrument2 has been calibrated for groundstations 5 and 7, and it supports image3. Instrument3, calibrated for groundstation5, supports image0. Satellite0 has instruments 0, 1, and 2 on board, and it has power available. Satellite1, which has instrument3 on board, has power and is currently pointing towards phenomenon10."}
{"question_id": "40557723-64ad-4cf3-add2-722717943e1f", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite1 is turned on, satellite1 turns to star1 from star3, instrument1 is calibrated on satellite1 to star1, satellite1 turns to phenomenon10 from star1, instrument1 which is on satellite1 takes an image of phenomenon10 in image5, instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3, satellite1 turns to phenomenon11 from phenomenon10, image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, satellite1 turns from phenomenon11 to phenomenon5 and image of phenomenon5 is taken with instrument1 on satellite1 in image4 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 122? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image2 is compatible with instrument0, image2 is compatible with instrument1, instrument1 supports image0, instrument1 supports image4, instrument1 supports image5, instrument1 supports spectrograph3, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is aimed towards star3 and spectrograph1 is supported by instrument1.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the instrument1 on satellite1 is activated, satellite1 reorients from star3 to star1, instrument1 on satellite1 is calibrated to star1, satellite1 then reorients from star1 to phenomenon10, instrument1 on satellite1 captures an image of phenomenon10 in image5 and another in spectrograph3, satellite1 reorients from phenomenon10 to phenomenon11, an image of phenomenon11 is captured with instrument1 on satellite1 in spectrograph1, satellite1 then reorients from phenomenon11 to phenomenon5, and an image of phenomenon5 is captured with instrument1 on satellite1 in image4, resulting in the current state. In this state, does the count of valid state properties involving negations equal 122? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image2 is found to be compatible with both instrument0 and instrument1. Additionally, instrument1 is capable of supporting image0, image4, image5, and spectrograph3. The satellite0 is currently focused on phenomenon5. Satellite0 is equipped with instrument0 and has a power source. Similarly, satellite1 is equipped with instrument1 and also has a power source. Satellite1 is currently directed towards star3, and instrument1 provides support for spectrograph1."}
{"question_id": "30d6dc7e-a973-4867-9f85-68258df1b7e3", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 27? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for groundstation4 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument3 for star6 is complete, for groundstation9, instrument2 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is supported by instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for star0, instrument0 is on board satellite0, instrument0 supports thermograph4, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph0, instrument2 is calibrated for groundstation4, instrument2 is calibrated for star7, instrument2 supports spectrograph2, instrument3 supports spectrograph1, power is available for satellite1, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has instrument2 on board, spectrograph0 is supported by instrument0, spectrograph0 is supported by instrument2, spectrograph1 is compatible with instrument1, spectrograph2 is supported by instrument3 and star1 is where satellite0 is pointed.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, does the count of valid properties that do not include negations equal 27? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to groundstation4 has been completed, instrument1's calibration for groundstation2 is also finished, and the calibration of instrument1 for star8 is complete as well. Furthermore, instrument3's calibration for star6 has been finalized. For groundstation9, the calibration of instrument2 is complete. Satellite1 is currently directed towards groundstation4. Instrument2 is capable of supporting infrared3. Additionally, instrument0 has been calibrated for groundstation2 and star0, and it is installed on satellite0, providing support for thermograph4. Instrument1, also on board satellite0, has been calibrated for groundstation4 and supports spectrograph0. Instrument2 has been calibrated for groundstation4 and star7, and it supports spectrograph2. Instrument3 supports spectrograph1. Satellite1 has power available, and satellite0 also has power available. Satellite1 is equipped with instrument3 on board, and it also carries instrument2. Spectrograph0 is supported by both instrument0 and instrument2, while spectrograph1 is compatible with instrument1, and spectrograph2 is supported by instrument3. Lastly, satellite0 is currently pointed towards star1."}
{"question_id": "faed596e-b29d-43c0-b86d-d21c0e893025", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite0, instrument1 is switched on, satellite0 turns to groundstation0 from groundstation2, calibration of instrument1 which is on satellite0 to groundstation0 is complete, satellite0 turns to planet11 from groundstation0, satellite0's instrument1 takes an image of planet11 in image5, image of planet11 is taken with instrument1 on satellite0 in image6, satellite0 turns from planet11 to planet13, image of planet13 is taken with instrument1 on satellite0 in image5, instrument1 which is on satellite0 takes an image of planet13 in spectrograph2, satellite0 turns to star10 from planet13, instrument1 which is on satellite0 takes an image of star10 in image6, satellite0's instrument1 takes an image of star10 in spectrograph2, instrument1 that is on satellite0 is turned off, on satellite0, instrument2 is switched on, from star10, satellite0 turns to star4, calibration of instrument2 which is on satellite0 to star4 is complete, satellite0 turns from star4 to star16, instrument2 which is on satellite0 takes an image of star16 in image0 and instrument2 on satellite0 is switched off to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 303? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for star3 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation9 is complete, for groundstation0, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, image6 is compatible with instrument0, image6 is compatible with instrument1, instrument1 is calibrated for groundstation6, instrument4 is calibrated for groundstation8, instrument4 is on board satellite1, instrument4 supports image1, instrument4 supports infrared7, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has power, satellite1 has power, satellite1 is pointing to planet13, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: on satellite0, instrument1 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, satellite0 then reorients from groundstation0 to planet11, instrument1 on satellite0 captures an image of planet11 in image5, another image of planet11 is taken using instrument1 on satellite0 in image6, satellite0 reorients from planet11 to planet13, an image of planet13 is captured using instrument1 on satellite0 in image5, instrument1 on satellite0 takes an image of planet13 in spectrograph2, satellite0 reorients from planet13 to star10, instrument1 on satellite0 captures an image of star10 in image6, instrument1 on satellite0 also takes an image of star10 in spectrograph2, instrument1 on satellite0 is then deactivated, instrument2 on satellite0 is activated, satellite0 reorients from star10 to star4, instrument2 on satellite0 is calibrated to star4, satellite0 reorients from star4 to star16, instrument2 on satellite0 captures an image of star16 in image0, and finally, instrument2 on satellite0 is deactivated to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 303? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star3 has been finalized, instrument2's calibration for star4 is also complete, and instrument3's calibration for groundstation9 is finished. Groundstation0 has instrument1 calibrated, while groundstation7 has instrument0 calibrated. Satellite0 is currently directed towards groundstation2. Image0 is compatible for use with instrument2, and image1 is also compatible with instrument2. Additionally, image1 is supported by instrument3, and image5 is supported by instrument1. Image6 is compatible with both instrument0 and instrument1. Instrument1 has been calibrated for groundstation6, and instrument4 has been calibrated for groundstation8. Instrument4 is on board satellite1 and supports both image1 and infrared7. Satellite0 has instruments 0, 1, 2, and 3 on board and is currently operational. Satellite1 also has power and is directed towards planet13. Furthermore, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is compatible with instrument3."}
{"question_id": "356155b3-1941-4a85-bc6e-f6a5e9f26861", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on to reach the current state. In this state, is the number of inexecutable actions equal to 2060? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for star3 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation9 is complete, for groundstation0, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, image6 is compatible with instrument0, image6 is compatible with instrument1, instrument1 is calibrated for groundstation6, instrument4 is calibrated for groundstation8, instrument4 is on board satellite1, instrument4 supports image1, instrument4 supports infrared7, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has power, satellite1 has power, satellite1 is pointing to planet13, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are taken: instrument1 on satellite0 is activated to achieve the current state. In this state, does the number of inexecutable actions amount to 2060? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star3 has been finalized, instrument2's calibration for star4 is also complete, and instrument3's calibration for groundstation9 is finished. Groundstation0 has instrument1 calibrated, while groundstation7 has instrument0 calibrated. Satellite0 is currently directed towards groundstation2. Image0 is compatible for use with instrument2, and image1 is also compatible with instrument2. Additionally, image1 is supported by instrument3, and image5 is supported by instrument1. Image6 is compatible with both instrument0 and instrument1. Instrument1 has been calibrated for groundstation6, and instrument4 has been calibrated for groundstation8. Instrument4 is on board satellite1 and supports both image1 and infrared7. Satellite0 has instruments 0, 1, 2, and 3 on board, and it is currently powered. Satellite1 also has power and is directed towards planet13. Furthermore, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is compatible with instrument3."}
{"question_id": "4e3d412e-07ea-438d-be33-49d24e9065aa", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on, from groundstation2, satellite0 turns to groundstation0, instrument1 that is on satellite0 is calibrated to groundstation0, from groundstation0, satellite0 turns to planet11, image of planet11 is taken with instrument1 on satellite0 in image5, image of planet11 is taken with instrument1 on satellite0 in image6, satellite0 turns from planet11 to planet13, image of planet13 is taken with instrument1 on satellite0 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2, from planet13, satellite0 turns to star10, image of star10 is taken with instrument1 on satellite0 in image6, instrument1 which is on satellite0 takes an image of star10 in spectrograph2, instrument1 on satellite0 is switched off, instrument2 on satellite0 is switched on, satellite0 turns to star4 from star10, instrument2 is calibrated on satellite0 to star4, satellite0 turns to star16 from star4, satellite0's instrument2 takes an image of star16 in image0 and on satellite0, instrument2 is switched off to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 281? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for star3 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation9 is complete, for groundstation0, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, image6 is compatible with instrument0, image6 is compatible with instrument1, instrument1 is calibrated for groundstation6, instrument4 is calibrated for groundstation8, instrument4 is on board satellite1, instrument4 supports image1, instrument4 supports infrared7, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has power, satellite1 has power, satellite1 is pointing to planet13, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, then satellite0 reorients from groundstation0 to planet11, an image of planet11 is captured using instrument1 on satellite0 and stored in image5, another image of planet11 is captured using instrument1 on satellite0 and stored in image6, satellite0 reorients from planet11 to planet13, an image of planet13 is captured using instrument1 on satellite0 and stored in image5, instrument1 on satellite0 captures an image of planet13 in spectrograph2, satellite0 reorients from planet13 to star10, an image of star10 is captured using instrument1 on satellite0 and stored in image6, instrument1 on satellite0 captures an image of star10 in spectrograph2, instrument1 on satellite0 is deactivated, instrument2 on satellite0 is activated, satellite0 reorients from star10 to star4, instrument2 on satellite0 is calibrated to star4, satellite0 reorients from star4 to star16, instrument2 on satellite0 captures an image of star16 in image0, and finally, instrument2 on satellite0 is deactivated to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 281? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star3 has been finalized, instrument2's calibration for star4 is also complete, and instrument3's calibration for groundstation9 is finished. Groundstation0 has instrument1 calibrated, while groundstation7 has instrument0 calibrated. Satellite0 is currently directed towards groundstation2. Image0 is compatible for use with instrument2, and image1 is also compatible with instrument2. Additionally, image1 is supported by instrument3, and image5 is supported by instrument1. Image6 is compatible with both instrument0 and instrument1. Instrument1 has been calibrated for groundstation6, and instrument4 has been calibrated for groundstation8. Instrument4 is installed on satellite1 and supports both image1 and infrared7. Satellite0 has instruments 0, 1, 2, and 3 on board and is currently operational. Satellite1 also has power and is directed towards planet13. Furthermore, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is compatible with instrument3."}
{"question_id": "f3d606a2-46b4-49c2-8a93-0362aca47ebe", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, instrument0 on satellite0 is switched on, satellite1 turns from phenomenon10 to groundstation5, calibration of instrument3 which is on satellite1 to groundstation5 is complete, satellite1 turns from groundstation5 to phenomenon16, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, satellite1 turns to phenomenon17 from phenomenon16, instrument3 which is on satellite1 takes an image of phenomenon17 in image3, from phenomenon17, satellite1 turns to planet11 and instrument3 which is on satellite1 takes an image of planet11 in image3 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 29? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, calibration of instrument3 for star8 is complete, for star1, instrument0 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image2 is supported by instrument2, image2 is supported by instrument3, image3 is compatible with instrument0, image3 is compatible with instrument1, image3 is supported by instrument3, infrared1 is supported by instrument0, instrument1 is on board satellite0, instrument1 supports image0, instrument1 supports infrared1, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument2 supports image3, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 has instrument0 on board, satellite0 has instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 on satellite1 is activated, instrument0 on satellite0 is activated, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated with respect to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, instrument3 on satellite1 captures an image of phenomenon16 in image3, satellite1 reorients from phenomenon16 to phenomenon17, instrument3 on satellite1 captures an image of phenomenon17 in image3, and finally, satellite1 reorients from phenomenon17 to planet11, capturing an image of planet11 in image3, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 29? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is now complete, instrument3's calibration for star6 is also complete, and instrument3's calibration for star8 has been finished. For star1, instrument0 has been calibrated, and instrument0 has also been calibrated for star9. The satellite0 is currently directed towards groundstation3. Instrument2 and instrument3 both support image2, while image3 is compatible with instrument0 and instrument1, and is also supported by instrument3. Instrument0 supports infrared1, and instrument1, which is on board satellite0, supports both image0 and infrared1. Instrument2 has been calibrated for groundstation5 and groundstation7, and it supports image3. Instrument3 has been calibrated for groundstation5 and supports image0. Satellite0 has instrument0 and instrument2 on board, and it has power available. Satellite1 has instrument3 on board, has power, and is currently pointing towards phenomenon10."}
{"question_id": "31f01783-d5c9-4e81-96f8-f57350f32e96", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, instrument0 that is on satellite0 is turned on, from groundstation4, satellite1 turns to star6, instrument3 is calibrated on satellite1 to star6, satellite1 turns to planet14 from star6, image of planet14 is taken with instrument3 on satellite1 in spectrograph1, from planet14, satellite1 turns to star10, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, satellite1 turns from star10 to star12, satellite1's instrument3 takes an image of star12 in spectrograph1, satellite1 turns to star0 from star12, satellite0 turns to groundstation2 from star1, instrument0 that is on satellite0 is calibrated to groundstation2, satellite0 turns from groundstation2 to phenomenon15, satellite0's instrument0 takes an image of phenomenon15 in spectrograph0, from phenomenon15, satellite0 turns to star11, image of star11 is taken with instrument0 on satellite0 in thermograph4, satellite0 turns from star11 to star13 and satellite0's instrument0 takes an image of star13 in spectrograph0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 190? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument0 for groundstation4 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument3 for star6 is complete, for groundstation9, instrument2 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is supported by instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for star0, instrument0 is on board satellite0, instrument0 supports thermograph4, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph0, instrument2 is calibrated for groundstation4, instrument2 is calibrated for star7, instrument2 supports spectrograph2, instrument3 supports spectrograph1, power is available for satellite1, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has instrument2 on board, spectrograph0 is supported by instrument0, spectrograph0 is supported by instrument2, spectrograph1 is compatible with instrument1, spectrograph2 is supported by instrument3 and star1 is where satellite0 is pointed.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 rotates to face star6 from groundstation4, instrument3 on satellite1 is calibrated to star6, satellite1 then rotates to planet14 from star6, an image of planet14 is captured using instrument3 on satellite1 in spectrograph1, satellite1 rotates to star10 from planet14, an image of star10 is taken by instrument3 on satellite1 in spectrograph1, satellite1 then rotates to star12 from star10, an image of star12 is captured by instrument3 on satellite1 in spectrograph1, satellite1 rotates to star0 from star12, satellite0 rotates to groundstation2 from star1, instrument0 on satellite0 is calibrated to groundstation2, satellite0 then rotates to phenomenon15 from groundstation2, an image of phenomenon15 is captured by instrument0 on satellite0 in spectrograph0, satellite0 rotates to star11 from phenomenon15, an image of star11 is taken by instrument0 on satellite0 in thermograph4, and finally, satellite0 rotates to star13 from star11 and captures an image of star13 using instrument0 in spectrograph0, resulting in the current state. In this state, is the number of valid properties of the state that involve negations equal to 190? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 at groundstation4 has been finalized, instrument1's calibration at groundstation2 is also complete, and the calibration of instrument1 for star8 has been finished. Additionally, instrument3's calibration for star6 is complete. For groundstation9, the calibration of instrument2 is done. Satellite1 is currently directed at groundstation4. Instrument2 is capable of supporting infrared3. Furthermore, instrument0 has been calibrated for groundstation2 and star0, and it is installed on satellite0, providing support for thermograph4. Instrument1, also on board satellite0, has been calibrated for groundstation4 and supports spectrograph0. Instrument2 has been calibrated for groundstation4 and star7, and it supports spectrograph2. Instrument3 supports spectrograph1. Satellite1 has available power, and satellite0 also has power available. Satellite1 is equipped with instrument3 on board, and it also carries instrument2. Spectrograph0 is supported by both instrument0 and instrument2, while spectrograph1 is compatible with instrument1, and spectrograph2 is supported by instrument3. Satellite0 is currently pointed at star1."}
{"question_id": "58ab8d46-9b3a-4d50-939b-5dd1abf6556e", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, instrument0 that is on satellite0 is turned on, from groundstation4, satellite1 turns to star6, instrument3 that is on satellite1 is calibrated to star6, from star6, satellite1 turns to planet14, instrument3 which is on satellite1 takes an image of planet14 in spectrograph1, satellite1 turns to star10 from planet14, satellite1's instrument3 takes an image of star10 in spectrograph1, from star10, satellite1 turns to star12 and instrument3 which is on satellite1 takes an image of star12 in spectrograph1 to reach the current state. In this state, is the number of inexecutable actions equal to 1340? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for groundstation4 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument3 for star6 is complete, for groundstation9, instrument2 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is supported by instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for star0, instrument0 is on board satellite0, instrument0 supports thermograph4, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph0, instrument2 is calibrated for groundstation4, instrument2 is calibrated for star7, instrument2 supports spectrograph2, instrument3 supports spectrograph1, power is available for satellite1, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has instrument2 on board, spectrograph0 is supported by instrument0, spectrograph0 is supported by instrument2, spectrograph1 is compatible with instrument1, spectrograph2 is supported by instrument3 and star1 is where satellite0 is pointed.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is turned on, satellite1 reorients from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, then satellite1 reorients from star6 to planet14, and instrument3 on satellite1 captures an image of planet14 using spectrograph1. Next, satellite1 reorients from planet14 to star10, and instrument3 on satellite1 captures an image of star10 using spectrograph1. Finally, satellite1 reorients from star10 to star12, and instrument3 on satellite1 captures an image of star12 using spectrograph1, resulting in the current state. In this state, is the number of inexecutable actions equal to 1340? True or False", "initial_state_nl_paraphrased": "Calibration of instrument0 for groundstation4 has been finalized, instrument1's calibration for groundstation2 is also complete, the calibration process for instrument1 with respect to star8 has been completed, and instrument3's calibration for star6 is finished. For groundstation9, the calibration of instrument2 is done. Satellite1 is currently directed towards groundstation4. Instrument2 provides support for infrared3. Instrument0 has been calibrated for groundstation2 and star0, and it is installed on satellite0, providing support for thermograph4. Instrument1, also on board satellite0, has been calibrated for groundstation4 and supports spectrograph0. Instrument2 has been calibrated for groundstation4 and star7, and it supports spectrograph2. Instrument3 supports spectrograph1. Satellite1 has power available, and satellite0 also has power. Satellite1 is equipped with instrument3 on board and has instrument2 installed. Spectrograph0 is supported by both instrument0 and instrument2, while spectrograph1 is compatible with instrument1, and spectrograph2 is supported by instrument3. Satellite0 is currently pointed towards star1."}
{"question_id": "b3be9c07-da8e-4be0-9c89-85badcb0e23f", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite0 is turned on, from groundstation2, satellite0 turns to groundstation0, calibration of instrument1 which is on satellite0 to groundstation0 is complete, satellite0 turns to planet11 from groundstation0, satellite0's instrument1 takes an image of planet11 in image5, instrument1 which is on satellite0 takes an image of planet11 in image6, from planet11, satellite0 turns to planet13, instrument1 which is on satellite0 takes an image of planet13 in image5, instrument1 which is on satellite0 takes an image of planet13 in spectrograph2 and from planet13, satellite0 turns to star10 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 32? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star3 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation9 is complete, for groundstation0, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, image6 is compatible with instrument0, image6 is compatible with instrument1, instrument1 is calibrated for groundstation6, instrument4 is calibrated for groundstation8, instrument4 is on board satellite1, instrument4 supports image1, instrument4 supports infrared7, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has power, satellite1 has power, satellite1 is pointing to planet13, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated with respect to groundstation0, satellite0 then reorients from groundstation0 to planet11, and instrument1 on satellite0 captures images of planet11 in both image5 and image6. Next, satellite0 reorients from planet11 to planet13, and instrument1 on satellite0 captures images of planet13 in image5 and spectrograph2. Finally, satellite0 reorients from planet13 to star10, resulting in the current state. In this state, is the number of valid properties that do not involve negations equal to 32? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star3 has been finalized, instrument2's calibration for star4 is also complete, and instrument3's calibration for groundstation9 is finished. Groundstation0 has instrument1 calibrated, while groundstation7 has instrument0 calibrated. Satellite0 is currently directed towards groundstation2. Image0 is compatible for use with instrument2, and image1 is also compatible with instrument2. Additionally, image1 is supported by instrument3, and image5 is supported by instrument1. Image6 is compatible with both instrument0 and instrument1. Instrument1 has been calibrated for groundstation6, and instrument4 has been calibrated for groundstation8. Instrument4 is on board satellite1 and supports both image1 and infrared7. Satellite0 has instruments 0, 1, 2, and 3 on board, and it is currently powered. Satellite1 also has power and is directed towards planet13. Furthermore, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is compatible with instrument3."}
{"question_id": "b08a7d8c-0fca-4bc7-835b-a65e3398ebd6", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_11", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, on satellite0, instrument0 is switched on, satellite1 turns to groundstation5 from phenomenon10, instrument3 is calibrated on satellite1 to groundstation5, satellite1 turns from groundstation5 to phenomenon16, satellite1's instrument3 takes an image of phenomenon16 in image3, from phenomenon16, satellite1 turns to phenomenon17, instrument3 which is on satellite1 takes an image of phenomenon17 in image3, from phenomenon17, satellite1 turns to planet11, instrument3 which is on satellite1 takes an image of planet11 in image3, from planet11, satellite1 turns to planet13, satellite1's instrument3 takes an image of planet13 in image0, from planet13, satellite1 turns to planet14, instrument3 which is on satellite1 takes an image of planet14 in image0, satellite1 turns to star15 from planet14, image of star15 is taken with instrument3 on satellite1 in image2, satellite0 turns from groundstation3 to star1, calibration of instrument0 which is on satellite0 to star1 is complete and from star1, satellite0 turns to phenomenon10 to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 214? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, calibration of instrument3 for star8 is complete, for star1, instrument0 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image2 is supported by instrument2, image2 is supported by instrument3, image3 is compatible with instrument0, image3 is compatible with instrument1, image3 is supported by instrument3, infrared1 is supported by instrument0, instrument1 is on board satellite0, instrument1 supports image0, instrument1 supports infrared1, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument2 supports image3, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 has instrument0 on board, satellite0 has instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument3 is activated on satellite1, instrument0 is activated on satellite0, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, an image of phenomenon16 is captured by instrument3 on satellite1 and stored in image3, satellite1 reorients from phenomenon16 to phenomenon17, instrument3 on satellite1 captures an image of phenomenon17 and stores it in image3, satellite1 then reorients from phenomenon17 to planet11, an image of planet11 is taken by instrument3 on satellite1 and stored in image3, satellite1 reorients from planet11 to planet13, an image of planet13 is captured by instrument3 on satellite1 and stored in image0, satellite1 then reorients from planet13 to planet14, an image of planet14 is taken by instrument3 on satellite1 and stored in image0, satellite1 reorients from planet14 to star15, an image of star15 is captured by instrument3 on satellite1 and stored in image2, satellite0 reorients from groundstation3 to star1, instrument0 on satellite0 is calibrated to star1, and satellite0 then reorients from star1 to phenomenon10, resulting in the current state. In this state, is the total number of valid properties (including both affirmative and negated properties) equal to 214? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is now complete, and instrument3's calibration for both star6 and star8 is also complete. For star1 and star9, instrument0 has been calibrated. Satellite0 is currently directed towards groundstation3. Instrument2 and instrument3 both support image2, while instrument3 and instrument0 are compatible with image3, and instrument3 also supports it. Instrument0 supports infrared1. Instrument1 is installed on satellite0, and it supports both image0 and infrared1. Instrument2 has been calibrated for groundstations 5 and 7, and it supports image3. Instrument3 has been calibrated for groundstation5 and supports image0. Satellite0 is equipped with instruments 0, 1, and 2, and it has available power. Satellite1 has instrument3 on board, has power, and is currently pointing towards phenomenon10."}
{"question_id": "1fac5bcd-9bbc-44b2-a601-1fd89a47466c", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, instrument0 on satellite0 is switched on, satellite1 turns to star6 from groundstation4, instrument3 that is on satellite1 is calibrated to star6, from star6, satellite1 turns to planet14, satellite1's instrument3 takes an image of planet14 in spectrograph1, from planet14, satellite1 turns to star10, satellite1's instrument3 takes an image of star10 in spectrograph1, from star10, satellite1 turns to star12 and image of star12 is taken with instrument3 on satellite1 in spectrograph1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 31? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for groundstation4 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument3 for star6 is complete, for groundstation9, instrument2 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is supported by instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for star0, instrument0 is on board satellite0, instrument0 supports thermograph4, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph0, instrument2 is calibrated for groundstation4, instrument2 is calibrated for star7, instrument2 supports spectrograph2, instrument3 supports spectrograph1, power is available for satellite1, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has instrument2 on board, spectrograph0 is supported by instrument0, spectrograph0 is supported by instrument2, spectrograph1 is compatible with instrument1, spectrograph2 is supported by instrument3 and star1 is where satellite0 is pointed.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, instrument0 on satellite0 is activated, satellite1 reorients from groundstation4 to star6, instrument3 on satellite1 is calibrated to star6, then satellite1 reorients from star6 to planet14, captures an image of planet14 using spectrograph1 with instrument3, reorients from planet14 to star10, captures an image of star10 using spectrograph1 with instrument3, and finally reorients from star10 to star12, capturing an image of star12 using spectrograph1 with instrument3, resulting in the current state. In this state, is the count of valid properties that do not involve negations equal to 31? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 at groundstation4 has been finalized, instrument1's calibration at groundstation2 is also complete, and instrument1's calibration for star8 has been finished. Additionally, instrument3's calibration for star6 is complete. For groundstation9, the calibration of instrument2 is done. Satellite1 is currently directed towards groundstation4. Instrument2 provides support for infrared3. Instrument0 has been calibrated for groundstation2 and star0. Instrument0 is installed on satellite0 and supports thermograph4. Instrument1, also on satellite0, has been calibrated for groundstation4 and supports spectrograph0. Instrument2 has been calibrated for groundstation4 and star7, and it supports spectrograph2. Instrument3 supports spectrograph1. Satellite1 has power available, and satellite0 also has power. Satellite1 is equipped with instrument3 on board, and instrument2 is also on board satellite1. Both instrument0 and instrument2 support spectrograph0. Spectrograph1 is compatible with instrument1, and spectrograph2 is supported by instrument3. Satellite0 is currently pointed towards star1."}
{"question_id": "14f97f7e-6c5d-4f9f-90be-b47c8aa12282", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 198? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument0 for groundstation4 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument3 for star6 is complete, for groundstation9, instrument2 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is supported by instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for star0, instrument0 is on board satellite0, instrument0 supports thermograph4, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph0, instrument2 is calibrated for groundstation4, instrument2 is calibrated for star7, instrument2 supports spectrograph2, instrument3 supports spectrograph1, power is available for satellite1, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has instrument2 on board, spectrograph0 is supported by instrument0, spectrograph0 is supported by instrument2, spectrograph1 is compatible with instrument1, spectrograph2 is supported by instrument3 and star1 is where satellite0 is pointed.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, does the number of valid properties of the state involving negations equal 198? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 at groundstation4 has been finalized, instrument1's calibration at groundstation2 is also complete, and instrument1's calibration for star8 has been finished. Additionally, instrument3's calibration for star6 is complete. For groundstation9, the calibration of instrument2 is done. Satellite1 is currently directed at groundstation4. Instrument2 supports infrared3, and instrument0 has been calibrated for groundstation2 and star0. Instrument0 is installed on satellite0 and supports thermograph4. Instrument1, also on board satellite0, supports spectrograph0 and has been calibrated for groundstation4. Instrument2 has been calibrated for groundstation4 and star7, and it supports spectrograph2. Instrument3 supports spectrograph1. Satellite1 has power available, and satellite0 also has power. Satellite1 is equipped with instrument3 on board and has instrument2 installed. Spectrograph0 is supported by both instrument0 and instrument2, and spectrograph1 is compatible with instrument1. Spectrograph2 is supported by instrument3, and satellite0 is currently pointed at star1."}
{"question_id": "d395991d-9c1d-4b3a-9e00-cc7a1f5ff06e", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 32? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, calibration of instrument3 for star8 is complete, for star1, instrument0 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image2 is supported by instrument2, image2 is supported by instrument3, image3 is compatible with instrument0, image3 is compatible with instrument1, image3 is supported by instrument3, infrared1 is supported by instrument0, instrument1 is on board satellite0, instrument1 supports image0, instrument1 supports infrared1, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument2 supports image3, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 has instrument0 on board, satellite0 has instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: instrument3 on satellite1 is activated to achieve the current state. In this state, does the count of valid properties that do not include negations equal 32? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is now complete, and instrument3's calibration for both star6 and star8 is also complete. For star1 and star9, instrument0 has been calibrated. Satellite0 is currently directed towards groundstation3. Instrument2 and instrument3 both support image2, while instrument3 and instrument0 are compatible with image3, and instrument3 also supports it. Instrument0 supports infrared1. Instrument1 is installed on satellite0, and it supports both image0 and infrared1. Instrument2 has been calibrated for groundstations 5 and 7, and it supports image3. Instrument3 has been calibrated for groundstation5 and supports image0. Satellite0 is equipped with instruments 0, 1, and 2, and it has power available. Satellite1 has instrument3 on board, has power, and is currently pointing towards phenomenon10."}
{"question_id": "ef1c7564-d12d-468e-8ef3-64c5e0ee3766", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is switched on to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 127? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image2 is compatible with instrument0, image2 is compatible with instrument1, instrument1 supports image0, instrument1 supports image4, instrument1 supports image5, instrument1 supports spectrograph3, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is aimed towards star3 and spectrograph1 is supported by instrument1.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: on satellite1, instrument1 is activated to achieve the current state. In this state, does the count of valid state properties involving negations equal 127? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image2 is found to be compatible with both instrument0 and instrument1. Additionally, instrument1 is capable of supporting image0, image4, image5, and spectrograph3. The satellite0 is currently focused on phenomenon5. Satellite0 is equipped with instrument0 and has a power supply. Similarly, satellite1 is equipped with instrument1 and also has a power supply. Satellite1 is currently directed towards star3, and instrument1 provides support for spectrograph1."}
{"question_id": "e5804952-fd7c-4a40-b73e-b2922ac3a4ac", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite0 is switched on, satellite0 turns from groundstation2 to groundstation0, instrument1 that is on satellite0 is calibrated to groundstation0, satellite0 turns from groundstation0 to planet11, instrument1 which is on satellite0 takes an image of planet11 in image5, image of planet11 is taken with instrument1 on satellite0 in image6, satellite0 turns from planet11 to planet13, instrument1 which is on satellite0 takes an image of planet13 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2 and satellite0 turns to star10 from planet13 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 26? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star3 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation9 is complete, for groundstation0, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, image6 is compatible with instrument0, image6 is compatible with instrument1, instrument1 is calibrated for groundstation6, instrument4 is calibrated for groundstation8, instrument4 is on board satellite1, instrument4 supports image1, instrument4 supports infrared7, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has power, satellite1 has power, satellite1 is pointing to planet13, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: instrument1 on satellite0 is activated, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, satellite0 then reorients from groundstation0 to planet11, instrument1 on satellite0 captures an image of planet11 in image5, another image of planet11 is taken using instrument1 on satellite0 in image6, satellite0 reorients from planet11 to planet13, instrument1 on satellite0 captures an image of planet13 in image5, satellite0's instrument1 takes a spectrograph of planet13 in spectrograph2, and finally, satellite0 reorients from planet13 to star10 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 26? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star3 has been finalized, instrument2's calibration for star4 is also complete, and instrument3's calibration for groundstation9 is finished. Groundstation0 has instrument1 calibrated, while groundstation7 has instrument0 calibrated. Satellite0 is currently directed towards groundstation2. Image0 is compatible for use with instrument2, and image1 is also compatible with instrument2. Additionally, image1 is supported by instrument3, and image5 is supported by instrument1. Image6 is compatible with both instrument0 and instrument1. Instrument1 has been calibrated for groundstation6, and instrument4 has been calibrated for groundstation8. Instrument4 is installed on satellite1 and supports both image1 and infrared7. Satellite0 is equipped with instruments 0, 1, 2, and 3 on board and has power. Satellite1 also has power and is currently pointing towards planet13. Furthermore, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is compatible with instrument3."}
{"question_id": "9adb766e-53c6-4b4a-8f99-2c2d2a92e68f", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 that is on satellite1 is turned on to reach the current state. Is it True or False that the number of actions that led to current state in the sequence is equal to 2?", "answer": "False", "plan_length": 1, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image2 is compatible with instrument0, image2 is compatible with instrument1, instrument1 supports image0, instrument1 supports image4, instrument1 supports image5, instrument1 supports spectrograph3, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is aimed towards star3 and spectrograph1 is supported by instrument1.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Considering the initial condition, the following actions are taken: instrument1 on satellite1 is activated to achieve the current state. Is it True or False that the number of actions in the sequence that resulted in the current state is 2?", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image2 is found to be compatible with both instrument0 and instrument1. Additionally, instrument1 is capable of supporting image0, image4, image5, and spectrograph3. The satellite0 is currently focused on phenomenon5. Satellite0 is equipped with instrument0 and has a power supply. Similarly, satellite1 is equipped with instrument1 and also has a power supply. Satellite1 is currently directed towards star3, and instrument1 provides support for spectrograph1."}
{"question_id": "fee7208e-a18a-405d-a840-b7dbc701ed0d", "domain_name": "satellite", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite0, instrument1 is switched on, satellite0 turns to groundstation0 from groundstation2, instrument1 that is on satellite0 is calibrated to groundstation0, from groundstation0, satellite0 turns to planet11, image of planet11 is taken with instrument1 on satellite0 in image5, satellite0's instrument1 takes an image of planet11 in image6, satellite0 turns to planet13 from planet11, satellite0's instrument1 takes an image of planet13 in image5, image of planet13 is taken with instrument1 on satellite0 in spectrograph2 and satellite0 turns from planet13 to star10 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 1684? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for star3 is complete, calibration of instrument2 for star4 is complete, calibration of instrument3 for groundstation9 is complete, for groundstation0, instrument1 is calibrated, for groundstation7, instrument0 is calibrated, groundstation2 is where satellite0 is pointed, image0 is compatible with instrument2, image1 is compatible with instrument2, image1 is supported by instrument3, image5 is supported by instrument1, image6 is compatible with instrument0, image6 is compatible with instrument1, instrument1 is calibrated for groundstation6, instrument4 is calibrated for groundstation8, instrument4 is on board satellite1, instrument4 supports image1, instrument4 supports infrared7, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has instrument0 on board, satellite0 has power, satellite1 has power, satellite1 is pointing to planet13, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3 and thermograph3 is compatible with instrument3.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 is activated on satellite0, satellite0 reorients from groundstation2 to groundstation0, instrument1 on satellite0 is calibrated to groundstation0, then satellite0 reorients from groundstation0 to planet11, an image of planet11 is captured using instrument1 on satellite0 and stored in image5, another image of planet11 is taken by instrument1 on satellite0 and stored in image6, satellite0 then reorients from planet11 to planet13, an image of planet13 is captured using instrument1 on satellite0 and stored in image5, and an image of planet13 is taken by instrument1 on satellite0 and stored in spectrograph2, finally satellite0 reorients from planet13 to star10 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 1684? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 with respect to star3 has been finalized, instrument2's calibration for star4 is also complete, and instrument3's calibration for groundstation9 is finished. Groundstation0 has instrument1 calibrated, while groundstation7 has instrument0 calibrated. Satellite0 is currently directed towards groundstation2. Image0 is compatible for use with instrument2, and image1 is also compatible with instrument2. Additionally, image1 is supported by instrument3, and image5 is supported by instrument1. Image6 is compatible with both instrument0 and instrument1. Instrument1 has been calibrated for groundstation6, and instrument4 has been calibrated for groundstation8. Instrument4 is on board satellite1 and supports both image1 and infrared7. Satellite0 has instruments 0, 1, 2, and 3 on board, and it is currently operational with power. Satellite1 also has power and is directed towards planet13. Furthermore, spectrograph2 is compatible with instrument1, spectrograph4 is compatible with instrument3, and thermograph3 is compatible with instrument3."}
{"question_id": "793afb8f-333d-4181-9851-ccdd6727bbb1", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_12", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on, instrument0 that is on satellite0 is turned on, satellite1 turns to star6 from groundstation4, instrument3 is calibrated on satellite1 to star6, satellite1 turns to planet14 from star6, image of planet14 is taken with instrument3 on satellite1 in spectrograph1, satellite1 turns to star10 from planet14, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, from star10, satellite1 turns to star12 and satellite1's instrument3 takes an image of star12 in spectrograph1 to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 222? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for groundstation4 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument3 for star6 is complete, for groundstation9, instrument2 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is supported by instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for star0, instrument0 is on board satellite0, instrument0 supports thermograph4, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph0, instrument2 is calibrated for groundstation4, instrument2 is calibrated for star7, instrument2 supports spectrograph2, instrument3 supports spectrograph1, power is available for satellite1, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has instrument2 on board, spectrograph0 is supported by instrument0, spectrograph0 is supported by instrument2, spectrograph1 is compatible with instrument1, spectrograph2 is supported by instrument3 and star1 is where satellite0 is pointed.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 on satellite1 is activated, and instrument0 on satellite0 is also activated. Satellite1 then reorients from groundstation4 to star6, followed by the calibration of instrument3 on satellite1 to star6. Next, satellite1 reorients from star6 to planet14, and an image of planet14 is captured using instrument3 on satellite1 in spectrograph1. Satellite1 then reorients from planet14 to star10, and instrument3 on satellite1 captures an image of star10 in spectrograph1. Finally, satellite1 reorients from star10 to star12, and instrument3 on satellite1 captures an image of star12 in spectrograph1, resulting in the current state. In this state, is the total number of valid properties (including both affirmative and negated properties) equal to 222? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 at groundstation4 has been finalized, instrument1's calibration at groundstation2 is also complete, and instrument1's calibration for star8 has been finished. Additionally, instrument3's calibration for star6 is complete. For groundstation9, the calibration of instrument2 is confirmed. Satellite1 is currently directed towards groundstation4. Instrument2 is capable of supporting infrared3. Furthermore, instrument0 has been calibrated for groundstation2 and star0, and it is installed on satellite0, providing support for thermograph4. Instrument1, also on board satellite0, has been calibrated for groundstation4 and supports spectrograph0. Instrument2 has been calibrated for groundstation4 and star7, and it supports spectrograph2. Instrument3 supports spectrograph1. Satellite1 has power available, and satellite0 also has power available. Satellite1 is equipped with instrument3 on board, and instrument2 is also on board satellite1. Spectrograph0 is supported by both instrument0 and instrument2, while spectrograph1 is compatible with instrument1, and spectrograph2 is supported by instrument3. Satellite0 is currently pointed towards star1."}
{"question_id": "c1ac95c7-12f4-40e9-b79c-6d9191cd4849", "domain_name": "satellite", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_11", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: on satellite1, instrument3 is switched on, instrument0 that is on satellite0 is turned on, satellite1 turns to groundstation5 from phenomenon10, calibration of instrument3 which is on satellite1 to groundstation5 is complete, satellite1 turns to phenomenon16 from groundstation5, satellite1's instrument3 takes an image of phenomenon16 in image3, from phenomenon16, satellite1 turns to phenomenon17, image of phenomenon17 is taken with instrument3 on satellite1 in image3, satellite1 turns from phenomenon17 to planet11 and image of planet11 is taken with instrument3 on satellite1 in image3 to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 214? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Calibration of instrument1 for groundstation0 is complete, calibration of instrument2 for star9 is complete, calibration of instrument3 for star6 is complete, calibration of instrument3 for star8 is complete, for star1, instrument0 is calibrated, for star9, instrument0 is calibrated, groundstation3 is where satellite0 is pointed, image2 is supported by instrument2, image2 is supported by instrument3, image3 is compatible with instrument0, image3 is compatible with instrument1, image3 is supported by instrument3, infrared1 is supported by instrument0, instrument1 is on board satellite0, instrument1 supports image0, instrument1 supports infrared1, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, instrument2 supports image3, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite0 has instrument0 on board, satellite0 has instrument2 on board, satellite0 has power available, satellite1 has instrument3 on board, satellite1 has power and satellite1 is pointing to phenomenon10.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following sequence of actions occurs: instrument3 is activated on satellite1, instrument0 on satellite0 is turned on, satellite1 reorients from phenomenon10 to groundstation5, instrument3 on satellite1 is calibrated to groundstation5, satellite1 then reorients from groundstation5 to phenomenon16, and instrument3 on satellite1 captures an image of phenomenon16 in image3. Next, satellite1 reorients from phenomenon16 to phenomenon17, and instrument3 on satellite1 captures an image of phenomenon17 in image3. Finally, satellite1 reorients from phenomenon17 to planet11, and instrument3 on satellite1 captures an image of planet11 in image3, resulting in the current state. In this state, is the total number of valid properties (including both affirmative and negated properties) equal to 214? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument1 at groundstation0 has been finalized, instrument2's calibration for star9 is now complete, and instrument3's calibration for both star6 and star8 is also complete. For star1 and star9, instrument0 has been calibrated. Satellite0 is currently directed towards groundstation3. Instrument2 and instrument3 both support image2, while image3 is compatible with instrument0 and instrument1, and is also supported by instrument3. Instrument0 supports infrared1. Instrument1, which is on board satellite0, supports both image0 and infrared1. Instrument2 has been calibrated for groundstations 5 and 7, and supports image3. Instrument3, calibrated for groundstation5, supports image0. Satellite0 has instruments 0, 1, and 2 on board, and has available power. Satellite1, which has instrument3 on board, has power and is currently pointing towards phenomenon10."}
{"question_id": "e0126212-67fa-48d7-b676-35722f0a2982", "domain_name": "satellite", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument1 on satellite1 is switched on, from star3, satellite1 turns to star1, instrument1 that is on satellite1 is calibrated to star1, satellite1 turns from star1 to phenomenon10, satellite1's instrument1 takes an image of phenomenon10 in image5, satellite1's instrument1 takes an image of phenomenon10 in spectrograph3, satellite1 turns from phenomenon10 to phenomenon11, instrument1 which is on satellite1 takes an image of phenomenon11 in spectrograph1, satellite1 turns to phenomenon5 from phenomenon11, instrument1 which is on satellite1 takes an image of phenomenon5 in image4, image of phenomenon5 is taken with instrument1 on satellite1 in image5, satellite1 turns from phenomenon5 to phenomenon7, instrument1 which is on satellite1 takes an image of phenomenon7 in image0, image of phenomenon7 is taken with instrument1 on satellite1 in image4, satellite1 turns to phenomenon9 from phenomenon7, instrument1 which is on satellite1 takes an image of phenomenon9 in image5, instrument1 which is on satellite1 takes an image of phenomenon9 in spectrograph1, from phenomenon9, satellite1 turns to planet8 and image of planet8 is taken with instrument1 on satellite1 in image5 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 116? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Calibration of instrument1 for star1 is complete, for star1, instrument0 is calibrated, image2 is compatible with instrument0, image2 is compatible with instrument1, instrument1 supports image0, instrument1 supports image4, instrument1 supports image5, instrument1 supports spectrograph3, phenomenon5 is where satellite0 is pointed, satellite0 carries instrument0 on board, satellite0 has power, satellite1 carries instrument1 on board, satellite1 has power, satellite1 is aimed towards star3 and spectrograph1 is supported by instrument1.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument1 on satellite1 is activated, satellite1 rotates from star3 to star1, instrument1 on satellite1 is calibrated to star1, then satellite1 turns from star1 to phenomenon10, and captures an image of phenomenon10 using image5 and spectrograph3 with instrument1, next satellite1 turns from phenomenon10 to phenomenon11, and instrument1 on satellite1 captures an image of phenomenon11 using spectrograph1, satellite1 then turns from phenomenon11 to phenomenon5, and instrument1 on satellite1 captures an image of phenomenon5 using image4 and image5, subsequently, satellite1 turns from phenomenon5 to phenomenon7, and instrument1 on satellite1 captures an image of phenomenon7 using image0 and image4, then satellite1 turns from phenomenon7 to phenomenon9, and instrument1 on satellite1 captures an image of phenomenon9 using image5 and spectrograph1, finally, satellite1 turns from phenomenon9 to planet8, and captures an image of planet8 using instrument1 in image5, resulting in the current state. In this state, is the number of valid properties of the state that involve negations equal to 116? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument1 with respect to star1 has been completed. For star1, instrument0 has already been calibrated. Image2 is found to be compatible with both instrument0 and instrument1. Additionally, instrument1 is capable of supporting image0, image4, image5, and spectrograph3. The satellite0 is currently focused on phenomenon5. Satellite0 is equipped with instrument0 and has a power supply. Similarly, satellite1 is equipped with instrument1 and also has a power supply. Satellite1 is currently directed towards star3, and instrument1 provides support for spectrograph1."}
{"question_id": "f2bfe884-12aa-40cd-a975-482ecfc0156b", "domain_name": "satellite", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on, on satellite0, instrument0 is switched on, from groundstation4, satellite1 turns to star6, instrument3 is calibrated on satellite1 to star6, satellite1 turns from star6 to planet14, instrument3 which is on satellite1 takes an image of planet14 in spectrograph1, from planet14, satellite1 turns to star10, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, satellite1 turns from star10 to star12 and instrument3 which is on satellite1 takes an image of star12 in spectrograph1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 34? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Calibration of instrument0 for groundstation4 is complete, calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, calibration of instrument3 for star6 is complete, for groundstation9, instrument2 is calibrated, groundstation4 is where satellite1 is pointed, infrared3 is supported by instrument2, instrument0 is calibrated for groundstation2, instrument0 is calibrated for star0, instrument0 is on board satellite0, instrument0 supports thermograph4, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph0, instrument2 is calibrated for groundstation4, instrument2 is calibrated for star7, instrument2 supports spectrograph2, instrument3 supports spectrograph1, power is available for satellite1, satellite0 has power available, satellite1 carries instrument3 on board, satellite1 has instrument2 on board, spectrograph0 is supported by instrument0, spectrograph0 is supported by instrument2, spectrograph1 is compatible with instrument1, spectrograph2 is supported by instrument3 and star1 is where satellite0 is pointed.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: instrument3 is activated on satellite1, instrument0 is activated on satellite0, satellite1 is directed towards star6 from groundstation4, instrument3 on satellite1 is calibrated to star6, then satellite1 is redirected from star6 to planet14, and instrument3 on satellite1 captures an image of planet14 using spectrograph1, next satellite1 is redirected from planet14 to star10, and instrument3 on satellite1 captures an image of star10 using spectrograph1, finally satellite1 is redirected from star10 to star12, and instrument3 on satellite1 captures an image of star12 using spectrograph1, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 34? True or False", "initial_state_nl_paraphrased": "The calibration process for instrument0 at groundstation4 has been finalized, instrument1's calibration at groundstation2 is also complete, and instrument1's calibration for star8 has been finished. Additionally, instrument3's calibration for star6 is complete. For groundstation9, the calibration of instrument2 is done. Satellite1 is currently directed towards groundstation4. Instrument2 supports infrared3, and instrument0 has been calibrated for groundstation2 and star0. Instrument0 is installed on satellite0 and supports thermograph4. Instrument1, also on board satellite0, supports spectrograph0 and has been calibrated for groundstation4. Instrument2 has been calibrated for groundstation4 and star7, and it supports spectrograph2. Instrument3 supports spectrograph1. Satellite1 has power available, and satellite0 also has power. Satellite1 is equipped with instrument3 on board and has instrument2 installed. Spectrograph0 is supported by both instrument0 and instrument2, and spectrograph1 is compatible with instrument1. Spectrograph2 is supported by instrument3, and satellite0 is currently pointed towards star1."}
