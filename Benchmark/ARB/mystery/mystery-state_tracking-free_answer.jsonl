{"question_id": "84fb9249-28c0-4945-bcbf-8fd24d230c1c", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1 and cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "cargo c0 is not at location l0cargo c0 is not present at location l0, cargo c0 is not in vehicle v0, cargo c0 is not located in vehicle v1, cargo c1 is not at location l0cargo c1 is not present at location l0, cargo c1 is not situated at location l1, cargo c2 is not in vehicle v1, cargo c2 is not located in vehicle v0, cargo c2 is not situated at location l1, cargo c3 is not at location l1cargo c3 is not present at location l1, cargo c3 is not located in vehicle v0, cargo c4 is not in vehicle v1, cargo c4 is not situated at location l0, cargo c5 is not at location l0cargo c5 is not present at location l0, cargo c5 is not in vehicle v0, cargo c6 is not at location l1cargo c6 is not present at location l1, cargo c6 is not located in vehicle v1, cargo c7 is not at location l1cargo c7 is not present at location l1, cargo c7 is not located in vehicle v1, cargo c8 is not in vehicle v1, cargo c8 is not situated at location l0, cargo c9 is not located in vehicle v1, cargo c9 is not situated at location l0, fuel f4 does not exist in location l1, fuel f5 does not exist in location l1, fuel f7 does not exist in location l1, fuel level f0 does not neighbour fuel level f2, fuel level f0 does not neighbour fuel level f8, fuel level f1 does not neighbour fuel level f3, fuel level f1 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f6, fuel level f3 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f8, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f6, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f7, fuel level f6 does not neighbour fuel level f1, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f0, fuel level f8 does not neighbour fuel level f2, fuel level f8 does not neighbour fuel level f4, fuel level f8 does not neighbour fuel level f5, fuel level f8 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f7, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f4 are not neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f0 and f6 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f1 and f8 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f2 and f8 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f4 and f8 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f4 are not neighbors, fuel-levels f5 and f8 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f6 and f8 are not neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f3 are not neighbors, fuel-levels f7 and f5 are not neighbors, fuel-levels f8 and f1 are not neighbors, fuel-levels f8 and f3 are not neighbors, location l0 does not have a fuel-level of f3, location l0 does not have a fuel-level of f5, location l0 does not have a fuel-level of f6, location l0 does not have a fuel-level of f7, location l0 does not have fuel f0, location l0 does not have fuel f1, location l0 does not have fuel f4, location l0 does not have fuel f8, location l1 does not have a fuel-level of f1, location l1 does not have a fuel-level of f2, location l1 does not have a fuel-level of f3, location l1 does not have a fuel-level of f8, location l1 does not have fuel f0, space s1 does not neighbour space s0, vehicle v0 does not contain cargo c4, vehicle v0 does not contain cargo c6, vehicle v0 does not contain cargo c7, vehicle v0 does not contain cargo c8, vehicle v0 does not contain cargo c9, vehicle v0 does not have space s1, vehicle v0 is not at location l1, vehicle v1 does not contain cargo c1, vehicle v1 does not contain cargo c3, vehicle v1 does not contain cargo c5, vehicle v1 does not contain space s0 and vehicle v1 is not situated at location l1", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l0, cargo c2 is present at location l1, cargo c3 is situated at location l1, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 and location l1 are connected, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, which has fuel levels f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1. Subsequently, cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0, at location l0. Next, vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1. Then, cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, which has fuel levels f7 and f6, to location l0. Finally, at location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, resulting in the current state. In this state, list all valid properties that involve negations. If none exist, write None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also at l0, while cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, and cargo c6 and c7 are at l0. Fuel f3 is found at location l0, and fuel levels f1 and f2 are adjacent, as are f4 and f5, and f7 and f8. Additionally, fuel levels f0 and f1, f2 and f3, f3 and f4, f5 and f6, and f6 and f7 are all neighboring. Locations l0 and l1 are connected, with l1 having fuel f8. Spaces s0 and s1 are adjacent, and there is a connection between l0 and l1. Vehicle v0 is at location l1 and has space s1, while vehicle v1 is at location l0 and also has space s1."}
{"question_id": "93551c30-a3fb-4e96-b063-e1e9737207ae", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, cargo c0 is loaded in vehicle v0 with space s2 and space s1 at location l0, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c10 is loaded in vehicle v0 with spaces s1 and s0, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0, at location l0, cargo c3 is loaded in vehicle v0 with spaces s2 and s1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, cargo c4 is unloaded from vehicle v0 with space s1 and space s2 at location l1, cargo c5 is loaded in vehicle v0 with space s2 and space s1 at location l1, at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f1 and f0 to location l0, cargo c5 is unloaded from vehicle v0 with space s0 and space s1 at location l0 and at location l0, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "cargo c0 is not at location l0cargo c0 is not present at location l0, cargo c0 is not in vehicle v1, cargo c0 is not located in vehicle v0, cargo c1 is not located in vehicle v1, cargo c1 is not situated at location l0, cargo c10 is not at location l1cargo c10 is not present at location l1, cargo c10 is not in vehicle v0, cargo c10 is not in vehicle v1, cargo c2 is not at location l0cargo c2 is not present at location l0, cargo c3 is not at location l0cargo c3 is not present at location l0, cargo c3 is not in vehicle v0, cargo c3 is not located in vehicle v1, cargo c4 is not in vehicle v0, cargo c4 is not in vehicle v1, cargo c4 is not situated at location l0, cargo c5 is not located in vehicle v1, cargo c5 is not situated at location l1, cargo c6 is not at location l0cargo c6 is not present at location l0, cargo c6 is not situated at location l1, cargo c7 is not at location l1cargo c7 is not present at location l1, cargo c7 is not in vehicle v1, cargo c7 is not located in vehicle v0, cargo c8 is not located in vehicle v0, cargo c8 is not situated at location l0, cargo c9 is not at location l1cargo c9 is not present at location l1, cargo c9 is not in vehicle v1, cargo c9 is not situated at location l0, fuel f3 does not exist in location l1, fuel f5 does not exist in location l0, fuel level f0 does not neighbour fuel level f2, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f3, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f0, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f2, fuel level f5 does not neighbour fuel level f4, fuel-levels f0 and f3 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f3 are not neighbors, location l0 does not have a fuel-level of f0, location l0 does not have a fuel-level of f1, location l0 does not have fuel f3, location l0 does not have fuel f4, location l1 does not have a fuel-level of f1, location l1 does not have a fuel-level of f2, location l1 does not have a fuel-level of f5, location l1 does not have fuel f4, space s0 does not neighbour space s2, space s1 does not neighbour space s0, space s2 does not neighbour space s0, space s2 does not neighbour space s1, vehicle v0 does not contain cargo c1, vehicle v0 does not contain cargo c2, vehicle v0 does not contain cargo c5, vehicle v0 does not have space s1, vehicle v0 does not have space s2, vehicle v0 is not situated at location l1, vehicle v1 does not contain cargo c2, vehicle v1 does not contain cargo c6, vehicle v1 does not contain cargo c8, vehicle v1 does not contain space s0, vehicle v1 does not contain space s1 and vehicle v1 is not present at location l0", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is at location l0, cargo c3 is at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is situated at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f4, location l0 is connected to location l1, location l1 has fuel f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 contains space s2, vehicle v0 is situated at location l1, vehicle v1 has space s2 and vehicle v1 is at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0, where cargo c0 is loaded onto vehicle v0, occupying spaces s2 and s1, and cargo c2 is also loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f4 and f3, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1, and cargo c10 is loaded onto vehicle v0, occupying spaces s1 and s0. Additionally, cargo c2 is unloaded from vehicle v0, freeing up spaces s0 and s1. Vehicle v0 then moves back to location l0, which has fuel levels f2 and f1. At location l0, cargo c10 is unloaded from vehicle v0, freeing up spaces s1 and s2, and cargo c3 and c4 are loaded onto vehicle v0, occupying spaces s2 and s1, and spaces s1 and s0, respectively. Vehicle v0 then moves to location l1, which has fuel levels f3 and f2. At location l1, cargo c3 and c4 are unloaded from vehicle v0, freeing up spaces s0 and s1, and spaces s1 and s2, respectively. Cargo c5 and c9 are then loaded onto vehicle v0, occupying spaces s2 and s1, and spaces s1 and s0, respectively. Finally, vehicle v0 moves back to location l0, which has fuel levels f1 and f0. At location l0, cargo c5 is unloaded from vehicle v0, freeing up spaces s0 and s1, and cargo c6 is loaded onto vehicle v0, occupying spaces s1 and s0, resulting in the current state. In this state, list all valid properties that involve negations. If none exist, write None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, and c4 are all at l0. Cargo c5 is at l1, whereas cargo c6 and c7 are at l0. Cargo c8 is located at l1, and cargo c9 is also present at l1. \n\nFuel level f0 is adjacent to fuel level f1. Fuel level f3 is adjacent to fuel level f4. Additionally, fuel levels f1 and f2 are neighboring, as are fuel levels f2 and f3, and fuel levels f4 and f5. \n\nLocation l0 is equipped with fuel f4 and is connected to location l1. Conversely, location l1 has fuel f3 and is connected to location l0. Space s0 is adjacent to space s1, and spaces s1 and s2 are also neighboring. Vehicle v0 contains space s2 and is situated at location l1. Similarly, vehicle v1 also contains space s2 and is located at l1."}
{"question_id": "64506286-ee50-4f99-90a6-1f4af6f483d6", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f1, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c5 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f1 and f0 to location l1, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1 and at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "cargo c0 is at location l1, cargo c0 is not at location l0cargo c0 is not present at location l0, cargo c0 is not in vehicle v0, cargo c0 is not in vehicle v1, cargo c1 is at location l1, cargo c1 is not at location l0cargo c1 is not present at location l0, cargo c1 is not in vehicle v1, cargo c2 is not situated at location l1, cargo c2 is present at location l0, cargo c3 is not at location l1cargo c3 is not present at location l1, cargo c3 is situated at location l0, cargo c4 is not at location l0cargo c4 is not present at location l0, cargo c4 is not in vehicle v1, cargo c4 is situated at location l1, cargo c5 is not in vehicle v0, cargo c5 is not situated at location l1, cargo c5 is present at location l0, cargo c6 is at location l0, cargo c6 is not at location l1cargo c6 is not present at location l1, cargo c7 is at location l1, cargo c7 is not at location l0cargo c7 is not present at location l0, cargo c7 is not located in vehicle v0, cargo c8 is not at location l0cargo c8 is not present at location l0, cargo c8 is not in vehicle v1, cargo c8 is not located in vehicle v0, cargo c8 is situated at location l1, cargo c9 is in vehicle v0, cargo c9 is not at location l0cargo c9 is not present at location l0, cargo c9 is not at location l1cargo c9 is not present at location l1, cargo c9 is not in vehicle v1, fuel f1 does not exist in location l1, fuel f2 does not exist in location l0, fuel f2 does not exist in location l1, fuel f5 does not exist in location l0, fuel f6 does not exist in location l1, fuel f7 does not exist in location l1, fuel level f0 does not neighbour fuel level f7, fuel level f0 neighbors fuel level f1, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f3, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f8, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f6, fuel level f3 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f8, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f7, fuel level f4 does not neighbour fuel level f8, fuel level f4 neighbors fuel level f5, fuel level f5 does not neighbour fuel level f4, fuel level f5 does not neighbour fuel level f8, fuel level f5 neighbors fuel level f6, fuel level f6 does not neighbour fuel level f0, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f5, fuel level f6 does not neighbour fuel level f8, fuel level f7 does not neighbour fuel level f0, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f5, fuel level f7 does not neighbour fuel level f6, fuel level f7 neighbors fuel level f8, fuel level f8 does not neighbour fuel level f0, fuel level f8 does not neighbour fuel level f1, fuel level f8 does not neighbour fuel level f2, fuel level f8 does not neighbour fuel level f5, fuel level f8 does not neighbour fuel level f6, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f4 are not neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f0 and f6 are not neighbors, fuel-levels f0 and f8 are not neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f1 and f8 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f2 and f7 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f7 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f3 are not neighbors, fuel-levels f8 and f3 are not neighbors, fuel-levels f8 and f4 are not neighbors, fuel-levels f8 and f7 are not neighbors, location l0 does not have a fuel-level of f4, location l0 does not have a fuel-level of f7, location l0 does not have a fuel-level of f8, location l0 does not have fuel f1, location l0 does not have fuel f3, location l0 does not have fuel f6, location l0 has fuel f0, location l1 does not have a fuel-level of f0, location l1 does not have a fuel-level of f4, location l1 does not have a fuel-level of f8, location l1 does not have fuel f3, location l1 has a fuel-level of f5, location l1 is connected to location l0, spaces s0 and s1 are neighbors, spaces s1 and s0 are not neighbors, there is a connection between locations l0 and l1, vehicle v0 contains space s0, vehicle v0 does not contain cargo c1, vehicle v0 does not contain cargo c2, vehicle v0 does not contain cargo c3, vehicle v0 does not contain cargo c4, vehicle v0 does not contain cargo c6, vehicle v0 does not contain space s1, vehicle v0 is not at location l0, vehicle v0 is present at location l1, vehicle v1 does not contain cargo c2, vehicle v1 does not contain cargo c3, vehicle v1 does not contain cargo c5, vehicle v1 does not contain cargo c6, vehicle v1 does not contain cargo c7, vehicle v1 does not contain space s0, vehicle v1 has space s1, vehicle v1 is not at location l1 and vehicle v1 is present at location l0", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l0, cargo c2 is present at location l1, cargo c3 is situated at location l1, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 and location l1 are connected, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, with fuel levels f8 and f7, to location l0. Upon arrival, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1, at location l0. Subsequently, cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0, at location l0. Vehicle v0 then moves from location l0, with fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1. Next, cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, with fuel levels f7 and f6, to location l0. Upon arrival, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, at location l0. At location l0, cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, with fuel levels f2 and f1, to location l1. At location l1, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1. Next, cargo c5 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, with fuel levels f6 and f5, to location l0. Upon arrival, cargo c5 is unloaded from vehicle v0, which has spaces s0 and s1, at location l0. At location l0, cargo c7 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, with fuel levels f1 and f0, to location l1. At location l1, cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1. Finally, cargo c9 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1, resulting in the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also at l0, while cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, and cargo c6 and c7 are at l0. Fuel f3 is found at location l0, and fuel levels f1 and f2 are adjacent, as are f4 and f5, and f7 and f8. Additionally, fuel levels f0 and f1, f2 and f3, f3 and f4, f5 and f6, and f6 and f7 are all neighboring. Locations l0 and l1 are connected, with l1 having fuel f8. Spaces s0 and s1 are adjacent, and there is a connection between l0 and l1. Vehicle v0 is at location l1 and has space s1, while vehicle v1 is at location l0 and also has space s1."}
{"question_id": "db905769-3c88-4014-8adf-dc6cf8a24948", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1 and vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "cargo c0 is not at location l1cargo c0 is not present at location l1, cargo c1 is not at location l1cargo c1 is not present at location l1, cargo c1 is not located in vehicle v0, cargo c2 is not situated at location l1, cargo c3 is not situated at location l1, cargo c4 is not in vehicle v0, cargo c4 is not situated at location l1, cargo c5 is not located in vehicle v1, cargo c5 is not situated at location l1, cargo c6 is not situated at location l0, cargo c6 is not situated at location l1, cargo c7 is not at location l0cargo c7 is not present at location l0, cargo c7 is not in vehicle v0, cargo c7 is not in vehicle v1, cargo c8 is not at location l1cargo c8 is not present at location l1, cargo c8 is not located in vehicle v1, fuel f1 does not exist in location l0, fuel f1 does not exist in location l1, fuel f3 does not exist in location l1, fuel f7 does not exist in location l0, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f6, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f6, fuel level f3 does not neighbour fuel level f7, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f3, fuel level f4 does not neighbour fuel level f7, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f1, fuel level f5 does not neighbour fuel level f4, fuel level f6 does not neighbour fuel level f0, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f5, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f5, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f6 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f2 and f7 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f7 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f1 are not neighbors, fuel-levels f7 and f4 are not neighbors, fuel-levels f7 and f6 are not neighbors, location l0 does not have a fuel-level of f3, location l0 does not have a fuel-level of f5, location l0 does not have fuel f2, location l0 does not have fuel f4, location l0 does not have fuel f6, location l1 does not have a fuel-level of f0, location l1 does not have a fuel-level of f4, location l1 does not have fuel f5, location l1 does not have fuel f6, location l1 does not have fuel f7, space s1 does not neighbour space s0, vehicle v0 does not contain cargo c0, vehicle v0 does not contain cargo c2, vehicle v0 does not contain cargo c3, vehicle v0 does not contain cargo c5, vehicle v0 does not contain cargo c8, vehicle v0 does not contain space s1, vehicle v0 is not at location l1, vehicle v1 does not contain cargo c0, vehicle v1 does not contain cargo c1, vehicle v1 does not contain cargo c2, vehicle v1 does not contain cargo c3, vehicle v1 does not contain cargo c4, vehicle v1 does not contain cargo c6, vehicle v1 does not have space s0 and vehicle v1 is not present at location l0", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is present at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l0, fuel f5 exists in location l0, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, location l1 has fuel f7, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, where cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0. Next, vehicle v0 moves from location l1, which has fuel levels f7 and f6, to location l0, where cargo c0 is unloaded from vehicle v0, occupying spaces s0 and s1. Then, vehicle v0 moves from location l0, which has fuel levels f4 and f3, to location l1, where cargo c1 is loaded onto vehicle v0, utilizing spaces s1 and s0. Subsequently, vehicle v0 relocates from location l1, which has fuel levels f6 and f5, to location l0, where cargo c1 is unloaded from vehicle v0, occupying spaces s0 and s1. This is followed by vehicle v0 moving from location l0, which has fuel levels f3 and f2, to location l1, where cargo c2 is loaded onto vehicle v0, utilizing spaces s1 and s0. Next, vehicle v0 moves from location l1, which has fuel levels f5 and f4, to location l0, where cargo c2 is unloaded from vehicle v0, occupying spaces s0 and s1. Then, vehicle v0 moves from location l0, which has fuel levels f2 and f1, to location l1, where cargo c4 is loaded onto vehicle v0, utilizing spaces s1 and s0. Subsequently, vehicle v0 relocates from location l1, which has fuel levels f4 and f3, to location l0, where cargo c4 is unloaded from vehicle v0, occupying spaces s0 and s1. This is followed by vehicle v0 moving from location l0, which has fuel levels f1 and f0, to location l1, where cargo c6 is loaded onto vehicle v0, utilizing spaces s1 and s0. Finally, vehicle v0 moves from location l1, which has fuel levels f3 and f2, to location l0, resulting in the current state. In this state, list all valid properties that involve negations. If none exist, write None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is positioned at l1, cargo c3 is found at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is positioned at l1, cargo c7 is situated at l1, cargo c8 is positioned at l0, fuel f5 is available at l0, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f0 and f1 are adjacent, fuel levels f1 and f2 are adjacent, fuel levels f4 and f5 are adjacent, locations l0 and l1 are connected, locations l1 and l0 are connected, location l1 has fuel f7, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is currently at l0, vehicle v1 contains space s1 and vehicle v1 is currently at l1."}
{"question_id": "4e2be4f3-2f55-4ce9-ab8a-a195a4738ce5", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "cargo c0 is not located in vehicle v0, cargo c0 is not located in vehicle v1, cargo c0 is not situated at location l1, cargo c1 is not at location l0cargo c1 is not present at location l0, cargo c1 is not in vehicle v0, cargo c10 is not at location l0cargo c10 is not present at location l0, cargo c2 is not in vehicle v0, cargo c2 is not in vehicle v1, cargo c2 is not situated at location l1, cargo c3 is not at location l1cargo c3 is not present at location l1, cargo c4 is not located in vehicle v0, cargo c4 is not situated at location l1, cargo c5 is not in vehicle v1, cargo c5 is not located in vehicle v0, cargo c5 is not situated at location l0, cargo c6 is not at location l1cargo c6 is not present at location l1, cargo c6 is not in vehicle v0, cargo c6 is not located in vehicle v1, cargo c7 is not in vehicle v1, cargo c7 is not located in vehicle v0, cargo c7 is not situated at location l1, cargo c8 is not at location l0cargo c8 is not present at location l0, cargo c8 is not located in vehicle v0, cargo c8 is not located in vehicle v1, cargo c9 is not at location l0cargo c9 is not present at location l0, cargo c9 is not in vehicle v1, fuel f1 does not exist in location l0, fuel f1 does not exist in location l1, fuel f3 does not exist in location l1, fuel f4 does not exist in location l1, fuel level f0 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f3, fuel level f2 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f5, fuel level f4 does not neighbour fuel level f0, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f2, fuel level f5 does not neighbour fuel level f2, fuel level f5 does not neighbour fuel level f4, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f3 are not neighbors, location l0 does not have a fuel-level of f5, location l0 does not have fuel f0, location l0 does not have fuel f2, location l0 does not have fuel f3, location l1 does not have fuel f0, location l1 does not have fuel f5, space s2 does not neighbour space s0, space s2 does not neighbour space s1, spaces s0 and s2 are not neighbors, spaces s1 and s0 are not neighbors, vehicle v0 does not contain cargo c10, vehicle v0 does not contain cargo c3, vehicle v0 does not contain cargo c9, vehicle v0 does not contain space s1, vehicle v0 does not have space s0, vehicle v0 is not situated at location l1, vehicle v1 does not contain cargo c1, vehicle v1 does not contain cargo c10, vehicle v1 does not contain cargo c3, vehicle v1 does not contain cargo c4, vehicle v1 does not have space s0, vehicle v1 does not have space s1 and vehicle v1 is not present at location l0", "plan_length": 1, "initial_state_nl": "Cargo c0 is at location l0, cargo c1 is present at location l1, cargo c10 is situated at location l1, cargo c2 is at location l0, cargo c3 is at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is situated at location l0, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c9 is present at location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has fuel f4, location l0 is connected to location l1, location l1 has fuel f3, location l1 is connected to location l0, space s0 neighbors space s1, spaces s1 and s2 are neighbors, vehicle v0 contains space s2, vehicle v0 is situated at location l1, vehicle v1 has space s2 and vehicle v1 is at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0, resulting in the current state. In this state, identify all valid properties that include negations; if there are no such properties, indicate None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Cargo c10 is also situated at l1. In contrast, cargo c2, c3, and c4 are all at l0. Cargo c5 is at l1, whereas cargo c6 and c7 are at l0. Cargo c8 is located at l1, and cargo c9 is also present at l1. \n\nFuel level f0 is adjacent to fuel level f1, and fuel level f3 is adjacent to fuel level f4. Additionally, fuel levels f1 and f2 are neighboring, as are fuel levels f2 and f3, and fuel levels f4 and f5. Location l0 is equipped with fuel f4, and it is connected to location l1. Conversely, location l1 has fuel f3 and is connected to location l0.\n\nSpace s0 is adjacent to space s1, and spaces s1 and s2 are also neighboring. Vehicle v0 contains space s2 and is situated at location l1. Similarly, vehicle v1 also contains space s2 and is located at l1."}
{"question_id": "324d1bf5-4f3f-455f-9d34-343180f98d7f", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 and vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "cargo c0 is not in vehicle v1, cargo c0 is not situated at location l1, cargo c0 is present at location l0, cargo c1 is at location l0, cargo c1 is not in vehicle v0, cargo c1 is not located in vehicle v1, cargo c1 is not situated at location l1, cargo c2 is at location l0, cargo c2 is not in vehicle v1, cargo c2 is not located in vehicle v0, cargo c2 is not situated at location l1, cargo c3 is at location l0, cargo c3 is not at location l1cargo c3 is not present at location l1, cargo c3 is not in vehicle v0, cargo c4 is at location l0, cargo c4 is not at location l1cargo c4 is not present at location l1, cargo c5 is at location l0, cargo c5 is not at location l1cargo c5 is not present at location l1, cargo c5 is not located in vehicle v0, cargo c5 is not located in vehicle v1, cargo c6 is located in vehicle v0, cargo c6 is not at location l1cargo c6 is not present at location l1, cargo c6 is not situated at location l0, cargo c7 is at location l1, cargo c7 is not at location l0cargo c7 is not present at location l0, cargo c7 is not located in vehicle v0, cargo c8 is at location l0, cargo c8 is not at location l1cargo c8 is not present at location l1, fuel f0 exists in location l0, fuel f1 does not exist in location l0, fuel f2 exists in location l1, fuel f7 does not exist in location l1, fuel level f0 does not neighbour fuel level f2, fuel level f0 does not neighbour fuel level f3, fuel level f0 does not neighbour fuel level f5, fuel level f0 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f7, fuel level f1 neighbors fuel level f2, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f5, fuel level f2 neighbors fuel level f3, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f0, fuel level f4 does not neighbour fuel level f3, fuel level f4 does not neighbour fuel level f7, fuel level f5 does not neighbour fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f3, fuel level f6 neighbors fuel level f7, fuel level f7 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f5, fuel-levels f0 and f1 are neighbors, fuel-levels f0 and f4 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f2 and f7 are not neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f3 and f6 are not neighbors, fuel-levels f3 and f7 are not neighbors, fuel-levels f4 and f1 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f7 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f1 are not neighbors, fuel-levels f7 and f2 are not neighbors, fuel-levels f7 and f6 are not neighbors, location l0 does not have a fuel-level of f6, location l0 does not have fuel f2, location l0 does not have fuel f3, location l0 does not have fuel f4, location l0 does not have fuel f5, location l0 does not have fuel f7, location l1 does not have a fuel-level of f3, location l1 does not have a fuel-level of f6, location l1 does not have fuel f0, location l1 does not have fuel f1, location l1 does not have fuel f4, location l1 does not have fuel f5, spaces s0 and s1 are neighbors, spaces s1 and s0 are not neighbors, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 does not contain cargo c0, vehicle v0 does not contain cargo c4, vehicle v0 does not contain cargo c8, vehicle v0 does not contain space s1, vehicle v0 has space s0, vehicle v0 is not at location l1, vehicle v0 is situated at location l0, vehicle v1 contains space s1, vehicle v1 does not contain cargo c3, vehicle v1 does not contain cargo c4, vehicle v1 does not contain cargo c6, vehicle v1 does not contain cargo c7, vehicle v1 does not contain cargo c8, vehicle v1 does not have space s0, vehicle v1 is at location l1 and vehicle v1 is not at location l0", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is present at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l0, fuel f5 exists in location l0, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, location l1 has fuel f7, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 travels from location l0, which has fuel levels f5 and f4, to location l1. At location l1, cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then returns to location l0, which now has fuel levels f7 and f6. Upon arrival at location l0, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. Next, vehicle v0 moves back to location l1, which has fuel levels f4 and f3. At location l1, cargo c1 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then travels to location l0, which has fuel levels f6 and f5. At location l0, cargo c1 is unloaded from vehicle v0, releasing spaces s0 and s1. This process is repeated, with vehicle v0 moving between locations l0 and l1, loading and unloading cargo c2, c4, and c6, and updating the fuel levels accordingly. The final state is reached after vehicle v0 moves from location l0, with fuel levels f1 and f0, to location l1, and then cargo c6 is loaded onto vehicle v0, utilizing spaces s1 and s0. Finally, vehicle v0 travels to location l0, which has fuel levels f3 and f2. In this final state, list all valid properties (both affirmative and negative) that hold true. If none exist, indicate None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is positioned at l1, cargo c3 is found at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is positioned at l1, cargo c7 is situated at l1, cargo c8 is positioned at l0, fuel f5 is available at l0, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f0 and f1 are adjacent, fuel levels f1 and f2 are adjacent, fuel levels f4 and f5 are adjacent, locations l0 and l1 are connected, locations l1 and l0 are connected, location l1 has fuel f7, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is positioned at l0, vehicle v1 contains space s1 and vehicle v1 is positioned at l1."}
{"question_id": "5514e1d1-82cf-4acb-a586-7dba3274c120", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1 and at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "cargo c0 is not in vehicle v0, cargo c0 is not in vehicle v1, cargo c0 is not situated at location l0, cargo c0 is present at location l1, cargo c1 is not in vehicle v1, cargo c1 is not situated at location l0, cargo c1 is not situated at location l1, cargo c2 is not located in vehicle v0, cargo c2 is not located in vehicle v1, cargo c2 is not situated at location l1, cargo c2 is present at location l0, cargo c3 is not at location l1cargo c3 is not present at location l1, cargo c3 is not located in vehicle v1, cargo c3 is present at location l0, cargo c4 is at location l1, cargo c4 is not at location l0cargo c4 is not present at location l0, cargo c4 is not located in vehicle v0, cargo c4 is not located in vehicle v1, cargo c5 is at location l1, cargo c5 is not in vehicle v0, cargo c5 is not located in vehicle v1, cargo c5 is not situated at location l0, cargo c6 is not at location l1cargo c6 is not present at location l1, cargo c6 is not in vehicle v0, cargo c6 is not in vehicle v1, cargo c6 is present at location l0, cargo c7 is not in vehicle v0, cargo c7 is not in vehicle v1, cargo c7 is not situated at location l1, cargo c7 is situated at location l0, cargo c8 is at location l1, cargo c8 is not at location l0cargo c8 is not present at location l0, cargo c9 is at location l1, cargo c9 is not in vehicle v0, cargo c9 is not situated at location l0, fuel f0 does not exist in location l0, fuel f1 does not exist in location l0, fuel f4 does not exist in location l0, fuel f4 does not exist in location l1, fuel f5 does not exist in location l0, fuel f8 does not exist in location l0, fuel level f0 does not neighbour fuel level f3, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f6, fuel level f0 does not neighbour fuel level f8, fuel level f1 does not neighbour fuel level f8, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f5, fuel level f2 neighbors fuel level f3, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f6, fuel level f3 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f8, fuel level f3 neighbors fuel level f4, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f6, fuel level f4 does not neighbour fuel level f7, fuel level f4 neighbors fuel level f5, fuel level f5 does not neighbour fuel level f1, fuel level f5 does not neighbour fuel level f2, fuel level f5 does not neighbour fuel level f7, fuel level f5 neighbors fuel level f6, fuel level f6 does not neighbour fuel level f1, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f5, fuel level f6 does not neighbour fuel level f8, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f5, fuel level f8 does not neighbour fuel level f0, fuel level f8 does not neighbour fuel level f1, fuel level f8 does not neighbour fuel level f4, fuel level f8 does not neighbour fuel level f5, fuel level f8 does not neighbour fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f2 and f7 are not neighbors, fuel-levels f2 and f8 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f4 and f8 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f4 are not neighbors, fuel-levels f5 and f8 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f2 are not neighbors, fuel-levels f7 and f3 are not neighbors, fuel-levels f7 and f4 are not neighbors, fuel-levels f7 and f6 are not neighbors, fuel-levels f7 and f8 are neighbors, fuel-levels f8 and f2 are not neighbors, fuel-levels f8 and f3 are not neighbors, fuel-levels f8 and f6 are not neighbors, location l0 and location l1 are connected, location l0 does not have a fuel-level of f3, location l0 does not have a fuel-level of f6, location l0 does not have a fuel-level of f7, location l0 has fuel f2, location l1 does not have a fuel-level of f0, location l1 does not have a fuel-level of f2, location l1 does not have a fuel-level of f3, location l1 does not have a fuel-level of f5, location l1 does not have a fuel-level of f7, location l1 does not have fuel f1, location l1 does not have fuel f8, location l1 has fuel f6, spaces s0 and s1 are neighbors, spaces s1 and s0 are not neighbors, there is a connection between locations l1 and l0, vehicle v0 contains cargo c1, vehicle v0 contains space s0, vehicle v0 does not contain cargo c3, vehicle v0 does not contain cargo c8, vehicle v0 does not have space s1, vehicle v0 is not situated at location l1, vehicle v0 is situated at location l0, vehicle v1 does not contain cargo c8, vehicle v1 does not contain cargo c9, vehicle v1 does not contain space s0, vehicle v1 has space s1, vehicle v1 is not at location l1 and vehicle v1 is situated at location l0", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l0, cargo c2 is present at location l1, cargo c3 is situated at location l1, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 and location l1 are connected, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, where the fuel levels are f8 and f7, to location l0. At location l0, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1. Subsequently, cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0, at location l0. Vehicle v0 then moves from location l0, where the fuel levels are f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1. Next, cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, where the fuel levels are f7 and f6, to location l0. At location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, resulting in the current state. In this state, list all valid properties (including both affirmative and negated properties). If there are none, indicate None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also at l0, while cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, and cargo c6 and c7 are at l0. Fuel f3 is found at location l0, and fuel levels f1 and f2 are adjacent, as are f4 and f5, and f7 and f8. Additionally, fuel levels f0 and f1, f2 and f3, f3 and f4, f5 and f6, and f6 and f7 are all neighboring. Locations l0 and l1 are connected, with l1 having fuel f8. Spaces s0 and s1 are adjacent, and there is a connection between locations l0 and l1. Vehicle v0 is at location l1 and has space s1, while vehicle v1 is at location l0 and also has space s1."}
{"question_id": "55dd0e29-b17b-4072-a904-497ed8c05c43", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "cargo c0 is at location l0, cargo c1 is present at location l0, cargo c2 is located in vehicle v0, cargo c3 is present at location l1, cargo c4 is at location l1, cargo c5 is situated at location l1, cargo c6 is present at location l0, cargo c7 is present at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel f8 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f2 neighbors fuel level f3, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 has a fuel-level of f3, location l0 is connected to location l1, space s0 neighbors space s1, there is a connection between locations l1 and l0, vehicle v0 contains space s0, vehicle v0 is situated at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l0, cargo c2 is present at location l1, cargo c3 is situated at location l1, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 and location l1 are connected, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent actions taken are as follows: cargo c2 is loaded into vehicle v0, which has spaces s1 and s0, at location l1, resulting in the current state. Now, identify all the valid properties of this state that do not include negations, or state None if there are no such properties.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also at l0, while cargo c2, c3, c4, c5, c8, and c9 are all situated at l1, with cargo c6 and c7 being at l0. Fuel f3 is found at l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f4 is adjacent to f5, and f7 is adjacent to f8. Additionally, f0 is a neighbor of f1, f2 is a neighbor of f3, f3 is a neighbor of f4, f5 is a neighbor of f6, and f6 is a neighbor of f7. Locations l0 and l1 are interconnected. Location l1 contains fuel f8. Spaces s0 and s1 are adjacent to each other. There is a connection between locations l0 and l1. Vehicle v0 is positioned at l1 and has space s1, while vehicle v1 is at l0 and also has space s1."}
{"question_id": "3d068f12-5d0a-4c3e-a483-66f4159fb76f", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f6 and f5, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 and vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "cargo c0 is situated at location l1, cargo c1 is at location l0, cargo c10 is at location l0, cargo c2 is situated at location l0, cargo c3 is present at location l0, cargo c4 is situated at location l0, cargo c5 is present at location l1, cargo c6 is located in vehicle v0, cargo c7 is present at location l1, cargo c8 is present at location l1, cargo c9 is situated at location l1, fuel f5 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, location l1 has fuel f3, spaces s0 and s1 are neighbors, vehicle v0 contains space s0 and vehicle v0 is present at location l0", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is situated at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is at location l1, cargo c6 is at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f7, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, where the fuel levels are f6 and f5, to location l0. Upon arrival at location l0, cargo c1 is unloaded from vehicle v0, which still has spaces s0 and s1. Next, vehicle v0 moves back to location l1 from location l0, which now has fuel levels f7 and f6. At location l1, cargo c4 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then proceeds to location l0 from location l1, where the fuel levels are f5 and f4. At location l0, cargo c4 is unloaded from vehicle v0, which still has spaces s0 and s1. Vehicle v0 then returns to location l1 from location l0, which now has fuel levels f6 and f5. Finally, at location l1, cargo c6 is loaded onto vehicle v0, which has spaces s1 and s0, and vehicle v0 moves to location l0 from location l1, where the fuel levels are f4 and f3, resulting in the current state. In this state, list all valid properties that do not involve negations. If none exist, state None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c10 is located at l0, cargo c2 is found at l0, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is at l1, cargo c6 is also at l1, cargo c7 is located at l1, cargo c8 is situated at l1, cargo c9 is at l1, fuel f6 is present in l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are connected, location l0 contains fuel f7, location l1 is connected to location l0, space s0 is adjacent to space s1, vehicle v0 contains space s1 and vehicle v0 is located at l1."}
{"question_id": "faad0c90-c9a1-4cf3-b082-bcca7e6dfbea", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f4 and f3, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1 and vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "cargo c0 is present at location l0, cargo c1 is present at location l0, cargo c2 is situated at location l0, cargo c3 is situated at location l0, cargo c4 is at location l0, cargo c5 is present at location l0, cargo c7 is situated at location l1, cargo c8 is situated at location l0, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f5 and f6 are neighbors, location l0 has fuel f0, location l1 has fuel f2, space s0 neighbors space s1, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 contains cargo c6, vehicle v0 has space s0, vehicle v0 is situated at location l0, vehicle v1 has space s1 and vehicle v1 is situated at location l1", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is present at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l0, fuel f5 exists in location l0, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, location l1 has fuel f7, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, where cargo c0 is loaded onto vehicle v0, occupying spaces s1 and s0. Then, vehicle v0 moves back to location l0, which now has fuel levels f7 and f6, and cargo c0 is unloaded from vehicle v0, freeing spaces s0 and s1. Next, vehicle v0 returns to location l1, which has fuel levels f4 and f3, and cargo c1 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves back to location l0, which now has fuel levels f6 and f5, and cargo c1 is unloaded from vehicle v0, freeing spaces s0 and s1. This sequence of actions continues: vehicle v0 moves to location l1, which has fuel levels f3 and f2, and cargo c2 is loaded onto vehicle v0, occupying spaces s1 and s0; vehicle v0 then moves to location l0, which has fuel levels f5 and f4, and cargo c2 is unloaded from vehicle v0, freeing spaces s0 and s1. The sequence continues with vehicle v0 moving to location l1, which has fuel levels f2 and f1, and cargo c4 is loaded onto vehicle v0, occupying spaces s1 and s0; vehicle v0 then moves to location l0, which has fuel levels f4 and f3, and cargo c4 is unloaded from vehicle v0, freeing spaces s0 and s1. Finally, vehicle v0 moves to location l1, which has fuel levels f1 and f0, and cargo c6 is loaded onto vehicle v0, occupying spaces s1 and s0, and then vehicle v0 moves to location l0, which has fuel levels f3 and f2, to reach the current state. In this state, list all valid properties that do not involve negations. If there are none, write None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is positioned at l1, cargo c3 is found at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is positioned at l1, cargo c7 is situated at l1, cargo c8 is positioned at l0, fuel f5 is available at l0, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f0 and f1 are adjacent, fuel levels f1 and f2 are adjacent, fuel levels f4 and f5 are adjacent, locations l0 and l1 are connected, locations l1 and l0 are connected, location l1 has fuel f7, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is positioned at l0, vehicle v1 contains space s1 and vehicle v1 is positioned at l1."}
{"question_id": "f55873b0-bf39-4796-8875-1ac4fe6a4c9b", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, at location l0, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1 and at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "cargo c0 is at location l1, cargo c1 is located in vehicle v0, cargo c2 is at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is at location l1, cargo c6 is situated at location l0, cargo c7 is present at location l0, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel level f0 neighbors fuel level f1, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f7 neighbors fuel level f8, fuel-levels f1 and f2 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 has fuel f2, location l0 is connected to location l1, location l1 has a fuel-level of f6, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s0, vehicle v0 is present at location l0, vehicle v1 contains space s1 and vehicle v1 is present at location l0", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l0, cargo c2 is present at location l1, cargo c3 is situated at location l1, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 and location l1 are connected, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, with fuel levels f8 and f7, to location l0. Upon arrival at location l0, cargo c2 is unloaded from vehicle v0, which still has spaces s0 and s1, and then cargo c0 is loaded onto vehicle v0, also with spaces s1 and s0. Next, vehicle v0 moves from location l0, with fuel levels f3 and f2, back to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1, and then cargo c3 is loaded onto vehicle v0, also with spaces s1 and s0. Vehicle v0 then moves from location l1, with fuel levels f7 and f6, back to location l0. Finally, at location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, also with spaces s1 and s0, resulting in the current state. In this state, list all valid properties that do not involve negations. If there are none, write None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also at l0, while cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, and cargo c6 and c7 are at l0. Fuel f3 is found at location l0, and fuel levels f1 and f2 are adjacent, as are f4 and f5, and f7 and f8. Additionally, fuel levels f0 and f1, f2 and f3, f3 and f4, f5 and f6, and f6 and f7 are all neighboring. Locations l0 and l1 are connected, with l1 having fuel f8. Spaces s0 and s1 are adjacent, and there is a connection between locations l0 and l1. Vehicle v0 is at location l1 and has space s1, while vehicle v1 is at location l0 and also has space s1."}
{"question_id": "1c2a9af3-1e81-4b37-b79f-8017ff570a63", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f8 and f7, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f1, at location l1, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, cargo c5 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1 and cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "cargo c0 is not at location l0cargo c0 is not present at location l0, cargo c0 is not located in vehicle v0, cargo c0 is not located in vehicle v1, cargo c1 is not in vehicle v0, cargo c1 is not in vehicle v1, cargo c1 is not situated at location l0, cargo c2 is not in vehicle v1, cargo c2 is not situated at location l1, cargo c3 is not in vehicle v1, cargo c3 is not located in vehicle v0, cargo c3 is not situated at location l1, cargo c4 is not at location l0cargo c4 is not present at location l0, cargo c4 is not in vehicle v1, cargo c4 is not located in vehicle v0, cargo c5 is not in vehicle v1, cargo c5 is not situated at location l1, cargo c6 is not in vehicle v1, cargo c6 is not situated at location l1, cargo c7 is not at location l0cargo c7 is not present at location l0, cargo c7 is not located in vehicle v1, cargo c8 is not at location l0cargo c8 is not present at location l0, cargo c8 is not in vehicle v0, cargo c8 is not in vehicle v1, cargo c9 is not at location l0cargo c9 is not present at location l0, cargo c9 is not at location l1cargo c9 is not present at location l1, cargo c9 is not in vehicle v1, fuel f1 does not exist in location l0, fuel f2 does not exist in location l0, fuel f2 does not exist in location l1, fuel f3 does not exist in location l0, fuel f4 does not exist in location l0, fuel f4 does not exist in location l1, fuel f8 does not exist in location l0, fuel level f0 does not neighbour fuel level f3, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f5, fuel level f0 does not neighbour fuel level f6, fuel level f0 does not neighbour fuel level f8, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f7, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f7, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f3, fuel level f4 does not neighbour fuel level f6, fuel level f4 does not neighbour fuel level f7, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f4, fuel level f5 does not neighbour fuel level f7, fuel level f5 does not neighbour fuel level f8, fuel level f6 does not neighbour fuel level f0, fuel level f6 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f0, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f1, fuel level f8 does not neighbour fuel level f2, fuel level f8 does not neighbour fuel level f5, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f8 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f2 and f8 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f6 are not neighbors, fuel-levels f3 and f8 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f8 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f6 and f8 are not neighbors, fuel-levels f7 and f3 are not neighbors, fuel-levels f7 and f4 are not neighbors, fuel-levels f7 and f5 are not neighbors, fuel-levels f8 and f0 are not neighbors, fuel-levels f8 and f3 are not neighbors, fuel-levels f8 and f4 are not neighbors, fuel-levels f8 and f6 are not neighbors, fuel-levels f8 and f7 are not neighbors, location l0 does not have fuel f5, location l0 does not have fuel f6, location l0 does not have fuel f7, location l1 does not have a fuel-level of f1, location l1 does not have a fuel-level of f3, location l1 does not have fuel f0, location l1 does not have fuel f6, location l1 does not have fuel f7, location l1 does not have fuel f8, space s1 does not neighbour space s0, vehicle v0 does not contain cargo c2, vehicle v0 does not contain cargo c5, vehicle v0 does not contain cargo c6, vehicle v0 does not contain cargo c7, vehicle v0 does not have space s1, vehicle v0 is not at location l0, vehicle v1 does not have space s0 and vehicle v1 is not present at location l1", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l0, cargo c2 is present at location l1, cargo c3 is situated at location l1, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 and location l1 are connected, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f8 and f7, to location l0, where cargo c2 is unloaded from vehicle v0 using spaces s0 and s1. Next, cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0, at location l0. Vehicle v0 then moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0 using spaces s0 and s1, and cargo c3 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then travels to location l0 from location l1, which has fuel levels f7 and f6. Upon arrival at location l0, cargo c3 is unloaded from vehicle v0 using spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f2 and f1, to location l1. At location l1, cargo c1 is unloaded from vehicle v0 using spaces s0 and s1, and cargo c5 is loaded onto vehicle v0, occupying spaces s1 and s0. Vehicle v0 then travels to location l0 from location l1, which has fuel levels f6 and f5. At location l0, cargo c5 is unloaded from vehicle v0 using spaces s0 and s1, and cargo c7 is loaded onto vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f1 and f0, to location l1. At location l1, cargo c7 is unloaded from vehicle v0 using spaces s0 and s1, and cargo c9 is loaded onto vehicle v0, occupying spaces s1 and s0, resulting in the current state. In this state, list all valid properties that involve negations. If none exist, state None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also at l0, while cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, and cargo c6 and c7 are at l0. Fuel f3 is found at location l0, and fuel levels f1 and f2 are adjacent, as are f4 and f5, and f7 and f8. Additionally, fuel levels f0 and f1, f2 and f3, f3 and f4, f5 and f6, and f6 and f7 are all neighboring. Locations l0 and l1 are connected, with l1 having fuel f8. Spaces s0 and s1 are adjacent, and there is a connection between l0 and l1. Vehicle v0 is at location l1 and has space s1, while vehicle v1 is at location l0 and also has space s1."}
{"question_id": "c178cc99-ce2a-4bbd-aa63-c693c2f59c89", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1 and cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "cargo c0 is not located in vehicle v0, cargo c0 is not located in vehicle v1, cargo c0 is not situated at location l1, cargo c0 is situated at location l0, cargo c1 is not at location l1cargo c1 is not present at location l1, cargo c1 is not located in vehicle v0, cargo c1 is not located in vehicle v1, cargo c1 is situated at location l0, cargo c2 is not at location l1cargo c2 is not present at location l1, cargo c2 is not located in vehicle v1, cargo c2 is not situated at location l0, cargo c3 is at location l0, cargo c3 is not in vehicle v1, cargo c3 is not located in vehicle v0, cargo c3 is not situated at location l1, cargo c4 is not located in vehicle v0, cargo c4 is not located in vehicle v1, cargo c4 is not situated at location l0, cargo c4 is present at location l1, cargo c5 is not in vehicle v0, cargo c5 is not located in vehicle v1, cargo c5 is not situated at location l1, cargo c5 is situated at location l0, cargo c6 is at location l1, cargo c6 is not at location l0cargo c6 is not present at location l0, cargo c7 is at location l1, cargo c7 is not at location l0cargo c7 is not present at location l0, cargo c7 is not in vehicle v1, cargo c7 is not located in vehicle v0, cargo c8 is not at location l1cargo c8 is not present at location l1, cargo c8 is not located in vehicle v1, cargo c8 is present at location l0, fuel f0 does not exist in location l0, fuel f0 does not exist in location l1, fuel f4 does not exist in location l1, fuel f6 does not exist in location l0, fuel f7 does not exist in location l0, fuel f7 does not exist in location l1, fuel level f0 does not neighbour fuel level f2, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f5, fuel level f0 neighbors fuel level f1, fuel level f1 does not neighbour fuel level f5, fuel level f1 neighbors fuel level f2, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f5, fuel level f2 neighbors fuel level f3, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f6, fuel level f4 does not neighbour fuel level f0, fuel level f4 does not neighbour fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f5 does not neighbour fuel level f4, fuel level f5 does not neighbour fuel level f7, fuel level f5 neighbors fuel level f6, fuel level f6 does not neighbour fuel level f0, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f0, fuel level f7 does not neighbour fuel level f5, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f6 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f2 and f7 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f3 and f7 are not neighbors, fuel-levels f4 and f1 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f3 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f1 are not neighbors, fuel-levels f7 and f2 are not neighbors, fuel-levels f7 and f3 are not neighbors, fuel-levels f7 and f4 are not neighbors, fuel-levels f7 and f6 are not neighbors, location l0 does not have a fuel-level of f3, location l0 does not have fuel f1, location l0 does not have fuel f4, location l0 does not have fuel f5, location l0 has a fuel-level of f2, location l0 is connected to location l1, location l1 does not have a fuel-level of f1, location l1 does not have a fuel-level of f3, location l1 does not have fuel f2, location l1 does not have fuel f6, location l1 has a fuel-level of f5, space s1 does not neighbour space s0, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 contains cargo c2, vehicle v0 contains space s0, vehicle v0 does not contain cargo c6, vehicle v0 does not contain cargo c8, vehicle v0 does not contain space s1, vehicle v0 is not situated at location l0, vehicle v0 is present at location l1, vehicle v1 does not contain cargo c6, vehicle v1 does not contain space s0, vehicle v1 has space s1, vehicle v1 is not at location l0 and vehicle v1 is situated at location l1", "plan_length": 10, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is present at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l0, fuel f5 exists in location l0, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, location l1 has fuel f7, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, where cargo c0 is loaded onto vehicle v0, occupying spaces s1 and s0. Next, vehicle v0 moves from location l1, which has fuel levels f7 and f6, back to location l0, where cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. Then, vehicle v0 moves from location l0, which has fuel levels f4 and f3, to location l1, where cargo c1 is loaded onto vehicle v0, occupying spaces s1 and s0. Subsequently, vehicle v0 relocates from location l1, which has fuel levels f6 and f5, to location l0, where cargo c1 is unloaded from vehicle v0, freeing up spaces s0 and s1. Finally, vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1, and cargo c2 is loaded onto vehicle v0, occupying spaces s1 and s0, resulting in the current state. In this state, list all valid properties (including both affirmative and negated properties). If there are none, indicate None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is positioned at l1, cargo c3 is found at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is positioned at l1, cargo c7 is situated at l1, cargo c8 is positioned at l0, fuel f5 is available at l0, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f0 and f1 are adjacent, fuel levels f1 and f2 are adjacent, fuel levels f4 and f5 are adjacent, locations l0 and l1 are connected, locations l1 and l0 are connected, location l1 has fuel f7, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is currently at l0, vehicle v1 contains space s1 and vehicle v1 is currently at l1."}
{"question_id": "e8bdc578-c5ab-45a5-a75c-6c1ee7858ce5", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "cargo c0 is at location l1, cargo c0 is not at location l0cargo c0 is not present at location l0, cargo c0 is not in vehicle v0, cargo c1 is not at location l1cargo c1 is not present at location l1, cargo c1 is not situated at location l0, cargo c10 is at location l0, cargo c10 is not situated at location l1, cargo c2 is not at location l1cargo c2 is not present at location l1, cargo c2 is not in vehicle v0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c3 is not at location l1cargo c3 is not present at location l1, cargo c4 is not at location l0cargo c4 is not present at location l0, cargo c4 is not in vehicle v0, cargo c4 is situated at location l1, cargo c5 is not located in vehicle v0, cargo c5 is not situated at location l0, cargo c5 is situated at location l1, cargo c6 is not at location l0cargo c6 is not present at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c7 is not at location l0cargo c7 is not present at location l0, cargo c8 is not at location l0cargo c8 is not present at location l0, cargo c8 is not in vehicle v0, cargo c8 is situated at location l1, cargo c9 is not at location l0cargo c9 is not present at location l0, cargo c9 is present at location l1, fuel f3 does not exist in location l1, fuel f4 does not exist in location l0, fuel f4 does not exist in location l1, fuel f5 does not exist in location l1, fuel f6 does not exist in location l0, fuel f7 does not exist in location l1, fuel f7 exists in location l0, fuel level f0 does not neighbour fuel level f2, fuel level f0 does not neighbour fuel level f3, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f6, fuel level f0 does not neighbour fuel level f7, fuel level f0 does not neighbour fuel level f8, fuel level f1 does not neighbour fuel level f3, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f8, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f6, fuel level f3 does not neighbour fuel level f7, fuel level f4 does not neighbour fuel level f0, fuel level f4 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f6, fuel level f4 does not neighbour fuel level f7, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f7, fuel level f5 neighbors fuel level f6, fuel level f6 does not neighbour fuel level f0, fuel level f6 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f0, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f2 and f7 are not neighbors, fuel-levels f2 and f8 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f3 and f8 are not neighbors, fuel-levels f4 and f1 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f4 and f8 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f4 are not neighbors, fuel-levels f5 and f8 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f6 and f8 are not neighbors, fuel-levels f7 and f1 are not neighbors, fuel-levels f7 and f3 are not neighbors, fuel-levels f7 and f5 are not neighbors, fuel-levels f7 and f8 are neighbors, fuel-levels f8 and f0 are not neighbors, fuel-levels f8 and f1 are not neighbors, fuel-levels f8 and f2 are not neighbors, fuel-levels f8 and f3 are not neighbors, fuel-levels f8 and f4 are not neighbors, fuel-levels f8 and f5 are not neighbors, fuel-levels f8 and f6 are not neighbors, fuel-levels f8 and f7 are not neighbors, location l0 and location l1 are connected, location l0 does not have a fuel-level of f0, location l0 does not have a fuel-level of f3, location l0 does not have a fuel-level of f5, location l0 does not have a fuel-level of f8, location l0 does not have fuel f1, location l0 does not have fuel f2, location l1 does not have a fuel-level of f0, location l1 does not have a fuel-level of f2, location l1 does not have a fuel-level of f8, location l1 does not have fuel f1, location l1 has a fuel-level of f6, space s1 does not neighbour space s0, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 contains cargo c1, vehicle v0 does not contain cargo c10, vehicle v0 does not contain cargo c3, vehicle v0 does not contain cargo c6, vehicle v0 does not contain cargo c7, vehicle v0 does not contain cargo c9, vehicle v0 does not have space s1, vehicle v0 has space s0, vehicle v0 is at location l1 and vehicle v0 is not present at location l0", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is situated at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is at location l1, cargo c6 is at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f7, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent actions taken are as follows: cargo c1 is loaded into vehicle v0, which has spaces s1 and s0, at location l1, resulting in the current state. In this state, enumerate all valid properties (including both affirmative and negated properties). If there are no valid properties, indicate None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c10 is located at l0, cargo c2 is found at l0, cargo c3 is situated at l0, cargo c4 is at l1, cargo c5 is also at l1, cargo c6 is at l1, cargo c7 is at l1, cargo c8 is located at l1, cargo c9 is at l1, fuel f6 is present in l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are connected, location l0 has fuel f7, location l1 is connected to location l0, spaces s0 and s1 are adjacent, vehicle v0 contains space s1 and vehicle v0 is located at l1."}
{"question_id": "e6b24a05-2b50-4ac3-b47b-97d45e887b3c", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, at location l0, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c8 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1 and cargo c8 is unloaded from vehicle v0 with space s0 and space s1 at location l0 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "cargo c0 is at location l1, cargo c1 is situated at location l0, cargo c10 is present at location l0, cargo c2 is situated at location l0, cargo c3 is at location l0, cargo c4 is situated at location l0, cargo c5 is situated at location l1, cargo c6 is present at location l0, cargo c7 is situated at location l0, cargo c8 is present at location l0, cargo c9 is at location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f7 neighbors fuel level f8, fuel-levels f6 and f7 are neighbors, location l0 has a fuel-level of f3, location l0 is connected to location l1, location l1 has fuel f1, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is present at location l0", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is situated at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is at location l1, cargo c6 is at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f7, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c1 is loaded onto vehicle v0, which has available spaces s1 and s0, at location l1. Then, vehicle v0 travels from location l1, where the fuel levels are f6 and f5, to location l0. At location l0, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1. Next, vehicle v0 moves from location l0, with fuel levels f7 and f6, to location l1. At location l1, cargo c4 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then travels from location l1, with fuel levels f5 and f4, to location l0. Upon arrival at location l0, cargo c4 is unloaded from vehicle v0, which has spaces s0 and s1. Vehicle v0 then moves from location l0, with fuel levels f6 and f5, to location l1. At location l1, cargo c6 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then travels from location l1, with fuel levels f4 and f3, to location l0. At location l0, cargo c6 is unloaded from vehicle v0, which has spaces s0 and s1. Next, vehicle v0 moves from location l0, with fuel levels f5 and f4, to location l1. At location l1, cargo c7 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then travels from location l1, with fuel levels f3 and f2, to location l0. Upon arrival at location l0, cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1. Vehicle v0 then moves from location l0, with fuel levels f4 and f3, to location l1. At location l1, cargo c8 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then travels from location l1, with fuel levels f2 and f1, to location l0, where cargo c8 is unloaded from vehicle v0, which has spaces s0 and s1, resulting in the current state. In this state, list all valid properties that do not involve negations. If there are none, write None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c10 is located at l0, cargo c2 is found at l0, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is at l1, cargo c6 is also at l1, cargo c7 is situated at l1, cargo c8 is located at l1, cargo c9 is at l1, fuel f6 is present in l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are connected, location l0 contains fuel f7, location l1 is connected to location l0, space s0 is adjacent to space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1."}
{"question_id": "923fcadb-0fb0-4c56-96d8-4c0595f8a5e0", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, cargo c6 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c8 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1 and at location l0, cargo c8 is unloaded from vehicle v0 with spaces s0 and s1 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "cargo c0 is not at location l0cargo c0 is not present at location l0, cargo c1 is not at location l1cargo c1 is not present at location l1, cargo c1 is not in vehicle v0, cargo c10 is not at location l1cargo c10 is not present at location l1, cargo c10 is not located in vehicle v0, cargo c2 is not at location l1cargo c2 is not present at location l1, cargo c3 is not at location l1cargo c3 is not present at location l1, cargo c3 is not located in vehicle v0, cargo c4 is not at location l1cargo c4 is not present at location l1, cargo c4 is not in vehicle v0, cargo c5 is not at location l0cargo c5 is not present at location l0, cargo c5 is not located in vehicle v0, cargo c6 is not at location l1cargo c6 is not present at location l1, cargo c7 is not situated at location l1, cargo c8 is not located in vehicle v0, cargo c8 is not situated at location l1, cargo c9 is not at location l0cargo c9 is not present at location l0, cargo c9 is not in vehicle v0, fuel f1 does not exist in location l0, fuel f2 does not exist in location l0, fuel f4 does not exist in location l0, fuel f5 does not exist in location l0, fuel f6 does not exist in location l0, fuel f6 does not exist in location l1, fuel f7 does not exist in location l0, fuel level f0 does not neighbour fuel level f3, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f6, fuel level f0 does not neighbour fuel level f8, fuel level f1 does not neighbour fuel level f3, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f8, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f5, fuel level f2 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f6, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f3, fuel level f4 does not neighbour fuel level f6, fuel level f4 does not neighbour fuel level f7, fuel level f4 does not neighbour fuel level f8, fuel level f5 does not neighbour fuel level f1, fuel level f6 does not neighbour fuel level f0, fuel level f6 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f5, fuel level f7 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f0, fuel level f8 does not neighbour fuel level f4, fuel level f8 does not neighbour fuel level f7, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f2 and f8 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f3 and f7 are not neighbors, fuel-levels f3 and f8 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f4 are not neighbors, fuel-levels f5 and f7 are not neighbors, fuel-levels f5 and f8 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f6 and f8 are not neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f2 are not neighbors, fuel-levels f8 and f1 are not neighbors, fuel-levels f8 and f2 are not neighbors, fuel-levels f8 and f3 are not neighbors, fuel-levels f8 and f5 are not neighbors, fuel-levels f8 and f6 are not neighbors, location l0 does not have a fuel-level of f0, location l0 does not have fuel f8, location l1 does not have a fuel-level of f0, location l1 does not have a fuel-level of f2, location l1 does not have a fuel-level of f3, location l1 does not have a fuel-level of f4, location l1 does not have a fuel-level of f5, location l1 does not have a fuel-level of f7, location l1 does not have fuel f8, spaces s1 and s0 are not neighbors, vehicle v0 does not contain cargo c0, vehicle v0 does not contain cargo c2, vehicle v0 does not contain cargo c6, vehicle v0 does not contain cargo c7, vehicle v0 does not contain space s0 and vehicle v0 is not situated at location l1", "plan_length": 19, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is at location l1, cargo c10 is situated at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is at location l1, cargo c5 is at location l1, cargo c6 is at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f6 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f6 neighbors fuel level f7, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 and location l1 are connected, location l0 has fuel f7, location l1 is connected to location l0, space s0 neighbors space s1, vehicle v0 contains space s1 and vehicle v0 is situated at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c1 is loaded onto vehicle v0, which has available spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f6 and f5, to location l0, where cargo c1 is unloaded from vehicle v0, utilizing spaces s0 and s1, next vehicle v0 moves back to location l1 from location l0, which now has fuel levels f7 and f6, at location l1, cargo c4 is loaded onto vehicle v0, occupying spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f5 and f4, to location l0, where cargo c4 is unloaded from vehicle v0, using spaces s0 and s1, vehicle v0 then moves from location l0, with fuel levels f6 and f5, back to location l1, at location l1, cargo c6 is loaded onto vehicle v0, utilizing spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f4 and f3, to location l0, where cargo c6 is unloaded from vehicle v0, occupying spaces s0 and s1, next vehicle v0 moves back to location l1 from location l0, which now has fuel levels f5 and f4, at location l1, cargo c7 is loaded onto vehicle v0, utilizing spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f3 and f2, to location l0, where cargo c7 is unloaded from vehicle v0, using spaces s0 and s1, vehicle v0 then moves back to location l1 from location l0, which now has fuel levels f4 and f3, at location l1, cargo c8 is loaded onto vehicle v0, occupying spaces s1 and s0, then vehicle v0 travels to location l0 from location l1, which has fuel levels f2 and f1, and finally, at location l0, cargo c8 is unloaded from vehicle v0, utilizing spaces s0 and s1, resulting in the current state. In this state, list all valid properties that involve negations, or write None if there are none.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c10 is located at l0, cargo c2 is found at l0, cargo c3 is situated at l0, cargo c4 is positioned at l1, cargo c5 is at l1, cargo c6 is also at l1, cargo c7 is located at l1, cargo c8 is situated at l1, cargo c9 is at l1, fuel f6 is present in l1, fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f6 is adjacent to fuel level f7, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, fuel levels f5 and f6 are adjacent, fuel levels f7 and f8 are adjacent, locations l0 and l1 are connected, location l0 contains fuel f7, location l1 is connected to location l0, space s0 is adjacent to space s1, vehicle v0 contains space s1 and vehicle v0 is located at l1."}
{"question_id": "6aa7ec8f-7003-4da4-9002-99e6115d307b", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "cargo c0 is not at location l1cargo c0 is not present at location l1, cargo c0 is not located in vehicle v1, cargo c1 is not in vehicle v0, cargo c1 is not in vehicle v1, cargo c1 is not situated at location l1, cargo c2 is not at location l0cargo c2 is not present at location l0, cargo c2 is not at location l1cargo c2 is not present at location l1, cargo c2 is not located in vehicle v1, cargo c3 is not located in vehicle v0, cargo c3 is not located in vehicle v1, cargo c3 is not situated at location l0, cargo c4 is not located in vehicle v0, cargo c4 is not situated at location l0, cargo c5 is not in vehicle v0, cargo c5 is not situated at location l0, cargo c6 is not in vehicle v1, cargo c6 is not located in vehicle v0, cargo c6 is not situated at location l1, cargo c7 is not at location l1cargo c7 is not present at location l1, cargo c7 is not located in vehicle v0, cargo c8 is not in vehicle v0, cargo c8 is not situated at location l0, cargo c9 is not in vehicle v1, cargo c9 is not located in vehicle v0, cargo c9 is not situated at location l0, fuel f0 does not exist in location l0, fuel f4 does not exist in location l1, fuel f5 does not exist in location l0, fuel f7 does not exist in location l0, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f7, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f7, fuel level f2 does not neighbour fuel level f8, fuel level f3 does not neighbour fuel level f1, fuel level f3 does not neighbour fuel level f2, fuel level f3 does not neighbour fuel level f5, fuel level f4 does not neighbour fuel level f0, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f6, fuel level f5 does not neighbour fuel level f4, fuel level f5 does not neighbour fuel level f7, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f5, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f5, fuel level f7 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f0, fuel level f8 does not neighbour fuel level f2, fuel level f8 does not neighbour fuel level f5, fuel level f8 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f7, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f0 and f6 are not neighbors, fuel-levels f0 and f8 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f1 and f8 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f6 are not neighbors, fuel-levels f3 and f7 are not neighbors, fuel-levels f3 and f8 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f4 and f8 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f8 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f8 are not neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f1 are not neighbors, fuel-levels f7 and f3 are not neighbors, fuel-levels f7 and f4 are not neighbors, fuel-levels f8 and f1 are not neighbors, fuel-levels f8 and f3 are not neighbors, fuel-levels f8 and f4 are not neighbors, location l0 does not have a fuel-level of f1, location l0 does not have a fuel-level of f2, location l0 does not have a fuel-level of f6, location l0 does not have a fuel-level of f8, location l0 does not have fuel f4, location l1 does not have a fuel-level of f0, location l1 does not have a fuel-level of f1, location l1 does not have a fuel-level of f2, location l1 does not have a fuel-level of f6, location l1 does not have fuel f3, location l1 does not have fuel f5, location l1 does not have fuel f7, spaces s1 and s0 are not neighbors, vehicle v0 does not contain cargo c0, vehicle v0 does not have space s1, vehicle v0 is not at location l0, vehicle v1 does not contain cargo c4, vehicle v1 does not contain cargo c5, vehicle v1 does not contain cargo c7, vehicle v1 does not contain cargo c8, vehicle v1 does not contain space s0 and vehicle v1 is not at location l1", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l0, cargo c2 is present at location l1, cargo c3 is situated at location l1, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is present at location l1, cargo c9 is present at location l1, fuel f3 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f4 neighbors fuel level f5, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f5 and f6 are neighbors, fuel-levels f6 and f7 are neighbors, location l0 and location l1 are connected, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent actions taken are as follows: cargo c2 is loaded into vehicle v0, which has spaces s1 and s0, at location l1, resulting in the current state. In this state, identify and list all valid properties that include negations; if there are no such properties, indicate None.", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also at l0, while cargo c2, c3, c4, c5, c8, and c9 are all situated at location l1, and cargo c6 and c7 are at l0. Fuel f3 is found at location l0, and fuel levels f1 and f2 are adjacent, as are f4 and f5, and f7 and f8. Additionally, fuel levels f0 and f1, f2 and f3, f3 and f4, f5 and f6, and f6 and f7 are all neighboring. Locations l0 and l1 are connected, with l1 having fuel f8. Spaces s0 and s1 are adjacent, and there is a connection between l0 and l1. Vehicle v0 is at location l1 and has space s1, while vehicle v1 is at location l0 and also has space s1."}
{"question_id": "73564473-b1aa-4a2e-a0b7-8780c847fccb", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "cargo c0 is at location l1, cargo c1 is situated at location l1, cargo c2 is at location l1, cargo c3 is at location l0, cargo c4 is situated at location l1, cargo c5 is at location l0, cargo c6 is situated at location l1, cargo c7 is present at location l1, cargo c8 is at location l0, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 has a fuel-level of f4, location l0 is connected to location l1, location l1 has fuel f7, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is at location l1, vehicle v1 contains space s1 and vehicle v1 is present at location l1", "plan_length": 1, "initial_state_nl": "Cargo c0 is present at location l1, cargo c1 is present at location l1, cargo c2 is situated at location l1, cargo c3 is present at location l0, cargo c4 is situated at location l1, cargo c5 is present at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is situated at location l0, fuel f5 exists in location l0, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f0 and f1 are neighbors, fuel-levels f1 and f2 are neighbors, fuel-levels f4 and f5 are neighbors, location l0 and location l1 are connected, location l1 and location l0 are connected, location l1 has fuel f7, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is at location l0, vehicle v1 contains space s1 and vehicle v1 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: vehicle v0 transitions from location l0, which has fuel levels of f5 and f4, to location l1, resulting in the current state. In this state, identify all valid properties that do not include negations, or indicate None if there are no such properties.", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also at l1, cargo c2 is positioned at l1, cargo c3 is found at l0, cargo c4 is positioned at l1, cargo c5 is located at l0, cargo c6 is positioned at l1, cargo c7 is situated at l1, cargo c8 is positioned at l0, fuel f5 is available at l0, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f0 and f1 are adjacent, fuel levels f1 and f2 are adjacent, fuel levels f4 and f5 are adjacent, locations l0 and l1 are connected, locations l1 and l0 are connected, location l1 has fuel f7, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is positioned at l0, vehicle v1 contains space s1 and vehicle v1 is positioned at l1."}
