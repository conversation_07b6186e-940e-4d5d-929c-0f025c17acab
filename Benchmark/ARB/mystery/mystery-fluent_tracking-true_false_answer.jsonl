{"question_id": "b3257d80-aece-4db9-acf6-bbff7d3d6cbd", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_14", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, is it True or False that fuel-levels f1 and f8 are neighbors?", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is present at location l0, cargo c1 is situated at location l0, cargo c2 is at location l1, cargo c3 is situated at location l1, cargo c4 is present at location l1, cargo c5 is at location l1, cargo c6 is present at location l0, cargo c7 is present at location l0, cargo c8 is situated at location l1, cargo c9 is situated at location l1, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 has a fuel-level of f3, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent actions taken are as follows: cargo c2 is loaded into vehicle v0, which has spaces s1 and s0, at location l1, resulting in the current state. In this state, the question arises: are fuel-levels f1 and f8 adjacent to each other, True or False?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also found at l0, cargo c2 is positioned at l1, cargo c3 is situated at l1, cargo c4 is located at l1, cargo c5 is positioned at l1, cargo c6 is found at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is situated at l1, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, location l0 is equipped with fuel level f3, location l1 is equipped with fuel f8, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, a connection exists between locations l1 and l0, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l1, vehicle v1 is equipped with space s1 and vehicle v1 is currently at location l0."}
{"question_id": "1c7f7dd3-1bcb-484c-9b10-1a4a4434a836", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_10", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1 to reach the current state. In this state, is it True or False that cargo c7 is located in vehicle v0?", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is at location l1, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 has space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0, which has fuel-levels f5 and f4, to location l1, resulting in the current state. In this state, is it True or False that cargo c7 is located in vehicle v0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, c1 is also at l1, c2 is situated at l1, c3 is found at l0, c4 is present at l1, c5 is located at l0, c6 is situated at l1, c7 is at l1, and c8 is at l0. Fuel f5 is available at l0, while f7 is available at l1. The fuel levels are connected in the following order: f0 is adjacent to f1, f1 is adjacent to f2, f3 is adjacent to f4, f4 is adjacent to f5, f5 is adjacent to f6, and f6 is adjacent to f7. Additionally, f2 and f3 are neighboring fuel levels. Locations l0 and l1 are interconnected, with l1 also being connected to l0. Spaces s0 and s1 are adjacent, and vehicle v0 contains space s1, currently situated at l0. Vehicle v1, which also has space s1, is present at l1."}
{"question_id": "0454f3f2-1c1f-465b-9d62-16a7f1c9ffc5", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_19", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f7 and f6, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, at location l0, cargo c6 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, at location l0, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c8 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0 and at location l0, cargo c8 is unloaded from vehicle v0 with spaces s0 and s1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f4 and f5 are neighbors, location l1 and location l0 are connected, spaces s0 and s1 are neighbors and there is a connection between locations l0 and l1?", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is present at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l1 has a fuel-level of f6, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1 and vehicle v0 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, then vehicle v0 travels from location l1, with fuel levels f6 and f5, to location l0, where cargo c1 is unloaded from vehicle v0, using spaces s0 and s1; subsequently, vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1, where cargo c4 is loaded onto vehicle v0, utilizing spaces s1 and s0; vehicle v0 then travels from location l1, with fuel levels f5 and f4, to location l0, where cargo c4 is unloaded from vehicle v0, using spaces s0 and s1; next, vehicle v0 moves from location l0, with fuel levels f6 and f5, back to location l1, where cargo c6 is loaded onto vehicle v0, using spaces s1 and s0; vehicle v0 then travels from location l1, with fuel levels f4 and f3, to location l0, where cargo c6 is unloaded from vehicle v0, using spaces s0 and s1; subsequently, vehicle v0 moves from location l0, with fuel levels f5 and f4, back to location l1, where cargo c7 is loaded onto vehicle v0, utilizing spaces s1 and s0; vehicle v0 then travels from location l1, with fuel levels f3 and f2, to location l0, where cargo c7 is unloaded from vehicle v0, using spaces s0 and s1; next, vehicle v0 moves from location l0, with fuel levels f4 and f3, back to location l1, where cargo c8 is loaded onto vehicle v0, using spaces s1 and s0; finally, vehicle v0 travels from location l1, with fuel levels f2 and f1, to location l0, where cargo c8 is unloaded from vehicle v0, using spaces s0 and s1, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel level f7 is adjacent to fuel level f8, fuel levels f0 and f1 are adjacent, fuel levels f4 and f5 are adjacent, locations l1 and l0 are connected, spaces s0 and s1 are adjacent, and is there a connection between locations l0 and l1?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, cargo c2, and cargo c3 are all situated at location l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all located at l1. Fuel f7 is present at location l0. The fuel levels are connected in the following order: f1 is adjacent to f2, f2 is adjacent to f3, f4 is adjacent to f5, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, as are f3 and f4. Locations l0 and l1 are connected, with l1 having a fuel level of f6. It is also noted that l1 is connected to l0. Spaces s0 and s1 are adjacent, and vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "9dd2f1ca-86f5-4b05-8e2c-209b49a0984d", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_22", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2 and cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: fuel level f0 does not neighbour fuel level f1, fuel level f0 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f2, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f5, fuel level f2 does not neighbour fuel level f6, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f4, fuel level f3 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f6, fuel level f4 does not neighbour fuel level f0, fuel level f4 does not neighbour fuel level f1, fuel level f5 does not neighbour fuel level f2, fuel level f5 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f6, fuel level f5 does not neighbour fuel level f7, fuel level f6 does not neighbour fuel level f1, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f4, fuel level f6 does not neighbour fuel level f5, fuel level f6 does not neighbour fuel level f7, fuel level f7 does not neighbour fuel level f0, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f5, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f4 are not neighbors, fuel-levels f0 and f6 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f3 are not neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f2 and f7 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f7 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f4 and f5 are not neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f4 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f7 and f1 are not neighbors, fuel-levels f7 and f6 are not neighbors, location l0 and location l1 are not connected, space s0 does not neighbour space s1, space s1 does not neighbour space s0 and there is no connection between locations l1 and l0?", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is at location l1, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 has space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, where cargo c0 is loaded into vehicle v0, which has available spaces s1 and s0. Then, vehicle v0 moves from location l1, which has fuel levels f7 and f6, back to location l0, where cargo c0 is unloaded from vehicle v0, which has available spaces s0 and s1. Next, vehicle v0 moves from location l0, which has fuel levels f4 and f3, to location l1, where cargo c1 is loaded into vehicle v0, which has available spaces s1 and s0. Subsequently, vehicle v0 relocates from location l1, which has fuel levels f6 and f5, to location l0, where cargo c1 is unloaded from vehicle v0, which has available spaces s0 and s1. Finally, vehicle v0 moves from location l0, which has fuel levels f3 and f2, to location l1, where cargo c2 is loaded into vehicle v0, which has available spaces s1 and s0, resulting in the current state. In this state, are the following properties that involve negations True or False: fuel level f0 is not adjacent to fuel level f1, fuel level f0 is not adjacent to fuel level f5, fuel level f1 is not adjacent to fuel level f0, fuel level f1 is not adjacent to fuel level f2, fuel level f1 is not adjacent to fuel level f4, fuel level f1 is not adjacent to fuel level f5, fuel level f2 is not adjacent to fuel level f6, fuel level f3 is not adjacent to fuel level f0, fuel level f3 is not adjacent to fuel level f4, fuel level f3 is not adjacent to fuel level f5, fuel level f3 is not adjacent to fuel level f6, fuel level f4 is not adjacent to fuel level f0, fuel level f4 is not adjacent to fuel level f1, fuel level f5 is not adjacent to fuel level f2, fuel level f5 is not adjacent to fuel level f3, fuel level f5 is not adjacent to fuel level f6, fuel level f5 is not adjacent to fuel level f7, fuel level f6 is not adjacent to fuel level f1, fuel level f6 is not adjacent to fuel level f3, fuel level f6 is not adjacent to fuel level f4, fuel level f6 is not adjacent to fuel level f5, fuel level f6 is not adjacent to fuel level f7, fuel level f7 is not adjacent to fuel level f0, fuel level f7 is not adjacent to fuel level f2, fuel level f7 is not adjacent to fuel level f3, fuel level f7 is not adjacent to fuel level f4, fuel level f7 is not adjacent to fuel level f5, fuel levels f0 and f2 are not adjacent, fuel levels f0 and f3 are not adjacent, fuel levels f0 and f4 are not adjacent, fuel levels f0 and f6 are not adjacent, fuel levels f0 and f7 are not adjacent, fuel levels f1 and f3 are not adjacent, fuel levels f1 and f6 are not adjacent, fuel levels f1 and f7 are not adjacent, fuel levels f2 and f0 are not adjacent, fuel levels f2 and f1 are not adjacent, fuel levels f2 and f3 are not adjacent, fuel levels f2 and f4 are not adjacent, fuel levels f2 and f5 are not adjacent, fuel levels f2 and f7 are not adjacent, fuel levels f3 and f1 are not adjacent, fuel levels f3 and f2 are not adjacent, fuel levels f3 and f7 are not adjacent, fuel levels f4 and f2 are not adjacent, fuel levels f4 and f3 are not adjacent, fuel levels f4 and f5 are not adjacent, fuel levels f4 and f6 are not adjacent, fuel levels f4 and f7 are not adjacent, fuel levels f5 and f0 are not adjacent, fuel levels f5 and f1 are not adjacent, fuel levels f5 and f4 are not adjacent, fuel levels f6 and f0 are not adjacent, fuel levels f6 and f2 are not adjacent, fuel levels f7 and f1 are not adjacent, fuel levels f7 and f6 are not adjacent, locations l0 and l1 are not connected, space s0 is not adjacent to space s1, space s1 is not adjacent to space s0, and there is no connection between locations l1 and l0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also found at l1, cargo c2 is situated at l1, cargo c3 is located at l0, cargo c4 is present at l1, cargo c5 is situated at l0, cargo c6 is located at l1, cargo c7 is found at l1, cargo c8 is situated at l0, fuel f5 is available at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, locations l0 and l1 are connected, location l1 is also connected to location l0, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is located at l0, vehicle v1 has space s1 and vehicle v1 is located at l1."}
{"question_id": "2e930469-03c9-42d3-941c-304ad0c013f2", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_13", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, is it True or False that fuel-levels f2 and f3 are neighbors?", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is present at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l1 has a fuel-level of f6, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1 and vehicle v0 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: at location l1, cargo c1 is loaded into vehicle v0 with spaces s1 and s0 to achieve the current state. In this state, is it True or False that fuel-levels f2 and f3 are adjacent?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, cargo c2, and cargo c3 are all situated at location l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all located at l1. Fuel f7 is present at location l0. The fuel levels are connected in the following order: f1 is adjacent to f2, f2 is adjacent to f3, f4 is adjacent to f5, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, as are f3 and f4. Locations l0 and l1 are connected, with l1 having a fuel level of f6. It is also noted that l1 is connected to l0. Spaces s0 and s1 are adjacent, and vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "b0f00bd8-0cff-4c7f-b054-d0b5a3968aba", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_15", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1 and at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, is it True or False that fuel level f3 does not neighbour fuel level f7?", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is at location l1, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 has space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, where cargo c0 is loaded onto vehicle v0, utilizing spaces s1 and s0. Then, vehicle v0 moves back to location l0, which has fuel levels f7 and f6, and cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. Next, vehicle v0 travels from location l0, with fuel levels f4 and f3, to location l1, where cargo c1 is loaded onto vehicle v0, occupying spaces s1 and s0. Subsequently, vehicle v0 moves from location l1, which has fuel levels f6 and f5, to location l0, and at location l0, cargo c1 is unloaded from vehicle v0, releasing spaces s0 and s1. Then, vehicle v0 moves from location l0, with fuel levels f3 and f2, to location l1, and at location l1, cargo c2 is loaded onto vehicle v0, utilizing spaces s1 and s0, resulting in the current state. In this state, is it True or False that fuel level f3 does not neighbour fuel level f7?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is also found at l1, cargo c2 is situated at l1, cargo c3 is positioned at l0, cargo c4 is present at l1, cargo c5 is located at l0, cargo c6 is situated at l1, cargo c7 is positioned at l1, cargo c8 is situated at l0, fuel f5 is available at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, locations l0 and l1 are connected, location l1 is also connected to location l0, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is positioned at location l0, vehicle v1 has space s1 and is situated at location l1."}
{"question_id": "74bc256a-d447-4498-8a50-c77217814804", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_21", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, at location l1, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, at location l0, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1 and cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: fuel level f0 does not neighbour fuel level f2, fuel level f0 does not neighbour fuel level f3, fuel level f0 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f5, fuel level f2 does not neighbour fuel level f1, fuel level f2 does not neighbour fuel level f5, fuel level f2 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f5, fuel level f4 does not neighbour fuel level f0, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f6, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f2, fuel level f5 does not neighbour fuel level f3, fuel level f5 does not neighbour fuel level f4, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f0, fuel level f7 does not neighbour fuel level f2, fuel level f7 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f5, fuel-levels f0 and f4 are not neighbors, fuel-levels f0 and f6 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f3 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f6 are not neighbors, fuel-levels f3 and f7 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f7 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f1 are not neighbors, fuel-levels f6 and f5 are not neighbors, fuel-levels f7 and f1 are not neighbors, fuel-levels f7 and f3 are not neighbors, fuel-levels f7 and f6 are not neighbors and space s1 does not neighbour space s0?", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is at location l1, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 has space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 moves from location l0, which has fuel levels f5 and f4, to location l1. Upon arrival at l1, cargo c0 is loaded into vehicle v0, occupying spaces s1 and s0. Vehicle v0 then returns to location l0, which now has fuel levels f7 and f6. At l0, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. Next, vehicle v0 moves from l0, which has fuel levels f4 and f3, back to location l1. At l1, cargo c1 is loaded into vehicle v0, occupying spaces s1 and s0. Vehicle v0 then moves from l1, which has fuel levels f6 and f5, to location l0. At l0, cargo c1 is unloaded from vehicle v0, freeing up spaces s0 and s1. Finally, vehicle v0 moves from l0, which has fuel levels f3 and f2, to location l1, where cargo c2 is loaded into vehicle v0, occupying spaces s1 and s0, resulting in the current state. In this state, are the following properties that involve negations True or False: fuel level f0 is not adjacent to fuel level f2, fuel level f0 is not adjacent to fuel level f3, fuel level f0 is not adjacent to fuel level f5, fuel level f1 is not adjacent to fuel level f0, fuel level f1 is not adjacent to fuel level f5, fuel level f2 is not adjacent to fuel level f1, fuel level f2 is not adjacent to fuel level f5, fuel level f2 is not adjacent to fuel level f7, fuel level f3 is not adjacent to fuel level f5, fuel level f4 is not adjacent to fuel level f0, fuel level f4 is not adjacent to fuel level f1, fuel level f4 is not adjacent to fuel level f2, fuel level f4 is not adjacent to fuel level f6, fuel level f5 is not adjacent to fuel level f0, fuel level f5 is not adjacent to fuel level f2, fuel level f5 is not adjacent to fuel level f3, fuel level f5 is not adjacent to fuel level f4, fuel level f6 is not adjacent to fuel level f2, fuel level f6 is not adjacent to fuel level f3, fuel level f6 is not adjacent to fuel level f4, fuel level f7 is not adjacent to fuel level f0, fuel level f7 is not adjacent to fuel level f2, fuel level f7 is not adjacent to fuel level f4, fuel level f7 is not adjacent to fuel level f5, fuel levels f0 and f4 are not adjacent, fuel levels f0 and f6 are not adjacent, fuel levels f0 and f7 are not adjacent, fuel levels f1 and f3 are not adjacent, fuel levels f1 and f4 are not adjacent, fuel levels f1 and f6 are not adjacent, fuel levels f1 and f7 are not adjacent, fuel levels f2 and f0 are not adjacent, fuel levels f2 and f4 are not adjacent, fuel levels f2 and f6 are not adjacent, fuel levels f3 and f0 are not adjacent, fuel levels f3 and f1 are not adjacent, fuel levels f3 and f2 are not adjacent, fuel levels f3 and f6 are not adjacent, fuel levels f3 and f7 are not adjacent, fuel levels f4 and f3 are not adjacent, fuel levels f4 and f7 are not adjacent, fuel levels f5 and f1 are not adjacent, fuel levels f5 and f7 are not adjacent, fuel levels f6 and f0 are not adjacent, fuel levels f6 and f1 are not adjacent, fuel levels f6 and f5 are not adjacent, fuel levels f7 and f1 are not adjacent, fuel levels f7 and f3 are not adjacent, fuel levels f7 and f6 are not adjacent, and space s1 is not adjacent to space s0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is found at l1, cargo c2 is situated at l1, cargo c3 is positioned at l0, cargo c4 is located at l1, cargo c5 is positioned at l0, cargo c6 is situated at l1, cargo c7 is located at l1, cargo c8 is positioned at l0, fuel f5 is available at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, locations l0 and l1 are linked, location l1 is linked to location l0, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is positioned at location l0, vehicle v1 has space s1 and vehicle v1 is located at location l1."}
{"question_id": "b25e2f5d-c26e-4e5a-ac89-738c4d95ebc6", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_16", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, is it True or False that fuel level f4 does not neighbour fuel level f5?", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is present at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l1 has a fuel-level of f6, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1 and vehicle v0 is at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, is it True or False that fuel level f5 is not adjacent to fuel level f4?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, c2, and c3 are all situated at l0. Additionally, cargo c4, c5, c6, c7, c8, and c9 are all located at l1. Fuel f7 is present at l0. The fuel levels are connected in the following order: f1 is adjacent to f2, f2 is adjacent to f3, f4 is adjacent to f5, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, as are f3 and f4. Locations l0 and l1 are connected, with l1 having a fuel level of f6. It is also noted that l1 is connected to l0. Spaces s0 and s1 are adjacent, and vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "3d3dfbe3-fe92-47ad-9ec7-9f1f08c046aa", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_19", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: fuel level f0 neighbors fuel level f1, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel-levels f1 and f2 are neighbors, fuel-levels f2 and f3 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, space s0 neighbors space s1 and spaces s1 and s2 are neighbors?", "answer": "True", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is at location l0, cargo c4 is at location l0, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is present at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel level f0 neighbors fuel level f1, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel-levels f1 and f2 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has fuel f3, space s1 neighbors space s2, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is present at location l1, vehicle v1 contains space s2 and vehicle v1 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: vehicle v0 moves from location l1, which has fuel levels f3 and f2, to location l0, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: fuel level f0 is adjacent to fuel level f1, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel levels f1 and f2 are adjacent, fuel levels f2 and f3 are adjacent, locations l0 and l1 are connected, location l1 is connected to location l0, space s0 is adjacent to space s1, and spaces s1 and s2 are adjacent?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is located at l1, cargo c10 is found at l1, cargo c2 is situated at l0, cargo c3 is situated at l0, cargo c4 is situated at l0, cargo c5 is located at l1, cargo c6 is situated at l0, cargo c7 is found at l0, cargo c8 is found at l1, cargo c9 is situated at l1, fuel level f0 is adjacent to fuel level f1, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel levels f1 and f2 are adjacent, locations l0 and l1 are linked, location l0 has a fuel level of f4, location l1 has a fuel level of f3, space s1 is adjacent to space s2, spaces s0 and s1 are adjacent, a connection exists between locations l1 and l0, vehicle v0 occupies space s2, vehicle v0 is currently at location l1, vehicle v1 contains space s2 and vehicle v1 is currently at location l1."}
{"question_id": "691afad4-b90a-4b11-a42e-3d6c809b7057", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_10", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l1 from location l0 that has fuel level f5 and f4, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f7 and f6, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, at location l1, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2 and cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, is it True or False that vehicle v1 contains cargo c2?", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is at location l1, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 has space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: vehicle v0 relocates from location l0, which has fuel levels f5 and f4, to location l1, where cargo c0 is loaded into vehicle v0, utilizing spaces s1 and s0. Then, vehicle v0 moves back to location l0 from location l1, which now has fuel levels f7 and f6, and cargo c0 is unloaded from vehicle v0, occupying spaces s0 and s1. Next, vehicle v0 travels from location l0, with fuel levels f4 and f3, to location l1, where cargo c1 is loaded into vehicle v0, using spaces s1 and s0. Subsequently, vehicle v0 moves to location l0 from location l1, which has fuel levels f6 and f5, and cargo c1 is unloaded from vehicle v0, occupying spaces s0 and s1. Finally, vehicle v0 relocates to location l1 from location l0, which has fuel levels f3 and f2, and cargo c2 is loaded into vehicle v0, utilizing spaces s1 and s0, resulting in the current state. In this state, is it True or False that vehicle v1 contains cargo c2?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is found at l1, cargo c2 is situated at l1, cargo c3 is positioned at l0, cargo c4 is located at l1, cargo c5 is positioned at l0, cargo c6 is situated at l1, cargo c7 is located at l1, cargo c8 is positioned at l0, fuel f5 is available at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, locations l0 and l1 are connected, location l1 is connected to location l0, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is positioned at location l0, vehicle v1 has space s1 and vehicle v1 is located at location l1."}
{"question_id": "a05d2071-7b97-4155-9a70-9fb7e3ec1d80", "domain_name": "mystery", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_16", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1 to reach the current state. In this state, is it True or False that fuel level f6 does not neighbour fuel level f7?", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is situated at location l1, cargo c1 is present at location l1, cargo c2 is at location l1, cargo c3 is situated at location l0, cargo c4 is present at location l1, cargo c5 is situated at location l0, cargo c6 is situated at location l1, cargo c7 is at location l1, cargo c8 is at location l0, fuel f5 exists in location l0, fuel f7 exists in location l1, fuel level f0 neighbors fuel level f1, fuel level f1 neighbors fuel level f2, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel-levels f2 and f3 are neighbors, location l0 and location l1 are connected, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1, vehicle v0 is situated at location l0, vehicle v1 has space s1 and vehicle v1 is present at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: vehicle v0 moves from location l0 with fuel levels f5 and f4 to location l1, resulting in the current state. In this state, is it True or False that fuel level f6 is not adjacent to fuel level f7?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, cargo c1 is found at l1, cargo c2 is situated at l1, cargo c3 is positioned at l0, cargo c4 is located at l1, cargo c5 is positioned at l0, cargo c6 is situated at l1, cargo c7 is found at l1, cargo c8 is positioned at l0, fuel f5 is available at l0, fuel f7 is available at l1, fuel level f0 is adjacent to fuel level f1, fuel level f1 is adjacent to fuel level f2, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel level f5 is adjacent to fuel level f6, fuel level f6 is adjacent to fuel level f7, fuel levels f2 and f3 are adjacent, locations l0 and l1 are linked, location l1 is linked to location l0, spaces s0 and s1 are adjacent, vehicle v0 contains space s1, vehicle v0 is positioned at location l0, vehicle v1 has space s1 and vehicle v1 is located at location l1."}
{"question_id": "de71d52b-e6a2-438d-82a8-c67a258cc8a3", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_21", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1 and cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: fuel level f0 does not neighbour fuel level f3, fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f5, fuel level f0 does not neighbour fuel level f8, fuel level f1 does not neighbour fuel level f0, fuel level f1 does not neighbour fuel level f3, fuel level f1 does not neighbour fuel level f8, fuel level f2 does not neighbour fuel level f4, fuel level f2 does not neighbour fuel level f5, fuel level f2 does not neighbour fuel level f6, fuel level f2 does not neighbour fuel level f8, fuel level f4 does not neighbour fuel level f3, fuel level f4 does not neighbour fuel level f6, fuel level f4 does not neighbour fuel level f8, fuel level f5 does not neighbour fuel level f7, fuel level f5 does not neighbour fuel level f8, fuel level f6 does not neighbour fuel level f0, fuel level f6 does not neighbour fuel level f1, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f5, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f4, fuel level f8 does not neighbour fuel level f0, fuel level f8 does not neighbour fuel level f2, fuel level f8 does not neighbour fuel level f5, fuel level f8 does not neighbour fuel level f6, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f6 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f1 and f6 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f2 and f0 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f7 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f3 and f6 are not neighbors, fuel-levels f3 and f7 are not neighbors, fuel-levels f3 and f8 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f1 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f5 and f0 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f2 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f4 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f6 and f8 are not neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f2 are not neighbors, fuel-levels f7 and f5 are not neighbors, fuel-levels f7 and f6 are not neighbors, fuel-levels f8 and f1 are not neighbors, fuel-levels f8 and f3 are not neighbors, fuel-levels f8 and f4 are not neighbors, fuel-levels f8 and f7 are not neighbors and spaces s1 and s0 are not neighbors?", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is present at location l0, cargo c1 is situated at location l0, cargo c2 is at location l1, cargo c3 is situated at location l1, cargo c4 is present at location l1, cargo c5 is at location l1, cargo c6 is present at location l0, cargo c7 is present at location l0, cargo c8 is situated at location l1, cargo c9 is situated at location l1, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 has a fuel-level of f3, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, which has fuel levels f8 and f7, to location l0. At location l0, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1. Subsequently, cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0, at location l0. Vehicle v0 then moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1. Next, cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0, at location l1. Vehicle v0 then moves from location l1, which has fuel levels f7 and f6, to location l0. At location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0, resulting in the current state. In this state, are the following properties that involve negations True or False: fuel level f0 is not adjacent to fuel level f3, fuel level f0 is not adjacent to fuel level f4, fuel level f0 is not adjacent to fuel level f5, fuel level f0 is not adjacent to fuel level f8, fuel level f1 is not adjacent to fuel level f0, fuel level f1 is not adjacent to fuel level f3, fuel level f1 is not adjacent to fuel level f8, fuel level f2 is not adjacent to fuel level f4, fuel level f2 is not adjacent to fuel level f5, fuel level f2 is not adjacent to fuel level f6, fuel level f2 is not adjacent to fuel level f8, fuel level f4 is not adjacent to fuel level f3, fuel level f4 is not adjacent to fuel level f6, fuel level f4 is not adjacent to fuel level f8, fuel level f5 is not adjacent to fuel level f7, fuel level f5 is not adjacent to fuel level f8, fuel level f6 is not adjacent to fuel level f0, fuel level f6 is not adjacent to fuel level f1, fuel level f6 is not adjacent to fuel level f3, fuel level f6 is not adjacent to fuel level f5, fuel level f7 is not adjacent to fuel level f1, fuel level f7 is not adjacent to fuel level f3, fuel level f7 is not adjacent to fuel level f4, fuel level f8 is not adjacent to fuel level f0, fuel level f8 is not adjacent to fuel level f2, fuel level f8 is not adjacent to fuel level f5, fuel level f8 is not adjacent to fuel level f6, fuel levels f0 and f2 are not adjacent, fuel levels f0 and f6 are not adjacent, fuel levels f0 and f7 are not adjacent, fuel levels f1 and f4 are not adjacent, fuel levels f1 and f5 are not adjacent, fuel levels f1 and f6 are not adjacent, fuel levels f1 and f7 are not adjacent, fuel levels f2 and f0 are not adjacent, fuel levels f2 and f1 are not adjacent, fuel levels f2 and f7 are not adjacent, fuel levels f3 and f0 are not adjacent, fuel levels f3 and f1 are not adjacent, fuel levels f3 and f2 are not adjacent, fuel levels f3 and f5 are not adjacent, fuel levels f3 and f6 are not adjacent, fuel levels f3 and f7 are not adjacent, fuel levels f3 and f8 are not adjacent, fuel levels f4 and f0 are not adjacent, fuel levels f4 and f1 are not adjacent, fuel levels f4 and f2 are not adjacent, fuel levels f4 and f7 are not adjacent, fuel levels f5 and f0 are not adjacent, fuel levels f5 and f1 are not adjacent, fuel levels f5 and f2 are not adjacent, fuel levels f5 and f3 are not adjacent, fuel levels f5 and f4 are not adjacent, fuel levels f6 and f2 are not adjacent, fuel levels f6 and f4 are not adjacent, fuel levels f6 and f8 are not adjacent, fuel levels f7 and f0 are not adjacent, fuel levels f7 and f2 are not adjacent, fuel levels f7 and f5 are not adjacent, fuel levels f7 and f6 are not adjacent, fuel levels f8 and f1 are not adjacent, fuel levels f8 and f3 are not adjacent, fuel levels f8 and f4 are not adjacent, fuel levels f8 and f7 are not adjacent, and spaces s1 and s0 are not adjacent?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also found at l0, cargo c2 is positioned at l1, cargo c3 is situated at l1, cargo c4 is located at l1, cargo c5 is positioned at l1, cargo c6 is found at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is also situated at l1, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, location l0 is equipped with fuel level f3, location l1 is equipped with fuel level f8, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, a connection also exists between locations l1 and l0, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l1, vehicle v1 is equipped with space s1 and vehicle v1 is currently at location l0."}
{"question_id": "1d0acd51-8d98-436b-a5c3-f27909ec3458", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_13", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, at location l0, cargo c0 is loaded in vehicle v0 with spaces s2 and s1, at location l0, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l1, at location l1, cargo c2 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves to location l0 from location l1 that has fuel level f2 and f1, cargo c10 is unloaded from vehicle v0 with space s1 and space s2 at location l0 and cargo c3 is loaded in vehicle v0 with space s2 and space s1 at location l0 to reach the current state. In this state, is it True or False that space s0 neighbors space s1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is at location l0, cargo c4 is at location l0, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is present at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel level f0 neighbors fuel level f1, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel-levels f1 and f2 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has fuel f3, space s1 neighbors space s2, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is present at location l1, vehicle v1 contains space s2 and vehicle v1 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0. Upon arrival at l0, cargo c0 is loaded into vehicle v0, occupying spaces s2 and s1. Additionally, at location l0, cargo c2 is loaded into vehicle v0, utilizing spaces s1 and s0. Vehicle v0 then moves from location l0 to location l1, which has fuel levels f4 and f3. At location l1, cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1. Subsequently, cargo c10 is loaded into vehicle v0 at location l1, occupying spaces s1 and s0. Furthermore, at location l1, cargo c2 is unloaded from vehicle v0, releasing spaces s0 and s1. Vehicle v0 then moves from location l1 back to location l0, which has fuel levels f2 and f1. At location l0, cargo c10 is unloaded from vehicle v0, vacating spaces s1 and s2, and cargo c3 is loaded into vehicle v0, occupying spaces s2 and s1. In this resulting state, is it True or False that space s0 is adjacent to space s1?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, while cargo c1 is found at l1. Additionally, cargo c10 is also present at l1. Cargos c2, c3, and c4 are all situated at l0, and cargo c6 and c7 are also located there. Cargo c5 is situated at l1, and cargos c8 and c9 are also present at l1. \n\nFuel levels f0 and f1 are adjacent, as are fuel levels f2 and f3, f3 and f4, and f4 and f5. Furthermore, fuel levels f1 and f2 are also neighboring. Locations l0 and l1 are interconnected. Location l0 has a fuel level of f4, whereas location l1 has a fuel level of f3. \n\nSpaces s1 and s2 are adjacent, and spaces s0 and s1 are also neighboring. Locations l0 and l1 are connected. Vehicle v0 is equipped with space s2 and is currently at location l1. Vehicle v1 also contains space s2 and is located at l1."}
{"question_id": "e1836f20-3f1e-47a8-a4b0-411c23cd8e8d", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_16", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, at location l1, cargo c4 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l0 from location l1 that has fuel level f5 and f4, cargo c4 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, at location l1, cargo c6 is loaded in vehicle v0 with spaces s1 and s0 and vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: cargo c0 is not at location l0cargo c0 is not present at location l0, cargo c0 is not at location l1cargo c0 is not present at location l1, cargo c1 is not at location l0cargo c1 is not present at location l0, cargo c1 is not at location l1cargo c1 is not present at location l1, cargo c1 is not located in vehicle v0, cargo c10 is not located in vehicle v0, cargo c10 is not situated at location l0, cargo c10 is not situated at location l1, cargo c2 is not at location l0cargo c2 is not present at location l0, cargo c2 is not at location l1cargo c2 is not present at location l1, cargo c2 is not in vehicle v0, cargo c3 is not situated at location l0, cargo c3 is not situated at location l1, cargo c4 is not situated at location l0, cargo c4 is not situated at location l1, cargo c5 is not at location l0cargo c5 is not present at location l0, cargo c5 is not at location l1cargo c5 is not present at location l1, cargo c5 is not in vehicle v0, cargo c6 is not at location l0cargo c6 is not present at location l0, cargo c6 is not located in vehicle v0, cargo c6 is not situated at location l1, cargo c7 is not at location l0cargo c7 is not present at location l0, cargo c7 is not at location l1cargo c7 is not present at location l1, cargo c7 is not located in vehicle v0, cargo c8 is not at location l0cargo c8 is not present at location l0, cargo c8 is not at location l1cargo c8 is not present at location l1, cargo c9 is not at location l0cargo c9 is not present at location l0, cargo c9 is not at location l1cargo c9 is not present at location l1, cargo c9 is not located in vehicle v0, fuel f0 does not exist in location l0, fuel f1 does not exist in location l0, fuel f2 does not exist in location l0, fuel f2 does not exist in location l1, fuel f3 does not exist in location l1, fuel f8 does not exist in location l1, location l0 does not have a fuel-level of f3, location l0 does not have a fuel-level of f5, location l0 does not have a fuel-level of f6, location l0 does not have a fuel-level of f7, location l0 does not have a fuel-level of f8, location l0 does not have fuel f4, location l1 does not have a fuel-level of f1, location l1 does not have a fuel-level of f4, location l1 does not have fuel f0, location l1 does not have fuel f5, location l1 does not have fuel f6, location l1 does not have fuel f7, vehicle v0 does not contain cargo c0, vehicle v0 does not contain cargo c3, vehicle v0 does not contain cargo c4, vehicle v0 does not contain cargo c8, vehicle v0 does not contain space s1, vehicle v0 does not have space s0, vehicle v0 is not present at location l0 and vehicle v0 is not present at location l1?", "answer": "False", "plan_length": 10, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is present at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l1 has a fuel-level of f6, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1 and vehicle v0 is at location l1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c1 is loaded into vehicle v0, which has spaces s1 and s0, at location l1, then vehicle v0 moves from location l1, which has fuel levels f6 and f5, to location l0, where cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, after which vehicle v0 moves from location l0, which has fuel levels f7 and f6, back to location l1. At location l1, cargo c4 is loaded into vehicle v0 with spaces s1 and s0, then vehicle v0 moves from location l1, which has fuel levels f5 and f4, to location l0, where cargo c4 is unloaded from vehicle v0 with spaces s0 and s1. Subsequently, vehicle v0 moves from location l0, which has fuel levels f6 and f5, back to location l1. At location l1, cargo c6 is loaded into vehicle v0 with spaces s1 and s0, and vehicle v0 moves from location l1, which has fuel levels f4 and f3, to location l0, resulting in the current state. In this state, are the following properties that involve negations True or False: cargo c0 is not located at location l0, cargo c0 is not present at location l0, cargo c0 is not located at location l1, cargo c0 is not present at location l1, cargo c1 is not located at location l0, cargo c1 is not present at location l0, cargo c1 is not located at location l1, cargo c1 is not present at location l1, cargo c1 is not inside vehicle v0, cargo c10 is not inside vehicle v0, cargo c10 is not situated at location l0, cargo c10 is not situated at location l1, cargo c2 is not located at location l0, cargo c2 is not present at location l0, cargo c2 is not located at location l1, cargo c2 is not present at location l1, cargo c2 is not inside vehicle v0, cargo c3 is not situated at location l0, cargo c3 is not situated at location l1, cargo c4 is not situated at location l0, cargo c4 is not situated at location l1, cargo c5 is not located at location l0, cargo c5 is not present at location l0, cargo c5 is not located at location l1, cargo c5 is not present at location l1, cargo c5 is not inside vehicle v0, cargo c6 is not located at location l0, cargo c6 is not present at location l0, cargo c6 is not inside vehicle v0, cargo c6 is not situated at location l1, cargo c7 is not located at location l0, cargo c7 is not present at location l0, cargo c7 is not located at location l1, cargo c7 is not present at location l1, cargo c7 is not inside vehicle v0, cargo c8 is not located at location l0, cargo c8 is not present at location l0, cargo c8 is not located at location l1, cargo c8 is not present at location l1, cargo c9 is not located at location l0, cargo c9 is not present at location l0, cargo c9 is not located at location l1, cargo c9 is not present at location l1, cargo c9 is not inside vehicle v0, fuel f0 does not exist at location l0, fuel f1 does not exist at location l0, fuel f2 does not exist at location l0, fuel f2 does not exist at location l1, fuel f3 does not exist at location l1, fuel f8 does not exist at location l1, location l0 does not have a fuel level of f3, location l0 does not have a fuel level of f5, location l0 does not have a fuel level of f6, location l0 does not have a fuel level of f7, location l0 does not have a fuel level of f8, location l0 does not have fuel f4, location l1 does not have a fuel level of f1, location l1 does not have a fuel level of f4, location l1 does not have fuel f0, location l1 does not have fuel f5, location l1 does not have fuel f6, location l1 does not have fuel f7, vehicle v0 does not contain cargo c0, vehicle v0 does not contain cargo c3, vehicle v0 does not contain cargo c4, vehicle v0 does not contain cargo c8, vehicle v0 does not contain space s1, vehicle v0 does not have space s0, vehicle v0 is not present at location l0, and vehicle v0 is not present at location l1?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, cargo c2, and cargo c3 are all situated at location l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all located at l1. Fuel f7 is present at location l0. The fuel levels are arranged in a sequence where f1 is adjacent to f2, f2 is adjacent to f3, f4 is adjacent to f5, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, as are f3 and f4. Locations l0 and l1 are connected, with l1 having a fuel level of f6 and also being connected to l0. Spaces s0 and s1 are adjacent, and vehicle v0, which contains space s1, is currently at location l1."}
{"question_id": "1f077c6e-9ee7-4811-993f-728e2ff11859", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_21", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, at location l0, cargo c0 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves to location l1 from location l0 that has fuel level f3 and f2, at location l1, cargo c0 is unloaded from vehicle v0 with spaces s0 and s1, at location l1, cargo c3 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, at location l0, cargo c3 is unloaded from vehicle v0 with spaces s0 and s1, at location l0, cargo c1 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l0 which has fuel-levels f2 and f1 to location l1, at location l1, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, cargo c5 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f6 and f5 to location l0, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f1 and f0 to location l1, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1 and cargo c9 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: fuel level f0 does not neighbour fuel level f4, fuel level f0 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f3, fuel level f1 does not neighbour fuel level f4, fuel level f1 does not neighbour fuel level f5, fuel level f1 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f8, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f8, fuel level f3 does not neighbour fuel level f0, fuel level f3 does not neighbour fuel level f5, fuel level f3 does not neighbour fuel level f7, fuel level f3 does not neighbour fuel level f8, fuel level f4 does not neighbour fuel level f1, fuel level f4 does not neighbour fuel level f2, fuel level f4 does not neighbour fuel level f6, fuel level f4 does not neighbour fuel level f7, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f2, fuel level f5 does not neighbour fuel level f4, fuel level f5 does not neighbour fuel level f7, fuel level f6 does not neighbour fuel level f0, fuel level f6 does not neighbour fuel level f1, fuel level f6 does not neighbour fuel level f2, fuel level f6 does not neighbour fuel level f5, fuel level f6 does not neighbour fuel level f8, fuel level f7 does not neighbour fuel level f0, fuel level f7 does not neighbour fuel level f1, fuel level f7 does not neighbour fuel level f4, fuel level f8 does not neighbour fuel level f0, fuel level f8 does not neighbour fuel level f3, fuel level f8 does not neighbour fuel level f5, fuel level f8 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f7, fuel-levels f0 and f2 are not neighbors, fuel-levels f0 and f3 are not neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f0 and f8 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f7 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f5 are not neighbors, fuel-levels f2 and f6 are not neighbors, fuel-levels f2 and f7 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f6 are not neighbors, fuel-levels f4 and f0 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f4 and f8 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f8 are not neighbors, fuel-levels f6 and f3 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f7 and f2 are not neighbors, fuel-levels f7 and f3 are not neighbors, fuel-levels f7 and f5 are not neighbors, fuel-levels f7 and f6 are not neighbors, fuel-levels f8 and f1 are not neighbors, fuel-levels f8 and f2 are not neighbors, fuel-levels f8 and f4 are not neighbors and space s1 does not neighbour space s0?", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l0, cargo c1 is situated at location l0, cargo c2 is at location l1, cargo c3 is situated at location l1, cargo c4 is present at location l1, cargo c5 is at location l1, cargo c6 is present at location l0, cargo c7 is present at location l0, cargo c8 is situated at location l1, cargo c9 is situated at location l1, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 has a fuel-level of f3, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, which has fuel levels f8 and f7, to location l0. At location l0, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1, and then cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1, and then cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l1, which has fuel levels f7 and f6, to location l0. At location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, and then cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f2 and f1, to location l1. At location l1, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1, and then cargo c5 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l1, which has fuel levels f6 and f5, to location l0. At location l0, cargo c5 is unloaded from vehicle v0, which has spaces s0 and s1, and then cargo c7 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f1 and f0, to location l1. At location l1, cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1, and then cargo c9 is loaded onto vehicle v0, which has spaces s1 and s0, resulting in the current state. In this state, are the following properties that involve negations true or false: fuel level f0 is not adjacent to fuel level f4, fuel level f0 is not adjacent to fuel level f6, fuel level f1 is not adjacent to fuel level f3, fuel level f1 is not adjacent to fuel level f4, fuel level f1 is not adjacent to fuel level f5, fuel level f1 is not adjacent to fuel level f6, fuel level f1 is not adjacent to fuel level f8, fuel level f2 is not adjacent to fuel level f0, fuel level f2 is not adjacent to fuel level f8, fuel level f3 is not adjacent to fuel level f0, fuel level f3 is not adjacent to fuel level f5, fuel level f3 is not adjacent to fuel level f7, fuel level f3 is not adjacent to fuel level f8, fuel level f4 is not adjacent to fuel level f1, fuel level f4 is not adjacent to fuel level f2, fuel level f4 is not adjacent to fuel level f6, fuel level f4 is not adjacent to fuel level f7, fuel level f5 is not adjacent to fuel level f0, fuel level f5 is not adjacent to fuel level f2, fuel level f5 is not adjacent to fuel level f4, fuel level f5 is not adjacent to fuel level f7, fuel level f6 is not adjacent to fuel level f0, fuel level f6 is not adjacent to fuel level f1, fuel level f6 is not adjacent to fuel level f2, fuel level f6 is not adjacent to fuel level f5, fuel level f6 is not adjacent to fuel level f8, fuel level f7 is not adjacent to fuel level f0, fuel level f7 is not adjacent to fuel level f1, fuel level f7 is not adjacent to fuel level f4, fuel level f8 is not adjacent to fuel level f0, fuel level f8 is not adjacent to fuel level f3, fuel level f8 is not adjacent to fuel level f5, fuel level f8 is not adjacent to fuel level f6, fuel level f8 is not adjacent to fuel level f7, fuel levels f0 and f2 are not adjacent, fuel levels f0 and f3 are not adjacent, fuel levels f0 and f5 are not adjacent, fuel levels f0 and f7 are not adjacent, fuel levels f0 and f8 are not adjacent, fuel levels f1 and f0 are not adjacent, fuel levels f1 and f7 are not adjacent, fuel levels f2 and f1 are not adjacent, fuel levels f2 and f4 are not adjacent, fuel levels f2 and f5 are not adjacent, fuel levels f2 and f6 are not adjacent, fuel levels f2 and f7 are not adjacent, fuel levels f3 and f1 are not adjacent, fuel levels f3 and f2 are not adjacent, fuel levels f3 and f6 are not adjacent, fuel levels f4 and f0 are not adjacent, fuel levels f4 and f3 are not adjacent, fuel levels f4 and f8 are not adjacent, fuel levels f5 and f1 are not adjacent, fuel levels f5 and f3 are not adjacent, fuel levels f5 and f8 are not adjacent, fuel levels f6 and f3 are not adjacent, fuel levels f6 and f4 are not adjacent, fuel levels f7 and f2 are not adjacent, fuel levels f7 and f3 are not adjacent, fuel levels f7 and f5 are not adjacent, fuel levels f7 and f6 are not adjacent, fuel levels f8 and f1 are not adjacent, fuel levels f8 and f2 are not adjacent, fuel levels f8 and f4 are not adjacent, and space s1 is not adjacent to space s0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also found at l0, cargo c2 is positioned at l1, cargo c3 is situated at l1, cargo c4 is located at l1, cargo c5 is positioned at l1, cargo c6 is found at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is situated at l1, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, location l0 is equipped with fuel level f3, location l1 is equipped with fuel level f8, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, a connection exists between locations l1 and l0, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l1, vehicle v1 is equipped with space s1 and vehicle v1 is currently at location l0."}
{"question_id": "6d8fea56-2fad-43ef-973d-8054ddde8969", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_16", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location l1, cargo c2 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f8 and f7 to location l0, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c0 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves from location l0 which has fuel-levels f3 and f2 to location l1, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c3 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f7 and f6 to location l0, cargo c3 is unloaded from vehicle v0 with space s0 and space s1 at location l0, cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f2 and f1, cargo c1 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c5 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c5 is unloaded from vehicle v0 with spaces s0 and s1, cargo c7 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f1 and f0, at location l1, cargo c7 is unloaded from vehicle v0 with spaces s0 and s1 and at location l1, cargo c9 is loaded in vehicle v0 with spaces s1 and s0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: cargo c0 is not at location l1cargo c0 is not present at location l1, cargo c0 is not located in vehicle v1, cargo c0 is not situated at location l0, cargo c1 is not at location l1cargo c1 is not present at location l1, cargo c1 is not in vehicle v1, cargo c1 is not situated at location l0, cargo c2 is not situated at location l0, cargo c2 is not situated at location l1, cargo c3 is not at location l0cargo c3 is not present at location l0, cargo c3 is not in vehicle v0, cargo c3 is not situated at location l1, cargo c4 is not at location l0cargo c4 is not present at location l0, cargo c4 is not in vehicle v1, cargo c4 is not located in vehicle v0, cargo c4 is not situated at location l1, cargo c5 is not at location l1cargo c5 is not present at location l1, cargo c5 is not located in vehicle v0, cargo c5 is not located in vehicle v1, cargo c5 is not situated at location l0, cargo c6 is not at location l1cargo c6 is not present at location l1, cargo c6 is not in vehicle v0, cargo c6 is not situated at location l0, cargo c7 is not situated at location l0, cargo c7 is not situated at location l1, cargo c8 is not at location l1cargo c8 is not present at location l1, cargo c8 is not located in vehicle v0, cargo c8 is not located in vehicle v1, cargo c8 is not situated at location l0, cargo c9 is not at location l1cargo c9 is not present at location l1, cargo c9 is not located in vehicle v0, cargo c9 is not located in vehicle v1, cargo c9 is not situated at location l0, fuel f0 does not exist in location l0, fuel f1 does not exist in location l1, fuel f2 does not exist in location l1, fuel f5 does not exist in location l0, fuel f6 does not exist in location l1, fuel f8 does not exist in location l1, location l0 does not have a fuel-level of f4, location l0 does not have a fuel-level of f8, location l0 does not have fuel f1, location l0 does not have fuel f2, location l0 does not have fuel f3, location l0 does not have fuel f6, location l0 does not have fuel f7, location l1 does not have a fuel-level of f5, location l1 does not have fuel f0, location l1 does not have fuel f3, location l1 does not have fuel f4, location l1 does not have fuel f7, vehicle v0 does not contain cargo c0, vehicle v0 does not contain cargo c1, vehicle v0 does not contain cargo c2, vehicle v0 does not contain cargo c7, vehicle v0 does not contain space s1, vehicle v0 does not have space s0, vehicle v0 is not at location l0, vehicle v0 is not present at location l1, vehicle v1 does not contain cargo c2, vehicle v1 does not contain cargo c3, vehicle v1 does not contain cargo c6, vehicle v1 does not contain cargo c7, vehicle v1 does not have space s0, vehicle v1 does not have space s1, vehicle v1 is not at location l1 and vehicle v1 is not situated at location l0?", "answer": "False", "plan_length": 19, "initial_state_nl": "Cargo c0 is present at location l0, cargo c1 is situated at location l0, cargo c2 is at location l1, cargo c3 is situated at location l1, cargo c4 is present at location l1, cargo c5 is at location l1, cargo c6 is present at location l0, cargo c7 is present at location l0, cargo c8 is situated at location l1, cargo c9 is situated at location l1, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 has a fuel-level of f3, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location l1, cargo c2 is loaded onto vehicle v0, which has spaces s1 and s0, and then vehicle v0 moves from location l1, which has fuel levels f8 and f7, to location l0. At location l0, cargo c2 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c0 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f3 and f2, to location l1. At location l1, cargo c0 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c3 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l1, which has fuel levels f7 and f6, to location l0. At location l0, cargo c3 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c1 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f2 and f1, to location l1. At location l1, cargo c1 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c5 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l1, which has fuel levels f6 and f5, to location l0. At location l0, cargo c5 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c7 is loaded onto vehicle v0, which has spaces s1 and s0. Vehicle v0 then moves from location l0, which has fuel levels f1 and f0, to location l1. At location l1, cargo c7 is unloaded from vehicle v0, which has spaces s0 and s1, and cargo c9 is loaded onto vehicle v0, which has spaces s1 and s0, resulting in the current state. In this state, are the following properties that involve negations True or False: cargo c0 is not located at location l1, cargo c0 is not present at location l1, cargo c0 is not in vehicle v1, cargo c0 is not situated at location l0, cargo c1 is not located at location l1, cargo c1 is not present at location l1, cargo c1 is not in vehicle v1, cargo c1 is not situated at location l0, cargo c2 is not situated at location l0, cargo c2 is not situated at location l1, cargo c3 is not located at location l0, cargo c3 is not present at location l0, cargo c3 is not in vehicle v0, cargo c3 is not situated at location l1, cargo c4 is not located at location l0, cargo c4 is not present at location l0, cargo c4 is not in vehicle v1, cargo c4 is not in vehicle v0, cargo c4 is not situated at location l1, cargo c5 is not located at location l1, cargo c5 is not present at location l1, cargo c5 is not in vehicle v0, cargo c5 is not in vehicle v1, cargo c5 is not situated at location l0, cargo c6 is not located at location l1, cargo c6 is not present at location l1, cargo c6 is not in vehicle v0, cargo c6 is not situated at location l0, cargo c7 is not situated at location l0, cargo c7 is not situated at location l1, cargo c8 is not located at location l1, cargo c8 is not present at location l1, cargo c8 is not in vehicle v0, cargo c8 is not in vehicle v1, cargo c8 is not situated at location l0, cargo c9 is not located at location l1, cargo c9 is not present at location l1, cargo c9 is not in vehicle v0, cargo c9 is not in vehicle v1, cargo c9 is not situated at location l0, fuel f0 does not exist at location l0, fuel f1 does not exist at location l1, fuel f2 does not exist at location l1, fuel f5 does not exist at location l0, fuel f6 does not exist at location l1, fuel f8 does not exist at location l1, location l0 does not have a fuel level of f4, location l0 does not have a fuel level of f8, location l0 does not have fuel f1, location l0 does not have fuel f2, location l0 does not have fuel f3, location l0 does not have fuel f6, location l0 does not have fuel f7, location l1 does not have a fuel level of f5, location l1 does not have fuel f0, location l1 does not have fuel f3, location l1 does not have fuel f4, location l1 does not have fuel f7, vehicle v0 does not contain cargo c0, vehicle v0 does not contain cargo c1, vehicle v0 does not contain cargo c2, vehicle v0 does not contain cargo c7, vehicle v0 does not contain space s1, vehicle v0 does not have space s0, vehicle v0 is not at location l0, vehicle v0 is not present at location l1, vehicle v1 does not contain cargo c2, vehicle v1 does not contain cargo c3, vehicle v1 does not contain cargo c6, vehicle v1 does not contain cargo c7, vehicle v1 does not have space s0, vehicle v1 does not have space s1, vehicle v1 is not at location l1, and vehicle v1 is not situated at location l0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also found at l0, cargo c2 is positioned at l1, cargo c3 is situated at l1, cargo c4 is located at l1, cargo c5 is positioned at l1, cargo c6 is found at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is situated at l1, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, location l0 is equipped with fuel level f3, location l1 is equipped with fuel level f8, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, a connection exists between locations l1 and l0, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l1, vehicle v1 is equipped with space s1 and vehicle v1 is currently at location l0."}
{"question_id": "9412409c-fd81-48c0-888e-3959372bad2a", "domain_name": "mystery", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_21", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: fuel level f0 does not neighbour fuel level f2, fuel level f0 does not neighbour fuel level f3, fuel level f0 does not neighbour fuel level f8, fuel level f1 does not neighbour fuel level f3, fuel level f1 does not neighbour fuel level f6, fuel level f1 does not neighbour fuel level f7, fuel level f2 does not neighbour fuel level f0, fuel level f2 does not neighbour fuel level f5, fuel level f2 does not neighbour fuel level f6, fuel level f3 does not neighbour fuel level f7, fuel level f4 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f0, fuel level f5 does not neighbour fuel level f2, fuel level f5 does not neighbour fuel level f4, fuel level f5 does not neighbour fuel level f8, fuel level f6 does not neighbour fuel level f1, fuel level f6 does not neighbour fuel level f3, fuel level f6 does not neighbour fuel level f5, fuel level f6 does not neighbour fuel level f8, fuel level f7 does not neighbour fuel level f3, fuel level f7 does not neighbour fuel level f4, fuel level f7 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f0, fuel level f8 does not neighbour fuel level f1, fuel level f8 does not neighbour fuel level f2, fuel level f8 does not neighbour fuel level f3, fuel level f8 does not neighbour fuel level f6, fuel level f8 does not neighbour fuel level f7, fuel-levels f0 and f4 are not neighbors, fuel-levels f0 and f5 are not neighbors, fuel-levels f0 and f6 are not neighbors, fuel-levels f0 and f7 are not neighbors, fuel-levels f1 and f0 are not neighbors, fuel-levels f1 and f4 are not neighbors, fuel-levels f1 and f5 are not neighbors, fuel-levels f1 and f8 are not neighbors, fuel-levels f2 and f1 are not neighbors, fuel-levels f2 and f4 are not neighbors, fuel-levels f2 and f7 are not neighbors, fuel-levels f2 and f8 are not neighbors, fuel-levels f3 and f0 are not neighbors, fuel-levels f3 and f1 are not neighbors, fuel-levels f3 and f2 are not neighbors, fuel-levels f3 and f5 are not neighbors, fuel-levels f3 and f6 are not neighbors, fuel-levels f3 and f8 are not neighbors, fuel-levels f4 and f1 are not neighbors, fuel-levels f4 and f2 are not neighbors, fuel-levels f4 and f3 are not neighbors, fuel-levels f4 and f6 are not neighbors, fuel-levels f4 and f7 are not neighbors, fuel-levels f4 and f8 are not neighbors, fuel-levels f5 and f1 are not neighbors, fuel-levels f5 and f3 are not neighbors, fuel-levels f5 and f7 are not neighbors, fuel-levels f6 and f0 are not neighbors, fuel-levels f6 and f2 are not neighbors, fuel-levels f6 and f4 are not neighbors, fuel-levels f7 and f0 are not neighbors, fuel-levels f7 and f1 are not neighbors, fuel-levels f7 and f2 are not neighbors, fuel-levels f7 and f5 are not neighbors, fuel-levels f8 and f4 are not neighbors, fuel-levels f8 and f5 are not neighbors and spaces s1 and s0 are not neighbors?", "answer": "False", "plan_length": 1, "initial_state_nl": "Cargo c0 is present at location l0, cargo c1 is situated at location l0, cargo c2 is at location l1, cargo c3 is situated at location l1, cargo c4 is present at location l1, cargo c5 is at location l1, cargo c6 is present at location l0, cargo c7 is present at location l0, cargo c8 is situated at location l1, cargo c9 is situated at location l1, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f5 neighbors fuel level f6, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, fuel-levels f4 and f5 are neighbors, fuel-levels f6 and f7 are neighbors, fuel-levels f7 and f8 are neighbors, location l0 has a fuel-level of f3, location l1 has fuel f8, spaces s0 and s1 are neighbors, there is a connection between locations l0 and l1, there is a connection between locations l1 and l0, vehicle v0 has space s1, vehicle v0 is present at location l1, vehicle v1 has space s1 and vehicle v1 is present at location l0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: fuel level f0 is not adjacent to fuel level f2, fuel level f0 is not adjacent to fuel level f3, fuel level f0 is not adjacent to fuel level f8, fuel level f1 is not adjacent to fuel level f3, fuel level f1 is not adjacent to fuel level f6, fuel level f1 is not adjacent to fuel level f7, fuel level f2 is not adjacent to fuel level f0, fuel level f2 is not adjacent to fuel level f5, fuel level f2 is not adjacent to fuel level f6, fuel level f3 is not adjacent to fuel level f7, fuel level f4 is not adjacent to fuel level f0, fuel level f5 is not adjacent to fuel level f0, fuel level f5 is not adjacent to fuel level f2, fuel level f5 is not adjacent to fuel level f4, fuel level f5 is not adjacent to fuel level f8, fuel level f6 is not adjacent to fuel level f1, fuel level f6 is not adjacent to fuel level f3, fuel level f6 is not adjacent to fuel level f5, fuel level f6 is not adjacent to fuel level f8, fuel level f7 is not adjacent to fuel level f3, fuel level f7 is not adjacent to fuel level f4, fuel level f7 is not adjacent to fuel level f6, fuel level f8 is not adjacent to fuel level f0, fuel level f8 is not adjacent to fuel level f1, fuel level f8 is not adjacent to fuel level f2, fuel level f8 is not adjacent to fuel level f3, fuel level f8 is not adjacent to fuel level f6, fuel level f8 is not adjacent to fuel level f7, fuel levels f0 and f4 do not share a boundary, fuel levels f0 and f5 do not share a boundary, fuel levels f0 and f6 do not share a boundary, fuel levels f0 and f7 do not share a boundary, fuel levels f1 and f0 do not share a boundary, fuel levels f1 and f4 do not share a boundary, fuel levels f1 and f5 do not share a boundary, fuel levels f1 and f8 do not share a boundary, fuel levels f2 and f1 do not share a boundary, fuel levels f2 and f4 do not share a boundary, fuel levels f2 and f7 do not share a boundary, fuel levels f2 and f8 do not share a boundary, fuel levels f3 and f0 do not share a boundary, fuel levels f3 and f1 do not share a boundary, fuel levels f3 and f2 do not share a boundary, fuel levels f3 and f5 do not share a boundary, fuel levels f3 and f6 do not share a boundary, fuel levels f3 and f8 do not share a boundary, fuel levels f4 and f1 do not share a boundary, fuel levels f4 and f2 do not share a boundary, fuel levels f4 and f3 do not share a boundary, fuel levels f4 and f6 do not share a boundary, fuel levels f4 and f7 do not share a boundary, fuel levels f4 and f8 do not share a boundary, fuel levels f5 and f1 do not share a boundary, fuel levels f5 and f3 do not share a boundary, fuel levels f5 and f7 do not share a boundary, fuel levels f6 and f0 do not share a boundary, fuel levels f6 and f2 do not share a boundary, fuel levels f6 and f4 do not share a boundary, fuel levels f7 and f0 do not share a boundary, fuel levels f7 and f1 do not share a boundary, fuel levels f7 and f2 do not share a boundary, fuel levels f7 and f5 do not share a boundary, fuel levels f8 and f4 do not share a boundary, fuel levels f8 and f5 do not share a boundary and spaces s1 and s0 do not share a boundary?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is also found at l0, cargo c2 is positioned at l1, cargo c3 is situated at l1, cargo c4 is located at l1, cargo c5 is positioned at l1, cargo c6 is found at l0, cargo c7 is located at l0, cargo c8 is situated at l1, cargo c9 is situated at l1, fuel level f1 is adjacent to fuel level f2, fuel level f2 is adjacent to fuel level f3, fuel level f5 is adjacent to fuel level f6, fuel levels f0 and f1 are adjacent, fuel levels f3 and f4 are adjacent, fuel levels f4 and f5 are adjacent, fuel levels f6 and f7 are adjacent, fuel levels f7 and f8 are adjacent, location l0 is equipped with fuel level f3, location l1 is equipped with fuel level f8, spaces s0 and s1 are adjacent, a connection exists between locations l0 and l1, a connection exists between locations l1 and l0, vehicle v0 is equipped with space s1, vehicle v0 is currently at location l1, vehicle v1 is equipped with space s1 and vehicle v1 is currently at location l0."}
{"question_id": "3935b762-99d3-45f4-816f-a5d388b9923e", "domain_name": "mystery", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_13", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: vehicle v0 moves to location l0 from location l1 that has fuel level f3 and f2, cargo c0 is loaded in vehicle v0 with space s2 and space s1 at location l0, cargo c2 is loaded in vehicle v0 with space s1 and space s0 at location l0, vehicle v0 moves to location l1 from location l0 that has fuel level f4 and f3, cargo c0 is unloaded from vehicle v0 with space s0 and space s1 at location l1, cargo c10 is loaded in vehicle v0 with space s1 and space s0 at location l1, cargo c2 is unloaded from vehicle v0 with space s0 and space s1 at location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0, at location l0, cargo c10 is unloaded from vehicle v0 with spaces s1 and s2 and at location l0, cargo c3 is loaded in vehicle v0 with spaces s2 and s1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: cargo c0 is at location l1, cargo c1 is present at location l1, cargo c10 is at location l0, cargo c2 is situated at location l1, cargo c4 is at location l0, cargo c5 is situated at location l1, cargo c6 is present at location l0, cargo c7 is situated at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel f3 exists in location l0, location l1 has fuel f1, vehicle v0 contains cargo c3, vehicle v0 contains space s1, vehicle v0 is present at location l0, vehicle v1 contains space s2 and vehicle v1 is at location l1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Cargo c0 is situated at location l0, cargo c1 is situated at location l1, cargo c10 is present at location l1, cargo c2 is at location l0, cargo c3 is at location l0, cargo c4 is at location l0, cargo c5 is situated at location l1, cargo c6 is at location l0, cargo c7 is present at location l0, cargo c8 is present at location l1, cargo c9 is at location l1, fuel level f0 neighbors fuel level f1, fuel level f2 neighbors fuel level f3, fuel level f3 neighbors fuel level f4, fuel level f4 neighbors fuel level f5, fuel-levels f1 and f2 are neighbors, location l0 and location l1 are connected, location l0 has a fuel-level of f4, location l1 has fuel f3, space s1 neighbors space s2, spaces s0 and s1 are neighbors, there is a connection between locations l1 and l0, vehicle v0 has space s2, vehicle v0 is present at location l1, vehicle v1 contains space s2 and vehicle v1 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: vehicle v0 relocates from location l1, which has fuel levels f3 and f2, to location l0, where cargo c0 is loaded into vehicle v0, occupying spaces s2 and s1, and cargo c2 is also loaded into vehicle v0, occupying spaces s1 and s0. Then, vehicle v0 moves from location l0, which has fuel levels f4 and f3, to location l1, where cargo c0 is unloaded from vehicle v0, freeing up spaces s0 and s1, and cargo c10 is loaded into vehicle v0, occupying spaces s1 and s0. Additionally, cargo c2 is unloaded from vehicle v0, freeing up spaces s0 and s1. Next, vehicle v0 moves from location l1, which has fuel levels f2 and f1, to location l0. Upon arrival at location l0, cargo c10 is unloaded from vehicle v0, freeing up spaces s1 and s2, and cargo c3 is loaded into vehicle v0, occupying spaces s2 and s1, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: is cargo c0 located at l1, is cargo c1 present at l1, is cargo c10 located at l0, is cargo c2 situated at l1, is cargo c4 located at l0, is cargo c5 situated at l1, is cargo c6 present at l0, is cargo c7 situated at l0, is cargo c8 present at l1, is cargo c9 located at l1, does location l0 contain fuel f3, does location l1 have fuel f1, does vehicle v0 contain cargo c3, does vehicle v0 have space s1 available, is vehicle v0 present at location l0, does vehicle v1 have space s2 available, and is vehicle v1 located at l1?", "initial_state_nl_paraphrased": "Cargo c0 is located at l0, cargo c1 is located at l1, cargo c10 is found at l1, cargo c2 is situated at l0, cargo c3 is situated at l0, cargo c4 is situated at l0, cargo c5 is located at l1, cargo c6 is situated at l0, cargo c7 is found at l0, cargo c8 is found at l1, cargo c9 is situated at l1, fuel level f0 is adjacent to fuel level f1, fuel level f2 is adjacent to fuel level f3, fuel level f3 is adjacent to fuel level f4, fuel level f4 is adjacent to fuel level f5, fuel levels f1 and f2 are adjacent, locations l0 and l1 are linked, location l0 has a fuel level of f4, location l1 has a fuel level of f3, space s1 is adjacent to space s2, spaces s0 and s1 are adjacent, a connection exists between locations l1 and l0, vehicle v0 occupies space s2, vehicle v0 is currently at location l1, vehicle v1 contains space s2 and vehicle v1 is currently at location l1."}
{"question_id": "13cb5228-a033-4f91-9da1-4a449ffe8f4a", "domain_name": "mystery", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_13", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: cargo c1 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves to location l0 from location l1 that has fuel level f6 and f5, at location l0, cargo c1 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f7 and f6 to location l1, cargo c4 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f5 and f4 to location l0, at location l0, cargo c4 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f6 and f5 to location l1, cargo c6 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f4 and f3 to location l0, at location l0, cargo c6 is unloaded from vehicle v0 with spaces s0 and s1, vehicle v0 moves from location l0 which has fuel-levels f5 and f4 to location l1, at location l1, cargo c7 is loaded in vehicle v0 with spaces s1 and s0, vehicle v0 moves from location l1 which has fuel-levels f3 and f2 to location l0, cargo c7 is unloaded from vehicle v0 with space s0 and space s1 at location l0, vehicle v0 moves from location l0 which has fuel-levels f4 and f3 to location l1, cargo c8 is loaded in vehicle v0 with space s1 and space s0 at location l1, vehicle v0 moves from location l1 which has fuel-levels f2 and f1 to location l0 and at location l0, cargo c8 is unloaded from vehicle v0 with spaces s0 and s1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: cargo c0 is at location l1, cargo c1 is situated at location l0, cargo c10 is at location l0, cargo c2 is at location l0, cargo c3 is present at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is situated at location l0, cargo c7 is at location l0, cargo c8 is at location l0, cargo c9 is at location l1, location l0 has a fuel-level of f3, location l1 has fuel f1, vehicle v0 contains space s1 and vehicle v0 is at location l0?", "answer": "True", "plan_length": 19, "initial_state_nl": "Cargo c0 is at location l1, cargo c1 is present at location l1, cargo c10 is present at location l0, cargo c2 is present at location l0, cargo c3 is at location l0, cargo c4 is situated at location l1, cargo c5 is situated at location l1, cargo c6 is present at location l1, cargo c7 is at location l1, cargo c8 is situated at location l1, cargo c9 is at location l1, fuel f7 exists in location l0, fuel level f1 neighbors fuel level f2, fuel level f2 neighbors fuel level f3, fuel level f4 neighbors fuel level f5, fuel level f5 neighbors fuel level f6, fuel level f6 neighbors fuel level f7, fuel level f7 neighbors fuel level f8, fuel-levels f0 and f1 are neighbors, fuel-levels f3 and f4 are neighbors, location l0 and location l1 are connected, location l1 has a fuel-level of f6, location l1 is connected to location l0, spaces s0 and s1 are neighbors, vehicle v0 contains space s1 and vehicle v0 is at location l1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: cargo c1 is placed in vehicle v0, which has available spaces s1 and s0, at location l1. Then, vehicle v0 travels from location l1, where the fuel levels are f6 and f5, to location l0. Upon arrival at location l0, cargo c1 is removed from vehicle v0, which still has spaces s0 and s1 available. Next, vehicle v0 moves from location l0, with fuel levels f7 and f6, back to location l1. At location l1, cargo c4 is loaded into vehicle v0, which has spaces s1 and s0 available. Vehicle v0 then travels from location l1, with fuel levels f5 and f4, to location l0. At location l0, cargo c4 is unloaded from vehicle v0, which still has spaces s0 and s1 available. This process is repeated for cargo c6, c7, and c8, with vehicle v0 moving between locations l0 and l1, and the respective cargo being loaded and unloaded. The final state is reached after these actions. In this state, are the following properties, which do not involve negations, True or False: cargo c0 is located at l1, cargo c1 is at location l0, cargo c10 is at location l0, cargo c2 is at location l0, cargo c3 is present at location l0, cargo c4 is at location l0, cargo c5 is at location l1, cargo c6 is at location l0, cargo c7 is at location l0, cargo c8 is at location l0, cargo c9 is at location l1, location l0 has a fuel level of f3, location l1 has fuel level f1, vehicle v0 has space s1 available, and vehicle v0 is at location l0?", "initial_state_nl_paraphrased": "Cargo c0 is located at l1, while cargo c1 is also found at l1. In contrast, cargo c10, cargo c2, and cargo c3 are all situated at location l0. Additionally, cargo c4, cargo c5, cargo c6, cargo c7, cargo c8, and cargo c9 are all located at l1. Fuel f7 is present at location l0. The fuel levels are connected in the following order: f1 is adjacent to f2, f2 is adjacent to f3, f4 is adjacent to f5, f5 is adjacent to f6, f6 is adjacent to f7, and f7 is adjacent to f8. Furthermore, f0 and f1 are neighboring fuel levels, as are f3 and f4. Locations l0 and l1 are connected, with l1 having a fuel level of f6. It is also noted that l1 is connected to l0. Spaces s0 and s1 are adjacent, and vehicle v0, which contains space s1, is currently at location l1."}
