{"question_id": "d94bef81-f0d0-43f3-a4d3-927931c72257", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: package3 is loaded in truck1 at location s0 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "775", "plan_length": 1, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p1_3 and s1 have a path between them, locations p1_3 and s3 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and s1 have a link between them, locations s1 and s0 have a link between them, locations s2 and s1 have a link between them, package1 is currently at location s1, package2 is at location s2, package3 is present at location s0, package4 is present at location s2, there exists a link between the locations s1 and s2, there exists a link between the locations s3 and s0, there exists a path between the locations p0_2 and s2, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p0_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_2 and location s0, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_3, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are performed: package3 is loaded in truck1 at location s0 to reach the current state. In this state, what is the total count of actions that cannot be executed? Provide the answer as an integer, or write None if there are no such actions.", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at s2. A path exists between locations p0_1 and s0, as well as between p1_3 and s1, and between p1_3 and s3. Additionally, a path is present between s0 and p0_2, and a link exists between s0 and s1, and between s1 and s0. Furthermore, a link is present between s2 and s1, and package1 is currently situated at s1, while package2 and package4 are at s2, and package3 is at s0. \n\nThere are links between s1 and s2, s3 and s0, and paths between p0_2 and s2, p0_3 and s0, p0_3 and s3, p2_1 and s1, s1 and p0_1, s1 and p1_3, and s3 and p0_3. Moreover, links exist between s0 and s2, s0 and s3, s1 and s3, s2 and s0, s2 and s3, s3 and s1, and s3 and s2. \n\nPaths also exist between p0_1 and s1, p0_2 and s0, p2_1 and s2, s0 and p0_1, s0 and p0_3, s1 and p2_1, s2 and p0_2, s2 and p2_1, and s3 and p1_3. Currently, truck1 is empty and located at s0, and truck2 is also empty and situated at s0."}
{"question_id": "fb01fa9c-967f-496e-913e-8554d655a170", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to location p4_3, driver1 walks from location p4_3 to s4, driver1 walks from location s4 to p4_1, driver1 walks from location p4_1 to s1, driver1 boards truck1 at location s1, driver1 drives truck1 from location s1 to location s0, at location s0, package4 is loaded in truck1, truck1 is driven from location s0 to s2 by driver1, truck1 is loaded with package2 at location s2, truck1 is loaded with package1 at location s2, truck1 is driven from location s2 to s3 by driver1, at location s3, package3 is loaded in truck1, truck1 is unloaded with package1 at location s3, truck1 is driven from location s3 to s4 by driver1, package4 is unloaded from truck1 at location s4, at location s4, package3 is unloaded in truck1, truck1 is unloaded with package2 at location s4, driver1 drives truck1 from location s4 to location s1 and from truck1, driver1 disembarks at location s1 to reach the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "answer": "282", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s3, driver2 is at location s4, driver3 is at location s3, locations p4_0 and s4 have a path between them, locations p4_1 and s4 have a path between them, locations p5_2 and s2 have a path between them, locations p5_2 and s5 have a path between them, locations s0 and s2 have a link between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s3 and p4_3 have a path between them, locations s3 and s5 have a link between them, locations s4 and p4_0 have a path between them, locations s4 and s0 have a link between them, locations s4 and s5 have a link between them, package1 is present at location s2, package2 is currently at location s2, package3 is at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s3, there exists a link between the locations s2 and s5, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s3, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p0_5 and s5, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p0_5, there exists a path between the locations s0 and p4_0, there exists a path between the locations s2 and p5_2, there exists a path between the locations s5 and p0_5, there is a link between location s0 and location s1, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s3 and location s2, there is a link between location s4 and location s1, there is a link between location s5 and location s0, there is a path between location p4_1 and location s1, there is a path between location p4_3 and location s3, there is a path between location s1 and location p4_1, there is a path between location s4 and location p4_1, there is a path between location s4 and location p4_3, there is a path between location s5 and location p5_2, truck1 is at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by s4 to p4_1, and then p4_1 to s1. At s1, driver1 boards truck1 and drives it to s0. Upon arrival at s0, package4 is loaded onto truck1. Driver1 then drives truck1 from s0 to s2, where packages 2 and 1 are loaded. Next, truck1 is driven to s3, where package3 is loaded and package1 is unloaded. From s3, truck1 is driven to s4, where package4 and package3 are unloaded, and package2 is also unloaded. Finally, driver1 drives truck1 back to s1 and disembarks. In this resulting state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is at s4, and driver3 is also at s3. A path exists between locations p4_0 and s4, as well as between p4_1 and s4. Additionally, a path is present between p5_2 and s2, and between p5_2 and s5. Locations s0 and s2 are connected by a link, as are s0 and s4, and s1 and s4. Furthermore, a path exists between s3 and p4_3, and a link exists between s3 and s5. Locations s4 and p4_0 are connected by a path, and s4 is linked to s0 and s5. Package1 is located at s2, package2 is also at s2, package3 is at s3, and package4 is at s0. Links exist between s0 and s5, s1 and s0, s1 and s2, s2 and s3, s2 and s5, s3 and s4, s4 and s3, s5 and s2, s5 and s3, and s5 and s4. Paths are present between p0_5 and s0, p0_5 and s5, p4_0 and s0, p4_3 and s4, s0 and p0_5, s0 and p4_0, s2 and p5_2, and s5 and p0_5. There are links between s0 and s1, s2 and s0, s2 and s1, s3 and s2, s4 and s1, and s5 and s0. Paths also exist between p4_1 and s1, p4_3 and s3, s1 and p4_1, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "73206141-5fd7-4c93-a668-b6c6aaca66f7", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver3 boards truck1 at location s0, package3 is loaded in truck1 at location s0, package1 is loaded in truck1 at location s0, driver3 drives truck1 to location s3 from location s0, at location s3, package1 is unloaded in truck1, truck1 is driven from location s3 to s1 by driver3, driver3 disembarks from truck1 at location s1, truck1 is unloaded with package3 at location s1, at location s2, package2 is loaded in truck2 and driver1 walks from location s3 to location p3_0 to reach the current state. In this state, what is the total number of inexecutable actions? Write as an integer. Write None if there are none.", "answer": "1244", "plan_length": 10, "initial_state_nl": "Driver1 is at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s2 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s3 have a link between them, locations s2 and p1_2 have a path between them, locations s3 and p3_0 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is at location s0, there exists a link between the locations s0 and s2, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s1 and p1_2, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p0_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver3 gets on truck1 at location s0, package3 is placed in truck1 at location s0, package1 is also loaded into truck1 at location s0, then driver3 drives truck1 from location s0 to location s3, at location s3, package1 is removed from truck1, driver3 then drives truck1 from location s3 to location s1, driver3 gets off truck1 at location s1, and package3 is unloaded from truck1 at location s1. Meanwhile, at location s2, package2 is loaded into truck2, and driver1 walks from location s3 to location p3_0 to reach the current state. In this state, what is the total number of actions that cannot be executed? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is also at location s3, and driver3 is at location s0. A path exists between locations p1_2 and s2, as well as between locations s0 and p3_0. Locations s0 and s3 are connected by a link, and the same applies to locations s2 and p1_2, locations s3 and p3_0, locations s3 and s1, and locations s3 and s2. Package1 is currently at location s0, package2 is at location s2, and package3 is also at location s0. There are links between locations s0 and s2, s2 and s0, s2 and s1, and s3 and s0. Paths exist between locations p0_1 and s0, p1_2 and s1, s0 and p0_1, s0 and p2_0, s1 and p1_2, s1 and p1_3, s2 and p2_0, p0_1 and s1, p1_3 and s1, p1_3 and s3, p2_0 and s0, p2_0 and s2, p3_0 and s0, and p3_0 and s3. Additionally, there are links between locations s1 and s2, s1 and s3, and s2 and s3. Furthermore, paths exist between locations s1 and p0_1, s3 and p1_3, p0_1 and s1, and p1_3 and s3. Truck1 is empty and currently at location s0, while truck2 is also empty and at location s2."}
{"question_id": "a227ec28-7199-4097-af02-8e8bfb4cb85d", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to p3_0 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "1500", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s1, driver2 is currently at location s3, driver3 is currently at location s3, locations p1_2 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p2_0 and s0 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is currently at location s2, package3 is at location s2, package4 is currently at location s1, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations p2_0 and s2, there exists a path between the locations p3_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p1_2, there exists a path between the locations s2 and p2_0, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a path between location p0_1 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s3 and location p3_0, truck1 is at location s0, truck1 is empty, truck2 contains nothing, truck2 is present at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following steps are taken: driver2 moves from location s3 to p3_0 to attain the current state. In this state, what is the total count of actions that can be executed and those that cannot? Provide the answer as an integer, or write None if there are no such actions.", "initial_state_nl_paraphrased": "Driver1 is located at s1, while driver2 and driver3 are both currently at s3. A path exists between p1_2 and s1, as well as between p1_2 and s2. Additionally, a path is present between p2_0 and s0. Locations s0 and s1 are connected by a link, and the same applies to locations s0 and s3. Furthermore, a path is present between s1 and p1_2, and locations s3 and s0 are linked. Package1 is currently at s3, package2 and package3 are at s2, and package4 is at s1. Links exist between s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s3 and s1. Paths are present between p0_1 and s1, p1_3 and s1, p1_3 and s3, p2_0 and s2, p3_0 and s0, s0 and p0_1, s1 and p1_3, s2 and p1_2, s2 and p2_0, s3 and p1_3, s0 and p2_0, and s0 and p3_0. Moreover, links exist between s0 and s2, and s1 and s0. Paths also exist between p0_1 and s0, p3_0 and s3, s1 and p0_1, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is empty and at s0."}
{"question_id": "6c7b0616-2ba9-4a54-bf2f-22b550e5b8a1", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks to location p4_3 from location s3, driver1 walks from location p4_3 to s4, driver1 walks to location p4_1 from location s4, driver1 walks from location p4_1 to location s1, driver1 boards truck1 at location s1, driver1 drives truck1 from location s1 to location s0, at location s0, package4 is loaded in truck1, truck1 is driven from location s0 to s2 by driver1, at location s2, package2 is loaded in truck1, package1 is loaded in truck1 at location s2, truck1 is driven from location s2 to s3 by driver1, truck1 is loaded with package3 at location s3, package1 is unloaded from truck1 at location s3, driver1 drives truck1 to location s4 from location s3, at location s4, package4 is unloaded in truck1, truck1 is unloaded with package3 at location s4, at location s4, package2 is unloaded in truck1, truck1 is driven from location s4 to s1 by driver1 and from truck1, driver1 disembarks at location s1 to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "answer": "6", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s3, driver2 is at location s4, driver3 is at location s3, locations p4_0 and s4 have a path between them, locations p4_1 and s4 have a path between them, locations p5_2 and s2 have a path between them, locations p5_2 and s5 have a path between them, locations s0 and s2 have a link between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s3 and p4_3 have a path between them, locations s3 and s5 have a link between them, locations s4 and p4_0 have a path between them, locations s4 and s0 have a link between them, locations s4 and s5 have a link between them, package1 is present at location s2, package2 is currently at location s2, package3 is at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s3, there exists a link between the locations s2 and s5, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s3, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p0_5 and s5, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p0_5, there exists a path between the locations s0 and p4_0, there exists a path between the locations s2 and p5_2, there exists a path between the locations s5 and p0_5, there is a link between location s0 and location s1, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s3 and location s2, there is a link between location s4 and location s1, there is a link between location s5 and location s0, there is a path between location p4_1 and location s1, there is a path between location p4_3 and location s3, there is a path between location s1 and location p4_1, there is a path between location s4 and location p4_1, there is a path between location s4 and location p4_3, there is a path between location s5 and location p5_2, truck1 is at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by a move from s4 to p4_1, and then from p4_1 to s1. At s1, driver1 boards truck1 and drives it to s0. Upon arrival at s0, package4 is loaded onto truck1. Driver1 then drives truck1 from s0 to s2, where packages 2 and 1 are loaded. Next, truck1 is driven to s3, where package3 is loaded and package1 is unloaded. From s3, truck1 is driven to s4, where packages 4, 3, and 2 are unloaded. Finally, truck1 is driven back to s1, and driver1 disembarks. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is at s4, and driver3 is also at s3. A path exists between locations p4_0 and s4, as well as between p4_1 and s4. Additionally, a path is present between p5_2 and s2, and between p5_2 and s5. Locations s0 and s2 are connected by a link, as are s0 and s4, and s1 and s4. Furthermore, a path exists between s3 and p4_3, and a link exists between s3 and s5. A path is also present between s4 and p4_0, and links exist between s4 and s0, and s4 and s5. Package1 is located at s2, package2 is currently at s2, package3 is at s3, and package4 is currently at s0. Links exist between s0 and s5, s1 and s0, s1 and s2, s2 and s3, s2 and s5, s3 and s4, s4 and s3, s5 and s2, s5 and s3, and s5 and s4. Paths exist between p0_5 and s0, p0_5 and s5, p4_0 and s0, p4_3 and s4, s0 and p0_5, s0 and p4_0, s2 and p5_2, and s5 and p0_5. There are links between s0 and s1, s2 and s0, s2 and s1, s3 and s2, s4 and s1, and s5 and s0. Paths also exist between p4_1 and s1, p4_3 and s3, s1 and p4_1, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "57df9c5f-7c20-4e68-941f-cee88cb2e04a", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: package3 is loaded in truck1 at location s0 to reach the current state. In this state, what is the total number of executable actions? Write as an integer. Write None if there are none.", "answer": "5", "plan_length": 1, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p1_3 and s1 have a path between them, locations p1_3 and s3 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and s1 have a link between them, locations s1 and s0 have a link between them, locations s2 and s1 have a link between them, package1 is currently at location s1, package2 is at location s2, package3 is present at location s0, package4 is present at location s2, there exists a link between the locations s1 and s2, there exists a link between the locations s3 and s0, there exists a path between the locations p0_2 and s2, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p0_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_2 and location s0, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_3, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are performed: package3 is loaded in truck1 at location s0 to reach the current state. In this state, what is the total count of actions that can be executed? Provide the answer as an integer, or None if there are no executable actions.", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at s2. A path exists between locations p0_1 and s0, as well as between p1_3 and s1, and between p1_3 and s3. Additionally, a path is present between s0 and p0_2, and a link exists between s0 and s1, and between s1 and s0. Furthermore, a link is established between s2 and s1. Package1 is currently situated at location s1, package2 is at s2, package3 is located at s0, and package4 is also at s2. A link exists between s1 and s2, and between s3 and s0. Paths are present between p0_2 and s2, p0_3 and s0, p0_3 and s3, p2_1 and s1, s1 and p0_1, s1 and p1_3, and s3 and p0_3. Links are established between s0 and s2, s0 and s3, s1 and s3, s2 and s0, s2 and s3, s3 and s1, and s3 and s2. Paths also exist between p0_1 and s1, p0_2 and s0, p2_1 and s2, s0 and p0_1, s0 and p0_3, s1 and p2_1, s2 and p0_2, s2 and p2_1, and s3 and p1_3. Truck1 is empty and currently at location s0, while truck2 is also empty and at location s0."}
{"question_id": "9baa4177-4952-47e5-b546-cdf949193c5f", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to p4_3, driver1 walks from location p4_3 to s4, driver1 walks to location p4_1 from location s4, driver1 walks from location p4_1 to location s1, at location s1, driver1 boards truck1, driver1 drives truck1 to location s0 from location s1, package4 is loaded in truck1 at location s0, driver1 drives truck1 from location s0 to location s2, package2 is loaded in truck1 at location s2, truck1 is loaded with package1 at location s2, driver1 drives truck1 to location s3 from location s2, truck1 is loaded with package3 at location s3, truck1 is unloaded with package1 at location s3, driver1 drives truck1 from location s3 to location s4, truck1 is unloaded with package4 at location s4, package3 is unloaded from truck1 at location s4, package2 is unloaded from truck1 at location s4, driver1 drives truck1 to location s1 from location s4 and driver1 disembarks from truck1 at location s1 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "1298", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s3, driver2 is at location s4, driver3 is at location s3, locations p4_0 and s4 have a path between them, locations p4_1 and s4 have a path between them, locations p5_2 and s2 have a path between them, locations p5_2 and s5 have a path between them, locations s0 and s2 have a link between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s3 and p4_3 have a path between them, locations s3 and s5 have a link between them, locations s4 and p4_0 have a path between them, locations s4 and s0 have a link between them, locations s4 and s5 have a link between them, package1 is present at location s2, package2 is currently at location s2, package3 is at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s3, there exists a link between the locations s2 and s5, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s3, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p0_5 and s5, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p0_5, there exists a path between the locations s0 and p4_0, there exists a path between the locations s2 and p5_2, there exists a path between the locations s5 and p0_5, there is a link between location s0 and location s1, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s3 and location s2, there is a link between location s4 and location s1, there is a link between location s5 and location s0, there is a path between location p4_1 and location s1, there is a path between location p4_3 and location s3, there is a path between location s1 and location p4_1, there is a path between location s4 and location p4_1, there is a path between location s4 and location p4_3, there is a path between location s5 and location p5_2, truck1 is at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by a move from s4 to p4_1, and then from p4_1 to s1. Upon reaching s1, driver1 boards truck1 and drives it from s1 to s0. At s0, package4 is loaded onto truck1. Driver1 then drives truck1 from s0 to s2, where package2 and package1 are loaded. Next, truck1 is driven from s2 to s3, where package3 is loaded and package1 is unloaded. Driver1 then drives truck1 from s3 to s4, where package4, package3, and package2 are unloaded. Finally, driver1 drives truck1 from s4 back to s1 and disembarks. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is at s4, and driver3 is also at s3. A path exists between locations p4_0 and s4, as well as between p4_1 and s4. Additionally, a path is present between p5_2 and s2, and between p5_2 and s5. Locations s0 and s2 are connected by a link, as are s0 and s4, and s1 and s4. Furthermore, a path exists between s3 and p4_3, and a link is present between s3 and s5. Locations s4 and p4_0 are connected by a path, and s4 is linked to both s0 and s5. Package1 is located at s2, package2 is also at s2, package3 is at s3, and package4 is at s0. Links exist between s0 and s5, s1 and s0, s1 and s2, s2 and s3, s2 and s5, s3 and s4, s4 and s3, s5 and s2, s5 and s3, and s5 and s4. Paths are present between p0_5 and s0, p0_5 and s5, p4_0 and s0, p4_3 and s4, s0 and p0_5, s0 and p4_0, s2 and p5_2, and s5 and p0_5. There are links between s0 and s1, s2 and s0, s2 and s1, s3 and s2, s4 and s1, and s5 and s0. Paths also exist between p4_1 and s1, p4_3 and s3, s1 and p4_1, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "eb90e8e1-cfdb-4156-b0b1-8161c7224316", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1, driver1 walks to location p0_2 from location s2, driver1 walks to location s0 from location p0_2, driver1 boards truck1 at location s0, driver1 drives truck1 to location s3 from location s0, driver1 disembarks from truck1 at location s3, at location s3, package3 is unloaded in truck1, driver1 walks from location s3 to location p0_3, driver1 walks from location p0_3 to location s0, driver2 walks from location s2 to location p0_2, driver2 walks to location s0 from location p0_2, truck2 is boarded by driver2 at location s0, driver2 drives truck2 to location s1 from location s0, at location s1, package1 is loaded in truck2, driver2 drives truck2 from location s1 to location s2, at location s2, package2 is loaded in truck2, package1 is unloaded from truck2 at location s2, driver2 drives truck2 to location s1 from location s2 and at location s1, driver2 disembarks from truck2 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "answer": "274", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p1_3 and s1 have a path between them, locations p1_3 and s3 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and s1 have a link between them, locations s1 and s0 have a link between them, locations s2 and s1 have a link between them, package1 is currently at location s1, package2 is at location s2, package3 is present at location s0, package4 is present at location s2, there exists a link between the locations s1 and s2, there exists a link between the locations s3 and s0, there exists a path between the locations p0_2 and s2, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p0_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_2 and location s0, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_3, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location s0, package3 is loaded onto truck1, driver1 moves from location s2 to location p0_2, then from location p0_2 to location s0, boards truck1 at location s0, drives truck1 from location s0 to location s3, disembarks from truck1 at location s3, and at location s3, package3 is unloaded from truck1. Subsequently, driver1 walks from location s3 to location p0_3 and then to location s0. Meanwhile, driver2 moves from location s2 to location p0_2, then to location s0, boards truck2 at location s0, drives truck2 from location s0 to location s1, loads package1 onto truck2 at location s1, drives truck2 from location s1 to location s2, loads package2 onto truck2 and unloads package1 from truck2 at location s2, drives truck2 from location s2 back to location s1, and finally disembarks from truck2 at location s1 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at s2. A path exists between locations p0_1 and s0, as well as between p1_3 and s1, and between p1_3 and s3. Additionally, a path is present between s0 and p0_2, and a link exists between s0 and s1, and between s1 and s0. Furthermore, a link is established between s2 and s1. Package1 is currently situated at s1, package2 is at s2, package3 is located at s0, and package4 is also at s2. A link is present between s1 and s2, and between s3 and s0. Paths exist between p0_2 and s2, p0_3 and s0, p0_3 and s3, p2_1 and s1, s1 and p0_1, s1 and p1_3, and s3 and p0_3. Links are established between s0 and s2, s0 and s3, s1 and s3, s2 and s0, s2 and s3, s3 and s1, and s3 and s2. Paths are also present between p0_1 and s1, p0_2 and s0, p2_1 and s2, s0 and p0_1, s0 and p0_3, s1 and p2_1, s2 and p0_2, s2 and p2_1, and s3 and p1_3. Truck1 is empty and currently at location s0, while truck2 is also empty and at location s0."}
{"question_id": "5eb419f7-c5d9-4e8f-b9aa-cb85b257cb62", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_2_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to location p3_0, driver2 walks from location p3_0 to s0, at location s0, driver2 boards truck1, driver2 drives truck1 from location s0 to location s2, package3 is loaded in truck1 at location s2, package2 is loaded in truck1 at location s2, truck1 is driven from location s2 to s0 by driver2, truck1 is unloaded with package2 at location s0, driver2 drives truck1 to location s3 from location s0 and truck1 is loaded with package1 at location s3 to reach the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "answer": "262", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s1, driver2 is currently at location s3, driver3 is currently at location s3, locations p1_2 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p2_0 and s0 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is currently at location s2, package3 is at location s2, package4 is currently at location s1, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations p2_0 and s2, there exists a path between the locations p3_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p1_2, there exists a path between the locations s2 and p2_0, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a path between location p0_1 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s3 and location p3_0, truck1 is at location s0, truck1 is empty, truck2 contains nothing, truck2 is present at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: driver2 moves from location s3 to p3_0, then from p3_0 to s0, where driver2 gets into truck1, and then drives truck1 from s0 to s2. At location s2, package3 and package2 are loaded into truck1. Subsequently, driver2 drives truck1 back to s0, unloads package2, then drives truck1 to s3 from s0, and finally loads package1 into truck1 at s3, resulting in the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is located at s1, while driver2 and driver3 are both currently at s3. A path exists between p1_2 and s1, as well as between p1_2 and s2. Additionally, a path is present between p2_0 and s0. Locations s0 and s1 are connected by a link, and the same applies to locations s0 and s3. Furthermore, a path is present between s1 and p1_2, and a link exists between s3 and s0. Package1 is currently at s3, package2 and package3 are at s2, and package4 is at s1. Links exist between s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s3 and s1. Paths are present between p0_1 and s1, p1_3 and s1, p1_3 and s3, p2_0 and s2, p3_0 and s0, s0 and p0_1, s1 and p1_3, s2 and p1_2, s2 and p2_0, s3 and p1_3, s0 and p2_0, and s0 and p3_0. Moreover, links exist between s0 and s2, and s1 and s0. Paths also exist between p0_1 and s0, p3_0 and s3, s1 and p0_1, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is empty and at s0."}
{"question_id": "a38e78be-8fa3-4a0f-b9dc-ffd370500fb9", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0, driver1 walks from location s2 to p0_2, driver1 walks from location p0_2 to location s0, driver1 boards truck1 at location s0, driver1 drives truck1 from location s0 to location s3, from truck1, driver1 disembarks at location s3, at location s3, package3 is unloaded in truck1, driver1 walks from location s3 to p0_3, driver1 walks from location p0_3 to s0 and driver2 walks to location p0_2 from location s2 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "answer": "780", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p1_3 and s1 have a path between them, locations p1_3 and s3 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and s1 have a link between them, locations s1 and s0 have a link between them, locations s2 and s1 have a link between them, package1 is currently at location s1, package2 is at location s2, package3 is present at location s0, package4 is present at location s2, there exists a link between the locations s1 and s2, there exists a link between the locations s3 and s0, there exists a path between the locations p0_2 and s2, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p0_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_2 and location s0, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_3, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: package3 is loaded onto truck1 at location s0, driver1 moves from location s2 to p0_2, then from p0_2 to s0, boards truck1 at s0, drives truck1 from s0 to s3, disembarks from truck1 at s3, unloads package3 from truck1 at s3, walks from s3 to p0_3, and then from p0_3 back to s0, while driver2 moves from s2 to p0_2 to reach the current state. In this state, what is the total number of executable and inexecutable actions? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at s2. A path exists between locations p0_1 and s0, as well as between p1_3 and s1, and between p1_3 and s3. Additionally, a path is present between s0 and p0_2, and a link exists between s0 and s1, and between s1 and s0. Furthermore, a link is established between s2 and s1. Package1 is currently situated at s1, package2 is at s2, package3 is located at s0, and package4 is also at s2. A link exists between s1 and s2, and between s3 and s0. Paths are present between p0_2 and s2, p0_3 and s0, p0_3 and s3, p2_1 and s1, s1 and p0_1, s1 and p1_3, and s3 and p0_3. Links are established between s0 and s2, s0 and s3, s1 and s3, s2 and s0, s2 and s3, s3 and s1, and s3 and s2. Paths also exist between p0_1 and s1, p0_2 and s0, p2_1 and s2, s0 and p0_1, s0 and p0_3, s1 and p2_1, s2 and p0_2, s2 and p2_1, and s3 and p1_3. Truck1 is empty and currently at location s0, while truck2 is also empty and at location s0."}
{"question_id": "9765112c-072d-43eb-bc85-96442a8ced99", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_2_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to location p4_3 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "answer": "335", "plan_length": 1, "initial_state_nl": "Driver1 is currently at location s3, driver2 is at location s4, driver3 is at location s3, locations p4_0 and s4 have a path between them, locations p4_1 and s4 have a path between them, locations p5_2 and s2 have a path between them, locations p5_2 and s5 have a path between them, locations s0 and s2 have a link between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s3 and p4_3 have a path between them, locations s3 and s5 have a link between them, locations s4 and p4_0 have a path between them, locations s4 and s0 have a link between them, locations s4 and s5 have a link between them, package1 is present at location s2, package2 is currently at location s2, package3 is at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s3, there exists a link between the locations s2 and s5, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s3, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p0_5 and s5, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p0_5, there exists a path between the locations s0 and p4_0, there exists a path between the locations s2 and p5_2, there exists a path between the locations s5 and p0_5, there is a link between location s0 and location s1, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s3 and location s2, there is a link between location s4 and location s1, there is a link between location s5 and location s0, there is a path between location p4_1 and location s1, there is a path between location p4_3 and location s3, there is a path between location s1 and location p4_1, there is a path between location s4 and location p4_1, there is a path between location s4 and location p4_3, there is a path between location s5 and location p5_2, truck1 is at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: driver1 moves from location s3 to location p4_3 to achieve the current state. In this state, what is the total count of valid state properties (including both affirmative and negated properties)? Provide the answer as an integer, or write None if there are no valid properties.", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is at s4, and driver3 is also at s3. A path exists between locations p4_0 and s4, as well as between p4_1 and s4. Additionally, a path is present between p5_2 and s2, and between p5_2 and s5. Locations s0 and s2 are connected by a link, as are s0 and s4, and s1 and s4. Furthermore, a path exists between s3 and p4_3, and a link is present between s3 and s5. Locations s4 and p4_0 are connected by a path, and s4 is linked to s0 and s5. Package1 is located at s2, package2 is also at s2, package3 is at s3, and package4 is at s0. Links exist between s0 and s5, s1 and s0, s1 and s2, s2 and s3, s2 and s5, s3 and s4, s4 and s3, s5 and s2, s5 and s3, and s5 and s4. Paths are present between p0_5 and s0, p0_5 and s5, p4_0 and s0, p4_3 and s4, s0 and p0_5, s0 and p4_0, s2 and p5_2, and s5 and p0_5. There are links between s0 and s1, s2 and s0, s2 and s1, s3 and s2, s4 and s1, and s5 and s0. Paths also exist between p4_1 and s1, p4_3 and s3, s1 and p4_1, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "b8cd57cf-89f3-454d-9091-bbad68ceb699", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_2_question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1, driver1 walks to location p0_2 from location s2, driver1 walks from location p0_2 to s0, truck1 is boarded by driver1 at location s0, driver1 drives truck1 to location s3 from location s0, from truck1, driver1 disembarks at location s3, at location s3, package3 is unloaded in truck1, driver1 walks to location p0_3 from location s3, driver1 walks from location p0_3 to location s0, driver2 walks to location p0_2 from location s2, driver2 walks from location p0_2 to location s0, at location s0, driver2 boards truck2, driver2 drives truck2 from location s0 to location s1, package1 is loaded in truck2 at location s1, driver2 drives truck2 to location s2 from location s1, at location s2, package2 is loaded in truck2, package1 is unloaded from truck2 at location s2, driver2 drives truck2 from location s2 to location s1 and from truck2, driver2 disembarks at location s1 to reach the current state. In this state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "answer": "232", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p1_3 and s1 have a path between them, locations p1_3 and s3 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and s1 have a link between them, locations s1 and s0 have a link between them, locations s2 and s1 have a link between them, package1 is currently at location s1, package2 is at location s2, package3 is present at location s0, package4 is present at location s2, there exists a link between the locations s1 and s2, there exists a link between the locations s3 and s0, there exists a path between the locations p0_2 and s2, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p0_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_2 and location s0, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_3, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location s0, package3 is placed in truck1, driver1 moves from location s2 to p0_2 and then to s0, where driver1 gets into truck1, then drives it from s0 to s3, gets out of truck1 at s3, and package3 is removed from truck1 at s3. Subsequently, driver1 walks from s3 to p0_3 and then back to s0. Meanwhile, driver2 moves from s2 to p0_2 and then to s0, boards truck2 at s0, drives it to s1, loads package1 into truck2 at s1, drives to s2, loads package2 into truck2, unloads package1 from truck2 at s2, and then drives back to s1, where driver2 gets out of truck2. In this resulting state, what is the total number of valid properties of the state that involve negations? Write as an integer. Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at s2. A path exists between locations p0_1 and s0, as well as between p1_3 and s1, and between p1_3 and s3. Additionally, a path is present between s0 and p0_2. Locations s0 and s1 are connected by a link, as are s1 and s0, s2 and s1, and s1 and s2. Package1 is currently situated at location s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link exists between s1 and s2, and between s3 and s0. Furthermore, paths exist between p0_2 and s2, p0_3 and s0, p0_3 and s3, p2_1 and s1, s1 and p0_1, s1 and p1_3, and s3 and p0_3. Links are present between s0 and s2, s0 and s3, s1 and s3, s2 and s0, s2 and s3, s3 and s1, and s3 and s2. Paths also exist between p0_1 and s1, p0_2 and s0, p2_1 and s2, s0 and p0_1, s0 and p0_3, s1 and p2_1, s2 and p0_2, s2 and p2_1, and s3 and p1_3. Truck1 is empty and currently located at s0, while truck2 is also empty and at s0."}
{"question_id": "fb229c43-0880-4410-82fc-3aa1bdfe2ab6", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are planned to be performed: driver2 drives truck1 to location p3_0 from location p0_2 to reach the current state. How many actions are there before the first inexecutable action? Write as an integer. Write None if there are none.", "answer": "0", "plan_length": 1, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p1_3 and s1 have a path between them, locations p1_3 and s3 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and s1 have a link between them, locations s1 and s0 have a link between them, locations s2 and s1 have a link between them, package1 is currently at location s1, package2 is at location s2, package3 is present at location s0, package4 is present at location s2, there exists a link between the locations s1 and s2, there exists a link between the locations s3 and s0, there exists a path between the locations p0_2 and s2, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p0_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_2 and location s0, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_3, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are scheduled: driver2 will drive truck1 from location p0_2 to location p3_0 to achieve the current state. What is the number of actions preceding the first inexecutable action? Provide the answer as an integer, or None if there are no such actions.", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at s2. A path exists between locations p0_1 and s0, as well as between p1_3 and s1, and between p1_3 and s3. Additionally, a path is present between s0 and p0_2, and a link exists between s0 and s1, and between s1 and s0. Furthermore, a link is present between s2 and s1. Package1 is currently situated at location s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link exists between s1 and s2, and between s3 and s0. Paths are present between p0_2 and s2, p0_3 and s0, p0_3 and s3, p2_1 and s1, s1 and p0_1, s1 and p1_3, and s3 and p0_3. Links exist between s0 and s2, s0 and s3, s1 and s3, s2 and s0, s2 and s3, s3 and s1, and s3 and s2. Paths are also present between p0_1 and s1, p0_2 and s0, p2_1 and s2, s0 and p0_1, s0 and p0_3, s1 and p2_1, s2 and p0_2, s2 and p2_1, and s3 and p1_3. Truck1 is empty and currently at location s0, while truck2 is also empty and at location s0."}
{"question_id": "c2fcb4ea-26be-4276-b20d-3ebdee88f61e", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_2_question_6", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location s0, driver3 boards truck1 to reach the current state. In this state, what is the total number of valid properties of the state (both with and without negations)? Write as an integer. Write None if there are none.", "answer": "322", "plan_length": 1, "initial_state_nl": "Driver1 is at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s2 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s3 have a link between them, locations s2 and p1_2 have a path between them, locations s3 and p3_0 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is at location s0, there exists a link between the locations s0 and s2, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s1 and p1_2, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p0_1, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is currently at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: at location s0, driver3 gets on truck1 to achieve the current state. In this state, what is the total count of valid properties (including both affirmative and negated properties)? Provide the answer as an integer, or write None if there are no valid properties.", "initial_state_nl_paraphrased": "Driver1 is currently situated at location s3, while driver2 is also at location s3, and driver3 is at location s0. A path exists between locations p1_2 and s2, as well as between locations s0 and p3_0. Locations s0 and s3 are connected by a link, and the same applies to locations s2 and p1_2, locations s3 and p3_0, locations s3 and s1, and locations s3 and s2. Package1 is currently at location s0, package2 is at location s2, and package3 is also at location s0. There are links between locations s0 and s2, s2 and s0, s2 and s1, and s3 and s0. Paths exist between locations p0_1 and s0, p1_2 and s1, s0 and p0_1, s0 and p2_0, s1 and p1_2, s1 and p1_3, s2 and p2_0, p0_1 and s1, p1_3 and s1, p1_3 and s3, p2_0 and s0, p2_0 and s2, p3_0 and s0, p3_0 and s3, s1 and p0_1, and s3 and p1_3. Additionally, there are links between locations s1 and s2, s1 and s3, and s2 and s3. Truck1 is empty and currently at location s0, while truck2 is also empty and at location s2."}
