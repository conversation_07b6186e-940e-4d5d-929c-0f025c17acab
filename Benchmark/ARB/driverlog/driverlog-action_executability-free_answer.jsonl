{"question_id": "bbfdbb9f-a19d-4586-955c-27c4468f30ad", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to p3_0 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "None", "plan_length": 1, "initial_state_nl": "Driver1 is at location s1, driver2 is present at location s3, driver3 is present at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and s2 have a link between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is currently at location s3, package2 is present at location s2, package3 is currently at location s2, package4 is currently at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s0, there is a link between location s2 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing, truck2 is at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: driver2 moves from location s3 to p3_0 to attain the current state. What is the first action in the sequence that cannot be executed? If there are no such actions, write None.", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is situated at s3, and driver3 is also at s3. A path exists between p0_1 and s1, as well as between p1_2 and s2, and p3_0 and s3. Additionally, a path is present between s0 and p0_1, and links exist between s0 and s1, s0 and s3. Furthermore, a path is found between s1 and p1_2, and links are present between s1 and s2, s3 and s0, and s3 and s1. Package1 is currently situated at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Links exist between s0 and s2, s1 and s3, and s2 and s1. Paths are present between p1_2 and s1, p1_3 and s3, and s2 and p1_2. A link connects s1 and s0, and another link connects s2 and s0. Paths are also present between p0_1 and s0, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s0 and p3_0, s1 and p0_1, s1 and p1_3, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is currently at s0 and is empty, while truck2 is at s3 and contains nothing. Truck3 is also at s0 and is empty."}
{"question_id": "f5b843c5-e014-42c3-b801-997d2752db81", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver3 disembarks from truck2 at location s1 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "from truck2, driver3 disembarks at location s1", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s3, driver2 is currently at location s4, driver3 is present at location s3, locations p5_2 and s2 have a path between them, locations s0 and p0_5 have a path between them, locations s1 and s4 have a link between them, locations s2 and p5_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s2 have a link between them, locations s3 and s4 have a link between them, locations s4 and p4_0 have a path between them, locations s5 and p0_5 have a path between them, locations s5 and s0 have a link between them, locations s5 and s2 have a link between them, package1 is at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is at location s0, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_1 and s1, there exists a path between the locations p4_1 and s4, there exists a path between the locations p4_3 and s3, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p4_0, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_1, there exists a path between the locations s4 and p4_3, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s2, there is a link between location s0 and location s4, there is a link between location s0 and location s5, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s2 and location s5, there is a link between location s4 and location s0, there is a link between location s4 and location s1, there is a link between location s4 and location s3, there is a link between location s4 and location s5, there is a link between location s5 and location s3, there is a path between location p0_5 and location s5, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s5, there is a path between location s1 and location p4_1, truck1 is currently at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: driver3 gets off truck2 at location s1 to reach the current state. What is the first action in the sequence that cannot be executed? Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is at location s4, and driver3 is also at location s3. A path exists between locations p5_2 and s2, as well as between locations s0 and p0_5. Locations s1 and s4 are connected by a link, and locations s2 and p5_2 also have a path between them. Furthermore, locations s2 and s0 are linked, locations s3 and s2 are connected, and locations s3 and s4 are also linked. A path exists between locations s4 and p4_0, locations s5 and p0_5, and locations s5 and s0 are connected by a link. Locations s5 and s2 are also linked. Package1 is located at s2, package2 is currently at s2, package3 is at location s3, and package4 is at location s0. There are links between locations s0 and s1, s1 and s0, s3 and s5, and s5 and s4. Paths exist between locations p0_5 and s0, p4_0 and s0, p4_1 and s1, p4_1 and s4, p4_3 and s3, and p4_3 and s4. Additionally, paths exist between locations s0 and p4_0, s3 and p4_3, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Links exist between locations s0 and s2, s0 and s4, s0 and s5, s1 and s2, s2 and s1, s2 and s3, s2 and s5, s4 and s0, s4 and s1, s4 and s3, s4 and s5, and s5 and s3. Paths also exist between locations p0_5 and s5, p4_0 and s4, p5_2 and s5, and s1 and p4_1. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "f3ce5341-0a6f-4940-a1a5-ee8bbb5060ed", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to p4_3, driver1 walks from location p4_3 to s4, driver1 walks from location s4 to p4_1, driver1 walks from location p4_1 to location s1, truck1 is boarded by driver1 at location s1, driver1 drives truck1 to location s0 from location s1, truck1 is loaded with package4 at location s0, driver1 drives truck1 from location s0 to location s2, package2 is loaded in truck1 at location s2, at location s2, package1 is loaded in truck1, driver1 drives truck1 from location s2 to location s3, at location s3, package3 is loaded in truck1, package1 is unloaded from truck1 at location s3, truck1 is driven from location s3 to s4 by driver1, truck1 is unloaded with package4 at location s4, package3 is unloaded from truck1 at location s4, truck1 is unloaded with package2 at location s4, driver1 drives truck1 to location s1 from location s4 and at location s1, driver1 disembarks from truck1 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "driver1 walks to location p4_1 from location s1, truck1 is boarded by driver1 at location s1, driver2 walks from location s4 to p4_3, driver3 walks from location s3 to p4_3, driver2 walks to location p4_0 from location s4 and driver2 walks from location s4 to p4_1", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s3, driver2 is currently at location s4, driver3 is present at location s3, locations p5_2 and s2 have a path between them, locations s0 and p0_5 have a path between them, locations s1 and s4 have a link between them, locations s2 and p5_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s2 have a link between them, locations s3 and s4 have a link between them, locations s4 and p4_0 have a path between them, locations s5 and p0_5 have a path between them, locations s5 and s0 have a link between them, locations s5 and s2 have a link between them, package1 is at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is at location s0, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_1 and s1, there exists a path between the locations p4_1 and s4, there exists a path between the locations p4_3 and s3, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p4_0, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_1, there exists a path between the locations s4 and p4_3, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s2, there is a link between location s0 and location s4, there is a link between location s0 and location s5, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s2 and location s5, there is a link between location s4 and location s0, there is a link between location s4 and location s1, there is a link between location s4 and location s3, there is a link between location s4 and location s5, there is a link between location s5 and location s3, there is a path between location p0_5 and location s5, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s5, there is a path between location s1 and location p4_1, truck1 is currently at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by s4 to p4_1, and p4_1 to s1. At s1, driver1 boards truck1, then drives it from s1 to s0. At s0, truck1 is loaded with package4, and driver1 drives it from s0 to s2. At s2, truck1 is loaded with package2 and package1. Driver1 then drives truck1 from s2 to s3, where package3 is loaded, and package1 is unloaded. Next, driver1 drives truck1 from s3 to s4, unloading package4, package3, and package2 at s4. Finally, driver1 drives truck1 from s4 back to s1 and disembarks at s1, reaching the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is at location s4, and driver3 is also at location s3. A path exists between locations p5_2 and s2, as well as between locations s0 and p0_5. Locations s1 and s4 are connected by a link, and locations s2 and p5_2 also have a path between them. Furthermore, locations s2 and s0 are linked, locations s3 and s2 are connected, and locations s3 and s4 are also linked. A path exists between locations s4 and p4_0, locations s5 and p0_5, and locations s5 and s0 are connected by a link. Locations s5 and s2 are also linked. Package1 is located at s2, package2 is currently at s2, package3 is at location s3, and package4 is at location s0. There are links between locations s0 and s1, s1 and s0, s3 and s5, and s5 and s4. Paths exist between locations p0_5 and s0, p4_0 and s0, p4_1 and s1, p4_1 and s4, p4_3 and s3, and p4_3 and s4. Additionally, paths exist between locations s0 and p4_0, s3 and p4_3, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Links exist between locations s0 and s2, s0 and s4, s0 and s5, s1 and s2, s2 and s1, s2 and s3, s2 and s5, s4 and s0, s4 and s1, s4 and s3, s4 and s5, and s5 and s3. Paths also exist between locations p0_5 and s5, p4_0 and s4, p5_2 and s5, and s1 and p4_1. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "d6549216-67cd-4ac9-b08e-6c83cd084991", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to p3_0 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "driver3 walks to location p1_3 from location s3, driver1 walks from location s1 to location p1_3, driver2 walks from location p3_0 to location s3, truck2 is boarded by driver3 at location s3, driver1 walks to location p0_1 from location s1, truck2 is loaded with package1 at location s3, driver3 walks to location p3_0 from location s3, driver1 walks to location p1_2 from location s1 and driver2 walks to location s0 from location p3_0", "plan_length": 1, "initial_state_nl": "Driver1 is at location s1, driver2 is present at location s3, driver3 is present at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and s2 have a link between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is currently at location s3, package2 is present at location s2, package3 is currently at location s2, package4 is currently at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s0, there is a link between location s2 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing, truck2 is at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: driver2 moves from location s3 to p3_0 to attain the current state. In this state, identify all possible actions that can be executed. If there are no actions, indicate None.", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is situated at s3, and driver3 is also at s3. A path exists between p0_1 and s1, as well as between p1_2 and s2, and p3_0 and s3. Additionally, paths are present between s0 and p0_1, s0 and s1, s0 and s3, s1 and p1_2, s1 and s2, and s3 and s0. Package1 is currently situated at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Links exist between s0 and s2, s1 and s3, s2 and s1, s1 and s0, and s2 and s0. Furthermore, paths are present between p1_2 and s1, p1_3 and s3, s2 and p1_2, p0_1 and s0, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s0 and p3_0, s1 and p0_1, s1 and p1_3, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is currently empty and located at s0, while truck2, which is also empty, is at s3. Truck3, which contains nothing, is present at location s0."}
{"question_id": "f4d103c5-93ed-4b76-bb35-04369804b5e7", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck2 is driven from location s3 to p2_1 by driver2, at location s0, package3 is loaded in truck1, package1 is loaded in truck1 at location s0, driver3 drives truck1 from location s0 to location s3, truck1 is unloaded with package1 at location s3, truck1 is driven from location s3 to s1 by driver3, from truck1, driver3 disembarks at location s1, package3 is unloaded from truck1 at location s1, at location s2, package2 is loaded in truck2 and driver1 walks from location s3 to p3_0 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "driver2 drives truck2 to location p2_1 from location s3", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s3, driver2 is present at location s3, driver3 is currently at location s0, locations p0_1 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and s2 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is present at location s2, package3 is at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s3 and location s0, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: truck2 is driven by driver2 from location s3 to p2_1, at location s0, truck1 is loaded with package3, then package1 is also loaded into truck1 at location s0, driver3 then drives truck1 from location s0 to location s3, package1 is unloaded from truck1 at location s3, truck1 is then driven by driver3 from location s3 to s1, driver3 gets off truck1 at location s1, package3 is then unloaded from truck1 at location s1, at location s2, truck2 is loaded with package2 and driver1 walks from location s3 to p3_0 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is located at s3, while driver2 is also at s3. Meanwhile, driver3 is currently situated at s0. There is a path connecting p0_1 and s0, as well as between p2_0 and s2, and p3_0 and s0. A link exists between s0 and s2, and another between s1 and p0_1, s1 and p1_2, s2 and p2_0, s2 and s0, s3 and p3_0, and s3 and s2. Package1 is currently at s0, package2 is at s2, and package3 is also at s0. Links are present between s0 and s3, s3 and s1, and paths exist between p1_2 and s1, p1_2 and s2, p1_3 and s3, s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p1_2. Additionally, links are found between s1 and s2, s1 and s3, s2 and s1, s2 and s3, and s3 and s0. Paths also exist between p0_1 and s1, p1_3 and s1, p2_0 and s0, p3_0 and s3, s1 and p1_3, and s3 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and present at s2."}
{"question_id": "ce8d081d-a1fb-415e-8c8b-4116a6ead721", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location s0, driver3 boards truck1, truck1 is loaded with package3 at location s0, package1 is loaded in truck1 at location s0, driver3 drives truck1 to location s3 from location s0, at location s3, package1 is unloaded in truck1, driver3 drives truck1 to location s1 from location s3, driver3 disembarks from truck1 at location s1, package3 is unloaded from truck1 at location s1, at location s2, package2 is loaded in truck2, driver1 walks from location s3 to location p3_0, driver1 walks from location p3_0 to s0, driver2 walks from location s3 to p1_3, driver2 drives truck1 from location p1_0 to location p1_2, driver2 walks from location s1 to location p1_2, driver2 walks from location p1_2 to location s2, driver3 walks from location s1 to p1_2, driver3 walks to location s2 from location p1_2, truck2 is boarded by driver3 at location s2 and truck2 is driven from location s2 to s3 by driver3 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "driver2 drives truck1 to location p1_2 from location p1_0", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s3, driver2 is present at location s3, driver3 is currently at location s0, locations p0_1 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and s2 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is present at location s2, package3 is at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s3 and location s0, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location s0, driver3 gets into truck1, truck1 is loaded with package3 at location s0, and package1 is also loaded into truck1 at location s0. Then, driver3 drives truck1 from location s0 to location s3, and at location s3, package1 is unloaded from truck1. Next, driver3 drives truck1 from location s3 to location s1, disembarks from truck1 at location s1, and package3 is unloaded from truck1 at location s1. Meanwhile, at location s2, package2 is loaded into truck2. Driver1 walks from location s3 to p3_0 and then to s0, while driver2 walks from location s3 to p1_3, drives truck1 from p1_0 to p1_2, walks from location s1 to p1_2, and then to location s2. Driver3 walks from location s1 to p1_2 and then to location s2, boards truck2 at location s2, and drives truck2 from location s2 to s3 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is located at s3, while driver2 is also at s3. Meanwhile, driver3 is currently situated at s0. There is a path connecting p0_1 and s0, as well as between p2_0 and s2, and p3_0 and s0. A link exists between s0 and s2, and another between s1 and p0_1, s1 and p1_2, s2 and p2_0, s2 and s0, s3 and p3_0, and s3 and s2. Package1 is currently at s0, package2 is at s2, and package3 is also at s0. A link is present between s0 and s3, s3 and s1, and a path exists between p1_2 and s1, p1_2 and s2, p1_3 and s3, s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p1_2. Additionally, links exist between s1 and s2, s1 and s3, s2 and s1, s2 and s3, and s3 and s0. Paths are also present between p0_1 and s1, p1_3 and s1, p2_0 and s0, p3_0 and s3, s1 and p1_3, and s3 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and at s2."}
{"question_id": "36e829b5-9dba-4882-81b7-fe9717f69dcf", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1, driver1 walks from location s2 to location p0_2, driver1 walks from location p0_2 to s0, driver1 boards truck1 at location s0, driver1 drives truck1 from location s0 to location s3, from truck1, driver1 disembarks at location s3, truck1 is unloaded with package3 at location s3, driver1 walks from location s3 to location p0_3, driver1 walks from location p0_3 to location s0 and driver2 walks from location s2 to p0_2 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "truck2 is boarded by driver1 at location s0, driver1 walks from location s0 to p0_1, driver2 walks from location p0_2 to s0, driver1 walks from location s0 to p0_2, package3 is loaded in truck1 at location s3, driver1 walks from location s0 to location p0_3 and driver2 walks from location p0_2 to location s2", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_3 and s3 have a path between them, locations p1_3 and s3 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_1 have a path between them, locations s2 and p2_1 have a path between them, locations s2 and s0 have a link between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is present at location s0, package4 is currently at location s2, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_2, there exists a path between the locations s3 and p0_3, there is a link between location s1 and location s2, there is a link between location s3 and location s1, there is a path between location p0_2 and location s2, there is a path between location p2_1 and location s1, there is a path between location s0 and location p0_1, there is a path between location s2 and location p0_2, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location s0, package3 is loaded onto truck1, driver1 moves from location s2 to p0_2 and then to s0, boards truck1 at s0, drives truck1 from s0 to s3, disembarks from truck1 at s3, unloads package3 from truck1 at s3, and then walks from s3 to p0_3 and back to s0. Meanwhile, driver2 moves from s2 to p0_2. This sequence of actions leads to the current state. Now, list all possible actions that can be executed from this state. If there are no executable actions, write None.", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at location s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s2, s0 and p0_3, s1 and p0_1, s1 and p1_3, s1 and p2_1, s2 and p2_1, s3 and p1_3. Additionally, a link is present between locations s0 and s2, s0 and s3, s2 and s0, s3 and s0. Package1 is currently at location s1, package2 is at location s2, package3 is at location s0, and package4 is at location s2. Furthermore, links exist between locations s0 and s1, s1 and s0, s1 and s3, s2 and s1, s2 and s3, s3 and s2, s1 and s2, and s3 and s1. Paths also exist between locations p0_1 and s1, p0_3 and s0, p1_3 and s1, s0 and p0_2, s3 and p0_3, p0_2 and s2, p2_1 and s1, s0 and p0_1, and s2 and p0_2. Lastly, truck1 is located at s0 and is empty, while truck2 is also at location s0 and contains nothing."}
{"question_id": "fc897cfe-ea8e-478f-bb1d-ad9b5998d378", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks to location p3_0 from location s3, driver2 walks from location p3_0 to s0, at location s0, driver2 boards truck1, driver2 drives truck1 from location s0 to location s2, package3 is loaded in truck1 at location s2, at location s2, package2 is loaded in truck1, driver2 drives truck1 from location s2 to location s0, at location s0, package2 is unloaded in truck1, driver2 drives truck1 from location s0 to location s3 and package1 is loaded in truck1 at location s3 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "None", "plan_length": 10, "initial_state_nl": "Driver1 is at location s1, driver2 is present at location s3, driver3 is present at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and s2 have a link between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is currently at location s3, package2 is present at location s2, package3 is currently at location s2, package4 is currently at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s0, there is a link between location s2 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing, truck2 is at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: driver2 moves from location s3 to p3_0, then from p3_0 to s0, where driver2 gets into truck1. Next, driver2 drives truck1 from s0 to s2, loads package3 into truck1 at s2, and then loads package2 into truck1 at the same location. Driver2 then drives truck1 from s2 back to s0, unloads package2 from truck1 at s0, drives truck1 from s0 to s3, and finally loads package1 into truck1 at s3 to reach the current state. What is the first action in the sequence that cannot be executed? Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is situated at s3, and driver3 is also at s3. A path exists between p0_1 and s1, p1_2 and s2, and p3_0 and s3. Additionally, paths are present between s0 and p0_1, s0 and s1, s0 and s3, s1 and p1_2, s1 and s2, and s3 and s0. Furthermore, links exist between s0 and s1, s0 and s3, s1 and s2, s3 and s0, and s3 and s1. Package1 is currently at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Links are also present between s0 and s2, s1 and s3, and s2 and s1. Paths exist between p1_2 and s1, p1_3 and s3, s2 and p1_2, p0_1 and s0, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s0 and p3_0, s1 and p0_1, s1 and p1_3, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is at s0 and is empty, while truck2 is at s3 and contains nothing. Truck3 is also at s0 and contains nothing."}
{"question_id": "da0b6141-fa2c-4a13-afdf-11ea90114276", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to p4_3 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "driver2 walks to location p4_3 from location s4, driver3 walks from location s3 to location p4_3, driver1 walks to location s3 from location p4_3, driver1 walks from location p4_3 to location s4, driver2 walks to location p4_0 from location s4 and driver2 walks from location s4 to p4_1", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s3, driver2 is currently at location s4, driver3 is present at location s3, locations p5_2 and s2 have a path between them, locations s0 and p0_5 have a path between them, locations s1 and s4 have a link between them, locations s2 and p5_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s2 have a link between them, locations s3 and s4 have a link between them, locations s4 and p4_0 have a path between them, locations s5 and p0_5 have a path between them, locations s5 and s0 have a link between them, locations s5 and s2 have a link between them, package1 is at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is at location s0, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_1 and s1, there exists a path between the locations p4_1 and s4, there exists a path between the locations p4_3 and s3, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p4_0, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_1, there exists a path between the locations s4 and p4_3, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s2, there is a link between location s0 and location s4, there is a link between location s0 and location s5, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s2 and location s5, there is a link between location s4 and location s0, there is a link between location s4 and location s1, there is a link between location s4 and location s3, there is a link between location s4 and location s5, there is a link between location s5 and location s3, there is a path between location p0_5 and location s5, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s5, there is a path between location s1 and location p4_1, truck1 is currently at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: driver1 moves from location s3 to p4_3 to attain the current state. In this state, list all possible actions that can be executed. If there are no actions, indicate None.", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is at location s4, and driver3 is also at location s3. A path exists between locations p5_2 and s2, as well as between locations s0 and p0_5. Locations s1 and s4 are connected by a link, and locations s2 and p5_2 have a path between them. Additionally, locations s2 and s0 are linked, locations s3 and s2 are connected, and locations s3 and s4 are also linked. A path exists between locations s4 and p4_0, locations s5 and p0_5, and locations s5 and s0 are connected by a link. Locations s5 and s2 are also linked. Package1 is located at s2, package2 is currently at s2, package3 is at location s3, and package4 is at location s0. There are links between locations s0 and s1, s1 and s0, s3 and s5, and s5 and s4. Paths exist between locations p0_5 and s0, p4_0 and s0, p4_1 and s1, p4_1 and s4, p4_3 and s3, and p4_3 and s4. Furthermore, paths exist between locations s0 and p4_0, s3 and p4_3, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Links exist between locations s0 and s2, s0 and s4, s0 and s5, s1 and s2, s2 and s1, s2 and s3, s2 and s5, s4 and s0, s4 and s1, s4 and s3, s4 and s5, and s5 and s3. Paths also exist between locations p0_5 and s5, p4_0 and s4, p5_2 and s5, and s1 and p4_1. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "37287a55-b206-48c8-8856-5416d42d99b9", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: package3 is loaded in truck1 at location s0, driver1 walks to location p0_2 from location s2, driver1 walks from location p0_2 to location s0, driver1 boards truck1 at location s0, driver1 drives truck1 to location s3 from location s0, from truck1, driver1 disembarks at location s3, package3 is unloaded from truck1 at location s3, driver1 walks to location p0_3 from location s3, driver1 walks from location p0_3 to location s0, driver2 walks from location s2 to location p0_2, driver2 walks to location s0 from location p0_2, at location s0, driver2 boards truck2, driver2 drives truck2 from location s0 to location s1, at location s1, package1 is loaded in truck2, truck2 is driven from location s1 to s2 by driver2, at location s2, package2 is loaded in truck2, at location s2, package1 is unloaded in truck2, driver2 drives truck2 from location s2 to location s1 and driver2 disembarks from truck2 at location s1 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "driver1 walks from location s0 to p0_1, package2 is unloaded from truck2 at location s1, driver1 walks to location p0_2 from location s0, at location s1, driver2 boards truck2, package3 is loaded in truck1 at location s3, driver2 walks from location s1 to p2_1, driver1 walks from location s0 to location p0_3, driver2 walks from location s1 to location p1_3 and driver2 walks from location s1 to location p0_1", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_3 and s3 have a path between them, locations p1_3 and s3 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_1 have a path between them, locations s2 and p2_1 have a path between them, locations s2 and s0 have a link between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is present at location s0, package4 is currently at location s2, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_2, there exists a path between the locations s3 and p0_3, there is a link between location s1 and location s2, there is a link between location s3 and location s1, there is a path between location p0_2 and location s2, there is a path between location p2_1 and location s1, there is a path between location s0 and location p0_1, there is a path between location s2 and location p0_2, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: truck1 is loaded with package3 at location s0, driver1 moves from location s2 to location p0_2, then proceeds to location s0, boards truck1 at location s0, and drives it to location s3. Upon arrival, driver1 disembarks from truck1 at location s3, unloads package3, and walks to location p0_3 and then back to location s0. Meanwhile, driver2 moves from location s2 to location p0_2 and then to location s0, where they board truck2. Driver2 then drives truck2 to location s1, loads package1, drives to location s2, loads package2, unloads package1, and returns to location s1, where they disembark from truck2. In this state, list all possible actions that can be executed. If there are none, indicate None.", "initial_state_nl_paraphrased": "Driver1 is presently situated at location s2, while driver2 is also at location s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s2, s0 and p0_3, s1 and p0_1, s1 and p1_3, s1 and p2_1, s2 and p2_1, and s3 and p1_3. Furthermore, a link exists between locations s0 and s2, s0 and s3, s2 and s0, s3 and s0, s0 and s1, s1 and s0, s1 and s3, s2 and s1, s2 and s3, s3 and s2, s1 and s2, and s3 and s1. Package1 is currently located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. Additionally, there are paths between p0_1 and s1, p0_3 and s0, p1_3 and s1, s0 and p0_2, s3 and p0_3, p0_2 and s2, p2_1 and s1, s0 and p0_1, and s2 and p0_2. Lastly, truck1 is empty and situated at location s0, while truck2 is also at location s0 but contains nothing."}
{"question_id": "5b822603-7058-44d4-a23d-a8b34a5b6591", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to p3_0, driver2 walks from location p3_0 to s0, at location s0, driver2 boards truck1, driver2 drives truck1 from location s0 to location s2, package3 is loaded in truck1 at location s2, truck1 is loaded with package2 at location s2, driver2 drives truck1 to location s0 from location s2, at location s0, package2 is unloaded in truck1, driver2 drives truck1 to location s3 from location s0 and at location s3, package1 is loaded in truck1 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "driver3 walks from location s3 to location p1_3, driver2 drives truck1 to location s0 from location s3, driver1 walks from location s1 to location p1_3, at location s3, driver3 boards truck2, driver2 drives truck1 to location s1 from location s3, driver1 walks from location s1 to location p0_1, package1 is unloaded from truck1 at location s3, driver3 walks to location p3_0 from location s3, at location s3, driver2 disembarks from truck1, driver1 walks from location s1 to p1_2, package2 is loaded in truck3 at location s0 and package3 is unloaded from truck1 at location s3", "plan_length": 10, "initial_state_nl": "Driver1 is at location s1, driver2 is present at location s3, driver3 is present at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and s2 have a link between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is currently at location s3, package2 is present at location s2, package3 is currently at location s2, package4 is currently at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s0, there is a link between location s2 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing, truck2 is at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver2 moves from location s3 to p3_0, then from p3_0 to s0, where driver2 gets into truck1. Driver2 then drives truck1 from s0 to s2, where package3 and package2 are loaded into truck1. Next, driver2 drives truck1 back to s0, where package2 is unloaded. Finally, driver2 drives truck1 from s0 to s3, where package1 is loaded into truck1, resulting in the current state. In this state, list all possible actions that can be executed. If there are no actions, write None.", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is situated at s3, and driver3 is also at s3. A path exists between p0_1 and s1, p1_2 and s2, and p3_0 and s3. Additionally, paths are present between s0 and p0_1, s0 and s1, s0 and s3, s1 and p1_2, s1 and s2, and s3 and s0. Furthermore, links are established between s0 and s1, s0 and s3, s1 and s2, s3 and s0, and s3 and s1. Package1 is currently at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Links exist between s0 and s2, s1 and s3, and s2 and s1. Paths are also present between p1_2 and s1, p1_3 and s3, s2 and p1_2, p0_1 and s0, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s0 and p3_0, s1 and p0_1, s1 and p1_3, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is at s0 and is empty, while truck2 is at s3 and contains nothing. Truck3 is also at s0 and contains nothing."}
{"question_id": "b975bf61-3560-4f03-b49e-ebcf7cb6df5d", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location s0, driver3 boards truck1, truck1 is loaded with package3 at location s0, truck1 is loaded with package1 at location s0, truck1 is driven from location s0 to s3 by driver3, package1 is unloaded from truck1 at location s3, truck1 is driven from location s3 to s1 by driver3, from truck1, driver3 disembarks at location s1, package3 is unloaded from truck1 at location s1, at location s2, package2 is loaded in truck2 and driver1 walks from location s3 to p3_0 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "driver3 walks to location p0_1 from location s1, driver3 walks from location s1 to location p1_2, truck1 is boarded by driver3 at location s1, driver1 walks from location p3_0 to s3, driver3 walks to location p1_3 from location s1, package3 is loaded in truck1 at location s1, driver2 walks from location s3 to p1_3, driver2 walks to location p3_0 from location s3, driver1 walks from location p3_0 to s0 and package2 is unloaded from truck2 at location s2", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s3, driver2 is present at location s3, driver3 is currently at location s0, locations p0_1 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and s2 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is present at location s2, package3 is at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s3 and location s0, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: at location s0, driver3 gets into truck1, then truck1 is loaded with package3 and package1 at location s0. Next, driver3 drives truck1 from location s0 to s3, where package1 is unloaded. After that, truck1 is driven from location s3 to s1 by driver3, who then gets out of truck1 at location s1. Package3 is then unloaded from truck1 at location s1. Meanwhile, at location s2, package2 is loaded into truck2, and driver1 walks from location s3 to p3_0 to reach the current state. In this state, list all possible actions that can be executed. If there are no executable actions, write None.", "initial_state_nl_paraphrased": "Driver1 is located at s3, while driver2 is also at s3. Meanwhile, driver3 is currently situated at s0. There is a path connecting p0_1 and s0, as well as between p2_0 and s2, and p3_0 and s0. A link exists between s0 and s2, and another between s1 and p0_1, s1 and p1_2, s2 and p2_0, s2 and s0, s3 and p3_0, and s3 and s2. Package1 is currently at s0, package2 is at s2, and package3 is also at s0. Links are present between s0 and s3, s3 and s1, and paths exist between p1_2 and s1, p1_2 and s2, p1_3 and s3, s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p1_2. Additionally, links are found between s1 and s2, s1 and s3, s2 and s1, s2 and s3, and s3 and s0. Paths also exist between p0_1 and s1, p1_3 and s1, p2_0 and s0, p3_0 and s3, s1 and p1_3, and s3 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and situated at s2."}
{"question_id": "29cd061e-74f1-4cc0-8825-8fc802c8985a", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0, driver1 walks to location p0_2 from location s2, driver1 walks to location s0 from location p0_2, driver1 boards truck1 at location s0, driver1 drives truck1 to location s3 from location s0, driver1 disembarks from truck1 at location s3, truck1 is driven from location p2_1 to s2 by driver1, driver1 walks from location s3 to location p0_3, driver1 walks from location p0_3 to location s0, driver2 walks from location s2 to location p0_2, driver2 walks from location p0_2 to location s0, truck2 is boarded by driver2 at location s0, driver2 drives truck2 to location s1 from location s0, package1 is loaded in truck2 at location s1, truck2 is driven from location s1 to s2 by driver2, package2 is loaded in truck2 at location s2, at location s2, package1 is unloaded in truck2, driver2 drives truck2 to location s1 from location s2 and from truck2, driver2 disembarks at location s1 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "truck1 is driven from location p2_1 to s2 by driver1", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_3 and s3 have a path between them, locations p1_3 and s3 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_1 have a path between them, locations s2 and p2_1 have a path between them, locations s2 and s0 have a link between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is present at location s0, package4 is currently at location s2, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_2, there exists a path between the locations s3 and p0_3, there is a link between location s1 and location s2, there is a link between location s3 and location s1, there is a path between location p0_2 and location s2, there is a path between location p2_1 and location s1, there is a path between location s0 and location p0_1, there is a path between location s2 and location p0_2, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: package3 is loaded onto truck1 at location s0, driver1 moves from location s2 to p0_2, then to s0, boards truck1 at s0, and drives it to s3, where driver1 gets off the truck. Subsequently, driver1 drives truck1 from p2_1 to s2, then walks from s3 to p0_3 and back to s0. Meanwhile, driver2 moves from s2 to p0_2 and then to s0, boards truck2, and drives it to s1. At s1, package1 is loaded into truck2, which is then driven to s2 where package2 is loaded. However, at s2, package1 is unloaded from truck2, and then truck2 is driven back to s1, where driver2 gets off. What is the first action in the sequence that cannot be executed? If none, write None.", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s2, s0 and p0_3, s1 and p0_1, s1 and p1_3, s1 and p2_1, s2 and p2_1, and s3 and p1_3. Furthermore, a link exists between locations s0 and s2, s0 and s3, s2 and s0, s3 and s0, s0 and s1, s1 and s0, s1 and s3, s2 and s1, s2 and s3, and s3 and s2. Package1 is currently at location s1, package2 is at location s2, package3 is at location s0, and package4 is also at location s2. Additionally, links exist between s0 and s1, s1 and s0, s1 and s3, s2 and s1, s2 and s3, and s3 and s2. Paths also exist between p0_1 and s1, p0_3 and s0, p1_3 and s1, s0 and p0_2, s3 and p0_3, p0_2 and s2, p2_1 and s1, s0 and p0_1, and s2 and p0_2. Moreover, a link is present between s1 and s2, as well as between s3 and s1. Lastly, truck1 is located at s0 and is empty, while truck2 is also at s0 and contains nothing."}
{"question_id": "cb271a1e-4375-4b5f-9ab4-ac7da4897dc3", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s1 to location p1_2 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "driver1 walks from location s1 to location p1_2", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s3, driver2 is present at location s3, driver3 is currently at location s0, locations p0_1 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and s2 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is present at location s2, package3 is at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s3 and location s0, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: driver1 moves from location s1 to location p1_2 to attain the current state. What is the first action in the sequence that cannot be executed? If there are no such actions, write None.", "initial_state_nl_paraphrased": "Driver1 is located at s3, while driver2 is also at s3. Meanwhile, driver3 is currently situated at s0. A path exists between locations p0_1 and s0, as well as between p2_0 and s2, and p3_0 and s0. Locations s0 and s2 are connected by a link, and s1 has paths to both p0_1 and p1_2. Similarly, s2 has paths to p2_0 and is linked to s0. Location s3 is connected to p3_0 and has a link to s2. Package1 is currently at s0, package2 is at s2, and package3 is also at s0. Links exist between s0 and s3, s3 and s1, and paths exist between p1_2 and s1, p1_2 and s2, p1_3 and s3, s0 and p0_1, s0 and p2_0, s0 and p3_0, and s2 and p1_2. Furthermore, links are present between s1 and s2, s1 and s3, s2 and s1, s2 and s3, and s3 and s0. Paths also exist between p0_1 and s1, p1_3 and s1, p2_0 and s0, p3_0 and s3, s1 and p1_3, and s3 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and situated at s2."}
{"question_id": "f782e5e3-f379-4b49-b813-179633190b29", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to p4_3, driver1 walks to location s4 from location p4_3, driver1 walks from location s4 to location p4_1, driver1 walks from location p4_1 to location s1, truck1 is boarded by driver1 at location s1, driver1 drives truck1 from location s1 to location s0, at location s0, package4 is loaded in truck1, driver1 drives truck1 from location s0 to location s2, driver3 drives truck2 from location p4_0 to location p4_1, truck1 is loaded with package1 at location s2, driver1 drives truck1 from location s2 to location s3, truck1 is loaded with package3 at location s3, truck1 is unloaded with package1 at location s3, driver1 drives truck1 to location s4 from location s3, at location s4, package4 is unloaded in truck1, package3 is unloaded from truck1 at location s4, at location s4, package2 is unloaded in truck1, driver1 drives truck1 from location s4 to location s1 and from truck1, driver1 disembarks at location s1 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "truck2 is driven from location p4_0 to p4_1 by driver3", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s3, driver2 is currently at location s4, driver3 is present at location s3, locations p5_2 and s2 have a path between them, locations s0 and p0_5 have a path between them, locations s1 and s4 have a link between them, locations s2 and p5_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s2 have a link between them, locations s3 and s4 have a link between them, locations s4 and p4_0 have a path between them, locations s5 and p0_5 have a path between them, locations s5 and s0 have a link between them, locations s5 and s2 have a link between them, package1 is at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is at location s0, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_1 and s1, there exists a path between the locations p4_1 and s4, there exists a path between the locations p4_3 and s3, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p4_0, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_1, there exists a path between the locations s4 and p4_3, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s2, there is a link between location s0 and location s4, there is a link between location s0 and location s5, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s2 and location s5, there is a link between location s4 and location s0, there is a link between location s4 and location s1, there is a link between location s4 and location s3, there is a link between location s4 and location s5, there is a link between location s5 and location s3, there is a path between location p0_5 and location s5, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s5, there is a path between location s1 and location p4_1, truck1 is currently at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: driver1 moves from location s3 to p4_3, then proceeds to location s4 from p4_3, followed by a move from s4 to p4_1, and then from p4_1 to s1. At s1, driver1 boards truck1, and then drives it from s1 to s0. Upon arrival at s0, package4 is loaded onto truck1. Driver1 then drives truck1 from s0 to s2. Meanwhile, driver3 drives truck2 from p4_0 to p4_1. At s2, truck1 is loaded with package1, and driver1 drives it from s2 to s3. At s3, truck1 is loaded with package3 and unloaded with package1. Driver1 then drives truck1 from s3 to s4. At s4, packages 4 and 3 are unloaded from truck1, and package2 is also unloaded. Finally, driver1 drives truck1 from s4 to s1 and disembarks from truck1 at s1 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is at location s4, and driver3 is also at location s3. A path exists between locations p5_2 and s2, as well as between locations s0 and p0_5. Locations s1 and s4 are connected by a link, and locations s2 and p5_2 also have a path between them. Furthermore, locations s2 and s0 are linked, locations s3 and s2 are connected, and locations s3 and s4 are also linked. A path exists between locations s4 and p4_0, locations s5 and p0_5, and locations s5 and s0 are connected by a link. Locations s5 and s2 are also linked. Package1 is located at s2, package2 is currently at s2, package3 is at location s3, and package4 is at location s0. There are links between locations s0 and s1, s1 and s0, s3 and s5, and s5 and s4. Paths exist between locations p0_5 and s0, p4_0 and s0, p4_1 and s1, p4_1 and s4, p4_3 and s3, and p4_3 and s4. Additionally, paths exist between locations s0 and p4_0, s3 and p4_3, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Links exist between locations s0 and s2, s0 and s4, s0 and s5, s1 and s2, s2 and s1, s2 and s3, s2 and s5, s4 and s0, s4 and s1, s4 and s3, s4 and s5, and s5 and s3. Paths also exist between locations p0_5 and s5, p4_0 and s4, p5_2 and s5, and s1 and p4_1. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "9ec6b86c-236c-4efd-9659-4d6ceef2908c", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to location p3_0, driver2 walks from location p3_0 to location s0, driver2 boards truck1 at location s0, driver2 drives truck1 to location s2 from location s0, at location s2, package3 is loaded in truck1, at location s2, package2 is loaded in truck1, driver2 drives truck1 from location s2 to location s0, at location s0, package2 is unloaded in truck1, driver2 drives truck1 to location s3 from location s0, package1 is loaded in truck1 at location s3, driver2 drives truck1 from location s3 to location s1, from truck1, driver2 disembarks at location s1, driver2 walks from location s1 to p0_1, driver2 walks from location p0_1 to location s0, driver2 boards truck3 at location s0, truck3 is driven from location s0 to s2 by driver2, at location s1, package3 is unloaded in truck1, package1 is unloaded from truck1 at location s1 and driver3 walks to location p3_0 from location s3 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "driver3 walks from location p3_0 to location s0, driver1 walks from location s1 to location p1_3, driver1 walks from location s1 to location p0_1, package4 is loaded in truck1 at location s1, driver3 walks from location p3_0 to s3, from truck3, driver2 disembarks at location s2, truck1 is loaded with package3 at location s1, driver2 drives truck3 to location s1 from location s2, truck1 is loaded with package1 at location s1, driver1 walks to location p1_2 from location s1, at location s1, driver1 boards truck1 and truck3 is driven from location s2 to s0 by driver2", "plan_length": 19, "initial_state_nl": "Driver1 is at location s1, driver2 is present at location s3, driver3 is present at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and s2 have a link between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is currently at location s3, package2 is present at location s2, package3 is currently at location s2, package4 is currently at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s0, there is a link between location s2 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing, truck2 is at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver2 moves from location s3 to p3_0, then from p3_0 to s0, where driver2 gets into truck1. Driver2 then drives truck1 from s0 to s2, where package3 and package2 are loaded into truck1. Next, driver2 drives truck1 back to s0, unloads package2, and then drives to s3, where package1 is loaded into truck1. Driver2 then drives truck1 to s1, disembarks, and walks to p0_1 and then to s0. At s0, driver2 boards truck3 and drives it to s2. Meanwhile, package3 and package1 are unloaded from truck1 at s1. Additionally, driver3 walks from s3 to p3_0 to reach the current state. In this state, list all possible actions that can be executed. If there are no actions, write None.", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is situated at s3, and driver3 is also at s3. A path exists between p0_1 and s1, as well as between p1_2 and s2, and p3_0 and s3. Additionally, paths are present between s0 and p0_1, s0 and s1, s0 and s3, s1 and p1_2, s1 and s2, and s3 and s0. Furthermore, links are established between s0 and s1, s0 and s3, s1 and s3, s2 and s1, s0 and s2, s1 and s0, and s2 and s0. Package1 is currently situated at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Moreover, links exist between s0 and s2, s1 and s3, and s2 and s1. Paths are also present between p1_2 and s1, p1_3 and s3, s2 and p1_2, p0_1 and s0, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s0 and p3_0, s1 and p0_1, s1 and p1_3, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is currently empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and situated at s0."}
{"question_id": "35a9b037-1f15-488b-b96d-58b39474d2d1", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "driver1 walks from location s2 to p0_2, at location s0, package3 is unloaded in truck1, driver2 walks to location p2_1 from location s2, driver1 walks to location p2_1 from location s2 and driver2 walks to location p0_2 from location s2", "plan_length": 1, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_3 and s3 have a path between them, locations p1_3 and s3 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_1 have a path between them, locations s2 and p2_1 have a path between them, locations s2 and s0 have a link between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is present at location s0, package4 is currently at location s2, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_2, there exists a path between the locations s3 and p0_3, there is a link between location s1 and location s2, there is a link between location s3 and location s1, there is a path between location p0_2 and location s2, there is a path between location p2_1 and location s1, there is a path between location s0 and location p0_1, there is a path between location s2 and location p0_2, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: truck1 is loaded with package3 at location s0 to achieve the current state. In this state, list all possible actions that can be executed. If there are no actions, write None.", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at location s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s2, s0 and p0_3, s1 and p0_1, s1 and p1_3, s1 and p2_1, s2 and p2_1, s3 and p1_3. Furthermore, a link exists between locations s0 and s2, s0 and s3, s2 and s0, s3 and s0. Package1 is currently situated at location s1, package2 is at location s2, package3 is at location s0, and package4 is also at location s2. Additionally, links exist between locations s0 and s1, s1 and s0, s1 and s3, s2 and s1, s2 and s3, s3 and s2, s1 and s2, and s3 and s1. Paths also exist between locations p0_1 and s1, p0_3 and s0, p1_3 and s1, s0 and p0_2, s3 and p0_3, p0_2 and s2, p2_1 and s1, s0 and p0_1, and s2 and p0_2. Lastly, truck1 is located at s0 and is empty, while truck2 is also at location s0 and contains nothing."}
{"question_id": "df5e870f-e0eb-4c8f-ba3b-128c1558b02f", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks to location p4_3 from location s3, driver1 walks to location s4 from location p4_3, driver1 walks from location s4 to location p4_1, driver1 walks to location s1 from location p4_1, truck1 is boarded by driver1 at location s1, driver1 drives truck1 to location s0 from location s1, package4 is loaded in truck1 at location s0, driver1 drives truck1 to location s2 from location s0, truck1 is loaded with package2 at location s2 and truck2 is driven from location p4_0 to p0_5 by driver2 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "truck2 is driven from location p4_0 to p0_5 by driver2", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s3, driver2 is currently at location s4, driver3 is present at location s3, locations p5_2 and s2 have a path between them, locations s0 and p0_5 have a path between them, locations s1 and s4 have a link between them, locations s2 and p5_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s2 have a link between them, locations s3 and s4 have a link between them, locations s4 and p4_0 have a path between them, locations s5 and p0_5 have a path between them, locations s5 and s0 have a link between them, locations s5 and s2 have a link between them, package1 is at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is at location s0, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_1 and s1, there exists a path between the locations p4_1 and s4, there exists a path between the locations p4_3 and s3, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p4_0, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_1, there exists a path between the locations s4 and p4_3, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s2, there is a link between location s0 and location s4, there is a link between location s0 and location s5, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s2 and location s5, there is a link between location s4 and location s0, there is a link between location s4 and location s1, there is a link between location s4 and location s3, there is a link between location s4 and location s5, there is a link between location s5 and location s3, there is a path between location p0_5 and location s5, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s5, there is a path between location s1 and location p4_1, truck1 is currently at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by s4 to p4_1, and then from p4_1 to s1. At location s1, driver1 boards truck1 and drives it from s1 to s0. At location s0, package4 is loaded into truck1, and then driver1 drives truck1 from s0 to s2. At location s2, truck1 is loaded with package2, and simultaneously, driver2 drives truck2 from p4_0 to p0_5 to reach the current state. What is the first action in the sequence that cannot be executed? If none, write None.", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is at location s4, and driver3 is also at location s3. A path exists between locations p5_2 and s2, as well as between locations s0 and p0_5. Locations s1 and s4 are connected by a link, and locations s2 and p5_2 also have a path between them. Furthermore, locations s2 and s0 are linked, locations s3 and s2 are connected, and locations s3 and s4 are also linked. A path exists between locations s4 and p4_0, locations s5 and p0_5, and locations s5 and s0 are connected by a link. Locations s5 and s2 are also linked. Package1 is located at s2, package2 is currently at s2, package3 is at location s3, and package4 is at location s0. There are links between locations s0 and s1, s1 and s0, s3 and s5, and s5 and s4. Paths exist between locations p0_5 and s0, p4_0 and s0, p4_1 and s1, p4_1 and s4, p4_3 and s3, and p4_3 and s4. Additionally, paths exist between locations s0 and p4_0, s3 and p4_3, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Links exist between locations s0 and s2, s0 and s4, s0 and s5, s1 and s2, s2 and s1, s2 and s3, s2 and s5, s4 and s0, s4 and s1, s4 and s3, s4 and s5, and s5 and s3. Paths also exist between locations p0_5 and s5, p4_0 and s4, p5_2 and s5, and s1 and p4_1. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "29260f86-ec80-476f-9868-38f0710d0d29", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 drives truck2 to location s1 from location p3_0 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "truck2 is driven from location p3_0 to s1 by driver2", "plan_length": 1, "initial_state_nl": "Driver1 is at location s1, driver2 is present at location s3, driver3 is present at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and s2 have a link between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is currently at location s3, package2 is present at location s2, package3 is currently at location s2, package4 is currently at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s0, there is a link between location s2 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing, truck2 is at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: driver2 drives truck2 from location p3_0 to location s1 to reach the current state. What is the first action in the sequence that cannot be executed? Write None if there are no such actions.", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is situated at s3, and driver3 is also at s3. A path exists between p0_1 and s1, another path is present between p1_2 and s2, and a path connects p3_0 to s3. Additionally, a path is available between s0 and p0_1, while a link connects s0 to s1 and another link connects s0 to s3. Furthermore, a path is present between s1 and p1_2, and a link connects s1 to s2. A link also exists between s3 and s0, as well as between s3 and s1. Package1 is currently situated at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Links exist between s0 and s2, s1 and s3, and s2 and s1. Paths are also present between p1_2 and s1, p1_3 and s3, and s2 and p1_2. Moreover, a link connects s1 to s0, and another link connects s2 to s0. Paths are available between p0_1 and s0, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s0 and p3_0, s1 and p0_1, s1 and p1_3, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is currently at s0 and is empty, while truck2 is at s3 and contains nothing. Truck3 is also at s0 and contains nothing."}
{"question_id": "f0f14681-27c5-4c8f-9266-967f7be0f296", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is boarded by driver3 at location s0, package3 is loaded in truck1 at location s0, at location s0, package1 is loaded in truck1, driver3 drives truck1 from location s0 to location s3, at location s3, package1 is unloaded in truck1, driver3 drives truck1 from location s3 to location s1, from truck1, driver3 disembarks at location s1, package3 is unloaded from truck1 at location s1, at location s2, package2 is loaded in truck2, driver1 walks to location p3_0 from location s3, driver1 walks from location p3_0 to location s0, driver2 walks to location p1_3 from location s3, driver2 walks from location p1_3 to location s1, driver2 walks from location s1 to p1_2, driver2 walks to location s2 from location p1_2, driver3 walks to location p1_2 from location s1, driver3 walks from location p1_2 to location s2, truck2 is boarded by driver3 at location s2 and driver3 drives truck2 from location s2 to location s3 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "driver3 drives truck2 from location s3 to location s0, driver1 walks to location p2_0 from location s0, driver3 disembarks from truck2 at location s3, at location s3, package2 is unloaded in truck2, driver3 drives truck2 from location s3 to location s1, driver1 walks from location s0 to location p3_0, at location s1, package3 is loaded in truck1, at location s3, package1 is loaded in truck2, driver1 walks from location s0 to location p0_1, driver2 walks from location s2 to location p1_2, driver3 drives truck2 to location s2 from location s3 and driver2 walks to location p2_0 from location s2", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s3, driver2 is present at location s3, driver3 is currently at location s0, locations p0_1 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and s2 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is present at location s2, package3 is at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s3 and location s0, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver3 boards truck1 at location s0, package3 is loaded onto truck1 at location s0, and package1 is also loaded onto truck1 at location s0. Then, driver3 drives truck1 from location s0 to location s3, where package1 is unloaded from truck1. Next, driver3 drives truck1 from location s3 to location s1, where driver3 disembarks from truck1, and package3 is unloaded from truck1. Meanwhile, at location s2, package2 is loaded onto truck2. Driver1 walks from location s3 to location p3_0 and then to location s0. Driver2 walks from location s3 to location p1_3, then to location s1, followed by location p1_2, and finally to location s2. Driver3 walks from location s1 to location p1_2 and then to location s2, where driver3 boards truck2 and drives it from location s2 to location s3, resulting in the current state. In this state, list all possible actions that can be executed. If there are no possible actions, write None.", "initial_state_nl_paraphrased": "Driver1 is located at s3, while driver2 is also at s3. Meanwhile, driver3 is currently situated at s0. There is a path connecting p0_1 and s0, as well as between p2_0 and s2, and p3_0 and s0. A link exists between s0 and s2, and another between s1 and p0_1, s1 and p1_2, s2 and p2_0, s2 and s0, s3 and p3_0, and s3 and s2. Package1 is currently at s0, package2 is at s2, and package3 is also at s0. Links are present between s0 and s3, s3 and s1, and paths exist between p1_2 and s1, p1_2 and s2, p1_3 and s3, s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p1_2. Additionally, links are found between s1 and s2, s1 and s3, s2 and s1, s2 and s3, and s3 and s0. Paths also exist between p0_1 and s1, p1_3 and s1, p2_0 and s0, p3_0 and s3, s1 and p1_3, and s3 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and at location s2."}
