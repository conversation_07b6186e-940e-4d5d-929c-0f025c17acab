{"question_id": "48b3e8c6-b4c8-436e-bc9d-181e803912a4", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver3 boards truck1 at location s0, at location s0, package3 is loaded in truck1, package1 is loaded in truck1 at location s0, driver3 drives truck1 to location s3 from location s0, at location s3, package1 is unloaded in truck1, truck1 is driven from location s3 to s1 by driver3, at location s1, driver3 disembarks from truck1, package3 is unloaded from truck1 at location s1, at location s2, package2 is loaded in truck2, driver1 walks from location s3 to location p3_0, driver1 walks from location p3_0 to location s0, driver2 walks from location s3 to location p1_3, driver2 walks to location s1 from location p1_3, driver2 walks to location p1_2 from location s1, driver2 walks to location s2 from location p1_2, driver3 walks to location p1_2 from location s1, driver3 walks from location p1_2 to location s2, driver3 boards truck2 at location s2 and driver3 drives truck2 from location s2 to location s3 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: truck1 is empty and truck2 is empty?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s3, driver2 is present at location s3, driver3 is at location s0, locations p0_1 and s0 have a path between them, locations p1_3 and s3 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and p3_0 have a path between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is currently at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s2 and p1_2, there exists a path between the locations s3 and p3_0, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s1, there is a path between location p3_0 and location s3, there is a path between location s0 and location p2_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_2, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is present at location s2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver3 gets on truck1 at location s0, package3 is loaded onto truck1 at location s0, and package1 is also loaded onto truck1 at location s0. Then, driver3 drives truck1 from location s0 to location s3, where package1 is unloaded from truck1. Next, truck1 is driven by driver3 from location s3 to location s1, where driver3 gets off truck1, and package3 is unloaded from truck1. Meanwhile, at location s2, package2 is loaded onto truck2. Additionally, driver1 walks from location s3 to location p3_0, then to location s0, while driver2 walks from location s3 to location p1_3, then to location s1, then to location p1_2, and finally to location s2. Driver3 also walks from location s1 to location p1_2 and then to location s2, where driver3 gets on truck2 and drives it from location s2 to location s3, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: truck1 is empty and truck2 is empty?", "initial_state_nl_paraphrased": "Driver1 is situated at location s3, while driver2 is also present at location s3. Meanwhile, driver3 is located at s0. A path exists between locations p0_1 and s0, as well as between p1_3 and s3, p2_0 and s2, and p3_0 and s0. Additionally, a path is present between s0 and p3_0, s3 and p1_3, and a link connects s0 and s3. Package1 is currently at location s0, package2 is at location s2, and package3 is also at location s0. Links exist between locations s0 and s3, s2 and s0, s2 and s1, s0 and s2, s1 and s2, s1 and s3, s2 and s3, and s3 and s1. Furthermore, paths are present between p1_2 and s2, p1_3 and s1, p2_0 and s0, s0 and p0_1, s2 and p1_2, s3 and p3_0, p0_1 and s1, p1_2 and s1, p3_0 and s3, s0 and p2_0, s1 and p0_1, s1 and p1_2, s1 and p1_3, and s2 and p2_0. Truck1 is currently at location s0 and is empty, while truck2 is at location s2 and contains nothing."}
{"question_id": "a055c9fb-d329-4f75-a9ca-02f1c24f12a3", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_5", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0 to reach the current state. In this state, is it True or False that truck1 is empty?", "answer": "False", "plan_length": 1, "initial_state_nl": "Driver1 is at location s2, driver2 is present at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_2 and s2 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_3 have a path between them, locations s2 and p0_2 have a path between them, locations s2 and s1 have a link between them, locations s2 and s3 have a link between them, locations s3 and p0_3 have a path between them, locations s3 and s1 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is at location s0, package4 is at location s2, there exists a link between the locations s1 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p2_1, there is a link between location s0 and location s1, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s3 and location s0, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_3 and location s0, there is a path between location p0_3 and location s3, there is a path between location p1_3 and location s3, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0 to reach the current state. In this state, is it True or False that truck1 has no packages?\n\nor \n\nGiven the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0 to reach the current state. In this state, is it True or False that truck1 is not carrying any packages?\n\nor \n\nGiven the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0 to reach the current state. In this state, is it True or False that truck1 contains no packages?", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, and p0_2 and s2. Similarly, paths are present between s0 and p0_1, s0 and p0_2, and s0 and p0_3. A link connects locations s0 and s2, and another link exists between s0 and s3. A path is present between s1 and p1_3, and between s2 and p0_2. Locations s2 and s1 are linked, as are s2 and s3, and s3 and s1. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link exists between s1 and s0, and a path is present between p1_3 and s1, s1 and p0_1, and s1 and p2_1. Additionally, links connect s0 and s1, s1 and s2, and s1 and s3, as well as s2 and s0, s3 and s0, and s3 and s2. Paths are present between p0_1 and s1, p0_3 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s1, and p2_1 and s2. Furthermore, a path exists between s2 and p2_1, and between s3 and p1_3. Truck1 is located at s0 and is empty, while truck2 is also at s0 and contains nothing."}
{"question_id": "ad8232f1-fbdf-4d87-8346-985c4e349f0b", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_16", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to p4_3 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: driver1 is not at location s5, driver1 is not currently at location p0_5, driver1 is not currently at location p5_2, driver1 is not currently at location s1, driver1 is not currently at location s3, driver1 is not currently at location s4, driver1 is not driving truck1, driver1 is not present at location p4_0, driver1 is not present at location p4_1, driver1 is not present at location p4_3, driver1 is not present at location s0, driver1 is not present at location s2, driver2 is not at location p4_0, driver2 is not at location p4_1, driver2 is not at location p4_3, driver2 is not at location p5_2, driver2 is not at location s2, driver2 is not currently at location s0, driver2 is not currently at location s1, driver2 is not currently at location s4, driver2 is not currently at location s5, driver2 is not driving truck1 currently, driver2 is not present at location p0_5, driver2 is not present at location s3, driver3 is not at location p4_1, driver3 is not at location p5_2, driver3 is not at location s3, driver3 is not currently at location p4_0, driver3 is not currently at location p4_3, driver3 is not currently at location s4, driver3 is not currently at location s5, driver3 is not driving truck1, driver3 is not driving truck2 currently, driver3 is not present at location p0_5, driver3 is not present at location s0, driver3 is not present at location s1, driver3 is not present at location s2, package1 is not at location p0_5, package1 is not at location p4_3, package1 is not at location s2, package1 is not currently at location s1, package1 is not currently at location s3, package1 is not currently at location s4, package1 is not currently at location s5, package1 is not located in truck1, package1 is not placed in truck2, package1 is not present at location p4_0, package1 is not present at location p4_1, package1 is not present at location p5_2, package1 is not present at location s0, package2 is not at location p4_1, package2 is not at location s0, package2 is not at location s2, package2 is not at location s3, package2 is not currently at location p4_0, package2 is not currently at location p5_2, package2 is not in truck2, package2 is not placed in truck1, package2 is not present at location p0_5, package2 is not present at location p4_3, package2 is not present at location s1, package2 is not present at location s4, package2 is not present at location s5, package3 is not at location p5_2, package3 is not at location s0, package3 is not at location s1, package3 is not at location s2, package3 is not at location s5, package3 is not currently at location p0_5, package3 is not currently at location s3, package3 is not in truck2, package3 is not placed in truck1, package3 is not present at location p4_0, package3 is not present at location p4_1, package3 is not present at location p4_3, package3 is not present at location s4, package4 is not at location p4_0, package4 is not at location p4_3, package4 is not at location p5_2, package4 is not at location s3, package4 is not at location s4, package4 is not currently at location p4_1, package4 is not currently at location s1, package4 is not located in truck1, package4 is not located in truck2, package4 is not present at location p0_5, package4 is not present at location s0, package4 is not present at location s2, package4 is not present at location s5, truck1 is not at location p4_0, truck1 is not at location p5_2, truck1 is not at location s4, truck1 is not currently at location p0_5, truck1 is not currently at location p4_3, truck1 is not currently at location s0, truck1 is not currently at location s2, truck1 is not currently at location s3, truck1 is not present at location p4_1, truck1 is not present at location s1, truck1 is not present at location s5, truck2 is not at location p0_5, truck2 is not at location p4_0, truck2 is not at location p5_2, truck2 is not at location s1, truck2 is not at location s5, truck2 is not being driven by driver1, truck2 is not being driven by driver2, truck2 is not currently at location p4_1, truck2 is not currently at location s2, truck2 is not present at location p4_3, truck2 is not present at location s0, truck2 is not present at location s3 and truck2 is not present at location s4?", "answer": "False", "plan_length": 1, "initial_state_nl": "Driver1 is currently at location s3, driver2 is at location s4, driver3 is at location s3, locations p0_5 and s0 have a path between them, locations p0_5 and s5 have a path between them, locations p4_0 and s0 have a path between them, locations p4_1 and s4 have a path between them, locations p4_3 and s3 have a path between them, locations p4_3 and s4 have a path between them, locations s0 and s2 have a link between them, locations s0 and s5 have a link between them, locations s1 and s2 have a link between them, locations s1 and s4 have a link between them, locations s2 and p5_2 have a path between them, locations s2 and s0 have a link between them, locations s2 and s5 have a link between them, locations s3 and s2 have a link between them, locations s3 and s5 have a link between them, locations s4 and s1 have a link between them, locations s4 and s5 have a link between them, locations s5 and p0_5 have a path between them, locations s5 and s3 have a link between them, package1 is present at location s2, package2 is at location s2, package3 is at location s3, package4 is present at location s0, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s0, there exists a link between the locations s5 and s0, there exists a link between the locations s5 and s2, there exists a path between the locations p4_0 and s4, there exists a path between the locations s1 and p4_1, there exists a path between the locations s4 and p4_3, there is a link between location s0 and location s4, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s4 and location s3, there is a link between location s5 and location s4, there is a path between location p4_1 and location s1, there is a path between location p5_2 and location s2, there is a path between location p5_2 and location s5, there is a path between location s0 and location p0_5, there is a path between location s0 and location p4_0, there is a path between location s3 and location p4_3, there is a path between location s4 and location p4_0, there is a path between location s4 and location p4_1, there is a path between location s5 and location p5_2, truck1 contains nothing, truck1 is at location s1, truck2 is currently at location s5 and truck2 is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: driver1 moves from location s3 to p4_3 to reach the current state. In this state, are all of the following properties that involve negations True or False: driver1 is not located at s5, driver1 is not at p0_5, driver1 is not at p5_2, driver1 is not at s1, driver1 is not at s3, driver1 is not at s4, driver1 is not operating truck1, driver1 is not at p4_0, driver1 is not at p4_1, driver1 is not at p4_3, driver1 is not at s0, driver1 is not at s2, driver2 is not at p4_0, driver2 is not at p4_1, driver2 is not at p4_3, driver2 is not at p5_2, driver2 is not at s2, driver2 is not at s0, driver2 is not at s1, driver2 is not at s4, driver2 is not at s5, driver2 is not operating truck1, driver2 is not at p0_5, driver2 is not at s3, driver3 is not at p4_1, driver3 is not at p5_2, driver3 is not at s3, driver3 is not at p4_0, driver3 is not at p4_3, driver3 is not at s4, driver3 is not at s5, driver3 is not operating truck1, driver3 is not operating truck2, driver3 is not at p0_5, driver3 is not at s0, driver3 is not at s1, driver3 is not at s2, package1 is not at p0_5, package1 is not at p4_3, package1 is not at s2, package1 is not at s1, package1 is not at s3, package1 is not at s4, package1 is not at s5, package1 is not in truck1, package1 is not in truck2, package1 is not at p4_0, package1 is not at p4_1, package1 is not at p5_2, package1 is not at s0, package2 is not at p4_1, package2 is not at s0, package2 is not at s2, package2 is not at s3, package2 is not at p4_0, package2 is not at p5_2, package2 is not in truck2, package2 is not in truck1, package2 is not at p0_5, package2 is not at p4_3, package2 is not at s1, package2 is not at s4, package2 is not at s5, package3 is not at p5_2, package3 is not at s0, package3 is not at s1, package3 is not at s2, package3 is not at s5, package3 is not at p0_5, package3 is not at s3, package3 is not in truck2, package3 is not in truck1, package3 is not at p4_0, package3 is not at p4_1, package3 is not at p4_3, package3 is not at s4, package4 is not at p4_0, package4 is not at p4_3, package4 is not at p5_2, package4 is not at s3, package4 is not at s4, package4 is not at p4_1, package4 is not at s1, package4 is not in truck1, package4 is not in truck2, package4 is not at p0_5, package4 is not at s0, package4 is not at s2, package4 is not at s5, truck1 is not at p4_0, truck1 is not at p5_2, truck1 is not at s4, truck1 is not at p0_5, truck1 is not at p4_3, truck1 is not at s0, truck1 is not at s2, truck1 is not at s3, truck1 is not at p4_1, truck1 is not at s1, truck1 is not at s5, truck2 is not at p0_5, truck2 is not at p4_0, truck2 is not at p5_2, truck2 is not at s1, truck2 is not at s5, truck2 is not driven by driver1, truck2 is not driven by driver2, truck2 is not at p4_1, truck2 is not at s2, truck2 is not at p4_3, truck2 is not at s0, truck2 is not at s3 and truck2 is not at s4?", "initial_state_nl_paraphrased": "Driver1 is situated at location s3, while driver2 is at location s4, and driver3 is also at location s3. A path exists between locations p0_5 and s0, as well as between p0_5 and s5. Additionally, a path is present between p4_0 and s0, p4_1 and s4, p4_3 and s3, and p4_3 and s4. Locations s0 and s2 are connected by a link, as are s0 and s5, s1 and s2, s1 and s4, s2 and s0, s2 and s5, s3 and s2, s3 and s5, s4 and s1, and s4 and s5. Furthermore, a path connects s2 and p5_2, s5 and p0_5, s5 and s3. Package1 and package2 are both located at s2, package3 is at s3, and package4 is at s0. Links exist between s0 and s1, s1 and s0, s3 and s4, s4 and s0, s5 and s0, and s5 and s2. Paths are also present between p4_0 and s4, s1 and p4_1, s4 and p4_3, s0 and p0_5, s0 and p4_0, s3 and p4_3, s4 and p4_0, s4 and p4_1, s5 and p5_2, p4_1 and s1, p5_2 and s2, and p5_2 and s5. Moreover, links exist between s0 and s4, s2 and s1, s2 and s3, s4 and s3, and s5 and s4. Truck1 is empty and located at s1, while truck2 is at location s5 and is also empty."}
{"question_id": "0fbf625d-02f6-49db-93f2-daad193bbf61", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver3 boards truck1 at location s0, truck1 is loaded with package3 at location s0, package1 is loaded in truck1 at location s0, truck1 is driven from location s0 to s3 by driver3, package1 is unloaded from truck1 at location s3, driver3 drives truck1 to location s1 from location s3, driver3 disembarks from truck1 at location s1, truck1 is unloaded with package3 at location s1, package2 is loaded in truck2 at location s2 and driver1 walks from location s3 to p3_0 to reach the current state. In this state, is it True or False that truck1 contains some package?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s3, driver2 is present at location s3, driver3 is at location s0, locations p0_1 and s0 have a path between them, locations p1_3 and s3 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and p3_0 have a path between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is currently at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s2 and p1_2, there exists a path between the locations s3 and p3_0, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s1, there is a path between location p3_0 and location s3, there is a path between location s0 and location p2_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_2, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is present at location s2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver3 gets on truck1 at location s0, truck1 is loaded with package3 at location s0, package1 is also loaded into truck1 at location s0, then driver3 drives truck1 from location s0 to s3, package1 is then unloaded from truck1 at location s3, driver3 then drives truck1 from location s3 to location s1, driver3 gets off truck1 at location s1, truck1 is then unloaded with package3 at location s1, package2 is loaded into truck2 at location s2, and driver1 walks from location s3 to p3_0 to reach the current state. In this state, is it True or False that truck1 contains some package?", "initial_state_nl_paraphrased": "Driver1 is situated at location s3, while driver2 is also present at location s3. Meanwhile, driver3 is located at s0. A path exists between locations p0_1 and s0, as well as between p1_3 and s3, p2_0 and s2, and p3_0 and s0. Additionally, a path is present between s0 and p3_0, s3 and p1_3, and a link connects s0 and s3. Package1 is currently at location s0, package2 is at location s2, and package3 is also at location s0. Links exist between locations s0 and s3, s2 and s0, and s2 and s1. Furthermore, paths are present between p1_2 and s2, p1_3 and s1, p2_0 and s0, s0 and p0_1, s2 and p1_2, and s3 and p3_0. There are also links between s0 and s2, s1 and s2, s1 and s3, s2 and s3, s3 and s1, and s3 and s2. Moreover, paths exist between p0_1 and s1, p1_2 and s1, p3_0 and s3, s0 and p2_0, s1 and p0_1, s1 and p1_2, s1 and p1_3, and s2 and p2_0. Truck1 is currently at location s0 and is empty, while truck2 is at location s2 and contains nothing."}
{"question_id": "db5f70d4-5fec-4dfc-bab9-c84f4d03e574", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_6", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location s0, driver3 boards truck1 to reach the current state. In this state, is it True or False that truck1 is empty?", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is currently at location s3, driver2 is present at location s3, driver3 is at location s0, locations p0_1 and s0 have a path between them, locations p1_3 and s3 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and p3_0 have a path between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is currently at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s2 and p1_2, there exists a path between the locations s3 and p3_0, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s1, there is a path between location p3_0 and location s3, there is a path between location s0 and location p2_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_2, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is present at location s2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: at location s0, driver3 boards truck1 to reach the current state. In this state, is it True or False that truck1 is empty?\n\nParaphrased text: \nGiven the initial condition, the following actions are performed: at location s0, driver3 boards truck1 to reach the current state. In this state, is truck1 empty: True or False?", "initial_state_nl_paraphrased": "Driver1 is situated at location s3, while driver2 is also present at location s3. Meanwhile, driver3 is located at s0. A path exists between locations p0_1 and s0, as well as between p1_3 and s3, p2_0 and s2, and p3_0 and s0. Additionally, a path is present between s0 and p3_0, s3 and p1_3, and a link connects s3 and s0. Package1 is currently at location s0, package2 is at location s2, and package3 is also at location s0. Furthermore, a link exists between locations s0 and s3, s2 and s0, and s2 and s1. A path is also present between p1_2 and s2, p1_3 and s1, p2_0 and s0, s0 and p0_1, s2 and p1_2, and s3 and p3_0. Moreover, links exist between locations s0 and s2, s1 and s2, s1 and s3, s2 and s3, s3 and s1, and s3 and s2. Paths are also present between p0_1 and s1, p1_2 and s1, p3_0 and s3, s0 and p2_0, s1 and p0_1, s1 and p1_2, s1 and p1_3, and s2 and p2_0. Truck1 is currently at location s0 and is empty, while truck2 is at location s2 and contains nothing."}
{"question_id": "95bbcb2e-05a2-4839-9f67-8cd555e352f6", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_14", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: package3 is loaded in truck1 at location s0, driver1 walks from location s2 to p0_2, driver1 walks from location p0_2 to s0, driver1 boards truck1 at location s0, driver1 drives truck1 to location s3 from location s0, at location s3, driver1 disembarks from truck1, truck1 is unloaded with package3 at location s3, driver1 walks from location s3 to p0_3, driver1 walks from location p0_3 to location s0 and driver2 walks from location s2 to p0_2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: driver1 is at location p0_2, driver1 is at location p0_3, driver1 is at location p1_3, driver1 is at location s3, driver1 is currently at location s0, driver1 is driving truck1 currently, driver1 is driving truck2 currently, driver1 is present at location p0_1, driver1 is present at location p2_1, driver1 is present at location p3_0, driver1 is present at location s1, driver1 is present at location s2, driver2 is at location p0_1, driver2 is at location p0_3, driver2 is at location s0, driver2 is currently at location p2_1, driver2 is currently at location s1, driver2 is currently at location s2, driver2 is driving truck1 currently, driver2 is driving truck2 currently, driver2 is present at location p0_2, driver2 is present at location p1_3, driver2 is present at location p3_0, driver2 is present at location s3, package1 is at location p0_1, package1 is at location p0_3, package1 is at location s3, package1 is currently at location p3_0, package1 is currently at location s0, package1 is currently at location s1, package1 is currently at location s2, package1 is located in truck2, package1 is placed in truck1, package1 is present at location p0_2, package1 is present at location p1_3, package1 is present at location p2_1, package2 is at location p0_1, package2 is at location p0_2, package2 is at location p0_3, package2 is at location p2_1, package2 is currently at location p1_3, package2 is currently at location s2, package2 is located in truck2, package2 is placed in truck1, package2 is present at location p3_0, package2 is present at location s0, package2 is present at location s1, package2 is present at location s3, package3 is at location p0_2, package3 is at location p0_3, package3 is at location p1_3, package3 is at location s0, package3 is at location s3, package3 is currently at location s1, package3 is located in truck2, package3 is placed in truck1, package3 is present at location p0_1, package3 is present at location p2_1, package3 is present at location p3_0, package3 is present at location s2, package4 is at location p0_2, package4 is at location p0_3, package4 is at location s1, package4 is currently at location p0_1, package4 is currently at location p2_1, package4 is currently at location s0, package4 is in truck2, package4 is placed in truck1, package4 is present at location p1_3, package4 is present at location p3_0, package4 is present at location s2, package4 is present at location s3, truck1 is at location p0_3, truck1 is at location p2_1, truck1 is at location s1, truck1 is currently at location p0_1, truck1 is currently at location p0_2, truck1 is currently at location p3_0, truck1 is currently at location s3, truck1 is present at location p1_3, truck1 is present at location s0, truck1 is present at location s2, truck2 is at location p0_3, truck2 is at location p2_1, truck2 is at location s0, truck2 is currently at location p0_1, truck2 is currently at location p3_0, truck2 is present at location p0_2, truck2 is present at location p1_3, truck2 is present at location s1, truck2 is present at location s2 and truck2 is present at location s3?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is at location s2, driver2 is present at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_2 and s2 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_3 have a path between them, locations s2 and p0_2 have a path between them, locations s2 and s1 have a link between them, locations s2 and s3 have a link between them, locations s3 and p0_3 have a path between them, locations s3 and s1 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is at location s0, package4 is at location s2, there exists a link between the locations s1 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p2_1, there is a link between location s0 and location s1, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s3 and location s0, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_3 and location s0, there is a path between location p0_3 and location s3, there is a path between location p1_3 and location s3, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: package3 is loaded onto truck1 at location s0, driver1 walks from location s2 to p0_2, then from p0_2 to s0, boards truck1 at location s0, drives truck1 from location s0 to s3, disembarks from truck1 at location s3, unloads package3 from truck1 at location s3, and then walks from location s3 to p0_3 and back to s0, while driver2 walks from location s2 to p0_2 to reach the current state. In this state, are the following properties of the state that do not involve negations True or False: is driver1 at location p0_2, is driver1 at location p0_3, is driver1 at location p1_3, is driver1 at location s3, is driver1 currently at location s0, is driver1 currently driving truck1, is driver1 currently driving truck2, is driver1 present at location p0_1, is driver1 present at location p2_1, is driver1 present at location p3_0, is driver1 present at location s1, is driver1 present at location s2, is driver2 at location p0_1, is driver2 at location p0_3, is driver2 at location s0, is driver2 currently at location p2_1, is driver2 currently at location s1, is driver2 currently at location s2, is driver2 currently driving truck1, is driver2 currently driving truck2, is driver2 present at location p0_2, is driver2 present at location p1_3, is driver2 present at location p3_0, is driver2 present at location s3, is package1 at location p0_1, is package1 at location p0_3, is package1 at location s3, is package1 currently at location p3_0, is package1 currently at location s0, is package1 currently at location s1, is package1 currently at location s2, is package1 located in truck2, is package1 placed in truck1, is package1 present at location p0_2, is package1 present at location p1_3, is package1 present at location p2_1, is package2 at location p0_1, is package2 at location p0_2, is package2 at location p0_3, is package2 at location p2_1, is package2 currently at location p1_3, is package2 currently at location s2, is package2 located in truck2, is package2 placed in truck1, is package2 present at location p3_0, is package2 present at location s0, is package2 present at location s1, is package2 present at location s3, is package3 at location p0_2, is package3 at location p0_3, is package3 at location p1_3, is package3 at location s0, is package3 at location s3, is package3 currently at location s1, is package3 located in truck2, is package3 placed in truck1, is package3 present at location p0_1, is package3 present at location p2_1, is package3 present at location p3_0, is package3 present at location s2, is package4 at location p0_2, is package4 at location p0_3, is package4 at location s1, is package4 currently at location p0_1, is package4 currently at location p2_1, is package4 currently at location s0, is package4 in truck2, is package4 placed in truck1, is package4 present at location p1_3, is package4 present at location p3_0, is package4 present at location s2, is package4 present at location s3, is truck1 at location p0_3, is truck1 at location p2_1, is truck1 at location s1, is truck1 currently at location p0_1, is truck1 currently at location p0_2, is truck1 currently at location p3_0, is truck1 currently at location s3, is truck1 present at location p1_3, is truck1 present at location s0, is truck1 present at location s2, is truck2 at location p0_3, is truck2 at location p2_1, is truck2 at location s0, is truck2 currently at location p0_1, is truck2 currently at location p3_0, is truck2 present at location p0_2, is truck2 present at location p1_3, is truck2 present at location s1, is truck2 present at location s2, and is truck2 present at location s3?", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, and p0_2 and s2. Similarly, paths are present between s0 and p0_1, s0 and p0_2, and s0 and p0_3. A link connects locations s0 and s2, and another link exists between s0 and s3. A path is present between s1 and p1_3, and between s2 and p0_2. Locations s2 and s1 are linked, as are s2 and s3, and s3 and s1. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link exists between s1 and s0, and a path is present between p1_3 and s1, s1 and p0_1, and s1 and p2_1. Additionally, links connect s0 and s1, s1 and s2, and s1 and s3, as well as s2 and s0, s3 and s0, and s3 and s2. Paths are present between p0_1 and s1, p0_3 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s1, p2_1 and s2, s2 and p2_1, and s3 and p1_3. Truck1 is located at s0 and is empty, while truck2 is also at s0 and contains nothing."}
{"question_id": "dffab384-3a10-456b-a042-000cc5ad9927", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1, driver1 walks from location s2 to location p0_2, driver1 walks from location p0_2 to location s0, driver1 boards truck1 at location s0, truck1 is driven from location s0 to s3 by driver1, at location s3, driver1 disembarks from truck1, truck1 is unloaded with package3 at location s3, driver1 walks to location p0_3 from location s3, driver1 walks from location p0_3 to location s0 and driver2 walks from location s2 to location p0_2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: truck1 contains nothing and truck2 is empty?", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is at location s2, driver2 is present at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_2 and s2 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_3 have a path between them, locations s2 and p0_2 have a path between them, locations s2 and s1 have a link between them, locations s2 and s3 have a link between them, locations s3 and p0_3 have a path between them, locations s3 and s1 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is at location s0, package4 is at location s2, there exists a link between the locations s1 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p2_1, there is a link between location s0 and location s1, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s3 and location s0, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_3 and location s0, there is a path between location p0_3 and location s3, there is a path between location p1_3 and location s3, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: at location s0, package3 is loaded onto truck1, driver1 moves from location s2 to p0_2, then from p0_2 to s0, boards truck1 at s0, drives truck1 from s0 to s3, disembarks from truck1 at s3, unloads package3 from truck1 at s3, walks from s3 to p0_3 and then back to s0, and driver2 moves from s2 to p0_2, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: truck1 is empty and truck2 contains nothing?", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, and p0_2 and s2. Similarly, paths are present between s0 and p0_1, s0 and p0_2, and s0 and p0_3. A link connects locations s0 and s2, and another link exists between s0 and s3. A path is present between s1 and p1_3, and between s2 and p0_2. Locations s2 and s1 are linked, as are s2 and s3, and s3 and s1. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link exists between s1 and s0, and a path is present between p1_3 and s1, s1 and p0_1, and s1 and p2_1. Additionally, links connect s0 and s1, s1 and s2, and s1 and s3, as well as s2 and s0, s3 and s0, and s3 and s2. Paths are present between p0_1 and s1, p0_3 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s1, and p2_1 and s2. Furthermore, a path exists between s2 and p2_1, and between s3 and p1_3. Truck1 is located at s0 and is empty, while truck2 is also at s0 and contains nothing."}
{"question_id": "0e3b3c19-8e51-4fbb-89d0-764598c5e694", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_13", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks to location p3_0 from location s3, driver2 walks from location p3_0 to location s0, at location s0, driver2 boards truck1, driver2 drives truck1 to location s2 from location s0, at location s2, package3 is loaded in truck1, truck1 is loaded with package2 at location s2, driver2 drives truck1 to location s0 from location s2, at location s0, package2 is unloaded in truck1, driver2 drives truck1 from location s0 to location s3 and at location s3, package1 is loaded in truck1 to reach the current state. In this state, is it True or False that locations s0 and s2 have a link between them?", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s1, driver2 is currently at location s3, driver3 is at location s3, locations p2_0 and s0 have a path between them, locations p3_0 and s0 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p2_0 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s2 and p1_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is present at location s3, package2 is present at location s2, package3 is at location s2, package4 is at location s1, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s2 and p2_0, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a link between location s2 and location s1, there is a path between location p0_1 and location s1, there is a path between location p2_0 and location s2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 contains nothing, truck1 is at location s0, truck2 contains nothing, truck2 is at location s3, truck3 is empty and truck3 is present at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver2 moves from location s3 to location p3_0, then from location p3_0 to location s0, where driver2 boards truck1. Driver2 then drives truck1 from location s0 to location s2, where package3 and package2 are loaded into truck1. Next, driver2 drives truck1 back from location s2 to location s0, where package2 is unloaded from truck1. Finally, driver2 drives truck1 from location s0 to location s3, where package1 is loaded into truck1, resulting in the current state. In this state, is it True or False that locations s0 and s2 are connected by a link?", "initial_state_nl_paraphrased": "Driver1 is currently located at s1, while driver2 and driver3 are both at location s3. A path exists between locations p2_0 and s0, as well as between p3_0 and s0, and also between p3_0 and s3. Similarly, a path is present between s0 and p2_0, s0 and p3_0, and a link exists between s0 and s3. Locations s1 and p1_2 are connected by a path, as are locations s2 and p1_2. A link is present between s2 and s0, and another link exists between s3 and s1. Package1 is located at s3, package2 and package3 are at s2, and package4 is at s1. Links exist between locations s1 and s2, s1 and s3, and s3 and s0. Paths are present between p0_1 and s0, p1_2 and s1, p1_2 and s2, p1_3 and s1, and p1_3 and s3. Additionally, paths exist between s0 and p0_1, s2 and p2_0, and links are present between s0 and s1, s0 and s2, s1 and s0, and s2 and s1. Furthermore, paths exist between p0_1 and s1, p2_0 and s2, s1 and p0_1, s1 and p1_3, s3 and p1_3, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and present at location s0."}
{"question_id": "ec3dcd31-7087-4691-be35-3b11a686df31", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_10", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0 to reach the current state. In this state, is it True or False that truck2 is at location p2_1?", "answer": "False", "plan_length": 1, "initial_state_nl": "Driver1 is at location s2, driver2 is present at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_2 and s2 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_3 have a path between them, locations s2 and p0_2 have a path between them, locations s2 and s1 have a link between them, locations s2 and s3 have a link between them, locations s3 and p0_3 have a path between them, locations s3 and s1 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is at location s0, package4 is at location s2, there exists a link between the locations s1 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p2_1, there is a link between location s0 and location s1, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s3 and location s0, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_3 and location s0, there is a path between location p0_3 and location s3, there is a path between location p1_3 and location s3, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: package3 is loaded onto truck1 at location s0 to achieve the current state. In this state, is it True or False that truck2 is currently located at p2_1?", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, and p0_2 and s2. Similarly, paths are present between s0 and p0_1, s0 and p0_2, and s0 and p0_3. A link connects locations s0 and s2, and another link exists between s0 and s3. A path is present between s1 and p1_3, and between s2 and p0_2. Locations s2 and s1 are linked, as are s2 and s3, and s3 and s1. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link exists between s1 and s0, and a path is present between p1_3 and s1, s1 and p0_1, and s1 and p2_1. Additionally, links connect s0 and s1, s1 and s2, and s1 and s3, as well as s2 and s0, s3 and s0, and s3 and s2. Paths are present between p0_1 and s1, p0_3 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s1, and p2_1 and s2. Furthermore, a path exists between s2 and p2_1, and between s3 and p1_3. Truck1 is located at s0 and is empty, while truck2 is also at s0 and contains nothing."}
{"question_id": "bde62c3e-f6a6-4c06-98ed-069aef76054a", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_20", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0, driver1 walks from location s2 to p0_2, driver1 walks from location p0_2 to s0, driver1 boards truck1 at location s0, driver1 drives truck1 from location s0 to location s3, from truck1, driver1 disembarks at location s3, truck1 is unloaded with package3 at location s3, driver1 walks to location p0_3 from location s3, driver1 walks from location p0_3 to s0 and driver2 walks from location s2 to p0_2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: locations p0_1 and p0_2 have a link between them, locations p0_1 and p2_1 have a link between them, locations p0_1 and p3_0 have a path between them, locations p0_1 and s1 have a link between them, locations p0_1 and s2 have a link between them, locations p0_2 and p0_1 have a link between them, locations p0_2 and p0_1 have a path between them, locations p0_2 and p0_3 have a path between them, locations p0_2 and s0 have a path between them, locations p0_3 and p0_2 have a path between them, locations p0_3 and p2_1 have a path between them, locations p0_3 and p3_0 have a link between them, locations p0_3 and p3_0 have a path between them, locations p0_3 and s0 have a link between them, locations p0_3 and s0 have a path between them, locations p0_3 and s1 have a path between them, locations p0_3 and s2 have a link between them, locations p0_3 and s2 have a path between them, locations p0_3 and s3 have a link between them, locations p0_3 and s3 have a path between them, locations p1_3 and p0_2 have a link between them, locations p1_3 and p0_3 have a path between them, locations p1_3 and p3_0 have a link between them, locations p1_3 and p3_0 have a path between them, locations p1_3 and s1 have a link between them, locations p2_1 and p0_1 have a link between them, locations p2_1 and p3_0 have a path between them, locations p2_1 and s0 have a link between them, locations p2_1 and s1 have a path between them, locations p2_1 and s3 have a path between them, locations p3_0 and p0_1 have a path between them, locations p3_0 and p0_2 have a path between them, locations p3_0 and p2_1 have a path between them, locations p3_0 and s1 have a path between them, locations s0 and p0_1 have a link between them, locations s0 and p0_2 have a link between them, locations s0 and p3_0 have a link between them, locations s0 and s3 have a link between them, locations s0 and s3 have a path between them, locations s1 and p0_1 have a path between them, locations s1 and p0_2 have a path between them, locations s1 and p0_3 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_1 have a link between them, locations s1 and p3_0 have a path between them, locations s2 and p0_2 have a link between them, locations s2 and p1_3 have a link between them, locations s2 and p1_3 have a path between them, locations s2 and p3_0 have a link between them, locations s2 and s0 have a link between them, locations s2 and s0 have a path between them, locations s2 and s1 have a link between them, locations s2 and s1 have a path between them, locations s2 and s3 have a path between them, locations s3 and p0_2 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 have a link between them, locations s3 and s0 have a path between them, locations s3 and s1 have a path between them, locations s3 and s2 have a link between them, locations s3 and s2 have a path between them, there exists a link between the locations p0_1 and p1_3, there exists a link between the locations p0_1 and p3_0, there exists a link between the locations p0_1 and s3, there exists a link between the locations p0_2 and p0_3, there exists a link between the locations p0_2 and p1_3, there exists a link between the locations p0_2 and p3_0, there exists a link between the locations p0_2 and s2, there exists a link between the locations p0_3 and p1_3, there exists a link between the locations p0_3 and p2_1, there exists a link between the locations p0_3 and s1, there exists a link between the locations p1_3 and p0_1, there exists a link between the locations p1_3 and p2_1, there exists a link between the locations p1_3 and s0, there exists a link between the locations p2_1 and p3_0, there exists a link between the locations p3_0 and p0_1, there exists a link between the locations p3_0 and p0_2, there exists a link between the locations p3_0 and p0_3, there exists a link between the locations p3_0 and p2_1, there exists a link between the locations p3_0 and s1, there exists a link between the locations p3_0 and s3, there exists a link between the locations s0 and s1, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and p0_1, there exists a link between the locations s1 and p0_3, there exists a link between the locations s1 and p1_3, there exists a link between the locations s1 and p3_0, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and p0_1, there exists a link between the locations s3 and p1_3, there exists a link between the locations s3 and p3_0, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and p0_2, there exists a path between the locations p0_1 and p1_3, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_1 and s2, there exists a path between the locations p0_1 and s3, there exists a path between the locations p0_2 and s2, there exists a path between the locations p0_2 and s3, there exists a path between the locations p0_3 and p0_1, there exists a path between the locations p1_3 and p0_1, there exists a path between the locations p2_1 and p0_1, there exists a path between the locations p2_1 and p0_2, there exists a path between the locations p2_1 and p0_3, there exists a path between the locations p2_1 and s0, there exists a path between the locations p2_1 and s2, there exists a path between the locations p3_0 and p0_3, there exists a path between the locations p3_0 and s0, there exists a path between the locations p3_0 and s2, there exists a path between the locations p3_0 and s3, there exists a path between the locations s0 and p0_3, there exists a path between the locations s0 and p2_1, there exists a path between the locations s1 and p2_1, there exists a path between the locations s1 and s0, there exists a path between the locations s1 and s2, there exists a path between the locations s2 and p3_0, there exists a path between the locations s3 and p0_3, there exists a path between the locations s3 and p2_1, there is a link between location p0_1 and location p0_3, there is a link between location p0_1 and location s0, there is a link between location p0_2 and location p2_1, there is a link between location p0_2 and location s0, there is a link between location p0_2 and location s1, there is a link between location p0_2 and location s3, there is a link between location p0_3 and location p0_1, there is a link between location p0_3 and location p0_2, there is a link between location p1_3 and location p0_3, there is a link between location p1_3 and location s2, there is a link between location p1_3 and location s3, there is a link between location p2_1 and location p0_2, there is a link between location p2_1 and location p0_3, there is a link between location p2_1 and location p1_3, there is a link between location p2_1 and location s1, there is a link between location p2_1 and location s2, there is a link between location p2_1 and location s3, there is a link between location p3_0 and location p1_3, there is a link between location p3_0 and location s0, there is a link between location p3_0 and location s2, there is a link between location s0 and location p0_3, there is a link between location s0 and location p1_3, there is a link between location s0 and location p2_1, there is a link between location s1 and location p0_2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location p0_1, there is a link between location s2 and location p0_3, there is a link between location s2 and location p2_1, there is a link between location s2 and location s3, there is a link between location s3 and location p0_3, there is a link between location s3 and location p2_1, there is a path between location p0_1 and location p0_3, there is a path between location p0_1 and location p2_1, there is a path between location p0_1 and location s1, there is a path between location p0_2 and location p1_3, there is a path between location p0_2 and location p2_1, there is a path between location p0_2 and location p3_0, there is a path between location p0_2 and location s1, there is a path between location p0_3 and location p1_3, there is a path between location p1_3 and location p0_2, there is a path between location p1_3 and location p2_1, there is a path between location p1_3 and location s0, there is a path between location p1_3 and location s1, there is a path between location p1_3 and location s2, there is a path between location p1_3 and location s3, there is a path between location p2_1 and location p1_3, there is a path between location p3_0 and location p1_3, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_2, there is a path between location s0 and location p1_3, there is a path between location s0 and location p3_0, there is a path between location s0 and location s1, there is a path between location s0 and location s2, there is a path between location s1 and location s3, there is a path between location s2 and location p0_1, there is a path between location s2 and location p0_2, there is a path between location s2 and location p0_3, there is a path between location s2 and location p2_1, there is a path between location s3 and location p0_1, there is a path between location s3 and location p0_2 and there is a path between location s3 and location p1_3?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is at location s2, driver2 is present at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_2 and s2 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_3 have a path between them, locations s2 and p0_2 have a path between them, locations s2 and s1 have a link between them, locations s2 and s3 have a link between them, locations s3 and p0_3 have a path between them, locations s3 and s1 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is at location s0, package4 is at location s2, there exists a link between the locations s1 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p2_1, there is a link between location s0 and location s1, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s3 and location s0, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_3 and location s0, there is a path between location p0_3 and location s3, there is a path between location p1_3 and location s3, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0, driver1 walks from location s2 to p0_2, driver1 walks from location p0_2 to s0, driver1 boards truck1 at location s0, driver1 drives truck1 from location s0 to location s3, from truck1, driver1 disembarks at location s3, truck1 is unloaded with package3 at location s3, driver1 walks to location p0_3 from location s3, driver1 walks from location p0_3 to s0 and driver2 walks from location s2 to p0_2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: \n\nlocations p0_1 and p0_2 are connected, locations p0_1 and p2_1 are connected, locations p0_1 and p3_0 are reachable, locations p0_1 and s1 are connected, locations p0_1 and s2 are connected, locations p0_2 and p0_1 are connected, locations p0_2 and p0_1 are reachable, locations p0_2 and p0_3 are reachable, locations p0_2 and s0 are reachable, locations p0_3 and p0_2 are reachable, locations p0_3 and p2_1 are reachable, locations p0_3 and p3_0 are connected, locations p0_3 and p3_0 are reachable, locations p0_3 and s0 are connected, locations p0_3 and s0 are reachable, locations p0_3 and s1 are reachable, locations p0_3 and s2 are connected, locations p0_3 and s2 are reachable, locations p0_3 and s3 are connected, locations p0_3 and s3 are reachable, locations p1_3 and p0_2 are connected, locations p1_3 and p0_3 are reachable, locations p1_3 and p3_0 are connected, locations p1_3 and p3_0 are reachable, locations p1_3 and s1 are connected, locations p2_1 and p0_1 are connected, locations p2_1 and p3_0 are reachable, locations p2_1 and s0 are connected, locations p2_1 and s1 are reachable, locations p2_1 and s3 are reachable, locations p3_0 and p0_1 are reachable, locations p3_0 and p0_2 are reachable, locations p3_0 and p2_1 are reachable, locations p3_0 and s1 are reachable, locations s0 and p0_1 are connected, locations s0 and p0_2 are connected, locations s0 and p3_0 are connected, locations s0 and s3 are connected, locations s0 and s3 are reachable, locations s1 and p0_1 are reachable, locations s1 and p0_2 are reachable, locations s1 and p0_3 are reachable, locations s1 and p1_3 are reachable, locations s1 and p2_1 are connected, locations s1 and p3_0 are reachable, locations s2 and p0_2 are connected, locations s2 and p1_3 are connected, locations s2 and p1_3 are reachable, locations s2 and p3_0 are connected, locations s2 and s0 are connected, locations s2 and s0 are reachable, locations s2 and s1 are connected, locations s2 and s1 are reachable, locations s2 and s3 are reachable, locations s3 and p0_2 are connected, locations s3 and p3_0 are reachable, locations s3 and s0 are connected, locations s3 and s0 are reachable, locations s3 and s1 are reachable, locations s3 and s2 are connected, locations s3 and s2 are reachable, there is a connection between locations p0_1 and p1_3, there is a connection between locations p0_1 and p3_0, there is a connection between locations p0_1 and s3, there is a connection between locations p0_2 and p0_3, there is a connection between locations p0_2 and p1_3, there is a connection between locations p0_2 and p3_0, there is a connection between locations p0_2 and s2, there is a connection between locations p0_3 and p1_3, there is a connection between locations p0_3 and p2_1, there is a connection between locations p0_3 and s1, there is a connection between locations p1_3 and p0_1, there is a connection between locations p1_3 and p2_1, there is a connection between locations p1_3 and s0, there is a connection between locations p2_1 and p3_0, there is a connection between locations p3_0 and p0_1, there is a connection between locations p3_0 and p0_2, there is a connection between locations p3_0 and p0_3, there is a connection between locations p3_0 and p2_1, there is a connection between locations p3_0 and s1, there is a connection between locations p3_0 and s3, there is a connection between locations s0 and s1, there is a connection between locations s0 and s2, there is a connection between locations s1 and p0_1, there is a connection between locations s1 and p0_3, there is a connection between locations s1 and p1_3, there is a connection between locations s1 and p3_0, there is a connection between locations s1 and s0, there is a connection between locations s3 and p0_1, there is a connection between locations s3 and p1_3, there is a connection between locations s3 and p3_0, there is a connection between locations s3 and s1, there is a path between locations p0_1 and p0_2, there is a path between locations p0_1 and p1_3, there is a path between locations p0_1 and s0, there is a path between locations p0_1 and s2, there is a path between locations p0_1 and s3, there is a path between locations p0_2 and s2, there is a path between locations p0_2 and s3, there is a path between locations p0_3 and p0_1, there is a path between locations p1_3 and p0_1, there is a path between locations p2_1 and p0_1, there is a path between locations p2_1 and p0_2, there is a path between locations p2_1 and p0_3, there is a path between locations p2_1 and s0, there is a path between locations p2_1 and s2, there is a path between locations p3_0 and p0_3, there is a path between locations p3_0 and s0, there is a path between locations p3_0 and s2, there is a path between locations p3_0 and s3, there is a path between locations s0 and p0_3, there is a path between locations s0 and p2_1, there is a path between locations s1 and p2_1, there is a path between locations s1 and s0, there is a path between locations s1 and s2, there is a path between locations s2 and p3_0, there is a path between locations s3 and p0_3, there is a path between locations s3 and p2_1, there is a connection between location p0_1 and location p0_3, there is a connection between location p0_1 and location s0, there is a connection between location p0_2 and location p2_1, there is a connection between location p0_2 and location s0, there is a connection between location p0_2 and location s1, there is a connection between location p0_2 and location s3, there is a connection between location p0_3 and location p0_1, there is a connection between location p0_3 and location p0_2, there is a connection between location p1_3 and location p0_3, there is a connection between location p1_3 and location s2, there is a connection between location p1_3 and location s3, there is a connection between location p2_1 and location p0_2, there is a connection between location p2_1 and location p0_3, there is a connection between location p2_1 and location p1_3, there is a connection between location p2_1 and location s1, there is a connection between location p2_1 and location s2, there is a connection between location p2_1 and location s3, there is a connection between location p3_0 and location p1_3, there is a connection between location p3_0 and location s0, there is a connection between location p3_0 and location s2, there is a connection between location s0 and location p0_3, there is a connection between location s0 and location p1_3, there is a connection between location s0 and location p2_1, there is a connection between location s1 and location p0_2, there is a connection between location s1 and location s2, there is a connection between location s1 and location s3, there is a connection between location s2 and location p0_1, there is a connection between location s2 and location p0_3, there is a connection between location s2 and location p2_1, there is a connection between location s2 and location s3, there is a connection between location s3 and location p0_3, there is a connection between location s3 and location p2_1, there is a path between location p0_1 and location p0_3, there is a path between location p0_1 and location p2_1, there is a path between location p0_1 and location s1, there is a path between location p0_2 and location p1_3, there is a path between location p0_2 and location p2_1, there is a path between location p0_2 and location p3_0, there is a path between location p0_2 and location s1, there is a path between location p0_3 and location p1_3, there is a path between location p1_3 and location p0_2, there is a path between location p1_3 and location p2_1, there is a path between location p1_3 and location s0, there is a path between location p1_3 and location s1, there is a path between location p1_3 and location s2, there is a path between location p1_3 and location s3, there is a path between location p2_1 and location p1_3, there is a path between location p3_0 and location p1_3, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_2, there is a path between location s0 and location p1_3, there is a path between location s0 and location p3_0, there is a path between location s0 and location s1, there is a path between location s0 and location s2, there is a path between location s1 and location s3, there is a path between location s2 and location p0_1, there is a path between location s2 and location p0_2, there is a path between location s2 and location p0_3, there is a path between location s2 and location p2_1, there is a path between location s3 and location p0_1, there is a path between location s3 and location p0_2 and there is a path between location s3 and location p1_3?", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, and p0_2 and s2. Similarly, paths are present between s0 and p0_1, s0 and p0_2, and s0 and p0_3. A link connects locations s0 and s2, and another link exists between s0 and s3. A path is present between s1 and p1_3, and between s2 and p0_2. Locations s2 and s1 are linked, as are s2 and s3, and s3 and s1. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link exists between s1 and s0, and a path is present between p1_3 and s1, s1 and p0_1, and s1 and p2_1. Additionally, links connect s0 and s1, s1 and s2, and s1 and s3, as well as s2 and s0, s3 and s0, and s3 and s2. Paths are present between p0_1 and s1, p0_3 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s1, and p2_1 and s2. Furthermore, a path exists between s2 and p2_1, and between s3 and p1_3. Truck1 is located at s0 and is empty, while truck2 is also at s0 and contains nothing."}
{"question_id": "6318ec3a-c103-426b-9ff6-f8b0983dad0b", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_22", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: locations p0_1 and p1_3 does not have a path between them, locations p0_1 and p3_0 does not have a link between them, locations p0_1 and s0 does not have a path between them, locations p0_1 and s2 does not have a link between them, locations p0_1 and s3 does not have a link between them, locations p0_2 and p1_3 does not have a path between them, locations p0_2 and p3_0 does not have a link between them, locations p0_2 and p3_0 does not have a path between them, locations p0_2 and s0 does not have a link between them, locations p0_2 and s1 does not have a link between them, locations p0_3 and p0_1 does not have a path between them, locations p0_3 and p0_2 does not have a path between them, locations p0_3 and p1_3 does not have a link between them, locations p0_3 and p3_0 does not have a link between them, locations p0_3 and p3_0 does not have a path between them, locations p0_3 and s3 does not have a path between them, locations p1_3 and p0_1 does not have a path between them, locations p1_3 and p0_2 does not have a link between them, locations p1_3 and p2_1 does not have a path between them, locations p1_3 and p3_0 does not have a path between them, locations p1_3 and s0 does not have a link between them, locations p1_3 and s2 does not have a link between them, locations p1_3 and s3 does not have a link between them, locations p1_3 and s3 does not have a path between them, locations p2_1 and p0_3 does not have a path between them, locations p2_1 and p3_0 does not have a path between them, locations p2_1 and s1 does not have a path between them, locations p3_0 and p0_1 does not have a link between them, locations p3_0 and p0_1 does not have a path between them, locations p3_0 and p0_2 does not have a link between them, locations p3_0 and p0_2 does not have a path between them, locations p3_0 and p0_3 does not have a path between them, locations p3_0 and s1 does not have a link between them, locations p3_0 and s3 does not have a path between them, locations s0 and p0_3 does not have a link between them, locations s0 and p0_3 does not have a path between them, locations s0 and s1 does not have a link between them, locations s0 and s2 does not have a link between them, locations s0 and s2 does not have a path between them, locations s0 and s3 does not have a link between them, locations s1 and p0_1 does not have a link between them, locations s1 and p0_1 does not have a path between them, locations s1 and p0_2 does not have a path between them, locations s1 and p0_3 does not have a link between them, locations s1 and p0_3 does not have a path between them, locations s1 and p2_1 does not have a path between them, locations s1 and p3_0 does not have a path between them, locations s1 and s0 does not have a path between them, locations s1 and s2 does not have a link between them, locations s2 and p0_1 does not have a link between them, locations s2 and p0_2 does not have a link between them, locations s2 and p0_2 does not have a path between them, locations s2 and p0_3 does not have a link between them, locations s2 and p0_3 does not have a path between them, locations s2 and p2_1 does not have a link between them, locations s2 and p3_0 does not have a link between them, locations s2 and s3 does not have a link between them, locations s2 and s3 does not have a path between them, locations s3 and p0_2 does not have a link between them, locations s3 and p0_3 does not have a link between them, locations s3 and s1 does not have a link between them, there doesn't exist a link between the locations p0_1 and p1_3, there doesn't exist a link between the locations p0_2 and p2_1, there doesn't exist a link between the locations p0_2 and s3, there doesn't exist a link between the locations p0_3 and p0_2, there doesn't exist a link between the locations p0_3 and s0, there doesn't exist a link between the locations p0_3 and s1, there doesn't exist a link between the locations p0_3 and s3, there doesn't exist a link between the locations p1_3 and p0_1, there doesn't exist a link between the locations p1_3 and p0_3, there doesn't exist a link between the locations p1_3 and p2_1, there doesn't exist a link between the locations p1_3 and p3_0, there doesn't exist a link between the locations p2_1 and p0_2, there doesn't exist a link between the locations p2_1 and p0_3, there doesn't exist a link between the locations p2_1 and s1, there doesn't exist a link between the locations p2_1 and s2, there doesn't exist a link between the locations p2_1 and s3, there doesn't exist a link between the locations p3_0 and p0_3, there doesn't exist a link between the locations p3_0 and p1_3, there doesn't exist a link between the locations s0 and p0_2, there doesn't exist a link between the locations s0 and p1_3, there doesn't exist a link between the locations s0 and p2_1, there doesn't exist a link between the locations s1 and p1_3, there doesn't exist a link between the locations s1 and p2_1, there doesn't exist a link between the locations s1 and p3_0, there doesn't exist a link between the locations s1 and s0, there doesn't exist a link between the locations s1 and s3, there doesn't exist a link between the locations s2 and s0, there doesn't exist a link between the locations s3 and p1_3, there doesn't exist a link between the locations s3 and p2_1, there doesn't exist a link between the locations s3 and p3_0, there doesn't exist a link between the locations s3 and s2, there doesn't exist a path between the locations p0_1 and p0_2, there doesn't exist a path between the locations p0_1 and p2_1, there doesn't exist a path between the locations p0_1 and p3_0, there doesn't exist a path between the locations p0_1 and s1, there doesn't exist a path between the locations p0_1 and s3, there doesn't exist a path between the locations p0_2 and p0_1, there doesn't exist a path between the locations p0_2 and s0, there doesn't exist a path between the locations p0_2 and s2, there doesn't exist a path between the locations p0_2 and s3, there doesn't exist a path between the locations p0_3 and p1_3, there doesn't exist a path between the locations p0_3 and s0, there doesn't exist a path between the locations p0_3 and s2, there doesn't exist a path between the locations p1_3 and p0_2, there doesn't exist a path between the locations p1_3 and p0_3, there doesn't exist a path between the locations p1_3 and s1, there doesn't exist a path between the locations p1_3 and s2, there doesn't exist a path between the locations p2_1 and p0_1, there doesn't exist a path between the locations p2_1 and s0, there doesn't exist a path between the locations p2_1 and s2, there doesn't exist a path between the locations p3_0 and p1_3, there doesn't exist a path between the locations p3_0 and s0, there doesn't exist a path between the locations p3_0 and s1, there doesn't exist a path between the locations p3_0 and s2, there doesn't exist a path between the locations s0 and p0_2, there doesn't exist a path between the locations s0 and p1_3, there doesn't exist a path between the locations s0 and p2_1, there doesn't exist a path between the locations s0 and p3_0, there doesn't exist a path between the locations s0 and s3, there doesn't exist a path between the locations s1 and p1_3, there doesn't exist a path between the locations s2 and p0_1, there doesn't exist a path between the locations s2 and p2_1, there doesn't exist a path between the locations s2 and s1, there doesn't exist a path between the locations s3 and p0_1, there doesn't exist a path between the locations s3 and p3_0, there doesn't exist a path between the locations s3 and s0, there doesn't exist a path between the locations s3 and s1, there is no link between location p0_1 and location p0_2, there is no link between location p0_1 and location p0_3, there is no link between location p0_1 and location p2_1, there is no link between location p0_1 and location s0, there is no link between location p0_1 and location s1, there is no link between location p0_2 and location p0_1, there is no link between location p0_2 and location p0_3, there is no link between location p0_2 and location p1_3, there is no link between location p0_2 and location s2, there is no link between location p0_3 and location p0_1, there is no link between location p0_3 and location p2_1, there is no link between location p0_3 and location s2, there is no link between location p1_3 and location s1, there is no link between location p2_1 and location p0_1, there is no link between location p2_1 and location p1_3, there is no link between location p2_1 and location p3_0, there is no link between location p2_1 and location s0, there is no link between location p3_0 and location p2_1, there is no link between location p3_0 and location s0, there is no link between location p3_0 and location s2, there is no link between location p3_0 and location s3, there is no link between location s0 and location p0_1, there is no link between location s0 and location p3_0, there is no link between location s1 and location p0_2, there is no link between location s2 and location p1_3, there is no link between location s2 and location s1, there is no link between location s3 and location p0_1, there is no link between location s3 and location s0, there is no path between location p0_1 and location p0_3, there is no path between location p0_1 and location s2, there is no path between location p0_2 and location p0_3, there is no path between location p0_2 and location p2_1, there is no path between location p0_2 and location s1, there is no path between location p0_3 and location p2_1, there is no path between location p0_3 and location s1, there is no path between location p1_3 and location s0, there is no path between location p2_1 and location p0_2, there is no path between location p2_1 and location p1_3, there is no path between location p2_1 and location s3, there is no path between location p3_0 and location p2_1, there is no path between location s0 and location p0_1, there is no path between location s0 and location s1, there is no path between location s1 and location s2, there is no path between location s1 and location s3, there is no path between location s2 and location p1_3, there is no path between location s2 and location p3_0, there is no path between location s2 and location s0, there is no path between location s3 and location p0_2, there is no path between location s3 and location p0_3, there is no path between location s3 and location p1_3, there is no path between location s3 and location p2_1 and there is no path between location s3 and location s2?", "answer": "False", "plan_length": 1, "initial_state_nl": "Driver1 is at location s2, driver2 is present at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_2 and s2 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_3 have a path between them, locations s2 and p0_2 have a path between them, locations s2 and s1 have a link between them, locations s2 and s3 have a link between them, locations s3 and p0_3 have a path between them, locations s3 and s1 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is at location s0, package4 is at location s2, there exists a link between the locations s1 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p2_1, there is a link between location s0 and location s1, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s3 and location s0, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_3 and location s0, there is a path between location p0_3 and location s3, there is a path between location p1_3 and location s3, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: \n\n- There is no path or link between locations p0_1 and p1_3, p0_1 and p3_0, p0_1 and s0, p0_1 and s2, p0_1 and s3, p0_2 and p1_3, p0_2 and p3_0, p0_2 and s0, p0_2 and s1, p0_3 and p0_1, p0_3 and p0_2, p0_3 and p1_3, p0_3 and p3_0, p0_3 and s3, p1_3 and p0_1, p1_3 and p0_2, p1_3 and p2_1, p1_3 and p3_0, p1_3 and s0, p1_3 and s2, p1_3 and s3, p2_1 and p0_3, p2_1 and p3_0, p2_1 and s1, p3_0 and p0_1, p3_0 and p0_2, p3_0 and p0_3, p3_0 and s1, p3_0 and s3, s0 and p0_3, s0 and s1, s0 and s2, s0 and s3, s1 and p0_1, s1 and p0_2, s1 and p0_3, s1 and p2_1, s1 and p3_0, s1 and s0, s1 and s2, s2 and p0_1, s2 and p0_2, s2 and p0_3, s2 and p2_1, s2 and p3_0, s2 and s3, s3 and p0_2, s3 and p0_3, s3 and s1.\n\nAdditionally, there doesn't exist a link between the locations p0_1 and p1_3, p0_2 and p2_1, p0_2 and s3, p0_3 and p0_2, p0_3 and s0, p0_3 and s1, p0_3 and s3, p1_3 and p0_1, p1_3 and p0_3, p1_3 and p2_1, p1_3 and p3_0, p2_1 and p0_2, p2_1 and p0_3, p2_1 and s1, p2_1 and s2, p2_1 and s3, p3_0 and p0_3, p3_0 and p1_3, s0 and p0_2, s0 and p1_3, s0 and p2_1, s1 and p1_3, s1 and p2_1, s1 and p3_0, s1 and s0, s1 and s3, s2 and s0, s3 and p1_3, s3 and p2_1, s3 and p3_0, s3 and s2.\n\nFurthermore, there doesn't exist a path between the locations p0_1 and p0_2, p0_1 and p2_1, p0_1 and p3_0, p0_1 and s1, p0_1 and s3, p0_2 and p0_1, p0_2 and s0, p0_2 and s2, p0_2 and s3, p0_3 and p1_3, p0_3 and s0, p0_3 and s2, p1_3 and p0_2, p1_3 and p0_3, p1_3 and s1, p1_3 and s2, p2_1 and p0_1, p2_1 and s0, p2_1 and s2, p3_0 and p1_3, p3_0 and s0, p3_0 and s1, p3_0 and s2, s0 and p0_2, s0 and p1_3, s0 and p2_1, s0 and p3_0, s0 and s3, s1 and p1_3, s2 and p0_1, s2 and p2_1, s2 and s1, s3 and p0_1, s3 and p3_0, s3 and s0, s3 and s1.\n\nAlso, there is no link between location p0_1 and location p0_2, p0_1 and location p0_3, p0_1 and location p2_1, p0_1 and location s0, p0_1 and location s1, p0_2 and location p0_1, p0_2 and location p0_3, p0_2 and location p1_3, p0_2 and location s2, p0_3 and location p0_1, p0_3 and location p2_1, p0_3 and location s2, p1_3 and location s1, p2_1 and location p0_1, p2_1 and location p1_3, p2_1 and location p3_0, p2_1 and location s0, p3_0 and location p2_1, p3_0 and location s0, p3_0 and location s2, p3_0 and location s3, s0 and location p0_1, s0 and location p3_0, s1 and location p0_2, s2 and location p1_3, s2 and location s1, s3 and location p0_1, s3 and location s0.\n\nLastly, there is no path between location p0_1 and location p0_3, p0_1 and location s2, p0_2 and location p0_3, p0_2 and location p2_1, p0_2 and location s1, p0_3 and location p2_1, p0_3 and location s1, p1_3 and location s0, p2_1 and location p0_2, p2_1 and location p1_3, p2_1 and location s3, p3_0 and location p2_1, s0 and location p0_1, s0 and location s1, s1 and location s2, s1 and location s3, s2 and location p1_3, s2 and location p3_0, s2 and location s0, s3 and location p0_2, s3 and location p0_3, s3 and location p1_3, s3 and location p2_1 and s3 and location s2.", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, and p0_2 and s2. Similarly, paths are present between s0 and p0_1, s0 and p0_2, and s0 and p0_3. A link connects locations s0 and s2, and another link exists between s0 and s3. A path is present between s1 and p1_3, and between s2 and p0_2. Locations s2 and s1 are linked, as are s2 and s3, and s3 and s1. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link exists between s1 and s0, and a path is present between p1_3 and s1, s1 and p0_1, and s1 and p2_1. Additionally, links connect s0 and s1, s1 and s2, and s1 and s3, as well as s2 and s0, s3 and s0, and s3 and s2. Paths are present between p0_1 and s1, p0_3 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s1, and p2_1 and s2. Furthermore, a path exists between s2 and p2_1, and between s3 and p1_3. Truck1 is located at s0 and is empty, while truck2 is also at s0 and contains nothing."}
{"question_id": "d9ae640f-c5b3-4063-8fdd-939c651d8d39", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_22", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks to location p3_0 from location s3, driver2 walks from location p3_0 to location s0, truck1 is boarded by driver2 at location s0, driver2 drives truck1 to location s2 from location s0, package3 is loaded in truck1 at location s2, at location s2, package2 is loaded in truck1, driver2 drives truck1 from location s2 to location s0, at location s0, package2 is unloaded in truck1, truck1 is driven from location s0 to s3 by driver2, truck1 is loaded with package1 at location s3, driver2 drives truck1 from location s3 to location s1, from truck1, driver2 disembarks at location s1, driver2 walks from location s1 to location p0_1, driver2 walks from location p0_1 to s0, driver2 boards truck3 at location s0, driver2 drives truck3 to location s2 from location s0, at location s1, package3 is unloaded in truck1, truck1 is unloaded with package1 at location s1 and driver3 walks from location s3 to p3_0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: locations p0_1 and p1_0 does not have a path between them, locations p0_1 and p1_2 does not have a link between them, locations p0_1 and p1_2 does not have a path between them, locations p0_1 and s1 does not have a link between them, locations p1_0 and p0_1 does not have a link between them, locations p1_0 and p1_2 does not have a link between them, locations p1_0 and p2_0 does not have a link between them, locations p1_0 and p2_0 does not have a path between them, locations p1_0 and s0 does not have a path between them, locations p1_0 and s2 does not have a link between them, locations p1_0 and s2 does not have a path between them, locations p1_0 and s3 does not have a path between them, locations p1_2 and p0_1 does not have a path between them, locations p1_2 and p1_0 does not have a link between them, locations p1_2 and p1_0 does not have a path between them, locations p1_2 and p1_3 does not have a path between them, locations p1_2 and p2_0 does not have a path between them, locations p1_2 and s0 does not have a path between them, locations p1_2 and s1 does not have a path between them, locations p1_2 and s3 does not have a path between them, locations p1_3 and p3_0 does not have a path between them, locations p1_3 and s0 does not have a path between them, locations p1_3 and s3 does not have a path between them, locations p2_0 and p0_1 does not have a link between them, locations p2_0 and p0_1 does not have a path between them, locations p2_0 and p1_0 does not have a path between them, locations p2_0 and p1_2 does not have a link between them, locations p2_0 and p1_3 does not have a link between them, locations p2_0 and p3_0 does not have a link between them, locations p2_0 and s0 does not have a link between them, locations p2_0 and s0 does not have a path between them, locations p3_0 and p0_1 does not have a link between them, locations p3_0 and p1_2 does not have a path between them, locations p3_0 and s0 does not have a link between them, locations p3_0 and s3 does not have a path between them, locations s0 and p1_2 does not have a link between them, locations s0 and p1_2 does not have a path between them, locations s0 and p3_0 does not have a path between them, locations s0 and s3 does not have a link between them, locations s1 and p1_0 does not have a link between them, locations s1 and p2_0 does not have a link between them, locations s1 and p3_0 does not have a path between them, locations s1 and s0 does not have a path between them, locations s1 and s2 does not have a link between them, locations s2 and p1_0 does not have a path between them, locations s2 and p1_3 does not have a link between them, locations s2 and p3_0 does not have a link between them, locations s2 and p3_0 does not have a path between them, locations s2 and s0 does not have a link between them, locations s2 and s1 does not have a path between them, locations s2 and s3 does not have a link between them, locations s3 and p0_1 does not have a link between them, locations s3 and p0_1 does not have a path between them, locations s3 and p1_0 does not have a link between them, locations s3 and p1_0 does not have a path between them, locations s3 and p2_0 does not have a link between them, locations s3 and p2_0 does not have a path between them, locations s3 and p3_0 does not have a link between them, locations s3 and p3_0 does not have a path between them, there doesn't exist a link between the locations p0_1 and p3_0, there doesn't exist a link between the locations p0_1 and s2, there doesn't exist a link between the locations p0_1 and s3, there doesn't exist a link between the locations p1_0 and p3_0, there doesn't exist a link between the locations p1_0 and s3, there doesn't exist a link between the locations p1_2 and p2_0, there doesn't exist a link between the locations p1_2 and s0, there doesn't exist a link between the locations p1_2 and s2, there doesn't exist a link between the locations p1_3 and p0_1, there doesn't exist a link between the locations p1_3 and p1_2, there doesn't exist a link between the locations p1_3 and s0, there doesn't exist a link between the locations p2_0 and s1, there doesn't exist a link between the locations p2_0 and s2, there doesn't exist a link between the locations p3_0 and p1_0, there doesn't exist a link between the locations s0 and p1_3, there doesn't exist a link between the locations s0 and p3_0, there doesn't exist a link between the locations s0 and s1, there doesn't exist a link between the locations s1 and p0_1, there doesn't exist a link between the locations s1 and p1_2, there doesn't exist a link between the locations s1 and p1_3, there doesn't exist a link between the locations s1 and p3_0, there doesn't exist a link between the locations s1 and s3, there doesn't exist a link between the locations s2 and p2_0, there doesn't exist a link between the locations s3 and p1_2, there doesn't exist a link between the locations s3 and s1, there doesn't exist a link between the locations s3 and s2, there doesn't exist a path between the locations p0_1 and p2_0, there doesn't exist a path between the locations p0_1 and s2, there doesn't exist a path between the locations p0_1 and s3, there doesn't exist a path between the locations p1_2 and p3_0, there doesn't exist a path between the locations p1_3 and p0_1, there doesn't exist a path between the locations p1_3 and p1_0, there doesn't exist a path between the locations p1_3 and p2_0, there doesn't exist a path between the locations p1_3 and s1, there doesn't exist a path between the locations p2_0 and p1_3, there doesn't exist a path between the locations p2_0 and s1, there doesn't exist a path between the locations p2_0 and s2, there doesn't exist a path between the locations p2_0 and s3, there doesn't exist a path between the locations p3_0 and p0_1, there doesn't exist a path between the locations p3_0 and p1_3, there doesn't exist a path between the locations p3_0 and p2_0, there doesn't exist a path between the locations p3_0 and s1, there doesn't exist a path between the locations s0 and p0_1, there doesn't exist a path between the locations s0 and p2_0, there doesn't exist a path between the locations s0 and s1, there doesn't exist a path between the locations s0 and s2, there doesn't exist a path between the locations s0 and s3, there doesn't exist a path between the locations s1 and p2_0, there doesn't exist a path between the locations s1 and s2, there doesn't exist a path between the locations s2 and p0_1, there doesn't exist a path between the locations s2 and p1_2, there doesn't exist a path between the locations s2 and s0, there doesn't exist a path between the locations s2 and s3, there doesn't exist a path between the locations s3 and p1_3, there doesn't exist a path between the locations s3 and s0, there doesn't exist a path between the locations s3 and s1, there is no link between location p0_1 and location p1_0, there is no link between location p0_1 and location p1_3, there is no link between location p0_1 and location p2_0, there is no link between location p0_1 and location s0, there is no link between location p1_0 and location p1_3, there is no link between location p1_0 and location s0, there is no link between location p1_0 and location s1, there is no link between location p1_2 and location p0_1, there is no link between location p1_2 and location p1_3, there is no link between location p1_2 and location p3_0, there is no link between location p1_2 and location s1, there is no link between location p1_2 and location s3, there is no link between location p1_3 and location p1_0, there is no link between location p1_3 and location p2_0, there is no link between location p1_3 and location p3_0, there is no link between location p1_3 and location s1, there is no link between location p1_3 and location s2, there is no link between location p1_3 and location s3, there is no link between location p2_0 and location p1_0, there is no link between location p2_0 and location s3, there is no link between location p3_0 and location p1_2, there is no link between location p3_0 and location p1_3, there is no link between location p3_0 and location p2_0, there is no link between location p3_0 and location s1, there is no link between location p3_0 and location s2, there is no link between location p3_0 and location s3, there is no link between location s0 and location p0_1, there is no link between location s0 and location p1_0, there is no link between location s0 and location p2_0, there is no link between location s0 and location s2, there is no link between location s1 and location s0, there is no link between location s2 and location p0_1, there is no link between location s2 and location p1_0, there is no link between location s2 and location p1_2, there is no link between location s2 and location s1, there is no link between location s3 and location p1_3, there is no link between location s3 and location s0, there is no path between location p0_1 and location p1_3, there is no path between location p0_1 and location p3_0, there is no path between location p0_1 and location s0, there is no path between location p0_1 and location s1, there is no path between location p1_0 and location p0_1, there is no path between location p1_0 and location p1_2, there is no path between location p1_0 and location p1_3, there is no path between location p1_0 and location p3_0, there is no path between location p1_0 and location s1, there is no path between location p1_2 and location s2, there is no path between location p1_3 and location p1_2, there is no path between location p1_3 and location s2, there is no path between location p2_0 and location p1_2, there is no path between location p2_0 and location p3_0, there is no path between location p3_0 and location p1_0, there is no path between location p3_0 and location s0, there is no path between location p3_0 and location s2, there is no path between location s0 and location p1_0, there is no path between location s0 and location p1_3, there is no path between location s1 and location p0_1, there is no path between location s1 and location p1_0, there is no path between location s1 and location p1_2, there is no path between location s1 and location p1_3, there is no path between location s1 and location s3, there is no path between location s2 and location p1_3, there is no path between location s2 and location p2_0, there is no path between location s3 and location p1_2 and there is no path between location s3 and location s2?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s1, driver2 is currently at location s3, driver3 is at location s3, locations p2_0 and s0 have a path between them, locations p3_0 and s0 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p2_0 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s2 and p1_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is present at location s3, package2 is present at location s2, package3 is at location s2, package4 is at location s1, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s2 and p2_0, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a link between location s2 and location s1, there is a path between location p0_1 and location s1, there is a path between location p2_0 and location s2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 contains nothing, truck1 is at location s0, truck2 contains nothing, truck2 is at location s3, truck3 is empty and truck3 is present at location s0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is performed: driver2 moves from location s3 to location p3_0, then from location p3_0 to location s0, boards truck1 at location s0, drives truck1 from location s0 to location s2, loads package3 into truck1 at location s2, also loads package2 into truck1 at location s2, drives truck1 from location s2 back to location s0, unloads package2 from truck1 at location s0, drives truck1 from location s0 to location s3, loads package1 into truck1 at location s3, drives truck1 from location s3 to location s1, disembarks from truck1 at location s1, walks from location s1 to location p0_1, then from location p0_1 to location s0, boards truck3 at location s0, drives truck3 from location s0 to location s2, unloads package3 from truck1 at location s1, unloads package1 from truck1 at location s1, and driver3 walks from location s3 to location p3_0 to reach the current state. In this state, are the following properties that involve negations True or False: there is no path between locations p0_1 and p1_0, there is no direct connection between locations p0_1 and p1_2, there is no path between locations p0_1 and p1_2, there is no direct connection between locations p0_1 and s1, there is no direct connection between locations p1_0 and p0_1, there is no direct connection between locations p1_0 and p1_2, there is no direct connection between locations p1_0 and p2_0, there is no path between locations p1_0 and p2_0, there is no path between locations p1_0 and s0, there is no direct connection between locations p1_0 and s2, there is no path between locations p1_0 and s2, there is no path between locations p1_0 and s3, there is no path between locations p1_2 and p0_1, there is no direct connection between locations p1_2 and p1_0, there is no path between locations p1_2 and p1_0, there is no path between locations p1_2 and p1_3, there is no path between locations p1_2 and p2_0, there is no path between locations p1_2 and s0, there is no path between locations p1_2 and s1, there is no path between locations p1_2 and s3, there is no path between locations p1_3 and p3_0, there is no path between locations p1_3 and s0, there is no path between locations p1_3 and s3, there is no direct connection between locations p2_0 and p0_1, there is no path between locations p2_0 and p0_1, there is no path between locations p2_0 and p1_0, there is no direct connection between locations p2_0 and p1_2, there is no direct connection between locations p2_0 and p1_3, there is no direct connection between locations p2_0 and p3_0, there is no direct connection between locations p2_0 and s0, there is no path between locations p2_0 and s0, there is no direct connection between locations p3_0 and p0_1, there is no path between locations p3_0 and p1_2, there is no direct connection between locations p3_0 and s0, there is no path between locations p3_0 and s3, there is no direct connection between locations s0 and p1_2, there is no path between locations s0 and p1_2, there is no path between locations s0 and p3_0, there is no direct connection between locations s0 and s3, there is no direct connection between locations s1 and p1_0, there is no direct connection between locations s1 and p2_0, there is no path between locations s1 and p3_0, there is no path between locations s1 and s0, there is no direct connection between locations s1 and s2, there is no path between locations s2 and p1_0, there is no direct connection between locations s2 and p1_3, there is no direct connection between locations s2 and p3_0, there is no path between locations s2 and p3_0, there is no direct connection between locations s2 and s0, there is no path between locations s2 and s1, there is no direct connection between locations s2 and s3, there is no direct connection between locations s3 and p0_1, there is no path between locations s3 and p0_1, there is no direct connection between locations s3 and p1_0, there is no path between locations s3 and p1_0, there is no direct connection between locations s3 and p2_0, there is no path between locations s3 and p2_0, there is no direct connection between locations s3 and p3_0, there is no path between locations s3 and p3_0, there is no direct connection between locations p0_1 and p3_0, there is no direct connection between locations p0_1 and s2, there is no direct connection between locations p0_1 and s3, there is no direct connection between locations p1_0 and p3_0, there is no direct connection between locations p1_0 and s3, there is no direct connection between locations p1_2 and p2_0, there is no direct connection between locations p1_2 and s0, there is no direct connection between locations p1_2 and s2, there is no direct connection between locations p1_3 and p0_1, there is no direct connection between locations p1_3 and p1_2, there is no direct connection between locations p1_3 and s0, there is no direct connection between locations p2_0 and s1, there is no direct connection between locations p2_0 and s2, there is no direct connection between locations p3_0 and p1_0, there is no direct connection between locations s0 and p1_3, there is no direct connection between locations s0 and p3_0, there is no direct connection between locations s0 and s1, there is no direct connection between locations s1 and p0_1, there is no direct connection between locations s1 and p1_2, there is no direct connection between locations s1 and p1_3, there is no direct connection between locations s1 and p3_0, there is no direct connection between locations s1 and s3, there is no direct connection between locations s2 and p2_0, there is no direct connection between locations s3 and p1_2, there is no direct connection between locations s3 and s1, there is no direct connection between locations s3 and s2, there is no path between locations p0_1 and p2_0, there is no path between locations p0_1 and s2, there is no path between locations p0_1 and s3, there is no path between locations p1_2 and p3_0, there is no path between locations p1_3 and p0_1, there is no path between locations p1_3 and p1_0, there is no path between locations p1_3 and p2_0, there is no path between locations p1_3 and s1, there is no path between locations p2_0 and p1_3, there is no path between locations p2_0 and s1, there is no path between locations p2_0 and s2, there is no path between locations p2_0 and s3, there is no path between locations p3_0 and p0_1, there is no path between locations p3_0 and p1_3, there is no path between locations p3_0 and p2_0, there is no path between locations p3_0 and s1, there is no path between locations s0 and p0_1, there is no path between locations s0 and p2_0, there is no path between locations s0 and s1, there is no path between locations s0 and s2, there is no path between locations s0 and s3, there is no path between locations s1 and p2_0, there is no path between locations s1 and s2, there is no path between locations s2 and p0_1, there is no path between locations s2 and p1_2, there is no path between locations s2 and s0, there is no path between locations s2 and s3, there is no path between locations s3 and p1_3, there is no path between locations s3 and s0, there is no path between locations s3 and s1, there is no direct connection between location p0_1 and location p1_0, there is no direct connection between location p0_1 and location p1_3, there is no direct connection between location p0_1 and location p2_0, there is no direct connection between location p0_1 and location s0, there is no direct connection between location p1_0 and location p1_3, there is no direct connection between location p1_0 and location s0, there is no direct connection between location p1_0 and location s1, there is no direct connection between location p1_2 and location p0_1, there is no direct connection between location p1_2 and location p1_3, there is no direct connection between location p1_2 and location p3_0, there is no direct connection between location p1_2 and location s1, there is no direct connection between location p1_2 and location s3, there is no direct connection between location p1_3 and location p1_0, there is no direct connection between location p1_3 and location p2_0, there is no direct connection between location p1_3 and location p3_0, there is no direct connection between location p1_3 and location s1, there is no direct connection between location p1_3 and location s2, there is no direct connection between location p1_3 and location s3, there is no direct connection between location p2_0 and location p1_0, there is no direct connection between location p2_0 and location s3, there is no direct connection between location p3_0 and location p1_2, there is no direct connection between location p3_0 and location p1_3, there is no direct connection between location p3_0 and location p2_0, there is no direct connection between location p3_0 and location s1, there is no direct connection between location p3_0 and location s2, there is no direct connection between location p3_0 and location s3, there is no direct connection between location s0 and location p0_1, there is no direct connection between location s0 and location p1_0, there is no direct connection between location s0 and location p2_0, there is no direct connection between location s0 and location s2, there is no direct connection between location s1 and location s0, there is no direct connection between location s2 and location p0_1, there is no direct connection between location s2 and location p1_0, there is no direct connection between location s2 and location p1_2, there is no direct connection between location s2 and location s1, there is no direct connection between location s3 and location p1_3, there is no direct connection between location s3 and location s0, there is no path between location p0_1 and location p1_3, there is no path between location p0_1 and location p3_0, there is no path between location p0_1 and location s0, there is no path between location p0_1 and location s1, there is no path between location p1_0 and location p0_1, there is no path between location p1_0 and location p1_2, there is no path between location p1_0 and location p1_3, there is no path between location p1_0 and location p3_0, there is no path between location p1_0 and location s1, there is no path between location p1_2 and location s2, there is no path between location p1_3 and location p1_2, there is no path between location p1_3 and location s2, there is no path between location p2_0 and location p1_2, there is no path between location p2_0 and location p3_0, there is no path between location p3_0 and location p1_0, there is no path between location p3_0 and location s0, there is no path between location p3_0 and location s2, there is no path between location s0 and location p1_0, there is no path between location s0 and location p1_3, there is no path between location s1 and location p0_1, there is no path between location s1 and location p1_0, there is no path between location s1 and location p1_2, there is no path between location s1 and location p1_3, there is no path between location s1 and location s3, there is no path between location s2 and location p1_3, there is no path between location s2 and location p2_0, there is no path between location s3 and location p1_2 and there is no path between location s3 and location s2?", "initial_state_nl_paraphrased": "Driver1 is currently located at s1, while driver2 and driver3 are both at location s3. A path exists between locations p2_0 and s0, as well as between p3_0 and s0, and also between p3_0 and s3. Similarly, a path is present between s0 and p2_0, s0 and p3_0, and a link exists between s0 and s3. Locations s1 and p1_2 are connected by a path, as are locations s2 and p1_2. A link is present between s2 and s0, and another link exists between s3 and s1. Package1 is located at s3, package2 and package3 are at s2, and package4 is at s1. Links exist between locations s1 and s2, s1 and s3, and s3 and s0. Paths are present between p0_1 and s0, p1_2 and s1, p1_2 and s2, p1_3 and s1, and p1_3 and s3. Additionally, paths exist between s0 and p0_1, s2 and p2_0, and links are present between s0 and s1, s0 and s2, s1 and s0, and s2 and s1. Furthermore, paths exist between p0_1 and s1, p2_0 and s2, s1 and p0_1, s1 and p1_3, s3 and p1_3, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and present at location s0."}
{"question_id": "a1611059-33d5-4760-8d25-4ae650f70e64", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_14", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0, driver1 walks from location s2 to location p0_2, driver1 walks from location p0_2 to s0, truck1 is boarded by driver1 at location s0, driver1 drives truck1 from location s0 to location s3, at location s3, driver1 disembarks from truck1, truck1 is unloaded with package3 at location s3, driver1 walks to location p0_3 from location s3, driver1 walks from location p0_3 to location s0, driver2 walks to location p0_2 from location s2, driver2 walks from location p0_2 to s0, at location s0, driver2 boards truck2, truck2 is driven from location s0 to s1 by driver2, at location s1, package1 is loaded in truck2, truck2 is driven from location s1 to s2 by driver2, truck2 is loaded with package2 at location s2, truck2 is unloaded with package1 at location s2, driver2 drives truck2 to location s1 from location s2 and driver2 disembarks from truck2 at location s1 to reach the current state. In this state, is it True or False that there exists a link between the locations p0_3 and p0_1?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is at location s2, driver2 is present at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_2 and s2 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_3 have a path between them, locations s2 and p0_2 have a path between them, locations s2 and s1 have a link between them, locations s2 and s3 have a link between them, locations s3 and p0_3 have a path between them, locations s3 and s1 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is at location s0, package4 is at location s2, there exists a link between the locations s1 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p2_1, there is a link between location s0 and location s1, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s3 and location s0, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_3 and location s0, there is a path between location p0_3 and location s3, there is a path between location p1_3 and location s3, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: package3 is loaded onto truck1 at location s0, driver1 moves from location s2 to p0_2 and then to s0, where driver1 boards truck1, and then drives it from s0 to s3. Upon arrival at s3, driver1 gets off truck1, and package3 is unloaded from truck1 at the same location. Driver1 then proceeds to walk from s3 to p0_3 and then back to s0. Meanwhile, driver2 walks from s2 to p0_2 and then to s0, where driver2 boards truck2. Driver2 then drives truck2 from s0 to s1, loads package1 into truck2 at s1, drives to s2, loads package2 into truck2, unloads package1, and then drives back to s1, where driver2 disembarks from truck2. In this resulting state, is it True or False that a link exists between locations p0_3 and p0_1?", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, and p0_2 and s2. Similarly, paths are present between s0 and p0_1, s0 and p0_2, and s0 and p0_3. A link connects locations s0 and s2, and another link exists between s0 and s3. A path is present between s1 and p1_3, and between s2 and p0_2. Locations s2 and s1 are linked, as are s2 and s3, and s3 and s1. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link exists between s1 and s0, and a path is present between p1_3 and s1, s1 and p0_1, and s1 and p2_1. Additionally, links exist between s0 and s1, s1 and s2, and s1 and s3, as well as between s2 and s0, s3 and s0, and s3 and s2. Paths are present between p0_1 and s1, p0_3 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s1, and p2_1 and s2. Furthermore, a path exists between s2 and p2_1, and between s3 and p1_3. Truck1 is located at s0 and is empty, while truck2 is also at s0 and contains nothing."}
{"question_id": "ef81bb12-6aa9-4726-8d40-eea970da151e", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_22", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to p4_3, driver1 walks from location p4_3 to s4, driver1 walks to location p4_1 from location s4, driver1 walks to location s1 from location p4_1, truck1 is boarded by driver1 at location s1, truck1 is driven from location s1 to s0 by driver1, at location s0, package4 is loaded in truck1, driver1 drives truck1 from location s0 to location s2, at location s2, package2 is loaded in truck1 and package1 is loaded in truck1 at location s2 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: locations p0_5 and p4_0 does not have a link between them, locations p0_5 and p4_0 does not have a path between them, locations p0_5 and p4_1 does not have a link between them, locations p0_5 and p4_1 does not have a path between them, locations p0_5 and p5_2 does not have a link between them, locations p0_5 and s1 does not have a path between them, locations p0_5 and s4 does not have a link between them, locations p4_0 and s0 does not have a link between them, locations p4_0 and s2 does not have a link between them, locations p4_0 and s2 does not have a path between them, locations p4_0 and s5 does not have a link between them, locations p4_1 and p0_5 does not have a link between them, locations p4_1 and p4_0 does not have a path between them, locations p4_1 and p5_2 does not have a path between them, locations p4_1 and s0 does not have a path between them, locations p4_1 and s3 does not have a link between them, locations p4_1 and s4 does not have a link between them, locations p4_3 and p4_1 does not have a link between them, locations p4_3 and p5_2 does not have a path between them, locations p4_3 and s0 does not have a link between them, locations p4_3 and s1 does not have a path between them, locations p4_3 and s3 does not have a link between them, locations p4_3 and s3 does not have a path between them, locations p4_3 and s5 does not have a path between them, locations p5_2 and p0_5 does not have a link between them, locations p5_2 and p4_0 does not have a path between them, locations p5_2 and p4_1 does not have a path between them, locations p5_2 and p4_3 does not have a path between them, locations p5_2 and s0 does not have a link between them, locations p5_2 and s1 does not have a link between them, locations p5_2 and s2 does not have a path between them, locations p5_2 and s3 does not have a path between them, locations p5_2 and s4 does not have a link between them, locations p5_2 and s5 does not have a path between them, locations s0 and p4_0 does not have a link between them, locations s0 and p4_3 does not have a link between them, locations s0 and p4_3 does not have a path between them, locations s0 and s1 does not have a path between them, locations s0 and s2 does not have a link between them, locations s0 and s3 does not have a path between them, locations s1 and p4_0 does not have a link between them, locations s1 and p4_0 does not have a path between them, locations s1 and p4_1 does not have a link between them, locations s1 and p4_3 does not have a link between them, locations s1 and s0 does not have a link between them, locations s1 and s3 does not have a path between them, locations s2 and p4_1 does not have a path between them, locations s2 and p5_2 does not have a link between them, locations s2 and s0 does not have a link between them, locations s2 and s3 does not have a path between them, locations s3 and p0_5 does not have a link between them, locations s3 and p4_0 does not have a path between them, locations s3 and p4_1 does not have a link between them, locations s3 and p4_1 does not have a path between them, locations s3 and p4_3 does not have a link between them, locations s3 and p5_2 does not have a link between them, locations s3 and s4 does not have a link between them, locations s3 and s4 does not have a path between them, locations s3 and s5 does not have a link between them, locations s3 and s5 does not have a path between them, locations s4 and p0_5 does not have a path between them, locations s4 and p4_1 does not have a path between them, locations s4 and p4_3 does not have a link between them, locations s4 and s0 does not have a link between them, locations s4 and s1 does not have a path between them, locations s4 and s2 does not have a path between them, locations s4 and s5 does not have a link between them, locations s4 and s5 does not have a path between them, locations s5 and p4_1 does not have a link between them, locations s5 and p4_3 does not have a path between them, locations s5 and p5_2 does not have a path between them, locations s5 and s0 does not have a path between them, locations s5 and s1 does not have a link between them, locations s5 and s3 does not have a link between them, there doesn't exist a link between the locations p0_5 and p4_3, there doesn't exist a link between the locations p4_0 and p0_5, there doesn't exist a link between the locations p4_0 and p5_2, there doesn't exist a link between the locations p4_0 and s1, there doesn't exist a link between the locations p4_1 and p4_3, there doesn't exist a link between the locations p4_1 and p5_2, there doesn't exist a link between the locations p4_1 and s1, there doesn't exist a link between the locations p4_1 and s2, there doesn't exist a link between the locations p4_1 and s5, there doesn't exist a link between the locations p4_3 and p0_5, there doesn't exist a link between the locations p4_3 and p4_0, there doesn't exist a link between the locations p4_3 and p5_2, there doesn't exist a link between the locations p4_3 and s1, there doesn't exist a link between the locations p4_3 and s2, there doesn't exist a link between the locations p4_3 and s4, there doesn't exist a link between the locations p4_3 and s5, there doesn't exist a link between the locations p5_2 and p4_0, there doesn't exist a link between the locations p5_2 and p4_1, there doesn't exist a link between the locations p5_2 and p4_3, there doesn't exist a link between the locations p5_2 and s3, there doesn't exist a link between the locations s0 and p5_2, there doesn't exist a link between the locations s0 and s3, there doesn't exist a link between the locations s0 and s5, there doesn't exist a link between the locations s1 and p0_5, there doesn't exist a link between the locations s1 and s3, there doesn't exist a link between the locations s1 and s4, there doesn't exist a link between the locations s2 and p4_0, there doesn't exist a link between the locations s2 and p4_3, there doesn't exist a link between the locations s2 and s1, there doesn't exist a link between the locations s2 and s3, there doesn't exist a link between the locations s2 and s4, there doesn't exist a link between the locations s3 and p4_0, there doesn't exist a link between the locations s3 and s0, there doesn't exist a link between the locations s3 and s2, there doesn't exist a link between the locations s4 and p0_5, there doesn't exist a link between the locations s4 and p4_1, there doesn't exist a link between the locations s4 and p5_2, there doesn't exist a link between the locations s4 and s1, there doesn't exist a link between the locations s4 and s2, there doesn't exist a link between the locations s4 and s3, there doesn't exist a link between the locations s5 and p0_5, there doesn't exist a link between the locations s5 and p5_2, there doesn't exist a link between the locations s5 and s0, there doesn't exist a link between the locations s5 and s2, there doesn't exist a path between the locations p0_5 and p4_3, there doesn't exist a path between the locations p0_5 and s0, there doesn't exist a path between the locations p0_5 and s3, there doesn't exist a path between the locations p4_0 and p0_5, there doesn't exist a path between the locations p4_0 and p5_2, there doesn't exist a path between the locations p4_0 and s0, there doesn't exist a path between the locations p4_0 and s1, there doesn't exist a path between the locations p4_1 and p0_5, there doesn't exist a path between the locations p4_1 and s1, there doesn't exist a path between the locations p4_1 and s3, there doesn't exist a path between the locations p4_1 and s4, there doesn't exist a path between the locations p4_1 and s5, there doesn't exist a path between the locations p5_2 and p0_5, there doesn't exist a path between the locations p5_2 and s1, there doesn't exist a path between the locations p5_2 and s4, there doesn't exist a path between the locations s0 and p4_1, there doesn't exist a path between the locations s1 and s0, there doesn't exist a path between the locations s1 and s2, there doesn't exist a path between the locations s1 and s4, there doesn't exist a path between the locations s1 and s5, there doesn't exist a path between the locations s2 and p4_3, there doesn't exist a path between the locations s2 and p5_2, there doesn't exist a path between the locations s2 and s0, there doesn't exist a path between the locations s2 and s4, there doesn't exist a path between the locations s2 and s5, there doesn't exist a path between the locations s3 and s1, there doesn't exist a path between the locations s3 and s2, there doesn't exist a path between the locations s4 and p4_0, there doesn't exist a path between the locations s4 and p4_3, there doesn't exist a path between the locations s4 and p5_2, there doesn't exist a path between the locations s4 and s0, there doesn't exist a path between the locations s4 and s3, there doesn't exist a path between the locations s5 and p0_5, there doesn't exist a path between the locations s5 and p4_0, there doesn't exist a path between the locations s5 and p4_1, there doesn't exist a path between the locations s5 and s2, there doesn't exist a path between the locations s5 and s3, there is no link between location p0_5 and location s0, there is no link between location p0_5 and location s1, there is no link between location p0_5 and location s2, there is no link between location p0_5 and location s3, there is no link between location p0_5 and location s5, there is no link between location p4_0 and location p4_1, there is no link between location p4_0 and location p4_3, there is no link between location p4_0 and location s3, there is no link between location p4_0 and location s4, there is no link between location p4_1 and location p4_0, there is no link between location p4_1 and location s0, there is no link between location p5_2 and location s2, there is no link between location p5_2 and location s5, there is no link between location s0 and location p0_5, there is no link between location s0 and location p4_1, there is no link between location s0 and location s1, there is no link between location s0 and location s4, there is no link between location s1 and location p5_2, there is no link between location s1 and location s2, there is no link between location s1 and location s5, there is no link between location s2 and location p0_5, there is no link between location s2 and location p4_1, there is no link between location s2 and location s5, there is no link between location s3 and location s1, there is no link between location s4 and location p4_0, there is no link between location s5 and location p4_0, there is no link between location s5 and location p4_3, there is no link between location s5 and location s4, there is no path between location p0_5 and location p5_2, there is no path between location p0_5 and location s2, there is no path between location p0_5 and location s4, there is no path between location p0_5 and location s5, there is no path between location p4_0 and location p4_1, there is no path between location p4_0 and location p4_3, there is no path between location p4_0 and location s3, there is no path between location p4_0 and location s4, there is no path between location p4_0 and location s5, there is no path between location p4_1 and location p4_3, there is no path between location p4_1 and location s2, there is no path between location p4_3 and location p0_5, there is no path between location p4_3 and location p4_0, there is no path between location p4_3 and location p4_1, there is no path between location p4_3 and location s0, there is no path between location p4_3 and location s2, there is no path between location p4_3 and location s4, there is no path between location p5_2 and location s0, there is no path between location s0 and location p0_5, there is no path between location s0 and location p4_0, there is no path between location s0 and location p5_2, there is no path between location s0 and location s2, there is no path between location s0 and location s4, there is no path between location s0 and location s5, there is no path between location s1 and location p0_5, there is no path between location s1 and location p4_1, there is no path between location s1 and location p4_3, there is no path between location s1 and location p5_2, there is no path between location s2 and location p0_5, there is no path between location s2 and location p4_0, there is no path between location s2 and location s1, there is no path between location s3 and location p0_5, there is no path between location s3 and location p4_3, there is no path between location s3 and location p5_2, there is no path between location s3 and location s0, there is no path between location s5 and location s1 and there is no path between location s5 and location s4?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s3, driver2 is at location s4, driver3 is at location s3, locations p0_5 and s0 have a path between them, locations p0_5 and s5 have a path between them, locations p4_0 and s0 have a path between them, locations p4_1 and s4 have a path between them, locations p4_3 and s3 have a path between them, locations p4_3 and s4 have a path between them, locations s0 and s2 have a link between them, locations s0 and s5 have a link between them, locations s1 and s2 have a link between them, locations s1 and s4 have a link between them, locations s2 and p5_2 have a path between them, locations s2 and s0 have a link between them, locations s2 and s5 have a link between them, locations s3 and s2 have a link between them, locations s3 and s5 have a link between them, locations s4 and s1 have a link between them, locations s4 and s5 have a link between them, locations s5 and p0_5 have a path between them, locations s5 and s3 have a link between them, package1 is present at location s2, package2 is at location s2, package3 is at location s3, package4 is present at location s0, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s0, there exists a link between the locations s5 and s0, there exists a link between the locations s5 and s2, there exists a path between the locations p4_0 and s4, there exists a path between the locations s1 and p4_1, there exists a path between the locations s4 and p4_3, there is a link between location s0 and location s4, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s4 and location s3, there is a link between location s5 and location s4, there is a path between location p4_1 and location s1, there is a path between location p5_2 and location s2, there is a path between location p5_2 and location s5, there is a path between location s0 and location p0_5, there is a path between location s0 and location p4_0, there is a path between location s3 and location p4_3, there is a path between location s4 and location p4_0, there is a path between location s4 and location p4_1, there is a path between location s5 and location p5_2, truck1 contains nothing, truck1 is at location s1, truck2 is currently at location s5 and truck2 is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by a move from s4 to p4_1, and then from p4_1 to s1. At location s1, driver1 boards truck1 and drives it from s1 to s0. At s0, package4 is loaded into truck1. Driver1 then drives truck1 from s0 to s2, where package2 and package1 are loaded into truck1, resulting in the current state. In this state, are the following properties that involve negations True or False: locations p0_5 and p4_0 are not connected by a link, locations p0_5 and p4_0 are not connected by a path, locations p0_5 and p4_1 are not connected by a link, locations p0_5 and p4_1 are not connected by a path, locations p0_5 and p5_2 are not connected by a link, locations p0_5 and s1 are not connected by a path, locations p0_5 and s4 are not connected by a link, locations p4_0 and s0 are not connected by a link, locations p4_0 and s2 are not connected by a link, locations p4_0 and s2 are not connected by a path, locations p4_0 and s5 are not connected by a link, locations p4_1 and p0_5 are not connected by a link, locations p4_1 and p4_0 are not connected by a path, locations p4_1 and p5_2 are not connected by a path, locations p4_1 and s0 are not connected by a path, locations p4_1 and s3 are not connected by a link, locations p4_1 and s4 are not connected by a link, locations p4_3 and p4_1 are not connected by a link, locations p4_3 and p5_2 are not connected by a path, locations p4_3 and s0 are not connected by a link, locations p4_3 and s1 are not connected by a path, locations p4_3 and s3 are not connected by a link, locations p4_3 and s3 are not connected by a path, locations p4_3 and s5 are not connected by a path, locations p5_2 and p0_5 are not connected by a link, locations p5_2 and p4_0 are not connected by a path, locations p5_2 and p4_1 are not connected by a path, locations p5_2 and p4_3 are not connected by a path, locations p5_2 and s0 are not connected by a link, locations p5_2 and s1 are not connected by a link, locations p5_2 and s2 are not connected by a path, locations p5_2 and s3 are not connected by a path, locations p5_2 and s4 are not connected by a link, locations p5_2 and s5 are not connected by a path, locations s0 and p4_0 are not connected by a link, locations s0 and p4_3 are not connected by a link, locations s0 and p4_3 are not connected by a path, locations s0 and s1 are not connected by a path, locations s0 and s2 are not connected by a link, locations s0 and s3 are not connected by a path, locations s1 and p4_0 are not connected by a link, locations s1 and p4_0 are not connected by a path, locations s1 and p4_1 are not connected by a link, locations s1 and p4_3 are not connected by a link, locations s1 and s0 are not connected by a link, locations s1 and s3 are not connected by a path, locations s2 and p4_1 are not connected by a path, locations s2 and p5_2 are not connected by a link, locations s2 and s0 are not connected by a link, locations s2 and s3 are not connected by a path, locations s3 and p0_5 are not connected by a link, locations s3 and p4_0 are not connected by a path, locations s3 and p4_1 are not connected by a link, locations s3 and p4_1 are not connected by a path, locations s3 and p4_3 are not connected by a link, locations s3 and p5_2 are not connected by a link, locations s3 and s4 are not connected by a link, locations s3 and s4 are not connected by a path, locations s3 and s5 are not connected by a link, locations s3 and s5 are not connected by a path, locations s4 and p0_5 are not connected by a path, locations s4 and p4_1 are not connected by a path, locations s4 and p4_3 are not connected by a link, locations s4 and s0 are not connected by a link, locations s4 and s1 are not connected by a path, locations s4 and s2 are not connected by a path, locations s4 and s5 are not connected by a link, locations s4 and s5 are not connected by a path, locations s5 and p4_1 are not connected by a link, locations s5 and p4_3 are not connected by a path, locations s5 and p5_2 are not connected by a path, locations s5 and s0 are not connected by a path, locations s5 and s1 are not connected by a link, locations s5 and s3 are not connected by a link, there is no direct connection between locations p0_5 and p4_3, there is no direct connection between locations p4_0 and p0_5, there is no direct connection between locations p4_0 and p5_2, there is no direct connection between locations p4_0 and s1, there is no direct connection between locations p4_1 and p4_3, there is no direct connection between locations p4_1 and p5_2, there is no direct connection between locations p4_1 and s1, there is no direct connection between locations p4_1 and s2, there is no direct connection between locations p4_1 and s5, there is no direct connection between locations p4_3 and p0_5, there is no direct connection between locations p4_3 and p4_0, there is no direct connection between locations p4_3 and p5_2, there is no direct connection between locations p4_3 and s1, there is no direct connection between locations p4_3 and s2, there is no direct connection between locations p4_3 and s4, there is no direct connection between locations p4_3 and s5, there is no direct connection between locations p5_2 and p4_0, there is no direct connection between locations p5_2 and p4_1, there is no direct connection between locations p5_2 and p4_3, there is no direct connection between locations p5_2 and s3, there is no direct connection between locations s0 and p5_2, there is no direct connection between locations s0 and s3, there is no direct connection between locations s0 and s5, there is no direct connection between locations s1 and p0_5, there is no direct connection between locations s1 and s3, there is no direct connection between locations s1 and s4, there is no direct connection between locations s2 and p4_0, there is no direct connection between locations s2 and p4_3, there is no direct connection between locations s2 and s1, there is no direct connection between locations s2 and s3, there is no direct connection between locations s2 and s4, there is no direct connection between locations s3 and p4_0, there is no direct connection between locations s3 and s0, there is no direct connection between locations s3 and s2, there is no direct connection between locations s4 and p0_5, there is no direct connection between locations s4 and p4_1, there is no direct connection between locations s4 and p5_2, there is no direct connection between locations s4 and s1, there is no direct connection between locations s4 and s2, there is no direct connection between locations s4 and s3, there is no direct connection between locations s5 and p0_5, there is no direct connection between locations s5 and p5_2, there is no direct connection between locations s5 and s0, there is no direct connection between locations s5 and s2, there is no indirect connection between locations p0_5 and p4_3, there is no indirect connection between locations p0_5 and s0, there is no indirect connection between locations p0_5 and s3, there is no indirect connection between locations p4_0 and p0_5, there is no indirect connection between locations p4_0 and p5_2, there is no indirect connection between locations p4_0 and s0, there is no indirect connection between locations p4_0 and s1, there is no indirect connection between locations p4_1 and p0_5, there is no indirect connection between locations p4_1 and s1, there is no indirect connection between locations p4_1 and s3, there is no indirect connection between locations p4_1 and s4, there is no indirect connection between locations p4_1 and s5, there is no indirect connection between locations p5_2 and p0_5, there is no indirect connection between locations p5_2 and s1, there is no indirect connection between locations p5_2 and s4, there is no indirect connection between locations s0 and p4_1, there is no indirect connection between locations s1 and s0, there is no indirect connection between locations s1 and s2, there is no indirect connection between locations s1 and s4, there is no indirect connection between locations s1 and s5, there is no indirect connection between locations s2 and p4_3, there is no indirect connection between locations s2 and p5_2, there is no indirect connection between locations s2 and s0, there is no indirect connection between locations s2 and s4, there is no indirect connection between locations s2 and s5, there is no indirect connection between locations s3 and s1, there is no indirect connection between locations s3 and s2, there is no indirect connection between locations s4 and p4_0, there is no indirect connection between locations s4 and p4_3, there is no indirect connection between locations s4 and p5_2, there is no indirect connection between locations s4 and s0, there is no indirect connection between locations s4 and s3, there is no indirect connection between locations s5 and p0_5, there is no indirect connection between locations s5 and p4_0, there is no indirect connection between locations s5 and p4_1, there is no indirect connection between locations s5 and s2, there is no indirect connection between locations s5 and s3, there is no direct connection between location p0_5 and location s0, there is no direct connection between location p0_5 and location s1, there is no direct connection between location p0_5 and location s2, there is no direct connection between location p0_5 and location s3, there is no direct connection between location p0_5 and location s5, there is no direct connection between location p4_0 and location p4_1, there is no direct connection between location p4_0 and location p4_3, there is no direct connection between location p4_0 and location s3, there is no direct connection between location p4_0 and location s4, there is no direct connection between location p4_1 and location p4_0, there is no direct connection between location p4_1 and location s0, there is no direct connection between location p5_2 and location s2, there is no direct connection between location p5_2 and location s5, there is no direct connection between location s0 and location p0_5, there is no direct connection between location s0 and location p4_1, there is no direct connection between location s0 and location s1, there is no direct connection between location s0 and location s4, there is no direct connection between location s1 and location p5_2, there is no direct connection between location s1 and location s2, there is no direct connection between location s1 and location s5, there is no direct connection between location s2 and location p0_5, there is no direct connection between location s2 and location p4_1, there is no direct connection between location s2 and location s5, there is no direct connection between location s3 and location s1, there is no direct connection between location s4 and location p4_0, there is no direct connection between location s5 and location p4_0, there is no direct connection between location s5 and location p4_3, there is no direct connection between location s5 and location s4, there is no indirect connection between location p0_5 and location p5_2, there is no indirect connection between location p0_5 and location s2, there is no indirect connection between location p0_5 and location s4, there is no indirect connection between location p0_5 and location s5, there is no indirect connection between location p4_0 and location p4_1, there is no indirect connection between location p4_0 and location p4_3, there is no indirect connection between location p4_0 and location s3, there is no indirect connection between location p4_0 and location s4, there is no indirect connection between location p4_0 and location s5, there is no indirect connection between location p4_1 and location p4_3, there is no indirect connection between location p4_1 and location s2, there is no indirect connection between location p4_3 and location p0_5, there is no indirect connection between location p4_3 and location p4_0, there is no indirect connection between location p4_3 and location p4_1, there is no indirect connection between location p4_3 and location s0, there is no indirect connection between location p4_3 and location s2, there is no indirect connection between location p4_3 and location s4, there is no indirect connection between location p5_2 and location s0, there is no indirect connection between location s0 and location p0_5, there is no indirect connection between location s0 and location p4_0, there is no indirect connection between location s0 and location p5_2, there is no indirect connection between location s0 and location s2, there is no indirect connection between location s0 and location s4, there is no indirect connection between location s0 and location s5, there is no indirect connection between location s1 and location p0_5, there is no indirect connection between location s1 and location p4_1, there is no indirect connection between location s1 and location p4_3, there is no indirect connection between location s1 and location p5_2, there is no indirect connection between location s2 and location p0_5, there is no indirect connection between location s2 and location p4_0, there is no indirect connection between location s2 and location s1, there is no indirect connection between location s3 and location p0_5, there is no indirect connection between location s3 and location p4_3, there is no indirect connection between location s3 and location p5_2, there is no indirect connection between location s3 and location s0, there is no indirect connection between location s5 and location s1 and there is no indirect connection between location s5 and location s4?", "initial_state_nl_paraphrased": "Driver1 is presently located at s3, while driver2 is at s4, and driver3 is also at s3. A path exists between locations p0_5 and s0, as well as between p0_5 and s5. Additionally, paths are present between p4_0 and s0, p4_1 and s4, p4_3 and s3, and p4_3 and s4. Furthermore, links exist between s0 and s2, s0 and s5, s1 and s2, s1 and s4, s2 and s0, s2 and s5, s3 and s2, s3 and s5, s4 and s1, and s4 and s5. Package1 and package2 are both located at s2, package3 is at s3, and package4 is at s0. There are links between s0 and s1, s1 and s0, s3 and s4, s4 and s0, s5 and s0, and s5 and s2. Paths also exist between p4_0 and s4, s1 and p4_1, s4 and p4_3, p4_1 and s1, p5_2 and s2, p5_2 and s5, s0 and p0_5, s0 and p4_0, s3 and p4_3, s4 and p4_0, s4 and p4_1, and s5 and p5_2. Moreover, there are links between s0 and s4, s2 and s1, s2 and s3, s4 and s3, and s5 and s4. Currently, truck1 is empty and located at s1, while truck2 is empty and at location s5."}
{"question_id": "4b9419df-9909-4bab-84df-3c50c79c08f0", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_22", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1, driver1 walks from location s2 to location p0_2, driver1 walks from location p0_2 to location s0, truck1 is boarded by driver1 at location s0, driver1 drives truck1 to location s3 from location s0, driver1 disembarks from truck1 at location s3, truck1 is unloaded with package3 at location s3, driver1 walks from location s3 to location p0_3, driver1 walks to location s0 from location p0_3 and driver2 walks to location p0_2 from location s2 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: locations p0_1 and p0_2 does not have a link between them, locations p0_1 and p0_3 does not have a link between them, locations p0_1 and p0_3 does not have a path between them, locations p0_1 and p3_0 does not have a path between them, locations p0_1 and s1 does not have a path between them, locations p0_1 and s2 does not have a link between them, locations p0_2 and p0_1 does not have a path between them, locations p0_2 and p0_3 does not have a path between them, locations p0_2 and p1_3 does not have a link between them, locations p0_2 and p2_1 does not have a path between them, locations p0_2 and s2 does not have a link between them, locations p0_3 and p3_0 does not have a link between them, locations p0_3 and p3_0 does not have a path between them, locations p0_3 and s0 does not have a link between them, locations p0_3 and s2 does not have a path between them, locations p0_3 and s3 does not have a link between them, locations p0_3 and s3 does not have a path between them, locations p1_3 and p0_1 does not have a link between them, locations p1_3 and p3_0 does not have a link between them, locations p1_3 and p3_0 does not have a path between them, locations p1_3 and s0 does not have a link between them, locations p1_3 and s0 does not have a path between them, locations p1_3 and s2 does not have a path between them, locations p2_1 and p0_1 does not have a path between them, locations p2_1 and p0_2 does not have a path between them, locations p2_1 and p0_3 does not have a link between them, locations p2_1 and p3_0 does not have a link between them, locations p3_0 and p0_1 does not have a path between them, locations p3_0 and p1_3 does not have a link between them, locations p3_0 and p1_3 does not have a path between them, locations p3_0 and s1 does not have a link between them, locations s0 and p0_1 does not have a link between them, locations s0 and p0_1 does not have a path between them, locations s0 and p0_2 does not have a link between them, locations s0 and p0_3 does not have a path between them, locations s0 and p1_3 does not have a path between them, locations s0 and s1 does not have a link between them, locations s0 and s3 does not have a link between them, locations s1 and p0_1 does not have a path between them, locations s1 and p0_2 does not have a link between them, locations s1 and p0_3 does not have a path between them, locations s1 and p2_1 does not have a link between them, locations s1 and s3 does not have a path between them, locations s2 and p0_3 does not have a link between them, locations s2 and p1_3 does not have a link between them, locations s2 and p2_1 does not have a path between them, locations s2 and p3_0 does not have a path between them, locations s2 and s0 does not have a link between them, locations s2 and s1 does not have a path between them, locations s2 and s3 does not have a link between them, locations s3 and p0_1 does not have a path between them, locations s3 and p0_2 does not have a link between them, locations s3 and p0_3 does not have a path between them, locations s3 and p1_3 does not have a path between them, locations s3 and s0 does not have a link between them, locations s3 and s0 does not have a path between them, locations s3 and s1 does not have a path between them, locations s3 and s2 does not have a path between them, there doesn't exist a link between the locations p0_1 and p1_3, there doesn't exist a link between the locations p0_1 and s0, there doesn't exist a link between the locations p0_1 and s3, there doesn't exist a link between the locations p0_2 and p0_3, there doesn't exist a link between the locations p0_2 and p2_1, there doesn't exist a link between the locations p0_3 and p0_2, there doesn't exist a link between the locations p0_3 and p1_3, there doesn't exist a link between the locations p0_3 and p2_1, there doesn't exist a link between the locations p0_3 and s2, there doesn't exist a link between the locations p1_3 and p0_3, there doesn't exist a link between the locations p1_3 and s2, there doesn't exist a link between the locations p1_3 and s3, there doesn't exist a link between the locations p2_1 and p0_1, there doesn't exist a link between the locations p2_1 and p1_3, there doesn't exist a link between the locations p2_1 and s1, there doesn't exist a link between the locations p3_0 and p0_1, there doesn't exist a link between the locations p3_0 and p0_2, there doesn't exist a link between the locations p3_0 and s2, there doesn't exist a link between the locations p3_0 and s3, there doesn't exist a link between the locations s0 and p2_1, there doesn't exist a link between the locations s0 and p3_0, there doesn't exist a link between the locations s0 and s2, there doesn't exist a link between the locations s1 and p0_1, there doesn't exist a link between the locations s1 and p3_0, there doesn't exist a link between the locations s1 and s2, there doesn't exist a link between the locations s2 and p0_1, there doesn't exist a link between the locations s2 and p0_2, there doesn't exist a link between the locations s2 and s1, there doesn't exist a link between the locations s3 and p0_1, there doesn't exist a link between the locations s3 and p1_3, there doesn't exist a link between the locations s3 and p2_1, there doesn't exist a path between the locations p0_2 and p1_3, there doesn't exist a path between the locations p0_2 and s2, there doesn't exist a path between the locations p0_3 and p0_1, there doesn't exist a path between the locations p0_3 and p0_2, there doesn't exist a path between the locations p0_3 and s0, there doesn't exist a path between the locations p1_3 and p0_2, there doesn't exist a path between the locations p1_3 and p0_3, there doesn't exist a path between the locations p1_3 and p2_1, there doesn't exist a path between the locations p1_3 and s1, there doesn't exist a path between the locations p2_1 and p0_3, there doesn't exist a path between the locations p2_1 and s0, there doesn't exist a path between the locations p2_1 and s1, there doesn't exist a path between the locations p2_1 and s2, there doesn't exist a path between the locations p3_0 and p2_1, there doesn't exist a path between the locations p3_0 and s0, there doesn't exist a path between the locations p3_0 and s1, there doesn't exist a path between the locations p3_0 and s3, there doesn't exist a path between the locations s0 and s2, there doesn't exist a path between the locations s0 and s3, there doesn't exist a path between the locations s1 and p0_2, there doesn't exist a path between the locations s1 and p3_0, there doesn't exist a path between the locations s1 and s0, there doesn't exist a path between the locations s1 and s2, there doesn't exist a path between the locations s2 and p0_3, there doesn't exist a path between the locations s2 and s0, there doesn't exist a path between the locations s2 and s3, there doesn't exist a path between the locations s3 and p2_1, there is no link between location p0_1 and location p2_1, there is no link between location p0_1 and location p3_0, there is no link between location p0_1 and location s1, there is no link between location p0_2 and location p0_1, there is no link between location p0_2 and location p3_0, there is no link between location p0_2 and location s0, there is no link between location p0_2 and location s1, there is no link between location p0_2 and location s3, there is no link between location p0_3 and location p0_1, there is no link between location p0_3 and location s1, there is no link between location p1_3 and location p0_2, there is no link between location p1_3 and location p2_1, there is no link between location p1_3 and location s1, there is no link between location p2_1 and location p0_2, there is no link between location p2_1 and location s0, there is no link between location p2_1 and location s2, there is no link between location p2_1 and location s3, there is no link between location p3_0 and location p0_3, there is no link between location p3_0 and location p2_1, there is no link between location p3_0 and location s0, there is no link between location s0 and location p0_3, there is no link between location s0 and location p1_3, there is no link between location s1 and location p0_3, there is no link between location s1 and location p1_3, there is no link between location s1 and location s0, there is no link between location s1 and location s3, there is no link between location s2 and location p2_1, there is no link between location s2 and location p3_0, there is no link between location s3 and location p0_3, there is no link between location s3 and location p3_0, there is no link between location s3 and location s1, there is no link between location s3 and location s2, there is no path between location p0_1 and location p0_2, there is no path between location p0_1 and location p1_3, there is no path between location p0_1 and location p2_1, there is no path between location p0_1 and location s0, there is no path between location p0_1 and location s2, there is no path between location p0_1 and location s3, there is no path between location p0_2 and location p3_0, there is no path between location p0_2 and location s0, there is no path between location p0_2 and location s1, there is no path between location p0_2 and location s3, there is no path between location p0_3 and location p1_3, there is no path between location p0_3 and location p2_1, there is no path between location p0_3 and location s1, there is no path between location p1_3 and location p0_1, there is no path between location p1_3 and location s3, there is no path between location p2_1 and location p1_3, there is no path between location p2_1 and location p3_0, there is no path between location p2_1 and location s3, there is no path between location p3_0 and location p0_2, there is no path between location p3_0 and location p0_3, there is no path between location p3_0 and location s2, there is no path between location s0 and location p0_2, there is no path between location s0 and location p2_1, there is no path between location s0 and location p3_0, there is no path between location s0 and location s1, there is no path between location s1 and location p1_3, there is no path between location s1 and location p2_1, there is no path between location s2 and location p0_1, there is no path between location s2 and location p0_2, there is no path between location s2 and location p1_3, there is no path between location s3 and location p0_2 and there is no path between location s3 and location p3_0?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is at location s2, driver2 is present at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_2 and s2 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_3 have a path between them, locations s2 and p0_2 have a path between them, locations s2 and s1 have a link between them, locations s2 and s3 have a link between them, locations s3 and p0_3 have a path between them, locations s3 and s1 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is at location s0, package4 is at location s2, there exists a link between the locations s1 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p2_1, there is a link between location s0 and location s1, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s3 and location s0, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_3 and location s0, there is a path between location p0_3 and location s3, there is a path between location p1_3 and location s3, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1, driver1 walks from location s2 to location p0_2, driver1 walks from location p0_2 to location s0, truck1 is boarded by driver1 at location s0, driver1 drives truck1 to location s3 from location s0, driver1 disembarks from truck1 at location s3, truck1 is unloaded with package3 at location s3, driver1 walks from location s3 to location p0_3, driver1 walks to location s0 from location p0_3 and driver2 walks to location p0_2 from location s2 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: locations p0_1 and p0_2 are not connected, locations p0_1 and p0_3 are not connected, locations p0_1 and p0_3 do not have a path, locations p0_1 and p3_0 do not have a path, locations p0_1 and s1 do not have a path, locations p0_1 and s2 do not have a connection, locations p0_2 and p0_1 do not have a path, locations p0_2 and p0_3 do not have a path, locations p0_2 and p1_3 do not have a connection, locations p0_2 and p2_1 do not have a path, locations p0_2 and s2 do not have a connection, locations p0_3 and p3_0 do not have a connection, locations p0_3 and p3_0 do not have a path, locations p0_3 and s0 do not have a connection, locations p0_3 and s2 do not have a path, locations p0_3 and s3 do not have a connection, locations p0_3 and s3 do not have a path, locations p1_3 and p0_1 do not have a connection, locations p1_3 and p3_0 do not have a connection, locations p1_3 and p3_0 do not have a path, locations p1_3 and s0 do not have a connection, locations p1_3 and s0 do not have a path, locations p1_3 and s2 do not have a path, locations p2_1 and p0_1 do not have a path, locations p2_1 and p0_2 do not have a path, locations p2_1 and p0_3 do not have a connection, locations p2_1 and p3_0 do not have a connection, locations p3_0 and p0_1 do not have a path, locations p3_0 and p1_3 do not have a connection, locations p3_0 and p1_3 do not have a path, locations p3_0 and s1 do not have a connection, locations s0 and p0_1 do not have a connection, locations s0 and p0_1 do not have a path, locations s0 and p0_2 do not have a connection, locations s0 and p0_3 do not have a path, locations s0 and p1_3 do not have a path, locations s0 and s1 do not have a connection, locations s0 and s3 do not have a connection, locations s1 and p0_1 do not have a path, locations s1 and p0_2 do not have a connection, locations s1 and p0_3 do not have a path, locations s1 and p2_1 do not have a connection, locations s1 and s3 do not have a path, locations s2 and p0_3 do not have a connection, locations s2 and p1_3 do not have a connection, locations s2 and p2_1 do not have a path, locations s2 and p3_0 do not have a path, locations s2 and s0 do not have a connection, locations s2 and s1 do not have a path, locations s2 and s3 do not have a connection, locations s3 and p0_1 do not have a path, locations s3 and p0_2 do not have a connection, locations s3 and p0_3 do not have a path, locations s3 and p1_3 do not have a path, locations s3 and s0 do not have a connection, locations s3 and s0 do not have a path, locations s3 and s1 do not have a path, locations s3 and s2 do not have a path, there is no connection between locations p0_1 and p1_3, there is no connection between locations p0_1 and s0, there is no connection between locations p0_1 and s3, there is no connection between locations p0_2 and p0_3, there is no connection between locations p0_2 and p2_1, there is no connection between locations p0_3 and p0_2, there is no connection between locations p0_3 and p1_3, there is no connection between locations p0_3 and p2_1, there is no connection between locations p0_3 and s2, there is no connection between locations p1_3 and p0_3, there is no connection between locations p1_3 and s2, there is no connection between locations p1_3 and s3, there is no connection between locations p2_1 and p0_1, there is no connection between locations p2_1 and p1_3, there is no connection between locations p2_1 and s1, there is no connection between locations p3_0 and p0_1, there is no connection between locations p3_0 and p0_2, there is no connection between locations p3_0 and s2, there is no connection between locations p3_0 and s3, there is no connection between locations s0 and p2_1, there is no connection between locations s0 and p3_0, there is no connection between locations s0 and s2, there is no connection between locations s1 and p0_1, there is no connection between locations s1 and p3_0, there is no connection between locations s1 and s2, there is no connection between locations s2 and p0_1, there is no connection between locations s2 and p0_2, there is no connection between locations s2 and s1, there is no connection between locations s3 and p0_1, there is no connection between locations s3 and p1_3, there is no connection between locations s3 and p2_1, there is no path between locations p0_2 and p1_3, there is no path between locations p0_2 and s2, there is no path between locations p0_3 and p0_1, there is no path between locations p0_3 and p0_2, there is no path between locations p0_3 and s0, there is no path between locations p1_3 and p0_2, there is no path between locations p1_3 and p0_3, there is no path between locations p1_3 and p2_1, there is no path between locations p1_3 and s1, there is no path between locations p2_1 and p0_3, there is no path between locations p2_1 and s0, there is no path between locations p2_1 and s1, there is no path between locations p2_1 and s2, there is no path between locations p3_0 and p2_1, there is no path between locations p3_0 and s0, there is no path between locations p3_0 and s1, there is no path between locations p3_0 and s3, there is no path between locations s0 and s2, there is no path between locations s0 and s3, there is no path between locations s1 and p0_2, there is no path between locations s1 and p3_0, there is no path between locations s1 and s0, there is no path between locations s1 and s2, there is no path between locations s2 and p0_3, there is no path between locations s2 and s0, there is no path between locations s2 and s3, there is no path between locations s3 and p2_1, there is no connection between location p0_1 and location p2_1, there is no connection between location p0_1 and location p3_0, there is no connection between location p0_1 and location s1, there is no connection between location p0_2 and location p0_1, there is no connection between location p0_2 and location p3_0, there is no connection between location p0_2 and location s0, there is no connection between location p0_2 and location s1, there is no connection between location p0_2 and location s3, there is no connection between location p0_3 and location p0_1, there is no connection between location p0_3 and location s1, there is no connection between location p1_3 and location p0_2, there is no connection between location p1_3 and location p2_1, there is no connection between location p1_3 and location s1, there is no connection between location p2_1 and location p0_2, there is no connection between location p2_1 and location s0, there is no connection between location p2_1 and location s2, there is no connection between location p2_1 and location s3, there is no connection between location p3_0 and location p0_3, there is no connection between location p3_0 and location p2_1, there is no connection between location p3_0 and location s0, there is no connection between location s0 and location p0_3, there is no connection between location s0 and location p1_3, there is no connection between location s1 and location p0_3, there is no connection between location s1 and location p1_3, there is no connection between location s1 and location s0, there is no connection between location s1 and location s3, there is no connection between location s2 and location p2_1, there is no connection between location s2 and location p3_0, there is no connection between location s3 and location p0_3, there is no connection between location s3 and location p3_0, there is no connection between location s3 and location s1, there is no connection between location s3 and location s2, there is no path between location p0_1 and location p0_2, there is no path between location p0_1 and location p1_3, there is no path between location p0_1 and location p2_1, there is no path between location p0_1 and location s0, there is no path between location p0_1 and location s2, there is no path between location p0_1 and location s3, there is no path between location p0_2 and location p3_0, there is no path between location p0_2 and location s0, there is no path between location p0_2 and location s1, there is no path between location p0_2 and location s3, there is no path between location p0_3 and location p1_3, there is no path between location p0_3 and location p2_1, there is no path between location p0_3 and location s1, there is no path between location p1_3 and location p0_1, there is no path between location p1_3 and location s3, there is no path between location p2_1 and location p1_3, there is no path between location p2_1 and location p3_0, there is no path between location p2_1 and location s3, there is no path between location p3_0 and location p0_2, there is no path between location p3_0 and location p0_3, there is no path between location p3_0 and location s2, there is no path between location s0 and location p0_2, there is no path between location s0 and location p2_1, there is no path between location s0 and location p3_0, there is no path between location s0 and location s1, there is no path between location s1 and location p1_3, there is no path between location s1 and location p2_1, there is no path between location s2 and location p0_1, there is no path between location s2 and location p0_2, there is no path between location s2 and location p1_3, there is no path between location s3 and location p0_2 and there is no path between location s3 and location p3_0?", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, and between p0_2 and s2. Similarly, paths are present between s0 and p0_1, s0 and p0_2, and s0 and p0_3. A link connects locations s0 and s2, and another link connects s0 and s3. A path is found between s1 and p1_3, and between s2 and p0_2. Links exist between s2 and s1, and between s2 and s3. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link is established between s1 and s0, and a path exists between p1_3 and s1, s1 and p0_1, and s1 and p2_1. Links are present between s0 and s1, s1 and s2, and s1 and s3. Additionally, links connect s2 and s0, s3 and s0, and s3 and s2. Paths are found between p0_1 and s1, p0_3 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s1, p2_1 and s2, s2 and p2_1, and s3 and p1_3. Truck1 is located at s0 and is empty, while truck2 is also at s0 and contains nothing."}
{"question_id": "7ab24987-1526-4faf-b73b-026d7493813d", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_14", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to p3_0, driver2 walks to location s0 from location p3_0, truck1 is boarded by driver2 at location s0, truck1 is driven from location s0 to s2 by driver2, at location s2, package3 is loaded in truck1, truck1 is loaded with package2 at location s2, truck1 is driven from location s2 to s0 by driver2, at location s0, package2 is unloaded in truck1, driver2 drives truck1 to location s3 from location s0, at location s3, package1 is loaded in truck1, driver2 drives truck1 from location s3 to location s1, at location s1, driver2 disembarks from truck1, driver2 walks to location p0_1 from location s1, driver2 walks from location p0_1 to location s0, truck3 is boarded by driver2 at location s0, driver2 drives truck3 from location s0 to location s2, at location s1, package3 is unloaded in truck1, at location s1, package1 is unloaded in truck1 and driver3 walks from location s3 to location p3_0 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: driver1 is at location p0_1, driver1 is at location p1_2, driver1 is at location p2_0, driver1 is at location s1, driver1 is at location s2, driver1 is currently at location p1_0, driver1 is currently at location p1_3, driver1 is currently at location p3_0, driver1 is currently at location s3, driver1 is present at location s0, driver2 is at location s0, driver2 is at location s1, driver2 is at location s2, driver2 is at location s3, driver2 is currently at location p0_1, driver2 is currently at location p1_0, driver2 is currently at location p2_0, driver2 is currently at location p3_0, driver2 is driving truck3 currently, driver2 is present at location p1_2, driver2 is present at location p1_3, driver3 is at location p0_1, driver3 is at location p1_0, driver3 is at location p3_0, driver3 is at location s2, driver3 is currently at location p1_2, driver3 is currently at location p1_3, driver3 is currently at location p2_0, driver3 is currently at location s1, driver3 is driving truck1, driver3 is driving truck2 currently, driver3 is present at location s0, driver3 is present at location s3, package1 is at location p0_1, package1 is at location p1_0, package1 is at location s0, package1 is currently at location p3_0, package1 is currently at location s1, package1 is currently at location s2, package1 is currently at location s3, package1 is located in truck3, package1 is placed in truck1, package1 is placed in truck2, package1 is present at location p1_2, package1 is present at location p1_3, package1 is present at location p2_0, package2 is at location p1_3, package2 is at location p2_0, package2 is currently at location p0_1, package2 is currently at location p1_0, package2 is currently at location p1_2, package2 is currently at location s0, package2 is currently at location s1, package2 is currently at location s2, package2 is in truck3, package2 is located in truck1, package2 is placed in truck2, package2 is present at location p3_0, package2 is present at location s3, package3 is at location p3_0, package3 is at location s0, package3 is at location s1, package3 is at location s2, package3 is currently at location p1_2, package3 is currently at location p2_0, package3 is in truck1, package3 is in truck2, package3 is in truck3, package3 is present at location p0_1, package3 is present at location p1_0, package3 is present at location p1_3, package3 is present at location s3, package4 is at location p3_0, package4 is at location s0, package4 is currently at location p1_2, package4 is currently at location p1_3, package4 is currently at location s3, package4 is in truck2, package4 is in truck3, package4 is placed in truck1, package4 is present at location p0_1, package4 is present at location p1_0, package4 is present at location p2_0, package4 is present at location s1, package4 is present at location s2, truck1 is at location p0_1, truck1 is at location p1_2, truck1 is at location p3_0, truck1 is being driven by driver1, truck1 is being driven by driver2, truck1 is currently at location p1_3, truck1 is currently at location p2_0, truck1 is currently at location s3, truck1 is present at location p1_0, truck1 is present at location s0, truck1 is present at location s1, truck1 is present at location s2, truck2 is at location p1_0, truck2 is at location p1_3, truck2 is at location p3_0, truck2 is at location s0, truck2 is being driven by driver1, truck2 is being driven by driver2, truck2 is currently at location p1_2, truck2 is currently at location s2, truck2 is present at location p0_1, truck2 is present at location p2_0, truck2 is present at location s1, truck2 is present at location s3, truck3 is being driven by driver1, truck3 is being driven by driver3, truck3 is currently at location p1_0, truck3 is currently at location p1_3, truck3 is present at location p0_1, truck3 is present at location p1_2, truck3 is present at location p2_0, truck3 is present at location p3_0, truck3 is present at location s0, truck3 is present at location s1, truck3 is present at location s2 and truck3 is present at location s3?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s1, driver2 is currently at location s3, driver3 is at location s3, locations p2_0 and s0 have a path between them, locations p3_0 and s0 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p2_0 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s2 and p1_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is present at location s3, package2 is present at location s2, package3 is at location s2, package4 is at location s1, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s2 and p2_0, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a link between location s2 and location s1, there is a path between location p0_1 and location s1, there is a path between location p2_0 and location s2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 contains nothing, truck1 is at location s0, truck2 contains nothing, truck2 is at location s3, truck3 is empty and truck3 is present at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: driver2 moves from location s3 to p3_0, then from p3_0 to s0, boards truck1 at s0, drives truck1 from s0 to s2, loads package3 into truck1 at s2, also loads package2 into truck1 at s2, drives truck1 from s2 back to s0, unloads package2 from truck1 at s0, drives truck1 from s0 to s3, loads package1 into truck1 at s3, drives truck1 from s3 to s1, disembarks from truck1 at s1, walks from s1 to p0_1, then from p0_1 to s0, boards truck3 at s0, drives truck3 from s0 to s2, unloads package3 from truck1 at s1, and unloads package1 from truck1 at s1, while driver3 walks from s3 to p3_0 to reach the current state. In this state, are the following properties of the state that do not involve negations True or False: \n\ndriver1 is at p0_1, \ndriver1 is at p1_2, \ndriver1 is at p2_0, \ndriver1 is at s1, \ndriver1 is at s2, \ndriver1 is currently at p1_0, \ndriver1 is currently at p1_3, \ndriver1 is currently at p3_0, \ndriver1 is currently at s3, \ndriver1 is present at s0, \ndriver2 is at s0, \ndriver2 is at s1, \ndriver2 is at s2, \ndriver2 is at s3, \ndriver2 is currently at p0_1, \ndriver2 is currently at p1_0, \ndriver2 is currently at p2_0, \ndriver2 is currently at p3_0, \ndriver2 is driving truck3, \ndriver2 is present at p1_2, \ndriver2 is present at p1_3, \ndriver3 is at p0_1, \ndriver3 is at p1_0, \ndriver3 is at p3_0, \ndriver3 is at s2, \ndriver3 is currently at p1_2, \ndriver3 is currently at p1_3, \ndriver3 is currently at p2_0, \ndriver3 is currently at s1, \ndriver3 is driving truck1, \ndriver3 is driving truck2, \ndriver3 is present at s0, \ndriver3 is present at s3, \npackage1 is at p0_1, \npackage1 is at p1_0, \npackage1 is at s0, \npackage1 is currently at p3_0, \npackage1 is currently at s1, \npackage1 is currently at s2, \npackage1 is currently at s3, \npackage1 is in truck3, \npackage1 is in truck1, \npackage1 is in truck2, \npackage1 is present at p1_2, \npackage1 is present at p1_3, \npackage1 is present at p2_0, \npackage2 is at p1_3, \npackage2 is at p2_0, \npackage2 is currently at p0_1, \npackage2 is currently at p1_0, \npackage2 is currently at p1_2, \npackage2 is currently at s0, \npackage2 is currently at s1, \npackage2 is currently at s2, \npackage2 is in truck3, \npackage2 is in truck1, \npackage2 is in truck2, \npackage2 is present at p3_0, \npackage2 is present at s3, \npackage3 is at p3_0, \npackage3 is at s0, \npackage3 is at s1, \npackage3 is at s2, \npackage3 is currently at p1_2, \npackage3 is currently at p2_0, \npackage3 is in truck1, \npackage3 is in truck2, \npackage3 is in truck3, \npackage3 is present at p0_1, \npackage3 is present at p1_0, \npackage3 is present at p1_3, \npackage3 is present at s3, \npackage4 is at p3_0, \npackage4 is at s0, \npackage4 is currently at p1_2, \npackage4 is currently at p1_3, \npackage4 is currently at s3, \npackage4 is in truck2, \npackage4 is in truck3, \npackage4 is in truck1, \npackage4 is present at p0_1, \npackage4 is present at p1_0, \npackage4 is present at p2_0, \npackage4 is present at s1, \npackage4 is present at s2, \ntruck1 is at p0_1, \ntruck1 is at p1_2, \ntruck1 is at p3_0, \ntruck1 is driven by driver1, \ntruck1 is driven by driver2, \ntruck1 is currently at p1_3, \ntruck1 is currently at p2_0, \ntruck1 is currently at s3, \ntruck1 is present at p1_0, \ntruck1 is present at s0, \ntruck1 is present at s1, \ntruck1 is present at s2, \ntruck2 is at p1_0, \ntruck2 is at p1_3, \ntruck2 is at p3_0, \ntruck2 is at s0, \ntruck2 is driven by driver1, \ntruck2 is driven by driver2, \ntruck2 is currently at p1_2, \ntruck2 is currently at s2, \ntruck2 is present at p0_1, \ntruck2 is present at p2_0, \ntruck2 is present at s1, \ntruck2 is present at s3, \ntruck3 is driven by driver1, \ntruck3 is driven by driver3, \ntruck3 is currently at p1_0, \ntruck3 is currently at p1_3, \ntruck3 is present at p0_1, \ntruck3 is present at p1_2, \ntruck3 is present at p2_0, \ntruck3 is present at p3_0, \ntruck3 is present at s0, \ntruck3 is present at s1, \ntruck3 is present at s2, and \ntruck3 is present at s3.", "initial_state_nl_paraphrased": "Driver1 is currently located at s1, while driver2 and driver3 are both at location s3. A path exists between locations p2_0 and s0, as well as between p3_0 and s0, and also between p3_0 and s3. Similarly, a path is present between s0 and p2_0, s0 and p3_0, and a link exists between s0 and s3. Locations s1 and p1_2 are connected by a path, as are locations s2 and p1_2. A link is present between s2 and s0, and another link exists between s3 and s1. Package1 is located at s3, package2 and package3 are at s2, and package4 is at s1. Links exist between locations s1 and s2, s1 and s3, and s3 and s0. Paths are present between p0_1 and s0, p1_2 and s1, p1_2 and s2, p1_3 and s1, and p1_3 and s3. Additionally, paths exist between s0 and p0_1, s2 and p2_0, s0 and s1, s0 and s2, s1 and s0, and s2 and s1. Furthermore, paths are present between p0_1 and s1, p2_0 and s2, s1 and p0_1, s1 and p1_3, s3 and p1_3, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and present at location s0."}
{"question_id": "24a61b62-5e40-49cc-b923-8e33955494dd", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_19", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks to location p3_0 from location s3 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: locations p1_2 and s1 have a path between them, locations p1_3 and s1 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and s2 have a link between them, locations s1 and s3 have a link between them, locations s2 and p2_0 have a path between them, locations s3 and p1_3 have a path between them, there exists a link between the locations s0 and s1, there exists a link between the locations s0 and s2, there exists a link between the locations s2 and s0, there exists a link between the locations s3 and s0, there exists a path between the locations p1_3 and s3, there exists a path between the locations p2_0 and s2, there exists a path between the locations p3_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s3 and p3_0, there is a link between location s1 and location s0, there is a link between location s2 and location s1, there is a link between location s3 and location s1, there is a path between location p0_1 and location s0, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s2, there is a path between location p2_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3 and there is a path between location s2 and location p1_2?", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s1, driver2 is currently at location s3, driver3 is at location s3, locations p2_0 and s0 have a path between them, locations p3_0 and s0 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p2_0 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s2 and p1_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is present at location s3, package2 is present at location s2, package3 is at location s2, package4 is at location s1, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s2 and p2_0, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a link between location s2 and location s1, there is a path between location p0_1 and location s1, there is a path between location p2_0 and location s2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 contains nothing, truck1 is at location s0, truck2 contains nothing, truck2 is at location s3, truck3 is empty and truck3 is present at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: driver2 moves from location s3 to location p3_0 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: a path exists between locations p1_2 and s1, a path exists between locations p1_3 and s1, a path exists between locations p3_0 and s3, a link exists between locations s0 and s3, a path exists between locations s1 and p1_2, a link exists between locations s1 and s2, a link exists between locations s1 and s3, a path exists between locations s2 and p2_0, a path exists between locations s3 and p1_3, a link is present between locations s0 and s1, a link is present between locations s0 and s2, a link is present between locations s2 and s0, a link is present between locations s3 and s0, a path is present between locations p1_3 and s3, a path is present between locations p2_0 and s2, a path is present between locations p3_0 and s0, a path is present between locations s0 and p0_1, a path is present between locations s3 and p3_0, locations s1 and s0 are linked, locations s2 and s1 are linked, locations s3 and s1 are linked, a path connects location p0_1 and location s0, a path connects location p0_1 and location s1, a path connects location p1_2 and location s2, a path connects location p2_0 and location s0, a path connects location s0 and location p2_0, a path connects location s0 and location p3_0, a path connects location s1 and location p0_1, a path connects location s1 and location p1_3 and a path connects location s2 and location p1_2?", "initial_state_nl_paraphrased": "Driver1 is currently located at s1, while driver2 and driver3 are both at location s3. A path exists between locations p2_0 and s0, as well as between p3_0 and s0, and also between p3_0 and s3. Similarly, a path is present between s0 and p2_0, s0 and p3_0, and a link exists between s0 and s3. Locations s1 and p1_2 are connected by a path, as are locations s2 and p1_2, and a link exists between s2 and s0. Additionally, a link is present between s3 and s1. Package1 is located at s3, package2 and package3 are at s2, and package4 is at s1. Links exist between locations s1 and s2, s1 and s3, and s3 and s0. Paths are present between p0_1 and s0, p1_2 and s1, p1_2 and s2, p1_3 and s1, and p1_3 and s3. Furthermore, paths exist between s0 and p0_1, s2 and p2_0, and links are present between s0 and s1, s0 and s2, s1 and s0, and s2 and s1. Paths also exist between p0_1 and s1, p2_0 and s2, s1 and p0_1, s1 and p1_3, s3 and p1_3, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and present at location s0."}
{"question_id": "42c6f7d0-a580-4c0f-8658-87ae82997559", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_13", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location s0, driver3 boards truck1, at location s0, package3 is loaded in truck1, truck1 is loaded with package1 at location s0, driver3 drives truck1 to location s3 from location s0, truck1 is unloaded with package1 at location s3, truck1 is driven from location s3 to s1 by driver3, at location s1, driver3 disembarks from truck1, at location s1, package3 is unloaded in truck1, package2 is loaded in truck2 at location s2, driver1 walks from location s3 to location p3_0, driver1 walks from location p3_0 to s0, driver2 walks to location p1_3 from location s3, driver2 walks to location s1 from location p1_3, driver2 walks from location s1 to location p1_2, driver2 walks to location s2 from location p1_2, driver3 walks from location s1 to location p1_2, driver3 walks from location p1_2 to location s2, at location s2, driver3 boards truck2 and driver3 drives truck2 from location s2 to location s3 to reach the current state. In this state, is it True or False that locations s2 and p1_2 have a path between them?", "answer": "True", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s3, driver2 is present at location s3, driver3 is at location s0, locations p0_1 and s0 have a path between them, locations p1_3 and s3 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and p3_0 have a path between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is currently at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s2 and p1_2, there exists a path between the locations s3 and p3_0, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s1, there is a path between location p3_0 and location s3, there is a path between location s0 and location p2_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_2, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is present at location s2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: at location s0, driver3 gets into truck1, package3 is loaded onto truck1 at location s0, and package1 is also loaded onto truck1 at location s0. Then, driver3 drives truck1 from location s0 to location s3, where package1 is unloaded from truck1. Next, truck1 is driven by driver3 from location s3 to location s1, where driver3 gets out of truck1, and package3 is unloaded from truck1. Meanwhile, at location s2, package2 is loaded onto truck2. Driver1 walks from location s3 to location p3_0 and then to location s0. Driver2 walks from location s3 to location p1_3, then to location s1, followed by location p1_2, and finally to location s2. Driver3 walks from location s1 to location p1_2 and then to location s2, where driver3 gets into truck2 and drives it from location s2 to location s3, reaching the current state. In this state, is it True or False that locations s2 and p1_2 are connected by a path?", "initial_state_nl_paraphrased": "Driver1 is situated at location s3, while driver2 is also present at location s3. Meanwhile, driver3 is located at s0. A path exists between locations p0_1 and s0, as well as between p1_3 and s3, p2_0 and s2, and p3_0 and s0. Additionally, a path is present between s0 and p3_0, s3 and p1_3, and a link connects s3 and s0. Package1 is currently at location s0, package2 is at location s2, and package3 is also at location s0. Furthermore, a link exists between locations s0 and s3, s2 and s0, and s2 and s1. Paths are also present between p1_2 and s2, p1_3 and s1, p2_0 and s0, s0 and p0_1, s2 and p1_2, and s3 and p3_0. Moreover, links connect locations s0 and s2, s1 and s2, s1 and s3, s2 and s3, s3 and s1, and s3 and s2. Paths also exist between p0_1 and s1, p1_2 and s1, p3_0 and s3, s0 and p2_0, s1 and p0_1, s1 and p1_2, s1 and p1_3, and s2 and p2_0. Truck1 is currently at location s0 and is empty, while truck2 is present at location s2 and contains nothing."}
{"question_id": "da6bfa2d-1af8-4fc5-904f-baa494afee68", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_14", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver3 boards truck1 at location s0, at location s0, package3 is loaded in truck1, truck1 is loaded with package1 at location s0, driver3 drives truck1 from location s0 to location s3, package1 is unloaded from truck1 at location s3, truck1 is driven from location s3 to s1 by driver3, at location s1, driver3 disembarks from truck1, package3 is unloaded from truck1 at location s1, truck2 is loaded with package2 at location s2 and driver1 walks from location s3 to location p3_0 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: driver1 is at location p1_0, driver1 is at location p1_2, driver1 is at location s2, driver1 is currently at location p0_1, driver1 is currently at location p1_3, driver1 is currently at location p2_0, driver1 is currently at location p3_0, driver1 is currently at location s0, driver1 is driving truck1 currently, driver1 is driving truck2 currently, driver1 is present at location p2_1, driver1 is present at location s1, driver1 is present at location s3, driver2 is at location p1_0, driver2 is at location p2_0, driver2 is at location p2_1, driver2 is at location p3_0, driver2 is at location s2, driver2 is currently at location p0_1, driver2 is currently at location p1_3, driver2 is driving truck1, driver2 is driving truck2 currently, driver2 is present at location p1_2, driver2 is present at location s0, driver2 is present at location s1, driver2 is present at location s3, driver3 is at location p0_1, driver3 is at location p1_0, driver3 is at location p1_2, driver3 is at location p1_3, driver3 is at location p3_0, driver3 is at location s1, driver3 is at location s2, driver3 is currently at location p2_0, driver3 is currently at location p2_1, driver3 is currently at location s0, driver3 is currently at location s3, package1 is at location p0_1, package1 is at location p1_2, package1 is at location p1_3, package1 is at location p3_0, package1 is at location s3, package1 is currently at location p1_0, package1 is currently at location p2_0, package1 is currently at location p2_1, package1 is currently at location s0, package1 is currently at location s1, package1 is currently at location s2, package1 is located in truck1, package1 is located in truck2, package2 is at location p1_2, package2 is at location p1_3, package2 is at location p3_0, package2 is at location s2, package2 is at location s3, package2 is currently at location p2_0, package2 is currently at location p2_1, package2 is placed in truck1, package2 is placed in truck2, package2 is present at location p0_1, package2 is present at location p1_0, package2 is present at location s0, package2 is present at location s1, package3 is at location p0_1, package3 is at location p1_0, package3 is at location p1_2, package3 is at location p1_3, package3 is at location p3_0, package3 is currently at location s0, package3 is in truck2, package3 is placed in truck1, package3 is present at location p2_0, package3 is present at location p2_1, package3 is present at location s1, package3 is present at location s2, package3 is present at location s3, truck1 is being driven by driver3, truck1 is currently at location p1_0, truck1 is currently at location p2_1, truck1 is currently at location p3_0, truck1 is currently at location s3, truck1 is present at location p0_1, truck1 is present at location p1_2, truck1 is present at location p1_3, truck1 is present at location p2_0, truck1 is present at location s0, truck1 is present at location s1, truck1 is present at location s2, truck2 is at location p1_0, truck2 is at location p1_3, truck2 is at location p2_0, truck2 is at location s0, truck2 is being driven by driver3, truck2 is currently at location p0_1, truck2 is currently at location p1_2, truck2 is currently at location p3_0, truck2 is currently at location s1, truck2 is currently at location s2, truck2 is present at location p2_1 and truck2 is present at location s3?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s3, driver2 is present at location s3, driver3 is at location s0, locations p0_1 and s0 have a path between them, locations p1_3 and s3 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and p3_0 have a path between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is currently at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s2 and p1_2, there exists a path between the locations s3 and p3_0, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s1, there is a path between location p3_0 and location s3, there is a path between location s0 and location p2_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_2, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is present at location s2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: driver3 boards truck1 at location s0, at location s0, package3 is loaded into truck1, truck1 is loaded with package1 at location s0, driver3 drives truck1 from location s0 to location s3, package1 is unloaded from truck1 at location s3, truck1 is driven from location s3 to s1 by driver3, at location s1, driver3 disembarks from truck1, package3 is unloaded from truck1 at location s1, truck2 is loaded with package2 at location s2 and driver1 walks from location s3 to location p3_0 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: driver1 is located at p1_0, driver1 is located at p1_2, driver1 is located at s2, driver1 is currently positioned at p0_1, driver1 is currently positioned at p1_3, driver1 is currently positioned at p2_0, driver1 is currently positioned at p3_0, driver1 is currently positioned at s0, driver1 is currently driving truck1, driver1 is currently driving truck2, driver1 is present at p2_1, driver1 is present at s1, driver1 is present at s3, driver2 is located at p1_0, driver2 is located at p2_0, driver2 is located at p2_1, driver2 is located at p3_0, driver2 is located at s2, driver2 is currently positioned at p0_1, driver2 is currently positioned at p1_3, driver2 is driving truck1, driver2 is currently driving truck2, driver2 is present at p1_2, driver2 is present at s0, driver2 is present at s1, driver2 is present at s3, driver3 is located at p0_1, driver3 is located at p1_0, driver3 is located at p1_2, driver3 is located at p1_3, driver3 is located at p3_0, driver3 is located at s1, driver3 is located at s2, driver3 is currently positioned at p2_0, driver3 is currently positioned at p2_1, driver3 is currently positioned at s0, driver3 is currently positioned at s3, package1 is located at p0_1, package1 is located at p1_2, package1 is located at p1_3, package1 is located at p3_0, package1 is located at s3, package1 is currently positioned at p1_0, package1 is currently positioned at p2_0, package1 is currently positioned at p2_1, package1 is currently positioned at s0, package1 is currently positioned at s1, package1 is currently positioned at s2, package1 is inside truck1, package1 is inside truck2, package2 is located at p1_2, package2 is located at p1_3, package2 is located at p3_0, package2 is located at s2, package2 is located at s3, package2 is currently positioned at p2_0, package2 is currently positioned at p2_1, package2 is placed inside truck1, package2 is placed inside truck2, package2 is present at p0_1, package2 is present at p1_0, package2 is present at s0, package2 is present at s1, package3 is located at p0_1, package3 is located at p1_0, package3 is located at p1_2, package3 is located at p1_3, package3 is located at p3_0, package3 is currently positioned at s0, package3 is inside truck2, package3 is placed inside truck1, package3 is present at p2_0, package3 is present at p2_1, package3 is present at s1, package3 is present at s2, package3 is present at s3, truck1 is being driven by driver3, truck1 is currently positioned at p1_0, truck1 is currently positioned at p2_1, truck1 is currently positioned at p3_0, truck1 is currently positioned at s3, truck1 is present at p0_1, truck1 is present at p1_2, truck1 is present at p1_3, truck1 is present at p2_0, truck1 is present at s0, truck1 is present at s1, truck1 is present at s2, truck2 is located at p1_0, truck2 is located at p1_3, truck2 is located at p2_0, truck2 is located at s0, truck2 is being driven by driver3, truck2 is currently positioned at p0_1, truck2 is currently positioned at p1_2, truck2 is currently positioned at p3_0, truck2 is currently positioned at s1, truck2 is currently positioned at s2, truck2 is present at p2_1 and truck2 is present at location s3?", "initial_state_nl_paraphrased": "Driver1 is situated at location s3, while driver2 is also present at location s3. Meanwhile, driver3 is located at s0. A path exists between locations p0_1 and s0, as well as between p1_3 and s3, p2_0 and s2, and p3_0 and s0. Additionally, a path is present between s0 and p3_0, s3 and p1_3, and a link connects s0 and s3. Currently, package1 is at location s0, package2 is at location s2, and package3 is also at location s0. Links are established between locations s0 and s3, s2 and s0, s2 and s1, s0 and s2, s1 and s2, s1 and s3, s2 and s3, s3 and s1, and s3 and s2. Furthermore, paths exist between p1_2 and s2, p1_3 and s1, p2_0 and s0, s0 and p0_1, s2 and p1_2, s3 and p3_0, p0_1 and s1, p1_2 and s1, p3_0 and s3, s0 and p2_0, s1 and p0_1, s1 and p1_2, s1 and p1_3, and s2 and p2_0. Lastly, truck1 is located at s0 and is empty, while truck2 is at location s2 and contains nothing."}
{"question_id": "b6066132-0e39-4cd8-9645-31199d63ac50", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_6", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to location p3_0, driver2 walks from location p3_0 to s0, truck1 is boarded by driver2 at location s0, driver2 drives truck1 to location s2 from location s0, at location s2, package3 is loaded in truck1, package2 is loaded in truck1 at location s2, truck1 is driven from location s2 to s0 by driver2, package2 is unloaded from truck1 at location s0, truck1 is driven from location s0 to s3 by driver2, truck1 is loaded with package1 at location s3, truck1 is driven from location s3 to s1 by driver2, at location s1, driver2 disembarks from truck1, driver2 walks from location s1 to location p0_1, driver2 walks from location p0_1 to s0, at location s0, driver2 boards truck3, driver2 drives truck3 to location s2 from location s0, package3 is unloaded from truck1 at location s1, truck1 is unloaded with package1 at location s1 and driver3 walks from location s3 to p3_0 to reach the current state. In this state, is it True or False that truck3 contains nothing?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s1, driver2 is currently at location s3, driver3 is at location s3, locations p2_0 and s0 have a path between them, locations p3_0 and s0 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p2_0 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s2 and p1_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is present at location s3, package2 is present at location s2, package3 is at location s2, package4 is at location s1, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s2 and p2_0, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a link between location s2 and location s1, there is a path between location p0_1 and location s1, there is a path between location p2_0 and location s2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 contains nothing, truck1 is at location s0, truck2 contains nothing, truck2 is at location s3, truck3 is empty and truck3 is present at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver2 moves from location s3 to location p3_0, then from p3_0 to s0, boards truck1 at s0, and drives it from s0 to s2. At s2, package3 and package2 are loaded into truck1. Driver2 then drives truck1 from s2 back to s0, unloads package2, and drives truck1 from s0 to s3. At s3, package1 is loaded into truck1, and driver2 drives it to s1. Upon arrival at s1, driver2 gets out of truck1, walks to p0_1, and then to s0, where they board truck3. Driver2 drives truck3 from s0 to s2. Meanwhile, package3 is unloaded from truck1 at s1, and package1 is also unloaded from truck1 at s1. Additionally, driver3 walks from s3 to p3_0. In this resulting state, is it True or False that truck3 is empty?", "initial_state_nl_paraphrased": "Driver1 is currently located at s1, while driver2 and driver3 are both at location s3. A path exists between locations p2_0 and s0, as well as between p3_0 and s0, and also between p3_0 and s3. Similarly, a path is present between s0 and p2_0, s0 and p3_0, and a link exists between s0 and s3. Locations s1 and p1_2 are connected by a path, as are locations s2 and p1_2. A link is present between s2 and s0, and another link exists between s3 and s1. Package1 is located at s3, package2 and package3 are at s2, and package4 is at s1. Links exist between locations s1 and s2, s1 and s3, and s3 and s0. Paths are present between p0_1 and s0, p1_2 and s1, p1_2 and s2, p1_3 and s1, and p1_3 and s3. Additionally, paths exist between s0 and p0_1, s2 and p2_0, s0 and s1, s0 and s2, s1 and s0, and s2 and s1. Furthermore, paths are present between p0_1 and s1, p2_0 and s2, s1 and p0_1, s1 and p1_3, s3 and p1_3, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and present at location s0."}
{"question_id": "34a653de-be9d-4993-97a3-366a1838b0e9", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_11", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is boarded by driver3 at location s0, truck1 is loaded with package3 at location s0, at location s0, package1 is loaded in truck1, truck1 is driven from location s0 to s3 by driver3, package1 is unloaded from truck1 at location s3, truck1 is driven from location s3 to s1 by driver3, driver3 disembarks from truck1 at location s1, package3 is unloaded from truck1 at location s1, at location s2, package2 is loaded in truck2, driver1 walks to location p3_0 from location s3, driver1 walks from location p3_0 to location s0, driver2 walks to location p1_3 from location s3, driver2 walks to location s1 from location p1_3, driver2 walks to location p1_2 from location s1, driver2 walks from location p1_2 to s2, driver3 walks to location p1_2 from location s1, driver3 walks from location p1_2 to location s2, at location s2, driver3 boards truck2 and driver3 drives truck2 to location s3 from location s2 to reach the current state. In this state, is it True or False that truck1 is not currently at location p3_0?", "answer": "True", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s3, driver2 is present at location s3, driver3 is at location s0, locations p0_1 and s0 have a path between them, locations p1_3 and s3 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and p3_0 have a path between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is currently at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s2 and p1_2, there exists a path between the locations s3 and p3_0, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s1, there is a path between location p3_0 and location s3, there is a path between location s0 and location p2_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_2, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is present at location s2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: driver3 boards truck1 at location s0, truck1 is loaded with package3 at location s0, package1 is also loaded into truck1 at location s0, then truck1 is driven by driver3 from location s0 to s3, package1 is unloaded from truck1 at location s3, truck1 is then driven by driver3 from location s3 to s1, driver3 disembarks from truck1 at location s1, and package3 is unloaded from truck1 at location s1. Meanwhile, at location s2, package2 is loaded into truck2. Driver1 walks from location s3 to p3_0 and then to location s0, while driver2 walks from location s3 to p1_3, then to location s1, then to p1_2, and finally to location s2. Driver3 walks from location s1 to p1_2 and then to location s2, where driver3 boards truck2 and drives it from location s2 to s3 to reach the current state. In this state, is it True or False that truck1 is not currently at location p3_0?", "initial_state_nl_paraphrased": "The current location of Driver1 is s3, while Driver2 is also at location s3, and Driver3 is at location s0. A path exists between locations p0_1 and s0, as well as between p1_3 and s3, p2_0 and s2, and p3_0 and s0. Additionally, a path is present between s0 and p3_0, s3 and p1_3, and a link exists between s0 and s3. Package1 is located at s0, Package2 is at s2, and Package3 is also at s0. Links are present between s0 and s3, s2 and s0, s2 and s1, and a path exists between p1_2 and s2, p1_3 and s1, p2_0 and s0, s0 and p0_1, s2 and p1_2, and s3 and p3_0. Furthermore, links exist between s0 and s2, s1 and s2, s1 and s3, s2 and s3, s3 and s1, and s3 and s2. Paths are also present between p0_1 and s1, p1_2 and s1, p3_0 and s3, s0 and p2_0, s1 and p0_1, s1 and p1_2, s1 and p1_3, and s2 and p2_0. Currently, Truck1 is empty and located at s0, while Truck2 is empty and at location s2."}
{"question_id": "74e6211a-d7b4-4093-b6f2-e77f719a9633", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to p3_0, driver2 walks from location p3_0 to location s0, truck1 is boarded by driver2 at location s0, truck1 is driven from location s0 to s2 by driver2, package3 is loaded in truck1 at location s2, truck1 is loaded with package2 at location s2, truck1 is driven from location s2 to s0 by driver2, at location s0, package2 is unloaded in truck1, truck1 is driven from location s0 to s3 by driver2 and at location s3, package1 is loaded in truck1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: truck2 contains nothing and truck3 contains nothing?", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s1, driver2 is currently at location s3, driver3 is at location s3, locations p2_0 and s0 have a path between them, locations p3_0 and s0 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p2_0 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s2 and p1_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is present at location s3, package2 is present at location s2, package3 is at location s2, package4 is at location s1, there exists a link between the locations s1 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s2 and p2_0, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a link between location s2 and location s1, there is a path between location p0_1 and location s1, there is a path between location p2_0 and location s2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 contains nothing, truck1 is at location s0, truck2 contains nothing, truck2 is at location s3, truck3 is empty and truck3 is present at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: driver2 moves from location s3 to p3_0, then from p3_0 to s0, boards truck1 at s0, drives truck1 from s0 to s2, loads package3 and package2 into truck1 at s2, drives truck1 back from s2 to s0, unloads package2 from truck1 at s0, drives truck1 from s0 to s3, and finally loads package1 into truck1 at s3, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: truck2 is empty and truck3 is empty?", "initial_state_nl_paraphrased": "Driver1 is currently located at s1, while driver2 and driver3 are both at location s3. A path exists between locations p2_0 and s0, as well as between p3_0 and s0, and also between p3_0 and s3. Similarly, a path is present between s0 and p2_0, s0 and p3_0, and a link exists between s0 and s3. Locations s1 and p1_2 are connected by a path, as are locations s2 and p1_2, and a link exists between s2 and s0. Additionally, a link is present between s3 and s1. Package1 is located at s3, package2 and package3 are at s2, and package4 is at s1. Links exist between locations s1 and s2, s1 and s3, and s3 and s0. Paths are present between p0_1 and s0, p1_2 and s1, p1_2 and s2, p1_3 and s1, and p1_3 and s3. Furthermore, paths exist between s0 and p0_1, s2 and p2_0, and links are present between s0 and s1, s0 and s2, s1 and s0, and s2 and s1. There are also paths between p0_1 and s1, p2_0 and s2, s1 and p0_1, s1 and p1_3, s3 and p1_3, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and present at location s0."}
{"question_id": "8e34721b-41cd-4bb4-89b7-2f50df4a8dd2", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_22", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1, driver1 walks from location s2 to p0_2, driver1 walks from location p0_2 to location s0, driver1 boards truck1 at location s0, driver1 drives truck1 to location s3 from location s0, at location s3, driver1 disembarks from truck1, package3 is unloaded from truck1 at location s3, driver1 walks to location p0_3 from location s3, driver1 walks from location p0_3 to s0, driver2 walks to location p0_2 from location s2, driver2 walks from location p0_2 to location s0, at location s0, driver2 boards truck2, driver2 drives truck2 from location s0 to location s1, truck2 is loaded with package1 at location s1, driver2 drives truck2 from location s1 to location s2, at location s2, package2 is loaded in truck2, at location s2, package1 is unloaded in truck2, driver2 drives truck2 to location s1 from location s2 and from truck2, driver2 disembarks at location s1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: locations p0_1 and p0_2 does not have a link between them, locations p0_1 and p2_1 does not have a link between them, locations p0_1 and s1 does not have a path between them, locations p0_1 and s2 does not have a link between them, locations p0_1 and s3 does not have a link between them, locations p0_1 and s3 does not have a path between them, locations p0_2 and p1_3 does not have a link between them, locations p0_2 and p3_0 does not have a link between them, locations p0_2 and s0 does not have a link between them, locations p0_2 and s3 does not have a path between them, locations p0_3 and p0_2 does not have a link between them, locations p0_3 and p2_1 does not have a link between them, locations p0_3 and s1 does not have a path between them, locations p1_3 and p3_0 does not have a path between them, locations p1_3 and s1 does not have a link between them, locations p1_3 and s1 does not have a path between them, locations p2_1 and p0_1 does not have a path between them, locations p2_1 and p0_3 does not have a link between them, locations p2_1 and p3_0 does not have a path between them, locations p2_1 and s0 does not have a link between them, locations p2_1 and s2 does not have a link between them, locations p2_1 and s3 does not have a path between them, locations p3_0 and p0_2 does not have a link between them, locations p3_0 and p0_2 does not have a path between them, locations p3_0 and p1_3 does not have a path between them, locations p3_0 and s1 does not have a path between them, locations p3_0 and s2 does not have a link between them, locations s0 and p0_1 does not have a link between them, locations s0 and p0_3 does not have a link between them, locations s0 and p1_3 does not have a link between them, locations s0 and p1_3 does not have a path between them, locations s0 and p3_0 does not have a link between them, locations s0 and s2 does not have a path between them, locations s1 and p1_3 does not have a path between them, locations s1 and p2_1 does not have a path between them, locations s1 and s0 does not have a path between them, locations s1 and s2 does not have a path between them, locations s2 and p0_1 does not have a link between them, locations s2 and p0_1 does not have a path between them, locations s2 and p1_3 does not have a link between them, locations s2 and p2_1 does not have a link between them, locations s2 and p3_0 does not have a link between them, locations s2 and s1 does not have a link between them, locations s2 and s1 does not have a path between them, locations s3 and p0_1 does not have a link between them, locations s3 and p0_1 does not have a path between them, locations s3 and p0_3 does not have a link between them, locations s3 and p2_1 does not have a path between them, locations s3 and s2 does not have a path between them, there doesn't exist a link between the locations p0_1 and p1_3, there doesn't exist a link between the locations p0_1 and p3_0, there doesn't exist a link between the locations p0_2 and p0_3, there doesn't exist a link between the locations p0_2 and s1, there doesn't exist a link between the locations p0_2 and s2, there doesn't exist a link between the locations p0_3 and p1_3, there doesn't exist a link between the locations p0_3 and p3_0, there doesn't exist a link between the locations p0_3 and s1, there doesn't exist a link between the locations p0_3 and s2, there doesn't exist a link between the locations p0_3 and s3, there doesn't exist a link between the locations p1_3 and p0_1, there doesn't exist a link between the locations p1_3 and p0_3, there doesn't exist a link between the locations p1_3 and p2_1, there doesn't exist a link between the locations p1_3 and p3_0, there doesn't exist a link between the locations p1_3 and s2, there doesn't exist a link between the locations p1_3 and s3, there doesn't exist a link between the locations p2_1 and p0_1, there doesn't exist a link between the locations p2_1 and s1, there doesn't exist a link between the locations p3_0 and p0_1, there doesn't exist a link between the locations p3_0 and p0_3, there doesn't exist a link between the locations p3_0 and p1_3, there doesn't exist a link between the locations p3_0 and p2_1, there doesn't exist a link between the locations p3_0 and s3, there doesn't exist a link between the locations s0 and p0_2, there doesn't exist a link between the locations s0 and p2_1, there doesn't exist a link between the locations s1 and p0_2, there doesn't exist a link between the locations s1 and p1_3, there doesn't exist a link between the locations s1 and p2_1, there doesn't exist a link between the locations s1 and p3_0, there doesn't exist a link between the locations s1 and s2, there doesn't exist a link between the locations s1 and s3, there doesn't exist a link between the locations s2 and s0, there doesn't exist a link between the locations s2 and s3, there doesn't exist a link between the locations s3 and p1_3, there doesn't exist a link between the locations s3 and s0, there doesn't exist a path between the locations p0_1 and p0_2, there doesn't exist a path between the locations p0_1 and p0_3, there doesn't exist a path between the locations p0_1 and p1_3, there doesn't exist a path between the locations p0_1 and p2_1, there doesn't exist a path between the locations p0_1 and p3_0, there doesn't exist a path between the locations p0_1 and s2, there doesn't exist a path between the locations p0_2 and p0_1, there doesn't exist a path between the locations p0_2 and p3_0, there doesn't exist a path between the locations p0_2 and s1, there doesn't exist a path between the locations p0_2 and s2, there doesn't exist a path between the locations p0_3 and p1_3, there doesn't exist a path between the locations p0_3 and p3_0, there doesn't exist a path between the locations p0_3 and s2, there doesn't exist a path between the locations p1_3 and p0_1, there doesn't exist a path between the locations p1_3 and p0_2, there doesn't exist a path between the locations p1_3 and s2, there doesn't exist a path between the locations p1_3 and s3, there doesn't exist a path between the locations p2_1 and p0_2, there doesn't exist a path between the locations p2_1 and p1_3, there doesn't exist a path between the locations p2_1 and s2, there doesn't exist a path between the locations p3_0 and p0_3, there doesn't exist a path between the locations p3_0 and s0, there doesn't exist a path between the locations s0 and p0_2, there doesn't exist a path between the locations s0 and p0_3, there doesn't exist a path between the locations s0 and p2_1, there doesn't exist a path between the locations s0 and s1, there doesn't exist a path between the locations s1 and p0_1, there doesn't exist a path between the locations s1 and p0_3, there doesn't exist a path between the locations s1 and p3_0, there doesn't exist a path between the locations s1 and s3, there doesn't exist a path between the locations s2 and p1_3, there doesn't exist a path between the locations s2 and s0, there doesn't exist a path between the locations s3 and p0_2, there doesn't exist a path between the locations s3 and p1_3, there is no link between location p0_1 and location p0_3, there is no link between location p0_1 and location s0, there is no link between location p0_1 and location s1, there is no link between location p0_2 and location p0_1, there is no link between location p0_2 and location p2_1, there is no link between location p0_2 and location s3, there is no link between location p0_3 and location p0_1, there is no link between location p0_3 and location s0, there is no link between location p1_3 and location p0_2, there is no link between location p1_3 and location s0, there is no link between location p2_1 and location p0_2, there is no link between location p2_1 and location p1_3, there is no link between location p2_1 and location p3_0, there is no link between location p2_1 and location s3, there is no link between location p3_0 and location s0, there is no link between location p3_0 and location s1, there is no link between location s0 and location s1, there is no link between location s0 and location s2, there is no link between location s0 and location s3, there is no link between location s1 and location p0_1, there is no link between location s1 and location p0_3, there is no link between location s1 and location s0, there is no link between location s2 and location p0_2, there is no link between location s2 and location p0_3, there is no link between location s3 and location p0_2, there is no link between location s3 and location p2_1, there is no link between location s3 and location p3_0, there is no link between location s3 and location s1, there is no link between location s3 and location s2, there is no path between location p0_1 and location s0, there is no path between location p0_2 and location p0_3, there is no path between location p0_2 and location p1_3, there is no path between location p0_2 and location p2_1, there is no path between location p0_2 and location s0, there is no path between location p0_3 and location p0_1, there is no path between location p0_3 and location p0_2, there is no path between location p0_3 and location p2_1, there is no path between location p0_3 and location s0, there is no path between location p0_3 and location s3, there is no path between location p1_3 and location p0_3, there is no path between location p1_3 and location p2_1, there is no path between location p1_3 and location s0, there is no path between location p2_1 and location p0_3, there is no path between location p2_1 and location s0, there is no path between location p2_1 and location s1, there is no path between location p3_0 and location p0_1, there is no path between location p3_0 and location p2_1, there is no path between location p3_0 and location s2, there is no path between location p3_0 and location s3, there is no path between location s0 and location p0_1, there is no path between location s0 and location p3_0, there is no path between location s0 and location s3, there is no path between location s1 and location p0_2, there is no path between location s2 and location p0_2, there is no path between location s2 and location p0_3, there is no path between location s2 and location p2_1, there is no path between location s2 and location p3_0, there is no path between location s2 and location s3, there is no path between location s3 and location p0_3, there is no path between location s3 and location p3_0, there is no path between location s3 and location s0 and there is no path between location s3 and location s1?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is at location s2, driver2 is present at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_2 and s2 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_3 have a path between them, locations s2 and p0_2 have a path between them, locations s2 and s1 have a link between them, locations s2 and s3 have a link between them, locations s3 and p0_3 have a path between them, locations s3 and s1 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is at location s0, package4 is at location s2, there exists a link between the locations s1 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p2_1, there is a link between location s0 and location s1, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s3 and location s0, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_3 and location s0, there is a path between location p0_3 and location s3, there is a path between location p1_3 and location s3, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is performed: at location s0, package3 is loaded onto truck1, driver1 walks from location s2 to p0_2, then from p0_2 to location s0, boards truck1 at location s0, drives truck1 from location s0 to location s3, disembarks from truck1 at location s3, unloads package3 from truck1 at location s3, walks to location p0_3 from location s3, and then from p0_3 to s0. Meanwhile, driver2 walks from location s2 to p0_2, then from p0_2 to location s0, boards truck2 at location s0, drives truck2 from location s0 to location s1, loads package1 onto truck2 at location s1, drives truck2 from location s1 to location s2, loads package2 onto truck2 at location s2, unloads package1 from truck2 at location s2, drives truck2 from location s2 to location s1, and disembarks from truck2 at location s1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: locations p0_1 and p0_2 are not connected, locations p0_1 and p2_1 are not connected, locations p0_1 and s1 are not reachable, locations p0_1 and s2 are not connected, locations p0_1 and s3 are not connected, locations p0_1 and s3 are not reachable, locations p0_2 and p1_3 are not connected, locations p0_2 and p3_0 are not connected, locations p0_2 and s0 are not connected, locations p0_2 and s3 are not reachable, locations p0_3 and p0_2 are not connected, locations p0_3 and p2_1 are not connected, locations p0_3 and s1 are not reachable, locations p1_3 and p3_0 are not reachable, locations p1_3 and s1 are not connected, locations p1_3 and s1 are not reachable, locations p2_1 and p0_1 are not reachable, locations p2_1 and p0_3 are not connected, locations p2_1 and p3_0 are not reachable, locations p2_1 and s0 are not connected, locations p2_1 and s2 are not connected, locations p2_1 and s3 are not reachable, locations p3_0 and p0_2 are not connected, locations p3_0 and p0_2 are not reachable, locations p3_0 and p1_3 are not reachable, locations p3_0 and s1 are not reachable, locations p3_0 and s2 are not connected, locations s0 and p0_1 are not connected, locations s0 and p0_3 are not connected, locations s0 and p1_3 are not connected, locations s0 and p1_3 are not reachable, locations s0 and p3_0 are not connected, locations s0 and s2 are not reachable, locations s1 and p1_3 are not reachable, locations s1 and p2_1 are not reachable, locations s1 and s0 are not reachable, locations s1 and s2 are not reachable, locations s2 and p0_1 are not connected, locations s2 and p0_1 are not reachable, locations s2 and p1_3 are not connected, locations s2 and p2_1 are not connected, locations s2 and p3_0 are not connected, locations s2 and s1 are not connected, locations s2 and s1 are not reachable, locations s3 and p0_1 are not connected, locations s3 and p0_1 are not reachable, locations s3 and p0_3 are not connected, locations s3 and p2_1 are not reachable, locations s3 and s2 are not reachable, there is no connection between locations p0_1 and p1_3, there is no connection between locations p0_1 and p3_0, there is no connection between locations p0_2 and p0_3, there is no connection between locations p0_2 and s1, there is no connection between locations p0_2 and s2, there is no connection between locations p0_3 and p1_3, there is no connection between locations p0_3 and p3_0, there is no connection between locations p0_3 and s1, there is no connection between locations p0_3 and s2, there is no connection between locations p0_3 and s3, there is no connection between locations p1_3 and p0_1, there is no connection between locations p1_3 and p0_3, there is no connection between locations p1_3 and p2_1, there is no connection between locations p1_3 and p3_0, there is no connection between locations p1_3 and s2, there is no connection between locations p1_3 and s3, there is no connection between locations p2_1 and p0_1, there is no connection between locations p2_1 and s1, there is no connection between locations p3_0 and p0_1, there is no connection between locations p3_0 and p0_3, there is no connection between locations p3_0 and p1_3, there is no connection between locations p3_0 and p2_1, there is no connection between locations p3_0 and s3, there is no connection between locations s0 and p0_2, there is no connection between locations s0 and p2_1, there is no connection between locations s1 and p0_2, there is no connection between locations s1 and p1_3, there is no connection between locations s1 and p2_1, there is no connection between locations s1 and p3_0, there is no connection between locations s1 and s2, there is no connection between locations s1 and s3, there is no connection between locations s2 and s0, there is no connection between locations s2 and s3, there is no connection between locations s3 and p1_3, there is no connection between locations s3 and s0, there is no path between locations p0_1 and p0_2, there is no path between locations p0_1 and p0_3, there is no path between locations p0_1 and p1_3, there is no path between locations p0_1 and p2_1, there is no path between locations p0_1 and p3_0, there is no path between locations p0_1 and s2, there is no path between locations p0_2 and p0_1, there is no path between locations p0_2 and p3_0, there is no path between locations p0_2 and s1, there is no path between locations p0_2 and s2, there is no path between locations p0_3 and p1_3, there is no path between locations p0_3 and p3_0, there is no path between locations p0_3 and s2, there is no path between locations p1_3 and p0_1, there is no path between locations p1_3 and p0_2, there is no path between locations p1_3 and s2, there is no path between locations p1_3 and s3, there is no path between locations p2_1 and p0_2, there is no path between locations p2_1 and p1_3, there is no path between locations p2_1 and s2, there is no path between locations p3_0 and p0_3, there is no path between locations p3_0 and s0, there is no path between locations s0 and p0_2, there is no path between locations s0 and p0_3, there is no path between locations s0 and p2_1, there is no path between locations s0 and s1, there is no path between locations s1 and p0_1, there is no path between locations s1 and p0_3, there is no path between locations s1 and p3_0, there is no path between locations s1 and s3, there is no path between locations s2 and p1_3, there is no path between locations s2 and s0, there is no path between locations s3 and p0_2, there is no path between locations s3 and p1_3, there is no connection between location p0_1 and location p0_3, there is no connection between location p0_1 and location s0, there is no connection between location p0_1 and location s1, there is no connection between location p0_2 and location p0_1, there is no connection between location p0_2 and location p2_1, there is no connection between location p0_2 and location s3, there is no connection between location p0_3 and location p0_1, there is no connection between location p0_3 and location s0, there is no connection between location p1_3 and location p0_2, there is no connection between location p1_3 and location s0, there is no connection between location p2_1 and location p0_2, there is no connection between location p2_1 and location p1_3, there is no connection between location p2_1 and location p3_0, there is no connection between location p2_1 and location s3, there is no connection between location p3_0 and location s0, there is no connection between location p3_0 and location s1, there is no connection between location s0 and location s1, there is no connection between location s0 and location s2, there is no connection between location s0 and location s3, there is no connection between location s1 and location p0_1, there is no connection between location s1 and location p0_3, there is no connection between location s1 and location s0, there is no connection between location s2 and location p0_2, there is no connection between location s2 and location p0_3, there is no connection between location s3 and location p0_2, there is no connection between location s3 and location p2_1, there is no connection between location s3 and location p3_0, there is no connection between location s3 and location s1, there is no connection between location s3 and location s2, there is no path between location p0_1 and location s0, there is no path between location p0_2 and location p0_3, there is no path between location p0_2 and location p1_3, there is no path between location p0_2 and location p2_1, there is no path between location p0_2 and location s0, there is no path between location p0_3 and location p0_1, there is no path between location p0_3 and location p0_2, there is no path between location p0_3 and location p2_1, there is no path between location p0_3 and location s0, there is no path between location p0_3 and location s3, there is no path between location p1_3 and location p0_3, there is no path between location p1_3 and location p2_1, there is no path between location p1_3 and location s0, there is no path between location p2_1 and location p0_3, there is no path between location p2_1 and location s0, there is no path between location p2_1 and location s1, there is no path between location p3_0 and location p0_1, there is no path between location p3_0 and location p2_1, there is no path between location p3_0 and location s2, there is no path between location p3_0 and location s3, there is no path between location s0 and location p0_1, there is no path between location s0 and location p3_0, there is no path between location s0 and location s3, there is no path between location s1 and location p0_2, there is no path between location s2 and location p0_2, there is no path between location s2 and location p0_3, there is no path between location s2 and location p2_1, there is no path between location s2 and location p3_0, there is no path between location s2 and location s3, there is no path between location s3 and location p0_3, there is no path between location s3 and location p3_0, there is no path between location s3 and location s0 and there is no path between location s3 and location s1?", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, and p0_2 and s2. Similarly, paths are present between s0 and p0_1, s0 and p0_2, and s0 and p0_3. A link connects locations s0 and s2, and another link exists between s0 and s3. A path is present between s1 and p1_3, and between s2 and p0_2. Locations s2 and s1 are linked, as are s2 and s3, and s3 and p0_3. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link exists between s1 and s0, and a path is present between p1_3 and s1, s1 and p0_1, and s1 and p2_1. Links connect s0 and s1, s1 and s2, and s1 and s3, as well as s2 and s0, s3 and s0, and s3 and s2. Paths are present between p0_1 and s1, p0_3 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s1, and p2_1 and s2. Additionally, a path exists between s2 and p2_1, and between s3 and p1_3. Truck1 is located at s0 and is empty, while truck2 is also at s0 and contains nothing."}
{"question_id": "4ca29108-9b48-4d52-9ce6-5dcc2c11ab78", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_14", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1, driver1 walks from location s2 to location p0_2, driver1 walks from location p0_2 to s0, driver1 boards truck1 at location s0, truck1 is driven from location s0 to s3 by driver1, at location s3, driver1 disembarks from truck1, truck1 is unloaded with package3 at location s3, driver1 walks to location p0_3 from location s3, driver1 walks to location s0 from location p0_3, driver2 walks from location s2 to p0_2, driver2 walks from location p0_2 to s0, driver2 boards truck2 at location s0, driver2 drives truck2 to location s1 from location s0, truck2 is loaded with package1 at location s1, truck2 is driven from location s1 to s2 by driver2, package2 is loaded in truck2 at location s2, at location s2, package1 is unloaded in truck2, driver2 drives truck2 from location s2 to location s1 and driver2 disembarks from truck2 at location s1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: driver1 is at location p3_0, driver1 is at location s0, driver1 is at location s1, driver1 is currently at location p0_1, driver1 is currently at location p0_2, driver1 is currently at location p1_3, driver1 is currently at location p2_1, driver1 is currently at location s3, driver1 is driving truck2 currently, driver1 is present at location p0_3, driver1 is present at location s2, driver2 is at location p3_0, driver2 is currently at location s0, driver2 is currently at location s1, driver2 is currently at location s2, driver2 is currently at location s3, driver2 is driving truck1, driver2 is present at location p0_1, driver2 is present at location p0_2, driver2 is present at location p0_3, driver2 is present at location p1_3, driver2 is present at location p2_1, package1 is at location p0_3, package1 is at location p1_3, package1 is at location p2_1, package1 is at location s2, package1 is at location s3, package1 is currently at location p0_2, package1 is currently at location p3_0, package1 is currently at location s1, package1 is located in truck1, package1 is placed in truck2, package1 is present at location p0_1, package1 is present at location s0, package2 is at location p0_1, package2 is at location s0, package2 is at location s2, package2 is currently at location p0_2, package2 is in truck2, package2 is located in truck1, package2 is present at location p0_3, package2 is present at location p1_3, package2 is present at location p2_1, package2 is present at location p3_0, package2 is present at location s1, package2 is present at location s3, package3 is at location p0_1, package3 is at location s0, package3 is at location s1, package3 is currently at location p0_2, package3 is currently at location p0_3, package3 is currently at location p2_1, package3 is currently at location p3_0, package3 is in truck1, package3 is located in truck2, package3 is present at location p1_3, package3 is present at location s2, package3 is present at location s3, package4 is at location p2_1, package4 is at location p3_0, package4 is at location s0, package4 is at location s1, package4 is at location s3, package4 is currently at location p0_1, package4 is currently at location p0_2, package4 is currently at location p1_3, package4 is currently at location s2, package4 is in truck2, package4 is placed in truck1, package4 is present at location p0_3, truck1 is at location p0_2, truck1 is at location p0_3, truck1 is at location s0, truck1 is at location s1, truck1 is being driven by driver1, truck1 is currently at location s2, truck1 is currently at location s3, truck1 is present at location p0_1, truck1 is present at location p1_3, truck1 is present at location p2_1, truck1 is present at location p3_0, truck2 is at location p3_0, truck2 is being driven by driver2, truck2 is currently at location p0_1, truck2 is currently at location p0_3, truck2 is currently at location p1_3, truck2 is currently at location s0, truck2 is currently at location s1, truck2 is currently at location s2, truck2 is currently at location s3, truck2 is present at location p0_2 and truck2 is present at location p2_1?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is at location s2, driver2 is present at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_2 and s2 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_3 have a path between them, locations s2 and p0_2 have a path between them, locations s2 and s1 have a link between them, locations s2 and s3 have a link between them, locations s3 and p0_3 have a path between them, locations s3 and s1 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is at location s0, package4 is at location s2, there exists a link between the locations s1 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p2_1, there is a link between location s0 and location s1, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s3 and location s0, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_3 and location s0, there is a path between location p0_3 and location s3, there is a path between location p1_3 and location s3, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: at location s0, package3 is loaded onto truck1, driver1 walks from location s2 to location p0_2, then from p0_2 to s0, boards truck1 at s0, and drives truck1 from s0 to s3, where driver1 disembarks from truck1, and package3 is unloaded from truck1 at s3. Driver1 then walks from s3 to p0_3 and then to s0. Meanwhile, driver2 walks from s2 to p0_2, then to s0, boards truck2 at s0, drives truck2 from s0 to s1, loads package1 onto truck2 at s1, drives truck2 from s1 to s2, loads package2 onto truck2 at s2, unloads package1 from truck2 at s2, and drives truck2 from s2 to s1, where driver2 disembarks from truck2. In this resulting state, the following properties that do not involve negations are evaluated as True or False: \n\n- driver1 is at location p3_0, \n- driver1 is at location s0, \n- driver1 is at location s1, \n- driver1 is currently at location p0_1, \n- driver1 is currently at location p0_2, \n- driver1 is currently at location p1_3, \n- driver1 is currently at location p2_1, \n- driver1 is currently at location s3, \n- driver1 is driving truck2 currently, \n- driver1 is present at location p0_3, \n- driver1 is present at location s2, \n- driver2 is at location p3_0, \n- driver2 is currently at location s0, \n- driver2 is currently at location s1, \n- driver2 is currently at location s2, \n- driver2 is currently at location s3, \n- driver2 is driving truck1, \n- driver2 is present at location p0_1, \n- driver2 is present at location p0_2, \n- driver2 is present at location p0_3, \n- driver2 is present at location p1_3, \n- driver2 is present at location p2_1, \n- package1 is at location p0_3, \n- package1 is at location p1_3, \n- package1 is at location p2_1, \n- package1 is at location s2, \n- package1 is at location s3, \n- package1 is currently at location p0_2, \n- package1 is currently at location p3_0, \n- package1 is currently at location s1, \n- package1 is located in truck1, \n- package1 is placed in truck2, \n- package1 is present at location p0_1, \n- package1 is present at location s0, \n- package2 is at location p0_1, \n- package2 is at location s0, \n- package2 is at location s2, \n- package2 is currently at location p0_2, \n- package2 is in truck2, \n- package2 is located in truck1, \n- package2 is present at location p0_3, \n- package2 is present at location p1_3, \n- package2 is present at location p2_1, \n- package2 is present at location p3_0, \n- package2 is present at location s1, \n- package2 is present at location s3, \n- package3 is at location p0_1, \n- package3 is at location s0, \n- package3 is at location s1, \n- package3 is currently at location p0_2, \n- package3 is currently at location p0_3, \n- package3 is currently at location p2_1, \n- package3 is currently at location p3_0, \n- package3 is in truck1, \n- package3 is located in truck2, \n- package3 is present at location p1_3, \n- package3 is present at location s2, \n- package3 is present at location s3, \n- package4 is at location p2_1, \n- package4 is at location p3_0, \n- package4 is at location s0, \n- package4 is at location s1, \n- package4 is at location s3, \n- package4 is currently at location p0_1, \n- package4 is currently at location p0_2, \n- package4 is currently at location p1_3, \n- package4 is currently at location s2, \n- package4 is in truck2, \n- package4 is placed in truck1, \n- package4 is present at location p0_3, \n- truck1 is at location p0_2, \n- truck1 is at location p0_3, \n- truck1 is at location s0, \n- truck1 is at location s1, \n- truck1 is being driven by driver1, \n- truck1 is currently at location s2, \n- truck1 is currently at location s3, \n- truck1 is present at location p0_1, \n- truck1 is present at location p1_3, \n- truck1 is present at location p2_1, \n- truck1 is present at location p3_0, \n- truck2 is at location p3_0, \n- truck2 is being driven by driver2, \n- truck2 is currently at location p0_1, \n- truck2 is currently at location p0_3, \n- truck2 is currently at location p1_3, \n- truck2 is currently at location s0, \n- truck2 is currently at location s1, \n- truck2 is currently at location s2, \n- truck2 is currently at location s3, \n- truck2 is present at location p0_2, \n- truck2 is present at location p2_1.", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, and p0_2 and s2. Similarly, paths are present between s0 and p0_1, s0 and p0_2, and s0 and p0_3. A link connects locations s0 and s2, and another link exists between s0 and s3. A path is present between s1 and p1_3, and between s2 and p0_2. Locations s2 and s1 are linked, as are s2 and s3, and s3 and p0_3. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link exists between s1 and s0, and a path is present between p1_3 and s1, s1 and p0_1, and s1 and p2_1. Additionally, links connect s0 and s1, s1 and s2, and s1 and s3, as well as s2 and s0, s3 and s0, and s3 and s2. Paths are present between p0_1 and s1, p0_3 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s1, and p2_1 and s2. Furthermore, a path exists between s2 and p2_1, and between s3 and p1_3. Truck1 is located at s0 and is empty, while truck2 is also at s0 and contains nothing."}
{"question_id": "83ba52a9-7329-4456-9061-5239b162c17f", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_19", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location s0, driver3 boards truck1, at location s0, package3 is loaded in truck1, at location s0, package1 is loaded in truck1, driver3 drives truck1 to location s3 from location s0, at location s3, package1 is unloaded in truck1, driver3 drives truck1 from location s3 to location s1, from truck1, driver3 disembarks at location s1, truck1 is unloaded with package3 at location s1, truck2 is loaded with package2 at location s2 and driver1 walks from location s3 to p3_0 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: locations p1_2 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p2_0 and s2 have a path between them, locations s0 and p2_0 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s2 have a link between them, locations s1 and s2 have a link between them, locations s2 and p2_0 have a path between them, locations s2 and s1 have a link between them, locations s3 and p3_0 have a path between them, there exists a link between the locations s1 and s3, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_0 and s0, there exists a path between the locations p3_0 and s3, there exists a path between the locations s1 and p0_1, there exists a path between the locations s2 and p1_2, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s3, there is a link between location s3 and location s0, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s3, there is a path between location p3_0 and location s0, there is a path between location s0 and location p0_1, there is a path between location s1 and location p1_2 and there is a path between location s1 and location p1_3?", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s3, driver2 is present at location s3, driver3 is at location s0, locations p0_1 and s0 have a path between them, locations p1_3 and s3 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and p3_0 have a path between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is currently at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s2 and p1_2, there exists a path between the locations s3 and p3_0, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s1, there is a path between location p3_0 and location s3, there is a path between location s0 and location p2_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_2, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is present at location s2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: at location s0, driver3 gets into truck1, package3 is then loaded into truck1 at location s0, followed by the loading of package1 into truck1 at the same location, driver3 then drives truck1 from location s0 to location s3, at location s3, package1 is unloaded from truck1, driver3 then drives truck1 from location s3 to location s1, upon arrival at location s1, driver3 gets out of truck1, and package3 is unloaded from truck1 at location s1, meanwhile, truck2 is loaded with package2 at location s2, and driver1 walks from location s3 to p3_0 to reach the current state. In this state, are the following properties of the state that do not involve negations True or False: there is a path connecting locations p1_2 and s1, there is a path connecting locations p1_2 and s2, there is a path connecting locations p2_0 and s2, there is a path connecting locations s0 and p2_0, there is a path connecting locations s0 and p3_0, locations s0 and s2 are linked, locations s1 and s2 are linked, there is a path connecting locations s2 and p2_0, locations s2 and s1 are linked, there is a path connecting locations s3 and p3_0, a link exists between locations s1 and s3, a path exists between locations p0_1 and s0, a path exists between locations p1_3 and s1, a path exists between locations p2_0 and s0, a path exists between locations p3_0 and s3, a path exists between locations s1 and p0_1, a path exists between locations s2 and p1_2, a path exists between locations s3 and p1_3, locations s0 and s3 are linked, locations s2 and s0 are linked, locations s2 and s3 are linked, locations s3 and s0 are linked, locations s3 and s1 are linked, locations s3 and s2 are linked, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s3, there is a path between location p3_0 and location s0, there is a path between location s0 and location p0_1, there is a path between location s1 and location p1_2, and there is a path between location s1 and location p1_3?", "initial_state_nl_paraphrased": "Driver1 is situated at location s3, while driver2 is also present at location s3. Meanwhile, driver3 is located at s0. A path exists between locations p0_1 and s0, as well as between p1_3 and s3. Additionally, a path is present between p2_0 and s2, and between p3_0 and s0. Furthermore, a path connects s0 and p3_0, s3 and p1_3, and a link exists between s0 and s3. \n\nCurrently, package1 is at location s0, package2 is at location s2, and package3 is also at location s0. Links exist between locations s0 and s3, s2 and s0, and s2 and s1. Paths are present between p1_2 and s2, p1_3 and s1, p2_0 and s0, s0 and p0_1, s2 and p1_2, and s3 and p3_0. \n\nMoreover, links connect location s0 to s2, s1 to s2, s1 to s3, s2 to s3, s3 to s1, and s3 to s2. Paths also exist between p0_1 and s1, p1_2 and s1, p3_0 and s3, s0 and p2_0, s1 and p0_1, s1 and p1_2, s1 and p1_3, and s2 and p2_0. \n\nLastly, truck1 is located at s0 and is empty, while truck2, which is also empty, is present at location s2."}
{"question_id": "a6f21fb3-fff6-4c80-95e5-80687ff36fac", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_16", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1, driver1 walks from location s2 to p0_2, driver1 walks from location p0_2 to s0, driver1 boards truck1 at location s0, driver1 drives truck1 from location s0 to location s3, from truck1, driver1 disembarks at location s3, truck1 is unloaded with package3 at location s3, driver1 walks to location p0_3 from location s3, driver1 walks from location p0_3 to s0, driver2 walks from location s2 to location p0_2, driver2 walks from location p0_2 to location s0, driver2 boards truck2 at location s0, truck2 is driven from location s0 to s1 by driver2, at location s1, package1 is loaded in truck2, truck2 is driven from location s1 to s2 by driver2, truck2 is loaded with package2 at location s2, package1 is unloaded from truck2 at location s2, driver2 drives truck2 to location s1 from location s2 and at location s1, driver2 disembarks from truck2 to reach the current state. In this state, is it True or False that there is no path between location p2_1 and location s2?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is at location s2, driver2 is present at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_2 and s2 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_3 have a path between them, locations s2 and p0_2 have a path between them, locations s2 and s1 have a link between them, locations s2 and s3 have a link between them, locations s3 and p0_3 have a path between them, locations s3 and s1 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is at location s0, package4 is at location s2, there exists a link between the locations s1 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p2_1, there is a link between location s0 and location s1, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s3 and location s0, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_3 and location s0, there is a path between location p0_3 and location s3, there is a path between location p1_3 and location s3, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: at location s0, package3 is placed in truck1, driver1 moves from location s2 to p0_2, then from p0_2 to s0, boards truck1 at s0, drives truck1 from s0 to s3, gets off truck1 at s3, unloads package3 from truck1 at s3, walks from s3 to p0_3, and then from p0_3 back to s0. Meanwhile, driver2 moves from s2 to p0_2, then to s0, boards truck2 at s0, drives truck2 from s0 to s1, loads package1 into truck2 at s1, drives truck2 from s1 to s2, loads package2 into truck2 at s2, unloads package1 from truck2 at s2, drives truck2 back from s2 to s1, and finally gets off truck2 at s1 to reach the current state. In this state, is it True or False that there is no path between location p2_1 and location s2?", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, and p0_2 and s2. Similarly, paths are present between s0 and p0_1, s0 and p0_2, and s0 and p0_3. A link connects locations s0 and s2, and another link exists between s0 and s3. A path is present between s1 and p1_3, and between s2 and p0_2. Locations s2 and s1 are linked, as are s2 and s3. A path exists between s3 and p0_3, and a link connects s3 and s1. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link exists between s1 and s0, and a path is present between p1_3 and s1. Paths also exist between s1 and p0_1, and between s1 and p2_1. Links connect s0 and s1, s1 and s2, and s1 and s3. Additionally, links exist between s2 and s0, s3 and s0, and s3 and s2. Paths are present between p0_1 and s1, p0_3 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s1, and p2_1 and s2. A path also exists between s2 and p2_1, and between s3 and p1_3. Truck1 is located at s0 and is empty, while truck2 is also at s0 and contains nothing."}
{"question_id": "c35e4cd9-2e5f-4a12-8b06-86b6ceef4e3e", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_12", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver3 boards truck1 at location s0, package3 is loaded in truck1 at location s0, truck1 is loaded with package1 at location s0, driver3 drives truck1 to location s3 from location s0, at location s3, package1 is unloaded in truck1, truck1 is driven from location s3 to s1 by driver3, at location s1, driver3 disembarks from truck1, package3 is unloaded from truck1 at location s1, at location s2, package2 is loaded in truck2 and driver1 walks from location s3 to p3_0 to reach the current state. In this state, is it True or False that truck1 is not present at location s1?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s3, driver2 is present at location s3, driver3 is at location s0, locations p0_1 and s0 have a path between them, locations p1_3 and s3 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and p3_0 have a path between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is currently at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s2 and p1_2, there exists a path between the locations s3 and p3_0, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s1, there is a path between location p3_0 and location s3, there is a path between location s0 and location p2_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_2, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is present at location s2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver3 gets on truck1 at location s0, package3 is placed in truck1 at location s0, truck1 is also loaded with package1 at location s0, then driver3 drives truck1 from location s0 to location s3, at location s3, package1 is removed from truck1, driver3 then drives truck1 from location s3 to location s1, at location s1, driver3 gets off truck1, package3 is also removed from truck1 at location s1, meanwhile, at location s2, package2 is loaded into truck2 and driver1 walks from location s3 to p3_0 to reach the current state. In this state, is it True or False that truck1 is not at location s1?", "initial_state_nl_paraphrased": "Driver1 is situated at location s3, while driver2 is also present at location s3. Meanwhile, driver3 is located at s0. A path exists between locations p0_1 and s0, as well as between p1_3 and s3, p2_0 and s2, and p3_0 and s0. Additionally, a path is present between s0 and p3_0, s3 and p1_3, and a link connects s3 and s0. Package1 is currently at location s0, package2 is at location s2, and package3 is also at location s0. Furthermore, a link exists between locations s0 and s3, s2 and s0, and s2 and s1. A path is also present between p1_2 and s2, p1_3 and s1, p2_0 and s0, s0 and p0_1, s2 and p1_2, and s3 and p3_0. Moreover, links connect locations s0 and s2, s1 and s2, s1 and s3, s2 and s3, s3 and s1, and s3 and s2. Paths also exist between p0_1 and s1, p1_2 and s1, p3_0 and s3, s0 and p2_0, s1 and p0_1, s1 and p1_2, s1 and p1_3, and s2 and p2_0. Truck1 is currently at location s0 and is empty, while truck2 is present at location s2 and contains nothing."}
{"question_id": "79f8897d-d942-4312-aef9-21ac0b2fe034", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_21", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location s0, driver3 boards truck1, at location s0, package3 is loaded in truck1, truck1 is loaded with package1 at location s0, truck1 is driven from location s0 to s3 by driver3, at location s3, package1 is unloaded in truck1, driver3 drives truck1 to location s1 from location s3, driver3 disembarks from truck1 at location s1, package3 is unloaded from truck1 at location s1, package2 is loaded in truck2 at location s2 and driver1 walks from location s3 to location p3_0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: locations p0_1 and p2_0 does not have a link between them, locations p0_1 and p3_0 does not have a path between them, locations p0_1 and s1 does not have a link between them, locations p0_1 and s3 does not have a link between them, locations p1_0 and p1_2 does not have a link between them, locations p1_0 and p1_3 does not have a link between them, locations p1_0 and p3_0 does not have a path between them, locations p1_0 and s2 does not have a path between them, locations p1_2 and p2_0 does not have a link between them, locations p1_2 and p3_0 does not have a link between them, locations p1_2 and s0 does not have a link between them, locations p1_2 and s0 does not have a path between them, locations p1_2 and s3 does not have a link between them, locations p1_3 and p2_1 does not have a path between them, locations p1_3 and p3_0 does not have a link between them, locations p1_3 and p3_0 does not have a path between them, locations p1_3 and s2 does not have a link between them, locations p1_3 and s2 does not have a path between them, locations p2_0 and p1_3 does not have a link between them, locations p2_0 and p3_0 does not have a path between them, locations p2_0 and s3 does not have a link between them, locations p2_1 and p1_0 does not have a link between them, locations p2_1 and p2_0 does not have a link between them, locations p2_1 and p3_0 does not have a path between them, locations p2_1 and s0 does not have a link between them, locations p2_1 and s3 does not have a link between them, locations p3_0 and p0_1 does not have a link between them, locations p3_0 and p1_2 does not have a path between them, locations p3_0 and p1_3 does not have a path between them, locations p3_0 and p2_0 does not have a path between them, locations p3_0 and s1 does not have a path between them, locations p3_0 and s2 does not have a link between them, locations s0 and p0_1 does not have a link between them, locations s0 and p1_2 does not have a link between them, locations s0 and p1_3 does not have a link between them, locations s0 and p1_3 does not have a path between them, locations s0 and p2_0 does not have a link between them, locations s0 and s1 does not have a path between them, locations s1 and p1_0 does not have a link between them, locations s1 and p2_1 does not have a link between them, locations s1 and p2_1 does not have a path between them, locations s1 and p3_0 does not have a link between them, locations s2 and p0_1 does not have a path between them, locations s2 and p1_0 does not have a path between them, locations s2 and p1_2 does not have a link between them, locations s2 and p1_3 does not have a link between them, locations s3 and p0_1 does not have a link between them, locations s3 and p1_0 does not have a path between them, locations s3 and p1_2 does not have a path between them, locations s3 and p2_0 does not have a link between them, locations s3 and p2_0 does not have a path between them, locations s3 and p2_1 does not have a link between them, locations s3 and s1 does not have a path between them, there doesn't exist a link between the locations p0_1 and p1_0, there doesn't exist a link between the locations p0_1 and p1_3, there doesn't exist a link between the locations p0_1 and p3_0, there doesn't exist a link between the locations p0_1 and s0, there doesn't exist a link between the locations p1_0 and p0_1, there doesn't exist a link between the locations p1_0 and p2_0, there doesn't exist a link between the locations p1_0 and p2_1, there doesn't exist a link between the locations p1_0 and p3_0, there doesn't exist a link between the locations p1_0 and s2, there doesn't exist a link between the locations p1_2 and p0_1, there doesn't exist a link between the locations p1_2 and s1, there doesn't exist a link between the locations p1_2 and s2, there doesn't exist a link between the locations p1_3 and s3, there doesn't exist a link between the locations p2_0 and p0_1, there doesn't exist a link between the locations p2_0 and p1_0, there doesn't exist a link between the locations p2_0 and p2_1, there doesn't exist a link between the locations p2_0 and s2, there doesn't exist a link between the locations p2_1 and p0_1, there doesn't exist a link between the locations p2_1 and p1_2, there doesn't exist a link between the locations p2_1 and p1_3, there doesn't exist a link between the locations p3_0 and p1_3, there doesn't exist a link between the locations s0 and p2_1, there doesn't exist a link between the locations s0 and p3_0, there doesn't exist a link between the locations s0 and s1, there doesn't exist a link between the locations s1 and p0_1, there doesn't exist a link between the locations s2 and p1_0, there doesn't exist a link between the locations s2 and p3_0, there doesn't exist a link between the locations s3 and p1_2, there doesn't exist a link between the locations s3 and p3_0, there doesn't exist a path between the locations p0_1 and p1_3, there doesn't exist a path between the locations p0_1 and p2_0, there doesn't exist a path between the locations p0_1 and p2_1, there doesn't exist a path between the locations p1_0 and p1_3, there doesn't exist a path between the locations p1_0 and s0, there doesn't exist a path between the locations p1_2 and p0_1, there doesn't exist a path between the locations p1_2 and p1_0, there doesn't exist a path between the locations p1_2 and p1_3, there doesn't exist a path between the locations p1_2 and p3_0, there doesn't exist a path between the locations p1_2 and s3, there doesn't exist a path between the locations p1_3 and p0_1, there doesn't exist a path between the locations p1_3 and p1_2, there doesn't exist a path between the locations p1_3 and s0, there doesn't exist a path between the locations p2_0 and p1_0, there doesn't exist a path between the locations p2_0 and p1_2, there doesn't exist a path between the locations p2_0 and p1_3, there doesn't exist a path between the locations p2_0 and p2_1, there doesn't exist a path between the locations p2_0 and s3, there doesn't exist a path between the locations p2_1 and p0_1, there doesn't exist a path between the locations p2_1 and p1_2, there doesn't exist a path between the locations p2_1 and p1_3, there doesn't exist a path between the locations p2_1 and p2_0, there doesn't exist a path between the locations p2_1 and s0, there doesn't exist a path between the locations p2_1 and s1, there doesn't exist a path between the locations p2_1 and s3, there doesn't exist a path between the locations s0 and p1_0, there doesn't exist a path between the locations s1 and p2_0, there doesn't exist a path between the locations s1 and p3_0, there doesn't exist a path between the locations s1 and s3, there doesn't exist a path between the locations s2 and p3_0, there doesn't exist a path between the locations s2 and s3, there doesn't exist a path between the locations s3 and p0_1, there doesn't exist a path between the locations s3 and s0, there doesn't exist a path between the locations s3 and s2, there is no link between location p0_1 and location p1_2, there is no link between location p0_1 and location p2_1, there is no link between location p0_1 and location s2, there is no link between location p1_0 and location s0, there is no link between location p1_0 and location s1, there is no link between location p1_0 and location s3, there is no link between location p1_2 and location p1_0, there is no link between location p1_2 and location p1_3, there is no link between location p1_2 and location p2_1, there is no link between location p1_3 and location p0_1, there is no link between location p1_3 and location p1_0, there is no link between location p1_3 and location p1_2, there is no link between location p1_3 and location p2_0, there is no link between location p1_3 and location p2_1, there is no link between location p1_3 and location s0, there is no link between location p1_3 and location s1, there is no link between location p2_0 and location p1_2, there is no link between location p2_0 and location p3_0, there is no link between location p2_0 and location s0, there is no link between location p2_0 and location s1, there is no link between location p2_1 and location p3_0, there is no link between location p2_1 and location s1, there is no link between location p2_1 and location s2, there is no link between location p3_0 and location p1_0, there is no link between location p3_0 and location p1_2, there is no link between location p3_0 and location p2_0, there is no link between location p3_0 and location p2_1, there is no link between location p3_0 and location s0, there is no link between location p3_0 and location s1, there is no link between location p3_0 and location s3, there is no link between location s0 and location p1_0, there is no link between location s1 and location p1_2, there is no link between location s1 and location p1_3, there is no link between location s1 and location p2_0, there is no link between location s1 and location s0, there is no link between location s2 and location p0_1, there is no link between location s2 and location p2_0, there is no link between location s2 and location p2_1, there is no link between location s3 and location p1_0, there is no link between location s3 and location p1_3, there is no path between location p0_1 and location p1_0, there is no path between location p0_1 and location p1_2, there is no path between location p0_1 and location s2, there is no path between location p0_1 and location s3, there is no path between location p1_0 and location p0_1, there is no path between location p1_0 and location p1_2, there is no path between location p1_0 and location p2_0, there is no path between location p1_0 and location p2_1, there is no path between location p1_0 and location s1, there is no path between location p1_0 and location s3, there is no path between location p1_2 and location p2_0, there is no path between location p1_2 and location p2_1, there is no path between location p1_3 and location p1_0, there is no path between location p1_3 and location p2_0, there is no path between location p2_0 and location p0_1, there is no path between location p2_0 and location s1, there is no path between location p2_1 and location p1_0, there is no path between location p2_1 and location s2, there is no path between location p3_0 and location p0_1, there is no path between location p3_0 and location p1_0, there is no path between location p3_0 and location p2_1, there is no path between location p3_0 and location s2, there is no path between location s0 and location p1_2, there is no path between location s0 and location p2_1, there is no path between location s0 and location s2, there is no path between location s0 and location s3, there is no path between location s1 and location p1_0, there is no path between location s1 and location s0, there is no path between location s1 and location s2, there is no path between location s2 and location p1_3, there is no path between location s2 and location p2_1, there is no path between location s2 and location s0, there is no path between location s2 and location s1 and there is no path between location s3 and location p2_1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s3, driver2 is present at location s3, driver3 is at location s0, locations p0_1 and s0 have a path between them, locations p1_3 and s3 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and p3_0 have a path between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is currently at location s0, package2 is currently at location s2, package3 is currently at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s2 and p1_2, there exists a path between the locations s3 and p3_0, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s1, there is a path between location p3_0 and location s3, there is a path between location s0 and location p2_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_2, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is present at location s2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: at location s0, driver3 boards truck1, at location s0, package3 is loaded in truck1, truck1 is loaded with package1 at location s0, truck1 is driven from location s0 to s3 by driver3, at location s3, package1 is unloaded in truck1, driver3 drives truck1 to location s1 from location s3, driver3 disembarks from truck1 at location s1, package3 is unloaded from truck1 at location s1, package2 is loaded in truck2 at location s2 and driver1 walks from location s3 to location p3_0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: locations p0_1 and p2_0 are not connected by a link, locations p0_1 and p3_0 are not connected by a path, locations p0_1 and s1 are not connected by a link, locations p0_1 and s3 are not connected by a link, locations p1_0 and p1_2 are not connected by a link, locations p1_0 and p1_3 are not connected by a link, locations p1_0 and p3_0 are not connected by a path, locations p1_0 and s2 are not connected by a path, locations p1_2 and p2_0 are not connected by a link, locations p1_2 and p3_0 are not connected by a link, locations p1_2 and s0 are not connected by a link, locations p1_2 and s0 are not connected by a path, locations p1_2 and s3 are not connected by a link, locations p1_3 and p2_1 are not connected by a path, locations p1_3 and p3_0 are not connected by a link, locations p1_3 and p3_0 are not connected by a path, locations p1_3 and s2 are not connected by a link, locations p1_3 and s2 are not connected by a path, locations p2_0 and p1_3 are not connected by a link, locations p2_0 and p3_0 are not connected by a path, locations p2_0 and s3 are not connected by a link, locations p2_1 and p1_0 are not connected by a link, locations p2_1 and p2_0 are not connected by a link, locations p2_1 and p3_0 are not connected by a path, locations p2_1 and s0 are not connected by a link, locations p2_1 and s3 are not connected by a link, locations p3_0 and p0_1 are not connected by a link, locations p3_0 and p1_2 are not connected by a path, locations p3_0 and p1_3 are not connected by a path, locations p3_0 and p2_0 are not connected by a path, locations p3_0 and s1 are not connected by a path, locations p3_0 and s2 are not connected by a link, locations s0 and p0_1 are not connected by a link, locations s0 and p1_2 are not connected by a link, locations s0 and p1_3 are not connected by a link, locations s0 and p1_3 are not connected by a path, locations s0 and p2_0 are not connected by a link, locations s0 and s1 are not connected by a path, locations s1 and p1_0 are not connected by a link, locations s1 and p2_1 are not connected by a link, locations s1 and p2_1 are not connected by a path, locations s1 and p3_0 are not connected by a link, locations s2 and p0_1 are not connected by a path, locations s2 and p1_0 are not connected by a path, locations s2 and p1_2 are not connected by a link, locations s2 and p1_3 are not connected by a link, locations s3 and p0_1 are not connected by a link, locations s3 and p1_0 are not connected by a path, locations s3 and p1_2 are not connected by a path, locations s3 and p2_0 are not connected by a link, locations s3 and p2_0 are not connected by a path, locations s3 and p2_1 are not connected by a link, locations s3 and s1 are not connected by a path, there is no connection between locations p0_1 and p1_0, there is no connection between locations p0_1 and p1_3, there is no connection between locations p0_1 and p3_0, there is no connection between locations p0_1 and s0, there is no connection between locations p1_0 and p0_1, there is no connection between locations p1_0 and p2_0, there is no connection between locations p1_0 and p2_1, there is no connection between locations p1_0 and p3_0, there is no connection between locations p1_0 and s2, there is no connection between locations p1_2 and p0_1, there is no connection between locations p1_2 and s1, there is no connection between locations p1_2 and s2, there is no connection between locations p1_3 and s3, there is no connection between locations p2_0 and p0_1, there is no connection between locations p2_0 and p1_0, there is no connection between locations p2_0 and p2_1, there is no connection between locations p2_0 and s2, there is no connection between locations p2_1 and p0_1, there is no connection between locations p2_1 and p1_2, there is no connection between locations p2_1 and p1_3, there is no connection between locations p3_0 and p1_3, there is no connection between locations s0 and p2_1, there is no connection between locations s0 and p3_0, there is no connection between locations s0 and s1, there is no connection between locations s1 and p0_1, there is no connection between locations s2 and p1_0, there is no connection between locations s2 and p3_0, there is no connection between locations s3 and p1_2, there is no connection between locations s3 and p3_0, there is no path between locations p0_1 and p1_3, there is no path between locations p0_1 and p2_0, there is no path between locations p0_1 and p2_1, there is no path between locations p1_0 and p1_3, there is no path between locations p1_0 and s0, there is no path between locations p1_2 and p0_1, there is no path between locations p1_2 and p1_0, there is no path between locations p1_2 and p1_3, there is no path between locations p1_2 and p3_0, there is no path between locations p1_2 and s3, there is no path between locations p1_3 and p0_1, there is no path between locations p1_3 and p1_2, there is no path between locations p1_3 and s0, there is no path between locations p2_0 and p1_0, there is no path between locations p2_0 and p1_2, there is no path between locations p2_0 and p1_3, there is no path between locations p2_0 and p2_1, there is no path between locations p2_0 and s3, there is no path between locations p2_1 and p0_1, there is no path between locations p2_1 and p1_2, there is no path between locations p2_1 and p1_3, there is no path between locations p2_1 and p2_0, there is no path between locations p2_1 and s0, there is no path between locations p2_1 and s1, there is no path between locations p2_1 and s3, there is no path between locations s0 and p1_0, there is no path between locations s1 and p2_0, there is no path between locations s1 and p3_0, there is no path between locations s1 and s3, there is no path between locations s2 and p3_0, there is no path between locations s2 and s3, there is no path between locations s3 and p0_1, there is no path between locations s3 and s0, there is no path between locations s3 and s2, there is no connection between location p0_1 and location p1_2, there is no connection between location p0_1 and location p2_1, there is no connection between location p0_1 and location s2, there is no connection between location p1_0 and location s0, there is no connection between location p1_0 and location s1, there is no connection between location p1_0 and location s3, there is no connection between location p1_2 and location p1_0, there is no connection between location p1_2 and location p1_3, there is no connection between location p1_2 and location p2_1, there is no connection between location p1_3 and location p0_1, there is no connection between location p1_3 and location p1_0, there is no connection between location p1_3 and location p1_2, there is no connection between location p1_3 and location p2_0, there is no connection between location p1_3 and location p2_1, there is no connection between location p1_3 and location s0, there is no connection between location p1_3 and location s1, there is no connection between location p2_0 and location p1_2, there is no connection between location p2_0 and location p3_0, there is no connection between location p2_0 and location s0, there is no connection between location p2_0 and location s1, there is no connection between location p2_1 and location p3_0, there is no connection between location p2_1 and location s1, there is no connection between location p2_1 and location s2, there is no connection between location p3_0 and location p1_0, there is no connection between location p3_0 and location p1_2, there is no connection between location p3_0 and location p2_0, there is no connection between location p3_0 and location p2_1, there is no connection between location p3_0 and location s0, there is no connection between location p3_0 and location s1, there is no connection between location p3_0 and location s3, there is no connection between location s0 and location p1_0, there is no connection between location s1 and location p1_2, there is no connection between location s1 and location p1_3, there is no connection between location s1 and location p2_0, there is no connection between location s1 and location s0, there is no connection between location s2 and location p0_1, there is no connection between location s2 and location p2_0, there is no connection between location s2 and location p2_1, there is no connection between location s3 and location p1_0, there is no connection between location s3 and location p1_3, there is no path between location p0_1 and location p1_0, there is no path between location p0_1 and location p1_2, there is no path between location p0_1 and location s2, there is no path between location p0_1 and location s3, there is no path between location p1_0 and location p0_1, there is no path between location p1_0 and location p1_2, there is no path between location p1_0 and location p2_0, there is no path between location p1_0 and location p2_1, there is no path between location p1_0 and location s1, there is no path between location p1_0 and location s3, there is no path between location p1_2 and location p2_0, there is no path between location p1_2 and location p2_1, there is no path between location p1_3 and location p1_0, there is no path between location p1_3 and location p2_0, there is no path between location p2_0 and location p0_1, there is no path between location p2_0 and location s1, there is no path between location p2_1 and location p1_0, there is no path between location p2_1 and location s2, there is no path between location p3_0 and location p0_1, there is no path between location p3_0 and location p1_0, there is no path between location p3_0 and location p2_1, there is no path between location p3_0 and location s2, there is no path between location s0 and location p1_2, there is no path between location s0 and location p2_1, there is no path between location s0 and location s2, there is no path between location s0 and location s3, there is no path between location s1 and location p1_0, there is no path between location s1 and location s0, there is no path between location s1 and location s2, there is no path between location s2 and location p1_3, there is no path between location s2 and location p2_1, there is no path between location s2 and location s0, there is no path between location s2 and location s1 and there is no path between location s3 and location p2_1?", "initial_state_nl_paraphrased": "Driver1 is situated at location s3, while driver2 is also present at location s3. Meanwhile, driver3 is located at s0. A path exists between locations p0_1 and s0, as well as between p1_3 and s3, p2_0 and s2, and p3_0 and s0. Additionally, a path is present between s0 and p3_0, s3 and p1_3, and a link connects s3 and s0. Package1 is currently at location s0, package2 is at location s2, and package3 is also at location s0. Furthermore, a link exists between locations s0 and s3, s2 and s0, and s2 and s1. A path is also present between p1_2 and s2, p1_3 and s1, p2_0 and s0, s0 and p0_1, s2 and p1_2, and s3 and p3_0. Moreover, links exist between locations s0 and s2, s1 and s2, s1 and s3, s2 and s3, s3 and s1, and s3 and s2. Paths are also present between p0_1 and s1, p1_2 and s1, p3_0 and s3, s0 and p2_0, s1 and p0_1, s1 and p1_2, s1 and p1_3, and s2 and p2_0. Truck1 is located at s0 and is empty, while truck2 is at location s2 and contains nothing."}
{"question_id": "52761d3c-6fc0-4057-8c34-d4a9656426f0", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_10", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks to location p4_3 from location s3, driver1 walks from location p4_3 to location s4, driver1 walks from location s4 to p4_1, driver1 walks from location p4_1 to s1, truck1 is boarded by driver1 at location s1, truck1 is driven from location s1 to s0 by driver1, truck1 is loaded with package4 at location s0, driver1 drives truck1 from location s0 to location s2, package2 is loaded in truck1 at location s2 and truck1 is loaded with package1 at location s2 to reach the current state. In this state, is it True or False that driver3 is at location p0_5?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s3, driver2 is at location s4, driver3 is at location s3, locations p0_5 and s0 have a path between them, locations p0_5 and s5 have a path between them, locations p4_0 and s0 have a path between them, locations p4_1 and s4 have a path between them, locations p4_3 and s3 have a path between them, locations p4_3 and s4 have a path between them, locations s0 and s2 have a link between them, locations s0 and s5 have a link between them, locations s1 and s2 have a link between them, locations s1 and s4 have a link between them, locations s2 and p5_2 have a path between them, locations s2 and s0 have a link between them, locations s2 and s5 have a link between them, locations s3 and s2 have a link between them, locations s3 and s5 have a link between them, locations s4 and s1 have a link between them, locations s4 and s5 have a link between them, locations s5 and p0_5 have a path between them, locations s5 and s3 have a link between them, package1 is present at location s2, package2 is at location s2, package3 is at location s3, package4 is present at location s0, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s0, there exists a link between the locations s5 and s0, there exists a link between the locations s5 and s2, there exists a path between the locations p4_0 and s4, there exists a path between the locations s1 and p4_1, there exists a path between the locations s4 and p4_3, there is a link between location s0 and location s4, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s4 and location s3, there is a link between location s5 and location s4, there is a path between location p4_1 and location s1, there is a path between location p5_2 and location s2, there is a path between location p5_2 and location s5, there is a path between location s0 and location p0_5, there is a path between location s0 and location p4_0, there is a path between location s3 and location p4_3, there is a path between location s4 and location p4_0, there is a path between location s4 and location p4_1, there is a path between location s5 and location p5_2, truck1 contains nothing, truck1 is at location s1, truck2 is currently at location s5 and truck2 is empty.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by s4 to p4_1, and then from p4_1 to s1. At location s1, driver1 boards truck1, and then drives it from s1 to s0. At s0, truck1 is loaded with package4. Driver1 then drives truck1 from s0 to s2, where truck1 is loaded with both package2 and package1, resulting in the current state. In this state, is it True or False that driver3 is at location p0_5?", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is at s4 and driver3 is also at s3. A path exists between locations p0_5 and s0, as well as between p0_5 and s5. Additionally, paths are present between p4_0 and s0, p4_1 and s4, p4_3 and s3, and p4_3 and s4. Furthermore, links exist between s0 and s2, s0 and s5, s1 and s2, s1 and s4, s2 and p5_2, s2 and s0, s2 and s5, s3 and s2, s3 and s5, s4 and s1, and s4 and s5. A path also exists between s5 and p0_5, and s5 and s3 have a link. Package1 and package2 are both located at s2, package3 is at s3, and package4 is at s0. Links are present between s0 and s1, s1 and s0, s3 and s4, s4 and s0, s5 and s0, and s5 and s2. Paths exist between p4_0 and s4, s1 and p4_1, s4 and p4_3, s0 and p0_5, s0 and p4_0, s3 and p4_3, s4 and p4_0, s4 and p4_1, and s5 and p5_2. Moreover, links are present between s0 and s4, s2 and s1, s2 and s3, s4 and s3, and s5 and s4. Lastly, truck1 is empty and located at s1, while truck2 is empty and at location s5."}
{"question_id": "5bf8bc36-0abb-460d-be48-0b2602ede7b2", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: package3 is loaded in truck1 at location s0 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: truck1 is empty and truck2 contains nothing?", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is at location s2, driver2 is present at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_2 and s2 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_3 have a path between them, locations s2 and p0_2 have a path between them, locations s2 and s1 have a link between them, locations s2 and s3 have a link between them, locations s3 and p0_3 have a path between them, locations s3 and s1 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is at location s0, package4 is at location s2, there exists a link between the locations s1 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p2_1, there is a link between location s0 and location s1, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s3 and location s0, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_3 and location s0, there is a path between location p0_3 and location s3, there is a path between location p1_3 and location s3, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: package3 is loaded into truck1 at location s0 to achieve the current state. In this state, are the following properties that do not involve negations True or False: truck1 is empty and truck2 is empty?", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, and p0_2 and s2. Similarly, paths are present between s0 and p0_1, s0 and p0_2, and s0 and p0_3. A link connects locations s0 and s2, and another link exists between s0 and s3. A path is present between s1 and p1_3, and between s2 and p0_2. Locations s2 and s1 are linked, as are s2 and s3, and s3 and s1. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link exists between s1 and s0, and a path is present between p1_3 and s1, s1 and p0_1, and s1 and p2_1. Additionally, links connect s0 and s1, s1 and s2, and s1 and s3, as well as s2 and s0, s3 and s0, and s3 and s2. Paths are present between p0_1 and s1, p0_3 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s1, and p2_1 and s2. Furthermore, a path exists between s2 and p2_1, and between s3 and p1_3. Truck1 is located at s0 and is empty, while truck2 is also at s0 and contains nothing."}
