{"question_id": "6c2295e0-476b-461c-9edd-1934a432dfbe", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks to location p4_3 from location s3, driver1 walks from location p4_3 to location s4, driver1 walks from location s4 to p4_1, driver1 walks from location p4_1 to location s1, at location s1, driver1 boards truck1, driver1 drives truck1 to location s0 from location s1, package4 is loaded in truck1 at location s0, truck1 is driven from location s0 to s2 by driver1, truck1 is loaded with package2 at location s2 and truck1 is loaded with package1 at location s2 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "driver1 is not at location p0_5, driver1 is not at location p4_0, driver1 is not at location p4_3, driver1 is not at location p5_2, driver1 is not at location s0, driver1 is not at location s2, driver1 is not at location s3, driver1 is not at location s5, driver1 is not currently at location p4_1, driver1 is not currently at location s4, driver1 is not driving truck2, driver1 is not present at location s1, driver2 is not at location p4_1, driver2 is not currently at location p0_5, driver2 is not currently at location p4_0, driver2 is not currently at location s0, driver2 is not currently at location s2, driver2 is not currently at location s5, driver2 is not driving truck1 currently, driver2 is not driving truck2, driver2 is not present at location p4_3, driver2 is not present at location p5_2, driver2 is not present at location s1, driver2 is not present at location s3, driver3 is not at location p4_1, driver3 is not at location p4_3, driver3 is not at location p5_2, driver3 is not at location s4, driver3 is not currently at location p0_5, driver3 is not currently at location p4_0, driver3 is not driving truck1, driver3 is not present at location s0, driver3 is not present at location s1, driver3 is not present at location s2, driver3 is not present at location s5, locations p0_5 and p4_0 does not have a path between them, locations p0_5 and p4_3 does not have a path between them, locations p0_5 and p5_2 does not have a path between them, locations p0_5 and s0 does not have a link between them, locations p0_5 and s2 does not have a path between them, locations p4_0 and p0_5 does not have a link between them, locations p4_0 and p0_5 does not have a path between them, locations p4_0 and p4_3 does not have a link between them, locations p4_0 and p5_2 does not have a link between them, locations p4_0 and s1 does not have a link between them, locations p4_0 and s4 does not have a link between them, locations p4_1 and p4_0 does not have a link between them, locations p4_1 and s3 does not have a link between them, locations p4_1 and s3 does not have a path between them, locations p4_1 and s5 does not have a link between them, locations p4_3 and p0_5 does not have a path between them, locations p4_3 and s1 does not have a path between them, locations p4_3 and s5 does not have a path between them, locations p5_2 and p0_5 does not have a link between them, locations p5_2 and p4_0 does not have a path between them, locations p5_2 and p4_1 does not have a path between them, locations p5_2 and s0 does not have a link between them, locations p5_2 and s4 does not have a path between them, locations s0 and p4_3 does not have a link between them, locations s0 and p5_2 does not have a link between them, locations s0 and s1 does not have a path between them, locations s0 and s3 does not have a path between them, locations s0 and s5 does not have a path between them, locations s1 and p0_5 does not have a link between them, locations s1 and p4_1 does not have a link between them, locations s1 and p4_3 does not have a link between them, locations s1 and s3 does not have a link between them, locations s1 and s3 does not have a path between them, locations s1 and s5 does not have a path between them, locations s2 and p4_0 does not have a link between them, locations s2 and p4_3 does not have a link between them, locations s2 and p4_3 does not have a path between them, locations s2 and s0 does not have a path between them, locations s2 and s3 does not have a path between them, locations s2 and s5 does not have a path between them, locations s3 and p0_5 does not have a link between them, locations s3 and p0_5 does not have a path between them, locations s3 and p4_1 does not have a link between them, locations s3 and s0 does not have a link between them, locations s4 and s2 does not have a link between them, locations s4 and s3 does not have a path between them, locations s5 and p0_5 does not have a link between them, locations s5 and p4_0 does not have a link between them, locations s5 and p4_1 does not have a link between them, locations s5 and p4_3 does not have a link between them, locations s5 and s4 does not have a path between them, package1 is not at location p0_5, package1 is not at location p4_1, package1 is not at location p4_3, package1 is not at location p5_2, package1 is not currently at location p4_0, package1 is not currently at location s0, package1 is not currently at location s1, package1 is not currently at location s2, package1 is not currently at location s3, package1 is not currently at location s5, package1 is not located in truck2, package1 is not present at location s4, package2 is not at location p4_0, package2 is not at location p4_1, package2 is not at location p4_3, package2 is not at location p5_2, package2 is not at location s0, package2 is not at location s1, package2 is not at location s3, package2 is not currently at location p0_5, package2 is not currently at location s4, package2 is not currently at location s5, package2 is not in truck2, package2 is not present at location s2, package3 is not at location s1, package3 is not currently at location p4_0, package3 is not currently at location p4_1, package3 is not located in truck1, package3 is not placed in truck2, package3 is not present at location p0_5, package3 is not present at location p4_3, package3 is not present at location p5_2, package3 is not present at location s0, package3 is not present at location s2, package3 is not present at location s4, package3 is not present at location s5, package4 is not at location p0_5, package4 is not at location s1, package4 is not currently at location p4_1, package4 is not currently at location p4_3, package4 is not currently at location s0, package4 is not currently at location s3, package4 is not located in truck2, package4 is not present at location p4_0, package4 is not present at location p5_2, package4 is not present at location s2, package4 is not present at location s4, package4 is not present at location s5, there doesn't exist a link between the locations p0_5 and s1, there doesn't exist a link between the locations p0_5 and s3, there doesn't exist a link between the locations p0_5 and s5, there doesn't exist a link between the locations p4_0 and s3, there doesn't exist a link between the locations p4_1 and s0, there doesn't exist a link between the locations p4_1 and s1, there doesn't exist a link between the locations p4_3 and p0_5, there doesn't exist a link between the locations p4_3 and p4_1, there doesn't exist a link between the locations p4_3 and s0, there doesn't exist a link between the locations p4_3 and s1, there doesn't exist a link between the locations p4_3 and s2, there doesn't exist a link between the locations p4_3 and s3, there doesn't exist a link between the locations p4_3 and s5, there doesn't exist a link between the locations p5_2 and s4, there doesn't exist a link between the locations p5_2 and s5, there doesn't exist a link between the locations s0 and p0_5, there doesn't exist a link between the locations s0 and p4_1, there doesn't exist a link between the locations s1 and s5, there doesn't exist a link between the locations s2 and p0_5, there doesn't exist a link between the locations s2 and p5_2, there doesn't exist a link between the locations s3 and p4_3, there doesn't exist a link between the locations s3 and p5_2, there doesn't exist a link between the locations s4 and p0_5, there doesn't exist a link between the locations s4 and p4_0, there doesn't exist a link between the locations s4 and p5_2, there doesn't exist a link between the locations s5 and s1, there doesn't exist a path between the locations p0_5 and s3, there doesn't exist a path between the locations p0_5 and s4, there doesn't exist a path between the locations p4_0 and p4_3, there doesn't exist a path between the locations p4_0 and p5_2, there doesn't exist a path between the locations p4_0 and s3, there doesn't exist a path between the locations p4_0 and s5, there doesn't exist a path between the locations p4_1 and s0, there doesn't exist a path between the locations p4_1 and s2, there doesn't exist a path between the locations p4_1 and s5, there doesn't exist a path between the locations p5_2 and p0_5, there doesn't exist a path between the locations p5_2 and p4_3, there doesn't exist a path between the locations s0 and p4_3, there doesn't exist a path between the locations s0 and p5_2, there doesn't exist a path between the locations s0 and s4, there doesn't exist a path between the locations s1 and p4_3, there doesn't exist a path between the locations s1 and s2, there doesn't exist a path between the locations s1 and s4, there doesn't exist a path between the locations s2 and p0_5, there doesn't exist a path between the locations s2 and p4_1, there doesn't exist a path between the locations s3 and p4_0, there doesn't exist a path between the locations s3 and p4_1, there doesn't exist a path between the locations s3 and p5_2, there doesn't exist a path between the locations s3 and s2, there doesn't exist a path between the locations s3 and s4, there doesn't exist a path between the locations s3 and s5, there doesn't exist a path between the locations s4 and s2, there doesn't exist a path between the locations s4 and s5, there doesn't exist a path between the locations s5 and p4_1, there doesn't exist a path between the locations s5 and p4_3, there doesn't exist a path between the locations s5 and s1, there is no link between location p0_5 and location p4_0, there is no link between location p0_5 and location p4_1, there is no link between location p0_5 and location p4_3, there is no link between location p0_5 and location p5_2, there is no link between location p0_5 and location s2, there is no link between location p0_5 and location s4, there is no link between location p4_0 and location p4_1, there is no link between location p4_0 and location s0, there is no link between location p4_0 and location s2, there is no link between location p4_0 and location s5, there is no link between location p4_1 and location p0_5, there is no link between location p4_1 and location p4_3, there is no link between location p4_1 and location p5_2, there is no link between location p4_1 and location s2, there is no link between location p4_1 and location s4, there is no link between location p4_3 and location p4_0, there is no link between location p4_3 and location p5_2, there is no link between location p4_3 and location s4, there is no link between location p5_2 and location p4_0, there is no link between location p5_2 and location p4_1, there is no link between location p5_2 and location p4_3, there is no link between location p5_2 and location s1, there is no link between location p5_2 and location s2, there is no link between location p5_2 and location s3, there is no link between location s0 and location p4_0, there is no link between location s0 and location s3, there is no link between location s1 and location p4_0, there is no link between location s1 and location p5_2, there is no link between location s2 and location p4_1, there is no link between location s2 and location s4, there is no link between location s3 and location p4_0, there is no link between location s3 and location s1, there is no link between location s4 and location p4_1, there is no link between location s4 and location p4_3, there is no link between location s5 and location p5_2, there is no path between location p0_5 and location p4_1, there is no path between location p0_5 and location s1, there is no path between location p4_0 and location p4_1, there is no path between location p4_0 and location s1, there is no path between location p4_0 and location s2, there is no path between location p4_1 and location p0_5, there is no path between location p4_1 and location p4_0, there is no path between location p4_1 and location p4_3, there is no path between location p4_1 and location p5_2, there is no path between location p4_3 and location p4_0, there is no path between location p4_3 and location p4_1, there is no path between location p4_3 and location p5_2, there is no path between location p4_3 and location s0, there is no path between location p4_3 and location s2, there is no path between location p5_2 and location s0, there is no path between location p5_2 and location s1, there is no path between location p5_2 and location s3, there is no path between location s0 and location p4_1, there is no path between location s0 and location s2, there is no path between location s1 and location p0_5, there is no path between location s1 and location p4_0, there is no path between location s1 and location p5_2, there is no path between location s1 and location s0, there is no path between location s2 and location p4_0, there is no path between location s2 and location s1, there is no path between location s2 and location s4, there is no path between location s3 and location s0, there is no path between location s3 and location s1, there is no path between location s4 and location p0_5, there is no path between location s4 and location p5_2, there is no path between location s4 and location s0, there is no path between location s4 and location s1, there is no path between location s5 and location p4_0, there is no path between location s5 and location s0, there is no path between location s5 and location s2, there is no path between location s5 and location s3, truck1 contains some package, truck1 is not at location p0_5, truck1 is not at location s0, truck1 is not at location s1, truck1 is not at location s3, truck1 is not at location s5, truck1 is not currently at location p4_3, truck1 is not present at location p4_0, truck1 is not present at location p4_1, truck1 is not present at location p5_2, truck1 is not present at location s4, truck2 is not at location p4_0, truck2 is not at location p4_3, truck2 is not at location s2, truck2 is not at location s3, truck2 is not being driven by driver3, truck2 is not currently at location p4_1, truck2 is not currently at location p5_2, truck2 is not currently at location s1, truck2 is not currently at location s4, truck2 is not present at location p0_5 and truck2 is not present at location s0", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s3, driver2 is present at location s4, driver3 is currently at location s3, locations p4_0 and s0 have a path between them, locations s0 and s1 have a link between them, locations s0 and s4 have a link between them, locations s1 and s0 have a link between them, locations s2 and s1 have a link between them, locations s2 and s3 have a link between them, locations s3 and p4_3 have a path between them, locations s3 and s5 have a link between them, locations s4 and s5 have a link between them, locations s5 and p0_5 have a path between them, locations s5 and s0 have a link between them, locations s5 and s4 have a link between them, package1 is currently at location s2, package2 is at location s2, package3 is at location s3, package4 is currently at location s0, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s5, there exists a link between the locations s3 and s2, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s1, there exists a link between the locations s4 and s3, there exists a link between the locations s5 and s3, there exists a path between the locations p0_5 and s5, there exists a path between the locations s0 and p4_0, there exists a path between the locations s1 and p4_1, there exists a path between the locations s4 and p4_0, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s2, there is a link between location s0 and location s5, there is a link between location s1 and location s4, there is a link between location s2 and location s0, there is a link between location s4 and location s0, there is a link between location s5 and location s2, there is a path between location p0_5 and location s0, there is a path between location p4_0 and location s4, there is a path between location p4_1 and location s1, there is a path between location p4_1 and location s4, there is a path between location p4_3 and location s3, there is a path between location p4_3 and location s4, there is a path between location p5_2 and location s2, there is a path between location p5_2 and location s5, there is a path between location s0 and location p0_5, there is a path between location s2 and location p5_2, there is a path between location s4 and location p4_1, there is a path between location s4 and location p4_3, truck1 is currently at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by s4 to p4_1, and p4_1 to s1. Upon reaching s1, driver1 boards truck1 and drives it from s1 to s0. At s0, package4 is loaded onto truck1. Driver1 then drives truck1 from s0 to s2, where it is loaded with package2 and package1, resulting in the current state. In this state, list all valid properties that involve negations. If none exist, state None.", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is at s4, and driver3 is also at s3. A path exists between locations p4_0 and s0, and links are present between s0 and s1, s0 and s4, s1 and s0, s1 and s2, s2 and s1, s2 and s3, s3 and s2, s3 and s5, s4 and s5, s5 and p0_5, s5 and s0, and s5 and s4. Package1 is at location s2, package2 is also at s2, package3 is at s3, and package4 is at s0. Links exist between s1 and s2, s2 and s5, s3 and s2, s3 and s4, s4 and s1, s4 and s3, and s5 and s3. Paths are present between p0_5 and s5, s0 and p4_0, s1 and p4_1, s4 and p4_0, and s5 and p5_2. Additionally, links are present between s0 and s2, s0 and s5, s1 and s4, s2 and s0, s4 and s0, and s5 and s2. Paths also exist between p0_5 and s0, p4_0 and s4, p4_1 and s1, p4_1 and s4, p4_3 and s3, p4_3 and s4, p5_2 and s2, p5_2 and s5, s0 and p0_5, s2 and p5_2, s4 and p4_1, and s4 and p4_3. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "1e3a3d8f-2a15-4ccb-aa25-1aeabf88ae09", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to p3_0, driver2 walks from location p3_0 to s0, at location s0, driver2 boards truck1, driver2 drives truck1 from location s0 to location s2, at location s2, package3 is loaded in truck1, package2 is loaded in truck1 at location s2, driver2 drives truck1 from location s2 to location s0, truck1 is unloaded with package2 at location s0, driver2 drives truck1 from location s0 to location s3, at location s3, package1 is loaded in truck1, truck1 is driven from location s3 to s1 by driver2, from truck1, driver2 disembarks at location s1, driver2 walks to location p0_1 from location s1, driver2 walks to location s0 from location p0_1, driver2 boards truck3 at location s0, truck3 is driven from location s0 to s2 by driver2, at location s1, package3 is unloaded in truck1, truck1 is unloaded with package1 at location s1 and driver3 walks from location s3 to location p3_0 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "driver1 is not at location p0_1, driver1 is not at location p1_0, driver1 is not currently at location p1_3, driver1 is not currently at location p2_0, driver1 is not currently at location p3_0, driver1 is not currently at location s2, driver1 is not driving truck1 currently, driver1 is not driving truck3 currently, driver1 is not present at location p1_2, driver1 is not present at location s0, driver1 is not present at location s3, driver1 is present at location s1, driver2 is driving truck3 currently, driver2 is not at location p1_0, driver2 is not at location p2_0, driver2 is not at location s1, driver2 is not currently at location p1_3, driver2 is not currently at location p3_0, driver2 is not driving truck1 currently, driver2 is not driving truck2 currently, driver2 is not present at location p0_1, driver2 is not present at location p1_2, driver2 is not present at location s0, driver2 is not present at location s2, driver2 is not present at location s3, driver3 is currently at location p3_0, driver3 is not at location p1_0, driver3 is not at location p1_2, driver3 is not at location s0, driver3 is not at location s1, driver3 is not at location s2, driver3 is not currently at location p0_1, driver3 is not currently at location s3, driver3 is not driving truck1, driver3 is not driving truck3, driver3 is not present at location p1_3, driver3 is not present at location p2_0, locations p0_1 and p1_0 does not have a link between them, locations p0_1 and p1_2 does not have a link between them, locations p0_1 and p1_2 does not have a path between them, locations p0_1 and s3 does not have a link between them, locations p1_0 and p1_2 does not have a link between them, locations p1_0 and p1_2 does not have a path between them, locations p1_0 and p1_3 does not have a path between them, locations p1_0 and p2_0 does not have a path between them, locations p1_0 and s1 does not have a path between them, locations p1_0 and s2 does not have a path between them, locations p1_0 and s3 does not have a path between them, locations p1_2 and s0 does not have a path between them, locations p1_2 and s1 does not have a link between them, locations p1_2 and s3 does not have a link between them, locations p1_3 and s1 have a path between them, locations p1_3 and s2 does not have a link between them, locations p1_3 and s3 have a path between them, locations p2_0 and p1_0 does not have a link between them, locations p2_0 and p1_2 does not have a link between them, locations p2_0 and p1_3 does not have a link between them, locations p2_0 and p3_0 does not have a link between them, locations p2_0 and s1 does not have a link between them, locations p2_0 and s1 does not have a path between them, locations p2_0 and s2 does not have a link between them, locations p2_0 and s2 have a path between them, locations p3_0 and p0_1 does not have a link between them, locations p3_0 and s0 have a path between them, locations p3_0 and s1 does not have a link between them, locations p3_0 and s3 does not have a link between them, locations s0 and p1_3 does not have a link between them, locations s0 and p2_0 have a path between them, locations s0 and p3_0 have a path between them, locations s0 and s1 does not have a path between them, locations s0 and s1 have a link between them, locations s0 and s2 have a link between them, locations s0 and s3 does not have a path between them, locations s0 and s3 have a link between them, locations s1 and p1_0 does not have a link between them, locations s1 and p1_0 does not have a path between them, locations s1 and p2_0 does not have a link between them, locations s1 and p3_0 does not have a path between them, locations s1 and s0 have a link between them, locations s1 and s3 does not have a path between them, locations s1 and s3 have a link between them, locations s2 and p0_1 does not have a link between them, locations s2 and p1_0 does not have a link between them, locations s2 and p1_2 does not have a link between them, locations s2 and p1_3 does not have a link between them, locations s2 and p1_3 does not have a path between them, locations s2 and p2_0 does not have a link between them, locations s2 and p3_0 does not have a link between them, locations s2 and p3_0 does not have a path between them, locations s2 and s0 does not have a path between them, locations s2 and s1 have a link between them, locations s3 and p0_1 does not have a link between them, locations s3 and p0_1 does not have a path between them, locations s3 and p1_2 does not have a link between them, locations s3 and p2_0 does not have a path between them, locations s3 and s1 does not have a path between them, locations s3 and s2 does not have a link between them, package1 is currently at location s1, package1 is not at location p1_2, package1 is not at location p1_3, package1 is not currently at location p1_0, package1 is not currently at location p2_0, package1 is not located in truck1, package1 is not located in truck2, package1 is not placed in truck3, package1 is not present at location p0_1, package1 is not present at location p3_0, package1 is not present at location s0, package1 is not present at location s2, package1 is not present at location s3, package2 is at location s0, package2 is not at location p1_0, package2 is not at location p1_3, package2 is not at location p2_0, package2 is not at location p3_0, package2 is not currently at location p0_1, package2 is not currently at location p1_2, package2 is not currently at location s1, package2 is not currently at location s3, package2 is not in truck2, package2 is not placed in truck1, package2 is not placed in truck3, package2 is not present at location s2, package3 is not at location p1_3, package3 is not at location s3, package3 is not currently at location p1_0, package3 is not currently at location p2_0, package3 is not currently at location s0, package3 is not located in truck3, package3 is not placed in truck1, package3 is not placed in truck2, package3 is not present at location p0_1, package3 is not present at location p1_2, package3 is not present at location p3_0, package3 is not present at location s2, package3 is present at location s1, package4 is currently at location s1, package4 is not at location p1_3, package4 is not at location p2_0, package4 is not at location s2, package4 is not at location s3, package4 is not currently at location p0_1, package4 is not currently at location p1_2, package4 is not currently at location s0, package4 is not in truck1, package4 is not in truck2, package4 is not in truck3, package4 is not present at location p1_0, package4 is not present at location p3_0, there doesn't exist a link between the locations p0_1 and p1_3, there doesn't exist a link between the locations p0_1 and p3_0, there doesn't exist a link between the locations p0_1 and s0, there doesn't exist a link between the locations p0_1 and s2, there doesn't exist a link between the locations p1_0 and p0_1, there doesn't exist a link between the locations p1_0 and p2_0, there doesn't exist a link between the locations p1_0 and s0, there doesn't exist a link between the locations p1_0 and s1, there doesn't exist a link between the locations p1_0 and s2, there doesn't exist a link between the locations p1_2 and p0_1, there doesn't exist a link between the locations p1_2 and p1_3, there doesn't exist a link between the locations p1_2 and p2_0, there doesn't exist a link between the locations p1_2 and p3_0, there doesn't exist a link between the locations p1_2 and s0, there doesn't exist a link between the locations p1_3 and p0_1, there doesn't exist a link between the locations p1_3 and p2_0, there doesn't exist a link between the locations p1_3 and s3, there doesn't exist a link between the locations p3_0 and p1_0, there doesn't exist a link between the locations p3_0 and p2_0, there doesn't exist a link between the locations s0 and p0_1, there doesn't exist a link between the locations s0 and p1_0, there doesn't exist a link between the locations s0 and p2_0, there doesn't exist a link between the locations s1 and p1_2, there doesn't exist a link between the locations s2 and s3, there doesn't exist a link between the locations s3 and p2_0, there doesn't exist a link between the locations s3 and p3_0, there doesn't exist a path between the locations p0_1 and p1_0, there doesn't exist a path between the locations p0_1 and p1_3, there doesn't exist a path between the locations p0_1 and p2_0, there doesn't exist a path between the locations p0_1 and s3, there doesn't exist a path between the locations p1_0 and p0_1, there doesn't exist a path between the locations p1_0 and s0, there doesn't exist a path between the locations p1_2 and p1_0, there doesn't exist a path between the locations p1_2 and p3_0, there doesn't exist a path between the locations p1_2 and s3, there doesn't exist a path between the locations p1_3 and p2_0, there doesn't exist a path between the locations p1_3 and s0, there doesn't exist a path between the locations p2_0 and p0_1, there doesn't exist a path between the locations p2_0 and p1_2, there doesn't exist a path between the locations p3_0 and p0_1, there doesn't exist a path between the locations p3_0 and p1_0, there doesn't exist a path between the locations p3_0 and p2_0, there doesn't exist a path between the locations p3_0 and s1, there doesn't exist a path between the locations p3_0 and s2, there doesn't exist a path between the locations s0 and p1_0, there doesn't exist a path between the locations s0 and s2, there doesn't exist a path between the locations s1 and p2_0, there doesn't exist a path between the locations s1 and s0, there doesn't exist a path between the locations s2 and p1_0, there doesn't exist a path between the locations s2 and s1, there doesn't exist a path between the locations s3 and p1_2, there doesn't exist a path between the locations s3 and s0, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s2, there exists a path between the locations p2_0 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p1_2, there exists a path between the locations s3 and p3_0, there is a link between location s3 and location s0, there is a link between location s3 and location s1, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s1, there is a path between location p3_0 and location s3, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_2, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is no link between location p0_1 and location p2_0, there is no link between location p0_1 and location s1, there is no link between location p1_0 and location p1_3, there is no link between location p1_0 and location p3_0, there is no link between location p1_0 and location s3, there is no link between location p1_2 and location p1_0, there is no link between location p1_2 and location s2, there is no link between location p1_3 and location p1_0, there is no link between location p1_3 and location p1_2, there is no link between location p1_3 and location p3_0, there is no link between location p1_3 and location s0, there is no link between location p1_3 and location s1, there is no link between location p2_0 and location p0_1, there is no link between location p2_0 and location s0, there is no link between location p2_0 and location s3, there is no link between location p3_0 and location p1_2, there is no link between location p3_0 and location p1_3, there is no link between location p3_0 and location s0, there is no link between location p3_0 and location s2, there is no link between location s0 and location p1_2, there is no link between location s0 and location p3_0, there is no link between location s1 and location p0_1, there is no link between location s1 and location p1_3, there is no link between location s1 and location p3_0, there is no link between location s3 and location p1_0, there is no link between location s3 and location p1_3, there is no path between location p0_1 and location p3_0, there is no path between location p0_1 and location s2, there is no path between location p1_0 and location p3_0, there is no path between location p1_2 and location p0_1, there is no path between location p1_2 and location p1_3, there is no path between location p1_2 and location p2_0, there is no path between location p1_3 and location p0_1, there is no path between location p1_3 and location p1_0, there is no path between location p1_3 and location p1_2, there is no path between location p1_3 and location p3_0, there is no path between location p1_3 and location s2, there is no path between location p2_0 and location p1_0, there is no path between location p2_0 and location p1_3, there is no path between location p2_0 and location p3_0, there is no path between location p2_0 and location s3, there is no path between location p3_0 and location p1_2, there is no path between location p3_0 and location p1_3, there is no path between location s0 and location p1_2, there is no path between location s0 and location p1_3, there is no path between location s1 and location s2, there is no path between location s2 and location p0_1, there is no path between location s2 and location s3, there is no path between location s3 and location p1_0, there is no path between location s3 and location s2, truck1 is currently at location s1, truck1 is empty, truck1 is not at location p0_1, truck1 is not at location p1_3, truck1 is not at location p2_0, truck1 is not at location s0, truck1 is not at location s2, truck1 is not currently at location p1_0, truck1 is not currently at location p3_0, truck1 is not present at location p1_2, truck1 is not present at location s3, truck2 is at location s3, truck2 is empty, truck2 is not being driven by driver1, truck2 is not being driven by driver3, truck2 is not currently at location p0_1, truck2 is not currently at location p1_0, truck2 is not currently at location p1_3, truck2 is not currently at location p3_0, truck2 is not present at location p1_2, truck2 is not present at location p2_0, truck2 is not present at location s0, truck2 is not present at location s1, truck2 is not present at location s2, truck3 contains some package, truck3 is not at location s3, truck3 is not currently at location p0_1, truck3 is not currently at location p1_3, truck3 is not currently at location p2_0, truck3 is not currently at location s0, truck3 is not present at location p1_0, truck3 is not present at location p1_2, truck3 is not present at location p3_0, truck3 is not present at location s1 and truck3 is present at location s2", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s1, driver2 is present at location s3, driver3 is at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations p2_0 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p2_0 have a path between them, locations s0 and s2 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and s3 have a link between them, locations s2 and p1_2 have a path between them, locations s2 and s1 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is present at location s3, package2 is present at location s2, package3 is at location s2, package4 is present at location s1, there exists a link between the locations s0 and s3, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p2_0, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p3_0 and location s0, there is a path between location s0 and location p0_1, there is a path between location s1 and location p0_1, truck1 contains nothing, truck1 is present at location s0, truck2 contains nothing, truck2 is at location s3, truck3 is at location s0 and truck3 is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: driver2 moves from location s3 to p3_0, then from p3_0 to s0, where driver2 gets into truck1. Driver2 then drives truck1 from s0 to s2, where package3 and package2 are loaded into truck1. Next, driver2 drives truck1 from s2 back to s0, unloading package2 at s0. Driver2 then drives truck1 from s0 to s3, loads package1 into truck1 at s3, and drives truck1 from s3 to s1. At s1, driver2 gets out of truck1, walks to p0_1, and then to s0, where driver2 boards truck3. Driver2 drives truck3 from s0 to s2. Meanwhile, package3 is unloaded from truck1 at s1, and package1 is also unloaded from truck1 at s1. Additionally, driver3 moves from s3 to p3_0 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is currently located at s1, while driver2 and driver3 are both at location s3. There are paths connecting p0_1 to s1, p1_2 to both s1 and s2, p1_3 to s3, p2_0 to both s0 and s2, and p3_0 to s3. Additionally, paths exist between s0 and p2_0, s1 and both p1_2 and p1_3, s2 and p1_2, s3 and p3_0, and s0 and p3_0. Furthermore, links are present between s0 and s2, s1 and s3, s2 and s1, s3 and s0, s0 and s1, and s1 and s0. Package1 is located at s3, package2 and package3 are at s2, and package4 is at s1. Direct links exist between s0 and s3, s1 and s2, s2 and s0, s0 and s1, and s1 and s0. Paths also exist between p0_1 and s0, p1_3 and s1, p3_0 and s0, s0 and p0_1, and s1 and p0_1. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and at s0."}
{"question_id": "3ed467f1-2744-4a65-b99f-ed4931b8071d", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0, driver1 walks to location p0_2 from location s2, driver1 walks to location s0 from location p0_2, at location s0, driver1 boards truck1, driver1 drives truck1 from location s0 to location s3, from truck1, driver1 disembarks at location s3, at location s3, package3 is unloaded in truck1, driver1 walks to location p0_3 from location s3, driver1 walks from location p0_3 to location s0, driver2 walks from location s2 to p0_2, driver2 walks from location p0_2 to s0, driver2 boards truck2 at location s0, driver2 drives truck2 to location s1 from location s0, truck2 is loaded with package1 at location s1, driver2 drives truck2 from location s1 to location s2, package2 is loaded in truck2 at location s2, at location s2, package1 is unloaded in truck2, driver2 drives truck2 from location s2 to location s1 and from truck2, driver2 disembarks at location s1 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "driver1 is currently at location s0, driver1 is not at location p0_1, driver1 is not at location p1_3, driver1 is not at location p2_1, driver1 is not at location p3_0, driver1 is not at location s1, driver1 is not currently at location p0_2, driver1 is not currently at location p0_3, driver1 is not driving truck1 currently, driver1 is not driving truck2 currently, driver1 is not present at location s2, driver1 is not present at location s3, driver2 is currently at location s1, driver2 is not at location p1_3, driver2 is not at location p3_0, driver2 is not at location s0, driver2 is not currently at location p0_3, driver2 is not currently at location p2_1, driver2 is not currently at location s3, driver2 is not driving truck1 currently, driver2 is not driving truck2, driver2 is not present at location p0_1, driver2 is not present at location p0_2, driver2 is not present at location s2, locations p0_1 and p0_2 does not have a link between them, locations p0_1 and p0_3 does not have a link between them, locations p0_1 and p1_3 does not have a link between them, locations p0_1 and p2_1 does not have a path between them, locations p0_2 and p1_3 does not have a link between them, locations p0_2 and p1_3 does not have a path between them, locations p0_2 and s0 have a path between them, locations p0_2 and s1 does not have a link between them, locations p0_2 and s2 does not have a link between them, locations p0_2 and s2 have a path between them, locations p0_2 and s3 does not have a link between them, locations p0_3 and p0_1 does not have a path between them, locations p0_3 and p0_2 does not have a link between them, locations p0_3 and p2_1 does not have a link between them, locations p0_3 and p2_1 does not have a path between them, locations p0_3 and p3_0 does not have a link between them, locations p0_3 and p3_0 does not have a path between them, locations p0_3 and s2 does not have a link between them, locations p1_3 and p0_1 does not have a link between them, locations p1_3 and p0_2 does not have a link between them, locations p1_3 and p0_3 does not have a link between them, locations p1_3 and s1 does not have a link between them, locations p1_3 and s1 have a path between them, locations p1_3 and s2 does not have a path between them, locations p1_3 and s3 have a path between them, locations p2_1 and p0_1 does not have a link between them, locations p2_1 and p0_1 does not have a path between them, locations p2_1 and p0_3 does not have a link between them, locations p2_1 and p0_3 does not have a path between them, locations p2_1 and p3_0 does not have a link between them, locations p2_1 and s1 have a path between them, locations p2_1 and s2 does not have a link between them, locations p2_1 and s2 have a path between them, locations p3_0 and p0_1 does not have a link between them, locations p3_0 and p0_1 does not have a path between them, locations p3_0 and p0_2 does not have a link between them, locations p3_0 and p0_2 does not have a path between them, locations p3_0 and p2_1 does not have a path between them, locations p3_0 and s0 does not have a path between them, locations p3_0 and s1 does not have a path between them, locations p3_0 and s3 does not have a link between them, locations s0 and p0_1 have a path between them, locations s0 and p0_3 does not have a link between them, locations s0 and p0_3 have a path between them, locations s0 and p2_1 does not have a link between them, locations s0 and p2_1 does not have a path between them, locations s1 and p0_3 does not have a link between them, locations s1 and p1_3 have a path between them, locations s1 and s0 have a link between them, locations s1 and s2 does not have a path between them, locations s2 and p1_3 does not have a link between them, locations s2 and p2_1 does not have a link between them, locations s2 and p2_1 have a path between them, locations s2 and s0 have a link between them, locations s2 and s1 have a link between them, locations s3 and p0_1 does not have a path between them, locations s3 and p0_2 does not have a path between them, locations s3 and p3_0 does not have a link between them, locations s3 and s2 have a link between them, package1 is currently at location s2, package1 is not at location p0_2, package1 is not at location s0, package1 is not at location s1, package1 is not currently at location p2_1, package1 is not currently at location p3_0, package1 is not in truck1, package1 is not located in truck2, package1 is not present at location p0_1, package1 is not present at location p0_3, package1 is not present at location p1_3, package1 is not present at location s3, package2 is not at location p0_3, package2 is not at location p2_1, package2 is not at location p3_0, package2 is not at location s0, package2 is not at location s2, package2 is not currently at location p0_2, package2 is not currently at location p1_3, package2 is not currently at location s1, package2 is not located in truck1, package2 is not present at location p0_1, package2 is not present at location s3, package2 is placed in truck2, package3 is at location s3, package3 is not at location p0_1, package3 is not at location s1, package3 is not at location s2, package3 is not currently at location p1_3, package3 is not currently at location p3_0, package3 is not in truck1, package3 is not placed in truck2, package3 is not present at location p0_2, package3 is not present at location p0_3, package3 is not present at location p2_1, package3 is not present at location s0, package4 is not at location p0_1, package4 is not at location p1_3, package4 is not at location s1, package4 is not at location s3, package4 is not currently at location p0_2, package4 is not currently at location p2_1, package4 is not currently at location p3_0, package4 is not currently at location s0, package4 is not located in truck1, package4 is not located in truck2, package4 is not present at location p0_3, package4 is present at location s2, there doesn't exist a link between the locations p0_1 and s0, there doesn't exist a link between the locations p0_1 and s1, there doesn't exist a link between the locations p0_2 and p0_1, there doesn't exist a link between the locations p0_2 and p2_1, there doesn't exist a link between the locations p0_3 and p1_3, there doesn't exist a link between the locations p0_3 and s0, there doesn't exist a link between the locations p0_3 and s1, there doesn't exist a link between the locations p1_3 and s2, there doesn't exist a link between the locations p1_3 and s3, there doesn't exist a link between the locations p2_1 and p0_2, there doesn't exist a link between the locations p2_1 and s1, there doesn't exist a link between the locations p2_1 and s3, there doesn't exist a link between the locations s0 and p0_2, there doesn't exist a link between the locations s0 and p1_3, there doesn't exist a link between the locations s1 and p1_3, there doesn't exist a link between the locations s1 and p3_0, there doesn't exist a link between the locations s2 and p0_2, there doesn't exist a link between the locations s2 and p3_0, there doesn't exist a link between the locations s3 and p0_3, there doesn't exist a link between the locations s3 and p1_3, there doesn't exist a link between the locations s3 and p2_1, there doesn't exist a path between the locations p0_1 and p0_2, there doesn't exist a path between the locations p0_1 and s2, there doesn't exist a path between the locations p0_2 and p0_3, there doesn't exist a path between the locations p0_2 and p2_1, there doesn't exist a path between the locations p0_2 and s1, there doesn't exist a path between the locations p0_3 and p0_2, there doesn't exist a path between the locations p1_3 and p0_3, there doesn't exist a path between the locations p1_3 and p3_0, there doesn't exist a path between the locations p1_3 and s0, there doesn't exist a path between the locations p2_1 and p0_2, there doesn't exist a path between the locations p3_0 and p0_3, there doesn't exist a path between the locations p3_0 and p1_3, there doesn't exist a path between the locations p3_0 and s2, there doesn't exist a path between the locations p3_0 and s3, there doesn't exist a path between the locations s0 and p1_3, there doesn't exist a path between the locations s0 and p3_0, there doesn't exist a path between the locations s0 and s1, there doesn't exist a path between the locations s0 and s3, there doesn't exist a path between the locations s1 and p0_3, there doesn't exist a path between the locations s1 and p3_0, there doesn't exist a path between the locations s2 and p0_1, there doesn't exist a path between the locations s2 and p3_0, there doesn't exist a path between the locations s2 and s3, there doesn't exist a path between the locations s3 and p3_0, there doesn't exist a path between the locations s3 and s2, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations s0 and p0_2, there exists a path between the locations s1 and p0_1, there exists a path between the locations s3 and p0_3, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s2, there is a link between location s3 and location s1, there is a path between location p0_1 and location s1, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, there is no link between location p0_1 and location p2_1, there is no link between location p0_1 and location p3_0, there is no link between location p0_1 and location s2, there is no link between location p0_1 and location s3, there is no link between location p0_2 and location p0_3, there is no link between location p0_2 and location p3_0, there is no link between location p0_2 and location s0, there is no link between location p0_3 and location p0_1, there is no link between location p0_3 and location s3, there is no link between location p1_3 and location p2_1, there is no link between location p1_3 and location p3_0, there is no link between location p1_3 and location s0, there is no link between location p2_1 and location p1_3, there is no link between location p2_1 and location s0, there is no link between location p3_0 and location p0_3, there is no link between location p3_0 and location p1_3, there is no link between location p3_0 and location p2_1, there is no link between location p3_0 and location s0, there is no link between location p3_0 and location s1, there is no link between location p3_0 and location s2, there is no link between location s0 and location p0_1, there is no link between location s0 and location p3_0, there is no link between location s1 and location p0_1, there is no link between location s1 and location p0_2, there is no link between location s1 and location p2_1, there is no link between location s2 and location p0_1, there is no link between location s2 and location p0_3, there is no link between location s3 and location p0_1, there is no link between location s3 and location p0_2, there is no path between location p0_1 and location p0_3, there is no path between location p0_1 and location p1_3, there is no path between location p0_1 and location p3_0, there is no path between location p0_1 and location s3, there is no path between location p0_2 and location p0_1, there is no path between location p0_2 and location p3_0, there is no path between location p0_2 and location s3, there is no path between location p0_3 and location p1_3, there is no path between location p0_3 and location s1, there is no path between location p0_3 and location s2, there is no path between location p1_3 and location p0_1, there is no path between location p1_3 and location p0_2, there is no path between location p1_3 and location p2_1, there is no path between location p2_1 and location p1_3, there is no path between location p2_1 and location p3_0, there is no path between location p2_1 and location s0, there is no path between location p2_1 and location s3, there is no path between location s0 and location s2, there is no path between location s1 and location p0_2, there is no path between location s1 and location s0, there is no path between location s1 and location s3, there is no path between location s2 and location p0_3, there is no path between location s2 and location p1_3, there is no path between location s2 and location s0, there is no path between location s2 and location s1, there is no path between location s3 and location p2_1, there is no path between location s3 and location s0, there is no path between location s3 and location s1, truck1 contains nothing, truck1 is currently at location s3, truck1 is not at location p0_2, truck1 is not at location p3_0, truck1 is not currently at location p0_1, truck1 is not currently at location p0_3, truck1 is not present at location p1_3, truck1 is not present at location p2_1, truck1 is not present at location s0, truck1 is not present at location s1, truck1 is not present at location s2, truck2 contains nothing, truck2 is at location s1, truck2 is not at location p0_2, truck2 is not at location p2_1, truck2 is not at location p3_0, truck2 is not currently at location p0_1, truck2 is not currently at location s3, truck2 is not present at location p0_3, truck2 is not present at location p1_3, truck2 is not present at location s0 and truck2 is not present at location s2", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s2, driver2 is at location s2, locations p0_2 and s0 have a path between them, locations p0_2 and s2 have a path between them, locations p0_3 and s0 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and s2 have a link between them, locations s2 and p0_2 have a path between them, locations s2 and s3 have a link between them, package1 is at location s1, package2 is present at location s2, package3 is present at location s0, package4 is at location s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s3 and s0, there exists a link between the locations s3 and s1, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s3, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a link between location s2 and location s1, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s3, there is a path between location s0 and location p0_2, there is a path between location s1 and location p1_3, there is a path between location s1 and location p2_1, there is a path between location s3 and location p0_3, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing and truck2 is currently at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: package3 is loaded onto truck1 at location s0, driver1 moves from location s2 to p0_2, then from p0_2 to s0, boards truck1 at s0, drives truck1 from s0 to s3, disembarks from truck1 at s3, unloads package3 from truck1 at s3, and then walks from s3 to p0_3 and back to s0. Meanwhile, driver2 moves from s2 to p0_2, then to s0, boards truck2 at s0, drives truck2 from s0 to s1, loads package1 onto truck2 at s1, drives truck2 from s1 to s2, loads package2 onto truck2 at s2, unloads package1 from truck2 at s2, drives truck2 from s2 back to s1, and finally disembarks from truck2 at s1, resulting in the current state. In this state, list all valid properties (both affirmative and negative) that apply, or indicate None if there are none.", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also at s2. A path exists between locations p0_2 and s0, as well as between p0_2 and s2. Additionally, a path is present between p0_3 and s0, and between p2_1 and s2. Furthermore, a path connects s0 to p0_1 and s0 to p0_3. Locations s0 and s1 are linked, as are s0 and s3, s1 and s2, s2 and s3, and s2 and p0_2. Package1 is situated at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. Links exist between s1 and s3, s2 and s0, s3 and s0, s3 and s1, and s3 and s2. Paths are present between p0_1 and s1, p0_3 and s3, p1_3 and s1, p2_1 and s1, s1 and p0_1, s2 and p2_1, and s3 and p1_3. Moreover, links connect s0 to s2, s1 to s0, and s2 to s1. Paths also exist between p0_1 and s0, p1_3 and s3, s0 and p0_2, s1 and p1_3, s1 and p2_1, and s3 and p0_3. Truck1 is currently empty and located at s0, while truck2 is also at s0 and contains nothing."}
{"question_id": "69217072-9f02-4ed1-9a24-ed3c13a0de33", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location s0, driver3 boards truck1, package3 is loaded in truck1 at location s0, at location s0, package1 is loaded in truck1, driver3 drives truck1 to location s3 from location s0, truck1 is unloaded with package1 at location s3, driver3 drives truck1 from location s3 to location s1, driver3 disembarks from truck1 at location s1, package3 is unloaded from truck1 at location s1, at location s2, package2 is loaded in truck2, driver1 walks to location p3_0 from location s3, driver1 walks to location s0 from location p3_0, driver2 walks from location s3 to location p1_3, driver2 walks to location s1 from location p1_3, driver2 walks from location s1 to p1_2, driver2 walks to location s2 from location p1_2, driver3 walks to location p1_2 from location s1, driver3 walks from location p1_2 to location s2, truck2 is boarded by driver3 at location s2 and driver3 drives truck2 from location s2 to location s3 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "driver1 is not at location p1_0, driver1 is not at location p2_1, driver1 is not at location p3_0, driver1 is not at location s2, driver1 is not at location s3, driver1 is not currently at location p0_1, driver1 is not driving truck2, driver1 is not present at location p1_2, driver1 is not present at location p1_3, driver1 is not present at location p2_0, driver1 is not present at location s1, driver1 is present at location s0, driver2 is at location s2, driver2 is not at location p0_1, driver2 is not at location p1_0, driver2 is not at location s1, driver2 is not currently at location s3, driver2 is not driving truck1, driver2 is not driving truck2, driver2 is not present at location p1_2, driver2 is not present at location p1_3, driver2 is not present at location p2_0, driver2 is not present at location p2_1, driver2 is not present at location p3_0, driver2 is not present at location s0, driver3 is not at location p1_2, driver3 is not at location p1_3, driver3 is not at location s1, driver3 is not currently at location p1_0, driver3 is not currently at location p2_0, driver3 is not currently at location s0, driver3 is not currently at location s2, driver3 is not driving truck1 currently, driver3 is not present at location p0_1, driver3 is not present at location p2_1, driver3 is not present at location p3_0, driver3 is not present at location s3, locations p0_1 and p1_0 does not have a path between them, locations p0_1 and p2_0 does not have a link between them, locations p0_1 and p2_0 does not have a path between them, locations p0_1 and p2_1 does not have a link between them, locations p0_1 and p3_0 does not have a link between them, locations p0_1 and p3_0 does not have a path between them, locations p0_1 and s1 does not have a link between them, locations p0_1 and s1 have a path between them, locations p0_1 and s2 does not have a link between them, locations p1_0 and p0_1 does not have a link between them, locations p1_0 and p1_2 does not have a link between them, locations p1_0 and p1_3 does not have a path between them, locations p1_0 and p2_0 does not have a path between them, locations p1_0 and p2_1 does not have a link between them, locations p1_0 and s1 does not have a link between them, locations p1_0 and s1 does not have a path between them, locations p1_0 and s3 does not have a link between them, locations p1_2 and p0_1 does not have a path between them, locations p1_2 and p1_3 does not have a path between them, locations p1_2 and p2_0 does not have a path between them, locations p1_2 and s1 does not have a link between them, locations p1_2 and s2 does not have a link between them, locations p1_2 and s3 does not have a link between them, locations p1_3 and p0_1 does not have a link between them, locations p1_3 and p1_0 does not have a path between them, locations p1_3 and p2_0 does not have a path between them, locations p1_3 and p2_1 does not have a link between them, locations p1_3 and p3_0 does not have a link between them, locations p1_3 and p3_0 does not have a path between them, locations p1_3 and s2 does not have a link between them, locations p1_3 and s2 does not have a path between them, locations p1_3 and s3 does not have a link between them, locations p2_0 and p0_1 does not have a link between them, locations p2_0 and p1_0 does not have a path between them, locations p2_0 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p2_1 and p1_0 does not have a path between them, locations p2_1 and p1_3 does not have a path between them, locations p2_1 and p2_0 does not have a link between them, locations p2_1 and s0 does not have a link between them, locations p2_1 and s1 does not have a path between them, locations p2_1 and s3 does not have a path between them, locations p3_0 and p0_1 does not have a link between them, locations p3_0 and p0_1 does not have a path between them, locations p3_0 and p1_0 does not have a link between them, locations p3_0 and p1_0 does not have a path between them, locations p3_0 and p1_2 does not have a link between them, locations p3_0 and p1_2 does not have a path between them, locations p3_0 and p1_3 does not have a path between them, locations p3_0 and s1 does not have a link between them, locations p3_0 and s1 does not have a path between them, locations s0 and p1_0 does not have a link between them, locations s0 and p1_3 does not have a link between them, locations s0 and p1_3 does not have a path between them, locations s0 and p2_0 does not have a link between them, locations s0 and p2_1 does not have a link between them, locations s0 and p2_1 does not have a path between them, locations s0 and s2 have a link between them, locations s1 and p1_0 does not have a link between them, locations s1 and p1_2 does not have a link between them, locations s1 and p1_2 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_0 does not have a path between them, locations s1 and p2_1 does not have a path between them, locations s1 and p3_0 does not have a link between them, locations s1 and s2 have a link between them, locations s2 and p1_0 does not have a path between them, locations s2 and p1_2 have a path between them, locations s2 and p2_0 does not have a link between them, locations s2 and p3_0 does not have a path between them, locations s2 and s0 have a link between them, locations s2 and s1 does not have a path between them, locations s3 and p1_2 does not have a path between them, locations s3 and p1_3 does not have a link between them, locations s3 and p2_1 does not have a link between them, locations s3 and s2 does not have a path between them, package1 is at location s3, package1 is not at location p0_1, package1 is not at location s2, package1 is not currently at location p1_0, package1 is not currently at location p1_3, package1 is not currently at location p3_0, package1 is not currently at location s1, package1 is not located in truck1, package1 is not located in truck2, package1 is not present at location p1_2, package1 is not present at location p2_0, package1 is not present at location p2_1, package1 is not present at location s0, package2 is not at location p1_2, package2 is not at location p1_3, package2 is not at location p2_1, package2 is not at location s0, package2 is not currently at location p0_1, package2 is not currently at location p2_0, package2 is not currently at location p3_0, package2 is not currently at location s2, package2 is not placed in truck1, package2 is not present at location p1_0, package2 is not present at location s1, package2 is not present at location s3, package2 is placed in truck2, package3 is not at location p2_1, package3 is not at location s0, package3 is not currently at location p0_1, package3 is not currently at location p1_0, package3 is not currently at location p1_3, package3 is not currently at location p2_0, package3 is not currently at location p3_0, package3 is not currently at location s2, package3 is not currently at location s3, package3 is not in truck1, package3 is not in truck2, package3 is not present at location p1_2, package3 is present at location s1, there doesn't exist a link between the locations p0_1 and p1_2, there doesn't exist a link between the locations p0_1 and s3, there doesn't exist a link between the locations p1_0 and p1_3, there doesn't exist a link between the locations p1_0 and p2_0, there doesn't exist a link between the locations p1_0 and s0, there doesn't exist a link between the locations p1_0 and s2, there doesn't exist a link between the locations p1_2 and p0_1, there doesn't exist a link between the locations p1_2 and p1_0, there doesn't exist a link between the locations p1_2 and p2_0, there doesn't exist a link between the locations p1_3 and p1_0, there doesn't exist a link between the locations p1_3 and p1_2, there doesn't exist a link between the locations p1_3 and p2_0, there doesn't exist a link between the locations p1_3 and s1, there doesn't exist a link between the locations p2_0 and p1_0, there doesn't exist a link between the locations p2_0 and p1_2, there doesn't exist a link between the locations p2_1 and p1_3, there doesn't exist a link between the locations p2_1 and p3_0, there doesn't exist a link between the locations p2_1 and s1, there doesn't exist a link between the locations p2_1 and s3, there doesn't exist a link between the locations p3_0 and p2_1, there doesn't exist a link between the locations p3_0 and s0, there doesn't exist a link between the locations p3_0 and s2, there doesn't exist a link between the locations s0 and p0_1, there doesn't exist a link between the locations s0 and p3_0, there doesn't exist a link between the locations s1 and p1_3, there doesn't exist a link between the locations s1 and p2_0, there doesn't exist a link between the locations s1 and p2_1, there doesn't exist a link between the locations s2 and p2_1, there doesn't exist a link between the locations s2 and p3_0, there doesn't exist a link between the locations s3 and p1_0, there doesn't exist a link between the locations s3 and p1_2, there doesn't exist a link between the locations s3 and p2_0, there doesn't exist a link between the locations s3 and p3_0, there doesn't exist a path between the locations p0_1 and p1_3, there doesn't exist a path between the locations p0_1 and s2, there doesn't exist a path between the locations p0_1 and s3, there doesn't exist a path between the locations p1_0 and p1_2, there doesn't exist a path between the locations p1_0 and p2_1, there doesn't exist a path between the locations p1_0 and p3_0, there doesn't exist a path between the locations p1_0 and s0, there doesn't exist a path between the locations p1_2 and p2_1, there doesn't exist a path between the locations p1_2 and s0, there doesn't exist a path between the locations p1_3 and p1_2, there doesn't exist a path between the locations p1_3 and s0, there doesn't exist a path between the locations p2_0 and p0_1, there doesn't exist a path between the locations p2_0 and p1_3, there doesn't exist a path between the locations p2_0 and p2_1, there doesn't exist a path between the locations p2_0 and p3_0, there doesn't exist a path between the locations p2_0 and s3, there doesn't exist a path between the locations p2_1 and s0, there doesn't exist a path between the locations p2_1 and s2, there doesn't exist a path between the locations p3_0 and p2_0, there doesn't exist a path between the locations p3_0 and p2_1, there doesn't exist a path between the locations p3_0 and s2, there doesn't exist a path between the locations s0 and p1_2, there doesn't exist a path between the locations s1 and s2, there doesn't exist a path between the locations s2 and p1_3, there doesn't exist a path between the locations s2 and p2_1, there doesn't exist a path between the locations s2 and s0, there doesn't exist a path between the locations s2 and s3, there doesn't exist a path between the locations s3 and p0_1, there doesn't exist a path between the locations s3 and p1_0, there doesn't exist a path between the locations s3 and s0, there exists a link between the locations s0 and s3, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations p3_0 and s0, there exists a path between the locations s0 and p2_0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p2_0, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s0, there is a link between location s3 and location s2, there is a path between location p0_1 and location s0, there is a path between location p1_2 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s1, there is a path between location p3_0 and location s3, there is a path between location s0 and location p0_1, there is a path between location s1 and location p0_1, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, there is no link between location p0_1 and location p1_0, there is no link between location p0_1 and location p1_3, there is no link between location p0_1 and location s0, there is no link between location p1_0 and location p3_0, there is no link between location p1_2 and location p1_3, there is no link between location p1_2 and location p2_1, there is no link between location p1_2 and location p3_0, there is no link between location p1_2 and location s0, there is no link between location p1_3 and location s0, there is no link between location p2_0 and location p1_3, there is no link between location p2_0 and location p2_1, there is no link between location p2_0 and location p3_0, there is no link between location p2_0 and location s0, there is no link between location p2_0 and location s1, there is no link between location p2_0 and location s2, there is no link between location p2_0 and location s3, there is no link between location p2_1 and location p0_1, there is no link between location p2_1 and location p1_0, there is no link between location p2_1 and location p1_2, there is no link between location p2_1 and location s2, there is no link between location p3_0 and location p1_3, there is no link between location p3_0 and location p2_0, there is no link between location p3_0 and location s3, there is no link between location s0 and location p1_2, there is no link between location s0 and location s1, there is no link between location s1 and location p0_1, there is no link between location s1 and location s0, there is no link between location s2 and location p0_1, there is no link between location s2 and location p1_0, there is no link between location s2 and location p1_2, there is no link between location s2 and location p1_3, there is no link between location s3 and location p0_1, there is no path between location p0_1 and location p1_2, there is no path between location p0_1 and location p2_1, there is no path between location p1_0 and location p0_1, there is no path between location p1_0 and location s2, there is no path between location p1_0 and location s3, there is no path between location p1_2 and location p1_0, there is no path between location p1_2 and location p3_0, there is no path between location p1_2 and location s3, there is no path between location p1_3 and location p0_1, there is no path between location p1_3 and location p2_1, there is no path between location p2_0 and location p1_2, there is no path between location p2_0 and location s1, there is no path between location p2_1 and location p0_1, there is no path between location p2_1 and location p1_2, there is no path between location p2_1 and location p2_0, there is no path between location p2_1 and location p3_0, there is no path between location s0 and location p1_0, there is no path between location s0 and location s1, there is no path between location s0 and location s2, there is no path between location s0 and location s3, there is no path between location s1 and location p1_0, there is no path between location s1 and location p3_0, there is no path between location s1 and location s0, there is no path between location s1 and location s3, there is no path between location s2 and location p0_1, there is no path between location s3 and location p2_0, there is no path between location s3 and location p2_1, there is no path between location s3 and location s1, truck1 contains nothing, truck1 is not at location p2_1, truck1 is not at location p3_0, truck1 is not at location s3, truck1 is not being driven by driver1, truck1 is not currently at location p0_1, truck1 is not currently at location p1_0, truck1 is not currently at location p2_0, truck1 is not present at location p1_2, truck1 is not present at location p1_3, truck1 is not present at location s0, truck1 is not present at location s2, truck1 is present at location s1, truck2 is being driven by driver3, truck2 is currently at location s3, truck2 is not at location p0_1, truck2 is not at location p1_2, truck2 is not at location p2_0, truck2 is not at location p3_0, truck2 is not at location s0, truck2 is not at location s1, truck2 is not currently at location p1_0, truck2 is not currently at location p1_3, truck2 is not empty, truck2 is not present at location p2_1 and truck2 is not present at location s2", "plan_length": 19, "initial_state_nl": "Driver1 is at location s3, driver2 is present at location s3, driver3 is currently at location s0, locations s1 and p1_3 have a path between them, locations s1 and s3 have a link between them, locations s3 and s2 have a link between them, package1 is present at location s0, package2 is currently at location s2, package3 is at location s0, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s0, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_0 and s0, there exists a path between the locations p2_0 and s2, there exists a path between the locations p3_0 and s0, there exists a path between the locations s0 and p2_0, there exists a path between the locations s1 and p1_2, there exists a path between the locations s2 and p1_2, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s2, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s3, there is a path between location p3_0 and location s3, there is a path between location s0 and location p0_1, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s2 and location p2_0, there is a path between location s3 and location p3_0, truck1 is empty, truck1 is present at location s0, truck2 contains nothing and truck2 is at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: at location s0, driver3 gets into truck1, package3 is placed in truck1 at location s0, followed by package1 being loaded into truck1 at the same location, then driver3 drives truck1 from location s0 to location s3, package1 is unloaded from truck1 at location s3, driver3 drives truck1 from location s3 to location s1, driver3 gets out of truck1 at location s1, and package3 is removed from truck1 at location s1. Meanwhile, at location s2, package2 is loaded into truck2. Additionally, driver1 walks from location s3 to p3_0 and then back to location s0, driver2 walks from location s3 to p1_3, then to location s1, followed by p1_2, and finally to location s2, while driver3 walks from location s1 to p1_2 and then to location s2, where driver3 boards truck2 and drives it from location s2 to location s3, resulting in the current state. In this state, list all valid properties (both affirmative and negative) that apply, or indicate None if there are none.", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is also present at s3, and driver3 is at location s0. A path exists between locations s1 and p1_3, and a connection is established between locations s1 and s3, as well as between locations s3 and s2. Package1 is situated at location s0, package2 is at location s2, and package3 is also at location s0. There are links between locations s2 and s0, s2 and s1, s2 and s3, s3 and s0, and s3 and s1. Additionally, paths exist between locations p0_1 and s0, p1_2 and s1, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s1 and p1_2, s2 and p1_2, s3 and p1_3, s0 and s2, s0 and s3, s1 and s2, p0_1 and s1, p1_2 and s2, p1_3 and s3, p3_0 and s3, s0 and p0_1, s0 and p3_0, s1 and p0_1, s2 and p2_0, and s3 and p3_0. Furthermore, truck1 is empty and located at s0, and truck2, which is also empty, is at location s2."}
{"question_id": "b9336433-63dd-4154-b7c2-c4054d2f8959", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver3 boards truck1 at location s0, truck1 is loaded with package3 at location s0, truck1 is loaded with package1 at location s0, driver3 drives truck1 from location s0 to location s3, package1 is unloaded from truck1 at location s3, truck1 is driven from location s3 to s1 by driver3, driver3 disembarks from truck1 at location s1, truck1 is unloaded with package3 at location s1, package2 is loaded in truck2 at location s2, driver1 walks from location s3 to p3_0, driver1 walks from location p3_0 to s0, driver2 walks from location s3 to location p1_3, driver2 walks from location p1_3 to location s1, driver2 walks from location s1 to location p1_2, driver2 walks from location p1_2 to location s2, driver3 walks to location p1_2 from location s1, driver3 walks to location s2 from location p1_2, driver3 boards truck2 at location s2 and truck2 is driven from location s2 to s3 by driver3 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "driver1 is present at location s0, driver2 is present at location s2, locations p0_1 and s0 have a path between them, locations p1_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations p2_0 and s2 have a path between them, locations s0 and s3 have a link between them, locations s1 and s2 have a link between them, locations s1 and s3 have a link between them, locations s2 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s3 have a link between them, locations s3 and s2 have a link between them, package1 is currently at location s3, package2 is placed in truck2, package3 is present at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s2 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_0 and s0, there exists a path between the locations p3_0 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_2, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p1_3, there exists a path between the locations s3 and p3_0, there is a link between location s2 and location s0, there is a link between location s3 and location s0, there is a link between location s3 and location s1, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, truck1 contains nothing, truck1 is at location s1, truck2 is being driven by driver3 and truck2 is present at location s3", "plan_length": 19, "initial_state_nl": "Driver1 is at location s3, driver2 is present at location s3, driver3 is currently at location s0, locations s1 and p1_3 have a path between them, locations s1 and s3 have a link between them, locations s3 and s2 have a link between them, package1 is present at location s0, package2 is currently at location s2, package3 is at location s0, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s0, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_0 and s0, there exists a path between the locations p2_0 and s2, there exists a path between the locations p3_0 and s0, there exists a path between the locations s0 and p2_0, there exists a path between the locations s1 and p1_2, there exists a path between the locations s2 and p1_2, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s2, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s3, there is a path between location p3_0 and location s3, there is a path between location s0 and location p0_1, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s2 and location p2_0, there is a path between location s3 and location p3_0, truck1 is empty, truck1 is present at location s0, truck2 contains nothing and truck2 is at location s2.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: driver3 gets on truck1 at location s0, truck1 is loaded with package3 at location s0, followed by the loading of package1 at location s0, then driver3 drives truck1 from location s0 to location s3, package1 is unloaded from truck1 at location s3, truck1 is then driven by driver3 from location s3 to location s1, driver3 gets off truck1 at location s1, and package3 is unloaded from truck1 at location s1. Meanwhile, package2 is loaded onto truck2 at location s2. Additionally, driver1 walks from location s3 to p3_0 and then to s0, while driver2 walks from location s3 to p1_3, then to s1, followed by p1_2, and finally to s2. Driver3 also walks from location s1 to p1_2 and then to s2, where driver3 boards truck2 and drives it from location s2 to s3, resulting in the current state. In this state, list all valid properties that do not involve negations. If there are none, state None.", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is also present at s3, and driver3 is at location s0. A path exists between locations s1 and p1_3, and a connection is established between locations s1 and s3, as well as between locations s3 and s2. Package1 is situated at location s0, package2 is at location s2, and package3 is also at location s0. There are links between locations s2 and s0, s2 and s1, s2 and s3, s3 and s0, and s3 and s1. Paths exist between locations p0_1 and s0, p1_2 and s1, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s1 and p1_2, s2 and p1_2, s3 and p1_3, s0 and s2, s0 and s3, and s1 and s2. Furthermore, paths are established between locations p0_1 and s1, p1_2 and s2, p1_3 and s3, p3_0 and s3, s0 and p0_1, s0 and p3_0, s1 and p0_1, s2 and p2_0, and s3 and p3_0. Truck1 is empty and located at s0, while truck2 is also empty and situated at location s2."}
{"question_id": "804767bd-2517-46a7-83e9-516bffb4491e", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver3 boards truck1 at location s0 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "driver1 is not at location p0_1, driver1 is not at location p1_0, driver1 is not at location p2_0, driver1 is not currently at location p1_2, driver1 is not currently at location p1_3, driver1 is not currently at location s2, driver1 is not driving truck1 currently, driver1 is not driving truck2, driver1 is not present at location p2_1, driver1 is not present at location p3_0, driver1 is not present at location s0, driver1 is not present at location s1, driver2 is not at location p1_3, driver2 is not at location s0, driver2 is not at location s1, driver2 is not currently at location p1_0, driver2 is not currently at location p1_2, driver2 is not currently at location p2_0, driver2 is not currently at location p2_1, driver2 is not currently at location p3_0, driver2 is not driving truck1 currently, driver2 is not driving truck2, driver2 is not present at location p0_1, driver2 is not present at location s2, driver3 is not at location p0_1, driver3 is not at location p2_1, driver3 is not at location p3_0, driver3 is not at location s2, driver3 is not currently at location p1_0, driver3 is not currently at location p1_2, driver3 is not currently at location s3, driver3 is not driving truck2, driver3 is not present at location p1_3, driver3 is not present at location p2_0, driver3 is not present at location s0, driver3 is not present at location s1, locations p0_1 and p1_2 does not have a path between them, locations p0_1 and p2_1 does not have a path between them, locations p0_1 and s0 does not have a link between them, locations p0_1 and s2 does not have a path between them, locations p0_1 and s3 does not have a link between them, locations p0_1 and s3 does not have a path between them, locations p1_0 and p1_2 does not have a link between them, locations p1_0 and p1_3 does not have a path between them, locations p1_0 and p2_0 does not have a path between them, locations p1_0 and s0 does not have a link between them, locations p1_0 and s0 does not have a path between them, locations p1_0 and s2 does not have a link between them, locations p1_0 and s3 does not have a link between them, locations p1_0 and s3 does not have a path between them, locations p1_2 and p0_1 does not have a link between them, locations p1_2 and p0_1 does not have a path between them, locations p1_2 and p1_0 does not have a link between them, locations p1_2 and p1_0 does not have a path between them, locations p1_2 and p1_3 does not have a link between them, locations p1_2 and p2_0 does not have a link between them, locations p1_2 and p3_0 does not have a link between them, locations p1_2 and p3_0 does not have a path between them, locations p1_2 and s0 does not have a link between them, locations p1_2 and s0 does not have a path between them, locations p1_2 and s1 does not have a link between them, locations p1_2 and s3 does not have a link between them, locations p1_3 and p1_0 does not have a path between them, locations p1_3 and p1_2 does not have a path between them, locations p1_3 and p3_0 does not have a link between them, locations p1_3 and s0 does not have a path between them, locations p1_3 and s1 does not have a link between them, locations p1_3 and s2 does not have a link between them, locations p2_0 and p1_2 does not have a link between them, locations p2_0 and p1_2 does not have a path between them, locations p2_0 and p1_3 does not have a link between them, locations p2_0 and p2_1 does not have a link between them, locations p2_0 and p2_1 does not have a path between them, locations p2_0 and p3_0 does not have a link between them, locations p2_0 and p3_0 does not have a path between them, locations p2_0 and s3 does not have a path between them, locations p2_1 and p0_1 does not have a link between them, locations p2_1 and p0_1 does not have a path between them, locations p2_1 and p1_0 does not have a path between them, locations p2_1 and p1_3 does not have a path between them, locations p2_1 and p3_0 does not have a link between them, locations p2_1 and p3_0 does not have a path between them, locations p2_1 and s3 does not have a link between them, locations p3_0 and p0_1 does not have a path between them, locations p3_0 and p1_2 does not have a link between them, locations p3_0 and p1_3 does not have a path between them, locations p3_0 and p2_0 does not have a path between them, locations p3_0 and p2_1 does not have a link between them, locations p3_0 and s0 does not have a link between them, locations p3_0 and s1 does not have a path between them, locations s0 and p1_0 does not have a path between them, locations s0 and p1_2 does not have a path between them, locations s0 and p1_3 does not have a link between them, locations s0 and p1_3 does not have a path between them, locations s0 and p2_0 does not have a link between them, locations s0 and p3_0 does not have a link between them, locations s0 and s1 does not have a link between them, locations s1 and p1_0 does not have a link between them, locations s1 and p1_0 does not have a path between them, locations s1 and p1_2 does not have a link between them, locations s1 and p1_3 does not have a link between them, locations s1 and p2_0 does not have a link between them, locations s1 and p2_0 does not have a path between them, locations s1 and p2_1 does not have a link between them, locations s1 and p2_1 does not have a path between them, locations s1 and p3_0 does not have a link between them, locations s1 and s0 does not have a link between them, locations s1 and s2 does not have a path between them, locations s2 and p1_0 does not have a link between them, locations s2 and p1_0 does not have a path between them, locations s2 and p1_2 does not have a link between them, locations s2 and p1_3 does not have a link between them, locations s3 and p0_1 does not have a path between them, locations s3 and p1_0 does not have a path between them, locations s3 and p1_3 does not have a link between them, locations s3 and p2_1 does not have a path between them, locations s3 and p3_0 does not have a link between them, locations s3 and s1 does not have a path between them, package1 is not at location p1_3, package1 is not at location p2_0, package1 is not currently at location p1_0, package1 is not currently at location p3_0, package1 is not currently at location s1, package1 is not currently at location s2, package1 is not currently at location s3, package1 is not in truck2, package1 is not placed in truck1, package1 is not present at location p0_1, package1 is not present at location p1_2, package1 is not present at location p2_1, package2 is not at location p1_0, package2 is not at location p2_0, package2 is not at location p3_0, package2 is not at location s3, package2 is not currently at location p1_2, package2 is not currently at location p2_1, package2 is not currently at location s0, package2 is not currently at location s1, package2 is not in truck1, package2 is not placed in truck2, package2 is not present at location p0_1, package2 is not present at location p1_3, package3 is not at location p1_2, package3 is not at location p1_3, package3 is not at location p2_1, package3 is not at location s3, package3 is not currently at location p0_1, package3 is not currently at location p1_0, package3 is not currently at location p3_0, package3 is not currently at location s1, package3 is not currently at location s2, package3 is not in truck1, package3 is not in truck2, package3 is not present at location p2_0, there doesn't exist a link between the locations p0_1 and p1_0, there doesn't exist a link between the locations p0_1 and p1_2, there doesn't exist a link between the locations p0_1 and s1, there doesn't exist a link between the locations p1_0 and p0_1, there doesn't exist a link between the locations p1_0 and p1_3, there doesn't exist a link between the locations p1_0 and p3_0, there doesn't exist a link between the locations p1_0 and s1, there doesn't exist a link between the locations p1_2 and p2_1, there doesn't exist a link between the locations p1_2 and s2, there doesn't exist a link between the locations p1_3 and p1_2, there doesn't exist a link between the locations p1_3 and p2_1, there doesn't exist a link between the locations p1_3 and s0, there doesn't exist a link between the locations p1_3 and s3, there doesn't exist a link between the locations p2_0 and p1_0, there doesn't exist a link between the locations p2_0 and s3, there doesn't exist a link between the locations p2_1 and p1_0, there doesn't exist a link between the locations p2_1 and p1_2, there doesn't exist a link between the locations p2_1 and p2_0, there doesn't exist a link between the locations p2_1 and s0, there doesn't exist a link between the locations p2_1 and s1, there doesn't exist a link between the locations p3_0 and p1_3, there doesn't exist a link between the locations p3_0 and s1, there doesn't exist a link between the locations s0 and p0_1, there doesn't exist a link between the locations s0 and p1_0, there doesn't exist a link between the locations s2 and p2_0, there doesn't exist a link between the locations s3 and p1_0, there doesn't exist a link between the locations s3 and p1_2, there doesn't exist a link between the locations s3 and p2_0, there doesn't exist a path between the locations p0_1 and p1_0, there doesn't exist a path between the locations p0_1 and p1_3, there doesn't exist a path between the locations p0_1 and p2_0, there doesn't exist a path between the locations p1_0 and p1_2, there doesn't exist a path between the locations p1_0 and p2_1, there doesn't exist a path between the locations p1_0 and p3_0, there doesn't exist a path between the locations p1_0 and s1, there doesn't exist a path between the locations p1_0 and s2, there doesn't exist a path between the locations p1_2 and p1_3, there doesn't exist a path between the locations p1_2 and p2_0, there doesn't exist a path between the locations p1_2 and p2_1, there doesn't exist a path between the locations p1_3 and p0_1, there doesn't exist a path between the locations p1_3 and p2_1, there doesn't exist a path between the locations p2_0 and p0_1, there doesn't exist a path between the locations p2_0 and p1_0, there doesn't exist a path between the locations p2_0 and s1, there doesn't exist a path between the locations p2_1 and p2_0, there doesn't exist a path between the locations p2_1 and s0, there doesn't exist a path between the locations p2_1 and s1, there doesn't exist a path between the locations p3_0 and p1_0, there doesn't exist a path between the locations p3_0 and p2_1, there doesn't exist a path between the locations s1 and p3_0, there doesn't exist a path between the locations s2 and p1_3, there doesn't exist a path between the locations s2 and s0, there doesn't exist a path between the locations s2 and s3, there doesn't exist a path between the locations s3 and p2_0, there doesn't exist a path between the locations s3 and s0, there is no link between location p0_1 and location p1_3, there is no link between location p0_1 and location p2_0, there is no link between location p0_1 and location p2_1, there is no link between location p0_1 and location p3_0, there is no link between location p0_1 and location s2, there is no link between location p1_0 and location p2_0, there is no link between location p1_0 and location p2_1, there is no link between location p1_3 and location p0_1, there is no link between location p1_3 and location p1_0, there is no link between location p1_3 and location p2_0, there is no link between location p2_0 and location p0_1, there is no link between location p2_0 and location s0, there is no link between location p2_0 and location s1, there is no link between location p2_0 and location s2, there is no link between location p2_1 and location p1_3, there is no link between location p2_1 and location s2, there is no link between location p3_0 and location p0_1, there is no link between location p3_0 and location p1_0, there is no link between location p3_0 and location p2_0, there is no link between location p3_0 and location s2, there is no link between location p3_0 and location s3, there is no link between location s0 and location p1_2, there is no link between location s0 and location p2_1, there is no link between location s1 and location p0_1, there is no link between location s2 and location p0_1, there is no link between location s2 and location p2_1, there is no link between location s2 and location p3_0, there is no link between location s3 and location p0_1, there is no link between location s3 and location p2_1, there is no path between location p0_1 and location p3_0, there is no path between location p1_0 and location p0_1, there is no path between location p1_2 and location s3, there is no path between location p1_3 and location p2_0, there is no path between location p1_3 and location p3_0, there is no path between location p1_3 and location s2, there is no path between location p2_0 and location p1_3, there is no path between location p2_1 and location p1_2, there is no path between location p2_1 and location s2, there is no path between location p2_1 and location s3, there is no path between location p3_0 and location p1_2, there is no path between location p3_0 and location s2, there is no path between location s0 and location p2_1, there is no path between location s0 and location s1, there is no path between location s0 and location s2, there is no path between location s0 and location s3, there is no path between location s1 and location s0, there is no path between location s1 and location s3, there is no path between location s2 and location p0_1, there is no path between location s2 and location p2_1, there is no path between location s2 and location p3_0, there is no path between location s2 and location s1, there is no path between location s3 and location p1_2, there is no path between location s3 and location s2, truck1 contains some package, truck1 is not at location p1_2, truck1 is not at location p2_1, truck1 is not at location p3_0, truck1 is not currently at location p2_0, truck1 is not currently at location s1, truck1 is not present at location p0_1, truck1 is not present at location p1_0, truck1 is not present at location p1_3, truck1 is not present at location s2, truck1 is not present at location s3, truck2 is not at location p1_0, truck2 is not at location p2_1, truck2 is not at location s0, truck2 is not at location s3, truck2 is not currently at location s1, truck2 is not present at location p0_1, truck2 is not present at location p1_2, truck2 is not present at location p1_3, truck2 is not present at location p2_0 and truck2 is not present at location p3_0", "plan_length": 1, "initial_state_nl": "Driver1 is at location s3, driver2 is present at location s3, driver3 is currently at location s0, locations s1 and p1_3 have a path between them, locations s1 and s3 have a link between them, locations s3 and s2 have a link between them, package1 is present at location s0, package2 is currently at location s2, package3 is at location s0, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s0, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_0 and s0, there exists a path between the locations p2_0 and s2, there exists a path between the locations p3_0 and s0, there exists a path between the locations s0 and p2_0, there exists a path between the locations s1 and p1_2, there exists a path between the locations s2 and p1_2, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s2, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s3, there is a path between location p3_0 and location s3, there is a path between location s0 and location p0_1, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s2 and location p2_0, there is a path between location s3 and location p3_0, truck1 is empty, truck1 is present at location s0, truck2 contains nothing and truck2 is at location s2.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: driver3 boards truck1 at location s0 to reach the current state. In this state, identify all valid properties that include negations and list them; if there are no such properties, indicate None.", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is also present at s3, and driver3 is at location s0. A path exists between locations s1 and p1_3, and a connection is established between locations s1 and s3, as well as between locations s3 and s2. Package1 is situated at location s0, package2 is at location s2, and package3 is also at location s0. There are links between locations s2 and s0, s2 and s1, s2 and s3, s3 and s0, and s3 and s1. Additionally, paths exist between locations p0_1 and s0, p1_2 and s1, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s1 and p1_2, s2 and p1_2, s3 and p1_3, s0 and s2, s0 and s3, s1 and s2, p0_1 and s1, p1_2 and s2, p1_3 and s3, p3_0 and s3, s0 and p0_1, s0 and p3_0, s1 and p0_1, s2 and p2_0, and s3 and p3_0. Furthermore, truck1 is empty and located at s0, while truck2 is also empty and situated at location s2."}
{"question_id": "cff3abe6-3afa-47b5-aa99-b24847d72b25", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks to location p4_3 from location s3 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "driver1 is present at location p4_3, driver2 is currently at location s4, driver3 is present at location s3, locations p4_0 and s0 have a path between them, locations p4_0 and s4 have a path between them, locations p4_1 and s4 have a path between them, locations s0 and p4_0 have a path between them, locations s1 and p4_1 have a path between them, locations s1 and s2 have a link between them, locations s2 and p5_2 have a path between them, locations s3 and s4 have a link between them, locations s3 and s5 have a link between them, locations s4 and p4_0 have a path between them, locations s4 and p4_1 have a path between them, locations s4 and s1 have a link between them, locations s4 and s5 have a link between them, locations s5 and p0_5 have a path between them, locations s5 and p5_2 have a path between them, locations s5 and s2 have a link between them, package1 is currently at location s2, package2 is at location s2, package3 is currently at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s4, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s5, there exists a link between the locations s3 and s2, there exists a link between the locations s5 and s3, there exists a path between the locations p4_1 and s1, there exists a path between the locations p4_3 and s3, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p0_5, there exists a path between the locations s3 and p4_3, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s0 and location s4, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s4 and location s0, there is a link between location s4 and location s3, there is a link between location s5 and location s0, there is a link between location s5 and location s4, there is a path between location p0_5 and location s0, there is a path between location p0_5 and location s5, there is a path between location p5_2 and location s2, there is a path between location p5_2 and location s5, there is a path between location s4 and location p4_3, truck1 contains nothing, truck1 is currently at location s1, truck2 is currently at location s5 and truck2 is empty", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s3, driver2 is present at location s4, driver3 is currently at location s3, locations p4_0 and s0 have a path between them, locations s0 and s1 have a link between them, locations s0 and s4 have a link between them, locations s1 and s0 have a link between them, locations s2 and s1 have a link between them, locations s2 and s3 have a link between them, locations s3 and p4_3 have a path between them, locations s3 and s5 have a link between them, locations s4 and s5 have a link between them, locations s5 and p0_5 have a path between them, locations s5 and s0 have a link between them, locations s5 and s4 have a link between them, package1 is currently at location s2, package2 is at location s2, package3 is at location s3, package4 is currently at location s0, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s5, there exists a link between the locations s3 and s2, there exists a link between the locations s3 and s4, there exists a link between the locations s4 and s1, there exists a link between the locations s4 and s3, there exists a link between the locations s5 and s3, there exists a path between the locations p0_5 and s5, there exists a path between the locations s0 and p4_0, there exists a path between the locations s1 and p4_1, there exists a path between the locations s4 and p4_0, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s2, there is a link between location s0 and location s5, there is a link between location s1 and location s4, there is a link between location s2 and location s0, there is a link between location s4 and location s0, there is a link between location s5 and location s2, there is a path between location p0_5 and location s0, there is a path between location p4_0 and location s4, there is a path between location p4_1 and location s1, there is a path between location p4_1 and location s4, there is a path between location p4_3 and location s3, there is a path between location p4_3 and location s4, there is a path between location p5_2 and location s2, there is a path between location p5_2 and location s5, there is a path between location s0 and location p0_5, there is a path between location s2 and location p5_2, there is a path between location s4 and location p4_1, there is a path between location s4 and location p4_3, truck1 is currently at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: driver1 moves from location s3 to location p4_3 to achieve the current state. In this state, identify all valid properties that do not include negations, or state None if there are no such properties.", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is at s4, and driver3 is also at s3. A path exists between locations p4_0 and s0, and links are present between s0 and s1, s0 and s4, s1 and s0, s1 and s2, s2 and s3, s3 and s5, s4 and s5, s5 and s0, and s5 and s4. Additionally, a path is present between s3 and p4_3, and between s5 and p0_5. Package1 is at location s2, package2 is also at s2, package3 is at s3, and package4 is at s0. Furthermore, links exist between s1 and s2, s2 and s5, s3 and s2, s3 and s4, s4 and s1, s4 and s3, and s5 and s3. Paths also exist between p0_5 and s5, s0 and p4_0, s1 and p4_1, s4 and p4_0, and s5 and p5_2. Moreover, links are present between s0 and s2, s0 and s5, s1 and s4, s2 and s0, s4 and s0, and s5 and s2. Paths also exist between p0_5 and s0, p4_0 and s4, p4_1 and s1, p4_1 and s4, p4_3 and s3, p4_3 and s4, p5_2 and s2, and p5_2 and s5. Additionally, paths are present between s0 and p0_5, s2 and p5_2, s4 and p4_1, and s4 and p4_3. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "f22b5868-3e08-4ca4-9e35-aa303bd50e8c", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to p3_0, driver2 walks from location p3_0 to location s0, truck1 is boarded by driver2 at location s0, driver2 drives truck1 to location s2 from location s0, truck1 is loaded with package3 at location s2, package2 is loaded in truck1 at location s2, driver2 drives truck1 from location s2 to location s0, truck1 is unloaded with package2 at location s0, driver2 drives truck1 to location s3 from location s0, truck1 is loaded with package1 at location s3, driver2 drives truck1 to location s1 from location s3, from truck1, driver2 disembarks at location s1, driver2 walks from location s1 to location p0_1, driver2 walks from location p0_1 to s0, truck3 is boarded by driver2 at location s0, driver2 drives truck3 to location s2 from location s0, at location s1, package3 is unloaded in truck1, package1 is unloaded from truck1 at location s1 and driver3 walks to location p3_0 from location s3 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "driver1 is not at location p1_2, driver1 is not at location p1_3, driver1 is not at location p2_0, driver1 is not at location p3_0, driver1 is not at location s0, driver1 is not currently at location p0_1, driver1 is not currently at location p1_0, driver1 is not driving truck1, driver1 is not driving truck3, driver1 is not present at location s2, driver1 is not present at location s3, driver2 is not at location p0_1, driver2 is not at location p3_0, driver2 is not at location s1, driver2 is not at location s3, driver2 is not currently at location p1_0, driver2 is not currently at location p1_2, driver2 is not currently at location p2_0, driver2 is not currently at location s0, driver2 is not driving truck2, driver2 is not present at location p1_3, driver2 is not present at location s2, driver3 is not at location p1_2, driver3 is not at location s1, driver3 is not at location s2, driver3 is not currently at location p0_1, driver3 is not currently at location s0, driver3 is not driving truck1, driver3 is not driving truck2 currently, driver3 is not driving truck3, driver3 is not present at location p1_0, driver3 is not present at location p1_3, driver3 is not present at location p2_0, driver3 is not present at location s3, locations p0_1 and p1_2 does not have a path between them, locations p0_1 and p1_3 does not have a link between them, locations p0_1 and p2_0 does not have a link between them, locations p0_1 and p2_0 does not have a path between them, locations p0_1 and s0 does not have a link between them, locations p1_0 and p0_1 does not have a link between them, locations p1_0 and p0_1 does not have a path between them, locations p1_0 and p1_2 does not have a link between them, locations p1_0 and p1_3 does not have a link between them, locations p1_0 and s0 does not have a link between them, locations p1_0 and s0 does not have a path between them, locations p1_0 and s1 does not have a link between them, locations p1_0 and s2 does not have a link between them, locations p1_0 and s2 does not have a path between them, locations p1_0 and s3 does not have a path between them, locations p1_2 and p1_0 does not have a link between them, locations p1_2 and p3_0 does not have a link between them, locations p1_2 and s1 does not have a link between them, locations p1_3 and p1_0 does not have a path between them, locations p1_3 and s2 does not have a link between them, locations p1_3 and s2 does not have a path between them, locations p2_0 and p0_1 does not have a link between them, locations p2_0 and p1_2 does not have a link between them, locations p2_0 and p1_2 does not have a path between them, locations p2_0 and p1_3 does not have a link between them, locations p2_0 and p3_0 does not have a link between them, locations p2_0 and p3_0 does not have a path between them, locations p2_0 and s0 does not have a link between them, locations p3_0 and p2_0 does not have a link between them, locations p3_0 and s0 does not have a link between them, locations p3_0 and s1 does not have a link between them, locations p3_0 and s3 does not have a link between them, locations s0 and p0_1 does not have a link between them, locations s0 and p1_0 does not have a link between them, locations s0 and p1_2 does not have a link between them, locations s0 and p1_3 does not have a path between them, locations s0 and s1 does not have a path between them, locations s0 and s2 does not have a path between them, locations s1 and p0_1 does not have a link between them, locations s1 and p2_0 does not have a link between them, locations s1 and p2_0 does not have a path between them, locations s2 and p1_0 does not have a path between them, locations s2 and p1_3 does not have a link between them, locations s2 and s0 does not have a path between them, locations s2 and s3 does not have a path between them, locations s3 and p1_0 does not have a path between them, locations s3 and p1_2 does not have a path between them, locations s3 and p2_0 does not have a link between them, locations s3 and p2_0 does not have a path between them, locations s3 and s1 does not have a path between them, locations s3 and s2 does not have a link between them, locations s3 and s2 does not have a path between them, package1 is not at location p1_2, package1 is not at location p1_3, package1 is not at location p3_0, package1 is not in truck1, package1 is not in truck3, package1 is not placed in truck2, package1 is not present at location p0_1, package1 is not present at location p1_0, package1 is not present at location p2_0, package1 is not present at location s0, package1 is not present at location s2, package1 is not present at location s3, package2 is not at location p1_3, package2 is not at location p3_0, package2 is not at location s2, package2 is not at location s3, package2 is not currently at location p0_1, package2 is not currently at location p2_0, package2 is not currently at location s1, package2 is not placed in truck1, package2 is not placed in truck2, package2 is not placed in truck3, package2 is not present at location p1_0, package2 is not present at location p1_2, package3 is not at location p1_0, package3 is not at location p1_3, package3 is not at location s3, package3 is not currently at location p0_1, package3 is not currently at location p2_0, package3 is not currently at location s2, package3 is not located in truck3, package3 is not placed in truck1, package3 is not placed in truck2, package3 is not present at location p1_2, package3 is not present at location p3_0, package3 is not present at location s0, package4 is not at location p1_0, package4 is not at location p2_0, package4 is not at location p3_0, package4 is not at location s0, package4 is not currently at location p0_1, package4 is not currently at location p1_2, package4 is not currently at location p1_3, package4 is not currently at location s2, package4 is not located in truck3, package4 is not placed in truck1, package4 is not placed in truck2, package4 is not present at location s3, there doesn't exist a link between the locations p0_1 and p1_0, there doesn't exist a link between the locations p0_1 and p1_2, there doesn't exist a link between the locations p0_1 and p3_0, there doesn't exist a link between the locations p0_1 and s2, there doesn't exist a link between the locations p1_2 and p0_1, there doesn't exist a link between the locations p1_2 and p1_3, there doesn't exist a link between the locations p1_2 and p2_0, there doesn't exist a link between the locations p1_2 and s0, there doesn't exist a link between the locations p1_2 and s3, there doesn't exist a link between the locations p1_3 and p1_2, there doesn't exist a link between the locations p1_3 and p2_0, there doesn't exist a link between the locations p1_3 and s0, there doesn't exist a link between the locations p1_3 and s3, there doesn't exist a link between the locations p2_0 and s2, there doesn't exist a link between the locations p2_0 and s3, there doesn't exist a link between the locations p3_0 and p1_0, there doesn't exist a link between the locations s0 and p1_3, there doesn't exist a link between the locations s0 and p3_0, there doesn't exist a link between the locations s1 and p3_0, there doesn't exist a link between the locations s2 and p1_0, there doesn't exist a link between the locations s2 and p1_2, there doesn't exist a link between the locations s3 and p1_3, there doesn't exist a path between the locations p0_1 and p1_0, there doesn't exist a path between the locations p0_1 and p1_3, there doesn't exist a path between the locations p0_1 and p3_0, there doesn't exist a path between the locations p0_1 and s3, there doesn't exist a path between the locations p1_0 and p3_0, there doesn't exist a path between the locations p1_0 and s1, there doesn't exist a path between the locations p1_2 and p0_1, there doesn't exist a path between the locations p1_2 and p1_0, there doesn't exist a path between the locations p1_2 and p1_3, there doesn't exist a path between the locations p1_2 and s3, there doesn't exist a path between the locations p1_3 and p0_1, there doesn't exist a path between the locations p1_3 and p2_0, there doesn't exist a path between the locations p1_3 and s0, there doesn't exist a path between the locations p2_0 and p0_1, there doesn't exist a path between the locations p2_0 and s1, there doesn't exist a path between the locations p2_0 and s3, there doesn't exist a path between the locations p3_0 and p0_1, there doesn't exist a path between the locations p3_0 and p1_0, there doesn't exist a path between the locations p3_0 and s1, there doesn't exist a path between the locations s0 and p1_0, there doesn't exist a path between the locations s0 and s3, there doesn't exist a path between the locations s1 and s0, there doesn't exist a path between the locations s1 and s2, there doesn't exist a path between the locations s2 and p0_1, there doesn't exist a path between the locations s2 and p3_0, there doesn't exist a path between the locations s2 and s1, there doesn't exist a path between the locations s3 and p0_1, there doesn't exist a path between the locations s3 and s0, there is no link between location p0_1 and location s1, there is no link between location p0_1 and location s3, there is no link between location p1_0 and location p2_0, there is no link between location p1_0 and location p3_0, there is no link between location p1_0 and location s3, there is no link between location p1_2 and location s2, there is no link between location p1_3 and location p0_1, there is no link between location p1_3 and location p1_0, there is no link between location p1_3 and location p3_0, there is no link between location p1_3 and location s1, there is no link between location p2_0 and location p1_0, there is no link between location p2_0 and location s1, there is no link between location p3_0 and location p0_1, there is no link between location p3_0 and location p1_2, there is no link between location p3_0 and location p1_3, there is no link between location p3_0 and location s2, there is no link between location s0 and location p2_0, there is no link between location s1 and location p1_0, there is no link between location s1 and location p1_2, there is no link between location s1 and location p1_3, there is no link between location s2 and location p0_1, there is no link between location s2 and location p2_0, there is no link between location s2 and location p3_0, there is no link between location s2 and location s3, there is no link between location s3 and location p0_1, there is no link between location s3 and location p1_0, there is no link between location s3 and location p1_2, there is no link between location s3 and location p3_0, there is no path between location p0_1 and location s2, there is no path between location p1_0 and location p1_2, there is no path between location p1_0 and location p1_3, there is no path between location p1_0 and location p2_0, there is no path between location p1_2 and location p2_0, there is no path between location p1_2 and location p3_0, there is no path between location p1_2 and location s0, there is no path between location p1_3 and location p1_2, there is no path between location p1_3 and location p3_0, there is no path between location p2_0 and location p1_0, there is no path between location p2_0 and location p1_3, there is no path between location p3_0 and location p1_2, there is no path between location p3_0 and location p1_3, there is no path between location p3_0 and location p2_0, there is no path between location p3_0 and location s2, there is no path between location s0 and location p1_2, there is no path between location s1 and location p1_0, there is no path between location s1 and location p3_0, there is no path between location s1 and location s3, there is no path between location s2 and location p1_3, truck1 is not at location p1_0, truck1 is not at location p1_2, truck1 is not at location p1_3, truck1 is not at location p3_0, truck1 is not at location s0, truck1 is not at location s2, truck1 is not at location s3, truck1 is not being driven by driver2, truck1 is not currently at location p0_1, truck1 is not currently at location p2_0, truck2 is not at location p1_0, truck2 is not at location p3_0, truck2 is not at location s2, truck2 is not being driven by driver1, truck2 is not currently at location p0_1, truck2 is not currently at location p1_3, truck2 is not present at location p1_2, truck2 is not present at location p2_0, truck2 is not present at location s0, truck2 is not present at location s1, truck3 is not at location p1_0, truck3 is not at location p1_3, truck3 is not at location s0, truck3 is not currently at location p1_2, truck3 is not currently at location p3_0, truck3 is not currently at location s3, truck3 is not empty, truck3 is not present at location p0_1, truck3 is not present at location p2_0 and truck3 is not present at location s1", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s1, driver2 is present at location s3, driver3 is at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations p2_0 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p2_0 have a path between them, locations s0 and s2 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and s3 have a link between them, locations s2 and p1_2 have a path between them, locations s2 and s1 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is present at location s3, package2 is present at location s2, package3 is at location s2, package4 is present at location s1, there exists a link between the locations s0 and s3, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p2_0, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p3_0 and location s0, there is a path between location s0 and location p0_1, there is a path between location s1 and location p0_1, truck1 contains nothing, truck1 is present at location s0, truck2 contains nothing, truck2 is at location s3, truck3 is at location s0 and truck3 is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver2 moves from location s3 to p3_0, then from p3_0 to s0, boards truck1 at s0, drives truck1 from s0 to s2, loads truck1 with package3 and package2 at s2, drives truck1 back from s2 to s0, unloads package2 from truck1 at s0, drives truck1 from s0 to s3, loads truck1 with package1 at s3, drives truck1 from s3 to s1, disembarks from truck1 at s1, walks from s1 to p0_1 and then to s0, boards truck3 at s0, drives truck3 from s0 to s2, unloads package3 and package1 from truck1 at s1, and driver3 walks from s3 to p3_0 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is currently located at s1, while driver2 and driver3 are both at location s3. There are paths connecting p0_1 to s1, p1_2 to both s1 and s2, p1_3 to s3, p2_0 to both s0 and s2, and p3_0 to s3. Additionally, paths exist between s0 and p2_0, s1 and p1_2, s1 and p1_3, s2 and p1_2, s3 and p3_0, s0 and p3_0, s2 and p2_0, and s3 and p1_3. Furthermore, links are present between s0 and s2, s1 and s3, s2 and s1, s3 and s0, s0 and s1, s1 and s0, and s0 and s3. Package1 is located at s3, package2 and package3 are at s2, and package4 is at s1. The locations s0 and s3, s1 and s2, and s2 and s0 are all connected by links. Moreover, paths exist between s0 and p0_1, s1 and p0_1, s0 and p3_0, and s1 and p1_3. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and at location s0."}
{"question_id": "4ce33538-7595-4ccc-a31c-97c58fdf0720", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1, driver1 walks from location s2 to location p0_2, driver1 walks from location p0_2 to location s0, driver1 boards truck1 at location s0, driver1 drives truck1 to location s3 from location s0, driver1 disembarks from truck1 at location s3, at location s3, package3 is unloaded in truck1, driver1 walks to location p0_3 from location s3, driver1 walks from location p0_3 to s0 and driver2 walks to location p0_2 from location s2 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "driver1 is present at location s0, driver2 is currently at location p0_2, locations p0_1 and s1 have a path between them, locations p0_2 and s2 have a path between them, locations p0_3 and s3 have a path between them, locations p1_3 and s1 have a path between them, locations s0 and s2 have a link between them, locations s1 and s2 have a link between them, locations s1 and s3 have a link between them, locations s2 and p0_2 have a path between them, locations s2 and p2_1 have a path between them, locations s2 and s0 have a link between them, locations s3 and p1_3 have a path between them, locations s3 and s2 have a link between them, package1 is currently at location s1, package2 is present at location s2, package3 is currently at location s3, package4 is at location s2, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p0_3 and s0, there exists a path between the locations p1_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations p2_1 and s2, there exists a path between the locations s0 and p0_3, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_3, there exists a path between the locations s1 and p2_1, there is a link between location s0 and location s3, there is a link between location s2 and location s1, there is a link between location s3 and location s0, there is a path between location p0_1 and location s0, there is a path between location p0_2 and location s0, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_2, there is a path between location s3 and location p0_3, truck1 contains nothing, truck1 is present at location s3, truck2 is at location s0 and truck2 is empty", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s2, driver2 is at location s2, locations p0_2 and s0 have a path between them, locations p0_2 and s2 have a path between them, locations p0_3 and s0 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and s2 have a link between them, locations s2 and p0_2 have a path between them, locations s2 and s3 have a link between them, package1 is at location s1, package2 is present at location s2, package3 is present at location s0, package4 is at location s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s3 and s0, there exists a link between the locations s3 and s1, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s3, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a link between location s2 and location s1, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s3, there is a path between location s0 and location p0_2, there is a path between location s1 and location p1_3, there is a path between location s1 and location p2_1, there is a path between location s3 and location p0_3, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing and truck2 is currently at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: at location s0, truck1 is loaded with package3, driver1 moves from location s2 to p0_2, then proceeds from p0_2 to s0, boards truck1 at s0, drives truck1 from s0 to s3, disembarks from truck1 at s3, unloads package3 from truck1 at s3, walks from s3 to p0_3, and then from p0_3 back to s0, while driver2 moves from s2 to p0_2 to reach the current state. In this state, list all valid properties that do not involve negations. Write None if there are none.", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also at s2. A path exists between locations p0_2 and s0, as well as between p0_2 and s2. Additionally, a path is present between p0_3 and s0, and between p2_1 and s2. Furthermore, a path connects s0 to p0_1 and s0 to p0_3. Locations s0 and s1 are linked, as are s0 and s3, s1 and s2, s2 and s3. Package1 is situated at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. Links exist between s1 and s3, s2 and s0, s3 and s0, s3 and s1, and s3 and s2. Paths are present between p0_1 and s1, p0_3 and s3, p1_3 and s1, p2_1 and s1, s1 and p0_1, s2 and p2_1, and s3 and p1_3. There are links between s0 and s2, s1 and s0, and s2 and s1. Paths also exist between p0_1 and s0, p1_3 and s3, s0 and p0_2, s1 and p1_3, s1 and p2_1, and s3 and p0_3. Currently, truck1 is at location s0 and is empty, while truck2 is also at s0 and contains nothing."}
{"question_id": "4ba19f47-b343-437e-8efa-193d2e22deb1", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to location p3_0, driver2 walks from location p3_0 to s0, truck1 is boarded by driver2 at location s0, truck1 is driven from location s0 to s2 by driver2, package3 is loaded in truck1 at location s2, truck1 is loaded with package2 at location s2, truck1 is driven from location s2 to s0 by driver2, truck1 is unloaded with package2 at location s0, driver2 drives truck1 to location s3 from location s0 and at location s3, package1 is loaded in truck1 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "driver1 is not currently at location p0_1, driver1 is not currently at location p1_0, driver1 is not currently at location p1_2, driver1 is not currently at location p1_3, driver1 is not currently at location s0, driver1 is not currently at location s3, driver1 is not driving truck2, driver1 is not present at location p2_0, driver1 is not present at location p3_0, driver1 is not present at location s2, driver2 is not at location p1_0, driver2 is not at location p2_0, driver2 is not at location s0, driver2 is not at location s1, driver2 is not currently at location p0_1, driver2 is not currently at location p3_0, driver2 is not driving truck3 currently, driver2 is not present at location p1_2, driver2 is not present at location p1_3, driver2 is not present at location s2, driver2 is not present at location s3, driver3 is not at location p2_0, driver3 is not currently at location p1_0, driver3 is not currently at location p3_0, driver3 is not currently at location s0, driver3 is not currently at location s2, driver3 is not driving truck1, driver3 is not present at location p0_1, driver3 is not present at location p1_2, driver3 is not present at location p1_3, driver3 is not present at location s1, locations p0_1 and p1_0 does not have a link between them, locations p0_1 and p3_0 does not have a path between them, locations p0_1 and s2 does not have a path between them, locations p1_0 and p0_1 does not have a link between them, locations p1_0 and p1_3 does not have a link between them, locations p1_0 and p2_0 does not have a path between them, locations p1_0 and p3_0 does not have a link between them, locations p1_0 and s3 does not have a link between them, locations p1_0 and s3 does not have a path between them, locations p1_2 and p1_0 does not have a path between them, locations p1_2 and p1_3 does not have a path between them, locations p1_2 and p2_0 does not have a link between them, locations p1_2 and p3_0 does not have a link between them, locations p1_2 and p3_0 does not have a path between them, locations p1_2 and s1 does not have a link between them, locations p1_2 and s2 does not have a link between them, locations p1_2 and s3 does not have a link between them, locations p1_3 and p0_1 does not have a link between them, locations p1_3 and p1_2 does not have a path between them, locations p1_3 and p3_0 does not have a path between them, locations p1_3 and s1 does not have a link between them, locations p1_3 and s2 does not have a link between them, locations p2_0 and p0_1 does not have a path between them, locations p2_0 and p1_0 does not have a link between them, locations p2_0 and p1_2 does not have a link between them, locations p3_0 and p1_0 does not have a path between them, locations p3_0 and p2_0 does not have a path between them, locations p3_0 and s1 does not have a path between them, locations s0 and p1_2 does not have a link between them, locations s0 and p1_2 does not have a path between them, locations s0 and p2_0 does not have a link between them, locations s0 and s1 does not have a path between them, locations s1 and p3_0 does not have a link between them, locations s1 and s2 does not have a path between them, locations s2 and p0_1 does not have a link between them, locations s2 and p1_2 does not have a link between them, locations s2 and p3_0 does not have a link between them, locations s2 and s0 does not have a path between them, locations s2 and s1 does not have a path between them, locations s2 and s3 does not have a link between them, locations s3 and p1_2 does not have a link between them, locations s3 and p1_2 does not have a path between them, locations s3 and p3_0 does not have a link between them, package1 is not at location p1_2, package1 is not at location p1_3, package1 is not at location p3_0, package1 is not at location s0, package1 is not currently at location p0_1, package1 is not currently at location p1_0, package1 is not currently at location s1, package1 is not currently at location s2, package1 is not located in truck3, package1 is not placed in truck2, package1 is not present at location p2_0, package1 is not present at location s3, package2 is not at location p1_2, package2 is not at location p2_0, package2 is not at location p3_0, package2 is not at location s2, package2 is not currently at location p1_0, package2 is not currently at location s3, package2 is not in truck1, package2 is not in truck3, package2 is not located in truck2, package2 is not present at location p0_1, package2 is not present at location p1_3, package2 is not present at location s1, package3 is not at location p1_2, package3 is not at location p1_3, package3 is not at location p3_0, package3 is not at location s0, package3 is not at location s2, package3 is not currently at location p0_1, package3 is not currently at location p1_0, package3 is not located in truck2, package3 is not located in truck3, package3 is not present at location p2_0, package3 is not present at location s1, package3 is not present at location s3, package4 is not at location p1_0, package4 is not at location s0, package4 is not at location s3, package4 is not currently at location p1_2, package4 is not currently at location p1_3, package4 is not currently at location s2, package4 is not in truck1, package4 is not located in truck2, package4 is not placed in truck3, package4 is not present at location p0_1, package4 is not present at location p2_0, package4 is not present at location p3_0, there doesn't exist a link between the locations p0_1 and p1_2, there doesn't exist a link between the locations p0_1 and p1_3, there doesn't exist a link between the locations p1_0 and p1_2, there doesn't exist a link between the locations p1_0 and p2_0, there doesn't exist a link between the locations p1_0 and s0, there doesn't exist a link between the locations p1_0 and s1, there doesn't exist a link between the locations p1_0 and s2, there doesn't exist a link between the locations p1_2 and p0_1, there doesn't exist a link between the locations p1_2 and s0, there doesn't exist a link between the locations p1_3 and p2_0, there doesn't exist a link between the locations p1_3 and p3_0, there doesn't exist a link between the locations p2_0 and p1_3, there doesn't exist a link between the locations p2_0 and s3, there doesn't exist a link between the locations p3_0 and p1_0, there doesn't exist a link between the locations p3_0 and p1_3, there doesn't exist a link between the locations p3_0 and s3, there doesn't exist a link between the locations s0 and p0_1, there doesn't exist a link between the locations s0 and p1_3, there doesn't exist a link between the locations s1 and p1_3, there doesn't exist a link between the locations s1 and p2_0, there doesn't exist a link between the locations s3 and p1_0, there doesn't exist a link between the locations s3 and p1_3, there doesn't exist a link between the locations s3 and p2_0, there doesn't exist a link between the locations s3 and s2, there doesn't exist a path between the locations p0_1 and p2_0, there doesn't exist a path between the locations p0_1 and s3, there doesn't exist a path between the locations p1_0 and p1_2, there doesn't exist a path between the locations p1_0 and p1_3, there doesn't exist a path between the locations p1_0 and p3_0, there doesn't exist a path between the locations p1_0 and s1, there doesn't exist a path between the locations p1_2 and p0_1, there doesn't exist a path between the locations p1_2 and p2_0, there doesn't exist a path between the locations p1_3 and p1_0, there doesn't exist a path between the locations p1_3 and s0, there doesn't exist a path between the locations p1_3 and s2, there doesn't exist a path between the locations p2_0 and s1, there doesn't exist a path between the locations p2_0 and s3, there doesn't exist a path between the locations p3_0 and s2, there doesn't exist a path between the locations s0 and s3, there doesn't exist a path between the locations s1 and p2_0, there doesn't exist a path between the locations s1 and s3, there doesn't exist a path between the locations s2 and p1_0, there doesn't exist a path between the locations s2 and s3, there doesn't exist a path between the locations s3 and p0_1, there doesn't exist a path between the locations s3 and p1_0, there doesn't exist a path between the locations s3 and p2_0, there doesn't exist a path between the locations s3 and s0, there doesn't exist a path between the locations s3 and s1, there is no link between location p0_1 and location p2_0, there is no link between location p0_1 and location p3_0, there is no link between location p0_1 and location s0, there is no link between location p0_1 and location s1, there is no link between location p0_1 and location s2, there is no link between location p0_1 and location s3, there is no link between location p1_2 and location p1_0, there is no link between location p1_2 and location p1_3, there is no link between location p1_3 and location p1_0, there is no link between location p1_3 and location p1_2, there is no link between location p1_3 and location s0, there is no link between location p1_3 and location s3, there is no link between location p2_0 and location p0_1, there is no link between location p2_0 and location p3_0, there is no link between location p2_0 and location s0, there is no link between location p2_0 and location s1, there is no link between location p2_0 and location s2, there is no link between location p3_0 and location p0_1, there is no link between location p3_0 and location p1_2, there is no link between location p3_0 and location p2_0, there is no link between location p3_0 and location s0, there is no link between location p3_0 and location s1, there is no link between location p3_0 and location s2, there is no link between location s0 and location p1_0, there is no link between location s0 and location p3_0, there is no link between location s1 and location p0_1, there is no link between location s1 and location p1_0, there is no link between location s1 and location p1_2, there is no link between location s2 and location p1_0, there is no link between location s2 and location p1_3, there is no link between location s2 and location p2_0, there is no link between location s3 and location p0_1, there is no path between location p0_1 and location p1_0, there is no path between location p0_1 and location p1_2, there is no path between location p0_1 and location p1_3, there is no path between location p1_0 and location p0_1, there is no path between location p1_0 and location s0, there is no path between location p1_0 and location s2, there is no path between location p1_2 and location s0, there is no path between location p1_2 and location s3, there is no path between location p1_3 and location p0_1, there is no path between location p1_3 and location p2_0, there is no path between location p2_0 and location p1_0, there is no path between location p2_0 and location p1_2, there is no path between location p2_0 and location p1_3, there is no path between location p2_0 and location p3_0, there is no path between location p3_0 and location p0_1, there is no path between location p3_0 and location p1_2, there is no path between location p3_0 and location p1_3, there is no path between location s0 and location p1_0, there is no path between location s0 and location p1_3, there is no path between location s0 and location s2, there is no path between location s1 and location p1_0, there is no path between location s1 and location p3_0, there is no path between location s1 and location s0, there is no path between location s2 and location p0_1, there is no path between location s2 and location p1_3, there is no path between location s2 and location p3_0, there is no path between location s3 and location s2, truck1 is not at location p0_1, truck1 is not at location p1_3, truck1 is not at location s0, truck1 is not being driven by driver1, truck1 is not currently at location p2_0, truck1 is not currently at location p3_0, truck1 is not currently at location s1, truck1 is not currently at location s2, truck1 is not empty, truck1 is not present at location p1_0, truck1 is not present at location p1_2, truck2 is not at location p1_0, truck2 is not at location p1_2, truck2 is not at location p3_0, truck2 is not being driven by driver2, truck2 is not being driven by driver3, truck2 is not currently at location s0, truck2 is not currently at location s2, truck2 is not present at location p0_1, truck2 is not present at location p1_3, truck2 is not present at location p2_0, truck2 is not present at location s1, truck3 is not at location p0_1, truck3 is not at location p1_0, truck3 is not at location p2_0, truck3 is not at location s2, truck3 is not being driven by driver1, truck3 is not being driven by driver3, truck3 is not currently at location p3_0, truck3 is not currently at location s1, truck3 is not present at location p1_2, truck3 is not present at location p1_3 and truck3 is not present at location s3", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s1, driver2 is present at location s3, driver3 is at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations p2_0 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p2_0 have a path between them, locations s0 and s2 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and s3 have a link between them, locations s2 and p1_2 have a path between them, locations s2 and s1 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is present at location s3, package2 is present at location s2, package3 is at location s2, package4 is present at location s1, there exists a link between the locations s0 and s3, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p2_0, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p3_0 and location s0, there is a path between location s0 and location p0_1, there is a path between location s1 and location p0_1, truck1 contains nothing, truck1 is present at location s0, truck2 contains nothing, truck2 is at location s3, truck3 is at location s0 and truck3 is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver2 moves from location s3 to p3_0, then from p3_0 to s0, boards truck1 at s0, drives truck1 from s0 to s2, loads package3 into truck1 at s2, also loads package2 into truck1 at s2, drives truck1 back from s2 to s0, unloads package2 from truck1 at s0, drives truck1 from s0 to s3, and finally loads package1 into truck1 at s3 to reach the current state. In this state, list all valid properties that involve negations; if there are none, indicate None.", "initial_state_nl_paraphrased": "Driver1 is currently located at s1, while driver2 and driver3 are both at location s3. There are paths connecting p0_1 and s1, p1_2 and s1, p1_2 and s2, p1_3 and s3, p2_0 and s0, p2_0 and s2, p3_0 and s3, s0 and p2_0, s1 and p1_2, s1 and p1_3, s2 and p1_2, s3 and p3_0. Additionally, there are links between s0 and s2, s1 and s3, s2 and s1, s3 and s0, s0 and s1, and s1 and s0. Package1 is located at s3, package2 and package3 are at s2, and package4 is at s1. Furthermore, links exist between s0 and s3, s1 and s2, s2 and s0, and paths exist between s0 and p3_0, s2 and p2_0, s3 and p1_3. Also, there are paths between p0_1 and s0, p1_3 and s1, p3_0 and s0, s0 and p0_1, s1 and p0_1. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and at location s0."}
{"question_id": "103c8c41-8b90-4ffc-a124-cb0cf10da0d1", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks to location p3_0 from location s3 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "driver1 is currently at location s1, driver1 is not at location p2_0, driver1 is not at location p3_0, driver1 is not at location s0, driver1 is not currently at location p1_0, driver1 is not currently at location p1_2, driver1 is not currently at location p1_3, driver1 is not driving truck1 currently, driver1 is not driving truck3, driver1 is not present at location p0_1, driver1 is not present at location s2, driver1 is not present at location s3, driver2 is currently at location p3_0, driver2 is not at location p1_0, driver2 is not at location s1, driver2 is not at location s2, driver2 is not currently at location p1_2, driver2 is not currently at location p1_3, driver2 is not currently at location s0, driver2 is not driving truck1, driver2 is not driving truck2 currently, driver2 is not driving truck3 currently, driver2 is not present at location p0_1, driver2 is not present at location p2_0, driver2 is not present at location s3, driver3 is not at location p3_0, driver3 is not currently at location p0_1, driver3 is not currently at location p1_0, driver3 is not currently at location p1_2, driver3 is not currently at location p1_3, driver3 is not currently at location p2_0, driver3 is not currently at location s2, driver3 is not driving truck1, driver3 is not driving truck3 currently, driver3 is not present at location s0, driver3 is not present at location s1, driver3 is present at location s3, locations p0_1 and p1_0 does not have a path between them, locations p0_1 and p2_0 does not have a link between them, locations p0_1 and p2_0 does not have a path between them, locations p0_1 and s0 does not have a link between them, locations p0_1 and s2 does not have a link between them, locations p1_0 and p0_1 does not have a path between them, locations p1_0 and s0 does not have a link between them, locations p1_0 and s1 does not have a path between them, locations p1_0 and s2 does not have a link between them, locations p1_0 and s3 does not have a path between them, locations p1_2 and p0_1 does not have a link between them, locations p1_2 and p1_3 does not have a path between them, locations p1_2 and p2_0 does not have a link between them, locations p1_2 and s0 does not have a link between them, locations p1_2 and s1 have a path between them, locations p1_2 and s3 does not have a link between them, locations p1_2 and s3 does not have a path between them, locations p1_3 and p0_1 does not have a path between them, locations p1_3 and p1_2 does not have a link between them, locations p1_3 and p3_0 does not have a link between them, locations p1_3 and s0 does not have a link between them, locations p1_3 and s2 does not have a link between them, locations p1_3 and s3 have a path between them, locations p2_0 and p0_1 does not have a link between them, locations p2_0 and p0_1 does not have a path between them, locations p2_0 and p1_0 does not have a link between them, locations p2_0 and p1_0 does not have a path between them, locations p2_0 and p1_2 does not have a path between them, locations p2_0 and p3_0 does not have a link between them, locations p2_0 and s1 does not have a path between them, locations p3_0 and p1_0 does not have a path between them, locations p3_0 and s2 does not have a link between them, locations p3_0 and s3 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p1_0 does not have a path between them, locations s0 and s1 does not have a path between them, locations s0 and s1 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_0 does not have a path between them, locations s1 and p3_0 does not have a link between them, locations s1 and s0 have a link between them, locations s2 and p0_1 does not have a path between them, locations s2 and p1_0 does not have a link between them, locations s2 and p1_3 does not have a path between them, locations s2 and p2_0 does not have a link between them, locations s2 and p3_0 does not have a path between them, locations s2 and s0 have a link between them, locations s2 and s1 have a link between them, locations s2 and s3 does not have a link between them, locations s3 and p0_1 does not have a link between them, locations s3 and p1_0 does not have a path between them, locations s3 and p1_3 have a path between them, locations s3 and p2_0 does not have a link between them, locations s3 and s0 does not have a path between them, locations s3 and s1 does not have a path between them, locations s3 and s2 does not have a path between them, package1 is at location s3, package1 is not at location p0_1, package1 is not at location s2, package1 is not currently at location p1_2, package1 is not currently at location p1_3, package1 is not currently at location p2_0, package1 is not currently at location s1, package1 is not located in truck1, package1 is not placed in truck2, package1 is not placed in truck3, package1 is not present at location p1_0, package1 is not present at location p3_0, package1 is not present at location s0, package2 is at location s2, package2 is not at location s0, package2 is not at location s1, package2 is not at location s3, package2 is not currently at location p1_0, package2 is not currently at location p1_2, package2 is not currently at location p1_3, package2 is not currently at location p2_0, package2 is not in truck2, package2 is not in truck3, package2 is not located in truck1, package2 is not present at location p0_1, package2 is not present at location p3_0, package3 is currently at location s2, package3 is not at location p0_1, package3 is not at location p1_0, package3 is not at location p2_0, package3 is not at location s1, package3 is not currently at location p1_3, package3 is not currently at location p3_0, package3 is not currently at location s0, package3 is not in truck1, package3 is not located in truck2, package3 is not placed in truck3, package3 is not present at location p1_2, package3 is not present at location s3, package4 is at location s1, package4 is not at location p0_1, package4 is not at location p1_0, package4 is not at location p1_3, package4 is not at location s0, package4 is not at location s3, package4 is not currently at location p1_2, package4 is not currently at location p2_0, package4 is not currently at location p3_0, package4 is not in truck3, package4 is not located in truck1, package4 is not located in truck2, package4 is not present at location s2, there doesn't exist a link between the locations p0_1 and p1_0, there doesn't exist a link between the locations p0_1 and p1_3, there doesn't exist a link between the locations p0_1 and p3_0, there doesn't exist a link between the locations p0_1 and s3, there doesn't exist a link between the locations p1_0 and p0_1, there doesn't exist a link between the locations p1_0 and p1_3, there doesn't exist a link between the locations p1_0 and p3_0, there doesn't exist a link between the locations p1_0 and s1, there doesn't exist a link between the locations p1_0 and s3, there doesn't exist a link between the locations p1_2 and p1_3, there doesn't exist a link between the locations p1_2 and p3_0, there doesn't exist a link between the locations p1_3 and p1_0, there doesn't exist a link between the locations p1_3 and p2_0, there doesn't exist a link between the locations p1_3 and s1, there doesn't exist a link between the locations p2_0 and p1_2, there doesn't exist a link between the locations p2_0 and p1_3, there doesn't exist a link between the locations p2_0 and s0, there doesn't exist a link between the locations p2_0 and s1, there doesn't exist a link between the locations p3_0 and p0_1, there doesn't exist a link between the locations p3_0 and p1_0, there doesn't exist a link between the locations p3_0 and s0, there doesn't exist a link between the locations p3_0 and s1, there doesn't exist a link between the locations p3_0 and s3, there doesn't exist a link between the locations s0 and p0_1, there doesn't exist a link between the locations s0 and p1_0, there doesn't exist a link between the locations s0 and p2_0, there doesn't exist a link between the locations s0 and p3_0, there doesn't exist a link between the locations s1 and p0_1, there doesn't exist a link between the locations s1 and p1_3, there doesn't exist a link between the locations s1 and p2_0, there doesn't exist a link between the locations s2 and p0_1, there doesn't exist a link between the locations s3 and p3_0, there doesn't exist a link between the locations s3 and s2, there doesn't exist a path between the locations p0_1 and p1_3, there doesn't exist a path between the locations p0_1 and s2, there doesn't exist a path between the locations p1_2 and p0_1, there doesn't exist a path between the locations p1_3 and p2_0, there doesn't exist a path between the locations p1_3 and s0, there doesn't exist a path between the locations p1_3 and s2, there doesn't exist a path between the locations p2_0 and p1_3, there doesn't exist a path between the locations p2_0 and p3_0, there doesn't exist a path between the locations p3_0 and p1_2, there doesn't exist a path between the locations p3_0 and s1, there doesn't exist a path between the locations s0 and p1_2, there doesn't exist a path between the locations s0 and s2, there doesn't exist a path between the locations s1 and p2_0, there doesn't exist a path between the locations s1 and p3_0, there doesn't exist a path between the locations s1 and s2, there doesn't exist a path between the locations s2 and p1_0, there doesn't exist a path between the locations s2 and s0, there doesn't exist a path between the locations s2 and s3, there doesn't exist a path between the locations s3 and p0_1, there doesn't exist a path between the locations s3 and p1_2, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s2, there exists a link between the locations s3 and s0, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations p3_0 and s0, there exists a path between the locations s0 and p2_0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s1 and p1_3, there exists a path between the locations s3 and p3_0, there is a link between location s0 and location s3, there is a link between location s1 and location s3, there is a path between location p0_1 and location s1, there is a path between location p1_2 and location s2, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location s1 and location p1_2, there is a path between location s2 and location p1_2, there is a path between location s2 and location p2_0, there is no link between location p0_1 and location p1_2, there is no link between location p0_1 and location s1, there is no link between location p1_0 and location p1_2, there is no link between location p1_0 and location p2_0, there is no link between location p1_2 and location p1_0, there is no link between location p1_2 and location s1, there is no link between location p1_2 and location s2, there is no link between location p1_3 and location p0_1, there is no link between location p1_3 and location s3, there is no link between location p2_0 and location s2, there is no link between location p2_0 and location s3, there is no link between location p3_0 and location p1_2, there is no link between location p3_0 and location p1_3, there is no link between location p3_0 and location p2_0, there is no link between location s0 and location p1_2, there is no link between location s0 and location p1_3, there is no link between location s1 and location p1_0, there is no link between location s1 and location p1_2, there is no link between location s2 and location p1_2, there is no link between location s2 and location p1_3, there is no link between location s2 and location p3_0, there is no link between location s3 and location p1_0, there is no link between location s3 and location p1_2, there is no link between location s3 and location p1_3, there is no path between location p0_1 and location p1_2, there is no path between location p0_1 and location p3_0, there is no path between location p0_1 and location s3, there is no path between location p1_0 and location p1_2, there is no path between location p1_0 and location p1_3, there is no path between location p1_0 and location p2_0, there is no path between location p1_0 and location p3_0, there is no path between location p1_0 and location s0, there is no path between location p1_0 and location s2, there is no path between location p1_2 and location p1_0, there is no path between location p1_2 and location p2_0, there is no path between location p1_2 and location p3_0, there is no path between location p1_2 and location s0, there is no path between location p1_3 and location p1_0, there is no path between location p1_3 and location p1_2, there is no path between location p1_3 and location p3_0, there is no path between location p2_0 and location s3, there is no path between location p3_0 and location p0_1, there is no path between location p3_0 and location p1_3, there is no path between location p3_0 and location p2_0, there is no path between location p3_0 and location s2, there is no path between location s0 and location p1_3, there is no path between location s0 and location s3, there is no path between location s1 and location s0, there is no path between location s1 and location s3, there is no path between location s2 and location s1, there is no path between location s3 and location p2_0, truck1 contains nothing, truck1 is not at location p0_1, truck1 is not at location p1_3, truck1 is not at location p3_0, truck1 is not at location s1, truck1 is not at location s3, truck1 is not currently at location p1_2, truck1 is not currently at location s2, truck1 is not present at location p1_0, truck1 is not present at location p2_0, truck1 is present at location s0, truck2 is at location s3, truck2 is empty, truck2 is not at location p1_2, truck2 is not at location s2, truck2 is not being driven by driver1, truck2 is not being driven by driver3, truck2 is not currently at location p1_0, truck2 is not currently at location p1_3, truck2 is not currently at location p3_0, truck2 is not currently at location s1, truck2 is not present at location p0_1, truck2 is not present at location p2_0, truck2 is not present at location s0, truck3 contains nothing, truck3 is currently at location s0, truck3 is not at location p3_0, truck3 is not currently at location p0_1, truck3 is not currently at location p1_2, truck3 is not currently at location p2_0, truck3 is not currently at location s2, truck3 is not currently at location s3, truck3 is not present at location p1_0, truck3 is not present at location p1_3 and truck3 is not present at location s1", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s1, driver2 is present at location s3, driver3 is at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations p2_0 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p2_0 have a path between them, locations s0 and s2 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and s3 have a link between them, locations s2 and p1_2 have a path between them, locations s2 and s1 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is present at location s3, package2 is present at location s2, package3 is at location s2, package4 is present at location s1, there exists a link between the locations s0 and s3, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p2_0, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p3_0 and location s0, there is a path between location s0 and location p0_1, there is a path between location s1 and location p0_1, truck1 contains nothing, truck1 is present at location s0, truck2 contains nothing, truck2 is at location s3, truck3 is at location s0 and truck3 is empty.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are taken: driver2 moves from location s3 to location p3_0 to achieve the current state. In this state, list all applicable properties (including both affirmative and negated properties). If none exist, indicate None.", "initial_state_nl_paraphrased": "Driver1 is currently located at s1, while driver2 and driver3 are both at location s3. There are paths connecting p0_1 and s1, p1_2 and s1, p1_2 and s2, p1_3 and s3, p2_0 and s0, p2_0 and s2, p3_0 and s3, s0 and p2_0, s1 and p1_2, s1 and p1_3, s2 and p1_2, s3 and p3_0. Additionally, there are links between s0 and s2, s1 and s3, s2 and s1, s3 and s0, s0 and s1, and s1 and s0. Package1 is located at s3, package2 and package3 are at s2, and package4 is at s1. Furthermore, links exist between s0 and s3, s1 and s2, s2 and s0, and paths exist between s0 and p3_0, s2 and p2_0, s3 and p1_3. Also, there are links between s0 and s1, s1 and s0, and paths between p0_1 and s0, p1_3 and s1, p3_0 and s0, s0 and p0_1, s1 and p0_1. The current status of the trucks is as follows: truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and at s0."}
{"question_id": "94aa1b7c-68ed-4a04-af07-971aa17c0e70", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: package3 is loaded in truck1 at location s0, driver1 walks from location s2 to location p0_2, driver1 walks from location p0_2 to location s0, at location s0, driver1 boards truck1, driver1 drives truck1 to location s3 from location s0, from truck1, driver1 disembarks at location s3, package3 is unloaded from truck1 at location s3, driver1 walks to location p0_3 from location s3, driver1 walks to location s0 from location p0_3 and driver2 walks to location p0_2 from location s2 to reach the current state. In this state, list all valid properties of the state (both with and without negations). Write None if there are none.", "answer": "driver1 is not at location p0_1, driver1 is not at location s1, driver1 is not currently at location p0_2, driver1 is not currently at location p1_3, driver1 is not currently at location p2_1, driver1 is not currently at location s2, driver1 is not driving truck1 currently, driver1 is not driving truck2, driver1 is not present at location p0_3, driver1 is not present at location p3_0, driver1 is not present at location s3, driver1 is present at location s0, driver2 is not at location p0_3, driver2 is not at location p1_3, driver2 is not at location s1, driver2 is not currently at location p3_0, driver2 is not driving truck1 currently, driver2 is not present at location p0_1, driver2 is not present at location p2_1, driver2 is not present at location s0, driver2 is not present at location s2, driver2 is not present at location s3, driver2 is present at location p0_2, locations p0_1 and p0_2 does not have a path between them, locations p0_1 and p2_1 does not have a link between them, locations p0_1 and p3_0 does not have a path between them, locations p0_1 and s0 have a path between them, locations p0_1 and s1 have a path between them, locations p0_1 and s2 does not have a link between them, locations p0_1 and s3 does not have a path between them, locations p0_2 and p0_3 does not have a link between them, locations p0_2 and p0_3 does not have a path between them, locations p0_2 and p2_1 does not have a path between them, locations p0_2 and p3_0 does not have a path between them, locations p0_2 and s0 have a path between them, locations p0_2 and s2 does not have a link between them, locations p0_2 and s3 does not have a path between them, locations p0_3 and p1_3 does not have a link between them, locations p0_3 and p2_1 does not have a path between them, locations p0_3 and s0 does not have a link between them, locations p0_3 and s2 does not have a path between them, locations p0_3 and s3 does not have a link between them, locations p1_3 and p0_1 does not have a link between them, locations p1_3 and p0_1 does not have a path between them, locations p1_3 and p3_0 does not have a link between them, locations p1_3 and s1 does not have a link between them, locations p1_3 and s3 does not have a link between them, locations p2_1 and p0_1 does not have a link between them, locations p2_1 and p0_2 does not have a link between them, locations p2_1 and p0_2 does not have a path between them, locations p2_1 and p0_3 does not have a path between them, locations p2_1 and p1_3 does not have a path between them, locations p2_1 and p3_0 does not have a link between them, locations p2_1 and p3_0 does not have a path between them, locations p2_1 and s0 does not have a link between them, locations p2_1 and s0 does not have a path between them, locations p2_1 and s3 does not have a link between them, locations p2_1 and s3 does not have a path between them, locations p3_0 and p0_3 does not have a path between them, locations p3_0 and p1_3 does not have a path between them, locations s0 and p0_1 does not have a link between them, locations s0 and p2_1 does not have a link between them, locations s0 and p3_0 does not have a link between them, locations s0 and s2 does not have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 does not have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_1 have a path between them, locations s1 and p3_0 does not have a link between them, locations s1 and s0 does not have a path between them, locations s1 and s0 have a link between them, locations s1 and s2 does not have a path between them, locations s1 and s3 does not have a path between them, locations s2 and p0_1 does not have a link between them, locations s2 and s0 does not have a path between them, locations s2 and s3 does not have a path between them, locations s3 and p0_1 does not have a link between them, locations s3 and p0_3 have a path between them, locations s3 and p2_1 does not have a link between them, locations s3 and p3_0 does not have a link between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is at location s1, package1 is not at location p2_1, package1 is not at location p3_0, package1 is not at location s3, package1 is not currently at location p0_2, package1 is not currently at location p0_3, package1 is not currently at location s0, package1 is not currently at location s2, package1 is not placed in truck1, package1 is not placed in truck2, package1 is not present at location p0_1, package1 is not present at location p1_3, package2 is not at location p0_1, package2 is not at location p0_2, package2 is not at location p2_1, package2 is not at location p3_0, package2 is not currently at location p1_3, package2 is not currently at location s0, package2 is not currently at location s1, package2 is not currently at location s3, package2 is not placed in truck1, package2 is not placed in truck2, package2 is not present at location p0_3, package2 is present at location s2, package3 is not at location p1_3, package3 is not at location s0, package3 is not at location s2, package3 is not currently at location p0_1, package3 is not currently at location p0_2, package3 is not currently at location p0_3, package3 is not in truck1, package3 is not located in truck2, package3 is not present at location p2_1, package3 is not present at location p3_0, package3 is not present at location s1, package3 is present at location s3, package4 is at location s2, package4 is not at location s0, package4 is not at location s3, package4 is not currently at location p0_1, package4 is not currently at location p0_2, package4 is not currently at location p0_3, package4 is not currently at location p1_3, package4 is not currently at location p2_1, package4 is not currently at location s1, package4 is not in truck2, package4 is not located in truck1, package4 is not present at location p3_0, there doesn't exist a link between the locations p0_1 and p0_2, there doesn't exist a link between the locations p0_1 and p0_3, there doesn't exist a link between the locations p0_1 and p1_3, there doesn't exist a link between the locations p0_1 and p3_0, there doesn't exist a link between the locations p0_1 and s0, there doesn't exist a link between the locations p0_1 and s3, there doesn't exist a link between the locations p0_2 and p0_1, there doesn't exist a link between the locations p0_2 and s1, there doesn't exist a link between the locations p0_2 and s3, there doesn't exist a link between the locations p0_3 and p0_1, there doesn't exist a link between the locations p0_3 and p0_2, there doesn't exist a link between the locations p0_3 and p2_1, there doesn't exist a link between the locations p0_3 and s2, there doesn't exist a link between the locations p1_3 and p0_2, there doesn't exist a link between the locations p1_3 and p0_3, there doesn't exist a link between the locations p1_3 and s2, there doesn't exist a link between the locations p2_1 and p0_3, there doesn't exist a link between the locations p3_0 and p0_1, there doesn't exist a link between the locations p3_0 and p0_2, there doesn't exist a link between the locations p3_0 and p0_3, there doesn't exist a link between the locations p3_0 and p1_3, there doesn't exist a link between the locations p3_0 and p2_1, there doesn't exist a link between the locations p3_0 and s1, there doesn't exist a link between the locations p3_0 and s2, there doesn't exist a link between the locations p3_0 and s3, there doesn't exist a link between the locations s0 and p0_2, there doesn't exist a link between the locations s0 and p0_3, there doesn't exist a link between the locations s1 and p0_2, there doesn't exist a link between the locations s1 and p0_3, there doesn't exist a link between the locations s1 and p1_3, there doesn't exist a link between the locations s2 and p0_2, there doesn't exist a link between the locations s2 and p1_3, there doesn't exist a link between the locations s2 and p3_0, there doesn't exist a link between the locations s3 and p0_2, there doesn't exist a link between the locations s3 and p0_3, there doesn't exist a link between the locations s3 and p1_3, there doesn't exist a path between the locations p0_1 and p1_3, there doesn't exist a path between the locations p0_1 and p2_1, there doesn't exist a path between the locations p0_2 and p0_1, there doesn't exist a path between the locations p0_3 and p3_0, there doesn't exist a path between the locations p0_3 and s1, there doesn't exist a path between the locations p1_3 and p0_3, there doesn't exist a path between the locations p1_3 and p3_0, there doesn't exist a path between the locations p1_3 and s2, there doesn't exist a path between the locations p2_1 and p0_1, there doesn't exist a path between the locations p3_0 and p0_1, there doesn't exist a path between the locations p3_0 and p0_2, there doesn't exist a path between the locations p3_0 and s1, there doesn't exist a path between the locations p3_0 and s2, there doesn't exist a path between the locations s0 and p1_3, there doesn't exist a path between the locations s0 and p2_1, there doesn't exist a path between the locations s0 and s1, there doesn't exist a path between the locations s1 and p0_3, there doesn't exist a path between the locations s1 and p3_0, there doesn't exist a path between the locations s2 and p0_3, there doesn't exist a path between the locations s2 and p3_0, there doesn't exist a path between the locations s2 and s1, there doesn't exist a path between the locations s3 and p0_1, there doesn't exist a path between the locations s3 and p0_2, there doesn't exist a path between the locations s3 and s0, there doesn't exist a path between the locations s3 and s2, there exists a link between the locations s0 and s1, there exists a link between the locations s2 and s0, there exists a link between the locations s2 and s1, there exists a link between the locations s3 and s2, there exists a path between the locations p0_3 and s0, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p0_3, there exists a path between the locations s1 and p0_1, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s3, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a path between location p0_2 and location s2, there is a path between location p0_3 and location s3, there is a path between location p1_3 and location s1, there is a path between location p1_3 and location s3, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_2, there is a path between location s2 and location p0_2, there is no link between location p0_1 and location s1, there is no link between location p0_2 and location p1_3, there is no link between location p0_2 and location p2_1, there is no link between location p0_2 and location p3_0, there is no link between location p0_2 and location s0, there is no link between location p0_3 and location p3_0, there is no link between location p0_3 and location s1, there is no link between location p1_3 and location p2_1, there is no link between location p1_3 and location s0, there is no link between location p2_1 and location p1_3, there is no link between location p2_1 and location s1, there is no link between location p2_1 and location s2, there is no link between location p3_0 and location s0, there is no link between location s0 and location p1_3, there is no link between location s1 and location p0_1, there is no link between location s1 and location p2_1, there is no link between location s2 and location p0_3, there is no link between location s2 and location p2_1, there is no path between location p0_1 and location p0_3, there is no path between location p0_1 and location s2, there is no path between location p0_2 and location p1_3, there is no path between location p0_2 and location s1, there is no path between location p0_3 and location p0_1, there is no path between location p0_3 and location p0_2, there is no path between location p0_3 and location p1_3, there is no path between location p1_3 and location p0_2, there is no path between location p1_3 and location p2_1, there is no path between location p1_3 and location s0, there is no path between location p3_0 and location p2_1, there is no path between location p3_0 and location s0, there is no path between location p3_0 and location s3, there is no path between location s0 and location p3_0, there is no path between location s1 and location p0_2, there is no path between location s2 and location p0_1, there is no path between location s2 and location p1_3, there is no path between location s3 and location p2_1, there is no path between location s3 and location p3_0, there is no path between location s3 and location s1, truck1 is at location s3, truck1 is empty, truck1 is not at location p0_1, truck1 is not at location p0_3, truck1 is not at location p1_3, truck1 is not at location p3_0, truck1 is not at location s2, truck1 is not currently at location p0_2, truck1 is not currently at location s1, truck1 is not present at location p2_1, truck1 is not present at location s0, truck2 contains nothing, truck2 is currently at location s0, truck2 is not at location p0_2, truck2 is not at location p1_3, truck2 is not at location p2_1, truck2 is not at location p3_0, truck2 is not at location s2, truck2 is not being driven by driver2, truck2 is not currently at location p0_1, truck2 is not currently at location s1, truck2 is not currently at location s3 and truck2 is not present at location p0_3", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s2, driver2 is at location s2, locations p0_2 and s0 have a path between them, locations p0_2 and s2 have a path between them, locations p0_3 and s0 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and s2 have a link between them, locations s2 and p0_2 have a path between them, locations s2 and s3 have a link between them, package1 is at location s1, package2 is present at location s2, package3 is present at location s0, package4 is at location s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s3 and s0, there exists a link between the locations s3 and s1, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s3, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a link between location s2 and location s1, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s3, there is a path between location s0 and location p0_2, there is a path between location s1 and location p1_3, there is a path between location s1 and location p2_1, there is a path between location s3 and location p0_3, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing and truck2 is currently at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: truck1 is loaded with package3 at location s0, driver1 moves from location s2 to p0_2, then proceeds to location s0, boards truck1 at location s0, drives truck1 from location s0 to location s3, disembarks from truck1 at location s3, unloads package3 from truck1 at location s3, walks to location p0_3 from location s3, and then returns to location s0 from location p0_3, while driver2 moves from location s2 to location p0_2 to reach the current state. In this state, list all valid properties (including both affirmative and negated properties) of the state. If there are no properties, indicate None.", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also at s2. A path exists between locations p0_2 and s0, as well as between p0_2 and s2. Additionally, a path is present between p0_3 and s0, and between p2_1 and s2. Furthermore, a path connects s0 to p0_1 and s0 to p0_3. Locations s0 and s1 are linked, as are s0 and s3, s1 and s2, s2 and s3, and s1 and s3. Package1 is situated at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. Links exist between s1 and s3, s2 and s0, s3 and s0, s3 and s1, and s3 and s2. Paths are present between p0_1 and s1, p0_3 and s3, p1_3 and s1, p2_1 and s1, s1 and p0_1, s2 and p2_1, and s3 and p1_3. Moreover, links connect s0 to s2, s1 to s0, and s2 to s1. Paths also exist between p0_1 and s0, p1_3 and s3, s0 and p0_2, s1 and p1_3, s1 and p2_1, and s3 and p0_3. Currently, truck1 is at location s0 and is empty, while truck2, which is also empty, is at location s0."}
{"question_id": "4f23d773-d68e-44c4-9d94-a5d359336350", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "state_tracking", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks to location p3_0 from location s3 to reach the current state. In this state, list all valid properties of the state that involve negations. Write None if there are none.", "answer": "driver1 is not at location p0_1, driver1 is not at location p1_3, driver1 is not at location s3, driver1 is not currently at location p1_0, driver1 is not currently at location p1_2, driver1 is not currently at location p3_0, driver1 is not currently at location s2, driver1 is not driving truck2 currently, driver1 is not driving truck3 currently, driver1 is not present at location p2_0, driver1 is not present at location s0, driver2 is not at location p1_2, driver2 is not at location s2, driver2 is not at location s3, driver2 is not currently at location p1_0, driver2 is not currently at location s0, driver2 is not driving truck1 currently, driver2 is not driving truck3 currently, driver2 is not present at location p0_1, driver2 is not present at location p1_3, driver2 is not present at location p2_0, driver2 is not present at location s1, driver3 is not at location p1_0, driver3 is not at location p2_0, driver3 is not at location s0, driver3 is not at location s1, driver3 is not currently at location p0_1, driver3 is not currently at location p1_3, driver3 is not currently at location p3_0, driver3 is not driving truck1 currently, driver3 is not present at location p1_2, driver3 is not present at location s2, locations p0_1 and p1_0 does not have a path between them, locations p0_1 and p2_0 does not have a link between them, locations p0_1 and p3_0 does not have a path between them, locations p0_1 and s1 does not have a link between them, locations p0_1 and s2 does not have a link between them, locations p1_0 and p1_3 does not have a path between them, locations p1_0 and p2_0 does not have a link between them, locations p1_0 and p3_0 does not have a path between them, locations p1_0 and s1 does not have a link between them, locations p1_0 and s1 does not have a path between them, locations p1_0 and s3 does not have a link between them, locations p1_2 and p0_1 does not have a link between them, locations p1_2 and p1_0 does not have a link between them, locations p1_2 and p1_3 does not have a link between them, locations p1_2 and p3_0 does not have a link between them, locations p1_2 and s0 does not have a link between them, locations p1_3 and p0_1 does not have a path between them, locations p1_3 and p1_0 does not have a path between them, locations p1_3 and p2_0 does not have a link between them, locations p1_3 and p2_0 does not have a path between them, locations p1_3 and s2 does not have a link between them, locations p2_0 and p1_0 does not have a link between them, locations p2_0 and p1_0 does not have a path between them, locations p2_0 and p1_2 does not have a link between them, locations p2_0 and p1_2 does not have a path between them, locations p2_0 and s1 does not have a link between them, locations p2_0 and s2 does not have a link between them, locations p2_0 and s3 does not have a link between them, locations p3_0 and p0_1 does not have a link between them, locations p3_0 and p1_0 does not have a link between them, locations p3_0 and p1_2 does not have a path between them, locations p3_0 and p2_0 does not have a path between them, locations p3_0 and s1 does not have a link between them, locations p3_0 and s1 does not have a path between them, locations p3_0 and s2 does not have a link between them, locations s0 and p1_3 does not have a path between them, locations s0 and p3_0 does not have a link between them, locations s0 and s1 does not have a path between them, locations s0 and s2 does not have a path between them, locations s1 and p1_0 does not have a link between them, locations s1 and p2_0 does not have a link between them, locations s1 and p3_0 does not have a link between them, locations s1 and s3 does not have a path between them, locations s2 and p2_0 does not have a link between them, locations s2 and s0 does not have a path between them, locations s2 and s3 does not have a path between them, locations s3 and p1_2 does not have a link between them, locations s3 and s2 does not have a link between them, package1 is not at location p1_2, package1 is not at location p2_0, package1 is not at location s0, package1 is not at location s1, package1 is not currently at location p1_0, package1 is not currently at location p3_0, package1 is not located in truck3, package1 is not placed in truck1, package1 is not placed in truck2, package1 is not present at location p0_1, package1 is not present at location p1_3, package1 is not present at location s2, package2 is not at location p1_0, package2 is not at location p1_3, package2 is not currently at location p0_1, package2 is not currently at location p2_0, package2 is not currently at location s0, package2 is not currently at location s3, package2 is not in truck1, package2 is not located in truck2, package2 is not located in truck3, package2 is not present at location p1_2, package2 is not present at location p3_0, package2 is not present at location s1, package3 is not at location p0_1, package3 is not at location p1_0, package3 is not at location p2_0, package3 is not at location s0, package3 is not at location s3, package3 is not currently at location p1_3, package3 is not currently at location p3_0, package3 is not located in truck1, package3 is not placed in truck2, package3 is not placed in truck3, package3 is not present at location p1_2, package3 is not present at location s1, package4 is not at location p1_2, package4 is not at location p3_0, package4 is not at location s0, package4 is not at location s2, package4 is not at location s3, package4 is not currently at location p1_0, package4 is not currently at location p1_3, package4 is not located in truck1, package4 is not located in truck3, package4 is not placed in truck2, package4 is not present at location p0_1, package4 is not present at location p2_0, there doesn't exist a link between the locations p0_1 and p1_3, there doesn't exist a link between the locations p0_1 and p3_0, there doesn't exist a link between the locations p0_1 and s3, there doesn't exist a link between the locations p1_0 and p0_1, there doesn't exist a link between the locations p1_0 and p1_2, there doesn't exist a link between the locations p1_0 and p1_3, there doesn't exist a link between the locations p1_0 and p3_0, there doesn't exist a link between the locations p1_0 and s0, there doesn't exist a link between the locations p1_0 and s2, there doesn't exist a link between the locations p1_2 and s3, there doesn't exist a link between the locations p1_3 and p1_0, there doesn't exist a link between the locations p1_3 and p1_2, there doesn't exist a link between the locations p1_3 and s0, there doesn't exist a link between the locations p1_3 and s3, there doesn't exist a link between the locations p2_0 and p0_1, there doesn't exist a link between the locations p3_0 and p1_2, there doesn't exist a link between the locations p3_0 and s3, there doesn't exist a link between the locations s0 and p0_1, there doesn't exist a link between the locations s1 and p1_3, there doesn't exist a link between the locations s2 and p1_2, there doesn't exist a link between the locations s2 and s3, there doesn't exist a link between the locations s3 and p1_3, there doesn't exist a link between the locations s3 and p3_0, there doesn't exist a path between the locations p0_1 and s2, there doesn't exist a path between the locations p0_1 and s3, there doesn't exist a path between the locations p1_0 and p0_1, there doesn't exist a path between the locations p1_0 and p1_2, there doesn't exist a path between the locations p1_0 and p2_0, there doesn't exist a path between the locations p1_0 and s0, there doesn't exist a path between the locations p1_2 and p0_1, there doesn't exist a path between the locations p1_2 and p1_0, there doesn't exist a path between the locations p1_2 and p2_0, there doesn't exist a path between the locations p1_3 and p1_2, there doesn't exist a path between the locations p1_3 and s0, there doesn't exist a path between the locations p2_0 and p0_1, there doesn't exist a path between the locations p2_0 and p1_3, there doesn't exist a path between the locations p3_0 and p1_3, there doesn't exist a path between the locations p3_0 and s2, there doesn't exist a path between the locations s0 and p1_0, there doesn't exist a path between the locations s0 and p1_2, there doesn't exist a path between the locations s0 and s3, there doesn't exist a path between the locations s1 and p2_0, there doesn't exist a path between the locations s1 and s2, there doesn't exist a path between the locations s2 and p1_0, there doesn't exist a path between the locations s2 and s1, there doesn't exist a path between the locations s3 and p2_0, there is no link between location p0_1 and location p1_0, there is no link between location p0_1 and location p1_2, there is no link between location p0_1 and location s0, there is no link between location p1_2 and location p2_0, there is no link between location p1_2 and location s1, there is no link between location p1_2 and location s2, there is no link between location p1_3 and location p0_1, there is no link between location p1_3 and location p3_0, there is no link between location p1_3 and location s1, there is no link between location p2_0 and location p1_3, there is no link between location p2_0 and location p3_0, there is no link between location p2_0 and location s0, there is no link between location p3_0 and location p1_3, there is no link between location p3_0 and location p2_0, there is no link between location p3_0 and location s0, there is no link between location s0 and location p1_0, there is no link between location s0 and location p1_2, there is no link between location s0 and location p1_3, there is no link between location s0 and location p2_0, there is no link between location s1 and location p0_1, there is no link between location s1 and location p1_2, there is no link between location s2 and location p0_1, there is no link between location s2 and location p1_0, there is no link between location s2 and location p1_3, there is no link between location s2 and location p3_0, there is no link between location s3 and location p0_1, there is no link between location s3 and location p1_0, there is no link between location s3 and location p2_0, there is no path between location p0_1 and location p1_2, there is no path between location p0_1 and location p1_3, there is no path between location p0_1 and location p2_0, there is no path between location p1_0 and location s2, there is no path between location p1_0 and location s3, there is no path between location p1_2 and location p1_3, there is no path between location p1_2 and location p3_0, there is no path between location p1_2 and location s0, there is no path between location p1_2 and location s3, there is no path between location p1_3 and location p3_0, there is no path between location p1_3 and location s2, there is no path between location p2_0 and location p3_0, there is no path between location p2_0 and location s1, there is no path between location p2_0 and location s3, there is no path between location p3_0 and location p0_1, there is no path between location p3_0 and location p1_0, there is no path between location s1 and location p1_0, there is no path between location s1 and location p3_0, there is no path between location s1 and location s0, there is no path between location s2 and location p0_1, there is no path between location s2 and location p1_3, there is no path between location s2 and location p3_0, there is no path between location s3 and location p0_1, there is no path between location s3 and location p1_0, there is no path between location s3 and location p1_2, there is no path between location s3 and location s0, there is no path between location s3 and location s1, there is no path between location s3 and location s2, truck1 is not at location p1_0, truck1 is not at location p1_3, truck1 is not at location p2_0, truck1 is not at location p3_0, truck1 is not at location s2, truck1 is not being driven by driver1, truck1 is not currently at location p0_1, truck1 is not currently at location s3, truck1 is not present at location p1_2, truck1 is not present at location s1, truck2 is not at location p0_1, truck2 is not at location p3_0, truck2 is not being driven by driver2, truck2 is not being driven by driver3, truck2 is not currently at location p1_0, truck2 is not currently at location p1_2, truck2 is not currently at location p1_3, truck2 is not present at location p2_0, truck2 is not present at location s0, truck2 is not present at location s1, truck2 is not present at location s2, truck3 is not at location p2_0, truck3 is not at location p3_0, truck3 is not at location s2, truck3 is not being driven by driver3, truck3 is not currently at location p1_3, truck3 is not currently at location s3, truck3 is not present at location p0_1, truck3 is not present at location p1_0, truck3 is not present at location p1_2 and truck3 is not present at location s1", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s1, driver2 is present at location s3, driver3 is at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations p2_0 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p2_0 have a path between them, locations s0 and s2 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and s3 have a link between them, locations s2 and p1_2 have a path between them, locations s2 and s1 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is present at location s3, package2 is present at location s2, package3 is at location s2, package4 is present at location s1, there exists a link between the locations s0 and s3, there exists a link between the locations s1 and s2, there exists a link between the locations s2 and s0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p2_0, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p3_0 and location s0, there is a path between location s0 and location p0_1, there is a path between location s1 and location p0_1, truck1 contains nothing, truck1 is present at location s0, truck2 contains nothing, truck2 is at location s3, truck3 is at location s0 and truck3 is empty.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: driver2 moves from location s3 to location p3_0 to achieve the current state. In this state, identify all valid properties that include negations, or state None if none exist.", "initial_state_nl_paraphrased": "Driver1 is currently located at s1, while driver2 and driver3 are both at location s3. There are paths connecting p0_1 and s1, p1_2 and s1, p1_2 and s2, p1_3 and s3, p2_0 and s0, p2_0 and s2, p3_0 and s3, s0 and p2_0, p1_2 and s1, p1_3 and s1, p3_0 and s3. Additionally, there are links between s0 and s2, s1 and s3, s2 and s1, s3 and s0, s3 and s1, s0 and s3, s1 and s2, s2 and s0, s0 and s1, and s1 and s0. Package1 is located at s3, package2 and package3 are at s2, and package4 is at s1. Furthermore, paths exist between s0 and p3_0, s2 and p2_0, s3 and p1_3, p0_1 and s0, p1_3 and s1, p3_0 and s0, s0 and p0_1, and s1 and p0_1. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and at s0."}
{"question_id": "9fb68ee4-af38-44a4-97ba-b86b5338a4ef", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "state_tracking", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0, driver1 walks from location s2 to p0_2, driver1 walks from location p0_2 to s0, at location s0, driver1 boards truck1, truck1 is driven from location s0 to s3 by driver1, at location s3, driver1 disembarks from truck1, truck1 is unloaded with package3 at location s3, driver1 walks from location s3 to location p0_3, driver1 walks from location p0_3 to location s0, driver2 walks from location s2 to p0_2, driver2 walks to location s0 from location p0_2, driver2 boards truck2 at location s0, truck2 is driven from location s0 to s1 by driver2, at location s1, package1 is loaded in truck2, truck2 is driven from location s1 to s2 by driver2, package2 is loaded in truck2 at location s2, at location s2, package1 is unloaded in truck2, driver2 drives truck2 to location s1 from location s2 and from truck2, driver2 disembarks at location s1 to reach the current state. In this state, list all valid properties of the state that do not involve negations. Write None if there are none.", "answer": "driver1 is present at location s0, driver2 is at location s1, locations s0 and p0_1 have a path between them, locations s0 and p0_2 have a path between them, locations s0 and s3 have a link between them, locations s1 and p1_3 have a path between them, locations s2 and s0 have a link between them, locations s2 and s1 have a link between them, locations s3 and s1 have a link between them, package1 is currently at location s2, package2 is in truck2, package3 is currently at location s3, package4 is at location s2, there exists a link between the locations s0 and s1, there exists a link between the locations s0 and s2, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_2 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations p2_1 and s1, there exists a path between the locations p2_1 and s2, there exists a path between the locations s3 and p0_3, there is a link between location s1 and location s0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s2, there is a path between location p0_1 and location s1, there is a path between location p0_2 and location s2, there is a path between location p0_3 and location s0, there is a path between location p0_3 and location s3, there is a path between location s0 and location p0_3, there is a path between location s1 and location p0_1, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, there is a path between location s2 and location p2_1, there is a path between location s3 and location p1_3, truck1 is empty, truck1 is present at location s3, truck2 contains nothing and truck2 is at location s1", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s2, driver2 is at location s2, locations p0_2 and s0 have a path between them, locations p0_2 and s2 have a path between them, locations p0_3 and s0 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and s2 have a link between them, locations s2 and p0_2 have a path between them, locations s2 and s3 have a link between them, package1 is at location s1, package2 is present at location s2, package3 is present at location s0, package4 is at location s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s0, there exists a link between the locations s3 and s0, there exists a link between the locations s3 and s1, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s3, there exists a path between the locations p1_3 and s1, there exists a path between the locations p2_1 and s1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s1 and location s0, there is a link between location s2 and location s1, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s3, there is a path between location s0 and location p0_2, there is a path between location s1 and location p1_3, there is a path between location s1 and location p2_1, there is a path between location s3 and location p0_3, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing and truck2 is currently at location s0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: package3 is loaded onto truck1 at location s0, driver1 moves from location s2 to p0_2 and then to s0, where driver1 gets on truck1, and then truck1 is driven by driver1 from s0 to s3, after which driver1 gets off truck1 at s3, and package3 is unloaded from truck1 at s3, followed by driver1 walking from s3 to p0_3 and then back to s0. Meanwhile, driver2 moves from s2 to p0_2 and then to s0, boards truck2 at s0, and drives it to s1, where package1 is loaded onto truck2, then to s2 where package2 is loaded, and then back to s1, unloading package1 from truck2 at s2, and finally, driver2 gets off truck2 at s1, resulting in the current state. In this state, list all valid properties that do not involve negations. If none exist, state None.", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also at s2. A path exists between locations p0_2 and s0, as well as between p0_2 and s2. Additionally, a path is present between p0_3 and s0, and between p2_1 and s2. Furthermore, a path connects s0 to p0_1 and s0 to p0_3. Locations s0 and s1 are linked, as are s0 and s3, s1 and s2, s2 and s3, and s2 and p0_2. Package1 is situated at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. Links exist between s1 and s3, s2 and s0, s3 and s0, s3 and s1, and s3 and s2. Paths are present between p0_1 and s1, p0_3 and s3, p1_3 and s1, p2_1 and s1, s1 and p0_1, s2 and p2_1, and s3 and p1_3. There are links between s0 and s2, s1 and s0, and s2 and s1. Paths also exist between p0_1 and s0, p1_3 and s3, s0 and p0_2, s1 and p1_3, s1 and p2_1, and s3 and p0_3. Truck1 is currently empty and located at s0, while truck2 is also at s0 and contains nothing."}
