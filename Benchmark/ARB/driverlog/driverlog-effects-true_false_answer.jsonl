{"question_id": "be949d7c-3fea-4099-938e-eff49ef302f6", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to location p3_0, driver2 walks to location s0 from location p3_0, driver2 boards truck1 at location s0, truck1 is driven from location s0 to s2 by driver2, truck1 is loaded with package3 at location s2, package2 is loaded in truck1 at location s2, driver2 drives truck1 from location s2 to location s0, package2 is unloaded from truck1 at location s0, truck1 is driven from location s0 to s3 by driver2 and truck1 is loaded with package1 at location s3 to reach the current state. In this state, if driver2 drives truck1 to location s1 from location s3, is it True or False that driver1 is present at location s1 and truck3 is at location s3?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s1, driver2 is at location s3, driver3 is present at location s3, locations s1 and p1_2 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and s0 have a link between them, locations s1 and s3 have a link between them, locations s2 and s0 have a link between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is at location s2, package3 is present at location s2, package4 is at location s1, there exists a link between the locations s0 and s1, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s1 and p0_1, there exists a path between the locations s2 and p1_2, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a path between location p0_1 and location s0, there is a path between location p1_2 and location s2, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p0_1, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 contains nothing, truck1 is present at location s0, truck2 is empty, truck2 is present at location s3, truck3 is empty and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: driver2 moves from location s3 to p3_0, then proceeds to location s0 from p3_0, boards truck1 at location s0, drives truck1 from s0 to s2, loads truck1 with package3 at s2, also loads package2 into truck1 at s2, drives truck1 from s2 back to s0, unloads package2 from truck1 at s0, drives truck1 from s0 to s3, and finally loads truck1 with package1 at s3, resulting in the current state. In this state, if driver2 drives truck1 from location s3 to s1, is it True or False that driver1 is at location s1 and truck3 is at location s3?", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is at s3, and driver3 is also at s3. A path exists between s1 and p1_2, as well as between s1 and p1_3, and a link is present between s1 and s0, s1 and s3, s2 and s0, and s3 and s0. Package1 is currently at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. There are links between s0 and s1, s0 and s3, and s3 and s1. Paths exist between p0_1 and s1, p1_2 and s1, p1_3 and s1, p1_3 and s3, s1 and p0_1, s2 and p1_2, s0 and p0_1, s2 and p1_2, p2_0 and s0, p2_0 and s2, p3_0 and s0, p3_0 and s3, s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and at s0."}
{"question_id": "09d4af99-ba4f-4570-bf35-eb66020f3c21", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is boarded by driver3 at location s0 to reach the current state. In this state, if truck1 is loaded with package3 at location s0, is it True or False that package3 is in truck1 and package3 is not present at location s0?", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is currently at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s1 have a path between them, locations p2_0 and s0 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p3_0 have a path between them, locations s2 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s2 and s1 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s0, package2 is at location s2, package3 is at location s0, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_2, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s1 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: driver3 boards truck1 at location s0 to reach the current state. In this state, if truck1 is loaded with package3 at location s0, is it True or False that package3 is in truck1 and package3 is no longer at location s0?", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is also at location s3, and driver3 is at location s0. A path exists between locations p1_2 and s1, as well as between p2_0 and s0, p3_0 and s3, s0 and p3_0, s2 and p1_2, and s2 and p2_0. Locations s2 and s0, s2 and s1, s3 and p3_0, and s3 and s0 are all connected by a link. Package1 is located at s0, package2 is at s2, and package3 is also at s0. A link exists between locations s3 and s2. Paths also exist between p0_1 and s0, p0_1 and s1, p1_3 and s1, s0 and p0_1, s1 and p0_1, s1 and p1_2, s3 and p1_3, s0 and p2_0, s1 and p1_3, p1_2 and s2, p1_3 and s3, p2_0 and s2, and p3_0 and s0. Additionally, links exist between locations s0 and s2, s0 and s3, s1 and s2, s1 and s3, s2 and s3, and s3 and s1. Truck1 is empty and located at s0, while truck2 is also empty and at location s2."}
{"question_id": "ac8f8056-5e90-413e-bc61-7287be86183c", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: package3 is loaded in truck1 at location s0 to reach the current state. In this state, if driver1 walks to location p0_2 from location s2, is it True or False that driver1 is currently at location p0_2 and driver1 is not currently at location s2?", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s2, driver2 is currently at location s2, locations p0_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is currently at location s0, package4 is at location s2, there exists a link between the locations s0 and s2, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_2 and s0, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_3, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p0_3, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, truck1 is currently at location s0, truck1 is empty, truck2 is empty and truck2 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: package3 is loaded in truck1 at location s0 to reach the current state. In this state, if driver1 walks to location p0_2 from location s2, is it True or False that driver1 is now at location p0_2 and simultaneously not at location s2?", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at location s2. A path exists between locations p0_2 and s2, as well as between p1_3 and s3. Locations s3 and s1 are connected by a link, and s3 and s2 are also linked. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link is present between s0 and s2, s0 and s3, and s3 and s0. Paths exist between p0_1 and s0, p0_2 and s0, p0_3 and s0, p0_3 and s3, p1_3 and s1, s0 and p0_3, s1 and p1_3, s2 and p2_1, s3 and p0_3, and s3 and p1_3. Additionally, links are present between s0 and s1, s1 and s0, s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s2 and s3. Furthermore, paths exist between p0_1 and s1, p2_1 and s1, p2_1 and s2, s0 and p0_1, s0 and p0_2, s1 and p0_1, s1 and p2_1, and s2 and p0_2. Truck1 is currently empty and located at s0, and truck2 is also empty and present at s0."}
{"question_id": "4ce984ab-36c3-44e7-903b-f0a61388f498", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to location p3_0 to reach the current state. In this state, if driver2 walks from location p3_0 to location s0, is it True or False that truck1 is not present at location s0?", "answer": "False", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s1, driver2 is at location s3, driver3 is present at location s3, locations s1 and p1_2 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and s0 have a link between them, locations s1 and s3 have a link between them, locations s2 and s0 have a link between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is at location s2, package3 is present at location s2, package4 is at location s1, there exists a link between the locations s0 and s1, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s1 and p0_1, there exists a path between the locations s2 and p1_2, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a path between location p0_1 and location s0, there is a path between location p1_2 and location s2, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p0_1, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 contains nothing, truck1 is present at location s0, truck2 is empty, truck2 is present at location s3, truck3 is empty and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: driver2 moves from location s3 to location p3_0 to reach the current state. In this state, if driver2 moves from location p3_0 to location s0, is it True or False that truck1 is not at location s0?", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is at s3, and driver3 is also at s3. A path exists between s1 and p1_2, as well as between s1 and p1_3, and a link is present between s1 and s0, s1 and s3, s2 and s0, and s3 and s0. Package1 is currently at s3, package2 is at s2, package3 is at s2, and package4 is at s1. Links are present between s0 and s1, s0 and s3, and s3 and s1. Paths exist between p0_1 and s1, p1_2 and s1, p1_3 and s1, p1_3 and s3, s1 and p0_1, s2 and p1_2, s0 and p0_1, s2 and p1_2, p2_0 and s0, p2_0 and s2, p3_0 and s0, and p3_0 and s3. Additionally, links are present between s0 and s2, s1 and s2, and s2 and s1. Furthermore, paths exist between s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is empty and at s0."}
{"question_id": "b65601d1-5ba2-4a7f-9095-e3e838f8eeed", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: package3 is loaded in truck1 at location s0, driver1 walks from location s2 to location p0_2, driver1 walks to location s0 from location p0_2, driver1 boards truck1 at location s0, driver1 drives truck1 to location s3 from location s0, from truck1, driver1 disembarks at location s3, at location s3, package3 is unloaded in truck1, driver1 walks from location s3 to p0_3, driver1 walks to location s0 from location p0_3 and driver2 walks to location p0_2 from location s2 to reach the current state. In this state, if driver2 walks to location s0 from location p0_2, is it True or False that driver2 is at location s0 and driver2 is not currently at location p0_2?", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s2, driver2 is currently at location s2, locations p0_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is currently at location s0, package4 is at location s2, there exists a link between the locations s0 and s2, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_2 and s0, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_3, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p0_3, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, truck1 is currently at location s0, truck1 is empty, truck2 is empty and truck2 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: package3 is placed in truck1 at location s0, driver1 moves from location s2 to location p0_2, then proceeds to location s0 from location p0_2, boards truck1 at location s0, drives truck1 from location s0 to location s3, disembarks from truck1 at location s3, unloads package3 from truck1 at location s3, walks from location s3 to p0_3, and then returns to location s0 from p0_3, while driver2 moves from location s2 to location p0_2 to reach the current state. In this state, if driver2 proceeds to location s0 from location p0_2, is it True or False that driver2 is now at location s0 and no longer at location p0_2?", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at location s2. A path exists between locations p0_2 and s2, as well as between p1_3 and s3. Locations s3 and s1 are connected by a link, and s3 and s2 are also linked. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. Links exist between s0 and s2, s0 and s3, and s3 and s0. Paths are present between p0_1 and s0, p0_2 and s0, p0_3 and s0, p0_3 and s3, p1_3 and s1, s0 and p0_3, s1 and p1_3, s2 and p2_1, s3 and p0_3, and s3 and p1_3. Furthermore, links exist between s0 and s1, s1 and s0, s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s2 and s3. Paths also exist between p0_1 and s1, p2_1 and s1, p2_1 and s2, s0 and p0_1, s0 and p0_2, s1 and p0_1, s1 and p2_1, and s2 and p0_2. Additionally, truck1 is currently at location s0 and is empty, while truck2 is also empty and present at location s0."}
{"question_id": "797afdd0-4897-4970-ad15-f175ce116088", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks to location p4_3 from location s3 to reach the current state. In this state, if driver1 walks from location p4_3 to s4, is it True or False that driver1 is at location s4 and driver1 is not at location p4_3?", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s3, driver2 is at location s4, driver3 is present at location s3, locations p0_5 and s0 have a path between them, locations p0_5 and s5 have a path between them, locations p4_0 and s0 have a path between them, locations p4_1 and s1 have a path between them, locations p4_3 and s3 have a path between them, locations p4_3 and s4 have a path between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s2 and s3 have a link between them, locations s3 and s2 have a link between them, locations s4 and s0 have a link between them, locations s5 and s4 have a link between them, package1 is currently at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s4 and s1, there exists a link between the locations s4 and s5, there exists a link between the locations s5 and s0, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a path between the locations p4_1 and s4, there exists a path between the locations s1 and p4_1, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_0, there exists a path between the locations s4 and p4_1, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s5, there is a link between location s3 and location s4, there is a link between location s4 and location s3, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s2, there is a path between location p5_2 and location s5, there is a path between location s0 and location p0_5, there is a path between location s0 and location p4_0, there is a path between location s2 and location p5_2, there is a path between location s4 and location p4_3, there is a path between location s5 and location p0_5, truck1 contains nothing, truck1 is present at location s1, truck2 is empty and truck2 is present at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: driver1 moves from location s3 to location p4_3 to reach the current state. In this state, if driver1 then proceeds from location p4_3 to s4, is it True or False that driver1 is now at location s4 and no longer at location p4_3?", "initial_state_nl_paraphrased": "Driver1 is located at s3, driver2 is at s4, and driver3 is also at s3. A path exists between p0_5 and s0, as well as between p0_5 and s5. Additionally, paths are present between p4_0 and s0, p4_1 and s1, p4_3 and s3, and p4_3 and s4. Furthermore, links exist between s0 and s4, s1 and s4, s2 and s3, s3 and s2, s4 and s0, and s5 and s4. Package1 and package2 are currently at s2, package3 is at s3, and package4 is at s0. Links also exist between s0 and s5, s1 and s0, s3 and s5, s4 and s1, s4 and s5, s5 and s0, s5 and s2, and s5 and s3. Moreover, paths are present between p4_1 and s4, s1 and p4_1, s3 and p4_3, s4 and p4_0, s4 and p4_1, and s5 and p5_2. There are also links between s0 and s1, s0 and s2, s1 and s2, s2 and s0, s2 and s1, s2 and s5, s3 and s4, and s4 and s3. Paths exist between p4_0 and s4, p5_2 and s2, p5_2 and s5, s0 and p0_5, s0 and p4_0, s2 and p5_2, s4 and p4_3, and s5 and p0_5. Truck1 is empty and located at s1, while truck2 is also empty and at s5."}
{"question_id": "6d5e0c8d-855e-4b22-aa8d-d9a2af58986d", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1 to reach the current state. In this state, if driver1 walks to location p0_2 from location s2, is it True or False that package1 is present at location s1 and package3 is currently at location p0_2?", "answer": "False", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s2, driver2 is currently at location s2, locations p0_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is currently at location s0, package4 is at location s2, there exists a link between the locations s0 and s2, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_2 and s0, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_3, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p0_3, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, truck1 is currently at location s0, truck1 is empty, truck2 is empty and truck2 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: at location s0, package3 is loaded in truck1 to reach the current state. In this state, if driver1 walks to location p0_2 from location s2, is it True or False that package1 is at location s1 and package3 is now at location p0_2?", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at location s2. A path exists between locations p0_2 and s2, as well as between p1_3 and s3. Locations s3 and s1 are connected by a link, and s3 and s2 are also linked. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link exists between s0 and s2, s0 and s3, and s3 and s0. Paths are present between p0_1 and s0, p0_2 and s0, p0_3 and s0, p0_3 and s3, p1_3 and s1, s0 and p0_3, s1 and p1_3, s2 and p2_1, s3 and p0_3, and s3 and p1_3. Additionally, links exist between s0 and s1, s1 and s0, s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s2 and s3. Furthermore, paths are present between p0_1 and s1, p2_1 and s1, p2_1 and s2, s0 and p0_1, s0 and p0_2, s1 and p0_1, s1 and p2_1, and s2 and p0_2. Truck1 is currently empty and located at s0, and truck2 is also empty and present at s0."}
{"question_id": "d22d0156-0b01-4fd5-8964-2f48919250b2", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is boarded by driver3 at location s0, truck1 is loaded with package3 at location s0, at location s0, package1 is loaded in truck1, driver3 drives truck1 from location s0 to location s3, package1 is unloaded from truck1 at location s3, driver3 drives truck1 from location s3 to location s1, driver3 disembarks from truck1 at location s1, at location s1, package3 is unloaded in truck1, package2 is loaded in truck2 at location s2 and driver1 walks to location p3_0 from location s3 to reach the current state. In this state, if driver1 walks from location p3_0 to location s0, is it True or False that package2 is not in truck1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s1 have a path between them, locations p2_0 and s0 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p3_0 have a path between them, locations s2 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s2 and s1 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s0, package2 is at location s2, package3 is at location s0, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_2, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s1 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver3 boards truck1 at location s0, truck1 is loaded with package3 at location s0, package1 is also loaded into truck1 at location s0, then driver3 drives truck1 from location s0 to location s3, where package1 is unloaded from truck1, after which driver3 drives truck1 from location s3 to location s1, and driver3 gets off truck1 at location s1, at location s1, package3 is unloaded from truck1, meanwhile, package2 is loaded into truck2 at location s2, and driver1 moves from location s3 to location p3_0 to reach the current state. In this state, if driver1 walks from location p3_0 to location s0, is it True or False that package2 is not in truck1?", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is also at location s3, and driver3 is at location s0. A path exists between locations p1_2 and s1, as well as between p2_0 and s0, p3_0 and s3, s0 and p3_0, s2 and p1_2, and s2 and p2_0. Locations s2 and s0 are connected by a link, as are locations s2 and s1, s3 and p3_0, and s3 and s0. Package1 is located at s0, package2 is at s2, and package3 is also at s0. A link exists between locations s3 and s2. Additionally, paths exist between p0_1 and s0, p0_1 and s1, p1_3 and s1, s0 and p0_1, s1 and p0_1, s1 and p1_2, s3 and p1_3, s0 and s2, s0 and s3, s1 and s2, s1 and s3, s2 and s3, and s3 and s1. Furthermore, paths are present between p1_2 and s2, p1_3 and s3, p2_0 and s2, p3_0 and s0, s0 and p2_0, and s1 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and at location s2."}
{"question_id": "ecf1948a-3b4d-4bff-b106-0dccf7456c71", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: at location s0, driver3 boards truck1, at location s0, package3 is loaded in truck1, at location s0, package1 is loaded in truck1, truck1 is driven from location s0 to s3 by driver3, package1 is unloaded from truck1 at location s3, driver3 drives truck1 to location s1 from location s3, driver3 disembarks from truck1 at location s1, truck1 is unloaded with package3 at location s1, package2 is loaded in truck2 at location s2, driver1 walks to location p3_0 from location s3, driver1 walks from location p3_0 to location s0, driver2 walks from location s3 to p1_3, driver2 walks to location s1 from location p1_3, driver2 walks to location p1_2 from location s1, driver2 walks to location s2 from location p1_2, driver3 walks from location s1 to location p1_2, driver3 walks from location p1_2 to location s2, driver3 boards truck2 at location s2 and driver3 drives truck2 to location s3 from location s2 to reach the current state. In this state, if driver3 disembarks from truck2 at location s3, is it True or False that truck1 is empty?", "answer": "True", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s1 have a path between them, locations p2_0 and s0 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p3_0 have a path between them, locations s2 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s2 and s1 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s0, package2 is at location s2, package3 is at location s0, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_2, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s1 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: at location s0, driver3 gets into truck1, then package3 and package1 are loaded into truck1 at location s0. Next, driver3 drives truck1 from location s0 to s3, unloads package1 at location s3, and then drives truck1 to location s1. At location s1, driver3 gets out of truck1, and package3 is unloaded from truck1. Meanwhile, package2 is loaded into truck2 at location s2. Driver1 walks from location s3 to p3_0 and then to location s0, while driver2 walks from location s3 to p1_3, then to location s1, to p1_2, and finally to location s2. Driver3 walks from location s1 to p1_2 and then to location s2, where driver3 gets into truck2 and drives it to location s3. In this state, if driver3 gets out of truck2 at location s3, is it True or False that truck1 is empty?", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is also at location s3, and driver3 is at location s0. A path exists between locations p1_2 and s1, as well as between p2_0 and s0, p3_0 and s3, s0 and p3_0, s2 and p1_2, and s2 and p2_0. Locations s2 and s0 are connected by a link, as are locations s2 and s1, s3 and p3_0, and s3 and s0. Package1 is located at s0, package2 is at s2, and package3 is at s0. A link exists between locations s3 and s2. Paths also exist between p0_1 and s0, p0_1 and s1, p1_3 and s1, s0 and p0_1, s1 and p0_1, s1 and p1_2, and s3 and p1_3. Furthermore, links exist between locations s0 and s2, s0 and s3, s1 and s2, s1 and s3, s2 and s3, and s3 and s1. Paths also exist between p1_2 and s2, p1_3 and s3, p2_0 and s2, p3_0 and s0, s0 and p2_0, and s1 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and located at s2."}
{"question_id": "75c23afe-cfd2-4c0f-835b-e48609d2a8da", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: package3 is loaded in truck1 at location s0, driver1 walks to location p0_2 from location s2, driver1 walks to location s0 from location p0_2, driver1 boards truck1 at location s0, truck1 is driven from location s0 to s3 by driver1, from truck1, driver1 disembarks at location s3, package3 is unloaded from truck1 at location s3, driver1 walks to location p0_3 from location s3, driver1 walks to location s0 from location p0_3 and driver2 walks from location s2 to p0_2 to reach the current state. In this state, if driver2 walks to location s0 from location p0_2, is it True or False that driver1 is not present at location p2_1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s2, driver2 is currently at location s2, locations p0_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is currently at location s0, package4 is at location s2, there exists a link between the locations s0 and s2, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_2 and s0, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_3, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p0_3, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, truck1 is currently at location s0, truck1 is empty, truck2 is empty and truck2 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: truck1 is loaded with package3 at location s0, driver1 moves from location s2 to p0_2, then to s0, boards truck1 at s0, drives truck1 from s0 to s3, disembarks from truck1 at s3, unloads package3 from truck1 at s3, and then walks to p0_3 and back to s0. Meanwhile, driver2 moves from s2 to p0_2. In this resulting state, if driver2 proceeds to location s0 from p0_2, is it True or False that driver1 is not at location p2_1?", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at location s2. A path exists between locations p0_2 and s2, as well as between p1_3 and s3. Locations s3 and s1 are connected by a link, and s3 and s2 are also linked. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. A link exists between s0 and s2, s0 and s3, and s3 and s0. Paths are present between p0_1 and s0, p0_2 and s0, p0_3 and s0, p0_3 and s3, p1_3 and s1, s0 and p0_3, s1 and p1_3, s2 and p2_1, s3 and p0_3, and s3 and p1_3. Additionally, links exist between s0 and s1, s1 and s0, s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s2 and s3. Furthermore, paths are present between p0_1 and s1, p2_1 and s1, p2_1 and s2, s0 and p0_1, s0 and p0_2, s1 and p0_1, s1 and p2_1, and s2 and p0_2. Truck1 is currently empty and located at s0, and truck2 is also empty and present at s0."}
{"question_id": "11c17b25-a8af-4626-b0e5-b14f07d0bb25", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks to location p4_3 from location s3, driver1 walks from location p4_3 to s4, driver1 walks from location s4 to p4_1, driver1 walks from location p4_1 to location s1, truck1 is boarded by driver1 at location s1, driver1 drives truck1 to location s0 from location s1, truck1 is loaded with package4 at location s0, driver1 drives truck1 to location s2 from location s0, package2 is loaded in truck1 at location s2 and package1 is loaded in truck1 at location s2 to reach the current state. In this state, if driver1 drives truck1 to location s3 from location s2, is it True or False that driver2 is present at location s1 and package4 is not currently at location s4?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s3, driver2 is at location s4, driver3 is present at location s3, locations p0_5 and s0 have a path between them, locations p0_5 and s5 have a path between them, locations p4_0 and s0 have a path between them, locations p4_1 and s1 have a path between them, locations p4_3 and s3 have a path between them, locations p4_3 and s4 have a path between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s2 and s3 have a link between them, locations s3 and s2 have a link between them, locations s4 and s0 have a link between them, locations s5 and s4 have a link between them, package1 is currently at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s4 and s1, there exists a link between the locations s4 and s5, there exists a link between the locations s5 and s0, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a path between the locations p4_1 and s4, there exists a path between the locations s1 and p4_1, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_0, there exists a path between the locations s4 and p4_1, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s5, there is a link between location s3 and location s4, there is a link between location s4 and location s3, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s2, there is a path between location p5_2 and location s5, there is a path between location s0 and location p0_5, there is a path between location s0 and location p4_0, there is a path between location s2 and location p5_2, there is a path between location s4 and location p4_3, there is a path between location s5 and location p0_5, truck1 contains nothing, truck1 is present at location s1, truck2 is empty and truck2 is present at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by s4 to p4_1, and finally from p4_1 to s1. At location s1, driver1 boards truck1, then drives it from s1 to s0. At location s0, truck1 is loaded with package4, and subsequently, driver1 drives truck1 from s0 to s2. At location s2, both package2 and package1 are loaded into truck1, resulting in the current state. In this state, if driver1 drives truck1 from location s2 to s3, is it True or False that driver2 is at location s1 and package4 is not at location s4?", "initial_state_nl_paraphrased": "Driver1 is located at s3, while driver2 is at s4, and driver3 is also at s3. A path exists between locations p0_5 and s0, as well as between p0_5 and s5. Additionally, paths are present between p4_0 and s0, p4_1 and s1, p4_3 and s3, and p4_3 and s4. Furthermore, a link connects locations s0 and s4, s1 and s4, s2 and s3, s3 and s2, s4 and s0, and s5 and s4. Package1 and package2 are both at location s2, package3 is at s3, and package4 is at s0. Links also exist between s0 and s5, s1 and s0, s3 and s5, s4 and s1, s4 and s5, s5 and s0, s5 and s2, and s5 and s3. Moreover, paths are present between p4_1 and s4, s1 and p4_1, s3 and p4_3, s4 and p4_0, s4 and p4_1, and s5 and p5_2. There are also links between s0 and s1, s0 and s2, s1 and s2, s2 and s0, s2 and s1, s2 and s5, s3 and s4, and s4 and s3. Paths exist between p4_0 and s4, p5_2 and s2, p5_2 and s5, s0 and p0_5, s0 and p4_0, s2 and p5_2, s4 and p4_3, and s5 and p0_5. Truck1 is empty and located at s1, while truck2 is also empty and at location s5."}
{"question_id": "4247534d-2966-4a46-9a58-ef46aa95c111", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks from location s3 to location p3_0, driver2 walks to location s0 from location p3_0, truck1 is boarded by driver2 at location s0, driver2 drives truck1 from location s0 to location s2, at location s2, package3 is loaded in truck1, truck1 is loaded with package2 at location s2, truck1 is driven from location s2 to s0 by driver2, package2 is unloaded from truck1 at location s0, driver2 drives truck1 from location s0 to location s3 and package1 is loaded in truck1 at location s3 to reach the current state. In this state, if driver2 drives truck1 from location s3 to location s1, is it True or False that truck1 is at location s1 and truck1 is not currently at location s3?", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s1, driver2 is at location s3, driver3 is present at location s3, locations s1 and p1_2 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and s0 have a link between them, locations s1 and s3 have a link between them, locations s2 and s0 have a link between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is at location s2, package3 is present at location s2, package4 is at location s1, there exists a link between the locations s0 and s1, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s1 and p0_1, there exists a path between the locations s2 and p1_2, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a path between location p0_1 and location s0, there is a path between location p1_2 and location s2, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p0_1, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 contains nothing, truck1 is present at location s0, truck2 is empty, truck2 is present at location s3, truck3 is empty and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver2 moves from location s3 to p3_0, then proceeds to location s0 from p3_0, boards truck1 at location s0, drives truck1 from s0 to s2, loads package3 and package2 into truck1 at location s2, drives truck1 back to s0, unloads package2 at location s0, drives truck1 from s0 to s3, and loads package1 into truck1 at location s3, ultimately reaching the current state. In this state, if driver2 drives truck1 from location s3 to location s1, is it True or False that truck1 is now at location s1 and no longer at location s3?", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is at s3, and driver3 is also at s3. A path exists between s1 and p1_2, as well as between s1 and p1_3, and a link is present between s1 and s0, s1 and s3, s2 and s0, and s3 and s0. Package1 is currently at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. There are links between s0 and s1, s0 and s3, and s3 and s1. Paths exist between p0_1 and s1, p1_2 and s1, p1_3 and s1, p1_3 and s3, s1 and p0_1, s2 and p1_2, p0_1 and s0, p1_2 and s2, p2_0 and s0, p2_0 and s2, p3_0 and s0, and p3_0 and s3. Additionally, links are present between s0 and s2, s1 and s2, and s2 and s1. Furthermore, paths exist between s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and at s0."}
{"question_id": "471ef861-bb55-4eb5-9d0b-8c8953352383", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0, driver1 walks to location p0_2 from location s2, driver1 walks to location s0 from location p0_2, truck1 is boarded by driver1 at location s0, truck1 is driven from location s0 to s3 by driver1, from truck1, driver1 disembarks at location s3, at location s3, package3 is unloaded in truck1, driver1 walks to location p0_3 from location s3, driver1 walks to location s0 from location p0_3, driver2 walks from location s2 to location p0_2, driver2 walks from location p0_2 to s0, truck2 is boarded by driver2 at location s0, driver2 drives truck2 from location s0 to location s1, package1 is loaded in truck2 at location s1, driver2 drives truck2 to location s2 from location s1, at location s2, package2 is loaded in truck2, truck2 is unloaded with package1 at location s2, driver2 drives truck2 to location s1 from location s2 and at location s1, driver2 disembarks from truck2 to reach the current state. In this state, if truck2 is unloaded with package2 at location s1, is it True or False that package2 is currently at location s1 and package2 is not placed in truck2?", "answer": "True", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s2, driver2 is currently at location s2, locations p0_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is currently at location s0, package4 is at location s2, there exists a link between the locations s0 and s2, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_2 and s0, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_3, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p0_3, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, truck1 is currently at location s0, truck1 is empty, truck2 is empty and truck2 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: package3 is loaded onto truck1 at location s0, driver1 moves from location s2 to p0_2, then from p0_2 to s0, boards truck1 at s0, drives truck1 from s0 to s3, disembarks from truck1 at s3, unloads package3 from truck1 at s3, and then walks from s3 to p0_3 and back to s0. Meanwhile, driver2 moves from s2 to p0_2, then to s0, boards truck2 at s0, drives truck2 from s0 to s1, loads package1 into truck2 at s1, drives truck2 from s1 to s2, loads package2 into truck2 at s2, unloads package1 from truck2 at s2, drives truck2 back to s1, and finally disembarks from truck2 at s1. In this resulting state, if truck2 is unloaded with package2 at location s1, is it True or False that package2 is currently at location s1 and package2 is not in truck2?", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at the same location, s2. A path exists between locations p0_2 and s2, and another path is present between locations p1_3 and s3. Locations s3 and s1 are connected by a link, as are locations s3 and s2. Package1 is currently situated at location s1, package2 is at location s2, package3 is at location s0, and package4 is also located at s2. A link is present between locations s0 and s2, as well as between locations s0 and s3, and another link exists between locations s3 and s0. Paths exist between various locations, including p0_1 and s0, p0_2 and s0, p0_3 and s0, p0_3 and s3, p1_3 and s1, s0 and p0_3, s1 and p1_3, s2 and p2_1, s3 and p0_3, and s3 and p1_3. Furthermore, links are present between locations s0 and s1, s1 and s0, s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s2 and s3. Additional paths exist between locations p0_1 and s1, p2_1 and s1, p2_1 and s2, s0 and p0_1, s0 and p0_2, s1 and p0_1, s1 and p2_1, and s2 and p0_2. Truck1 is currently empty and located at s0, and truck2 is also empty and present at location s0."}
{"question_id": "fae6266a-9aac-4836-88b8-8aa2577b56b6", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is boarded by driver3 at location s0 to reach the current state. In this state, if at location s0, package3 is loaded in truck1, is it True or False that truck2 is not at location p2_1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is currently at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s1 have a path between them, locations p2_0 and s0 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p3_0 have a path between them, locations s2 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s2 and s1 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s0, package2 is at location s2, package3 is at location s0, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_2, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s1 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: driver3 boards truck1 at location s0 to reach the current state. In this state, if at location s0, package3 is loaded in truck1, is it True or False that truck2 is not located at p2_1?", "initial_state_nl_paraphrased": "Driver1's current location is s3, while driver2 is also at location s3, and driver3 is at location s0. A path exists between locations p1_2 and s1, as well as between p2_0 and s0, p3_0 and s3, s0 and p3_0, s2 and p1_2, and s2 and p2_0. Locations s2 and s0 are connected by a link, as are locations s2 and s1, s3 and p3_0, and s3 and s0. Package1 is located at s0, package2 is at s2, and package3 is at s0. A link exists between locations s3 and s2. Additionally, paths exist between p0_1 and s0, p0_1 and s1, p1_3 and s1, s0 and p0_1, s1 and p0_1, s1 and p1_2, s3 and p1_3, s0 and p2_0, s1 and p1_3, p1_2 and s2, p1_3 and s3, p2_0 and s2, p3_0 and s0, and s0 and p2_0. Furthermore, links exist between locations s0 and s2, s0 and s3, s1 and s2, s1 and s3, s2 and s3, and s3 and s1. Truck1 is empty and located at s0, while truck2 is also empty and located at s2."}
{"question_id": "2418ac5f-534c-4f69-8e2e-00bd362c6cfd", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is boarded by driver3 at location s0, truck1 is loaded with package3 at location s0, package1 is loaded in truck1 at location s0, truck1 is driven from location s0 to s3 by driver3, package1 is unloaded from truck1 at location s3, driver3 drives truck1 from location s3 to location s1, from truck1, driver3 disembarks at location s1, truck1 is unloaded with package3 at location s1, at location s2, package2 is loaded in truck2 and driver1 walks to location p3_0 from location s3 to reach the current state. In this state, if driver1 walks from location p3_0 to location s0, is it True or False that driver1 is currently at location s0 and driver1 is not present at location p3_0?", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s1 have a path between them, locations p2_0 and s0 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p3_0 have a path between them, locations s2 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s2 and s1 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s0, package2 is at location s2, package3 is at location s0, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_2, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s1 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver3 boards truck1 at location s0, truck1 is loaded with package3 at location s0, and package1 is also loaded into truck1 at location s0. Then, driver3 drives truck1 from location s0 to s3, where package1 is unloaded from truck1. Next, driver3 drives truck1 from location s3 to location s1, and upon arrival, driver3 disembarks from truck1 at location s1. Additionally, package3 is unloaded from truck1 at location s1. Meanwhile, at location s2, package2 is loaded into truck2, and driver1 moves from location s3 to location p3_0 to reach the current state. In this state, if driver1 walks from location p3_0 to location s0, is it True or False that driver1 is currently at location s0 and driver1 is not present at location p3_0?", "initial_state_nl_paraphrased": "The current location of Driver1 is s3, while Driver2 is also at location s3, and Driver3 is at location s0. A path exists between locations p1_2 and s1, as well as between p2_0 and s0, p3_0 and s3, s0 and p3_0, s2 and p1_2, s2 and p2_0. Locations s2 and s0 are connected by a link, as are locations s2 and s1, s3 and p3_0, and s3 and s0. Package1 is located at s0, Package2 is at s2, and Package3 is at s0. A link exists between locations s3 and s2. Additionally, paths exist between p0_1 and s0, p0_1 and s1, p1_3 and s1, s0 and p0_1, s1 and p0_1, s1 and p1_2, s3 and p1_3. Furthermore, links exist between locations s0 and s2, s0 and s3, s1 and s2, s1 and s3, s2 and s3, and s3 and s1. Paths also exist between p1_2 and s2, p1_3 and s3, p2_0 and s2, p3_0 and s0, s0 and p2_0, and s1 and p1_3. Truck1 is empty and located at s0, while Truck2 is also empty and at location s2."}
{"question_id": "51ea6f34-7fc0-47bc-a2b6-a93762ed2825", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is boarded by driver3 at location s0, at location s0, package3 is loaded in truck1, package1 is loaded in truck1 at location s0, truck1 is driven from location s0 to s3 by driver3, at location s3, package1 is unloaded in truck1, truck1 is driven from location s3 to s1 by driver3, from truck1, driver3 disembarks at location s1, truck1 is unloaded with package3 at location s1, truck2 is loaded with package2 at location s2, driver1 walks to location p3_0 from location s3, driver1 walks from location p3_0 to location s0, driver2 walks to location p1_3 from location s3, driver2 walks from location p1_3 to location s1, driver2 walks from location s1 to p1_2, driver2 walks from location p1_2 to s2, driver3 walks from location s1 to p1_2, driver3 walks to location s2 from location p1_2, at location s2, driver3 boards truck2 and truck2 is driven from location s2 to s3 by driver3 to reach the current state. In this state, if from truck2, driver3 disembarks at location s3, is it True or False that driver3 is currently at location s3 and truck2 is not being driven by driver3?", "answer": "True", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s3, driver2 is currently at location s3, driver3 is at location s0, locations p1_2 and s1 have a path between them, locations p2_0 and s0 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p3_0 have a path between them, locations s2 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s2 and s1 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s0, package2 is at location s2, package3 is at location s0, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_1, there exists a path between the locations s1 and p0_1, there exists a path between the locations s1 and p1_2, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s2, there is a link between location s0 and location s3, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s3, there is a link between location s3 and location s1, there is a path between location p1_2 and location s2, there is a path between location p1_3 and location s3, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s1 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver3 boards truck1 at location s0, package3 and package1 are loaded into truck1 at location s0, then truck1 is driven by driver3 from location s0 to s3, where package1 is unloaded, and subsequently truck1 is driven from location s3 to s1 by driver3, after which driver3 disembarks from truck1 at location s1, and package3 is unloaded from truck1 at location s1. Meanwhile, truck2 is loaded with package2 at location s2. Driver1 walks from location s3 to p3_0 and then to location s0, while driver2 walks from location s3 to p1_3, then to location s1, followed by p1_2, and finally to location s2. Driver3 walks from location s1 to p1_2 and then to location s2, where driver3 boards truck2, and truck2 is driven by driver3 from location s2 to s3 to reach the current state. In this state, if driver3 disembarks from truck2 at location s3, is it True or False that driver3 is currently at location s3 and truck2 is no longer being driven by driver3?", "initial_state_nl_paraphrased": "Driver1 is presently located at s3, while driver2 is also at location s3, and driver3 is situated at location s0. A path exists between locations p1_2 and s1, as well as between p2_0 and s0, p3_0 and s3, s0 and p3_0, s2 and p1_2, and s2 and p2_0. Furthermore, a link is present between locations s2 and s0, s2 and s1, s3 and p3_0, and s3 and s0. Package1 is currently at location s0, package2 is at location s2, and package3 is also at location s0. Additionally, a link exists between locations s3 and s2, and paths exist between p0_1 and s0, p0_1 and s1, p1_3 and s1, s0 and p0_1, s1 and p0_1, s1 and p1_2, and s3 and p1_3. Links are also present between locations s0 and s2, s0 and s3, s1 and s2, s1 and s3, s2 and s3, and s3 and s1. Moreover, paths exist between p1_2 and s2, p1_3 and s3, p2_0 and s2, p3_0 and s0, s0 and p2_0, and s1 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and situated at location s2."}
{"question_id": "b4320cf9-ef99-4d08-b50a-3191101b961c", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to p4_3, driver1 walks from location p4_3 to s4, driver1 walks from location s4 to p4_1, driver1 walks from location p4_1 to s1, at location s1, driver1 boards truck1, truck1 is driven from location s1 to s0 by driver1, package4 is loaded in truck1 at location s0, driver1 drives truck1 from location s0 to location s2, package2 is loaded in truck1 at location s2, package1 is loaded in truck1 at location s2, driver1 drives truck1 to location s3 from location s2, at location s3, package3 is loaded in truck1, package1 is unloaded from truck1 at location s3, truck1 is driven from location s3 to s4 by driver1, package4 is unloaded from truck1 at location s4, package3 is unloaded from truck1 at location s4, package2 is unloaded from truck1 at location s4, truck1 is driven from location s4 to s1 by driver1 and from truck1, driver1 disembarks at location s1 to reach the current state. In this state, if driver2 walks to location p4_0 from location s4, is it True or False that driver2 is at location p4_0 and driver2 is not present at location s4?", "answer": "True", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s3, driver2 is at location s4, driver3 is present at location s3, locations p0_5 and s0 have a path between them, locations p0_5 and s5 have a path between them, locations p4_0 and s0 have a path between them, locations p4_1 and s1 have a path between them, locations p4_3 and s3 have a path between them, locations p4_3 and s4 have a path between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s2 and s3 have a link between them, locations s3 and s2 have a link between them, locations s4 and s0 have a link between them, locations s5 and s4 have a link between them, package1 is currently at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s4 and s1, there exists a link between the locations s4 and s5, there exists a link between the locations s5 and s0, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a path between the locations p4_1 and s4, there exists a path between the locations s1 and p4_1, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_0, there exists a path between the locations s4 and p4_1, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s5, there is a link between location s3 and location s4, there is a link between location s4 and location s3, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s2, there is a path between location p5_2 and location s5, there is a path between location s0 and location p0_5, there is a path between location s0 and location p4_0, there is a path between location s2 and location p5_2, there is a path between location s4 and location p4_3, there is a path between location s5 and location p0_5, truck1 contains nothing, truck1 is present at location s1, truck2 is empty and truck2 is present at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by s4 to p4_1, and p4_1 to s1. Upon reaching s1, driver1 boards truck1, and then drives it from s1 to s0. At s0, package4 is loaded onto truck1. Driver1 then drives truck1 from s0 to s2, where package2 and package1 are loaded. Next, driver1 drives truck1 from s2 to s3, where package3 is loaded and package1 is unloaded. Subsequently, truck1 is driven from s3 to s4, where package4, package3, and package2 are unloaded. Finally, driver1 drives truck1 from s4 to s1 and disembarks at s1, reaching the current state. In this state, if driver2 walks from location s4 to p4_0, is it True or False that driver2 is at location p4_0 and driver2 is no longer at location s4?", "initial_state_nl_paraphrased": "Driver1 is located at s3, driver2 is at s4, and driver3 is also at s3. A path exists between p0_5 and s0, as well as between p0_5 and s5. Additionally, paths are present between p4_0 and s0, p4_1 and s1, p4_3 and s3, and p4_3 and s4. Furthermore, links exist between s0 and s4, s1 and s4, s2 and s3, s3 and s2, s4 and s0, and s5 and s4. Package1 and package2 are both currently at s2, package3 is at s3, and package4 is at s0. Links also exist between s0 and s5, s1 and s0, s3 and s5, s4 and s1, s4 and s5, s5 and s0, s5 and s2, and s5 and s3. Moreover, paths are present between p4_1 and s4, s1 and p4_1, s3 and p4_3, s4 and p4_0, s4 and p4_1, and s5 and p5_2. There are also links between s0 and s1, s0 and s2, s1 and s2, s2 and s0, s2 and s1, s2 and s5, s3 and s4, and s4 and s3. Paths exist between p4_0 and s4, p5_2 and s2, p5_2 and s5, s0 and p0_5, s0 and p4_0, s2 and p5_2, s4 and p4_3, and s5 and p0_5. Truck1 is empty and located at s1, while truck2 is also empty and at s5."}
{"question_id": "cf8d1766-881a-4ec6-a9da-59409fac4d4d", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks to location p3_0 from location s3, driver2 walks from location p3_0 to s0, at location s0, driver2 boards truck1, truck1 is driven from location s0 to s2 by driver2, truck1 is loaded with package3 at location s2, at location s2, package2 is loaded in truck1, driver2 drives truck1 from location s2 to location s0, package2 is unloaded from truck1 at location s0, truck1 is driven from location s0 to s3 by driver2, at location s3, package1 is loaded in truck1, driver2 drives truck1 to location s1 from location s3, driver2 disembarks from truck1 at location s1, driver2 walks to location p0_1 from location s1, driver2 walks from location p0_1 to location s0, truck3 is boarded by driver2 at location s0, driver2 drives truck3 to location s2 from location s0, at location s1, package3 is unloaded in truck1, truck1 is unloaded with package1 at location s1 and driver3 walks from location s3 to location p3_0 to reach the current state. In this state, if driver3 walks from location p3_0 to location s0, is it True or False that driver3 is currently at location s0 and driver3 is not currently at location p3_0?", "answer": "True", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s1, driver2 is at location s3, driver3 is present at location s3, locations s1 and p1_2 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and s0 have a link between them, locations s1 and s3 have a link between them, locations s2 and s0 have a link between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is at location s2, package3 is present at location s2, package4 is at location s1, there exists a link between the locations s0 and s1, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s1 and p0_1, there exists a path between the locations s2 and p1_2, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a path between location p0_1 and location s0, there is a path between location p1_2 and location s2, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p0_1, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 contains nothing, truck1 is present at location s0, truck2 is empty, truck2 is present at location s3, truck3 is empty and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver2 moves from location s3 to location p3_0, then from location p3_0 to location s0, where driver2 boards truck1. Driver2 then drives truck1 from location s0 to location s2, loads truck1 with package3 at location s2, and also loads package2 into truck1 at location s2. Next, driver2 drives truck1 from location s2 to location s0, unloads package2 from truck1 at location s0, and then drives truck1 from location s0 to location s3. At location s3, package1 is loaded into truck1, and driver2 drives truck1 from location s3 to location s1. Driver2 then disembarks from truck1 at location s1, walks to location p0_1 from location s1, and then walks from location p0_1 to location s0. At location s0, driver2 boards truck3 and drives it from location s0 to location s2. Meanwhile, package3 is unloaded from truck1 at location s1, and package1 is also unloaded from truck1 at location s1. Driver3 walks from location s3 to location p3_0 to reach the current state. In this state, if driver3 walks from location p3_0 to location s0, is it True or False that driver3 is currently at location s0 and driver3 is not currently at location p3_0?", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is at s3, and driver3 is also at s3. A path exists between s1 and p1_2, as well as between s1 and p1_3, and a link is present between s1 and s0, s1 and s3, s2 and s0, and s3 and s0. Package1 is currently at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. There are links between s0 and s1, s0 and s3, and s3 and s1. Paths exist between p0_1 and s1, p1_2 and s1, p1_3 and s1, p1_3 and s3, s1 and p0_1, and s2 and p1_2. Additionally, links are present between s0 and s2, s1 and s2, and s2 and s1. Paths also exist between p0_1 and s0, p1_2 and s2, p2_0 and s0, p2_0 and s2, p3_0 and s0, and p3_0 and s3. Furthermore, paths are present between s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is also empty and at s0."}
{"question_id": "ec354bff-8efd-480d-8bb1-36040423b590", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: truck1 is loaded with package3 at location s0, driver1 walks to location p0_2 from location s2, driver1 walks from location p0_2 to s0, at location s0, driver1 boards truck1, driver1 drives truck1 from location s0 to location s3, at location s3, driver1 disembarks from truck1, truck1 is unloaded with package3 at location s3, driver1 walks to location p0_3 from location s3, driver1 walks to location s0 from location p0_3, driver2 walks to location p0_2 from location s2, driver2 walks to location s0 from location p0_2, at location s0, driver2 boards truck2, truck2 is driven from location s0 to s1 by driver2, package1 is loaded in truck2 at location s1, truck2 is driven from location s1 to s2 by driver2, at location s2, package2 is loaded in truck2, truck2 is unloaded with package1 at location s2, driver2 drives truck2 to location s1 from location s2 and from truck2, driver2 disembarks at location s1 to reach the current state. In this state, if at location s1, package2 is unloaded in truck2, is it True or False that driver1 is present at location s2 and package3 is not currently at location s0?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s2, driver2 is currently at location s2, locations p0_2 and s2 have a path between them, locations p1_3 and s3 have a path between them, locations s3 and s1 have a link between them, locations s3 and s2 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is currently at location s0, package4 is at location s2, there exists a link between the locations s0 and s2, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s0, there exists a path between the locations p0_1 and s0, there exists a path between the locations p0_2 and s0, there exists a path between the locations p0_3 and s0, there exists a path between the locations p0_3 and s3, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_3, there exists a path between the locations s1 and p1_3, there exists a path between the locations s2 and p2_1, there exists a path between the locations s3 and p0_3, there exists a path between the locations s3 and p1_3, there is a link between location s0 and location s1, there is a link between location s1 and location s0, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a path between location p0_1 and location s1, there is a path between location p2_1 and location s1, there is a path between location p2_1 and location s2, there is a path between location s0 and location p0_1, there is a path between location s0 and location p0_2, there is a path between location s1 and location p0_1, there is a path between location s1 and location p2_1, there is a path between location s2 and location p0_2, truck1 is currently at location s0, truck1 is empty, truck2 is empty and truck2 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: package3 is loaded onto truck1 at location s0, driver1 moves from location s2 to p0_2, then proceeds from p0_2 to s0, boards truck1 at location s0, drives truck1 from s0 to s3, disembarks from truck1 at s3, unloads package3 from truck1 at s3, and then walks from s3 to p0_3 and back to s0. Meanwhile, driver2 moves from s2 to p0_2, then to s0, boards truck2 at s0, drives truck2 from s0 to s1, loads package1 into truck2 at s1, drives truck2 from s1 to s2, loads package2 into truck2 at s2, unloads package1 from truck2 at s2, drives truck2 back to s1 from s2, and finally disembarks from truck2 at s1, resulting in the current state. In this state, if package2 is unloaded from truck2 at location s1, is it True or False that driver1 is at location s2 and package3 is no longer at location s0?", "initial_state_nl_paraphrased": "Driver1 is currently located at s2, while driver2 is also present at location s2. A path exists between locations p0_2 and s2, as well as between p1_3 and s3. Locations s3 and s1 are connected by a link, and s3 and s2 are also linked. Package1 is located at s1, package2 is at s2, package3 is at s0, and package4 is also at s2. There are links between s0 and s2, s0 and s3, and s3 and s0. Paths exist between p0_1 and s0, p0_2 and s0, p0_3 and s0, p0_3 and s3, p1_3 and s1, s0 and p0_3, s1 and p1_3, s2 and p2_1, s3 and p0_3, and s3 and p1_3. Additionally, links exist between s0 and s1, s1 and s0, s1 and s2, s1 and s3, s2 and s0, s2 and s1, and s2 and s3. Furthermore, paths exist between p0_1 and s1, p2_1 and s1, p2_1 and s2, s0 and p0_1, s0 and p0_2, s1 and p0_1, s1 and p2_1, and s2 and p0_2. Truck1 is currently empty and located at s0, and truck2 is also empty and present at s0."}
{"question_id": "7da79d1e-2dec-4c6e-930d-79e9c6282d67", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver2 walks to location p3_0 from location s3, driver2 walks from location p3_0 to s0, driver2 boards truck1 at location s0, driver2 drives truck1 from location s0 to location s2, at location s2, package3 is loaded in truck1, truck1 is loaded with package2 at location s2, driver2 drives truck1 to location s0 from location s2, package2 is unloaded from truck1 at location s0, driver2 drives truck1 from location s0 to location s3, package1 is loaded in truck1 at location s3, driver2 drives truck1 to location s1 from location s3, from truck1, driver2 disembarks at location s1, driver2 walks to location p0_1 from location s1, driver2 walks from location p0_1 to location s0, at location s0, driver2 boards truck3, driver2 drives truck3 to location s2 from location s0, package3 is unloaded from truck1 at location s1, truck1 is unloaded with package1 at location s1 and driver3 walks to location p3_0 from location s3 to reach the current state. In this state, if driver3 walks from location p3_0 to s0, is it True or False that driver2 is present at location p1_3?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s1, driver2 is at location s3, driver3 is present at location s3, locations s1 and p1_2 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and s0 have a link between them, locations s1 and s3 have a link between them, locations s2 and s0 have a link between them, locations s3 and s0 have a link between them, package1 is currently at location s3, package2 is at location s2, package3 is present at location s2, package4 is at location s1, there exists a link between the locations s0 and s1, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p0_1 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s1 and p0_1, there exists a path between the locations s2 and p1_2, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a path between location p0_1 and location s0, there is a path between location p1_2 and location s2, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s0 and location p0_1, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 contains nothing, truck1 is present at location s0, truck2 is empty, truck2 is present at location s3, truck3 is empty and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver2 moves from location s3 to p3_0, then from p3_0 to s0, boards truck1 at s0, drives truck1 from s0 to s2, loads package3 and package2 into truck1 at s2, drives truck1 back to s0, unloads package2 at s0, drives truck1 to s3, loads package1 into truck1 at s3, drives truck1 to s1, disembarks from truck1 at s1, walks to p0_1 and then to s0, boards truck3 at s0, drives truck3 to s2, unloads package3 from truck1 at s1, and unloads package1 from truck1 at s1, while driver3 moves from s3 to p3_0 to reach the current state. In this state, if driver3 walks from p3_0 to s0, is it True or False that driver2 is present at location p1_3?", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is at s3, and driver3 is also at s3. A path exists between s1 and p1_2, as well as between s1 and p1_3, and a link is present between s1 and s0, s1 and s3, s2 and s0, and s3 and s0. Package1 is currently at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Links exist between s0 and s1, s0 and s3, and s3 and s1. Paths are present between p0_1 and s1, p1_2 and s1, p1_3 and s1, p1_3 and s3, s1 and p0_1, s2 and p1_2, p0_1 and s0, p1_2 and s2, p2_0 and s0, p2_0 and s2, p3_0 and s0, and p3_0 and s3. Additionally, links exist between s0 and s2, s1 and s2, and s2 and s1. Paths also exist between s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is empty and located at s0, truck2 is empty and at s3, and truck3 is empty and at s0."}
{"question_id": "baa615c9-8bd8-48ac-816c-86fe11f893fc", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: driver1 walks from location s3 to location p4_3, driver1 walks to location s4 from location p4_3, driver1 walks from location s4 to p4_1, driver1 walks to location s1 from location p4_1, truck1 is boarded by driver1 at location s1, truck1 is driven from location s1 to s0 by driver1, package4 is loaded in truck1 at location s0, truck1 is driven from location s0 to s2 by driver1, truck1 is loaded with package2 at location s2 and package1 is loaded in truck1 at location s2 to reach the current state. In this state, if driver1 drives truck1 from location s2 to location s3, is it True or False that truck1 is currently at location s3 and truck1 is not present at location s2?", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s3, driver2 is at location s4, driver3 is present at location s3, locations p0_5 and s0 have a path between them, locations p0_5 and s5 have a path between them, locations p4_0 and s0 have a path between them, locations p4_1 and s1 have a path between them, locations p4_3 and s3 have a path between them, locations p4_3 and s4 have a path between them, locations s0 and s4 have a link between them, locations s1 and s4 have a link between them, locations s2 and s3 have a link between them, locations s3 and s2 have a link between them, locations s4 and s0 have a link between them, locations s5 and s4 have a link between them, package1 is currently at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is currently at location s0, there exists a link between the locations s0 and s5, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s4 and s1, there exists a link between the locations s4 and s5, there exists a link between the locations s5 and s0, there exists a link between the locations s5 and s2, there exists a link between the locations s5 and s3, there exists a path between the locations p4_1 and s4, there exists a path between the locations s1 and p4_1, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_0, there exists a path between the locations s4 and p4_1, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s1, there is a link between location s0 and location s2, there is a link between location s1 and location s2, there is a link between location s2 and location s0, there is a link between location s2 and location s1, there is a link between location s2 and location s5, there is a link between location s3 and location s4, there is a link between location s4 and location s3, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s2, there is a path between location p5_2 and location s5, there is a path between location s0 and location p0_5, there is a path between location s0 and location p4_0, there is a path between location s2 and location p5_2, there is a path between location s4 and location p4_3, there is a path between location s5 and location p0_5, truck1 contains nothing, truck1 is present at location s1, truck2 is empty and truck2 is present at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by s4 to p4_1, and finally from p4_1 to s1. At location s1, driver1 boards truck1, then drives it from s1 to s0. At s0, package4 is loaded into truck1, which is then driven to s2 by driver1. At location s2, truck1 is loaded with both package2 and package1, resulting in the current state. In this state, if driver1 drives truck1 from location s2 to s3, is it True or False that truck1 is now at location s3 and no longer at location s2?", "initial_state_nl_paraphrased": "Driver1 is located at s3, driver2 is at s4, and driver3 is also at s3. A path exists between p0_5 and s0, as well as between p0_5 and s5. Additionally, paths are present between p4_0 and s0, p4_1 and s1, p4_3 and s3, and p4_3 and s4. Furthermore, links exist between s0 and s4, s1 and s4, s2 and s3, s3 and s2, s4 and s0, and s5 and s4. Package1 and package2 are currently at s2, package3 is at s3, and package4 is at s0. Links also exist between s0 and s5, s1 and s0, s3 and s5, s4 and s1, s4 and s5, s5 and s0, s5 and s2, and s5 and s3. Moreover, paths are present between p4_1 and s4, s1 and p4_1, s3 and p4_3, s4 and p4_0, s4 and p4_1, and s5 and p5_2. There are also links between s0 and s1, s0 and s2, s1 and s2, s2 and s0, s2 and s1, s2 and s5, s3 and s4, and s4 and s3. Paths exist between p4_0 and s4, p5_2 and s2, p5_2 and s5, s0 and p0_5, s0 and p4_0, s2 and p5_2, s4 and p4_3, and s5 and p0_5. Truck1 is empty and located at s1, while truck2 is also empty and at s5."}
