{"question_id": "0ff44d69-ba3d-43db-8685-48a3426e5648", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: truck1 is loaded with package3 at location p0_3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_3 and s3 have a path between them, locations p1_3 and s3 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_1 have a path between them, locations s2 and p2_1 have a path between them, locations s2 and s0 have a link between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is present at location s0, package4 is currently at location s2, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_2, there exists a path between the locations s3 and p0_3, there is a link between location s1 and location s2, there is a link between location s3 and location s1, there is a path between location p0_2 and location s2, there is a path between location p2_1 and location s1, there is a path between location s0 and location p0_1, there is a path between location s2 and location p0_2, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: loading truck1 with package3 at location p0_3. Is the execution of this action feasible, True or False?", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at location s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s2, s0 and p0_3, s1 and p0_1, s1 and p1_3, s1 and p2_1, s2 and p2_1, s3 and p1_3. Furthermore, a link exists between locations s0 and s2, s0 and s3, s2 and s0, s3 and s0. Package1 is currently at location s1, package2 is at location s2, package3 is at location s0, and package4 is at location s2. Links are present between locations s0 and s1, s1 and s0, s1 and s3, s2 and s1, s2 and s3, s3 and s2, s1 and s2, and s3 and s1. Additionally, paths exist between locations p0_1 and s1, p0_3 and s0, p1_3 and s1, s0 and p0_2, s3 and p0_3, p0_2 and s2, p2_1 and s1, s0 and p0_1, and s2 and p0_2. Lastly, truck1 is located at s0 and is empty, while truck2 is also at location s0 and contains nothing."}
{"question_id": "9cecea29-089c-4b8a-a384-95c4a47e6788", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: package3 is loaded in truck1 at location s0, driver1 walks to location p0_2 from location s2, driver1 walks to location s0 from location p0_2, truck1 is boarded by driver1 at location s0, driver1 drives truck1 to location s3 from location s0, at location s3, driver1 disembarks from truck1, truck1 is unloaded with package3 at location s3, driver2 walks from location s1 to s3, driver1 walks to location s0 from location p0_3 and driver2 walks to location p0_2 from location s2. Is the action: driver2 walks from location s1 to location s3 executable at step 8, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_3 and s3 have a path between them, locations p1_3 and s3 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_1 have a path between them, locations s2 and p2_1 have a path between them, locations s2 and s0 have a link between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is present at location s0, package4 is currently at location s2, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_2, there exists a path between the locations s3 and p0_3, there is a link between location s1 and location s2, there is a link between location s3 and location s1, there is a path between location p0_2 and location s2, there is a path between location p2_1 and location s1, there is a path between location s0 and location p0_1, there is a path between location s2 and location p0_2, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 10: package3 is loaded onto truck1 at location s0, driver1 moves from location s2 to location p0_2, then from location p0_2 to location s0, boards truck1 at location s0, drives truck1 from location s0 to location s3, disembarks from truck1 at location s3, unloads package3 from truck1 at location s3, driver2 moves from location s1 to location s3, driver1 moves from location p0_3 to location s0, and driver2 moves from location s2 to location p0_2. Is the action: driver2 moves from location s1 to location s3 executable at step 8, True or False?", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at location s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s2, s0 and p0_3, s1 and p0_1, s1 and p1_3, s1 and p2_1, s2 and p2_1, s3 and p1_3. Furthermore, a link exists between locations s0 and s2, s0 and s3, s2 and s0, s3 and s0. Package1 is currently situated at location s1, package2 is at location s2, package3 is at location s0, and package4 is also at location s2. Additionally, links exist between locations s0 and s1, s1 and s0, s1 and s3, s2 and s1, s2 and s3, s3 and s2, s1 and s2, and s3 and s1. Paths also exist between locations p0_1 and s1, p0_3 and s0, p1_3 and s1, s0 and p0_2, s3 and p0_3, p0_2 and s2, p2_1 and s1, s0 and p0_1, and s2 and p0_2. Lastly, truck1 is located at s0 and is empty, while truck2 is also at location s0 and contains nothing."}
{"question_id": "263f26e9-bdf8-41dd-ba0f-0c8444a1dc42", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: driver3 boards truck1 at location s0, truck1 is loaded with package3 at location s0, truck1 is loaded with package1 at location s0, driver3 drives truck1 from location s0 to location s3, driver1 walks from location s2 to s0, truck1 is driven from location s3 to s1 by driver3, driver3 disembarks from truck1 at location s1, at location s1, package3 is unloaded in truck1, package2 is loaded in truck2 at location s2, driver1 walks from location s3 to location p3_0, driver1 walks from location p3_0 to s0, driver2 walks from location s3 to p1_3, driver2 walks from location p1_3 to location s1, driver2 walks to location p1_2 from location s1, driver2 walks to location s2 from location p1_2, driver3 walks from location s1 to p1_2, driver3 walks from location p1_2 to location s2, truck2 is boarded by driver3 at location s2 and driver3 drives truck2 to location s3 from location s2. Is the action: driver1 walks to location s0 from location s2 executable at step 5, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s3, driver2 is present at location s3, driver3 is currently at location s0, locations p0_1 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and s2 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is present at location s2, package3 is at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s3 and location s0, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: driver3 gets on truck1 at location s0, truck1 is loaded with package3 at location s0, truck1 is also loaded with package1 at location s0, driver3 drives truck1 from location s0 to location s3, driver1 moves from location s2 to s0 on foot, truck1 is driven by driver3 from location s3 to s1, driver3 gets off truck1 at location s1, at location s1, package3 is unloaded from truck1, package2 is loaded into truck2 at location s2, driver1 walks from location s3 to p3_0 and then to s0, driver2 walks from location s3 to p1_3 and then to location s1, driver2 then walks to location p1_2 and then to location s2, driver3 walks from location s1 to p1_2 and then to location s2, driver3 boards truck2 at location s2 and drives it to location s3 from location s2. Is the action: driver1 walks from location s2 to location s0 executable at step 5, True or False?", "initial_state_nl_paraphrased": "Driver1 is located at s3, while driver2 is also at s3. Meanwhile, driver3 is currently situated at s0. There is a path connecting p0_1 and s0, as well as between p2_0 and s2, and p3_0 and s0. A link exists between s0 and s2, and another between s1 and p0_1, s1 and p1_2, s2 and p2_0, s2 and s0, s3 and p3_0, and s3 and s2. Package1 is currently at s0, package2 is at s2, and package3 is also at s0. Links are present between s0 and s3, s3 and s1, and paths exist between p1_2 and s1, p1_2 and s2, p1_3 and s3, s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p1_2. Additionally, links are found between s1 and s2, s1 and s3, s2 and s1, s2 and s3, and s3 and s0. Paths also exist between p0_1 and s1, p1_3 and s1, p2_0 and s0, p3_0 and s3, s1 and p1_3, and s3 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and present at s2."}
{"question_id": "6b5f2e75-249a-4f29-a982-4fc3aa3b8807", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location s1, package1 is loaded in truck2, at location s2, package1 is unloaded in truck2, at location s2, package2 is loaded in truck2, driver1 drives truck1 to location s3 from location s0, driver1 walks from location p0_3 to location s0, driver1 walks from location s2 to p0_2, driver1 walks to location p0_3 from location s3, driver1 walks to location s0 from location p0_2, driver2 drives truck2 from location s2 to location s1, driver2 drives truck2 to location s1 from location s0, driver2 drives truck2 to location s2 from location s1, driver2 walks from location s2 to location p0_2, driver2 walks to location s0 from location p0_2, from truck1, driver1 disembarks at location s3, from truck2, driver2 disembarks at location s1, package3 is loaded in truck1 at location s0, truck1 is boarded by driver1 at location s0, truck1 is unloaded with package3 at location s3 and truck2 is boarded by driver2 at location s0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_3 and s3 have a path between them, locations p1_3 and s3 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_1 have a path between them, locations s2 and p2_1 have a path between them, locations s2 and s0 have a link between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is present at location s0, package4 is currently at location s2, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_2, there exists a path between the locations s3 and p0_3, there is a link between location s1 and location s2, there is a link between location s3 and location s1, there is a path between location p0_2 and location s2, there is a path between location p2_1 and location s1, there is a path between location s0 and location p0_1, there is a path between location s2 and location p0_2, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: at location s1, truck2 will be loaded with package1, at location s2, package1 will be unloaded from truck2, and package2 will be loaded into truck2 at the same location. Meanwhile, driver1 will drive truck1 from location s0 to location s3, and then walk between various locations (p0_3 to s0, s2 to p0_2, s3 to p0_3, and p0_2 to s0). Driver2 will drive truck2 from location s2 to location s1, then from s0 to s1, and finally from s1 to s2, while also walking between locations s2 and p0_2, and p0_2 and s0. Additionally, driver1 will disembark from truck1 at location s3, and driver2 will disembark from truck2 at location s1. Furthermore, package3 will be loaded into truck1 at location s0, driver1 will board truck1 at the same location, truck1 will be unloaded with package3 at location s3, and driver2 will board truck2 at location s0. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at location s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s2, s0 and p0_3, s1 and p0_1, s1 and p1_3, s1 and p2_1, s2 and p2_1, and s3 and p1_3. Furthermore, a link exists between locations s0 and s2, s0 and s3, s2 and s0, s3 and s0, s0 and s1, s1 and s0, s1 and s3, s2 and s1, s2 and s3, s3 and s2, s1 and s2, and s3 and s1. Package1 is currently at location s1, package2 is at location s2, package3 is at location s0, and package4 is at location s2. Additionally, there are links between s0 and s1, s1 and s0, s1 and s3, s2 and s1, s2 and s3, and s3 and s2. There are also paths between p0_1 and s1, p0_3 and s0, p1_3 and s1, s0 and p0_2, s3 and p0_3, p0_2 and s2, p2_1 and s1, s0 and p0_1, and s2 and p0_2. Lastly, truck1 is at location s0 and is empty, while truck2 is also at location s0 and contains nothing."}
{"question_id": "288e6f59-3a66-496a-9986-a00827bde141", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: truck1 is loaded with package3 at location s0. Is the action: package3 is loaded in truck1 at location s0 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_3 and s3 have a path between them, locations p1_3 and s3 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_1 have a path between them, locations s2 and p2_1 have a path between them, locations s2 and s0 have a link between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is present at location s0, package4 is currently at location s2, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_2, there exists a path between the locations s3 and p0_3, there is a link between location s1 and location s2, there is a link between location s3 and location s1, there is a path between location p0_2 and location s2, there is a path between location p2_1 and location s1, there is a path between location s0 and location p0_1, there is a path between location s2 and location p0_2, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: truck1 will be loaded with package3 at location s0. Is the action of loading package3 into truck1 at location s0 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at location s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s2, s0 and p0_3, s1 and p0_1, s1 and p1_3, s1 and p2_1, s2 and p2_1, s3 and p1_3. Furthermore, a link exists between locations s0 and s2, s0 and s3, s2 and s0, s3 and s0. Package1 is currently situated at location s1, package2 is at location s2, package3 is at location s0, and package4 is also at location s2. Additionally, links exist between locations s0 and s1, s1 and s0, s1 and s3, s2 and s1, s2 and s3, s3 and s2, s1 and s2, and s3 and s1. Paths also exist between locations p0_1 and s1, p0_3 and s0, p1_3 and s1, s0 and p0_2, s3 and p0_3, p0_2 and s2, p2_1 and s1, s0 and p0_1, and s2 and p0_2. Lastly, truck1 is located at s0 and is empty, while truck2 is also at location s0 and contains nothing."}
{"question_id": "8eb192f9-9eaf-4c6d-bc45-6b8b3d9fa97d", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: driver1 walks to location p4_3 from location s3, driver1 walks from location p4_3 to s4, driver1 walks from location s4 to p4_1, driver1 walks from location p4_1 to s1, driver1 boards truck1 at location s1, driver1 drives truck1 to location s0 from location s1, at location s0, package4 is loaded in truck1, driver1 drives truck1 to location s2 from location s0, at location s2, package2 is loaded in truck1 and at location s2, package1 is loaded in truck1. Is the action: truck1 is loaded with package4 at location s0 executable at step 7, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s3, driver2 is currently at location s4, driver3 is present at location s3, locations p5_2 and s2 have a path between them, locations s0 and p0_5 have a path between them, locations s1 and s4 have a link between them, locations s2 and p5_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s2 have a link between them, locations s3 and s4 have a link between them, locations s4 and p4_0 have a path between them, locations s5 and p0_5 have a path between them, locations s5 and s0 have a link between them, locations s5 and s2 have a link between them, package1 is at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is at location s0, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_1 and s1, there exists a path between the locations p4_1 and s4, there exists a path between the locations p4_3 and s3, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p4_0, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_1, there exists a path between the locations s4 and p4_3, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s2, there is a link between location s0 and location s4, there is a link between location s0 and location s5, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s2 and location s5, there is a link between location s4 and location s0, there is a link between location s4 and location s1, there is a link between location s4 and location s3, there is a link between location s4 and location s5, there is a link between location s5 and location s3, there is a path between location p0_5 and location s5, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s5, there is a path between location s1 and location p4_1, truck1 is currently at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 10: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by s4 to p4_1, and then p4_1 to s1, where driver1 boards truck1. Subsequently, driver1 drives truck1 from s1 to s0, at which point package4 is loaded onto truck1. Driver1 then drives truck1 from s0 to s2, where both package2 and package1 are loaded onto truck1. Is the action of loading package4 onto truck1 at location s0 executable at step 7, True or False?", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is at location s4, and driver3 is also at location s3. A path exists between locations p5_2 and s2, as well as between locations s0 and p0_5. Locations s1 and s4 are connected by a link, and locations s2 and p5_2 also have a path between them. Furthermore, locations s2 and s0 are linked, locations s3 and s2 are connected, and locations s3 and s4 are also linked. A path exists between locations s4 and p4_0, locations s5 and p0_5, and locations s5 and s0 are connected by a link. Locations s5 and s2 are also linked. Package1 is located at s2, package2 is currently at s2, package3 is at location s3, and package4 is at location s0. There are links between locations s0 and s1, s1 and s0, s3 and s5, and s5 and s4. Paths exist between locations p0_5 and s0, p4_0 and s0, p4_1 and s1, p4_1 and s4, p4_3 and s3, and p4_3 and s4. Additionally, paths exist between locations s0 and p4_0, s3 and p4_3, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Links exist between locations s0 and s2, s0 and s4, s0 and s5, s1 and s2, s2 and s1, s2 and s3, s2 and s5, s4 and s0, s4 and s1, s4 and s3, s4 and s5, and s5 and s3. Paths also exist between locations p0_5 and s5, p4_0 and s4, p5_2 and s5, and s1 and p4_1. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "09e3cd30-1a9e-45ec-98d6-e0025b1bf8e1", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location s0, package1 is loaded in truck1, at location s2, package2 is loaded in truck2, driver1 walks from location s3 to p3_0, driver2 drives truck1 from location p2_0 to location p1_3, driver3 boards truck1 at location s0, driver3 drives truck1 from location s0 to location s3, from truck1, driver3 disembarks at location s1, truck1 is driven from location s3 to s1 by driver3, truck1 is unloaded with package1 at location s3 and truck1 is unloaded with package3 at location s1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s3, driver2 is present at location s3, driver3 is currently at location s0, locations p0_1 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and s2 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is present at location s2, package3 is at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s3 and location s0, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at location s0, package1 is loaded onto truck1, at location s2, package2 is loaded onto truck2, driver1 walks from location s3 to p3_0, driver2 drives truck1 from location p2_0 to location p1_3, driver3 boards truck1 at location s0, driver3 drives truck1 from location s0 to location s3, then driver3 gets off truck1 at location s1, driver3 drives truck1 from location s3 to s1, package1 is unloaded from truck1 at location s3 and package3 is unloaded from truck1 at location s1. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is also at location s3. Meanwhile, driver3 is at location s0. A path exists between locations p0_1 and s0, as well as between p2_0 and s2, and p3_0 and s0. Locations s0 and s2 are connected by a link, and s1 has paths to both p0_1 and p1_2. Similarly, s2 has paths to p2_0 and is linked to s0. Location s3 is connected to p3_0 and has a link to s2. Package1 is at location s0, package2 is at s2, and package3 is also at s0. Links exist between s0 and s3, s3 and s1, and paths exist between p1_2 and s1, p1_2 and s2, and p1_3 and s3. Additionally, paths exist between s0 and p0_1, s0 and p2_0, and s0 and p3_0. Location s2 has paths to p1_2, and links exist between s1 and s2, s1 and s3, s2 and s1, s2 and s3, and s3 and s0. Furthermore, paths exist between p0_1 and s1, p1_3 and s1, p2_0 and s0, p3_0 and s3, s1 and p1_3, and s3 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and at location s2."}
{"question_id": "372559bd-3636-44c8-94cf-0c97f95d3ec0", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location s0, package1 is loaded in truck1, at location s1, package3 is unloaded in truck1, driver1 walks from location s3 to location p3_0, driver3 boards truck1 at location s0, driver3 disembarks from truck1 at location s1, driver3 drives truck1 from location s3 to location s1, driver3 drives truck1 to location s3 from location s0, package1 is unloaded from truck1 at location s3, package2 is loaded in truck2 at location s2 and package3 is loaded in truck1 at location s0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s3, driver2 is present at location s3, driver3 is currently at location s0, locations p0_1 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and s2 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is present at location s2, package3 is at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s3 and location s0, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at location s0, truck1 is loaded with package1, at location s1, truck1 is unloaded of package3, driver1 moves from location s3 to location p3_0, driver3 gets into truck1 at location s0, driver3 gets out of truck1 at location s1, driver3 drives truck1 from location s3 to location s1, driver3 drives truck1 from location s0 to location s3, truck1 is unloaded of package1 at location s3, truck2 is loaded with package2 at location s2, and truck1 is loaded with package3 at location s0. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Driver1 is located at s3, while driver2 is also at s3. Meanwhile, driver3 is currently situated at s0. There is a path connecting p0_1 and s0, as well as between p2_0 and s2, and p3_0 and s0. A link exists between s0 and s2, and another between s1 and p0_1, s1 and p1_2, s2 and p2_0, s2 and s0, s3 and p3_0, and s3 and s2. Package1 is currently at s0, package2 is at s2, and package3 is also at s0. Links are present between s0 and s3, s3 and s1, and paths exist between p1_2 and s1, p1_2 and s2, p1_3 and s3, s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p1_2, s1 and s2, s1 and s3, s2 and s1, s2 and s3, and s3 and s0. Additionally, paths are found between p0_1 and s1, p1_3 and s1, p2_0 and s0, p3_0 and s3, s1 and p1_3, and s3 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and at s2."}
{"question_id": "599be0dd-e663-47e0-a671-32c4062cdc83", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: driver1 walks from location s3 to location p4_3, driver1 walks from location p4_3 to s4, driver3 walks from location s1 to p0_5, driver1 walks from location p4_1 to s1, truck1 is boarded by driver1 at location s1, truck1 is driven from location s1 to s0 by driver1, at location s0, package4 is loaded in truck1, driver1 drives truck1 to location s2 from location s0, package2 is loaded in truck1 at location s2 and at location s2, package1 is loaded in truck1. Is the action: driver3 walks to location p0_5 from location s1 executable at step 3, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s3, driver2 is currently at location s4, driver3 is present at location s3, locations p5_2 and s2 have a path between them, locations s0 and p0_5 have a path between them, locations s1 and s4 have a link between them, locations s2 and p5_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s2 have a link between them, locations s3 and s4 have a link between them, locations s4 and p4_0 have a path between them, locations s5 and p0_5 have a path between them, locations s5 and s0 have a link between them, locations s5 and s2 have a link between them, package1 is at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is at location s0, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_1 and s1, there exists a path between the locations p4_1 and s4, there exists a path between the locations p4_3 and s3, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p4_0, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_1, there exists a path between the locations s4 and p4_3, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s2, there is a link between location s0 and location s4, there is a link between location s0 and location s5, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s2 and location s5, there is a link between location s4 and location s0, there is a link between location s4 and location s1, there is a link between location s4 and location s3, there is a link between location s4 and location s5, there is a link between location s5 and location s3, there is a path between location p0_5 and location s5, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s5, there is a path between location s1 and location p4_1, truck1 is currently at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are scheduled for steps 1 through 10: driver1 moves from location s3 to p4_3, then from p4_3 to s4, while driver3 moves from s1 to p0_5, and driver1 also moves from p4_1 to s1. Subsequently, driver1 boards truck1 at s1, drives it from s1 to s0, loads package4 into truck1 at s0, drives truck1 from s0 to s2, and loads packages 2 and 1 into truck1 at s2. Is the action of driver3 walking from s1 to p0_5 executable at step 3, True or False?", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is at location s4, and driver3 is also at location s3. A path exists between locations p5_2 and s2, as well as between locations s0 and p0_5. Locations s1 and s4 are connected by a link, and locations s2 and p5_2 also have a path between them. Furthermore, locations s2 and s0 are linked, locations s3 and s2 are connected, and locations s3 and s4 are also linked. A path exists between locations s4 and p4_0, locations s5 and p0_5, and locations s5 and s0 are connected by a link. Locations s5 and s2 are also linked. Package1 is located at s2, package2 is currently at s2, package3 is at location s3, and package4 is at location s0. There are links between locations s0 and s1, s1 and s0, s3 and s5, and s5 and s4. Paths exist between locations p0_5 and s0, p4_0 and s0, p4_1 and s1, p4_1 and s4, p4_3 and s3, and p4_3 and s4. Additionally, paths exist between locations s0 and p4_0, s3 and p4_3, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Links exist between locations s0 and s2, s0 and s4, s0 and s5, s1 and s2, s2 and s1, s2 and s3, s2 and s5, s4 and s0, s4 and s1, s4 and s3, s4 and s5, and s5 and s3. Paths also exist between locations p0_5 and s5, p4_0 and s4, p5_2 and s5, and s1 and p4_1. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "b4706c9a-1168-4dbf-b0cd-b7b9e2c3a92b", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: package3 is unloaded from truck1 at location p2_0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s3, driver2 is present at location s3, driver3 is currently at location s0, locations p0_1 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and s2 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is present at location s2, package3 is at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s3 and location s0, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: unloading package3 from truck1 at location p2_0. Is the execution of this action feasible, True or False?", "initial_state_nl_paraphrased": "Driver1 is located at s3, while driver2 is also at s3. Meanwhile, driver3 is currently situated at s0. There is a path connecting p0_1 and s0, as well as between p2_0 and s2, and p3_0 and s0. A link exists between s0 and s2, and another between s1 and p0_1, s1 and p1_2, s2 and p2_0, s2 and s0, s3 and p3_0, and s3 and s2. Package1 is currently at s0, package2 is at s2, and package3 is also at s0. Links are present between s0 and s3, s3 and s1, and paths exist between p1_2 and s1, p1_2 and s2, p1_3 and s3, s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p1_2, s1 and s2, s1 and s3, s2 and s1, s2 and s3, and s3 and s0. Additionally, paths are present between p0_1 and s1, p1_3 and s1, p2_0 and s0, p3_0 and s3, s1 and p1_3, and s3 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and situated at s2."}
{"question_id": "2c619b19-f0c1-4ce0-97b6-a338616aefbe", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: truck1 is loaded with package3 at location s0, driver1 walks from location s2 to location p0_2, driver1 walks to location s0 from location p0_2, truck1 is boarded by driver1 at location s0, truck1 is driven from location s0 to s3 by driver1, at location s3, driver1 disembarks from truck1, package3 is unloaded from truck1 at location s3, driver1 walks to location p0_3 from location s3, driver1 walks from location p0_3 to s0, driver2 walks from location s2 to p0_2, driver2 walks to location s0 from location p0_2, truck2 is boarded by driver2 at location s0, driver2 drives truck2 to location s1 from location s0, truck2 is loaded with package1 at location s1, driver2 drives truck2 to location s2 from location s1, at location s2, package2 is loaded in truck2, truck2 is unloaded with package1 at location s2, driver2 drives truck2 from location s2 to location s1 and from truck2, driver2 disembarks at location s1. Is the action: at location s3, package3 is unloaded in truck1 executable at step 7, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_3 and s3 have a path between them, locations p1_3 and s3 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_1 have a path between them, locations s2 and p2_1 have a path between them, locations s2 and s0 have a link between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is present at location s0, package4 is currently at location s2, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_2, there exists a path between the locations s3 and p0_3, there is a link between location s1 and location s2, there is a link between location s3 and location s1, there is a path between location p0_2 and location s2, there is a path between location p2_1 and location s1, there is a path between location s0 and location p0_1, there is a path between location s2 and location p0_2, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: at location s0, truck1 is loaded with package3, driver1 walks from location s2 to location p0_2 and then to location s0, driver1 boards truck1 at location s0, driver1 drives truck1 from location s0 to s3, at location s3, driver1 gets off truck1, package3 is unloaded from truck1 at location s3, driver1 walks from location s3 to p0_3 and then to s0, driver2 walks from location s2 to p0_2 and then to location s0, driver2 boards truck2 at location s0, driver2 drives truck2 from location s0 to s1, truck2 is loaded with package1 at location s1, driver2 drives truck2 from location s1 to s2, at location s2, package2 is loaded into truck2, package1 is unloaded from truck2 at location s2, driver2 drives truck2 from location s2 to location s1 and gets off truck2 at location s1. Is the action: at location s3, package3 is unloaded from truck1 executable at step 7, True or False?", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at location s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s2, s0 and p0_3, s1 and p0_1, s1 and p1_3, s1 and p2_1, s2 and p2_1, s3 and p1_3. Furthermore, a link exists between locations s0 and s2, s0 and s3, s2 and s0, s3 and s0. Package1 is currently situated at location s1, package2 is at location s2, package3 is at location s0, and package4 is also at location s2. Additionally, links exist between locations s0 and s1, s1 and s0, s1 and s3, s2 and s1, s2 and s3, s3 and s2, s1 and s2, and s3 and s1. Paths also exist between locations p0_1 and s1, p0_3 and s0, p1_3 and s1, s0 and p0_2, s3 and p0_3, p0_2 and s2, p2_1 and s1, s0 and p0_1, and s2 and p0_2. Lastly, truck1 is currently at location s0 and is empty, while truck2 is also at location s0 and contains nothing."}
{"question_id": "0c44c4d9-01c9-4726-a554-2a3471c190cd", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location s0, driver2 boards truck2, at location s1, package1 is loaded in truck2, at location s2, package2 is loaded in truck2, driver1 boards truck1 at location s0, driver1 drives truck1 to location s3 from location s0, driver1 walks from location s2 to p0_2, driver1 walks from location s3 to location p0_3, driver1 walks to location s0 from location p0_2, driver1 walks to location s0 from location p0_3, driver2 disembarks from truck2 at location s1, driver2 drives truck2 from location s0 to location s1, driver2 walks from location p0_2 to location s0, driver2 walks from location s2 to p0_3, driver2 walks to location p0_2 from location s2, from truck1, driver1 disembarks at location s3, package3 is loaded in truck1 at location s0, truck1 is unloaded with package3 at location s3, truck2 is driven from location s2 to s1 by driver2 and truck2 is unloaded with package1 at location s2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_3 and s3 have a path between them, locations p1_3 and s3 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_1 have a path between them, locations s2 and p2_1 have a path between them, locations s2 and s0 have a link between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is present at location s0, package4 is currently at location s2, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_2, there exists a path between the locations s3 and p0_3, there is a link between location s1 and location s2, there is a link between location s3 and location s1, there is a path between location p0_2 and location s2, there is a path between location p2_1 and location s1, there is a path between location s0 and location p0_1, there is a path between location s2 and location p0_2, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: at location s0, driver2 gets into truck2, at location s1, package1 is placed in truck2, at location s2, package2 is placed in truck2, driver1 boards truck1 at location s0, driver1 drives truck1 from location s0 to location s3, driver1 walks from location s2 to p0_2, driver1 walks from location s3 to location p0_3, driver1 returns to location s0 from location p0_2, driver1 returns to location s0 from location p0_3, driver2 gets out of truck2 at location s1, driver2 drives truck2 from location s0 to location s1, driver2 walks from location p0_2 to location s0, driver2 walks from location s2 to p0_3, driver2 walks from location s2 to p0_2, driver1 gets out of truck1 at location s3, package3 is loaded into truck1 at location s0, truck1 is unloaded with package3 at location s3, and driver2 drives truck2 from location s2 to s1 and unloads package1 from truck2 at location s2. Is it possible to execute this sequence, True or False?", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at location s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s2, s0 and p0_3, s1 and p0_1, s1 and p1_3, s1 and p2_1, s2 and p2_1, s3 and p1_3. Furthermore, a link exists between locations s0 and s2, s0 and s3, s2 and s0, s3 and s0. Package1 is currently situated at location s1, package2 is at location s2, package3 is at location s0, and package4 is also at location s2. Links are present between locations s0 and s1, s1 and s0, s1 and s3, s2 and s1, s2 and s3, s3 and s2, s1 and s2, and s3 and s1. Additionally, paths exist between locations p0_1 and s1, p0_3 and s0, p1_3 and s1, s0 and p0_2, s3 and p0_3, p0_2 and s2, p2_1 and s1, s0 and p0_1, and s2 and p0_2. Lastly, truck1 is located at s0 and is empty, while truck2 is also at location s0 and contains nothing."}
{"question_id": "f73aa6cd-026a-4191-a7a1-97ec35e709ed", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: driver2 walks to location p3_0 from location s3. Is the action: driver2 walks from location s3 to location p3_0 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is at location s1, driver2 is present at location s3, driver3 is present at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and s2 have a link between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is currently at location s3, package2 is present at location s2, package3 is currently at location s2, package4 is currently at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s0, there is a link between location s2 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing, truck2 is at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: driver2 moves from location s3 to location p3_0. Is the action of driver2 moving from location s3 to location p3_0 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is situated at s3, and driver3 is also at s3. A path exists between p0_1 and s1, as well as between p1_2 and s2, and p3_0 and s3. Additionally, paths are present between s0 and p0_1, s0 and s1, s0 and s3, s1 and p1_2, s1 and s2, and s3 and s0. Package1 is currently situated at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Links exist between s0 and s2, s1 and s3, s2 and s1, and s1 and s0. Furthermore, paths are present between p1_2 and s1, p1_3 and s3, s2 and p1_2, p0_1 and s0, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s0 and p3_0, s1 and p0_1, s1 and p1_3, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is currently empty and located at s0, while truck2, which is also empty, is at s3. Truck3, which contains nothing, is present at location s0."}
{"question_id": "19714b20-8477-47e6-94be-0296b71a7ce8", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: driver2 walks from location p3_0 to location s3. Is the action: driver2 walks to location s3 from location p3_0 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Driver1 is at location s1, driver2 is present at location s3, driver3 is present at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and s2 have a link between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is currently at location s3, package2 is present at location s2, package3 is currently at location s2, package4 is currently at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s0, there is a link between location s2 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing, truck2 is at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the planned actions include: driver2 moving from location p3_0 to location s3. Is the action of driver2 walking from location p3_0 to location s3 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is situated at s3, and driver3 is also at s3. A path exists between p0_1 and s1, as well as between p1_2 and s2, and p3_0 and s3. Additionally, a path is present between s0 and p0_1, and a link connects s0 to s1 and s3. Furthermore, a path is found between s1 and p1_2, and a link connects s1 to s2. A link also connects s3 to s0 and s1. Package1 is currently situated at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Links exist between s0 and s2, s1 and s3, and s2 and s1. Paths are present between p1_2 and s1, p1_3 and s3, and s2 and p1_2. A link connects s1 to s0, and another link connects s2 to s0. Paths are also present between p0_1 and s0, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s0 and p3_0, s1 and p0_1, s1 and p1_3, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is currently at s0 and is empty, while truck2 is at s3 and contains nothing. Truck3 is also at s0 and contains nothing."}
{"question_id": "f15bae7a-901b-4246-afd5-24d0aa191c77", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: driver1 walks to location p4_3 from location s3. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s3, driver2 is currently at location s4, driver3 is present at location s3, locations p5_2 and s2 have a path between them, locations s0 and p0_5 have a path between them, locations s1 and s4 have a link between them, locations s2 and p5_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s2 have a link between them, locations s3 and s4 have a link between them, locations s4 and p4_0 have a path between them, locations s5 and p0_5 have a path between them, locations s5 and s0 have a link between them, locations s5 and s2 have a link between them, package1 is at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is at location s0, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_1 and s1, there exists a path between the locations p4_1 and s4, there exists a path between the locations p4_3 and s3, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p4_0, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_1, there exists a path between the locations s4 and p4_3, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s2, there is a link between location s0 and location s4, there is a link between location s0 and location s5, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s2 and location s5, there is a link between location s4 and location s0, there is a link between location s4 and location s1, there is a link between location s4 and location s3, there is a link between location s4 and location s5, there is a link between location s5 and location s3, there is a path between location p0_5 and location s5, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s5, there is a path between location s1 and location p4_1, truck1 is currently at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: driver1 moves from location s3 to location p4_3. Is the execution of this action feasible, True or False?", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is at location s4, and driver3 is also at location s3. A path exists between locations p5_2 and s2, as well as between locations s0 and p0_5. Locations s1 and s4 are connected by a link, and locations s2 and p5_2 are also connected by a path. Furthermore, locations s2 and s0 are linked, locations s3 and s2 are linked, and locations s3 and s4 are linked. A path exists between locations s4 and p4_0, locations s5 and p0_5, and locations s5 and s0 are linked. Locations s5 and s2 are also linked. Package1 is located at s2, package2 is currently at s2, package3 is at location s3, and package4 is at location s0. There are links between locations s0 and s1, s1 and s0, s3 and s5, and s5 and s4. Paths exist between locations p0_5 and s0, p4_0 and s0, p4_1 and s1, p4_1 and s4, p4_3 and s3, and p4_3 and s4. Additionally, paths exist between locations s0 and p4_0, s3 and p4_3, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Links exist between locations s0 and s2, s0 and s4, s0 and s5, s1 and s2, s2 and s1, s2 and s3, s2 and s5, s4 and s0, s4 and s1, s4 and s3, s4 and s5, and s5 and s3. Paths also exist between locations p0_5 and s5, p4_0 and s4, p5_2 and s5, and s1 and p4_1. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "4c17856d-57c1-4b82-9593-b3d89961e4da", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location s0, driver1 boards truck1, at location s0, package3 is loaded in truck1, at location s3, package3 is unloaded in truck1, driver1 drives truck1 from location s0 to location s3, driver1 walks from location p0_2 to s0, driver1 walks from location p0_3 to location s0, driver1 walks from location s2 to p0_2, driver1 walks from location s3 to location p0_3, driver2 drives truck1 to location p0_3 from location s1 and driver2 walks from location s2 to location p0_2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_3 and s3 have a path between them, locations p1_3 and s3 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_1 have a path between them, locations s2 and p2_1 have a path between them, locations s2 and s0 have a link between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is present at location s0, package4 is currently at location s2, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_2, there exists a path between the locations s3 and p0_3, there is a link between location s1 and location s2, there is a link between location s3 and location s1, there is a path between location p0_2 and location s2, there is a path between location p2_1 and location s1, there is a path between location s0 and location p0_1, there is a path between location s2 and location p0_2, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at location s0, driver1 gets into truck1, at location s0, package3 is placed into truck1, at location s3, package3 is taken out of truck1, driver1 drives truck1 from location s0 to location s3, driver1 walks from location p0_2 to s0, driver1 walks from location p0_3 to location s0, driver1 walks from location s2 to p0_2, driver1 walks from location s3 to location p0_3, driver2 drives truck1 from location s1 to location p0_3 and driver2 walks from location s2 to location p0_2. Is it feasible to carry out this sequence, True or False?", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at location s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s2, s0 and p0_3, s1 and p0_1, s1 and p1_3, s1 and p2_1, s2 and p2_1, s3 and p1_3. Furthermore, a link exists between locations s0 and s2, s0 and s3, s2 and s0, s3 and s0. Package1 is currently situated at location s1, package2 is at location s2, package3 is at location s0, and package4 is also at location s2. Additionally, links exist between locations s0 and s1, s1 and s0, s1 and s3, s2 and s1, s2 and s3, s3 and s2, s1 and s2, and s3 and s1. Paths also exist between locations p0_1 and s1, p0_3 and s0, p1_3 and s1, s0 and p0_2, s3 and p0_3, p0_2 and s2, p2_1 and s1, s0 and p0_1, and s2 and p0_2. Lastly, truck1 is currently at location s0 and is empty, while truck2 is also at location s0 and contains nothing."}
{"question_id": "cdc771b3-276c-4eed-b537-de485fdb3159", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: driver1 walks from location s3 to location p4_3. Is the action: driver1 walks to location p4_3 from location s3 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s3, driver2 is currently at location s4, driver3 is present at location s3, locations p5_2 and s2 have a path between them, locations s0 and p0_5 have a path between them, locations s1 and s4 have a link between them, locations s2 and p5_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s2 have a link between them, locations s3 and s4 have a link between them, locations s4 and p4_0 have a path between them, locations s5 and p0_5 have a path between them, locations s5 and s0 have a link between them, locations s5 and s2 have a link between them, package1 is at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is at location s0, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_1 and s1, there exists a path between the locations p4_1 and s4, there exists a path between the locations p4_3 and s3, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p4_0, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_1, there exists a path between the locations s4 and p4_3, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s2, there is a link between location s0 and location s4, there is a link between location s0 and location s5, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s2 and location s5, there is a link between location s4 and location s0, there is a link between location s4 and location s1, there is a link between location s4 and location s3, there is a link between location s4 and location s5, there is a link between location s5 and location s3, there is a path between location p0_5 and location s5, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s5, there is a path between location s1 and location p4_1, truck1 is currently at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: driver1 moves from location s3 to location p4_3. Is the action: driver1 moving from location s3 to location p4_3 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is at location s4, and driver3 is also at location s3. A path exists between locations p5_2 and s2, as well as between locations s0 and p0_5. Locations s1 and s4 are connected by a link, and locations s2 and p5_2 also have a path between them. Furthermore, locations s2 and s0 are linked, locations s3 and s2 are connected, and locations s3 and s4 are also linked. A path exists between locations s4 and p4_0, locations s5 and p0_5, and locations s5 and s0 are connected by a link. Locations s5 and s2 are also linked. Package1 is located at s2, package2 is currently at s2, package3 is at location s3, and package4 is at location s0. There are links between locations s0 and s1, s1 and s0, s3 and s5, and s5 and s4. Paths exist between locations p0_5 and s0, p4_0 and s0, p4_1 and s1, p4_1 and s4, p4_3 and s3, and p4_3 and s4. Additionally, paths exist between locations s0 and p4_0, s3 and p4_3, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Links exist between locations s0 and s2, s0 and s4, s0 and s5, s1 and s2, s2 and s1, s2 and s3, s2 and s5, s4 and s0, s4 and s1, s4 and s3, s4 and s5, and s5 and s3. Paths also exist between locations p0_5 and s5, p4_0 and s4, p5_2 and s5, and s1 and p4_1. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "7fcbe706-a4bf-42da-a5e6-353187284c27", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location s0, driver2 boards truck1, driver2 drives truck1 from location s0 to location s3, driver2 drives truck1 from location s2 to location s0, driver2 walks from location p3_0 to location s0, driver3 walks from location p1_2 to s2, package2 is loaded in truck1 at location s2, package2 is unloaded from truck1 at location s0, package3 is loaded in truck1 at location s2, truck1 is driven from location s0 to s2 by driver2 and truck1 is loaded with package1 at location s3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is at location s1, driver2 is present at location s3, driver3 is present at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and s2 have a link between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is currently at location s3, package2 is present at location s2, package3 is currently at location s2, package4 is currently at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s0, there is a link between location s2 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing, truck2 is at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at location s0, driver2 gets into truck1, then driver2 drives truck1 from s0 to s3, followed by driving truck1 from s2 back to s0, driver2 walks from p3_0 to s0, driver3 walks from p1_2 to s2, package2 is loaded onto truck1 at s2, package2 is unloaded from truck1 at s0, package3 is loaded onto truck1 at s2, driver2 drives truck1 from s0 to s2 and loads package1 onto truck1 at s3. Can this sequence be executed, True or False?", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is situated at s3, and driver3 is also at s3. A path exists between p0_1 and s1, p1_2 and s2, and p3_0 and s3. Additionally, paths are present between s0 and p0_1, s0 and s1, s0 and s3, s1 and p1_2, s1 and s2, and s3 and s0. Package1 is at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Links exist between s0 and s2, s1 and s3, s2 and s1, s1 and s0, and s2 and s0. Furthermore, paths are found between p1_2 and s1, p1_3 and s3, s2 and p1_2, p0_1 and s0, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s0 and p3_0, s1 and p0_1, s1 and p1_3, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is at s0 and is empty, while truck2 is at s3 and contains nothing. Truck3 is also at s0 and contains nothing."}
{"question_id": "13accd0e-3e8e-4855-8431-5acc9e3fa4b7", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: truck1 is loaded with package3 at location s0, driver1 walks from location s2 to location p0_2, driver1 walks from location p0_2 to location s0, driver1 boards truck1 at location s0, truck1 is driven from location s0 to s3 by driver1, driver1 disembarks from truck1 at location s3, truck2 is unloaded with package3 at location p2_1, driver1 walks from location s3 to location p0_3, driver1 walks to location s0 from location p0_3, driver2 walks from location s2 to p0_2, driver2 walks to location s0 from location p0_2, driver2 boards truck2 at location s0, driver2 drives truck2 to location s1 from location s0, at location s1, package1 is loaded in truck2, driver2 drives truck2 to location s2 from location s1, package2 is loaded in truck2 at location s2, package1 is unloaded from truck2 at location s2, driver2 drives truck2 from location s2 to location s1 and at location s1, driver2 disembarks from truck2. Is the action: package3 is unloaded from truck2 at location p2_1 executable at step 7, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_3 and s3 have a path between them, locations p1_3 and s3 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_1 have a path between them, locations s2 and p2_1 have a path between them, locations s2 and s0 have a link between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is present at location s0, package4 is currently at location s2, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_2, there exists a path between the locations s3 and p0_3, there is a link between location s1 and location s2, there is a link between location s3 and location s1, there is a path between location p0_2 and location s2, there is a path between location p2_1 and location s1, there is a path between location s0 and location p0_1, there is a path between location s2 and location p0_2, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: at location s0, truck1 is loaded with package3, meanwhile driver1 walks from location s2 to p0_2 and then to location s0, where driver1 boards truck1, and then truck1 is driven by driver1 from location s0 to s3, after which driver1 gets off truck1 at location s3, and package3 is unloaded from truck2 at location p2_1, then driver1 walks from location s3 to p0_3 and back to location s0, while driver2 walks from location s2 to p0_2 and then to location s0, boards truck2, drives it from location s0 to s1, loads package1 into truck2 at location s1, drives truck2 to location s2, loads package2 into truck2 and unloads package1 at location s2, then drives truck2 back to location s1, and finally disembarks from truck2 at location s1. Is the action: package3 is unloaded from truck2 at location p2_1 executable at step 7, True or False?", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at location s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s2, s0 and p0_3, s1 and p0_1, s1 and p1_3, s1 and p2_1, s2 and p2_1, and s3 and p1_3. Furthermore, a link exists between locations s0 and s2, s0 and s3, s2 and s0, s3 and s0, s0 and s1, s1 and s0, s1 and s3, s2 and s1, s2 and s3, and s3 and s2. Package1 is currently at location s1, package2 is at location s2, package3 is at location s0, and package4 is at location s2. Additionally, a link is present between locations s0 and s1, s1 and s0, s1 and s3, s2 and s1, s2 and s3, and s3 and s2. There are also paths between locations p0_1 and s1, p0_3 and s0, p1_3 and s1, s0 and p0_2, s3 and p0_3, p0_2 and s2, p2_1 and s1, s0 and p0_1, and s2 and p0_2. Moreover, a link exists between locations s1 and s2, and s3 and s1. Lastly, truck1 is at location s0 and is empty, while truck2 is also at location s0 and contains nothing."}
{"question_id": "1fa9ba8c-dc67-4c07-b4ac-68ae033bb37f", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: driver1 walks to location p4_3 from location s3, driver1 walks to location s4 from location p4_3, driver1 walks from location s4 to p4_1, driver1 walks from location p4_1 to location s1, at location s1, driver1 boards truck1, truck1 is driven from location s1 to s0 by driver1, package4 is loaded in truck1 at location s0, driver1 drives truck1 from location s0 to location s2, truck1 is loaded with package2 at location s2, package1 is loaded in truck1 at location s2, truck1 is driven from location s2 to s3 by driver1, at location s3, package3 is loaded in truck1, truck1 is unloaded with package1 at location s3, driver1 drives truck1 from location s3 to location s4, truck1 is unloaded with package4 at location s4, at location s4, package3 is unloaded in truck1, package2 is unloaded from truck1 at location s4, driver1 drives truck1 from location s4 to location s1 and from truck1, driver1 disembarks at location s1. Is the action: driver1 drives truck1 from location s2 to location s3 executable at step 11, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s3, driver2 is currently at location s4, driver3 is present at location s3, locations p5_2 and s2 have a path between them, locations s0 and p0_5 have a path between them, locations s1 and s4 have a link between them, locations s2 and p5_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s2 have a link between them, locations s3 and s4 have a link between them, locations s4 and p4_0 have a path between them, locations s5 and p0_5 have a path between them, locations s5 and s0 have a link between them, locations s5 and s2 have a link between them, package1 is at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is at location s0, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_1 and s1, there exists a path between the locations p4_1 and s4, there exists a path between the locations p4_3 and s3, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p4_0, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_1, there exists a path between the locations s4 and p4_3, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s2, there is a link between location s0 and location s4, there is a link between location s0 and location s5, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s2 and location s5, there is a link between location s4 and location s0, there is a link between location s4 and location s1, there is a link between location s4 and location s3, there is a link between location s4 and location s5, there is a link between location s5 and location s3, there is a path between location p0_5 and location s5, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s5, there is a path between location s1 and location p4_1, truck1 is currently at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: driver1 moves from location s3 to p4_3, then from p4_3 to s4, followed by a move from s4 to p4_1, and then from p4_1 to s1. Upon arriving at s1, driver1 boards truck1, and then drives it from s1 to s0. At s0, package4 is loaded onto truck1. Next, driver1 drives truck1 from s0 to s2, where package2 and package1 are loaded onto truck1. Driver1 then drives truck1 from s2 to s3, where package3 is loaded, and package1 is unloaded. The sequence continues with driver1 driving truck1 from s3 to s4, where package4 and package3 are unloaded, and package2 is also unloaded. Finally, driver1 drives truck1 from s4 back to s1 and disembarks. Is the action: driver1 drives truck1 from location s2 to location s3 executable at step 11, True or False?", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is at location s4, and driver3 is also at location s3. A path exists between locations p5_2 and s2, as well as between locations s0 and p0_5. Locations s1 and s4 are connected by a link, and locations s2 and p5_2 are also connected by a path. Furthermore, locations s2 and s0 are linked, locations s3 and s2 are linked, and locations s3 and s4 are linked. A path exists between locations s4 and p4_0, locations s5 and p0_5, and locations s5 and s0 are linked. Locations s5 and s2 are also linked. Package1 is located at s2, package2 is currently at s2, package3 is at location s3, and package4 is at location s0. A link exists between locations s0 and s1, and another link exists between locations s1 and s0. Additionally, links exist between locations s3 and s5, and locations s5 and s4. Paths exist between locations p0_5 and s0, p4_0 and s0, p4_1 and s1, p4_1 and s4, p4_3 and s3, and p4_3 and s4. There are also paths between locations s0 and p4_0, s3 and p4_3, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Links exist between locations s0 and s2, s0 and s4, s0 and s5, s1 and s2, s2 and s1, s2 and s3, s2 and s5, s4 and s0, s4 and s1, s4 and s3, s4 and s5, and s5 and s3. Paths exist between locations p0_5 and s5, p4_0 and s4, p5_2 and s5, and s1 and p4_1. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "71649c30-68c6-48d4-954f-34337e1fb172", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location s0, driver2 boards truck1, at location s0, package2 is unloaded in truck1, at location s2, package3 is loaded in truck1, at location s3, package1 is loaded in truck1, driver2 drives truck1 to location s0 from location s2, driver2 drives truck1 to location s2 from location s0, driver2 drives truck1 to location s3 from location s0, driver2 drives truck3 to location s2 from location s0, driver2 walks from location p0_1 to location s0, driver2 walks from location p3_0 to location s0, driver2 walks from location s3 to location p3_0, driver2 walks to location p0_1 from location s1, driver3 walks from location s3 to location p3_0, from truck1, driver2 disembarks at location s1, package2 is loaded in truck1 at location s2, package3 is unloaded from truck1 at location s1, truck1 is driven from location s3 to s1 by driver2, truck1 is unloaded with package1 at location s1 and truck3 is boarded by driver2 at location s0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is at location s1, driver2 is present at location s3, driver3 is present at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and s2 have a link between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is currently at location s3, package2 is present at location s2, package3 is currently at location s2, package4 is currently at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s0, there is a link between location s2 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing, truck2 is at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at location s0, driver2 gets into truck1, at location s0, package2 is placed into truck1, at location s2, package3 is put into truck1, at location s3, package1 is loaded into truck1, driver2 drives truck1 from location s2 to location s0, driver2 drives truck1 from location s0 to location s2, driver2 drives truck1 from location s0 to location s3, driver2 drives truck3 from location s0 to location s2, driver2 walks from location p0_1 to location s0, driver2 walks from location p3_0 to location s0, driver2 walks from location s3 to location p3_0, driver2 walks from location s1 to location p0_1, driver3 walks from location s3 to location p3_0, driver2 gets out of truck1 at location s1, package2 is put into truck1 at location s2, package3 is taken out of truck1 at location s1, driver2 drives truck1 from location s3 to location s1, package1 is unloaded from truck1 at location s1, and driver2 boards truck3 at location s0. Is it possible to execute this sequence, True or False?", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is situated at s3, and driver3 is also at s3. A path exists between p0_1 and s1, p1_2 and s2, and p3_0 and s3. Additionally, paths are present between s0 and p0_1, s0 and s1, s0 and s3, s1 and p1_2, s1 and s2, and s3 and s0. Furthermore, links exist between s0 and s1, s0 and s3, s1 and s2, s3 and s0, and s3 and s1. Package1 is currently at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Links are also present between s0 and s2, s1 and s3, and s2 and s1. Paths exist between p1_2 and s1, p1_3 and s3, s2 and p1_2, p0_1 and s0, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s0 and p3_0, s1 and p0_1, s1 and p1_3, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is at s0 and is empty, while truck2 is at s3 and contains nothing. Truck3 is also at s0 and contains nothing."}
{"question_id": "a99c3802-28e6-4cab-8fd2-62a272a21a39", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location s0, package3 is loaded in truck1, at location s3, package3 is unloaded in truck1, driver1 disembarks from truck1 at location s3, driver1 drives truck1 to location s3 from location s0, driver1 walks from location s2 to p0_2, driver1 walks to location p0_3 from location s3, driver1 walks to location s0 from location p0_2, driver1 walks to location s0 from location p0_3, driver2 walks from location s2 to p0_2 and truck1 is boarded by driver1 at location s0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_3 and s3 have a path between them, locations p1_3 and s3 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_1 have a path between them, locations s2 and p2_1 have a path between them, locations s2 and s0 have a link between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is present at location s0, package4 is currently at location s2, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_2, there exists a path between the locations s3 and p0_3, there is a link between location s1 and location s2, there is a link between location s3 and location s1, there is a path between location p0_2 and location s2, there is a path between location p2_1 and location s1, there is a path between location s0 and location p0_1, there is a path between location s2 and location p0_2, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at location s0, package3 is loaded onto truck1, at location s3, package3 is unloaded from truck1, driver1 gets off truck1 at location s3, driver1 drives truck1 from location s0 to location s3, driver1 walks from location s2 to p0_2, driver1 walks from location s3 to p0_3, driver1 walks from location p0_2 to location s0, driver1 walks from location p0_3 to location s0, driver2 walks from location s2 to p0_2, and driver1 boards truck1 at location s0. Is it feasible to execute this sequence, True or False?", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at location s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s2, s0 and p0_3, s1 and p0_1, s1 and p1_3, s1 and p2_1, s2 and p2_1, s3 and p1_3. Furthermore, a link exists between locations s0 and s2, s0 and s3, s2 and s0, s3 and s0. Package1 is currently at location s1, package2 is at location s2, package3 is at location s0, and package4 is at location s2. Additionally, links exist between locations s0 and s1, s1 and s0, s1 and s3, s2 and s1, s2 and s3, s3 and s2, s1 and s2, and s3 and s1. Paths also exist between locations p0_1 and s1, p0_3 and s0, p1_3 and s1, s0 and p0_2, s3 and p0_3, p0_2 and s2, p2_1 and s1, s0 and p0_1, and s2 and p0_2. Lastly, truck1 is at location s0 and is empty, while truck2 is also at location s0 and contains nothing."}
{"question_id": "6cfbaf0f-0a1c-435a-991c-fd4dbb12282b", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: driver2 walks to location p3_0 from location s3, driver2 walks to location s0 from location p3_0, truck1 is boarded by driver2 at location s0, driver2 drives truck1 to location s2 from location s0, truck1 is loaded with package3 at location s2, at location s2, package2 is loaded in truck1, driver2 drives truck1 to location s0 from location s2, package2 is unloaded from truck1 at location s0, truck1 is driven from location s0 to s3 by driver2, truck1 is loaded with package1 at location s3, driver2 drives truck1 from location s3 to location s1, at location s1, driver2 disembarks from truck1, driver2 walks from location s1 to p0_1, driver2 walks from location p0_1 to s0, at location s0, driver2 boards truck3, truck3 is driven from location s0 to s2 by driver2, at location s1, package3 is unloaded in truck1, package1 is unloaded from truck1 at location s1 and driver3 walks from location s3 to location p3_0. Is the action: at location s3, package1 is loaded in truck1 executable at step 10, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Driver1 is at location s1, driver2 is present at location s3, driver3 is present at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and s2 have a link between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is currently at location s3, package2 is present at location s2, package3 is currently at location s2, package4 is currently at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s0, there is a link between location s2 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing, truck2 is at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 19: driver2 moves from location s3 to p3_0, then from p3_0 to s0, boards truck1 at s0, drives truck1 from s0 to s2, loads truck1 with package3 at s2, also loads package2 into truck1 at s2, drives truck1 back from s2 to s0, unloads package2 from truck1 at s0, drives truck1 from s0 to s3, loads truck1 with package1 at s3, drives truck1 from s3 to s1, disembarks from truck1 at s1, walks from s1 to p0_1 and then to s0, boards truck3 at s0, drives truck3 from s0 to s2, unloads package3 from truck1 at s1, unloads package1 from truck1 at s1, and driver3 moves from s3 to p3_0. Is the action: at location s3, package1 is loaded in truck1 executable at step 10, True or False?", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is situated at s3, and driver3 is also at s3. A path exists between p0_1 and s1, p1_2 and s2, and p3_0 and s3. Additionally, paths are present between s0 and p0_1, s0 and s1, s0 and s3, s1 and p1_2, s1 and s2, and s3 and s0. Furthermore, links exist between s0 and s1, s0 and s3, s1 and s2, s3 and s0, and s3 and s1. Package1 is currently at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Links are also present between s0 and s2, s1 and s3, and s2 and s1. Paths exist between p1_2 and s1, p1_3 and s3, s2 and p1_2, p0_1 and s0, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s0 and p3_0, s1 and p0_1, s1 and p1_3, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is at s0 and is empty, while truck2 is at s3 and contains nothing. Truck3 is also at s0 and contains nothing."}
{"question_id": "bd5a6b05-4294-4273-8838-e1b07e5b2fee", "domain_name": "driverlog", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: at location s0, package3 is loaded in truck1, driver1 walks from location s2 to p0_2, driver1 walks from location p0_2 to s0, truck1 is boarded by driver1 at location s0, driver1 drives truck1 from location s0 to location s3, driver1 disembarks from truck1 at location s3, package3 is unloaded from truck1 at location s3, driver1 walks from location s3 to location p0_3, driver1 walks from location p0_3 to s0 and driver2 walks from location s2 to location p0_2. Is the action: truck1 is unloaded with package3 at location s3 executable at step 7, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is currently at location s2, driver2 is at location s2, locations p0_1 and s0 have a path between them, locations p0_2 and s0 have a path between them, locations p0_3 and s3 have a path between them, locations p1_3 and s3 have a path between them, locations p2_1 and s2 have a path between them, locations s0 and p0_3 have a path between them, locations s0 and s2 have a link between them, locations s0 and s3 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_3 have a path between them, locations s1 and p2_1 have a path between them, locations s2 and p2_1 have a path between them, locations s2 and s0 have a link between them, locations s3 and p1_3 have a path between them, locations s3 and s0 have a link between them, package1 is present at location s1, package2 is currently at location s2, package3 is present at location s0, package4 is currently at location s2, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a link between the locations s2 and s3, there exists a link between the locations s3 and s2, there exists a path between the locations p0_1 and s1, there exists a path between the locations p0_3 and s0, there exists a path between the locations p1_3 and s1, there exists a path between the locations s0 and p0_2, there exists a path between the locations s3 and p0_3, there is a link between location s1 and location s2, there is a link between location s3 and location s1, there is a path between location p0_2 and location s2, there is a path between location p2_1 and location s1, there is a path between location s0 and location p0_1, there is a path between location s2 and location p0_2, truck1 is at location s0, truck1 is empty, truck2 contains nothing and truck2 is at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 10: at location s0, package3 is loaded onto truck1, driver1 moves from location s2 to p0_2, then from p0_2 to s0, boards truck1 at location s0, drives truck1 from s0 to s3, disembarks from truck1 at s3, unloads package3 from truck1 at s3, walks from s3 to p0_3 and then back to s0, and driver2 moves from s2 to p0_2. Is the action of unloading truck1 with package3 at location s3 executable at step 7, True or False?", "initial_state_nl_paraphrased": "Driver1 is presently located at s2, while driver2 is also at location s2. A path exists between locations p0_1 and s0, as well as between p0_2 and s0, p0_3 and s3, p1_3 and s3, p2_1 and s2, s0 and p0_3, s1 and p0_1, s1 and p1_3, s1 and p2_1, s2 and p2_1, s3 and p1_3. Furthermore, a link exists between locations s0 and s2, s0 and s3, s2 and s0, s3 and s0. Package1 is currently situated at location s1, package2 is at location s2, package3 is at location s0, and package4 is also at location s2. Additionally, links exist between locations s0 and s1, s1 and s0, s1 and s3, s2 and s1, s2 and s3, s3 and s2, s1 and s2, and s3 and s1. Paths also exist between locations p0_1 and s1, p0_3 and s0, p1_3 and s1, s0 and p0_2, s3 and p0_3, p0_2 and s2, p2_1 and s1, s0 and p0_1, and s2 and p0_2. Lastly, truck1 is currently at location s0 and is empty, while truck2 is also at location s0 and contains nothing."}
{"question_id": "06157e29-840f-4b79-83f0-c20314764674", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: truck2 is driven from location s0 to s1 by driver1, driver2 walks from location p3_0 to s0, truck1 is boarded by driver2 at location s0, driver2 drives truck1 to location s2 from location s0, package3 is loaded in truck1 at location s2, truck1 is loaded with package2 at location s2, driver2 drives truck1 to location s0 from location s2, truck1 is unloaded with package2 at location s0, truck1 is driven from location s0 to s3 by driver2, package1 is loaded in truck1 at location s3, driver2 drives truck1 from location s3 to location s1, at location s1, driver2 disembarks from truck1, driver2 walks from location s1 to p0_1, driver2 walks from location p0_1 to s0, driver2 boards truck3 at location s0, truck3 is driven from location s0 to s2 by driver2, at location s1, package3 is unloaded in truck1, package1 is unloaded from truck1 at location s1 and driver3 walks from location s3 to location p3_0. Is the action: driver1 drives truck2 from location s0 to location s1 executable at step 1, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is at location s1, driver2 is present at location s3, driver3 is present at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and s2 have a link between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is currently at location s3, package2 is present at location s2, package3 is currently at location s2, package4 is currently at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s0, there is a link between location s2 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing, truck2 is at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: driver1 drives truck2 from location s0 to s1, driver2 walks from location p3_0 to s0, driver2 boards truck1 at location s0, then drives it from s0 to s2, loads package3 and package2 into truck1 at location s2, drives truck1 back to s0, unloads package2 from truck1 at s0, drives truck1 from s0 to s3, loads package1 into truck1 at s3, drives truck1 from s3 to s1, disembarks from truck1 at s1, walks from s1 to p0_1 and then to s0, boards truck3 at s0, drives truck3 from s0 to s2, unloads package3 and package1 from truck1 at location s1, and driver3 walks from s3 to p3_0. Is the action: driver1 drives truck2 from location s0 to location s1 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is situated at s3, and driver3 is also at s3. A path exists between p0_1 and s1, p1_2 and s2, and p3_0 and s3. Additionally, paths are present between s0 and p0_1, s0 and s1, s0 and s3, s1 and p1_2, s1 and s2, and s3 and s0. Furthermore, links exist between s0 and s1, s0 and s3, s1 and s2, s3 and s0, and s3 and s1. Package1 is currently at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Links are also present between s0 and s2, s1 and s3, and s2 and s1. Paths exist between p1_2 and s1, p1_3 and s3, s2 and p1_2, p0_1 and s0, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s0 and p3_0, s1 and p0_1, s1 and p1_3, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is at s0 and is empty, while truck2 is at s3 and contains nothing. Truck3 is also at s0 and contains nothing."}
{"question_id": "0992bf3e-ffe6-48ab-953f-8daae933d6bb", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: driver1 boards truck3 at location s0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Driver1 is at location s1, driver2 is present at location s3, driver3 is present at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and s2 have a link between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is currently at location s3, package2 is present at location s2, package3 is currently at location s2, package4 is currently at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s0, there is a link between location s2 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing, truck2 is at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: driver1 gets on truck3 at location s0. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is situated at s3, and driver3 is also at s3. A path exists between locations p0_1 and s1, as well as between p1_2 and s2, and p3_0 and s3. Additionally, paths are present between s0 and p0_1, s0 and s1, s0 and s3, s1 and p1_2, s1 and s2, and s3 and s0. Furthermore, links exist between s0 and s1, s0 and s3, s1 and s3, s0 and s2, s1 and s2, and s2 and s1. Package1 is currently situated at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Moreover, links are present between s0 and s2, s1 and s3, s2 and s1, and s1 and s0. Paths also exist between p1_2 and s1, p1_3 and s3, s2 and p1_2, p0_1 and s0, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s0 and p3_0, s1 and p0_1, s1 and p1_3, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is currently empty and located at s0, while truck2 is empty and at s3. Truck3 is also empty and situated at s0."}
{"question_id": "35602582-65ad-4370-a8ff-17dcb0cea7bd", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location s2, package2 is loaded in truck1, driver1 boards truck1 at location s1, driver1 drives truck1 from location s1 to location s0, driver1 drives truck1 to location s2 from location s0, driver1 walks from location p4_3 to s4, driver1 walks from location s3 to location p4_3, driver1 walks from location s4 to p4_1, driver1 walks to location s1 from location p4_1, driver2 walks from location s3 to s5 and truck1 is loaded with package4 at location s0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s3, driver2 is currently at location s4, driver3 is present at location s3, locations p5_2 and s2 have a path between them, locations s0 and p0_5 have a path between them, locations s1 and s4 have a link between them, locations s2 and p5_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s2 have a link between them, locations s3 and s4 have a link between them, locations s4 and p4_0 have a path between them, locations s5 and p0_5 have a path between them, locations s5 and s0 have a link between them, locations s5 and s2 have a link between them, package1 is at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is at location s0, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_1 and s1, there exists a path between the locations p4_1 and s4, there exists a path between the locations p4_3 and s3, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p4_0, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_1, there exists a path between the locations s4 and p4_3, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s2, there is a link between location s0 and location s4, there is a link between location s0 and location s5, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s2 and location s5, there is a link between location s4 and location s0, there is a link between location s4 and location s1, there is a link between location s4 and location s3, there is a link between location s4 and location s5, there is a link between location s5 and location s3, there is a path between location p0_5 and location s5, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s5, there is a path between location s1 and location p4_1, truck1 is currently at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at location s2, package2 is loaded onto truck1, driver1 boards truck1 at location s1, driver1 drives truck1 from location s1 to location s0, then from location s0 to location s2, driver1 walks from location p4_3 to s4, then from location s3 to p4_3, and from location s4 to p4_1, after which driver1 walks back to location s1 from p4_1, meanwhile driver2 walks from location s3 to s5, and truck1 is loaded with package4 at location s0. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is at s4, and driver3 is also at s3. A path exists between p5_2 and s2, as well as between s0 and p0_5. There is a link between s1 and s4, s2 and p5_2, s2 and s0, s3 and s2, s3 and s4, s4 and p4_0, s5 and p0_5, s5 and s0, and s5 and s2. Package1 is located at s2, package2 is at s2, package3 is at s3, and package4 is at s0. Links exist between s0 and s1, s1 and s0, s3 and s5, and s5 and s4. Paths exist between p0_5 and s0, p4_0 and s0, p4_1 and s1, p4_1 and s4, p4_3 and s3, p4_3 and s4, s0 and p4_0, s3 and p4_3, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Additionally, links exist between s0 and s2, s0 and s4, s0 and s5, s1 and s2, s2 and s1, s2 and s3, s2 and s5, s4 and s0, s4 and s1, s4 and s3, s4 and s5, and s5 and s3. Paths also exist between p0_5 and s5, p4_0 and s4, p5_2 and s5, and s1 and p4_1. Truck1 is currently at s1 and is empty, while truck2 is at s5 and contains nothing."}
{"question_id": "3fd8081a-d68b-4a6b-aa86-1c67b644f345", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: driver3 boards truck1 at location s0. Is the action: truck1 is boarded by driver3 at location s0 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s3, driver2 is present at location s3, driver3 is currently at location s0, locations p0_1 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and s2 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is present at location s2, package3 is at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s3 and location s0, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the planned actions include: driver3 gets on truck1 at location s0. Is the action of truck1 being boarded by driver3 at location s0 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Driver1 is located at s3, while driver2 is also at s3. Meanwhile, driver3 is currently situated at s0. A path exists between locations p0_1 and s0, as well as between p2_0 and s2, and p3_0 and s0. Locations s0 and s2 are connected by a link, and s1 has paths to both p0_1 and p1_2. Additionally, s2 has paths to p2_0 and is linked to s0. Location s3 is connected to p3_0 and has a link to s2. Package1 is currently at s0, package2 is at s2, and package3 is also at s0. Links exist between s0 and s3, s3 and s1, and paths exist between p1_2 and s1, p1_2 and s2, p1_3 and s3, s0 and p0_1, s0 and p2_0, s0 and p3_0, and s2 and p1_2. Furthermore, links are present between s1 and s2, s1 and s3, s2 and s1, s2 and s3, and s3 and s0. Paths also exist between p0_1 and s1, p1_3 and s1, p2_0 and s0, p3_0 and s3, s1 and p1_3, and s3 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and situated at s2."}
{"question_id": "8c711b50-e2f3-4d8a-adbf-51b5328d3165", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: at location s0, driver3 boards truck1, package3 is loaded in truck1 at location s0, at location s0, package1 is loaded in truck1, driver3 drives truck1 to location s3 from location s0, truck1 is unloaded with package1 at location s3, truck1 is driven from location s3 to s1 by driver3, from truck1, driver3 disembarks at location s1, at location s1, package3 is unloaded in truck1, truck2 is loaded with package2 at location s2 and driver1 walks from location s3 to p3_0. Is the action: driver3 boards truck1 at location s0 executable at step 1, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is present at location s3, driver2 is present at location s3, driver3 is currently at location s0, locations p0_1 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and s2 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is present at location s2, package3 is at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s3 and location s0, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 10: at location s0, driver3 gets into truck1, package3 is placed in truck1 at location s0, package1 is also loaded into truck1 at location s0, driver3 then drives truck1 from location s0 to location s3, package1 is unloaded from truck1 at location s3, driver3 drives truck1 from location s3 to location s1, driver3 gets out of truck1 at location s1, package3 is then unloaded from truck1 at location s1, meanwhile, truck2 is loaded with package2 at location s2, and driver1 walks from location s3 to p3_0. Is the action: driver3 gets into truck1 at location s0 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Driver1 is located at s3, while driver2 is also at s3. Meanwhile, driver3 is currently situated at s0. There is a path connecting p0_1 and s0, as well as between p2_0 and s2, and p3_0 and s0. A link exists between s0 and s2, and another between s1 and p0_1, s1 and p1_2, s2 and p2_0, s2 and s0, s3 and p3_0, and s3 and s2. Package1 is currently at s0, package2 is at s2, and package3 is also at s0. Links are present between s0 and s3, s3 and s1, and paths exist between p1_2 and s1, p1_2 and s2, p1_3 and s3, s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p1_2, s1 and s2, s1 and s3, s2 and s1, s2 and s3, and s3 and s0. Additionally, paths are present between p0_1 and s1, p1_3 and s1, p2_0 and s0, p3_0 and s3, s1 and p1_3, and s3 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and situated at s2."}
{"question_id": "f5a30e02-7735-46ee-9db9-c8532d26e3f7", "domain_name": "driverlog", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location s0, driver3 boards truck1, at location s0, package1 is loaded in truck1, at location s0, package3 is loaded in truck1, at location s1, driver3 disembarks from truck1, driver1 walks from location p3_0 to s0, driver1 walks to location p3_0 from location s3, driver2 walks from location p1_3 to s1, driver2 walks from location s1 to location p1_2, driver2 walks from location s3 to p1_3, driver2 walks to location s2 from location p1_2, driver3 drives truck1 to location s1 from location s3, driver3 walks from location s1 to p1_2, driver3 walks to location s2 from location p1_2, package2 is loaded in truck2 at location s2, package3 is unloaded from truck1 at location s1, truck1 is driven from location s0 to s3 by driver3, truck1 is unloaded with package1 at location s3, truck2 is boarded by driver3 at location s2 and truck2 is driven from location s2 to s3 by driver3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s3, driver2 is present at location s3, driver3 is currently at location s0, locations p0_1 and s0 have a path between them, locations p2_0 and s2 have a path between them, locations p3_0 and s0 have a path between them, locations s0 and s2 have a link between them, locations s1 and p0_1 have a path between them, locations s1 and p1_2 have a path between them, locations s2 and p2_0 have a path between them, locations s2 and s0 have a link between them, locations s3 and p3_0 have a path between them, locations s3 and s2 have a link between them, package1 is currently at location s0, package2 is present at location s2, package3 is at location s0, there exists a link between the locations s0 and s3, there exists a link between the locations s3 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_2 and s2, there exists a path between the locations p1_3 and s3, there exists a path between the locations s0 and p0_1, there exists a path between the locations s0 and p2_0, there exists a path between the locations s0 and p3_0, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s2, there is a link between location s1 and location s3, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s3 and location s0, there is a path between location p0_1 and location s1, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p3_0 and location s3, there is a path between location s1 and location p1_3, there is a path between location s3 and location p1_3, truck1 contains nothing, truck1 is at location s0, truck2 is empty and truck2 is present at location s2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at location s0, driver3 gets into truck1, at location s0, package1 is placed in truck1, at location s0, package3 is also placed in truck1, at location s1, driver3 gets out of truck1, driver1 moves from p3_0 to s0, driver1 moves from s3 to p3_0, driver2 moves from p1_3 to s1, driver2 moves from s1 to p1_2, driver2 moves from s3 to p1_3, driver2 moves from p1_2 to s2, driver3 drives truck1 from s3 to s1, driver3 moves from s1 to p1_2, driver3 moves from p1_2 to s2, package2 is placed in truck2 at location s2, package3 is removed from truck1 at location s1, truck1 is driven by driver3 from s0 to s3, truck1 is unloaded with package1 at location s3, driver3 gets into truck2 at location s2 and truck2 is driven by driver3 from location s2 to s3. Is it possible to execute this sequence, True or False?", "initial_state_nl_paraphrased": "Driver1 is located at s3, while driver2 is also at s3. Meanwhile, driver3 is currently situated at s0. There is a path connecting p0_1 and s0, as well as between p2_0 and s2, and p3_0 and s0. A link exists between s0 and s2, and another between s1 and p0_1, s1 and p1_2, s2 and p2_0, s2 and s0, s3 and p3_0, and s3 and s2. Package1 is currently at s0, package2 is at s2, and package3 is also at s0. Links are present between s0 and s3, s3 and s1, and paths exist between p1_2 and s1, p1_2 and s2, p1_3 and s3, s0 and p0_1, s0 and p2_0, s0 and p3_0, s2 and p1_2. Additionally, links are found between s1 and s2, s1 and s3, s2 and s1, s2 and s3, and s3 and s0. Paths also exist between p0_1 and s1, p1_3 and s1, p2_0 and s0, p3_0 and s3, s1 and p1_3, and s3 and p1_3. Truck1 is empty and located at s0, while truck2 is also empty and present at s2."}
{"question_id": "50d7e1bb-a52e-4092-bfc3-29d16624f2ec", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: driver2 walks from location s3 to location p3_0, driver2 walks from location p3_0 to s0, driver2 boards truck1 at location s0, truck1 is driven from location s0 to s2 by driver2, package3 is loaded in truck1 at location s2, package2 is loaded in truck1 at location s2, truck1 is driven from location s2 to s0 by driver2, truck1 is unloaded with package2 at location s0, driver2 drives truck1 from location s0 to location s3 and driver3 drives truck1 from location s0 to location s1. Is the action: truck1 is driven from location s0 to s1 by driver3 executable at step 10, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Driver1 is at location s1, driver2 is present at location s3, driver3 is present at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and s2 have a link between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is currently at location s3, package2 is present at location s2, package3 is currently at location s2, package4 is currently at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s0, there is a link between location s2 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing, truck2 is at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 10: driver2 moves from location s3 to p3_0, then from p3_0 to s0, boards truck1 at s0, drives truck1 from s0 to s2, loads package3 and package2 into truck1 at s2, drives truck1 back from s2 to s0, unloads package2 from truck1 at s0, drives truck1 from s0 to s3, and simultaneously, driver3 drives truck1 from s0 to s1. Is the action of driver3 driving truck1 from s0 to s1 executable at step 10, True or False?", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is situated at s3, and driver3 is also at s3. A path exists between p0_1 and s1, p1_2 and s2, and p3_0 and s3. Additionally, paths are present between s0 and p0_1, s0 and s1, s0 and s3, s1 and p1_2, s1 and s2, and s3 and s0. Furthermore, links exist between s0 and s1, s0 and s3, s1 and s2, s3 and s0, and s3 and s1. Package1 is currently at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Links are also present between s0 and s2, s1 and s3, and s2 and s1. Paths exist between p1_2 and s1, p1_3 and s3, s2 and p1_2, p0_1 and s0, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s0 and p3_0, s1 and p0_1, s1 and p1_3, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is at s0 and is empty, while truck2 is at s3 and contains nothing. Truck3 is also at s0 and contains nothing."}
{"question_id": "d9f7384a-4536-482f-add8-dcd8eb9a32fd", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: driver1 drives truck1 from location s5 to location p0_5. Is the action: driver1 drives truck1 to location p0_5 from location s5 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Driver1 is present at location s3, driver2 is currently at location s4, driver3 is present at location s3, locations p5_2 and s2 have a path between them, locations s0 and p0_5 have a path between them, locations s1 and s4 have a link between them, locations s2 and p5_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s2 have a link between them, locations s3 and s4 have a link between them, locations s4 and p4_0 have a path between them, locations s5 and p0_5 have a path between them, locations s5 and s0 have a link between them, locations s5 and s2 have a link between them, package1 is at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is at location s0, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_1 and s1, there exists a path between the locations p4_1 and s4, there exists a path between the locations p4_3 and s3, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p4_0, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_1, there exists a path between the locations s4 and p4_3, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s2, there is a link between location s0 and location s4, there is a link between location s0 and location s5, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s2 and location s5, there is a link between location s4 and location s0, there is a link between location s4 and location s1, there is a link between location s4 and location s3, there is a link between location s4 and location s5, there is a link between location s5 and location s3, there is a path between location p0_5 and location s5, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s5, there is a path between location s1 and location p4_1, truck1 is currently at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: driver1 is to drive truck1 from location s5 to location p0_5. Is the action of driver1 driving truck1 from location s5 to location p0_5 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is at location s4, and driver3 is also at location s3. A path exists between locations p5_2 and s2, as well as between locations s0 and p0_5. Locations s1 and s4 are connected by a link, and locations s2 and p5_2 also have a path between them. Furthermore, locations s2 and s0 are linked, locations s3 and s2 are connected, and locations s3 and s4 are also linked. A path exists between locations s4 and p4_0, locations s5 and p0_5, and locations s5 and s0 are connected by a link. Locations s5 and s2 are also linked. Package1 is located at s2, package2 is currently at s2, package3 is at location s3, and package4 is at location s0. There are links between locations s0 and s1, s1 and s0, s3 and s5, and s5 and s4. Paths exist between locations p0_5 and s0, p4_0 and s0, p4_1 and s1, p4_1 and s4, p4_3 and s3, and p4_3 and s4. Additionally, paths exist between locations s0 and p4_0, s3 and p4_3, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Links exist between locations s0 and s2, s0 and s4, s0 and s5, s1 and s2, s2 and s1, s2 and s3, s2 and s5, s4 and s0, s4 and s1, s4 and s3, s4 and s5, and s5 and s3. Paths also exist between locations p0_5 and s5, p4_0 and s4, p5_2 and s5, and s1 and p4_1. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "9d7b74a9-1708-4933-b8ee-88219390536d", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: driver2 walks to location p3_0 from location s3, driver2 walks to location s0 from location p3_0, driver2 boards truck1 at location s0, driver2 drives truck1 to location s2 from location s0, truck1 is loaded with package3 at location s2, package2 is loaded in truck1 at location s2, driver2 drives truck1 to location s0 from location s2, package2 is unloaded from truck1 at location s0, truck1 is driven from location s0 to s3 by driver2 and at location s3, package1 is loaded in truck1. Is the action: driver2 walks from location p3_0 to location s0 executable at step 2, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Driver1 is at location s1, driver2 is present at location s3, driver3 is present at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and s2 have a link between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is currently at location s3, package2 is present at location s2, package3 is currently at location s2, package4 is currently at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s0, there is a link between location s2 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing, truck2 is at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are scheduled for steps 1 through 10: driver2 moves from location s3 to location p3_0, then from location p3_0 to location s0, boards truck1 at location s0, drives truck1 from location s0 to location s2, loads truck1 with package3 at location s2, loads package2 into truck1 at location s2, drives truck1 from location s2 back to location s0, unloads package2 from truck1 at location s0, drives truck1 from location s0 to location s3, and loads package1 into truck1 at location s3. Is the action: driver2 walks from location p3_0 to location s0 executable at step 2, True or False?", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is situated at s3, and driver3 is also at s3. A path exists between p0_1 and s1, p1_2 and s2, and p3_0 and s3. Additionally, paths are present between s0 and p0_1, s0 and s1, s0 and s3, s1 and p1_2, s1 and s2, and s3 and s0. Furthermore, links exist between s0 and s1, s0 and s3, s1 and s2, s3 and s0, and s3 and s1. Package1 is currently at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Links are also present between s0 and s2, s1 and s3, and s2 and s1. Paths exist between p1_2 and s1, p1_3 and s3, s2 and p1_2, p0_1 and s0, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s0 and p3_0, s1 and p0_1, s1 and p1_3, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is at s0 and is empty, while truck2 is at s3 and contains nothing. Truck3 is also at s0 and contains nothing."}
{"question_id": "15adbe80-4171-4ae5-bc5c-a65bf8c518c6", "domain_name": "driverlog", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location s1, driver1 disembarks from truck1, at location s2, package2 is loaded in truck1, at location s3, package1 is unloaded in truck1, at location s4, package2 is unloaded in truck1, driver1 drives truck1 from location s0 to location s2, driver1 drives truck1 from location s2 to location s3, driver1 drives truck1 from location s3 to location s4, driver1 drives truck1 to location s1 from location s4, driver1 walks from location p4_1 to s1, driver1 walks from location p4_3 to location s4, driver1 walks to location p4_1 from location s4, driver1 walks to location p4_3 from location s3, package3 is unloaded from truck1 at location s4, truck1 is boarded by driver1 at location s1, truck1 is driven from location s1 to s0 by driver1, truck1 is loaded with package1 at location s2, truck1 is loaded with package3 at location s3, truck1 is loaded with package4 at location s0 and truck1 is unloaded with package4 at location s4. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is present at location s3, driver2 is currently at location s4, driver3 is present at location s3, locations p5_2 and s2 have a path between them, locations s0 and p0_5 have a path between them, locations s1 and s4 have a link between them, locations s2 and p5_2 have a path between them, locations s2 and s0 have a link between them, locations s3 and s2 have a link between them, locations s3 and s4 have a link between them, locations s4 and p4_0 have a path between them, locations s5 and p0_5 have a path between them, locations s5 and s0 have a link between them, locations s5 and s2 have a link between them, package1 is at location s2, package2 is currently at location s2, package3 is currently at location s3, package4 is at location s0, there exists a link between the locations s0 and s1, there exists a link between the locations s1 and s0, there exists a link between the locations s3 and s5, there exists a link between the locations s5 and s4, there exists a path between the locations p0_5 and s0, there exists a path between the locations p4_0 and s0, there exists a path between the locations p4_1 and s1, there exists a path between the locations p4_1 and s4, there exists a path between the locations p4_3 and s3, there exists a path between the locations p4_3 and s4, there exists a path between the locations s0 and p4_0, there exists a path between the locations s3 and p4_3, there exists a path between the locations s4 and p4_1, there exists a path between the locations s4 and p4_3, there exists a path between the locations s5 and p5_2, there is a link between location s0 and location s2, there is a link between location s0 and location s4, there is a link between location s0 and location s5, there is a link between location s1 and location s2, there is a link between location s2 and location s1, there is a link between location s2 and location s3, there is a link between location s2 and location s5, there is a link between location s4 and location s0, there is a link between location s4 and location s1, there is a link between location s4 and location s3, there is a link between location s4 and location s5, there is a link between location s5 and location s3, there is a path between location p0_5 and location s5, there is a path between location p4_0 and location s4, there is a path between location p5_2 and location s5, there is a path between location s1 and location p4_1, truck1 is currently at location s1, truck1 is empty, truck2 contains nothing and truck2 is at location s5.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is planned: at location s1, driver1 gets off truck1, at location s2, package2 is placed in truck1, at location s3, package1 is removed from truck1, at location s4, package2 is removed from truck1, driver1 drives truck1 from location s0 to location s2, then from location s2 to location s3, then from location s3 to location s4, and finally from location s4 to location s1, driver1 walks from location p4_1 to s1, from location p4_3 to location s4, from location s4 to p4_1, and from location s3 to p4_3, package3 is removed from truck1 at location s4, driver1 boards truck1 at location s1, driver1 drives truck1 from location s1 to s0, package1 is loaded into truck1 at location s2, package3 is loaded into truck1 at location s3, package4 is loaded into truck1 at location s0, and package4 is removed from truck1 at location s4. Is it possible to execute this sequence, True or False?", "initial_state_nl_paraphrased": "Driver1 is currently located at s3, while driver2 is at location s4, and driver3 is also at location s3. A path exists between locations p5_2 and s2, as well as between locations s0 and p0_5. Locations s1 and s4 are connected by a link, and locations s2 and p5_2 have a path between them. Additionally, locations s2 and s0 are linked, locations s3 and s2 are linked, and locations s3 and s4 are also linked. A path exists between locations s4 and p4_0, locations s5 and p0_5, and locations s5 and s0 are linked. Locations s5 and s2 are also linked. Package1 is located at s2, package2 is currently at s2, package3 is at location s3, and package4 is at location s0. There are links between locations s0 and s1, s1 and s0, s3 and s5, and s5 and s4. Paths exist between locations p0_5 and s0, p4_0 and s0, p4_1 and s1, p4_1 and s4, p4_3 and s3, and p4_3 and s4. Furthermore, paths exist between locations s0 and p4_0, s3 and p4_3, s4 and p4_1, s4 and p4_3, and s5 and p5_2. Links exist between locations s0 and s2, s0 and s4, s0 and s5, s1 and s2, s2 and s1, s2 and s3, s2 and s5, s4 and s0, s4 and s1, s4 and s3, s4 and s5, and s5 and s3. Paths also exist between locations p0_5 and s5, p4_0 and s4, p5_2 and s5, and s1 and p4_1. Truck1 is currently at location s1 and is empty, while truck2 is at location s5 and contains nothing."}
{"question_id": "037c0dab-50a2-4f96-8537-bf3d2c7e1e56", "domain_name": "driverlog", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location s1, driver2 disembarks from truck1, driver2 boards truck1 at location s0, driver2 boards truck3 at location s0, driver2 drives truck1 from location s0 to location s2, driver2 drives truck1 from location s0 to location s3, driver2 drives truck1 to location s0 from location s2, driver2 drives truck3 from location s0 to location s2, driver2 walks from location p0_1 to location s0, driver2 walks from location s1 to location p0_1, driver2 walks from location s3 to location p3_0, driver2 walks to location s0 from location p3_0, driver3 walks from location s3 to location p3_0, package1 is loaded in truck1 at location s3, package1 is unloaded from truck1 at location s1, package3 is unloaded from truck1 at location s1, truck1 is loaded with package2 at location s2, truck1 is loaded with package3 at location s2, truck1 is loaded with package4 at location s1 and truck1 is unloaded with package2 at location s0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Driver1 is at location s1, driver2 is present at location s3, driver3 is present at location s3, locations p0_1 and s1 have a path between them, locations p1_2 and s2 have a path between them, locations p3_0 and s3 have a path between them, locations s0 and p0_1 have a path between them, locations s0 and s1 have a link between them, locations s0 and s3 have a link between them, locations s1 and p1_2 have a path between them, locations s1 and s2 have a link between them, locations s3 and s0 have a link between them, locations s3 and s1 have a link between them, package1 is currently at location s3, package2 is present at location s2, package3 is currently at location s2, package4 is currently at location s1, there exists a link between the locations s0 and s2, there exists a link between the locations s1 and s3, there exists a link between the locations s2 and s1, there exists a path between the locations p1_2 and s1, there exists a path between the locations p1_3 and s3, there exists a path between the locations s2 and p1_2, there is a link between location s1 and location s0, there is a link between location s2 and location s0, there is a path between location p0_1 and location s0, there is a path between location p1_3 and location s1, there is a path between location p2_0 and location s0, there is a path between location p2_0 and location s2, there is a path between location p3_0 and location s0, there is a path between location s0 and location p2_0, there is a path between location s0 and location p3_0, there is a path between location s1 and location p0_1, there is a path between location s1 and location p1_3, there is a path between location s2 and location p2_0, there is a path between location s3 and location p1_3, there is a path between location s3 and location p3_0, truck1 is currently at location s0, truck1 is empty, truck2 contains nothing, truck2 is at location s3, truck3 contains nothing and truck3 is present at location s0.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: at location s1, driver2 gets off truck1, driver2 gets on truck1 at location s0, driver2 also gets on truck3 at location s0, driver2 drives truck1 from location s0 to location s2, driver2 drives truck1 from location s0 to location s3, driver2 drives truck1 back from location s2 to location s0, driver2 drives truck3 from location s0 to location s2, driver2 walks from location p0_1 to location s0, driver2 walks from location s1 to location p0_1, driver2 walks from location s3 to location p3_0, driver2 walks from location p3_0 to location s0, driver3 walks from location s3 to location p3_0, package1 is put into truck1 at location s3, package1 is taken out of truck1 at location s1, package3 is taken out of truck1 at location s1, truck1 is loaded with package2 at location s2, truck1 is loaded with package3 at location s2, truck1 is loaded with package4 at location s1, and truck1 is unloaded of package2 at location s0. Is it possible to carry out this sequence, True or False?", "initial_state_nl_paraphrased": "Driver1 is located at s1, driver2 is situated at s3, and driver3 is also at s3. A path exists between p0_1 and s1, p1_2 and s2, and p3_0 and s3. Additionally, paths are present between s0 and p0_1, s0 and s1, s0 and s3, s1 and p1_2, s1 and s2, and s3 and s0. Package1 is currently situated at s3, package2 is at s2, package3 is also at s2, and package4 is at s1. Links exist between s0 and s2, s1 and s3, and s2 and s1. Furthermore, paths are present between p1_2 and s1, p1_3 and s3, s2 and p1_2, p0_1 and s0, p1_3 and s1, p2_0 and s0, p2_0 and s2, p3_0 and s0, s0 and p2_0, s0 and p3_0, s1 and p0_1, s1 and p1_3, s2 and p2_0, s3 and p1_3, and s3 and p3_0. Truck1 is currently empty and located at s0, while truck2 is empty and at s3. Truck3 is also empty and situated at s0."}
