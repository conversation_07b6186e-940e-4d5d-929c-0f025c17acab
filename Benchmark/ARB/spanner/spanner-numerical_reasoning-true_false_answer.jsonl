{"question_id": "fd46a0ef-c63a-4dee-92d3-15f10077b7d3", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks to location2 from location1, from location2 to location3, bob walks, from location3 to location4, bob walks, from location4 to location5, bob walks, from location5, bob picks up spanner4, bob walks from location5 to location6, spanner1 is picked up by bob from location6, from location6 to location7, bob walks and spanner5 is picked up by bob from location7 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 213? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location5 and location6 exists, bob is currently at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 is linked to location5, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 can be used, spanner1 is currently at location6, spanner2 is currently at location8, spanner2 is functional, spanner3 is currently at location8, spanner3 is usable, spanner4 can be used, spanner4 is currently at location5, spanner5 is located at location7 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then from location1 to location2, from location2 to location3, from location3 to location4, and from location4 to location5. At location5, Bob picks up spanner4, then walks to location6, where he picks up spanner1. From location6, Bob walks to location7, where he picks up spanner5, resulting in the current state. In this state, is the number of valid properties that involve negations equal to 213? True or False", "initial_state_nl_paraphrased": "There is a connection between location5 and location6. Bob is presently at the shed. A link exists between location1 and location2, and location2 is also connected to location3. Furthermore, location3 is linked to location4, which in turn is connected to location5. On the other hand, location6 is linked to location7, location7 to location8, location8 to location9, and location9 to the gate. At the gate, the following items are located: nut1, which is loose, nut2, also loose, nut3, which is not secured, nut4, also not secured, and nut5, which is not secured as well. The shed is connected to location1. The following spanners are available for use: spanner1, which is currently at location6, spanner2, which is functional and at location8, spanner3, which is usable and at location8, spanner4, which is at location5, and spanner5, which is usable and located at location7."}
{"question_id": "b575b5ce-03a5-48c8-91be-8acb385c0745", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, from location1, bob picks up spanner5, bob picks up spanner4 from location1, from location1 to location2, bob walks, bob walks from location2 to location3, bob walks from location3 to location4, spanner1 is picked up by bob from location4, from location4 to location5, bob walks, bob walks from location5 to location6, bob picks up spanner3 from location6, bob walks from location6 to location7, spanner2 is picked up by bob from location7, bob walks from location7 to location8, from location8 to location9, bob walks, bob walks from location9 to gate, at gate, bob uses spanner5 to tighten nut1, nut2 is tightened by bob using spanner4 at gate, at gate, bob uses spanner3 to tighten nut3 and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 33? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is located at shed, location3 is linked to location4, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is not secured, shed and location1 are linked, spanner1 is located at location4, spanner1 is usable, spanner2 can be used, spanner2 is located at location7, spanner3 is located at location6, spanner3 is usable, spanner4 is currently at location1, spanner4 is usable, spanner5 is at location1 and spanner5 is usable.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob walks from the shed to location1, then picks up spanner5 and spanner4 from location1. He then proceeds to walk from location1 to location2, from location2 to location3, and from location3 to location4, where he picks up spanner1. Next, Bob walks from location4 to location5, from location5 to location6, where he picks up spanner3, and from location6 to location7, where he picks up spanner2. He then walks from location7 to location8, from location8 to location9, and finally from location9 to the gate. Upon reaching the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 33? True or False", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a link is established between location5 and location6, and a connection exists between location6 and location7. Bob is currently at the shed. Additionally, location3 is connected to location4, location7 is linked to location8, location8 is connected to location9, and location9 is linked to the gate. At the gate, nut1 is present and is loose, nut2 is also located at the gate and is loose, nut3 is at the gate but not secured, nut4 is at the gate and is loose, and nut5 is at the gate but not secured. Furthermore, the shed is connected to location1. Spanner1 is located at location4 and is in working condition, spanner2 is available for use and is at location7, spanner3 is at location6 and is usable, spanner4 is currently at location1 and is in working condition, and spanner5 is also at location1 and is usable."}
{"question_id": "c3b71d0b-69df-4795-a90b-cfa80c58f382", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 175? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location2 and location3 exists, a link between location3 and location4 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location4 is linked to location5, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, spanner1 is functional, spanner1 is located at location8, spanner2 is currently at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location2, spanner5 is currently at location6 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, does the number of valid properties of the state that involve negations equal 175? True or False", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, a connection between location3 and location4, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is situated at the shed, location1 is connected to location2, location4 is connected to location5, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, nut1 is presently at the gate and is loose, nut2 is also at the gate and is loose, nut3 is at the gate and is loose, nut4 is currently at the gate but is not secured, nut5 is at the gate and is loose, spanner1 is in working order and is located at location8, spanner2 is currently at location6 and is functional, spanner3 is at location2 and is usable, spanner4 is functional and is located at location2, spanner5 is currently at location6 and is usable."}
{"question_id": "3241529d-f1b0-4319-aa71-4174273b4ecd", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob picks up spanner5 from location1, from location1, bob picks up spanner4, from location1 to location2, bob walks, bob walks from location2 to location3, bob walks from location3 to location4, from location4, bob picks up spanner1, from location4 to location5, bob walks, bob walks to location6 from location5, bob picks up spanner3 from location6, from location6 to location7, bob walks, spanner2 is picked up by bob from location7, bob walks from location7 to location8, from location8 to location9, bob walks, bob walks to gate from location9, nut1 is tightened by bob using spanner5 at gate, bob tightens nut2 with spanner4 at gate, bob tightens nut3 with spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 224? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is located at shed, location3 is linked to location4, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is not secured, shed and location1 are linked, spanner1 is located at location4, spanner1 is usable, spanner2 can be used, spanner2 is located at location7, spanner3 is located at location6, spanner3 is usable, spanner4 is currently at location1, spanner4 is usable, spanner5 is at location1 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then collects spanner5 from location1, followed by picking up spanner4 from the same location. Next, Bob walks from location1 to location2, then proceeds to location3, and subsequently to location4. At location4, Bob picks up spanner1, then walks to location5, and from there to location6, where he collects spanner3. Bob then walks to location7, picks up spanner2, and moves to location8, then to location9. Finally, Bob walks from location9 to the gate, where he uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, is the number of valid properties of the state that involve negations equal to 224? True or False", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a link is established between location5 and location6, and a connection exists between location6 and location7. Bob is currently at the shed. Additionally, location3 is connected to location4, location7 is linked to location8, location8 is connected to location9, and location9 is linked to the gate. At the gate, nut1 is present and is loose, nut2 is also located at the gate and is loose, nut3 is at the gate but not secured, nut4 is at the gate and is loose, and nut5 is at the gate but not secured. Furthermore, the shed is connected to location1. Spanner1 is located at location4 and is in working condition, spanner2 is usable and is located at location7, spanner3 is at location6 and is usable, spanner4 is currently at location1 and is usable, and spanner5 is also at location1 and is usable."}
{"question_id": "4ca0994a-2cb3-4b95-b1db-cdd31a4ba7cb", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, bob walks to location3 from location2, bob walks to location4 from location3, from location4 to location5, bob walks, bob picks up spanner4 from location5, bob walks to location6 from location5, from location6, bob picks up spanner1, from location6 to location7, bob walks, from location7, bob picks up spanner5, bob walks from location7 to location8, from location8, bob picks up spanner3, spanner2 is picked up by bob from location8, from location8 to location9, bob walks, bob walks to gate from location9, bob tightens nut1 with spanner5 at gate, nut2 is tightened by bob using spanner4 at gate, nut3 is tightened by bob using spanner3 at gate and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 356? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location5 and location6 exists, bob is currently at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 is linked to location5, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 can be used, spanner1 is currently at location6, spanner2 is currently at location8, spanner2 is functional, spanner3 is currently at location8, spanner3 is usable, spanner4 can be used, spanner4 is currently at location5, spanner5 is located at location7 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, followed by location3, and then location4. From location4, Bob proceeds to location5, where he collects spanner4. Next, Bob walks to location6 and picks up spanner1. He then moves to location7, where he collects spanner5, and subsequently walks to location8. At location8, Bob picks up both spanner3 and spanner2. From location8, Bob walks to location9 and then to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, is the number of executable and inexecutable actions equal to 356? True or False", "initial_state_nl_paraphrased": "There is a connection between location5 and location6. Bob is presently at the shed. A link exists between location1 and location2, and location2 is also connected to location3. Furthermore, location3 is linked to location4, which in turn is connected to location5. On the other hand, location6 is linked to location7, location7 to location8, location8 to location9, and location9 to the gate. The shed is connected to location1. At the gate, the following items are located: nut1, which is loose, nut2, also loose, nut3, which is not secured, nut4, also not secured, and nut5, which is not secured as well. As for the spanners, spanner1 is available and is currently at location6, spanner2 is functional and located at location8, spanner3 is usable and also at location8, spanner4 is available and at location5, and spanner5 is usable and located at location7."}
{"question_id": "096b501e-bf16-4887-818b-6744a6e1d623", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 220? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location2 and location3 exists, a link between location3 and location4 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location4 is linked to location5, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, spanner1 is functional, spanner1 is located at location8, spanner2 is currently at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location2, spanner5 is currently at location6 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, does the number of valid properties of the state that involve negations equal 220? True or False", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, a connection between location3 and location4, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is currently at the shed, location1 is connected to location2, location4 is connected to location5, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, nut1 is currently situated at the gate and is loose, nut2 is also at the gate and is loose, nut3 is at the gate and is loose, nut4 is currently at the gate but is not secured, nut5 is located at the gate and is loose, spanner1 is in working order and is located at location8, spanner2 is currently at location6 and is functional, spanner3 is currently at location2 and is usable, spanner4 is functional and is located at location2, spanner5 is currently at location6 and is usable."}
{"question_id": "2eb6a571-9d03-4d44-8170-a9f85068e12c", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, is the number of executable and inexecutable actions equal to 440? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, nut1 is at gate, nut1 is not secured, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is not secured, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 can be used, spanner1 is currently at location3, spanner2 is functional, spanner2 is located at location5, spanner3 is at location2, spanner3 is usable, spanner4 can be used, spanner4 is at location6, spanner5 is at location3 and spanner5 is functional.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are taken: bob moves from the shed to location1 to achieve the current state. In this state, is the number of executable and inexecutable actions equal to 440? True or False", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a connection exists between location8 and location9, and a connection is found between location9 and the gate. Additionally, a connection exists between the shed and location1. Bob is currently situated at the shed. Location3 and location4 are interconnected, location5 is linked to location6, location6 is connected to location7, and location7 is linked to location8. At the gate, nut1 is present and unsecured, nut2 is also at the gate and unsecured, nut3 is currently at the gate and unsecured, nut4 is at the gate and loose, and nut5 is located at the gate and loose. Spanner1 is available for use and is currently situated at location3, spanner2 is functional and located at location5, spanner3 is at location2 and usable, spanner4 is available for use and is at location6, and spanner5 is at location3 and functional."}
{"question_id": "df71806d-509b-4d1e-b604-f32f2b96e539", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks to location2 from location1, from location2 to location3, bob walks, from location3 to location4, bob walks, bob walks from location4 to location5, from location5, bob picks up spanner4, bob walks from location5 to location6, from location6, bob picks up spanner1, from location6 to location7, bob walks, bob picks up spanner5 from location7, from location7 to location8, bob walks, from location8, bob picks up spanner3, bob picks up spanner2 from location8, bob walks to location9 from location8, bob walks to gate from location9, at gate, bob uses spanner5 to tighten nut1, bob tightens nut2 with spanner4 at gate, nut3 is tightened by bob using spanner3 at gate and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 25? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location5 and location6 exists, bob is currently at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 is linked to location5, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 can be used, spanner1 is currently at location6, spanner2 is currently at location8, spanner2 is functional, spanner3 is currently at location8, spanner3 is usable, spanner4 can be used, spanner4 is currently at location5, spanner5 is located at location7 and spanner5 is usable.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob walks from the shed to location1, then from location1 to location2, from location2 to location3, from location3 to location4, and from location4 to location5. At location5, Bob picks up spanner4, then walks to location6, where he picks up spanner1. He proceeds to location7, picks up spanner5, and then walks to location8. At location8, Bob picks up both spanner3 and spanner2. He then walks to location9 and from there to the gate. Upon reaching the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 25? True or False", "initial_state_nl_paraphrased": "There is a connection between location5 and location6. Bob is presently at the shed. A link exists between location1 and location2, and location2 is also connected to location3. Furthermore, location3 is linked to location4, which in turn is connected to location5. On the other hand, location6 is linked to location7, location7 to location8, location8 to location9, and location9 to the gate. At the gate, the following items are located: nut1, which is loose, nut2, also loose, nut3, which is not secured, nut4, also not secured, and nut5, which is not secured as well. The shed is connected to location1. The following spanners are available for use: spanner1, which is currently at location6, spanner2, which is at location8 and functional, spanner3, also at location8 and usable, spanner4, which is at location5, and spanner5, located at location7 and usable."}
{"question_id": "cb2e20a3-5930-47dc-83e9-4a7807d7469e", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, spanner3 is picked up by bob from location2, bob walks to location3 from location2, bob picks up spanner5 from location3, spanner1 is picked up by bob from location3, from location3 to location4, bob walks, from location4 to location5, bob walks, spanner2 is picked up by bob from location5, bob walks to location6 from location5, from location6, bob picks up spanner4, from location6 to location7, bob walks, from location7 to location8, bob walks, bob walks from location8 to location9, bob walks to gate from location9, nut1 is tightened by bob using spanner5 at gate, bob tightens nut2 with spanner4 at gate, nut3 is tightened by bob using spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, is the number of executable actions equal to 2? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, nut1 is at gate, nut1 is not secured, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is not secured, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 can be used, spanner1 is currently at location3, spanner2 is functional, spanner2 is located at location5, spanner3 is at location2, spanner3 is usable, spanner4 can be used, spanner4 is at location6, spanner5 is at location3 and spanner5 is functional.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then to location2, where he collects spanner3. He proceeds to location3, picks up spanner5 and spanner1, and then walks to location4 and location5. At location5, he collects spanner2 and moves to location6, where he picks up spanner4. He then walks to location7, location8, and location9, before heading to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, is the number of executable actions equal to 2? True or False", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a connection exists between location8 and location9, and a connection is found between location9 and the gate. Additionally, a connection exists between the shed and location1. Bob is currently situated at the shed. Location3 and location4 are interconnected, location5 is linked to location6, location6 is connected to location7, and location7 is linked to location8. At the gate, nut1 is present and unsecured, nut2 is also at the gate and unsecured, nut3 is currently at the gate and unsecured, nut4 is at the gate and loose, and nut5 is located at the gate and loose. Spanner1 is available for use and is currently situated at location3, spanner2 is functional and located at location5, spanner3 is at location2 and usable, spanner4 is available for use and is at location6, and spanner5 is at location3 and functional."}
{"question_id": "75b29b35-4b5f-4be0-8bed-be3556277b79", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, from location2, bob picks up spanner4, spanner3 is picked up by bob from location2, bob walks from location2 to location3, bob walks from location3 to location4, from location4 to location5, bob walks, bob walks from location5 to location6, from location6, bob picks up spanner5 and from location6, bob picks up spanner2 to reach the current state. Is it True or False that the number of actions that led to current state in the sequence is equal to 8?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location2 and location3 exists, a link between location3 and location4 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location4 is linked to location5, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, spanner1 is functional, spanner1 is located at location8, spanner2 is currently at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location2, spanner5 is currently at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then to location2, where he collects spanner4 and spanner3. He then proceeds to location3, followed by location4, then location5, and subsequently location6. At location6, Bob picks up spanner5 and spanner2, ultimately reaching the current state. Is it True or False that the total number of actions in this sequence that led to the current state is 8?", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, a connection between location3 and location4, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is situated at the shed, location1 is connected to location2, location4 is connected to location5, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, nut1 is presently at the gate and is loose, nut2 is at the gate and is loose, nut3 is at the gate and is loose, nut4 is currently at the gate but is not secured, nut5 is at the gate and is loose, spanner1 is in working order and is located at location8, spanner2 is currently at location6 and is functional, spanner3 is currently at location2 and is usable, spanner4 is functional and is located at location2, spanner5 is currently at location6 and is usable."}
{"question_id": "37666e52-5f0b-48bd-bc5c-1bd99b5b959e", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 220? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is located at shed, location3 is linked to location4, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is not secured, shed and location1 are linked, spanner1 is located at location4, spanner1 is usable, spanner2 can be used, spanner2 is located at location7, spanner3 is located at location6, spanner3 is usable, spanner4 is currently at location1, spanner4 is usable, spanner5 is at location1 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, does the number of valid properties of the state that involve negations equal 220?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a link is established between location5 and location6, and a connection exists between location6 and location7. Bob is currently at the shed. Additionally, location3 is connected to location4, location7 is linked to location8, location8 is connected to location9, and location9 is linked to the gate. At the gate, nut1, nut2, and nut4 are loose, while nut3 and nut5 are not secured. The shed is connected to location1. Spanner1, located at location4, is in working condition, and spanner2, which can be used, is at location7. Spanner3, located at location6, is usable, and spanner4, currently at location1, is also in working condition. Furthermore, spanner5 is at location1 and is usable."}
{"question_id": "1b33613d-76ff-431a-8e26-9685fd21237b", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks to location2 from location1, spanner4 is picked up by bob from location2, from location2, bob picks up spanner3, bob walks from location2 to location3, bob walks from location3 to location4, from location4 to location5, bob walks, bob walks to location6 from location5, bob picks up spanner5 from location6 and spanner2 is picked up by bob from location6 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 220? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location2 and location3 exists, a link between location3 and location4 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location4 is linked to location5, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, spanner1 is functional, spanner1 is located at location8, spanner2 is currently at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location2, spanner5 is currently at location6 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then proceeds to location2, where he collects spanner4 and spanner3. From location2, Bob walks to location3, then to location4, and subsequently to location5. He continues from location5 to location6, where he picks up spanner5 and spanner2, ultimately reaching the current state. In this state, is the number of valid properties that involve negations equal to 220? True or False", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, a connection between location3 and location4, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is situated at the shed, location1 is connected to location2, location4 is connected to location5, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, nut1 is presently at the gate and is loose, nut2 is at the gate and is loose, nut3 is at the gate and is loose, nut4 is currently at the gate but is not secured, nut5 is at the gate and is loose, spanner1 is in working order and is located at location8, spanner2 is currently at location6 and is functional, spanner3 is currently at location2 and is usable, spanner4 is functional and is located at location2, spanner5 is currently at location6 and is usable."}
{"question_id": "dcc0360b-8315-43a2-9508-f3c937457929", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 219? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location5 and location6 exists, bob is currently at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 is linked to location5, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 can be used, spanner1 is currently at location6, spanner2 is currently at location8, spanner2 is functional, spanner3 is currently at location8, spanner3 is usable, spanner4 can be used, spanner4 is currently at location5, spanner5 is located at location7 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, does the number of valid properties of the state that involve negations equal 219?", "initial_state_nl_paraphrased": "There is a connection between location5 and location6. Bob's current location is the shed. A link exists between location1 and location2, and location2 is also connected to location3. Furthermore, location3 is linked to location4, and location4 is connected to location5. On the other hand, location6 is linked to location7, location7 is connected to location8, and location8 is linked to location9. Location9 is connected to the gate. At the gate, the following items are located: nut1, which is loose, nut2, also loose, nut3, which is not secured, nut4, also not secured, and nut5, which is not secured as well. The shed is connected to location1. The following spanners are available for use: spanner1, which is currently at location6, spanner2, which is functional and located at location8, spanner3, which is usable and also at location8, spanner4, which is at location5, and spanner5, which is located at location7 and is usable."}
{"question_id": "1e16f6aa-e5b6-47ef-96df-4e16a7097da9", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, from location1, bob picks up spanner5, bob picks up spanner4 from location1, bob walks from location1 to location2, from location2 to location3, bob walks, bob walks to location4 from location3, bob picks up spanner1 from location4, bob walks from location4 to location5, from location5 to location6, bob walks and from location6, bob picks up spanner3 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 232? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is located at shed, location3 is linked to location4, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is not secured, shed and location1 are linked, spanner1 is located at location4, spanner1 is usable, spanner2 can be used, spanner2 is located at location7, spanner3 is located at location6, spanner3 is usable, spanner4 is currently at location1, spanner4 is usable, spanner5 is at location1 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then collects spanner5 and spanner4 from location1, proceeds to location2, then to location3, walks, and then moves from location3 to location4, where he picks up spanner1. He then walks from location4 to location5, then to location6, walks again, and finally collects spanner3 from location6 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 232? True or False", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a link is established between location5 and location6, and a connection exists between location6 and location7. Bob is currently at the shed. Additionally, location3 is connected to location4, location7 is linked to location8, location8 is connected to location9, and location9 is linked to the gate. At the gate, nut1 is located and is loose, nut2 is also at the gate and is loose, nut3 is at the gate but not secured, nut4 is at the gate and is loose, and nut5 is at the gate but not secured. Furthermore, the shed is connected to location1. Spanner1 is at location4 and is in working condition, spanner2 is at location7 and can be used, spanner3 is at location6 and is usable, spanner4 is currently at location1 and is in working condition, and spanner5 is also at location1 and is usable."}
{"question_id": "42c2b50a-916f-49af-8ba7-9f86bebde50a", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_12", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 233? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, nut1 is at gate, nut1 is not secured, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is not secured, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 can be used, spanner1 is currently at location3, spanner2 is functional, spanner2 is located at location5, spanner3 is at location2, spanner3 is usable, spanner4 can be used, spanner4 is at location6, spanner5 is at location3 and spanner5 is functional.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: bob moves from the shed to location1 to reach the current state. In this state, does the total number of valid properties (including both affirmative and negated properties) equal 233? True or False", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location2 and location3, a connection between location4 and location5, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, bob is currently at the shed, location3 and location4 are connected, location5 and location6 are connected, location6 is connected to location7, location7 and location8 are connected, nut1 is situated at the gate and is unsecured, nut2 is also at the gate and is unsecured, nut3 is currently at the gate and is unsecured, nut4 is at the gate and is loose, nut5 is located at the gate and is loose, spanner1 is available for use and is currently at location3, spanner2 is in working order and is located at location5, spanner3 is at location2 and is usable, spanner4 is available for use and is at location6, and spanner5 is at location3 and is functional."}
{"question_id": "5394f1ba-41fd-487b-bd33-c0a376c83872", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 31? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location2 and location3 exists, a link between location3 and location4 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location4 is linked to location5, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, spanner1 is functional, spanner1 is located at location8, spanner2 is currently at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location2, spanner5 is currently at location6 and spanner5 is usable.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, are there 31 valid properties that do not involve negations? True or False", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, a connection between location3 and location4, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is situated at the shed, location1 is connected to location2, location4 is connected to location5, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, nut1 is presently at the gate and is loose, nut2 is also at the gate and is loose, nut3 is at the gate and is loose, nut4 is currently at the gate but is not secured, nut5 is at the gate and is loose, spanner1 is in working condition and is located at location8, spanner2 is currently at location6 and is functional, spanner3 is at location2 and is usable, spanner4 is functional and is located at location2, spanner5 is currently at location6 and is usable."}
{"question_id": "95d30f18-3694-408e-a2a6-c6485135ee3f", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, from location1 to location2, bob walks, spanner3 is picked up by bob from location2, from location2 to location3, bob walks, bob picks up spanner5 from location3, spanner1 is picked up by bob from location3, bob walks to location4 from location3, from location4 to location5, bob walks, from location5, bob picks up spanner2, bob walks to location6 from location5, from location6, bob picks up spanner4, from location6 to location7, bob walks, bob walks to location8 from location7, bob walks from location8 to location9, bob walks to gate from location9, bob tightens nut1 with spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, at gate, bob uses spanner3 to tighten nut3 and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 28? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, nut1 is at gate, nut1 is not secured, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is not secured, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 can be used, spanner1 is currently at location3, spanner2 is functional, spanner2 is located at location5, spanner3 is at location2, spanner3 is usable, spanner4 can be used, spanner4 is at location6, spanner5 is at location3 and spanner5 is functional.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, where he picks up spanner3. From location2, Bob proceeds to location3, picks up spanner5 and spanner1, and then heads to location4. He then moves to location5, picks up spanner2, and continues to location6, where he picks up spanner4. Next, Bob walks to location7, then to location8, and finally to location9. From location9, he proceeds to the gate, where he uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 28? True or False", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a connection exists between location8 and location9, and a connection is found between location9 and the gate. Additionally, a connection exists between the shed and location1. Bob is currently situated at the shed. Location3 and location4 are interconnected, location5 is linked to location6, location6 is connected to location7, and location7 is linked to location8. At the gate, nut1 is present and unsecured, nut2 is also at the gate and unsecured, nut3 is currently at the gate and unsecured, nut4 is at the gate and loose, and nut5 is located at the gate and loose. Spanner1 is available for use and is currently situated at location3, spanner2 is functional and located at location5, spanner3 is at location2 and usable, spanner4 is available for use and is at location6, and spanner5 is at location3 and functional."}
{"question_id": "5ac8e654-f773-416d-bcd4-6ca0b17a86e9", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 31? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, nut1 is at gate, nut1 is not secured, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is not secured, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 can be used, spanner1 is currently at location3, spanner2 is functional, spanner2 is located at location5, spanner3 is at location2, spanner3 is usable, spanner4 can be used, spanner4 is at location6, spanner5 is at location3 and spanner5 is functional.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, are there 31 valid properties that do not involve negations? True or False", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location2 and location3 exists, a connection between location4 and location5 exists, a connection between location8 and location9 exists, a connection between location9 and the gate exists, a connection between the shed and location1 exists, bob is currently at the shed, location3 and location4 are connected, location5 and location6 are connected, location6 is connected to location7, location7 and location8 are connected, nut1 is currently located at the gate and is unsecured, nut2 is at the gate and is unsecured, nut3 is at the gate and is unsecured, nut4 is at the gate and is loose, nut5 is located at the gate and is loose, spanner1 is available and is currently at location3, spanner2 is in working condition and is located at location5, spanner3 is at location2 and is usable, spanner4 is available and is at location6, and spanner5 is at location3 and is functional."}
{"question_id": "e427fae4-304a-4479-8cdc-338d6512f3fa", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks from location1 to location2, bob walks to location3 from location2, from location3 to location4, bob walks, bob walks to location5 from location4, spanner4 is picked up by bob from location5, bob walks from location5 to location6, from location6, bob picks up spanner1, from location6 to location7, bob walks and bob picks up spanner5 from location7 to reach the current state. In this state, is the number of executable actions equal to 1? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location5 and location6 exists, bob is currently at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 is linked to location5, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 can be used, spanner1 is currently at location6, spanner2 is currently at location8, spanner2 is functional, spanner3 is currently at location8, spanner3 is usable, spanner4 can be used, spanner4 is currently at location5, spanner5 is located at location7 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then to location2, then to location3, then to location4, and then to location5. At location5, Bob picks up spanner4, then walks to location6, where he picks up spanner1. From location6, Bob proceeds to location7, where he picks up spanner5, resulting in the current state. In this state, is the number of executable actions equal to 1? True or False", "initial_state_nl_paraphrased": "There is a connection between location5 and location6. Bob is presently at the shed. A link exists between location1 and location2, and location2 is also connected to location3. Furthermore, location3 is linked to location4, which in turn is connected to location5. On the other hand, location6 is linked to location7, location7 to location8, location8 to location9, and location9 to the gate. At the gate, the following items are located: nut1, which is loose, nut2, also loose, nut3, which is not secured, nut4, also not secured, and nut5, which is not secured as well. The shed is connected to location1. The following spanners are available for use: spanner1, which is currently at location6, spanner2, which is functional and located at location8, spanner3, which is usable and also at location8, spanner4, which is at location5, and spanner5, which is located at location7 and is usable."}
{"question_id": "19e85335-da93-425b-93f3-f5b7c4c5344d", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks to location2 from location1, from location2, bob picks up spanner3, from location2 to location3, bob walks, spanner5 is picked up by bob from location3, from location3, bob picks up spanner1, bob walks to location4 from location3, from location4 to location5, bob walks, spanner2 is picked up by bob from location5 and from location5 to location6, bob walks to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 220? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, nut1 is at gate, nut1 is not secured, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is not secured, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 can be used, spanner1 is currently at location3, spanner2 is functional, spanner2 is located at location5, spanner3 is at location2, spanner3 is usable, spanner4 can be used, spanner4 is at location6, spanner5 is at location3 and spanner5 is functional.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then from location1 to location2, where he picks up spanner3. Next, he walks from location2 to location3, picks up spanner5, and then spanner1 from location3. After that, he walks from location3 to location4, then to location5, where he picks up spanner2, and finally walks from location5 to location6 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 220? True or False", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a connection exists between location8 and location9, and a connection is found between location9 and the gate. Additionally, a connection exists between the shed and location1. Bob is currently situated at the shed. Location3 and location4 are interconnected, location5 is linked to location6, location6 is connected to location7, and location7 is linked to location8. At the gate, nut1 is present and unsecured, nut2 is also at the gate and unsecured, nut3 is currently at the gate and unsecured, nut4 is at the gate and loose, and nut5 is located at the gate and loose. Spanner1 is available for use and is currently situated at location3, spanner2 is functional and located at location5, spanner3 is at location2 and usable, spanner4 is available for use and is at location6, and spanner5 is at location3 and functional."}
{"question_id": "9dd64dea-6b5c-4fe9-8092-67fa8dc17479", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, spanner4 is picked up by bob from location2, spanner3 is picked up by bob from location2, bob walks from location2 to location3, bob walks from location3 to location4, bob walks to location5 from location4, from location5 to location6, bob walks, spanner5 is picked up by bob from location6, spanner2 is picked up by bob from location6, bob walks from location6 to location7, from location7 to location8, bob walks, spanner1 is picked up by bob from location8, from location8 to location9, bob walks, bob walks from location9 to gate, nut1 is tightened by bob using spanner5 at gate, bob tightens nut2 with spanner4 at gate, nut3 is tightened by bob using spanner3 at gate and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 27? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location2 and location3 exists, a link between location3 and location4 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location4 is linked to location5, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, spanner1 is functional, spanner1 is located at location8, spanner2 is currently at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location2, spanner5 is currently at location6 and spanner5 is usable.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, where he collects spanner4 and spanner3. He proceeds to location3, then location4, followed by location5, and then location6, where he picks up spanner5 and spanner2. Bob then walks to location7 and location8, where he collects spanner1. He continues to location9 and finally reaches the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 27? True or False", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, a connection between location3 and location4, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is currently at the shed, location1 is connected to location2, location4 is connected to location5, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, nut1 is currently situated at the gate and is loose, nut2 is also at the gate and is loose, nut3 is at the gate and is loose, nut4 is currently at the gate but is not secured, nut5 is located at the gate and is loose, spanner1 is in working condition and is located at location8, spanner2 is currently at location6 and is functional, spanner3 is currently at location2 and is usable, spanner4 is functional and is located at location2, spanner5 is currently at location6 and is usable."}
{"question_id": "aaaa78e8-6d47-450d-b0ab-7ede6257b10a", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 220? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location5 and location6 exists, bob is currently at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 is linked to location5, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 can be used, spanner1 is currently at location6, spanner2 is currently at location8, spanner2 is functional, spanner3 is currently at location8, spanner3 is usable, spanner4 can be used, spanner4 is currently at location5, spanner5 is located at location7 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, does the number of valid properties of the state that involve negations equal 220?", "initial_state_nl_paraphrased": "There is a connection between location5 and location6. Bob is presently at the shed. A link exists between location1 and location2, and location2 is also connected to location3. Furthermore, location3 is linked to location4, which in turn is connected to location5. On the other hand, location6 is linked to location7, location7 to location8, location8 to location9, and location9 to the gate. At the gate, the following items are located: nut1, which is loose, nut2, also loose, nut3, which is not secured, nut4, also not secured, and nut5, which is not secured as well. The shed is connected to location1. The following spanners are available for use: spanner1, which is currently at location6, spanner2, which is functional and at location8, spanner3, which is usable and at location8, spanner4, which is at location5, and spanner5, which is usable and located at location7."}
{"question_id": "496560c3-f235-494f-ad8a-4cf49fe6ef6c", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_11", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob picks up spanner5 from location1, from location1, bob picks up spanner4, bob walks from location1 to location2, bob walks to location3 from location2, bob walks to location4 from location3, spanner1 is picked up by bob from location4, from location4 to location5, bob walks, bob walks to location6 from location5, bob picks up spanner3 from location6, bob walks to location7 from location6, spanner2 is picked up by bob from location7, bob walks from location7 to location8, bob walks to location9 from location8, bob walks to gate from location9, bob tightens nut1 with spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, bob tightens nut3 with spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 251? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is located at shed, location3 is linked to location4, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is not secured, shed and location1 are linked, spanner1 is located at location4, spanner1 is usable, spanner2 can be used, spanner2 is located at location7, spanner3 is located at location6, spanner3 is usable, spanner4 is currently at location1, spanner4 is usable, spanner5 is at location1 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob walks from the shed to location1, picks up spanner5 at location1, and then picks up spanner4 from the same location. He then proceeds to walk from location1 to location2, then to location3, and subsequently to location4. At location4, Bob picks up spanner1, and then walks to location5, followed by location6, where he picks up spanner3. He then walks to location7, picks up spanner2, and continues to location8, then location9, before finally reaching the gate. Upon arriving at the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, is the total number of valid properties (including both affirmative and negated properties) equal to 251? True or False", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a link is established between location5 and location6, and a connection exists between location6 and location7. Bob is currently at the shed. Additionally, location3 is connected to location4, location7 is linked to location8, location8 is connected to location9, and location9 is linked to the gate. At the gate, nut1, nut2, and nut4 are loose, while nut3 and nut5 are not secured. The shed is connected to location1. Spanner1, located at location4, is in working condition, and spanner2, which can be used, is at location7. Spanner3, located at location6, is usable, and spanner4, currently at location1, is also in working condition. Furthermore, spanner5 is at location1 and is usable."}
{"question_id": "dc381983-c30a-43fd-b6d2-a26ac967d44b", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 244? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, nut1 is at gate, nut1 is not secured, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is not secured, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 can be used, spanner1 is currently at location3, spanner2 is functional, spanner2 is located at location5, spanner3 is at location2, spanner3 is usable, spanner4 can be used, spanner4 is at location6, spanner5 is at location3 and spanner5 is functional.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, does the number of valid properties of the state that involve negations equal 244?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a connection exists between location8 and location9, and a connection is found between location9 and the gate. Additionally, a connection exists between the shed and location1. Bob is currently situated at the shed. Location3 and location4 are interconnected, location5 is linked to location6, location6 is connected to location7, and location7 is linked to location8. At the gate, nut1 is present and unsecured, nut2 is also at the gate and unsecured, nut3 is currently at the gate and unsecured, nut4 is at the gate and loose, and nut5 is located at the gate and loose. Spanner1 is available for use and is currently situated at location3, spanner2 is functional and located at location5, spanner3 is at location2 and usable, spanner4 is available for use and situated at location6, and spanner5 is at location3 and functional."}
{"question_id": "6054fbae-09a7-45d6-9ba8-debda0356454", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, from location1 to location2, bob walks, bob picks up spanner4 from location2, bob picks up spanner3 from location2, bob walks to location3 from location2, bob walks to location4 from location3, bob walks to location5 from location4, bob walks from location5 to location6, from location6, bob picks up spanner5, bob picks up spanner2 from location6, bob walks to location7 from location6, from location7 to location8, bob walks, from location8, bob picks up spanner1, bob walks to location9 from location8, from location9 to gate, bob walks, nut1 is tightened by bob using spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, nut3 is tightened by bob using spanner3 at gate and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 224? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location2 and location3 exists, a link between location3 and location4 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location4 is linked to location5, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, spanner1 is functional, spanner1 is located at location8, spanner2 is currently at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location2, spanner5 is currently at location6 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob walks from the shed to location1, then from location1 to location2, where he collects spanner4 and spanner3. He proceeds to location3 from location2, then to location4 from location3, followed by location5 from location4, and location6 from location5. At location6, Bob picks up spanner5 and spanner2, then heads to location7 from location6, and from location7 to location8. At location8, he collects spanner1 and walks to location9, and finally from location9 to the gate. Upon reaching the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, is the number of valid properties that involve negations equal to 224? True or False", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, a connection between location3 and location4, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is currently at the shed, location1 is connected to location2, location4 is connected to location5, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, nut1 is currently located at the gate and is loose, nut2 is at the gate and is loose, nut3 is located at the gate and is loose, nut4 is currently at the gate but is not secured, nut5 is located at the gate and is loose, spanner1 is in working condition and is located at location8, spanner2 is currently at location6 and is functional, spanner3 is currently at location2 and is usable, spanner4 is functional and is located at location2, spanner5 is currently at location6 and is usable."}
{"question_id": "fa259dfb-c6ed-42f7-84d1-26914dee5438", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, is the number of executable actions equal to 1? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location2 and location3 exists, a link between location3 and location4 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location4 is linked to location5, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, spanner1 is functional, spanner1 is located at location8, spanner2 is currently at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location2, spanner5 is currently at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are taken: bob moves from the shed to location1 to attain the current state. In this state, is the number of executable actions equal to 1? True or False", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, a connection between location3 and location4, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is currently at the shed, location1 is connected to location2, location4 is connected to location5, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, nut1 is currently situated at the gate and is loose, nut2 is at the gate and is loose, nut3 is situated at the gate and is loose, nut4 is currently at the gate but is not secured, nut5 is at the gate and is loose, spanner1 is in working condition and is located at location8, spanner2 is currently at location6 and is functional, spanner3 is currently at location2 and is usable, spanner4 is in working condition and is located at location2, spanner5 is currently at location6 and is usable."}
{"question_id": "7f3c4343-181f-41cb-843e-6f08fa8faa46", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks from location1 to location2, bob picks up spanner3 from location2, bob walks to location3 from location2, spanner5 is picked up by bob from location3, spanner1 is picked up by bob from location3, from location3 to location4, bob walks, from location4 to location5, bob walks, bob picks up spanner2 from location5 and from location5 to location6, bob walks to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 32? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, nut1 is at gate, nut1 is not secured, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is not secured, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 can be used, spanner1 is currently at location3, spanner2 is functional, spanner2 is located at location5, spanner3 is at location2, spanner3 is usable, spanner4 can be used, spanner4 is at location6, spanner5 is at location3 and spanner5 is functional.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then to location2, where he collects spanner3. He proceeds to location3, where he picks up both spanner5 and spanner1. From there, he walks to location4 and then to location5, where he collects spanner2. Finally, he moves to location6, reaching the current state. In this state, is the number of valid properties that do not involve negations equal to 32? True or False", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location2 and location3, a connection between location4 and location5, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is currently at the shed, location3 and location4 are connected, location5 and location6 are connected, location6 is connected to location7, location7 and location8 are connected, nut1 is situated at the gate and is unsecured, nut2 is at the gate and is unsecured, nut3 is currently situated at the gate and is unsecured, nut4 is at the gate and is loose, nut5 is located at the gate and is loose, spanner1 is available for use and is currently at location3, spanner2 is in working condition and is located at location5, spanner3 is at location2 and is usable, spanner4 is available for use and is at location6, and spanner5 is at location3 and is functional."}
{"question_id": "0253c84c-53b8-42dc-ad69-5a98fb0f42e8", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, from location1, bob picks up spanner5, spanner4 is picked up by bob from location1, from location1 to location2, bob walks, bob walks to location3 from location2, bob walks to location4 from location3, from location4, bob picks up spanner1, bob walks to location5 from location4, from location5 to location6, bob walks, from location6, bob picks up spanner3, bob walks to location7 from location6, spanner2 is picked up by bob from location7, bob walks to location8 from location7, bob walks from location8 to location9, from location9 to gate, bob walks, at gate, bob uses spanner5 to tighten nut1, nut2 is tightened by bob using spanner4 at gate, nut3 is tightened by bob using spanner3 at gate and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, is the number of executable and inexecutable actions equal to 440? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is located at shed, location3 is linked to location4, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is not secured, shed and location1 are linked, spanner1 is located at location4, spanner1 is usable, spanner2 can be used, spanner2 is located at location7, spanner3 is located at location6, spanner3 is usable, spanner4 is currently at location1, spanner4 is usable, spanner5 is at location1 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then collects spanner5 and spanner4 from location1. He proceeds to walk from location1 to location2, then to location3, and subsequently to location4. At location4, Bob picks up spanner1 and continues walking to location5, then to location6. From location6, he collects spanner3 and walks to location7, where he picks up spanner2. Bob then walks to location8 and then to location9, before finally reaching the gate. Upon arriving at the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, is the number of executable and inexecutable actions equal to 440? True or False", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a link is established between location5 and location6, and a connection exists between location6 and location7. Bob is currently at the shed. Additionally, location3 is connected to location4, location7 is linked to location8, location8 is connected to location9, and location9 is linked to the gate. At the gate, nut1 is present and is loose, nut2 is also located there and is loose, nut3 is at the gate but not secured, nut4 is at the gate and is loose, and nut5 is at the gate but not secured. The shed is connected to location1. Spanner1 is located at location4 and is in working condition, spanner2 is usable and is at location7, spanner3 is at location6 and is usable, spanner4 is currently at location1 and is usable, and spanner5 is also at location1 and is usable."}
{"question_id": "97afb7b5-0d34-425d-9ab5-4f66e153dbe0", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 35? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, nut1 is at gate, nut1 is not secured, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is not secured, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 can be used, spanner1 is currently at location3, spanner2 is functional, spanner2 is located at location5, spanner3 is at location2, spanner3 is usable, spanner4 can be used, spanner4 is at location6, spanner5 is at location3 and spanner5 is functional.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, are there 35 valid properties that do not involve negations? True or False", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a connection exists between location8 and location9, and a connection is found between location9 and the gate. Additionally, a connection exists between the shed and location1. Bob is currently situated at the shed. Location3 and location4 are interconnected, location5 is linked to location6, location6 is connected to location7, and location7 is linked to location8. At the gate, nut1 is present and unsecured, nut2 is also at the gate and unsecured, nut3 is currently at the gate and unsecured, nut4 is at the gate and loose, and nut5 is located at the gate and loose. Spanner1 is available for use and is currently situated at location3, spanner2 is functional and located at location5, spanner3 is at location2 and usable, spanner4 is available for use and is at location6, and spanner5 is at location3 and functional."}
{"question_id": "26ddee6e-a0e3-42ea-8d75-3cfeb27cbb69", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, from location1 to location2, bob walks, bob picks up spanner3 from location2, bob walks to location3 from location2, bob picks up spanner5 from location3, spanner1 is picked up by bob from location3, from location3 to location4, bob walks, from location4 to location5, bob walks, spanner2 is picked up by bob from location5 and from location5 to location6, bob walks to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 31? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location3 and location4 are linked, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, nut1 is at gate, nut1 is not secured, nut2 is at gate, nut2 is not secured, nut3 is currently at gate, nut3 is not secured, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 can be used, spanner1 is currently at location3, spanner2 is functional, spanner2 is located at location5, spanner3 is at location2, spanner3 is usable, spanner4 can be used, spanner4 is at location6, spanner5 is at location3 and spanner5 is functional.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then from location1 to location2, where he picks up spanner3. He then proceeds to location3 from location2, collects spanner5, and also picks up spanner1 from location3. Next, Bob walks from location3 to location4, and then from location4 to location5, where he picks up spanner2. Finally, he walks from location5 to location6, reaching the current state. In this state, is the number of valid properties that do not involve negations equal to 31? True or False", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a connection exists between location8 and location9, and a connection is found between location9 and the gate. Additionally, a connection exists between the shed and location1. Bob is currently situated at the shed. Location3 and location4 are interconnected, location5 is linked to location6, location6 is connected to location7, and location7 is linked to location8. At the gate, nut1 is present but not secured, nut2 is also at the gate and not secured, nut3 is currently at the gate and not secured, nut4 is at the gate and loose, and nut5 is located at the gate and loose. Spanner1 is available for use and is currently at location3, spanner2 is functional and located at location5, spanner3 is at location2 and usable, spanner4 is available for use and at location6, and spanner5 is at location3 and functional."}
{"question_id": "9baa9839-e9aa-4ff6-84ba-6dbea4c55e26", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 36? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location2 and location3 exists, a link between location3 and location4 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location4 is linked to location5, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, nut1 is currently at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, spanner1 is functional, spanner1 is located at location8, spanner2 is currently at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location2, spanner5 is currently at location6 and spanner5 is usable.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, are there 36 valid properties that do not involve negations? True or False", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, a connection between location3 and location4, a connection between location8 and location9, a connection between location9 and the gate, a connection between the shed and location1, Bob is currently at the shed, location1 is connected to location2, location4 is connected to location5, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, nut1 is currently situated at the gate and is loose, nut2 is also at the gate and is loose, nut3 is at the gate and is loose, nut4 is currently at the gate but is not secured, nut5 is located at the gate and is loose, spanner1 is in working order and is located at location8, spanner2 is currently at location6 and is functional, spanner3 is currently at location2 and is usable, spanner4 is functional and is located at location2, spanner5 is currently at location6 and is usable."}
{"question_id": "1874b15e-1603-4774-8bd1-4bd949a008f0", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 31? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location4 and location5 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is located at shed, location3 is linked to location4, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is not secured, shed and location1 are linked, spanner1 is located at location4, spanner1 is usable, spanner2 can be used, spanner2 is located at location7, spanner3 is located at location6, spanner3 is usable, spanner4 is currently at location1, spanner4 is usable, spanner5 is at location1 and spanner5 is usable.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, are there 31 valid properties that do not involve negations? True or False", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, a connection is present between location4 and location5, a link is established between location5 and location6, and a connection exists between location6 and location7. Bob is currently at the shed. Additionally, location3 is connected to location4, location7 is linked to location8, location8 is connected to location9, and location9 is linked to the gate. At the gate, nut1 is located and is loose, nut2 is also at the gate and is loose, nut3 is at the gate but not secured, nut4 is located at the gate and is loose, and nut5 is at the gate but not secured. Furthermore, the shed is connected to location1. Spanner1 is located at location4 and is in working condition, spanner2 is usable and can be found at location7, spanner3 is at location6 and is usable, spanner4 is currently at location1 and is usable, and spanner5 is also at location1 and is usable."}
{"question_id": "4d2cd142-1d71-4af8-bbd9-1b749399faa4", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 31? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location5 and location6 exists, bob is currently at shed, location1 is linked to location2, location2 and location3 are linked, location3 and location4 are linked, location4 is linked to location5, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 can be used, spanner1 is currently at location6, spanner2 is currently at location8, spanner2 is functional, spanner3 is currently at location8, spanner3 is usable, spanner4 can be used, spanner4 is currently at location5, spanner5 is located at location7 and spanner5 is usable.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, are there 31 valid properties that do not involve negations? True or False", "initial_state_nl_paraphrased": "There is a connection between location5 and location6. Bob is presently at the shed. A link exists between location1 and location2, and location2 is also connected to location3. Furthermore, location3 is linked to location4, which in turn is connected to location5. On the other hand, location6 is linked to location7, location7 to location8, location8 to location9, and location9 to the gate. At the gate, the following items are located: nut1, which is loose, nut2, also loose, nut3, which is not secured, nut4, also not secured, and nut5, which is not secured as well. The shed is connected to location1. The following spanners are available for use: spanner1, which is currently at location6, spanner2, which is functional and located at location8, spanner3, which is usable and also at location8, spanner4, which is at location5, and spanner5, which is located at location7 and is usable."}
