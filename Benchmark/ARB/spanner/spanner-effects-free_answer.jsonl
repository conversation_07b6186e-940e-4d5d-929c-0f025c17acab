{"question_id": "a1fd0d5b-7a97-4c27-94ef-a45a3efc868c", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, from location1 to location2, bob walks, bob walks from location2 to location3, bob walks from location3 to location4, bob walks from location4 to location5, bob picks up spanner4 from location5, from location5 to location6, bob walks, spanner1 is picked up by bob from location6, bob walks from location6 to location7 and from location7, bob picks up spanner5 to reach the current state. In this state, if from location7 to location8, bob walks, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "a link between gate and location1 does not exist, a link between gate and location3 does not exist, a link between gate and location4 does not exist, a link between gate and location6 does not exist, a link between location1 and gate does not exist, a link between location1 and location8 does not exist, a link between location1 and shed does not exist, a link between location2 and location3 exists, a link between location2 and location4 does not exist, a link between location2 and location5 does not exist, a link between location2 and shed does not exist, a link between location3 and location7 does not exist, a link between location3 and location8 does not exist, a link between location4 and location5 exists, a link between location4 and location7 does not exist, a link between location4 and location8 does not exist, a link between location4 and location9 does not exist, a link between location5 and location1 does not exist, a link between location5 and location2 does not exist, a link between location5 and location3 does not exist, a link between location5 and location4 does not exist, a link between location5 and location6 exists, a link between location6 and gate does not exist, a link between location6 and location1 does not exist, a link between location6 and location3 does not exist, a link between location7 and gate does not exist, a link between location7 and location1 does not exist, a link between location7 and location3 does not exist, a link between location7 and location4 does not exist, a link between location8 and location5 does not exist, a link between location8 and location9 exists, a link between location9 and location3 does not exist, a link between location9 and location4 does not exist, a link between shed and location1 exists, a link between shed and location3 does not exist, a link between shed and location8 does not exist, bob is at location8, bob is carrying spanner1, bob is not at gate, bob is not at location3, bob is not carrying spanner2, bob is not currently at location2, bob is not currently at location5, bob is not currently at location7, bob is not located at location1, bob is not located at location4, bob is not located at location6, bob is not located at location9, bob is not located at shed, gate and location2 are not linked, gate and location5 are not linked, gate and location8 are not linked, gate and location9 are not linked, gate is not linked to location7, gate is not linked to shed, location1 and location3 are not linked, location1 and location9 are not linked, location1 is linked to location2, location1 is not linked to location4, location1 is not linked to location5, location1 is not linked to location6, location1 is not linked to location7, location2 and location6 are not linked, location2 and location9 are not linked, location2 is not linked to gate, location2 is not linked to location1, location2 is not linked to location7, location2 is not linked to location8, location3 and gate are not linked, location3 and location1 are not linked, location3 and location5 are not linked, location3 and location6 are not linked, location3 and location9 are not linked, location3 is linked to location4, location3 is not linked to location2, location3 is not linked to shed, location4 and location2 are not linked, location4 and location3 are not linked, location4 and location6 are not linked, location4 is not linked to gate, location4 is not linked to location1, location4 is not linked to shed, location5 and location8 are not linked, location5 and location9 are not linked, location5 and shed are not linked, location5 is not linked to gate, location5 is not linked to location7, location6 and location4 are not linked, location6 and location5 are not linked, location6 and location8 are not linked, location6 and shed are not linked, location6 is linked to location7, location6 is not linked to location2, location6 is not linked to location9, location7 and location5 are not linked, location7 and location6 are not linked, location7 and location8 are linked, location7 is not linked to location2, location7 is not linked to location9, location7 is not linked to shed, location8 and gate are not linked, location8 and location1 are not linked, location8 and location2 are not linked, location8 and location3 are not linked, location8 and location4 are not linked, location8 and location6 are not linked, location8 is not linked to location7, location8 is not linked to shed, location9 and location1 are not linked, location9 and location2 are not linked, location9 and location7 are not linked, location9 is linked to gate, location9 is not linked to location5, location9 is not linked to location6, location9 is not linked to location8, location9 is not linked to shed, nut1 is currently at gate, nut1 is loose, nut1 is not at location4, nut1 is not at location8, nut1 is not at location9, nut1 is not at shed, nut1 is not currently at location7, nut1 is not located at location1, nut1 is not located at location2, nut1 is not located at location3, nut1 is not located at location5, nut1 is not located at location6, nut2 is at gate, nut2 is loose, nut2 is not at location6, nut2 is not at location8, nut2 is not at shed, nut2 is not currently at location2, nut2 is not currently at location5, nut2 is not located at location1, nut2 is not located at location3, nut2 is not located at location4, nut2 is not located at location7, nut2 is not located at location9, nut2 is not tightened, nut3 is located at gate, nut3 is not at location1, nut3 is not currently at location3, nut3 is not currently at location5, nut3 is not currently at location6, nut3 is not currently at location8, nut3 is not currently at location9, nut3 is not currently at shed, nut3 is not located at location2, nut3 is not located at location4, nut3 is not located at location7, nut3 is not secured, nut4 is at gate, nut4 is not at location2, nut4 is not at location5, nut4 is not at location6, nut4 is not currently at location4, nut4 is not currently at location8, nut4 is not located at location1, nut4 is not located at location3, nut4 is not located at location7, nut4 is not located at location9, nut4 is not located at shed, nut4 is not secured, nut4 is not tightened, nut5 is at gate, nut5 is not at location2, nut5 is not at location3, nut5 is not at location5, nut5 is not at location6, nut5 is not at location8, nut5 is not currently at location4, nut5 is not currently at location7, nut5 is not currently at location9, nut5 is not currently at shed, nut5 is not located at location1, nut5 is not secured, shed and location2 are not linked, shed and location5 are not linked, shed and location7 are not linked, shed and location9 are not linked, shed is not linked to gate, shed is not linked to location4, shed is not linked to location6, spanner1 is not at location1, spanner1 is not currently at location2, spanner1 is not currently at location3, spanner1 is not currently at location5, spanner1 is not currently at location9, spanner1 is not located at gate, spanner1 is not located at location4, spanner1 is not located at location6, spanner1 is not located at location7, spanner1 is not located at location8, spanner1 is not located at shed, spanner1 is usable, spanner2 is at location8, spanner2 is functional, spanner2 is not at location6, spanner2 is not at location7, spanner2 is not at location9, spanner2 is not located at gate, spanner2 is not located at location1, spanner2 is not located at location2, spanner2 is not located at location3, spanner2 is not located at location4, spanner2 is not located at location5, spanner2 is not located at shed, spanner3 is functional, spanner3 is located at location8, spanner3 is not at gate, spanner3 is not at location1, spanner3 is not at location3, spanner3 is not at location5, spanner3 is not at location6, spanner3 is not at location7, spanner3 is not at location9, spanner3 is not carried by bob, spanner3 is not currently at location2, spanner3 is not currently at shed, spanner3 is not located at location4, spanner4 is carried by bob, spanner4 is not at location2, spanner4 is not at location4, spanner4 is not at location5, spanner4 is not at location6, spanner4 is not at location7, spanner4 is not at location8, spanner4 is not currently at location1, spanner4 is not currently at location3, spanner4 is not currently at location9, spanner4 is not located at gate, spanner4 is not located at shed, spanner4 is usable, spanner5 is carried by bob, spanner5 is not at gate, spanner5 is not at location2, spanner5 is not currently at location1, spanner5 is not currently at location3, spanner5 is not currently at location5, spanner5 is not currently at location6, spanner5 is not currently at location8, spanner5 is not currently at location9, spanner5 is not located at location4, spanner5 is not located at location7, spanner5 is not located at shed, spanner5 is usable, tightening of nut1 is incomplete, tightening of nut3 is incomplete and tightening of nut5 is incomplete", "plan_length": 10, "initial_state_nl": "A link between location3 and location4 exists, a link between location7 and location8 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location4 and location5 are linked, location5 is linked to location6, location6 and location7 are linked, location8 and location9 are linked, location9 is linked to gate, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is functional, spanner1 is located at location6, spanner2 is functional, spanner2 is located at location8, spanner3 is at location8, spanner3 is usable, spanner4 is located at location5, spanner4 is usable, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then to location2, then to location3, then to location4, and then to location5. At location5, Bob picks up spanner4. He then walks to location6, where he picks up spanner1, and proceeds to location7. At location7, Bob picks up spanner5, resulting in the current state. If Bob walks from location7 to location8 in this state, what are all the valid properties of the resulting state, including both affirmative and negated properties? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, and another connection exists between location7 and location8. Bob is currently situated at the shed. Location1 is connected to location2, which in turn is connected to location3. Location4 is linked to location5, and location5 is connected to location6. Location6 and location7 are also connected, and location8 is linked to location9. Location9 is connected to the gate. At the gate, there is nut1, which is not secured. Additionally, nut2, nut3, nut4, and nut5 are all located at the gate, and all of them are loose. The shed is connected to location1. Spanner1, which is functional, is situated at location6. Spanner2, also functional, is located at location8. Spanner3, which is usable, is at location8, while spanner4, also usable, is at location5. Spanner5 is functional and is currently located at location7."}
{"question_id": "3a09756f-bc39-4b14-9acb-7eb994ff1ce1", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks from location1 to location2, spanner4 is picked up by bob from location2, spanner3 is picked up by bob from location2, bob walks from location2 to location3, bob walks from location3 to location4, from location4 to location5, bob walks, bob walks from location5 to location6, from location6, bob picks up spanner5, spanner2 is picked up by bob from location6, bob walks from location6 to location7, bob walks to location8 from location7, spanner1 is picked up by bob from location8, from location8 to location9, bob walks, from location9 to gate, bob walks, at gate, bob uses spanner5 to tighten nut1, at gate, bob uses spanner4 to tighten nut2, bob tightens nut3 with spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, if bob tightens nut5 with spanner1 at gate, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "a link between gate and location1 does not exist, a link between gate and location5 does not exist, a link between gate and location6 does not exist, a link between gate and location8 does not exist, a link between location1 and gate does not exist, a link between location1 and location2 exists, a link between location1 and location4 does not exist, a link between location1 and location9 does not exist, a link between location2 and gate does not exist, a link between location2 and location1 does not exist, a link between location2 and location4 does not exist, a link between location2 and location8 does not exist, a link between location2 and location9 does not exist, a link between location2 and shed does not exist, a link between location3 and gate does not exist, a link between location3 and location1 does not exist, a link between location3 and location8 does not exist, a link between location4 and gate does not exist, a link between location4 and location1 does not exist, a link between location4 and location8 does not exist, a link between location4 and location9 does not exist, a link between location4 and shed does not exist, a link between location5 and gate does not exist, a link between location5 and location1 does not exist, a link between location5 and location2 does not exist, a link between location5 and location9 does not exist, a link between location6 and location1 does not exist, a link between location6 and location3 does not exist, a link between location6 and location4 does not exist, a link between location6 and location7 exists, a link between location7 and location1 does not exist, a link between location7 and location2 does not exist, a link between location7 and location8 exists, a link between location8 and location1 does not exist, a link between location8 and location2 does not exist, a link between location8 and location7 does not exist, a link between location9 and gate exists, a link between location9 and location1 does not exist, a link between shed and gate does not exist, a link between shed and location2 does not exist, bob is at gate, bob is carrying spanner1, bob is carrying spanner3, bob is carrying spanner4, bob is carrying spanner5, bob is not at location1, bob is not at location3, bob is not at location4, bob is not at location8, bob is not currently at location2, bob is not currently at location6, bob is not currently at location7, bob is not currently at location9, bob is not located at location5, bob is not located at shed, gate and location2 are not linked, gate and location3 are not linked, gate and location4 are not linked, gate and location7 are not linked, gate and location9 are not linked, gate and shed are not linked, location1 and location3 are not linked, location1 and location6 are not linked, location1 and location7 are not linked, location1 and location8 are not linked, location1 is not linked to location5, location1 is not linked to shed, location2 and location5 are not linked, location2 and location7 are not linked, location2 is linked to location3, location2 is not linked to location6, location3 and location2 are not linked, location3 and location4 are linked, location3 and location6 are not linked, location3 and shed are not linked, location3 is not linked to location5, location3 is not linked to location7, location3 is not linked to location9, location4 and location3 are not linked, location4 and location7 are not linked, location4 is linked to location5, location4 is not linked to location2, location4 is not linked to location6, location5 and location6 are linked, location5 and location7 are not linked, location5 is not linked to location3, location5 is not linked to location4, location5 is not linked to location8, location5 is not linked to shed, location6 and gate are not linked, location6 and location2 are not linked, location6 is not linked to location5, location6 is not linked to location8, location6 is not linked to location9, location6 is not linked to shed, location7 and gate are not linked, location7 and location6 are not linked, location7 and shed are not linked, location7 is not linked to location3, location7 is not linked to location4, location7 is not linked to location5, location7 is not linked to location9, location8 and gate are not linked, location8 and location4 are not linked, location8 and location6 are not linked, location8 and location9 are linked, location8 is not linked to location3, location8 is not linked to location5, location8 is not linked to shed, location9 and location2 are not linked, location9 and location3 are not linked, location9 and location4 are not linked, location9 and shed are not linked, location9 is not linked to location5, location9 is not linked to location6, location9 is not linked to location7, location9 is not linked to location8, nut1 is at gate, nut1 is not at location2, nut1 is not at location3, nut1 is not at shed, nut1 is not currently at location1, nut1 is not currently at location6, nut1 is not located at location4, nut1 is not located at location5, nut1 is not located at location7, nut1 is not located at location8, nut1 is not located at location9, nut1 is secured, nut2 is at gate, nut2 is not at location3, nut2 is not at location6, nut2 is not at location7, nut2 is not at location9, nut2 is not currently at location1, nut2 is not currently at location2, nut2 is not currently at location5, nut2 is not currently at shed, nut2 is not located at location4, nut2 is not located at location8, nut2 is not loose, nut2 is tightened, nut3 is located at gate, nut3 is not at location4, nut3 is not at location5, nut3 is not at location7, nut3 is not at location8, nut3 is not currently at location1, nut3 is not currently at location2, nut3 is not currently at location3, nut3 is not currently at location6, nut3 is not currently at location9, nut3 is not located at shed, nut3 is not loose, nut4 is located at gate, nut4 is not at location5, nut4 is not at location6, nut4 is not at shed, nut4 is not currently at location7, nut4 is not currently at location9, nut4 is not located at location1, nut4 is not located at location2, nut4 is not located at location3, nut4 is not located at location4, nut4 is not located at location8, nut4 is secured, nut4 is tightened, nut5 is currently at gate, nut5 is not at location2, nut5 is not currently at location1, nut5 is not currently at location6, nut5 is not located at location3, nut5 is not located at location4, nut5 is not located at location5, nut5 is not located at location7, nut5 is not located at location8, nut5 is not located at location9, nut5 is not located at shed, nut5 is secured, shed and location1 are linked, shed and location3 are not linked, shed and location4 are not linked, shed and location5 are not linked, shed and location7 are not linked, shed and location8 are not linked, shed is not linked to location6, shed is not linked to location9, spanner1 is not at location2, spanner1 is not at location4, spanner1 is not at location6, spanner1 is not at location7, spanner1 is not at location8, spanner1 is not currently at gate, spanner1 is not currently at location1, spanner1 is not currently at location9, spanner1 is not functional, spanner1 is not located at location3, spanner1 is not located at location5, spanner1 is not located at shed, spanner2 is carried by bob, spanner2 is not at location1, spanner2 is not at location5, spanner2 is not at location9, spanner2 is not at shed, spanner2 is not currently at location2, spanner2 is not currently at location4, spanner2 is not currently at location8, spanner2 is not located at gate, spanner2 is not located at location3, spanner2 is not located at location6, spanner2 is not located at location7, spanner2 is not usable, spanner3 is not at shed, spanner3 is not currently at gate, spanner3 is not currently at location2, spanner3 is not currently at location4, spanner3 is not currently at location6, spanner3 is not currently at location8, spanner3 is not currently at location9, spanner3 is not functional, spanner3 is not located at location1, spanner3 is not located at location3, spanner3 is not located at location5, spanner3 is not located at location7, spanner4 can't be used, spanner4 is not at location3, spanner4 is not at location4, spanner4 is not at location5, spanner4 is not at location6, spanner4 is not at location7, spanner4 is not at location9, spanner4 is not currently at location1, spanner4 is not currently at location8, spanner4 is not located at gate, spanner4 is not located at location2, spanner4 is not located at shed, spanner5 can't be used, spanner5 is not at location3, spanner5 is not at location7, spanner5 is not at shed, spanner5 is not currently at gate, spanner5 is not currently at location1, spanner5 is not currently at location2, spanner5 is not currently at location9, spanner5 is not located at location4, spanner5 is not located at location5, spanner5 is not located at location6, spanner5 is not located at location8, tightening of nut1 is complete, tightening of nut3 is complete and tightening of nut5 is complete", "plan_length": 19, "initial_state_nl": "A link between shed and location1 exists, bob is currently at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 is linked to location5, location5 and location6 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 can be used, spanner1 is at location8, spanner2 can be used, spanner2 is currently at location6, spanner3 is located at location2, spanner3 is usable, spanner4 is at location2, spanner4 is usable, spanner5 is currently at location6 and spanner5 is functional.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob walks from the shed to location1, then from location1 to location2, where he picks up spanner4 and spanner3. He then proceeds to location3 and then location4, followed by location5 and location6, where he collects spanner5 and spanner2. Next, Bob walks to location7 and then location8, where he picks up spanner1. He then moves to location9 and finally reaches the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. If Bob tightens nut5 with spanner1 at the gate in this state, what are all the valid properties of the resulting state (including both affirmative and negative properties)? If there are none, state 'None'.", "initial_state_nl_paraphrased": "There is a connection between the shed and location1. Bob is currently present at the shed, location1, and location2 are connected, location2 is connected to location3, location3 is linked with location4, location4 is connected to location5, location5 and location6 are connected, location6 is linked to location7, location7 is connected to location8, location8 and location9 are connected, location9 is linked to the gate. At the gate, nut1 is situated and is loose, nut2 is also at the gate and is loose, nut3 is currently at the gate but not secured, nut4 is located at the gate and is not secured, and nut5 is at the gate and is loose. Spanner1 is available for use and is located at location8, spanner2 is available for use and is currently at location6, spanner3 is situated at location2 and is usable, spanner4 is at location2 and is usable, and spanner5 is currently at location6 and is functional."}
{"question_id": "fbe54764-7e81-4b63-8471-21d5a94ef519", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, from location2 to location3, bob walks, bob walks to location4 from location3, bob walks from location4 to location5, from location5, bob picks up spanner4, bob walks to location6 from location5, from location6, bob picks up spanner1, bob walks from location6 to location7 and bob picks up spanner5 from location7 to reach the current state. In this state, if from location7 to location8, bob walks, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "a link between location1 and location2 exists, a link between location3 and location4 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is carrying spanner4, bob is located at location8, location2 is linked to location3, location4 is linked to location5, location5 is linked to location6, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is not secured, nut3 is at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is located at gate, nut5 is not secured, spanner1 is carried by bob, spanner1 is functional, spanner2 can be used, spanner2 is at location8, spanner3 is located at location8, spanner3 is usable, spanner4 can be used, spanner5 is carried by bob and spanner5 is usable", "plan_length": 10, "initial_state_nl": "A link between location3 and location4 exists, a link between location7 and location8 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location4 and location5 are linked, location5 is linked to location6, location6 and location7 are linked, location8 and location9 are linked, location9 is linked to gate, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is functional, spanner1 is located at location6, spanner2 is functional, spanner2 is located at location8, spanner3 is at location8, spanner3 is usable, spanner4 is located at location5, spanner4 is usable, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then to location2, followed by location3, then location4, and location5. From location5, Bob collects spanner4, proceeds to location6, and picks up spanner1. He then walks to location7 and collects spanner5, resulting in the current state. In this state, if Bob walks from location7 to location8, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, and another connection exists between location7 and location8. Bob is currently situated at the shed. Location1 is connected to location2, which in turn is connected to location3. Location4 is linked to location5, and location5 is connected to location6. Location6 and location7 are also connected, and location8 is linked to location9. Location9 is connected to the gate. At the gate, there is nut1, which is not secured. Additionally, nut2, nut3, nut4, and nut5 are all located at the gate, and all of them are loose. The shed is connected to location1. Spanner1 is in working condition and is situated at location6. Spanner2 is also functional and is located at location8. Spanner3 is at location8 and is usable. Spanner4 is located at location5 and is usable. Spanner5 is in a usable state and is currently at location7."}
{"question_id": "2ecdd27f-c88c-48f9-a2f6-8f761725d481", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, if bob walks from location1 to location2, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "a link between gate and location3 does not exist, a link between gate and location7 does not exist, a link between location1 and location2 exists, a link between location1 and location4 does not exist, a link between location1 and location5 does not exist, a link between location1 and location6 does not exist, a link between location1 and location9 does not exist, a link between location2 and gate does not exist, a link between location2 and location6 does not exist, a link between location2 and location7 does not exist, a link between location2 and location8 does not exist, a link between location2 and location9 does not exist, a link between location2 and shed does not exist, a link between location3 and gate does not exist, a link between location3 and location6 does not exist, a link between location3 and location8 does not exist, a link between location4 and location3 does not exist, a link between location4 and location5 exists, a link between location4 and location7 does not exist, a link between location4 and location8 does not exist, a link between location4 and location9 does not exist, a link between location5 and location7 does not exist, a link between location6 and location4 does not exist, a link between location6 and location5 does not exist, a link between location7 and location4 does not exist, a link between location8 and location7 does not exist, a link between location8 and shed does not exist, a link between location9 and location1 does not exist, a link between location9 and shed does not exist, a link between shed and gate does not exist, a link between shed and location2 does not exist, a link between shed and location4 does not exist, a link between shed and location7 does not exist, a link between shed and location8 does not exist, bob is currently at location2, bob is not at location5, bob is not at location7, bob is not at location8, bob is not at location9, bob is not carrying spanner1, bob is not carrying spanner3, bob is not currently at gate, bob is not currently at location3, bob is not currently at location4, bob is not currently at location6, bob is not currently at shed, bob is not located at location1, gate and location5 are not linked, gate and location8 are not linked, gate and location9 are not linked, gate is not linked to location1, gate is not linked to location2, gate is not linked to location4, gate is not linked to location6, gate is not linked to shed, location1 and location3 are not linked, location1 and location8 are not linked, location1 and shed are not linked, location1 is not linked to gate, location1 is not linked to location7, location2 and location5 are not linked, location2 is linked to location3, location2 is not linked to location1, location2 is not linked to location4, location3 and location2 are not linked, location3 and location4 are linked, location3 and location9 are not linked, location3 and shed are not linked, location3 is not linked to location1, location3 is not linked to location5, location3 is not linked to location7, location4 and gate are not linked, location4 and location2 are not linked, location4 and shed are not linked, location4 is not linked to location1, location4 is not linked to location6, location5 and gate are not linked, location5 and location1 are not linked, location5 and location2 are not linked, location5 and location3 are not linked, location5 and location9 are not linked, location5 and shed are not linked, location5 is linked to location6, location5 is not linked to location4, location5 is not linked to location8, location6 and gate are not linked, location6 and location2 are not linked, location6 and location3 are not linked, location6 and location8 are not linked, location6 and location9 are not linked, location6 is linked to location7, location6 is not linked to location1, location6 is not linked to shed, location7 and gate are not linked, location7 and location1 are not linked, location7 and location8 are linked, location7 and location9 are not linked, location7 is not linked to location2, location7 is not linked to location3, location7 is not linked to location5, location7 is not linked to location6, location7 is not linked to shed, location8 and gate are not linked, location8 and location4 are not linked, location8 is linked to location9, location8 is not linked to location1, location8 is not linked to location2, location8 is not linked to location3, location8 is not linked to location5, location8 is not linked to location6, location9 and gate are linked, location9 and location2 are not linked, location9 and location4 are not linked, location9 and location7 are not linked, location9 and location8 are not linked, location9 is not linked to location3, location9 is not linked to location5, location9 is not linked to location6, nut1 is currently at gate, nut1 is loose, nut1 is not at location5, nut1 is not at shed, nut1 is not currently at location4, nut1 is not currently at location7, nut1 is not located at location1, nut1 is not located at location2, nut1 is not located at location3, nut1 is not located at location6, nut1 is not located at location8, nut1 is not located at location9, nut2 is located at gate, nut2 is not at location8, nut2 is not at shed, nut2 is not currently at location2, nut2 is not currently at location6, nut2 is not currently at location7, nut2 is not located at location1, nut2 is not located at location3, nut2 is not located at location4, nut2 is not located at location5, nut2 is not located at location9, nut2 is not secured, nut2 is not tightened, nut3 is located at gate, nut3 is loose, nut3 is not at location1, nut3 is not at location3, nut3 is not at location6, nut3 is not at location9, nut3 is not currently at location2, nut3 is not currently at shed, nut3 is not located at location4, nut3 is not located at location5, nut3 is not located at location7, nut3 is not located at location8, nut3 is not tightened, nut4 is located at gate, nut4 is not at location6, nut4 is not currently at location1, nut4 is not currently at location2, nut4 is not currently at location3, nut4 is not currently at location4, nut4 is not currently at location8, nut4 is not located at location5, nut4 is not located at location7, nut4 is not located at location9, nut4 is not located at shed, nut4 is not secured, nut4 is not tightened, nut5 is located at gate, nut5 is not at location4, nut5 is not at location9, nut5 is not currently at location1, nut5 is not currently at location2, nut5 is not currently at location5, nut5 is not currently at location7, nut5 is not currently at shed, nut5 is not located at location3, nut5 is not located at location6, nut5 is not located at location8, nut5 is not secured, shed and location5 are not linked, shed and location6 are not linked, shed and location9 are not linked, shed is linked to location1, shed is not linked to location3, spanner1 can be used, spanner1 is located at location6, spanner1 is not at location4, spanner1 is not at location7, spanner1 is not at location9, spanner1 is not currently at gate, spanner1 is not currently at location8, spanner1 is not currently at shed, spanner1 is not located at location1, spanner1 is not located at location2, spanner1 is not located at location3, spanner1 is not located at location5, spanner2 is functional, spanner2 is located at location8, spanner2 is not at location1, spanner2 is not at location3, spanner2 is not at location5, spanner2 is not carried by bob, spanner2 is not currently at gate, spanner2 is not currently at location4, spanner2 is not currently at location6, spanner2 is not currently at location7, spanner2 is not currently at shed, spanner2 is not located at location2, spanner2 is not located at location9, spanner3 is located at location8, spanner3 is not currently at location1, spanner3 is not currently at location4, spanner3 is not currently at location6, spanner3 is not currently at location9, spanner3 is not located at gate, spanner3 is not located at location2, spanner3 is not located at location3, spanner3 is not located at location5, spanner3 is not located at location7, spanner3 is not located at shed, spanner3 is usable, spanner4 can be used, spanner4 is located at location5, spanner4 is not at location1, spanner4 is not at location4, spanner4 is not at shed, spanner4 is not carried by bob, spanner4 is not currently at location2, spanner4 is not currently at location3, spanner4 is not currently at location8, spanner4 is not currently at location9, spanner4 is not located at gate, spanner4 is not located at location6, spanner4 is not located at location7, spanner5 is functional, spanner5 is located at location7, spanner5 is not at location8, spanner5 is not carried by bob, spanner5 is not currently at gate, spanner5 is not currently at location5, spanner5 is not currently at location6, spanner5 is not currently at location9, spanner5 is not currently at shed, spanner5 is not located at location1, spanner5 is not located at location2, spanner5 is not located at location3, spanner5 is not located at location4, tightening of nut1 is incomplete and tightening of nut5 is incomplete", "plan_length": 1, "initial_state_nl": "A link between location3 and location4 exists, a link between location7 and location8 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location4 and location5 are linked, location5 is linked to location6, location6 and location7 are linked, location8 and location9 are linked, location9 is linked to gate, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is functional, spanner1 is located at location6, spanner2 is functional, spanner2 is located at location8, spanner3 is at location8, spanner3 is usable, spanner4 is located at location5, spanner4 is usable, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: bob moves from the shed to location1 to attain the current state. In this state, if bob proceeds from location1 to location2, what are all the possible valid properties of the state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, and another connection exists between location7 and location8. Bob is currently situated at the shed. Location1 is connected to location2, which in turn is connected to location3. Location4 is linked to location5, and location5 is connected to location6. Location6 and location7 are also connected, and location8 is linked to location9. Location9 is connected to the gate. At the gate, there is nut1, which is not secured. Additionally, nut2, nut3, nut4, and nut5 are all located at the gate, and all of them are loose. The shed is connected to location1. Spanner1 is in working condition and is situated at location6. Spanner2 is also functional and is located at location8. Spanner3 is at location8 and is usable. Spanner4 is located at location5 and is usable. Spanner5 is in a usable state and is currently at location7."}
{"question_id": "0ed6a9a7-2bc9-4c9f-9717-f29876206610", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks to location2 from location1, bob picks up spanner3 from location2, bob walks from location2 to location3, spanner5 is picked up by bob from location3, spanner1 is picked up by bob from location3, from location3 to location4, bob walks, bob walks to location5 from location4, spanner2 is picked up by bob from location5, bob walks to location6 from location5, spanner4 is picked up by bob from location6, bob walks from location6 to location7, bob walks to location8 from location7, bob walks to location9 from location8, bob walks from location9 to gate, nut1 is tightened by bob using spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, bob tightens nut3 with spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, if bob tightens nut5 with spanner1 at gate, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "a link between gate and location1 does not exist, a link between gate and location6 does not exist, a link between location1 and location3 does not exist, a link between location1 and location4 does not exist, a link between location1 and location8 does not exist, a link between location2 and location9 does not exist, a link between location2 and shed does not exist, a link between location3 and gate does not exist, a link between location3 and location4 exists, a link between location3 and location9 does not exist, a link between location4 and gate does not exist, a link between location4 and location8 does not exist, a link between location5 and location6 exists, a link between location5 and location7 does not exist, a link between location5 and location8 does not exist, a link between location5 and shed does not exist, a link between location6 and gate does not exist, a link between location6 and location8 does not exist, a link between location6 and location9 does not exist, a link between location6 and shed does not exist, a link between location7 and location1 does not exist, a link between location7 and location2 does not exist, a link between location7 and location4 does not exist, a link between location9 and location1 does not exist, a link between location9 and location7 does not exist, a link between location9 and location8 does not exist, a link between shed and gate does not exist, a link between shed and location4 does not exist, a link between shed and location6 does not exist, a link between shed and location8 does not exist, bob is carrying spanner1, bob is carrying spanner3, bob is carrying spanner4, bob is carrying spanner5, bob is located at gate, bob is not at location3, bob is not at location6, bob is not currently at location1, bob is not currently at location2, bob is not currently at location4, bob is not currently at location7, bob is not currently at shed, bob is not located at location5, bob is not located at location8, bob is not located at location9, gate and location2 are not linked, gate and location3 are not linked, gate and location8 are not linked, gate and location9 are not linked, gate and shed are not linked, gate is not linked to location4, gate is not linked to location5, gate is not linked to location7, location1 and gate are not linked, location1 and location5 are not linked, location1 and location7 are not linked, location1 and location9 are not linked, location1 and shed are not linked, location1 is linked to location2, location1 is not linked to location6, location2 and location1 are not linked, location2 and location6 are not linked, location2 and location7 are not linked, location2 and location8 are not linked, location2 is linked to location3, location2 is not linked to gate, location2 is not linked to location4, location2 is not linked to location5, location3 and location1 are not linked, location3 and location5 are not linked, location3 and location6 are not linked, location3 and location7 are not linked, location3 and shed are not linked, location3 is not linked to location2, location3 is not linked to location8, location4 and location2 are not linked, location4 and location3 are not linked, location4 and location7 are not linked, location4 is linked to location5, location4 is not linked to location1, location4 is not linked to location6, location4 is not linked to location9, location4 is not linked to shed, location5 and location2 are not linked, location5 and location3 are not linked, location5 and location4 are not linked, location5 is not linked to gate, location5 is not linked to location1, location5 is not linked to location9, location6 and location3 are not linked, location6 and location7 are linked, location6 is not linked to location1, location6 is not linked to location2, location6 is not linked to location4, location6 is not linked to location5, location7 and location9 are not linked, location7 and shed are not linked, location7 is linked to location8, location7 is not linked to gate, location7 is not linked to location3, location7 is not linked to location5, location7 is not linked to location6, location8 and gate are not linked, location8 and location5 are not linked, location8 and location7 are not linked, location8 and shed are not linked, location8 is linked to location9, location8 is not linked to location1, location8 is not linked to location2, location8 is not linked to location3, location8 is not linked to location4, location8 is not linked to location6, location9 and location3 are not linked, location9 and location6 are not linked, location9 is linked to gate, location9 is not linked to location2, location9 is not linked to location4, location9 is not linked to location5, location9 is not linked to shed, nut1 is located at gate, nut1 is not at location2, nut1 is not at location8, nut1 is not at shed, nut1 is not currently at location6, nut1 is not currently at location7, nut1 is not located at location1, nut1 is not located at location3, nut1 is not located at location4, nut1 is not located at location5, nut1 is not located at location9, nut1 is secured, nut2 is located at gate, nut2 is not at location2, nut2 is not at location4, nut2 is not at location8, nut2 is not currently at location1, nut2 is not currently at location3, nut2 is not currently at location5, nut2 is not currently at location7, nut2 is not currently at location9, nut2 is not located at location6, nut2 is not located at shed, nut2 is secured, nut2 is tightened, nut3 is currently at gate, nut3 is not at location3, nut3 is not at location4, nut3 is not at location5, nut3 is not at location6, nut3 is not at location8, nut3 is not at shed, nut3 is not currently at location2, nut3 is not currently at location7, nut3 is not currently at location9, nut3 is not located at location1, nut3 is secured, nut4 is at gate, nut4 is not at location3, nut4 is not at location5, nut4 is not at shed, nut4 is not currently at location1, nut4 is not currently at location4, nut4 is not currently at location6, nut4 is not currently at location9, nut4 is not located at location2, nut4 is not located at location7, nut4 is not located at location8, nut4 is secured, nut4 is tightened, nut5 is located at gate, nut5 is not at location2, nut5 is not at location6, nut5 is not at shed, nut5 is not currently at location3, nut5 is not currently at location7, nut5 is not currently at location8, nut5 is not currently at location9, nut5 is not located at location1, nut5 is not located at location4, nut5 is not located at location5, nut5 is secured, shed and location2 are not linked, shed and location3 are not linked, shed and location5 are not linked, shed is linked to location1, shed is not linked to location7, shed is not linked to location9, spanner1 is not at location3, spanner1 is not at location8, spanner1 is not currently at gate, spanner1 is not currently at location5, spanner1 is not currently at location6, spanner1 is not functional, spanner1 is not located at location1, spanner1 is not located at location2, spanner1 is not located at location4, spanner1 is not located at location7, spanner1 is not located at location9, spanner1 is not located at shed, spanner2 is carried by bob, spanner2 is not at gate, spanner2 is not at location3, spanner2 is not at location4, spanner2 is not at location5, spanner2 is not at location6, spanner2 is not at shed, spanner2 is not currently at location1, spanner2 is not currently at location2, spanner2 is not currently at location7, spanner2 is not currently at location8, spanner2 is not located at location9, spanner2 is not usable, spanner3 can't be used, spanner3 is not at location1, spanner3 is not at location3, spanner3 is not at location5, spanner3 is not at location6, spanner3 is not at location7, spanner3 is not at location9, spanner3 is not currently at gate, spanner3 is not currently at shed, spanner3 is not located at location2, spanner3 is not located at location4, spanner3 is not located at location8, spanner4 can't be used, spanner4 is not at location1, spanner4 is not at location2, spanner4 is not at location6, spanner4 is not currently at location3, spanner4 is not currently at location4, spanner4 is not currently at location5, spanner4 is not currently at location9, spanner4 is not located at gate, spanner4 is not located at location7, spanner4 is not located at location8, spanner4 is not located at shed, spanner5 is not at location3, spanner5 is not at location7, spanner5 is not currently at location1, spanner5 is not currently at location4, spanner5 is not currently at location5, spanner5 is not currently at location9, spanner5 is not functional, spanner5 is not located at gate, spanner5 is not located at location2, spanner5 is not located at location6, spanner5 is not located at location8, spanner5 is not located at shed, tightening of nut1 is complete, tightening of nut3 is complete and tightening of nut5 is complete", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is currently at shed, location2 is linked to location3, location3 is linked to location4, location4 is linked to location5, location6 is linked to location7, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, spanner1 is currently at location3, spanner1 is usable, spanner2 can be used, spanner2 is located at location5, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob starts at the shed and walks to location1, then proceeds to location2, where he picks up spanner3. From location2, he walks to location3, and there he collects spanner5 and spanner1. Next, Bob walks to location4 and then to location5, where he picks up spanner2. He then walks to location6, collects spanner4, and proceeds to location7, followed by location8, location9, and finally the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. If, in this state, Bob tightens nut5 with spanner1 at the gate, what are all the valid properties of the resulting state, including both affirmative and negative properties? If there are none, please state None.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6, a connection between location9 and the gate, and a connection between the shed and location1. Bob is currently situated at the shed. Location2 is adjacent to location3, location3 is adjacent to location4, location4 is adjacent to location5, location6 is adjacent to location7, location7 is connected to location8, and location8 is connected to location9. At the gate, there is nut1, which is unsecured, as well as nut2, nut3, nut4, and nut5, all of which are loose. Spanner1 is currently at location3 and is in working condition, spanner2 is located at location5 and can be used, spanner3 is at location2 and is usable, spanner4 is functional and located at location6, and spanner5 is functional and situated at location3."}
{"question_id": "80b4fbad-6d0d-4d96-ba7d-90d8381576ec", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, bob walks to location3 from location2, from location3 to location4, bob walks, bob walks to location5 from location4, from location5, bob picks up spanner4, from location5 to location6, bob walks, from location6, bob picks up spanner1, bob walks from location6 to location7, spanner5 is picked up by bob from location7, from location7 to location8, bob walks, from location8, bob picks up spanner3, bob picks up spanner2 from location8, from location8 to location9, bob walks, bob walks from location9 to gate, at gate, bob uses spanner5 to tighten nut1, nut2 is tightened by bob using spanner4 at gate, nut3 is tightened by bob using spanner3 at gate and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, if bob tightens nut5 with spanner1 at gate, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "a link between gate and location5 does not exist, a link between gate and location8 does not exist, a link between gate and shed does not exist, a link between location1 and gate does not exist, a link between location1 and location6 does not exist, a link between location2 and gate does not exist, a link between location2 and location3 exists, a link between location2 and location8 does not exist, a link between location3 and location4 exists, a link between location3 and location5 does not exist, a link between location3 and location6 does not exist, a link between location3 and location7 does not exist, a link between location3 and location8 does not exist, a link between location4 and location1 does not exist, a link between location4 and location2 does not exist, a link between location4 and location3 does not exist, a link between location4 and location5 exists, a link between location5 and location3 does not exist, a link between location5 and location4 does not exist, a link between location6 and location1 does not exist, a link between location6 and location5 does not exist, a link between location6 and shed does not exist, a link between location7 and gate does not exist, a link between location7 and location2 does not exist, a link between location7 and location8 exists, a link between location7 and location9 does not exist, a link between location7 and shed does not exist, a link between location8 and gate does not exist, a link between location8 and location1 does not exist, a link between location8 and location5 does not exist, a link between location8 and shed does not exist, a link between location9 and location6 does not exist, a link between location9 and location8 does not exist, a link between shed and location5 does not exist, a link between shed and location7 does not exist, a link between shed and location8 does not exist, a link between shed and location9 does not exist, bob is carrying spanner1, bob is carrying spanner3, bob is carrying spanner4, bob is carrying spanner5, bob is located at gate, bob is not at location1, bob is not at location2, bob is not at location4, bob is not at location9, bob is not located at location3, bob is not located at location5, bob is not located at location6, bob is not located at location7, bob is not located at location8, bob is not located at shed, gate and location7 are not linked, gate is not linked to location1, gate is not linked to location2, gate is not linked to location3, gate is not linked to location4, gate is not linked to location6, gate is not linked to location9, location1 and location2 are linked, location1 and location5 are not linked, location1 is not linked to location3, location1 is not linked to location4, location1 is not linked to location7, location1 is not linked to location8, location1 is not linked to location9, location1 is not linked to shed, location2 and location4 are not linked, location2 and location6 are not linked, location2 and location7 are not linked, location2 and location9 are not linked, location2 is not linked to location1, location2 is not linked to location5, location2 is not linked to shed, location3 and gate are not linked, location3 is not linked to location1, location3 is not linked to location2, location3 is not linked to location9, location3 is not linked to shed, location4 and gate are not linked, location4 and location6 are not linked, location4 and location7 are not linked, location4 and location8 are not linked, location4 and shed are not linked, location4 is not linked to location9, location5 and gate are not linked, location5 and location1 are not linked, location5 and location6 are linked, location5 and location7 are not linked, location5 and location8 are not linked, location5 and location9 are not linked, location5 and shed are not linked, location5 is not linked to location2, location6 and gate are not linked, location6 and location7 are linked, location6 and location8 are not linked, location6 is not linked to location2, location6 is not linked to location3, location6 is not linked to location4, location6 is not linked to location9, location7 and location3 are not linked, location7 and location4 are not linked, location7 and location5 are not linked, location7 is not linked to location1, location7 is not linked to location6, location8 and location4 are not linked, location8 and location9 are linked, location8 is not linked to location2, location8 is not linked to location3, location8 is not linked to location6, location8 is not linked to location7, location9 and location1 are not linked, location9 and location2 are not linked, location9 and location4 are not linked, location9 is linked to gate, location9 is not linked to location3, location9 is not linked to location5, location9 is not linked to location7, location9 is not linked to shed, nut1 is currently at gate, nut1 is not at location2, nut1 is not at location8, nut1 is not currently at location3, nut1 is not currently at location5, nut1 is not currently at location7, nut1 is not currently at location9, nut1 is not currently at shed, nut1 is not located at location1, nut1 is not located at location4, nut1 is not located at location6, nut1 is not loose, nut1 is tightened, nut2 is located at gate, nut2 is not at location2, nut2 is not at location3, nut2 is not at location4, nut2 is not at location7, nut2 is not at location8, nut2 is not at location9, nut2 is not currently at location6, nut2 is not currently at shed, nut2 is not located at location1, nut2 is not located at location5, nut2 is not loose, nut3 is located at gate, nut3 is not at location2, nut3 is not at location8, nut3 is not at location9, nut3 is not at shed, nut3 is not currently at location5, nut3 is not currently at location7, nut3 is not located at location1, nut3 is not located at location3, nut3 is not located at location4, nut3 is not located at location6, nut3 is not loose, nut3 is tightened, nut4 is at gate, nut4 is not currently at location1, nut4 is not currently at location2, nut4 is not currently at location4, nut4 is not currently at location5, nut4 is not currently at location6, nut4 is not currently at location7, nut4 is not currently at location8, nut4 is not currently at location9, nut4 is not located at location3, nut4 is not located at shed, nut4 is secured, nut5 is currently at gate, nut5 is not at location4, nut5 is not at location5, nut5 is not at location7, nut5 is not at location8, nut5 is not at shed, nut5 is not currently at location1, nut5 is not currently at location2, nut5 is not currently at location9, nut5 is not located at location3, nut5 is not located at location6, nut5 is secured, nut5 is tightened, shed and location3 are not linked, shed and location4 are not linked, shed and location6 are not linked, shed is linked to location1, shed is not linked to gate, shed is not linked to location2, spanner1 is not at location1, spanner1 is not at location4, spanner1 is not at location5, spanner1 is not at location6, spanner1 is not at location9, spanner1 is not currently at gate, spanner1 is not currently at location3, spanner1 is not currently at location7, spanner1 is not located at location2, spanner1 is not located at location8, spanner1 is not located at shed, spanner1 is not usable, spanner2 can't be used, spanner2 is carried by bob, spanner2 is not at location1, spanner2 is not at location3, spanner2 is not at location7, spanner2 is not at location8, spanner2 is not at location9, spanner2 is not at shed, spanner2 is not currently at location2, spanner2 is not currently at location4, spanner2 is not currently at location6, spanner2 is not located at gate, spanner2 is not located at location5, spanner3 is not at location1, spanner3 is not at location6, spanner3 is not at location8, spanner3 is not at location9, spanner3 is not currently at gate, spanner3 is not currently at location2, spanner3 is not currently at location3, spanner3 is not located at location4, spanner3 is not located at location5, spanner3 is not located at location7, spanner3 is not located at shed, spanner3 is not usable, spanner4 can't be used, spanner4 is not at location1, spanner4 is not at location2, spanner4 is not at location4, spanner4 is not at location6, spanner4 is not at location7, spanner4 is not at shed, spanner4 is not currently at gate, spanner4 is not currently at location5, spanner4 is not currently at location8, spanner4 is not located at location3, spanner4 is not located at location9, spanner5 is not at location1, spanner5 is not currently at gate, spanner5 is not currently at location7, spanner5 is not currently at location9, spanner5 is not currently at shed, spanner5 is not located at location2, spanner5 is not located at location3, spanner5 is not located at location4, spanner5 is not located at location5, spanner5 is not located at location6, spanner5 is not located at location8, spanner5 is not usable, tightening of nut2 is complete and tightening of nut4 is complete", "plan_length": 19, "initial_state_nl": "A link between location3 and location4 exists, a link between location7 and location8 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location4 and location5 are linked, location5 is linked to location6, location6 and location7 are linked, location8 and location9 are linked, location9 is linked to gate, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is functional, spanner1 is located at location6, spanner2 is functional, spanner2 is located at location8, spanner3 is at location8, spanner3 is usable, spanner4 is located at location5, spanner4 is usable, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, followed by location3, then location4, and subsequently location5. At location5, Bob collects spanner4, then proceeds to location6, where he picks up spanner1. From location6, Bob heads to location7, where he retrieves spanner5, and then moves to location8. At location8, Bob collects both spanner3 and spanner2, before proceeding to location9 and finally reaching the gate. Upon arriving at the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, and spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, if Bob uses spanner1 to tighten nut5 at the gate, what are all the valid properties of the state (including both affirmative and negated properties)? If there are none, state 'None'.", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, and another connection exists between location7 and location8. Bob is currently situated at the shed. Location1 is connected to location2, which in turn is linked to location3. A connection also exists between location4 and location5, and location5 is further linked to location6. Additionally, location6 is connected to location7, and location8 is linked to location9. Location9 is connected to the gate. At the gate, nut1 is present but not secured. Similarly, nut2, nut3, nut4, and nut5 are all located at the gate and are loose. The shed is connected to location1. Spanner1 is in working condition and is situated at location6. Spanner2 is also functional and is located at location8. Spanner3 is at location8 and is usable. Spanner4 is located at location5 and is usable. Spanner5 is functional and is currently at location7."}
{"question_id": "1cd00cc8-ff40-4c69-a802-a8a0b33b55cf", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, if bob picks up spanner5 from location1, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "a link between gate and location2 does not exist, a link between gate and location4 does not exist, a link between gate and location8 does not exist, a link between location1 and location2 exists, a link between location1 and location3 does not exist, a link between location1 and location7 does not exist, a link between location1 and shed does not exist, a link between location2 and location3 exists, a link between location2 and location5 does not exist, a link between location3 and gate does not exist, a link between location3 and location1 does not exist, a link between location3 and shed does not exist, a link between location4 and location3 does not exist, a link between location4 and location7 does not exist, a link between location4 and location9 does not exist, a link between location4 and shed does not exist, a link between location5 and location2 does not exist, a link between location5 and location7 does not exist, a link between location5 and shed does not exist, a link between location6 and location1 does not exist, a link between location6 and location4 does not exist, a link between location6 and location8 does not exist, a link between location6 and shed does not exist, a link between location7 and location1 does not exist, a link between location7 and location6 does not exist, a link between location7 and shed does not exist, a link between location8 and location3 does not exist, a link between location9 and location3 does not exist, a link between location9 and location4 does not exist, a link between location9 and location7 does not exist, a link between location9 and location8 does not exist, a link between location9 and shed does not exist, a link between shed and location1 exists, a link between shed and location3 does not exist, a link between shed and location5 does not exist, a link between shed and location6 does not exist, a link between shed and location7 does not exist, bob is at location1, bob is carrying spanner5, bob is not at location5, bob is not at location7, bob is not carrying spanner1, bob is not currently at gate, bob is not currently at location2, bob is not currently at location3, bob is not currently at location6, bob is not currently at location8, bob is not currently at shed, bob is not located at location4, bob is not located at location9, gate and location1 are not linked, gate and location3 are not linked, gate and location5 are not linked, gate and shed are not linked, gate is not linked to location6, gate is not linked to location7, gate is not linked to location9, location1 and location4 are not linked, location1 and location8 are not linked, location1 is not linked to gate, location1 is not linked to location5, location1 is not linked to location6, location1 is not linked to location9, location2 and location1 are not linked, location2 and location4 are not linked, location2 and location9 are not linked, location2 and shed are not linked, location2 is not linked to gate, location2 is not linked to location6, location2 is not linked to location7, location2 is not linked to location8, location3 and location2 are not linked, location3 and location5 are not linked, location3 and location6 are not linked, location3 and location7 are not linked, location3 is linked to location4, location3 is not linked to location8, location3 is not linked to location9, location4 and gate are not linked, location4 and location6 are not linked, location4 and location8 are not linked, location4 is linked to location5, location4 is not linked to location1, location4 is not linked to location2, location5 and gate are not linked, location5 and location1 are not linked, location5 and location4 are not linked, location5 and location6 are linked, location5 and location9 are not linked, location5 is not linked to location3, location5 is not linked to location8, location6 and gate are not linked, location6 and location2 are not linked, location6 and location5 are not linked, location6 is linked to location7, location6 is not linked to location3, location6 is not linked to location9, location7 and location4 are not linked, location7 and location5 are not linked, location7 is linked to location8, location7 is not linked to gate, location7 is not linked to location2, location7 is not linked to location3, location7 is not linked to location9, location8 and location2 are not linked, location8 and location5 are not linked, location8 and location9 are linked, location8 and shed are not linked, location8 is not linked to gate, location8 is not linked to location1, location8 is not linked to location4, location8 is not linked to location6, location8 is not linked to location7, location9 and location1 are not linked, location9 and location2 are not linked, location9 and location6 are not linked, location9 is linked to gate, location9 is not linked to location5, nut1 is at gate, nut1 is loose, nut1 is not at location3, nut1 is not at location6, nut1 is not at location9, nut1 is not at shed, nut1 is not currently at location4, nut1 is not currently at location7, nut1 is not located at location1, nut1 is not located at location2, nut1 is not located at location5, nut1 is not located at location8, nut2 is at gate, nut2 is not at location1, nut2 is not at location2, nut2 is not at location6, nut2 is not at shed, nut2 is not currently at location7, nut2 is not currently at location8, nut2 is not located at location3, nut2 is not located at location4, nut2 is not located at location5, nut2 is not located at location9, nut2 is not secured, nut2 is not tightened, nut3 is located at gate, nut3 is not at location6, nut3 is not at location8, nut3 is not at location9, nut3 is not currently at location4, nut3 is not currently at location5, nut3 is not located at location1, nut3 is not located at location2, nut3 is not located at location3, nut3 is not located at location7, nut3 is not located at shed, nut3 is not secured, nut4 is at gate, nut4 is not at location4, nut4 is not at location5, nut4 is not at location8, nut4 is not at shed, nut4 is not currently at location2, nut4 is not currently at location6, nut4 is not currently at location9, nut4 is not located at location1, nut4 is not located at location3, nut4 is not located at location7, nut4 is not secured, nut5 is currently at gate, nut5 is loose, nut5 is not at location2, nut5 is not at location8, nut5 is not at location9, nut5 is not currently at location3, nut5 is not currently at location5, nut5 is not currently at location6, nut5 is not currently at location7, nut5 is not located at location1, nut5 is not located at location4, nut5 is not located at shed, nut5 is not tightened, shed and gate are not linked, shed is not linked to location2, shed is not linked to location4, shed is not linked to location8, shed is not linked to location9, spanner1 can be used, spanner1 is located at location4, spanner1 is not at location2, spanner1 is not at location6, spanner1 is not at location8, spanner1 is not at shed, spanner1 is not currently at gate, spanner1 is not currently at location1, spanner1 is not currently at location3, spanner1 is not currently at location9, spanner1 is not located at location5, spanner1 is not located at location7, spanner2 can be used, spanner2 is currently at location7, spanner2 is not at gate, spanner2 is not at location3, spanner2 is not at location9, spanner2 is not at shed, spanner2 is not carried by bob, spanner2 is not currently at location2, spanner2 is not currently at location4, spanner2 is not currently at location6, spanner2 is not located at location1, spanner2 is not located at location5, spanner2 is not located at location8, spanner3 can be used, spanner3 is located at location6, spanner3 is not at location1, spanner3 is not at location2, spanner3 is not at location5, spanner3 is not at location7, spanner3 is not at location8, spanner3 is not at location9, spanner3 is not carried by bob, spanner3 is not currently at location4, spanner3 is not currently at shed, spanner3 is not located at gate, spanner3 is not located at location3, spanner4 is currently at location1, spanner4 is functional, spanner4 is not at gate, spanner4 is not at location5, spanner4 is not at location9, spanner4 is not carried by bob, spanner4 is not currently at location2, spanner4 is not currently at location6, spanner4 is not currently at location7, spanner4 is not currently at shed, spanner4 is not located at location3, spanner4 is not located at location4, spanner4 is not located at location8, spanner5 can be used, spanner5 is not at location6, spanner5 is not currently at location1, spanner5 is not currently at location2, spanner5 is not currently at location4, spanner5 is not currently at shed, spanner5 is not located at gate, spanner5 is not located at location3, spanner5 is not located at location5, spanner5 is not located at location7, spanner5 is not located at location8, spanner5 is not located at location9, tightening of nut1 is incomplete, tightening of nut3 is incomplete and tightening of nut4 is incomplete", "plan_length": 1, "initial_state_nl": "A link between location3 and location4 exists, a link between location6 and location7 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location4 and location5 are linked, location5 and location6 are linked, location7 and location8 are linked, location9 is linked to gate, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 is at location4, spanner1 is usable, spanner2 can be used, spanner2 is at location7, spanner3 is functional, spanner3 is located at location6, spanner4 is at location1, spanner4 is usable, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following steps are taken: bob moves from the shed to location1 to achieve the current state. In this state, if bob collects spanner5 from location1, what are all the possible valid properties of the state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, a connection between location6 and location7, a connection between location8 and location9, and a connection between the shed and location1. Bob is currently at the shed. Location1 is adjacent to location2, and location2 is connected to location3. Additionally, location4 is linked to location5, location5 is connected to location6, location7 is adjacent to location8, and location9 is connected to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3, nut4, and nut5. Furthermore, there are several usable spanners: spanner1 is at location4, spanner2 is at location7, spanner3 is at location6, and both spanner4 and spanner5 are at location1, all of which are functional."}
{"question_id": "886b6c97-43ee-491a-9b9a-c808381e8c97", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, bob walks to location3 from location2, bob walks to location4 from location3, bob walks to location5 from location4, spanner4 is picked up by bob from location5, bob walks to location6 from location5, from location6, bob picks up spanner1, bob walks to location7 from location6, from location7, bob picks up spanner5, bob walks from location7 to location8, from location8, bob picks up spanner3, spanner2 is picked up by bob from location8, from location8 to location9, bob walks, bob walks from location9 to gate, nut1 is tightened by bob using spanner5 at gate, bob tightens nut2 with spanner4 at gate, nut3 is tightened by bob using spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, if at gate, bob uses spanner1 to tighten nut5, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "a link between location2 and location3 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is at gate, bob is carrying spanner1, bob is carrying spanner2, location1 is linked to location2, location3 is linked to location4, location4 and location5 are linked, location5 is linked to location6, location6 and location7 are linked, location7 is linked to location8, location9 is linked to gate, nut1 is currently at gate, nut2 is at gate, nut3 is at gate, nut4 is currently at gate, nut4 is tightened, nut5 is located at gate, nut5 is tightened, spanner3 is carried by bob, spanner4 is carried by bob, spanner5 is carried by bob, tightening of nut1 is complete, tightening of nut2 is complete and tightening of nut3 is complete", "plan_length": 19, "initial_state_nl": "A link between location3 and location4 exists, a link between location7 and location8 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location4 and location5 are linked, location5 is linked to location6, location6 and location7 are linked, location8 and location9 are linked, location9 is linked to gate, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is functional, spanner1 is located at location6, spanner2 is functional, spanner2 is located at location8, spanner3 is at location8, spanner3 is usable, spanner4 is located at location5, spanner4 is usable, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, followed by location3, location4, and location5. At location5, Bob picks up spanner4, then proceeds to location6, where he picks up spanner1. He continues to location7, where he collects spanner5, and then moves to location8, where he picks up both spanner3 and spanner2. From location8, Bob walks to location9 and then to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, if Bob uses spanner1 to tighten nut5 at the gate, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, and another connection exists between location7 and location8. Bob is currently situated at the shed. Location1 is connected to location2, which in turn is connected to location3. Location4 is linked to location5, and location5 is connected to location6. Location6 and location7 are also connected, and location8 is linked to location9. Location9 is connected to the gate. At the gate, there is nut1, which is not secured. Additionally, nut2, nut3, nut4, and nut5 are all located at the gate, and all of them are loose. The shed is connected to location1. Spanner1 is in working condition and is situated at location6. Spanner2 is also functional and is located at location8. Spanner3 is at location8 and is usable. Spanner4 is located at location5 and is usable. Spanner5 is in a usable state and is currently at location7."}
{"question_id": "04b4539c-521b-4b06-ac82-6418d1839179", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, if bob walks to location2 from location1, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "a link between gate and location1 does not exist, a link between gate and location7 does not exist, a link between gate and location8 does not exist, a link between gate and location9 does not exist, a link between location1 and location5 does not exist, a link between location1 and location7 does not exist, a link between location1 and location8 does not exist, a link between location2 and gate does not exist, a link between location2 and location1 does not exist, a link between location2 and location9 does not exist, a link between location3 and location2 does not exist, a link between location3 and location7 does not exist, a link between location3 and location9 does not exist, a link between location3 and shed does not exist, a link between location4 and location1 does not exist, a link between location4 and location2 does not exist, a link between location5 and gate does not exist, a link between location5 and location1 does not exist, a link between location5 and location2 does not exist, a link between location6 and location8 does not exist, a link between location6 and location9 does not exist, a link between location7 and location4 does not exist, a link between location8 and location6 does not exist, a link between location8 and shed does not exist, a link between location9 and location6 does not exist, a link between location9 and shed does not exist, bob is not at location5, bob is not at location6, bob is not at location9, bob is not carrying spanner1, bob is not carrying spanner3, bob is not carrying spanner4, bob is not currently at gate, bob is not currently at location1, bob is not currently at location4, bob is not currently at location7, bob is not located at location3, bob is not located at location8, bob is not located at shed, gate and location2 are not linked, gate is not linked to location3, gate is not linked to location4, gate is not linked to location5, gate is not linked to location6, gate is not linked to shed, location1 and shed are not linked, location1 is not linked to gate, location1 is not linked to location3, location1 is not linked to location4, location1 is not linked to location6, location1 is not linked to location9, location2 and location4 are not linked, location2 and location7 are not linked, location2 and location8 are not linked, location2 is not linked to location5, location2 is not linked to location6, location2 is not linked to shed, location3 and location6 are not linked, location3 is not linked to gate, location3 is not linked to location1, location3 is not linked to location5, location3 is not linked to location8, location4 and location6 are not linked, location4 and location7 are not linked, location4 is not linked to gate, location4 is not linked to location3, location4 is not linked to location8, location4 is not linked to location9, location4 is not linked to shed, location5 and location3 are not linked, location5 and location4 are not linked, location5 is not linked to location7, location5 is not linked to location8, location5 is not linked to location9, location5 is not linked to shed, location6 and gate are not linked, location6 and location1 are not linked, location6 and location3 are not linked, location6 and location5 are not linked, location6 and shed are not linked, location6 is not linked to location2, location6 is not linked to location4, location7 and gate are not linked, location7 and location6 are not linked, location7 and location9 are not linked, location7 is not linked to location1, location7 is not linked to location2, location7 is not linked to location3, location7 is not linked to location5, location7 is not linked to shed, location8 and gate are not linked, location8 and location1 are not linked, location8 and location2 are not linked, location8 and location4 are not linked, location8 is not linked to location3, location8 is not linked to location5, location8 is not linked to location7, location9 and location2 are not linked, location9 and location3 are not linked, location9 and location8 are not linked, location9 is not linked to location1, location9 is not linked to location4, location9 is not linked to location5, location9 is not linked to location7, nut1 is not at location1, nut1 is not at location9, nut1 is not at shed, nut1 is not currently at location2, nut1 is not currently at location3, nut1 is not currently at location4, nut1 is not currently at location6, nut1 is not currently at location7, nut1 is not currently at location8, nut1 is not located at location5, nut1 is not tightened, nut2 is not at location1, nut2 is not at location6, nut2 is not at location8, nut2 is not currently at location3, nut2 is not currently at location4, nut2 is not currently at location5, nut2 is not currently at location7, nut2 is not located at location2, nut2 is not located at location9, nut2 is not located at shed, nut2 is not tightened, nut3 is not at location5, nut3 is not currently at location1, nut3 is not currently at location6, nut3 is not currently at location8, nut3 is not currently at shed, nut3 is not located at location2, nut3 is not located at location3, nut3 is not located at location4, nut3 is not located at location7, nut3 is not located at location9, nut3 is not tightened, nut4 is not at location6, nut4 is not at location7, nut4 is not currently at location3, nut4 is not currently at location4, nut4 is not currently at location8, nut4 is not located at location1, nut4 is not located at location2, nut4 is not located at location5, nut4 is not located at location9, nut4 is not located at shed, nut4 is not tightened, nut5 is not at location2, nut5 is not at location3, nut5 is not at location9, nut5 is not currently at location1, nut5 is not currently at location6, nut5 is not currently at location7, nut5 is not currently at location8, nut5 is not located at location4, nut5 is not located at location5, nut5 is not located at shed, nut5 is not tightened, shed and location5 are not linked, shed and location8 are not linked, shed and location9 are not linked, shed is not linked to gate, shed is not linked to location2, shed is not linked to location3, shed is not linked to location4, shed is not linked to location6, shed is not linked to location7, spanner1 is not at location6, spanner1 is not at location8, spanner1 is not currently at location2, spanner1 is not currently at location5, spanner1 is not currently at location7, spanner1 is not currently at location9, spanner1 is not currently at shed, spanner1 is not located at gate, spanner1 is not located at location1, spanner1 is not located at location4, spanner2 is not at gate, spanner2 is not at location1, spanner2 is not carried by bob, spanner2 is not currently at location2, spanner2 is not currently at location4, spanner2 is not currently at location6, spanner2 is not currently at location7, spanner2 is not currently at location8, spanner2 is not currently at location9, spanner2 is not located at location3, spanner2 is not located at shed, spanner3 is not at location6, spanner3 is not at location9, spanner3 is not currently at gate, spanner3 is not currently at location5, spanner3 is not currently at location7, spanner3 is not currently at location8, spanner3 is not currently at shed, spanner3 is not located at location1, spanner3 is not located at location3, spanner3 is not located at location4, spanner4 is not at location2, spanner4 is not at location5, spanner4 is not at location7, spanner4 is not at shed, spanner4 is not currently at gate, spanner4 is not currently at location1, spanner4 is not currently at location3, spanner4 is not located at location4, spanner4 is not located at location8, spanner4 is not located at location9, spanner5 is not at gate, spanner5 is not at location1, spanner5 is not at location4, spanner5 is not at location6, spanner5 is not at location8, spanner5 is not at location9, spanner5 is not carried by bob, spanner5 is not currently at location2, spanner5 is not currently at location5, spanner5 is not currently at shed and spanner5 is not located at location7", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is currently at shed, location2 is linked to location3, location3 is linked to location4, location4 is linked to location5, location6 is linked to location7, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, spanner1 is currently at location3, spanner1 is usable, spanner2 can be used, spanner2 is located at location5, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: bob moves from the shed to location1 to achieve the current state. In this state, if bob proceeds from location1 to location2, what are all the valid state properties that involve negations? If there are no such properties, write None.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location5 and location6, and a connection is present between location9 and the gate. Additionally, a connection exists between the shed and location1. Currently, bob is at the shed. Location2 is connected to location3, which in turn is connected to location4, and location4 is linked to location5. Furthermore, location6 is connected to location7, and location7 is linked to location8, which is also connected to location9. At the gate, nut1 is present but not secured, while nut2, nut3, nut4, and nut5 are all loose. Spanner1 is currently at location3 and is in working condition, spanner2 is located at location5 and can be used, spanner3 is at location2 and is usable, spanner4 is functional and located at location6, and spanner5 is also functional and situated at location3."}
{"question_id": "14755bfd-71c9-4475-a39a-9b6a1ecac4cf", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks from location1 to location2, bob walks to location3 from location2, bob walks from location3 to location4, bob walks to location5 from location4, bob picks up spanner4 from location5, bob walks from location5 to location6, from location6, bob picks up spanner1, bob walks from location6 to location7 and spanner5 is picked up by bob from location7 to reach the current state. In this state, if bob walks to location8 from location7, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "a link between gate and location1 does not exist, a link between gate and location3 does not exist, a link between gate and location7 does not exist, a link between gate and shed does not exist, a link between location1 and location4 does not exist, a link between location1 and location5 does not exist, a link between location1 and location9 does not exist, a link between location2 and gate does not exist, a link between location2 and location4 does not exist, a link between location2 and location6 does not exist, a link between location3 and location2 does not exist, a link between location3 and location8 does not exist, a link between location3 and shed does not exist, a link between location4 and gate does not exist, a link between location4 and location1 does not exist, a link between location4 and location2 does not exist, a link between location4 and location7 does not exist, a link between location4 and location9 does not exist, a link between location5 and gate does not exist, a link between location5 and location1 does not exist, a link between location5 and location4 does not exist, a link between location6 and gate does not exist, a link between location6 and location4 does not exist, a link between location6 and location8 does not exist, a link between location6 and shed does not exist, a link between location7 and location1 does not exist, a link between location7 and location2 does not exist, a link between location7 and location5 does not exist, a link between location7 and location9 does not exist, a link between location7 and shed does not exist, a link between location8 and gate does not exist, a link between location8 and location2 does not exist, a link between location8 and location6 does not exist, a link between location9 and location6 does not exist, a link between location9 and location7 does not exist, a link between location9 and location8 does not exist, a link between location9 and shed does not exist, a link between shed and location3 does not exist, a link between shed and location4 does not exist, a link between shed and location8 does not exist, a link between shed and location9 does not exist, bob is not at location1, bob is not at location2, bob is not at location3, bob is not at location4, bob is not at location5, bob is not at location9, bob is not carrying spanner3, bob is not currently at gate, bob is not currently at location7, bob is not currently at shed, bob is not located at location6, gate and location4 are not linked, gate and location5 are not linked, gate is not linked to location2, gate is not linked to location6, gate is not linked to location8, gate is not linked to location9, location1 and gate are not linked, location1 and location6 are not linked, location1 and location7 are not linked, location1 and location8 are not linked, location1 and shed are not linked, location1 is not linked to location3, location2 and location1 are not linked, location2 and location7 are not linked, location2 and location9 are not linked, location2 and shed are not linked, location2 is not linked to location5, location2 is not linked to location8, location3 and gate are not linked, location3 and location6 are not linked, location3 and location7 are not linked, location3 and location9 are not linked, location3 is not linked to location1, location3 is not linked to location5, location4 and location8 are not linked, location4 and shed are not linked, location4 is not linked to location3, location4 is not linked to location6, location5 and location7 are not linked, location5 and location8 are not linked, location5 is not linked to location2, location5 is not linked to location3, location5 is not linked to location9, location5 is not linked to shed, location6 and location1 are not linked, location6 and location2 are not linked, location6 and location3 are not linked, location6 and location9 are not linked, location6 is not linked to location5, location7 and gate are not linked, location7 and location3 are not linked, location7 is not linked to location4, location7 is not linked to location6, location8 and location1 are not linked, location8 and location4 are not linked, location8 and location5 are not linked, location8 is not linked to location3, location8 is not linked to location7, location8 is not linked to shed, location9 and location1 are not linked, location9 and location2 are not linked, location9 and location3 are not linked, location9 and location5 are not linked, location9 is not linked to location4, nut1 is not at location2, nut1 is not at location3, nut1 is not at location4, nut1 is not at location9, nut1 is not currently at location1, nut1 is not currently at location8, nut1 is not located at location5, nut1 is not located at location6, nut1 is not located at location7, nut1 is not located at shed, nut1 is not tightened, nut2 is not at location4, nut2 is not at location5, nut2 is not at location7, nut2 is not at shed, nut2 is not currently at location3, nut2 is not currently at location6, nut2 is not currently at location8, nut2 is not located at location1, nut2 is not located at location2, nut2 is not located at location9, nut2 is not tightened, nut3 is not at location1, nut3 is not at location3, nut3 is not at location6, nut3 is not at shed, nut3 is not currently at location5, nut3 is not currently at location7, nut3 is not currently at location8, nut3 is not located at location2, nut3 is not located at location4, nut3 is not located at location9, nut4 is not at location1, nut4 is not at location4, nut4 is not at shed, nut4 is not currently at location2, nut4 is not currently at location5, nut4 is not currently at location8, nut4 is not located at location3, nut4 is not located at location6, nut4 is not located at location7, nut4 is not located at location9, nut4 is not tightened, nut5 is not at location4, nut5 is not at location5, nut5 is not at location7, nut5 is not at shed, nut5 is not currently at location8, nut5 is not located at location1, nut5 is not located at location2, nut5 is not located at location3, nut5 is not located at location6, nut5 is not located at location9, shed and location6 are not linked, shed is not linked to gate, shed is not linked to location2, shed is not linked to location5, shed is not linked to location7, spanner1 is not at location2, spanner1 is not currently at location1, spanner1 is not currently at location3, spanner1 is not currently at location6, spanner1 is not currently at location8, spanner1 is not currently at shed, spanner1 is not located at gate, spanner1 is not located at location4, spanner1 is not located at location5, spanner1 is not located at location7, spanner1 is not located at location9, spanner2 is not at location1, spanner2 is not at location2, spanner2 is not at location4, spanner2 is not at location7, spanner2 is not at location9, spanner2 is not at shed, spanner2 is not carried by bob, spanner2 is not currently at gate, spanner2 is not currently at location3, spanner2 is not currently at location5, spanner2 is not located at location6, spanner3 is not at location2, spanner3 is not at location4, spanner3 is not at location6, spanner3 is not at shed, spanner3 is not currently at gate, spanner3 is not currently at location9, spanner3 is not located at location1, spanner3 is not located at location3, spanner3 is not located at location5, spanner3 is not located at location7, spanner4 is not at location6, spanner4 is not at location7, spanner4 is not at location9, spanner4 is not currently at location1, spanner4 is not currently at location5, spanner4 is not currently at location8, spanner4 is not currently at shed, spanner4 is not located at gate, spanner4 is not located at location2, spanner4 is not located at location3, spanner4 is not located at location4, spanner5 is not at location6, spanner5 is not at location8, spanner5 is not at location9, spanner5 is not currently at location2, spanner5 is not currently at location5, spanner5 is not currently at location7, spanner5 is not currently at shed, spanner5 is not located at gate, spanner5 is not located at location1, spanner5 is not located at location3, spanner5 is not located at location4, tightening of nut3 is incomplete and tightening of nut5 is incomplete", "plan_length": 10, "initial_state_nl": "A link between location3 and location4 exists, a link between location7 and location8 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location4 and location5 are linked, location5 is linked to location6, location6 and location7 are linked, location8 and location9 are linked, location9 is linked to gate, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is functional, spanner1 is located at location6, spanner2 is functional, spanner2 is located at location8, spanner3 is at location8, spanner3 is usable, spanner4 is located at location5, spanner4 is usable, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob moves from the shed to location1, then to location2, then to location3, followed by location4, then location5, where he collects spanner4. From location5, he proceeds to location6, picks up spanner1, and then heads to location7, where he picks up spanner5, resulting in the current state. In this state, if Bob walks from location7 to location8, what are all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, and another connection exists between location7 and location8. Bob is currently situated at the shed. Location1 is connected to location2, which in turn is connected to location3. Location4 is linked to location5, and location5 is connected to location6. Location6 and location7 are also connected, while location8 is linked to location9. Location9 is connected to the gate. At the gate, there is nut1, which is not secured. Additionally, nut2, nut3, nut4, and nut5 are all located at the gate, and they are all loose. The shed is connected to location1. Spanner1 is in working condition and is situated at location6. Spanner2 is also functional and is located at location8. Spanner3 is at location8 and is usable. Spanner4 is located at location5 and is usable. Spanner5 is functional and is currently at location7."}
{"question_id": "83c3a042-d77c-4ba1-b00f-281b00f4487b", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, if from location1 to location2, bob walks, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "a link between location6 and location7 exists, a link between location9 and gate exists, bob is at location2, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location4 is linked to location5, location5 is linked to location6, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is at gate, nut2 is not secured, nut3 is at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is currently at location6, spanner2 is at location8, spanner2 is functional, spanner3 is currently at location8, spanner3 is usable, spanner4 is functional, spanner4 is located at location5, spanner5 is at location7 and spanner5 is functional", "plan_length": 1, "initial_state_nl": "A link between location3 and location4 exists, a link between location7 and location8 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location4 and location5 are linked, location5 is linked to location6, location6 and location7 are linked, location8 and location9 are linked, location9 is linked to gate, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is functional, spanner1 is located at location6, spanner2 is functional, spanner2 is located at location8, spanner3 is at location8, spanner3 is usable, spanner4 is located at location5, spanner4 is usable, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob walks from the shed to location1 to reach the current state. In this state, if Bob walks from location1 to location2, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, and another connection exists between location7 and location8. Bob is currently situated at the shed. Location1 is connected to location2, which in turn is connected to location3. Location4 is linked to location5, and location5 is connected to location6. Location6 and location7 are also connected, and location8 is linked to location9. Location9 is connected to the gate. At the gate, there is nut1, which is not secured. Additionally, nut2, nut3, nut4, and nut5 are all located at the gate, and they are all loose. The shed is connected to location1. Spanner1, which is functional, is situated at location6. Spanner2, also functional, is located at location8. Spanner3, which is usable, is at location8, while spanner4, also usable, is at location5. Spanner5 is functional and is currently located at location7."}
{"question_id": "264abcb8-2747-46ca-a257-aaf02d7d6942", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, if from location1 to location2, bob walks, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "a link between gate and location3 does not exist, a link between gate and location5 does not exist, a link between gate and location7 does not exist, a link between location1 and shed does not exist, a link between location2 and location5 does not exist, a link between location2 and location9 does not exist, a link between location4 and location9 does not exist, a link between location4 and shed does not exist, a link between location5 and location2 does not exist, a link between location5 and location7 does not exist, a link between location6 and gate does not exist, a link between location6 and location1 does not exist, a link between location6 and location2 does not exist, a link between location6 and location3 does not exist, a link between location6 and location4 does not exist, a link between location6 and shed does not exist, a link between location7 and location2 does not exist, a link between location7 and location3 does not exist, a link between location7 and location4 does not exist, a link between location7 and location9 does not exist, a link between location7 and shed does not exist, a link between location8 and location5 does not exist, a link between location8 and location7 does not exist, a link between location9 and location4 does not exist, a link between shed and location7 does not exist, bob is not at gate, bob is not at location6, bob is not at location8, bob is not at shed, bob is not carrying spanner1, bob is not carrying spanner3, bob is not carrying spanner4, bob is not carrying spanner5, bob is not currently at location1, bob is not currently at location3, bob is not currently at location5, bob is not currently at location7, bob is not currently at location9, bob is not located at location4, gate and location4 are not linked, gate and location8 are not linked, gate is not linked to location1, gate is not linked to location2, gate is not linked to location6, gate is not linked to location9, gate is not linked to shed, location1 and gate are not linked, location1 and location4 are not linked, location1 and location5 are not linked, location1 and location7 are not linked, location1 and location9 are not linked, location1 is not linked to location3, location1 is not linked to location6, location1 is not linked to location8, location2 and gate are not linked, location2 and location6 are not linked, location2 and location7 are not linked, location2 and location8 are not linked, location2 and shed are not linked, location2 is not linked to location1, location2 is not linked to location4, location3 and gate are not linked, location3 and location2 are not linked, location3 and location5 are not linked, location3 and location6 are not linked, location3 and location9 are not linked, location3 is not linked to location1, location3 is not linked to location7, location3 is not linked to location8, location3 is not linked to shed, location4 and location1 are not linked, location4 and location3 are not linked, location4 and location7 are not linked, location4 is not linked to gate, location4 is not linked to location2, location4 is not linked to location6, location4 is not linked to location8, location5 and location1 are not linked, location5 is not linked to gate, location5 is not linked to location3, location5 is not linked to location4, location5 is not linked to location8, location5 is not linked to location9, location5 is not linked to shed, location6 and location5 are not linked, location6 is not linked to location8, location6 is not linked to location9, location7 and location1 are not linked, location7 and location6 are not linked, location7 is not linked to gate, location7 is not linked to location5, location8 is not linked to gate, location8 is not linked to location1, location8 is not linked to location2, location8 is not linked to location3, location8 is not linked to location4, location8 is not linked to location6, location8 is not linked to shed, location9 and location1 are not linked, location9 and location2 are not linked, location9 and location3 are not linked, location9 and location5 are not linked, location9 and location6 are not linked, location9 and location7 are not linked, location9 is not linked to location8, location9 is not linked to shed, nut1 is not at location2, nut1 is not at location4, nut1 is not at location7, nut1 is not currently at location1, nut1 is not currently at location3, nut1 is not currently at location5, nut1 is not currently at location9, nut1 is not located at location6, nut1 is not located at location8, nut1 is not located at shed, nut1 is not tightened, nut2 is not at location3, nut2 is not at location4, nut2 is not at location6, nut2 is not at location7, nut2 is not currently at location1, nut2 is not currently at location2, nut2 is not currently at location5, nut2 is not currently at location8, nut2 is not currently at location9, nut2 is not currently at shed, nut3 is not at location1, nut3 is not at location2, nut3 is not at location3, nut3 is not at location9, nut3 is not at shed, nut3 is not currently at location7, nut3 is not currently at location8, nut3 is not located at location4, nut3 is not located at location5, nut3 is not located at location6, nut3 is not tightened, nut4 is not currently at location2, nut4 is not currently at location3, nut4 is not currently at location9, nut4 is not currently at shed, nut4 is not located at location1, nut4 is not located at location4, nut4 is not located at location5, nut4 is not located at location6, nut4 is not located at location7, nut4 is not located at location8, nut4 is not tightened, nut5 is not at location1, nut5 is not at location8, nut5 is not currently at location2, nut5 is not currently at location3, nut5 is not currently at location4, nut5 is not currently at location9, nut5 is not currently at shed, nut5 is not located at location5, nut5 is not located at location6, nut5 is not located at location7, shed and gate are not linked, shed and location6 are not linked, shed and location8 are not linked, shed is not linked to location2, shed is not linked to location3, shed is not linked to location4, shed is not linked to location5, shed is not linked to location9, spanner1 is not at location2, spanner1 is not at location3, spanner1 is not at location5, spanner1 is not at location6, spanner1 is not at location7, spanner1 is not at location9, spanner1 is not currently at location4, spanner1 is not located at gate, spanner1 is not located at location1, spanner1 is not located at shed, spanner2 is not at location4, spanner2 is not at location5, spanner2 is not at location8, spanner2 is not carried by bob, spanner2 is not currently at location2, spanner2 is not currently at location9, spanner2 is not currently at shed, spanner2 is not located at gate, spanner2 is not located at location1, spanner2 is not located at location3, spanner2 is not located at location7, spanner3 is not at gate, spanner3 is not at location1, spanner3 is not at location6, spanner3 is not at location7, spanner3 is not at location9, spanner3 is not at shed, spanner3 is not currently at location3, spanner3 is not currently at location5, spanner3 is not located at location4, spanner3 is not located at location8, spanner4 is not at gate, spanner4 is not at location3, spanner4 is not at location4, spanner4 is not at location5, spanner4 is not currently at location1, spanner4 is not currently at location6, spanner4 is not currently at location7, spanner4 is not currently at shed, spanner4 is not located at location8, spanner4 is not located at location9, spanner5 is not at gate, spanner5 is not at location5, spanner5 is not at location9, spanner5 is not currently at location1, spanner5 is not currently at location4, spanner5 is not currently at location8, spanner5 is not currently at shed, spanner5 is not located at location2, spanner5 is not located at location3, spanner5 is not located at location7, tightening of nut2 is incomplete and tightening of nut5 is incomplete", "plan_length": 1, "initial_state_nl": "A link between shed and location1 exists, bob is currently at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 is linked to location5, location5 and location6 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 can be used, spanner1 is at location8, spanner2 can be used, spanner2 is currently at location6, spanner3 is located at location2, spanner3 is usable, spanner4 is at location2, spanner4 is usable, spanner5 is currently at location6 and spanner5 is functional.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob moves from the shed to location1 to attain the current state. In this state, if Bob proceeds from location1 to location2, what are all the valid properties of the state that involve negations? If there are no such properties, write None.", "initial_state_nl_paraphrased": "There is a connection between the shed and location1. Bob is currently present at the shed, location1, and location2 are connected, location2 is connected to location3, location3 is linked with location4, location4 is connected to location5, location5 and location6 are connected, location6 is linked to location7, location7 is connected to location8, location8 and location9 are connected, location9 is linked to the gate. At the gate, nut1 is situated and is loose, nut2 is also at the gate and is loose, nut3 is currently at the gate but is not secured, nut4 is located at the gate and is not secured, and nut5 is at the gate and is loose. Spanner1 is available for use and is located at location8, spanner2 is available for use and is currently at location6, spanner3 is situated at location2 and is usable, spanner4 is at location2 and is usable, and spanner5 is currently at location6 and is functional."}
{"question_id": "873c66fe-4a2f-45ea-8213-06354573fee0", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, if from location1, bob picks up spanner5, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "a link between location2 and location3 exists, bob is carrying spanner5, bob is located at location1, location1 and location2 are linked, location3 and location4 are linked, location4 and location5 are linked, location5 and location6 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is located at gate, nut2 is not secured, nut3 is currently at gate, nut3 is not secured, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is not secured, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is functional, spanner2 is located at location7, spanner3 is at location6, spanner3 is functional, spanner4 is functional, spanner4 is located at location1 and spanner5 is usable", "plan_length": 1, "initial_state_nl": "A link between location3 and location4 exists, a link between location6 and location7 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location4 and location5 are linked, location5 and location6 are linked, location7 and location8 are linked, location9 is linked to gate, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 is at location4, spanner1 is usable, spanner2 can be used, spanner2 is at location7, spanner3 is functional, spanner3 is located at location6, spanner4 is at location1, spanner4 is usable, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob walks from the shed to location1 to achieve the current state. In this state, if Bob picks up spanner5 from location1, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, a connection between location6 and location7, a connection between location8 and location9, and a connection between the shed and location1. Bob is currently at the shed. Location1 is adjacent to location2, location2 is connected to location3, location4 is linked to location5, location5 is adjacent to location6, location7 is connected to location8, and location9 is linked to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3, nut4, and nut5. Additionally, there are several usable spanners: spanner1 is located at location4, spanner2 is at location7, spanner3 is at location6, and both spanner4 and spanner5 are at location1, all of which are functional."}
{"question_id": "4d397401-4446-4a8a-96e9-d0a6988498c7", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks from location1 to location2, bob walks to location3 from location2, bob walks from location3 to location4, bob walks from location4 to location5, bob picks up spanner4 from location5, bob walks to location6 from location5, bob picks up spanner1 from location6, bob walks to location7 from location6, from location7, bob picks up spanner5, bob walks to location8 from location7, from location8, bob picks up spanner3, from location8, bob picks up spanner2, from location8 to location9, bob walks, bob walks to gate from location9, nut1 is tightened by bob using spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, bob tightens nut3 with spanner3 at gate and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, if at gate, bob uses spanner1 to tighten nut5, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "a link between gate and location1 does not exist, a link between gate and location2 does not exist, a link between gate and location4 does not exist, a link between gate and location5 does not exist, a link between gate and location9 does not exist, a link between gate and shed does not exist, a link between location1 and gate does not exist, a link between location1 and location6 does not exist, a link between location1 and location7 does not exist, a link between location2 and location9 does not exist, a link between location2 and shed does not exist, a link between location3 and location2 does not exist, a link between location3 and location7 does not exist, a link between location3 and shed does not exist, a link between location4 and location3 does not exist, a link between location4 and location6 does not exist, a link between location4 and location8 does not exist, a link between location4 and location9 does not exist, a link between location5 and gate does not exist, a link between location5 and location1 does not exist, a link between location5 and location2 does not exist, a link between location5 and location3 does not exist, a link between location5 and location7 does not exist, a link between location5 and shed does not exist, a link between location6 and location2 does not exist, a link between location6 and location8 does not exist, a link between location6 and location9 does not exist, a link between location8 and location1 does not exist, a link between location8 and location2 does not exist, a link between location8 and shed does not exist, a link between location9 and location2 does not exist, a link between location9 and location3 does not exist, a link between location9 and location6 does not exist, a link between location9 and location8 does not exist, a link between shed and gate does not exist, a link between shed and location2 does not exist, a link between shed and location7 does not exist, a link between shed and location8 does not exist, bob is not at location2, bob is not at location4, bob is not at location5, bob is not at shed, bob is not currently at location1, bob is not currently at location3, bob is not currently at location8, bob is not located at location6, bob is not located at location7, bob is not located at location9, gate and location7 are not linked, gate and location8 are not linked, gate is not linked to location3, gate is not linked to location6, location1 and shed are not linked, location1 is not linked to location3, location1 is not linked to location4, location1 is not linked to location5, location1 is not linked to location8, location1 is not linked to location9, location2 and location1 are not linked, location2 and location4 are not linked, location2 and location6 are not linked, location2 and location8 are not linked, location2 is not linked to gate, location2 is not linked to location5, location2 is not linked to location7, location3 and gate are not linked, location3 and location1 are not linked, location3 and location6 are not linked, location3 is not linked to location5, location3 is not linked to location8, location3 is not linked to location9, location4 and gate are not linked, location4 and location2 are not linked, location4 is not linked to location1, location4 is not linked to location7, location4 is not linked to shed, location5 and location4 are not linked, location5 and location8 are not linked, location5 and location9 are not linked, location6 and location3 are not linked, location6 and location5 are not linked, location6 is not linked to gate, location6 is not linked to location1, location6 is not linked to location4, location6 is not linked to shed, location7 and gate are not linked, location7 and location2 are not linked, location7 and location4 are not linked, location7 and location5 are not linked, location7 and location9 are not linked, location7 and shed are not linked, location7 is not linked to location1, location7 is not linked to location3, location7 is not linked to location6, location8 and location3 are not linked, location8 is not linked to gate, location8 is not linked to location4, location8 is not linked to location5, location8 is not linked to location6, location8 is not linked to location7, location9 and location4 are not linked, location9 and location7 are not linked, location9 is not linked to location1, location9 is not linked to location5, location9 is not linked to shed, nut1 is not at location1, nut1 is not at location3, nut1 is not at location4, nut1 is not at location5, nut1 is not at location7, nut1 is not at shed, nut1 is not currently at location2, nut1 is not currently at location6, nut1 is not currently at location8, nut1 is not located at location9, nut1 is secured, nut2 is not at location2, nut2 is not at location4, nut2 is not at location5, nut2 is not at location8, nut2 is not currently at shed, nut2 is not located at location1, nut2 is not located at location3, nut2 is not located at location6, nut2 is not located at location7, nut2 is not located at location9, nut2 is not loose, nut3 is not at shed, nut3 is not currently at location2, nut3 is not currently at location3, nut3 is not currently at location5, nut3 is not currently at location6, nut3 is not currently at location7, nut3 is not currently at location8, nut3 is not currently at location9, nut3 is not located at location1, nut3 is not located at location4, nut3 is not loose, nut4 is not at location9, nut4 is not at shed, nut4 is not currently at location1, nut4 is not currently at location2, nut4 is not currently at location6, nut4 is not currently at location7, nut4 is not currently at location8, nut4 is not located at location3, nut4 is not located at location4, nut4 is not located at location5, nut4 is secured, nut5 is not at location1, nut5 is not at location4, nut5 is not at location8, nut5 is not currently at location5, nut5 is not currently at location7, nut5 is not currently at location9, nut5 is not located at location2, nut5 is not located at location3, nut5 is not located at location6, nut5 is not located at shed, nut5 is secured, shed and location3 are not linked, shed and location6 are not linked, shed is not linked to location4, shed is not linked to location5, shed is not linked to location9, spanner1 is not at gate, spanner1 is not at shed, spanner1 is not currently at location1, spanner1 is not currently at location5, spanner1 is not currently at location6, spanner1 is not currently at location8, spanner1 is not functional, spanner1 is not located at location2, spanner1 is not located at location3, spanner1 is not located at location4, spanner1 is not located at location7, spanner1 is not located at location9, spanner2 can't be used, spanner2 is not at gate, spanner2 is not at location4, spanner2 is not at location8, spanner2 is not at location9, spanner2 is not currently at location1, spanner2 is not currently at location5, spanner2 is not currently at location6, spanner2 is not currently at location7, spanner2 is not located at location2, spanner2 is not located at location3, spanner2 is not located at shed, spanner3 can't be used, spanner3 is not at location2, spanner3 is not at location3, spanner3 is not at location7, spanner3 is not at location9, spanner3 is not at shed, spanner3 is not currently at gate, spanner3 is not currently at location6, spanner3 is not located at location1, spanner3 is not located at location4, spanner3 is not located at location5, spanner3 is not located at location8, spanner4 is not at location6, spanner4 is not currently at gate, spanner4 is not currently at location2, spanner4 is not currently at location3, spanner4 is not currently at location7, spanner4 is not currently at location8, spanner4 is not currently at location9, spanner4 is not functional, spanner4 is not located at location1, spanner4 is not located at location4, spanner4 is not located at location5, spanner4 is not located at shed, spanner5 is not at gate, spanner5 is not at location2, spanner5 is not at location3, spanner5 is not at location4, spanner5 is not currently at location1, spanner5 is not currently at location5, spanner5 is not currently at location8, spanner5 is not currently at location9, spanner5 is not functional, spanner5 is not located at location6, spanner5 is not located at location7 and spanner5 is not located at shed", "plan_length": 19, "initial_state_nl": "A link between location3 and location4 exists, a link between location7 and location8 exists, bob is located at shed, location1 is linked to location2, location2 is linked to location3, location4 and location5 are linked, location5 is linked to location6, location6 and location7 are linked, location8 and location9 are linked, location9 is linked to gate, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is functional, spanner1 is located at location6, spanner2 is functional, spanner2 is located at location8, spanner3 is at location8, spanner3 is usable, spanner4 is located at location5, spanner4 is usable, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, followed by location3, then location4, and finally location5. At location5, Bob collects spanner4 and proceeds to location6, where he picks up spanner1. He then moves to location7, collects spanner5, and continues to location8. At location8, Bob collects both spanner3 and spanner2. From location8, he walks to location9 and then to the gate. Upon reaching the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, if Bob uses spanner1 to tighten nut5 at the gate, what are all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, and another connection exists between location7 and location8. Bob is currently situated at the shed. Location1 is connected to location2, which in turn is connected to location3. Location4 is linked to location5, and location5 is connected to location6. Location6 and location7 are also connected, and location8 is linked to location9. Location9 is connected to the gate. At the gate, there is nut1, which is not secured. Additionally, nut2, nut3, nut4, and nut5 are all located at the gate, and all of them are loose. The shed is connected to location1. Spanner1 is in working condition and is situated at location6. Spanner2 is also functional and is located at location8. Spanner3 is at location8 and is usable. Spanner4 is located at location5 and is usable. Spanner5 is in a usable state and is currently at location7."}
{"question_id": "8e4e19af-2fd5-4416-89c9-2def88d84368", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, from location1 to location2, bob walks, bob picks up spanner3 from location2, bob walks to location3 from location2, bob picks up spanner5 from location3, from location3, bob picks up spanner1, bob walks from location3 to location4, from location4 to location5, bob walks, spanner2 is picked up by bob from location5, bob walks to location6 from location5, spanner4 is picked up by bob from location6, bob walks to location7 from location6, bob walks to location8 from location7, bob walks from location8 to location9, bob walks from location9 to gate, at gate, bob uses spanner5 to tighten nut1, at gate, bob uses spanner4 to tighten nut2, bob tightens nut3 with spanner3 at gate and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, if nut5 is tightened by bob using spanner1 at gate, what would be all of the valid properties of the state that involve negations? Write None if there are none.", "answer": "a link between gate and location4 does not exist, a link between gate and location5 does not exist, a link between gate and location9 does not exist, a link between location1 and gate does not exist, a link between location2 and location1 does not exist, a link between location2 and location4 does not exist, a link between location3 and gate does not exist, a link between location3 and location8 does not exist, a link between location3 and location9 does not exist, a link between location3 and shed does not exist, a link between location4 and location2 does not exist, a link between location5 and location4 does not exist, a link between location5 and location9 does not exist, a link between location6 and location8 does not exist, a link between location6 and shed does not exist, a link between location7 and location9 does not exist, a link between location7 and shed does not exist, a link between location8 and location5 does not exist, a link between location9 and location6 does not exist, a link between location9 and location7 does not exist, a link between location9 and shed does not exist, a link between shed and location2 does not exist, bob is not at location2, bob is not at location6, bob is not at location7, bob is not at location8, bob is not currently at location9, bob is not currently at shed, bob is not located at location1, bob is not located at location3, bob is not located at location4, bob is not located at location5, gate and location1 are not linked, gate and location3 are not linked, gate and location7 are not linked, gate and location8 are not linked, gate is not linked to location2, gate is not linked to location6, gate is not linked to shed, location1 and location4 are not linked, location1 and location6 are not linked, location1 and location7 are not linked, location1 and location8 are not linked, location1 and location9 are not linked, location1 is not linked to location3, location1 is not linked to location5, location1 is not linked to shed, location2 and gate are not linked, location2 and location6 are not linked, location2 and location8 are not linked, location2 and location9 are not linked, location2 and shed are not linked, location2 is not linked to location5, location2 is not linked to location7, location3 and location1 are not linked, location3 and location2 are not linked, location3 and location7 are not linked, location3 is not linked to location5, location3 is not linked to location6, location4 and location1 are not linked, location4 and location6 are not linked, location4 and location7 are not linked, location4 is not linked to gate, location4 is not linked to location3, location4 is not linked to location8, location4 is not linked to location9, location4 is not linked to shed, location5 and gate are not linked, location5 and location2 are not linked, location5 and location3 are not linked, location5 and location7 are not linked, location5 and location8 are not linked, location5 and shed are not linked, location5 is not linked to location1, location6 and gate are not linked, location6 and location3 are not linked, location6 and location9 are not linked, location6 is not linked to location1, location6 is not linked to location2, location6 is not linked to location4, location6 is not linked to location5, location7 and location1 are not linked, location7 and location3 are not linked, location7 and location6 are not linked, location7 is not linked to gate, location7 is not linked to location2, location7 is not linked to location4, location7 is not linked to location5, location8 and location1 are not linked, location8 and location3 are not linked, location8 and location4 are not linked, location8 and location7 are not linked, location8 and shed are not linked, location8 is not linked to gate, location8 is not linked to location2, location8 is not linked to location6, location9 and location1 are not linked, location9 and location2 are not linked, location9 and location3 are not linked, location9 and location4 are not linked, location9 and location5 are not linked, location9 and location8 are not linked, nut1 is not at location1, nut1 is not at location2, nut1 is not at location5, nut1 is not at location8, nut1 is not at location9, nut1 is not currently at location7, nut1 is not currently at shed, nut1 is not located at location3, nut1 is not located at location4, nut1 is not located at location6, nut1 is secured, nut2 is not at location2, nut2 is not at location5, nut2 is not at location6, nut2 is not at location8, nut2 is not currently at location1, nut2 is not currently at location3, nut2 is not currently at location7, nut2 is not located at location4, nut2 is not located at location9, nut2 is not located at shed, nut2 is secured, nut3 is not at location1, nut3 is not at location2, nut3 is not at location5, nut3 is not currently at location6, nut3 is not currently at location9, nut3 is not located at location3, nut3 is not located at location4, nut3 is not located at location7, nut3 is not located at location8, nut3 is not located at shed, nut3 is not loose, nut4 is not at location1, nut4 is not at location5, nut4 is not at location6, nut4 is not at shed, nut4 is not currently at location2, nut4 is not currently at location7, nut4 is not located at location3, nut4 is not located at location4, nut4 is not located at location8, nut4 is not located at location9, nut4 is secured, nut5 is not at location1, nut5 is not at location3, nut5 is not at location5, nut5 is not at location8, nut5 is not currently at location2, nut5 is not currently at location4, nut5 is not currently at location7, nut5 is not currently at location9, nut5 is not located at location6, nut5 is not located at shed, nut5 is not loose, shed and gate are not linked, shed and location6 are not linked, shed and location8 are not linked, shed is not linked to location3, shed is not linked to location4, shed is not linked to location5, shed is not linked to location7, shed is not linked to location9, spanner1 is not at gate, spanner1 is not at location1, spanner1 is not at location2, spanner1 is not at location5, spanner1 is not at location8, spanner1 is not at shed, spanner1 is not currently at location6, spanner1 is not currently at location7, spanner1 is not located at location3, spanner1 is not located at location4, spanner1 is not located at location9, spanner1 is not usable, spanner2 is not at location3, spanner2 is not at location5, spanner2 is not currently at location2, spanner2 is not currently at location7, spanner2 is not currently at location8, spanner2 is not currently at location9, spanner2 is not currently at shed, spanner2 is not located at gate, spanner2 is not located at location1, spanner2 is not located at location4, spanner2 is not located at location6, spanner2 is not usable, spanner3 is not at location4, spanner3 is not at location5, spanner3 is not at location6, spanner3 is not currently at gate, spanner3 is not currently at location9, spanner3 is not currently at shed, spanner3 is not functional, spanner3 is not located at location1, spanner3 is not located at location2, spanner3 is not located at location3, spanner3 is not located at location7, spanner3 is not located at location8, spanner4 is not at gate, spanner4 is not at location2, spanner4 is not at location3, spanner4 is not at location4, spanner4 is not currently at location1, spanner4 is not currently at location7, spanner4 is not currently at location8, spanner4 is not currently at shed, spanner4 is not functional, spanner4 is not located at location5, spanner4 is not located at location6, spanner4 is not located at location9, spanner5 can't be used, spanner5 is not at location9, spanner5 is not currently at gate, spanner5 is not currently at location1, spanner5 is not currently at location2, spanner5 is not currently at location3, spanner5 is not currently at location5, spanner5 is not currently at location8, spanner5 is not currently at shed, spanner5 is not located at location4, spanner5 is not located at location6 and spanner5 is not located at location7", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is currently at shed, location2 is linked to location3, location3 is linked to location4, location4 is linked to location5, location6 is linked to location7, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, spanner1 is currently at location3, spanner1 is usable, spanner2 can be used, spanner2 is located at location5, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob walks from the shed to location1, then from location1 to location2, where he picks up spanner3. He proceeds to location3 from location2, collects spanner5, and also picks up spanner1 from location3. Bob then walks to location4, followed by location5, where he picks up spanner2. He continues to location6, collects spanner4, and then walks to location7, location8, and finally location9 before reaching the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, and spanner3 to tighten nut3, and he also uses spanner2 to tighten nut4, resulting in the current state. In this state, if Bob tightens nut5 with spanner1 at the gate, what would be all the valid properties of the state that involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6, a connection between location9 and the gate, and a connection between the shed and location1. Bob is presently at the shed. Location2 is connected to location3, location3 is connected to location4, location4 is connected to location5, location6 is connected to location7, and location7 and location8 are connected, with location8 also being connected to location9. At the gate, nut1 is located but not secured, while nut2, nut3, nut4, and nut5 are all loose. Spanner1 is currently at location3 and is in working condition, spanner2 is located at location5 and can be used, spanner3 is at location2 and is usable, spanner4 is functional and located at location6, and spanner5 is also functional and located at location3."}
{"question_id": "910f4d47-a7a4-4ce8-b75d-16246fdd0260", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks to location2 from location1, bob picks up spanner4 from location2, bob picks up spanner3 from location2, bob walks to location3 from location2, bob walks to location4 from location3, bob walks to location5 from location4, from location5 to location6, bob walks, bob picks up spanner5 from location6, spanner2 is picked up by bob from location6, bob walks from location6 to location7, bob walks from location7 to location8, spanner1 is picked up by bob from location8, from location8 to location9, bob walks, from location9 to gate, bob walks, at gate, bob uses spanner5 to tighten nut1, nut2 is tightened by bob using spanner4 at gate, nut3 is tightened by bob using spanner3 at gate and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, if bob tightens nut5 with spanner1 at gate, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "a link between location1 and location2 exists, a link between location3 and location4 exists, a link between location4 and location5 exists, a link between location6 and location7 exists, a link between location7 and location8 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is carrying spanner1, bob is carrying spanner2, bob is carrying spanner5, bob is currently at gate, location2 and location3 are linked, location5 is linked to location6, location8 and location9 are linked, nut1 is currently at gate, nut1 is tightened, nut2 is currently at gate, nut3 is located at gate, nut3 is tightened, nut4 is at gate, nut5 is at gate, spanner3 is carried by bob, spanner4 is carried by bob, tightening of nut2 is complete, tightening of nut4 is complete and tightening of nut5 is complete", "plan_length": 19, "initial_state_nl": "A link between shed and location1 exists, bob is currently at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 is linked to location5, location5 and location6 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 can be used, spanner1 is at location8, spanner2 can be used, spanner2 is currently at location6, spanner3 is located at location2, spanner3 is usable, spanner4 is at location2, spanner4 is usable, spanner5 is currently at location6 and spanner5 is functional.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, where he collects spanner4 and spanner3. He proceeds to location3, then to location4, and subsequently to location5, followed by location6. At location6, Bob picks up spanner5 and spanner2. He then walks to location7, then to location8, where he collects spanner1. From location8, Bob moves to location9 and finally to the gate. Upon reaching the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, and spanner3 to tighten nut3. Additionally, Bob uses spanner2 to tighten nut4, resulting in the current state. In this state, if Bob tightens nut5 with spanner1 at the gate, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between the shed and location1. Bob is currently present at the shed, location1, and location2 are connected, location2 is connected to location3, location3 is linked with location4, location4 is connected to location5, location5 and location6 are connected, location6 is linked to location7, location7 is connected to location8, location8 and location9 are connected, location9 is linked to the gate. At the gate, nut1 is situated and is loose, nut2 is also at the gate and is loose, nut3 is currently at the gate but is not secured, nut4 is located at the gate and is not secured, and nut5 is at the gate and is loose. Spanner1 is available for use and is located at location8, spanner2 is available for use and is currently at location6, spanner3 is situated at location2 and is usable, spanner4 is at location2 and is usable, and spanner5 is currently at location6 and is functional."}
{"question_id": "58802a94-44f1-4bb6-8723-f3a5a9a32996", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks from location1 to location2, bob picks up spanner3 from location2, bob walks from location2 to location3, from location3, bob picks up spanner5, bob picks up spanner1 from location3, bob walks to location4 from location3, bob walks to location5 from location4, from location5, bob picks up spanner2, from location5 to location6, bob walks, from location6, bob picks up spanner4, bob walks to location7 from location6, bob walks to location8 from location7, bob walks from location8 to location9, from location9 to gate, bob walks, nut1 is tightened by bob using spanner5 at gate, bob tightens nut2 with spanner4 at gate, nut3 is tightened by bob using spanner3 at gate and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, if nut5 is tightened by bob using spanner1 at gate, what would be all of the valid properties of the state that do not involve negations? Write None if there are none.", "answer": "a link between location1 and location2 exists, a link between location7 and location8 exists, a link between shed and location1 exists, bob is carrying spanner2, bob is carrying spanner3, bob is carrying spanner5, bob is currently at gate, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location5 is linked to location6, location6 is linked to location7, location8 is linked to location9, location9 and gate are linked, nut1 is currently at gate, nut2 is at gate, nut3 is at gate, nut3 is tightened, nut4 is located at gate, nut4 is tightened, nut5 is at gate, nut5 is tightened, spanner1 is carried by bob, spanner4 is carried by bob, tightening of nut1 is complete and tightening of nut2 is complete", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is currently at shed, location2 is linked to location3, location3 is linked to location4, location4 is linked to location5, location6 is linked to location7, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, spanner1 is currently at location3, spanner1 is usable, spanner2 can be used, spanner2 is located at location5, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then to location2, where he collects spanner3. He proceeds to location3, picks up spanner5 and spanner1, and then heads to location4. From there, he goes to location5, collects spanner2, and moves to location6, where he picks up spanner4. He then walks to location7, followed by location8, and then location9, before finally reaching the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, if Bob uses spanner1 to tighten nut5 at the gate, what are all the valid properties of the state that do not involve negations? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location5 and location6, and a connection is present between location9 and the gate. Additionally, a connection exists between the shed and location1. Currently, bob is at the shed. Location2 is connected to location3, which in turn is connected to location4, and location4 is linked to location5. Furthermore, location6 is connected to location7, and location7 is linked to location8, which is also connected to location9. At the gate, nut1 is present but not secured, while nut2, nut3, nut4, and nut5 are all loose. Spanner1 is currently at location3 and is in working condition, spanner2 is located at location5 and can be used, spanner3 is at location2 and is usable, spanner4 is functional and located at location6, and spanner5 is also functional and situated at location3."}
{"question_id": "ac56558f-eaa2-4ddf-997c-01c719f6efd9", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, spanner5 is picked up by bob from location1, bob picks up spanner4 from location1, from location1 to location2, bob walks, bob walks from location2 to location3, bob walks from location3 to location4, spanner1 is picked up by bob from location4, bob walks from location4 to location5, from location5 to location6, bob walks and bob picks up spanner3 from location6 to reach the current state. In this state, if bob walks to location7 from location6, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "a link between gate and location1 does not exist, a link between gate and location3 does not exist, a link between gate and location6 does not exist, a link between gate and location7 does not exist, a link between gate and location9 does not exist, a link between location1 and location8 does not exist, a link between location1 and location9 does not exist, a link between location2 and gate does not exist, a link between location2 and location9 does not exist, a link between location3 and location2 does not exist, a link between location3 and location6 does not exist, a link between location3 and location7 does not exist, a link between location3 and location8 does not exist, a link between location4 and location1 does not exist, a link between location4 and location2 does not exist, a link between location4 and location8 does not exist, a link between location5 and location2 does not exist, a link between location5 and location3 does not exist, a link between location5 and location7 does not exist, a link between location5 and location9 does not exist, a link between location5 and shed does not exist, a link between location6 and location2 does not exist, a link between location6 and location3 does not exist, a link between location6 and location5 does not exist, a link between location7 and gate does not exist, a link between location7 and location1 does not exist, a link between location7 and location3 does not exist, a link between location7 and location4 does not exist, a link between location7 and location6 does not exist, a link between location7 and location8 exists, a link between location7 and location9 does not exist, a link between location8 and location2 does not exist, a link between location8 and location6 does not exist, a link between location9 and location3 does not exist, a link between location9 and location4 does not exist, a link between location9 and location7 does not exist, a link between location9 and shed does not exist, a link between shed and gate does not exist, a link between shed and location1 exists, a link between shed and location2 does not exist, a link between shed and location7 does not exist, a link between shed and location9 does not exist, bob is at location7, bob is carrying spanner1, bob is carrying spanner3, bob is carrying spanner4, bob is carrying spanner5, bob is not at location1, bob is not at location8, bob is not at shed, bob is not currently at location3, bob is not currently at location4, bob is not located at gate, bob is not located at location2, bob is not located at location5, bob is not located at location6, bob is not located at location9, gate and location4 are not linked, gate and location5 are not linked, gate and shed are not linked, gate is not linked to location2, gate is not linked to location8, location1 and gate are not linked, location1 and location2 are linked, location1 is not linked to location3, location1 is not linked to location4, location1 is not linked to location5, location1 is not linked to location6, location1 is not linked to location7, location1 is not linked to shed, location2 and location3 are linked, location2 and location4 are not linked, location2 and location6 are not linked, location2 and location8 are not linked, location2 and shed are not linked, location2 is not linked to location1, location2 is not linked to location5, location2 is not linked to location7, location3 and gate are not linked, location3 and location1 are not linked, location3 and location5 are not linked, location3 and location9 are not linked, location3 and shed are not linked, location3 is linked to location4, location4 and location5 are linked, location4 and location6 are not linked, location4 and location7 are not linked, location4 and location9 are not linked, location4 and shed are not linked, location4 is not linked to gate, location4 is not linked to location3, location5 and location4 are not linked, location5 and location6 are linked, location5 is not linked to gate, location5 is not linked to location1, location5 is not linked to location8, location6 and gate are not linked, location6 and location7 are linked, location6 and location8 are not linked, location6 and shed are not linked, location6 is not linked to location1, location6 is not linked to location4, location6 is not linked to location9, location7 and shed are not linked, location7 is not linked to location2, location7 is not linked to location5, location8 and gate are not linked, location8 and location1 are not linked, location8 and location7 are not linked, location8 and shed are not linked, location8 is linked to location9, location8 is not linked to location3, location8 is not linked to location4, location8 is not linked to location5, location9 and location1 are not linked, location9 and location2 are not linked, location9 and location6 are not linked, location9 and location8 are not linked, location9 is linked to gate, location9 is not linked to location5, nut1 is at gate, nut1 is loose, nut1 is not currently at location6, nut1 is not currently at location8, nut1 is not currently at location9, nut1 is not currently at shed, nut1 is not located at location1, nut1 is not located at location2, nut1 is not located at location3, nut1 is not located at location4, nut1 is not located at location5, nut1 is not located at location7, nut1 is not tightened, nut2 is currently at gate, nut2 is not at location1, nut2 is not at location5, nut2 is not at location6, nut2 is not currently at location2, nut2 is not currently at location3, nut2 is not currently at location4, nut2 is not currently at location7, nut2 is not currently at location8, nut2 is not currently at shed, nut2 is not located at location9, nut2 is not secured, nut3 is located at gate, nut3 is not at location2, nut3 is not at location3, nut3 is not at location5, nut3 is not at location8, nut3 is not at shed, nut3 is not currently at location1, nut3 is not located at location4, nut3 is not located at location6, nut3 is not located at location7, nut3 is not located at location9, nut3 is not secured, nut4 is at gate, nut4 is not at location1, nut4 is not at location2, nut4 is not at location3, nut4 is not at location5, nut4 is not at location7, nut4 is not at location8, nut4 is not at shed, nut4 is not currently at location4, nut4 is not currently at location9, nut4 is not located at location6, nut4 is not secured, nut4 is not tightened, nut5 is located at gate, nut5 is not at location3, nut5 is not at location5, nut5 is not at location6, nut5 is not at location9, nut5 is not at shed, nut5 is not currently at location1, nut5 is not currently at location4, nut5 is not currently at location8, nut5 is not located at location2, nut5 is not located at location7, nut5 is not secured, nut5 is not tightened, shed and location3 are not linked, shed and location6 are not linked, shed and location8 are not linked, shed is not linked to location4, shed is not linked to location5, spanner1 can be used, spanner1 is not at location1, spanner1 is not at location2, spanner1 is not at location8, spanner1 is not at location9, spanner1 is not currently at location3, spanner1 is not currently at location4, spanner1 is not currently at location5, spanner1 is not currently at location6, spanner1 is not currently at location7, spanner1 is not currently at shed, spanner1 is not located at gate, spanner2 is currently at location7, spanner2 is not at gate, spanner2 is not at location1, spanner2 is not at shed, spanner2 is not carried by bob, spanner2 is not currently at location4, spanner2 is not currently at location6, spanner2 is not currently at location9, spanner2 is not located at location2, spanner2 is not located at location3, spanner2 is not located at location5, spanner2 is not located at location8, spanner2 is usable, spanner3 can be used, spanner3 is not at location3, spanner3 is not at location4, spanner3 is not at location5, spanner3 is not at location6, spanner3 is not at shed, spanner3 is not currently at location1, spanner3 is not currently at location2, spanner3 is not currently at location7, spanner3 is not currently at location8, spanner3 is not currently at location9, spanner3 is not located at gate, spanner4 is not at gate, spanner4 is not at location9, spanner4 is not currently at location1, spanner4 is not currently at location5, spanner4 is not currently at location8, spanner4 is not located at location2, spanner4 is not located at location3, spanner4 is not located at location4, spanner4 is not located at location6, spanner4 is not located at location7, spanner4 is not located at shed, spanner4 is usable, spanner5 is functional, spanner5 is not at gate, spanner5 is not at location2, spanner5 is not at location5, spanner5 is not at location9, spanner5 is not at shed, spanner5 is not currently at location3, spanner5 is not currently at location6, spanner5 is not located at location1, spanner5 is not located at location4, spanner5 is not located at location7, spanner5 is not located at location8, tightening of nut2 is incomplete and tightening of nut3 is incomplete", "plan_length": 10, "initial_state_nl": "A link between location3 and location4 exists, a link between location6 and location7 exists, a link between location8 and location9 exists, a link between shed and location1 exists, bob is at shed, location1 is linked to location2, location2 and location3 are linked, location4 and location5 are linked, location5 and location6 are linked, location7 and location8 are linked, location9 is linked to gate, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, spanner1 is at location4, spanner1 is usable, spanner2 can be used, spanner2 is at location7, spanner3 is functional, spanner3 is located at location6, spanner4 is at location1, spanner4 is usable, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then collects spanner5 and spanner4 from location1. He proceeds to walk from location1 to location2, then to location3, and subsequently to location4, where he picks up spanner1. Bob continues walking from location4 to location5 and then to location6, where he collects spanner3, ultimately reaching the current state. If Bob then walks from location6 to location7, what are all the valid properties of the resulting state, including both affirmative and negated properties? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "There is a connection between location3 and location4, a connection between location6 and location7, a connection between location8 and location9, and a connection between the shed and location1. Bob is currently at the shed. Location1 is adjacent to location2, location2 is connected to location3, location4 is linked to location5, location5 is adjacent to location6, location7 is connected to location8, and location9 is linked to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3, nut4, and nut5. Additionally, there are several usable spanners: spanner1 is at location4, spanner2 is at location7, spanner3 is at location6, and both spanner4 and spanner5 are at location1, with all of them being functional."}
{"question_id": "8062f34c-dbd9-4280-92be-15a8c2d0a2ca", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, if bob walks to location2 from location1, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "a link between gate and location4 does not exist, a link between gate and location7 does not exist, a link between gate and location9 does not exist, a link between location1 and location3 does not exist, a link between location1 and location6 does not exist, a link between location1 and location9 does not exist, a link between location2 and location1 does not exist, a link between location2 and location6 does not exist, a link between location2 and location9 does not exist, a link between location3 and gate does not exist, a link between location3 and shed does not exist, a link between location4 and location2 does not exist, a link between location4 and location3 does not exist, a link between location4 and location5 exists, a link between location4 and location8 does not exist, a link between location4 and shed does not exist, a link between location5 and location1 does not exist, a link between location5 and location2 does not exist, a link between location5 and location3 does not exist, a link between location5 and location9 does not exist, a link between location6 and gate does not exist, a link between location6 and location2 does not exist, a link between location7 and location1 does not exist, a link between location7 and location2 does not exist, a link between location7 and location9 does not exist, a link between location8 and location1 does not exist, a link between location8 and location2 does not exist, a link between location8 and location5 does not exist, a link between location8 and shed does not exist, a link between location9 and gate exists, a link between location9 and location1 does not exist, a link between location9 and location5 does not exist, a link between location9 and location6 does not exist, a link between shed and location8 does not exist, bob is currently at location2, bob is not at gate, bob is not at location3, bob is not at location6, bob is not at location8, bob is not carrying spanner3, bob is not carrying spanner4, bob is not currently at location1, bob is not currently at location7, bob is not currently at location9, bob is not currently at shed, bob is not located at location4, bob is not located at location5, gate and location1 are not linked, gate and location5 are not linked, gate and location6 are not linked, gate and shed are not linked, gate is not linked to location2, gate is not linked to location3, gate is not linked to location8, location1 and location2 are linked, location1 and location7 are not linked, location1 and location8 are not linked, location1 and shed are not linked, location1 is not linked to gate, location1 is not linked to location4, location1 is not linked to location5, location2 and location3 are linked, location2 and location4 are not linked, location2 and location8 are not linked, location2 and shed are not linked, location2 is not linked to gate, location2 is not linked to location5, location2 is not linked to location7, location3 and location2 are not linked, location3 and location4 are linked, location3 and location8 are not linked, location3 is not linked to location1, location3 is not linked to location5, location3 is not linked to location6, location3 is not linked to location7, location3 is not linked to location9, location4 and location6 are not linked, location4 and location7 are not linked, location4 and location9 are not linked, location4 is not linked to gate, location4 is not linked to location1, location5 and location4 are not linked, location5 and shed are not linked, location5 is linked to location6, location5 is not linked to gate, location5 is not linked to location7, location5 is not linked to location8, location6 and location7 are linked, location6 and location8 are not linked, location6 and location9 are not linked, location6 and shed are not linked, location6 is not linked to location1, location6 is not linked to location3, location6 is not linked to location4, location6 is not linked to location5, location7 and gate are not linked, location7 and location3 are not linked, location7 and shed are not linked, location7 is linked to location8, location7 is not linked to location4, location7 is not linked to location5, location7 is not linked to location6, location8 and location3 are not linked, location8 and location9 are linked, location8 is not linked to gate, location8 is not linked to location4, location8 is not linked to location6, location8 is not linked to location7, location9 and location2 are not linked, location9 is not linked to location3, location9 is not linked to location4, location9 is not linked to location7, location9 is not linked to location8, location9 is not linked to shed, nut1 is at gate, nut1 is loose, nut1 is not at location1, nut1 is not at location2, nut1 is not at location4, nut1 is not at location6, nut1 is not at location8, nut1 is not currently at location3, nut1 is not currently at location5, nut1 is not located at location7, nut1 is not located at location9, nut1 is not located at shed, nut2 is currently at gate, nut2 is loose, nut2 is not at location3, nut2 is not at location7, nut2 is not currently at location2, nut2 is not currently at location5, nut2 is not currently at location6, nut2 is not currently at location8, nut2 is not currently at shed, nut2 is not located at location1, nut2 is not located at location4, nut2 is not located at location9, nut2 is not tightened, nut3 is at gate, nut3 is not at location1, nut3 is not at location4, nut3 is not at location5, nut3 is not at location7, nut3 is not at location8, nut3 is not at shed, nut3 is not currently at location2, nut3 is not currently at location3, nut3 is not currently at location9, nut3 is not located at location6, nut3 is not secured, nut3 is not tightened, nut4 is at gate, nut4 is not at location1, nut4 is not at location2, nut4 is not at location3, nut4 is not at location6, nut4 is not at location9, nut4 is not currently at location7, nut4 is not currently at location8, nut4 is not located at location4, nut4 is not located at location5, nut4 is not located at shed, nut4 is not secured, nut5 is at gate, nut5 is loose, nut5 is not at location3, nut5 is not at location7, nut5 is not at location8, nut5 is not at location9, nut5 is not currently at location1, nut5 is not currently at location2, nut5 is not currently at location5, nut5 is not currently at location6, nut5 is not located at location4, nut5 is not located at shed, nut5 is not tightened, shed and location3 are not linked, shed and location6 are not linked, shed and location9 are not linked, shed is linked to location1, shed is not linked to gate, shed is not linked to location2, shed is not linked to location4, shed is not linked to location5, shed is not linked to location7, spanner1 is at location8, spanner1 is functional, spanner1 is not at location7, spanner1 is not carried by bob, spanner1 is not currently at gate, spanner1 is not currently at location1, spanner1 is not currently at location4, spanner1 is not currently at location5, spanner1 is not currently at location9, spanner1 is not currently at shed, spanner1 is not located at location2, spanner1 is not located at location3, spanner1 is not located at location6, spanner2 is located at location6, spanner2 is not at gate, spanner2 is not at location2, spanner2 is not carried by bob, spanner2 is not currently at location5, spanner2 is not currently at location8, spanner2 is not located at location1, spanner2 is not located at location3, spanner2 is not located at location4, spanner2 is not located at location7, spanner2 is not located at location9, spanner2 is not located at shed, spanner2 is usable, spanner3 is located at location2, spanner3 is not at location1, spanner3 is not at location4, spanner3 is not at location8, spanner3 is not currently at gate, spanner3 is not currently at location3, spanner3 is not currently at location5, spanner3 is not currently at location9, spanner3 is not located at location6, spanner3 is not located at location7, spanner3 is not located at shed, spanner3 is usable, spanner4 is located at location2, spanner4 is not at location7, spanner4 is not at location8, spanner4 is not at location9, spanner4 is not currently at location3, spanner4 is not currently at location5, spanner4 is not currently at location6, spanner4 is not currently at shed, spanner4 is not located at gate, spanner4 is not located at location1, spanner4 is not located at location4, spanner4 is usable, spanner5 can be used, spanner5 is located at location6, spanner5 is not at gate, spanner5 is not at location1, spanner5 is not at location2, spanner5 is not at location3, spanner5 is not at location5, spanner5 is not at location9, spanner5 is not carried by bob, spanner5 is not currently at location8, spanner5 is not located at location4, spanner5 is not located at location7, spanner5 is not located at shed, tightening of nut1 is incomplete and tightening of nut4 is incomplete", "plan_length": 1, "initial_state_nl": "A link between shed and location1 exists, bob is currently at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 is linked to location5, location5 and location6 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is currently at gate, nut3 is not secured, nut4 is located at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 can be used, spanner1 is at location8, spanner2 can be used, spanner2 is currently at location6, spanner3 is located at location2, spanner3 is usable, spanner4 is at location2, spanner4 is usable, spanner5 is currently at location6 and spanner5 is functional.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob moves from the shed to location1 to attain the current state. In this state, if Bob proceeds from location1 to location2, what are all the valid properties of the resulting state (including both affirmative and negated properties)? If there are no valid properties, state None.", "initial_state_nl_paraphrased": "There is a connection between the shed and location1. Bob is currently present at the shed, location1, and location2 are connected, location2 is connected to location3, location3 is linked with location4, location4 is connected to location5, location5 and location6 are connected, location6 is linked to location7, location7 is connected to location8, location8 and location9 are connected, location9 is connected to the gate. At the gate, nut1 is situated and is loose, nut2 is also at the gate and is loose, nut3 is currently at the gate but not secured, nut4 is located at the gate and is not secured, and nut5 is at the gate and is loose. Spanner1 is available for use and is located at location8, spanner2 is available for use and is currently at location6, spanner3 is situated at location2 and is usable, spanner4 is at location2 and is usable, and spanner5 is currently at location6 and is functional."}
{"question_id": "444734a5-e1dc-4710-bfbf-b22550e15529", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_2_question_3", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, if bob walks to location2 from location1, what would be all of the valid properties of the state (both with and without negations)? Write None if there are none.", "answer": "a link between gate and location1 does not exist, a link between gate and location2 does not exist, a link between gate and location3 does not exist, a link between gate and location5 does not exist, a link between gate and location8 does not exist, a link between location1 and location3 does not exist, a link between location1 and location4 does not exist, a link between location1 and location6 does not exist, a link between location1 and location8 does not exist, a link between location1 and location9 does not exist, a link between location2 and gate does not exist, a link between location2 and location3 exists, a link between location2 and location8 does not exist, a link between location2 and shed does not exist, a link between location3 and gate does not exist, a link between location3 and location5 does not exist, a link between location3 and location6 does not exist, a link between location3 and location9 does not exist, a link between location3 and shed does not exist, a link between location4 and location5 exists, a link between location4 and shed does not exist, a link between location5 and location1 does not exist, a link between location5 and location7 does not exist, a link between location6 and gate does not exist, a link between location6 and location5 does not exist, a link between location6 and location8 does not exist, a link between location7 and gate does not exist, a link between location7 and location1 does not exist, a link between location7 and location6 does not exist, a link between location8 and location5 does not exist, a link between location9 and location8 does not exist, a link between shed and gate does not exist, a link between shed and location3 does not exist, a link between shed and location6 does not exist, a link between shed and location7 does not exist, a link between shed and location9 does not exist, bob is currently at location2, bob is not at location3, bob is not carrying spanner3, bob is not carrying spanner4, bob is not currently at location4, bob is not currently at location5, bob is not currently at location7, bob is not currently at location8, bob is not currently at location9, bob is not currently at shed, bob is not located at gate, bob is not located at location1, bob is not located at location6, gate and location6 are not linked, gate and location7 are not linked, gate is not linked to location4, gate is not linked to location9, gate is not linked to shed, location1 and gate are not linked, location1 and location7 are not linked, location1 is linked to location2, location1 is not linked to location5, location1 is not linked to shed, location2 and location1 are not linked, location2 and location5 are not linked, location2 and location9 are not linked, location2 is not linked to location4, location2 is not linked to location6, location2 is not linked to location7, location3 and location1 are not linked, location3 and location2 are not linked, location3 and location4 are linked, location3 and location7 are not linked, location3 is not linked to location8, location4 and location1 are not linked, location4 and location2 are not linked, location4 and location7 are not linked, location4 and location9 are not linked, location4 is not linked to gate, location4 is not linked to location3, location4 is not linked to location6, location4 is not linked to location8, location5 and location3 are not linked, location5 and location6 are linked, location5 and location8 are not linked, location5 and shed are not linked, location5 is not linked to gate, location5 is not linked to location2, location5 is not linked to location4, location5 is not linked to location9, location6 and location1 are not linked, location6 and location7 are linked, location6 and location9 are not linked, location6 is not linked to location2, location6 is not linked to location3, location6 is not linked to location4, location6 is not linked to shed, location7 and location2 are not linked, location7 and location3 are not linked, location7 and location4 are not linked, location7 and location8 are linked, location7 and shed are not linked, location7 is not linked to location5, location7 is not linked to location9, location8 and gate are not linked, location8 and location2 are not linked, location8 and location3 are not linked, location8 and location7 are not linked, location8 and shed are not linked, location8 is linked to location9, location8 is not linked to location1, location8 is not linked to location4, location8 is not linked to location6, location9 and location1 are not linked, location9 and location2 are not linked, location9 and location3 are not linked, location9 and location5 are not linked, location9 is linked to gate, location9 is not linked to location4, location9 is not linked to location6, location9 is not linked to location7, location9 is not linked to shed, nut1 is currently at gate, nut1 is loose, nut1 is not at location3, nut1 is not at location6, nut1 is not at location9, nut1 is not at shed, nut1 is not currently at location1, nut1 is not currently at location2, nut1 is not currently at location4, nut1 is not currently at location5, nut1 is not currently at location7, nut1 is not currently at location8, nut2 is at gate, nut2 is not at location3, nut2 is not at location4, nut2 is not at location8, nut2 is not at location9, nut2 is not currently at location1, nut2 is not currently at location2, nut2 is not currently at location5, nut2 is not currently at shed, nut2 is not located at location6, nut2 is not located at location7, nut2 is not secured, nut2 is not tightened, nut3 is at gate, nut3 is not at location1, nut3 is not at location3, nut3 is not currently at location2, nut3 is not currently at location6, nut3 is not currently at location7, nut3 is not currently at location9, nut3 is not located at location4, nut3 is not located at location5, nut3 is not located at location8, nut3 is not located at shed, nut3 is not secured, nut4 is currently at gate, nut4 is loose, nut4 is not at location5, nut4 is not at location6, nut4 is not at location9, nut4 is not currently at location1, nut4 is not currently at location4, nut4 is not currently at location7, nut4 is not located at location2, nut4 is not located at location3, nut4 is not located at location8, nut4 is not located at shed, nut4 is not tightened, nut5 is located at gate, nut5 is not at location2, nut5 is not at location7, nut5 is not at location8, nut5 is not at location9, nut5 is not currently at location3, nut5 is not currently at location4, nut5 is not currently at location5, nut5 is not currently at location6, nut5 is not currently at shed, nut5 is not located at location1, nut5 is not secured, nut5 is not tightened, shed and location2 are not linked, shed and location4 are not linked, shed is linked to location1, shed is not linked to location5, shed is not linked to location8, spanner1 is currently at location3, spanner1 is functional, spanner1 is not at location4, spanner1 is not at location5, spanner1 is not carried by bob, spanner1 is not currently at location6, spanner1 is not currently at shed, spanner1 is not located at gate, spanner1 is not located at location1, spanner1 is not located at location2, spanner1 is not located at location7, spanner1 is not located at location8, spanner1 is not located at location9, spanner2 is at location5, spanner2 is not at gate, spanner2 is not at location4, spanner2 is not carried by bob, spanner2 is not currently at location1, spanner2 is not currently at location6, spanner2 is not currently at shed, spanner2 is not located at location2, spanner2 is not located at location3, spanner2 is not located at location7, spanner2 is not located at location8, spanner2 is not located at location9, spanner2 is usable, spanner3 is currently at location2, spanner3 is functional, spanner3 is not at location1, spanner3 is not at location3, spanner3 is not at location4, spanner3 is not at location5, spanner3 is not at location6, spanner3 is not currently at location7, spanner3 is not currently at shed, spanner3 is not located at gate, spanner3 is not located at location8, spanner3 is not located at location9, spanner4 can be used, spanner4 is currently at location6, spanner4 is not at gate, spanner4 is not at location3, spanner4 is not at location8, spanner4 is not at shed, spanner4 is not currently at location1, spanner4 is not currently at location2, spanner4 is not currently at location5, spanner4 is not currently at location7, spanner4 is not located at location4, spanner4 is not located at location9, spanner5 is located at location3, spanner5 is not at shed, spanner5 is not carried by bob, spanner5 is not currently at location1, spanner5 is not currently at location2, spanner5 is not currently at location4, spanner5 is not located at gate, spanner5 is not located at location5, spanner5 is not located at location6, spanner5 is not located at location7, spanner5 is not located at location8, spanner5 is not located at location9, spanner5 is usable, tightening of nut1 is incomplete and tightening of nut3 is incomplete", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is currently at shed, location2 is linked to location3, location3 is linked to location4, location4 is linked to location5, location6 is linked to location7, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, spanner1 is currently at location3, spanner1 is usable, spanner2 can be used, spanner2 is located at location5, spanner3 is currently at location2, spanner3 is usable, spanner4 is functional, spanner4 is located at location6, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: bob moves from the shed to location1 to attain the current state. In this state, if bob proceeds from location1 to location2, what are all the valid properties of the resulting state (including both affirmative and negated properties)? If there are no valid properties, indicate None.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location5 and location6, and a connection is present between location9 and the gate. Additionally, a connection exists between the shed and location1. Currently, bob is at the shed. Location2 is connected to location3, which in turn is connected to location4, and location4 is linked to location5. Furthermore, location6 is connected to location7, and location7 is linked to location8, which is also connected to location9. At the gate, nut1 is present but not secured, while nut2, nut3, nut4, and nut5 are all loose. Spanner1 is currently at location3 and is in working condition, spanner2 is located at location5 and can be used, spanner3 is at location2 and is usable, spanner4 is functional and located at location6, and spanner5 is also functional and situated at location3."}
