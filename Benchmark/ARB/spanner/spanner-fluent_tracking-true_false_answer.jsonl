{"question_id": "993e785e-5743-4fd9-9f60-38bb9c1b6a24", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, is it True or False that nut3 is tightened?", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, is it True or False that nut3 is tightened?\n\nParaphrased text: \nGiven the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, is nut3 tightened or not?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Furthermore, location5 is linked to location6, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, nut1 is present but not secured, nut2 is also at the gate and is loose, nut3 is at the gate but not secured, nut4 is currently at the gate and not secured, and nut5 is at the gate and loose. The shed is connected to location1. Spanner1 is located at location3 and is in working condition, spanner2 is at location5 and can be used, spanner3 is at location2 and functional, spanner4 is at location6 and usable, and spanner5 is at location3 and in working condition."}
{"question_id": "39c81d8c-6801-4ac9-8e4a-927afee10f86", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_5", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks to location2 from location1, bob picks up spanner3 from location2, bob walks from location2 to location3, bob picks up spanner5 from location3, from location3, bob picks up spanner1, bob walks from location3 to location4, bob walks from location4 to location5, from location5, bob picks up spanner2 and from location5 to location6, bob walks to reach the current state. In this state, is it True or False that spanner3 is usable?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then from location1 to location2, where he picks up spanner3. Next, he walks from location2 to location3, picks up spanner5, and also picks up spanner1 from location3. He then walks from location3 to location4, followed by location5, where he picks up spanner2. Finally, he walks from location5 to location6, reaching the current state. In this state, is it True or False that spanner3 is usable?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are interconnected, and location2 is also linked to location3, which in turn is connected to location4. Furthermore, location5 is linked to location6, which is connected to location7, and location7 is linked to location8, which is then connected to location9. At the gate, the following items are located: nut1, which is not secured, nut2, which is loose, nut3, which is not secured, nut4, which is not secured, and nut5, which is loose. Additionally, the shed is connected to location1. The following spanners are located at their respective locations: spanner1 at location3 and is functional, spanner2 at location5 and is usable, spanner3 at location2 and can be used, spanner4 at location6 and is usable, and spanner5 at location3 and is functional."}
{"question_id": "cd5b493b-b6fd-4ad2-ad71-e203d9009254", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_13", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, from location1 to location2, bob walks, from location2, bob picks up spanner4, spanner3 is picked up by bob from location2, bob walks to location3 from location2, from location3 to location4, bob walks, bob walks to location5 from location4, bob walks from location5 to location6, bob picks up spanner5 from location6, spanner2 is picked up by bob from location6, bob walks from location6 to location7, from location7 to location8, bob walks, from location8, bob picks up spanner1, from location8 to location9, bob walks, bob walks to gate from location9, nut1 is tightened by bob using spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, at gate, bob uses spanner3 to tighten nut3 and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: bob is at gate, nut1 is at gate, nut2 is currently at gate, nut3 is currently at gate, nut4 is at gate and nut5 is at gate?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob walks from the shed to location1, then from location1 to location2, where he picks up spanner4 and spanner3. He then proceeds to walk from location2 to location3, followed by location4, and then location5. From location5, Bob walks to location6, where he picks up spanner5 and spanner2. He continues walking from location6 to location7, then to location8, where he picks up spanner1. After that, Bob walks from location8 to location9 and finally to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: Bob is at the gate, nut1 is at the gate, nut2 is at the gate, nut3 is at the gate, nut4 is at the gate, and nut5 is at the gate?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, and a connection also exists between location3 and location4. Additionally, a connection is present between location5 and location6. Bob is currently at the shed. Furthermore, location4 and location5 are connected, location6 is linked to location7, location7 is connected to location8, location8 and location9 are linked, and location9 is connected to the gate. At the gate, nut1 is located and is not secured. Also at the gate, nut2 is currently present and is loose, nut3 is located and is loose, nut4 is currently present and is not secured, and nut5 is currently present and is not secured. The shed is connected to location1. Spanner1 is functional and is located at location8. Spanner2 is located at location6 and is usable. Spanner3 is functional and is currently at location2. Spanner4 is functional and is located at location2. Spanner5 is functional and is located at location6."}
{"question_id": "200b4c6f-59c1-4cc2-9c7d-17f2e20a02b2", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_16", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, is it True or False that location8 is not linked to location9?", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob moves from the shed to location1 to reach the current state. In this state, is it True or False that location9 is not connected to location8?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, and a connection also exists between location3 and location4. Additionally, a connection is present between location5 and location6. Bob is currently situated at the shed. Furthermore, location4 and location5 are interconnected, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. Location9, in turn, is connected to the gate. At the gate, nut1 is located and is not secured. Similarly, nut2 is currently at the gate and is loose, nut3 is also located at the gate and is loose, nut4 is at the gate and is not secured, and nut5 is at the gate and is not secured. The shed is connected to location1. Spanner1 is functional and is located at location8. Spanner2 is situated at location6 and is usable. Spanner3 is functional and is currently at location2, spanner4 is also functional and is located at location2. Lastly, spanner5 is functional and is located at location6."}
{"question_id": "1bd358bb-5778-4b27-84ca-421cdc248eaf", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_14", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: bob is at location3, bob is at shed, bob is currently at location2, bob is currently at location5, bob is currently at location8, bob is located at gate, bob is located at location1, bob is located at location4, bob is located at location6, bob is located at location7, bob is located at location9, nut1 is at location1, nut1 is at location5, nut1 is at location6, nut1 is currently at gate, nut1 is currently at location2, nut1 is currently at location3, nut1 is currently at location4, nut1 is currently at location7, nut1 is currently at location8, nut1 is currently at location9, nut1 is located at shed, nut2 is at location1, nut2 is at location3, nut2 is at location4, nut2 is at location5, nut2 is at location8, nut2 is currently at gate, nut2 is currently at location2, nut2 is currently at location6, nut2 is currently at location9, nut2 is currently at shed, nut2 is located at location7, nut3 is at gate, nut3 is at location1, nut3 is at location4, nut3 is at location7, nut3 is at location9, nut3 is currently at location5, nut3 is currently at location8, nut3 is located at location2, nut3 is located at location3, nut3 is located at location6, nut3 is located at shed, nut4 is at location4, nut4 is at location5, nut4 is at location8, nut4 is currently at gate, nut4 is currently at location6, nut4 is located at location1, nut4 is located at location2, nut4 is located at location3, nut4 is located at location7, nut4 is located at location9, nut4 is located at shed, nut5 is at shed, nut5 is currently at location1, nut5 is currently at location4, nut5 is currently at location6, nut5 is currently at location8, nut5 is located at gate, nut5 is located at location2, nut5 is located at location3, nut5 is located at location5, nut5 is located at location7, nut5 is located at location9, spanner1 is at location3, spanner1 is at location4, spanner1 is at location5, spanner1 is at location8, spanner1 is at location9, spanner1 is currently at location2, spanner1 is currently at location7, spanner1 is located at gate, spanner1 is located at location1, spanner1 is located at location6, spanner1 is located at shed, spanner2 is at location1, spanner2 is at location8, spanner2 is at shed, spanner2 is currently at location2, spanner2 is currently at location3, spanner2 is currently at location5, spanner2 is currently at location6, spanner2 is located at gate, spanner2 is located at location4, spanner2 is located at location7, spanner2 is located at location9, spanner3 is at gate, spanner3 is at location1, spanner3 is at location2, spanner3 is at location4, spanner3 is at location5, spanner3 is at location7, spanner3 is at location8, spanner3 is currently at location6, spanner3 is currently at location9, spanner3 is currently at shed, spanner3 is located at location3, spanner4 is at location1, spanner4 is at location5, spanner4 is at location6, spanner4 is at location7, spanner4 is at shed, spanner4 is currently at location3, spanner4 is located at gate, spanner4 is located at location2, spanner4 is located at location4, spanner4 is located at location8, spanner4 is located at location9, spanner5 is at location2, spanner5 is at location3, spanner5 is at location4, spanner5 is at location5, spanner5 is at location6, spanner5 is currently at gate, spanner5 is currently at location7, spanner5 is currently at location8, spanner5 is currently at location9, spanner5 is located at location1 and spanner5 is located at shed?", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location3 and location4 exists, a link between location4 and location5 exists, bob is at shed, location2 and location3 are linked, location5 is linked to location6, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is located at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is usable, spanner3 can be used, spanner3 is located at location8, spanner4 can be used, spanner4 is at location5, spanner5 is currently at location7 and spanner5 is usable.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob moves from the shed to location1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: bob is at location3, bob is at the shed, bob is currently at location2, bob is currently at location5, bob is currently at location8, bob is located at the gate, bob is located at location1, bob is located at location4, bob is located at location6, bob is located at location7, bob is located at location9, nut1 is at location1, nut1 is at location5, nut1 is at location6, nut1 is currently at the gate, nut1 is currently at location2, nut1 is currently at location3, nut1 is currently at location4, nut1 is currently at location7, nut1 is currently at location8, nut1 is currently at location9, nut1 is located at the shed, nut2 is at location1, nut2 is at location3, nut2 is at location4, nut2 is at location5, nut2 is at location8, nut2 is currently at the gate, nut2 is currently at location2, nut2 is currently at location6, nut2 is currently at location9, nut2 is currently at the shed, nut2 is located at location7, nut3 is at the gate, nut3 is at location1, nut3 is at location4, nut3 is at location7, nut3 is at location9, nut3 is currently at location5, nut3 is currently at location8, nut3 is located at location2, nut3 is located at location3, nut3 is located at location6, nut3 is located at the shed, nut4 is at location4, nut4 is at location5, nut4 is at location8, nut4 is currently at the gate, nut4 is currently at location6, nut4 is located at location1, nut4 is located at location2, nut4 is located at location3, nut4 is located at location7, nut4 is located at location9, nut4 is located at the shed, nut5 is at the shed, nut5 is currently at location1, nut5 is currently at location4, nut5 is currently at location6, nut5 is currently at location8, nut5 is located at the gate, nut5 is located at location2, nut5 is located at location3, nut5 is located at location5, nut5 is located at location7, nut5 is located at location9, spanner1 is at location3, spanner1 is at location4, spanner1 is at location5, spanner1 is at location8, spanner1 is at location9, spanner1 is currently at location2, spanner1 is currently at location7, spanner1 is located at the gate, spanner1 is located at location1, spanner1 is located at location6, spanner1 is located at the shed, spanner2 is at location1, spanner2 is at location8, spanner2 is at the shed, spanner2 is currently at location2, spanner2 is currently at location3, spanner2 is currently at location5, spanner2 is currently at location6, spanner2 is located at the gate, spanner2 is located at location4, spanner2 is located at location7, spanner2 is located at location9, spanner3 is at the gate, spanner3 is at location1, spanner3 is at location2, spanner3 is at location4, spanner3 is at location5, spanner3 is at location7, spanner3 is at location8, spanner3 is currently at location6, spanner3 is currently at location9, spanner3 is currently at the shed, spanner3 is located at location3, spanner4 is at location1, spanner4 is at location5, spanner4 is at location6, spanner4 is at location7, spanner4 is at the shed, spanner4 is currently at location3, spanner4 is located at the gate, spanner4 is located at location2, spanner4 is located at location4, spanner4 is located at location8, spanner4 is located at location9, spanner5 is at location2, spanner5 is at location3, spanner5 is at location4, spanner5 is at location5, spanner5 is at location6, spanner5 is currently at the gate, spanner5 is currently at location7, spanner5 is currently at location8, spanner5 is currently at location9, spanner5 is located at location1 and spanner5 is located at the shed?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location3 and location4, and a connection between location4 and location5. Bob is currently at the shed. Additionally, location2 is connected to location3, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3 (which is not secured), nut4, and nut5. Furthermore, the shed is connected to location1. There are several usable spanners: spanner1 is at location6, spanner2 and spanner3 are at location8, spanner4 is at location5, and spanner5 is at location7."}
{"question_id": "7e8f80ef-144f-4406-b467-f01e5298c3f3", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_19", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks to location2 from location1, from location2 to location3, bob walks, from location3 to location4, bob walks, from location4 to location5, bob walks, spanner4 is picked up by bob from location5, bob walks from location5 to location6, from location6, bob picks up spanner1, bob walks to location7 from location6, spanner5 is picked up by bob from location7, bob walks from location7 to location8, bob picks up spanner3 from location8, bob picks up spanner2 from location8, bob walks from location8 to location9, bob walks to gate from location9, at gate, bob uses spanner5 to tighten nut1, at gate, bob uses spanner4 to tighten nut2, at gate, bob uses spanner3 to tighten nut3 and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: a link between location1 and location2 exists, a link between location2 and location3 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, a link between location7 and location8 exists, a link between shed and location1 exists, location3 is linked to location4, location4 and location5 are linked, location8 and location9 are linked and location9 and gate are linked?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location3 and location4 exists, a link between location4 and location5 exists, bob is at shed, location2 and location3 are linked, location5 is linked to location6, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is located at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is usable, spanner3 can be used, spanner3 is located at location8, spanner4 can be used, spanner4 is at location5, spanner5 is currently at location7 and spanner5 is usable.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob walks from the shed to location1, then from location1 to location2, from location2 to location3, from location3 to location4, from location4 to location5, and from location5 to location6. At location5, Bob picks up spanner4, and at location6, he picks up spanner1. He then walks to location7, where he picks up spanner5, and proceeds to location8. At location8, Bob picks up both spanner3 and spanner2. From location8, he walks to location9 and then to the gate. Upon reaching the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately leading to the current state. In this state, are the following properties, which do not involve negations, True or False: a connection exists between location1 and location2, a connection exists between location2 and location3, a connection exists between location5 and location6, a connection exists between location6 and location7, a connection exists between location7 and location8, a connection exists between the shed and location1, location3 is connected to location4, location4 is connected to location5, location8 is connected to location9, and location9 is connected to the gate?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location3 and location4, and a connection between location4 and location5. Bob is currently at the shed. Additionally, location2 is connected to location3, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3 (which is not secured), nut4, and nut5. Furthermore, the shed is connected to location1. There are several usable spanners: spanner1 is at location6, spanner2 and spanner3 are at location8, spanner4 is at location5, and spanner5 is at location7."}
{"question_id": "1a8cbf2f-5e76-4063-8b1e-f8d8e8928b40", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_13", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, from location1 to location2, bob walks, bob picks up spanner3 from location2, bob walks to location3 from location2, from location3, bob picks up spanner5, bob picks up spanner1 from location3, bob walks from location3 to location4, from location4 to location5, bob walks, from location5, bob picks up spanner2 and bob walks to location6 from location5 to reach the current state. In this state, is it True or False that a link between location9 and gate exists?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then to location2, walks, collects spanner3 at location2, proceeds to location3 from location2, picks up spanner5 and spanner1 at location3, walks to location4 and then to location5, walks again, collects spanner2 at location5, and finally moves to location6 from location5, resulting in the current state. In this state, does a connection between location9 and the gate exist, True or False?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Furthermore, location5 is linked to location6, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, nut1 is present and unsecured, nut2 is also at the gate and loose, nut3 is at the gate and unsecured, nut4 is currently at the gate and unsecured, and nut5 is at the gate and loose. Additionally, the shed is connected to location1. Spanner1 is located at location3 and is in working condition, spanner2 is at location5 and usable, spanner3 is located at location2 and can be used, spanner4 is at location6 and usable, and spanner5 is functional and located at location3."}
{"question_id": "2f82ce01-6e12-4687-9b1b-52d7dac616d2", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_20", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: a link between gate and location5 exists, a link between gate and shed exists, a link between location1 and gate exists, a link between location1 and location2 exists, a link between location1 and location5 exists, a link between location1 and shed exists, a link between location2 and location5 exists, a link between location2 and location8 exists, a link between location3 and location1 exists, a link between location4 and location3 exists, a link between location4 and location7 exists, a link between location4 and location9 exists, a link between location5 and location8 exists, a link between location5 and shed exists, a link between location6 and location3 exists, a link between location7 and location1 exists, a link between location7 and location2 exists, a link between location7 and location3 exists, a link between location7 and location5 exists, a link between location7 and location6 exists, a link between location8 and gate exists, a link between location8 and location4 exists, a link between location8 and location5 exists, a link between location8 and location6 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between location9 and location1 exists, a link between location9 and location2 exists, a link between location9 and location3 exists, a link between location9 and location4 exists, a link between location9 and location7 exists, a link between location9 and location8 exists, a link between location9 and shed exists, a link between shed and gate exists, a link between shed and location4 exists, a link between shed and location7 exists, a link between shed and location9 exists, gate and location1 are linked, gate and location2 are linked, gate and location3 are linked, gate and location4 are linked, gate and location6 are linked, gate and location7 are linked, gate and location8 are linked, gate is linked to location9, location1 and location3 are linked, location1 and location4 are linked, location1 and location6 are linked, location1 and location7 are linked, location1 and location9 are linked, location1 is linked to location8, location2 and gate are linked, location2 and location6 are linked, location2 and location7 are linked, location2 is linked to location1, location2 is linked to location3, location2 is linked to location4, location2 is linked to location9, location2 is linked to shed, location3 and location6 are linked, location3 and location7 are linked, location3 and location8 are linked, location3 is linked to gate, location3 is linked to location2, location3 is linked to location4, location3 is linked to location5, location3 is linked to location9, location3 is linked to shed, location4 and gate are linked, location4 and location6 are linked, location4 and location8 are linked, location4 and shed are linked, location4 is linked to location1, location4 is linked to location2, location4 is linked to location5, location5 and location6 are linked, location5 and location7 are linked, location5 is linked to gate, location5 is linked to location1, location5 is linked to location2, location5 is linked to location3, location5 is linked to location4, location5 is linked to location9, location6 and location1 are linked, location6 and location4 are linked, location6 and location5 are linked, location6 and location7 are linked, location6 and location9 are linked, location6 and shed are linked, location6 is linked to gate, location6 is linked to location2, location6 is linked to location8, location7 and location4 are linked, location7 and location8 are linked, location7 and shed are linked, location7 is linked to gate, location7 is linked to location9, location8 and location2 are linked, location8 and location3 are linked, location8 and location7 are linked, location8 is linked to location1, location8 is linked to shed, location9 is linked to location5, location9 is linked to location6, shed and location3 are linked, shed and location6 are linked, shed is linked to location1, shed is linked to location2, shed is linked to location5 and shed is linked to location8?", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location3 and location4 exists, a link between location4 and location5 exists, bob is at shed, location2 and location3 are linked, location5 is linked to location6, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is located at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is usable, spanner3 can be used, spanner3 is located at location8, spanner4 can be used, spanner4 is at location5, spanner5 is currently at location7 and spanner5 is usable.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: a connection exists between gate and location5, a connection exists between gate and shed, a connection exists between location1 and gate, a connection exists between location1 and location2, a connection exists between location1 and location5, a connection exists between location1 and shed, a connection exists between location2 and location5, a connection exists between location2 and location8, a connection exists between location3 and location1, a connection exists between location4 and location3, a connection exists between location4 and location7, a connection exists between location4 and location9, a connection exists between location5 and location8, a connection exists between location5 and shed, a connection exists between location6 and location3, a connection exists between location7 and location1, a connection exists between location7 and location2, a connection exists between location7 and location3, a connection exists between location7 and location5, a connection exists between location7 and location6, a connection exists between location8 and gate, a connection exists between location8 and location4, a connection exists between location8 and location5, a connection exists between location8 and location6, a connection exists between location8 and location9, a connection exists between location9 and gate, a connection exists between location9 and location1, a connection exists between location9 and location2, a connection exists between location9 and location3, a connection exists between location9 and location4, a connection exists between location9 and location7, a connection exists between location9 and location8, a connection exists between location9 and shed, a connection exists between shed and gate, a connection exists between shed and location4, a connection exists between shed and location7, a connection exists between shed and location9, gate is connected to location1, gate is connected to location2, gate is connected to location3, gate is connected to location4, gate is connected to location6, gate is connected to location7, gate is connected to location8, gate is connected to location9, location1 is connected to location3, location1 is connected to location4, location1 is connected to location6, location1 is connected to location7, location1 is connected to location9, location1 is connected to location8, location2 is connected to gate, location2 is connected to location6, location2 is connected to location7, location2 is connected to location1, location2 is connected to location3, location2 is connected to location4, location2 is connected to location9, location2 is connected to shed, location3 is connected to location6, location3 is connected to location7, location3 is connected to location8, location3 is connected to gate, location3 is connected to location2, location3 is connected to location4, location3 is connected to location5, location3 is connected to location9, location3 is connected to shed, location4 is connected to gate, location4 is connected to location6, location4 is connected to location8, location4 is connected to shed, location4 is connected to location1, location4 is connected to location2, location4 is connected to location5, location5 is connected to location6, location5 is connected to location7, location5 is connected to gate, location5 is connected to location1, location5 is connected to location2, location5 is connected to location3, location5 is connected to location4, location5 is connected to location9, location6 is connected to location1, location6 is connected to location4, location6 is connected to location5, location6 is connected to location7, location6 is connected to location9, location6 is connected to shed, location6 is connected to gate, location6 is connected to location2, location6 is connected to location8, location7 is connected to location4, location7 is connected to location8, location7 is connected to shed, location7 is connected to gate, location7 is connected to location9, location8 is connected to location2, location8 is connected to location3, location8 is connected to location7, location8 is connected to location1, location8 is connected to shed, location9 is connected to location5, location9 is connected to location6, shed is connected to location3, shed is connected to location6, shed is connected to location1, shed is connected to location2, shed is connected to location5 and shed is connected to location8.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location3 and location4, and a connection between location4 and location5. Bob is currently at the shed. Additionally, location2 is connected to location3, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3 (which is not secured), nut4, and nut5. Furthermore, the shed is connected to location1. There are several usable spanners: spanner1 is at location6, spanner2 and spanner3 are at location8, spanner4 is at location5, and spanner5 is at location7."}
{"question_id": "0c942fd8-d520-4a68-bb06-34f37986bcec", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_13", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: bob is located at location1, nut1 is at gate, nut2 is located at gate, nut3 is located at gate, nut4 is at gate, nut5 is currently at gate, spanner1 is at location4, spanner2 is at location7, spanner3 is at location6, spanner4 is located at location1 and spanner5 is currently at location1?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob moves from the shed to location1 to reach the current state. In this state, are all of the following properties of the state that do not involve negations True or False: bob is currently at location1, nut1 is located at the gate, nut2 is at the gate, nut3 is at the gate, nut4 is located at the gate, nut5 is at the gate, spanner1 is located at location4, spanner2 is at location7, spanner3 is at location6, spanner4 is currently at location1 and spanner5 is at location1?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 is connected to location2, location2 is connected to location3, and location3 is linked to location4. Additionally, location6 is connected to location7, location7 is connected to location8, location8 is connected to location9, and location9 is linked to the gate. At the gate, nut1 is present and unsecured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1, which is usable, is located at location4. Spanner2, which is functional, is currently at location7. Spanner3, also functional, is at location6. Spanner4, which is functional, and spanner5, which is usable, are both located at location1."}
{"question_id": "6f5b2159-4ef8-4b04-90fe-f54560757aeb", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_3", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: bob is not carrying spanner1, bob is not carrying spanner2, bob is not carrying spanner4, nut1 is not tightened, nut2 is not tightened, nut4 is not tightened, nut5 is not tightened, spanner3 is not carried by bob, spanner5 is not carried by bob and tightening of nut3 is incomplete?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob moves from the shed to location1 to reach the current state. In this state, are all of the following properties that involve negations True or False: bob does not have spanner1, bob does not have spanner2, bob does not have spanner4, nut1 is not tightened, nut2 is not tightened, nut4 is not tightened, nut5 is not tightened, bob is not holding spanner3, bob is not holding spanner5, and the tightening of nut3 is not complete?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Furthermore, location5 is linked to location6, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, nut1 is present and unsecured, nut2 is also at the gate and loose, nut3 is located at the gate and unsecured, nut4 is currently at the gate and unsecured, and nut5 is at the gate and loose. The shed is connected to location1. Spanner1 is located at location3 and is in working condition, spanner2 is at location5 and usable, spanner3 is located at location2 and can be used, spanner4 is at location6 and usable, and spanner5 is functional and located at location3."}
{"question_id": "d182d98a-638c-4296-9860-d671e9ac5276", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_22", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks from location1 to location2, spanner4 is picked up by bob from location2, from location2, bob picks up spanner3, bob walks from location2 to location3, from location3 to location4, bob walks, from location4 to location5, bob walks, from location5 to location6, bob walks, bob picks up spanner5 from location6, bob picks up spanner2 from location6, bob walks from location6 to location7, bob walks to location8 from location7, spanner1 is picked up by bob from location8, bob walks to location9 from location8, bob walks to gate from location9, bob tightens nut1 with spanner5 at gate, nut2 is tightened by bob using spanner4 at gate, nut3 is tightened by bob using spanner3 at gate and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: a link between gate and location4 does not exist, a link between gate and shed does not exist, a link between location1 and location2 does not exist, a link between location1 and location3 does not exist, a link between location1 and location4 does not exist, a link between location1 and location7 does not exist, a link between location1 and shed does not exist, a link between location2 and location6 does not exist, a link between location3 and location2 does not exist, a link between location3 and location4 does not exist, a link between location3 and location7 does not exist, a link between location3 and shed does not exist, a link between location4 and location9 does not exist, a link between location5 and location4 does not exist, a link between location5 and location7 does not exist, a link between location5 and location8 does not exist, a link between location6 and location4 does not exist, a link between location6 and location8 does not exist, a link between location6 and shed does not exist, a link between location7 and location2 does not exist, a link between location7 and location3 does not exist, a link between location7 and location6 does not exist, a link between location7 and location9 does not exist, a link between location7 and shed does not exist, a link between location8 and location6 does not exist, a link between location9 and location1 does not exist, a link between location9 and location2 does not exist, a link between location9 and location7 does not exist, a link between shed and location3 does not exist, a link between shed and location8 does not exist, a link between shed and location9 does not exist, gate and location1 are not linked, gate and location2 are not linked, gate and location3 are not linked, gate and location5 are not linked, gate and location6 are not linked, gate and location7 are not linked, gate and location8 are not linked, gate and location9 are not linked, location1 is not linked to gate, location1 is not linked to location5, location1 is not linked to location6, location1 is not linked to location8, location1 is not linked to location9, location2 and location1 are not linked, location2 and location4 are not linked, location2 and location7 are not linked, location2 and location8 are not linked, location2 and shed are not linked, location2 is not linked to gate, location2 is not linked to location3, location2 is not linked to location5, location2 is not linked to location9, location3 and location1 are not linked, location3 and location8 are not linked, location3 and location9 are not linked, location3 is not linked to gate, location3 is not linked to location5, location3 is not linked to location6, location4 and gate are not linked, location4 and location1 are not linked, location4 and location3 are not linked, location4 and location5 are not linked, location4 and location6 are not linked, location4 and location7 are not linked, location4 and shed are not linked, location4 is not linked to location2, location4 is not linked to location8, location5 and location2 are not linked, location5 is not linked to gate, location5 is not linked to location1, location5 is not linked to location3, location5 is not linked to location6, location5 is not linked to location9, location5 is not linked to shed, location6 and location3 are not linked, location6 and location5 are not linked, location6 and location7 are not linked, location6 and location9 are not linked, location6 is not linked to gate, location6 is not linked to location1, location6 is not linked to location2, location7 and location1 are not linked, location7 and location4 are not linked, location7 and location8 are not linked, location7 is not linked to gate, location7 is not linked to location5, location8 and location2 are not linked, location8 and location5 are not linked, location8 and location9 are not linked, location8 and shed are not linked, location8 is not linked to gate, location8 is not linked to location1, location8 is not linked to location3, location8 is not linked to location4, location8 is not linked to location7, location9 and location3 are not linked, location9 and location4 are not linked, location9 and location6 are not linked, location9 and location8 are not linked, location9 is not linked to gate, location9 is not linked to location5, location9 is not linked to shed, shed and location1 are not linked, shed and location4 are not linked, shed is not linked to gate, shed is not linked to location2, shed is not linked to location5, shed is not linked to location6 and shed is not linked to location7?", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: Bob walks from the shed to location1, then from location1 to location2, picks up spanner4 and spanner3 from location2, walks to location3, then to location4, location5, location6, picks up spanner5 and spanner2 from location6, walks to location7, then to location8, picks up spanner1 from location8, walks to location9, then to the gate, and uses spanner5, spanner4, spanner3, and spanner2 to tighten nut1, nut2, nut3, and nut4 at the gate, respectively, to reach the current state. In this state, are the following properties that involve negations True or False: There is no link between gate and location4, no link between gate and shed, no link between location1 and location2, no link between location1 and location3, no link between location1 and location4, no link between location1 and location7, no link between location1 and shed, no link between location2 and location6, no link between location3 and location2, no link between location3 and location4, no link between location3 and location7, no link between location3 and shed, no link between location4 and location9, no link between location5 and location4, no link between location5 and location7, no link between location5 and location8, no link between location6 and location4, no link between location6 and location8, no link between location6 and shed, no link between location7 and location2, no link between location7 and location3, no link between location7 and location6, no link between location7 and location9, no link between location7 and shed, no link between location8 and location6, no link between location9 and location1, no link between location9 and location2, no link between location9 and location7, no link between shed and location3, no link between shed and location8, no link between shed and location9, gate and location1 are not connected, gate and location2 are not connected, gate and location3 are not connected, gate and location5 are not connected, gate and location6 are not connected, gate and location7 are not connected, gate and location8 are not connected, gate and location9 are not connected, location1 is not connected to gate, location1 is not connected to location5, location1 is not connected to location6, location1 is not connected to location8, location1 is not connected to location9, location2 and location1 are not connected, location2 and location4 are not connected, location2 and location7 are not connected, location2 and location8 are not connected, location2 and shed are not connected, location2 is not connected to gate, location2 is not connected to location3, location2 is not connected to location5, location2 is not connected to location9, location3 and location1 are not connected, location3 and location8 are not connected, location3 and location9 are not connected, location3 is not connected to gate, location3 is not connected to location5, location3 is not connected to location6, location4 and gate are not connected, location4 and location1 are not connected, location4 and location3 are not connected, location4 and location5 are not connected, location4 and location6 are not connected, location4 and location7 are not connected, location4 and shed are not connected, location4 is not connected to location2, location4 is not connected to location8, location5 and location2 are not connected, location5 is not connected to gate, location5 is not connected to location1, location5 is not connected to location3, location5 is not connected to location6, location5 is not connected to location9, location5 is not connected to shed, location6 and location3 are not connected, location6 and location5 are not connected, location6 and location7 are not connected, location6 and location9 are not connected, location6 is not connected to gate, location6 is not connected to location1, location6 is not connected to location2, location7 and location1 are not connected, location7 and location4 are not connected, location7 and location8 are not connected, location7 is not connected to gate, location7 is not connected to location5, location8 and location2 are not connected, location8 and location5 are not connected, location8 and location9 are not connected, location8 and shed are not connected, location8 is not connected to gate, location8 is not connected to location1, location8 is not connected to location3, location8 is not connected to location4, location8 is not connected to location7, location9 and location3 are not connected, location9 and location4 are not connected, location9 and location6 are not connected, location9 and location8 are not connected, location9 is not connected to gate, location9 is not connected to location5, location9 is not connected to shed, shed and location1 are not connected, shed and location4 are not connected, shed is not connected to gate, shed is not connected to location2, shed is not connected to location5, shed is not connected to location6, and shed is not connected to location7?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, and a connection also exists between location3 and location4. Additionally, a connection is present between location5 and location6. Bob is currently situated at the shed. Furthermore, location4 and location5 are interconnected, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. Location9, in turn, is linked to the gate. At the gate, nut1 is located and is not secured. Similarly, nut2 is currently at the gate and is loose, nut3 is also located at the gate and is loose, nut4 is at the gate and is not secured, and nut5 is at the gate and is not secured. The shed is connected to location1. Spanner1 is functional and is located at location8. Spanner2 is situated at location6 and is usable. Spanner3 is functional and is currently at location2, spanner4 is also functional and is located at location2, and spanner5 is functional and is located at location6."}
{"question_id": "14bd77b2-1bea-4b5a-afd5-84e47b429ef9", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: nut1 is loose, nut2 is loose, nut3 is loose, nut4 is loose, nut5 is loose, spanner1 is usable, spanner2 is usable, spanner3 can be used, spanner4 is functional and spanner5 is usable?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: nut1 is loose, nut2 is loose, nut3 is loose, nut4 is loose, nut5 is loose, spanner1 is in working order, spanner2 is in working order, spanner3 is usable, spanner4 is operational and spanner5 is in working order?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present but not secured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1, which is usable, is located at location4. Spanner2, which is functional, is currently at location7. Spanner3, also functional, is at location6. Spanner4, which is functional, and spanner5, which is usable, are both located at location1."}
{"question_id": "450293d5-68f0-4640-aa14-71a7c8827f27", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks from location1 to location2, spanner3 is picked up by bob from location2, from location2 to location3, bob walks, bob picks up spanner5 from location3, spanner1 is picked up by bob from location3, bob walks to location4 from location3, bob walks from location4 to location5, spanner2 is picked up by bob from location5 and bob walks from location5 to location6 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: bob is carrying spanner1, bob is carrying spanner2, spanner3 is carried by bob and spanner5 is carried by bob?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: Bob moves from the shed to location1, then walks to location2, picks up spanner3 at location2, proceeds to location3, collects spanner5 and spanner1 at location3, walks to location4 and then to location5, where he picks up spanner2, and finally moves to location6, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: Bob is holding spanner1, Bob is holding spanner2, Bob has spanner3, and Bob has spanner5?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Furthermore, location5 is linked to location6, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, nut1 is present but not secured, nut2 is also at the gate and is loose, nut3 is at the gate but not secured, nut4 is currently at the gate and not secured, and nut5 is at the gate and loose. The shed is connected to location1. Spanner1 is located at location3 and is in working condition, spanner2 is at location5 and can be used, spanner3 is at location2 and functional, spanner4 is at location6 and usable, and spanner5 is at location3 and functional."}
{"question_id": "69605a7f-5377-497a-96ad-0bf38713478c", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, is it True or False that tightening of nut1 is complete?", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, is the tightening of nut1 complete, True or False?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, and a connection also exists between location3 and location4. Additionally, a connection is present between location5 and location6. Bob is currently situated at the shed. Furthermore, location4 and location5 are interconnected, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. Location9, in turn, is linked to the gate. At the gate, the following items are located: nut1, which is not secured, nut2, which is loose, nut3, also loose, nut4, which is not secured, and nut5, also not secured. The shed is connected to location1. The following spanners are available for use: spanner1, which is located at location8, spanner2, which is usable and located at location6, spanner3, which is currently at location2, spanner4, also at location2, and spanner5, which is functional and located at location6."}
{"question_id": "c247ef81-3bf8-41ee-902c-b878ddfcdaae", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_14", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, from location2, bob picks up spanner4, bob picks up spanner3 from location2, bob walks to location3 from location2, from location3 to location4, bob walks, bob walks to location5 from location4, from location5 to location6, bob walks, bob picks up spanner5 from location6 and spanner2 is picked up by bob from location6 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: bob is at location4, bob is at location8, bob is at location9, bob is currently at location5, bob is currently at shed, bob is located at gate, bob is located at location1, bob is located at location2, bob is located at location3, bob is located at location6, bob is located at location7, nut1 is at location3, nut1 is at location4, nut1 is at location5, nut1 is at location8, nut1 is currently at location1, nut1 is currently at location2, nut1 is currently at location6, nut1 is currently at location9, nut1 is currently at shed, nut1 is located at gate, nut1 is located at location7, nut2 is at location1, nut2 is at location2, nut2 is at location5, nut2 is at location8, nut2 is at shed, nut2 is currently at location3, nut2 is currently at location7, nut2 is currently at location9, nut2 is located at gate, nut2 is located at location4, nut2 is located at location6, nut3 is at gate, nut3 is at location7, nut3 is at location8, nut3 is at location9, nut3 is currently at location1, nut3 is currently at location2, nut3 is currently at location5, nut3 is currently at location6, nut3 is currently at shed, nut3 is located at location3, nut3 is located at location4, nut4 is at location2, nut4 is at location4, nut4 is at location6, nut4 is at location7, nut4 is currently at location3, nut4 is located at gate, nut4 is located at location1, nut4 is located at location5, nut4 is located at location8, nut4 is located at location9, nut4 is located at shed, nut5 is at gate, nut5 is at location3, nut5 is at location4, nut5 is at location5, nut5 is at location9, nut5 is currently at location1, nut5 is currently at location6, nut5 is currently at location7, nut5 is currently at shed, nut5 is located at location2, nut5 is located at location8, spanner1 is at gate, spanner1 is at location1, spanner1 is at location6, spanner1 is at location8, spanner1 is currently at location3, spanner1 is currently at location5, spanner1 is currently at location7, spanner1 is currently at location9, spanner1 is currently at shed, spanner1 is located at location2, spanner1 is located at location4, spanner2 is at location1, spanner2 is at location2, spanner2 is at location3, spanner2 is at location6, spanner2 is at shed, spanner2 is currently at location4, spanner2 is currently at location5, spanner2 is currently at location8, spanner2 is currently at location9, spanner2 is located at gate, spanner2 is located at location7, spanner3 is at location2, spanner3 is at location3, spanner3 is at location5, spanner3 is at location7, spanner3 is at location8, spanner3 is currently at gate, spanner3 is currently at location1, spanner3 is currently at location4, spanner3 is currently at location6, spanner3 is currently at location9, spanner3 is located at shed, spanner4 is at gate, spanner4 is at location1, spanner4 is at location3, spanner4 is at location5, spanner4 is at location6, spanner4 is at location9, spanner4 is currently at location2, spanner4 is currently at location4, spanner4 is located at location7, spanner4 is located at location8, spanner4 is located at shed, spanner5 is at location3, spanner5 is at location6, spanner5 is currently at location1, spanner5 is currently at location2, spanner5 is currently at location4, spanner5 is currently at location5, spanner5 is currently at location8, spanner5 is located at gate, spanner5 is located at location7, spanner5 is located at location9 and spanner5 is located at shed?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob moves from the shed to location1, then from location1 to location2, where he picks up spanner4 and spanner3, then proceeds to location3, then to location4, then to location5, and finally to location6, where he picks up spanner5 and spanner2 to reach the current state. In this state, are the following properties that do not involve negations True or False: is bob at location4, is bob at location8, is bob at location9, is bob currently at location5, is bob currently at the shed, is bob located at the gate, is bob located at location1, is bob located at location2, is bob located at location3, is bob located at location6, is bob located at location7, is nut1 at location3, is nut1 at location4, is nut1 at location5, is nut1 at location8, is nut1 currently at location1, is nut1 currently at location2, is nut1 currently at location6, is nut1 currently at location9, is nut1 currently at the shed, is nut1 located at the gate, is nut1 located at location7, is nut2 at location1, is nut2 at location2, is nut2 at location5, is nut2 at location8, is nut2 at the shed, is nut2 currently at location3, is nut2 currently at location7, is nut2 currently at location9, is nut2 located at the gate, is nut2 located at location4, is nut2 located at location6, is nut3 at the gate, is nut3 at location7, is nut3 at location8, is nut3 at location9, is nut3 currently at location1, is nut3 currently at location2, is nut3 currently at location5, is nut3 currently at location6, is nut3 currently at the shed, is nut3 located at location3, is nut3 located at location4, is nut4 at location2, is nut4 at location4, is nut4 at location6, is nut4 at location7, is nut4 currently at location3, is nut4 located at the gate, is nut4 located at location1, is nut4 located at location5, is nut4 located at location8, is nut4 located at location9, is nut4 located at the shed, is nut5 at the gate, is nut5 at location3, is nut5 at location4, is nut5 at location5, is nut5 at location9, is nut5 currently at location1, is nut5 currently at location6, is nut5 currently at location7, is nut5 currently at the shed, is nut5 located at location2, is nut5 located at location8, is spanner1 at the gate, is spanner1 at location1, is spanner1 at location6, is spanner1 at location8, is spanner1 currently at location3, is spanner1 currently at location5, is spanner1 currently at location7, is spanner1 currently at location9, is spanner1 currently at the shed, is spanner1 located at location2, is spanner1 located at location4, is spanner2 at location1, is spanner2 at location2, is spanner2 at location3, is spanner2 at location6, is spanner2 at the shed, is spanner2 currently at location4, is spanner2 currently at location5, is spanner2 currently at location8, is spanner2 currently at location9, is spanner2 located at the gate, is spanner2 located at location7, is spanner3 at location2, is spanner3 at location3, is spanner3 at location5, is spanner3 at location7, is spanner3 at location8, is spanner3 currently at the gate, is spanner3 currently at location1, is spanner3 currently at location4, is spanner3 currently at location6, is spanner3 currently at location9, is spanner3 located at the shed, is spanner4 at the gate, is spanner4 at location1, is spanner4 at location3, is spanner4 at location5, is spanner4 at location6, is spanner4 at location9, is spanner4 currently at location2, is spanner4 currently at location4, is spanner4 located at location7, is spanner4 located at location8, is spanner4 located at the shed, is spanner5 at location3, is spanner5 at location6, is spanner5 currently at location1, is spanner5 currently at location2, is spanner5 currently at location4, is spanner5 currently at location5, is spanner5 currently at location8, is spanner5 located at the gate, is spanner5 located at location7, is spanner5 located at location9, and is spanner5 located at the shed?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, and a connection also exists between location3 and location4. Additionally, a connection is present between location5 and location6. Bob is currently situated at the shed. Furthermore, location4 and location5 are interconnected, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. Location9, in turn, is linked to the gate. At the gate, the following items are located: nut1, which is not secured, nut2, which is loose, nut3, also loose, nut4, which is not secured, and nut5, also not secured. The shed is connected to location1. The following spanners are available for use: spanner1, which is located at location8, spanner2, which is usable and located at location6, spanner3, which is currently at location2, spanner4, also at location2, and spanner5, which is functional and located at location6."}
{"question_id": "f78719f3-dbdb-479a-a816-20679cc184ee", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks from location1 to location2, from location2, bob picks up spanner4, bob picks up spanner3 from location2, from location2 to location3, bob walks, bob walks from location3 to location4, bob walks to location5 from location4, from location5 to location6, bob walks, bob picks up spanner5 from location6 and from location6, bob picks up spanner2 to reach the current state. In this state, is it True or False that bob is carrying spanner4?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then from location1 to location2, where he picks up spanner4 and spanner3. He then proceeds to walk from location2 to location3, from location3 to location4, from location4 to location5, and from location5 to location6. At location6, he picks up spanner5 and spanner2, ultimately reaching the current state. In this state, is it True or False that bob is carrying spanner4?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, and a connection also exists between location3 and location4. Additionally, a connection is present between location5 and location6. Bob is currently situated at the shed. Furthermore, location4 and location5 are interconnected, location6 is connected to location7, location7 is linked to location8, location8 and location9 are connected, and location9 is linked to the gate. At the gate, nut1 is located and is not secured. Also at the gate, nut2 is currently present and is loose, nut3 is located and is loose, nut4 is currently present and is not secured, and nut5 is currently present and is not secured. The shed and location1 are interconnected. Spanner1 is functional and is located at location8. Spanner2 is located at location6 and is usable. Spanner3 is functional and is currently at location2. Spanner4 is functional and is located at location2. Spanner5 is functional and is located at location6."}
{"question_id": "5d396928-0890-49eb-874f-1b1874303969", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks to location2 from location1, bob picks up spanner4 from location2, bob picks up spanner3 from location2, bob walks from location2 to location3, bob walks to location4 from location3, bob walks to location5 from location4, from location5 to location6, bob walks, spanner5 is picked up by bob from location6, spanner2 is picked up by bob from location6, from location6 to location7, bob walks, bob walks to location8 from location7, spanner1 is picked up by bob from location8, bob walks from location8 to location9, from location9 to gate, bob walks, nut1 is tightened by bob using spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, nut3 is tightened by bob using spanner3 at gate and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, is it True or False that spanner2 is not functional?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then proceeds to location2, where he collects spanner4 and spanner3. From location2, Bob heads to location3, then to location4, followed by location5, and subsequently location6. At location6, Bob picks up spanner5 and spanner2. He then walks to location7, followed by location8, where he collects spanner1. Bob then proceeds to location9 and finally reaches the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, is it True or False that spanner2 is not functional?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location2 and location3, a connection between location3 and location4, a connection between location5 and location6, bob is currently at the shed, location4 and location5 are connected, location6 is connected to location7, location7 is connected to location8, location8 and location9 are connected, location9 is connected to the gate, nut1 is situated at the gate and is unsecured, nut2 is currently at the gate and is loose, nut3 is located at the gate and is loose, nut4 is currently at the gate and is unsecured, nut5 is currently at the gate and is unsecured, the shed and location1 are connected, spanner1 is functional and is located at location8, spanner2 is located at location6 and is usable, spanner3 is functional and is currently at location2, spanner4 is functional and is located at location2, and spanner5 is functional and is located at location6."}
{"question_id": "d1647b7e-d685-42c7-892a-57fb4f919179", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_13", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks from location1 to location2, bob picks up spanner3 from location2, bob walks to location3 from location2, bob picks up spanner5 from location3, from location3, bob picks up spanner1, from location3 to location4, bob walks, from location4 to location5, bob walks, bob picks up spanner2 from location5, from location5 to location6, bob walks, spanner4 is picked up by bob from location6, bob walks from location6 to location7, bob walks from location7 to location8, bob walks to location9 from location8, bob walks to gate from location9, at gate, bob uses spanner5 to tighten nut1, at gate, bob uses spanner4 to tighten nut2, nut3 is tightened by bob using spanner3 at gate and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: bob is at gate, nut1 is at gate, nut2 is currently at gate, nut3 is located at gate, nut4 is at gate and nut5 is at gate?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then from location1 to location2, where he collects spanner3. He proceeds to location3 from location2, picks up spanner5 and spanner1 at location3, and then walks to location4. From location4, he moves to location5, where he collects spanner2, and then walks to location6, where he picks up spanner4. Bob then walks from location6 to location7, then to location8, and from location8 to location9, before finally walking to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: Bob is at the gate, nut1 is at the gate, nut2 is at the gate, nut3 is at the gate, nut4 is at the gate, and nut5 is at the gate?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Furthermore, location5 is linked to location6, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, nut1 is present and unsecured, nut2 is also at the gate and loose, nut3 is located at the gate and unsecured, nut4 is currently at the gate and unsecured, and nut5 is at the gate and loose. The shed is connected to location1. Spanner1 is located at location3 and is in working condition, spanner2 is at location5 and can be used, spanner3 is located at location2 and is functional, spanner4 is at location6 and usable, and spanner5 is located at location3 and functional."}
{"question_id": "477e17b4-1c40-4062-94c2-eb6e3d66b313", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_22", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, from location1, bob picks up spanner5, from location1, bob picks up spanner4, from location1 to location2, bob walks, from location2 to location3, bob walks, bob walks from location3 to location4, spanner1 is picked up by bob from location4, from location4 to location5, bob walks, bob walks from location5 to location6, bob picks up spanner3 from location6, bob walks from location6 to location7, spanner2 is picked up by bob from location7, from location7 to location8, bob walks, bob walks to location9 from location8, from location9 to gate, bob walks, at gate, bob uses spanner5 to tighten nut1, nut2 is tightened by bob using spanner4 at gate, at gate, bob uses spanner3 to tighten nut3 and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: a link between gate and location1 does not exist, a link between gate and location4 does not exist, a link between location1 and location4 does not exist, a link between location1 and location5 does not exist, a link between location1 and location6 does not exist, a link between location1 and location7 does not exist, a link between location1 and location9 does not exist, a link between location2 and gate does not exist, a link between location2 and location3 does not exist, a link between location2 and location7 does not exist, a link between location3 and location1 does not exist, a link between location3 and location2 does not exist, a link between location3 and location4 does not exist, a link between location3 and location8 does not exist, a link between location5 and gate does not exist, a link between location5 and location6 does not exist, a link between location5 and location8 does not exist, a link between location6 and gate does not exist, a link between location6 and location3 does not exist, a link between location6 and location5 does not exist, a link between location6 and location7 does not exist, a link between location6 and location8 does not exist, a link between location6 and shed does not exist, a link between location7 and location1 does not exist, a link between location7 and location5 does not exist, a link between location7 and location6 does not exist, a link between location7 and location9 does not exist, a link between location8 and gate does not exist, a link between location8 and location1 does not exist, a link between location8 and location2 does not exist, a link between location8 and location7 does not exist, a link between location8 and location9 does not exist, a link between location8 and shed does not exist, a link between location9 and location5 does not exist, a link between location9 and location7 does not exist, a link between shed and location3 does not exist, a link between shed and location4 does not exist, a link between shed and location6 does not exist, a link between shed and location7 does not exist, gate and location7 are not linked, gate and location8 are not linked, gate and shed are not linked, gate is not linked to location2, gate is not linked to location3, gate is not linked to location5, gate is not linked to location6, gate is not linked to location9, location1 and gate are not linked, location1 and location8 are not linked, location1 is not linked to location2, location1 is not linked to location3, location1 is not linked to shed, location2 and location5 are not linked, location2 and location6 are not linked, location2 and location8 are not linked, location2 and shed are not linked, location2 is not linked to location1, location2 is not linked to location4, location2 is not linked to location9, location3 and location5 are not linked, location3 and location7 are not linked, location3 and location9 are not linked, location3 is not linked to gate, location3 is not linked to location6, location3 is not linked to shed, location4 and gate are not linked, location4 and location1 are not linked, location4 and location5 are not linked, location4 and location6 are not linked, location4 and location8 are not linked, location4 and location9 are not linked, location4 and shed are not linked, location4 is not linked to location2, location4 is not linked to location3, location4 is not linked to location7, location5 and location1 are not linked, location5 and location3 are not linked, location5 and location9 are not linked, location5 is not linked to location2, location5 is not linked to location4, location5 is not linked to location7, location5 is not linked to shed, location6 and location1 are not linked, location6 and location4 are not linked, location6 is not linked to location2, location6 is not linked to location9, location7 and location3 are not linked, location7 and location8 are not linked, location7 and shed are not linked, location7 is not linked to gate, location7 is not linked to location2, location7 is not linked to location4, location8 and location3 are not linked, location8 and location6 are not linked, location8 is not linked to location4, location8 is not linked to location5, location9 and gate are not linked, location9 and location2 are not linked, location9 and location8 are not linked, location9 is not linked to location1, location9 is not linked to location3, location9 is not linked to location4, location9 is not linked to location6, location9 is not linked to shed, shed and location2 are not linked, shed and location8 are not linked, shed is not linked to gate, shed is not linked to location1, shed is not linked to location5 and shed is not linked to location9?", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob walks from the shed to location1, then picks up spanner5 and spanner4 from location1. He then walks from location1 to location2, and from location2 to location3, and then from location3 to location4. At location4, Bob picks up spanner1. He then walks from location4 to location5, from location5 to location6, and picks up spanner3 from location6. Next, Bob walks from location6 to location7, picks up spanner2 from location7, and walks from location7 to location8. He then walks from location8 to location9 and from location9 to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, are the following properties that involve negations True or False: There is no link between gate and location1, no link between gate and location4, no link between location1 and location4, no link between location1 and location5, no link between location1 and location6, no link between location1 and location7, no link between location1 and location9, no link between location2 and gate, no link between location2 and location3, no link between location2 and location7, no link between location3 and location1, no link between location3 and location2, no link between location3 and location4, no link between location3 and location8, no link between location5 and gate, no link between location5 and location6, no link between location5 and location8, no link between location6 and gate, no link between location6 and location3, no link between location6 and location5, no link between location6 and location7, no link between location6 and location8, no link between location6 and the shed, no link between location7 and location1, no link between location7 and location5, no link between location7 and location6, no link between location7 and location9, no link between location8 and gate, no link between location8 and location1, no link between location8 and location2, no link between location8 and location7, no link between location8 and location9, no link between location8 and the shed, no link between location9 and location5, no link between location9 and location7, no link between the shed and location3, no link between the shed and location4, no link between the shed and location6, no link between the shed and location7, gate and location7 are not connected, gate and location8 are not connected, gate and the shed are not connected, gate is not connected to location2, gate is not connected to location3, gate is not connected to location5, gate is not connected to location6, gate is not connected to location9, location1 and gate are not connected, location1 and location8 are not connected, location1 is not connected to location2, location1 is not connected to location3, location1 is not connected to the shed, location2 and location5 are not connected, location2 and location6 are not connected, location2 and location8 are not connected, location2 and the shed are not connected, location2 is not connected to location1, location2 is not connected to location4, location2 is not connected to location9, location3 and location5 are not connected, location3 and location7 are not connected, location3 and location9 are not connected, location3 is not connected to gate, location3 is not connected to location6, location3 is not connected to the shed, location4 and gate are not connected, location4 and location1 are not connected, location4 and location5 are not connected, location4 and location6 are not connected, location4 and location8 are not connected, location4 and location9 are not connected, location4 and the shed are not connected, location4 is not connected to location2, location4 is not connected to location3, location4 is not connected to location7, location5 and location1 are not connected, location5 and location3 are not connected, location5 and location9 are not connected, location5 is not connected to location2, location5 is not connected to location4, location5 is not connected to location7, location5 is not connected to the shed, location6 and location1 are not connected, location6 and location4 are not connected, location6 is not connected to location2, location6 is not connected to location9, location7 and location3 are not connected, location7 and location8 are not connected, location7 and the shed are not connected, location7 is not connected to gate, location7 is not connected to location2, location7 is not connected to location4, location8 and location3 are not connected, location8 and location6 are not connected, location8 is not connected to location4, location8 is not connected to location5, location9 and gate are not connected, location9 and location2 are not connected, location9 and location8 are not connected, location9 is not connected to location1, location9 is not connected to location3, location9 is not connected to location4, location9 is not connected to location6, location9 is not connected to the shed, the shed and location2 are not connected, the shed and location8 are not connected, the shed is not connected to gate, the shed is not connected to location1, the shed is not connected to location5, and the shed is not connected to location9.", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 is connected to location2, and location2 is linked to location3, which in turn is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present and unsecured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1, which is usable, is located at location4. Spanner2, which is functional, is currently at location7. Spanner3, also functional, is at location6. Spanner4, which is functional, and spanner5, which is usable, are both located at location1."}
{"question_id": "b9bb7884-a637-4aab-a64b-344c4ca0c459", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_16", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, spanner3 is picked up by bob from location2, from location2 to location3, bob walks, spanner5 is picked up by bob from location3, from location3, bob picks up spanner1, bob walks from location3 to location4, bob walks from location4 to location5, bob picks up spanner2 from location5 and from location5 to location6, bob walks to reach the current state. In this state, is it True or False that location8 and location9 are not linked?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then to location2, where he collects spanner3. From location2, Bob proceeds to location3, picks up spanner5, and also collects spanner1. He then walks from location3 to location4, and from location4 to location5, where he picks up spanner2. Finally, Bob walks from location5 to location6, reaching the current state. In this state, is it True or False that location8 and location9 are not connected?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Furthermore, location5 is linked to location6, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, nut1 is present but not secured, nut2 is also at the gate and is loose, nut3 is at the gate but not secured, nut4 is currently at the gate and not secured, and nut5 is at the gate and loose. The shed is connected to location1. Spanner1 is located at location3 and is in working condition, spanner2 is at location5 and can be used, spanner3 is at location2 and functional, spanner4 is at location6 and usable, and spanner5 is at location3 and functional."}
{"question_id": "24e4d73a-ba2c-4343-b56c-7b6b16c3080e", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks to location2 from location1, spanner4 is picked up by bob from location2, bob picks up spanner3 from location2, bob walks to location3 from location2, from location3 to location4, bob walks, bob walks from location4 to location5, from location5 to location6, bob walks, spanner5 is picked up by bob from location6 and bob picks up spanner2 from location6 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: bob is carrying spanner1, nut2 is tightened, nut3 is tightened, nut4 is tightened, nut5 is tightened, spanner2 is carried by bob, spanner3 is carried by bob, spanner4 is carried by bob, spanner5 is carried by bob and tightening of nut1 is complete?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: bob moves from the shed to location1, then to location2, where he collects spanner4 and spanner3, before proceeding to location3, then location4, and subsequently location5 and location6, where he picks up spanner5 and spanner2, ultimately reaching the current state. In this state, are the following properties, which do not involve negations, True or False: bob has spanner1, nut2 is tightened, nut3 is tightened, nut4 is tightened, nut5 is tightened, bob is carrying spanner2, bob is carrying spanner3, bob is carrying spanner4, bob is carrying spanner5, and the tightening of nut1 is complete?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, and a connection also exists between location3 and location4. Additionally, a connection is present between location5 and location6. Bob is currently situated at the shed. Furthermore, location4 and location5 are interconnected, location6 is connected to location7, location7 is linked to location8, location8 and location9 are connected, and location9 is linked to the gate. At the gate, nut1 is located and is not secured, nut2 is currently present and is loose, nut3 is also located at the gate and is loose, nut4 is currently at the gate and is not secured, and nut5 is currently at the gate and is not secured. The shed and location1 are interconnected. Spanner1 is functional and is located at location8, spanner2 is located at location6 and is usable, spanner3 is functional and is currently at location2, spanner4 is functional and is located at location2, and spanner5 is functional and is located at location6."}
{"question_id": "3f41dfb1-0af1-4fa2-b71b-b6f65b688057", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_16", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks to location2 from location1, from location2, bob picks up spanner3, from location2 to location3, bob walks, from location3, bob picks up spanner5, spanner1 is picked up by bob from location3, bob walks to location4 from location3, bob walks from location4 to location5, from location5, bob picks up spanner2 and bob walks from location5 to location6 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: bob is not at location1, bob is not at location3, bob is not at location4, bob is not at location6, bob is not at location7, bob is not at location9, bob is not at shed, bob is not currently at gate, bob is not currently at location2, bob is not currently at location8, bob is not located at location5, nut1 is not at location3, nut1 is not at location4, nut1 is not at location7, nut1 is not currently at location2, nut1 is not currently at location5, nut1 is not currently at location6, nut1 is not currently at location8, nut1 is not currently at location9, nut1 is not currently at shed, nut1 is not located at gate, nut1 is not located at location1, nut2 is not at location2, nut2 is not at location3, nut2 is not at location5, nut2 is not at location6, nut2 is not at location7, nut2 is not currently at location1, nut2 is not currently at location8, nut2 is not located at gate, nut2 is not located at location4, nut2 is not located at location9, nut2 is not located at shed, nut3 is not at location2, nut3 is not at location9, nut3 is not currently at location1, nut3 is not currently at location3, nut3 is not currently at location5, nut3 is not currently at shed, nut3 is not located at gate, nut3 is not located at location4, nut3 is not located at location6, nut3 is not located at location7, nut3 is not located at location8, nut4 is not at location1, nut4 is not at location2, nut4 is not at location5, nut4 is not at location6, nut4 is not at location8, nut4 is not at shed, nut4 is not currently at gate, nut4 is not currently at location3, nut4 is not located at location4, nut4 is not located at location7, nut4 is not located at location9, nut5 is not at location1, nut5 is not at location3, nut5 is not currently at location2, nut5 is not currently at location6, nut5 is not currently at location9, nut5 is not currently at shed, nut5 is not located at gate, nut5 is not located at location4, nut5 is not located at location5, nut5 is not located at location7, nut5 is not located at location8, spanner1 is not at gate, spanner1 is not currently at location1, spanner1 is not currently at location2, spanner1 is not currently at location3, spanner1 is not currently at location5, spanner1 is not currently at location6, spanner1 is not currently at location7, spanner1 is not currently at location8, spanner1 is not located at location4, spanner1 is not located at location9, spanner1 is not located at shed, spanner2 is not at location8, spanner2 is not currently at location1, spanner2 is not currently at location2, spanner2 is not currently at location4, spanner2 is not currently at location5, spanner2 is not currently at location7, spanner2 is not located at gate, spanner2 is not located at location3, spanner2 is not located at location6, spanner2 is not located at location9, spanner2 is not located at shed, spanner3 is not at location4, spanner3 is not at location6, spanner3 is not at location8, spanner3 is not currently at location3, spanner3 is not located at gate, spanner3 is not located at location1, spanner3 is not located at location2, spanner3 is not located at location5, spanner3 is not located at location7, spanner3 is not located at location9, spanner3 is not located at shed, spanner4 is not at location1, spanner4 is not at location7, spanner4 is not at location9, spanner4 is not at shed, spanner4 is not currently at location4, spanner4 is not currently at location8, spanner4 is not located at gate, spanner4 is not located at location2, spanner4 is not located at location3, spanner4 is not located at location5, spanner4 is not located at location6, spanner5 is not at location6, spanner5 is not at location9, spanner5 is not currently at gate, spanner5 is not currently at location1, spanner5 is not currently at location2, spanner5 is not currently at location3, spanner5 is not currently at location5, spanner5 is not currently at location7, spanner5 is not currently at shed, spanner5 is not located at location4 and spanner5 is not located at location8?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then from location1 to location2, picks up spanner3 at location2, walks to location3, picks up spanner5 and spanner1 at location3, walks to location4, then to location5, picks up spanner2 at location5, and finally walks to location6 to reach the current state. In this state, are the following properties that involve negations True or False: Bob is not at location1, Bob is not at location3, Bob is not at location4, Bob is not at location7, Bob is not at location9, Bob is not at the shed, Bob is not at the gate, Bob is not at location2, Bob is not at location8, Bob is not at location5, nut1 is not at location3, nut1 is not at location4, nut1 is not at location7, nut1 is not at location2, nut1 is not at location5, nut1 is not at location6, nut1 is not at location8, nut1 is not at location9, nut1 is not at the shed, nut1 is not at the gate, nut1 is not at location1, nut2 is not at location2, nut2 is not at location3, nut2 is not at location5, nut2 is not at location6, nut2 is not at location7, nut2 is not at location1, nut2 is not at location8, nut2 is not at the gate, nut2 is not at location4, nut2 is not at location9, nut2 is not at the shed, nut3 is not at location2, nut3 is not at location9, nut3 is not at location1, nut3 is not at location3, nut3 is not at location5, nut3 is not at the shed, nut3 is not at the gate, nut3 is not at location4, nut3 is not at location6, nut3 is not at location7, nut3 is not at location8, nut4 is not at location1, nut4 is not at location2, nut4 is not at location5, nut4 is not at location6, nut4 is not at location8, nut4 is not at the shed, nut4 is not at the gate, nut4 is not at location3, nut4 is not at location4, nut4 is not at location7, nut4 is not at location9, nut5 is not at location1, nut5 is not at location3, nut5 is not at location2, nut5 is not at location6, nut5 is not at location9, nut5 is not at the shed, nut5 is not at the gate, nut5 is not at location4, nut5 is not at location5, nut5 is not at location7, nut5 is not at location8, spanner1 is not at the gate, spanner1 is not at location1, spanner1 is not at location2, spanner1 is not at location3, spanner1 is not at location5, spanner1 is not at location6, spanner1 is not at location7, spanner1 is not at location8, spanner1 is not at location4, spanner1 is not at location9, spanner1 is not at the shed, spanner2 is not at location8, spanner2 is not at location1, spanner2 is not at location2, spanner2 is not at location4, spanner2 is not at location5, spanner2 is not at location7, spanner2 is not at the gate, spanner2 is not at location3, spanner2 is not at location6, spanner2 is not at location9, spanner2 is not at the shed, spanner3 is not at location4, spanner3 is not at location6, spanner3 is not at location8, spanner3 is not at location3, spanner3 is not at the gate, spanner3 is not at location1, spanner3 is not at location2, spanner3 is not at location5, spanner3 is not at location7, spanner3 is not at location9, spanner3 is not at the shed, spanner4 is not at location1, spanner4 is not at location7, spanner4 is not at location9, spanner4 is not at the shed, spanner4 is not at location4, spanner4 is not at location8, spanner4 is not at the gate, spanner4 is not at location2, spanner4 is not at location3, spanner4 is not at location5, spanner4 is not at location6, spanner5 is not at location6, spanner5 is not at location9, spanner5 is not at the gate, spanner5 is not at location1, spanner5 is not at location2, spanner5 is not at location3, spanner5 is not at location5, spanner5 is not at location7, spanner5 is not at the shed, spanner5 is not at location4 and spanner5 is not at location8?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Furthermore, location5 is linked to location6, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, nut1 is present but not secured, nut2 is also at the gate and is loose, nut3 is at the gate and not secured, nut4 is currently at the gate and not secured, and nut5 is at the gate and loose. The shed is connected to location1. Spanner1 is located at location3 and is in working condition, spanner2 is at location5 and can be used, spanner3 is at location2 and functional, spanner4 is at location6 and usable, and spanner5 is at location3 and functional."}
{"question_id": "319260ac-4c05-48f7-84fb-97494a6a73de", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks to location2 from location1, bob walks from location2 to location3, from location3 to location4, bob walks, bob walks to location5 from location4, from location5, bob picks up spanner4, from location5 to location6, bob walks, spanner1 is picked up by bob from location6, from location6 to location7, bob walks, bob picks up spanner5 from location7, bob walks from location7 to location8, spanner3 is picked up by bob from location8, from location8, bob picks up spanner2, from location8 to location9, bob walks, from location9 to gate, bob walks, nut1 is tightened by bob using spanner5 at gate, bob tightens nut2 with spanner4 at gate, bob tightens nut3 with spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, is it True or False that tightening of nut4 is complete?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location3 and location4 exists, a link between location4 and location5 exists, bob is at shed, location2 and location3 are linked, location5 is linked to location6, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is located at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is usable, spanner3 can be used, spanner3 is located at location8, spanner4 can be used, spanner4 is at location5, spanner5 is currently at location7 and spanner5 is usable.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: bob moves from the shed to location1, then to location2, followed by location3, then location4, then location5, and finally picks up spanner4. From location5, bob proceeds to location6, where he picks up spanner1, then moves to location7, picks up spanner5, and walks to location8. At location8, bob picks up both spanner3 and spanner2, then walks to location9 and finally to the gate. At the gate, bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4. In this state, is it True or False that the tightening of nut4 is complete?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location3 and location4, and a connection between location4 and location5. Bob is currently at the shed. Additionally, location2 is connected to location3, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3 (which is not secured), nut4, and nut5. Furthermore, the shed is connected to location1. There are several usable spanners: spanner1 is at location6, spanner2 and spanner3 are at location8, spanner4 is at location5, and spanner5 is at location7."}
{"question_id": "a130ed5c-0ee2-4097-a8d8-cf67fa71c45a", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_9", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks to location2 from location1, bob picks up spanner4 from location2, from location2, bob picks up spanner3, bob walks from location2 to location3, bob walks from location3 to location4, from location4 to location5, bob walks, from location5 to location6, bob walks, bob picks up spanner5 from location6 and from location6, bob picks up spanner2 to reach the current state. In this state, is it True or False that nut2 is currently at gate?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: Bob moves from the shed to location1, then proceeds to location2, where he collects spanner4 and spanner3. From location2, Bob walks to location3, then to location4, and subsequently to location5 and location6. At location6, he picks up spanner5 and spanner2, ultimately reaching the current state. In this state, is it True or False that nut2 is currently located at the gate?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, and a connection also exists between location3 and location4. Additionally, a connection is present between location5 and location6. Bob is currently situated at the shed. Furthermore, location4 and location5 are interconnected, location6 is connected to location7, location7 is linked to location8, location8 and location9 are connected, and location9 is linked to the gate. At the gate, nut1 is located and is not secured, nut2 is currently present and is loose, nut3 is also located at the gate and is loose, nut4 is currently at the gate and is not secured, and nut5 is currently at the gate and is not secured. The shed and location1 are interconnected. Spanner1 is functional and is located at location8, spanner2 is located at location6 and is usable, spanner3 is functional and is currently at location2, spanner4 is functional and is located at location2, and spanner5 is functional and is located at location6."}
{"question_id": "f4ef37d2-4335-46f3-a159-a3bed5ef7044", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, spanner5 is picked up by bob from location1, from location1, bob picks up spanner4, bob walks from location1 to location2, from location2 to location3, bob walks, bob walks to location4 from location3, bob picks up spanner1 from location4, bob walks to location5 from location4, bob walks to location6 from location5, from location6, bob picks up spanner3, from location6 to location7, bob walks, from location7, bob picks up spanner2, from location7 to location8, bob walks, bob walks from location8 to location9, from location9 to gate, bob walks, nut1 is tightened by bob using spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, nut3 is tightened by bob using spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: bob is carrying spanner1, bob is carrying spanner4, spanner2 is carried by bob, spanner3 is carried by bob, spanner5 is carried by bob, tightening of nut1 is complete, tightening of nut2 is complete, tightening of nut3 is complete, tightening of nut4 is complete and tightening of nut5 is complete?", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: Bob starts at the shed and walks to location1, where he picks up spanner5 and then spanner4. He then proceeds to location2, followed by location3, and then walks to location4. At location4, Bob picks up spanner1 and then walks to location5, then location6, where he picks up spanner3. From location6, he walks to location7, picks up spanner2, and then walks to location8, followed by location9, and finally reaches the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, are the following properties True or False: Bob is holding spanner1, Bob is holding spanner4, Bob is carrying spanner2, Bob is carrying spanner3, Bob is carrying spanner5, the tightening of nut1 is complete, the tightening of nut2 is complete, the tightening of nut3 is complete, the tightening of nut4 is complete, and the tightening of nut5 is complete?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present and unsecured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1 is available and located at location4. Spanner2 is functional and currently at location7, while spanner3 is functional and at location6. Spanner4 is functional and located at location1, and spanner5 is also available and located at location1."}
{"question_id": "796e298f-bf1c-489e-97e4-0d16d32b0332", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_20", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, from location1 to location2, bob walks, bob picks up spanner4 from location2, spanner3 is picked up by bob from location2, bob walks from location2 to location3, bob walks from location3 to location4, from location4 to location5, bob walks, from location5 to location6, bob walks, bob picks up spanner5 from location6 and from location6, bob picks up spanner2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: a link between gate and location9 exists, a link between location1 and location2 exists, a link between location1 and location4 exists, a link between location1 and location5 exists, a link between location1 and location6 exists, a link between location2 and gate exists, a link between location2 and location3 exists, a link between location2 and location9 exists, a link between location3 and location2 exists, a link between location5 and gate exists, a link between location5 and location4 exists, a link between location5 and location7 exists, a link between location5 and shed exists, a link between location7 and gate exists, a link between location7 and location1 exists, a link between location7 and location5 exists, a link between location7 and location6 exists, a link between location7 and location8 exists, a link between location7 and location9 exists, a link between location8 and location6 exists, a link between location8 and shed exists, a link between location9 and location2 exists, a link between location9 and location3 exists, a link between shed and location4 exists, gate and location5 are linked, gate and location6 are linked, gate and location7 are linked, gate is linked to location1, gate is linked to location2, gate is linked to location3, gate is linked to location4, gate is linked to location8, gate is linked to shed, location1 and gate are linked, location1 and location3 are linked, location1 and location7 are linked, location1 and location8 are linked, location1 and location9 are linked, location1 is linked to shed, location2 and location4 are linked, location2 and location5 are linked, location2 is linked to location1, location2 is linked to location6, location2 is linked to location7, location2 is linked to location8, location2 is linked to shed, location3 and location6 are linked, location3 and location8 are linked, location3 and location9 are linked, location3 is linked to gate, location3 is linked to location1, location3 is linked to location4, location3 is linked to location5, location3 is linked to location7, location3 is linked to shed, location4 and location1 are linked, location4 and location3 are linked, location4 and location8 are linked, location4 and location9 are linked, location4 and shed are linked, location4 is linked to gate, location4 is linked to location2, location4 is linked to location5, location4 is linked to location6, location4 is linked to location7, location5 and location2 are linked, location5 and location6 are linked, location5 and location8 are linked, location5 is linked to location1, location5 is linked to location3, location5 is linked to location9, location6 and location1 are linked, location6 and location4 are linked, location6 and location5 are linked, location6 and location7 are linked, location6 and shed are linked, location6 is linked to gate, location6 is linked to location2, location6 is linked to location3, location6 is linked to location8, location6 is linked to location9, location7 and location2 are linked, location7 and location4 are linked, location7 and shed are linked, location7 is linked to location3, location8 and gate are linked, location8 and location1 are linked, location8 and location2 are linked, location8 and location3 are linked, location8 and location4 are linked, location8 is linked to location5, location8 is linked to location7, location8 is linked to location9, location9 and location1 are linked, location9 and location4 are linked, location9 is linked to gate, location9 is linked to location5, location9 is linked to location6, location9 is linked to location7, location9 is linked to location8, location9 is linked to shed, shed and gate are linked, shed and location1 are linked, shed and location5 are linked, shed is linked to location2, shed is linked to location3, shed is linked to location6, shed is linked to location7, shed is linked to location8 and shed is linked to location9?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob moves from the shed to location1, then to location2, then walks, picks up spanner4 at location2, and also picks up spanner3 at location2. He then walks from location2 to location3, then to location4, then to location5, then walks, then to location6, then walks, picks up spanner5 at location6, and also picks up spanner2 at location6 to reach the current state. In this state, are the following properties of the state that do not involve negations True or False: a connection exists between gate and location9, a connection exists between location1 and location2, a connection exists between location1 and location4, a connection exists between location1 and location5, a connection exists between location1 and location6, a connection exists between location2 and gate, a connection exists between location2 and location3, a connection exists between location2 and location9, a connection exists between location3 and location2, a connection exists between location5 and gate, a connection exists between location5 and location4, a connection exists between location5 and location7, a connection exists between location5 and shed, a connection exists between location7 and gate, a connection exists between location7 and location1, a connection exists between location7 and location5, a connection exists between location7 and location6, a connection exists between location7 and location8, a connection exists between location7 and location9, a connection exists between location8 and location6, a connection exists between location8 and shed, a connection exists between location9 and location2, a connection exists between location9 and location3, a connection exists between shed and location4, gate and location5 are connected, gate and location6 are connected, gate and location7 are connected, gate is connected to location1, gate is connected to location2, gate is connected to location3, gate is connected to location4, gate is connected to location8, gate is connected to shed, location1 and gate are connected, location1 and location3 are connected, location1 and location7 are connected, location1 and location8 are connected, location1 and location9 are connected, location1 is connected to shed, location2 and location4 are connected, location2 and location5 are connected, location2 is connected to location1, location2 is connected to location6, location2 is connected to location7, location2 is connected to location8, location2 is connected to shed, location3 and location6 are connected, location3 and location8 are connected, location3 and location9 are connected, location3 is connected to gate, location3 is connected to location1, location3 is connected to location4, location3 is connected to location5, location3 is connected to location7, location3 is connected to shed, location4 and location1 are connected, location4 and location3 are connected, location4 and location8 are connected, location4 and location9 are connected, location4 and shed are connected, location4 is connected to gate, location4 is connected to location2, location4 is connected to location5, location4 is connected to location6, location4 is connected to location7, location5 and location2 are connected, location5 and location6 are connected, location5 and location8 are connected, location5 is connected to location1, location5 is connected to location3, location5 is connected to location9, location6 and location1 are connected, location6 and location4 are connected, location6 and location5 are connected, location6 and location7 are connected, location6 and shed are connected, location6 is connected to gate, location6 is connected to location2, location6 is connected to location3, location6 is connected to location8, location6 is connected to location9, location7 and location2 are connected, location7 and location4 are connected, location7 and shed are connected, location7 is connected to location3, location8 and gate are connected, location8 and location1 are connected, location8 and location2 are connected, location8 and location3 are connected, location8 and location4 are connected, location8 is connected to location5, location8 is connected to location7, location8 is connected to location9, location9 and location1 are connected, location9 and location4 are connected, location9 is connected to gate, location9 is connected to location5, location9 is connected to location6, location9 is connected to location7, location9 is connected to location8, location9 is connected to shed, shed and gate are connected, shed and location1 are connected, shed and location5 are connected, shed is connected to location2, shed is connected to location3, shed is connected to location6, shed is connected to location7, shed is connected to location8 and shed is connected to location9?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, and a connection also exists between location3 and location4. Additionally, a connection is present between location5 and location6. Bob is currently situated at the shed. Furthermore, location4 and location5 are interconnected, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. Location9, in turn, is connected to the gate. At the gate, nut1 is located and is not secured. Similarly, nut2 is currently at the gate and is loose, nut3 is also located at the gate and is loose, nut4 is at the gate and is not secured, and nut5 is at the gate and is not secured. The shed is connected to location1. Spanner1 is functional and is located at location8. Spanner2 is situated at location6 and is usable. Spanner3 is functional and is currently at location2, spanner4 is also functional and is located at location2, and spanner5 is functional and is located at location6."}
{"question_id": "a643a6fd-b68d-4bd0-8564-edefebfd4e7f", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_13", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, is it True or False that location7 is linked to location8?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, is it True or False that location8 is connected to location7?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present and unsecured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1, which is usable, is located at location4. Spanner2, which is functional, is currently at location7. Spanner3, also functional, is at location6. Spanner4, which is functional, and spanner5, which is usable, are both located at location1."}
{"question_id": "824f78ff-3689-46ce-a1e4-6ab40916e441", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_15", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, from location1 to location2, bob walks, from location2, bob picks up spanner4, bob picks up spanner3 from location2, bob walks from location2 to location3, from location3 to location4, bob walks, bob walks to location5 from location4, bob walks to location6 from location5, bob picks up spanner5 from location6 and from location6, bob picks up spanner2 to reach the current state. In this state, is it True or False that location6 and gate are not linked?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob moves from the shed to location1, then to location2, then walks, then from location2, he collects spanner4, and also picks up spanner3 from location2, then walks from location2 to location3, then to location4, then walks, then proceeds from location4 to location5, then to location6, and from location6, he collects spanner5 and also picks up spanner2 to reach the current state. In this state, is it True or False that location6 and the gate are not connected?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, and a connection also exists between location3 and location4. Additionally, a connection is present between location5 and location6. Bob is currently situated at the shed. Furthermore, location4 and location5 are interconnected, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. Location9, in turn, is linked to the gate. At the gate, the following items are located: nut1, which is not secured, nut2, which is loose, nut3, also loose, nut4, which is not secured, and nut5, also not secured. The shed is connected to location1. The following spanners are available for use: spanner1, which is located at location8, spanner2, which is usable and located at location6, spanner3, which is currently at location2, spanner4, also at location2, and spanner5, which is functional and located at location6."}
{"question_id": "4bb49db5-423b-4842-938c-256ff7d66cf0", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_5", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, is it True or False that spanner3 is usable?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob moves from the shed to location1 to reach the current state. In this state, is it True or False that spanner3 is usable?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location2 and location3, a connection between location3 and location4, a connection between location5 and location6, bob is currently at the shed, location4 and location5 are connected, location6 is connected to location7, location7 is connected to location8, location8 and location9 are connected, location9 is connected to the gate, nut1 is situated at the gate and is unsecured, nut2 is currently at the gate and is loose, nut3 is located at the gate and is loose, nut4 is currently at the gate and is unsecured, nut5 is currently at the gate and is unsecured, the shed and location1 are connected, spanner1 is usable and is located at location8, spanner2 is located at location6 and is usable, spanner3 is usable and is currently at location2, spanner4 is usable and is located at location2, spanner5 is functional and is located at location6."}
{"question_id": "6de6c4a9-38bb-4cb6-ad38-37364c7a572f", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_3", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, spanner5 is picked up by bob from location1, bob picks up spanner4 from location1, bob walks from location1 to location2, bob walks from location2 to location3, bob walks to location4 from location3, spanner1 is picked up by bob from location4, bob walks to location5 from location4, from location5 to location6, bob walks, spanner3 is picked up by bob from location6, from location6 to location7, bob walks, spanner2 is picked up by bob from location7, from location7 to location8, bob walks, bob walks from location8 to location9, bob walks from location9 to gate, at gate, bob uses spanner5 to tighten nut1, bob tightens nut2 with spanner4 at gate, bob tightens nut3 with spanner3 at gate and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, is it True or False that tightening of nut5 is incomplete?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: Bob moves from the shed to location1, then collects spanner5 from location1, followed by picking up spanner4 from the same location. He then proceeds to walk from location1 to location2, then to location3, and subsequently from location3 to location4. At location4, Bob picks up spanner1 and continues walking to location5, then to location6, where he collects spanner3. From location6, he walks to location7, picks up spanner2, and then walks to location8, followed by location9, and finally reaches the gate. Upon arriving at the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, is it True or False that the tightening of nut5 is incomplete?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present and unsecured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1, which is usable, is located at location4. Spanner2, which is functional, is currently at location7. Spanner3, also functional, is at location6. Spanner4, which is functional, and spanner5, which is usable, are both located at location1."}
{"question_id": "40d1a206-ee9e-43e3-82c2-24304dbb88d6", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob picks up spanner5 from location1, bob picks up spanner4 from location1, from location1 to location2, bob walks, bob walks to location3 from location2, from location3 to location4, bob walks, spanner1 is picked up by bob from location4, from location4 to location5, bob walks, bob walks to location6 from location5 and from location6, bob picks up spanner3 to reach the current state. In this state, is it True or False that spanner2 is carried by bob?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: bob moves from the shed to location1, then collects spanner5 and spanner4 from location1, proceeds to location2, walks to location3 from location2, and then to location4, where he picks up spanner1. From location4, bob walks to location5 and then to location6, where he collects spanner3, ultimately reaching the current state. In this state, is it True or False that bob is carrying spanner2?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present and unsecured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1, which is usable, is located at location4. Spanner2, which is functional, is currently at location7. Spanner3, also functional, is at location6. Spanner4, which is functional, and spanner5, which is usable, are both located at location1."}
{"question_id": "8e1ca268-dab8-4d31-a776-c90b516d03af", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_21", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks to location2 from location1, bob walks from location2 to location3, from location3 to location4, bob walks, from location4 to location5, bob walks, bob picks up spanner4 from location5, bob walks to location6 from location5, from location6, bob picks up spanner1, from location6 to location7, bob walks, bob picks up spanner5 from location7, bob walks from location7 to location8, from location8, bob picks up spanner3, spanner2 is picked up by bob from location8, bob walks from location8 to location9, bob walks to gate from location9, nut1 is tightened by bob using spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, at gate, bob uses spanner3 to tighten nut3 and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: a link between gate and location2 does not exist, a link between gate and location6 does not exist, a link between gate and location7 does not exist, a link between gate and shed does not exist, a link between location1 and gate does not exist, a link between location1 and location4 does not exist, a link between location1 and location7 does not exist, a link between location2 and location5 does not exist, a link between location2 and location6 does not exist, a link between location2 and location7 does not exist, a link between location2 and location8 does not exist, a link between location2 and shed does not exist, a link between location3 and location6 does not exist, a link between location3 and location9 does not exist, a link between location3 and shed does not exist, a link between location4 and location1 does not exist, a link between location5 and location1 does not exist, a link between location5 and location2 does not exist, a link between location5 and location3 does not exist, a link between location5 and shed does not exist, a link between location6 and location2 does not exist, a link between location7 and location2 does not exist, a link between location7 and location4 does not exist, a link between location7 and location5 does not exist, a link between location8 and gate does not exist, a link between location8 and shed does not exist, a link between location9 and location7 does not exist, a link between location9 and location8 does not exist, a link between location9 and shed does not exist, a link between shed and gate does not exist, a link between shed and location3 does not exist, a link between shed and location6 does not exist, a link between shed and location9 does not exist, gate and location1 are not linked, gate and location3 are not linked, gate and location8 are not linked, gate and location9 are not linked, gate is not linked to location4, gate is not linked to location5, location1 and location3 are not linked, location1 and location6 are not linked, location1 and location8 are not linked, location1 is not linked to location5, location1 is not linked to location9, location1 is not linked to shed, location2 and gate are not linked, location2 and location1 are not linked, location2 and location4 are not linked, location2 and location9 are not linked, location3 and location1 are not linked, location3 and location2 are not linked, location3 and location7 are not linked, location3 and location8 are not linked, location3 is not linked to gate, location3 is not linked to location5, location4 and location3 are not linked, location4 and location6 are not linked, location4 and location7 are not linked, location4 and location9 are not linked, location4 is not linked to gate, location4 is not linked to location2, location4 is not linked to location8, location4 is not linked to shed, location5 and gate are not linked, location5 and location4 are not linked, location5 and location7 are not linked, location5 is not linked to location8, location5 is not linked to location9, location6 and gate are not linked, location6 and location1 are not linked, location6 and location8 are not linked, location6 and location9 are not linked, location6 is not linked to location3, location6 is not linked to location4, location6 is not linked to location5, location6 is not linked to shed, location7 and location9 are not linked, location7 is not linked to gate, location7 is not linked to location1, location7 is not linked to location3, location7 is not linked to location6, location7 is not linked to shed, location8 and location1 are not linked, location8 and location3 are not linked, location8 and location4 are not linked, location8 is not linked to location2, location8 is not linked to location5, location8 is not linked to location6, location8 is not linked to location7, location9 and location4 are not linked, location9 and location5 are not linked, location9 and location6 are not linked, location9 is not linked to location1, location9 is not linked to location2, location9 is not linked to location3, shed and location4 are not linked, shed and location5 are not linked, shed and location7 are not linked, shed is not linked to location2 and shed is not linked to location8?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location3 and location4 exists, a link between location4 and location5 exists, bob is at shed, location2 and location3 are linked, location5 is linked to location6, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is located at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is usable, spanner3 can be used, spanner3 is located at location8, spanner4 can be used, spanner4 is at location5, spanner5 is currently at location7 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: Bob moves from the shed to location1, then to location2, then to location3, then to location4, then to location5, then picks up spanner4, then moves to location6, picks up spanner1, then moves to location7, picks up spanner5, then moves to location8, picks up spanner3 and spanner2, then moves to location9, then to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4 to reach the current state. In this state, are the following properties that involve negations True or False: There is no connection between the gate and location2, no connection between the gate and location6, no connection between the gate and location7, no connection between the gate and the shed, no connection between location1 and the gate, no connection between location1 and location4, no connection between location1 and location7, no connection between location2 and location5, no connection between location2 and location6, no connection between location2 and location7, no connection between location2 and location8, no connection between location2 and the shed, no connection between location3 and location6, no connection between location3 and location9, no connection between location3 and the shed, no connection between location4 and location1, no connection between location5 and location1, no connection between location5 and location2, no connection between location5 and location3, no connection between location5 and the shed, no connection between location6 and location2, no connection between location7 and location2, no connection between location7 and location4, no connection between location7 and location5, no connection between location8 and the gate, no connection between location8 and the shed, no connection between location9 and location7, no connection between location9 and location8, no connection between location9 and the shed, no connection between the shed and the gate, no connection between the shed and location3, no connection between the shed and location6, no connection between the shed and location9, the gate and location1 are not connected, the gate and location3 are not connected, the gate and location8 are not connected, the gate and location9 are not connected, the gate is not connected to location4, the gate is not connected to location5, location1 and location3 are not connected, location1 and location6 are not connected, location1 and location8 are not connected, location1 is not connected to location5, location1 is not connected to location9, location1 is not connected to the shed, location2 and the gate are not connected, location2 and location1 are not connected, location2 and location4 are not connected, location2 and location9 are not connected, location3 and location1 are not connected, location3 and location2 are not connected, location3 and location7 are not connected, location3 and location8 are not connected, location3 is not connected to the gate, location3 is not connected to location5, location4 and location3 are not connected, location4 and location6 are not connected, location4 and location7 are not connected, location4 and location9 are not connected, location4 is not connected to the gate, location4 is not connected to location2, location4 is not connected to location8, location4 is not connected to the shed, location5 and the gate are not connected, location5 and location4 are not connected, location5 and location7 are not connected, location5 is not connected to location8, location5 is not connected to location9, location6 and the gate are not connected, location6 and location1 are not connected, location6 and location8 are not connected, location6 and location9 are not connected, location6 is not connected to location3, location6 is not connected to location4, location6 is not connected to location5, location6 is not connected to the shed, location7 and location9 are not connected, location7 is not connected to the gate, location7 is not connected to location1, location7 is not connected to location3, location7 is not connected to location6, location7 is not connected to the shed, location8 and location1 are not connected, location8 and location3 are not connected, location8 and location4 are not connected, location8 is not connected to location2, location8 is not connected to location5, location8 is not connected to location6, location8 is not connected to location7, location9 and location4 are not connected, location9 and location5 are not connected, location9 and location6 are not connected, location9 is not connected to location1, location9 is not connected to location2, location9 is not connected to location3, the shed and location4 are not connected, the shed and location5 are not connected, the shed and location7 are not connected, the shed is not connected to location2, and the shed is not connected to location8?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location3 and location4, and a connection between location4 and location5. Bob is currently at the shed. Additionally, location2 is connected to location3, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3 (which is not secured), nut4, and nut5. Furthermore, the shed is connected to location1. There are several usable spanners: spanner1 is at location6, spanner2 and spanner3 are at location8, spanner4 is at location5, and spanner5 is at location7."}
{"question_id": "4b982cd9-578a-4f47-91ca-ccda57c7dc29", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, from location1, bob picks up spanner5, bob picks up spanner4 from location1, from location1 to location2, bob walks, from location2 to location3, bob walks, bob walks from location3 to location4, from location4, bob picks up spanner1, bob walks to location5 from location4, from location5 to location6, bob walks and spanner3 is picked up by bob from location6 to reach the current state. In this state, is it True or False that spanner5 is carried by bob?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then collects spanner5 and spanner4 from location1. He proceeds to walk from location1 to location2, then to location3, and subsequently from location3 to location4. At location4, Bob picks up spanner1 and then walks to location5. From location5, he walks to location6, where he collects spanner3, ultimately reaching the current state. In this state, is it True or False that Bob is carrying spanner5?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present but not secured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1, which is usable, is located at location4. Spanner2, which is functional, is currently at location7, while spanner3, also functional, is at location6. Spanner4, which is functional, and spanner5, which is usable, are both located at location1."}
{"question_id": "6c351e6c-2972-4474-b513-5ed174ffad0b", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_15", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks to location2 from location1, bob walks to location3 from location2, from location3 to location4, bob walks, bob walks to location5 from location4, bob picks up spanner4 from location5, bob walks from location5 to location6, from location6, bob picks up spanner1, bob walks from location6 to location7, bob picks up spanner5 from location7, bob walks to location8 from location7, from location8, bob picks up spanner3, from location8, bob picks up spanner2, from location8 to location9, bob walks, bob walks to gate from location9, bob tightens nut1 with spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, bob tightens nut3 with spanner3 at gate and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: bob is not at location2, bob is not at location5, bob is not at location7, bob is not at location8, bob is not at shed, bob is not currently at location1, bob is not currently at location3, bob is not currently at location9, bob is not located at location4, bob is not located at location6, nut1 is not at location3, nut1 is not at location7, nut1 is not at shed, nut1 is not currently at location1, nut1 is not currently at location2, nut1 is not currently at location8, nut1 is not located at location4, nut1 is not located at location5, nut1 is not located at location6, nut1 is not located at location9, nut2 is not at location3, nut2 is not at shed, nut2 is not currently at location1, nut2 is not currently at location2, nut2 is not currently at location6, nut2 is not currently at location7, nut2 is not currently at location8, nut2 is not located at location4, nut2 is not located at location5, nut2 is not located at location9, nut3 is not at location4, nut3 is not at location5, nut3 is not at location6, nut3 is not at location7, nut3 is not at location8, nut3 is not at location9, nut3 is not at shed, nut3 is not located at location1, nut3 is not located at location2, nut3 is not located at location3, nut4 is not at location5, nut4 is not currently at location1, nut4 is not currently at location2, nut4 is not currently at location3, nut4 is not currently at location6, nut4 is not currently at location8, nut4 is not currently at location9, nut4 is not currently at shed, nut4 is not located at location4, nut4 is not located at location7, nut5 is not at location2, nut5 is not at location6, nut5 is not at location8, nut5 is not at location9, nut5 is not currently at location1, nut5 is not currently at location3, nut5 is not located at location4, nut5 is not located at location5, nut5 is not located at location7, nut5 is not located at shed, spanner1 is not at location9, spanner1 is not at shed, spanner1 is not currently at gate, spanner1 is not currently at location1, spanner1 is not currently at location3, spanner1 is not currently at location4, spanner1 is not currently at location5, spanner1 is not currently at location7, spanner1 is not currently at location8, spanner1 is not located at location2, spanner1 is not located at location6, spanner2 is not at gate, spanner2 is not at location6, spanner2 is not currently at location3, spanner2 is not currently at location4, spanner2 is not currently at location8, spanner2 is not currently at shed, spanner2 is not located at location1, spanner2 is not located at location2, spanner2 is not located at location5, spanner2 is not located at location7, spanner2 is not located at location9, spanner3 is not at location2, spanner3 is not at location8, spanner3 is not currently at location1, spanner3 is not currently at location3, spanner3 is not currently at location4, spanner3 is not currently at location6, spanner3 is not located at gate, spanner3 is not located at location5, spanner3 is not located at location7, spanner3 is not located at location9, spanner3 is not located at shed, spanner4 is not at gate, spanner4 is not at location3, spanner4 is not at shed, spanner4 is not currently at location1, spanner4 is not currently at location2, spanner4 is not currently at location4, spanner4 is not currently at location8, spanner4 is not located at location5, spanner4 is not located at location6, spanner4 is not located at location7, spanner4 is not located at location9, spanner5 is not at location3, spanner5 is not at location8, spanner5 is not currently at location2, spanner5 is not currently at location4, spanner5 is not currently at location5, spanner5 is not currently at location6, spanner5 is not currently at location7, spanner5 is not currently at location9, spanner5 is not located at gate, spanner5 is not located at location1 and spanner5 is not located at shed?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location3 and location4 exists, a link between location4 and location5 exists, bob is at shed, location2 and location3 are linked, location5 is linked to location6, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is located at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is usable, spanner3 can be used, spanner3 is located at location8, spanner4 can be used, spanner4 is at location5, spanner5 is currently at location7 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from the shed, bob proceeds to location1, then walks to location2, followed by location3, then location4, and then location5, where he picks up spanner4, then walks to location6, picks up spanner1, walks to location7, picks up spanner5, walks to location8, picks up spanner3 and spanner2, then walks to location9, and finally to the gate, where he uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, are the following properties that involve negations True or False: bob is not at location2, bob is not at location5, bob is not at location7, bob is not at location8, bob is not at the shed, bob is not currently at location1, bob is not currently at location3, bob is not currently at location9, bob is not located at location4, bob is not located at location6, nut1 is not at location3, nut1 is not at location7, nut1 is not at the shed, nut1 is not currently at location1, nut1 is not currently at location2, nut1 is not currently at location8, nut1 is not located at location4, nut1 is not located at location5, nut1 is not located at location6, nut1 is not located at location9, nut2 is not at location3, nut2 is not at the shed, nut2 is not currently at location1, nut2 is not currently at location2, nut2 is not currently at location6, nut2 is not currently at location7, nut2 is not currently at location8, nut2 is not located at location4, nut2 is not located at location5, nut2 is not located at location9, nut3 is not at location4, nut3 is not at location5, nut3 is not at location6, nut3 is not at location7, nut3 is not at location8, nut3 is not at location9, nut3 is not at the shed, nut3 is not located at location1, nut3 is not located at location2, nut3 is not located at location3, nut4 is not at location5, nut4 is not currently at location1, nut4 is not currently at location2, nut4 is not currently at location3, nut4 is not currently at location6, nut4 is not currently at location8, nut4 is not currently at location9, nut4 is not currently at the shed, nut4 is not located at location4, nut4 is not located at location7, nut5 is not at location2, nut5 is not at location6, nut5 is not at location8, nut5 is not at location9, nut5 is not currently at location1, nut5 is not currently at location3, nut5 is not located at location4, nut5 is not located at location5, nut5 is not located at location7, nut5 is not located at the shed, spanner1 is not at location9, spanner1 is not at the shed, spanner1 is not currently at the gate, spanner1 is not currently at location1, spanner1 is not currently at location3, spanner1 is not currently at location4, spanner1 is not currently at location5, spanner1 is not currently at location7, spanner1 is not currently at location8, spanner1 is not located at location2, spanner1 is not located at location6, spanner2 is not at the gate, spanner2 is not at location6, spanner2 is not currently at location3, spanner2 is not currently at location4, spanner2 is not currently at location8, spanner2 is not currently at the shed, spanner2 is not located at location1, spanner2 is not located at location2, spanner2 is not located at location5, spanner2 is not located at location7, spanner2 is not located at location9, spanner3 is not at location2, spanner3 is not at location8, spanner3 is not currently at location1, spanner3 is not currently at location3, spanner3 is not currently at location4, spanner3 is not currently at location6, spanner3 is not located at the gate, spanner3 is not located at location5, spanner3 is not located at location7, spanner3 is not located at location9, spanner3 is not located at the shed, spanner4 is not at the gate, spanner4 is not at location3, spanner4 is not at the shed, spanner4 is not currently at location1, spanner4 is not currently at location2, spanner4 is not currently at location4, spanner4 is not currently at location8, spanner4 is not located at location5, spanner4 is not located at location6, spanner4 is not located at location7, spanner4 is not located at location9, spanner5 is not at location3, spanner5 is not at location8, spanner5 is not currently at location2, spanner5 is not currently at location4, spanner5 is not currently at location5, spanner5 is not currently at location6, spanner5 is not currently at location7, spanner5 is not currently at location9, spanner5 is not located at the gate, spanner5 is not located at location1 and spanner5 is not located at the shed?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location3 and location4, and a connection between location4 and location5. Bob is currently at the shed. Additionally, location2 is connected to location3, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3 (which is not secured), nut4, and nut5. Furthermore, the shed is connected to location1. There are several usable spanners: spanner1 is at location6, spanner2 and spanner3 are at location8, spanner4 is at location5, and spanner5 is at location7."}
{"question_id": "b3f14f4a-d0c3-45f0-b497-09593d83277b", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_16", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, spanner3 is picked up by bob from location2, bob walks from location2 to location3, spanner5 is picked up by bob from location3, from location3, bob picks up spanner1, from location3 to location4, bob walks, bob walks from location4 to location5, spanner2 is picked up by bob from location5, bob walks from location5 to location6, bob picks up spanner4 from location6, bob walks to location7 from location6, bob walks to location8 from location7, bob walks to location9 from location8, from location9 to gate, bob walks, nut1 is tightened by bob using spanner5 at gate, nut2 is tightened by bob using spanner4 at gate, nut3 is tightened by bob using spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: bob is not at location4, bob is not at location6, bob is not at location7, bob is not at location8, bob is not currently at gate, bob is not currently at location1, bob is not currently at location2, bob is not currently at location3, bob is not currently at location5, bob is not currently at shed, bob is not located at location9, nut1 is not at location1, nut1 is not at shed, nut1 is not currently at location5, nut1 is not currently at location6, nut1 is not currently at location8, nut1 is not currently at location9, nut1 is not located at gate, nut1 is not located at location2, nut1 is not located at location3, nut1 is not located at location4, nut1 is not located at location7, nut2 is not at location3, nut2 is not at location5, nut2 is not at location7, nut2 is not at location9, nut2 is not currently at gate, nut2 is not currently at location2, nut2 is not currently at location4, nut2 is not currently at location8, nut2 is not currently at shed, nut2 is not located at location1, nut2 is not located at location6, nut3 is not at location6, nut3 is not currently at gate, nut3 is not currently at location2, nut3 is not currently at location7, nut3 is not currently at location8, nut3 is not located at location1, nut3 is not located at location3, nut3 is not located at location4, nut3 is not located at location5, nut3 is not located at location9, nut3 is not located at shed, nut4 is not at location1, nut4 is not at location2, nut4 is not at location9, nut4 is not currently at gate, nut4 is not currently at location3, nut4 is not currently at location4, nut4 is not currently at location5, nut4 is not currently at location7, nut4 is not located at location6, nut4 is not located at location8, nut4 is not located at shed, nut5 is not at gate, nut5 is not at location1, nut5 is not at location3, nut5 is not at location7, nut5 is not at location9, nut5 is not currently at location4, nut5 is not currently at location5, nut5 is not located at location2, nut5 is not located at location6, nut5 is not located at location8, nut5 is not located at shed, spanner1 is not at location2, spanner1 is not at location3, spanner1 is not at location4, spanner1 is not at location9, spanner1 is not currently at gate, spanner1 is not currently at location5, spanner1 is not currently at location6, spanner1 is not located at location1, spanner1 is not located at location7, spanner1 is not located at location8, spanner1 is not located at shed, spanner2 is not at location8, spanner2 is not at shed, spanner2 is not currently at gate, spanner2 is not currently at location1, spanner2 is not currently at location5, spanner2 is not currently at location6, spanner2 is not currently at location7, spanner2 is not located at location2, spanner2 is not located at location3, spanner2 is not located at location4, spanner2 is not located at location9, spanner3 is not at location8, spanner3 is not currently at location1, spanner3 is not currently at location2, spanner3 is not currently at location4, spanner3 is not currently at location6, spanner3 is not currently at location9, spanner3 is not currently at shed, spanner3 is not located at gate, spanner3 is not located at location3, spanner3 is not located at location5, spanner3 is not located at location7, spanner4 is not at location1, spanner4 is not at location2, spanner4 is not at location4, spanner4 is not at location5, spanner4 is not at location7, spanner4 is not at shed, spanner4 is not currently at gate, spanner4 is not currently at location3, spanner4 is not currently at location6, spanner4 is not currently at location8, spanner4 is not currently at location9, spanner5 is not at location8, spanner5 is not currently at location4, spanner5 is not currently at location7, spanner5 is not currently at location9, spanner5 is not currently at shed, spanner5 is not located at gate, spanner5 is not located at location1, spanner5 is not located at location2, spanner5 is not located at location3, spanner5 is not located at location5 and spanner5 is not located at location6?", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob moves from the shed to location1, then to location2, picks up spanner3 at location2, proceeds to location3, collects spanner5 at location3, and also picks up spanner1 at location3. He then walks to location4, then to location5, picks up spanner2 at location5, moves to location6, collects spanner4 at location6, and continues to location7, then to location8, and finally to location9 before reaching the gate. At the gate, Bob tightens nut1 using spanner5, nut2 using spanner4, nut3 using spanner3, and nut4 using spanner2 to reach the current state. In this state, are the following properties that involve negations True or False: Bob is not at location4, Bob is not at location6, Bob is not at location7, Bob is not at location8, Bob is not currently at the gate, Bob is not currently at location1, Bob is not currently at location2, Bob is not currently at location3, Bob is not currently at location5, Bob is not currently at the shed, Bob is not located at location9, nut1 is not at location1, nut1 is not at the shed, nut1 is not currently at location5, nut1 is not currently at location6, nut1 is not currently at location8, nut1 is not currently at location9, nut1 is not located at the gate, nut1 is not located at location2, nut1 is not located at location3, nut1 is not located at location4, nut1 is not located at location7, nut2 is not at location3, nut2 is not at location5, nut2 is not at location7, nut2 is not at location9, nut2 is not currently at the gate, nut2 is not currently at location2, nut2 is not currently at location4, nut2 is not currently at location8, nut2 is not currently at the shed, nut2 is not located at location1, nut2 is not located at location6, nut3 is not at location6, nut3 is not currently at the gate, nut3 is not currently at location2, nut3 is not currently at location7, nut3 is not currently at location8, nut3 is not located at location1, nut3 is not located at location3, nut3 is not located at location4, nut3 is not located at location5, nut3 is not located at location9, nut3 is not located at the shed, nut4 is not at location1, nut4 is not at location2, nut4 is not at location9, nut4 is not currently at the gate, nut4 is not currently at location3, nut4 is not currently at location4, nut4 is not currently at location5, nut4 is not currently at location7, nut4 is not located at location6, nut4 is not located at location8, nut4 is not located at the shed, nut5 is not at the gate, nut5 is not at location1, nut5 is not at location3, nut5 is not at location7, nut5 is not at location9, nut5 is not currently at location4, nut5 is not currently at location5, nut5 is not located at location2, nut5 is not located at location6, nut5 is not located at location8, nut5 is not located at the shed, spanner1 is not at location2, spanner1 is not at location3, spanner1 is not at location4, spanner1 is not at location9, spanner1 is not currently at the gate, spanner1 is not currently at location5, spanner1 is not currently at location6, spanner1 is not located at location1, spanner1 is not located at location7, spanner1 is not located at location8, spanner1 is not located at the shed, spanner2 is not at location8, spanner2 is not at the shed, spanner2 is not currently at the gate, spanner2 is not currently at location1, spanner2 is not currently at location5, spanner2 is not currently at location6, spanner2 is not currently at location7, spanner2 is not located at location2, spanner2 is not located at location3, spanner2 is not located at location4, spanner2 is not located at location9, spanner3 is not at location8, spanner3 is not currently at location1, spanner3 is not currently at location2, spanner3 is not currently at location4, spanner3 is not currently at location6, spanner3 is not currently at location9, spanner3 is not currently at the shed, spanner3 is not located at the gate, spanner3 is not located at location3, spanner3 is not located at location5, spanner3 is not located at location7, spanner4 is not at location1, spanner4 is not at location2, spanner4 is not at location4, spanner4 is not at location5, spanner4 is not at location7, spanner4 is not at the shed, spanner4 is not currently at the gate, spanner4 is not currently at location3, spanner4 is not currently at location6, spanner4 is not currently at location8, spanner4 is not currently at location9, spanner5 is not at location8, spanner5 is not currently at location4, spanner5 is not currently at location7, spanner5 is not currently at location9, spanner5 is not currently at the shed, spanner5 is not located at the gate, spanner5 is not located at location1, spanner5 is not located at location2, spanner5 is not located at location3, spanner5 is not located at location5, and spanner5 is not located at location6?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Furthermore, location5 is linked to location6, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, nut1 is present and unsecured, nut2 is also at the gate and loose, nut3 is located at the gate and unsecured, nut4 is currently at the gate and unsecured, and nut5 is at the gate and loose. The shed is connected to location1. Spanner1 is functional and located at location3, spanner2 is usable and situated at location5, spanner3 is usable and located at location2, spanner4 is usable and at location6, and spanner5 is functional and located at location3."}
{"question_id": "58c8c802-9c2b-4925-8ab4-9a3993eec1e2", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_15", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: bob is not at gate, bob is not at location2, bob is not at location4, bob is not at location6, bob is not at location9, bob is not currently at location5, bob is not currently at location7, bob is not currently at shed, bob is not located at location3, bob is not located at location8, nut1 is not at location4, nut1 is not at location5, nut1 is not at location7, nut1 is not currently at location1, nut1 is not currently at location6, nut1 is not currently at location9, nut1 is not currently at shed, nut1 is not located at location2, nut1 is not located at location3, nut1 is not located at location8, nut2 is not at location4, nut2 is not at location5, nut2 is not at shed, nut2 is not currently at location3, nut2 is not currently at location9, nut2 is not located at location1, nut2 is not located at location2, nut2 is not located at location6, nut2 is not located at location7, nut2 is not located at location8, nut3 is not at location2, nut3 is not at location4, nut3 is not at location6, nut3 is not at location7, nut3 is not at location8, nut3 is not at location9, nut3 is not currently at location5, nut3 is not currently at shed, nut3 is not located at location1, nut3 is not located at location3, nut4 is not at location3, nut4 is not at location6, nut4 is not at location9, nut4 is not currently at location2, nut4 is not currently at location5, nut4 is not currently at location8, nut4 is not currently at shed, nut4 is not located at location1, nut4 is not located at location4, nut4 is not located at location7, nut5 is not at location2, nut5 is not at location4, nut5 is not at location5, nut5 is not currently at location8, nut5 is not located at location1, nut5 is not located at location3, nut5 is not located at location6, nut5 is not located at location7, nut5 is not located at location9, nut5 is not located at shed, spanner1 is not at location2, spanner1 is not at location4, spanner1 is not currently at gate, spanner1 is not currently at location1, spanner1 is not currently at location6, spanner1 is not currently at location7, spanner1 is not currently at location8, spanner1 is not currently at shed, spanner1 is not located at location5, spanner1 is not located at location9, spanner2 is not at gate, spanner2 is not at location1, spanner2 is not at location4, spanner2 is not at location6, spanner2 is not currently at location2, spanner2 is not currently at location3, spanner2 is not currently at location7, spanner2 is not located at location8, spanner2 is not located at location9, spanner2 is not located at shed, spanner3 is not at location1, spanner3 is not at location3, spanner3 is not at location6, spanner3 is not currently at location4, spanner3 is not currently at location5, spanner3 is not currently at location8, spanner3 is not located at gate, spanner3 is not located at location7, spanner3 is not located at location9, spanner3 is not located at shed, spanner4 is not at gate, spanner4 is not at location3, spanner4 is not at location5, spanner4 is not at location8, spanner4 is not at location9, spanner4 is not currently at location1, spanner4 is not currently at location4, spanner4 is not currently at location7, spanner4 is not currently at shed, spanner4 is not located at location2, spanner5 is not at location2, spanner5 is not at location6, spanner5 is not at location7, spanner5 is not currently at gate, spanner5 is not currently at location5, spanner5 is not currently at location9, spanner5 is not located at location1, spanner5 is not located at location4, spanner5 is not located at location8 and spanner5 is not located at shed?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob moves from the shed to location1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: bob is not positioned at the gate, bob is not at location2, bob is not at location4, bob is not at location6, bob is not at location9, bob is not currently situated at location5, bob is not currently situated at location7, bob is not currently at the shed, bob is not situated at location3, bob is not situated at location8, nut1 is not positioned at location4, nut1 is not positioned at location5, nut1 is not positioned at location7, nut1 is not currently at location1, nut1 is not currently at location6, nut1 is not currently at location9, nut1 is not currently at the shed, nut1 is not situated at location2, nut1 is not situated at location3, nut1 is not situated at location8, nut2 is not positioned at location4, nut2 is not positioned at location5, nut2 is not at the shed, nut2 is not currently situated at location3, nut2 is not currently situated at location9, nut2 is not situated at location1, nut2 is not situated at location2, nut2 is not situated at location6, nut2 is not situated at location7, nut2 is not situated at location8, nut3 is not positioned at location2, nut3 is not positioned at location4, nut3 is not positioned at location6, nut3 is not positioned at location7, nut3 is not positioned at location8, nut3 is not positioned at location9, nut3 is not currently situated at location5, nut3 is not currently at the shed, nut3 is not situated at location1, nut3 is not situated at location3, nut4 is not positioned at location3, nut4 is not positioned at location6, nut4 is not positioned at location9, nut4 is not currently situated at location2, nut4 is not currently situated at location5, nut4 is not currently situated at location8, nut4 is not currently at the shed, nut4 is not situated at location1, nut4 is not situated at location4, nut4 is not situated at location7, nut5 is not positioned at location2, nut5 is not positioned at location4, nut5 is not positioned at location5, nut5 is not currently situated at location8, nut5 is not situated at location1, nut5 is not situated at location3, nut5 is not situated at location6, nut5 is not situated at location7, nut5 is not situated at location9, nut5 is not situated at the shed, spanner1 is not positioned at location2, spanner1 is not positioned at location4, spanner1 is not currently situated at the gate, spanner1 is not currently situated at location1, spanner1 is not currently situated at location6, spanner1 is not currently situated at location7, spanner1 is not currently situated at location8, spanner1 is not currently at the shed, spanner1 is not situated at location5, spanner1 is not situated at location9, spanner2 is not positioned at the gate, spanner2 is not positioned at location1, spanner2 is not positioned at location4, spanner2 is not positioned at location6, spanner2 is not currently situated at location2, spanner2 is not currently situated at location3, spanner2 is not currently situated at location7, spanner2 is not situated at location8, spanner2 is not situated at location9, spanner2 is not situated at the shed, spanner3 is not positioned at location1, spanner3 is not positioned at location3, spanner3 is not positioned at location6, spanner3 is not currently situated at location4, spanner3 is not currently situated at location5, spanner3 is not currently situated at location8, spanner3 is not situated at the gate, spanner3 is not situated at location7, spanner3 is not situated at location9, spanner3 is not situated at the shed, spanner4 is not positioned at the gate, spanner4 is not positioned at location3, spanner4 is not positioned at location5, spanner4 is not positioned at location8, spanner4 is not positioned at location9, spanner4 is not currently situated at location1, spanner4 is not currently situated at location4, spanner4 is not currently situated at location7, spanner4 is not currently at the shed, spanner4 is not situated at location2, spanner5 is not positioned at location2, spanner5 is not positioned at location6, spanner5 is not positioned at location7, spanner5 is not currently situated at the gate, spanner5 is not currently situated at location5, spanner5 is not currently situated at location9, spanner5 is not situated at location1, spanner5 is not situated at location4, spanner5 is not situated at location8 and spanner5 is not situated at the shed?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Furthermore, location5 is linked to location6, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, nut1 is present and unsecured, nut2 is also at the gate and loose, nut3 is at the gate and unsecured, nut4 is currently at the gate and unsecured, and nut5 is at the gate and loose. The shed is connected to location1. Spanner1 is located at location3 and is in working condition, spanner2 is at location5 and usable, spanner3 is located at location2 and can be used, spanner4 is at location6 and usable, and spanner5 is functional and located at location3."}
{"question_id": "27352103-9365-4f62-9be4-bdb6658aa0df", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_13", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks to location2 from location1, bob picks up spanner3 from location2, bob walks to location3 from location2, bob picks up spanner5 from location3, spanner1 is picked up by bob from location3, bob walks to location4 from location3, bob walks from location4 to location5, from location5, bob picks up spanner2, bob walks from location5 to location6, bob picks up spanner4 from location6, bob walks to location7 from location6, bob walks from location7 to location8, bob walks from location8 to location9, bob walks to gate from location9, nut1 is tightened by bob using spanner5 at gate, nut2 is tightened by bob using spanner4 at gate, nut3 is tightened by bob using spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, is it True or False that location6 is linked to location7?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: starting from the shed, bob proceeds to location1, then walks to location2, picks up spanner3 at location2, and then moves to location3. At location3, bob collects spanner5 and spanner1, before heading to location4, then location5, where he picks up spanner2. From location5, bob walks to location6, collects spanner4, and then proceeds to location7, followed by location8, location9, and finally the gate. Upon reaching the gate, bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, is it True or False that location6 is connected to location7?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Furthermore, location5 is linked to location6, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, nut1 is present but not secured, nut2 is also at the gate and is loose, nut3 is at the gate but not secured, nut4 is currently at the gate and not secured, and nut5 is at the gate and loose. The shed is connected to location1. Spanner1 is located at location3 and is in working condition, spanner2 is at location5 and can be used, spanner3 is at location2 and is functional, spanner4 is at location6 and is usable, and spanner5 is at location3 and is functional."}
{"question_id": "2cde76dc-8dd6-4b95-b27b-c6ea8f1ad757", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_21", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: a link between gate and location4 does not exist, a link between location1 and location6 does not exist, a link between location1 and location8 does not exist, a link between location2 and location5 does not exist, a link between location2 and location6 does not exist, a link between location2 and location7 does not exist, a link between location2 and location9 does not exist, a link between location3 and location1 does not exist, a link between location3 and location2 does not exist, a link between location3 and location8 does not exist, a link between location3 and shed does not exist, a link between location4 and location1 does not exist, a link between location4 and location6 does not exist, a link between location4 and location9 does not exist, a link between location5 and gate does not exist, a link between location5 and location1 does not exist, a link between location5 and location3 does not exist, a link between location5 and location8 does not exist, a link between location5 and location9 does not exist, a link between location5 and shed does not exist, a link between location6 and location3 does not exist, a link between location6 and location8 does not exist, a link between location6 and location9 does not exist, a link between location7 and location5 does not exist, a link between location7 and location9 does not exist, a link between location8 and location3 does not exist, a link between location8 and location6 does not exist, a link between location8 and location7 does not exist, a link between location9 and location1 does not exist, a link between location9 and location3 does not exist, a link between location9 and location7 does not exist, a link between shed and location4 does not exist, a link between shed and location7 does not exist, a link between shed and location9 does not exist, gate and location2 are not linked, gate and location6 are not linked, gate and location7 are not linked, gate and location9 are not linked, gate is not linked to location1, gate is not linked to location3, gate is not linked to location5, gate is not linked to location8, gate is not linked to shed, location1 and gate are not linked, location1 and location4 are not linked, location1 and location5 are not linked, location1 and location9 are not linked, location1 and shed are not linked, location1 is not linked to location3, location1 is not linked to location7, location2 and gate are not linked, location2 and location1 are not linked, location2 and location8 are not linked, location2 and shed are not linked, location2 is not linked to location4, location3 and location6 are not linked, location3 and location7 are not linked, location3 and location9 are not linked, location3 is not linked to gate, location3 is not linked to location5, location4 and location3 are not linked, location4 and shed are not linked, location4 is not linked to gate, location4 is not linked to location2, location4 is not linked to location7, location4 is not linked to location8, location5 and location7 are not linked, location5 is not linked to location2, location5 is not linked to location4, location6 and gate are not linked, location6 and location1 are not linked, location6 and location2 are not linked, location6 and location4 are not linked, location6 and shed are not linked, location6 is not linked to location5, location7 and gate are not linked, location7 and location1 are not linked, location7 and location2 are not linked, location7 is not linked to location3, location7 is not linked to location4, location7 is not linked to location6, location7 is not linked to shed, location8 and location1 are not linked, location8 and location2 are not linked, location8 and location4 are not linked, location8 and location5 are not linked, location8 and shed are not linked, location8 is not linked to gate, location9 and location2 are not linked, location9 and location4 are not linked, location9 and location6 are not linked, location9 and location8 are not linked, location9 is not linked to location5, location9 is not linked to shed, shed and gate are not linked, shed and location3 are not linked, shed and location5 are not linked, shed is not linked to location2, shed is not linked to location6 and shed is not linked to location8?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: there is no link between gate and location4, there is no link between location1 and location6, there is no link between location1 and location8, there is no link between location2 and location5, there is no link between location2 and location6, there is no link between location2 and location7, there is no link between location2 and location9, there is no link between location3 and location1, there is no link between location3 and location2, there is no link between location3 and location8, there is no link between location3 and shed, there is no link between location4 and location1, there is no link between location4 and location6, there is no link between location4 and location9, there is no link between location5 and gate, there is no link between location5 and location1, there is no link between location5 and location3, there is no link between location5 and location8, there is no link between location5 and location9, there is no link between location5 and shed, there is no link between location6 and location3, there is no link between location6 and location8, there is no link between location6 and location9, there is no link between location7 and location5, there is no link between location7 and location9, there is no link between location8 and location3, there is no link between location8 and location6, there is no link between location8 and location7, there is no link between location9 and location1, there is no link between location9 and location3, there is no link between location9 and location7, there is no link between shed and location4, there is no link between shed and location7, there is no link between shed and location9, gate and location2 are disconnected, gate and location6 are disconnected, gate and location7 are disconnected, gate and location9 are disconnected, gate is not connected to location1, gate is not connected to location3, gate is not connected to location5, gate is not connected to location8, gate is not connected to shed, location1 and gate are disconnected, location1 and location4 are disconnected, location1 and location5 are disconnected, location1 and location9 are disconnected, location1 and shed are disconnected, location1 is not connected to location3, location1 is not connected to location7, location2 and gate are disconnected, location2 and location1 are disconnected, location2 and location8 are disconnected, location2 and shed are disconnected, location2 is not connected to location4, location3 and location6 are disconnected, location3 and location7 are disconnected, location3 and location9 are disconnected, location3 is not connected to gate, location3 is not connected to location5, location4 and location3 are disconnected, location4 and shed are disconnected, location4 is not connected to gate, location4 is not connected to location2, location4 is not connected to location7, location4 is not connected to location8, location5 and location7 are disconnected, location5 is not connected to location2, location5 is not connected to location4, location6 and gate are disconnected, location6 and location1 are disconnected, location6 and location2 are disconnected, location6 and location4 are disconnected, location6 and shed are disconnected, location6 is not connected to location5, location7 and gate are disconnected, location7 and location1 are disconnected, location7 and location2 are disconnected, location7 is not connected to location3, location7 is not connected to location4, location7 is not connected to location6, location7 is not connected to shed, location8 and location1 are disconnected, location8 and location2 are disconnected, location8 and location4 are disconnected, location8 and location5 are disconnected, location8 and shed are disconnected, location8 is not connected to gate, location9 and location2 are disconnected, location9 and location4 are disconnected, location9 and location6 are disconnected, location9 and location8 are disconnected, location9 is not connected to location5, location9 is not connected to shed, shed and gate are disconnected, shed and location3 are disconnected, shed and location5 are disconnected, shed is not connected to location2, shed is not connected to location6 and shed is not connected to location8?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present but not secured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1, which is usable, is located at location4. Spanner2, which is functional, is currently at location7. Spanner3, also functional, is at location6. Spanner4, which is functional, and spanner5, which is usable, are both located at location1."}
{"question_id": "fa4d1e81-8304-441c-849d-4d514725e1ae", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_13", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, is it True or False that location4 is linked to location5?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location4 and location5 exists, a link between location9 and gate exists, bob is located at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location5 and location6 are linked, location6 is linked to location7, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is loose, shed and location1 are linked, spanner1 is at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 can be used, spanner3 is located at location2, spanner4 is located at location6, spanner4 is usable, spanner5 is functional and spanner5 is located at location3.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, is it True or False that location5 is connected to location4?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location9 and the gate. Bob is currently situated at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Furthermore, location5 is linked to location6, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, nut1 is present but not secured, nut2 is also at the gate and is loose, nut3 is at the gate but not secured, nut4 is currently at the gate and not secured, and nut5 is at the gate and loose. The shed is connected to location1. Spanner1 is located at location3 and is in working condition, spanner2 is at location5 and can be used, spanner3 is at location2 and is functional, spanner4 is at location6 and is usable, and spanner5 is at location3 and is functional."}
{"question_id": "b05f1c32-50ff-49ac-bddf-168fe1c8a88d", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_16", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob picks up spanner5 from location1, spanner4 is picked up by bob from location1, from location1 to location2, bob walks, bob walks from location2 to location3, from location3 to location4, bob walks, spanner1 is picked up by bob from location4, bob walks to location5 from location4, bob walks to location6 from location5 and bob picks up spanner3 from location6 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: bob is not at location3, bob is not at location8, bob is not at location9, bob is not currently at location4, bob is not currently at location7, bob is not located at gate, bob is not located at location1, bob is not located at location2, bob is not located at location5, bob is not located at location6, bob is not located at shed, nut1 is not at location1, nut1 is not at location2, nut1 is not at location3, nut1 is not at location6, nut1 is not at location9, nut1 is not at shed, nut1 is not currently at gate, nut1 is not currently at location4, nut1 is not currently at location5, nut1 is not currently at location7, nut1 is not currently at location8, nut2 is not at location4, nut2 is not at location5, nut2 is not at location9, nut2 is not at shed, nut2 is not currently at location2, nut2 is not currently at location3, nut2 is not currently at location6, nut2 is not currently at location7, nut2 is not currently at location8, nut2 is not located at gate, nut2 is not located at location1, nut3 is not at shed, nut3 is not currently at location1, nut3 is not currently at location3, nut3 is not currently at location4, nut3 is not currently at location7, nut3 is not currently at location8, nut3 is not located at gate, nut3 is not located at location2, nut3 is not located at location5, nut3 is not located at location6, nut3 is not located at location9, nut4 is not at gate, nut4 is not at location1, nut4 is not at location3, nut4 is not currently at location4, nut4 is not currently at location8, nut4 is not currently at shed, nut4 is not located at location2, nut4 is not located at location5, nut4 is not located at location6, nut4 is not located at location7, nut4 is not located at location9, nut5 is not at gate, nut5 is not at location5, nut5 is not at location9, nut5 is not currently at location1, nut5 is not currently at location2, nut5 is not currently at location8, nut5 is not currently at shed, nut5 is not located at location3, nut5 is not located at location4, nut5 is not located at location6, nut5 is not located at location7, spanner1 is not at location2, spanner1 is not at location6, spanner1 is not at location7, spanner1 is not at shed, spanner1 is not currently at gate, spanner1 is not currently at location1, spanner1 is not located at location3, spanner1 is not located at location4, spanner1 is not located at location5, spanner1 is not located at location8, spanner1 is not located at location9, spanner2 is not at gate, spanner2 is not at location1, spanner2 is not at location6, spanner2 is not currently at location2, spanner2 is not currently at location7, spanner2 is not currently at shed, spanner2 is not located at location3, spanner2 is not located at location4, spanner2 is not located at location5, spanner2 is not located at location8, spanner2 is not located at location9, spanner3 is not at location4, spanner3 is not at location6, spanner3 is not at shed, spanner3 is not currently at gate, spanner3 is not currently at location1, spanner3 is not currently at location2, spanner3 is not currently at location8, spanner3 is not currently at location9, spanner3 is not located at location3, spanner3 is not located at location5, spanner3 is not located at location7, spanner4 is not at gate, spanner4 is not at location1, spanner4 is not at location2, spanner4 is not at location3, spanner4 is not currently at location8, spanner4 is not currently at location9, spanner4 is not located at location4, spanner4 is not located at location5, spanner4 is not located at location6, spanner4 is not located at location7, spanner4 is not located at shed, spanner5 is not at location1, spanner5 is not at location4, spanner5 is not at location5, spanner5 is not at location9, spanner5 is not at shed, spanner5 is not currently at gate, spanner5 is not currently at location2, spanner5 is not currently at location3, spanner5 is not currently at location6, spanner5 is not currently at location7 and spanner5 is not currently at location8?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob moves from the shed to location1, picks up spanner5 from location1, then picks up spanner4 from location1, walks to location2, then to location3, and then to location4, picks up spanner1 from location4, walks to location5, then to location6, and finally picks up spanner3 from location6 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: bob is not at location3, bob is not at location8, bob is not at location9, bob is not currently at location4, bob is not currently at location7, bob is not located at the gate, bob is not located at location1, bob is not located at location2, bob is not located at location5, bob is not located at location6, bob is not located at the shed, nut1 is not at location1, nut1 is not at location2, nut1 is not at location3, nut1 is not at location6, nut1 is not at location9, nut1 is not at the shed, nut1 is not currently at the gate, nut1 is not currently at location4, nut1 is not currently at location5, nut1 is not currently at location7, nut1 is not currently at location8, nut2 is not at location4, nut2 is not at location5, nut2 is not at location9, nut2 is not at the shed, nut2 is not currently at location2, nut2 is not currently at location3, nut2 is not currently at location6, nut2 is not currently at location7, nut2 is not currently at location8, nut2 is not located at the gate, nut2 is not located at location1, nut3 is not at the shed, nut3 is not currently at location1, nut3 is not currently at location3, nut3 is not currently at location4, nut3 is not currently at location7, nut3 is not currently at location8, nut3 is not located at the gate, nut3 is not located at location2, nut3 is not located at location5, nut3 is not located at location6, nut3 is not located at location9, nut4 is not at the gate, nut4 is not at location1, nut4 is not at location3, nut4 is not currently at location4, nut4 is not currently at location8, nut4 is not currently at the shed, nut4 is not located at location2, nut4 is not located at location5, nut4 is not located at location6, nut4 is not located at location7, nut4 is not located at location9, nut5 is not at the gate, nut5 is not at location5, nut5 is not at location9, nut5 is not currently at location1, nut5 is not currently at location2, nut5 is not currently at location8, nut5 is not currently at the shed, nut5 is not located at location3, nut5 is not located at location4, nut5 is not located at location6, nut5 is not located at location7, spanner1 is not at location2, spanner1 is not at location6, spanner1 is not at location7, spanner1 is not at the shed, spanner1 is not currently at the gate, spanner1 is not currently at location1, spanner1 is not located at location3, spanner1 is not located at location4, spanner1 is not located at location5, spanner1 is not located at location8, spanner1 is not located at location9, spanner2 is not at the gate, spanner2 is not at location1, spanner2 is not at location6, spanner2 is not currently at location2, spanner2 is not currently at location7, spanner2 is not currently at the shed, spanner2 is not located at location3, spanner2 is not located at location4, spanner2 is not located at location5, spanner2 is not located at location8, spanner2 is not located at location9, spanner3 is not at location4, spanner3 is not at location6, spanner3 is not at the shed, spanner3 is not currently at the gate, spanner3 is not currently at location1, spanner3 is not currently at location2, spanner3 is not currently at location8, spanner3 is not currently at location9, spanner3 is not located at location3, spanner3 is not located at location5, spanner3 is not located at location7, spanner4 is not at the gate, spanner4 is not at location1, spanner4 is not at location2, spanner4 is not at location3, spanner4 is not currently at location8, spanner4 is not currently at location9, spanner4 is not located at location4, spanner4 is not located at location5, spanner4 is not located at location6, spanner4 is not located at location7, spanner4 is not located at the shed, spanner5 is not at location1, spanner5 is not at location4, spanner5 is not at location5, spanner5 is not at location9, spanner5 is not at the shed, spanner5 is not currently at the gate, spanner5 is not currently at location2, spanner5 is not currently at location3, spanner5 is not currently at location6, spanner5 is not currently at location7, and spanner5 is not currently at location8?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present and unsecured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1, which is usable, is located at location4. Spanner2, which is functional, is currently at location7. Spanner3, also functional, is at location6. Spanner4, which is functional, and spanner5, which is usable, are both located at location1."}
{"question_id": "e4176cf9-235e-456e-b3cd-ecc1ac218e85", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_5", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob picks up spanner5 from location1, bob picks up spanner4 from location1, bob walks to location2 from location1, bob walks from location2 to location3, from location3 to location4, bob walks, bob picks up spanner1 from location4, bob walks from location4 to location5, bob walks from location5 to location6 and spanner3 is picked up by bob from location6 to reach the current state. In this state, is it True or False that spanner3 can be used?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions takes place: Bob starts at the shed and walks to location1, where he collects spanner5 and spanner4. He then proceeds to location2, followed by location3, and then location4, where he picks up spanner1. From there, he walks to location5 and then to location6, where he retrieves spanner3. In this current state, is it True or False that spanner3 is available for use?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present but not secured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1, which is usable, is located at location4. Spanner2, which is functional, is currently at location7. Spanner3, also functional, is at location6. Spanner4, which is functional, and spanner5, which is usable, are both located at location1."}
{"question_id": "6c4de010-ef82-43fa-9628-4c99f67bb815", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_15", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, from location1, bob picks up spanner5, bob picks up spanner4 from location1, bob walks to location2 from location1, bob walks from location2 to location3, bob walks to location4 from location3, spanner1 is picked up by bob from location4, bob walks to location5 from location4, from location5 to location6, bob walks and bob picks up spanner3 from location6 to reach the current state. In this state, is it True or False that a link between shed and location5 does not exist?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: bob moves from the shed to location1, then from location1, bob collects spanner5, followed by picking up spanner4 from location1, then bob proceeds to location2 from location1, then to location3 from location2, then to location4 from location3, where bob picks up spanner1, then bob moves to location5 from location4, then to location6 from location5, and finally, bob collects spanner3 from location6 to reach the current state. In this state, is it True or False that there is no connection between the shed and location5?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 is connected to location2, and location2 is linked to location3, which in turn is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present and unsecured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1, which is usable, is located at location4. Spanner2, which is functional, is currently at location7. Spanner3, also functional, is at location6. Spanner4, which is functional, and spanner5, which is usable, are both located at location1."}
{"question_id": "a6e3078e-553e-4e9d-aba7-d5bb34e5e566", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_2", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, is it True or False that tightening of nut3 is complete?", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, is the tightening of nut3 complete, True or False?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present and unsecured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1, which is usable, is located at location4. Spanner2, which is functional, is currently at location7. Spanner3, also functional, is at location6. Spanner4, which is functional, and spanner5, which is usable, are both located at location1."}
{"question_id": "b8e0d013-2eff-41ba-ba92-a141f4101dc6", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_4", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks from location1 to location2, bob walks from location2 to location3, bob walks to location4 from location3, bob walks from location4 to location5, from location5, bob picks up spanner4, from location5 to location6, bob walks, bob picks up spanner1 from location6, bob walks from location6 to location7, bob picks up spanner5 from location7, bob walks from location7 to location8, bob picks up spanner3 from location8, from location8, bob picks up spanner2, bob walks to location9 from location8, bob walks to gate from location9, bob tightens nut1 with spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, nut3 is tightened by bob using spanner3 at gate and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: bob is not carrying spanner4, nut3 is not tightened, spanner1 is not carried by bob, spanner2 is not carried by bob, spanner3 is not carried by bob, spanner5 is not carried by bob, tightening of nut1 is incomplete, tightening of nut2 is incomplete, tightening of nut4 is incomplete and tightening of nut5 is incomplete?", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location3 and location4 exists, a link between location4 and location5 exists, bob is at shed, location2 and location3 are linked, location5 is linked to location6, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, location9 and gate are linked, nut1 is at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is located at gate, nut4 is loose, nut5 is located at gate, nut5 is loose, shed and location1 are linked, spanner1 is located at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is usable, spanner3 can be used, spanner3 is located at location8, spanner4 can be used, spanner4 is at location5, spanner5 is currently at location7 and spanner5 is usable.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob walks from the shed to location1, then from location1 to location2, followed by location2 to location3, then location3 to location4, and location4 to location5. At location5, Bob picks up spanner4 and proceeds to location6, where he picks up spanner1. He then walks to location7, picks up spanner5, and continues to location8, where he picks up spanner3 and spanner2. From location8, Bob walks to location9 and then to the gate. At the gate, he uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, are the following properties that involve negations True or False: Bob is not holding spanner4, nut3 remains untightened, Bob is not carrying spanner1, Bob is not carrying spanner2, Bob is not carrying spanner3, Bob is not carrying spanner5, the tightening of nut1 is incomplete, the tightening of nut2 is incomplete, the tightening of nut4 is incomplete, and the tightening of nut5 is incomplete?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location3 and location4, and a connection between location4 and location5. Bob is currently at the shed. Additionally, location2 is connected to location3, location5 is connected to location6, location6 is connected to location7, location7 is connected to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, there are several loose nuts: nut1, nut2, nut3 (which is not secured), nut4, and nut5. Furthermore, the shed is connected to location1. There are several usable spanners: spanner1 is at location6, spanner2 and spanner3 are at location8, spanner4 is at location5, and spanner5 is at location7."}
{"question_id": "89449a18-110a-4393-baa3-974bb05dffaa", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_1", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks from location1 to location2, from location2, bob picks up spanner4, from location2, bob picks up spanner3, bob walks to location3 from location2, bob walks from location3 to location4, bob walks from location4 to location5, from location5 to location6, bob walks, bob picks up spanner5 from location6, from location6, bob picks up spanner2, bob walks to location7 from location6, bob walks from location7 to location8, from location8, bob picks up spanner1, bob walks to location9 from location8, bob walks from location9 to gate, bob tightens nut1 with spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, at gate, bob uses spanner3 to tighten nut3 and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: bob is carrying spanner2, bob is carrying spanner3, bob is carrying spanner4, bob is carrying spanner5, nut2 is tightened, nut3 is tightened, spanner1 is carried by bob, tightening of nut1 is complete and tightening of nut4 is complete?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then from location1 to location2. At location2, Bob collects spanner4 and spanner3. He then proceeds to location3 from location2, followed by location4, and then location5, and subsequently location6. From location6, Bob picks up spanner5 and spanner2. He then walks to location7 from location6, then to location8, where he collects spanner1. Bob then moves to location9 from location8 and finally to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, are the following properties, which do not involve negations, True or False: Bob has spanner2, Bob has spanner3, Bob has spanner4, Bob has spanner5, nut2 is tightened, nut3 is tightened, Bob is carrying spanner1, the tightening of nut1 is complete, and the tightening of nut4 is complete?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, and a connection also exists between location3 and location4. Additionally, a connection is present between location5 and location6. Bob is currently situated at the shed. Furthermore, location4 and location5 are interconnected, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. Location9, in turn, is linked to the gate. At the gate, nut1 is located and is not secured. Similarly, nut2 is currently at the gate and is loose, nut3 is also located at the gate and is loose, nut4 is at the gate and is not secured, and nut5 is at the gate and is not secured. The shed is connected to location1. Spanner1 is functional and is located at location8. Spanner2 is situated at location6 and is usable. Spanner3 is functional and is currently at location2, spanner4 is also functional and is located at location2, and spanner5 is functional and is located at location6."}
{"question_id": "65c99581-7b90-4867-86ec-3d30c54e4ed4", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_3", "fluent_type": "base_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, is it True or False that nut5 is not tightened?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location4 and location5 exists, a link between location5 and location6 exists, bob is at shed, location1 and location2 are linked, location2 and location3 are linked, location3 is linked to location4, location6 and location7 are linked, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is currently at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is currently at gate, nut5 is loose, shed is linked to location1, spanner1 can be used, spanner1 is located at location4, spanner2 is currently at location7, spanner2 is functional, spanner3 is currently at location6, spanner3 is functional, spanner4 is at location1, spanner4 is functional, spanner5 can be used and spanner5 is located at location1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, is it True or False that nut5 remains untightened?", "initial_state_nl_paraphrased": "There is a connection between location4 and location5, and another connection exists between location5 and location6. Bob is currently at the shed. Location1 and location2 are connected, location2 is linked to location3, and location3 is connected to location4. Additionally, location6 is linked to location7, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present but not secured. Also at the gate are nut2, nut3, nut4, and nut5, all of which are loose. The shed is connected to location1. Spanner1, which is usable, is located at location4. Spanner2, which is functional, is currently at location7. Spanner3, also functional, is at location6. Spanner4, which is functional, and spanner5, which is usable, are both located at location1."}
{"question_id": "7fa10701-2516-43c0-8ccd-65356a4b53df", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_19", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks from location1 to location2, from location2, bob picks up spanner4, spanner3 is picked up by bob from location2, from location2 to location3, bob walks, from location3 to location4, bob walks, from location4 to location5, bob walks, from location5 to location6, bob walks, bob picks up spanner5 from location6, bob picks up spanner2 from location6, bob walks from location6 to location7, from location7 to location8, bob walks, from location8, bob picks up spanner1, bob walks from location8 to location9, bob walks from location9 to gate, bob tightens nut1 with spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, bob tightens nut3 with spanner3 at gate and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: a link between location1 and location2 exists, a link between location3 and location4 exists, a link between location6 and location7 exists, a link between location8 and location9 exists, a link between location9 and gate exists, a link between shed and location1 exists, location2 and location3 are linked, location4 is linked to location5, location5 and location6 are linked and location7 and location8 are linked?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location2 and location3 exists, a link between location3 and location4 exists, a link between location5 and location6 exists, bob is currently at shed, location4 and location5 are linked, location6 is linked to location7, location7 is linked to location8, location8 and location9 are linked, location9 is linked to gate, nut1 is located at gate, nut1 is not secured, nut2 is currently at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is currently at gate, nut5 is not secured, shed and location1 are linked, spanner1 can be used, spanner1 is located at location8, spanner2 is located at location6, spanner2 is usable, spanner3 can be used, spanner3 is currently at location2, spanner4 can be used, spanner4 is located at location2, spanner5 is functional and spanner5 is located at location6.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then from location1 to location2, where he collects spanner4 and spanner3. He then proceeds from location2 to location3, followed by location4, location5, and location6, where he picks up spanner5 and spanner2. Next, Bob walks from location6 to location7, then to location8, where he collects spanner1. He continues from location8 to location9 and finally to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, are the following properties, which do not involve negations, True or False: a connection exists between location1 and location2, a connection exists between location3 and location4, a connection exists between location6 and location7, a connection exists between location8 and location9, a connection exists between location9 and the gate, a connection exists between the shed and location1, location2 and location3 are connected, location4 is connected to location5, location5 and location6 are connected, and location7 and location8 are connected?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, another connection exists between location2 and location3, and a connection also exists between location3 and location4. Additionally, a connection is present between location5 and location6. Bob is currently situated at the shed. Furthermore, location4 and location5 are interconnected, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. Location9, in turn, is linked to the gate. At the gate, nut1 is located and is not secured. Similarly, nut2 is currently at the gate and is loose, nut3 is also located at the gate and is loose, nut4 is at the gate and is not secured, and nut5 is at the gate and is not secured. The shed is connected to location1. Spanner1 is functional and is located at location8. Spanner2 is situated at location6 and is usable. Spanner3 is functional and is currently at location2, spanner4 is also functional and is located at location2, and spanner5 is functional and is located at location6."}
