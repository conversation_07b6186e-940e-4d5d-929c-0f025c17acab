{"question_id": "694bdd72-6ece-454f-92d7-788d4a9eb74d", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at gate, bob uses spanner2 to tighten nut4, bob picks up spanner1 from location4, bob tightens nut1 with spanner5 at gate, bob tightens nut2 with spanner4 at gate, bob tightens nut5 with spanner1 at location8, bob walks from location5 to location6, bob walks from location8 to location9, bob walks to location2 from location1, bob walks to location3 from location2, bob walks to location4 from location3, bob walks to location5 from location4, bob walks to location7 from location6, bob walks to location8 from location7, from location1, bob picks up spanner4, from location9 to gate, bob walks, from shed to location1, bob walks, nut3 is tightened by bob using spanner3 at gate, spanner2 is picked up by bob from location7 and spanner5 is picked up by bob from location1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned: at the gate, bob will use spanner2 to secure nut4, then bob will collect spanner1 from location4, next bob will use spanner5 to tighten nut1 at the gate, followed by bob using spanner4 to tighten nut2 at the gate, then bob will use spanner1 to tighten nut5 at location8, meanwhile bob will move from location5 to location6, from location8 to location9, from location1 to location2, from location2 to location3, from location3 to location4, from location4 to location5, from location6 to location7, and from location7 to location8, also from location1, bob will pick up spanner4, then bob will walk from location9 to the gate, and from the shed to location1, in addition, bob will use spanner3 to tighten nut3 at the gate, and bob will collect spanner2 from location7 and spanner5 from location1. Is it possible to execute it, True or False?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and another connection exists between location6 and location7. Bob is currently situated at the shed. Additionally, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location7 is linked to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is unsecured, nut2, which is loose, nut3, which is also loose, nut4, which is loose, and nut5, which is loose as well. The shed is connected to location1. The following spanners are located at their respective locations: spanner1, which is functional, is at location4, spanner2, which is functional, is at location7, spanner3, which can be used, is at location6, spanner4, which can be used, is at location1, and spanner5, which is functional, is also at location1."}
{"question_id": "64641bd2-7ec1-407b-b3f0-3c0ceffc1aca", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location4, bob uses spanner5 to tighten nut1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: at location4, bob will utilize spanner5 to tighten nut1. Can this be executed, True or False?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and another connection exists between location6 and location7. Bob is presently at the shed. Additionally, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present but not secured, while nut2, nut3, nut4, and nut5 are all located at the gate and are loose. The shed is connected to location1. Spanner1 is currently at location4 and is in working condition, spanner2 is functional and located at location7, spanner3 is usable and at location6, spanner4 is usable and at location1, and spanner5 is functional and also located at location1."}
{"question_id": "33d9abc9-3465-4fbe-a34d-b4103da89a1d", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at gate, bob uses spanner5 to tighten nut1, bob picks up spanner1 from location6, bob tightens nut3 with spanner3 at gate, bob walks from location2 to location3, bob walks from location6 to location7, bob walks from location8 to location9, bob walks from location9 to gate, bob walks to location1 from shed, bob walks to location4 from location3, bob walks to location5 from location4, bob walks to location6 from location5, bob walks to location8 from location7, from location1 to location2, bob walks, from location8, bob picks up spanner2, nut2 is tightened by bob using spanner4 at gate, nut4 is tightened by bob using spanner2 at gate, spanner3 is picked up by bob from location8, spanner4 is picked up by bob from location5 and spanner5 is picked up by bob from location7. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is currently at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is functional, spanner3 can be used, spanner3 is currently at location8, spanner4 can be used, spanner4 is currently at location5, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned: at the gate, Bob will use spanner5 to secure nut1, then Bob will collect spanner1 from location6, followed by tightening nut3 with spanner3 at the gate. Additionally, Bob will move between various locations: from location2 to location3, location6 to location7, location8 to location9, location9 to the gate, the shed to location1, location3 to location4, location4 to location5, location5 to location6, and location7 to location8. Furthermore, Bob will also move from location1 to location2 and from location8, he will pick up spanner2. At the gate, Bob will use spanner4 to tighten nut2 and spanner2 to tighten nut4. Bob will also collect spanner3 from location8, spanner4 from location5, and spanner5 from location7. Is it possible to execute this plan, True or False?", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, a connection between location9 and the gate exists, and a connection between the shed and location1 is present. Bob is currently at the shed. Location1 is connected to location2, location2 is linked to location3, location3 is connected to location4, and location4 is connected to location5. Furthermore, location6 is connected to location7, location7 is connected to location8, and location8 is linked to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is not secured, nut4, which is not secured, and nut5, which is loose. The current locations of the spanners are as follows: spanner1 is at location6 and is in working condition, spanner2 is at location8 and is functional, spanner3 is at location8 and can be used, spanner4 is at location5 and is usable, and spanner5 is at location7 and can be used."}
{"question_id": "0256e5b7-d148-4c5a-b123-c6617100892e", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location8, bob uses spanner1 to tighten nut5, bob picks up spanner1 from location3, bob picks up spanner3 from location2, bob walks to location2 from location1, bob walks to location4 from location3, from location2 to location3, bob walks, from location5 to location6, bob walks, from shed to location1, bob walks, spanner2 is picked up by bob from location5 and spanner5 is picked up by bob from location3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location2 and location3 exists, a link between location6 and location7 exists, bob is located at shed, location1 and location2 are linked, location3 and location4 are linked, location4 is linked to location5, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 is currently at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 is at location2, spanner3 is usable, spanner4 is currently at location6, spanner4 is functional, spanner5 can be used and spanner5 is at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are scheduled: at location8, bob will use spanner1 to tighten nut5, bob will retrieve spanner1 from location3, bob will collect spanner3 from location2, bob will move from location1 to location2, bob will proceed from location3 to location4, bob will walk from location2 to location3, bob will walk from location5 to location6, and from the shed to location1, bob will also walk, additionally, bob will pick up spanner2 from location5 and spanner5 from location3. Is the execution of these actions feasible, True or False?", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, and another connection exists between location6 and location7. Bob is situated at the shed. Location1 is connected to location2, location3 is linked to location4, location4 is connected to location5, location5 is linked to location6, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. Nut1, nut2, nut3, and nut4 are all located at the gate, and each of them is loose. Additionally, nut5 is currently at the gate but is not secured. The shed is connected to location1. Spanner1 is currently situated at location3 and is in working condition. Spanner2 is at location5 and is usable, while spanner3 is at location2 and is also usable. Spanner4 is currently at location6 and is functional. Furthermore, spanner5 is located at location3 and can be used."}
{"question_id": "937c4cc4-2892-4945-aa16-a10defc31ce6", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: bob picks up spanner3 from location2, bob walks from location3 to location4, bob walks to location1 from shed, bob walks to location2 from location1, bob walks to location5 from location4, from location2 to location3, bob walks, from location5 to location6, bob walks, from location6, bob picks up spanner5, spanner2 is picked up by bob from location6 and spanner4 is picked up by bob from location2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location6 and location7 exists, a link between location9 and gate exists, bob is at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, shed is linked to location1, spanner1 is at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 can be used, spanner4 is currently at location2, spanner5 is located at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: bob retrieves spanner3 from location2, bob moves from location3 to location4, bob proceeds from the shed to location1, then from location1 to location2, and from location4 to location5, then from location2 to location3, bob walks, then from location5 to location6, bob walks again, from location6, bob collects spanner5, bob also picks up spanner2 from location6 and spanner4 from location2. Can this sequence be executed, True or False?", "initial_state_nl_paraphrased": "There is a connection between location6 and location7, and another connection exists between location9 and the gate. Bob is currently at the shed. The locations are interconnected as follows: location1 is connected to location2, location2 is connected to location3, location3 is linked with location4, location4 is linked with location5, and location5 is connected to location6. Additionally, location7 is connected to location8, and location8 is connected to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is loose, nut4, which is not secured, and nut5, which is loose. The shed is connected to location1. The spanners are located as follows: spanner1 is at location8 and is in working condition, spanner2 is at location6 and is functional, spanner3 is at location2 and is usable, spanner4 is also at location2 and can be used, and spanner5 is at location6 and is usable."}
{"question_id": "0062c7cd-5439-4ee0-aefb-5afca0ba8866", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: bob picks up spanner4 from location5, bob walks from location1 to location2, bob walks from location2 to location3, bob walks from location4 to location5, bob walks from location5 to location6, bob walks to location1 from shed, bob walks to location4 from location3, from location6 to location7, bob walks, spanner1 is picked up by bob from location6 and spanner5 is picked up by bob from location7. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is currently at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is functional, spanner3 can be used, spanner3 is currently at location8, spanner4 can be used, spanner4 is currently at location5, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled: bob retrieves spanner4 from location5, bob moves from location1 to location2, then from location2 to location3, then from location4 to location5, and from location5 to location6, bob proceeds from the shed to location1, then from location3 to location4, and from location6 to location7, bob walks to location1 from the shed, and finally, bob collects spanner1 from location6 and spanner5 from location7. Can this sequence be executed, True or False?", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, a connection between location9 and the gate exists, and a connection between the shed and location1 is present. Bob is currently situated at the shed. Location1 is connected to location2, location2 is linked to location3, location3 is connected to location4, and location4 is connected to location5. Furthermore, location6 is connected to location7, location7 is connected to location8, and location8 is linked to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is not secured, nut4, which is not secured, and nut5, which is loose. The current locations of the spanners are as follows: spanner1 is at location6 and is in working condition, spanner2 is at location8 and is functional, spanner3 is at location8 and can be used, spanner4 is at location5 and is usable, and spanner5 is at location7 and can be used."}
{"question_id": "87169313-3465-4a69-a9b2-6e56ae1457d3", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: bob walks to location1 from shed. Is the action: bob walks from shed to location1 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is currently at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is functional, spanner3 can be used, spanner3 is currently at location8, spanner4 can be used, spanner4 is currently at location5, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: bob walks to location1 from shed. Is the action: bob walks from shed to location1 executable at step 1, True or False?\n\nParaphrased text: \nGiven the initial condition, for steps 1 through 1 the following actions are planned to be performed: bob walks to location1 from shed. Can the action 'bob walks from shed to location1' be executed at step 1, True or False?", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, a connection between location9 and the gate exists, and a connection between the shed and location1 is present. Bob is currently situated at the shed. Location1 is connected to location2, location2 is linked to location3, location3 is connected to location4, and location4 is connected to location5. Furthermore, location6 is connected to location7, location7 is connected to location8, and location8 is linked to location9. At the gate, the following items are located: nut1, which is loose, nut2, also loose, nut3, which is not secured, nut4, also not secured, and nut5, which is loose. The current locations of the spanners are as follows: spanner1 is at location6 and is in working condition, spanner2 is at location8 and is functional, spanner3 is at location8 and can be used, spanner4 is at location5 and is usable, and spanner5 is at location7 and can be used."}
{"question_id": "31030fcd-9d46-47d8-b313-c7a1515b5115", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: from shed to location1, bob walks, from location1 to location2, bob walks, bob walks to shed from gate, bob picks up spanner3 from location2, bob walks from location2 to location3, bob walks to location4 from location3, bob walks to location5 from location4, bob walks to location6 from location5, spanner5 is picked up by bob from location6 and spanner2 is picked up by bob from location6. Is the action: bob walks to shed from gate executable at step 3, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location6 and location7 exists, a link between location9 and gate exists, bob is at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, shed is linked to location1, spanner1 is at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 can be used, spanner4 is currently at location2, spanner5 is located at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: bob walks from the shed to location1, then from location1 to location2, then from location2 to the shed via the gate, then picks up spanner3 at location2, then proceeds to location3 from location2, then to location4 from location3, then to location5 from location4, then to location6 from location5, and finally picks up both spanner5 and spanner2 at location6. Is the action of bob walking to the shed from the gate executable at step 3, True or False?", "initial_state_nl_paraphrased": "There is a connection between location6 and location7, and another connection exists between location9 and the gate. Bob is currently at the shed. The locations are interconnected as follows: location1 is connected to location2, location2 is connected to location3, location3 is linked with location4, location4 is linked with location5, and location5 is connected to location6. Additionally, location7 is connected to location8, and location8 is connected to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is loose, nut4, which is not secured, and nut5, which is loose. The shed is connected to location1. The following spanners are located at the respective locations: spanner1 is at location8 and is usable, spanner2 is at location6 and is functional, spanner3 is at location2 and is usable, spanner4 is at location2 and can be used, and spanner5 is at location6 and is usable."}
{"question_id": "49939525-6be3-432d-a40e-5fec7143844d", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from location4 to location9, bob walks. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location2 and location3 exists, a link between location6 and location7 exists, bob is located at shed, location1 and location2 are linked, location3 and location4 are linked, location4 is linked to location5, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 is currently at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 is at location2, spanner3 is usable, spanner4 is currently at location6, spanner4 is functional, spanner5 can be used and spanner5 is at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: bob will walk from location4 to location9. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, and another connection exists between location6 and location7. Bob is situated at the shed. Location1 is connected to location2, location3 is linked to location4, location4 is connected to location5, location5 is linked to location6, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. Nut1, nut2, nut3, and nut4 are all located at the gate, and each of them is loose. Additionally, nut5 is currently at the gate but is not secure. The shed is connected to location1. Spanner1 is currently situated at location3 and is in working condition. Spanner2 is at location5 and is usable, while spanner3 is at location2 and is also usable. Spanner4 is currently at location6 and is functional. Furthermore, spanner5 is both usable and located at location3."}
{"question_id": "87de9234-99fe-4719-8038-d948e6b4ffb2", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from shed to location1, bob walks. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: from the shed to location1, bob walks. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and another connection exists between location6 and location7. Bob is currently situated at the shed. Additionally, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location7 is linked to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is unsecured, nut2, which is loose, nut3, which is also loose, nut4, which is loose, and nut5, which is loose as well. The shed is connected to location1. The following spanners are located at their respective locations: spanner1, which is functional, is at location4, spanner2, which is functional, is at location7, spanner3, which can be used, is at location6, spanner4, which can be used, is at location1, and spanner5, which is functional, is also at location1."}
{"question_id": "ccb26e91-5831-40ce-9293-6be3ecc5c87b", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: bob picks up spanner1 from location4, bob picks up spanner5 from location1, bob walks from location1 to location2, bob walks from location2 to location3, bob walks from location3 to location4, bob walks from location4 to location5, bob walks to location6 from location5, from location1, bob picks up spanner4, from location6, bob picks up spanner3 and from shed to location1, bob walks. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: Bob retrieves spanner1 from location4, Bob retrieves spanner5 from location1, Bob moves from location1 to location2, Bob moves from location2 to location3, Bob moves from location3 to location4, Bob moves from location4 to location5, Bob proceeds from location5 to location6, from location1, Bob picks up spanner4, from location6, Bob picks up spanner3, and from the shed, Bob walks to location1. Is the execution of these actions feasible, True or False?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and another connection exists between location6 and location7. Bob is currently situated at the shed. Additionally, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location7 is linked to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is unsecured, nut2, which is loose, nut3, which is also loose, nut4, which is loose, and nut5, which is loose as well. The shed is connected to location1. The following spanners are located at their respective locations: spanner1, which is functional, is at location4, spanner2, which is functional, is at location7, spanner3, which can be used, is at location6, spanner4, which can be used, is at location1, and spanner5, which is functional, is also at location1."}
{"question_id": "9a6c50f6-28af-4891-978a-a2173765ea47", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: nut3 is tightened by bob using spanner1 at location4. Is the action: nut3 is tightened by bob using spanner1 at location4 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is currently at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is functional, spanner3 can be used, spanner3 is currently at location8, spanner4 can be used, spanner4 is currently at location5, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: bob will tighten nut3 with spanner1 at location4. Is the action of bob tightening nut3 with spanner1 at location4 executable at step 1, True or False?", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, a connection between location9 and the gate exists, and a connection between the shed and location1 is present. Bob is currently situated at the shed. Location1 is connected to location2, location2 is linked to location3, location3 is connected to location4, and location4 is connected to location5. Furthermore, location6 is connected to location7, location7 is connected to location8, and location8 is linked to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is not secured, nut4, which is not secured, and nut5, which is loose. The current locations of the spanners are as follows: spanner1 is at location6 and is in working condition, spanner2 is at location8 and is functional, spanner3 is at location8 and can be used, spanner4 is at location5 and is usable, and spanner5 is at location7 and can be used."}
{"question_id": "5e5945ae-6f79-4bdb-a16a-0c3667214267", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: bob walks from location8 to location5. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location6 and location7 exists, a link between location9 and gate exists, bob is at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, shed is linked to location1, spanner1 is at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 can be used, spanner4 is currently at location2, spanner5 is located at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: bob moves from location8 to location5. Can it be executed, True or False?", "initial_state_nl_paraphrased": "There is a connection between location6 and location7, and another connection exists between location9 and the gate. Bob is currently at the shed. The locations are interconnected as follows: location1 is connected to location2, location2 is connected to location3, location3 is linked with location4, location4 is linked with location5, and location5 is connected to location6. Additionally, location7 is connected to location8, and location8 is connected to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is loose, nut4, which is not secured, and nut5, which is loose. The shed is connected to location1. The following spanners are located at the respective locations: spanner1 is at location8 and is usable, spanner2 is at location6 and is functional, spanner3 is at location2 and is usable, spanner4 is at location2 and can be used, and spanner5 is at location6 and is usable."}
{"question_id": "d00094b3-eb56-43cb-9658-26d422e892e6", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: bob picks up spanner1 from location3, bob picks up spanner2 from location5, bob walks from location5 to location6, from location1 to location2, bob walks, from location2 to location3, bob walks, from location3 to location4, bob walks, from location3, bob picks up spanner5, from location4 to location5, bob walks, from shed to location1, bob walks and spanner3 is picked up by bob from location2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location2 and location3 exists, a link between location6 and location7 exists, bob is located at shed, location1 and location2 are linked, location3 and location4 are linked, location4 is linked to location5, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 is currently at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 is at location2, spanner3 is usable, spanner4 is currently at location6, spanner4 is functional, spanner5 can be used and spanner5 is at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: bob retrieves spanner1 from location3, bob retrieves spanner2 from location5, bob moves from location5 to location6, then from location1 to location2, bob proceeds, then from location2 to location3, bob moves again, then from location3 to location4, bob continues walking, then from location3, bob collects spanner5, then from location4 to location5, bob walks, then from the shed to location1, bob walks, and bob also picks up spanner3 from location2. Is the execution of this sequence feasible, True or False?", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, and another connection exists between location6 and location7. Bob is situated at the shed. Location1 is connected to location2, location3 is linked to location4, location4 is connected to location5, location5 is linked to location6, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. Nut1, nut2, nut3, and nut4 are all located at the gate, and each of them is loose. Additionally, nut5 is currently at the gate but is not secure. The shed is connected to location1. Spanner1 is currently situated at location3 and is in working condition. Spanner2 is at location5 and is usable, while spanner3 is at location2 and is also usable. Spanner4 is currently at location6 and is functional. Furthermore, spanner5 is located at location3 and is available for use."}
{"question_id": "741ccf7b-6fbf-4d67-b50b-4bb06220240f", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: at location3, bob uses spanner3 to tighten nut5. Is the action: nut5 is tightened by bob using spanner3 at location3 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are scheduled: at location3, bob will utilize spanner3 to tighten nut5. Is the action: nut5 is tightened by bob using spanner3 at location3 executable at step 1, True or False?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and a connection between location6 and location7 also exists. Bob is presently at the shed. Additionally, location2 is connected to location3, location3 is connected to location4, and location4 is connected to location5. Furthermore, location7 is connected to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is unsecured, nut2, which is loose, nut3, which is loose, nut4, which is loose, and nut5, which is also loose. The shed is connected to location1. The following spanners are located at their respective locations: spanner1, which is functional, is at location4, spanner2, which is functional, is at location7, spanner3, which can be used, is at location6, spanner4, which can be used, is at location1, and spanner5, which is functional, is also at location1."}
{"question_id": "641d08bf-9d16-4a71-8d6f-e3c239330441", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: bob walks to location1 from shed, from location1 to location2, bob walks, from location2 to location3, bob walks, bob walks from location3 to location4, from location4 to location5, bob walks, from location5, bob picks up spanner4, bob walks to location6 from location5, bob picks up spanner1 from location6, from location6 to location7, bob walks, spanner5 is picked up by bob from location7, from location7 to location8, bob walks, from location8, bob picks up spanner3, bob picks up spanner2 from location8, bob walks from location8 to location9, bob walks to gate from location9, bob tightens nut1 with spanner5 at gate, bob tightens nut2 with spanner4 at gate, nut3 is tightened by bob using spanner3 at gate and nut4 is tightened by bob using spanner2 at gate. Is the action: nut4 is tightened by bob using spanner2 at gate executable at step 19, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is currently at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is functional, spanner3 can be used, spanner3 is currently at location8, spanner4 can be used, spanner4 is currently at location5, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: Bob moves from the shed to location1, then to location2, walks, proceeds from location2 to location3, walks again, and walks from location3 to location4. He then moves from location4 to location5, walks, and picks up spanner4 from location5. Next, Bob walks from location5 to location6, picks up spanner1 from location6, and moves from location6 to location7. He then walks and picks up spanner5 from location7. From location7, Bob walks to location8, picks up spanner3 and spanner2 from location8, and then walks from location8 to location9. Finally, Bob walks from location9 to the gate, where he tightens nut1 with spanner5, nut2 with spanner4, nut3 with spanner3, and nut4 with spanner2. Is the action: nut4 is tightened by bob using spanner2 at gate executable at step 19, True or False?", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, a connection between location9 and the gate exists, and a connection between the shed and location1 is present. Bob is currently at the shed. Location1 is connected to location2, location2 is connected to location3, location3 is connected to location4, and location4 is connected to location5. Furthermore, location6 is connected to location7, location7 is connected to location8, and location8 is connected to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is not secured, nut4, which is not secured, and nut5, which is loose. The current locations of the spanners are as follows: spanner1 is at location6 and is usable, spanner2 is at location8 and is functional, spanner3 is at location8 and can be used, spanner4 is at location5 and can be used, and spanner5 is at location7 and can be used."}
{"question_id": "4e14aa9d-555c-4d75-9781-69406b709d54", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: bob picks up spanner1 from location8, bob tightens nut4 with spanner2 at gate, bob walks from location1 to location2, bob walks from location3 to location4, bob walks from location7 to location8, bob walks from location8 to location9, bob walks from location9 to gate, bob walks from shed to location1, bob walks to location3 from location2, bob walks to location3 from location6, bob walks to location5 from location4, bob walks to location6 from location5, bob walks to location7 from location6, from location2, bob picks up spanner3, from location2, bob picks up spanner4, from location6, bob picks up spanner2, from location6, bob picks up spanner5, nut1 is tightened by bob using spanner5 at gate and nut3 is tightened by bob using spanner3 at gate. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location6 and location7 exists, a link between location9 and gate exists, bob is at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, shed is linked to location1, spanner1 is at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 can be used, spanner4 is currently at location2, spanner5 is located at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following sequence of actions is proposed: bob retrieves spanner1 from location8, bob uses spanner2 to tighten nut4 at the gate, bob moves from location1 to location2, bob moves from location3 to location4, bob moves from location7 to location8, bob moves from location8 to location9, bob moves from location9 to the gate, bob moves from the shed to location1, bob proceeds from location2 to location3, bob proceeds from location6 to location3, bob proceeds from location4 to location5, bob proceeds from location5 to location6, bob proceeds from location6 to location7, from location2, bob collects spanner3, from location2, bob collects spanner4, from location6, bob collects spanner2, from location6, bob collects spanner5, bob uses spanner5 to tighten nut1 at the gate and bob uses spanner3 to tighten nut3 at the gate. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "There is a connection between location6 and location7, and another connection exists between location9 and the gate. Bob is currently at the shed. The locations are interconnected as follows: location1 is connected to location2, location2 is connected to location3, location3 is linked with location4, location4 is linked with location5, and location5 is connected to location6. Additionally, location7 is connected to location8, and location8 is connected to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is loose, nut4, which is not secured, and nut5, which is loose. The shed is connected to location1. The following spanners are located at the respective locations: spanner1 is at location8 and is usable, spanner2 is at location6 and is functional, spanner3 is at location2 and is usable, spanner4 is at location2 and can be used, and spanner5 is at location6 and is usable."}
{"question_id": "cd4df286-dab5-4eba-a8d6-a19710554f3d", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: bob walks from shed to location1, from location1 to location2, bob walks, from location2, bob picks up spanner3, bob walks from location2 to location3, spanner5 is picked up by bob from location3, spanner1 is picked up by bob from location3, nut5 is tightened by bob using spanner4 at gate, bob walks from location4 to location5, bob picks up spanner2 from location5 and from location5 to location6, bob walks. Is the action: at gate, bob uses spanner4 to tighten nut5 executable at step 7, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location2 and location3 exists, a link between location6 and location7 exists, bob is located at shed, location1 and location2 are linked, location3 and location4 are linked, location4 is linked to location5, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 is currently at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 is at location2, spanner3 is usable, spanner4 is currently at location6, spanner4 is functional, spanner5 can be used and spanner5 is at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: Bob will move from the shed to location1, then to location2, then walk, then proceed from location2, pick up spanner3, walk from location2 to location3, pick up spanner5 and spanner1 from location3, tighten nut5 at the gate using spanner4, walk from location4 to location5, pick up spanner2 from location5, and then walk from location5 to location6. Is the action of using spanner4 to tighten nut5 at the gate executable at step 7, True or False?", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, and another connection exists between location6 and location7. Bob is situated at the shed. Location1 is connected to location2, location3 is connected to location4, location4 is linked to location5, location5 is connected to location6, location7 is connected to location8, location8 is connected to location9, and location9 is connected to the gate. Nut1, nut2, nut3, and nut4 are all located at the gate, and each of them is loose. Additionally, nut5 is currently at the gate but is not secured. The shed is connected to location1. Spanner1 is currently situated at location3 and is in working condition. Spanner2 is at location5 and is usable, while spanner3 is at location2 and is also usable. Spanner4 is currently at location6 and is functional. Furthermore, spanner5 is both usable and located at location3."}
{"question_id": "8d5959e0-dcfb-4931-a556-24d948cd31fc", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: bob walks from shed to location1, bob picks up spanner5 from location1, from location1, bob picks up spanner4, bob walks from location1 to location2, bob walks from location2 to location3, bob walks to location4 from location3, bob tightens nut2 with spanner5 at location2, bob walks to location5 from location4, bob walks from location5 to location6, bob picks up spanner3 from location6, bob walks to location7 from location6, spanner2 is picked up by bob from location7, bob walks to location8 from location7, bob walks to location9 from location8, from location9 to gate, bob walks, nut1 is tightened by bob using spanner5 at gate, bob tightens nut2 with spanner4 at gate, nut3 is tightened by bob using spanner3 at gate and bob tightens nut4 with spanner2 at gate. Is the action: bob tightens nut2 with spanner5 at location2 executable at step 7, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed for steps 1 through 19: Bob starts by walking from the shed to location1, then picks up spanner5 from location1, followed by picking up spanner4 from the same location. Next, Bob walks from location1 to location2, then to location3, and subsequently to location4. After that, Bob returns to location2 to tighten nut2 using spanner5. Then, Bob proceeds to walk from location4 to location5, then to location6, where he picks up spanner3. From location6, Bob walks to location7, picks up spanner2, and then walks to location8, followed by location9. Finally, Bob walks from location9 to the gate, where he tightens nut1 with spanner5, nut2 with spanner4, nut3 with spanner3, and nut4 with spanner2. Is the action: Bob tightens nut2 with spanner5 at location2 executable at step 7, True or False?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and another connection exists between location6 and location7. Bob is currently situated at the shed. Additionally, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location7 is linked to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is unsecured, nut2, which is loose, nut3, which is also loose, nut4, which is loose, and nut5, which is loose as well. The shed is connected to location1. Spanner1 is currently at location4 and is in working condition. Spanner2 is functional and located at location7. Spanner3 is usable and situated at location6. Spanner4 is also usable and currently at location1. Lastly, spanner5 is functional and located at location1."}
{"question_id": "d187bda9-a643-480e-bc23-a1cef48b78a9", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: bob walks from shed to location1, from location1 to location2, bob walks, bob walks to location3 from location2, bob walks to location4 from location3, bob walks to location5 from location4, spanner4 is picked up by bob from location5, bob walks from location5 to location6, from location6, bob picks up spanner1, bob walks to location7 from location6, spanner5 is picked up by bob from location7, bob walks from location7 to location8, from location8, bob picks up spanner3, spanner2 is picked up by bob from location8, bob tightens nut4 with spanner1 at location4, from location9 to gate, bob walks, nut1 is tightened by bob using spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, bob tightens nut3 with spanner3 at gate and nut4 is tightened by bob using spanner2 at gate. Is the action: nut4 is tightened by bob using spanner1 at location4 executable at step 14, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is currently at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is functional, spanner3 can be used, spanner3 is currently at location8, spanner4 can be used, spanner4 is currently at location5, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed for steps 1 through 19: Bob moves from the shed to location1, then to location2, then walks, then proceeds from location2 to location3, from location3 to location4, and from location4 to location5. At location5, Bob picks up spanner4, then walks to location6, where he picks up spanner1. He then moves to location7 from location6, picks up spanner5 at location7, and walks to location8. At location8, Bob picks up both spanner3 and spanner2. Subsequently, Bob tightens nut4 with spanner1 at location4, walks from location9 to the gate, and at the gate, he tightens nut1 with spanner5, nut2 with spanner4, and nut3 with spanner3, and also tightens nut4 with spanner2. Is the action: nut4 is tightened by bob using spanner1 at location4 executable at step 14, True or False?", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, a connection between location9 and the gate exists, and a connection between the shed and location1 is present. Bob is currently situated at the shed. Location1 is connected to location2, location2 is linked to location3, location3 is connected to location4, and location4 is linked to location5. Furthermore, location6 is connected to location7, location7 is linked to location8, and location8 is connected to location9. At the gate, nut1, nut2, nut3, nut4, and nut5 are located, with nut1, nut2, and nut5 being loose, and nut3 and nut4 not being secured. Spanner1 is located at location6 and is in working condition, spanner2 is at location8 and is functional, spanner3 is at location8 and can be used, spanner4 is at location5 and is usable, and spanner5 is at location7 and can be used."}
{"question_id": "44a96daf-b5ff-4c91-99ab-4c8ff6d0cd55", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: bob tightens nut1 with spanner2 at location3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is currently at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is functional, spanner3 can be used, spanner3 is currently at location8, spanner4 can be used, spanner4 is currently at location5, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: bob uses spanner2 to tighten nut1 at location3. Can this be executed, True or False?", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, a connection between location9 and the gate exists, and a connection between the shed and location1 is present. Bob is currently situated at the shed. Location1 is connected to location2, location2 is linked to location3, location3 is connected to location4, and location4 is connected to location5. Furthermore, location6 is connected to location7, location7 is connected to location8, and location8 is linked to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is not secured, nut4, which is not secured, and nut5, which is loose. The current locations of the spanners are as follows: spanner1 is at location6 and is in working condition, spanner2 is at location8 and is functional, spanner3 is at location8 and can be used, spanner4 is at location5 and is usable, and spanner5 is at location7 and can be used."}
{"question_id": "8866a1af-3dbb-4c4b-bed8-f9055fb27f09", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: bob walks from shed to location1, from location1, bob picks up spanner5, spanner4 is picked up by bob from location1, from location1 to location2, bob walks, bob walks to location3 from location2, from location3 to location4, bob walks, spanner1 is picked up by bob from location4, bob walks from location4 to location5, bob walks from location5 to location6 and bob picks up spanner3 from location6. Is the action: spanner4 is picked up by bob from location1 executable at step 3, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: bob will walk from the shed to location1, then pick up spanner5 from location1, followed by picking up spanner4 from location1, then proceed to walk from location1 to location2, then to location3, and from location3 to location4, pick up spanner1 from location4, then walk from location4 to location5, from location5 to location6, and finally pick up spanner3 from location6. Is the action of bob picking up spanner4 from location1 executable at step 3, True or False?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and another connection exists between location6 and location7. Bob is currently situated at the shed. Additionally, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location7 is linked to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is unsecured, nut2, which is loose, nut3, which is also loose, nut4, which is loose, and nut5, which is loose as well. The shed is connected to location1. The following spanners are located at their respective locations: spanner1, which is functional, is at location4, spanner2, which is functional, is at location7, spanner3, which can be used, is at location6, spanner4, which can be used, is at location1, and spanner5, which is functional, is also at location1."}
{"question_id": "7feea5b7-928f-46f4-b6c6-660a79da0bb1", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: bob walks from shed to location1, from location1 to location2, bob walks, spanner4 is picked up by bob from location2, from location2, bob picks up spanner3, bob walks from location2 to location3, bob walks to location4 from location3, bob walks to location5 from location4, from location5 to location6, bob walks, bob picks up spanner5 from location6, bob picks up spanner2 from location6, from location6 to location7, bob walks, bob walks from location7 to location8, bob picks up spanner1 from location8, bob walks from location8 to location9, from location9 to gate, bob walks, at gate, bob uses spanner5 to tighten nut1, at gate, bob uses spanner4 to tighten nut2, at gate, bob uses spanner3 to tighten nut3 and bob tightens nut4 with spanner2 at gate. Is the action: from location7 to location8, bob walks executable at step 12, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "A link between location6 and location7 exists, a link between location9 and gate exists, bob is at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, shed is linked to location1, spanner1 is at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 can be used, spanner4 is currently at location2, spanner5 is located at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: Bob starts by walking from the shed to location1, then proceeds to location2, walks, picks up spanner4 from location2, and also picks up spanner3 from location2. He then walks from location2 to location3, followed by location4, then location5, and from location5 to location6, where he walks and picks up spanner5 and spanner2. Next, Bob walks from location6 to location7, then to location8, where he picks up spanner1, and walks from location8 to location9. Finally, he walks from location9 to the gate, where he uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4. Is the action of Bob walking from location7 to location8 executable at step 12, True or False?", "initial_state_nl_paraphrased": "There is a connection between location6 and location7, and another connection exists between location9 and the gate. Bob is currently at the shed. The locations are interconnected as follows: location1 is connected to location2, location2 is connected to location3, location3 is linked with location4, location4 is linked with location5, and location5 is connected to location6. Additionally, location7 is connected to location8, and location8 is connected to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is loose, nut4, which is not secured, and nut5, which is loose. The shed is connected to location1. The following spanners are located at the respective locations: spanner1 is at location8 and is usable, spanner2 is at location6 and is functional, spanner3 is at location2 and is usable, spanner4 is at location2 and can be used, and spanner5 is at location6 and is usable."}
{"question_id": "a4acbbfa-4d7c-4d9c-b429-559f242cab98", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from shed to location1, bob walks. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is currently at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is functional, spanner3 can be used, spanner3 is currently at location8, spanner4 can be used, spanner4 is currently at location5, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: from the shed to location1, bob walks. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, a connection between location9 and the gate exists, and a connection between the shed and location1 is present. Bob is currently situated at the shed. Location1 is connected to location2, location2 is linked to location3, location3 is connected to location4, and location4 is connected to location5. Furthermore, location6 is connected to location7, location7 is connected to location8, and location8 is linked to location9. At the gate, nut1 is present and loose, nut2 is also at the gate and loose, nut3 is located at the gate but not secured, nut4 is at the gate and not secured, and nut5 is at the gate and loose. Spanner1 is currently located at location6 and is in working condition, spanner2 is at location8 and functional, spanner3 is at location8 and usable, spanner4 is at location5 and usable, and spanner5 is at location7 and usable."}
{"question_id": "8c8871df-c397-492c-91ae-19bd077a09b1", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: bob picks up spanner5 from location3, bob tightens nut3 with spanner3 at gate, bob walks from location1 to location2, bob walks from location7 to location8, bob walks to gate from location9, bob walks to location4 from location3, bob walks to location5 from location4, bob walks to location6 from location5, bob walks to location7 from location6, from location2 to location3, bob walks, from location2, bob picks up spanner3, from location5, bob picks up spanner2, from location8 to location9, bob walks, nut1 is tightened by bob using spanner5 at gate, nut2 is tightened by bob using spanner4 at gate, nut3 is tightened by bob using spanner5 at location2, nut4 is tightened by bob using spanner2 at gate, spanner1 is picked up by bob from location3 and spanner4 is picked up by bob from location6. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location2 and location3 exists, a link between location6 and location7 exists, bob is located at shed, location1 and location2 are linked, location3 and location4 are linked, location4 is linked to location5, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 is currently at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 is at location2, spanner3 is usable, spanner4 is currently at location6, spanner4 is functional, spanner5 can be used and spanner5 is at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned to be executed: bob retrieves spanner5 from location3, bob secures nut3 with spanner3 at the gate, bob moves from location1 to location2, bob moves from location7 to location8, bob proceeds to the gate from location9, bob moves from location3 to location4, bob moves from location4 to location5, bob moves from location5 to location6, bob moves from location6 to location7, bob moves from location2 to location3, bob picks up spanner3 from location2, bob picks up spanner2 from location5, bob moves from location8 to location9, bob tightens nut1 with spanner5 at the gate, bob tightens nut2 with spanner4 at the gate, bob tightens nut3 with spanner5 at location2, bob tightens nut4 with spanner2 at the gate, bob picks up spanner1 from location3 and bob picks up spanner4 from location6. Is it possible to execute it, True or False?", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, and another connection exists between location6 and location7. Bob is situated at the shed. Location1 is connected to location2, location3 is linked to location4, location4 is connected to location5, location5 is linked to location6, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. Nut1, nut2, nut3, and nut4 are all located at the gate, and each of them is loose. Additionally, nut5 is currently at the gate but is not secured. The shed is connected to location1. Spanner1 is currently situated at location3 and is in working condition. Spanner2 is at location5 and is usable, while spanner3 is at location2 and is also usable. Spanner4 is currently at location6 and is functional. Furthermore, spanner5 is located at location3 and can be used."}
{"question_id": "bd78f2b9-8b82-4874-bccd-4987fc9a2465", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: bob walks from shed to location1. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location6 and location7 exists, a link between location9 and gate exists, bob is at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, shed is linked to location1, spanner1 is at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 can be used, spanner4 is currently at location2, spanner5 is located at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: bob moves from the shed to location1. Can this be executed, True or False?", "initial_state_nl_paraphrased": "There is a connection between location6 and location7, and another connection exists between location9 and the gate. Bob is currently at the shed. The locations are interconnected as follows: location1 is connected to location2, location2 is connected to location3, location3 is linked with location4, location4 is linked with location5, and location5 is connected to location6. Additionally, location7 is connected to location8, and location8 is connected to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is loose, nut4, which is not secured, and nut5, which is loose. The shed is connected to location1. The following spanners are located at the respective locations: spanner1 is at location8 and is usable, spanner2 is at location6 and is functional, spanner3 is at location2 and is usable, spanner4 is at location2 and can be used, and spanner5 is at location6 and is usable."}
{"question_id": "5f593943-b19a-4daa-8a0e-ab84a2e6c98f", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: from location9 to location7, bob walks. Is the action: bob walks from location9 to location7 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "A link between location2 and location3 exists, a link between location6 and location7 exists, bob is located at shed, location1 and location2 are linked, location3 and location4 are linked, location4 is linked to location5, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 is currently at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 is at location2, spanner3 is usable, spanner4 is currently at location6, spanner4 is functional, spanner5 can be used and spanner5 is at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: from location9 to location7, bob walks. Is the action: bob walks from location9 to location7 executable at step 1, True or False?\n\nParaphrased text: \nGiven the initial condition, for steps 1 through 1 the following actions are planned to be performed: from location9 to location7, bob walks. Can the action: bob walks from location9 to location7 be executed at step 1, True or False?", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, and another connection exists between location6 and location7. Bob is situated at the shed. Location1 is connected to location2, location3 is connected to location4, location4 is linked to location5, location5 is connected to location6, location7 is connected to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, nut2, nut3, nut4, and nut5, all of which are loose or not secured. The shed is connected to location1. The following spanners are located at their respective locations: spanner1 at location3 (functional), spanner2 at location5 (usable), spanner3 at location2 (usable), spanner4 at location6 (functional), and spanner5 at location3 (usable)."}
{"question_id": "c22de396-cc71-470e-83a1-dee8d443047d", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: bob walks from shed to location1. Is the action: bob walks to location1 from shed executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: bob walks from shed to location1. Is the action: bob walks from shed to location1 executable at step 1, True or False?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and another connection exists between location6 and location7. Bob is currently situated at the shed. Additionally, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location7 is linked to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is not secured, nut2, which is loose, nut3, which is loose, nut4, which is loose, and nut5, which is also loose. The shed is connected to location1. The following spanners are located at their respective locations: spanner1, which is functional, is at location4, spanner2, which is functional, is at location7, spanner3, which can be used, is at location6, spanner4, which can be used, is at location1, and spanner5, which is functional, is also at location1."}
{"question_id": "300dd466-a26f-4aaa-9c67-edf4323926bc", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: bob picks up spanner5 from location7, bob walks from location3 to location4, bob walks from location4 to location5, bob walks from location5 to location6, bob walks from location6 to location7, bob walks to location2 from location1, bob walks to location3 from location2, from location5 to shed, bob walks, from location5, bob picks up spanner4 and spanner1 is picked up by bob from location6. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is currently at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is functional, spanner3 can be used, spanner3 is currently at location8, spanner4 can be used, spanner4 is currently at location5, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: bob retrieves spanner5 from location7, bob moves from location3 to location4, then from location4 to location5, followed by location5 to location6, and location6 to location7, bob proceeds from location1 to location2, then from location2 to location3, from location5, bob heads to the shed, and from location5, bob collects spanner4, while spanner1 is also picked up by bob from location6. Is the execution of these actions feasible, True or False?", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, a connection between location9 and the gate exists, and a connection between the shed and location1 is present. Bob is currently situated at the shed. Location1 is connected to location2, location2 is linked to location3, location3 is connected to location4, and location4 is connected to location5. Furthermore, location6 is connected to location7, location7 is connected to location8, and location8 is linked to location9. At the gate, the following items are located: nut1, which is loose, nut2, also loose, nut3, which is not secured, nut4, also not secured, and nut5, which is loose. The current locations of the spanners are as follows: spanner1 is at location6 and is in working condition, spanner2 is at location8 and is functional, spanner3 is at location8 and can be used, spanner4 is at location5 and is usable, and spanner5 is at location7 and can be used."}
{"question_id": "f6e35258-0c9c-4ea5-9c0e-34d7ba48db28", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: bob walks to location1 from shed, from location1, bob picks up spanner5, from location1, bob picks up spanner4, bob walks to location2 from location1, bob walks from location2 to location3, bob walks from location3 to location4, bob picks up spanner2 from gate, from location4 to location5, bob walks, bob walks to location6 from location5 and from location6, bob picks up spanner3. Is the action: from gate, bob picks up spanner2 executable at step 7, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: Bob will move from the shed to location1, then from location1, he will collect spanner5 and spanner4, followed by walking to location2, then to location3, and then to location4. At the gate, Bob will pick up spanner2, then proceed to location5 and subsequently to location6, where he will collect spanner3. Is the action of picking up spanner2 from the gate executable at step 7, True or False?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and another connection exists between location6 and location7. Bob is currently situated at the shed. Additionally, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location7 is linked to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is unsecured, nut2, which is loose, nut3, which is also loose, nut4, which is loose, and nut5, which is loose as well. The shed is connected to location1. The following spanners are located at their respective locations: spanner1, which is functional, is at location4, spanner2, which is functional, is at location7, spanner3, which can be used, is at location6, spanner4, which can be used, is at location1, and spanner5, which is functional, is also at location1."}
{"question_id": "deaa2bd2-7c4b-4525-9d9e-00eb17dfaf45", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: bob walks from shed to location1, bob walks to location2 from location1, bob picks up spanner3 from location2, bob walks from location2 to location3, bob picks up spanner5 from location3, spanner1 is picked up by bob from location3, bob walks from location3 to location4, bob walks to location5 from location4, bob picks up spanner2 from location5, bob tightens nut2 with spanner5 at location1, spanner4 is picked up by bob from location6, bob walks to location7 from location6, from location7 to location8, bob walks, from location8 to location9, bob walks, from location9 to gate, bob walks, bob tightens nut1 with spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, nut3 is tightened by bob using spanner3 at gate and nut4 is tightened by bob using spanner2 at gate. Is the action: bob tightens nut2 with spanner5 at location1 executable at step 10, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location2 and location3 exists, a link between location6 and location7 exists, bob is located at shed, location1 and location2 are linked, location3 and location4 are linked, location4 is linked to location5, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 is currently at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 is at location2, spanner3 is usable, spanner4 is currently at location6, spanner4 is functional, spanner5 can be used and spanner5 is at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed for steps 1 through 19: Bob will walk from the shed to location1, then proceed to location2, where he will pick up spanner3. From location2, Bob will head to location3, collect spanner5, and also pick up spanner1. He will then walk to location4 and continue to location5, where he will pick up spanner2. Subsequently, Bob will use spanner5 to tighten nut2 at location1. Additionally, Bob will pick up spanner4 from location6, and then walk through locations 7, 8, and 9 before reaching the gate. At the gate, Bob will use spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4. Is the action: Bob tightens nut2 with spanner5 at location1 executable at step 10, True or False?", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, and another connection exists between location6 and location7. Bob is situated at the shed. Location1 is connected to location2, location3 is linked to location4, location4 is connected to location5, location5 is linked to location6, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. Nut1, nut2, nut3, and nut4 are all located at the gate, and each of them is loose. Additionally, nut5 is currently at the gate but is not secure. The shed is connected to location1. Spanner1 is currently situated at location3 and is in working condition. Spanner2 is at location5 and is usable, while spanner3 is at location2 and is also usable. Spanner4 is currently at location6 and is functional. Furthermore, spanner5 is located at location3 and can be used."}
{"question_id": "70108592-cc7b-4b4c-b553-606a2d22ee7c", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: bob walks to location1 from shed, from location1 to location2, bob walks, bob walks to location3 from location2, from location3 to location4, bob walks, from location4 to location5, bob walks, from location5, bob picks up spanner4, bob walks to location6 from location5, spanner1 is picked up by bob from location6, from location6 to location7, bob walks and spanner5 is picked up by bob from location7. Is the action: from location5 to location6, bob walks executable at step 7, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "A link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is currently at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is functional, spanner3 can be used, spanner3 is currently at location8, spanner4 can be used, spanner4 is currently at location5, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: Bob moves from the shed to location1, then to location2, then walks, proceeds from location2 to location3, from location3 to location4, walks again, from location4 to location5, walks once more, picks up spanner4 at location5, walks to location6 from location5, picks up spanner1 at location6, moves from location6 to location7, walks, and picks up spanner5 at location7. Is the action: Bob walking from location5 to location6 executable at step 7, True or False?", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, a connection between location9 and the gate exists, and a connection between the shed and location1 is present. Bob is currently situated at the shed. Location1 is connected to location2, location2 is linked to location3, location3 is connected to location4, and location4 is connected to location5. Furthermore, location6 is connected to location7, location7 is connected to location8, and location8 is linked to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is not secured, nut4, which is not secured, and nut5, which is loose. The current locations of the spanners are as follows: spanner1 is at location6 and is in working condition, spanner2 is at location8 and is functional, spanner3 is at location8 and can be used, spanner4 is at location5 and is usable, and spanner5 is at location7 and can be used."}
{"question_id": "770b76cd-3797-466b-8d0d-b6c98f91fa51", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location3, bob uses spanner3 to tighten nut1, bob picks up spanner5 from location1, bob walks from location1 to location2, bob walks from shed to location1, bob walks to location3 from location2, bob walks to location4 from location3, bob walks to location5 from location4, spanner1 is picked up by bob from location4, spanner3 is picked up by bob from location6 and spanner4 is picked up by bob from location1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned: at location3, bob will use spanner3 to tighten nut1, bob will retrieve spanner5 from location1, bob will move from location1 to location2, bob will move from the shed to location1, bob will proceed from location2 to location3, bob will proceed from location3 to location4, bob will then move from location4 to location5, bob will collect spanner1 from location4, bob will collect spanner3 from location6, and bob will collect spanner4 from location1. Is the execution of these actions possible, True or False?", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and another connection exists between location6 and location7. Bob is currently situated at the shed. Additionally, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location7 is linked to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is unsecured, nut2, which is loose, nut3, which is also loose, nut4, which is loose, and nut5, which is loose as well. The shed is connected to location1. The following spanners are located at their respective locations: spanner1, which is functional, is at location4, spanner2, which is functional, is at location7, spanner3, which can be used, is at location6, spanner4, which can be used, is at location1, and spanner5, which is functional, is also at location1."}
{"question_id": "b33372db-b60d-4eca-a10e-03ccff83037a", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: bob walks from shed to location1. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "A link between location2 and location3 exists, a link between location6 and location7 exists, bob is located at shed, location1 and location2 are linked, location3 and location4 are linked, location4 is linked to location5, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 is currently at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 is at location2, spanner3 is usable, spanner4 is currently at location6, spanner4 is functional, spanner5 can be used and spanner5 is at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: bob moves from the shed to location1. Can this be executed, True or False?", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, and another connection exists between location6 and location7. Bob is situated at the shed. Location1 is connected to location2, location3 is linked to location4, location4 is connected to location5, location5 is linked to location6, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. Nut1, nut2, nut3, and nut4 are all located at the gate, and each of them is loose. Additionally, nut5 is currently at the gate but is not secure. The shed is connected to location1. Spanner1 is currently situated at location3 and is in working condition. Spanner2 is at location5 and is usable, while spanner3 is at location2 and is also usable. Spanner4 is currently at location6 and is functional. Furthermore, spanner5 is located at location3 and is available for use."}
{"question_id": "9c9e36a7-8f45-4e02-a21b-323b5d449280", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at location6, bob uses spanner5 to tighten nut5, bob walks from location1 to location2, bob walks from location2 to location3, bob walks from location4 to location5, bob walks from location5 to location6, bob walks to location4 from location3, from location2, bob picks up spanner4, from location6, bob picks up spanner2, from location6, bob picks up spanner5 and spanner3 is picked up by bob from location2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "A link between location6 and location7 exists, a link between location9 and gate exists, bob is at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, shed is linked to location1, spanner1 is at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 can be used, spanner4 is currently at location2, spanner5 is located at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are scheduled: at location6, bob will use spanner5 to tighten nut5, bob will move from location1 to location2, then from location2 to location3, from location4 to location5, from location5 to location6, and from location3 back to location4. Additionally, bob will collect spanner4 at location2, spanner2 at location6, and both spanner5 and spanner3 will be picked up by bob at location6 and location2 respectively. Is the execution of these actions feasible, True or False?", "initial_state_nl_paraphrased": "There is a connection between location6 and location7, and another connection exists between location9 and the gate. Bob is currently at the shed. The locations are interconnected as follows: location1 is connected to location2, location2 is connected to location3, location3 is linked with location4, location4 is linked with location5, and location5 is connected to location6. Additionally, location7 is connected to location8, and location8 is connected to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is loose, nut4, which is not secured, and nut5, which is loose. The shed is connected to location1. The following spanners are located at the respective locations: spanner1 is at location8 and is usable, spanner2 is at location6 and is functional, spanner3 is at location2 and is usable, spanner4 is at location2 and can be used, and spanner5 is at location6 and is usable."}
{"question_id": "2b99e571-e7d7-4ffc-90df-102fe53a2a7f", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: at gate, bob uses spanner4 to tighten nut2, bob picks up spanner2 from location6, bob picks up spanner3 from location2, bob picks up spanner5 from location6, bob walks from location1 to location2, bob walks from location6 to location7, bob walks from location9 to gate, bob walks from shed to location1, bob walks to location3 from location2, bob walks to location4 from location3, bob walks to location6 from location5, from location2, bob picks up spanner4, from location4 to location5, bob walks, from location7 to location8, bob walks, from location8 to location9, bob walks, from location8, bob picks up spanner1, nut1 is tightened by bob using spanner5 at gate, nut3 is tightened by bob using spanner3 at gate and nut4 is tightened by bob using spanner2 at gate. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "A link between location6 and location7 exists, a link between location9 and gate exists, bob is at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, shed is linked to location1, spanner1 is at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 can be used, spanner4 is currently at location2, spanner5 is located at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are scheduled to take place: at the gate, bob will use spanner4 to secure nut2, bob will collect spanner2 from location6, bob will collect spanner3 from location2, bob will collect spanner5 from location6, bob will move from location1 to location2, bob will move from location6 to location7, bob will move from location9 to the gate, bob will move from the shed to location1, bob will proceed from location2 to location3, bob will proceed from location3 to location4, bob will proceed from location5 to location6, from location2, bob will collect spanner4, from location4 to location5, bob will walk, from location7 to location8, bob will walk, from location8 to location9, bob will walk, from location8, bob will collect spanner1, at the gate, bob will use spanner5 to tighten nut1, at the gate, bob will use spanner3 to tighten nut3, and at the gate, bob will use spanner2 to tighten nut4. Is it possible to execute this plan, True or False?", "initial_state_nl_paraphrased": "There is a connection between location6 and location7, and another connection exists between location9 and the gate. Bob is currently at the shed. The locations are interconnected as follows: location1 is connected to location2, location2 is connected to location3, location3 is linked with location4, location4 is linked with location5, and location5 is connected to location6. Additionally, location7 is connected to location8, and location8 is connected to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is loose, nut4, which is not secured, and nut5, which is loose. The shed is connected to location1. The following spanners are located at the specified locations: spanner1 is at location8 and is usable, spanner2 is at location6 and is functional, spanner3 is at location2 and is usable, spanner4 is at location2 and can be used, and spanner5 is at location6 and is usable."}
