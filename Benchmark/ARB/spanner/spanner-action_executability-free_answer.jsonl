{"question_id": "d572763e-d10d-4dad-a2df-70a5c2a46631", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from location6, bob picks up spanner1 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "bob picks up spanner1 from location6", "plan_length": 1, "initial_state_nl": "A link between location6 and location7 exists, a link between location9 and gate exists, bob is at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, shed is linked to location1, spanner1 is at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 can be used, spanner4 is currently at location2, spanner5 is located at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: starting from location6, bob retrieves spanner1 to achieve the current state. What is the first action in the sequence that cannot be executed? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location6 and location7, and another connection exists between location9 and the gate. Bob is currently at the shed. The locations are interconnected as follows: location1 is connected to location2, location2 is connected to location3, location3 is linked with location4, location4 is linked with location5, and location5 is connected to location6. Additionally, location7 is connected to location8, and location8 is connected to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is loose, nut4, which is not secured, and nut5, which is loose. The shed is connected to location1. The following spanners are located at the respective locations: spanner1 is at location8 and is usable, spanner2 is at location6 and is functional, spanner3 is at location2 and is usable, spanner4 is at location2 and can be used, and spanner5 is at location6 and is usable."}
{"question_id": "91b63741-1604-4211-a963-55421224e6dd", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, from location1, bob picks up spanner5, bob picks up spanner4 from location1, bob walks from location1 to location2, nut1 is tightened by bob using spanner2 at location7, from location3 to location4, bob walks, from location4, bob picks up spanner1, bob walks to location5 from location4, bob walks from location5 to location6 and spanner3 is picked up by bob from location6 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "bob tightens nut1 with spanner2 at location7", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob walks from the shed to location1, then picks up spanner5 and spanner4 from location1, and proceeds to location2. Next, Bob tightens nut1 with spanner2 at location7. After that, Bob walks from location3 to location4, picks up spanner1, and then walks to location5 and then to location6, where he picks up spanner3, resulting in the current state. What is the first action in the sequence that cannot be executed? If there are none, write None.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and another connection exists between location6 and location7. Bob is currently situated at the shed. Additionally, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location7 is linked to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is not secured, nut2, which is loose, nut3, which is loose, nut4, which is loose, and nut5, which is also loose. The shed is connected to location1. The following spanners are located at their respective locations: spanner1, which is functional, is at location4, spanner2, which is functional, is at location7, spanner3, which can be used, is at location6, spanner4, which can be used, is at location1, and spanner5, which is functional, is also at location1."}
{"question_id": "d413628d-1a52-4d33-a788-03a714ce0dc2", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, from location1 to location2, bob walks, bob walks from location2 to location3, from location3 to location4, bob walks, bob walks to location5 from location4, from location5, bob picks up spanner4, bob walks from location5 to location6, spanner1 is picked up by bob from location6, bob walks from location6 to location7 and from location7, bob picks up spanner5 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "bob walks from location7 to location8", "plan_length": 10, "initial_state_nl": "A link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is currently at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is functional, spanner3 can be used, spanner3 is currently at location8, spanner4 can be used, spanner4 is currently at location5, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob moves from the shed to location1, then to location2, and continues walking. He proceeds from location2 to location3, then to location4, and walks some more. Next, Bob walks from location4 to location5, and from location5, he picks up spanner4. He then walks from location5 to location6, where he picks up spanner1. After that, Bob moves from location6 to location7, and from location7, he picks up spanner5, ultimately reaching the current state. In this state, list all possible actions that can be executed. If there are none, write None.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, a connection between location9 and the gate exists, and a connection between the shed and location1 is present. Bob is currently situated at the shed. Location1 is connected to location2, location2 is linked to location3, location3 is connected to location4, and location4 is connected to location5. Furthermore, location6 is connected to location7, location7 is connected to location8, and location8 is linked to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is not secured, nut4, which is not secured, and nut5, which is loose. The current locations of the spanners are as follows: spanner1 is at location6 and is in working condition, spanner2 is at location8 and is functional, spanner3 is at location8 and can be used, spanner4 is at location5 and is usable, and spanner5 is at location7 and can be used."}
{"question_id": "053131a4-13d7-4a4e-80d3-6095fefc0840", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks to location2 from location1, spanner3 is picked up by bob from location2, from location2 to location3, bob walks, spanner5 is picked up by bob from location3, spanner1 is picked up by bob from location3, bob walks from location3 to location4, bob walks to location5 from location4, bob picks up spanner2 from location5, from location5 to location6, bob walks, bob picks up spanner4 from location6, bob walks from location6 to location7, from location7 to location8, bob walks, bob walks from location8 to location9, from location9 to gate, bob walks, bob tightens nut1 with spanner5 at gate, nut2 is tightened by bob using spanner4 at gate, nut3 is tightened by bob using spanner3 at gate and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "nut5 is tightened by bob using spanner1 at gate", "plan_length": 19, "initial_state_nl": "A link between location2 and location3 exists, a link between location6 and location7 exists, bob is located at shed, location1 and location2 are linked, location3 and location4 are linked, location4 is linked to location5, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 is currently at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 is at location2, spanner3 is usable, spanner4 is currently at location6, spanner4 is functional, spanner5 can be used and spanner5 is at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then proceeds to location2, where he collects spanner3. From location2, Bob walks to location3, picks up spanner5 and spanner1, and then heads to location4. He continues to location5, where he collects spanner2, and then proceeds to location6 to pick up spanner4. Bob then walks to location7, followed by location8, and then location9, before finally reaching the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, list all possible actions that can be executed. If there are no actions, write None.", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, and another connection exists between location6 and location7. Bob is situated at the shed. Location1 is connected to location2, location3 is linked to location4, location4 is connected to location5, location5 is linked to location6, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is loose, nut2, also loose, nut3, which is loose, nut4, which is loose, and nut5, which is not secured. The shed is connected to location1. The following spanners are located at the respective locations: spanner1 at location3, which is functional, spanner2 at location5, which is usable, spanner3 at location2, which is usable, spanner4 at location6, which is functional, and spanner5 at location3, which can be used."}
{"question_id": "39c8b356-cc83-4414-8803-c5e95510ffe5", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at location4, bob uses spanner5 to tighten nut1 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "nut1 is tightened by bob using spanner5 at location4", "plan_length": 1, "initial_state_nl": "A link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is currently at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is functional, spanner3 can be used, spanner3 is currently at location8, spanner4 can be used, spanner4 is currently at location5, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: at location4, bob uses spanner5 to tighten nut1 to reach the current state. What is the first action in the sequence that cannot be executed? Write None if there are no such actions.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, a connection between location9 and the gate exists, and a connection between the shed and location1 is present. Bob is currently at the shed. Location1 is connected to location2, location2 is linked to location3, location3 is connected to location4, and location4 is connected to location5. Furthermore, location6 is connected to location7, location7 is connected to location8, and location8 is linked to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is not secured, nut4, which is not secured, and nut5, which is loose. The current locations of the spanners are as follows: spanner1 is at location6 and is in working condition, spanner2 is at location8 and is functional, spanner3 is at location8 and can be used, spanner4 is at location5 and is usable, and spanner5 is at location7 and can be used."}
{"question_id": "53e0b194-f6a1-402f-8a83-98f14ba7bec0", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from location6 to shed, bob walks, bob walks from location1 to location2, spanner3 is picked up by bob from location2, bob walks from location2 to location3, from location3, bob picks up spanner5, spanner1 is picked up by bob from location3, bob walks from location3 to location4, bob walks from location4 to location5, from location5, bob picks up spanner2 and bob walks to location6 from location5 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "bob walks to shed from location6", "plan_length": 10, "initial_state_nl": "A link between location2 and location3 exists, a link between location6 and location7 exists, bob is located at shed, location1 and location2 are linked, location3 and location4 are linked, location4 is linked to location5, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 is currently at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 is at location2, spanner3 is usable, spanner4 is currently at location6, spanner4 is functional, spanner5 can be used and spanner5 is at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: Bob walks from location6 to the shed, then from location1 to location2, picks up spanner3 at location2, proceeds to location3, collects spanner5 and spanner1 at location3, walks to location4 and then location5, picks up spanner2 at location5, and finally walks from location5 to location6 to reach the current state. What is the first action in the sequence that cannot be executed? If none, write None.", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, and another connection exists between location6 and location7. Bob is situated at the shed. Location1 is connected to location2, location3 is linked to location4, location4 is connected to location5, location5 is linked to location6, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. Nut1 is situated at the gate and is loose, nut2 is also at the gate and is loose, nut3 is located at the gate and is loose, nut4 is at the gate and is loose, and nut5 is currently at the gate but is not secured. The shed is connected to location1. Spanner1 is currently situated at location3 and is in working condition, spanner2 is at location5 and is usable, spanner3 is at location2 and is usable, spanner4 is currently at location6 and is functional, and spanner5 is at location3 and can be used."}
{"question_id": "3112b619-b181-4b86-8b09-1f9d641d4ee8", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob picks up spanner5 from location1, from location1, bob picks up spanner4, bob walks from location1 to location2, from location2 to location3, bob walks, bob walks from location3 to location4, bob picks up spanner1 from location4, from location4 to location5, bob walks, bob walks to location6 from location5 and spanner3 is picked up by bob from location6 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "bob walks to location7 from location6", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then collects spanner5 and spanner4 from location1. He proceeds to walk from location1 to location2, then to location3, and subsequently to location4. At location4, Bob picks up spanner1 and then walks to location5. From location5, he moves to location6, where he collects spanner3, resulting in the current state. In this state, list all possible actions that can be executed. If there are no executable actions, write None.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and another connection exists between location6 and location7. Bob is currently situated at the shed. Additionally, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is not secured, nut2, which is loose, nut3, which is loose, nut4, which is loose, and nut5, which is also loose. The shed is connected to location1. The following spanners are located at their respective locations: spanner1, which is functional, is at location4, spanner2, which is functional, is at location7, spanner3, which can be used, is at location6, spanner4, which can be used, is at location1, and spanner5, which is functional, is also at location1."}
{"question_id": "6dd02a06-4b67-40a3-b934-ee1d224264f6", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob walks to location2 from location1, bob picks up spanner4 from location2, bob picks up spanner3 from location2, bob walks from location2 to location3, bob walks to location4 from location3, from location4 to location5, bob walks, from location5 to location6, bob walks, from location6, bob picks up spanner5, spanner2 is picked up by bob from location6, bob walks from location6 to location7, from location7 to location8, bob walks, from location8, bob picks up spanner1, from location8 to location9, bob walks, from location9 to gate, bob walks, at gate, bob uses spanner5 to tighten nut1, at gate, bob uses spanner4 to tighten nut2, bob tightens nut3 with spanner3 at gate and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "bob tightens nut5 with spanner1 at gate", "plan_length": 19, "initial_state_nl": "A link between location6 and location7 exists, a link between location9 and gate exists, bob is at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, shed is linked to location1, spanner1 is at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 can be used, spanner4 is currently at location2, spanner5 is located at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then from location1 to location2, where he collects spanner4 and spanner3. He proceeds to location3 from location2, then to location4, followed by location5, and then location6. At location6, Bob picks up spanner5 and spanner2. He then walks to location7, then to location8, where he collects spanner1. From location8, he moves to location9 and then to the gate. Upon reaching the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, list all possible actions that can be executed. If there are no executable actions, write None.", "initial_state_nl_paraphrased": "There is a connection between location6 and location7, and another connection exists between location9 and the gate. Bob is currently at the shed. The locations are interconnected as follows: location1 is connected to location2, location2 is connected to location3, location3 is linked with location4, location4 is linked with location5, and location5 is connected to location6. Additionally, location7 is connected to location8, and location8 is connected to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is loose, nut4, which is not secured, and nut5, which is loose. The shed is connected to location1. The following spanners are located at the specified locations: spanner1 is at location8 and is usable, spanner2 is at location6 and is functional, spanner3 is at location2 and is usable, spanner4 is at location2 and can be used, and spanner5 is at location6 and is usable."}
{"question_id": "b225a907-0960-4478-9ca1-65c31796864d", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, from location1, bob picks up spanner5, from location1, bob picks up spanner4, bob walks to location2 from location1, bob walks from location2 to location3, from location3 to location4, bob walks, from location4, bob picks up spanner1, from location4 to location5, bob walks, bob walks to location6 from location5, nut4 is tightened by bob using spanner2 at location1, bob walks from location6 to location7, bob picks up spanner2 from location7, bob walks to location8 from location7, bob walks from location8 to location9, bob walks from location9 to gate, bob tightens nut1 with spanner5 at gate, bob tightens nut2 with spanner4 at gate, bob tightens nut3 with spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "bob tightens nut4 with spanner2 at location1", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then from location1, he collects spanner5 and spanner4. Next, Bob proceeds from location1 to location2, then to location3, and subsequently to location4. From location4, he picks up spanner1 and moves to location5, then to location6. However, at location1, Bob uses spanner2 to tighten nut4. Continuing, Bob moves from location6 to location7, collects spanner2, and then proceeds to location8, location9, and finally to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and another connection exists between location6 and location7. Bob is currently situated at the shed. Additionally, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location7 is linked to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, nut1 is present but not secured, while nut2, nut3, nut4, and nut5 are all located at the gate and are loose. The shed is connected to location1. Spanner1 is currently at location4 and is in working condition, spanner2 is functional and located at location7, spanner3 is usable and situated at location6, spanner4 is usable and currently at location1, and spanner5 is functional and located at location1."}
{"question_id": "4ef66b13-12d5-4736-ae70-e1f6b0c38119", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "from location1 to location9, bob walks, bob tightens nut4 with spanner1 at location3, spanner2 is picked up by bob from location9, bob tightens nut2 with spanner3 at location8, nut3 is tightened by bob using spanner2 at gate, bob tightens nut2 with spanner2 at location9, at location9, bob uses spanner5 to tighten nut5, bob tightens nut5 with spanner5 at location5, at location6, bob uses spanner4 to tighten nut1, spanner2 is picked up by bob from shed, nut3 is tightened by bob using spanner1 at location9, nut3 is tightened by bob using spanner2 at location2, from shed to gate, bob walks, at location8, bob uses spanner5 to tighten nut4, bob walks to location3 from location9, bob tightens nut3 with spanner2 at location4, at location7, bob uses spanner4 to tighten nut4, bob tightens nut3 with spanner2 at location1, nut1 is tightened by bob using spanner2 at location8, at location2, bob uses spanner4 to tighten nut5, bob tightens nut2 with spanner1 at location7, from location1 to location8, bob walks, nut2 is tightened by bob using spanner2 at shed, bob walks from location9 to location1, bob tightens nut2 with spanner4 at location7, from location9, bob picks up spanner5, at location9, bob uses spanner5 to tighten nut2, nut1 is tightened by bob using spanner1 at location2, bob tightens nut4 with spanner2 at location3, bob tightens nut1 with spanner4 at location1, at location8, bob uses spanner3 to tighten nut4, bob tightens nut1 with spanner3 at gate, from location9, bob picks up spanner3, from location9 to location4, bob walks, bob walks from shed to location1, bob tightens nut1 with spanner2 at location2, spanner3 is picked up by bob from gate, from location2 to location4, bob walks, bob tightens nut5 with spanner1 at shed, bob tightens nut2 with spanner2 at location1, spanner5 is picked up by bob from location7, bob tightens nut4 with spanner4 at location3, nut1 is tightened by bob using spanner4 at location9, nut4 is tightened by bob using spanner5 at location2, nut4 is tightened by bob using spanner4 at location1, bob walks to location2 from gate, bob walks to location2 from location8, bob picks up spanner4 from location7, nut3 is tightened by bob using spanner2 at shed, spanner4 is picked up by bob from location4, at location2, bob uses spanner4 to tighten nut4, nut5 is tightened by bob using spanner4 at shed, at location2, bob uses spanner3 to tighten nut4, nut2 is tightened by bob using spanner4 at location1, from location6 to shed, bob walks, bob walks to location5 from location3, at shed, bob uses spanner5 to tighten nut3, bob walks to location3 from location5, bob tightens nut1 with spanner2 at location7, spanner4 is picked up by bob from location3, bob tightens nut3 with spanner5 at location6, bob tightens nut4 with spanner4 at location9, bob tightens nut1 with spanner1 at shed, nut2 is tightened by bob using spanner4 at location3, at location1, bob uses spanner4 to tighten nut3, bob walks to location5 from location8, from location4, bob picks up spanner5, spanner4 is picked up by bob from location5, nut5 is tightened by bob using spanner1 at location1, nut5 is tightened by bob using spanner2 at location3, from gate, bob picks up spanner2, bob tightens nut5 with spanner3 at location2, from location1, bob picks up spanner1, spanner3 is picked up by bob from location7, nut4 is tightened by bob using spanner2 at location2, from shed to location7, bob walks, nut3 is tightened by bob using spanner5 at location2, bob walks from location3 to location2, nut4 is tightened by bob using spanner3 at location9, at location4, bob uses spanner4 to tighten nut2, from location3 to gate, bob walks, nut2 is tightened by bob using spanner2 at gate, bob tightens nut1 with spanner1 at location5, bob tightens nut2 with spanner5 at location2, from location4 to shed, bob walks, at location9, bob uses spanner2 to tighten nut4, nut5 is tightened by bob using spanner3 at location1, at shed, bob uses spanner1 to tighten nut2, at location8, bob uses spanner5 to tighten nut5, spanner1 is picked up by bob from location7, nut2 is tightened by bob using spanner3 at location1, bob tightens nut1 with spanner5 at location9, bob walks to shed from location8, nut5 is tightened by bob using spanner4 at location8, nut1 is tightened by bob using spanner2 at location6, bob walks to location3 from location1, bob walks to location7 from gate, from location2, bob picks up spanner2, from location9, bob picks up spanner4, nut4 is tightened by bob using spanner5 at gate, bob tightens nut3 with spanner1 at location6, from location7 to location9, bob walks, bob tightens nut3 with spanner1 at location1, bob tightens nut3 with spanner2 at location3, nut1 is tightened by bob using spanner5 at location5, bob tightens nut1 with spanner4 at location5, bob tightens nut2 with spanner3 at location9, bob walks from location5 to location6, bob tightens nut3 with spanner4 at location3, at location7, bob uses spanner2 to tighten nut2, from location7, bob picks up spanner2, nut5 is tightened by bob using spanner4 at location4, from location4 to location1, bob walks, bob tightens nut5 with spanner2 at location5, nut5 is tightened by bob using spanner3 at location3, bob tightens nut2 with spanner5 at location4, bob picks up spanner2 from location6, bob tightens nut4 with spanner2 at location4, nut1 is tightened by bob using spanner3 at location3, bob tightens nut4 with spanner5 at location1, spanner3 is picked up by bob from location5, from location9 to location7, bob walks, nut2 is tightened by bob using spanner5 at location6, nut4 is tightened by bob using spanner5 at location5, nut5 is tightened by bob using spanner2 at location2, bob walks to location3 from shed, from location8, bob picks up spanner2, at shed, bob uses spanner3 to tighten nut3, at location1, bob uses spanner2 to tighten nut5, at location3, bob uses spanner5 to tighten nut2, at location6, bob uses spanner2 to tighten nut3, bob picks up spanner3 from location1, spanner3 is picked up by bob from shed, bob walks from location2 to gate, at location5, bob uses spanner3 to tighten nut2, at location4, bob uses spanner4 to tighten nut3, bob tightens nut3 with spanner5 at location3, from location7 to location1, bob walks, bob walks from gate to location9, bob walks from location5 to location2, at location8, bob uses spanner1 to tighten nut4, bob walks from location1 to gate, at shed, bob uses spanner4 to tighten nut2, bob walks from location6 to location4, at location5, bob uses spanner5 to tighten nut3, bob tightens nut4 with spanner5 at shed, nut2 is tightened by bob using spanner1 at location8, bob walks from location9 to location5, at location4, bob uses spanner1 to tighten nut3, bob walks to location3 from location2, bob tightens nut3 with spanner3 at location5, spanner4 is picked up by bob from location6, bob tightens nut3 with spanner3 at location1, spanner1 is picked up by bob from location3, from location2 to location5, bob walks, bob tightens nut2 with spanner3 at location6, bob walks from location9 to location6, bob tightens nut2 with spanner3 at location7, at shed, bob uses spanner1 to tighten nut3, nut4 is tightened by bob using spanner1 at location2, spanner5 is picked up by bob from location2, bob walks to location1 from location3, nut1 is tightened by bob using spanner2 at location4, at location5, bob uses spanner2 to tighten nut4, nut2 is tightened by bob using spanner1 at gate, nut3 is tightened by bob using spanner5 at location8, at location4, bob uses spanner3 to tighten nut5, bob walks from location1 to shed, bob walks from location2 to shed, bob picks up spanner5 from location1, from location6, bob picks up spanner5, nut2 is tightened by bob using spanner5 at location5, bob walks to location3 from location6, bob walks from shed to location5, from location8 to gate, bob walks, nut3 is tightened by bob using spanner5 at location4, bob walks from location3 to location6, from location7 to location2, bob walks, bob tightens nut5 with spanner4 at location9, bob walks from location7 to gate, at location1, bob uses spanner1 to tighten nut4, from location2, bob picks up spanner3, nut5 is tightened by bob using spanner3 at location8, at location7, bob uses spanner5 to tighten nut3, spanner4 is picked up by bob from gate, bob tightens nut2 with spanner2 at location5, bob tightens nut1 with spanner5 at location2, bob walks from location2 to location8, bob tightens nut1 with spanner3 at shed, nut1 is tightened by bob using spanner3 at location7, nut3 is tightened by bob using spanner4 at location6, at shed, bob uses spanner2 to tighten nut4, at location5, bob uses spanner1 to tighten nut3, at location9, bob uses spanner1 to tighten nut2, at gate, bob uses spanner4 to tighten nut4, from location2 to location9, bob walks, at location5, bob uses spanner2 to tighten nut1, bob tightens nut1 with spanner2 at location3, bob walks from location5 to gate, nut1 is tightened by bob using spanner5 at location7, bob tightens nut1 with spanner3 at location6, bob tightens nut5 with spanner5 at shed, bob picks up spanner1 from location9, bob walks to location7 from location3, bob tightens nut3 with spanner3 at location7, bob tightens nut3 with spanner4 at gate, nut2 is tightened by bob using spanner2 at location3, at location6, bob uses spanner1 to tighten nut5, at location3, bob uses spanner3 to tighten nut3, spanner5 is picked up by bob from location3, bob tightens nut4 with spanner3 at location3, from location8 to location4, bob walks, bob tightens nut4 with spanner1 at shed, spanner4 is picked up by bob from location8, at location1, bob uses spanner2 to tighten nut1, from shed to location2, bob walks, at location9, bob uses spanner5 to tighten nut4, nut5 is tightened by bob using spanner1 at location2, bob walks from location4 to location5, from location9 to gate, bob walks, spanner4 is picked up by bob from location1, bob tightens nut2 with spanner1 at location1, bob walks to location8 from location4, from gate to location5, bob walks, bob tightens nut3 with spanner4 at location9, from location4, bob picks up spanner3, nut4 is tightened by bob using spanner1 at gate, bob tightens nut4 with spanner2 at location8, bob tightens nut1 with spanner1 at location7, at location4, bob uses spanner4 to tighten nut1, nut5 is tightened by bob using spanner1 at gate, from location4 to location3, bob walks, nut1 is tightened by bob using spanner5 at location6, at location1, bob uses spanner2 to tighten nut4, from location4 to location9, bob walks, bob tightens nut3 with spanner4 at location8, bob walks from location9 to location8, nut5 is tightened by bob using spanner3 at shed, bob walks to location5 from location7, at location7, bob uses spanner4 to tighten nut3, bob tightens nut5 with spanner4 at location1, bob walks from location7 to location8, bob walks from location3 to location9, bob tightens nut4 with spanner2 at gate, bob walks from location3 to shed, bob tightens nut5 with spanner5 at location1, bob tightens nut3 with spanner3 at location8, bob walks from location3 to location8, bob tightens nut3 with spanner4 at location5, nut5 is tightened by bob using spanner2 at location6, nut2 is tightened by bob using spanner1 at location3, from shed, bob picks up spanner4, nut3 is tightened by bob using spanner3 at gate, nut3 is tightened by bob using spanner1 at location7, nut5 is tightened by bob using spanner2 at location7, bob tightens nut5 with spanner5 at location7, at location2, bob uses spanner1 to tighten nut2, bob walks from location1 to location7, bob tightens nut1 with spanner1 at location3, bob picks up spanner1 from location8, bob walks to location7 from location4, nut5 is tightened by bob using spanner1 at location4, nut3 is tightened by bob using spanner3 at location2, nut1 is tightened by bob using spanner1 at location8, at location3, bob uses spanner1 to tighten nut3, bob walks to location5 from location1, bob walks to location4 from location5, bob walks to location3 from gate, nut2 is tightened by bob using spanner2 at location4, bob tightens nut1 with spanner5 at location1, bob walks to shed from location5, nut2 is tightened by bob using spanner5 at shed, bob walks from gate to location6, at location3, bob uses spanner3 to tighten nut2, nut5 is tightened by bob using spanner4 at location5, at location7, bob uses spanner2 to tighten nut3, bob tightens nut4 with spanner4 at shed, bob tightens nut4 with spanner4 at location5, bob tightens nut5 with spanner5 at location2, bob tightens nut5 with spanner4 at location6, bob tightens nut5 with spanner2 at location8, bob walks from location9 to location2, at location4, bob uses spanner3 to tighten nut3, nut3 is tightened by bob using spanner2 at location8, bob picks up spanner2 from location1, from location5 to location7, bob walks, at gate, bob uses spanner3 to tighten nut2, nut1 is tightened by bob using spanner5 at shed, from location2, bob picks up spanner4, at location4, bob uses spanner3 to tighten nut4, at location7, bob uses spanner5 to tighten nut4, bob walks from gate to location8, nut1 is tightened by bob using spanner1 at location4, bob tightens nut2 with spanner4 at location9, nut4 is tightened by bob using spanner1 at location5, nut4 is tightened by bob using spanner1 at location4, at location8, bob uses spanner1 to tighten nut5, at location2, bob uses spanner4 to tighten nut1, from shed to location9, bob walks, at location5, bob uses spanner4 to tighten nut2, bob tightens nut2 with spanner5 at location7, bob tightens nut4 with spanner1 at location6, at location8, bob uses spanner4 to tighten nut1, bob picks up spanner1 from location2, bob tightens nut4 with spanner2 at location6, bob tightens nut5 with spanner2 at location4, bob tightens nut1 with spanner5 at gate, bob tightens nut1 with spanner3 at location2, nut1 is tightened by bob using spanner4 at location3, bob walks from location6 to location1, bob tightens nut4 with spanner2 at location7, bob walks to location8 from location5, from location7 to location3, bob walks, nut1 is tightened by bob using spanner1 at location9, from location5 to location1, bob walks, bob tightens nut5 with spanner4 at gate, at location2, bob uses spanner3 to tighten nut2, at shed, bob uses spanner2 to tighten nut5, bob walks from location2 to location7, bob walks to location8 from location6, bob walks from gate to shed, bob tightens nut3 with spanner1 at location2, at location6, bob uses spanner4 to tighten nut4, from location3 to location4, bob walks, at gate, bob uses spanner5 to tighten nut2, bob tightens nut1 with spanner1 at location1, bob tightens nut1 with spanner3 at location8, at location5, bob uses spanner3 to tighten nut4, nut4 is tightened by bob using spanner1 at location7, nut5 is tightened by bob using spanner3 at gate, at location1, bob uses spanner3 to tighten nut4, at location9, bob uses spanner2 to tighten nut3, bob walks from gate to location1, nut1 is tightened by bob using spanner3 at location4, at location6, bob uses spanner3 to tighten nut5, bob tightens nut5 with spanner4 at location7, nut2 is tightened by bob using spanner2 at location2, bob walks from location6 to location2, nut5 is tightened by bob using spanner3 at location5, bob walks from location4 to gate, nut3 is tightened by bob using spanner4 at location2, nut1 is tightened by bob using spanner5 at location8, nut4 is tightened by bob using spanner5 at location6, at gate, bob uses spanner2 to tighten nut5, bob tightens nut4 with spanner4 at location8, from shed to location6, bob walks, nut1 is tightened by bob using spanner3 at location1, bob picks up spanner2 from location4, bob tightens nut4 with spanner5 at location3, from location6 to location7, bob walks, from location1 to location6, bob walks, bob walks to location2 from location4, nut3 is tightened by bob using spanner2 at location5, bob tightens nut1 with spanner2 at location9, nut3 is tightened by bob using spanner3 at location9, at location6, bob uses spanner1 to tighten nut2, at location4, bob uses spanner5 to tighten nut5, nut1 is tightened by bob using spanner4 at shed, bob picks up spanner5 from gate, from location6, bob picks up spanner1, at gate, bob uses spanner5 to tighten nut3, nut1 is tightened by bob using spanner1 at gate, bob tightens nut1 with spanner4 at location7, at location2, bob uses spanner4 to tighten nut2, bob tightens nut2 with spanner3 at location4, bob tightens nut5 with spanner1 at location7, bob walks from location7 to location4, from location3, bob picks up spanner3, nut2 is tightened by bob using spanner3 at shed, bob tightens nut2 with spanner5 at location1, bob tightens nut1 with spanner5 at location4, at location3, bob uses spanner4 to tighten nut5, from location7 to shed, bob walks, bob tightens nut5 with spanner2 at location9, from location8 to location7, bob walks, spanner3 is picked up by bob from location8, at location5, bob uses spanner3 to tighten nut1, nut5 is tightened by bob using spanner3 at location7, from location8 to location6, bob walks, nut5 is tightened by bob using spanner1 at location3, at location8, bob uses spanner1 to tighten nut3, bob walks from location6 to location5, at location7, bob uses spanner3 to tighten nut4, at location3, bob uses spanner5 to tighten nut5, at gate, bob uses spanner4 to tighten nut1, at location9, bob uses spanner5 to tighten nut3, bob tightens nut5 with spanner1 at location9, from location2 to location6, bob walks, bob picks up spanner5 from location8, spanner1 is picked up by bob from shed, bob tightens nut4 with spanner5 at location4, nut4 is tightened by bob using spanner4 at location4, at gate, bob uses spanner3 to tighten nut4, nut5 is tightened by bob using spanner5 at gate, nut2 is tightened by bob using spanner2 at location8, nut2 is tightened by bob using spanner5 at location8, nut1 is tightened by bob using spanner5 at location3, bob picks up spanner5 from shed, spanner1 is picked up by bob from gate, bob walks to location4 from location1, from location6 to location9, bob walks, bob picks up spanner5 from location5, at location5, bob uses spanner1 to tighten nut5, nut3 is tightened by bob using spanner1 at gate, spanner1 is picked up by bob from location4, bob walks to gate from location6, bob tightens nut4 with spanner3 at shed, nut2 is tightened by bob using spanner2 at location6, nut1 is tightened by bob using spanner2 at gate, at location9, bob uses spanner1 to tighten nut4, nut2 is tightened by bob using spanner1 at location4, nut2 is tightened by bob using spanner4 at location8, bob walks from location9 to shed, bob tightens nut4 with spanner3 at location6, from location8 to location9, bob walks, at location9, bob uses spanner3 to tighten nut5, from location7 to location6, bob walks, at location6, bob uses spanner5 to tighten nut5, bob walks to location3 from location8, from location3, bob picks up spanner2, bob walks from location4 to location6, nut2 is tightened by bob using spanner4 at gate, bob walks from gate to location4, at location6, bob uses spanner3 to tighten nut3, bob picks up spanner2 from location5, bob walks from shed to location4, nut3 is tightened by bob using spanner4 at shed, spanner1 is picked up by bob from location5, bob tightens nut1 with spanner2 at shed, nut3 is tightened by bob using spanner5 at location1, bob tightens nut1 with spanner3 at location9, bob tightens nut1 with spanner1 at location6, bob tightens nut2 with spanner1 at location5, bob walks to location1 from location2, from location5 to location9, bob walks, from shed to location8, bob walks, from location6, bob picks up spanner3, nut2 is tightened by bob using spanner4 at location6 and bob walks to location1 from location8", "plan_length": 1, "initial_state_nl": "A link between location6 and location7 exists, a link between location9 and gate exists, bob is at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, shed is linked to location1, spanner1 is at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 can be used, spanner4 is currently at location2, spanner5 is located at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: from shed to location1, bob walks to reach the current state. In this state, identify all actions that cannot be executed. If there are no such actions, write None.", "initial_state_nl_paraphrased": "There is a connection between location6 and location7, and another connection exists between location9 and the gate. Bob is currently at the shed. The locations are interconnected as follows: location1 is connected to location2, location2 is connected to location3, location3 is linked with location4, location4 is linked with location5, and location5 is connected to location6. Additionally, location7 is connected to location8, and location8 is connected to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is loose, nut4, which is not secured, and nut5, which is loose. The shed is connected to location1. The following spanners are located at the respective locations: spanner1 is at location8 and is usable, spanner2 is at location6 and is functional, spanner3 is at location2 and is usable, spanner4 is at location2 and can be used, and spanner5 is at location6 and is usable."}
{"question_id": "a1a7fc54-e7d3-473c-8e41-1fffa2e7587c", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "bob walks from location1 to location2", "plan_length": 1, "initial_state_nl": "A link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is currently at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is functional, spanner3 can be used, spanner3 is currently at location8, spanner4 can be used, spanner4 is currently at location5, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent steps are taken: bob moves from the shed to location1 to attain the current state. In this state, enumerate all possible actions that can be executed. Indicate None if there are no actions available.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, a connection between location9 and the gate exists, and a connection between the shed and location1 is present. Bob is currently situated at the shed. Location1 is connected to location2, which in turn is linked to location3, and location3 is connected to location4, which is then linked to location5. Furthermore, location6 is connected to location7, and location7 is linked to location8, which is connected to location9. At the gate, the following items are located: nut1, which is loose, nut2, also loose, nut3, which is not secured, nut4, also not secured, and nut5, which is loose. The current locations of the spanners are as follows: spanner1 is at location6 and is in working condition, spanner2 is at location8 and is functional, spanner3 is at location8 and can be used, spanner4 is at location5 and is usable, and spanner5 is at location7 and can be used."}
{"question_id": "d1ff5e7c-5737-4803-a1be-02f42ff30075", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob picks up spanner5 from location1, from location1, bob picks up spanner4, from location1 to location2, bob walks, from location2 to location3, bob walks, bob walks to location4 from location3, bob picks up spanner1 from location4, bob walks to location5 from location4, bob walks to location6 from location5, bob picks up spanner3 from location6, bob walks from location6 to location7, spanner2 is picked up by bob from location7, bob walks from location7 to location8, bob walks to location9 from location8, bob walks to gate from location9, at gate, bob uses spanner5 to tighten nut1, bob tightens nut2 with spanner4 at gate, bob tightens nut3 with spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "bob walks from location9 to gate, nut3 is tightened by bob using spanner5 at location7, at gate, bob uses spanner4 to tighten nut1, at location4, bob uses spanner3 to tighten nut3, from location7, bob picks up spanner3, nut4 is tightened by bob using spanner2 at location9, nut4 is tightened by bob using spanner2 at location7, nut1 is tightened by bob using spanner4 at location1, bob walks to location1 from location7, at location5, bob uses spanner1 to tighten nut4, bob walks from location6 to location2, bob tightens nut3 with spanner2 at location2, at location9, bob uses spanner5 to tighten nut2, bob walks to location8 from location1, at location4, bob uses spanner4 to tighten nut4, at location4, bob uses spanner2 to tighten nut1, from location1 to location2, bob walks, bob walks to gate from location4, nut3 is tightened by bob using spanner4 at location7, at location5, bob uses spanner4 to tighten nut1, nut4 is tightened by bob using spanner4 at location1, bob tightens nut4 with spanner3 at location2, nut3 is tightened by bob using spanner4 at gate, at location7, bob uses spanner3 to tighten nut1, at gate, bob uses spanner2 to tighten nut1, bob walks to location3 from location5, bob walks to location8 from location2, at location4, bob uses spanner5 to tighten nut4, at location5, bob uses spanner1 to tighten nut5, bob tightens nut4 with spanner1 at location2, at location9, bob uses spanner1 to tighten nut1, from location9, bob picks up spanner2, bob tightens nut3 with spanner2 at location7, from location5 to location8, bob walks, at location1, bob uses spanner2 to tighten nut3, bob tightens nut3 with spanner3 at location1, at gate, bob uses spanner4 to tighten nut4, nut3 is tightened by bob using spanner3 at location9, nut4 is tightened by bob using spanner3 at shed, spanner5 is picked up by bob from location8, nut3 is tightened by bob using spanner3 at shed, nut5 is tightened by bob using spanner3 at gate, bob walks to location8 from shed, bob walks to gate from location6, nut5 is tightened by bob using spanner4 at location5, at shed, bob uses spanner5 to tighten nut3, spanner1 is picked up by bob from shed, at location6, bob uses spanner5 to tighten nut4, from location1, bob picks up spanner5, from location5 to shed, bob walks, nut5 is tightened by bob using spanner3 at location2, at location3, bob uses spanner1 to tighten nut1, at shed, bob uses spanner2 to tighten nut2, bob tightens nut1 with spanner1 at shed, nut5 is tightened by bob using spanner1 at shed, bob walks from location7 to location3, from location3 to gate, bob walks, spanner3 is picked up by bob from gate, bob picks up spanner1 from location8, at location1, bob uses spanner3 to tighten nut5, bob tightens nut2 with spanner4 at location3, bob tightens nut2 with spanner3 at location5, at gate, bob uses spanner5 to tighten nut5, from shed to location9, bob walks, from location9, bob picks up spanner3, nut4 is tightened by bob using spanner2 at shed, nut5 is tightened by bob using spanner4 at shed, from location1 to location9, bob walks, from location7, bob picks up spanner5, bob walks from location3 to location8, at location5, bob uses spanner5 to tighten nut4, bob walks to shed from location1, bob tightens nut2 with spanner3 at location3, bob walks to location9 from location7, nut4 is tightened by bob using spanner1 at gate, at gate, bob uses spanner5 to tighten nut3, nut2 is tightened by bob using spanner5 at location6, at location4, bob uses spanner4 to tighten nut5, bob walks from location6 to location1, bob tightens nut3 with spanner4 at shed, bob walks from location9 to location1, bob walks to shed from location9, from shed to location5, bob walks, bob tightens nut1 with spanner5 at location6, nut1 is tightened by bob using spanner1 at location8, nut2 is tightened by bob using spanner5 at location2, nut4 is tightened by bob using spanner3 at location3, at location3, bob uses spanner5 to tighten nut4, at location7, bob uses spanner2 to tighten nut1, at location9, bob uses spanner4 to tighten nut2, nut5 is tightened by bob using spanner3 at location4, at location5, bob uses spanner3 to tighten nut1, nut2 is tightened by bob using spanner3 at location1, nut2 is tightened by bob using spanner1 at shed, bob tightens nut4 with spanner5 at location8, nut2 is tightened by bob using spanner2 at location3, nut5 is tightened by bob using spanner2 at location2, at location5, bob uses spanner3 to tighten nut3, bob walks from location3 to location4, at location5, bob uses spanner2 to tighten nut2, from gate to location9, bob walks, bob walks from location6 to location8, bob tightens nut4 with spanner2 at location2, at location8, bob uses spanner2 to tighten nut1, bob walks to location7 from location8, bob tightens nut4 with spanner1 at location7, from shed, bob picks up spanner2, from location2, bob picks up spanner4, nut2 is tightened by bob using spanner4 at location6, bob picks up spanner4 from location5, at location2, bob uses spanner5 to tighten nut5, bob tightens nut1 with spanner2 at location2, from location8, bob picks up spanner4, from location5, bob picks up spanner5, nut3 is tightened by bob using spanner1 at location4, nut1 is tightened by bob using spanner5 at location1, bob tightens nut3 with spanner1 at location2, nut5 is tightened by bob using spanner5 at location8, bob walks to location4 from gate, bob walks from shed to location6, bob walks from shed to location3, bob walks to gate from location7, spanner3 is picked up by bob from location6, from location2, bob picks up spanner5, nut2 is tightened by bob using spanner1 at location3, bob walks to location4 from location1, bob picks up spanner5 from location4, bob walks to location4 from location8, spanner5 is picked up by bob from shed, nut3 is tightened by bob using spanner3 at location2, bob tightens nut2 with spanner3 at location8, bob tightens nut5 with spanner4 at location3, nut1 is tightened by bob using spanner2 at location1, bob tightens nut3 with spanner2 at location4, at location6, bob uses spanner5 to tighten nut3, nut5 is tightened by bob using spanner1 at location9, nut5 is tightened by bob using spanner1 at location8, at location4, bob uses spanner2 to tighten nut2, bob picks up spanner2 from location3, nut3 is tightened by bob using spanner3 at location8, bob walks from location4 to location1, bob tightens nut2 with spanner3 at location4, bob walks from location8 to location9, bob walks to location8 from location7, bob walks from shed to location7, bob walks from location7 to location2, at location4, bob uses spanner1 to tighten nut5, bob picks up spanner3 from location5, from location3, bob picks up spanner1, bob tightens nut3 with spanner3 at location6, from location1, bob picks up spanner1, nut5 is tightened by bob using spanner2 at shed, bob tightens nut4 with spanner2 at location4, bob tightens nut3 with spanner2 at location6, from gate, bob picks up spanner4, bob tightens nut3 with spanner5 at location1, bob tightens nut1 with spanner3 at location9, nut3 is tightened by bob using spanner2 at location5, bob tightens nut3 with spanner4 at location1, nut1 is tightened by bob using spanner2 at location5, bob walks from location8 to location6, bob picks up spanner3 from location4, at shed, bob uses spanner5 to tighten nut5, bob walks to location7 from gate, bob walks from gate to location3, bob tightens nut5 with spanner4 at location9, nut1 is tightened by bob using spanner1 at location5, nut1 is tightened by bob using spanner2 at location3, at location6, bob uses spanner1 to tighten nut1, bob walks to location3 from location1, bob walks to location3 from location2, nut2 is tightened by bob using spanner1 at location9, nut1 is tightened by bob using spanner5 at location3, bob tightens nut5 with spanner5 at location9, spanner4 is picked up by bob from location6, nut1 is tightened by bob using spanner5 at location7, bob walks to location6 from location7, bob tightens nut4 with spanner2 at location5, bob tightens nut2 with spanner4 at location2, bob walks from gate to shed, nut5 is tightened by bob using spanner5 at location7, bob walks to location7 from location4, at shed, bob uses spanner3 to tighten nut1, bob picks up spanner1 from location6, nut5 is tightened by bob using spanner1 at location6, at location9, bob uses spanner5 to tighten nut4, from location1 to location7, bob walks, from location2 to location7, bob walks, nut2 is tightened by bob using spanner5 at location8, from location9 to location2, bob walks, nut1 is tightened by bob using spanner3 at location2, bob walks to location9 from location2, at location3, bob uses spanner5 to tighten nut5, spanner5 is picked up by bob from location6, at location6, bob uses spanner5 to tighten nut5, bob tightens nut2 with spanner4 at location4, bob tightens nut2 with spanner2 at location8, bob tightens nut4 with spanner2 at location3, bob tightens nut4 with spanner2 at location8, nut1 is tightened by bob using spanner1 at location7, nut1 is tightened by bob using spanner4 at location4, at location7, bob uses spanner2 to tighten nut2, nut3 is tightened by bob using spanner5 at location9, nut5 is tightened by bob using spanner4 at location7, nut5 is tightened by bob using spanner1 at location3, nut1 is tightened by bob using spanner5 at location9, at location6, bob uses spanner1 to tighten nut3, at gate, bob uses spanner4 to tighten nut5, from location3 to location2, bob walks, bob tightens nut4 with spanner4 at location6, bob walks from location7 to location4, nut1 is tightened by bob using spanner5 at shed, bob walks to location7 from location6, bob walks to location4 from location9, at location9, bob uses spanner3 to tighten nut2, bob walks from location4 to location2, bob walks from shed to location2, bob walks to location8 from location9, spanner3 is picked up by bob from shed, from location9, bob picks up spanner1, bob walks to location7 from location9, nut3 is tightened by bob using spanner4 at location3, bob walks to location1 from location5, bob picks up spanner5 from location9, at location7, bob uses spanner3 to tighten nut4, from location6 to location5, bob walks, at location7, bob uses spanner1 to tighten nut5, nut1 is tightened by bob using spanner2 at location6, at location1, bob uses spanner1 to tighten nut1, nut1 is tightened by bob using spanner5 at location2, bob tightens nut2 with spanner4 at location5, bob tightens nut5 with spanner3 at shed, bob walks from location8 to location1, bob walks to gate from location2, spanner2 is picked up by bob from location1, nut4 is tightened by bob using spanner3 at location8, bob picks up spanner3 from location1, from location2 to shed, bob walks, spanner4 is picked up by bob from location1, at location8, bob uses spanner1 to tighten nut4, spanner5 is picked up by bob from location3, bob walks to location2 from location8, at location1, bob uses spanner1 to tighten nut3, bob tightens nut3 with spanner1 at location3, nut4 is tightened by bob using spanner5 at gate, nut2 is tightened by bob using spanner2 at location2, bob tightens nut5 with spanner3 at location6, bob walks from location9 to location3, from location3 to location7, bob walks, at location3, bob uses spanner5 to tighten nut3, at location6, bob uses spanner2 to tighten nut5, at gate, bob uses spanner3 to tighten nut4, bob tightens nut5 with spanner2 at location8, bob tightens nut5 with spanner4 at location8, from location4 to location3, bob walks, bob walks to location7 from location5, at location8, bob uses spanner4 to tighten nut3, at location4, bob uses spanner1 to tighten nut4, at location6, bob uses spanner4 to tighten nut3, nut4 is tightened by bob using spanner3 at location5, from location2 to location6, bob walks, bob picks up spanner1 from location2, nut2 is tightened by bob using spanner1 at location5, at location4, bob uses spanner1 to tighten nut2, bob walks from shed to location4, bob walks to location9 from location4, nut5 is tightened by bob using spanner5 at location5, nut4 is tightened by bob using spanner4 at location9, bob walks to location9 from location6, bob tightens nut2 with spanner5 at location4, at location8, bob uses spanner3 to tighten nut5, bob tightens nut1 with spanner2 at location9, from gate, bob picks up spanner5, bob walks from location3 to location6, at location1, bob uses spanner2 to tighten nut2, at shed, bob uses spanner1 to tighten nut3, nut1 is tightened by bob using spanner5 at location4, bob tightens nut2 with spanner3 at location6, at gate, bob uses spanner2 to tighten nut2, bob walks to location6 from location1, nut5 is tightened by bob using spanner2 at gate, bob tightens nut4 with spanner5 at location7, spanner2 is picked up by bob from location7, bob picks up spanner1 from location4, at location5, bob uses spanner5 to tighten nut3, bob picks up spanner2 from location8, bob tightens nut3 with spanner3 at location3, at location7, bob uses spanner3 to tighten nut2, nut3 is tightened by bob using spanner1 at location9, bob tightens nut5 with spanner1 at location2, bob walks to location6 from location5, at location6, bob uses spanner2 to tighten nut4, at location3, bob uses spanner2 to tighten nut5, bob tightens nut1 with spanner3 at location1, bob tightens nut5 with spanner2 at location1, nut3 is tightened by bob using spanner1 at location5, from gate to location5, bob walks, nut3 is tightened by bob using spanner2 at location3, bob tightens nut2 with spanner3 at shed, spanner1 is picked up by bob from location7, nut3 is tightened by bob using spanner4 at location2, nut3 is tightened by bob using spanner2 at shed, at location8, bob uses spanner1 to tighten nut2, bob tightens nut2 with spanner5 at location7, nut5 is tightened by bob using spanner4 at location2, at location8, bob uses spanner3 to tighten nut1, at location4, bob uses spanner5 to tighten nut5, at location1, bob uses spanner3 to tighten nut4, at location2, bob uses spanner1 to tighten nut1, bob tightens nut3 with spanner5 at location8, nut1 is tightened by bob using spanner3 at location3, nut5 is tightened by bob using spanner2 at location5, bob walks to shed from location3, bob walks to gate from location1, bob walks to location4 from location2, from location4 to location6, bob walks, bob tightens nut1 with spanner1 at gate, nut4 is tightened by bob using spanner1 at shed, bob walks to location5 from location9, at location5, bob uses spanner5 to tighten nut2, at shed, bob uses spanner5 to tighten nut4, bob walks to location6 from gate, at location5, bob uses spanner3 to tighten nut5, bob tightens nut2 with spanner2 at location6, bob tightens nut1 with spanner4 at location6, bob walks from location5 to location9, at gate, bob uses spanner5 to tighten nut1, bob walks from gate to location8, from shed, bob picks up spanner4, bob tightens nut4 with spanner4 at location3, at location1, bob uses spanner4 to tighten nut5, from location2, bob picks up spanner2, bob walks from location9 to location6, bob walks to location5 from location8, bob tightens nut1 with spanner4 at location8, at location8, bob uses spanner1 to tighten nut3, bob walks from location3 to location5, at location5, bob uses spanner4 to tighten nut3, bob tightens nut1 with spanner4 at location3, bob picks up spanner4 from location9, bob walks from location6 to location3, bob picks up spanner3 from location2, bob tightens nut2 with spanner1 at location1, bob tightens nut1 with spanner3 at gate, nut1 is tightened by bob using spanner3 at location4, nut3 is tightened by bob using spanner1 at gate, bob tightens nut1 with spanner5 at location5, bob walks from location3 to location1, at location2, bob uses spanner5 to tighten nut3, at shed, bob uses spanner4 to tighten nut4, nut2 is tightened by bob using spanner1 at location6, nut2 is tightened by bob using spanner1 at location7, nut2 is tightened by bob using spanner5 at gate, bob walks to location5 from location4, bob picks up spanner4 from location7, nut1 is tightened by bob using spanner4 at shed, nut5 is tightened by bob using spanner3 at location3, at location4, bob uses spanner4 to tighten nut3, bob walks to location1 from gate, at location1, bob uses spanner5 to tighten nut5, nut1 is tightened by bob using spanner3 at location6, bob walks to gate from shed, at location6, bob uses spanner4 to tighten nut5, nut2 is tightened by bob using spanner2 at location9, nut3 is tightened by bob using spanner4 at location9, at location8, bob uses spanner4 to tighten nut2, bob walks to location3 from location8, bob tightens nut4 with spanner4 at location2, from location7 to location5, bob walks, at location9, bob uses spanner4 to tighten nut1, nut3 is tightened by bob using spanner3 at gate, bob picks up spanner2 from gate, nut1 is tightened by bob using spanner4 at location2, nut2 is tightened by bob using spanner5 at location1, bob walks from shed to location1, from location3, bob picks up spanner4, bob tightens nut2 with spanner4 at location1, from location6 to location4, bob walks, bob picks up spanner2 from location4, bob tightens nut4 with spanner5 at location2, from location5 to gate, bob walks, bob picks up spanner3 from location3, bob tightens nut1 with spanner2 at shed, from location5, bob picks up spanner2, bob walks to shed from location8, nut1 is tightened by bob using spanner4 at location7, from location5 to location4, bob walks, nut2 is tightened by bob using spanner4 at location7, bob tightens nut3 with spanner5 at location4, nut2 is tightened by bob using spanner5 at shed, nut5 is tightened by bob using spanner2 at location7, from location6 to shed, bob walks, nut2 is tightened by bob using spanner1 at location2, bob tightens nut3 with spanner1 at location7, at location8, bob uses spanner4 to tighten nut4, bob tightens nut3 with spanner2 at gate, nut2 is tightened by bob using spanner3 at gate, bob tightens nut5 with spanner1 at location1, at location9, bob uses spanner2 to tighten nut5, from gate to location2, bob walks, bob tightens nut2 with spanner1 at gate, nut4 is tightened by bob using spanner4 at location7, nut4 is tightened by bob using spanner3 at location4, bob walks from location2 to location1, bob tightens nut2 with spanner4 at shed, from gate, bob picks up spanner1, from location7 to shed, bob walks, nut4 is tightened by bob using spanner1 at location6, bob tightens nut3 with spanner3 at location7, spanner3 is picked up by bob from location8, bob walks to location5 from location2, at location8, bob uses spanner5 to tighten nut1, bob tightens nut4 with spanner3 at location9, at location9, bob uses spanner2 to tighten nut3, nut5 is tightened by bob using spanner3 at location7, at location4, bob uses spanner1 to tighten nut1, spanner2 is picked up by bob from location6, nut3 is tightened by bob using spanner2 at location8, from location5, bob picks up spanner1, bob walks from location4 to location8, nut5 is tightened by bob using spanner3 at location9, from location4 to shed, bob walks, at location9, bob uses spanner1 to tighten nut4, nut2 is tightened by bob using spanner5 at location3, at location4, bob uses spanner2 to tighten nut5, at location6, bob uses spanner3 to tighten nut4, from location1 to location5, bob walks, at gate, bob uses spanner2 to tighten nut4, bob walks to location2 from location5, bob tightens nut4 with spanner4 at location5, bob walks from location3 to location9, bob tightens nut4 with spanner1 at location3, bob walks from location8 to gate, bob tightens nut4 with spanner1 at location1, at location1, bob uses spanner2 to tighten nut4, bob tightens nut4 with spanner5 at location1, bob picks up spanner4 from location4, bob tightens nut2 with spanner4 at gate and at location2, bob uses spanner3 to tighten nut2", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob starts at the shed and walks to location1, where he collects spanner5 and then spanner4. He then proceeds to location2, followed by location3, and then walks from location3 to location4. At location4, Bob picks up spanner1 and then walks to location5, then to location6, where he collects spanner3. Next, he walks to location7, picks up spanner2, and then walks to location8, followed by location9. Finally, he walks to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, list all inexecutable actions. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and another connection exists between location6 and location7. Bob is currently situated at the shed. Additionally, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location7 is linked to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is unsecured, nut2, which is loose, nut3, which is also loose, nut4, which is loose, and nut5, which is loose as well. The shed is connected to location1. The following spanners are located at their respective locations: spanner1, which is functional, is at location4, spanner2, which is functional, is at location7, spanner3, which can be used, is at location6, spanner4, which can be used, is at location1, and spanner5, which is functional, is also at location1."}
{"question_id": "8598f2fd-9519-49f8-87e6-b6c086422a78", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, bob picks up spanner5 from location1, spanner4 is picked up by bob from location1, bob walks to location2 from location1, bob walks to location3 from location2, bob walks to location4 from location3, spanner1 is picked up by bob from location4, bob walks to location5 from location4, bob walks to location6 from location5, from location6, bob picks up spanner3, bob walks from location6 to location7, spanner2 is picked up by bob from location7, bob walks from location7 to location8, from location8 to location9, bob walks, bob walks to gate from location9, bob tightens nut1 with spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, bob tightens nut3 with spanner3 at gate and nut4 is tightened by bob using spanner2 at gate to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "nut5 is tightened by bob using spanner1 at gate", "plan_length": 19, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then collects spanner5 and spanner4 from location1. He proceeds to location2 from location1, then to location3 from location2, and subsequently to location4 from location3. At location4, Bob picks up spanner1, then heads to location5, followed by location6. From location6, he retrieves spanner3 and moves to location7, where he collects spanner2. Bob then walks to location8 and then to location9. Finally, he proceeds to the gate, where he uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, ultimately reaching the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and another connection exists between location6 and location7. Bob is currently situated at the shed. Additionally, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location7 is linked to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is not secured, nut2, which is loose, nut3, which is loose, nut4, which is loose, and nut5, which is also loose. The shed is connected to location1. The following spanners are located at their respective locations: spanner1, which is functional, is at location4, spanner2, which is functional, is at location7, spanner3, which can be used, is at location6, spanner4, which can be used, is at location1, and spanner5, which is functional, is also at location1."}
{"question_id": "0c695bf0-df71-4832-81c5-71173addd73a", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "from location1 to location2, bob walks, spanner5 is picked up by bob from location1 and bob picks up spanner4 from location1", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent steps are taken: bob moves from the shed to location1 to attain the current state. In this state, enumerate all possible actions that can be executed. If there are no actions, indicate None.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and another connection exists between location6 and location7. Bob is currently situated at the shed. Additionally, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is unsecured, nut2, which is loose, nut3, which is also loose, nut4, which is loose, and nut5, which is loose as well. The shed is connected to location1. The following spanners are located at their respective locations: spanner1, which is functional, is at location4, spanner2, which is functional, is at location7, spanner3, which can be used, is at location6, spanner4, which can be used, is at location1, and spanner5, which is functional, is also at location1."}
{"question_id": "605a195d-b13e-46d3-8cf9-a568baa5d431", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, bob walks to location2 from location1, spanner4 is picked up by bob from location2, spanner3 is picked up by bob from location2, from location2 to location3, bob walks, bob walks from location3 to location4, from location4 to location5, bob walks, from location5 to location6, bob walks, spanner5 is picked up by bob from location6 and bob picks up spanner2 from location6 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "bob walks to location7 from location6", "plan_length": 10, "initial_state_nl": "A link between location6 and location7 exists, a link between location9 and gate exists, bob is at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, shed is linked to location1, spanner1 is at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 can be used, spanner4 is currently at location2, spanner5 is located at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: Bob walks from the shed to location1, then from location1 to location2, where he picks up spanner4 and spanner3. He then proceeds to location3, followed by location4, location5, and finally location6, where he collects spanner5 and spanner2, resulting in the current state. In this state, list all possible actions that can be executed. If there are no executable actions, write None.", "initial_state_nl_paraphrased": "There is a connection between location6 and location7, and another connection exists between location9 and the gate. Bob is currently at the shed. The locations are interconnected as follows: location1 is connected to location2, location2 is connected to location3, location3 is linked with location4, location4 is linked with location5, and location5 is connected to location6. Additionally, location7 is connected to location8, and location8 is connected to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is loose, nut4, which is not secured, and nut5, which is loose. The shed is connected to location1. The following spanners are located at the respective locations: spanner1 is at location8 and is usable, spanner2 is at location6 and is functional, spanner3 is at location2 and is usable, spanner4 is at location2 and can be used, and spanner5 is at location6 and is usable."}
{"question_id": "fea842ee-7dee-4626-b619-d29748af7735", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, bob walks to location2 from location1, spanner3 is picked up by bob from location2, bob walks from location2 to location3, from location3, bob picks up spanner5, from location3, bob picks up spanner1, bob walks to location4 from location3, bob walks from location4 to location5, bob picks up spanner2 from location5, bob walks from location5 to location6, spanner4 is picked up by bob from location6, bob walks from location6 to location7, bob walks to location8 from location7, bob walks to location9 from location8, bob walks from location9 to gate, bob tightens nut1 with spanner5 at gate, at gate, bob uses spanner4 to tighten nut2, nut3 is tightened by bob using spanner3 at gate and at gate, bob uses spanner2 to tighten nut4 to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "nut5 is tightened by bob using spanner4 at location5, from location8 to shed, bob walks, nut5 is tightened by bob using spanner5 at location4, bob tightens nut1 with spanner4 at location1, from location8 to location9, bob walks, nut2 is tightened by bob using spanner2 at location4, nut3 is tightened by bob using spanner3 at location5, bob picks up spanner4 from location2, from gate, bob picks up spanner2, bob tightens nut3 with spanner4 at location1, bob walks to shed from gate, bob tightens nut1 with spanner5 at location9, bob tightens nut2 with spanner3 at shed, bob walks from location9 to gate, nut1 is tightened by bob using spanner4 at gate, at location1, bob uses spanner1 to tighten nut5, at location1, bob uses spanner1 to tighten nut4, bob walks to location5 from location3, bob tightens nut3 with spanner4 at location5, bob tightens nut5 with spanner3 at location3, at location4, bob uses spanner4 to tighten nut4, bob picks up spanner5 from shed, at location7, bob uses spanner2 to tighten nut5, bob tightens nut2 with spanner3 at location4, bob tightens nut4 with spanner1 at shed, from location2 to location4, bob walks, nut5 is tightened by bob using spanner4 at location3, bob tightens nut3 with spanner1 at location5, bob tightens nut1 with spanner5 at location4, spanner4 is picked up by bob from location4, bob tightens nut5 with spanner4 at location9, bob tightens nut4 with spanner4 at location3, bob walks to gate from location1, at location4, bob uses spanner1 to tighten nut4, from location7 to location1, bob walks, bob tightens nut2 with spanner4 at location3, nut3 is tightened by bob using spanner2 at location5, spanner5 is picked up by bob from location2, at shed, bob uses spanner5 to tighten nut4, from location9 to shed, bob walks, nut3 is tightened by bob using spanner1 at location3, bob walks to gate from location2, bob picks up spanner2 from location6, at location6, bob uses spanner5 to tighten nut5, bob walks from location6 to location9, bob walks from location5 to location7, at location9, bob uses spanner2 to tighten nut2, spanner3 is picked up by bob from location3, bob picks up spanner2 from location4, nut2 is tightened by bob using spanner5 at location3, bob walks from gate to location7, from location9 to location7, bob walks, nut4 is tightened by bob using spanner3 at location2, at location8, bob uses spanner2 to tighten nut4, bob tightens nut3 with spanner1 at location8, at gate, bob uses spanner2 to tighten nut5, nut3 is tightened by bob using spanner2 at location8, bob tightens nut1 with spanner1 at location3, nut5 is tightened by bob using spanner5 at location5, bob tightens nut5 with spanner2 at location9, at location5, bob uses spanner3 to tighten nut2, at location6, bob uses spanner4 to tighten nut5, from location3 to location6, bob walks, bob tightens nut4 with spanner2 at gate, bob walks from location2 to location6, spanner1 is picked up by bob from shed, bob walks from location4 to location2, bob tightens nut3 with spanner1 at location6, nut1 is tightened by bob using spanner4 at location6, nut3 is tightened by bob using spanner3 at location7, bob picks up spanner3 from location2, bob walks to location7 from location6, at location2, bob uses spanner3 to tighten nut1, bob tightens nut5 with spanner2 at shed, bob tightens nut4 with spanner5 at location4, at location6, bob uses spanner2 to tighten nut5, at location8, bob uses spanner3 to tighten nut4, from location5, bob picks up spanner1, bob picks up spanner5 from location1, from location4 to gate, bob walks, bob tightens nut3 with spanner2 at location1, bob tightens nut5 with spanner5 at location8, bob walks to location5 from shed, at location3, bob uses spanner1 to tighten nut5, nut5 is tightened by bob using spanner3 at location7, spanner4 is picked up by bob from gate, at gate, bob uses spanner4 to tighten nut2, at shed, bob uses spanner3 to tighten nut5, nut3 is tightened by bob using spanner3 at location6, bob tightens nut5 with spanner5 at location1, at location5, bob uses spanner5 to tighten nut1, at location2, bob uses spanner1 to tighten nut4, bob tightens nut1 with spanner5 at location6, bob tightens nut3 with spanner5 at location2, bob walks to location2 from location5, bob walks to location8 from location5, bob walks from location4 to location9, nut1 is tightened by bob using spanner1 at location7, bob tightens nut2 with spanner1 at location1, from gate to location5, bob walks, spanner4 is picked up by bob from location8, bob picks up spanner2 from location3, spanner1 is picked up by bob from location8, from gate, bob picks up spanner5, bob tightens nut4 with spanner4 at location5, at gate, bob uses spanner3 to tighten nut5, at location7, bob uses spanner1 to tighten nut4, at location2, bob uses spanner2 to tighten nut1, from shed, bob picks up spanner3, from location2, bob picks up spanner2, bob picks up spanner3 from location8, from location4 to location5, bob walks, bob tightens nut4 with spanner3 at gate, bob tightens nut5 with spanner3 at location8, bob walks from location4 to location3, nut4 is tightened by bob using spanner3 at location9, nut4 is tightened by bob using spanner2 at shed, nut2 is tightened by bob using spanner4 at location8, at location5, bob uses spanner3 to tighten nut4, bob walks from location9 to location1, nut4 is tightened by bob using spanner4 at location2, from location1 to location7, bob walks, spanner2 is picked up by bob from location9, bob tightens nut4 with spanner4 at location1, nut3 is tightened by bob using spanner3 at gate, bob walks to location2 from location6, from location2 to location9, bob walks, bob walks to location9 from location3, from location5 to gate, bob walks, bob picks up spanner1 from location6, at location4, bob uses spanner3 to tighten nut3, at location5, bob uses spanner1 to tighten nut4, bob walks to shed from location3, nut5 is tightened by bob using spanner5 at location3, spanner1 is picked up by bob from location7, bob walks from location4 to location6, bob walks to location1 from location4, bob walks to location8 from location3, bob walks from location6 to location4, bob walks to location9 from location7, at shed, bob uses spanner3 to tighten nut3, bob walks to shed from location2, nut5 is tightened by bob using spanner1 at location4, bob picks up spanner3 from location5, at gate, bob uses spanner2 to tighten nut1, bob picks up spanner3 from location7, bob tightens nut2 with spanner4 at location5, bob tightens nut4 with spanner3 at location7, from shed to location4, bob walks, nut2 is tightened by bob using spanner5 at location9, nut4 is tightened by bob using spanner3 at location1, bob walks from location9 to location6, bob walks to location2 from shed, nut3 is tightened by bob using spanner1 at location7, nut5 is tightened by bob using spanner3 at location6, bob picks up spanner2 from location5, bob tightens nut2 with spanner1 at location4, bob tightens nut3 with spanner3 at location9, bob walks to location2 from location8, bob tightens nut3 with spanner5 at location7, bob tightens nut1 with spanner1 at location6, nut2 is tightened by bob using spanner5 at location6, nut3 is tightened by bob using spanner4 at location8, bob walks to location4 from location7, bob walks from location8 to location6, bob tightens nut5 with spanner1 at shed, bob tightens nut4 with spanner2 at location6, bob walks from gate to location8, at location5, bob uses spanner3 to tighten nut1, nut5 is tightened by bob using spanner3 at location5, bob tightens nut2 with spanner2 at location6, at location2, bob uses spanner1 to tighten nut2, nut5 is tightened by bob using spanner4 at location1, bob picks up spanner4 from location1, from location4, bob picks up spanner3, bob walks from location7 to location8, nut3 is tightened by bob using spanner1 at location9, at location5, bob uses spanner1 to tighten nut2, bob walks to location7 from location3, at location7, bob uses spanner5 to tighten nut2, bob tightens nut4 with spanner5 at location1, bob walks to location1 from location8, from location7, bob picks up spanner4, bob tightens nut1 with spanner1 at gate, at location9, bob uses spanner3 to tighten nut1, nut4 is tightened by bob using spanner1 at gate, nut1 is tightened by bob using spanner4 at location5, nut1 is tightened by bob using spanner4 at location2, bob walks to location3 from location2, bob walks to location9 from location5, bob walks from location6 to location1, bob tightens nut4 with spanner2 at location7, at location5, bob uses spanner5 to tighten nut3, from location1 to location2, bob walks, at location5, bob uses spanner5 to tighten nut2, at location2, bob uses spanner3 to tighten nut2, from location3 to location4, bob walks, at location5, bob uses spanner5 to tighten nut4, nut5 is tightened by bob using spanner4 at location4, at gate, bob uses spanner5 to tighten nut4, nut5 is tightened by bob using spanner2 at location1, nut5 is tightened by bob using spanner1 at location9, bob tightens nut1 with spanner3 at location4, nut3 is tightened by bob using spanner2 at location9, at location2, bob uses spanner2 to tighten nut3, nut5 is tightened by bob using spanner5 at gate, from location2 to location1, bob walks, bob walks to location3 from gate, bob tightens nut3 with spanner2 at location4, bob tightens nut2 with spanner4 at location6, at location4, bob uses spanner5 to tighten nut3, bob tightens nut1 with spanner5 at location2, nut1 is tightened by bob using spanner3 at gate, bob walks from location2 to location8, at shed, bob uses spanner5 to tighten nut3, nut5 is tightened by bob using spanner1 at location7, nut4 is tightened by bob using spanner4 at gate, nut1 is tightened by bob using spanner3 at location3, nut1 is tightened by bob using spanner4 at location3, bob walks from location2 to location7, nut2 is tightened by bob using spanner1 at gate, nut4 is tightened by bob using spanner2 at location5, nut4 is tightened by bob using spanner1 at location3, bob tightens nut5 with spanner1 at location6, bob tightens nut2 with spanner5 at shed, bob tightens nut2 with spanner4 at location7, from location1, bob picks up spanner3, from location5 to location3, bob walks, nut5 is tightened by bob using spanner5 at location9, bob walks from gate to location9, from location8 to location5, bob walks, at location9, bob uses spanner3 to tighten nut2, at gate, bob uses spanner4 to tighten nut3, bob walks to location4 from location5, at location4, bob uses spanner4 to tighten nut3, from location1 to location9, bob walks, nut5 is tightened by bob using spanner4 at location2, bob walks from location7 to location2, bob walks from location2 to location5, bob walks from location7 to gate, bob tightens nut5 with spanner4 at location8, at location6, bob uses spanner1 to tighten nut2, bob walks from location9 to location3, bob picks up spanner1 from location1, bob tightens nut3 with spanner1 at location4, at location6, bob uses spanner2 to tighten nut1, from gate to location4, bob walks, nut1 is tightened by bob using spanner5 at gate, bob tightens nut2 with spanner4 at location2, bob picks up spanner1 from location3, at shed, bob uses spanner2 to tighten nut3, bob tightens nut1 with spanner3 at location8, bob walks from location5 to shed, at location1, bob uses spanner3 to tighten nut3, bob walks from location1 to location3, bob walks from location9 to location5, at location6, bob uses spanner3 to tighten nut4, at location9, bob uses spanner2 to tighten nut4, from location1, bob picks up spanner2, at location5, bob uses spanner2 to tighten nut5, at location3, bob uses spanner2 to tighten nut3, at location8, bob uses spanner4 to tighten nut1, nut2 is tightened by bob using spanner4 at location1, bob walks to location3 from location7, bob walks from location7 to shed, at location4, bob uses spanner5 to tighten nut2, bob walks to location8 from shed, at gate, bob uses spanner4 to tighten nut5, at location4, bob uses spanner3 to tighten nut5, bob tightens nut1 with spanner3 at shed, nut4 is tightened by bob using spanner3 at shed, nut2 is tightened by bob using spanner2 at location8, bob tightens nut5 with spanner2 at location4, bob tightens nut2 with spanner5 at gate, spanner5 is picked up by bob from location4, bob walks to shed from location6, bob tightens nut3 with spanner1 at location2, from shed to location3, bob walks, at location4, bob uses spanner2 to tighten nut1, bob walks to location6 from location5, bob walks to location8 from location4, from location9, bob picks up spanner5, nut5 is tightened by bob using spanner4 at shed, bob tightens nut3 with spanner1 at gate, bob walks from gate to location1, nut2 is tightened by bob using spanner2 at location3, bob tightens nut1 with spanner1 at location8, bob tightens nut3 with spanner5 at location6, bob tightens nut3 with spanner5 at gate, at location7, bob uses spanner3 to tighten nut2, from location9, bob picks up spanner3, at location9, bob uses spanner2 to tighten nut1, nut1 is tightened by bob using spanner2 at location7, nut3 is tightened by bob using spanner2 at location7, at location8, bob uses spanner5 to tighten nut3, at location2, bob uses spanner5 to tighten nut2, from shed to location1, bob walks, nut2 is tightened by bob using spanner5 at location1, bob tightens nut2 with spanner4 at shed, spanner5 is picked up by bob from location5, nut3 is tightened by bob using spanner4 at location3, bob tightens nut5 with spanner1 at location8, from location3, bob picks up spanner5, nut2 is tightened by bob using spanner2 at location7, at location3, bob uses spanner3 to tighten nut3, nut2 is tightened by bob using spanner4 at location9, bob tightens nut2 with spanner2 at shed, from location3 to location1, bob walks, bob tightens nut4 with spanner5 at location3, bob tightens nut3 with spanner2 at gate, at location2, bob uses spanner1 to tighten nut5, bob walks from location3 to gate, at location4, bob uses spanner4 to tighten nut1, bob walks to location5 from location1, bob walks to location8 from location6, bob tightens nut4 with spanner5 at location2, bob tightens nut1 with spanner5 at location7, bob walks from location7 to location6, bob picks up spanner5 from location6, from location8 to gate, bob walks, from location9 to location2, bob walks, bob tightens nut4 with spanner3 at location4, from shed, bob picks up spanner4, bob tightens nut3 with spanner4 at location6, nut5 is tightened by bob using spanner5 at shed, bob walks to location7 from location4, at location7, bob uses spanner1 to tighten nut2, bob walks to location5 from location7, at gate, bob uses spanner3 to tighten nut2, from location5 to location1, bob walks, at location6, bob uses spanner2 to tighten nut3, bob tightens nut1 with spanner5 at shed, bob tightens nut2 with spanner1 at shed, from location3 to location2, bob walks, nut3 is tightened by bob using spanner4 at location7, bob tightens nut5 with spanner2 at location2, nut1 is tightened by bob using spanner1 at location1, nut1 is tightened by bob using spanner5 at location8, at location1, bob uses spanner5 to tighten nut1, at location9, bob uses spanner4 to tighten nut3, bob tightens nut5 with spanner2 at location8, at location2, bob uses spanner3 to tighten nut3, bob tightens nut3 with spanner5 at location1, at location3, bob uses spanner2 to tighten nut5, nut1 is tightened by bob using spanner3 at location1, bob tightens nut5 with spanner5 at location7, at location5, bob uses spanner2 to tighten nut1, nut2 is tightened by bob using spanner1 at location8, nut4 is tightened by bob using spanner1 at location6, bob tightens nut2 with spanner3 at location1, nut1 is tightened by bob using spanner2 at location3, bob walks from location9 to location4, bob walks to location6 from shed, nut1 is tightened by bob using spanner3 at location6, spanner1 is picked up by bob from location9, nut5 is tightened by bob using spanner1 at location5, from location6 to gate, bob walks, nut1 is tightened by bob using spanner2 at shed, at location2, bob uses spanner2 to tighten nut2, nut2 is tightened by bob using spanner1 at location3, from gate to location2, bob walks, bob tightens nut4 with spanner4 at shed, nut4 is tightened by bob using spanner3 at location3, bob tightens nut1 with spanner1 at location5, bob picks up spanner2 from shed, bob walks from location6 to location5, bob tightens nut1 with spanner4 at location7, nut5 is tightened by bob using spanner3 at location2, nut5 is tightened by bob using spanner4 at location7, bob tightens nut1 with spanner4 at shed, bob tightens nut4 with spanner5 at location6, nut3 is tightened by bob using spanner1 at location1, bob tightens nut4 with spanner1 at location8, bob tightens nut4 with spanner2 at location3, nut1 is tightened by bob using spanner4 at location9, bob walks from location4 to shed, at shed, bob uses spanner1 to tighten nut1, bob tightens nut2 with spanner2 at gate, nut1 is tightened by bob using spanner2 at location8, bob tightens nut4 with spanner2 at location1, nut1 is tightened by bob using spanner1 at location9, nut2 is tightened by bob using spanner4 at location4, bob tightens nut1 with spanner3 at location7, bob walks to location8 from location1, spanner4 is picked up by bob from location3, bob tightens nut4 with spanner4 at location6, bob walks to location7 from location8, at location7, bob uses spanner5 to tighten nut4, from shed to location7, bob walks, bob tightens nut1 with spanner5 at location3, nut5 is tightened by bob using spanner3 at location9, bob tightens nut2 with spanner3 at location6, bob picks up spanner1 from location2, nut2 is tightened by bob using spanner3 at location8, bob walks to location4 from location1, bob tightens nut3 with spanner1 at shed, bob tightens nut3 with spanner5 at location3, nut3 is tightened by bob using spanner5 at location9, nut4 is tightened by bob using spanner5 at location9, at location1, bob uses spanner2 to tighten nut1, bob picks up spanner1 from gate, bob walks from location8 to location3, nut4 is tightened by bob using spanner2 at location2, bob picks up spanner4 from location9, spanner1 is picked up by bob from location4, bob picks up spanner5 from location8, bob tightens nut5 with spanner5 at location2, bob walks to location8 from location9, at location9, bob uses spanner1 to tighten nut4, bob tightens nut1 with spanner1 at location4, at shed, bob uses spanner4 to tighten nut3, spanner4 is picked up by bob from location5, nut4 is tightened by bob using spanner5 at location8, bob tightens nut1 with spanner1 at location2, nut3 is tightened by bob using spanner3 at location8, nut5 is tightened by bob using spanner3 at location1, from location7, bob picks up spanner2, bob tightens nut2 with spanner2 at location1, at location9, bob uses spanner4 to tighten nut4, at location9, bob uses spanner1 to tighten nut2, bob tightens nut4 with spanner2 at location4, bob walks to location6 from gate, bob tightens nut3 with spanner4 at location2, spanner3 is picked up by bob from location6, bob walks to location4 from location8, bob tightens nut2 with spanner3 at location3, bob tightens nut4 with spanner4 at location8, from gate, bob picks up spanner3, nut2 is tightened by bob using spanner2 at location5, bob picks up spanner2 from location8, from location6, bob picks up spanner4, from location6 to location3, bob walks, nut4 is tightened by bob using spanner4 at location7, bob picks up spanner5 from location7, from location1 to shed, bob walks, bob tightens nut2 with spanner5 at location8, from shed to gate, bob walks, bob walks from shed to location9 and from location1 to location6, bob walks", "plan_length": 19, "initial_state_nl": "A link between location2 and location3 exists, a link between location6 and location7 exists, bob is located at shed, location1 and location2 are linked, location3 and location4 are linked, location4 is linked to location5, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 is currently at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 is at location2, spanner3 is usable, spanner4 is currently at location6, spanner4 is functional, spanner5 can be used and spanner5 is at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then proceeds to location2, where he collects spanner3. From location2, Bob heads to location3, where he picks up both spanner5 and spanner1. He then walks to location4 and subsequently to location5, where he collects spanner2. Next, Bob moves to location6, picks up spanner4, and then proceeds to location7, followed by location8, location9, and finally the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, list all inexecutable actions. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, and another connection exists between location6 and location7. Bob is situated at the shed. Location1 is connected to location2, location3 is linked to location4, location4 is connected to location5, location5 is linked to location6, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. Nut1, nut2, nut3, and nut4 are all located at the gate, and each of them is loose. Additionally, nut5 is currently at the gate but is not secure. The shed is connected to location1. Spanner1 is currently situated at location3 and is in working condition. Spanner2 is at location5 and is usable, while spanner3 is at location2 and is also usable. Spanner4 is currently at location6 and is functional. Furthermore, spanner5 is both usable and located at location3."}
{"question_id": "8764027f-c751-4146-b57b-907bf23803a0", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1 to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "bob walks to gate from location9, at location7, bob uses spanner5 to tighten nut3, nut1 is tightened by bob using spanner4 at gate, nut3 is tightened by bob using spanner3 at location4, spanner3 is picked up by bob from location7, at location9, bob uses spanner2 to tighten nut4, bob tightens nut4 with spanner2 at location7, nut1 is tightened by bob using spanner4 at location1, from location7 to location1, bob walks, nut4 is tightened by bob using spanner1 at location5, bob walks from location6 to location2, nut3 is tightened by bob using spanner2 at location2, bob tightens nut2 with spanner5 at location9, bob walks to location8 from location1, nut4 is tightened by bob using spanner4 at location4, at location4, bob uses spanner2 to tighten nut1, from location4 to gate, bob walks, bob tightens nut3 with spanner4 at location7, at location5, bob uses spanner4 to tighten nut1, nut4 is tightened by bob using spanner4 at location1, nut4 is tightened by bob using spanner3 at location2, nut3 is tightened by bob using spanner4 at gate, at location7, bob uses spanner3 to tighten nut1, nut1 is tightened by bob using spanner2 at gate, bob walks to location3 from location5, bob walks to location8 from location2, bob tightens nut4 with spanner5 at location4, at location5, bob uses spanner1 to tighten nut5, at location2, bob uses spanner1 to tighten nut4, bob tightens nut1 with spanner1 at location9, from location9, bob picks up spanner2, nut3 is tightened by bob using spanner2 at location7, from location5 to location8, bob walks, at location1, bob uses spanner2 to tighten nut3, bob tightens nut3 with spanner3 at location1, nut4 is tightened by bob using spanner4 at gate, at location9, bob uses spanner3 to tighten nut3, bob tightens nut4 with spanner3 at shed, bob picks up spanner5 from location8, at shed, bob uses spanner3 to tighten nut3, nut5 is tightened by bob using spanner3 at gate, bob walks from shed to location8, bob walks from location6 to gate, bob tightens nut5 with spanner4 at location5, bob tightens nut3 with spanner5 at shed, spanner1 is picked up by bob from shed, at location6, bob uses spanner5 to tighten nut4, bob walks from location5 to shed, bob tightens nut5 with spanner3 at location2, nut1 is tightened by bob using spanner1 at location3, nut2 is tightened by bob using spanner2 at shed, nut1 is tightened by bob using spanner1 at shed, nut5 is tightened by bob using spanner1 at shed, bob walks from location7 to location3, from location3 to gate, bob walks, spanner3 is picked up by bob from gate, bob picks up spanner1 from location8, bob tightens nut5 with spanner3 at location1, at location3, bob uses spanner4 to tighten nut2, at location5, bob uses spanner3 to tighten nut2, at gate, bob uses spanner5 to tighten nut5, bob walks to location9 from shed, spanner3 is picked up by bob from location9, bob tightens nut4 with spanner2 at shed, at shed, bob uses spanner4 to tighten nut5, bob walks from location1 to location9, from location7, bob picks up spanner5, from location3 to location8, bob walks, bob tightens nut4 with spanner5 at location5, from location1 to shed, bob walks, bob tightens nut2 with spanner3 at location3, bob walks to location9 from location7, nut4 is tightened by bob using spanner1 at gate, bob tightens nut3 with spanner5 at gate, at location6, bob uses spanner5 to tighten nut2, bob tightens nut5 with spanner4 at location4, bob walks to location1 from location6, at shed, bob uses spanner4 to tighten nut3, from location9 to location1, bob walks, bob walks from location9 to shed, from shed to location5, bob walks, at location6, bob uses spanner5 to tighten nut1, bob tightens nut1 with spanner1 at location8, bob tightens nut2 with spanner5 at location2, at location3, bob uses spanner3 to tighten nut4, at location3, bob uses spanner5 to tighten nut4, bob tightens nut1 with spanner2 at location7, at location9, bob uses spanner4 to tighten nut2, at location4, bob uses spanner3 to tighten nut5, bob tightens nut1 with spanner3 at location5, bob tightens nut2 with spanner3 at location1, bob tightens nut2 with spanner1 at shed, bob tightens nut4 with spanner5 at location8, nut2 is tightened by bob using spanner2 at location3, at location2, bob uses spanner2 to tighten nut5, nut3 is tightened by bob using spanner3 at location5, bob walks from location3 to location4, bob tightens nut2 with spanner2 at location5, bob walks to location9 from gate, from location6 to location8, bob walks, bob tightens nut4 with spanner2 at location2, bob tightens nut1 with spanner2 at location8, from location8 to location7, bob walks, bob tightens nut4 with spanner1 at location7, bob picks up spanner2 from shed, spanner4 is picked up by bob from location2, at location6, bob uses spanner4 to tighten nut2, spanner4 is picked up by bob from location5, nut5 is tightened by bob using spanner5 at location2, nut1 is tightened by bob using spanner2 at location2, spanner4 is picked up by bob from location8, from location5, bob picks up spanner5, bob tightens nut3 with spanner1 at location4, at location1, bob uses spanner5 to tighten nut1, nut3 is tightened by bob using spanner1 at location2, bob tightens nut5 with spanner5 at location8, from gate to location4, bob walks, from shed to location6, bob walks, bob walks to location3 from shed, bob walks from location7 to gate, spanner3 is picked up by bob from location6, bob picks up spanner5 from location2, bob tightens nut2 with spanner1 at location3, bob walks from location1 to location4, bob picks up spanner5 from location4, from location8 to location4, bob walks, spanner5 is picked up by bob from shed, at location2, bob uses spanner3 to tighten nut3, at location8, bob uses spanner3 to tighten nut2, at location3, bob uses spanner4 to tighten nut5, bob tightens nut1 with spanner2 at location1, nut3 is tightened by bob using spanner2 at location4, at location6, bob uses spanner5 to tighten nut3, nut5 is tightened by bob using spanner1 at location9, bob tightens nut5 with spanner1 at location8, nut2 is tightened by bob using spanner2 at location4, from location3, bob picks up spanner2, bob tightens nut3 with spanner3 at location8, bob walks to location1 from location4, bob tightens nut2 with spanner3 at location4, bob walks from location8 to location9, bob walks from location7 to location8, from shed to location7, bob walks, bob walks to location2 from location7, bob tightens nut5 with spanner1 at location4, spanner3 is picked up by bob from location5, from location3, bob picks up spanner1, at location6, bob uses spanner3 to tighten nut3, bob picks up spanner1 from location1, bob tightens nut5 with spanner2 at shed, nut4 is tightened by bob using spanner2 at location4, nut3 is tightened by bob using spanner2 at location6, from gate, bob picks up spanner4, nut3 is tightened by bob using spanner5 at location1, at location9, bob uses spanner3 to tighten nut1, bob tightens nut3 with spanner2 at location5, bob tightens nut3 with spanner4 at location1, nut1 is tightened by bob using spanner2 at location5, bob walks to location6 from location8, spanner3 is picked up by bob from location4, at shed, bob uses spanner5 to tighten nut5, from gate to location7, bob walks, bob walks from gate to location3, at location9, bob uses spanner4 to tighten nut5, nut1 is tightened by bob using spanner1 at location5, bob tightens nut1 with spanner2 at location3, bob tightens nut1 with spanner1 at location6, bob walks to location3 from location1, bob walks to location3 from location2, nut2 is tightened by bob using spanner1 at location9, at location3, bob uses spanner5 to tighten nut1, nut5 is tightened by bob using spanner5 at location9, from location6, bob picks up spanner4, at location7, bob uses spanner5 to tighten nut1, from location7 to location6, bob walks, bob tightens nut4 with spanner2 at location5, nut2 is tightened by bob using spanner4 at location2, bob walks from gate to shed, at location7, bob uses spanner5 to tighten nut5, bob walks to location7 from location4, nut1 is tightened by bob using spanner3 at shed, from location6, bob picks up spanner1, at location6, bob uses spanner1 to tighten nut5, at location9, bob uses spanner5 to tighten nut4, bob walks from location1 to location7, from location2 to location7, bob walks, bob tightens nut2 with spanner5 at location8, bob walks from location9 to location2, bob tightens nut1 with spanner3 at location2, bob walks from location2 to location9, bob tightens nut5 with spanner5 at location3, bob picks up spanner5 from location6, nut5 is tightened by bob using spanner5 at location6, at location4, bob uses spanner4 to tighten nut2, nut2 is tightened by bob using spanner2 at location8, nut4 is tightened by bob using spanner2 at location3, bob tightens nut4 with spanner2 at location8, nut1 is tightened by bob using spanner1 at location7, bob tightens nut1 with spanner4 at location4, at location7, bob uses spanner2 to tighten nut2, nut3 is tightened by bob using spanner5 at location9, bob tightens nut5 with spanner4 at location7, nut5 is tightened by bob using spanner1 at location3, bob tightens nut1 with spanner5 at location9, bob tightens nut3 with spanner1 at location6, nut5 is tightened by bob using spanner4 at gate, from location3 to location2, bob walks, at location6, bob uses spanner4 to tighten nut4, bob walks from location7 to location4, nut1 is tightened by bob using spanner5 at shed, from location6 to location7, bob walks, from location9 to location4, bob walks, nut2 is tightened by bob using spanner3 at location9, from location4 to location2, bob walks, from shed to location2, bob walks, from location9 to location8, bob walks, bob picks up spanner3 from shed, spanner1 is picked up by bob from location9, from location9 to location7, bob walks, at location3, bob uses spanner4 to tighten nut3, bob walks from location5 to location1, spanner5 is picked up by bob from location9, at location7, bob uses spanner3 to tighten nut4, bob walks from location6 to location5, nut5 is tightened by bob using spanner1 at location7, nut1 is tightened by bob using spanner2 at location6, bob tightens nut1 with spanner1 at location1, at location2, bob uses spanner5 to tighten nut1, at location5, bob uses spanner4 to tighten nut2, nut5 is tightened by bob using spanner3 at shed, bob walks to location1 from location8, from location2 to gate, bob walks, bob picks up spanner2 from location1, nut4 is tightened by bob using spanner3 at location8, bob picks up spanner3 from location1, from location2 to shed, bob walks, nut4 is tightened by bob using spanner1 at location8, spanner5 is picked up by bob from location3, bob walks to location2 from location8, bob tightens nut3 with spanner1 at location1, at location3, bob uses spanner1 to tighten nut3, nut4 is tightened by bob using spanner5 at gate, bob tightens nut2 with spanner2 at location2, nut5 is tightened by bob using spanner3 at location6, bob walks from location9 to location3, from location3 to location7, bob walks, at location3, bob uses spanner5 to tighten nut3, nut5 is tightened by bob using spanner2 at location6, at gate, bob uses spanner3 to tighten nut4, at location8, bob uses spanner2 to tighten nut5, nut5 is tightened by bob using spanner4 at location8, from location4 to location3, bob walks, bob walks to location7 from location5, at location8, bob uses spanner4 to tighten nut3, bob tightens nut4 with spanner1 at location4, at location6, bob uses spanner4 to tighten nut3, nut4 is tightened by bob using spanner3 at location5, bob walks to location6 from location2, spanner1 is picked up by bob from location2, bob tightens nut2 with spanner1 at location5, bob tightens nut2 with spanner1 at location4, bob walks to location4 from shed, bob walks from location4 to location9, at location5, bob uses spanner5 to tighten nut5, at location9, bob uses spanner4 to tighten nut4, bob walks to location9 from location6, at location4, bob uses spanner5 to tighten nut2, at location8, bob uses spanner3 to tighten nut5, nut1 is tightened by bob using spanner2 at location9, spanner5 is picked up by bob from gate, bob walks from location3 to location6, bob tightens nut2 with spanner2 at location1, nut3 is tightened by bob using spanner1 at shed, at location4, bob uses spanner5 to tighten nut1, at location6, bob uses spanner3 to tighten nut2, bob tightens nut2 with spanner2 at gate, from location1 to location6, bob walks, nut5 is tightened by bob using spanner2 at gate, nut4 is tightened by bob using spanner5 at location7, spanner2 is picked up by bob from location7, bob picks up spanner1 from location4, at location5, bob uses spanner5 to tighten nut3, spanner2 is picked up by bob from location8, at location3, bob uses spanner3 to tighten nut3, nut2 is tightened by bob using spanner3 at location7, nut3 is tightened by bob using spanner1 at location9, nut5 is tightened by bob using spanner1 at location2, from location5 to location6, bob walks, at location6, bob uses spanner2 to tighten nut4, nut5 is tightened by bob using spanner2 at location3, bob tightens nut1 with spanner3 at location1, bob tightens nut5 with spanner2 at location1, at location5, bob uses spanner1 to tighten nut3, bob walks from gate to location5, at location3, bob uses spanner2 to tighten nut3, nut2 is tightened by bob using spanner3 at shed, from location7, bob picks up spanner1, bob tightens nut3 with spanner4 at location2, bob tightens nut3 with spanner2 at shed, bob tightens nut2 with spanner1 at location8, nut2 is tightened by bob using spanner5 at location7, at location2, bob uses spanner4 to tighten nut5, at location8, bob uses spanner3 to tighten nut1, bob tightens nut5 with spanner5 at location4, nut4 is tightened by bob using spanner3 at location1, nut1 is tightened by bob using spanner1 at location2, at location8, bob uses spanner5 to tighten nut3, nut1 is tightened by bob using spanner3 at location3, nut5 is tightened by bob using spanner2 at location5, from location3 to shed, bob walks, from location1 to gate, bob walks, from location2 to location4, bob walks, from location4 to location6, bob walks, at gate, bob uses spanner1 to tighten nut1, at shed, bob uses spanner1 to tighten nut4, bob walks from location9 to location5, at location5, bob uses spanner5 to tighten nut2, at shed, bob uses spanner5 to tighten nut4, bob walks from gate to location6, at location5, bob uses spanner3 to tighten nut5, at location6, bob uses spanner2 to tighten nut2, nut1 is tightened by bob using spanner4 at location6, bob walks to location9 from location5, bob tightens nut1 with spanner5 at gate, bob walks from gate to location8, spanner4 is picked up by bob from shed, bob tightens nut4 with spanner4 at location3, nut5 is tightened by bob using spanner4 at location1, bob picks up spanner2 from location2, bob walks from location9 to location6, from location8 to location5, bob walks, bob tightens nut1 with spanner4 at location8, nut3 is tightened by bob using spanner1 at location8, bob walks to location5 from location3, bob tightens nut3 with spanner4 at location5, bob tightens nut1 with spanner4 at location3, bob picks up spanner4 from location9, bob walks to location3 from location6, bob picks up spanner3 from location2, bob tightens nut2 with spanner1 at location1, at gate, bob uses spanner3 to tighten nut1, at location4, bob uses spanner3 to tighten nut1, at gate, bob uses spanner1 to tighten nut3, nut1 is tightened by bob using spanner5 at location5, bob walks to location1 from location3, nut3 is tightened by bob using spanner5 at location2, at shed, bob uses spanner4 to tighten nut4, at location6, bob uses spanner1 to tighten nut2, nut2 is tightened by bob using spanner1 at location7, at gate, bob uses spanner5 to tighten nut2, bob walks to location5 from location4, from location7, bob picks up spanner4, nut1 is tightened by bob using spanner4 at shed, at location3, bob uses spanner3 to tighten nut5, nut3 is tightened by bob using spanner4 at location4, from gate to location1, bob walks, at location1, bob uses spanner5 to tighten nut5, bob tightens nut1 with spanner3 at location6, from shed to gate, bob walks, at location6, bob uses spanner4 to tighten nut5, at gate, bob uses spanner1 to tighten nut5, bob tightens nut2 with spanner2 at location9, at location9, bob uses spanner4 to tighten nut3, at location8, bob uses spanner4 to tighten nut2, bob walks from location8 to location3, bob tightens nut4 with spanner4 at location2, bob walks from location7 to location5, bob tightens nut1 with spanner4 at location9, nut3 is tightened by bob using spanner3 at gate, from gate, bob picks up spanner2, bob tightens nut1 with spanner4 at location2, nut2 is tightened by bob using spanner5 at location1, bob walks to location1 from shed, from location3, bob picks up spanner4, nut2 is tightened by bob using spanner4 at location1, bob walks from location6 to location4, from location4, bob picks up spanner2, nut4 is tightened by bob using spanner5 at location2, bob walks to gate from location5, bob picks up spanner3 from location3, at shed, bob uses spanner2 to tighten nut1, bob picks up spanner2 from location5, bob walks to shed from location8, at location7, bob uses spanner4 to tighten nut1, from location5 to location4, bob walks, at location7, bob uses spanner4 to tighten nut2, nut3 is tightened by bob using spanner5 at location4, nut2 is tightened by bob using spanner5 at shed, nut5 is tightened by bob using spanner2 at location7, bob walks to shed from location6, at location2, bob uses spanner1 to tighten nut2, nut3 is tightened by bob using spanner1 at location7, bob tightens nut4 with spanner4 at location8, nut3 is tightened by bob using spanner2 at gate, bob tightens nut2 with spanner3 at gate, nut5 is tightened by bob using spanner1 at location1, bob tightens nut5 with spanner2 at location9, from gate to location2, bob walks, nut2 is tightened by bob using spanner1 at gate, bob tightens nut4 with spanner4 at location7, nut4 is tightened by bob using spanner3 at location4, bob walks from location2 to location1, bob tightens nut2 with spanner4 at shed, spanner1 is picked up by bob from gate, bob walks from location7 to shed, bob tightens nut4 with spanner1 at location6, bob tightens nut3 with spanner3 at location7, from location8, bob picks up spanner3, bob walks from location2 to location5, bob tightens nut1 with spanner5 at location8, at location9, bob uses spanner3 to tighten nut4, nut3 is tightened by bob using spanner2 at location9, nut5 is tightened by bob using spanner3 at location7, at location4, bob uses spanner1 to tighten nut1, from location6, bob picks up spanner2, nut3 is tightened by bob using spanner2 at location8, spanner1 is picked up by bob from location5, from location4 to location8, bob walks, bob tightens nut5 with spanner3 at location9, bob walks from location4 to shed, nut4 is tightened by bob using spanner1 at location9, at location3, bob uses spanner5 to tighten nut2, at location4, bob uses spanner2 to tighten nut5, at location6, bob uses spanner3 to tighten nut4, from location1 to location5, bob walks, at gate, bob uses spanner2 to tighten nut4, from location5 to location2, bob walks, bob tightens nut4 with spanner4 at location5, from location3 to location9, bob walks, nut4 is tightened by bob using spanner1 at location3, from location8 to gate, bob walks, at location1, bob uses spanner1 to tighten nut4, bob tightens nut4 with spanner2 at location1, nut4 is tightened by bob using spanner5 at location1, from location4, bob picks up spanner4, at gate, bob uses spanner4 to tighten nut2 and nut2 is tightened by bob using spanner3 at location2", "plan_length": 1, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent steps taken are as follows: Bob moves from the shed to location1 to attain the current state. In this state, identify all actions that cannot be executed. If there are no such actions, indicate None.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and another connection exists between location6 and location7. Bob is currently situated at the shed. Additionally, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location7 is linked to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is unsecured, nut2, which is loose, nut3, which is also loose, nut4, which is loose, and nut5, which is loose as well. The shed is connected to location1. The following spanners are located at their respective locations: spanner1, which is functional, is at location4, spanner2, which is functional, is at location7, spanner3, which can be used, is at location6, spanner4, which can be used, is at location1, and spanner5, which is functional, is also at location1."}
{"question_id": "17330d83-4937-424e-8d7c-377d6dcafe0d", "domain_name": "spanner", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: at gate, bob uses spanner5 to tighten nut5 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "at gate, bob uses spanner5 to tighten nut5", "plan_length": 1, "initial_state_nl": "A link between location2 and location3 exists, a link between location6 and location7 exists, bob is located at shed, location1 and location2 are linked, location3 and location4 are linked, location4 is linked to location5, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, location9 and gate are linked, nut1 is located at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is loose, nut4 is at gate, nut4 is loose, nut5 is currently at gate, nut5 is not secured, shed is linked to location1, spanner1 is currently at location3, spanner1 is functional, spanner2 is at location5, spanner2 is usable, spanner3 is at location2, spanner3 is usable, spanner4 is currently at location6, spanner4 is functional, spanner5 can be used and spanner5 is at location3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent actions taken are as follows: at the gate, Bob utilizes spanner5 to secure nut5, thereby achieving the current state. What is the first action in the sequence that cannot be executed? If none exist, write None.", "initial_state_nl_paraphrased": "There is a connection between location2 and location3, and another connection exists between location6 and location7. Bob is situated at the shed. Location1 is connected to location2, location3 is linked to location4, location4 is connected to location5, location5 is linked to location6, location7 is connected to location8, location8 is linked to location9, and location9 is connected to the gate. At the gate, nut1 is present and loose, nut2 is also at the gate and loose, nut3 is located at the gate and loose, nut4 is at the gate and loose, and nut5 is currently at the gate but not secured. The shed is connected to location1. Spanner1 is currently situated at location3 and is in working condition, spanner2 is at location5 and usable, spanner3 is at location2 and usable, spanner4 is currently at location6 and functional, and spanner5 is at location3 and can be used."}
{"question_id": "008c3e56-93a4-41d1-975e-5ea6bc1d4761", "domain_name": "spanner", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks from shed to location1, from location1, bob picks up spanner5, from location1, bob picks up spanner4, from location1 to location2, bob walks, bob walks to location3 from location2, bob walks to location4 from location3, spanner1 is picked up by bob from location4, bob walks to location5 from location4, from location5 to location6, bob walks and spanner3 is picked up by bob from location6 to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "from location9 to gate, bob walks, at location7, bob uses spanner5 to tighten nut3, at gate, bob uses spanner4 to tighten nut1, nut3 is tightened by bob using spanner3 at location4, spanner3 is picked up by bob from location7, nut4 is tightened by bob using spanner2 at location9, at location7, bob uses spanner2 to tighten nut4, nut1 is tightened by bob using spanner4 at location1, from location7 to location1, bob walks, nut4 is tightened by bob using spanner1 at location5, from location6 to location2, bob walks, nut3 is tightened by bob using spanner2 at location2, bob tightens nut2 with spanner5 at location9, from location1 to location8, bob walks, bob tightens nut4 with spanner4 at location4, bob tightens nut1 with spanner2 at location4, bob walks to location2 from location1, bob walks to gate from location4, at location7, bob uses spanner4 to tighten nut3, at location5, bob uses spanner4 to tighten nut1, nut4 is tightened by bob using spanner4 at location1, at location2, bob uses spanner3 to tighten nut4, bob tightens nut3 with spanner4 at gate, bob tightens nut1 with spanner3 at location7, nut1 is tightened by bob using spanner2 at gate, from location5 to location3, bob walks, bob walks to location8 from location2, nut4 is tightened by bob using spanner5 at location4, at location5, bob uses spanner1 to tighten nut5, at location2, bob uses spanner1 to tighten nut4, nut1 is tightened by bob using spanner1 at location9, bob picks up spanner2 from location9, at location7, bob uses spanner2 to tighten nut3, bob walks to location8 from location5, bob tightens nut3 with spanner2 at location1, nut3 is tightened by bob using spanner3 at location1, nut4 is tightened by bob using spanner4 at gate, bob tightens nut3 with spanner3 at location9, nut4 is tightened by bob using spanner3 at shed, bob picks up spanner5 from location8, nut3 is tightened by bob using spanner3 at shed, bob tightens nut5 with spanner3 at gate, bob walks from shed to location8, from location6 to gate, bob walks, nut5 is tightened by bob using spanner4 at location5, nut3 is tightened by bob using spanner5 at shed, bob picks up spanner1 from shed, nut4 is tightened by bob using spanner5 at location6, spanner5 is picked up by bob from location1, bob walks from location5 to shed, nut5 is tightened by bob using spanner3 at location2, nut1 is tightened by bob using spanner1 at location3, bob tightens nut2 with spanner2 at shed, bob tightens nut1 with spanner1 at shed, bob tightens nut5 with spanner1 at shed, bob walks to location3 from location7, bob walks from location3 to gate, from gate, bob picks up spanner3, bob picks up spanner1 from location8, bob tightens nut5 with spanner3 at location1, bob tightens nut2 with spanner4 at location3, bob tightens nut2 with spanner3 at location5, nut5 is tightened by bob using spanner5 at gate, bob walks from shed to location9, spanner3 is picked up by bob from location9, at shed, bob uses spanner2 to tighten nut4, nut5 is tightened by bob using spanner4 at shed, bob walks from location1 to location9, bob picks up spanner5 from location7, from location3 to location8, bob walks, bob tightens nut4 with spanner5 at location5, bob walks from location1 to shed, at location3, bob uses spanner3 to tighten nut2, from location7 to location9, bob walks, bob tightens nut4 with spanner1 at gate, at gate, bob uses spanner5 to tighten nut3, nut2 is tightened by bob using spanner5 at location6, nut5 is tightened by bob using spanner4 at location4, bob walks from location6 to location1, at shed, bob uses spanner4 to tighten nut3, bob walks to location1 from location9, bob walks to shed from location9, bob walks to location5 from shed, bob tightens nut1 with spanner5 at location6, nut1 is tightened by bob using spanner1 at location8, bob tightens nut2 with spanner5 at location2, at location3, bob uses spanner3 to tighten nut4, at location3, bob uses spanner5 to tighten nut4, at location7, bob uses spanner2 to tighten nut1, at location9, bob uses spanner4 to tighten nut2, nut5 is tightened by bob using spanner3 at location4, bob tightens nut1 with spanner3 at location5, at location1, bob uses spanner3 to tighten nut2, nut2 is tightened by bob using spanner1 at shed, bob tightens nut4 with spanner5 at location8, bob tightens nut2 with spanner2 at location3, at location2, bob uses spanner2 to tighten nut5, bob tightens nut3 with spanner3 at location5, bob walks from location3 to location4, at location5, bob uses spanner2 to tighten nut2, from gate to location9, bob walks, from location6 to location8, bob walks, bob tightens nut4 with spanner2 at location2, nut1 is tightened by bob using spanner2 at location8, from location8 to location7, bob walks, at location7, bob uses spanner1 to tighten nut4, spanner2 is picked up by bob from shed, spanner4 is picked up by bob from location2, nut2 is tightened by bob using spanner4 at location6, from location5, bob picks up spanner4, bob tightens nut5 with spanner5 at location2, bob tightens nut1 with spanner2 at location2, bob picks up spanner4 from location8, spanner5 is picked up by bob from location5, at location4, bob uses spanner1 to tighten nut3, nut1 is tightened by bob using spanner5 at location1, nut3 is tightened by bob using spanner1 at location2, at location8, bob uses spanner5 to tighten nut5, bob walks to location4 from gate, bob walks from shed to location6, bob walks to location3 from shed, from location7 to gate, bob walks, from location6, bob picks up spanner3, spanner5 is picked up by bob from location2, bob tightens nut2 with spanner1 at location3, from location1 to location4, bob walks, spanner5 is picked up by bob from location4, bob walks to location4 from location8, spanner5 is picked up by bob from shed, nut3 is tightened by bob using spanner3 at location2, bob tightens nut2 with spanner3 at location8, at location3, bob uses spanner4 to tighten nut5, bob tightens nut1 with spanner2 at location1, bob tightens nut3 with spanner2 at location4, at location6, bob uses spanner5 to tighten nut3, bob tightens nut5 with spanner1 at location9, at location8, bob uses spanner1 to tighten nut5, nut2 is tightened by bob using spanner2 at location4, bob picks up spanner2 from location3, nut3 is tightened by bob using spanner3 at location8, bob walks from location4 to location1, at location4, bob uses spanner3 to tighten nut2, bob walks from location8 to location9, bob walks to location8 from location7, bob walks to location7 from shed, bob walks to location2 from location7, nut5 is tightened by bob using spanner1 at location4, bob picks up spanner3 from location5, from location3, bob picks up spanner1, at location6, bob uses spanner3 to tighten nut3, from location1, bob picks up spanner1, at shed, bob uses spanner2 to tighten nut5, nut4 is tightened by bob using spanner2 at location4, nut3 is tightened by bob using spanner2 at location6, spanner4 is picked up by bob from gate, at location1, bob uses spanner5 to tighten nut3, at location9, bob uses spanner3 to tighten nut1, bob tightens nut3 with spanner2 at location5, nut3 is tightened by bob using spanner4 at location1, at location5, bob uses spanner2 to tighten nut1, bob walks to location6 from location8, from location4, bob picks up spanner3, at shed, bob uses spanner5 to tighten nut5, bob walks to location7 from gate, bob walks from gate to location3, bob tightens nut5 with spanner4 at location9, nut1 is tightened by bob using spanner1 at location5, at location3, bob uses spanner2 to tighten nut1, at location6, bob uses spanner1 to tighten nut1, bob walks from location1 to location3, from location2 to location3, bob walks, nut2 is tightened by bob using spanner1 at location9, bob tightens nut1 with spanner5 at location3, bob tightens nut5 with spanner5 at location9, bob picks up spanner4 from location6, nut1 is tightened by bob using spanner5 at location7, bob walks from location7 to location6, nut4 is tightened by bob using spanner2 at location5, bob tightens nut2 with spanner4 at location2, bob walks from gate to shed, at location7, bob uses spanner5 to tighten nut5, bob walks from location4 to location7, nut1 is tightened by bob using spanner3 at shed, from location6, bob picks up spanner1, nut5 is tightened by bob using spanner1 at location6, at location9, bob uses spanner5 to tighten nut4, bob walks to location7 from location1, bob walks from location2 to location7, bob tightens nut2 with spanner5 at location8, bob walks from location9 to location2, bob tightens nut1 with spanner3 at location2, from location2 to location9, bob walks, at location3, bob uses spanner5 to tighten nut5, from location6, bob picks up spanner5, nut5 is tightened by bob using spanner5 at location6, bob tightens nut2 with spanner4 at location4, bob tightens nut2 with spanner2 at location8, bob tightens nut4 with spanner2 at location3, at location8, bob uses spanner2 to tighten nut4, at location7, bob uses spanner1 to tighten nut1, at location4, bob uses spanner4 to tighten nut1, bob tightens nut2 with spanner2 at location7, nut3 is tightened by bob using spanner5 at location9, bob tightens nut5 with spanner4 at location7, bob tightens nut5 with spanner1 at location3, at location9, bob uses spanner5 to tighten nut1, bob tightens nut3 with spanner1 at location6, bob tightens nut5 with spanner4 at gate, bob walks from location3 to location2, at location6, bob uses spanner4 to tighten nut4, from location7 to location4, bob walks, bob tightens nut1 with spanner5 at shed, from location9 to location4, bob walks, nut2 is tightened by bob using spanner3 at location9, bob walks from location4 to location2, bob walks from shed to location2, from location9 to location8, bob walks, bob picks up spanner3 from shed, spanner1 is picked up by bob from location9, bob walks to location7 from location9, at location3, bob uses spanner4 to tighten nut3, from location5 to location1, bob walks, spanner5 is picked up by bob from location9, bob tightens nut4 with spanner3 at location7, bob walks to location5 from location6, bob tightens nut5 with spanner1 at location7, bob tightens nut1 with spanner2 at location6, nut1 is tightened by bob using spanner1 at location1, bob tightens nut1 with spanner5 at location2, nut2 is tightened by bob using spanner4 at location5, nut5 is tightened by bob using spanner3 at shed, bob walks from location8 to location1, bob walks to gate from location2, bob picks up spanner2 from location1, nut4 is tightened by bob using spanner3 at location8, spanner3 is picked up by bob from location1, bob walks to shed from location2, from location1, bob picks up spanner4, bob tightens nut4 with spanner1 at location8, spanner5 is picked up by bob from location3, from location8 to location2, bob walks, at location1, bob uses spanner1 to tighten nut3, nut3 is tightened by bob using spanner1 at location3, at gate, bob uses spanner5 to tighten nut4, at location2, bob uses spanner2 to tighten nut2, at location6, bob uses spanner3 to tighten nut5, bob walks to location3 from location9, bob walks to location7 from location3, bob tightens nut3 with spanner5 at location3, at location6, bob uses spanner2 to tighten nut5, bob tightens nut4 with spanner3 at gate, bob tightens nut5 with spanner2 at location8, nut5 is tightened by bob using spanner4 at location8, bob walks to location3 from location4, bob walks from location5 to location7, bob tightens nut3 with spanner4 at location8, at location4, bob uses spanner1 to tighten nut4, at location6, bob uses spanner4 to tighten nut3, nut4 is tightened by bob using spanner3 at location5, bob walks to location6 from location2, spanner1 is picked up by bob from location2, at location5, bob uses spanner1 to tighten nut2, at location4, bob uses spanner1 to tighten nut2, from shed to location4, bob walks, bob walks to location9 from location4, nut5 is tightened by bob using spanner5 at location5, at location9, bob uses spanner4 to tighten nut4, bob walks from location6 to location9, bob tightens nut2 with spanner5 at location4, nut5 is tightened by bob using spanner3 at location8, at location9, bob uses spanner2 to tighten nut1, bob picks up spanner5 from gate, from location3 to location6, bob walks, bob tightens nut2 with spanner2 at location1, at shed, bob uses spanner1 to tighten nut3, nut1 is tightened by bob using spanner5 at location4, at location6, bob uses spanner3 to tighten nut2, at gate, bob uses spanner2 to tighten nut2, from location1 to location6, bob walks, nut5 is tightened by bob using spanner2 at gate, at location7, bob uses spanner5 to tighten nut4, bob picks up spanner2 from location7, bob picks up spanner1 from location4, nut3 is tightened by bob using spanner5 at location5, from location8, bob picks up spanner2, at location3, bob uses spanner3 to tighten nut3, at location7, bob uses spanner3 to tighten nut2, at location9, bob uses spanner1 to tighten nut3, at location2, bob uses spanner1 to tighten nut5, bob walks to location6 from location5, nut4 is tightened by bob using spanner2 at location6, at location3, bob uses spanner2 to tighten nut5, bob tightens nut1 with spanner3 at location1, nut5 is tightened by bob using spanner2 at location1, bob tightens nut3 with spanner1 at location5, from gate to location5, bob walks, at location3, bob uses spanner2 to tighten nut3, bob tightens nut2 with spanner3 at shed, from location7, bob picks up spanner1, bob tightens nut3 with spanner4 at location2, nut3 is tightened by bob using spanner2 at shed, nut2 is tightened by bob using spanner1 at location8, bob tightens nut2 with spanner5 at location7, nut5 is tightened by bob using spanner4 at location2, nut1 is tightened by bob using spanner3 at location8, nut5 is tightened by bob using spanner5 at location4, at location1, bob uses spanner3 to tighten nut4, nut1 is tightened by bob using spanner1 at location2, at location8, bob uses spanner5 to tighten nut3, nut1 is tightened by bob using spanner3 at location3, nut5 is tightened by bob using spanner2 at location5, from location3 to shed, bob walks, bob walks to gate from location1, bob walks to location4 from location2, bob walks from location4 to location6, nut1 is tightened by bob using spanner1 at gate, nut4 is tightened by bob using spanner1 at shed, bob walks to location5 from location9, bob tightens nut2 with spanner5 at location5, at shed, bob uses spanner5 to tighten nut4, bob walks to location6 from gate, nut5 is tightened by bob using spanner3 at location5, bob tightens nut2 with spanner2 at location6, nut1 is tightened by bob using spanner4 at location6, bob walks to location9 from location5, bob tightens nut1 with spanner5 at gate, bob walks from gate to location8, bob picks up spanner4 from shed, nut4 is tightened by bob using spanner4 at location3, nut5 is tightened by bob using spanner4 at location1, from location2, bob picks up spanner2, bob walks from location9 to location6, bob walks from location8 to location5, at location8, bob uses spanner4 to tighten nut1, nut3 is tightened by bob using spanner1 at location8, bob walks from location3 to location5, nut3 is tightened by bob using spanner4 at location5, nut1 is tightened by bob using spanner4 at location3, from location9, bob picks up spanner4, bob walks to location3 from location6, from location2, bob picks up spanner3, bob tightens nut2 with spanner1 at location1, nut1 is tightened by bob using spanner3 at gate, bob tightens nut1 with spanner3 at location4, bob tightens nut3 with spanner1 at gate, bob tightens nut1 with spanner5 at location5, from location3 to location1, bob walks, at location2, bob uses spanner5 to tighten nut3, at shed, bob uses spanner4 to tighten nut4, at location6, bob uses spanner1 to tighten nut2, at location7, bob uses spanner1 to tighten nut2, bob tightens nut2 with spanner5 at gate, bob walks to location5 from location4, spanner4 is picked up by bob from location7, nut1 is tightened by bob using spanner4 at shed, at location3, bob uses spanner3 to tighten nut5, at location4, bob uses spanner4 to tighten nut3, bob walks to location1 from gate, nut5 is tightened by bob using spanner5 at location1, at location6, bob uses spanner3 to tighten nut1, bob walks from shed to gate, at location6, bob uses spanner4 to tighten nut5, nut5 is tightened by bob using spanner1 at gate, nut2 is tightened by bob using spanner2 at location9, nut3 is tightened by bob using spanner4 at location9, nut2 is tightened by bob using spanner4 at location8, from location8 to location3, bob walks, nut4 is tightened by bob using spanner4 at location2, from location7 to location5, bob walks, at location9, bob uses spanner4 to tighten nut1, nut3 is tightened by bob using spanner3 at gate, bob picks up spanner2 from gate, at location2, bob uses spanner4 to tighten nut1, at location1, bob uses spanner5 to tighten nut2, bob walks from shed to location1, bob picks up spanner4 from location3, at location1, bob uses spanner4 to tighten nut2, bob walks from location6 to location4, from location4, bob picks up spanner2, nut4 is tightened by bob using spanner5 at location2, bob walks to gate from location5, spanner3 is picked up by bob from location3, nut1 is tightened by bob using spanner2 at shed, from location5, bob picks up spanner2, bob walks from location8 to shed, nut1 is tightened by bob using spanner4 at location7, bob walks to location4 from location5, at location7, bob uses spanner4 to tighten nut2, bob tightens nut3 with spanner5 at location4, bob tightens nut2 with spanner5 at shed, at location7, bob uses spanner2 to tighten nut5, from location6 to shed, bob walks, nut2 is tightened by bob using spanner1 at location2, at location7, bob uses spanner1 to tighten nut3, bob tightens nut4 with spanner4 at location8, bob tightens nut3 with spanner2 at gate, nut2 is tightened by bob using spanner3 at gate, bob tightens nut5 with spanner1 at location1, nut5 is tightened by bob using spanner2 at location9, bob walks from gate to location2, at gate, bob uses spanner1 to tighten nut2, nut4 is tightened by bob using spanner4 at location7, bob tightens nut4 with spanner3 at location4, bob walks from location2 to location1, bob tightens nut2 with spanner4 at shed, bob picks up spanner1 from gate, bob walks to shed from location7, bob tightens nut4 with spanner1 at location6, at location7, bob uses spanner3 to tighten nut3, spanner3 is picked up by bob from location8, from location2 to location5, bob walks, at location8, bob uses spanner5 to tighten nut1, bob tightens nut4 with spanner3 at location9, nut3 is tightened by bob using spanner2 at location9, nut5 is tightened by bob using spanner3 at location7, bob tightens nut1 with spanner1 at location4, bob picks up spanner2 from location6, bob tightens nut3 with spanner2 at location8, spanner1 is picked up by bob from location5, from location4 to location8, bob walks, nut5 is tightened by bob using spanner3 at location9, bob walks to shed from location4, nut4 is tightened by bob using spanner1 at location9, at location3, bob uses spanner5 to tighten nut2, nut5 is tightened by bob using spanner2 at location4, bob tightens nut4 with spanner3 at location6, bob walks to location5 from location1, at gate, bob uses spanner2 to tighten nut4, bob walks from location5 to location2, nut4 is tightened by bob using spanner4 at location5, bob walks from location3 to location9, nut4 is tightened by bob using spanner1 at location3, bob walks from location8 to gate, bob tightens nut4 with spanner1 at location1, bob tightens nut4 with spanner2 at location1, at location1, bob uses spanner5 to tighten nut4, spanner4 is picked up by bob from location4, at gate, bob uses spanner4 to tighten nut2 and bob tightens nut2 with spanner3 at location2", "plan_length": 10, "initial_state_nl": "A link between location1 and location2 exists, a link between location5 and location6 exists, a link between location6 and location7 exists, bob is currently at shed, location2 and location3 are linked, location3 is linked to location4, location4 is linked to location5, location7 is linked to location8, location8 and location9 are linked, location9 and gate are linked, nut1 is at gate, nut1 is not secured, nut2 is located at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is located at gate, nut4 is loose, nut5 is at gate, nut5 is loose, shed is linked to location1, spanner1 is currently at location4, spanner1 is functional, spanner2 is functional, spanner2 is located at location7, spanner3 can be used, spanner3 is at location6, spanner4 can be used, spanner4 is currently at location1, spanner5 is functional and spanner5 is located at location1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: Bob moves from the shed to location1, then from location1, he collects spanner5 and spanner4. Next, he walks from location1 to location2, then to location3, and subsequently to location4. At location4, Bob picks up spanner1, proceeds to location5, and then to location6, where he collects spanner3, ultimately reaching the current state. In this state, list all actions that cannot be executed. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location1 and location2, a connection between location5 and location6 exists, and another connection exists between location6 and location7. Bob is presently at the shed. Additionally, location2 is connected to location3, location3 is linked to location4, and location4 is connected to location5. Furthermore, location7 is linked to location8, location8 is connected to location9, and location9 is connected to the gate. At the gate, the following items are located: nut1, which is unsecured, nut2, which is loose, nut3, which is also loose, nut4, which is loose, and nut5, which is loose as well. The shed is connected to location1. Spanner1 is currently at location4 and is in working condition. Spanner2 is functional and located at location7. Spanner3 is usable and situated at location6. Spanner4 is also usable and currently at location1. Lastly, spanner5 is functional and located at location1."}
{"question_id": "346fcdc5-1ba3-486c-8f91-3b36aad38346", "domain_name": "spanner", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: bob walks to location1 from shed, from location1 to location2, bob walks, bob walks from location2 to location3, bob walks to location4 from location3, bob walks from location4 to location5, from location5, bob picks up spanner4, from location5 to location6, bob walks, bob picks up spanner1 from location6, bob walks from location6 to location7, spanner5 is picked up by bob from location7, bob walks to location8 from location7, spanner3 is picked up by bob from location8, spanner2 is picked up by bob from location8, bob walks from location8 to location9, from location9 to gate, bob walks, bob tightens nut1 with spanner5 at gate, nut2 is tightened by bob using spanner4 at gate, bob tightens nut3 with spanner3 at gate and bob tightens nut4 with spanner2 at gate to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "bob tightens nut1 with spanner5 at location5, bob tightens nut5 with spanner1 at location8, at location7, bob uses spanner3 to tighten nut1, from shed, bob picks up spanner1, bob tightens nut5 with spanner4 at gate, bob walks from location5 to location8, from location4 to location3, bob walks, nut1 is tightened by bob using spanner1 at location3, at location7, bob uses spanner2 to tighten nut4, at gate, bob uses spanner2 to tighten nut2, bob walks from location6 to location7, bob tightens nut4 with spanner3 at shed, nut4 is tightened by bob using spanner5 at location1, spanner4 is picked up by bob from location6, bob walks to location3 from location9, bob tightens nut1 with spanner1 at location6, bob picks up spanner4 from location5, nut5 is tightened by bob using spanner5 at location9, bob tightens nut4 with spanner1 at shed, nut5 is tightened by bob using spanner4 at location6, bob walks from location9 to location7, bob tightens nut4 with spanner2 at location5, from location8 to location3, bob walks, at location1, bob uses spanner5 to tighten nut1, bob walks to location3 from location5, at gate, bob uses spanner2 to tighten nut4, bob tightens nut1 with spanner1 at location5, bob tightens nut1 with spanner2 at gate, bob tightens nut3 with spanner2 at location2, bob picks up spanner3 from location2, at location6, bob uses spanner5 to tighten nut4, at location9, bob uses spanner1 to tighten nut3, bob tightens nut2 with spanner5 at location2, at location4, bob uses spanner5 to tighten nut1, nut4 is tightened by bob using spanner4 at location4, nut2 is tightened by bob using spanner4 at location5, nut5 is tightened by bob using spanner1 at location7, at location1, bob uses spanner3 to tighten nut5, at shed, bob uses spanner2 to tighten nut2, bob walks from location3 to location7, nut3 is tightened by bob using spanner2 at location6, bob walks to location2 from location7, bob tightens nut3 with spanner1 at location7, nut4 is tightened by bob using spanner2 at location2, at location6, bob uses spanner4 to tighten nut4, bob tightens nut3 with spanner5 at location9, bob tightens nut5 with spanner3 at location5, from location2 to location7, bob walks, bob walks from shed to location9, bob walks to location2 from location5, spanner3 is picked up by bob from location8, at location1, bob uses spanner5 to tighten nut3, from location8, bob picks up spanner2, nut2 is tightened by bob using spanner5 at shed, bob tightens nut5 with spanner2 at gate, bob tightens nut1 with spanner4 at location9, at location3, bob uses spanner3 to tighten nut2, nut3 is tightened by bob using spanner4 at location4, at location1, bob uses spanner2 to tighten nut1, bob tightens nut3 with spanner1 at location8, bob tightens nut3 with spanner4 at location5, at shed, bob uses spanner5 to tighten nut4, bob picks up spanner2 from gate, from location5 to gate, bob walks, at location6, bob uses spanner3 to tighten nut4, bob walks to gate from location3, bob tightens nut1 with spanner4 at location2, at gate, bob uses spanner4 to tighten nut4, from location1 to location9, bob walks, at location9, bob uses spanner4 to tighten nut4, bob walks to location1 from location9, spanner4 is picked up by bob from gate, spanner4 is picked up by bob from shed, bob walks to location3 from location7, nut5 is tightened by bob using spanner2 at location7, bob walks to location3 from gate, spanner3 is picked up by bob from shed, nut4 is tightened by bob using spanner1 at gate, from location7 to shed, bob walks, from location1 to location2, bob walks, bob tightens nut2 with spanner1 at location2, bob walks to location1 from location2, from location3, bob picks up spanner3, spanner2 is picked up by bob from location1, at location3, bob uses spanner2 to tighten nut5, nut3 is tightened by bob using spanner2 at location5, bob walks to shed from location1, at location7, bob uses spanner1 to tighten nut2, from location6, bob picks up spanner5, at location5, bob uses spanner3 to tighten nut4, nut5 is tightened by bob using spanner3 at location7, spanner1 is picked up by bob from location5, spanner1 is picked up by bob from gate, at location2, bob uses spanner1 to tighten nut4, bob walks from location9 to location8, at location3, bob uses spanner4 to tighten nut2, bob tightens nut3 with spanner2 at location1, nut3 is tightened by bob using spanner1 at location5, at location3, bob uses spanner3 to tighten nut5, bob walks from location8 to location7, from location4 to location2, bob walks, at location6, bob uses spanner4 to tighten nut3, nut4 is tightened by bob using spanner1 at location4, bob walks to gate from location7, at shed, bob uses spanner1 to tighten nut5, bob walks from shed to location5, nut5 is tightened by bob using spanner5 at gate, bob picks up spanner4 from location9, nut4 is tightened by bob using spanner4 at location3, bob tightens nut3 with spanner5 at location5, bob walks from shed to location3, bob walks from location6 to shed, nut1 is tightened by bob using spanner5 at gate, bob tightens nut2 with spanner1 at location1, nut2 is tightened by bob using spanner3 at gate, bob tightens nut3 with spanner3 at location6, from location8, bob picks up spanner1, nut3 is tightened by bob using spanner5 at location8, nut1 is tightened by bob using spanner3 at location5, nut4 is tightened by bob using spanner2 at location6, nut4 is tightened by bob using spanner1 at location6, bob walks from location9 to shed, bob tightens nut2 with spanner4 at location8, bob tightens nut4 with spanner3 at location8, bob walks from location3 to location2, bob tightens nut4 with spanner1 at location3, bob tightens nut3 with spanner5 at gate, bob picks up spanner2 from shed, bob picks up spanner5 from shed, bob tightens nut2 with spanner1 at location3, bob picks up spanner5 from location2, from gate to location2, bob walks, at location5, bob uses spanner4 to tighten nut1, at location2, bob uses spanner1 to tighten nut5, bob tightens nut1 with spanner2 at location4, nut2 is tightened by bob using spanner1 at gate, nut4 is tightened by bob using spanner1 at location7, at location3, bob uses spanner2 to tighten nut3, bob tightens nut3 with spanner4 at gate, bob tightens nut3 with spanner2 at location8, from location2 to gate, bob walks, nut3 is tightened by bob using spanner3 at location3, bob tightens nut4 with spanner3 at location4, bob tightens nut1 with spanner5 at location8, bob walks to location6 from location5, at location7, bob uses spanner2 to tighten nut1, bob walks to location6 from location1, at location4, bob uses spanner5 to tighten nut2, at location3, bob uses spanner5 to tighten nut3, at location7, bob uses spanner4 to tighten nut1, nut1 is tightened by bob using spanner4 at location1, spanner1 is picked up by bob from location1, bob tightens nut3 with spanner1 at location1, at location8, bob uses spanner1 to tighten nut2, bob walks to location7 from gate, from location3 to location5, bob walks, bob walks from location2 to shed, nut4 is tightened by bob using spanner2 at location9, from location1 to gate, bob walks, from location9, bob picks up spanner2, nut1 is tightened by bob using spanner3 at location6, bob tightens nut4 with spanner5 at location9, nut2 is tightened by bob using spanner5 at location9, from location9 to location2, bob walks, spanner1 is picked up by bob from location9, bob tightens nut4 with spanner5 at location4, bob walks from location4 to location7, at location8, bob uses spanner5 to tighten nut4, bob picks up spanner1 from location4, nut1 is tightened by bob using spanner3 at location9, from location1 to location7, bob walks, from location5, bob picks up spanner2, nut2 is tightened by bob using spanner2 at location9, bob walks from gate to location8, bob tightens nut4 with spanner5 at location5, nut3 is tightened by bob using spanner4 at location2, bob tightens nut5 with spanner5 at location1, bob tightens nut1 with spanner1 at location1, bob walks from location7 to location6, bob tightens nut1 with spanner1 at location4, bob walks from shed to gate, bob tightens nut2 with spanner2 at location2, bob walks to location1 from location4, spanner4 is picked up by bob from location4, nut1 is tightened by bob using spanner1 at location2, bob walks from shed to location6, nut1 is tightened by bob using spanner2 at location3, at gate, bob uses spanner5 to tighten nut4, bob tightens nut4 with spanner4 at location2, at gate, bob uses spanner5 to tighten nut2, bob tightens nut5 with spanner1 at location6, at location6, bob uses spanner1 to tighten nut2, at location8, bob uses spanner2 to tighten nut1, bob tightens nut1 with spanner5 at location9, bob tightens nut2 with spanner4 at location2, from location1, bob picks up spanner4, at location9, bob uses spanner4 to tighten nut2, bob tightens nut5 with spanner3 at location9, bob tightens nut2 with spanner1 at location9, at location7, bob uses spanner5 to tighten nut4, at location8, bob uses spanner4 to tighten nut5, at location2, bob uses spanner3 to tighten nut1, nut5 is tightened by bob using spanner2 at location6, at location8, bob uses spanner5 to tighten nut2, from location6 to location1, bob walks, from location1 to location3, bob walks, nut5 is tightened by bob using spanner1 at location3, at location9, bob uses spanner3 to tighten nut4, bob walks to location8 from location7, at location1, bob uses spanner4 to tighten nut5, spanner3 is picked up by bob from location6, bob tightens nut3 with spanner1 at gate, nut1 is tightened by bob using spanner4 at gate, at location4, bob uses spanner4 to tighten nut1, bob tightens nut2 with spanner5 at location5, nut1 is tightened by bob using spanner5 at location7, bob tightens nut2 with spanner2 at location4, at shed, bob uses spanner4 to tighten nut2, bob tightens nut2 with spanner4 at location7, spanner5 is picked up by bob from gate, bob walks from location2 to location9, at location4, bob uses spanner3 to tighten nut1, bob picks up spanner5 from location1, at shed, bob uses spanner1 to tighten nut3, at location9, bob uses spanner2 to tighten nut1, at location1, bob uses spanner4 to tighten nut2, spanner5 is picked up by bob from location5, bob walks from location4 to location8, nut5 is tightened by bob using spanner5 at location4, bob tightens nut5 with spanner1 at location9, nut5 is tightened by bob using spanner5 at location6, from location8 to location6, bob walks, bob tightens nut2 with spanner3 at location5, bob tightens nut4 with spanner3 at location2, nut5 is tightened by bob using spanner3 at gate, nut3 is tightened by bob using spanner5 at location2, nut5 is tightened by bob using spanner2 at location5, nut1 is tightened by bob using spanner5 at location6, bob picks up spanner2 from location4, bob walks to shed from location5, at location9, bob uses spanner4 to tighten nut3, bob walks from location6 to location4, at shed, bob uses spanner5 to tighten nut5, bob tightens nut4 with spanner4 at location7, from location4 to gate, bob walks, from location9 to gate, bob walks, bob tightens nut3 with spanner3 at location2, at location7, bob uses spanner5 to tighten nut5, bob walks to location6 from gate, bob picks up spanner1 from location3, bob tightens nut2 with spanner5 at location3, bob tightens nut2 with spanner5 at location6, bob picks up spanner4 from location2, at location9, bob uses spanner1 to tighten nut1, at location1, bob uses spanner2 to tighten nut5, from location8 to location4, bob walks, bob walks to shed from location3, nut4 is tightened by bob using spanner5 at location3, bob walks to location9 from location8, nut1 is tightened by bob using spanner5 at shed, bob tightens nut4 with spanner3 at location3, at location3, bob uses spanner4 to tighten nut3, at location8, bob uses spanner2 to tighten nut2, spanner3 is picked up by bob from location4, nut2 is tightened by bob using spanner2 at location5, bob walks from location6 to gate, bob tightens nut1 with spanner5 at location2, bob tightens nut5 with spanner2 at shed, bob picks up spanner5 from location7, from location3, bob picks up spanner2, bob walks to location6 from location4, bob walks from location8 to gate, nut2 is tightened by bob using spanner2 at location7, at location1, bob uses spanner3 to tighten nut1, bob walks from shed to location2, at location1, bob uses spanner1 to tighten nut5, bob walks to location1 from location7, from location1 to location8, bob walks, at location2, bob uses spanner3 to tighten nut5, at location5, bob uses spanner3 to tighten nut3, nut3 is tightened by bob using spanner2 at location4, from location6 to location2, bob walks, nut1 is tightened by bob using spanner4 at location8, bob tightens nut5 with spanner5 at location5, nut5 is tightened by bob using spanner2 at location4, bob picks up spanner4 from location3, bob picks up spanner1 from location2, nut5 is tightened by bob using spanner2 at location9, at location6, bob uses spanner3 to tighten nut5, nut1 is tightened by bob using spanner2 at shed, at location6, bob uses spanner2 to tighten nut2, at location8, bob uses spanner2 to tighten nut5, bob walks to shed from location8, at location3, bob uses spanner1 to tighten nut3, at location3, bob uses spanner4 to tighten nut5, bob walks from location4 to location9, nut4 is tightened by bob using spanner2 at shed, from location7 to location5, bob walks, at location1, bob uses spanner2 to tighten nut4, bob picks up spanner5 from location8, nut3 is tightened by bob using spanner5 at shed, at location9, bob uses spanner1 to tighten nut4, nut2 is tightened by bob using spanner3 at location1, at location3, bob uses spanner2 to tighten nut2, nut4 is tightened by bob using spanner4 at shed, bob tightens nut4 with spanner3 at location1, at shed, bob uses spanner3 to tighten nut1, at location2, bob uses spanner5 to tighten nut5, bob picks up spanner3 from gate, bob tightens nut3 with spanner1 at location2, bob tightens nut3 with spanner4 at location7, bob tightens nut2 with spanner3 at location4, from gate to shed, bob walks, bob tightens nut3 with spanner5 at location4, spanner2 is picked up by bob from location7, spanner5 is picked up by bob from location4, nut4 is tightened by bob using spanner1 at location5, nut3 is tightened by bob using spanner3 at gate, bob walks from location3 to location4, from location7 to location9, bob walks, at location4, bob uses spanner3 to tighten nut5, at location1, bob uses spanner2 to tighten nut2, nut4 is tightened by bob using spanner2 at location4, nut3 is tightened by bob using spanner2 at gate, bob tightens nut3 with spanner4 at location8, bob tightens nut3 with spanner1 at location6, bob walks from location9 to location5, from location5 to location4, bob walks, bob tightens nut2 with spanner4 at gate, bob walks from location9 to location4, bob walks from location3 to location1, nut4 is tightened by bob using spanner2 at location8, bob picks up spanner3 from location1, bob tightens nut1 with spanner3 at gate, bob walks to location7 from location5, at location3, bob uses spanner2 to tighten nut4, bob walks from location2 to location5, bob tightens nut3 with spanner3 at shed, nut5 is tightened by bob using spanner1 at location5, nut3 is tightened by bob using spanner4 at location1, nut4 is tightened by bob using spanner3 at location7, nut3 is tightened by bob using spanner3 at location7, at location6, bob uses spanner5 to tighten nut3, from location9, bob picks up spanner5, bob tightens nut2 with spanner4 at location6, bob tightens nut4 with spanner4 at location8, bob tightens nut2 with spanner4 at location4, nut2 is tightened by bob using spanner5 at location1, from location3 to location9, bob walks, from location9 to location6, bob walks, at location4, bob uses spanner1 to tighten nut3, nut3 is tightened by bob using spanner2 at location7, nut2 is tightened by bob using spanner1 at shed, nut5 is tightened by bob using spanner4 at location5, from gate to location4, bob walks, bob tightens nut3 with spanner4 at shed, nut3 is tightened by bob using spanner2 at shed, bob tightens nut2 with spanner5 at location7, at gate, bob uses spanner3 to tighten nut4, nut2 is tightened by bob using spanner3 at location9, from gate to location5, bob walks, spanner5 is picked up by bob from location3, nut1 is tightened by bob using spanner4 at location3, at location5, bob uses spanner1 to tighten nut2, at location5, bob uses spanner2 to tighten nut1, at location8, bob uses spanner1 to tighten nut4, bob walks from location1 to location5, bob tightens nut2 with spanner3 at location7, bob tightens nut5 with spanner5 at location8, nut1 is tightened by bob using spanner1 at location7, at location3, bob uses spanner5 to tighten nut5, nut5 is tightened by bob using spanner4 at location9, from location2 to location6, bob walks, at location8, bob uses spanner1 to tighten nut1, at location2, bob uses spanner2 to tighten nut5, bob tightens nut1 with spanner3 at location8, bob walks to location4 from location2, bob picks up spanner4 from location7, bob walks to location5 from location4, nut5 is tightened by bob using spanner3 at location8, nut3 is tightened by bob using spanner2 at location9, bob walks from location8 to location2, bob walks to location4 from shed, at location4, bob uses spanner1 to tighten nut5, nut2 is tightened by bob using spanner3 at location8, nut5 is tightened by bob using spanner4 at location2, bob tightens nut1 with spanner2 at location2, from location7 to location4, bob walks, bob walks to location1 from location8, bob walks to location4 from location1, at shed, bob uses spanner4 to tighten nut1, nut1 is tightened by bob using spanner1 at shed, nut4 is tightened by bob using spanner4 at location5, bob picks up spanner3 from location9, from location2 to location8, bob walks, bob walks to location9 from location5, at location1, bob uses spanner3 to tighten nut3, from location5, bob picks up spanner3, bob picks up spanner3 from location7, at location9, bob uses spanner3 to tighten nut3, spanner2 is picked up by bob from location2, bob walks to shed from location4, bob walks to location6 from location3, bob walks from location2 to location3, nut5 is tightened by bob using spanner3 at shed, spanner1 is picked up by bob from location7, bob tightens nut4 with spanner4 at location1, from shed to location7, bob walks, spanner4 is picked up by bob from location8, nut5 is tightened by bob using spanner4 at location7, from location8 to location5, bob walks, nut2 is tightened by bob using spanner3 at location2, at location8, bob uses spanner3 to tighten nut3, at location1, bob uses spanner1 to tighten nut4, bob tightens nut1 with spanner2 at location6, at shed, bob uses spanner4 to tighten nut5, bob walks from location3 to location8, from location6 to location9, bob walks, from gate to location9, bob walks, at location4, bob uses spanner1 to tighten nut2, at location6, bob uses spanner3 to tighten nut2, nut2 is tightened by bob using spanner3 at shed, bob walks to location1 from gate, nut1 is tightened by bob using spanner1 at gate, at location4, bob uses spanner4 to tighten nut5, from location6 to location5, bob walks, bob walks from location5 to location1, bob walks to location1 from shed, at location6, bob uses spanner4 to tighten nut1, bob walks from shed to location8, bob picks up spanner1 from location6, bob tightens nut3 with spanner3 at location4, from location6, bob picks up spanner2, bob walks to location8 from location6, from location6 to location3, bob walks, nut1 is tightened by bob using spanner5 at location3, nut4 is tightened by bob using spanner5 at location2, nut1 is tightened by bob using spanner3 at location3 and bob tightens nut3 with spanner5 at location7", "plan_length": 19, "initial_state_nl": "A link between location5 and location6 exists, a link between location9 and gate exists, a link between shed and location1 exists, bob is located at shed, location1 and location2 are linked, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location6 and location7 are linked, location7 and location8 are linked, location8 is linked to location9, nut1 is at gate, nut1 is loose, nut2 is at gate, nut2 is loose, nut3 is located at gate, nut3 is not secured, nut4 is at gate, nut4 is not secured, nut5 is at gate, nut5 is loose, spanner1 is currently at location6, spanner1 is usable, spanner2 is currently at location8, spanner2 is functional, spanner3 can be used, spanner3 is currently at location8, spanner4 can be used, spanner4 is currently at location5, spanner5 can be used and spanner5 is currently at location7.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob moves from the shed to location1, then to location2, and continues walking. He proceeds from location2 to location3, then to location4, and from location4 to location5. At location5, Bob picks up spanner4, then moves to location6, where he picks up spanner1. He walks from location6 to location7, picks up spanner5, and then moves to location8. At location8, Bob picks up spanner3 and spanner2. He then walks from location8 to location9 and finally to the gate. At the gate, Bob uses spanner5 to tighten nut1, spanner4 to tighten nut2, spanner3 to tighten nut3, and spanner2 to tighten nut4, resulting in the current state. In this state, list all inexecutable actions. Write None if there are none.", "initial_state_nl_paraphrased": "There is a connection between location5 and location6, a connection between location9 and the gate exists, and a connection between the shed and location1 is present. Bob is currently situated at the shed. Location1 is connected to location2, location2 is linked to location3, location3 is connected to location4, and location4 is connected to location5. Furthermore, location6 is connected to location7, location7 is connected to location8, and location8 is linked to location9. At the gate, nut1 is present and loose, nut2 is also at the gate and loose, nut3 is located at the gate but not secured, nut4 is at the gate and not secured, and nut5 is at the gate and loose. Spanner1 is currently located at location6 and is in working condition, spanner2 is at location8 and functional, spanner3 is at location8 and usable, spanner4 is at location5 and usable, and spanner5 is at location7 and usable."}
{"question_id": "d542e16f-8324-4a8f-ae60-b2f6e0864c8c", "domain_name": "spanner", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from shed to location1, bob walks, from location1 to location2, bob walks, from location2, bob picks up spanner4, bob picks up spanner3 from location2, bob walks from location2 to location3, bob walks from location3 to location4, bob walks from location4 to location5, bob walks from location5 to location6, spanner5 is picked up by bob from location6 and from shed, bob picks up spanner4 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "bob picks up spanner4 from shed", "plan_length": 10, "initial_state_nl": "A link between location6 and location7 exists, a link between location9 and gate exists, bob is at shed, location1 is linked to location2, location2 is linked to location3, location3 and location4 are linked, location4 and location5 are linked, location5 is linked to location6, location7 and location8 are linked, location8 and location9 are linked, nut1 is located at gate, nut1 is loose, nut2 is currently at gate, nut2 is loose, nut3 is currently at gate, nut3 is loose, nut4 is currently at gate, nut4 is not secured, nut5 is located at gate, nut5 is loose, shed is linked to location1, spanner1 is at location8, spanner1 is usable, spanner2 is at location6, spanner2 is functional, spanner3 is currently at location2, spanner3 is usable, spanner4 can be used, spanner4 is currently at location2, spanner5 is located at location6 and spanner5 is usable.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: Bob walks from the shed to location1, then from location1 to location2, then from location2 he picks up spanner4 and spanner3, then walks from location2 to location3, from location3 to location4, from location4 to location5, from location5 to location6, and finally picks up spanner5 from location6. However, initially, bob picks up spanner4 from the shed to reach the current state. What is the first action in the sequence that cannot be executed? If none, write None.", "initial_state_nl_paraphrased": "There is a connection between location6 and location7, and another connection exists between location9 and the gate. Bob is currently at the shed. The locations are interconnected as follows: location1 is connected to location2, location2 is connected to location3, location3 is linked with location4, location4 is linked with location5, and location5 is connected to location6. Additionally, location7 is connected to location8, and location8 is connected to location9. At the gate, the following items are located: nut1, which is loose, nut2, which is also loose, nut3, which is loose, nut4, which is not secured, and nut5, which is loose. The shed is connected to location1. The following spanners are located at the respective locations: spanner1 is at location8 and is usable, spanner2 is at location6 and is functional, spanner3 is at location2 and is usable, spanner4 is at location2 and can be used, and spanner5 is at location6 and is usable."}
