{"question_id": "7d9f7280-c438-46ec-bc0e-a3d5907a4fcb", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_11", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x4_y2 to loc_x3_y2, moves to loc_x2_y2 from loc_x3_y2, robot moves from loc_x2_y2 to loc_x2_y1, moves from loc_x2_y1 to loc_x3_y1, moves to loc_x4_y1 from loc_x3_y1, moves to loc_x4_y0 from loc_x4_y1, robot moves from loc_x4_y0 to loc_x3_y0, from loc_x3_y0, the robot moves to loc_x2_y0, moves from loc_x2_y0 to loc_x1_y0, robot moves from loc_x1_y0 to loc_x0_y0, from loc_x0_y0, the robot moves to loc_x0_y1, moves from loc_x0_y1 to loc_x1_y1, from loc_x1_y1, the robot moves to loc_x1_y2, from loc_x1_y2, the robot moves to loc_x1_y3, robot moves from loc_x1_y3 to loc_x0_y3, moves to loc_x0_y4 from loc_x0_y3, robot moves from loc_x0_y4 to loc_x1_y4, from loc_x1_y4, the robot moves to loc_x2_y4 and robot moves from loc_x2_y4 to loc_x2_y3 to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 552? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x3_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 and loc_x4_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, robot is located at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x4_y1 and loc_x3_y1 and there is a connection between loc_x4_y4 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the robot undergoes a series of movements as follows: it transitions from loc_x4_y2 to loc_x3_y2, then to loc_x2_y2, followed by loc_x2_y1, then loc_x3_y1, then loc_x4_y1, and subsequently loc_x4_y0. From loc_x4_y0, the robot moves to loc_x3_y0, then to loc_x2_y0, followed by loc_x1_y0, and then loc_x0_y0. The robot then proceeds to loc_x0_y1, then loc_x1_y1, followed by loc_x1_y2, then loc_x1_y3, and then loc_x0_y3. It then moves to loc_x0_y4, followed by loc_x1_y4, then loc_x2_y4, and finally loc_x2_y3, resulting in the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 552? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x3_y0, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x2_y3, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y0 and loc_x2_y0, loc_x3_y1 and loc_x3_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x3_y4 and loc_x4_y4, loc_x3_y4 and loc_x2_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, the robot is at loc_x4_y2. Additionally, connections exist between loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x1_y4, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x3_y4, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x4_y2, loc_x4_y1 and loc_x3_y1, and loc_x4_y4 and loc_x3_y4."}
{"question_id": "e579562f-263f-45ce-854e-98d510db5299", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x1_y0 to loc_x0_y0, robot moves from loc_x0_y0 to loc_x0_y1, moves from loc_x0_y1 to loc_x1_y1, moves from loc_x1_y1 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, moves from loc_x3_y0 to loc_x3_y1, moves from loc_x3_y1 to loc_x3_y2, moves to loc_x3_y1 from loc_x3_y2, moves to loc_x4_y1 from loc_x3_y1, moves from loc_x4_y1 to loc_x4_y0, robot moves from loc_x4_y0 to loc_x5_y0, robot moves from loc_x5_y0 to loc_x5_y1, robot moves from loc_x5_y1 to loc_x5_y2, moves to loc_x4_y2 from loc_x5_y2, robot moves from loc_x4_y2 to loc_x4_y3, robot moves from loc_x4_y3 to loc_x5_y3, moves from loc_x5_y3 to loc_x5_y4 and robot moves from loc_x5_y4 to loc_x4_y4 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 708? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is visited, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y4 and loc_x4_y4 are connected, robot is located at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y2, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y0 and loc_x4_y0, there is a connection between loc_x5_y1 and loc_x5_y0, there is a connection between loc_x5_y3 and loc_x4_y3, there is a connection between loc_x5_y3 and loc_x5_y4 and there is a connection between loc_x5_y4 and loc_x5_y3.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot transitions from loc_x1_y0 to loc_x0_y0, then from loc_x0_y0 to loc_x0_y1, followed by a move from loc_x0_y1 to loc_x1_y1, and then from loc_x1_y1 to loc_x2_y1. The robot continues by moving from loc_x2_y1 to loc_x2_y0, then from loc_x2_y0 to loc_x3_y0, followed by transitions from loc_x3_y0 to loc_x3_y1, from loc_x3_y1 to loc_x3_y2, and back from loc_x3_y2 to loc_x3_y1. Subsequent moves include transitions from loc_x3_y1 to loc_x4_y1, from loc_x4_y1 to loc_x4_y0, from loc_x4_y0 to loc_x5_y0, from loc_x5_y0 to loc_x5_y1, and from loc_x5_y1 to loc_x5_y2. The robot then moves from loc_x5_y2 to loc_x4_y2, from loc_x4_y2 to loc_x4_y3, from loc_x4_y3 to loc_x5_y3, from loc_x5_y3 to loc_x5_y4, and finally from loc_x5_y4 to loc_x4_y4, resulting in the current state. In this state, is the number of valid properties of the state that involve negations equal to 708? True or False", "initial_state_nl_paraphrased": "Loc_x0_y0 is adjacent to loc_x1_y0, loc_x0_y1, and loc_x0_y2 are adjacent, loc_x0_y1 is adjacent to loc_x0_y0, loc_x0_y2, and loc_x0_y3 are adjacent, loc_x0_y4 is adjacent to loc_x0_y3, loc_x0_y4 is adjacent to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are adjacent, loc_x1_y0 and loc_x1_y1 are adjacent, loc_x1_y0 has been visited, loc_x1_y1 and loc_x2_y1 are adjacent, loc_x1_y1 is adjacent to loc_x0_y1, loc_x1_y1 is adjacent to loc_x1_y0, loc_x1_y1 is adjacent to loc_x1_y2, loc_x1_y2 is adjacent to loc_x1_y1, loc_x1_y2 is adjacent to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are adjacent, loc_x2_y0 is adjacent to loc_x1_y0, loc_x2_y0 is adjacent to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are adjacent, loc_x2_y1 and loc_x2_y2 are adjacent, loc_x2_y1 and loc_x3_y1 are adjacent, loc_x2_y1 is adjacent to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are adjacent, loc_x2_y2 is adjacent to loc_x1_y2, loc_x2_y2 is adjacent to loc_x2_y1, loc_x2_y3 is adjacent to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are adjacent, loc_x3_y1 is adjacent to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are adjacent, loc_x3_y2 is adjacent to loc_x2_y2, loc_x3_y2 is adjacent to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are adjacent, loc_x3_y3 and loc_x4_y3 are adjacent, loc_x3_y3 is adjacent to loc_x2_y3, loc_x3_y3 is adjacent to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are adjacent, loc_x3_y4 is adjacent to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are adjacent, loc_x4_y0 is adjacent to loc_x4_y1, loc_x4_y0 is adjacent to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are adjacent, loc_x4_y1 is adjacent to loc_x5_y1, loc_x4_y2 is adjacent to loc_x4_y1, loc_x4_y2 is adjacent to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are adjacent, loc_x5_y0 is adjacent to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are adjacent, loc_x5_y1 is adjacent to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are adjacent, loc_x5_y2 is adjacent to loc_x5_y1, loc_x5_y2 is adjacent to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are adjacent, loc_x5_y4 and loc_x4_y4 are adjacent, the robot is positioned at loc_x1_y0, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y1 and loc_x1_y1, a path exists between loc_x0_y2 and loc_x0_y1, a path exists between loc_x0_y2 and loc_x1_y2, a path exists between loc_x0_y3 and loc_x0_y2, a path exists between loc_x0_y3 and loc_x0_y4, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y2 and loc_x0_y2, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y0 and loc_x4_y0, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x4_y1 and loc_x3_y1, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, a path exists between loc_x4_y2 and loc_x5_y2, a path exists between loc_x4_y3 and loc_x3_y3, a path exists between loc_x4_y3 and loc_x4_y2, a path exists between loc_x4_y3 and loc_x4_y4, a path exists between loc_x4_y3 and loc_x5_y3, a path exists between loc_x4_y4 and loc_x3_y4, a path exists between loc_x4_y4 and loc_x5_y4, a path exists between loc_x5_y0 and loc_x4_y0, a path exists between loc_x5_y1 and loc_x5_y0, a path exists between loc_x5_y3 and loc_x4_y3, a path exists between loc_x5_y3 and loc_x5_y4, and a path exists between loc_x5_y4 and loc_x5_y3."}
{"question_id": "3e3646d1-d447-4b38-a575-f47812d40bc8", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y3, moves to loc_x1_y3 from loc_x0_y3, robot moves from loc_x1_y3 to loc_x1_y4, robot moves from loc_x1_y4 to loc_x2_y4, robot moves from loc_x2_y4 to loc_x3_y4, from loc_x3_y4, the robot moves to loc_x3_y3, moves to loc_x2_y3 from loc_x3_y3, robot moves from loc_x2_y3 to loc_x2_y2, moves to loc_x2_y1 from loc_x2_y2, moves from loc_x2_y1 to loc_x1_y1, from loc_x1_y1, the robot moves to loc_x0_y1, robot moves from loc_x0_y1 to loc_x0_y0, robot moves from loc_x0_y0 to loc_x1_y0, moves from loc_x1_y0 to loc_x2_y0, moves to loc_x3_y0 from loc_x2_y0, robot moves from loc_x3_y0 to loc_x3_y1, from loc_x3_y1, the robot moves to loc_x3_y2 and moves from loc_x3_y2 to loc_x4_y2 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 449? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 is visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the robot performs the following sequence of actions: it moves from loc_x0_y3 to loc_x0_y4, then back to loc_x0_y3, followed by a move to loc_x1_y3, then to loc_x1_y4, and subsequently to loc_x2_y4 and loc_x3_y4. From loc_x3_y4, the robot moves to loc_x3_y3, then to loc_x2_y3, followed by loc_x2_y2, loc_x2_y1, loc_x1_y1, loc_x0_y1, loc_x0_y0, loc_x1_y0, loc_x2_y0, and loc_x3_y0. The robot then moves to loc_x3_y1, loc_x3_y2, and finally to loc_x4_y2 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 449? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is connected to both loc_x0_y3 and loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is also connected to loc_x0_y0 and loc_x1_y1, loc_x1_y1 is connected to both loc_x0_y1 and loc_x2_y1, loc_x1_y3 is connected to loc_x1_y4 and loc_x2_y3, loc_x1_y3 is also connected to loc_x0_y3, loc_x1_y4 is connected to loc_x0_y4 and loc_x1_y3, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to both loc_x2_y2 and loc_x1_y3, loc_x2_y4 is connected to loc_x2_y3 and loc_x3_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is also connected to loc_x3_y1 and loc_x4_y0, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is also connected to loc_x2_y1 and loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is also connected to loc_x3_y1 and loc_x3_y3, and loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 is connected to loc_x3_y2 and loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y1 is connected to loc_x4_y0 and loc_x4_y2, loc_x4_y1 is also connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, the robot's current location is loc_x0_y3, and the following connections exist: loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x1_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y1 and loc_x4_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "b016b364-b4a5-4912-a2c5-d8a8a3b163b9", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y2 to loc_x0_y1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 75? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 and loc_x0_y4 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x2_y5, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the robot executes the following actions: it moves from loc_x0_y2 to loc_x0_y1, resulting in the current state. In this state, does the count of valid properties that do not involve negations equal 75? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 is also connected to loc_x0_y4, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is also connected to loc_x2_y1, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is also connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is also connected to loc_x2_y5, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is also connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is also connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is also connected to loc_x2_y5, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is also connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, the robot is currently at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2."}
{"question_id": "a4fa98fe-64fd-4c0e-9af0-67c02798a3dd", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x3_y2 from loc_x4_y2, moves to loc_x2_y2 from loc_x3_y2, moves to loc_x2_y1 from loc_x2_y2, robot moves from loc_x2_y1 to loc_x3_y1, moves to loc_x4_y1 from loc_x3_y1, from loc_x4_y1, the robot moves to loc_x4_y0, from loc_x4_y0, the robot moves to loc_x3_y0, moves to loc_x2_y0 from loc_x3_y0, moves to loc_x1_y0 from loc_x2_y0 and robot moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 75? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x3_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 and loc_x4_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, robot is located at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x4_y1 and loc_x3_y1 and there is a connection between loc_x4_y4 and loc_x3_y4.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot relocates from loc_x4_y2 to loc_x3_y2, then from loc_x3_y2 to loc_x2_y2, followed by a move from loc_x2_y2 to loc_x2_y1, then from loc_x2_y1 to loc_x3_y1, and subsequently from loc_x3_y1 to loc_x4_y1. From loc_x4_y1, the robot proceeds to loc_x4_y0, then to loc_x3_y0, followed by a move to loc_x2_y0, then to loc_x1_y0, and finally from loc_x1_y0 to loc_x0_y0, resulting in the current state. In this state, is the count of valid properties that do not involve negations equal to 75? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x3_y0, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x2_y3, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y0 and loc_x2_y0, loc_x3_y1 and loc_x3_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x3_y4 and loc_x4_y4, loc_x3_y4 and loc_x2_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x4_y1, and loc_x4_y2 is visited. The robot is currently at loc_x4_y2. Additionally, connections exist between loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x1_y4, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x3_y4, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x4_y2, loc_x4_y1 and loc_x3_y1, and loc_x4_y4 and loc_x3_y4."}
{"question_id": "246e68e9-da54-42ae-b044-dabc8fb27c82", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x0_y2 to loc_x0_y1, moves to loc_x0_y0 from loc_x0_y1, robot moves from loc_x0_y0 to loc_x1_y0, from loc_x1_y0, the robot moves to loc_x1_y1, from loc_x1_y1, the robot moves to loc_x1_y2, robot moves from loc_x1_y2 to loc_x0_y2, moves from loc_x0_y2 to loc_x0_y3, from loc_x0_y3, the robot moves to loc_x0_y4, from loc_x0_y4, the robot moves to loc_x0_y5 and moves from loc_x0_y5 to loc_x1_y5 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 469? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 and loc_x0_y4 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x2_y5, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: the robot transitions from loc_x0_y2 to loc_x0_y1, then from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y0 to loc_x1_y0. From loc_x1_y0, the robot proceeds to loc_x1_y1, then to loc_x1_y2, and subsequently returns to loc_x0_y2. The robot then moves from loc_x0_y2 to loc_x0_y3, then to loc_x0_y4, and from loc_x0_y4 to loc_x0_y5. Finally, it moves from loc_x0_y5 to loc_x1_y5, reaching the current state. In this state, is the number of valid properties of the state that involve negations equal to 469? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 is also connected to loc_x0_y4, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is also connected to loc_x2_y1, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is also connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is also connected to loc_x2_y5, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is also connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is also connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is also connected to loc_x2_y5, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is also connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, the robot is currently at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2."}
{"question_id": "cdf6d23a-066d-4010-b9b3-d21fd9a08fa3", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x4_y2 to loc_x3_y2, from loc_x3_y2, the robot moves to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, moves from loc_x4_y0 to loc_x3_y0, moves to loc_x2_y0 from loc_x3_y0, moves to loc_x1_y0 from loc_x2_y0 and from loc_x1_y0, the robot moves to loc_x0_y0 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 506? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x3_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 and loc_x4_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, robot is located at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x4_y1 and loc_x3_y1 and there is a connection between loc_x4_y4 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the robot undergoes a series of movements as follows: it transitions from loc_x4_y2 to loc_x3_y2, then from loc_x3_y2 to loc_x2_y2, followed by a move from loc_x2_y2 to loc_x2_y1, then to loc_x3_y1 from loc_x2_y1, and subsequently to loc_x4_y1 from loc_x3_y1. The robot continues its movement from loc_x4_y1 to loc_x4_y0, then to loc_x3_y0 from loc_x4_y0, and from loc_x3_y0 to loc_x2_y0. It then proceeds to loc_x1_y0 from loc_x2_y0, and finally reaches the current state by moving from loc_x1_y0 to loc_x0_y0. In this state, is the number of executable and inexecutable actions equal to 506? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x3_y0, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x2_y3, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y0 and loc_x2_y0, loc_x3_y1 and loc_x3_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x3_y4 and loc_x4_y4, loc_x3_y4 and loc_x2_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, the robot is at loc_x4_y2. Additionally, the following connections exist: loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x1_y4, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x3_y4, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x4_y2, loc_x4_y1 and loc_x3_y1, and loc_x4_y4 and loc_x3_y4."}
{"question_id": "7b0d9864-61e9-4156-a245-6d323cf49319", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x1_y0 to loc_x0_y0 to reach the current state. Is it True or False that the number of actions that led to current state in the sequence is equal to 1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is visited, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y4 and loc_x4_y4 are connected, robot is located at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y2, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y0 and loc_x4_y0, there is a connection between loc_x5_y1 and loc_x5_y0, there is a connection between loc_x5_y3 and loc_x4_y3, there is a connection between loc_x5_y3 and loc_x5_y4 and there is a connection between loc_x5_y4 and loc_x5_y3.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Considering the initial condition, the robot executes the following actions: it moves from loc_x1_y0 to loc_x0_y0 to attain the current state. Is it True or False that the number of actions in the sequence that resulted in the current state is 1?", "initial_state_nl_paraphrased": "Loc_x0_y0 is adjacent to loc_x1_y0, loc_x0_y1, and loc_x0_y2 are adjacent, loc_x0_y1 is adjacent to loc_x0_y0, loc_x0_y2, and loc_x0_y3 are adjacent, loc_x0_y4 is adjacent to loc_x0_y3, loc_x0_y4 is adjacent to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are adjacent, loc_x1_y0 and loc_x1_y1 are adjacent, loc_x1_y0 has been visited, loc_x1_y1 and loc_x2_y1 are adjacent, loc_x1_y1 is adjacent to loc_x0_y1, loc_x1_y1 is adjacent to loc_x1_y0, loc_x1_y1 is adjacent to loc_x1_y2, loc_x1_y2 is adjacent to loc_x1_y1, loc_x1_y2 is adjacent to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are adjacent, loc_x2_y0 is adjacent to loc_x1_y0, loc_x2_y0 is adjacent to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are adjacent, loc_x2_y1 and loc_x2_y2 are adjacent, loc_x2_y1 and loc_x3_y1 are adjacent, loc_x2_y1 is adjacent to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are adjacent, loc_x2_y2 is adjacent to loc_x1_y2, loc_x2_y2 is adjacent to loc_x2_y1, loc_x2_y3 is adjacent to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are adjacent, loc_x3_y1 is adjacent to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are adjacent, loc_x3_y2 is adjacent to loc_x2_y2, loc_x3_y2 is adjacent to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are adjacent, loc_x3_y3 and loc_x4_y3 are adjacent, loc_x3_y3 is adjacent to loc_x2_y3, loc_x3_y3 is adjacent to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are adjacent, loc_x3_y4 is adjacent to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are adjacent, loc_x4_y0 is adjacent to loc_x4_y1, loc_x4_y0 is adjacent to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are adjacent, loc_x4_y1 is adjacent to loc_x5_y1, loc_x4_y2 is adjacent to loc_x4_y1, loc_x4_y2 is adjacent to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are adjacent, loc_x5_y0 is adjacent to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are adjacent, loc_x5_y1 is adjacent to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are adjacent, loc_x5_y2 is adjacent to loc_x5_y1, loc_x5_y2 is adjacent to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are adjacent, loc_x5_y4 and loc_x4_y4 are adjacent, the robot is positioned at loc_x1_y0, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y1 and loc_x1_y1, a path exists between loc_x0_y2 and loc_x0_y1, a path exists between loc_x0_y2 and loc_x1_y2, a path exists between loc_x0_y3 and loc_x0_y2, a path exists between loc_x0_y3 and loc_x0_y4, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y2 and loc_x0_y2, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y0 and loc_x4_y0, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x4_y1 and loc_x3_y1, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, a path exists between loc_x4_y2 and loc_x5_y2, a path exists between loc_x4_y3 and loc_x3_y3, a path exists between loc_x4_y3 and loc_x4_y2, a path exists between loc_x4_y3 and loc_x4_y4, a path exists between loc_x4_y3 and loc_x5_y3, a path exists between loc_x4_y4 and loc_x3_y4, a path exists between loc_x4_y4 and loc_x5_y4, a path exists between loc_x5_y0 and loc_x4_y0, a path exists between loc_x5_y1 and loc_x5_y0, a path exists between loc_x5_y3 and loc_x4_y3, a path exists between loc_x5_y3 and loc_x5_y4, and a path exists between loc_x5_y4 and loc_x5_y3."}
{"question_id": "d38d20eb-2b74-4226-a211-b74372e0486f", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x4_y2 to loc_x3_y2, robot moves from loc_x3_y2 to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, from loc_x4_y0, the robot moves to loc_x3_y0, robot moves from loc_x3_y0 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x1_y0, moves to loc_x0_y0 from loc_x1_y0, robot moves from loc_x0_y0 to loc_x0_y1, robot moves from loc_x0_y1 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x1_y2, moves from loc_x1_y2 to loc_x1_y3, moves from loc_x1_y3 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x0_y4, from loc_x0_y4, the robot moves to loc_x1_y4, robot moves from loc_x1_y4 to loc_x2_y4 and from loc_x2_y4, the robot moves to loc_x2_y3 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 474? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x3_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 and loc_x4_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, robot is located at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x4_y1 and loc_x3_y1 and there is a connection between loc_x4_y4 and loc_x3_y4.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the robot undergoes a series of movements as follows: it moves from loc_x4_y2 to loc_x3_y2, then to loc_x2_y2, followed by loc_x2_y1, then loc_x3_y1, loc_x4_y1, and loc_x4_y0. From loc_x4_y0, the robot proceeds to loc_x3_y0, then loc_x2_y0, loc_x1_y0, and loc_x0_y0. Next, it moves to loc_x0_y1, loc_x1_y1, loc_x1_y2, loc_x1_y3, and loc_x0_y3. The robot then moves to loc_x0_y4, followed by loc_x1_y4, loc_x2_y4, and finally loc_x2_y3 to reach its current state. In this state, is the number of valid properties of the state that involve negations equal to 474? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x3_y0, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x2_y3, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y0 and loc_x2_y0, loc_x3_y1 and loc_x3_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x3_y4 and loc_x4_y4, loc_x3_y4 and loc_x2_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, the robot is at loc_x4_y2. Additionally, connections exist between loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x1_y4, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x3_y4, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x4_y2, loc_x4_y1 and loc_x3_y1, and loc_x4_y4 and loc_x3_y4."}
{"question_id": "a39eaaf6-7a62-4a4f-b860-f263c2fc4f62", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y2 to loc_x0_y1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 492? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 and loc_x0_y4 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x2_y5, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the robot executes the following actions: it moves from loc_x0_y2 to loc_x0_y1 to attain the current state. In this state, does the count of valid properties involving negations equal 492? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 is also connected to loc_x0_y4, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is also connected to loc_x2_y1, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is also connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is also connected to loc_x2_y5, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is also connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is also connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is also connected to loc_x2_y5, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is also connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, the robot is currently at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2."}
{"question_id": "6aa990e6-fa1c-4030-ace0-d80df2fd4c5f", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x4_y2 to loc_x3_y2, robot moves from loc_x3_y2 to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, from loc_x2_y1, the robot moves to loc_x3_y1, moves from loc_x3_y1 to loc_x4_y1, from loc_x4_y1, the robot moves to loc_x4_y0, from loc_x4_y0, the robot moves to loc_x3_y0, robot moves from loc_x3_y0 to loc_x2_y0, moves from loc_x2_y0 to loc_x1_y0 and moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 453? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x3_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 and loc_x4_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, robot is located at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x4_y1 and loc_x3_y1 and there is a connection between loc_x4_y4 and loc_x3_y4.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: the robot transitions from loc_x4_y2 to loc_x3_y2, then from loc_x3_y2 to loc_x2_y2, followed by a move from loc_x2_y2 to loc_x2_y1. From loc_x2_y1, the robot proceeds to loc_x3_y1, then to loc_x4_y1, and subsequently from loc_x4_y1 to loc_x4_y0. The robot continues from loc_x4_y0 to loc_x3_y0, then to loc_x2_y0, and from loc_x2_y0 to loc_x1_y0, finally reaching loc_x0_y0. In this resulting state, is the count of valid state properties involving negations equal to 453? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x3_y0, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x2_y3, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y0 and loc_x2_y0, loc_x3_y1 and loc_x3_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x3_y4 and loc_x4_y4, loc_x3_y4 and loc_x2_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, the robot is at loc_x4_y2. Additionally, connections exist between loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x1_y4, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x3_y4, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x4_y2, loc_x4_y1 and loc_x3_y1, and loc_x4_y4 and loc_x3_y4."}
{"question_id": "4456a990-69b2-4b59-83c9-97e13de242b0", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_5", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x1_y0 to loc_x0_y0, from loc_x0_y0, the robot moves to loc_x0_y1, moves to loc_x1_y1 from loc_x0_y1, moves to loc_x2_y1 from loc_x1_y1, robot moves from loc_x2_y1 to loc_x2_y0, moves to loc_x3_y0 from loc_x2_y0, from loc_x3_y0, the robot moves to loc_x3_y1, from loc_x3_y1, the robot moves to loc_x3_y2, robot moves from loc_x3_y2 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, moves to loc_x5_y0 from loc_x4_y0, moves to loc_x5_y1 from loc_x5_y0, from loc_x5_y1, the robot moves to loc_x5_y2, moves to loc_x4_y2 from loc_x5_y2, moves to loc_x4_y3 from loc_x4_y2, robot moves from loc_x4_y3 to loc_x5_y3, moves to loc_x5_y4 from loc_x5_y3 and moves from loc_x5_y4 to loc_x4_y4 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 756? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is visited, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y4 and loc_x4_y4 are connected, robot is located at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y2, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y0 and loc_x4_y0, there is a connection between loc_x5_y1 and loc_x5_y0, there is a connection between loc_x5_y3 and loc_x4_y3, there is a connection between loc_x5_y3 and loc_x5_y4 and there is a connection between loc_x5_y4 and loc_x5_y3.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the robot performs the following sequence of actions: it moves from loc_x1_y0 to loc_x0_y0, then to loc_x0_y1, followed by loc_x1_y1, then loc_x2_y1, and then back to loc_x2_y0. From there, it moves to loc_x3_y0, then to loc_x3_y1, and then to loc_x3_y2, before returning to loc_x3_y1. The robot then proceeds to loc_x4_y1, then to loc_x4_y0, followed by loc_x5_y0, then loc_x5_y1, and then loc_x5_y2. It then moves to loc_x4_y2, then to loc_x4_y3, followed by loc_x5_y3, then loc_x5_y4, and finally to loc_x4_y4, reaching the current state. In this state, is the number of executable and inexecutable actions equal to 756? True or False", "initial_state_nl_paraphrased": "Loc_x0_y0 is adjacent to loc_x1_y0, loc_x0_y1, and loc_x0_y2 are adjacent, loc_x0_y1 is adjacent to loc_x0_y0, loc_x0_y2, and loc_x0_y3 are adjacent, loc_x0_y4 is adjacent to loc_x0_y3, loc_x0_y4 is adjacent to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are adjacent, loc_x1_y0 and loc_x1_y1 are adjacent, loc_x1_y0 has been visited, loc_x1_y1 and loc_x2_y1 are adjacent, loc_x1_y1 is adjacent to loc_x0_y1, loc_x1_y1 is adjacent to loc_x1_y0, loc_x1_y1 is adjacent to loc_x1_y2, loc_x1_y2 is adjacent to loc_x1_y1, loc_x1_y2 is adjacent to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are adjacent, loc_x2_y0 is adjacent to loc_x1_y0, loc_x2_y0 is adjacent to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are adjacent, loc_x2_y1 and loc_x2_y2 are adjacent, loc_x2_y1 and loc_x3_y1 are adjacent, loc_x2_y1 is adjacent to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are adjacent, loc_x2_y2 is adjacent to loc_x1_y2, loc_x2_y2 is adjacent to loc_x2_y1, loc_x2_y3 is adjacent to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are adjacent, loc_x3_y1 is adjacent to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are adjacent, loc_x3_y2 is adjacent to loc_x2_y2, loc_x3_y2 is adjacent to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are adjacent, loc_x3_y3 and loc_x4_y3 are adjacent, loc_x3_y3 is adjacent to loc_x2_y3, loc_x3_y3 is adjacent to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are adjacent, loc_x3_y4 is adjacent to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are adjacent, loc_x4_y0 is adjacent to loc_x4_y1, loc_x4_y0 is adjacent to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are adjacent, loc_x4_y1 is adjacent to loc_x5_y1, loc_x4_y2 is adjacent to loc_x4_y1, loc_x4_y2 is adjacent to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are adjacent, loc_x5_y0 is adjacent to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are adjacent, loc_x5_y1 is adjacent to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are adjacent, loc_x5_y2 is adjacent to loc_x5_y1, loc_x5_y2 is adjacent to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are adjacent, loc_x5_y4 and loc_x4_y4 are adjacent, the robot is positioned at loc_x1_y0, there is a link between loc_x0_y0 and loc_x0_y1, there is a link between loc_x0_y1 and loc_x1_y1, there is a link between loc_x0_y2 and loc_x0_y1, there is a link between loc_x0_y2 and loc_x1_y2, there is a link between loc_x0_y3 and loc_x0_y2, there is a link between loc_x0_y3 and loc_x0_y4, there is a link between loc_x1_y0 and loc_x2_y0, there is a link between loc_x1_y2 and loc_x0_y2, there is a link between loc_x1_y4 and loc_x0_y4, there is a link between loc_x2_y2 and loc_x2_y3, there is a link between loc_x2_y3 and loc_x3_y3, there is a link between loc_x3_y0 and loc_x2_y0, there is a link between loc_x3_y0 and loc_x3_y1, there is a link between loc_x3_y0 and loc_x4_y0, there is a link between loc_x3_y1 and loc_x2_y1, there is a link between loc_x3_y1 and loc_x3_y2, there is a link between loc_x3_y2 and loc_x3_y1, there is a link between loc_x4_y1 and loc_x3_y1, there is a link between loc_x4_y1 and loc_x4_y0, there is a link between loc_x4_y2 and loc_x3_y2, there is a link between loc_x4_y2 and loc_x5_y2, there is a link between loc_x4_y3 and loc_x3_y3, there is a link between loc_x4_y3 and loc_x4_y2, there is a link between loc_x4_y3 and loc_x4_y4, there is a link between loc_x4_y3 and loc_x5_y3, there is a link between loc_x4_y4 and loc_x3_y4, there is a link between loc_x4_y4 and loc_x5_y4, there is a link between loc_x5_y0 and loc_x4_y0, there is a link between loc_x5_y1 and loc_x5_y0, there is a link between loc_x5_y3 and loc_x4_y3, there is a link between loc_x5_y3 and loc_x5_y4, and there is a link between loc_x5_y4 and loc_x5_y3."}
{"question_id": "29e360dc-4d3f-4686-8884-1dd57a8fdbbb", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y1 from loc_x0_y2, robot moves from loc_x0_y1 to loc_x0_y0, moves to loc_x1_y0 from loc_x0_y0, from loc_x1_y0, the robot moves to loc_x1_y1, from loc_x1_y1, the robot moves to loc_x1_y2, robot moves from loc_x1_y2 to loc_x0_y2, from loc_x0_y2, the robot moves to loc_x0_y3, moves to loc_x0_y4 from loc_x0_y3, robot moves from loc_x0_y4 to loc_x0_y5, robot moves from loc_x0_y5 to loc_x1_y5, moves to loc_x1_y4 from loc_x1_y5, moves to loc_x1_y3 from loc_x1_y4, robot moves from loc_x1_y3 to loc_x2_y3, moves to loc_x2_y2 from loc_x2_y3, from loc_x2_y2, the robot moves to loc_x2_y1, robot moves from loc_x2_y1 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x3_y0, moves to loc_x3_y1 from loc_x3_y0 and robot moves from loc_x3_y1 to loc_x3_y2 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 413? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 and loc_x0_y4 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x2_y5, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot transitions from loc_x0_y2 to loc_x0_y1, then from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y0 to loc_x1_y0, then from loc_x1_y0 to loc_x1_y1, from loc_x1_y1 to loc_x1_y2, from loc_x1_y2 to loc_x0_y2, from loc_x0_y2 to loc_x0_y3, from loc_x0_y3 to loc_x0_y4, from loc_x0_y4 to loc_x0_y5, from loc_x0_y5 to loc_x1_y5, then from loc_x1_y5 to loc_x1_y4, from loc_x1_y4 to loc_x1_y3, from loc_x1_y3 to loc_x2_y3, from loc_x2_y3 to loc_x2_y2, from loc_x2_y2 to loc_x2_y1, from loc_x2_y1 to loc_x2_y0, from loc_x2_y0 to loc_x3_y0, from loc_x3_y0 to loc_x3_y1, and finally from loc_x3_y1 to loc_x3_y2, resulting in the current state. In this state, is the count of valid properties involving negations equal to 413? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 is also connected to loc_x0_y4, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is also connected to loc_x2_y1, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is also connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is also connected to loc_x2_y5, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is also connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is also connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is also connected to loc_x2_y5, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is also connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, the robot is currently at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2."}
{"question_id": "0a9f8657-5d6e-4911-8d5a-d1bc51d4e202", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y0 from loc_x1_y0, robot moves from loc_x0_y0 to loc_x0_y1, from loc_x0_y1, the robot moves to loc_x1_y1, robot moves from loc_x1_y1 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x3_y0, from loc_x3_y0, the robot moves to loc_x3_y1, robot moves from loc_x3_y1 to loc_x3_y2, robot moves from loc_x3_y2 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, robot moves from loc_x4_y0 to loc_x5_y0, moves to loc_x5_y1 from loc_x5_y0, moves to loc_x5_y2 from loc_x5_y1, moves from loc_x5_y2 to loc_x4_y2, from loc_x4_y2, the robot moves to loc_x4_y3, moves to loc_x5_y3 from loc_x4_y3, moves to loc_x5_y4 from loc_x5_y3 and moves from loc_x5_y4 to loc_x4_y4 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 846? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is visited, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y4 and loc_x4_y4 are connected, robot is located at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y2, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y0 and loc_x4_y0, there is a connection between loc_x5_y1 and loc_x5_y0, there is a connection between loc_x5_y3 and loc_x4_y3, there is a connection between loc_x5_y3 and loc_x5_y4 and there is a connection between loc_x5_y4 and loc_x5_y3.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot transitions from loc_x1_y0 to loc_x0_y0, then from loc_x0_y0 to loc_x0_y1, followed by a move from loc_x0_y1 to loc_x1_y1, then from loc_x1_y1 to loc_x2_y1, and from loc_x2_y1 to loc_x2_y0. Next, the robot moves from loc_x2_y0 to loc_x3_y0, then from loc_x3_y0 to loc_x3_y1, followed by a move from loc_x3_y1 to loc_x3_y2, then back to loc_x3_y1, and then to loc_x4_y1. The robot continues by moving from loc_x4_y1 to loc_x4_y0, then to loc_x5_y0, followed by a transition to loc_x5_y1, then to loc_x5_y2, and from loc_x5_y2 to loc_x4_y2. From loc_x4_y2, the robot moves to loc_x4_y3, then to loc_x5_y3, followed by a move to loc_x5_y4, and finally from loc_x5_y4 to loc_x4_y4, reaching the current state. In this state, is the number of valid properties of the state that involve negations equal to 846? True or False", "initial_state_nl_paraphrased": "Loc_x0_y0 is adjacent to loc_x1_y0, loc_x0_y1, and loc_x0_y2 are adjacent, loc_x0_y1 is adjacent to loc_x0_y0, loc_x0_y2, and loc_x0_y3 are adjacent, loc_x0_y4 is adjacent to loc_x0_y3, loc_x0_y4 is adjacent to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are adjacent, loc_x1_y0 and loc_x1_y1 are adjacent, loc_x1_y0 has been visited, loc_x1_y1 and loc_x2_y1 are adjacent, loc_x1_y1 is adjacent to loc_x0_y1, loc_x1_y1 is adjacent to loc_x1_y0, loc_x1_y1 is adjacent to loc_x1_y2, loc_x1_y2 is adjacent to loc_x1_y1, loc_x1_y2 is adjacent to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are adjacent, loc_x2_y0 is adjacent to loc_x1_y0, loc_x2_y0 is adjacent to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are adjacent, loc_x2_y1 and loc_x2_y2 are adjacent, loc_x2_y1 and loc_x3_y1 are adjacent, loc_x2_y1 is adjacent to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are adjacent, loc_x2_y2 is adjacent to loc_x1_y2, loc_x2_y2 is adjacent to loc_x2_y1, loc_x2_y3 is adjacent to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are adjacent, loc_x3_y1 is adjacent to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are adjacent, loc_x3_y2 is adjacent to loc_x2_y2, loc_x3_y2 is adjacent to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are adjacent, loc_x3_y3 and loc_x4_y3 are adjacent, loc_x3_y3 is adjacent to loc_x2_y3, loc_x3_y3 is adjacent to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are adjacent, loc_x3_y4 is adjacent to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are adjacent, loc_x4_y0 is adjacent to loc_x4_y1, loc_x4_y0 is adjacent to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are adjacent, loc_x4_y1 is adjacent to loc_x5_y1, loc_x4_y2 is adjacent to loc_x4_y1, loc_x4_y2 is adjacent to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are adjacent, loc_x5_y0 is adjacent to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are adjacent, loc_x5_y1 is adjacent to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are adjacent, loc_x5_y2 is adjacent to loc_x5_y1, loc_x5_y2 is adjacent to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are adjacent, loc_x5_y4 and loc_x4_y4 are adjacent, the robot is positioned at loc_x1_y0, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y1 and loc_x1_y1, a path exists between loc_x0_y2 and loc_x0_y1, a path exists between loc_x0_y2 and loc_x1_y2, a path exists between loc_x0_y3 and loc_x0_y2, a path exists between loc_x0_y3 and loc_x0_y4, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y2 and loc_x0_y2, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y0 and loc_x4_y0, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x4_y1 and loc_x3_y1, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, a path exists between loc_x4_y2 and loc_x5_y2, a path exists between loc_x4_y3 and loc_x3_y3, a path exists between loc_x4_y3 and loc_x4_y2, a path exists between loc_x4_y3 and loc_x4_y4, a path exists between loc_x4_y3 and loc_x5_y3, a path exists between loc_x4_y4 and loc_x3_y4, a path exists between loc_x4_y4 and loc_x5_y4, a path exists between loc_x5_y0 and loc_x4_y0, a path exists between loc_x5_y1 and loc_x5_y0, a path exists between loc_x5_y3 and loc_x4_y3, a path exists between loc_x5_y3 and loc_x5_y4, and a path exists between loc_x5_y4 and loc_x5_y3."}
{"question_id": "1e476b39-d907-4958-9583-5774ef04c873", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_3", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x1_y0, the robot moves to loc_x0_y0, robot moves from loc_x0_y0 to loc_x0_y1, robot moves from loc_x0_y1 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x2_y1, moves from loc_x2_y1 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, moves to loc_x3_y2 from loc_x3_y1, robot moves from loc_x3_y2 to loc_x3_y1 and from loc_x3_y1, the robot moves to loc_x4_y1 to reach the current state. In this state, is the number of inexecutable actions equal to 752? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is visited, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y4 and loc_x4_y4 are connected, robot is located at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y2, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y0 and loc_x4_y0, there is a connection between loc_x5_y1 and loc_x5_y0, there is a connection between loc_x5_y3 and loc_x4_y3, there is a connection between loc_x5_y3 and loc_x5_y4 and there is a connection between loc_x5_y4 and loc_x5_y3.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot starts at loc_x1_y0 and moves to loc_x0_y0, then to loc_x0_y1, followed by loc_x1_y1, then loc_x2_y1, then loc_x2_y0, then loc_x3_y0, then loc_x3_y1, then loc_x3_y2, then back to loc_x3_y1, and finally to loc_x4_y1 to reach the current state. In this state, is the number of inexecutable actions equal to 752? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x0_y0, loc_x0_y2 and loc_x0_y3, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y0 is visited, loc_x1_y1 and loc_x2_y1, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y2 and loc_x2_y2, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x2_y2, loc_x3_y0 and loc_x3_y1, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x3_y3, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x3_y2, loc_x3_y3 and loc_x4_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x3_y3, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y0 and loc_x5_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y1 and loc_x5_y1, loc_x4_y2 and loc_x4_y1, loc_x4_y2 and loc_x4_y3, loc_x4_y4 and loc_x4_y3, loc_x5_y0 and loc_x5_y1, loc_x5_y1 and loc_x4_y1, loc_x5_y1 and loc_x5_y2, loc_x5_y2 and loc_x4_y2, loc_x5_y2 and loc_x5_y1, loc_x5_y2 and loc_x5_y3, loc_x5_y3 and loc_x5_y2, loc_x5_y4 and loc_x4_y4, the robot is at loc_x1_y0, a connection exists between loc_x0_y0 and loc_x0_y1, a connection exists between loc_x0_y1 and loc_x1_y1, a connection exists between loc_x0_y2 and loc_x0_y1, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y3 and loc_x0_y2, a connection exists between loc_x0_y3 and loc_x0_y4, a connection exists between loc_x1_y0 and loc_x2_y0, a connection exists between loc_x1_y2 and loc_x0_y2, a connection exists between loc_x1_y4 and loc_x0_y4, a connection exists between loc_x2_y2 and loc_x2_y3, a connection exists between loc_x2_y3 and loc_x3_y3, a connection exists between loc_x3_y0 and loc_x2_y0, a connection exists between loc_x3_y0 and loc_x3_y1, a connection exists between loc_x3_y0 and loc_x4_y0, a connection exists between loc_x3_y1 and loc_x2_y1, a connection exists between loc_x3_y1 and loc_x3_y2, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x4_y1 and loc_x3_y1, a connection exists between loc_x4_y1 and loc_x4_y0, a connection exists between loc_x4_y2 and loc_x3_y2, a connection exists between loc_x4_y2 and loc_x5_y2, a connection exists between loc_x4_y3 and loc_x3_y3, a connection exists between loc_x4_y3 and loc_x4_y2, a connection exists between loc_x4_y3 and loc_x4_y4, a connection exists between loc_x4_y3 and loc_x5_y3, a connection exists between loc_x4_y4 and loc_x3_y4, a connection exists between loc_x4_y4 and loc_x5_y4, a connection exists between loc_x5_y0 and loc_x4_y0, a connection exists between loc_x5_y1 and loc_x5_y0, a connection exists between loc_x5_y3 and loc_x4_y3, a connection exists between loc_x5_y3 and loc_x5_y4, and a connection exists between loc_x5_y4 and loc_x5_y3."}
{"question_id": "ea0f13af-a146-4411-9a40-02aad84479e6", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x1_y0 to loc_x0_y0, robot moves from loc_x0_y0 to loc_x0_y1, robot moves from loc_x0_y1 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, moves to loc_x3_y1 from loc_x3_y0, robot moves from loc_x3_y1 to loc_x3_y2, moves from loc_x3_y2 to loc_x3_y1 and from loc_x3_y1, the robot moves to loc_x4_y1 to reach the current state. Is it True or False that the number of actions that led to current state in the sequence is equal to 10?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is visited, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y4 and loc_x4_y4 are connected, robot is located at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y2, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y0 and loc_x4_y0, there is a connection between loc_x5_y1 and loc_x5_y0, there is a connection between loc_x5_y3 and loc_x4_y3, there is a connection between loc_x5_y3 and loc_x5_y4 and there is a connection between loc_x5_y4 and loc_x5_y3.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: the robot transitions from loc_x1_y0 to loc_x0_y0, then from loc_x0_y0 to loc_x0_y1, followed by loc_x0_y1 to loc_x1_y1, then loc_x1_y1 to loc_x2_y1, loc_x2_y1 to loc_x2_y0, loc_x2_y0 to loc_x3_y0, loc_x3_y0 to loc_x3_y1, loc_x3_y1 to loc_x3_y2, loc_x3_y2 back to loc_x3_y1, and finally from loc_x3_y1 to loc_x4_y1 to attain the current state. Is it True or False that the total number of actions in this sequence that led to the current state is 10?", "initial_state_nl_paraphrased": "Loc_x0_y0 is adjacent to loc_x1_y0, loc_x0_y1, and loc_x0_y2 are adjacent, loc_x0_y1 is adjacent to loc_x0_y0, loc_x0_y2, and loc_x0_y3 are adjacent, loc_x0_y4 is adjacent to loc_x0_y3, loc_x0_y4 is adjacent to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are adjacent, loc_x1_y0 and loc_x1_y1 are adjacent, loc_x1_y0 has been visited, loc_x1_y1 and loc_x2_y1 are adjacent, loc_x1_y1 is adjacent to loc_x0_y1, loc_x1_y1 is adjacent to loc_x1_y0, loc_x1_y1 is adjacent to loc_x1_y2, loc_x1_y2 is adjacent to loc_x1_y1, loc_x1_y2 is adjacent to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are adjacent, loc_x2_y0 is adjacent to loc_x1_y0, loc_x2_y0 is adjacent to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are adjacent, loc_x2_y1 and loc_x2_y2 are adjacent, loc_x2_y1 and loc_x3_y1 are adjacent, loc_x2_y1 is adjacent to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are adjacent, loc_x2_y2 is adjacent to loc_x1_y2, loc_x2_y2 is adjacent to loc_x2_y1, loc_x2_y3 is adjacent to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are adjacent, loc_x3_y1 is adjacent to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are adjacent, loc_x3_y2 is adjacent to loc_x2_y2, loc_x3_y2 is adjacent to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are adjacent, loc_x3_y3 and loc_x4_y3 are adjacent, loc_x3_y3 is adjacent to loc_x2_y3, loc_x3_y3 is adjacent to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are adjacent, loc_x3_y4 is adjacent to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are adjacent, loc_x4_y0 is adjacent to loc_x4_y1, loc_x4_y0 is adjacent to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are adjacent, loc_x4_y1 is adjacent to loc_x5_y1, loc_x4_y2 is adjacent to loc_x4_y1, loc_x4_y2 is adjacent to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are adjacent, loc_x5_y0 is adjacent to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are adjacent, loc_x5_y1 is adjacent to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are adjacent, loc_x5_y2 is adjacent to loc_x5_y1, loc_x5_y2 is adjacent to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are adjacent, loc_x5_y4 and loc_x4_y4 are adjacent, the robot is positioned at loc_x1_y0, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y1 and loc_x1_y1, a path exists between loc_x0_y2 and loc_x0_y1, a path exists between loc_x0_y2 and loc_x1_y2, a path exists between loc_x0_y3 and loc_x0_y2, a path exists between loc_x0_y3 and loc_x0_y4, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y2 and loc_x0_y2, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y0 and loc_x4_y0, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x4_y1 and loc_x3_y1, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, a path exists between loc_x4_y2 and loc_x5_y2, a path exists between loc_x4_y3 and loc_x3_y3, a path exists between loc_x4_y3 and loc_x4_y2, a path exists between loc_x4_y3 and loc_x4_y4, a path exists between loc_x4_y3 and loc_x5_y3, a path exists between loc_x4_y4 and loc_x3_y4, a path exists between loc_x4_y4 and loc_x5_y4, a path exists between loc_x5_y0 and loc_x4_y0, a path exists between loc_x5_y1 and loc_x5_y0, a path exists between loc_x5_y3 and loc_x4_y3, a path exists between loc_x5_y3 and loc_x5_y4, and a path exists between loc_x5_y4 and loc_x5_y3."}
{"question_id": "9416c6bb-ea3e-4330-9559-ee307f6d46d8", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x4_y2 to loc_x3_y2, robot moves from loc_x3_y2 to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, from loc_x2_y1, the robot moves to loc_x3_y1, moves from loc_x3_y1 to loc_x4_y1, moves from loc_x4_y1 to loc_x4_y0, robot moves from loc_x4_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x2_y0, moves from loc_x2_y0 to loc_x1_y0, moves to loc_x0_y0 from loc_x1_y0, robot moves from loc_x0_y0 to loc_x0_y1, robot moves from loc_x0_y1 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x1_y2, robot moves from loc_x1_y2 to loc_x1_y3, from loc_x1_y3, the robot moves to loc_x0_y3, moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x1_y4, robot moves from loc_x1_y4 to loc_x2_y4 and robot moves from loc_x2_y4 to loc_x2_y3 to reach the current state. Is it True or False that the number of actions that led to current state in the sequence is equal to 19?", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x3_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 and loc_x4_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, robot is located at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x4_y1 and loc_x3_y1 and there is a connection between loc_x4_y4 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the robot undergoes the following sequence of movements: it moves from loc_x4_y2 to loc_x3_y2, then to loc_x2_y2, followed by loc_x2_y1, then loc_x3_y1, loc_x4_y1, loc_x4_y0, loc_x3_y0, loc_x2_y0, loc_x1_y0, and finally loc_x0_y0. From there, it proceeds to loc_x0_y1, loc_x1_y1, loc_x1_y2, loc_x1_y3, loc_x0_y3, loc_x0_y4, loc_x1_y4, loc_x2_y4, and loc_x2_y3 to reach its current state. The question is whether the total number of actions in this sequence that led to the current state is 19.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x3_y0, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x2_y3, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y0 and loc_x2_y0, loc_x3_y1 and loc_x3_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x3_y4 and loc_x4_y4, loc_x3_y4 and loc_x2_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, the robot is at loc_x4_y2. Additionally, connections exist between loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x1_y4, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x3_y4, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x4_y2, loc_x4_y1 and loc_x3_y1, and loc_x4_y4 and loc_x3_y4."}
{"question_id": "2e2f50ba-c98f-4398-a3d1-e7ec5c49c166", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x0_y2, the robot moves to loc_x0_y1, from loc_x0_y1, the robot moves to loc_x0_y0, from loc_x0_y0, the robot moves to loc_x1_y0, robot moves from loc_x1_y0 to loc_x1_y1, moves to loc_x1_y2 from loc_x1_y1, moves from loc_x1_y2 to loc_x0_y2, robot moves from loc_x0_y2 to loc_x0_y3, from loc_x0_y3, the robot moves to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y5 and moves to loc_x1_y5 from loc_x0_y5 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 66? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 and loc_x0_y4 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x2_y5, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot transitions from loc_x0_y2 to loc_x0_y1, then from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y0 to loc_x1_y0, then to loc_x1_y1, next to loc_x1_y2, and subsequently back to loc_x0_y2. The robot continues by moving from loc_x0_y2 to loc_x0_y3, then to loc_x0_y4, and from loc_x0_y4 to loc_x0_y5, before finally reaching loc_x1_y5 from loc_x0_y5. In this resulting state, is the count of valid properties that do not involve negations equal to 66? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 is also connected to loc_x0_y4, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is also connected to loc_x2_y1, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is also connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is also connected to loc_x2_y5, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is also connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is also connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is also connected to loc_x2_y5, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is also connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, the robot is currently at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2."}
{"question_id": "49ec1e6a-36a9-4db5-ba6a-cbc299384860", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y0 from loc_x1_y0, moves to loc_x0_y1 from loc_x0_y0, moves to loc_x1_y1 from loc_x0_y1, moves to loc_x2_y1 from loc_x1_y1, moves from loc_x2_y1 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x3_y0, from loc_x3_y0, the robot moves to loc_x3_y1, robot moves from loc_x3_y1 to loc_x3_y2, robot moves from loc_x3_y2 to loc_x3_y1 and robot moves from loc_x3_y1 to loc_x4_y1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 658? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is visited, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y4 and loc_x4_y4 are connected, robot is located at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y2, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y0 and loc_x4_y0, there is a connection between loc_x5_y1 and loc_x5_y0, there is a connection between loc_x5_y3 and loc_x4_y3, there is a connection between loc_x5_y3 and loc_x5_y4 and there is a connection between loc_x5_y4 and loc_x5_y3.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot transitions from loc_x1_y0 to loc_x0_y0, then from loc_x0_y0 to loc_x0_y1, followed by a move from loc_x0_y1 to loc_x1_y1, then from loc_x1_y1 to loc_x2_y1, and subsequently from loc_x2_y1 to loc_x2_y0. From loc_x2_y0, the robot proceeds to loc_x3_y0, then to loc_x3_y1, followed by a move to loc_x3_y2, then back to loc_x3_y1, and finally from loc_x3_y1 to loc_x4_y1, resulting in the current state. In this state, is the count of valid properties that include negations equal to 658? True or False", "initial_state_nl_paraphrased": "Loc_x0_y0 is adjacent to loc_x1_y0, loc_x0_y1, and loc_x0_y2 are adjacent, loc_x0_y1 is adjacent to loc_x0_y0, loc_x0_y2, and loc_x0_y3 are adjacent, loc_x0_y4 is adjacent to loc_x0_y3, loc_x0_y4 is adjacent to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are adjacent, loc_x1_y0 and loc_x1_y1 are adjacent, loc_x1_y0 has been visited, loc_x1_y1 and loc_x2_y1 are adjacent, loc_x1_y1 is adjacent to loc_x0_y1, loc_x1_y1 is adjacent to loc_x1_y0, loc_x1_y1 is adjacent to loc_x1_y2, loc_x1_y2 is adjacent to loc_x1_y1, loc_x1_y2 is adjacent to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are adjacent, loc_x2_y0 is adjacent to loc_x1_y0, loc_x2_y0 is adjacent to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are adjacent, loc_x2_y1 and loc_x2_y2 are adjacent, loc_x2_y1 and loc_x3_y1 are adjacent, loc_x2_y1 is adjacent to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are adjacent, loc_x2_y2 is adjacent to loc_x1_y2, loc_x2_y2 is adjacent to loc_x2_y1, loc_x2_y3 is adjacent to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are adjacent, loc_x3_y1 is adjacent to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are adjacent, loc_x3_y2 is adjacent to loc_x2_y2, loc_x3_y2 is adjacent to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are adjacent, loc_x3_y3 and loc_x4_y3 are adjacent, loc_x3_y3 is adjacent to loc_x2_y3, loc_x3_y3 is adjacent to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are adjacent, loc_x3_y4 is adjacent to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are adjacent, loc_x4_y0 is adjacent to loc_x4_y1, loc_x4_y0 is adjacent to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are adjacent, loc_x4_y1 is adjacent to loc_x5_y1, loc_x4_y2 is adjacent to loc_x4_y1, loc_x4_y2 is adjacent to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are adjacent, loc_x5_y0 is adjacent to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are adjacent, loc_x5_y1 is adjacent to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are adjacent, loc_x5_y2 is adjacent to loc_x5_y1, loc_x5_y2 is adjacent to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are adjacent, loc_x5_y4 and loc_x4_y4 are adjacent, the robot is positioned at loc_x1_y0, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y1 and loc_x1_y1, a path exists between loc_x0_y2 and loc_x0_y1, a path exists between loc_x0_y2 and loc_x1_y2, a path exists between loc_x0_y3 and loc_x0_y2, a path exists between loc_x0_y3 and loc_x0_y4, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y2 and loc_x0_y2, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y0 and loc_x4_y0, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x4_y1 and loc_x3_y1, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, a path exists between loc_x4_y2 and loc_x5_y2, a path exists between loc_x4_y3 and loc_x3_y3, a path exists between loc_x4_y3 and loc_x4_y2, a path exists between loc_x4_y3 and loc_x4_y4, a path exists between loc_x4_y3 and loc_x5_y3, a path exists between loc_x4_y4 and loc_x3_y4, a path exists between loc_x4_y4 and loc_x5_y4, a path exists between loc_x5_y0 and loc_x4_y0, a path exists between loc_x5_y1 and loc_x5_y0, a path exists between loc_x5_y3 and loc_x4_y3, a path exists between loc_x5_y3 and loc_x5_y4, and a path exists between loc_x5_y4 and loc_x5_y3."}
{"question_id": "dc11a189-ee4f-41c4-ad4e-26e219759423", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y3 to loc_x0_y4, from loc_x0_y4, the robot moves to loc_x0_y3, moves to loc_x1_y3 from loc_x0_y3, moves to loc_x1_y4 from loc_x1_y3, moves to loc_x2_y4 from loc_x1_y4, from loc_x2_y4, the robot moves to loc_x3_y4, robot moves from loc_x3_y4 to loc_x3_y3, moves from loc_x3_y3 to loc_x2_y3, robot moves from loc_x2_y3 to loc_x2_y2 and robot moves from loc_x2_y2 to loc_x2_y1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 78? True or False", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 is visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the robot undergoes the following sequence of actions: it moves from loc_x0_y3 to loc_x0_y4, then back to loc_x0_y3, followed by a move to loc_x1_y3, then to loc_x1_y4, then to loc_x2_y4, and from there to loc_x3_y4. The robot then moves to loc_x3_y3, followed by loc_x2_y3, then loc_x2_y2, and finally to loc_x2_y1, resulting in the current state. In this state, is the number of valid properties that do not involve negations equal to 78? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is connected to both loc_x0_y3 and loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is also connected to loc_x0_y0 and loc_x1_y1, loc_x1_y1 is connected to both loc_x0_y1 and loc_x2_y1, loc_x1_y3 is connected to loc_x1_y4 and loc_x2_y3, loc_x1_y3 is also connected to loc_x0_y3, loc_x1_y4 is connected to loc_x0_y4 and loc_x1_y3, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to both loc_x2_y2 and loc_x1_y3, loc_x2_y4 is connected to loc_x2_y3 and loc_x3_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is also connected to loc_x3_y1 and loc_x4_y0, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is also connected to loc_x2_y1 and loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is also connected to loc_x3_y1 and loc_x3_y3 and loc_x4_y2, loc_x3_y3 is connected to loc_x3_y2 and loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y1 is connected to loc_x4_y0 and loc_x4_y2, loc_x4_y1 is also connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1 and loc_x3_y2, the robot is currently at loc_x0_y3, connections exist between loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x1_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y1 and loc_x4_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "84c0c555-e355-4e26-8df0-bfbe528c8a42", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x0_y3, the robot moves to loc_x0_y4 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 66? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 is visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: from loc_x0_y3, the robot moves to loc_x0_y4 to reach the current state. In this state, does the number of valid properties of the state that do not involve negations equal 66? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is connected to both loc_x0_y3 and loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is also connected to loc_x0_y0 and loc_x1_y1, loc_x1_y1 is connected to both loc_x0_y1 and loc_x2_y1, loc_x1_y3 is connected to loc_x1_y4 and loc_x2_y3, loc_x1_y3 is also connected to loc_x0_y3, loc_x1_y4 is connected to loc_x0_y4 and loc_x1_y3, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to loc_x2_y2 and loc_x1_y3, loc_x2_y4 is connected to loc_x2_y3 and loc_x3_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is also connected to loc_x3_y1 and loc_x4_y0, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is also connected to loc_x2_y1 and loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is also connected to loc_x3_y1 and loc_x3_y3 and loc_x4_y2, loc_x3_y3 is connected to loc_x3_y2 and loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4 and loc_x3_y3, loc_x4_y1 is connected to loc_x4_y0 and loc_x4_y2, loc_x4_y1 is also connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1 and loc_x3_y2, the robot is currently at loc_x0_y3, and the following connections exist: loc_x0_y0 and loc_x0_y1, loc_x0_y3 and loc_x1_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y1 and loc_x4_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "90a84ed8-9ccd-4620-a0fa-f2170157ef85", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y3 to loc_x0_y4, moves from loc_x0_y4 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x1_y3, from loc_x1_y3, the robot moves to loc_x1_y4, moves from loc_x1_y4 to loc_x2_y4, robot moves from loc_x2_y4 to loc_x3_y4, moves from loc_x3_y4 to loc_x3_y3, moves from loc_x3_y3 to loc_x2_y3, robot moves from loc_x2_y3 to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, from loc_x2_y1, the robot moves to loc_x1_y1, moves to loc_x0_y1 from loc_x1_y1, from loc_x0_y1, the robot moves to loc_x0_y0, moves to loc_x1_y0 from loc_x0_y0, from loc_x1_y0, the robot moves to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, from loc_x3_y1, the robot moves to loc_x3_y2 and moves from loc_x3_y2 to loc_x4_y2 to reach the current state. In this state, is the number of inexecutable actions equal to 465? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 is visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the robot performs the following sequence of actions: it moves from loc_x0_y3 to loc_x0_y4 and then back to loc_x0_y3, then proceeds from loc_x0_y3 to loc_x1_y3, followed by a move to loc_x1_y4, then to loc_x2_y4, and subsequently to loc_x3_y4, loc_x3_y3, and loc_x2_y3. The robot then moves to loc_x2_y2, then to loc_x2_y1, and from there to loc_x1_y1, followed by a move to loc_x0_y1, then to loc_x0_y0, and then to loc_x1_y0, loc_x2_y0, and loc_x3_y0. Finally, the robot moves from loc_x3_y0 to loc_x3_y1, then to loc_x3_y2, and lastly to loc_x4_y2, reaching the current state. In this state, is the number of inexecutable actions equal to 465? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is connected to both loc_x0_y3 and loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is also connected to loc_x0_y0 and loc_x1_y1, loc_x1_y1 is connected to both loc_x0_y1 and loc_x2_y1, loc_x1_y3 is connected to loc_x1_y4 and loc_x2_y3, loc_x1_y3 is also connected to loc_x0_y3, loc_x1_y4 is connected to loc_x0_y4 and loc_x1_y3, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to both loc_x2_y2 and loc_x2_y4, loc_x2_y3 is also connected to loc_x1_y3, loc_x2_y4 is connected to loc_x2_y3 and loc_x3_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is also connected to loc_x3_y1 and loc_x4_y0, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is also connected to loc_x2_y1 and loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is also connected to loc_x3_y1 and loc_x3_y3, and loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 is connected to loc_x3_y2 and loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is also connected to loc_x4_y2, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, the robot is currently at loc_x0_y3, and the following connections exist: loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x1_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y1 and loc_x4_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "22c8ba5e-cfd5-46c7-bb66-aca64bd576c1", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 812? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is visited, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y4 and loc_x4_y4 are connected, robot is located at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y2, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y0 and loc_x4_y0, there is a connection between loc_x5_y1 and loc_x5_y0, there is a connection between loc_x5_y3 and loc_x4_y3, there is a connection between loc_x5_y3 and loc_x5_y4 and there is a connection between loc_x5_y4 and loc_x5_y3.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the robot executes the following actions: it moves from loc_x1_y0 to loc_x0_y0 to attain the current state. In this state, does the count of valid properties involving negations equal 812? True or False", "initial_state_nl_paraphrased": "Loc_x0_y0 is adjacent to loc_x1_y0, loc_x0_y1, and loc_x0_y2 are adjacent, loc_x0_y1 is adjacent to loc_x0_y0, loc_x0_y2, and loc_x0_y3 are adjacent, loc_x0_y4 is adjacent to loc_x0_y3, loc_x0_y4 is adjacent to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are adjacent, loc_x1_y0 and loc_x1_y1 are adjacent, loc_x1_y0 has been visited, loc_x1_y1 and loc_x2_y1 are adjacent, loc_x1_y1 is adjacent to loc_x0_y1, loc_x1_y1 is adjacent to loc_x1_y0, loc_x1_y1 is adjacent to loc_x1_y2, loc_x1_y2 is adjacent to loc_x1_y1, loc_x1_y2 is adjacent to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are adjacent, loc_x2_y0 is adjacent to loc_x1_y0, loc_x2_y0 is adjacent to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are adjacent, loc_x2_y1 and loc_x2_y2 are adjacent, loc_x2_y1 and loc_x3_y1 are adjacent, loc_x2_y1 is adjacent to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are adjacent, loc_x2_y2 is adjacent to loc_x1_y2, loc_x2_y2 is adjacent to loc_x2_y1, loc_x2_y3 is adjacent to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are adjacent, loc_x3_y1 is adjacent to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are adjacent, loc_x3_y2 is adjacent to loc_x2_y2, loc_x3_y2 is adjacent to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are adjacent, loc_x3_y3 and loc_x4_y3 are adjacent, loc_x3_y3 is adjacent to loc_x2_y3, loc_x3_y3 is adjacent to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are adjacent, loc_x3_y4 is adjacent to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are adjacent, loc_x4_y0 is adjacent to loc_x4_y1, loc_x4_y0 is adjacent to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are adjacent, loc_x4_y1 is adjacent to loc_x5_y1, loc_x4_y2 is adjacent to loc_x4_y1, loc_x4_y2 is adjacent to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are adjacent, loc_x5_y0 is adjacent to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are adjacent, loc_x5_y1 is adjacent to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are adjacent, loc_x5_y2 is adjacent to loc_x5_y1, loc_x5_y2 is adjacent to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are adjacent, loc_x5_y4 and loc_x4_y4 are adjacent, the robot is positioned at loc_x1_y0, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y1 and loc_x1_y1, a path exists between loc_x0_y2 and loc_x0_y1, a path exists between loc_x0_y2 and loc_x1_y2, a path exists between loc_x0_y3 and loc_x0_y2, a path exists between loc_x0_y3 and loc_x0_y4, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y2 and loc_x0_y2, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y0 and loc_x4_y0, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x4_y1 and loc_x3_y1, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, a path exists between loc_x4_y2 and loc_x5_y2, a path exists between loc_x4_y3 and loc_x3_y3, a path exists between loc_x4_y3 and loc_x4_y2, a path exists between loc_x4_y3 and loc_x4_y4, a path exists between loc_x4_y3 and loc_x5_y3, a path exists between loc_x4_y4 and loc_x3_y4, a path exists between loc_x4_y4 and loc_x5_y4, a path exists between loc_x5_y0 and loc_x4_y0, a path exists between loc_x5_y1 and loc_x5_y0, a path exists between loc_x5_y3 and loc_x4_y3, a path exists between loc_x5_y3 and loc_x5_y4, and a path exists between loc_x5_y4 and loc_x5_y3."}
{"question_id": "fe4e05f7-3744-41a4-9588-72546775dccf", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_11", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y3 to loc_x0_y4 to reach the current state. In this state, is the number of valid properties of the state (both with and without negations) equal to 462? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 is visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the robot executes the following actions: it moves from loc_x0_y3 to loc_x0_y4, resulting in the current state. In this state, does the total number of valid properties (including both affirmative and negated properties) equal 462? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is connected to both loc_x0_y3 and loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is also connected to loc_x0_y0 and loc_x1_y1, loc_x1_y1 is connected to both loc_x0_y1 and loc_x2_y1, loc_x1_y3 is connected to loc_x1_y4 and loc_x2_y3, loc_x1_y3 is also connected to loc_x0_y3, loc_x1_y4 is connected to loc_x0_y4 and loc_x1_y3, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to loc_x1_y3 and loc_x2_y2, loc_x2_y4 is connected to loc_x2_y3 and loc_x3_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is also connected to loc_x3_y1 and loc_x4_y0, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is also connected to loc_x2_y1 and loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is also connected to loc_x3_y1 and loc_x3_y3 and loc_x4_y2, loc_x3_y3 is connected to loc_x3_y2 and loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y1 is connected to loc_x4_y0 and loc_x4_y2, loc_x4_y1 is also connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1 and loc_x3_y2, the robot is currently at loc_x0_y3, and the following connections exist: loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x1_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y1 and loc_x4_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "13a5b859-ff2b-4475-93fd-d766bbc008da", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y1 from loc_x0_y2, robot moves from loc_x0_y1 to loc_x0_y0, robot moves from loc_x0_y0 to loc_x1_y0, robot moves from loc_x1_y0 to loc_x1_y1, from loc_x1_y1, the robot moves to loc_x1_y2, moves to loc_x0_y2 from loc_x1_y2, moves to loc_x0_y3 from loc_x0_y2, robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y5 and moves from loc_x0_y5 to loc_x1_y5 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 83? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 and loc_x0_y4 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x2_y5, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are taken: the robot relocates from loc_x0_y2 to loc_x0_y1, then from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y0 to loc_x1_y0, then to loc_x1_y1, and subsequently from loc_x1_y1 to loc_x1_y2. The robot then moves from loc_x1_y2 back to loc_x0_y2, then to loc_x0_y3, followed by a move from loc_x0_y3 to loc_x0_y4, then to loc_x0_y5, and finally from loc_x0_y5 to loc_x1_y5 to attain the current state. In this state, is the count of valid state properties without negations equal to 83? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 is also connected to loc_x0_y4, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is also connected to loc_x2_y1, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is also connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is also connected to loc_x2_y5, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is also connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is also connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is also connected to loc_x2_y5, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is also connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, the robot is currently at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2."}
{"question_id": "f7d2444b-c5b0-4525-bf03-e99913302d94", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y3 to loc_x0_y4, moves to loc_x0_y3 from loc_x0_y4, from loc_x0_y3, the robot moves to loc_x1_y3, robot moves from loc_x1_y3 to loc_x1_y4, moves from loc_x1_y4 to loc_x2_y4, from loc_x2_y4, the robot moves to loc_x3_y4, moves from loc_x3_y4 to loc_x3_y3, moves to loc_x2_y3 from loc_x3_y3, moves to loc_x2_y2 from loc_x2_y3, moves to loc_x2_y1 from loc_x2_y2, moves from loc_x2_y1 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x0_y1, from loc_x0_y1, the robot moves to loc_x0_y0, robot moves from loc_x0_y0 to loc_x1_y0, moves to loc_x2_y0 from loc_x1_y0, moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, moves from loc_x3_y1 to loc_x3_y2 and robot moves from loc_x3_y2 to loc_x4_y2 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 382? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 is visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the robot undergoes the following sequence of actions: it moves from loc_x0_y3 to loc_x0_y4 and then back to loc_x0_y3, then proceeds to loc_x1_y3, followed by loc_x1_y4, loc_x2_y4, loc_x3_y4, loc_x3_y3, loc_x2_y3, loc_x2_y2, loc_x2_y1, loc_x1_y1, loc_x0_y1, loc_x0_y0, loc_x1_y0, loc_x2_y0, loc_x3_y0, loc_x3_y1, loc_x3_y2, and finally loc_x4_y2 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 382? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is connected to both loc_x0_y3 and loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is also connected to loc_x0_y0 and loc_x1_y1, loc_x1_y1 is connected to both loc_x0_y1 and loc_x2_y1, loc_x1_y3 is connected to loc_x1_y4 and loc_x2_y3, loc_x1_y3 is also connected to loc_x0_y3, loc_x1_y4 is connected to loc_x0_y4 and loc_x1_y3, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to loc_x1_y3 and loc_x2_y2, loc_x2_y4 is connected to loc_x2_y3 and loc_x3_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is also connected to loc_x3_y1 and loc_x4_y0, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is also connected to loc_x2_y1 and loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is also connected to loc_x3_y1 and loc_x3_y3 and loc_x4_y2, loc_x3_y3 is connected to loc_x3_y2 and loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y1 is connected to loc_x4_y0 and loc_x4_y2, loc_x4_y1 is also connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1 and loc_x3_y2, the robot's current location is loc_x0_y3, and the following connections exist: loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x1_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y1 and loc_x4_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "5697131d-0135-4580-8e66-730ff4fb14ee", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x3_y2 from loc_x4_y2 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 81? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x3_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 and loc_x4_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, robot is located at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x4_y1 and loc_x3_y1 and there is a connection between loc_x4_y4 and loc_x3_y4.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the following actions are taken: a move is made from loc_x4_y2 to loc_x3_y2 to achieve the current state. In this state, is the count of valid properties that do not include negations equal to 81? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x3_y0, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x2_y3, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y0 and loc_x2_y0, loc_x3_y1 and loc_x3_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x3_y4 and loc_x4_y4, loc_x3_y4 and loc_x2_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, the robot is at loc_x4_y2. Additionally, connections exist between loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x1_y4, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x3_y4, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x4_y2, loc_x4_y1 and loc_x3_y1, and loc_x4_y4 and loc_x3_y4."}
{"question_id": "80ea876f-29e6-445b-a76e-52786ffcd04b", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x1_y0 to loc_x0_y0, robot moves from loc_x0_y0 to loc_x0_y1, moves from loc_x0_y1 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x3_y0, moves to loc_x3_y1 from loc_x3_y0, robot moves from loc_x3_y1 to loc_x3_y2, robot moves from loc_x3_y2 to loc_x3_y1, moves from loc_x3_y1 to loc_x4_y1, moves from loc_x4_y1 to loc_x4_y0, robot moves from loc_x4_y0 to loc_x5_y0, from loc_x5_y0, the robot moves to loc_x5_y1, moves to loc_x5_y2 from loc_x5_y1, from loc_x5_y2, the robot moves to loc_x4_y2, robot moves from loc_x4_y2 to loc_x4_y3, moves to loc_x5_y3 from loc_x4_y3, from loc_x5_y3, the robot moves to loc_x5_y4 and robot moves from loc_x5_y4 to loc_x4_y4 to reach the current state. In this state, is the number of executable actions equal to 3? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is visited, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y4 and loc_x4_y4 are connected, robot is located at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y2, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y0 and loc_x4_y0, there is a connection between loc_x5_y1 and loc_x5_y0, there is a connection between loc_x5_y3 and loc_x4_y3, there is a connection between loc_x5_y3 and loc_x5_y4 and there is a connection between loc_x5_y4 and loc_x5_y3.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the robot undergoes the following sequence of movements: it moves from loc_x1_y0 to loc_x0_y0, then to loc_x0_y1, followed by loc_x1_y1, then loc_x2_y1, and loc_x2_y0. From loc_x2_y0, the robot proceeds to loc_x3_y0, then to loc_x3_y1, loc_x3_y2, and back to loc_x3_y1. It then moves to loc_x4_y1, followed by loc_x4_y0, loc_x5_y0, loc_x5_y1, and loc_x5_y2. The robot then moves to loc_x4_y2, loc_x4_y3, loc_x5_y3, loc_x5_y4, and finally to loc_x4_y4 to reach the current state. In this state, is the number of executable actions equal to 3? True or False", "initial_state_nl_paraphrased": "Loc_x0_y0 is adjacent to loc_x1_y0, loc_x0_y1, and loc_x0_y2 are adjacent, loc_x0_y1 is adjacent to loc_x0_y0, loc_x0_y2, and loc_x0_y3 are adjacent, loc_x0_y4 is adjacent to loc_x0_y3, loc_x0_y4 is adjacent to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are adjacent, loc_x1_y0 and loc_x1_y1 are adjacent, loc_x1_y0 has been visited, loc_x1_y1 and loc_x2_y1 are adjacent, loc_x1_y1 is adjacent to loc_x0_y1, loc_x1_y1 is adjacent to loc_x1_y0, loc_x1_y1 is adjacent to loc_x1_y2, loc_x1_y2 is adjacent to loc_x1_y1, loc_x1_y2 is adjacent to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are adjacent, loc_x2_y0 is adjacent to loc_x1_y0, loc_x2_y0 is adjacent to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are adjacent, loc_x2_y1 and loc_x2_y2 are adjacent, loc_x2_y1 and loc_x3_y1 are adjacent, loc_x2_y1 is adjacent to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are adjacent, loc_x2_y2 is adjacent to loc_x1_y2, loc_x2_y2 is adjacent to loc_x2_y1, loc_x2_y3 is adjacent to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are adjacent, loc_x3_y1 is adjacent to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are adjacent, loc_x3_y2 is adjacent to loc_x2_y2, loc_x3_y2 is adjacent to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are adjacent, loc_x3_y3 and loc_x4_y3 are adjacent, loc_x3_y3 is adjacent to loc_x2_y3, loc_x3_y3 is adjacent to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are adjacent, loc_x3_y4 is adjacent to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are adjacent, loc_x4_y0 is adjacent to loc_x4_y1, loc_x4_y0 is adjacent to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are adjacent, loc_x4_y1 is adjacent to loc_x5_y1, loc_x4_y2 is adjacent to loc_x4_y1, loc_x4_y2 is adjacent to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are adjacent, loc_x5_y0 is adjacent to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are adjacent, loc_x5_y1 is adjacent to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are adjacent, loc_x5_y2 is adjacent to loc_x5_y1, loc_x5_y2 is adjacent to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are adjacent, loc_x5_y4 and loc_x4_y4 are adjacent, the robot is positioned at loc_x1_y0, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y1 and loc_x1_y1, a path exists between loc_x0_y2 and loc_x0_y1, a path exists between loc_x0_y2 and loc_x1_y2, a path exists between loc_x0_y3 and loc_x0_y2, a path exists between loc_x0_y3 and loc_x0_y4, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y2 and loc_x0_y2, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y0 and loc_x4_y0, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x4_y1 and loc_x3_y1, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, a path exists between loc_x4_y2 and loc_x5_y2, a path exists between loc_x4_y3 and loc_x3_y3, a path exists between loc_x4_y3 and loc_x4_y2, a path exists between loc_x4_y3 and loc_x4_y4, a path exists between loc_x4_y3 and loc_x5_y3, a path exists between loc_x4_y4 and loc_x3_y4, a path exists between loc_x4_y4 and loc_x5_y4, a path exists between loc_x5_y0 and loc_x4_y0, a path exists between loc_x5_y1 and loc_x5_y0, a path exists between loc_x5_y3 and loc_x4_y3, a path exists between loc_x5_y3 and loc_x5_y4, and a path exists between loc_x5_y4 and loc_x5_y3."}
{"question_id": "0c0c32a5-6486-4089-8dc2-8ea430e1c1a0", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x0_y2, the robot moves to loc_x0_y1 to reach the current state. In this state, is the number of executable actions equal to 3? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 and loc_x0_y4 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x2_y5, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Given the initial condition, the following actions are performed: from loc_x0_y2, the robot moves to loc_x0_y1 to reach the current state. In this state, is the number of executable actions equal to 3? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 is also connected to loc_x0_y4, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is also connected to loc_x2_y1, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is also connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is also connected to loc_x2_y5, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is also connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is also connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is also connected to loc_x2_y5, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is also connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, the robot is currently at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2."}
{"question_id": "3f29fe6b-c812-49af-b578-8da51b6d5c56", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_4", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y4 from loc_x0_y3 to reach the current state. In this state, is the number of inexecutable actions equal to 494? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 is visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the following actions are taken: a move is made from loc_x0_y3 to loc_x0_y4 to achieve the current state. In this state, is the count of inexecutable actions 494? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is connected to both loc_x0_y3 and loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is also connected to loc_x0_y0 and loc_x1_y1, loc_x1_y1 is connected to both loc_x0_y1 and loc_x2_y1, loc_x1_y3 is connected to loc_x1_y4 and loc_x2_y3, loc_x1_y3 is also connected to loc_x0_y3, loc_x1_y4 is connected to loc_x0_y4 and loc_x1_y3, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to both loc_x2_y2 and loc_x1_y3, loc_x2_y4 is connected to loc_x2_y3 and loc_x3_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is also connected to loc_x3_y1 and loc_x4_y0, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is also connected to loc_x2_y1 and loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is also connected to loc_x3_y1 and loc_x3_y3, and loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 is connected to loc_x3_y2 and loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is also connected to loc_x4_y2 and loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, the robot's current location is loc_x0_y3, and the following connections exist: loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x1_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y1 and loc_x4_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "bce49ef3-ca2f-4625-ab5e-11cd2f32f5be", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x4_y2 to loc_x3_y2 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 481? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x3_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 and loc_x4_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, robot is located at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x4_y1 and loc_x3_y1 and there is a connection between loc_x4_y4 and loc_x3_y4.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the robot executes the following actions: it moves from loc_x4_y2 to loc_x3_y2, resulting in the current state. In this state, does the count of valid properties that include negations equal 481? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y1 is connected to both loc_x0_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to both loc_x1_y0 and loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to both loc_x1_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 is connected to both loc_x1_y3 and loc_x2_y4, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to both loc_x2_y2 and loc_x3_y1, loc_x3_y3 is connected to both loc_x2_y3 and loc_x3_y4, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 and loc_x4_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 and loc_x4_y1 are connected, loc_x4_y2 is visited, the robot is at loc_x4_y2."}
{"question_id": "2e3ecbbe-2a94-4ed8-9860-c014db2e413e", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y4 from loc_x0_y3, robot moves from loc_x0_y4 to loc_x0_y3, moves from loc_x0_y3 to loc_x1_y3, from loc_x1_y3, the robot moves to loc_x1_y4, moves from loc_x1_y4 to loc_x2_y4, robot moves from loc_x2_y4 to loc_x3_y4, moves to loc_x3_y3 from loc_x3_y4, from loc_x3_y3, the robot moves to loc_x2_y3, robot moves from loc_x2_y3 to loc_x2_y2 and robot moves from loc_x2_y2 to loc_x2_y1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 391? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 is visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot transitions from loc_x0_y3 to loc_x0_y4, then back to loc_x0_y3, followed by a move to loc_x1_y3, then to loc_x1_y4, then to loc_x2_y4, and then to loc_x3_y4. From loc_x3_y4, the robot moves to loc_x3_y3, then to loc_x2_y3, then to loc_x2_y2, and finally to loc_x2_y1, resulting in the current state. In this state, is the count of valid properties involving negations equal to 391? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is connected to both loc_x0_y3 and loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is also connected to loc_x0_y0 and loc_x1_y1, loc_x1_y1 is connected to both loc_x0_y1 and loc_x2_y1, loc_x1_y3 is connected to loc_x1_y4 and loc_x2_y3, loc_x1_y3 is also connected to loc_x0_y3, loc_x1_y4 is connected to loc_x0_y4 and loc_x1_y3, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to loc_x2_y2 and loc_x1_y3, loc_x2_y4 is connected to loc_x2_y3 and loc_x3_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is also connected to loc_x3_y1 and loc_x4_y0, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is also connected to loc_x2_y1 and loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is also connected to loc_x3_y1 and loc_x3_y3 and loc_x4_y2, loc_x3_y3 is connected to loc_x3_y2 and loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4 and loc_x3_y3, loc_x4_y1 is connected to loc_x4_y0 and loc_x4_y2, loc_x4_y1 is also connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1 and loc_x3_y2, the robot's current location is loc_x0_y3, and the following connections exist: loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x1_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y1 and loc_x4_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "7d0aad6b-b8e3-4515-9792-c00e4cbea8d5", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x1_y3, robot moves from loc_x1_y3 to loc_x1_y4, moves from loc_x1_y4 to loc_x2_y4, moves to loc_x3_y4 from loc_x2_y4, moves from loc_x3_y4 to loc_x3_y3, robot moves from loc_x3_y3 to loc_x2_y3, moves to loc_x2_y2 from loc_x2_y3, from loc_x2_y2, the robot moves to loc_x2_y1, moves to loc_x1_y1 from loc_x2_y1, moves to loc_x0_y1 from loc_x1_y1, robot moves from loc_x0_y1 to loc_x0_y0, moves to loc_x1_y0 from loc_x0_y0, moves from loc_x1_y0 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, moves to loc_x3_y2 from loc_x3_y1 and moves from loc_x3_y2 to loc_x4_y2 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 78? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 is visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the robot undergoes the following sequence of movements: it moves from loc_x0_y3 to loc_x0_y4, then back to loc_x0_y3, followed by a move to loc_x1_y3, then to loc_x1_y4, then to loc_x2_y4, then to loc_x3_y4, and then to loc_x3_y3. The robot then moves to loc_x2_y3, then to loc_x2_y2, and then to loc_x2_y1. From there, it moves to loc_x1_y1, then to loc_x0_y1, then to loc_x0_y0, then to loc_x1_y0, then to loc_x2_y0, and then to loc_x3_y0. The robot then moves to loc_x3_y1, then to loc_x3_y2, and finally to loc_x4_y2, reaching the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 78? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is connected to both loc_x0_y3 and loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is also connected to loc_x0_y0 and loc_x1_y1, loc_x1_y1 is connected to both loc_x0_y1 and loc_x2_y1, loc_x1_y3 is connected to loc_x1_y4 and loc_x2_y3, loc_x1_y3 is also connected to loc_x0_y3, loc_x1_y4 is connected to loc_x0_y4 and loc_x1_y3, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to loc_x2_y2 and loc_x1_y3, loc_x2_y4 is connected to loc_x2_y3 and loc_x3_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is also connected to loc_x3_y1 and loc_x4_y0, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is also connected to loc_x2_y1 and loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is also connected to loc_x3_y1 and loc_x3_y3 and loc_x4_y2, loc_x3_y3 is connected to loc_x3_y2 and loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4 and loc_x3_y3, loc_x4_y1 is connected to loc_x4_y0 and loc_x4_y2, loc_x4_y1 is also connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1 and loc_x3_y2, the robot's current location is loc_x0_y3, and the following connections exist: loc_x0_y0 and loc_x0_y1, loc_x0_y3 and loc_x1_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y1 and loc_x4_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "eea373b7-f01a-418b-8db8-4a0ffc7cf19a", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x4_y2, the robot moves to loc_x3_y2, from loc_x3_y2, the robot moves to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, from loc_x2_y1, the robot moves to loc_x3_y1, moves from loc_x3_y1 to loc_x4_y1, from loc_x4_y1, the robot moves to loc_x4_y0, moves from loc_x4_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x1_y0 and robot moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 80? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x3_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 and loc_x4_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, robot is located at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x4_y1 and loc_x3_y1 and there is a connection between loc_x4_y4 and loc_x3_y4.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the robot undergoes a series of movements as follows: it starts at loc_x4_y2 and moves to loc_x3_y2, then to loc_x2_y2, followed by loc_x2_y1, then loc_x3_y1, loc_x4_y1, loc_x4_y0, loc_x3_y0, loc_x2_y0, loc_x1_y0, and finally loc_x0_y0 to reach its current state. In this state, is the count of valid properties that do not involve negations equal to 80? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x3_y0, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x2_y3, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y0 and loc_x2_y0, loc_x3_y1 and loc_x3_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x3_y4 and loc_x4_y4, loc_x3_y4 and loc_x2_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, the robot is at loc_x4_y2. Additionally, connections exist between loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x1_y4, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x3_y4, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x4_y2, loc_x4_y1 and loc_x3_y1, and loc_x4_y4 and loc_x3_y4."}
{"question_id": "54c08019-1114-40a8-bb50-5fec39c875c6", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y2 to loc_x0_y1, moves from loc_x0_y1 to loc_x0_y0, robot moves from loc_x0_y0 to loc_x1_y0, moves to loc_x1_y1 from loc_x1_y0, moves from loc_x1_y1 to loc_x1_y2, from loc_x1_y2, the robot moves to loc_x0_y2, robot moves from loc_x0_y2 to loc_x0_y3, moves to loc_x0_y4 from loc_x0_y3, moves to loc_x0_y5 from loc_x0_y4, robot moves from loc_x0_y5 to loc_x1_y5, robot moves from loc_x1_y5 to loc_x1_y4, moves from loc_x1_y4 to loc_x1_y3, moves to loc_x2_y3 from loc_x1_y3, from loc_x2_y3, the robot moves to loc_x2_y2, from loc_x2_y2, the robot moves to loc_x2_y1, from loc_x2_y1, the robot moves to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, from loc_x3_y0, the robot moves to loc_x3_y1 and moves from loc_x3_y1 to loc_x3_y2 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 92? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 and loc_x0_y4 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x2_y5, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the robot undergoes a series of movements as follows: it transitions from loc_x0_y2 to loc_x0_y1, then to loc_x0_y0, followed by a move to loc_x1_y0, then to loc_x1_y1, and subsequently to loc_x1_y2. From loc_x1_y2, the robot returns to loc_x0_y2, then proceeds to loc_x0_y3, loc_x0_y4, and loc_x0_y5. The robot then moves to loc_x1_y5, followed by loc_x1_y4, loc_x1_y3, and then to loc_x2_y3. From loc_x2_y3, the robot moves to loc_x2_y2, then to loc_x2_y1, and loc_x2_y0, before transitioning to loc_x3_y0, loc_x3_y1, and finally loc_x3_y2, reaching the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 92? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 is also connected to loc_x0_y4, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is also connected to loc_x2_y1, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is also connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is also connected to loc_x2_y5, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is also connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is also connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is also connected to loc_x2_y5, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is also connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, the robot is currently at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2."}
{"question_id": "954d1278-0021-4663-a4ff-02bd1205c57b", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x1_y0 to loc_x0_y0, moves to loc_x0_y1 from loc_x0_y0, robot moves from loc_x0_y1 to loc_x1_y1, from loc_x1_y1, the robot moves to loc_x2_y1, robot moves from loc_x2_y1 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, from loc_x3_y0, the robot moves to loc_x3_y1, moves to loc_x3_y2 from loc_x3_y1, moves from loc_x3_y2 to loc_x3_y1 and moves from loc_x3_y1 to loc_x4_y1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 95? True or False", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is visited, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y4 and loc_x4_y4 are connected, robot is located at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y2, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y0 and loc_x4_y0, there is a connection between loc_x5_y1 and loc_x5_y0, there is a connection between loc_x5_y3 and loc_x4_y3, there is a connection between loc_x5_y3 and loc_x5_y4 and there is a connection between loc_x5_y4 and loc_x5_y3.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the robot executes the following sequence of actions: it moves from loc_x1_y0 to loc_x0_y0, then to loc_x0_y1, followed by loc_x1_y1, then loc_x2_y1, then loc_x2_y0, then loc_x3_y0, then loc_x3_y1, then loc_x3_y2, then back to loc_x3_y1, and finally to loc_x4_y1, resulting in the current state. In this state, is the count of valid properties that do not involve negations equal to 95? True or False", "initial_state_nl_paraphrased": "Loc_x0_y0 is adjacent to loc_x1_y0, loc_x0_y1, and loc_x0_y2 are adjacent, loc_x0_y1 is adjacent to loc_x0_y0, loc_x0_y2, and loc_x0_y3 are adjacent, loc_x0_y4 is adjacent to loc_x0_y3, loc_x0_y4 is adjacent to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are adjacent, loc_x1_y0 and loc_x1_y1 are adjacent, loc_x1_y0 has been visited, loc_x1_y1 and loc_x2_y1 are adjacent, loc_x1_y1 is adjacent to loc_x0_y1, loc_x1_y1 is adjacent to loc_x1_y0, loc_x1_y1 is adjacent to loc_x1_y2, loc_x1_y2 is adjacent to loc_x1_y1, loc_x1_y2 is adjacent to loc_x2_y2, loc_x2_y0 and loc_x2_y1 are adjacent, loc_x2_y0 is adjacent to loc_x1_y0, loc_x2_y0 is adjacent to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are adjacent, loc_x2_y1 and loc_x2_y2 are adjacent, loc_x2_y1 and loc_x3_y1 are adjacent, loc_x2_y1 is adjacent to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are adjacent, loc_x2_y2 is adjacent to loc_x1_y2, loc_x2_y2 is adjacent to loc_x2_y1, loc_x2_y3 is adjacent to loc_x2_y2, loc_x3_y1 and loc_x3_y0 are adjacent, loc_x3_y1 is adjacent to loc_x4_y1, loc_x3_y2 and loc_x3_y3 are adjacent, loc_x3_y2 is adjacent to loc_x2_y2, loc_x3_y2 is adjacent to loc_x4_y2, loc_x3_y3 and loc_x3_y2 are adjacent, loc_x3_y3 and loc_x4_y3 are adjacent, loc_x3_y3 is adjacent to loc_x2_y3, loc_x3_y3 is adjacent to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are adjacent, loc_x3_y4 is adjacent to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are adjacent, loc_x4_y0 is adjacent to loc_x4_y1, loc_x4_y0 is adjacent to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are adjacent, loc_x4_y1 is adjacent to loc_x5_y1, loc_x4_y2 is adjacent to loc_x4_y1, loc_x4_y2 is adjacent to loc_x4_y3, loc_x4_y4 and loc_x4_y3 are adjacent, loc_x5_y0 is adjacent to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are adjacent, loc_x5_y1 is adjacent to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are adjacent, loc_x5_y2 is adjacent to loc_x5_y1, loc_x5_y2 is adjacent to loc_x5_y3, loc_x5_y3 and loc_x5_y2 are adjacent, loc_x5_y4 and loc_x4_y4 are adjacent, the robot is positioned at loc_x1_y0, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y1 and loc_x1_y1, a path exists between loc_x0_y2 and loc_x0_y1, a path exists between loc_x0_y2 and loc_x1_y2, a path exists between loc_x0_y3 and loc_x0_y2, a path exists between loc_x0_y3 and loc_x0_y4, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y2 and loc_x0_y2, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y0 and loc_x4_y0, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x4_y1 and loc_x3_y1, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, a path exists between loc_x4_y2 and loc_x5_y2, a path exists between loc_x4_y3 and loc_x3_y3, a path exists between loc_x4_y3 and loc_x4_y2, a path exists between loc_x4_y3 and loc_x4_y4, a path exists between loc_x4_y3 and loc_x5_y3, a path exists between loc_x4_y4 and loc_x3_y4, a path exists between loc_x4_y4 and loc_x5_y4, a path exists between loc_x5_y0 and loc_x4_y0, a path exists between loc_x5_y1 and loc_x5_y0, a path exists between loc_x5_y3 and loc_x4_y3, a path exists between loc_x5_y3 and loc_x5_y4, and a path exists between loc_x5_y4 and loc_x5_y3."}
{"question_id": "9461dfaa-c1f3-44d3-9c96-717766ea5d7a", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_6", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x0_y3, the robot moves to loc_x0_y4 to reach the current state. In this state, is the number of executable and inexecutable actions equal to 351? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 is visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": ["action_executability"], "question_paraphrased": "Based on the initial condition, the robot executes the following actions: it moves from loc_x0_y3 to loc_x0_y4 to attain the current state. In this state, is the number of executable and inexecutable actions equal to 351? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is connected to both loc_x0_y3 and loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is also connected to loc_x0_y0 and loc_x1_y1, loc_x1_y1 is connected to both loc_x0_y1 and loc_x2_y1, loc_x1_y3 is connected to loc_x1_y4 and loc_x2_y3, loc_x1_y3 is also connected to loc_x0_y3, loc_x1_y4 is connected to loc_x0_y4 and loc_x1_y3, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to both loc_x2_y2 and loc_x2_y4, loc_x2_y3 is also connected to loc_x1_y3, loc_x2_y4 is connected to loc_x2_y3 and loc_x3_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is also connected to loc_x3_y1 and loc_x4_y0, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is also connected to loc_x2_y1 and loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is also connected to loc_x3_y1 and loc_x3_y3, and loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 is connected to loc_x3_y2 and loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is also connected to loc_x4_y2, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, the robot is currently at loc_x0_y3, and the following connections exist: loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x1_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y1 and loc_x4_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "be33d906-5b70-4d67-a533-951c682b9124", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x0_y3 to loc_x0_y4, moves to loc_x0_y3 from loc_x0_y4, robot moves from loc_x0_y3 to loc_x1_y3, robot moves from loc_x1_y3 to loc_x1_y4, moves to loc_x2_y4 from loc_x1_y4, robot moves from loc_x2_y4 to loc_x3_y4, robot moves from loc_x3_y4 to loc_x3_y3, robot moves from loc_x3_y3 to loc_x2_y3, robot moves from loc_x2_y3 to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, from loc_x2_y1, the robot moves to loc_x1_y1, robot moves from loc_x1_y1 to loc_x0_y1, moves to loc_x0_y0 from loc_x0_y1, moves from loc_x0_y0 to loc_x1_y0, moves to loc_x2_y0 from loc_x1_y0, moves from loc_x2_y0 to loc_x3_y0, moves from loc_x3_y0 to loc_x3_y1, from loc_x3_y1, the robot moves to loc_x3_y2 and robot moves from loc_x3_y2 to loc_x4_y2 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 426? True or False", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 is visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot moves from loc_x0_y3 to loc_x0_y4 and then back to loc_x0_y3, then proceeds from loc_x0_y3 to loc_x1_y3, followed by a move from loc_x1_y3 to loc_x1_y4, then to loc_x2_y4 from loc_x1_y4, and continues from loc_x2_y4 to loc_x3_y4, then to loc_x3_y3, and subsequently to loc_x2_y3, loc_x2_y2, and loc_x2_y1. From loc_x2_y1, the robot moves to loc_x1_y1, then to loc_x0_y1, followed by a move to loc_x0_y0, then to loc_x1_y0, loc_x2_y0, and loc_x3_y0. The robot then moves from loc_x3_y0 to loc_x3_y1, then to loc_x3_y2, and finally from loc_x3_y2 to loc_x4_y2, reaching the current state. In this state, is the number of valid properties of the state that involve negations equal to 426? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is connected to both loc_x0_y3 and loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is also connected to loc_x0_y0 and loc_x1_y1, loc_x1_y1 is connected to both loc_x0_y1 and loc_x2_y1, loc_x1_y3 is connected to loc_x1_y4 and loc_x2_y3, loc_x1_y3 is also connected to loc_x0_y3, loc_x1_y4 is connected to loc_x0_y4 and loc_x1_y3, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to loc_x2_y2 and loc_x1_y3, loc_x2_y4 is connected to loc_x2_y3 and loc_x3_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is also connected to loc_x3_y1 and loc_x4_y0, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is also connected to loc_x2_y1 and loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is also connected to loc_x3_y1 and loc_x3_y3 and loc_x4_y2, loc_x3_y3 is connected to loc_x3_y2 and loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4 and loc_x3_y3, loc_x4_y1 is connected to loc_x4_y0 and loc_x4_y2, loc_x4_y1 is also connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1 and loc_x3_y2, the robot is currently at loc_x0_y3, and the following connections exist: loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x1_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y1 and loc_x4_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "99e4006c-d1f4-4a19-8c39-ee8d57e18800", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y3 to loc_x0_y4 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 399? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 is visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the robot executes the following actions: it moves from loc_x0_y3 to loc_x0_y4 to attain the current state. In this state, does the count of valid properties involving negations equal 399? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is connected to both loc_x0_y3 and loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is also connected to loc_x0_y0 and loc_x1_y1, loc_x1_y1 is connected to both loc_x0_y1 and loc_x2_y1, loc_x1_y3 is connected to loc_x1_y4 and loc_x2_y3, loc_x1_y3 is also connected to loc_x0_y3, loc_x1_y4 is connected to loc_x0_y4 and loc_x1_y3, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to both loc_x2_y2 and loc_x1_y3, loc_x2_y4 is connected to loc_x2_y3 and loc_x3_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is also connected to loc_x3_y1 and loc_x4_y0, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is also connected to loc_x2_y1 and loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is also connected to loc_x3_y1 and loc_x3_y3, and loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 is connected to loc_x3_y2 and loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is also connected to loc_x4_y2 and loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1 and loc_x3_y2, the robot's current location is loc_x0_y3, and the following connections exist: loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x1_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y1 and loc_x4_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "2b040ffb-402b-4f43-b1e3-e77f57d718fe", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "numerical_reasoning", "question_name": "iter_1_question_7", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x4_y2 to loc_x3_y2, robot moves from loc_x3_y2 to loc_x2_y2, moves from loc_x2_y2 to loc_x2_y1, moves to loc_x3_y1 from loc_x2_y1, from loc_x3_y1, the robot moves to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, robot moves from loc_x4_y0 to loc_x3_y0, from loc_x3_y0, the robot moves to loc_x2_y0, robot moves from loc_x2_y0 to loc_x1_y0, robot moves from loc_x1_y0 to loc_x0_y0, moves from loc_x0_y0 to loc_x0_y1, moves from loc_x0_y1 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x1_y2, from loc_x1_y2, the robot moves to loc_x1_y3, robot moves from loc_x1_y3 to loc_x0_y3, from loc_x0_y3, the robot moves to loc_x0_y4, robot moves from loc_x0_y4 to loc_x1_y4, robot moves from loc_x1_y4 to loc_x2_y4 and robot moves from loc_x2_y4 to loc_x2_y3 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 89? True or False", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x3_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 and loc_x4_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, robot is located at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x4_y1 and loc_x3_y1 and there is a connection between loc_x4_y4 and loc_x3_y4.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot transitions from loc_x4_y2 to loc_x3_y2, then from loc_x3_y2 to loc_x2_y2, followed by a move from loc_x2_y2 to loc_x2_y1, then to loc_x3_y1 from loc_x2_y1, and from loc_x3_y1, the robot proceeds to loc_x4_y1. The robot continues by moving from loc_x4_y1 to loc_x4_y0, then to loc_x3_y0 from loc_x4_y0, followed by a transition from loc_x3_y0 to loc_x2_y0, then to loc_x1_y0 from loc_x2_y0, and from loc_x1_y0 to loc_x0_y0. Subsequently, the robot moves from loc_x0_y0 to loc_x0_y1, then to loc_x1_y1 from loc_x0_y1, followed by a move from loc_x1_y1 to loc_x1_y2, then to loc_x1_y3 from loc_x1_y2, and from loc_x1_y3, the robot proceeds to loc_x0_y3. The robot then moves from loc_x0_y3 to loc_x0_y4, followed by a transition from loc_x0_y4 to loc_x1_y4, then to loc_x2_y4 from loc_x1_y4, and finally from loc_x2_y4 to loc_x2_y3 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 89? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x3_y0, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x2_y3, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y0 and loc_x2_y0, loc_x3_y1 and loc_x3_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x3_y4 and loc_x4_y4, loc_x3_y4 and loc_x2_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, the robot is at loc_x4_y2. Additionally, connections exist between loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x1_y4, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x3_y4, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x4_y2, loc_x4_y1 and loc_x3_y1, and loc_x4_y4 and loc_x3_y4."}
{"question_id": "d566f9e2-b7af-4039-8574-205bb5ef7cf4", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_9", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y2 to loc_x0_y1 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 477? True or False", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 and loc_x0_y4 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x2_y5, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the robot executes the following actions: it moves from loc_x0_y2 to loc_x0_y1, resulting in the current state. In this state, does the count of valid properties that include negations equal 477? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 is also connected to loc_x0_y4, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is also connected to loc_x2_y1, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is also connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is also connected to loc_x2_y5, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is also connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is also connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is also connected to loc_x2_y5, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is also connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, the robot is currently at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2."}
{"question_id": "4027db9c-1b6e-4a1b-8995-72d0c5e18453", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "numerical_reasoning", "question_name": "iter_1_question_8", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y2 to loc_x0_y1 to reach the current state. In this state, is the number of valid properties of the state that do not involve negations equal to 86? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 and loc_x0_y4 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x2_y5, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y4 and loc_x1_y5, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y2.", "fluent_sign_question": "pos", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Based on the initial condition, the robot executes the following actions: it moves from loc_x0_y2 to loc_x0_y1, resulting in the current state. In this state, does the count of valid properties that do not involve negations equal 86? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x1_y1, loc_x0_y2 is marked as visited, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y5 is also connected to loc_x0_y4, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is also connected to loc_x2_y1, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is also connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is also connected to loc_x2_y5, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is also connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is also connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is also connected to loc_x2_y5, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is also connected to loc_x3_y1, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, the robot is currently at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x0_y1, a connection exists between loc_x0_y1 and loc_x0_y0, a connection exists between loc_x0_y2 and loc_x0_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y3 and loc_x0_y4, a connection exists between loc_x0_y4 and loc_x0_y3, a connection exists between loc_x0_y4 and loc_x1_y4, a connection exists between loc_x0_y5 and loc_x1_y5, a connection exists between loc_x1_y0 and loc_x2_y0, a connection exists between loc_x1_y1 and loc_x0_y1, a connection exists between loc_x1_y1 and loc_x1_y0, a connection exists between loc_x1_y2 and loc_x2_y2, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y3 and loc_x2_y3, a connection exists between loc_x1_y4 and loc_x0_y4, a connection exists between loc_x1_y4 and loc_x1_y5, a connection exists between loc_x1_y4 and loc_x2_y4, a connection exists between loc_x1_y5 and loc_x0_y5, a connection exists between loc_x2_y0 and loc_x1_y0, a connection exists between loc_x2_y0 and loc_x2_y1, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y4 and loc_x3_y4, a connection exists between loc_x2_y5 and loc_x1_y5, a connection exists between loc_x2_y5 and loc_x2_y4, a connection exists between loc_x3_y0 and loc_x2_y0, a connection exists between loc_x3_y1 and loc_x3_y0, a connection exists between loc_x3_y3 and loc_x2_y3 and a connection exists between loc_x3_y3 and loc_x3_y2."}
{"question_id": "394b11a6-f615-4f45-9804-642a92df4430", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "numerical_reasoning", "question_name": "iter_1_question_10", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x0_y3, the robot moves to loc_x0_y4 to reach the current state. In this state, is the number of valid properties of the state that involve negations equal to 447? True or False", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 is visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x1_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "neg", "question_subcategories": ["fluent_tracking"], "question_paraphrased": "Given the initial condition, the following actions are performed: from loc_x0_y3, the robot moves to loc_x0_y4 to reach the current state. In this state, does the number of valid properties of the state that involve negations equal 447? True or False", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is connected to both loc_x0_y3 and loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is also connected to loc_x0_y0 and loc_x1_y1, loc_x1_y1 is connected to both loc_x0_y1 and loc_x2_y1, loc_x1_y3 is connected to loc_x1_y4 and loc_x2_y3, loc_x1_y3 is also connected to loc_x0_y3, loc_x1_y4 is connected to loc_x0_y4 and loc_x1_y3, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y1 is also connected to loc_x1_y1, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 is connected to both loc_x2_y2 and loc_x2_y4, loc_x2_y3 is also connected to loc_x1_y3, loc_x2_y4 is connected to loc_x2_y3 and loc_x3_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is also connected to loc_x3_y1 and loc_x4_y0, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is also connected to loc_x2_y1 and loc_x3_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is also connected to loc_x3_y1 and loc_x3_y3, loc_x3_y2 is also connected to loc_x4_y2, loc_x3_y3 is connected to loc_x3_y2 and loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is also connected to loc_x4_y2, loc_x4_y1 is also connected to loc_x3_y1, loc_x4_y2 is connected to loc_x4_y1, the robot is currently at loc_x0_y3, and the following connections exist: loc_x0_y0 and loc_x0_y1, loc_x0_y3 and loc_x1_y3, loc_x1_y0 and loc_x1_y1, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y1 and loc_x4_y1, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x3_y2."}
