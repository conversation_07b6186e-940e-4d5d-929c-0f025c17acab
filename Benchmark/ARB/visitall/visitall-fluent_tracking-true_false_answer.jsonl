{"question_id": "f690f083-cf6e-408e-beed-df4b980b18da", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_9", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: loc_x0_y1 is not marked as visited, loc_x0_y2 is not visited, loc_x0_y3 is not visited, loc_x0_y4 is not visited, loc_x1_y1 is not visited, loc_x1_y2 is not marked as visited, loc_x1_y4 is not visited, loc_x2_y0 is not visited, loc_x2_y1 is not marked as visited, loc_x2_y2 is not visited, loc_x2_y3 is not marked as visited, loc_x3_y0 is not marked as visited, loc_x3_y1 is not visited, loc_x3_y2 is not visited, loc_x3_y3 is not visited, loc_x3_y4 is not marked as visited, loc_x4_y0 is not marked as visited, loc_x4_y1 is not marked as visited, loc_x4_y2 is not visited, loc_x4_y3 is not visited, loc_x4_y4 is not marked as visited, loc_x5_y0 is not marked as visited, loc_x5_y1 is not visited, loc_x5_y2 is not visited, loc_x5_y3 is not visited and loc_x5_y4 is not marked as visited?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is visited, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x5_y1 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 and loc_x3_y3 are connected, loc_x4_y3 is connected to loc_x4_y4, loc_x4_y4 and loc_x4_y3 are connected, loc_x4_y4 and loc_x5_y4 are connected, loc_x4_y4 is connected to loc_x3_y4, loc_x5_y0 and loc_x4_y0 are connected, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 is connected to loc_x5_y3, robot is at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y4, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x4_y2, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x5_y1 and loc_x5_y0, there is a connection between loc_x5_y2 and loc_x5_y1 and there is a connection between loc_x5_y2 and loc_x5_y3.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot transitions from loc_x1_y0 to loc_x0_y0 to attain the current state. In this state, are the following properties that involve negations True or False: loc_x0_y1 is unvisited, loc_x0_y2 is unvisited, loc_x0_y3 is unvisited, loc_x0_y4 is unvisited, loc_x1_y1 is unvisited, loc_x1_y2 is unmarked as visited, loc_x1_y4 is unvisited, loc_x2_y0 is unvisited, loc_x2_y1 is unmarked as visited, loc_x2_y2 is unvisited, loc_x2_y3 is unmarked as visited, loc_x3_y0 is unmarked as visited, loc_x3_y1 is unvisited, loc_x3_y2 is unvisited, loc_x3_y3 is unvisited, loc_x3_y4 is unmarked as visited, loc_x4_y0 is unmarked as visited, loc_x4_y1 is unmarked as visited, loc_x4_y2 is unvisited, loc_x4_y3 is unvisited, loc_x4_y4 is unmarked as visited, loc_x5_y0 is unmarked as visited, loc_x5_y1 is unvisited, loc_x5_y2 is unvisited, loc_x5_y3 is unvisited and loc_x5_y4 is unmarked as visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y2 and loc_x0_y3, loc_x0_y3 and loc_x0_y2, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x0_y3, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y0 has been visited, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y1, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x3_y0 and loc_x2_y0, loc_x3_y1 and loc_x3_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y3 and loc_x4_y3, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x4_y1, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x5_y1, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 and loc_x4_y3, loc_x4_y3 and loc_x3_y3, loc_x4_y3 and loc_x4_y4, loc_x4_y4 and loc_x4_y3, loc_x4_y4 and loc_x5_y4, loc_x4_y4 and loc_x3_y4, loc_x5_y0 and loc_x4_y0, loc_x5_y0 and loc_x5_y1, loc_x5_y1 and loc_x4_y1, loc_x5_y1 and loc_x5_y2, loc_x5_y2 and loc_x4_y2, loc_x5_y3 and loc_x5_y2, loc_x5_y3 and loc_x4_y3, loc_x5_y3 and loc_x5_y4, loc_x5_y4 and loc_x4_y4, loc_x5_y4 and loc_x5_y3. The robot is currently at loc_x1_y0. Additionally, the following connections exist: loc_x0_y0 and loc_x1_y0, loc_x0_y2 and loc_x0_y1, loc_x0_y2 and loc_x1_y2, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x0_y2, loc_x1_y2 and loc_x2_y2, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x3_y2, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x5_y0, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x5_y2, loc_x4_y3 and loc_x4_y2, loc_x4_y3 and loc_x5_y3, loc_x5_y1 and loc_x5_y0, loc_x5_y2 and loc_x5_y1, and loc_x5_y2 and loc_x5_y3."}
{"question_id": "656c630e-9616-4b37-bbf2-a39d481f0c75", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_14", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y1 from loc_x0_y2, moves to loc_x0_y0 from loc_x0_y1, robot moves from loc_x0_y0 to loc_x1_y0, robot moves from loc_x1_y0 to loc_x1_y1, from loc_x1_y1, the robot moves to loc_x1_y2, moves to loc_x0_y2 from loc_x1_y2, moves from loc_x0_y2 to loc_x0_y3, moves from loc_x0_y3 to loc_x0_y4, moves from loc_x0_y4 to loc_x0_y5, moves to loc_x1_y5 from loc_x0_y5, moves to loc_x1_y4 from loc_x1_y5, robot moves from loc_x1_y4 to loc_x1_y3, moves from loc_x1_y3 to loc_x2_y3, from loc_x2_y3, the robot moves to loc_x2_y2, from loc_x2_y2, the robot moves to loc_x2_y1, robot moves from loc_x2_y1 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, moves from loc_x3_y0 to loc_x3_y1 and moves from loc_x3_y1 to loc_x3_y2 to reach the current state. In this state, is it True or False that loc_x3_y2 is connected to loc_x1_y2?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, robot is placed at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y4 and loc_x0_y5, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x0_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x2_y5, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot relocates from loc_x0_y2 to loc_x0_y1, then to loc_x0_y0, followed by a move to loc_x1_y0, then to loc_x1_y1, and subsequently to loc_x1_y2. From loc_x1_y2, the robot moves to loc_x0_y2, then to loc_x0_y3, loc_x0_y4, and loc_x0_y5. The robot then proceeds to loc_x1_y5, followed by loc_x1_y4, loc_x1_y3, and then loc_x2_y3. From loc_x2_y3, the robot moves to loc_x2_y2, then to loc_x2_y1, and loc_x2_y0, before reaching loc_x3_y0, loc_x3_y1, and finally loc_x3_y2. In this state, is it True or False that loc_x3_y2 is connected to loc_x1_y2?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is linked to loc_x1_y1, loc_x0_y2 has been visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, the robot is positioned at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x0_y1, a connection exists between loc_x0_y0 and loc_x1_y0, a connection exists between loc_x0_y2 and loc_x0_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y4 and loc_x0_y5, a connection exists between loc_x0_y4 and loc_x1_y4, a connection exists between loc_x0_y5 and loc_x1_y5, a connection exists between loc_x1_y0 and loc_x1_y1, a connection exists between loc_x1_y2 and loc_x2_y2, a connection exists between loc_x1_y3 and loc_x0_y3, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y5 and loc_x1_y4, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y2 and loc_x3_y2, a connection exists between loc_x2_y3 and loc_x2_y2, a connection exists between loc_x2_y4 and loc_x2_y3, a connection exists between loc_x2_y4 and loc_x2_y5, a connection exists between loc_x2_y5 and loc_x1_y5, a connection exists between loc_x2_y5 and loc_x2_y4, a connection exists between loc_x3_y1 and loc_x3_y0, a connection exists between loc_x3_y2 and loc_x2_y2, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, and a connection exists between loc_x3_y3 and loc_x3_y4."}
{"question_id": "2e1e09a4-278e-447f-aada-2fae631d10d4", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y1 from loc_x0_y2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: loc_x0_y0 is marked as visited, loc_x0_y1 is marked as visited, loc_x0_y2 is visited, loc_x0_y3 is visited, loc_x0_y4 is visited, loc_x0_y5 is marked as visited, loc_x1_y0 is marked as visited, loc_x1_y1 is marked as visited, loc_x1_y2 is marked as visited, loc_x1_y3 is marked as visited, loc_x1_y4 is visited, loc_x1_y5 is visited, loc_x2_y0 is visited, loc_x2_y1 is marked as visited, loc_x2_y2 is marked as visited, loc_x2_y3 is visited, loc_x2_y4 is marked as visited, loc_x2_y5 is visited, loc_x3_y0 is visited, loc_x3_y1 is marked as visited, loc_x3_y2 is marked as visited, loc_x3_y3 is visited and loc_x3_y4 is marked as visited?", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, robot is placed at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y4 and loc_x0_y5, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x0_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x2_y5, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: a move is made from loc_x0_y2 to loc_x0_y1 to reach the current state. In this state, are all of the following properties of the state that do not involve negations True or False: loc_x0_y0 is marked as visited, loc_x0_y1 is marked as visited, loc_x0_y2 is visited, loc_x0_y3 is visited, loc_x0_y4 is visited, loc_x0_y5 is marked as visited, loc_x1_y0 is marked as visited, loc_x1_y1 is marked as visited, loc_x1_y2 is marked as visited, loc_x1_y3 is marked as visited, loc_x1_y4 is visited, loc_x1_y5 is visited, loc_x2_y0 is visited, loc_x2_y1 is marked as visited, loc_x2_y2 is marked as visited, loc_x2_y3 is visited, loc_x2_y4 is marked as visited, loc_x2_y5 is visited, loc_x3_y0 is visited, loc_x3_y1 is marked as visited, loc_x3_y2 is marked as visited, loc_x3_y3 is visited and loc_x3_y4 is marked as visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is linked to loc_x1_y1, loc_x0_y2 has been visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, the robot is positioned at loc_x0_y2, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y0 and loc_x1_y0, a path exists between loc_x0_y2 and loc_x0_y1, a path exists between loc_x0_y2 and loc_x0_y3, a path exists between loc_x0_y2 and loc_x1_y2, a path exists between loc_x0_y4 and loc_x0_y5, a path exists between loc_x0_y4 and loc_x1_y4, a path exists between loc_x0_y5 and loc_x1_y5, a path exists between loc_x1_y0 and loc_x1_y1, a path exists between loc_x1_y2 and loc_x2_y2, a path exists between loc_x1_y3 and loc_x0_y3, a path exists between loc_x1_y3 and loc_x1_y4, a path exists between loc_x1_y5 and loc_x1_y4, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y1 and loc_x3_y1, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x3_y2, a path exists between loc_x2_y3 and loc_x2_y2, a path exists between loc_x2_y4 and loc_x2_y3, a path exists between loc_x2_y4 and loc_x2_y5, a path exists between loc_x2_y5 and loc_x1_y5, a path exists between loc_x2_y5 and loc_x2_y4, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x3_y2 and loc_x3_y3, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y3 and loc_x3_y2, and a path exists between loc_x3_y3 and loc_x3_y4."}
{"question_id": "a72dc539-f59a-4e99-ab6e-f0c4300a52cc", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x4_y2 to loc_x3_y2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: loc_x0_y0 is visited, loc_x0_y1 is marked as visited, loc_x0_y3 is visited, loc_x0_y4 is visited, loc_x1_y0 is visited, loc_x1_y1 is marked as visited, loc_x1_y2 is visited, loc_x1_y3 is visited, loc_x1_y4 is visited, loc_x2_y0 is marked as visited, loc_x2_y1 is marked as visited, loc_x2_y2 is visited, loc_x2_y3 is visited, loc_x2_y4 is visited, loc_x3_y0 is visited, loc_x3_y1 is marked as visited, loc_x3_y2 is visited, loc_x3_y3 is marked as visited, loc_x3_y4 is visited, loc_x4_y0 is visited, loc_x4_y1 is marked as visited, loc_x4_y2 is marked as visited and loc_x4_y4 is visited?", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, robot is at loc_x4_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: a move is made from loc_x4_y2 to loc_x3_y2 to reach the current state. In this state, are all of the following properties of the state that do not involve negations True or False: loc_x0_y0 has been visited, loc_x0_y1 is marked as visited, loc_x0_y3 has been visited, loc_x0_y4 has been visited, loc_x1_y0 has been visited, loc_x1_y1 is marked as visited, loc_x1_y2 has been visited, loc_x1_y3 has been visited, loc_x1_y4 has been visited, loc_x2_y0 is marked as visited, loc_x2_y1 is marked as visited, loc_x2_y2 has been visited, loc_x2_y3 has been visited, loc_x2_y4 has been visited, loc_x3_y0 has been visited, loc_x3_y1 is marked as visited, loc_x3_y2 has been visited, loc_x3_y3 is marked as visited, loc_x3_y4 has been visited, loc_x4_y0 has been visited, loc_x4_y1 is marked as visited, loc_x4_y2 is marked as visited and loc_x4_y4 has been visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y3 and loc_x1_y3, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x2_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 and loc_x2_y3, loc_x1_y3 and loc_x0_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x2_y0, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x4_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y1 and loc_x3_y1, loc_x4_y2 and loc_x3_y2, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4, the robot is at loc_x4_y2. \n\nAdditionally, the following connections exist: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x0_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x0_y4, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x4_y1."}
{"question_id": "4fa7f6fd-38f5-4fd2-a1d7-ca156776b0fc", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_15", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x1_y3, from loc_x1_y3, the robot moves to loc_x1_y4, robot moves from loc_x1_y4 to loc_x2_y4, from loc_x2_y4, the robot moves to loc_x3_y4, robot moves from loc_x3_y4 to loc_x3_y3, robot moves from loc_x3_y3 to loc_x2_y3, robot moves from loc_x2_y3 to loc_x2_y2 and from loc_x2_y2, the robot moves to loc_x2_y1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: robot is not at loc_x0_y0, robot is not at loc_x0_y1, robot is not at loc_x0_y4, robot is not at loc_x1_y4, robot is not at loc_x2_y0, robot is not at loc_x4_y1, robot is not at loc_x4_y2, robot is not located at loc_x0_y3, robot is not located at loc_x1_y3, robot is not located at loc_x2_y2, robot is not located at loc_x3_y2, robot is not located at loc_x3_y3, robot is not located at loc_x3_y4, robot is not placed at loc_x1_y0, robot is not placed at loc_x1_y1, robot is not placed at loc_x2_y3, robot is not placed at loc_x2_y4, robot is not placed at loc_x3_y0, robot is not placed at loc_x3_y1 and robot is not placed at loc_x4_y0?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is marked as visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y3 and loc_x0_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y4 and there is a connection between loc_x4_y1 and loc_x4_y0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot performs the following sequence of actions: it moves from loc_x0_y3 to loc_x0_y4, then back to loc_x0_y3, followed by a move to loc_x1_y3, then to loc_x1_y4, then to loc_x2_y4, then to loc_x3_y4, then to loc_x3_y3, then to loc_x2_y3, then to loc_x2_y2, and finally to loc_x2_y1, resulting in the current state. In this state, are the following properties that involve negations True or False: the robot is not at loc_x0_y0, the robot is not at loc_x0_y1, the robot is not at loc_x0_y4, the robot is not at loc_x1_y4, the robot is not at loc_x2_y0, the robot is not at loc_x4_y1, the robot is not at loc_x4_y2, the robot is not located at loc_x0_y3, the robot is not located at loc_x1_y3, the robot is not located at loc_x2_y2, the robot is not located at loc_x3_y2, the robot is not located at loc_x3_y3, the robot is not located at loc_x3_y4, the robot is not placed at loc_x1_y0, the robot is not placed at loc_x1_y1, the robot is not placed at loc_x2_y3, the robot is not placed at loc_x2_y4, the robot is not placed at loc_x3_y0, the robot is not placed at loc_x3_y1, and the robot is not placed at loc_x4_y0?", "initial_state_nl_paraphrased": "loc_x0_y1 is adjacent to loc_x0_y0, loc_x0_y1 is adjacent to loc_x1_y1, loc_x0_y3 is adjacent to loc_x1_y3, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is adjacent to loc_x0_y3, loc_x0_y4 is adjacent to loc_x1_y4, loc_x1_y0 is adjacent to loc_x0_y0, loc_x1_y0 is adjacent to loc_x1_y1, loc_x1_y1 is adjacent to loc_x0_y1, loc_x1_y1 is adjacent to loc_x1_y0, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is adjacent to loc_x2_y3, loc_x1_y4 is adjacent to loc_x0_y4, loc_x1_y4 is adjacent to loc_x1_y3, loc_x1_y4 is adjacent to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is adjacent to loc_x3_y0, loc_x2_y1 is adjacent to loc_x2_y0, loc_x2_y1 is adjacent to loc_x3_y1, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is adjacent to loc_x2_y3, loc_x2_y2 is adjacent to loc_x3_y2, loc_x2_y3 is adjacent to loc_x2_y2, loc_x2_y3 is adjacent to loc_x1_y3, loc_x2_y4 is adjacent to loc_x2_y3, loc_x2_y4 is adjacent to loc_x3_y4, loc_x2_y4 is adjacent to loc_x1_y4, loc_x3_y0 is adjacent to loc_x2_y0, loc_x3_y0 is adjacent to loc_x4_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is adjacent to loc_x3_y0, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is adjacent to loc_x2_y4, loc_x3_y4 is adjacent to loc_x3_y3, loc_x4_y0 is adjacent to loc_x4_y1, loc_x4_y0 is adjacent to loc_x3_y0, loc_x4_y1 is adjacent to loc_x3_y1, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is adjacent to loc_x3_y2, loc_x4_y2 is adjacent to loc_x4_y1, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y0 and loc_x1_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y3 and loc_x0_y3, a path exists between loc_x1_y3 and loc_x1_y4, a path exists between loc_x2_y0 and loc_x1_y0, a path exists between loc_x2_y1 and loc_x1_y1, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y3 and loc_x2_y4, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x3_y2 and loc_x4_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y3 and loc_x3_y4, and a path exists between loc_x4_y1 and loc_x4_y0."}
{"question_id": "b2f23867-e0c0-408b-9bf2-48588ee9746a", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_2_question_10", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x4_y2 to loc_x3_y2, from loc_x3_y2, the robot moves to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, moves to loc_x3_y1 from loc_x2_y1, moves from loc_x3_y1 to loc_x4_y1, moves from loc_x4_y1 to loc_x4_y0, robot moves from loc_x4_y0 to loc_x3_y0, moves from loc_x3_y0 to loc_x2_y0, moves from loc_x2_y0 to loc_x1_y0, robot moves from loc_x1_y0 to loc_x0_y0, from loc_x0_y0, the robot moves to loc_x0_y1, from loc_x0_y1, the robot moves to loc_x1_y1, moves from loc_x1_y1 to loc_x1_y2, moves from loc_x1_y2 to loc_x1_y3, moves to loc_x0_y3 from loc_x1_y3, moves to loc_x0_y4 from loc_x0_y3, robot moves from loc_x0_y4 to loc_x1_y4, moves to loc_x2_y4 from loc_x1_y4 and moves from loc_x2_y4 to loc_x2_y3 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: loc_x0_y0 is not marked as visited, loc_x0_y1 is not marked as visited, loc_x0_y3 is not visited, loc_x0_y4 is not visited, loc_x1_y0 is not visited, loc_x1_y1 is not visited, loc_x1_y2 is not marked as visited, loc_x1_y3 is not visited, loc_x1_y4 is not visited, loc_x2_y0 is not visited, loc_x2_y1 is not visited, loc_x2_y2 is not marked as visited, loc_x2_y3 is not visited, loc_x2_y4 is not marked as visited, loc_x3_y0 is not visited, loc_x3_y1 is not marked as visited, loc_x3_y2 is not visited, loc_x3_y3 is not marked as visited, loc_x3_y4 is not marked as visited, loc_x4_y0 is not visited, loc_x4_y1 is not marked as visited, loc_x4_y2 is not visited and loc_x4_y4 is not marked as visited?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, robot is at loc_x4_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot performs the following sequence of actions: it moves from loc_x4_y2 to loc_x3_y2, then to loc_x2_y2, followed by loc_x2_y1, then loc_x3_y1, loc_x4_y1, loc_x4_y0, loc_x3_y0, loc_x2_y0, loc_x1_y0, and loc_x0_y0. From loc_x0_y0, the robot proceeds to loc_x0_y1, then loc_x1_y1, loc_x1_y2, loc_x1_y3, loc_x0_y3, loc_x0_y4, loc_x1_y4, loc_x2_y4, and finally loc_x2_y3, reaching the current state. In this state, are the following properties that involve negations True or False: loc_x0_y0 is not marked as visited, loc_x0_y1 is not marked as visited, loc_x0_y3 is not visited, loc_x0_y4 is not visited, loc_x1_y0 is not visited, loc_x1_y1 is not visited, loc_x1_y2 is not marked as visited, loc_x1_y3 is not visited, loc_x1_y4 is not visited, loc_x2_y0 is not visited, loc_x2_y1 is not visited, loc_x2_y2 is not marked as visited, loc_x2_y3 is not visited, loc_x2_y4 is not marked as visited, loc_x3_y0 is not visited, loc_x3_y1 is not marked as visited, loc_x3_y2 is not visited, loc_x3_y3 is not marked as visited, loc_x3_y4 is not marked as visited, loc_x4_y0 is not visited, loc_x4_y1 is not marked as visited, loc_x4_y2 is not visited and loc_x4_y4 is not marked as visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y3 and loc_x1_y3, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x2_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 and loc_x2_y3, loc_x1_y3 and loc_x0_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x2_y0, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x4_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y1 and loc_x3_y1, loc_x4_y2 and loc_x3_y2, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4, the robot is at loc_x4_y2. \n\nAdditionally, the following connections exist: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x0_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x0_y4, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x4_y1."}
{"question_id": "067fa57e-b42c-45cb-9c0b-b59bbd96580a", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_20", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x1_y3, moves to loc_x1_y4 from loc_x1_y3, robot moves from loc_x1_y4 to loc_x2_y4, moves from loc_x2_y4 to loc_x3_y4, moves to loc_x3_y3 from loc_x3_y4, moves to loc_x2_y3 from loc_x3_y3, from loc_x2_y3, the robot moves to loc_x2_y2 and moves to loc_x2_y1 from loc_x2_y2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 and loc_x0_y4 are connected, loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y0 and loc_x2_y1 are connected, loc_x0_y0 and loc_x2_y3 are connected, loc_x0_y0 and loc_x2_y4 are connected, loc_x0_y0 and loc_x3_y3 are connected, loc_x0_y0 and loc_x4_y2 are connected, loc_x0_y0 is connected to loc_x0_y3, loc_x0_y0 is connected to loc_x1_y1, loc_x0_y0 is connected to loc_x1_y3, loc_x0_y0 is connected to loc_x3_y0, loc_x0_y0 is connected to loc_x3_y1, loc_x0_y0 is connected to loc_x4_y1, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y1 and loc_x2_y0 are connected, loc_x0_y1 and loc_x2_y2 are connected, loc_x0_y1 and loc_x2_y4 are connected, loc_x0_y1 and loc_x3_y0 are connected, loc_x0_y1 and loc_x3_y4 are connected, loc_x0_y1 is connected to loc_x0_y3, loc_x0_y1 is connected to loc_x2_y3, loc_x0_y1 is connected to loc_x3_y1, loc_x0_y1 is connected to loc_x3_y2, loc_x0_y1 is connected to loc_x3_y3, loc_x0_y1 is connected to loc_x4_y0, loc_x0_y1 is connected to loc_x4_y2, loc_x0_y3 and loc_x1_y4 are connected, loc_x0_y3 and loc_x2_y4 are connected, loc_x0_y3 and loc_x3_y0 are connected, loc_x0_y3 and loc_x4_y0 are connected, loc_x0_y3 and loc_x4_y2 are connected, loc_x0_y3 is connected to loc_x0_y0, loc_x0_y3 is connected to loc_x0_y1, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y1, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is connected to loc_x2_y0, loc_x0_y3 is connected to loc_x2_y2, loc_x0_y3 is connected to loc_x3_y3, loc_x0_y4 and loc_x0_y0 are connected, loc_x0_y4 and loc_x0_y1 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 and loc_x3_y3 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x2_y3, loc_x0_y4 is connected to loc_x3_y1, loc_x0_y4 is connected to loc_x4_y1, loc_x1_y0 and loc_x0_y1 are connected, loc_x1_y0 and loc_x0_y3 are connected, loc_x1_y0 and loc_x2_y1 are connected, loc_x1_y0 and loc_x2_y2 are connected, loc_x1_y0 and loc_x3_y0 are connected, loc_x1_y0 and loc_x3_y3 are connected, loc_x1_y0 and loc_x4_y2 are connected, loc_x1_y0 is connected to loc_x0_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is connected to loc_x2_y3, loc_x1_y0 is connected to loc_x3_y1, loc_x1_y0 is connected to loc_x3_y2, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 and loc_x2_y2 are connected, loc_x1_y1 and loc_x3_y0 are connected, loc_x1_y1 and loc_x3_y3 are connected, loc_x1_y1 and loc_x4_y2 are connected, loc_x1_y1 is connected to loc_x0_y0, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x0_y3, loc_x1_y1 is connected to loc_x1_y4, loc_x1_y1 is connected to loc_x2_y3, loc_x1_y1 is connected to loc_x3_y2, loc_x1_y1 is connected to loc_x3_y4, loc_x1_y1 is connected to loc_x4_y1, loc_x1_y3 and loc_x0_y4 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y0 are connected, loc_x1_y3 and loc_x2_y1 are connected, loc_x1_y3 and loc_x2_y4 are connected, loc_x1_y3 and loc_x3_y3 are connected, loc_x1_y3 and loc_x4_y2 are connected, loc_x1_y3 is connected to loc_x0_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y0, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y3 is connected to loc_x3_y1, loc_x1_y3 is connected to loc_x4_y0, loc_x1_y3 is connected to loc_x4_y1, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x1_y1 are connected, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x2_y0 are connected, loc_x1_y4 and loc_x2_y1 are connected, loc_x1_y4 and loc_x2_y2 are connected, loc_x1_y4 and loc_x3_y2 are connected, loc_x1_y4 and loc_x3_y3 are connected, loc_x1_y4 and loc_x4_y0 are connected, loc_x1_y4 and loc_x4_y2 are connected, loc_x1_y4 is connected to loc_x0_y1, loc_x1_y4 is connected to loc_x1_y0, loc_x1_y4 is connected to loc_x2_y3, loc_x1_y4 is connected to loc_x3_y0, loc_x1_y4 is connected to loc_x3_y4, loc_x1_y4 is connected to loc_x4_y1, loc_x2_y0 and loc_x0_y0 are connected, loc_x2_y0 and loc_x2_y4 are connected, loc_x2_y0 and loc_x3_y3 are connected, loc_x2_y0 and loc_x4_y1 are connected, loc_x2_y0 and loc_x4_y2 are connected, loc_x2_y0 is connected to loc_x0_y1, loc_x2_y0 is connected to loc_x0_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x1_y1, loc_x2_y0 is connected to loc_x2_y2, loc_x2_y0 is connected to loc_x3_y1, loc_x2_y0 is connected to loc_x3_y2, loc_x2_y1 and loc_x0_y3 are connected, loc_x2_y1 and loc_x0_y4 are connected, loc_x2_y1 and loc_x1_y0 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 and loc_x3_y3 are connected, loc_x2_y1 and loc_x3_y4 are connected, loc_x2_y1 and loc_x4_y2 are connected, loc_x2_y1 is connected to loc_x0_y1, loc_x2_y1 is connected to loc_x1_y3, loc_x2_y1 is connected to loc_x2_y3, loc_x2_y1 is connected to loc_x2_y4, loc_x2_y1 is connected to loc_x3_y2, loc_x2_y1 is connected to loc_x4_y0, loc_x2_y2 and loc_x1_y0 are connected, loc_x2_y2 and loc_x2_y0 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y2 and loc_x4_y0 are connected, loc_x2_y2 and loc_x4_y2 are connected, loc_x2_y2 is connected to loc_x0_y0, loc_x2_y2 is connected to loc_x0_y3, loc_x2_y2 is connected to loc_x1_y1, loc_x2_y2 is connected to loc_x1_y3, loc_x2_y2 is connected to loc_x1_y4, loc_x2_y2 is connected to loc_x2_y4, loc_x2_y2 is connected to loc_x3_y0, loc_x2_y2 is connected to loc_x3_y1, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x1_y0 are connected, loc_x2_y3 and loc_x1_y1 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y1 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x0_y0, loc_x2_y3 is connected to loc_x0_y3, loc_x2_y3 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x4_y0, loc_x2_y4 and loc_x1_y0 are connected, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y0 are connected, loc_x2_y4 and loc_x2_y1 are connected, loc_x2_y4 and loc_x2_y2 are connected, loc_x2_y4 and loc_x3_y0 are connected, loc_x2_y4 and loc_x3_y1 are connected, loc_x2_y4 and loc_x4_y1 are connected, loc_x2_y4 and loc_x4_y2 are connected, loc_x2_y4 is connected to loc_x0_y0, loc_x2_y4 is connected to loc_x0_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x2_y4 is connected to loc_x4_y0, loc_x3_y0 and loc_x0_y3 are connected, loc_x3_y0 and loc_x1_y4 are connected, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x2_y1 are connected, loc_x3_y0 and loc_x2_y2 are connected, loc_x3_y0 and loc_x2_y4 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 and loc_x4_y1 are connected, loc_x3_y0 is connected to loc_x0_y0, loc_x3_y0 is connected to loc_x0_y1, loc_x3_y0 is connected to loc_x0_y4, loc_x3_y0 is connected to loc_x1_y1, loc_x3_y0 is connected to loc_x1_y3, loc_x3_y0 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x3_y4, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y0 is connected to loc_x4_y2, loc_x3_y1 and loc_x0_y0 are connected, loc_x3_y1 and loc_x0_y3 are connected, loc_x3_y1 and loc_x1_y4 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y4 are connected, loc_x3_y1 and loc_x4_y1 are connected, loc_x3_y1 is connected to loc_x0_y4, loc_x3_y1 is connected to loc_x1_y0, loc_x3_y1 is connected to loc_x1_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y2 and loc_x0_y0 are connected, loc_x3_y2 and loc_x0_y4 are connected, loc_x3_y2 and loc_x1_y0 are connected, loc_x3_y2 and loc_x1_y3 are connected, loc_x3_y2 and loc_x2_y1 are connected, loc_x3_y2 and loc_x2_y3 are connected, loc_x3_y2 and loc_x3_y0 are connected, loc_x3_y2 and loc_x4_y0 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x1_y1, loc_x3_y2 is connected to loc_x1_y4, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x2_y4, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is connected to loc_x3_y4, loc_x3_y3 and loc_x0_y0 are connected, loc_x3_y3 and loc_x0_y4 are connected, loc_x3_y3 and loc_x1_y4 are connected, loc_x3_y3 and loc_x2_y0 are connected, loc_x3_y3 and loc_x2_y4 are connected, loc_x3_y3 and loc_x3_y1 are connected, loc_x3_y3 and loc_x4_y1 are connected, loc_x3_y3 is connected to loc_x0_y1, loc_x3_y3 is connected to loc_x1_y0, loc_x3_y3 is connected to loc_x1_y3, loc_x3_y3 is connected to loc_x2_y1, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y3 is connected to loc_x4_y0, loc_x3_y4 and loc_x0_y4 are connected, loc_x3_y4 and loc_x1_y4 are connected, loc_x3_y4 and loc_x3_y0 are connected, loc_x3_y4 and loc_x4_y2 are connected, loc_x3_y4 is connected to loc_x0_y1, loc_x3_y4 is connected to loc_x0_y3, loc_x3_y4 is connected to loc_x1_y0, loc_x3_y4 is connected to loc_x1_y1, loc_x3_y4 is connected to loc_x2_y3, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y1, loc_x3_y4 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x4_y0, loc_x3_y4 is connected to loc_x4_y1, loc_x4_y0 and loc_x0_y3 are connected, loc_x4_y0 and loc_x0_y4 are connected, loc_x4_y0 and loc_x1_y3 are connected, loc_x4_y0 and loc_x2_y2 are connected, loc_x4_y0 and loc_x2_y3 are connected, loc_x4_y0 and loc_x3_y2 are connected, loc_x4_y0 and loc_x3_y4 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 and loc_x4_y2 are connected, loc_x4_y0 is connected to loc_x0_y0, loc_x4_y0 is connected to loc_x0_y1, loc_x4_y0 is connected to loc_x1_y0, loc_x4_y0 is connected to loc_x1_y4, loc_x4_y0 is connected to loc_x2_y0, loc_x4_y0 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x3_y1, loc_x4_y1 and loc_x1_y1 are connected, loc_x4_y1 and loc_x3_y2 are connected, loc_x4_y1 and loc_x3_y3 are connected, loc_x4_y1 is connected to loc_x0_y0, loc_x4_y1 is connected to loc_x0_y3, loc_x4_y1 is connected to loc_x1_y0, loc_x4_y1 is connected to loc_x1_y3, loc_x4_y1 is connected to loc_x2_y0, loc_x4_y1 is connected to loc_x2_y2, loc_x4_y1 is connected to loc_x2_y4, loc_x4_y1 is connected to loc_x3_y4, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x0_y0 are connected, loc_x4_y2 and loc_x0_y4 are connected, loc_x4_y2 and loc_x2_y0 are connected, loc_x4_y2 and loc_x2_y2 are connected, loc_x4_y2 and loc_x2_y3 are connected, loc_x4_y2 and loc_x2_y4 are connected, loc_x4_y2 and loc_x3_y3 are connected, loc_x4_y2 and loc_x3_y4 are connected, loc_x4_y2 is connected to loc_x0_y3, loc_x4_y2 is connected to loc_x1_y3, loc_x4_y2 is connected to loc_x1_y4, loc_x4_y2 is connected to loc_x3_y0, loc_x4_y2 is connected to loc_x4_y0, loc_x4_y2 is connected to loc_x4_y1, there is a connection between loc_x0_y0 and loc_x1_y4, there is a connection between loc_x0_y0 and loc_x2_y0, there is a connection between loc_x0_y0 and loc_x2_y2, there is a connection between loc_x0_y0 and loc_x3_y2, there is a connection between loc_x0_y0 and loc_x3_y4, there is a connection between loc_x0_y0 and loc_x4_y0, there is a connection between loc_x0_y1 and loc_x0_y4, there is a connection between loc_x0_y1 and loc_x1_y3, there is a connection between loc_x0_y1 and loc_x1_y4, there is a connection between loc_x0_y1 and loc_x2_y1, there is a connection between loc_x0_y1 and loc_x4_y1, there is a connection between loc_x0_y3 and loc_x1_y0, there is a connection between loc_x0_y3 and loc_x2_y1, there is a connection between loc_x0_y3 and loc_x2_y3, there is a connection between loc_x0_y3 and loc_x3_y1, there is a connection between loc_x0_y3 and loc_x3_y2, there is a connection between loc_x0_y3 and loc_x3_y4, there is a connection between loc_x0_y3 and loc_x4_y1, there is a connection between loc_x0_y4 and loc_x1_y0, there is a connection between loc_x0_y4 and loc_x1_y1, there is a connection between loc_x0_y4 and loc_x1_y3, there is a connection between loc_x0_y4 and loc_x2_y0, there is a connection between loc_x0_y4 and loc_x2_y1, there is a connection between loc_x0_y4 and loc_x2_y2, there is a connection between loc_x0_y4 and loc_x2_y4, there is a connection between loc_x0_y4 and loc_x3_y0, there is a connection between loc_x0_y4 and loc_x3_y2, there is a connection between loc_x0_y4 and loc_x3_y4, there is a connection between loc_x0_y4 and loc_x4_y0, there is a connection between loc_x0_y4 and loc_x4_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y0 and loc_x1_y3, there is a connection between loc_x1_y0 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y4, there is a connection between loc_x1_y0 and loc_x3_y4, there is a connection between loc_x1_y0 and loc_x4_y0, there is a connection between loc_x1_y0 and loc_x4_y1, there is a connection between loc_x1_y1 and loc_x0_y4, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y3, there is a connection between loc_x1_y1 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x2_y4, there is a connection between loc_x1_y1 and loc_x3_y1, there is a connection between loc_x1_y1 and loc_x4_y0, there is a connection between loc_x1_y3 and loc_x0_y0, there is a connection between loc_x1_y3 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x3_y0, there is a connection between loc_x1_y3 and loc_x3_y2, there is a connection between loc_x1_y3 and loc_x3_y4, there is a connection between loc_x1_y4 and loc_x0_y0, there is a connection between loc_x1_y4 and loc_x0_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y4 and loc_x3_y1, there is a connection between loc_x2_y0 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x1_y3, there is a connection between loc_x2_y0 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x2_y3, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y0 and loc_x3_y4, there is a connection between loc_x2_y0 and loc_x4_y0, there is a connection between loc_x2_y1 and loc_x0_y0, there is a connection between loc_x2_y1 and loc_x1_y4, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x4_y1, there is a connection between loc_x2_y2 and loc_x0_y1, there is a connection between loc_x2_y2 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x3_y3, there is a connection between loc_x2_y2 and loc_x3_y4, there is a connection between loc_x2_y2 and loc_x4_y1, there is a connection between loc_x2_y3 and loc_x0_y1, there is a connection between loc_x2_y3 and loc_x0_y4, there is a connection between loc_x2_y3 and loc_x1_y4, there is a connection between loc_x2_y3 and loc_x2_y0, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y0, there is a connection between loc_x2_y3 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y3 and loc_x3_y4, there is a connection between loc_x2_y3 and loc_x4_y1, there is a connection between loc_x2_y3 and loc_x4_y2, there is a connection between loc_x2_y4 and loc_x0_y1, there is a connection between loc_x2_y4 and loc_x0_y3, there is a connection between loc_x2_y4 and loc_x1_y1, there is a connection between loc_x2_y4 and loc_x1_y3, there is a connection between loc_x2_y4 and loc_x3_y2, there is a connection between loc_x3_y0 and loc_x1_y0, there is a connection between loc_x3_y0 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x0_y1, there is a connection between loc_x3_y1 and loc_x1_y3, there is a connection between loc_x3_y1 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x2_y3, there is a connection between loc_x3_y1 and loc_x2_y4, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x3_y3, there is a connection between loc_x3_y1 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x4_y2, there is a connection between loc_x3_y2 and loc_x0_y1, there is a connection between loc_x3_y2 and loc_x0_y3, there is a connection between loc_x3_y2 and loc_x2_y0, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x4_y1, there is a connection between loc_x3_y3 and loc_x0_y3, there is a connection between loc_x3_y3 and loc_x1_y1, there is a connection between loc_x3_y3 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y0, there is a connection between loc_x3_y3 and loc_x3_y4, there is a connection between loc_x3_y3 and loc_x4_y2, there is a connection between loc_x3_y4 and loc_x0_y0, there is a connection between loc_x3_y4 and loc_x1_y3, there is a connection between loc_x3_y4 and loc_x2_y0, there is a connection between loc_x3_y4 and loc_x2_y1, there is a connection between loc_x3_y4 and loc_x2_y2, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x1_y1, there is a connection between loc_x4_y0 and loc_x2_y1, there is a connection between loc_x4_y0 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x0_y1, there is a connection between loc_x4_y1 and loc_x0_y4, there is a connection between loc_x4_y1 and loc_x1_y4, there is a connection between loc_x4_y1 and loc_x2_y1, there is a connection between loc_x4_y1 and loc_x2_y3, there is a connection between loc_x4_y1 and loc_x3_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x0_y1, there is a connection between loc_x4_y2 and loc_x1_y0, there is a connection between loc_x4_y2 and loc_x1_y1, there is a connection between loc_x4_y2 and loc_x2_y1, there is a connection between loc_x4_y2 and loc_x3_y1 and there is a connection between loc_x4_y2 and loc_x3_y2?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is marked as visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y3 and loc_x0_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y4 and there is a connection between loc_x4_y1 and loc_x4_y0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot performs the following actions: it moves from loc_x0_y3 to loc_x0_y4, then from loc_x0_y4 to loc_x0_y3, followed by a move from loc_x0_y3 to loc_x1_y3. The robot then proceeds to loc_x1_y4 from loc_x1_y3, then to loc_x2_y4 from loc_x1_y4, and subsequently to loc_x3_y4 from loc_x2_y4. It then moves to loc_x3_y3 from loc_x3_y4, followed by a move to loc_x2_y3 from loc_x3_y3. From loc_x2_y3, the robot moves to loc_x2_y2 and then to loc_x2_y1, ultimately reaching the current state. \n\nIn this state, the following properties of the state that do not involve negations are evaluated as True or False: \n\n- loc_x0_y0 is connected to loc_x0_y1, loc_x0_y4, loc_x1_y0, loc_x2_y1, loc_x2_y3, loc_x2_y4, loc_x3_y3, loc_x4_y2, loc_x0_y3, loc_x1_y1, loc_x1_y3, loc_x3_y0, loc_x3_y1, loc_x4_y1.\n- loc_x0_y1 is connected to loc_x0_y0, loc_x1_y0, loc_x1_y1, loc_x2_y0, loc_x2_y2, loc_x2_y4, loc_x3_y0, loc_x3_y4, loc_x0_y3, loc_x2_y3, loc_x3_y1, loc_x3_y2, loc_x3_y3, loc_x4_y0, loc_x4_y2.\n- loc_x0_y3 is connected to loc_x0_y0, loc_x0_y1, loc_x0_y4, loc_x1_y1, loc_x1_y3, loc_x2_y0, loc_x2_y2, loc_x3_y0, loc_x3_y3, loc_x4_y0, loc_x4_y2.\n- loc_x0_y4 is connected to loc_x0_y0, loc_x0_y1, loc_x1_y4, loc_x3_y3, loc_x0_y3, loc_x2_y3, loc_x3_y1, loc_x4_y1.\n- loc_x1_y0 is connected to loc_x0_y1, loc_x0_y3, loc_x2_y1, loc_x2_y2, loc_x3_y0, loc_x3_y3, loc_x4_y2, loc_x0_y4, loc_x2_y0, loc_x2_y3, loc_x3_y1, loc_x3_y2.\n- loc_x1_y1 is connected to loc_x0_y0, loc_x0_y1, loc_x0_y3, loc_x1_y4, loc_x2_y3, loc_x3_y2, loc_x3_y4, loc_x4_y1.\n- loc_x1_y3 is connected to loc_x0_y1, loc_x0_y3, loc_x1_y0, loc_x2_y0, loc_x2_y1, loc_x2_y4, loc_x3_y3, loc_x4_y2, loc_x0_y4, loc_x1_y4, loc_x2_y3, loc_x3_y1, loc_x4_y0, loc_x4_y1.\n- loc_x1_y4 is connected to loc_x0_y4, loc_x1_y1, loc_x1_y3, loc_x2_y0, loc_x2_y1, loc_x2_y2, loc_x3_y2, loc_x3_y3, loc_x4_y0, loc_x4_y2, loc_x0_y1, loc_x1_y0, loc_x2_y3, loc_x3_y0, loc_x3_y4, loc_x4_y1.\n- loc_x2_y0 is connected to loc_x0_y0, loc_x2_y4, loc_x3_y3, loc_x4_y1, loc_x4_y2, loc_x0_y1, loc_x0_y3, loc_x1_y0, loc_x1_y1, loc_x2_y2, loc_x3_y1, loc_x3_y2.\n- loc_x2_y1 is connected to loc_x0_y3, loc_x0_y4, loc_x1_y0, loc_x1_y1, loc_x2_y2, loc_x3_y1, loc_x3_y3, loc_x3_y4, loc_x4_y2, loc_x0_y1, loc_x1_y4, loc_x2_y0, loc_x2_y3, loc_x2_y4, loc_x3_y2, loc_x4_y0, loc_x4_y1.\n- loc_x2_y2 is connected to loc_x0_y0, loc_x0_y3, loc_x1_y0, loc_x1_y1, loc_x1_y3, loc_x1_y4, loc_x2_y0, loc_x2_y1, loc_x2_y4, loc_x3_y0, loc_x3_y1, loc_x3_y2, loc_x4_y0, loc_x4_y2.\n- loc_x2_y3 is connected to loc_x0_y0, loc_x0_y3, loc_x1_y0, loc_x1_y1, loc_x1_y3, loc_x1_y4, loc_x2_y1, loc_x2_y2, loc_x3_y2, loc_x4_y0, loc_x0_y1, loc_x0_y4, loc_x2_y0, loc_x2_y4, loc_x3_y0, loc_x3_y1, loc_x3_y3, loc_x3_y4, loc_x4_y1, loc_x4_y2.\n- loc_x2_y4 is connected to loc_x0_y0, loc_x0_y4, loc_x1_y0, loc_x1_y4, loc_x2_y0, loc_x2_y1, loc_x2_y2, loc_x3_y0, loc_x3_y1, loc_x3_y3, loc_x3_y4, loc_x4_y0, loc_x4_y1, loc_x4_y2, loc_x0_y1, loc_x0_y3, loc_x1_y1, loc_x1_y3, loc_x2_y3.\n- loc_x3_y0 is connected to loc_x0_y3, loc_x1_y4, loc_x2_y0, loc_x2_y1, loc_x2_y2, loc_x2_y4, loc_x3_y1, loc_x4_y1, loc_x0_y0, loc_x0_y1, loc_x0_y4, loc_x1_y1, loc_x1_y3, loc_x3_y3, loc_x3_y4, loc_x4_y0, loc_x4_y2.\n- loc_x3_y1 is connected to loc_x0_y0, loc_x0_y3, loc_x1_y4, loc_x2_y1, loc_x3_y4, loc_x4_y1, loc_x0_y4, loc_x1_y0, loc_x1_y1, loc_x1_y3, loc_x2_y0, loc_x2_y2, loc_x2_y3, loc_x2_y4, loc_x3_y0, loc_x3_y2, loc_x3_y3, loc_x4_y0, loc_x4_y2.\n- loc_x3_y2 is connected to loc_x0_y0, loc_x0_y4, loc_x1_y0, loc_x1_y3, loc_x2_y1, loc_x2_y3, loc_x3_y0, loc_x4_y0, loc_x4_y2, loc_x1_y1, loc_x1_y4, loc_x2_y2, loc_x2_y4, loc_x3_y1, loc_x3_y3, loc_x3_y4.\n- loc_x3_y3 is connected to loc_x0_y0, loc_x0_y4, loc_x1_y4, loc_x2_y0, loc_x2_y4, loc_x3_y1, loc_x4_y1, loc_x0_y1, loc_x0_y3, loc_x1_y0, loc_x1_y1, loc_x1_y3, loc_x2_y1, loc_x2_y2, loc_x2_y3, loc_x3_y0, loc_x3_y2, loc_x3_y4, loc_x4_y2.\n- loc_x3_y4 is connected to loc_x0_y4, loc_x1_y4, loc_x3_y0, loc_x4_y2, loc_x0_y1, loc_x0_y3, loc_x1_y0, loc_x1_y1, loc_x2_y0, loc_x2_y1, loc_x2_y2, loc_x2_y3, loc_x2_y4, loc_x3_y1, loc_x3_y2, loc_x3_y3, loc_x4_y0, loc_x4_y1.\n- loc_x4_y0 is connected to loc_x0_y3, loc_x0_y4, loc_x1_y3, loc_x2_y2, loc_x2_y3, loc_x3_y2, loc_x3_y4, loc_x4_y1, loc_x4_y2, loc_x0_y0, loc_x0_y1, loc_x1_y0, loc_x1_y4, loc_x2_y0, loc_x2_y4, loc_x3_y0, loc_x3_y1.\n- loc_x4_y1 is connected to loc_x1_y1, loc_x3_y2, loc_x3_y3, loc_x0_y0, loc_x0_y3, loc_x1_y0, loc_x1_y3, loc_x2_y0, loc_x2_y2, loc_x2_y4, loc_x3_y4, loc_x4_y2, loc_x0_y1, loc_x0_y4, loc_x1_y4, loc_x2_y1, loc_x2_y3, loc_x3_y0, loc_x3_y1, loc_x4_y0.\n- loc_x4_y2 is connected to loc_x0_y0, loc_x0_y4, loc_x2_y0, loc_x2_y2, loc_x2_y3, loc_x2_y4, loc_x3_y3, loc_x3_y4, loc_x0_y3, loc_x1_y3, loc_x1_y4, loc_x3_y0, loc_x4_y0, loc_x4_y1.", "initial_state_nl_paraphrased": "loc_x0_y1 is adjacent to loc_x0_y0, loc_x0_y1 is adjacent to loc_x1_y1, loc_x0_y3 is adjacent to loc_x1_y3, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is adjacent to loc_x0_y3, loc_x0_y4 is adjacent to loc_x1_y4, loc_x1_y0 is adjacent to loc_x0_y0, loc_x1_y0 is adjacent to loc_x1_y1, loc_x1_y1 is adjacent to loc_x0_y1, loc_x1_y1 is adjacent to loc_x1_y0, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is adjacent to loc_x2_y3, loc_x1_y4 is adjacent to loc_x0_y4, loc_x1_y4 is adjacent to loc_x1_y3, loc_x1_y4 is adjacent to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is adjacent to loc_x3_y0, loc_x2_y1 is adjacent to loc_x2_y0, loc_x2_y1 is adjacent to loc_x3_y1, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is adjacent to loc_x2_y3, loc_x2_y2 is adjacent to loc_x3_y2, loc_x2_y3 is adjacent to loc_x2_y2, loc_x2_y3 is adjacent to loc_x1_y3, loc_x2_y4 is adjacent to loc_x2_y3, loc_x2_y4 is adjacent to loc_x3_y4, loc_x2_y4 is adjacent to loc_x1_y4, loc_x3_y0 is adjacent to loc_x2_y0, loc_x3_y0 is adjacent to loc_x4_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is adjacent to loc_x3_y0, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is adjacent to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 is adjacent to loc_x4_y1, loc_x4_y0 is adjacent to loc_x3_y0, loc_x4_y1 is adjacent to loc_x3_y1, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is adjacent to loc_x3_y2, loc_x4_y2 is adjacent to loc_x4_y1, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y0 and loc_x1_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y3 and loc_x0_y3, a path exists between loc_x1_y3 and loc_x1_y4, a path exists between loc_x2_y0 and loc_x1_y0, a path exists between loc_x2_y1 and loc_x1_y1, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y3 and loc_x2_y4, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x3_y2 and loc_x4_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y3 and loc_x3_y4, and a path exists between loc_x4_y1 and loc_x4_y0."}
{"question_id": "9970b9ca-68a6-48e4-beef-3abb855b5255", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_16", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y2 to loc_x0_y1 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: robot is not at loc_x0_y2, robot is not at loc_x0_y5, robot is not at loc_x1_y2, robot is not at loc_x1_y4, robot is not at loc_x2_y0, robot is not at loc_x2_y2, robot is not at loc_x3_y0, robot is not at loc_x3_y2, robot is not at loc_x3_y3, robot is not located at loc_x1_y5, robot is not located at loc_x2_y1, robot is not located at loc_x2_y3, robot is not located at loc_x3_y1, robot is not located at loc_x3_y4, robot is not placed at loc_x0_y0, robot is not placed at loc_x0_y1, robot is not placed at loc_x0_y3, robot is not placed at loc_x0_y4, robot is not placed at loc_x1_y0, robot is not placed at loc_x1_y1, robot is not placed at loc_x1_y3, robot is not placed at loc_x2_y4 and robot is not placed at loc_x2_y5?", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, robot is placed at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y4 and loc_x0_y5, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x0_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x2_y5, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: the robot moves from loc_x0_y2 to loc_x0_y1 to reach the current state. In this state, are all of the following properties that involve negations True or False: the robot is not located at loc_x0_y2, the robot is not located at loc_x0_y5, the robot is not located at loc_x1_y2, the robot is not located at loc_x1_y4, the robot is not located at loc_x2_y0, the robot is not located at loc_x2_y2, the robot is not located at loc_x3_y0, the robot is not located at loc_x3_y2, the robot is not located at loc_x3_y3, the robot is not at loc_x1_y5, the robot is not at loc_x2_y1, the robot is not at loc_x2_y3, the robot is not at loc_x3_y1, the robot is not at loc_x3_y4, the robot is not positioned at loc_x0_y0, the robot is not positioned at loc_x0_y1, the robot is not positioned at loc_x0_y3, the robot is not positioned at loc_x0_y4, the robot is not positioned at loc_x1_y0, the robot is not positioned at loc_x1_y1, the robot is not positioned at loc_x1_y3, the robot is not positioned at loc_x2_y4 and the robot is not positioned at loc_x2_y5?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is linked to loc_x1_y1, loc_x0_y2 has been visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, the robot is positioned at loc_x0_y2, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y0 and loc_x1_y0, a path exists between loc_x0_y2 and loc_x0_y1, a path exists between loc_x0_y2 and loc_x0_y3, a path exists between loc_x0_y2 and loc_x1_y2, a path exists between loc_x0_y4 and loc_x0_y5, a path exists between loc_x0_y4 and loc_x1_y4, a path exists between loc_x0_y5 and loc_x1_y5, a path exists between loc_x1_y0 and loc_x1_y1, a path exists between loc_x1_y2 and loc_x2_y2, a path exists between loc_x1_y3 and loc_x0_y3, a path exists between loc_x1_y3 and loc_x1_y4, a path exists between loc_x1_y5 and loc_x1_y4, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y1 and loc_x3_y1, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x3_y2, a path exists between loc_x2_y3 and loc_x2_y2, a path exists between loc_x2_y4 and loc_x2_y3, a path exists between loc_x2_y4 and loc_x2_y5, a path exists between loc_x2_y5 and loc_x1_y5, a path exists between loc_x2_y5 and loc_x2_y4, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x3_y2 and loc_x3_y3, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y3 and loc_x3_y2, and a path exists between loc_x3_y3 and loc_x3_y4."}
{"question_id": "3ef4e357-b3dc-4b27-afab-29cbd2283e5d", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_13", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x4_y2 to loc_x3_y2, from loc_x3_y2, the robot moves to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, moves from loc_x2_y1 to loc_x3_y1, from loc_x3_y1, the robot moves to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, moves to loc_x3_y0 from loc_x4_y0, robot moves from loc_x3_y0 to loc_x2_y0, moves to loc_x1_y0 from loc_x2_y0 and from loc_x1_y0, the robot moves to loc_x0_y0 to reach the current state. In this state, is it True or False that loc_x2_y1 is connected to loc_x1_y1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, robot is at loc_x4_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot undergoes the following sequence of movements: it transitions from loc_x4_y2 to loc_x3_y2, then from loc_x3_y2 to loc_x2_y2, followed by a move from loc_x2_y2 to loc_x2_y1, then to loc_x3_y1 from loc_x2_y1, and subsequently from loc_x3_y1 to loc_x4_y1. The robot continues its movement from loc_x4_y1 to loc_x4_y0, then to loc_x3_y0 from loc_x4_y0, followed by a transition from loc_x3_y0 to loc_x2_y0, then to loc_x1_y0 from loc_x2_y0, and finally from loc_x1_y0 to loc_x0_y0, resulting in the current state. In this state, is it True or False that loc_x2_y1 is connected to loc_x1_y1?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y3 and loc_x1_y3, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x2_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 and loc_x2_y3, loc_x1_y3 and loc_x0_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x2_y0, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x4_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y1 and loc_x3_y1, loc_x4_y2 and loc_x3_y2, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4, the robot is at loc_x4_y2. \n\nAdditionally, the following connections exist: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x0_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x0_y4, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x4_y1."}
{"question_id": "aaa9b64b-c34b-4209-9774-1a75066b8583", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_12", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x1_y0 to loc_x0_y0, from loc_x0_y0, the robot moves to loc_x0_y1, from loc_x0_y1, the robot moves to loc_x1_y1, robot moves from loc_x1_y1 to loc_x2_y1, moves to loc_x2_y0 from loc_x2_y1, from loc_x2_y0, the robot moves to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x3_y2, moves from loc_x3_y2 to loc_x3_y1 and from loc_x3_y1, the robot moves to loc_x4_y1 to reach the current state. In this state, is it True or False that robot is not located at loc_x4_y1?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is visited, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x5_y1 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 and loc_x3_y3 are connected, loc_x4_y3 is connected to loc_x4_y4, loc_x4_y4 and loc_x4_y3 are connected, loc_x4_y4 and loc_x5_y4 are connected, loc_x4_y4 is connected to loc_x3_y4, loc_x5_y0 and loc_x4_y0 are connected, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 is connected to loc_x5_y3, robot is at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y4, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x4_y2, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x5_y1 and loc_x5_y0, there is a connection between loc_x5_y2 and loc_x5_y1 and there is a connection between loc_x5_y2 and loc_x5_y3.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot undergoes the following sequence of movements: it moves from loc_x1_y0 to loc_x0_y0, then from loc_x0_y0 to loc_x0_y1, followed by a move from loc_x0_y1 to loc_x1_y1, then to loc_x2_y1 from loc_x1_y1, and subsequently from loc_x2_y1 to loc_x2_y0. The robot then proceeds from loc_x2_y0 to loc_x3_y0, then to loc_x3_y1 from loc_x3_y0, and next to loc_x3_y2 from loc_x3_y1. After that, it moves back to loc_x3_y1 from loc_x3_y2, and finally from loc_x3_y1, it reaches loc_x4_y1. In this state, is it True or False that the robot is not located at loc_x4_y1?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y2 and loc_x0_y3, loc_x0_y3 and loc_x0_y2, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x0_y3, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y0 has been visited, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y1, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x3_y0 and loc_x2_y0, loc_x3_y1 and loc_x3_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y3 and loc_x4_y3, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x4_y1, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x5_y1, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 and loc_x4_y3, loc_x4_y3 and loc_x3_y3, loc_x4_y3 and loc_x4_y4, loc_x4_y4 and loc_x4_y3, loc_x4_y4 and loc_x5_y4, loc_x4_y4 and loc_x3_y4, loc_x5_y0 and loc_x4_y0, loc_x5_y0 and loc_x5_y1, loc_x5_y1 and loc_x4_y1, loc_x5_y1 and loc_x5_y2, loc_x5_y2 and loc_x4_y2, loc_x5_y3 and loc_x5_y2, loc_x5_y3 and loc_x4_y3, loc_x5_y3 and loc_x5_y4, loc_x5_y4 and loc_x4_y4, loc_x5_y4 and loc_x5_y3. The robot is currently at loc_x1_y0. Additionally, the following connections exist: loc_x0_y0 and loc_x1_y0, loc_x0_y2 and loc_x0_y1, loc_x0_y2 and loc_x1_y2, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x0_y2, loc_x1_y2 and loc_x2_y2, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x3_y2, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x5_y0, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x5_y2, loc_x4_y3 and loc_x4_y2, loc_x4_y3 and loc_x5_y3, loc_x5_y1 and loc_x5_y0, loc_x5_y2 and loc_x5_y1, and loc_x5_y2 and loc_x5_y3."}
{"question_id": "7a0b4318-1a55-4db8-9247-e6b831490f0e", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_16", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y2 to loc_x0_y1, moves from loc_x0_y1 to loc_x0_y0, from loc_x0_y0, the robot moves to loc_x1_y0, from loc_x1_y0, the robot moves to loc_x1_y1, robot moves from loc_x1_y1 to loc_x1_y2, moves to loc_x0_y2 from loc_x1_y2, robot moves from loc_x0_y2 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x0_y4, moves to loc_x0_y5 from loc_x0_y4 and robot moves from loc_x0_y5 to loc_x1_y5 to reach the current state. In this state, is it True or False that loc_x2_y2 is not connected to loc_x2_y3?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, robot is placed at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y4 and loc_x0_y5, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x0_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x2_y5, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot undergoes the following sequence of movements: it transitions from loc_x0_y2 to loc_x0_y1, then to loc_x0_y0, followed by a move to loc_x1_y0, then to loc_x1_y1, and subsequently to loc_x1_y2. From loc_x1_y2, the robot returns to loc_x0_y2, then proceeds to loc_x0_y3, loc_x0_y4, loc_x0_y5, and finally reaches loc_x1_y5. In this current state, is it True or False that loc_x2_y2 is not connected to loc_x2_y3?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is linked to loc_x1_y1, loc_x0_y2 has been visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, the robot is positioned at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x0_y1, a connection exists between loc_x0_y0 and loc_x1_y0, a connection exists between loc_x0_y2 and loc_x0_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y4 and loc_x0_y5, a connection exists between loc_x0_y4 and loc_x1_y4, a connection exists between loc_x0_y5 and loc_x1_y5, a connection exists between loc_x1_y0 and loc_x1_y1, a connection exists between loc_x1_y2 and loc_x2_y2, a connection exists between loc_x1_y3 and loc_x0_y3, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y5 and loc_x1_y4, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y2 and loc_x3_y2, a connection exists between loc_x2_y3 and loc_x2_y2, a connection exists between loc_x2_y4 and loc_x2_y3, a connection exists between loc_x2_y4 and loc_x2_y5, a connection exists between loc_x2_y5 and loc_x1_y5, a connection exists between loc_x2_y5 and loc_x2_y4, a connection exists between loc_x3_y1 and loc_x3_y0, a connection exists between loc_x3_y2 and loc_x2_y2, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, and a connection exists between loc_x3_y3 and loc_x3_y4."}
{"question_id": "d65ffba3-7e9c-40e4-ba6d-4b22b528a391", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_6", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x0_y2 to loc_x0_y1, moves to loc_x0_y0 from loc_x0_y1, moves from loc_x0_y0 to loc_x1_y0, robot moves from loc_x1_y0 to loc_x1_y1, moves from loc_x1_y1 to loc_x1_y2, moves from loc_x1_y2 to loc_x0_y2, robot moves from loc_x0_y2 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x0_y4, from loc_x0_y4, the robot moves to loc_x0_y5 and from loc_x0_y5, the robot moves to loc_x1_y5 to reach the current state. In this state, is it True or False that loc_x3_y1 is marked as visited?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, robot is placed at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y4 and loc_x0_y5, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x0_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x2_y5, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: the robot transitions from loc_x0_y2 to loc_x0_y1, then from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y0 to loc_x1_y0. Next, the robot proceeds from loc_x1_y0 to loc_x1_y1, then to loc_x1_y2, and subsequently returns to loc_x0_y2. The robot then continues from loc_x0_y2 to loc_x0_y3, then to loc_x0_y4, and from loc_x0_y4, it moves to loc_x0_y5. Finally, the robot moves from loc_x0_y5 to loc_x1_y5, reaching the current state. In this state, is it True or False that loc_x3_y1 is marked as visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is linked to loc_x1_y1, loc_x0_y2 has been visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, the robot is positioned at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x0_y1, a connection exists between loc_x0_y0 and loc_x1_y0, a connection exists between loc_x0_y2 and loc_x0_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y4 and loc_x0_y5, a connection exists between loc_x0_y4 and loc_x1_y4, a connection exists between loc_x0_y5 and loc_x1_y5, a connection exists between loc_x1_y0 and loc_x1_y1, a connection exists between loc_x1_y2 and loc_x2_y2, a connection exists between loc_x1_y3 and loc_x0_y3, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y5 and loc_x1_y4, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y2 and loc_x3_y2, a connection exists between loc_x2_y3 and loc_x2_y2, a connection exists between loc_x2_y4 and loc_x2_y3, a connection exists between loc_x2_y4 and loc_x2_y5, a connection exists between loc_x2_y5 and loc_x1_y5, a connection exists between loc_x2_y5 and loc_x2_y4, a connection exists between loc_x3_y1 and loc_x3_y0, a connection exists between loc_x3_y2 and loc_x2_y2, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, and a connection exists between loc_x3_y3 and loc_x3_y4."}
{"question_id": "46636ee9-6806-426b-a01b-542c5ed6f179", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x1_y0 to loc_x0_y0, moves from loc_x0_y0 to loc_x0_y1, from loc_x0_y1, the robot moves to loc_x1_y1, robot moves from loc_x1_y1 to loc_x2_y1, moves from loc_x2_y1 to loc_x2_y0, moves from loc_x2_y0 to loc_x3_y0, from loc_x3_y0, the robot moves to loc_x3_y1, moves from loc_x3_y1 to loc_x3_y2, from loc_x3_y2, the robot moves to loc_x3_y1, robot moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, robot moves from loc_x4_y0 to loc_x5_y0, from loc_x5_y0, the robot moves to loc_x5_y1, moves to loc_x5_y2 from loc_x5_y1, moves to loc_x4_y2 from loc_x5_y2, robot moves from loc_x4_y2 to loc_x4_y3, moves to loc_x5_y3 from loc_x4_y3, moves from loc_x5_y3 to loc_x5_y4 and moves from loc_x5_y4 to loc_x4_y4 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: loc_x0_y0 is visited, loc_x0_y1 is marked as visited, loc_x0_y2 is visited, loc_x0_y3 is visited, loc_x0_y4 is visited, loc_x1_y0 is marked as visited, loc_x1_y1 is marked as visited, loc_x1_y2 is marked as visited, loc_x1_y4 is visited, loc_x2_y0 is marked as visited, loc_x2_y1 is marked as visited, loc_x2_y2 is visited, loc_x2_y3 is marked as visited, loc_x3_y0 is marked as visited, loc_x3_y1 is visited, loc_x3_y2 is marked as visited, loc_x3_y3 is visited, loc_x3_y4 is marked as visited, loc_x4_y0 is visited, loc_x4_y1 is marked as visited, loc_x4_y2 is marked as visited, loc_x4_y3 is marked as visited, loc_x4_y4 is visited, loc_x5_y0 is marked as visited, loc_x5_y1 is marked as visited, loc_x5_y2 is marked as visited, loc_x5_y3 is marked as visited and loc_x5_y4 is marked as visited?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is visited, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x5_y1 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 and loc_x3_y3 are connected, loc_x4_y3 is connected to loc_x4_y4, loc_x4_y4 and loc_x4_y3 are connected, loc_x4_y4 and loc_x5_y4 are connected, loc_x4_y4 is connected to loc_x3_y4, loc_x5_y0 and loc_x4_y0 are connected, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 is connected to loc_x5_y3, robot is at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y4, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x4_y2, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x5_y1 and loc_x5_y0, there is a connection between loc_x5_y2 and loc_x5_y1 and there is a connection between loc_x5_y2 and loc_x5_y3.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot performs the following sequence of actions: it moves from loc_x1_y0 to loc_x0_y0, then to loc_x0_y1, followed by loc_x1_y1, then loc_x2_y1, loc_x2_y0, loc_x3_y0, loc_x3_y1, loc_x3_y2, back to loc_x3_y1, then loc_x4_y1, loc_x4_y0, loc_x5_y0, loc_x5_y1, loc_x5_y2, loc_x4_y2, loc_x4_y3, loc_x5_y3, loc_x5_y4, and finally loc_x4_y4 to reach its current state. In this state, are the following properties, which do not involve negations, True or False: loc_x0_y0 has been visited, loc_x0_y1 is marked as visited, loc_x0_y2 has been visited, loc_x0_y3 has been visited, loc_x0_y4 has been visited, loc_x1_y0 is marked as visited, loc_x1_y1 is marked as visited, loc_x1_y2 has been visited, loc_x1_y4 has been visited, loc_x2_y0 is marked as visited, loc_x2_y1 is marked as visited, loc_x2_y2 has been visited, loc_x2_y3 is marked as visited, loc_x3_y0 is marked as visited, loc_x3_y1 has been visited, loc_x3_y2 is marked as visited, loc_x3_y3 has been visited, loc_x3_y4 is marked as visited, loc_x4_y0 has been visited, loc_x4_y1 is marked as visited, loc_x4_y2 is marked as visited, loc_x4_y3 is marked as visited, loc_x4_y4 has been visited, loc_x5_y0 is marked as visited, loc_x5_y1 is marked as visited, loc_x5_y2 is marked as visited, loc_x5_y3 is marked as visited, and loc_x5_y4 is marked as visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y2 and loc_x0_y3, loc_x0_y3 and loc_x0_y2, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x0_y3, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y0 has been visited, loc_x1_y1 and loc_x2_y1, loc_x1_y1 and loc_x1_y2, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x3_y0 and loc_x2_y0, loc_x3_y1 and loc_x3_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y3 and loc_x4_y3, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x4_y1, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x5_y1, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 and loc_x4_y3, loc_x4_y3 and loc_x3_y3, loc_x4_y3 and loc_x4_y4, loc_x4_y4 and loc_x4_y3, loc_x4_y4 and loc_x5_y4, loc_x4_y4 and loc_x3_y4, loc_x5_y0 and loc_x4_y0, loc_x5_y0 and loc_x5_y1, loc_x5_y1 and loc_x4_y1, loc_x5_y1 and loc_x5_y2, loc_x5_y2 and loc_x4_y2, loc_x5_y3 and loc_x5_y2, loc_x5_y3 and loc_x4_y3, loc_x5_y3 and loc_x5_y4, loc_x5_y4 and loc_x4_y4, loc_x5_y4 and loc_x5_y3. The robot is currently at loc_x1_y0. Additionally, the following connections exist: loc_x0_y0 and loc_x1_y0, loc_x0_y2 and loc_x0_y1, loc_x0_y2 and loc_x1_y2, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x0_y2, loc_x1_y2 and loc_x2_y2, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x3_y2, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x5_y0, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x5_y2, loc_x4_y3 and loc_x4_y2, loc_x4_y3 and loc_x5_y3, loc_x5_y1 and loc_x5_y0, loc_x5_y2 and loc_x5_y1, and loc_x5_y2 and loc_x5_y3."}
{"question_id": "fe50f354-1725-4862-bcbc-113858d64e86", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_15", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x4_y2, the robot moves to loc_x3_y2, moves to loc_x2_y2 from loc_x3_y2, moves to loc_x2_y1 from loc_x2_y2, moves to loc_x3_y1 from loc_x2_y1, moves from loc_x3_y1 to loc_x4_y1, moves to loc_x4_y0 from loc_x4_y1, from loc_x4_y0, the robot moves to loc_x3_y0, robot moves from loc_x3_y0 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x1_y0 and from loc_x1_y0, the robot moves to loc_x0_y0 to reach the current state. In this state, is it True or False that loc_x3_y0 and loc_x0_y3 are not connected?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, robot is at loc_x4_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot undergoes a series of movements as follows: it starts at loc_x4_y2 and proceeds to loc_x3_y2, then to loc_x2_y2, followed by loc_x2_y1, then loc_x3_y1, then loc_x4_y1, then loc_x4_y0, then loc_x3_y0, then loc_x2_y0, then loc_x1_y0, and finally reaches loc_x0_y0. In this state, is it True or False that loc_x3_y0 and loc_x0_y3 are not connected?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y3 and loc_x1_y3, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x2_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 and loc_x2_y3, loc_x1_y3 and loc_x0_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x2_y0, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x4_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y1 and loc_x3_y1, loc_x4_y2 and loc_x3_y2, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4, the robot is at loc_x4_y2. \n\nAdditionally, the following connections exist: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x0_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x0_y4, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x4_y1."}
{"question_id": "97be21a9-e529-449d-8800-07b8d83b1003", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y3 to loc_x0_y4, moves to loc_x0_y3 from loc_x0_y4, robot moves from loc_x0_y3 to loc_x1_y3, from loc_x1_y3, the robot moves to loc_x1_y4, moves from loc_x1_y4 to loc_x2_y4, from loc_x2_y4, the robot moves to loc_x3_y4, moves from loc_x3_y4 to loc_x3_y3, moves to loc_x2_y3 from loc_x3_y3, moves from loc_x2_y3 to loc_x2_y2, moves from loc_x2_y2 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x1_y1, from loc_x1_y1, the robot moves to loc_x0_y1, robot moves from loc_x0_y1 to loc_x0_y0, from loc_x0_y0, the robot moves to loc_x1_y0, moves to loc_x2_y0 from loc_x1_y0, robot moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, moves from loc_x3_y1 to loc_x3_y2 and moves from loc_x3_y2 to loc_x4_y2 to reach the current state. In this state, is it True or False that loc_x0_y0 is not visited?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is marked as visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y3 and loc_x0_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y4 and there is a connection between loc_x4_y1 and loc_x4_y0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: the robot transitions from loc_x0_y3 to loc_x0_y4, then back to loc_x0_y3, followed by a move to loc_x1_y3. From loc_x1_y3, the robot proceeds to loc_x1_y4, then to loc_x2_y4, and from there to loc_x3_y4. The robot then moves to loc_x3_y3, followed by loc_x2_y3, then loc_x2_y2, and loc_x2_y1. Next, the robot moves to loc_x1_y1, then to loc_x0_y1, and from there to loc_x0_y0. The robot continues to loc_x1_y0, then loc_x2_y0, followed by loc_x3_y0. From loc_x3_y0, the robot moves to loc_x3_y1, then loc_x3_y2, and finally to loc_x4_y2 to reach the current state. In this state, is it True or False that loc_x0_y0 has not been visited?", "initial_state_nl_paraphrased": "loc_x0_y1 is adjacent to loc_x0_y0, loc_x0_y1 is adjacent to loc_x1_y1, loc_x0_y3 is adjacent to loc_x1_y3, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is adjacent to loc_x0_y3, loc_x0_y4 is adjacent to loc_x1_y4, loc_x1_y0 is adjacent to loc_x0_y0, loc_x1_y0 is adjacent to loc_x1_y1, loc_x1_y1 is adjacent to loc_x0_y1, loc_x1_y1 is adjacent to loc_x1_y0, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is adjacent to loc_x2_y3, loc_x1_y4 is adjacent to loc_x0_y4, loc_x1_y4 is adjacent to loc_x1_y3, loc_x1_y4 is adjacent to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is adjacent to loc_x3_y0, loc_x2_y1 is adjacent to loc_x2_y0, loc_x2_y1 is adjacent to loc_x3_y1, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is adjacent to loc_x2_y3, loc_x2_y2 is adjacent to loc_x3_y2, loc_x2_y3 is adjacent to loc_x2_y2, loc_x2_y3 is adjacent to loc_x1_y3, loc_x2_y4 is adjacent to loc_x2_y3, loc_x2_y4 is adjacent to loc_x3_y4, loc_x2_y4 is adjacent to loc_x1_y4, loc_x3_y0 is adjacent to loc_x2_y0, loc_x3_y0 is adjacent to loc_x4_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is adjacent to loc_x3_y0, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is adjacent to loc_x2_y4, loc_x3_y4 is adjacent to loc_x3_y3, loc_x4_y0 is adjacent to loc_x4_y1, loc_x4_y0 is adjacent to loc_x3_y0, loc_x4_y1 is adjacent to loc_x3_y1, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is adjacent to loc_x3_y2, loc_x4_y2 is adjacent to loc_x4_y1, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y0 and loc_x1_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y3 and loc_x0_y3, a path exists between loc_x1_y3 and loc_x1_y4, a path exists between loc_x2_y0 and loc_x1_y0, a path exists between loc_x2_y1 and loc_x1_y1, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y3 and loc_x2_y4, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x3_y2 and loc_x4_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y3 and loc_x3_y4 and a path exists between loc_x4_y1 and loc_x4_y0."}
{"question_id": "6fc0a80d-7722-4c76-80bc-be6af892c1ae", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_14", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x4_y2 to loc_x3_y2, from loc_x3_y2, the robot moves to loc_x2_y2, from loc_x2_y2, the robot moves to loc_x2_y1, robot moves from loc_x2_y1 to loc_x3_y1, from loc_x3_y1, the robot moves to loc_x4_y1, moves from loc_x4_y1 to loc_x4_y0, robot moves from loc_x4_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x2_y0, moves to loc_x1_y0 from loc_x2_y0 and from loc_x1_y0, the robot moves to loc_x0_y0 to reach the current state. In this state, is it True or False that loc_x1_y2 and loc_x4_y4 are connected?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, robot is at loc_x4_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot undergoes a series of movements as follows: it transitions from loc_x4_y2 to loc_x3_y2, then from loc_x3_y2 to loc_x2_y2, followed by a move from loc_x2_y2 to loc_x2_y1. The robot continues by moving from loc_x2_y1 to loc_x3_y1, then from loc_x3_y1 to loc_x4_y1, and subsequently from loc_x4_y1 to loc_x4_y0. Next, it moves from loc_x4_y0 to loc_x3_y0, then from loc_x3_y0 to loc_x2_y0, and from loc_x2_y0 to loc_x1_y0. Finally, the robot reaches its current state by moving from loc_x1_y0 to loc_x0_y0. In this state, is it True or False that loc_x1_y2 and loc_x4_y4 are connected?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y3 and loc_x1_y3, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x2_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 and loc_x2_y3, loc_x1_y3 and loc_x0_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x2_y0, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x4_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y1 and loc_x3_y1, loc_x4_y2 and loc_x3_y2, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4, the robot is at loc_x4_y2. \n\nAdditionally, the following connections exist: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x0_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x0_y4, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x4_y1."}
{"question_id": "ea9520e4-648c-48b8-b17d-fb75c92c7c93", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x4_y2 to loc_x3_y2 to reach the current state. In this state, is it True or False that loc_x1_y4 is not marked as visited?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, robot is at loc_x4_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the robot executes the following actions: it moves from loc_x4_y2 to loc_x3_y2, resulting in the current state. In this state, is it True or False that loc_x1_y4 has not been marked as visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y3 and loc_x1_y3, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x2_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 and loc_x2_y3, loc_x1_y3 and loc_x0_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x2_y0, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x4_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y1 and loc_x3_y1, loc_x4_y2 and loc_x3_y2, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4, the robot is at loc_x4_y2. \n\nAdditionally, the following connections exist: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x0_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x0_y4, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x4_y1."}
{"question_id": "ad1faacb-e336-4353-8168-22578fa91046", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y2 to loc_x0_y1, moves from loc_x0_y1 to loc_x0_y0, moves from loc_x0_y0 to loc_x1_y0, robot moves from loc_x1_y0 to loc_x1_y1, from loc_x1_y1, the robot moves to loc_x1_y2, robot moves from loc_x1_y2 to loc_x0_y2, moves to loc_x0_y3 from loc_x0_y2, robot moves from loc_x0_y3 to loc_x0_y4, from loc_x0_y4, the robot moves to loc_x0_y5, moves to loc_x1_y5 from loc_x0_y5, robot moves from loc_x1_y5 to loc_x1_y4, moves to loc_x1_y3 from loc_x1_y4, moves to loc_x2_y3 from loc_x1_y3, moves from loc_x2_y3 to loc_x2_y2, moves to loc_x2_y1 from loc_x2_y2, robot moves from loc_x2_y1 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1 and robot moves from loc_x3_y1 to loc_x3_y2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: loc_x0_y0 is visited, loc_x0_y1 is visited, loc_x0_y2 is visited, loc_x0_y3 is marked as visited, loc_x0_y4 is marked as visited, loc_x0_y5 is visited, loc_x1_y0 is marked as visited, loc_x1_y1 is marked as visited, loc_x1_y2 is visited, loc_x1_y3 is marked as visited, loc_x1_y4 is marked as visited, loc_x1_y5 is visited, loc_x2_y0 is visited, loc_x2_y1 is visited, loc_x2_y2 is marked as visited, loc_x2_y3 is marked as visited, loc_x3_y0 is visited, loc_x3_y1 is visited and loc_x3_y2 is visited?", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, robot is placed at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y4 and loc_x0_y5, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x0_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x2_y5, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot performs the following sequence of actions: it moves from loc_x0_y2 to loc_x0_y1, then to loc_x0_y0, followed by loc_x1_y0, then loc_x1_y1, and loc_x1_y2. From loc_x1_y2, the robot proceeds to loc_x0_y2, then to loc_x0_y3, loc_x0_y4, and loc_x0_y5. Next, it moves to loc_x1_y5, followed by loc_x1_y4, loc_x1_y3, loc_x2_y3, loc_x2_y2, loc_x2_y1, and loc_x2_y0. The robot then moves to loc_x3_y0, loc_x3_y1, and finally loc_x3_y2, reaching the current state. In this state, are the following properties, which do not involve negations, True or False: loc_x0_y0 has been visited, loc_x0_y1 has been visited, loc_x0_y2 has been visited, loc_x0_y3 is marked as visited, loc_x0_y4 is marked as visited, loc_x0_y5 has been visited, loc_x1_y0 is marked as visited, loc_x1_y1 is marked as visited, loc_x1_y2 has been visited, loc_x1_y3 is marked as visited, loc_x1_y4 is marked as visited, loc_x1_y5 has been visited, loc_x2_y0 has been visited, loc_x2_y1 has been visited, loc_x2_y2 is marked as visited, loc_x2_y3 is marked as visited, loc_x3_y0 has been visited, loc_x3_y1 has been visited, and loc_x3_y2 has been visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is linked to loc_x1_y1, loc_x0_y2 has been visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, the robot is positioned at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x0_y1, a connection exists between loc_x0_y0 and loc_x1_y0, a connection exists between loc_x0_y2 and loc_x0_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y4 and loc_x0_y5, a connection exists between loc_x0_y4 and loc_x1_y4, a connection exists between loc_x0_y5 and loc_x1_y5, a connection exists between loc_x1_y0 and loc_x1_y1, a connection exists between loc_x1_y2 and loc_x2_y2, a connection exists between loc_x1_y3 and loc_x0_y3, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y5 and loc_x1_y4, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y2 and loc_x3_y2, a connection exists between loc_x2_y3 and loc_x2_y2, a connection exists between loc_x2_y4 and loc_x2_y3, a connection exists between loc_x2_y4 and loc_x2_y5, a connection exists between loc_x2_y5 and loc_x1_y5, a connection exists between loc_x2_y5 and loc_x2_y4, a connection exists between loc_x3_y1 and loc_x3_y0, a connection exists between loc_x3_y2 and loc_x2_y2, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, and a connection exists between loc_x3_y3 and loc_x3_y4."}
{"question_id": "b3886a02-b0b2-4d0b-b9af-ff02742f4035", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_8", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, is it True or False that loc_x0_y0 is not marked as visited?", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is visited, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x5_y1 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 and loc_x3_y3 are connected, loc_x4_y3 is connected to loc_x4_y4, loc_x4_y4 and loc_x4_y3 are connected, loc_x4_y4 and loc_x5_y4 are connected, loc_x4_y4 is connected to loc_x3_y4, loc_x5_y0 and loc_x4_y0 are connected, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 is connected to loc_x5_y3, robot is at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y4, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x4_y2, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x5_y1 and loc_x5_y0, there is a connection between loc_x5_y2 and loc_x5_y1 and there is a connection between loc_x5_y2 and loc_x5_y3.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, is it True or False that loc_x0_y0 has not been marked as visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y2 is adjacent to loc_x0_y3, loc_x0_y3 is adjacent to loc_x0_y2, loc_x0_y3 is adjacent to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are adjacent, loc_x0_y4 is adjacent to loc_x1_y4, loc_x1_y0 is adjacent to loc_x2_y0, loc_x1_y0 has been visited, loc_x1_y1 is adjacent to loc_x2_y1, loc_x1_y2 and loc_x1_y1 are adjacent, loc_x1_y4 and loc_x0_y4 are adjacent, loc_x2_y0 and loc_x1_y0 are adjacent, loc_x2_y0 and loc_x3_y0 are adjacent, loc_x2_y1 and loc_x1_y1 are adjacent, loc_x2_y1 and loc_x2_y0 are adjacent, loc_x2_y1 is adjacent to loc_x2_y2, loc_x2_y2 is adjacent to loc_x2_y1, loc_x2_y2 is adjacent to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are adjacent, loc_x2_y3 is adjacent to loc_x3_y3, loc_x3_y0 is adjacent to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are adjacent, loc_x3_y2 and loc_x3_y3 are adjacent, loc_x3_y2 is adjacent to loc_x2_y2, loc_x3_y2 is adjacent to loc_x3_y1, loc_x3_y3 and loc_x3_y2 are adjacent, loc_x3_y3 and loc_x4_y3 are adjacent, loc_x3_y4 is adjacent to loc_x3_y3, loc_x4_y0 and loc_x4_y1 are adjacent, loc_x4_y0 is adjacent to loc_x3_y0, loc_x4_y1 and loc_x5_y1 are adjacent, loc_x4_y1 is adjacent to loc_x3_y1, loc_x4_y1 is adjacent to loc_x4_y0, loc_x4_y1 is adjacent to loc_x4_y2, loc_x4_y2 is adjacent to loc_x4_y1, loc_x4_y2 is adjacent to loc_x4_y3, loc_x4_y3 and loc_x3_y3 are adjacent, loc_x4_y3 is adjacent to loc_x4_y4, loc_x4_y4 and loc_x4_y3 are adjacent, loc_x4_y4 and loc_x5_y4 are adjacent, loc_x4_y4 is adjacent to loc_x3_y4, loc_x5_y0 and loc_x4_y0 are adjacent, loc_x5_y0 is adjacent to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are adjacent, loc_x5_y1 is adjacent to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are adjacent, loc_x5_y3 and loc_x5_y2 are adjacent, loc_x5_y3 is adjacent to loc_x4_y3, loc_x5_y3 is adjacent to loc_x5_y4, loc_x5_y4 and loc_x4_y4 are adjacent, loc_x5_y4 is adjacent to loc_x5_y3, the robot's current location is loc_x1_y0, a path exists between loc_x0_y0 and loc_x1_y0, a path exists between loc_x0_y2 and loc_x0_y1, a path exists between loc_x0_y2 and loc_x1_y2, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x1_y1, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y1 and loc_x1_y0, a path exists between loc_x1_y1 and loc_x1_y2, a path exists between loc_x1_y2 and loc_x0_y2, a path exists between loc_x1_y2 and loc_x2_y2, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y1 and loc_x3_y1, a path exists between loc_x2_y2 and loc_x1_y2, a path exists between loc_x2_y2 and loc_x3_y2, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y0 and loc_x4_y0, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x4_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y3 and loc_x3_y4, a path exists between loc_x3_y4 and loc_x4_y4, a path exists between loc_x4_y0 and loc_x5_y0, a path exists between loc_x4_y2 and loc_x3_y2, a path exists between loc_x4_y2 and loc_x5_y2, a path exists between loc_x4_y3 and loc_x4_y2, a path exists between loc_x4_y3 and loc_x5_y3, a path exists between loc_x5_y1 and loc_x5_y0, a path exists between loc_x5_y2 and loc_x5_y1, and a path exists between loc_x5_y2 and loc_x5_y3."}
{"question_id": "d15eaf53-011b-41f4-bdaa-152965eceebd", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_6", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x0_y3, the robot moves to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y3, moves to loc_x1_y3 from loc_x0_y3, moves from loc_x1_y3 to loc_x1_y4, moves to loc_x2_y4 from loc_x1_y4, from loc_x2_y4, the robot moves to loc_x3_y4, moves to loc_x3_y3 from loc_x3_y4, from loc_x3_y3, the robot moves to loc_x2_y3, robot moves from loc_x2_y3 to loc_x2_y2, from loc_x2_y2, the robot moves to loc_x2_y1, moves from loc_x2_y1 to loc_x1_y1, from loc_x1_y1, the robot moves to loc_x0_y1, robot moves from loc_x0_y1 to loc_x0_y0, moves from loc_x0_y0 to loc_x1_y0, robot moves from loc_x1_y0 to loc_x2_y0, moves to loc_x3_y0 from loc_x2_y0, robot moves from loc_x3_y0 to loc_x3_y1, moves from loc_x3_y1 to loc_x3_y2 and moves from loc_x3_y2 to loc_x4_y2 to reach the current state. In this state, is it True or False that loc_x4_y0 is visited?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is marked as visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y3 and loc_x0_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y4 and there is a connection between loc_x4_y1 and loc_x4_y0.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot undergoes the following sequence of actions: it starts at loc_x0_y3 and moves to loc_x0_y4, then returns to loc_x0_y3, proceeds to loc_x1_y3, then to loc_x1_y4, followed by loc_x2_y4, then loc_x3_y4, and then back to loc_x3_y3. From there, it moves to loc_x2_y3, then to loc_x2_y2, and loc_x2_y1, before moving to loc_x1_y1, loc_x0_y1, and loc_x0_y0. The robot then moves to loc_x1_y0, loc_x2_y0, and loc_x3_y0, followed by loc_x3_y1, loc_x3_y2, and finally loc_x4_y2. In this state, is it True or False that loc_x4_y0 has been visited?", "initial_state_nl_paraphrased": "loc_x0_y1 is adjacent to loc_x0_y0, loc_x0_y1 is adjacent to loc_x1_y1, loc_x0_y3 is adjacent to loc_x1_y3, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is adjacent to loc_x0_y3, loc_x0_y4 is adjacent to loc_x1_y4, loc_x1_y0 is adjacent to loc_x0_y0, loc_x1_y0 is adjacent to loc_x1_y1, loc_x1_y1 is adjacent to loc_x0_y1, loc_x1_y1 is adjacent to loc_x1_y0, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is adjacent to loc_x2_y3, loc_x1_y4 is adjacent to loc_x0_y4, loc_x1_y4 is adjacent to loc_x1_y3, loc_x1_y4 is adjacent to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is adjacent to loc_x3_y0, loc_x2_y1 is adjacent to loc_x2_y0, loc_x2_y1 is adjacent to loc_x3_y1, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is adjacent to loc_x2_y3, loc_x2_y2 is adjacent to loc_x3_y2, loc_x2_y3 is adjacent to loc_x2_y2, loc_x2_y3 is adjacent to loc_x1_y3, loc_x2_y4 is adjacent to loc_x2_y3, loc_x2_y4 is adjacent to loc_x3_y4, loc_x2_y4 is adjacent to loc_x1_y4, loc_x3_y0 is adjacent to loc_x2_y0, loc_x3_y0 is adjacent to loc_x4_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is adjacent to loc_x3_y0, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is adjacent to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 is adjacent to loc_x4_y1, loc_x4_y0 is adjacent to loc_x3_y0, loc_x4_y1 is adjacent to loc_x3_y1, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is adjacent to loc_x3_y2, loc_x4_y2 is adjacent to loc_x4_y1, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y0 and loc_x1_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y3 and loc_x0_y3, a path exists between loc_x1_y3 and loc_x1_y4, a path exists between loc_x2_y0 and loc_x1_y0, a path exists between loc_x2_y1 and loc_x1_y1, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y3 and loc_x2_y4, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x3_y2 and loc_x4_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y3 and loc_x3_y4 and a path exists between loc_x4_y1 and loc_x4_y0."}
{"question_id": "536dbea4-f94e-4d35-a344-dab831719dac", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_2_question_13", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y0 from loc_x1_y0 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: robot is at loc_x0_y0?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is visited, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x5_y1 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 and loc_x3_y3 are connected, loc_x4_y3 is connected to loc_x4_y4, loc_x4_y4 and loc_x4_y3 are connected, loc_x4_y4 and loc_x5_y4 are connected, loc_x4_y4 is connected to loc_x3_y4, loc_x5_y0 and loc_x4_y0 are connected, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 is connected to loc_x5_y3, robot is at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y4, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x4_y2, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x5_y1 and loc_x5_y0, there is a connection between loc_x5_y2 and loc_x5_y1 and there is a connection between loc_x5_y2 and loc_x5_y3.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, are all of the following non-negated properties of the state True or False: is the robot at loc_x0_y0?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y2 is adjacent to loc_x0_y3, loc_x0_y3 is adjacent to loc_x0_y2, loc_x0_y3 is adjacent to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are adjacent, loc_x0_y4 is adjacent to loc_x1_y4, loc_x1_y0 is adjacent to loc_x2_y0, loc_x1_y0 has been visited, loc_x1_y1 is adjacent to loc_x2_y1, loc_x1_y2 and loc_x1_y1 are adjacent, loc_x1_y4 and loc_x0_y4 are adjacent, loc_x2_y0 and loc_x1_y0 are adjacent, loc_x2_y0 and loc_x3_y0 are adjacent, loc_x2_y1 and loc_x1_y1 are adjacent, loc_x2_y1 and loc_x2_y0 are adjacent, loc_x2_y1 is adjacent to loc_x2_y2, loc_x2_y2 is adjacent to loc_x2_y1, loc_x2_y2 is adjacent to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are adjacent, loc_x2_y3 is adjacent to loc_x3_y3, loc_x3_y0 is adjacent to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are adjacent, loc_x3_y2 and loc_x3_y3 are adjacent, loc_x3_y2 is adjacent to loc_x2_y2, loc_x3_y2 is adjacent to loc_x3_y1, loc_x3_y3 and loc_x3_y2 are adjacent, loc_x3_y3 and loc_x4_y3 are adjacent, loc_x3_y4 is adjacent to loc_x3_y3, loc_x4_y0 and loc_x4_y1 are adjacent, loc_x4_y0 is adjacent to loc_x3_y0, loc_x4_y1 and loc_x5_y1 are adjacent, loc_x4_y1 is adjacent to loc_x3_y1, loc_x4_y1 is adjacent to loc_x4_y0, loc_x4_y1 is adjacent to loc_x4_y2, loc_x4_y2 is adjacent to loc_x4_y1, loc_x4_y2 is adjacent to loc_x4_y3, loc_x4_y3 and loc_x3_y3 are adjacent, loc_x4_y3 is adjacent to loc_x4_y4, loc_x4_y4 and loc_x4_y3 are adjacent, loc_x4_y4 and loc_x5_y4 are adjacent, loc_x4_y4 is adjacent to loc_x3_y4, loc_x5_y0 and loc_x4_y0 are adjacent, loc_x5_y0 is adjacent to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are adjacent, loc_x5_y1 is adjacent to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are adjacent, loc_x5_y3 and loc_x5_y2 are adjacent, loc_x5_y3 is adjacent to loc_x4_y3, loc_x5_y3 is adjacent to loc_x5_y4, loc_x5_y4 and loc_x4_y4 are adjacent, loc_x5_y4 is adjacent to loc_x5_y3, the robot is currently at loc_x1_y0, a path exists between loc_x0_y0 and loc_x1_y0, a path exists between loc_x0_y2 and loc_x0_y1, a path exists between loc_x0_y2 and loc_x1_y2, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x1_y1, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y1 and loc_x1_y0, a path exists between loc_x1_y1 and loc_x1_y2, a path exists between loc_x1_y2 and loc_x0_y2, a path exists between loc_x1_y2 and loc_x2_y2, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y1 and loc_x3_y1, a path exists between loc_x2_y2 and loc_x1_y2, a path exists between loc_x2_y2 and loc_x3_y2, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y0 and loc_x4_y0, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x4_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y3 and loc_x3_y4, a path exists between loc_x3_y4 and loc_x4_y4, a path exists between loc_x4_y0 and loc_x5_y0, a path exists between loc_x4_y2 and loc_x3_y2, a path exists between loc_x4_y2 and loc_x5_y2, a path exists between loc_x4_y3 and loc_x4_y2, a path exists between loc_x4_y3 and loc_x5_y3, a path exists between loc_x5_y1 and loc_x5_y0, a path exists between loc_x5_y2 and loc_x5_y1, and a path exists between loc_x5_y2 and loc_x5_y3."}
{"question_id": "5d7f86b2-e518-46f8-86d2-85045437bda5", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x0_y2, the robot moves to loc_x0_y1 to reach the current state. In this state, is it True or False that loc_x3_y4 is not marked as visited?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, robot is placed at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y4 and loc_x0_y5, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x0_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x2_y5, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: the robot moves from loc_x0_y2 to loc_x0_y1 to reach the current state. In this state, is it True or False that loc_x3_y4 has not been marked as visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is linked to loc_x1_y1, loc_x0_y2 has been visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, the robot is positioned at loc_x0_y2, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y0 and loc_x1_y0, a path exists between loc_x0_y2 and loc_x0_y1, a path exists between loc_x0_y2 and loc_x0_y3, a path exists between loc_x0_y2 and loc_x1_y2, a path exists between loc_x0_y4 and loc_x0_y5, a path exists between loc_x0_y4 and loc_x1_y4, a path exists between loc_x0_y5 and loc_x1_y5, a path exists between loc_x1_y0 and loc_x1_y1, a path exists between loc_x1_y2 and loc_x2_y2, a path exists between loc_x1_y3 and loc_x0_y3, a path exists between loc_x1_y3 and loc_x1_y4, a path exists between loc_x1_y5 and loc_x1_y4, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y1 and loc_x3_y1, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x3_y2, a path exists between loc_x2_y3 and loc_x2_y2, a path exists between loc_x2_y4 and loc_x2_y3, a path exists between loc_x2_y4 and loc_x2_y5, a path exists between loc_x2_y5 and loc_x1_y5, a path exists between loc_x2_y5 and loc_x2_y4, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x3_y2 and loc_x3_y3, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y3 and loc_x3_y2, and a path exists between loc_x3_y3 and loc_x3_y4."}
{"question_id": "f0be6215-4814-4bf7-912b-5acdb44ae5ae", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_9", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x4_y2 to loc_x3_y2, robot moves from loc_x3_y2 to loc_x2_y2, from loc_x2_y2, the robot moves to loc_x2_y1, robot moves from loc_x2_y1 to loc_x3_y1, moves from loc_x3_y1 to loc_x4_y1, from loc_x4_y1, the robot moves to loc_x4_y0, robot moves from loc_x4_y0 to loc_x3_y0, moves from loc_x3_y0 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x1_y0 and robot moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, is it True or False that robot is at loc_x0_y0?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, robot is at loc_x4_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot undergoes a series of movements: it transitions from loc_x4_y2 to loc_x3_y2, then from loc_x3_y2 to loc_x2_y2, followed by a move from loc_x2_y2 to loc_x2_y1, then from loc_x2_y1 to loc_x3_y1, and subsequently from loc_x3_y1 to loc_x4_y1. The robot continues its movement from loc_x4_y1 to loc_x4_y0, then from loc_x4_y0 to loc_x3_y0, followed by a transition from loc_x3_y0 to loc_x2_y0, and then from loc_x2_y0 to loc_x1_y0. Finally, the robot moves from loc_x1_y0 to loc_x0_y0 to reach its current state. In this state, is it True or False that the robot is at loc_x0_y0?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y3 and loc_x1_y3, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x2_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 and loc_x2_y3, loc_x1_y3 and loc_x0_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x2_y0, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x4_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y1 and loc_x3_y1, loc_x4_y2 and loc_x3_y2, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4, the robot is at loc_x4_y2. \n\nAdditionally, the following connections exist: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x0_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x0_y4, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x4_y1."}
{"question_id": "9be433c0-f8c1-4798-8afa-09b0b46749a0", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_2_question_9", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y3, moves to loc_x1_y3 from loc_x0_y3, robot moves from loc_x1_y3 to loc_x1_y4, moves from loc_x1_y4 to loc_x2_y4, robot moves from loc_x2_y4 to loc_x3_y4, moves from loc_x3_y4 to loc_x3_y3, moves to loc_x2_y3 from loc_x3_y3, robot moves from loc_x2_y3 to loc_x2_y2, moves from loc_x2_y2 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x0_y1, robot moves from loc_x0_y1 to loc_x0_y0, moves from loc_x0_y0 to loc_x1_y0, from loc_x1_y0, the robot moves to loc_x2_y0, moves from loc_x2_y0 to loc_x3_y0, moves from loc_x3_y0 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x3_y2 and from loc_x3_y2, the robot moves to loc_x4_y2 to reach the current state. In this state, are all of the following valid properties of the state that involve negations True or False: loc_x4_y0 is not marked as visited and loc_x4_y1 is not marked as visited?", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is marked as visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y3 and loc_x0_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y4 and there is a connection between loc_x4_y1 and loc_x4_y0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot executes the following sequence of actions: it moves from loc_x0_y3 to loc_x0_y4, then back to loc_x0_y3, followed by a move to loc_x1_y3, then to loc_x1_y4, and subsequently to loc_x2_y4, loc_x3_y4, loc_x3_y3, loc_x2_y3, loc_x2_y2, and loc_x2_y1. The robot then proceeds to loc_x1_y1, loc_x0_y1, loc_x0_y0, loc_x1_y0, loc_x2_y0, loc_x3_y0, loc_x3_y1, and loc_x3_y2, before finally reaching loc_x4_y2. In this resulting state, are the following properties that involve negations true or false: loc_x4_y0 is not marked as visited and loc_x4_y1 is not marked as visited?", "initial_state_nl_paraphrased": "loc_x0_y1 is adjacent to loc_x0_y0, loc_x0_y1 is adjacent to loc_x1_y1, loc_x0_y3 is adjacent to loc_x1_y3, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 has been visited, loc_x0_y4 is adjacent to loc_x0_y3, loc_x0_y4 is adjacent to loc_x1_y4, loc_x1_y0 is adjacent to loc_x0_y0, loc_x1_y0 is adjacent to loc_x1_y1, loc_x1_y1 is adjacent to loc_x0_y1, loc_x1_y1 is adjacent to loc_x1_y0, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is adjacent to loc_x2_y3, loc_x1_y4 is adjacent to loc_x0_y4, loc_x1_y4 is adjacent to loc_x1_y3, loc_x1_y4 is adjacent to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is adjacent to loc_x3_y0, loc_x2_y1 is adjacent to loc_x2_y0, loc_x2_y1 is adjacent to loc_x3_y1, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is adjacent to loc_x2_y3, loc_x2_y2 is adjacent to loc_x3_y2, loc_x2_y3 is adjacent to loc_x2_y2, loc_x2_y3 is adjacent to loc_x1_y3, loc_x2_y4 is adjacent to loc_x2_y3, loc_x2_y4 is adjacent to loc_x3_y4, loc_x2_y4 is adjacent to loc_x1_y4, loc_x3_y0 is adjacent to loc_x2_y0, loc_x3_y0 is adjacent to loc_x4_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is adjacent to loc_x3_y0, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is adjacent to loc_x2_y4, loc_x3_y4 is adjacent to loc_x3_y3, loc_x4_y0 is adjacent to loc_x4_y1, loc_x4_y0 is adjacent to loc_x3_y0, loc_x4_y1 is adjacent to loc_x3_y1, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is adjacent to loc_x3_y2, loc_x4_y2 is adjacent to loc_x4_y1, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y0 and loc_x1_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y3 and loc_x0_y3, a path exists between loc_x1_y3 and loc_x1_y4, a path exists between loc_x2_y0 and loc_x1_y0, a path exists between loc_x2_y1 and loc_x1_y1, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y3 and loc_x2_y4, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x3_y2 and loc_x4_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y3 and loc_x3_y4, and a path exists between loc_x4_y1 and loc_x4_y0."}
{"question_id": "fa4cd6a9-74ad-4123-bb57-c6a266372a09", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "fluent_tracking", "question_name": "iter_1_question_13", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x1_y0 to loc_x0_y0, moves from loc_x0_y0 to loc_x0_y1, robot moves from loc_x0_y1 to loc_x1_y1, moves to loc_x2_y1 from loc_x1_y1, moves from loc_x2_y1 to loc_x2_y0, moves from loc_x2_y0 to loc_x3_y0, moves to loc_x3_y1 from loc_x3_y0, from loc_x3_y1, the robot moves to loc_x3_y2, moves to loc_x3_y1 from loc_x3_y2 and robot moves from loc_x3_y1 to loc_x4_y1 to reach the current state. In this state, is it True or False that loc_x2_y3 and loc_x3_y3 are connected?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y0 is visited, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x5_y1 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 and loc_x3_y3 are connected, loc_x4_y3 is connected to loc_x4_y4, loc_x4_y4 and loc_x4_y3 are connected, loc_x4_y4 and loc_x5_y4 are connected, loc_x4_y4 is connected to loc_x3_y4, loc_x5_y0 and loc_x4_y0 are connected, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 is connected to loc_x5_y3, robot is at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y4, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x4_y2, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x5_y1 and loc_x5_y0, there is a connection between loc_x5_y2 and loc_x5_y1 and there is a connection between loc_x5_y2 and loc_x5_y3.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot undergoes the following sequence of actions: it moves from loc_x1_y0 to loc_x0_y0, then to loc_x0_y1, followed by a move to loc_x1_y1, then to loc_x2_y1, and subsequently to loc_x2_y0. The robot then proceeds to loc_x3_y0, followed by a move to loc_x3_y1, then to loc_x3_y2, and back to loc_x3_y1. Finally, it moves from loc_x3_y1 to loc_x4_y1, reaching the current state. In this state, is it True or False that loc_x2_y3 and loc_x3_y3 are connected?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y2 and loc_x0_y3, loc_x0_y3 and loc_x0_y2, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x0_y3, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y0 has been visited, loc_x1_y1 and loc_x2_y1, loc_x1_y1 and loc_x1_y2, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x3_y0 and loc_x2_y0, loc_x3_y1 and loc_x3_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y3 and loc_x4_y3, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x4_y1, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x5_y1, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 and loc_x4_y3, loc_x4_y3 and loc_x3_y3, loc_x4_y3 and loc_x4_y4, loc_x4_y4 and loc_x4_y3, loc_x4_y4 and loc_x5_y4, loc_x4_y4 and loc_x3_y4, loc_x5_y0 and loc_x4_y0, loc_x5_y0 and loc_x5_y1, loc_x5_y1 and loc_x4_y1, loc_x5_y1 and loc_x5_y2, loc_x5_y2 and loc_x4_y2, loc_x5_y3 and loc_x5_y2, loc_x5_y3 and loc_x4_y3, loc_x5_y3 and loc_x5_y4, loc_x5_y4 and loc_x4_y4, loc_x5_y4 and loc_x5_y3. The robot is currently at loc_x1_y0. Additionally, the following connections exist: loc_x0_y0 and loc_x1_y0, loc_x0_y2 and loc_x0_y1, loc_x0_y2 and loc_x1_y2, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x0_y2, loc_x1_y2 and loc_x2_y2, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x3_y2, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x5_y0, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x5_y2, loc_x4_y3 and loc_x4_y2, loc_x4_y3 and loc_x5_y3, loc_x5_y1 and loc_x5_y0, loc_x5_y2 and loc_x5_y1, and loc_x5_y2 and loc_x5_y3."}
{"question_id": "532e571d-5491-41fd-aafd-07706224fcda", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_12", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y2 to loc_x0_y1 to reach the current state. In this state, is it True or False that robot is not placed at loc_x0_y1?", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, robot is placed at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y4 and loc_x0_y5, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x0_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x2_y5, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: the robot moves from loc_x0_y2 to loc_x0_y1 to reach the current state. In this state, is it True or False that the robot is not located at loc_x0_y1?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is linked to loc_x1_y1, loc_x0_y2 has been visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, the robot is positioned at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x0_y1, a connection exists between loc_x0_y0 and loc_x1_y0, a connection exists between loc_x0_y2 and loc_x0_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y4 and loc_x0_y5, a connection exists between loc_x0_y4 and loc_x1_y4, a connection exists between loc_x0_y5 and loc_x1_y5, a connection exists between loc_x1_y0 and loc_x1_y1, a connection exists between loc_x1_y2 and loc_x2_y2, a connection exists between loc_x1_y3 and loc_x0_y3, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y5 and loc_x1_y4, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y2 and loc_x3_y2, a connection exists between loc_x2_y3 and loc_x2_y2, a connection exists between loc_x2_y4 and loc_x2_y3, a connection exists between loc_x2_y4 and loc_x2_y5, a connection exists between loc_x2_y5 and loc_x1_y5, a connection exists between loc_x2_y5 and loc_x2_y4, a connection exists between loc_x3_y1 and loc_x3_y0, a connection exists between loc_x3_y2 and loc_x2_y2, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, and a connection exists between loc_x3_y3 and loc_x3_y4."}
{"question_id": "1f230750-c051-45fc-8a8e-6c7c8090172e", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "fluent_tracking", "question_name": "iter_1_question_15", "fluent_type": "static_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y4 from loc_x0_y3, moves from loc_x0_y4 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x1_y3, robot moves from loc_x1_y3 to loc_x1_y4, robot moves from loc_x1_y4 to loc_x2_y4, moves from loc_x2_y4 to loc_x3_y4, from loc_x3_y4, the robot moves to loc_x3_y3, robot moves from loc_x3_y3 to loc_x2_y3, robot moves from loc_x2_y3 to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, moves from loc_x2_y1 to loc_x1_y1, moves to loc_x0_y1 from loc_x1_y1, moves to loc_x0_y0 from loc_x0_y1, robot moves from loc_x0_y0 to loc_x1_y0, moves from loc_x1_y0 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, moves to loc_x3_y2 from loc_x3_y1 and robot moves from loc_x3_y2 to loc_x4_y2 to reach the current state. In this state, is it True or False that loc_x3_y3 and loc_x0_y1 are not connected?", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is marked as visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y1, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y3 and loc_x0_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x4_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y4 and there is a connection between loc_x4_y1 and loc_x4_y0.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot moves from loc_x0_y3 to loc_x0_y4 and then back to loc_x0_y3, then proceeds from loc_x0_y3 to loc_x1_y3, followed by loc_x1_y3 to loc_x1_y4, then loc_x1_y4 to loc_x2_y4, and loc_x2_y4 to loc_x3_y4. From loc_x3_y4, the robot moves to loc_x3_y3, then to loc_x2_y3, followed by loc_x2_y3 to loc_x2_y2, loc_x2_y2 to loc_x2_y1, and loc_x2_y1 to loc_x1_y1. The robot then moves from loc_x1_y1 to loc_x0_y1, then to loc_x0_y0, followed by loc_x0_y0 to loc_x1_y0, loc_x1_y0 to loc_x2_y0, and loc_x2_y0 to loc_x3_y0. The robot then moves from loc_x3_y0 to loc_x3_y1, then to loc_x3_y2, and finally from loc_x3_y2 to loc_x4_y2, reaching the current state. In this state, is it True or False that loc_x3_y3 and loc_x0_y1 are not connected?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x1_y3, loc_x0_y3 and loc_x0_y4, loc_x0_y3 is marked as visited, loc_x0_y4 is connected to loc_x0_y3, loc_x0_y4 is connected to loc_x1_y4, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x3_y1, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y1, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y0 and loc_x1_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y3 and loc_x0_y3, a path exists between loc_x1_y3 and loc_x1_y4, a path exists between loc_x2_y0 and loc_x1_y0, a path exists between loc_x2_y1 and loc_x1_y1, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y3 and loc_x2_y4, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x3_y2 and loc_x4_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y3 and loc_x3_y4, and a path exists between loc_x4_y1 and loc_x4_y0."}
{"question_id": "d6ddac27-8fb7-4a1c-9918-ca0895fe8cc0", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "fluent_tracking", "question_name": "iter_1_question_6", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x4_y2, the robot moves to loc_x3_y2, from loc_x3_y2, the robot moves to loc_x2_y2, moves from loc_x2_y2 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x3_y1, moves to loc_x4_y1 from loc_x3_y1, moves to loc_x4_y0 from loc_x4_y1, robot moves from loc_x4_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x2_y0, moves from loc_x2_y0 to loc_x1_y0 and moves to loc_x0_y0 from loc_x1_y0 to reach the current state. In this state, is it True or False that loc_x0_y1 is visited?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, robot is at loc_x4_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y3 and loc_x2_y4, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Starting from the initial condition, the robot undergoes a series of movements: it begins at loc_x4_y2 and proceeds to loc_x3_y2, then to loc_x2_y2, followed by loc_x2_y1, then loc_x3_y1, then loc_x4_y1, then loc_x4_y0, then loc_x3_y0, then loc_x2_y0, then loc_x1_y0, and finally loc_x0_y0. Given this sequence of movements, the question arises: has loc_x0_y1 been visited, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y3 and loc_x1_y3, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x2_y0, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x2_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 and loc_x2_y3, loc_x1_y3 and loc_x0_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x2_y0, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x4_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x4_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y1 and loc_x3_y1, loc_x4_y2 and loc_x3_y2, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4, the robot is at loc_x4_y2. \n\nAdditionally, the following connections exist: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x0_y3, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x0_y4, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x4_y1, and loc_x4_y2 and loc_x4_y1."}
{"question_id": "6d14a10c-4430-4ce8-93f4-c62bca04698d", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_2_question_14", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y1 from loc_x0_y2 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: robot is at loc_x0_y3, robot is at loc_x0_y5, robot is at loc_x1_y3, robot is at loc_x1_y4, robot is at loc_x1_y5, robot is at loc_x2_y4, robot is at loc_x3_y1, robot is located at loc_x0_y1, robot is located at loc_x0_y4, robot is located at loc_x1_y0, robot is located at loc_x1_y2, robot is located at loc_x2_y0, robot is located at loc_x2_y3, robot is located at loc_x3_y0, robot is located at loc_x3_y2, robot is located at loc_x3_y3, robot is placed at loc_x0_y0, robot is placed at loc_x0_y2, robot is placed at loc_x1_y1, robot is placed at loc_x2_y1, robot is placed at loc_x2_y2, robot is placed at loc_x2_y5 and robot is placed at loc_x3_y4?", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, robot is placed at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y4 and loc_x0_y5, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x0_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x2_y5, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: moves from loc_x0_y2 to loc_x0_y1 to reach the current state. In this state, are all of the following valid properties of the state that do not involve negations True or False: the robot's location is loc_x0_y3, the robot's location is loc_x0_y5, the robot's location is loc_x1_y3, the robot's location is loc_x1_y4, the robot's location is loc_x1_y5, the robot's location is loc_x2_y4, the robot's location is loc_x3_y1, the robot is currently at loc_x0_y1, the robot is currently at loc_x0_y4, the robot is currently at loc_x1_y0, the robot is currently at loc_x1_y2, the robot is currently at loc_x2_y0, the robot is currently at loc_x2_y3, the robot is currently at loc_x3_y0, the robot is currently at loc_x3_y2, the robot is currently at loc_x3_y3, the robot's position is loc_x0_y0, the robot's position is loc_x0_y2, the robot's position is loc_x1_y1, the robot's position is loc_x2_y1, the robot's position is loc_x2_y2, the robot's position is loc_x2_y5 and the robot's position is loc_x3_y4?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is linked to loc_x1_y1, loc_x0_y2 has been visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, the robot is positioned at loc_x0_y2, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y0 and loc_x1_y0, a path exists between loc_x0_y2 and loc_x0_y1, a path exists between loc_x0_y2 and loc_x0_y3, a path exists between loc_x0_y2 and loc_x1_y2, a path exists between loc_x0_y4 and loc_x0_y5, a path exists between loc_x0_y4 and loc_x1_y4, a path exists between loc_x0_y5 and loc_x1_y5, a path exists between loc_x1_y0 and loc_x1_y1, a path exists between loc_x1_y2 and loc_x2_y2, a path exists between loc_x1_y3 and loc_x0_y3, a path exists between loc_x1_y3 and loc_x1_y4, a path exists between loc_x1_y5 and loc_x1_y4, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y1 and loc_x3_y1, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x3_y2, a path exists between loc_x2_y3 and loc_x2_y2, a path exists between loc_x2_y4 and loc_x2_y3, a path exists between loc_x2_y4 and loc_x2_y5, a path exists between loc_x2_y5 and loc_x1_y5, a path exists between loc_x2_y5 and loc_x2_y4, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x3_y2 and loc_x3_y3, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y3 and loc_x3_y2, and a path exists between loc_x3_y3 and loc_x3_y4."}
{"question_id": "5993d917-d8bd-436f-91a7-129b4410b7e0", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "fluent_tracking", "question_name": "iter_1_question_7", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y1 from loc_x0_y2, from loc_x0_y1, the robot moves to loc_x0_y0, from loc_x0_y0, the robot moves to loc_x1_y0, moves from loc_x1_y0 to loc_x1_y1, from loc_x1_y1, the robot moves to loc_x1_y2, from loc_x1_y2, the robot moves to loc_x0_y2, moves from loc_x0_y2 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y5 and robot moves from loc_x0_y5 to loc_x1_y5 to reach the current state. In this state, is it True or False that loc_x3_y3 is not marked as visited?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, robot is placed at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y4 and loc_x0_y5, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x0_y5 and loc_x1_y5, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x0_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x2_y5, there is a connection between loc_x2_y5 and loc_x1_y5, there is a connection between loc_x2_y5 and loc_x2_y4, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot executes the following sequence of actions: it moves from loc_x0_y2 to loc_x0_y1, then from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y0 to loc_x1_y0, then to loc_x1_y1, and subsequently to loc_x1_y2. From loc_x1_y2, the robot moves to loc_x0_y2, then to loc_x0_y3, followed by loc_x0_y4, then loc_x0_y5, and finally to loc_x1_y5, reaching the current state. In this state, is it True or False that loc_x3_y3 is not marked as visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is linked to loc_x1_y1, loc_x0_y2 has been visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y5 is connected to loc_x0_y4, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 and loc_x0_y5 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, the robot is positioned at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x0_y1, a connection exists between loc_x0_y0 and loc_x1_y0, a connection exists between loc_x0_y2 and loc_x0_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y4 and loc_x0_y5, a connection exists between loc_x0_y4 and loc_x1_y4, a connection exists between loc_x0_y5 and loc_x1_y5, a connection exists between loc_x1_y0 and loc_x1_y1, a connection exists between loc_x1_y2 and loc_x2_y2, a connection exists between loc_x1_y3 and loc_x0_y3, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y5 and loc_x1_y4, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y2 and loc_x3_y2, a connection exists between loc_x2_y3 and loc_x2_y2, a connection exists between loc_x2_y4 and loc_x2_y3, a connection exists between loc_x2_y4 and loc_x2_y5, a connection exists between loc_x2_y5 and loc_x1_y5, a connection exists between loc_x2_y5 and loc_x2_y4, a connection exists between loc_x3_y1 and loc_x3_y0, a connection exists between loc_x3_y2 and loc_x2_y2, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, and a connection exists between loc_x3_y3 and loc_x3_y4."}
