{"question_id": "fafcfbbf-be27-4dcf-bf53-27a3c7958da3", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x0_y2 to loc_x0_y1 to reach the current state. In this state, if moves from loc_x0_y1 to loc_x0_y0, is it True or False that robot is at loc_x0_y0 and robot is not at loc_x0_y1?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 is connected to loc_x1_y5, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y5, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y5 and loc_x0_y4, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y3 and loc_x3_y4 and there is a connection between loc_x3_y4 and loc_x3_y3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: the robot moves from loc_x0_y2 to loc_x0_y1 to arrive at the current state. In this state, if the robot then moves from loc_x0_y1 to loc_x0_y0, is it True or False that the robot is now at loc_x0_y0 and no longer at loc_x0_y1?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y2 is visited, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 and loc_x1_y5 are connected, loc_x2_y5 and loc_x2_y4 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, the robot is at loc_x0_y2, a connection exists between loc_x0_y1 and loc_x0_y2, a connection exists between loc_x0_y1 and loc_x1_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y3 and loc_x0_y4, a connection exists between loc_x0_y4 and loc_x0_y5, a connection exists between loc_x1_y1 and loc_x2_y1, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y4 and loc_x0_y4, a connection exists between loc_x1_y5 and loc_x0_y5, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x1_y2, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y2 and loc_x2_y3, a connection exists between loc_x2_y3 and loc_x3_y3, a connection exists between loc_x2_y4 and loc_x2_y3, a connection exists between loc_x2_y4 and loc_x3_y4, a connection exists between loc_x3_y0 and loc_x2_y0, a connection exists between loc_x3_y1 and loc_x3_y2, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, a connection exists between loc_x3_y3 and loc_x3_y4, and a connection exists between loc_x3_y4 and loc_x3_y3."}
{"question_id": "6e5282d5-5558-4f52-8a41-c4c7282ebe09", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x4_y2 to loc_x3_y2 to reach the current state. In this state, if moves to loc_x2_y2 from loc_x3_y2, is it True or False that loc_x2_y2 is visited?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y4, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x4_y1 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 is connected to loc_x3_y4, robot is located at loc_x4_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y1 and loc_x4_y0 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot executes the following actions: it moves from loc_x4_y2 to loc_x3_y2 to attain the current state. In this state, if the robot proceeds to loc_x2_y2 from loc_x3_y2, is it True or False that loc_x2_y2 has been visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x1_y3, loc_x0_y3 and loc_x0_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y4 and loc_x0_y4, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y2, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x2_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x4_y1, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, the robot is at loc_x4_y2. Additionally, connections exist between loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x2_y0 and loc_x1_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x1_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x4_y4, loc_x4_y1 and loc_x4_y0, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "ffa4b425-b4f9-4b09-b37d-d4d0f4d05a43", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x4_y2 to loc_x3_y2 to reach the current state. In this state, if robot moves from loc_x3_y2 to loc_x2_y2, is it True or False that loc_x0_y1 is not marked as visited?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y4, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x4_y1 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 is connected to loc_x3_y4, robot is located at loc_x4_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y1 and loc_x4_y0 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Starting from the initial condition, the robot takes the following steps: it moves from loc_x4_y2 to loc_x3_y2 to arrive at the current state. In this state, if the robot proceeds from loc_x3_y2 to loc_x2_y2, is it True or False that loc_x0_y1 remains unvisited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x1_y3, loc_x0_y3 and loc_x0_y4, loc_x0_y0 and loc_x1_y0, loc_x1_y0 and loc_x2_y0, loc_x0_y1 and loc_x1_y1, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x0_y3 and loc_x1_y3, loc_x1_y3 and loc_x1_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y2, loc_x1_y2 and loc_x2_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x3_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x3_y4, loc_x2_y0 and loc_x3_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x3_y1, loc_x3_y1 and loc_x4_y1, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x2_y2 and loc_x3_y2, loc_x3_y1 and loc_x3_y2, loc_x3_y2 and loc_x4_y2, loc_x2_y3 and loc_x3_y3, loc_x3_y3 and loc_x3_y4, loc_x2_y4 and loc_x3_y4, loc_x3_y3 and loc_x3_y4, loc_x3_y0 and loc_x4_y0, loc_x4_y0 and loc_x4_y1, loc_x3_y1 and loc_x4_y1, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, loc_x3_y4 and loc_x4_y4, the robot is at loc_x4_y2. Additionally, the following connections exist: loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x2_y0 and loc_x1_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x1_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x4_y4, loc_x4_y1 and loc_x4_y0, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "3522a4c4-beae-4f96-9814-568bc74b945f", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x0_y3, the robot moves to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y3, from loc_x0_y3, the robot moves to loc_x1_y3, robot moves from loc_x1_y3 to loc_x1_y4, moves to loc_x2_y4 from loc_x1_y4, robot moves from loc_x2_y4 to loc_x3_y4, moves to loc_x3_y3 from loc_x3_y4, from loc_x3_y3, the robot moves to loc_x2_y3, moves from loc_x2_y3 to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, moves from loc_x2_y1 to loc_x1_y1, from loc_x1_y1, the robot moves to loc_x0_y1, robot moves from loc_x0_y1 to loc_x0_y0, robot moves from loc_x0_y0 to loc_x1_y0, from loc_x1_y0, the robot moves to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, moves from loc_x3_y1 to loc_x3_y2 and robot moves from loc_x3_y2 to loc_x4_y2 to reach the current state. In this state, if moves from loc_x4_y2 to loc_x4_y1, is it True or False that loc_x3_y2 is not visited?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is visited, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, robot is placed at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y3 and loc_x3_y4, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot performs the following sequence of actions: it starts at loc_x0_y3 and moves to loc_x0_y4, then back to loc_x0_y3, followed by a move to loc_x1_y3, then to loc_x1_y4, then to loc_x2_y4, and then to loc_x3_y4. From loc_x3_y4, the robot moves to loc_x3_y3, then to loc_x2_y3, followed by a move to loc_x2_y2, then to loc_x2_y1, and then to loc_x1_y1. The robot then moves to loc_x0_y1, followed by loc_x0_y0, then to loc_x1_y0, then to loc_x2_y0, and then to loc_x3_y0. From loc_x3_y0, the robot moves to loc_x3_y1, then to loc_x3_y2, and finally to loc_x4_y2, reaching the current state. In this state, if the robot moves from loc_x4_y2 to loc_x4_y1, is it True or False that loc_x3_y2 has not been visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 has been visited, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, the robot's initial position is at loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y0 and loc_x1_y0, a path exists between loc_x1_y0 and loc_x1_y1, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y1 and loc_x1_y0, a path exists between loc_x1_y3 and loc_x1_y4, a path exists between loc_x1_y3 and loc_x2_y3, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x1_y0, a path exists between loc_x2_y1 and loc_x1_y1, a path exists between loc_x2_y1 and loc_x2_y0, a path exists between loc_x2_y1 and loc_x3_y1, a path exists between loc_x2_y2 and loc_x3_y2, a path exists between loc_x2_y3 and loc_x1_y3, a path exists between loc_x2_y3 and loc_x2_y2, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x2_y4 and loc_x1_y4, a path exists between loc_x3_y3 and loc_x3_y4, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y0 and loc_x4_y1, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "26f2fc6f-d876-4258-a27c-2539399c8ec1", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, if robot moves from loc_x0_y0 to loc_x0_y1, is it True or False that loc_x2_y2 is marked as visited?", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 and loc_x1_y2 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is visited, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 and loc_x4_y3 are connected, loc_x4_y2 is connected to loc_x5_y2, loc_x4_y3 and loc_x4_y2 are connected, loc_x4_y3 and loc_x4_y4 are connected, loc_x4_y3 and loc_x5_y3 are connected, loc_x4_y3 is connected to loc_x3_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x5_y0 are connected, loc_x5_y1 is connected to loc_x4_y1, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y3 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y2, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 and loc_x5_y3 are connected, robot is placed at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4 and there is a connection between loc_x5_y3 and loc_x5_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: the robot moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, if the robot moves from loc_x0_y0 to loc_x0_y1, is it True or False that loc_x2_y2 is marked as visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y2 and loc_x0_y1, loc_x0_y2 and loc_x1_y2, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 has been visited, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y2 and loc_x2_y2, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x3_y0 and loc_x2_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x3_y4, loc_x3_y3 and loc_x4_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x4_y1, loc_x4_y0 and loc_x5_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y1 and loc_x5_y1, loc_x4_y2 and loc_x4_y3, loc_x4_y2 and loc_x5_y2, loc_x4_y3 and loc_x4_y2, loc_x4_y3 and loc_x4_y4, loc_x4_y3 and loc_x5_y3, loc_x4_y3 and loc_x3_y3, loc_x4_y4 and loc_x4_y3, loc_x5_y0 and loc_x4_y0, loc_x5_y0 and loc_x5_y1, loc_x5_y1 and loc_x5_y0, loc_x5_y1 and loc_x4_y1, loc_x5_y1 and loc_x5_y2, loc_x5_y2 and loc_x5_y3, loc_x5_y2 and loc_x4_y2, loc_x5_y2 and loc_x5_y1, loc_x5_y3 and loc_x4_y3, loc_x5_y3 and loc_x5_y2, loc_x5_y4 and loc_x4_y4, loc_x5_y4 and loc_x5_y3, the robot is at loc_x1_y0, a connection exists between loc_x0_y0 and loc_x1_y0, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y3 and loc_x0_y2, a connection exists between loc_x0_y4 and loc_x0_y3, a connection exists between loc_x1_y1 and loc_x0_y1, a connection exists between loc_x1_y2 and loc_x0_y2, a connection exists between loc_x1_y2 and loc_x1_y1, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x1_y1, a connection exists between loc_x2_y2 and loc_x2_y3, a connection exists between loc_x2_y2 and loc_x3_y2, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y4 and loc_x4_y4, a connection exists between loc_x4_y0 and loc_x3_y0, a connection exists between loc_x4_y1 and loc_x3_y1, a connection exists between loc_x4_y1 and loc_x4_y0, a connection exists between loc_x4_y2 and loc_x3_y2, a connection exists between loc_x4_y2 and loc_x4_y1, a connection exists between loc_x4_y4 and loc_x3_y4, a connection exists between loc_x4_y4 and loc_x5_y4, and a connection exists between loc_x5_y3 and loc_x5_y4."}
{"question_id": "3d4c9a37-b0ad-4747-80cc-03d1d9a050ae", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x1_y0 to loc_x0_y0, robot moves from loc_x0_y0 to loc_x0_y1, from loc_x0_y1, the robot moves to loc_x1_y1, moves from loc_x1_y1 to loc_x2_y1, from loc_x2_y1, the robot moves to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, from loc_x3_y1, the robot moves to loc_x3_y2, moves to loc_x3_y1 from loc_x3_y2 and moves to loc_x4_y1 from loc_x3_y1 to reach the current state. In this state, if robot moves from loc_x4_y1 to loc_x4_y0, is it True or False that loc_x4_y0 is visited?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 and loc_x1_y2 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is visited, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 and loc_x4_y3 are connected, loc_x4_y2 is connected to loc_x5_y2, loc_x4_y3 and loc_x4_y2 are connected, loc_x4_y3 and loc_x4_y4 are connected, loc_x4_y3 and loc_x5_y3 are connected, loc_x4_y3 is connected to loc_x3_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x5_y0 are connected, loc_x5_y1 is connected to loc_x4_y1, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y3 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y2, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 and loc_x5_y3 are connected, robot is placed at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4 and there is a connection between loc_x5_y3 and loc_x5_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot executes the following sequence of actions: it moves from loc_x1_y0 to loc_x0_y0, then from loc_x0_y0 to loc_x0_y1, followed by a move from loc_x0_y1 to loc_x1_y1, then to loc_x2_y1, and subsequently to loc_x2_y0. The robot continues by moving from loc_x2_y0 to loc_x3_y0, then to loc_x3_y1, followed by a move to loc_x3_y2, then back to loc_x3_y1, and finally to loc_x4_y1. In this current state, if the robot moves from loc_x4_y1 to loc_x4_y0, is it True or False that loc_x4_y0 has been visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y2 and loc_x0_y1, loc_x0_y2 and loc_x1_y2, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 has been visited, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y2 and loc_x2_y2, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x3_y0 and loc_x2_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x3_y4, loc_x3_y3 and loc_x4_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x4_y1, loc_x4_y0 and loc_x5_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y1 and loc_x5_y1, loc_x4_y2 and loc_x4_y3, loc_x4_y2 and loc_x5_y2, loc_x4_y3 and loc_x4_y2, loc_x4_y3 and loc_x4_y4, loc_x4_y3 and loc_x5_y3, loc_x4_y3 and loc_x3_y3, loc_x4_y4 and loc_x4_y3, loc_x5_y0 and loc_x4_y0, loc_x5_y0 and loc_x5_y1, loc_x5_y1 and loc_x5_y0, loc_x5_y1 and loc_x4_y1, loc_x5_y1 and loc_x5_y2, loc_x5_y2 and loc_x5_y3, loc_x5_y2 and loc_x4_y2, loc_x5_y2 and loc_x5_y1, loc_x5_y3 and loc_x4_y3, loc_x5_y3 and loc_x5_y2, loc_x5_y4 and loc_x4_y4, loc_x5_y4 and loc_x5_y3, the robot is at loc_x1_y0, a connection exists between loc_x0_y0 and loc_x1_y0, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y3 and loc_x0_y2, a connection exists between loc_x0_y4 and loc_x0_y3, a connection exists between loc_x1_y1 and loc_x0_y1, a connection exists between loc_x1_y2 and loc_x0_y2, a connection exists between loc_x1_y2 and loc_x1_y1, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x1_y1, a connection exists between loc_x2_y2 and loc_x2_y3, a connection exists between loc_x2_y2 and loc_x3_y2, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y4 and loc_x4_y4, a connection exists between loc_x4_y0 and loc_x3_y0, a connection exists between loc_x4_y1 and loc_x3_y1, a connection exists between loc_x4_y1 and loc_x4_y0, a connection exists between loc_x4_y2 and loc_x3_y2, a connection exists between loc_x4_y2 and loc_x4_y1, a connection exists between loc_x4_y4 and loc_x3_y4, a connection exists between loc_x4_y4 and loc_x5_y4, and a connection exists between loc_x5_y3 and loc_x5_y4."}
{"question_id": "3618840f-a9af-494d-b3bc-343aa29c803b", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y2 to loc_x0_y1 to reach the current state. In this state, if moves to loc_x0_y0 from loc_x0_y1, is it True or False that loc_x0_y0 is visited?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 is connected to loc_x1_y5, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y5, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y5 and loc_x0_y4, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y3 and loc_x3_y4 and there is a connection between loc_x3_y4 and loc_x3_y3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: the robot moves from loc_x0_y2 to loc_x0_y1 to reach the current state. In this state, if the robot moves from loc_x0_y1 to loc_x0_y0, is it True or False that loc_x0_y0 has been visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y2 is visited, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 and loc_x1_y5 are connected, loc_x2_y5 and loc_x2_y4 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, the robot is at loc_x0_y2, a connection exists between loc_x0_y1 and loc_x0_y2, a connection exists between loc_x0_y1 and loc_x1_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y3 and loc_x0_y4, a connection exists between loc_x0_y5 and loc_x0_y4, a connection exists between loc_x1_y1 and loc_x2_y1, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y4 and loc_x0_y4, a connection exists between loc_x1_y5 and loc_x0_y5, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x1_y2, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y2 and loc_x2_y3, a connection exists between loc_x2_y3 and loc_x3_y3, a connection exists between loc_x2_y4 and loc_x2_y3, a connection exists between loc_x2_y4 and loc_x3_y4, a connection exists between loc_x3_y0 and loc_x2_y0, a connection exists between loc_x3_y1 and loc_x3_y2, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, a connection exists between loc_x3_y3 and loc_x3_y4, and a connection exists between loc_x3_y4 and loc_x3_y3."}
{"question_id": "140f296e-6f4a-4618-a6a1-ca0102476651", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x1_y0 to loc_x0_y0, robot moves from loc_x0_y0 to loc_x0_y1, moves to loc_x1_y1 from loc_x0_y1, moves to loc_x2_y1 from loc_x1_y1, robot moves from loc_x2_y1 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, moves from loc_x3_y1 to loc_x3_y2, moves from loc_x3_y2 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, moves from loc_x4_y0 to loc_x5_y0, moves to loc_x5_y1 from loc_x5_y0, moves to loc_x5_y2 from loc_x5_y1, from loc_x5_y2, the robot moves to loc_x4_y2, from loc_x4_y2, the robot moves to loc_x4_y3, robot moves from loc_x4_y3 to loc_x5_y3, moves to loc_x5_y4 from loc_x5_y3 and moves from loc_x5_y4 to loc_x4_y4 to reach the current state. In this state, if moves to loc_x3_y4 from loc_x4_y4, is it True or False that robot is located at loc_x3_y4 and robot is not placed at loc_x4_y4?", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 and loc_x1_y2 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is visited, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 and loc_x4_y3 are connected, loc_x4_y2 is connected to loc_x5_y2, loc_x4_y3 and loc_x4_y2 are connected, loc_x4_y3 and loc_x4_y4 are connected, loc_x4_y3 and loc_x5_y3 are connected, loc_x4_y3 is connected to loc_x3_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x5_y0 are connected, loc_x5_y1 is connected to loc_x4_y1, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y3 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y2, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 and loc_x5_y3 are connected, robot is placed at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4 and there is a connection between loc_x5_y3 and loc_x5_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot transitions from loc_x1_y0 to loc_x0_y0, then from loc_x0_y0 to loc_x0_y1, followed by a move from loc_x0_y1 to loc_x1_y1, then from loc_x1_y1 to loc_x2_y1, and the robot proceeds from loc_x2_y1 to loc_x2_y0. From loc_x2_y0, the robot moves to loc_x3_y0, then to loc_x3_y1, followed by a transition from loc_x3_y1 to loc_x3_y2, and then back to loc_x3_y1. The robot then moves from loc_x3_y1 to loc_x4_y1, then to loc_x4_y0, followed by a move from loc_x4_y0 to loc_x5_y0, then to loc_x5_y1, and subsequently to loc_x5_y2. From loc_x5_y2, the robot transitions to loc_x4_y2, then to loc_x4_y3, followed by a move to loc_x5_y3, then to loc_x5_y4, and finally to loc_x4_y4, resulting in the current state. In this state, if the robot moves from loc_x4_y4 to loc_x3_y4, is it True or False that the robot is now located at loc_x3_y4 and no longer at loc_x4_y4?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y2 and loc_x0_y1, loc_x0_y2 and loc_x1_y2, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 has been visited, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y1 and loc_x4_y1 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x3_y3 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 and loc_x5_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 and loc_x5_y1 are connected, loc_x4_y2 and loc_x4_y3 are connected, loc_x4_y2 and loc_x5_y2 are connected, loc_x4_y3 and loc_x4_y2 are connected, loc_x4_y3 and loc_x4_y4 are connected, loc_x4_y3 and loc_x5_y3 are connected, loc_x4_y3 and loc_x3_y3 are connected, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 and loc_x4_y0 are connected, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y1 and loc_x5_y0 are connected, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 and loc_x5_y2 are connected, loc_x5_y2 and loc_x5_y3 are connected, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y3 and loc_x4_y3 are connected, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 and loc_x5_y3 are connected. The robot is placed at loc_x1_y0. Additionally, the following connections exist: loc_x0_y0 and loc_x1_y0, loc_x0_y2 and loc_x0_y3, loc_x0_y3 and loc_x0_y2, loc_x0_y4 and loc_x0_y3, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 and loc_x1_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x1_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y4 and loc_x3_y4, loc_x4_y4 and loc_x5_y4, and loc_x5_y3 and loc_x5_y4."}
{"question_id": "90b89cbd-fb15-4f5a-b2b6-c1f2f697bd24", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y4 from loc_x0_y3, moves to loc_x0_y3 from loc_x0_y4, moves to loc_x1_y3 from loc_x0_y3, moves from loc_x1_y3 to loc_x1_y4, robot moves from loc_x1_y4 to loc_x2_y4, moves from loc_x2_y4 to loc_x3_y4, moves from loc_x3_y4 to loc_x3_y3, from loc_x3_y3, the robot moves to loc_x2_y3, from loc_x2_y3, the robot moves to loc_x2_y2 and robot moves from loc_x2_y2 to loc_x2_y1 to reach the current state. In this state, if robot moves from loc_x2_y1 to loc_x1_y1, is it True or False that robot is placed at loc_x0_y4 and robot is placed at loc_x2_y2?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is visited, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, robot is placed at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y3 and loc_x3_y4, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot transitions from loc_x0_y3 to loc_x0_y4, then from loc_x0_y4 to loc_x0_y3, followed by a move from loc_x0_y3 to loc_x1_y3, then to loc_x1_y4, and subsequently to loc_x2_y4, loc_x3_y4, loc_x3_y3, loc_x2_y3, loc_x2_y2, and finally to loc_x2_y1. From this current state, if the robot proceeds from loc_x2_y1 to loc_x1_y1, the question arises: is it True or False that the robot is simultaneously positioned at loc_x0_y4 and loc_x2_y2?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 has been visited, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, the robot's current position is at loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y0 and loc_x1_y0, a path exists between loc_x1_y0 and loc_x1_y1, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y1 and loc_x1_y0, a path exists between loc_x1_y3 and loc_x1_y4, a path exists between loc_x1_y3 and loc_x2_y3, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x1_y0, a path exists between loc_x2_y1 and loc_x1_y1, a path exists between loc_x2_y1 and loc_x2_y0, a path exists between loc_x2_y1 and loc_x3_y1, a path exists between loc_x2_y2 and loc_x3_y2, a path exists between loc_x2_y3 and loc_x1_y3, a path exists between loc_x2_y3 and loc_x2_y2, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x2_y4 and loc_x1_y4, a path exists between loc_x3_y3 and loc_x3_y4, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y0 and loc_x4_y1, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "f3e9882b-cd19-4efe-bb91-0cac932f75c3", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y0 from loc_x1_y0, robot moves from loc_x0_y0 to loc_x0_y1, moves to loc_x1_y1 from loc_x0_y1, robot moves from loc_x1_y1 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, moves from loc_x3_y1 to loc_x3_y2, moves to loc_x3_y1 from loc_x3_y2 and robot moves from loc_x3_y1 to loc_x4_y1 to reach the current state. In this state, if moves to loc_x4_y0 from loc_x4_y1, is it True or False that loc_x5_y4 is not marked as visited?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 and loc_x1_y2 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is visited, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 and loc_x4_y3 are connected, loc_x4_y2 is connected to loc_x5_y2, loc_x4_y3 and loc_x4_y2 are connected, loc_x4_y3 and loc_x4_y4 are connected, loc_x4_y3 and loc_x5_y3 are connected, loc_x4_y3 is connected to loc_x3_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x5_y0 are connected, loc_x5_y1 is connected to loc_x4_y1, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y3 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y2, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 and loc_x5_y3 are connected, robot is placed at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4 and there is a connection between loc_x5_y3 and loc_x5_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: the robot transitions from loc_x1_y0 to loc_x0_y0, then from loc_x0_y0 to loc_x0_y1, followed by a move from loc_x0_y1 to loc_x1_y1, then to loc_x2_y1, and subsequently to loc_x2_y0. From loc_x2_y0, the robot proceeds to loc_x3_y0, then to loc_x3_y1, followed by a move to loc_x3_y2, then back to loc_x3_y1, and finally to loc_x4_y1 to reach the current state. In this state, if the robot moves from loc_x4_y1 to loc_x4_y0, is it True or False that loc_x5_y4 is not marked as visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y2 and loc_x0_y1, loc_x0_y2 and loc_x1_y2, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y0 is visited, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y2 and loc_x2_y2, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x3_y0 and loc_x2_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x3_y4, loc_x3_y3 and loc_x4_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x4_y1, loc_x4_y0 and loc_x5_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y1 and loc_x5_y1, loc_x4_y2 and loc_x4_y3, loc_x4_y2 and loc_x5_y2, loc_x4_y3 and loc_x4_y2, loc_x4_y3 and loc_x4_y4, loc_x4_y3 and loc_x5_y3, loc_x4_y3 and loc_x3_y3, loc_x4_y4 and loc_x4_y3, loc_x5_y0 and loc_x4_y0, loc_x5_y0 and loc_x5_y1, loc_x5_y1 and loc_x5_y0, loc_x5_y1 and loc_x4_y1, loc_x5_y1 and loc_x5_y2, loc_x5_y2 and loc_x5_y3, loc_x5_y2 and loc_x4_y2, loc_x5_y2 and loc_x5_y1, loc_x5_y3 and loc_x4_y3, loc_x5_y3 and loc_x5_y2, loc_x5_y4 and loc_x4_y4, loc_x5_y4 and loc_x5_y3. \n\nAdditionally, the robot is placed at loc_x1_y0. The following connections exist: loc_x0_y0 and loc_x1_y0, loc_x0_y2 and loc_x0_y3, loc_x0_y3 and loc_x0_y2, loc_x0_y4 and loc_x0_y3, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 and loc_x1_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x1_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y4 and loc_x3_y4, loc_x4_y4 and loc_x5_y4, and loc_x5_y3 and loc_x5_y4."}
{"question_id": "dd2958b3-6243-4740-917f-577d5181d8b2", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x4_y2, the robot moves to loc_x3_y2, moves from loc_x3_y2 to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, from loc_x2_y1, the robot moves to loc_x3_y1, robot moves from loc_x3_y1 to loc_x4_y1, moves from loc_x4_y1 to loc_x4_y0, moves to loc_x3_y0 from loc_x4_y0, moves to loc_x2_y0 from loc_x3_y0, moves from loc_x2_y0 to loc_x1_y0 and robot moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, if moves from loc_x0_y0 to loc_x0_y1, is it True or False that robot is not at loc_x2_y2 and robot is not placed at loc_x1_y0?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y4, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x4_y1 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 is connected to loc_x3_y4, robot is located at loc_x4_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y1 and loc_x4_y0 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot starts at loc_x4_y2 and moves to loc_x3_y2, then to loc_x2_y2, followed by loc_x2_y1, then loc_x3_y1, loc_x4_y1, loc_x4_y0, loc_x3_y0, loc_x2_y0, loc_x1_y0, and finally loc_x0_y0. From this state, if the robot moves from loc_x0_y0 to loc_x0_y1, is it True or False that the robot is neither at loc_x2_y2 nor at loc_x1_y0?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x1_y3, loc_x0_y3 and loc_x0_y4, loc_x0_y0 and loc_x1_y0, loc_x1_y0 and loc_x2_y0, loc_x0_y1 and loc_x1_y1, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x0_y3 and loc_x1_y3, loc_x1_y3 and loc_x1_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y2, loc_x1_y2 and loc_x2_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x3_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x3_y4, loc_x2_y0 and loc_x3_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x3_y1, loc_x3_y1 and loc_x4_y1, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x2_y2 and loc_x3_y2, loc_x3_y1 and loc_x3_y2, loc_x3_y2 and loc_x4_y2, loc_x2_y3 and loc_x3_y3, loc_x3_y3 and loc_x3_y4, loc_x2_y4 and loc_x3_y4, loc_x3_y3 and loc_x3_y4, loc_x3_y0 and loc_x4_y0, loc_x4_y0 and loc_x4_y1, loc_x3_y1 and loc_x4_y1, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, loc_x3_y4 and loc_x4_y4, the robot is at loc_x4_y2. \n\nAdditionally, the following connections exist: loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x2_y0 and loc_x1_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x1_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x4_y4, loc_x4_y1 and loc_x4_y0, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "e4928738-f51d-4c99-9386-6db4bf7245ca", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x0_y2 to loc_x0_y1, from loc_x0_y1, the robot moves to loc_x0_y0, robot moves from loc_x0_y0 to loc_x1_y0, from loc_x1_y0, the robot moves to loc_x1_y1, robot moves from loc_x1_y1 to loc_x1_y2, from loc_x1_y2, the robot moves to loc_x0_y2, robot moves from loc_x0_y2 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x0_y4, from loc_x0_y4, the robot moves to loc_x0_y5, moves from loc_x0_y5 to loc_x1_y5, robot moves from loc_x1_y5 to loc_x1_y4, robot moves from loc_x1_y4 to loc_x1_y3, moves to loc_x2_y3 from loc_x1_y3, moves to loc_x2_y2 from loc_x2_y3, robot moves from loc_x2_y2 to loc_x2_y1, moves to loc_x2_y0 from loc_x2_y1, robot moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1 and robot moves from loc_x3_y1 to loc_x3_y2 to reach the current state. In this state, if robot moves from loc_x3_y2 to loc_x3_y3, is it True or False that robot is located at loc_x0_y1 and robot is not located at loc_x2_y3?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 is connected to loc_x1_y5, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y5, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y5 and loc_x0_y4, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y3 and loc_x3_y4 and there is a connection between loc_x3_y4 and loc_x3_y3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot undergoes a series of movements as follows: it transitions from loc_x0_y2 to loc_x0_y1, then from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y0 to loc_x1_y0, then from loc_x1_y0 to loc_x1_y1, and from loc_x1_y1 to loc_x1_y2. The robot continues by moving from loc_x1_y2 to loc_x0_y2, then from loc_x0_y2 to loc_x0_y3, and from loc_x0_y3 to loc_x0_y4. Next, it moves from loc_x0_y4 to loc_x0_y5, followed by a transition from loc_x0_y5 to loc_x1_y5, then from loc_x1_y5 to loc_x1_y4, and from loc_x1_y4 to loc_x1_y3. The robot then proceeds to move from loc_x1_y3 to loc_x2_y3, from loc_x2_y3 to loc_x2_y2, and from loc_x2_y2 to loc_x2_y1. It then moves from loc_x2_y1 to loc_x2_y0, followed by a transition from loc_x2_y0 to loc_x3_y0, then from loc_x3_y0 to loc_x3_y1, and finally from loc_x3_y1 to loc_x3_y2, reaching its current state. In this state, if the robot moves from loc_x3_y2 to loc_x3_y3, the question arises: is it True or False that the robot is located at loc_x0_y1 and not located at loc_x2_y3?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y2 is visited, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 and loc_x1_y5 are connected, loc_x2_y5 and loc_x2_y4 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, the robot is at loc_x0_y2, a connection exists between loc_x0_y1 and loc_x0_y2, a connection exists between loc_x0_y1 and loc_x1_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y3 and loc_x0_y4, a connection exists between loc_x0_y5 and loc_x0_y4, a connection exists between loc_x1_y1 and loc_x2_y1, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y4 and loc_x0_y4, a connection exists between loc_x1_y5 and loc_x0_y5, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x1_y2, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y2 and loc_x2_y3, a connection exists between loc_x2_y3 and loc_x3_y3, a connection exists between loc_x2_y4 and loc_x2_y3, a connection exists between loc_x2_y4 and loc_x3_y4, a connection exists between loc_x3_y0 and loc_x2_y0, a connection exists between loc_x3_y1 and loc_x3_y2, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, a connection exists between loc_x3_y3 and loc_x3_y4, and a connection exists between loc_x3_y4 and loc_x3_y3."}
{"question_id": "2c416168-92c3-4a1e-9b95-2f08a4c1d3f7", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y3 to loc_x0_y4, moves to loc_x0_y3 from loc_x0_y4, moves to loc_x1_y3 from loc_x0_y3, robot moves from loc_x1_y3 to loc_x1_y4, moves from loc_x1_y4 to loc_x2_y4, moves from loc_x2_y4 to loc_x3_y4, from loc_x3_y4, the robot moves to loc_x3_y3, moves to loc_x2_y3 from loc_x3_y3, robot moves from loc_x2_y3 to loc_x2_y2 and from loc_x2_y2, the robot moves to loc_x2_y1 to reach the current state. In this state, if robot moves from loc_x2_y1 to loc_x1_y1, is it True or False that loc_x1_y1 is visited?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is visited, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, robot is placed at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y3 and loc_x3_y4, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot executes the following sequence of actions: it moves from loc_x0_y3 to loc_x0_y4, then back to loc_x0_y3, followed by a move to loc_x1_y3. From there, the robot proceeds to loc_x1_y4, then to loc_x2_y4, and subsequently to loc_x3_y4. Next, it moves to loc_x3_y3, then to loc_x2_y3, and from there to loc_x2_y2. Finally, the robot reaches its current state by moving to loc_x2_y1. In this state, if the robot moves from loc_x2_y1 to loc_x1_y1, is it True or False that loc_x1_y1 has been visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 has been visited, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is also connected to loc_x0_y3, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is also connected to loc_x2_y4, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 is also connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is also connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y0 is also connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is also connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y2 is also connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y1 is also connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, the robot is currently at loc_x0_y3, a connection exists between loc_x0_y0 and loc_x0_y1, a connection exists between loc_x0_y0 and loc_x1_y0, a connection exists between loc_x1_y0 and loc_x1_y1, a connection exists between loc_x1_y0 and loc_x2_y0, a connection exists between loc_x1_y1 and loc_x0_y1, a connection exists between loc_x1_y1 and loc_x1_y0, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y3 and loc_x2_y3, a connection exists between loc_x1_y4 and loc_x0_y4, a connection exists between loc_x2_y0 and loc_x1_y0, a connection exists between loc_x2_y1 and loc_x1_y1, a connection exists between loc_x2_y1 and loc_x2_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x3_y2, a connection exists between loc_x2_y3 and loc_x1_y3, a connection exists between loc_x2_y3 and loc_x2_y2, a connection exists between loc_x2_y3 and loc_x3_y3, a connection exists between loc_x2_y4 and loc_x1_y4, a connection exists between loc_x3_y3 and loc_x3_y4, a connection exists between loc_x3_y4 and loc_x3_y3, a connection exists between loc_x4_y0 and loc_x4_y1, and a connection exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "05a06dac-2eaf-498b-aac5-59fca4acf461", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y3 to loc_x0_y4 to reach the current state. In this state, if robot moves from loc_x0_y4 to loc_x0_y3, is it True or False that robot is placed at loc_x3_y1?", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is visited, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, robot is placed at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y3 and loc_x3_y4, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the robot executes the following actions: it moves from loc_x0_y3 to loc_x0_y4 to attain the current state. In this state, if the robot moves from loc_x0_y4 to loc_x0_y3, is it True or False that the robot is positioned at loc_x3_y1?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 has been visited, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is also connected to loc_x0_y3, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is also connected to loc_x2_y4, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 is also connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is also connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y0 is also connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is also connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is also connected to loc_x4_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y1 is also connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, the robot is currently at loc_x0_y3, a connection exists between loc_x0_y0 and loc_x0_y1, a connection exists between loc_x0_y0 and loc_x1_y0, a connection exists between loc_x1_y0 and loc_x1_y1, a connection exists between loc_x1_y0 and loc_x2_y0, a connection exists between loc_x1_y1 and loc_x0_y1, a connection exists between loc_x1_y1 and loc_x1_y0, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y3 and loc_x2_y3, a connection exists between loc_x1_y4 and loc_x0_y4, a connection exists between loc_x2_y0 and loc_x1_y0, a connection exists between loc_x2_y1 and loc_x1_y1, a connection exists between loc_x2_y1 and loc_x2_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x3_y2, a connection exists between loc_x2_y3 and loc_x1_y3, a connection exists between loc_x2_y3 and loc_x2_y2, a connection exists between loc_x2_y3 and loc_x3_y3, a connection exists between loc_x2_y4 and loc_x1_y4, a connection exists between loc_x3_y3 and loc_x3_y4, a connection exists between loc_x3_y4 and loc_x3_y3, a connection exists between loc_x4_y0 and loc_x4_y1, and a connection exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "be1474d9-4d76-40be-847b-24ea1e530b62", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y0 from loc_x1_y0, moves to loc_x0_y1 from loc_x0_y0, robot moves from loc_x0_y1 to loc_x1_y1, moves from loc_x1_y1 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x3_y0, from loc_x3_y0, the robot moves to loc_x3_y1, robot moves from loc_x3_y1 to loc_x3_y2, robot moves from loc_x3_y2 to loc_x3_y1, from loc_x3_y1, the robot moves to loc_x4_y1, from loc_x4_y1, the robot moves to loc_x4_y0, moves to loc_x5_y0 from loc_x4_y0, moves to loc_x5_y1 from loc_x5_y0, robot moves from loc_x5_y1 to loc_x5_y2, moves from loc_x5_y2 to loc_x4_y2, from loc_x4_y2, the robot moves to loc_x4_y3, from loc_x4_y3, the robot moves to loc_x5_y3, robot moves from loc_x5_y3 to loc_x5_y4 and moves to loc_x4_y4 from loc_x5_y4 to reach the current state. In this state, if from loc_x4_y4, the robot moves to loc_x3_y4, is it True or False that loc_x3_y4 is marked as visited?", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 and loc_x1_y2 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is visited, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 and loc_x4_y3 are connected, loc_x4_y2 is connected to loc_x5_y2, loc_x4_y3 and loc_x4_y2 are connected, loc_x4_y3 and loc_x4_y4 are connected, loc_x4_y3 and loc_x5_y3 are connected, loc_x4_y3 is connected to loc_x3_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x5_y0 are connected, loc_x5_y1 is connected to loc_x4_y1, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y3 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y2, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 and loc_x5_y3 are connected, robot is placed at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4 and there is a connection between loc_x5_y3 and loc_x5_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot relocates from loc_x1_y0 to loc_x0_y0, then proceeds to loc_x0_y1 from loc_x0_y0, followed by a move to loc_x1_y1 from loc_x0_y1, then to loc_x2_y1 from loc_x1_y1, and subsequently to loc_x2_y0 from loc_x2_y1. The robot then moves to loc_x3_y0 from loc_x2_y0, then to loc_x3_y1 from loc_x3_y0, followed by a move to loc_x3_y2 from loc_x3_y1, and then back to loc_x3_y1 from loc_x3_y2. Next, the robot relocates to loc_x4_y1 from loc_x3_y1, then to loc_x4_y0 from loc_x4_y1, followed by a move to loc_x5_y0 from loc_x4_y0, and then to loc_x5_y1 from loc_x5_y0. The robot then proceeds to loc_x5_y2 from loc_x5_y1, then to loc_x4_y2 from loc_x5_y2, followed by a move to loc_x4_y3 from loc_x4_y2, and then to loc_x5_y3 from loc_x4_y3. Finally, the robot moves to loc_x5_y4 from loc_x5_y3, and then to loc_x4_y4 from loc_x5_y4, resulting in the current state. In this state, if the robot moves from loc_x4_y4 to loc_x3_y4, is it True or False that loc_x3_y4 is marked as visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y2 and loc_x0_y1, loc_x0_y2 and loc_x1_y2, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y0 is visited, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y2 and loc_x2_y2, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x3_y0 and loc_x2_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x3_y4, loc_x3_y3 and loc_x4_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x4_y1, loc_x4_y0 and loc_x5_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y1 and loc_x5_y1, loc_x4_y2 and loc_x4_y3, loc_x4_y2 and loc_x5_y2, loc_x4_y3 and loc_x4_y2, loc_x4_y3 and loc_x4_y4, loc_x4_y3 and loc_x5_y3, loc_x4_y3 and loc_x3_y3, loc_x4_y4 and loc_x4_y3, loc_x5_y0 and loc_x4_y0, loc_x5_y0 and loc_x5_y1, loc_x5_y1 and loc_x5_y0, loc_x5_y1 and loc_x4_y1, loc_x5_y1 and loc_x5_y2, loc_x5_y2 and loc_x5_y3, loc_x5_y2 and loc_x4_y2, loc_x5_y2 and loc_x5_y1, loc_x5_y3 and loc_x4_y3, loc_x5_y3 and loc_x5_y2, loc_x5_y4 and loc_x4_y4, loc_x5_y4 and loc_x5_y3. The robot is placed at loc_x1_y0. Additionally, the following connections exist: loc_x0_y0 and loc_x1_y0, loc_x0_y2 and loc_x0_y3, loc_x0_y3 and loc_x0_y2, loc_x0_y4 and loc_x0_y3, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 and loc_x1_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x1_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y4 and loc_x3_y4, loc_x4_y4 and loc_x5_y4, and loc_x5_y3 and loc_x5_y4."}
{"question_id": "410b65f0-2ba8-4156-847d-1705b33433a2", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, if moves to loc_x0_y1 from loc_x0_y0, is it True or False that loc_x0_y1 is marked as visited?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 and loc_x1_y2 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is visited, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 and loc_x4_y3 are connected, loc_x4_y2 is connected to loc_x5_y2, loc_x4_y3 and loc_x4_y2 are connected, loc_x4_y3 and loc_x4_y4 are connected, loc_x4_y3 and loc_x5_y3 are connected, loc_x4_y3 is connected to loc_x3_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x5_y0 are connected, loc_x5_y1 is connected to loc_x4_y1, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y3 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y2, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 and loc_x5_y3 are connected, robot is placed at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4 and there is a connection between loc_x5_y3 and loc_x5_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: the robot moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, if the robot moves from loc_x0_y0 to loc_x0_y1, is it True or False that loc_x0_y1 is marked as visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y2 and loc_x0_y1, loc_x0_y2 and loc_x1_y2, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 has been visited, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y1 and loc_x4_y1 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x3_y3 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 and loc_x5_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 and loc_x5_y1 are connected, loc_x4_y2 and loc_x4_y3 are connected, loc_x4_y2 and loc_x5_y2 are connected, loc_x4_y3 and loc_x4_y2 are connected, loc_x4_y3 and loc_x4_y4 are connected, loc_x4_y3 and loc_x5_y3 are connected, loc_x4_y3 and loc_x3_y3 are connected, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 and loc_x4_y0 are connected, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y1 and loc_x5_y0 are connected, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 and loc_x5_y2 are connected, loc_x5_y2 and loc_x5_y3 are connected, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y3 and loc_x4_y3 are connected, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 and loc_x5_y3 are connected, the robot is at loc_x1_y0, a connection exists between loc_x0_y0 and loc_x1_y0, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y3 and loc_x0_y2, a connection exists between loc_x0_y4 and loc_x0_y3, a connection exists between loc_x1_y1 and loc_x0_y1, a connection exists between loc_x1_y2 and loc_x0_y2, a connection exists between loc_x1_y2 and loc_x1_y1, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x1_y1, a connection exists between loc_x2_y2 and loc_x2_y3, a connection exists between loc_x2_y2 and loc_x3_y2, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y4 and loc_x4_y4, a connection exists between loc_x4_y0 and loc_x3_y0, a connection exists between loc_x4_y1 and loc_x3_y1, a connection exists between loc_x4_y1 and loc_x4_y0, a connection exists between loc_x4_y2 and loc_x3_y2, a connection exists between loc_x4_y2 and loc_x4_y1, a connection exists between loc_x4_y4 and loc_x3_y4, a connection exists between loc_x4_y4 and loc_x5_y4, and a connection exists between loc_x5_y3 and loc_x5_y4."}
{"question_id": "fac34e26-79b4-465e-97c9-ad2cb190cc1e", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x4_y2 to loc_x3_y2, from loc_x3_y2, the robot moves to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, moves from loc_x2_y1 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, moves from loc_x4_y0 to loc_x3_y0, moves from loc_x3_y0 to loc_x2_y0, moves to loc_x1_y0 from loc_x2_y0, moves from loc_x1_y0 to loc_x0_y0, robot moves from loc_x0_y0 to loc_x0_y1, from loc_x0_y1, the robot moves to loc_x1_y1, moves to loc_x1_y2 from loc_x1_y1, robot moves from loc_x1_y2 to loc_x1_y3, moves from loc_x1_y3 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x0_y4, from loc_x0_y4, the robot moves to loc_x1_y4, moves from loc_x1_y4 to loc_x2_y4 and robot moves from loc_x2_y4 to loc_x2_y3 to reach the current state. In this state, if from loc_x2_y3, the robot moves to loc_x3_y3, is it True or False that robot is not at loc_x0_y4 and robot is not placed at loc_x3_y1?", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y4, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x4_y1 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 is connected to loc_x3_y4, robot is located at loc_x4_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y1 and loc_x4_y0 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot performs the following sequence of actions: it moves from loc_x4_y2 to loc_x3_y2, then to loc_x2_y2, followed by loc_x2_y1, then loc_x3_y1, loc_x4_y1, loc_x4_y0, loc_x3_y0, loc_x2_y0, loc_x1_y0, and loc_x0_y0. From there, it moves to loc_x0_y1, then loc_x1_y1, loc_x1_y2, loc_x1_y3, loc_x0_y3, loc_x0_y4, loc_x1_y4, loc_x2_y4, and finally loc_x2_y3 to reach its current state. In this state, if the robot moves from loc_x2_y3 to loc_x3_y3, is it True or False that the robot is not at loc_x0_y4 and the robot is not at loc_x3_y1?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x1_y3, loc_x0_y3 and loc_x0_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y4 and loc_x0_y4, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y2, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x2_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x4_y1, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, the robot is at loc_x4_y2. Additionally, the following connections exist: loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x2_y0 and loc_x1_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x1_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x4_y4, loc_x4_y1 and loc_x4_y0, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "793aa705-dca6-4d9f-ba89-973962a9bb4a", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y1 from loc_x0_y2, from loc_x0_y1, the robot moves to loc_x0_y0, from loc_x0_y0, the robot moves to loc_x1_y0, robot moves from loc_x1_y0 to loc_x1_y1, from loc_x1_y1, the robot moves to loc_x1_y2, moves from loc_x1_y2 to loc_x0_y2, moves from loc_x0_y2 to loc_x0_y3, from loc_x0_y3, the robot moves to loc_x0_y4, moves to loc_x0_y5 from loc_x0_y4 and robot moves from loc_x0_y5 to loc_x1_y5 to reach the current state. In this state, if moves to loc_x1_y4 from loc_x1_y5, is it True or False that loc_x0_y4 is marked as visited?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 is connected to loc_x1_y5, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y5, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y5 and loc_x0_y4, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y3 and loc_x3_y4 and there is a connection between loc_x3_y4 and loc_x3_y3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot transitions from loc_x0_y2 to loc_x0_y1, then from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y0 to loc_x1_y0, then to loc_x1_y1, and subsequently to loc_x1_y2. The robot continues by moving from loc_x1_y2 to loc_x0_y2, then to loc_x0_y3, and from loc_x0_y3 to loc_x0_y4, followed by loc_x0_y5. Finally, the robot moves from loc_x0_y5 to loc_x1_y5, reaching the current state. In this state, if the robot moves from loc_x1_y5 to loc_x1_y4, is it True or False that loc_x0_y4 is marked as visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y2 is visited, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 and loc_x1_y5 are connected, loc_x2_y5 and loc_x2_y4 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, the robot is at loc_x0_y2, a connection exists between loc_x0_y1 and loc_x0_y2, a connection exists between loc_x0_y1 and loc_x1_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y3 and loc_x0_y4, a connection exists between loc_x0_y4 and loc_x0_y5, a connection exists between loc_x1_y1 and loc_x2_y1, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y4 and loc_x0_y4, a connection exists between loc_x1_y5 and loc_x0_y5, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x1_y2, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y2 and loc_x2_y3, a connection exists between loc_x2_y3 and loc_x3_y3, a connection exists between loc_x2_y4 and loc_x2_y3, a connection exists between loc_x2_y4 and loc_x3_y4, a connection exists between loc_x3_y0 and loc_x2_y0, a connection exists between loc_x3_y1 and loc_x3_y2, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, a connection exists between loc_x3_y3 and loc_x3_y4, and a connection exists between loc_x3_y4 and loc_x3_y3."}
{"question_id": "7b69818b-4505-483c-abac-9812299035a4", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, if moves to loc_x0_y1 from loc_x0_y0, is it True or False that robot is located at loc_x0_y1 and robot is not located at loc_x0_y0?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 and loc_x1_y2 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is visited, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 and loc_x4_y3 are connected, loc_x4_y2 is connected to loc_x5_y2, loc_x4_y3 and loc_x4_y2 are connected, loc_x4_y3 and loc_x4_y4 are connected, loc_x4_y3 and loc_x5_y3 are connected, loc_x4_y3 is connected to loc_x3_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x5_y0 are connected, loc_x5_y1 is connected to loc_x4_y1, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y3 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y2, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 and loc_x5_y3 are connected, robot is placed at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4 and there is a connection between loc_x5_y3 and loc_x5_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: the robot moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, if the robot moves to loc_x0_y1 from loc_x0_y0, is it True or False that the robot is now at loc_x0_y1 and no longer at loc_x0_y0?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y2 and loc_x0_y1, loc_x0_y2 and loc_x1_y2, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 has been visited, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y1 and loc_x4_y1 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x3_y3 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 and loc_x5_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 and loc_x5_y1 are connected, loc_x4_y2 and loc_x4_y3 are connected, loc_x4_y2 and loc_x5_y2 are connected, loc_x4_y3 and loc_x4_y2 are connected, loc_x4_y3 and loc_x4_y4 are connected, loc_x4_y3 and loc_x5_y3 are connected, loc_x4_y3 and loc_x3_y3 are connected, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 and loc_x4_y0 are connected, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y1 and loc_x5_y0 are connected, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 and loc_x5_y2 are connected, loc_x5_y2 and loc_x5_y3 are connected, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y3 and loc_x4_y3 are connected, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 and loc_x5_y3 are connected. The robot is placed at loc_x1_y0. Additionally, the following connections exist: loc_x0_y0 and loc_x1_y0, loc_x0_y2 and loc_x0_y3, loc_x0_y3 and loc_x0_y2, loc_x0_y4 and loc_x0_y3, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 and loc_x1_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x1_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y4 and loc_x3_y4, loc_x4_y4 and loc_x5_y4, and loc_x5_y3 and loc_x5_y4."}
{"question_id": "1640f5fe-f907-4f26-a198-a90f27775790", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x4_y2 to loc_x3_y2, moves from loc_x3_y2 to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, moves from loc_x4_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x1_y0 and moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, if robot moves from loc_x0_y0 to loc_x0_y1, is it True or False that robot is not located at loc_x0_y0 and robot is placed at loc_x0_y1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y4, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x4_y1 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 is connected to loc_x3_y4, robot is located at loc_x4_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y1 and loc_x4_y0 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot relocates from loc_x4_y2 to loc_x3_y2, then from loc_x3_y2 to loc_x2_y2, followed by a move from loc_x2_y2 to loc_x2_y1, then to loc_x3_y1, and subsequently to loc_x4_y1, loc_x4_y0, loc_x3_y0, loc_x2_y0, loc_x1_y0, and finally to loc_x0_y0, resulting in the current state. In this state, if the robot proceeds from loc_x0_y0 to loc_x0_y1, is it True or False that the robot is no longer at loc_x0_y0 and is now positioned at loc_x0_y1?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x1_y3, loc_x0_y3 and loc_x0_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y4 and loc_x0_y4, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y2, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x2_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x4_y1, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, the robot is at loc_x4_y2. Additionally, the following connections exist: loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x2_y0 and loc_x1_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x1_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x4_y4, loc_x4_y1 and loc_x4_y0, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "14399933-02f1-4400-8ed2-9ea77fcf73a6", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x0_y2 to loc_x0_y1, robot moves from loc_x0_y1 to loc_x0_y0, moves to loc_x1_y0 from loc_x0_y0, moves from loc_x1_y0 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x1_y2, moves to loc_x0_y2 from loc_x1_y2, robot moves from loc_x0_y2 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x0_y4, moves from loc_x0_y4 to loc_x0_y5, robot moves from loc_x0_y5 to loc_x1_y5, from loc_x1_y5, the robot moves to loc_x1_y4, robot moves from loc_x1_y4 to loc_x1_y3, moves to loc_x2_y3 from loc_x1_y3, moves to loc_x2_y2 from loc_x2_y3, robot moves from loc_x2_y2 to loc_x2_y1, moves to loc_x2_y0 from loc_x2_y1, moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1 and moves from loc_x3_y1 to loc_x3_y2 to reach the current state. In this state, if moves from loc_x3_y2 to loc_x3_y3, is it True or False that robot is at loc_x3_y3 and robot is not located at loc_x3_y2?", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 is connected to loc_x1_y5, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y5, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y5 and loc_x0_y4, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y3 and loc_x3_y4 and there is a connection between loc_x3_y4 and loc_x3_y3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot transitions from loc_x0_y2 to loc_x0_y1, then from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y0 to loc_x1_y0, then from loc_x1_y0 to loc_x1_y1, and the robot proceeds from loc_x1_y1 to loc_x1_y2. Subsequently, the robot moves from loc_x1_y2 to loc_x0_y2, then from loc_x0_y2 to loc_x0_y3, and from loc_x0_y3 to loc_x0_y4. The robot continues by moving from loc_x0_y4 to loc_x0_y5, then from loc_x0_y5 to loc_x1_y5, followed by a transition from loc_x1_y5 to loc_x1_y4, and from loc_x1_y4 to loc_x1_y3. Next, the robot moves from loc_x1_y3 to loc_x2_y3, then from loc_x2_y3 to loc_x2_y2, and from loc_x2_y2 to loc_x2_y1. The sequence continues with a move from loc_x2_y1 to loc_x2_y0, then from loc_x2_y0 to loc_x3_y0, followed by a transition from loc_x3_y0 to loc_x3_y1, and finally from loc_x3_y1 to loc_x3_y2, resulting in the current state. In this state, if the robot moves from loc_x3_y2 to loc_x3_y3, is it True or False that the robot is now at loc_x3_y3 and no longer at loc_x3_y2?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y2 is visited, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 and loc_x1_y5 are connected, loc_x2_y5 and loc_x2_y4 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, the robot is at loc_x0_y2, a connection exists between loc_x0_y1 and loc_x0_y2, a connection exists between loc_x0_y1 and loc_x1_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y3 and loc_x0_y4, a connection exists between loc_x0_y5 and loc_x0_y4, a connection exists between loc_x1_y1 and loc_x2_y1, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y4 and loc_x0_y4, a connection exists between loc_x1_y5 and loc_x0_y5, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x1_y2, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y2 and loc_x2_y3, a connection exists between loc_x2_y3 and loc_x3_y3, a connection exists between loc_x2_y4 and loc_x2_y3, a connection exists between loc_x2_y4 and loc_x3_y4, a connection exists between loc_x3_y0 and loc_x2_y0, a connection exists between loc_x3_y1 and loc_x3_y2, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, a connection exists between loc_x3_y3 and loc_x3_y4, and a connection exists between loc_x3_y4 and loc_x3_y3."}
{"question_id": "ef652530-ab8a-43cf-9624-1abaee72419e", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x4_y2 to loc_x3_y2, robot moves from loc_x3_y2 to loc_x2_y2, moves from loc_x2_y2 to loc_x2_y1, moves from loc_x2_y1 to loc_x3_y1, moves to loc_x4_y1 from loc_x3_y1, robot moves from loc_x4_y1 to loc_x4_y0, robot moves from loc_x4_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x1_y0 and moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, if robot moves from loc_x0_y0 to loc_x0_y1, is it True or False that loc_x0_y1 is visited?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y4, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x4_y1 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 is connected to loc_x3_y4, robot is located at loc_x4_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y1 and loc_x4_y0 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot undergoes a series of movements: it transitions from loc_x4_y2 to loc_x3_y2, then to loc_x2_y2, followed by loc_x2_y1, then loc_x3_y1, and subsequently loc_x4_y1. From loc_x4_y1, the robot moves to loc_x4_y0, then to loc_x3_y0, loc_x2_y0, loc_x1_y0, and finally loc_x0_y0, resulting in the current state. In this state, if the robot proceeds from loc_x0_y0 to loc_x0_y1, is it True or False that loc_x0_y1 has been visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x1_y3, loc_x0_y3 and loc_x0_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y4 and loc_x0_y4, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y2, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x2_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x4_y1, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, the robot is at loc_x4_y2. Additionally, connections exist between loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x2_y0 and loc_x1_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x1_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x4_y4, loc_x4_y1 and loc_x4_y0, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "a28ce46b-4e5d-45a6-bf75-63cbb7d303b7", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x1_y3, robot moves from loc_x1_y3 to loc_x1_y4, robot moves from loc_x1_y4 to loc_x2_y4, robot moves from loc_x2_y4 to loc_x3_y4, from loc_x3_y4, the robot moves to loc_x3_y3, moves from loc_x3_y3 to loc_x2_y3, robot moves from loc_x2_y3 to loc_x2_y2, moves from loc_x2_y2 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x0_y1, moves from loc_x0_y1 to loc_x0_y0, from loc_x0_y0, the robot moves to loc_x1_y0, robot moves from loc_x1_y0 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x3_y2 and robot moves from loc_x3_y2 to loc_x4_y2 to reach the current state. In this state, if robot moves from loc_x4_y2 to loc_x4_y1, is it True or False that robot is located at loc_x4_y1 and robot is placed at loc_x4_y0?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is visited, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, robot is placed at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y3 and loc_x3_y4, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot undergoes a series of movements: it transitions from loc_x0_y3 to loc_x0_y4, then back to loc_x0_y3, followed by a move to loc_x1_y3, then to loc_x1_y4, and subsequently to loc_x2_y4, loc_x3_y4, and then down to loc_x3_y3. From there, the robot moves to loc_x2_y3, then to loc_x2_y2, loc_x2_y1, loc_x1_y1, loc_x0_y1, and loc_x0_y0. The robot then proceeds to loc_x1_y0, loc_x2_y0, loc_x3_y0, up to loc_x3_y1, loc_x3_y2, and finally to loc_x4_y2, reaching its current state. In this state, if the robot moves from loc_x4_y2 to loc_x4_y1, is it True or False that the robot is now at loc_x4_y1 and simultaneously placed at loc_x4_y0?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 has been visited, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, the robot's current position is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y0 and loc_x1_y0, a path exists between loc_x1_y0 and loc_x1_y1, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y1 and loc_x1_y0, a path exists between loc_x1_y3 and loc_x1_y4, a path exists between loc_x1_y3 and loc_x2_y3, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x1_y0, a path exists between loc_x2_y1 and loc_x1_y1, a path exists between loc_x2_y1 and loc_x2_y0, a path exists between loc_x2_y1 and loc_x3_y1, a path exists between loc_x2_y2 and loc_x3_y2, a path exists between loc_x2_y3 and loc_x1_y3, a path exists between loc_x2_y3 and loc_x2_y2, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x2_y4 and loc_x1_y4, a path exists between loc_x3_y3 and loc_x3_y4, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y0 and loc_x4_y1, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "54d67042-d2a3-4718-bb94-807506575b45", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x1_y0, the robot moves to loc_x0_y0, robot moves from loc_x0_y0 to loc_x0_y1, moves from loc_x0_y1 to loc_x1_y1, moves from loc_x1_y1 to loc_x2_y1, from loc_x2_y1, the robot moves to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, moves from loc_x3_y0 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x3_y2, from loc_x3_y2, the robot moves to loc_x3_y1, robot moves from loc_x3_y1 to loc_x4_y1, moves from loc_x4_y1 to loc_x4_y0, robot moves from loc_x4_y0 to loc_x5_y0, moves to loc_x5_y1 from loc_x5_y0, from loc_x5_y1, the robot moves to loc_x5_y2, moves from loc_x5_y2 to loc_x4_y2, from loc_x4_y2, the robot moves to loc_x4_y3, robot moves from loc_x4_y3 to loc_x5_y3, from loc_x5_y3, the robot moves to loc_x5_y4 and robot moves from loc_x5_y4 to loc_x4_y4 to reach the current state. In this state, if moves to loc_x3_y4 from loc_x4_y4, is it True or False that loc_x0_y2 is visited?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 and loc_x1_y2 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is visited, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 and loc_x4_y3 are connected, loc_x4_y2 is connected to loc_x5_y2, loc_x4_y3 and loc_x4_y2 are connected, loc_x4_y3 and loc_x4_y4 are connected, loc_x4_y3 and loc_x5_y3 are connected, loc_x4_y3 is connected to loc_x3_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x5_y0 are connected, loc_x5_y1 is connected to loc_x4_y1, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y3 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y2, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 and loc_x5_y3 are connected, robot is placed at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4 and there is a connection between loc_x5_y3 and loc_x5_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot performs the following sequence of actions: it starts at loc_x1_y0 and moves to loc_x0_y0, then to loc_x0_y1, followed by loc_x1_y1, loc_x2_y1, and loc_x2_y0. From there, it proceeds to loc_x3_y0, loc_x3_y1, loc_x3_y2, and then back to loc_x3_y1. The robot continues to loc_x4_y1, loc_x4_y0, loc_x5_y0, loc_x5_y1, loc_x5_y2, loc_x4_y2, loc_x4_y3, loc_x5_y3, loc_x5_y4, and finally loc_x4_y4. In this state, if the robot moves from loc_x4_y4 to loc_x3_y4, the question arises: is it True or False that loc_x0_y2 has been visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y2 and loc_x0_y1, loc_x0_y2 and loc_x1_y2, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 has been visited, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y1 and loc_x4_y1 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x3_y3 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 and loc_x5_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 and loc_x5_y1 are connected, loc_x4_y2 and loc_x4_y3 are connected, loc_x4_y2 and loc_x5_y2 are connected, loc_x4_y3 and loc_x4_y2 are connected, loc_x4_y3 and loc_x4_y4 are connected, loc_x4_y3 and loc_x5_y3 are connected, loc_x4_y3 and loc_x3_y3 are connected, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 and loc_x4_y0 are connected, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y1 and loc_x5_y0 are connected, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 and loc_x5_y2 are connected, loc_x5_y2 and loc_x5_y3 are connected, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y3 and loc_x4_y3 are connected, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 and loc_x5_y3 are connected. The robot is currently at loc_x1_y0. Additionally, the following connections exist: loc_x0_y0 and loc_x1_y0, loc_x0_y2 and loc_x0_y3, loc_x0_y3 and loc_x0_y2, loc_x0_y4 and loc_x0_y3, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 and loc_x1_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x1_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y4 and loc_x3_y4, loc_x4_y4 and loc_x5_y4, and loc_x5_y3 and loc_x5_y4."}
{"question_id": "6db5fc6b-755b-4829-90eb-afe48e68786f", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y1 from loc_x0_y2 to reach the current state. In this state, if moves from loc_x0_y1 to loc_x0_y0, is it True or False that robot is placed at loc_x3_y2?", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 is connected to loc_x1_y5, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y5, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y5 and loc_x0_y4, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y3 and loc_x3_y4 and there is a connection between loc_x3_y4 and loc_x3_y3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: moves from loc_x0_y2 to loc_x0_y1 to attain the current state. In this state, if moves from loc_x0_y1 to loc_x0_y0, is it True or False that the robot is positioned at loc_x3_y2?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y2 is visited, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 and loc_x1_y5 are connected, loc_x2_y5 and loc_x2_y4 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, the robot is at loc_x0_y2, a connection exists between loc_x0_y1 and loc_x0_y2, a connection exists between loc_x0_y1 and loc_x1_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y3 and loc_x0_y4, a connection exists between loc_x0_y5 and loc_x0_y4, a connection exists between loc_x1_y1 and loc_x2_y1, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y4 and loc_x0_y4, a connection exists between loc_x1_y5 and loc_x0_y5, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x1_y2, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y2 and loc_x2_y3, a connection exists between loc_x2_y3 and loc_x3_y3, a connection exists between loc_x2_y4 and loc_x2_y3, a connection exists between loc_x2_y4 and loc_x3_y4, a connection exists between loc_x3_y0 and loc_x2_y0, a connection exists between loc_x3_y1 and loc_x3_y2, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, a connection exists between loc_x3_y3 and loc_x3_y4, and a connection exists between loc_x3_y4 and loc_x3_y3."}
{"question_id": "a5f20a49-e606-4e6a-ab40-b3d1bd114833", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x4_y2, the robot moves to loc_x3_y2 to reach the current state. In this state, if robot moves from loc_x3_y2 to loc_x2_y2, is it True or False that robot is located at loc_x0_y3?", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y4, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x4_y1 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 is connected to loc_x3_y4, robot is located at loc_x4_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y1 and loc_x4_y0 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: the robot transitions from loc_x4_y2 to loc_x3_y2 to attain the current state. In this state, if the robot proceeds from loc_x3_y2 to loc_x2_y2, is it True or False that the robot is positioned at loc_x0_y3?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x1_y3, loc_x0_y3 and loc_x0_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y4 and loc_x0_y4, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y2, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x2_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x4_y1, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, the robot is at loc_x4_y2. Additionally, connections exist between loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x2_y0 and loc_x1_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x1_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x4_y4, loc_x4_y1 and loc_x4_y0, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "da9aa80c-ebe6-4052-8efe-1747d016fb80", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x1_y0, the robot moves to loc_x0_y0, moves from loc_x0_y0 to loc_x0_y1, moves from loc_x0_y1 to loc_x1_y1, from loc_x1_y1, the robot moves to loc_x2_y1, moves from loc_x2_y1 to loc_x2_y0, moves from loc_x2_y0 to loc_x3_y0, moves to loc_x3_y1 from loc_x3_y0, robot moves from loc_x3_y1 to loc_x3_y2, moves to loc_x3_y1 from loc_x3_y2 and moves to loc_x4_y1 from loc_x3_y1 to reach the current state. In this state, if moves to loc_x4_y0 from loc_x4_y1, is it True or False that robot is located at loc_x4_y0 and robot is not placed at loc_x4_y1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 and loc_x1_y2 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is visited, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 and loc_x4_y3 are connected, loc_x4_y2 is connected to loc_x5_y2, loc_x4_y3 and loc_x4_y2 are connected, loc_x4_y3 and loc_x4_y4 are connected, loc_x4_y3 and loc_x5_y3 are connected, loc_x4_y3 is connected to loc_x3_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x5_y0 are connected, loc_x5_y1 is connected to loc_x4_y1, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y3 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y2, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 and loc_x5_y3 are connected, robot is placed at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4 and there is a connection between loc_x5_y3 and loc_x5_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: starting from loc_x1_y0, the robot proceeds to loc_x0_y0, then to loc_x0_y1, followed by loc_x1_y1, then loc_x2_y1, loc_x2_y0, loc_x3_y0, loc_x3_y1, loc_x3_y2, back to loc_x3_y1, and finally to loc_x4_y1, resulting in the current state. In this state, if the robot moves from loc_x4_y1 to loc_x4_y0, is it True or False that the robot is now at loc_x4_y0 and no longer at loc_x4_y1?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y2 and loc_x0_y1, loc_x0_y2 and loc_x1_y2, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y0 is visited, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y2 and loc_x2_y2, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x3_y0 and loc_x2_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x3_y4, loc_x3_y3 and loc_x4_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x4_y1, loc_x4_y0 and loc_x5_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y1 and loc_x5_y1, loc_x4_y2 and loc_x4_y3, loc_x4_y2 and loc_x5_y2, loc_x4_y3 and loc_x4_y2, loc_x4_y3 and loc_x4_y4, loc_x4_y3 and loc_x5_y3, loc_x4_y3 and loc_x3_y3, loc_x4_y4 and loc_x4_y3, loc_x5_y0 and loc_x4_y0, loc_x5_y0 and loc_x5_y1, loc_x5_y1 and loc_x5_y0, loc_x5_y1 and loc_x4_y1, loc_x5_y1 and loc_x5_y2, loc_x5_y2 and loc_x5_y3, loc_x5_y2 and loc_x4_y2, loc_x5_y2 and loc_x5_y1, loc_x5_y3 and loc_x4_y3, loc_x5_y3 and loc_x5_y2, loc_x5_y4 and loc_x4_y4, loc_x5_y4 and loc_x5_y3. \n\nAdditionally, the robot is placed at loc_x1_y0. The following connections exist: loc_x0_y0 and loc_x1_y0, loc_x0_y2 and loc_x0_y3, loc_x0_y3 and loc_x0_y2, loc_x0_y4 and loc_x0_y3, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 and loc_x1_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x1_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y4 and loc_x3_y4, loc_x4_y4 and loc_x5_y4, and loc_x5_y3 and loc_x5_y4."}
{"question_id": "cb5fa685-d31d-4f92-84d7-54342b1338bf", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x4_y2, the robot moves to loc_x3_y2 to reach the current state. In this state, if moves from loc_x3_y2 to loc_x2_y2, is it True or False that robot is at loc_x2_y2 and robot is not at loc_x3_y2?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y4, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x4_y1 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 is connected to loc_x3_y4, robot is located at loc_x4_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y1 and loc_x4_y0 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: the robot transitions from loc_x4_y2 to loc_x3_y2 to attain the current state. In this state, if the robot moves from loc_x3_y2 to loc_x2_y2, is it True or False that the robot is now at loc_x2_y2 and no longer at loc_x3_y2?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x1_y3, loc_x0_y3 and loc_x0_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y4 and loc_x0_y4, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y2, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x2_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x4_y1, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, the robot is at loc_x4_y2. Additionally, connections exist between loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x2_y0 and loc_x1_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x1_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x4_y4, loc_x4_y1 and loc_x4_y0, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "5eccf08d-4f29-45df-b2e6-0370a984342e", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x4_y2 to loc_x3_y2, robot moves from loc_x3_y2 to loc_x2_y2, moves from loc_x2_y2 to loc_x2_y1, from loc_x2_y1, the robot moves to loc_x3_y1, moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, moves to loc_x3_y0 from loc_x4_y0, robot moves from loc_x3_y0 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x1_y0, moves to loc_x0_y0 from loc_x1_y0, moves to loc_x0_y1 from loc_x0_y0, moves to loc_x1_y1 from loc_x0_y1, robot moves from loc_x1_y1 to loc_x1_y2, moves to loc_x1_y3 from loc_x1_y2, moves to loc_x0_y3 from loc_x1_y3, from loc_x0_y3, the robot moves to loc_x0_y4, moves from loc_x0_y4 to loc_x1_y4, robot moves from loc_x1_y4 to loc_x2_y4 and robot moves from loc_x2_y4 to loc_x2_y3 to reach the current state. In this state, if moves to loc_x3_y3 from loc_x2_y3, is it True or False that loc_x3_y3 is marked as visited?", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y4, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x4_y1 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 is connected to loc_x3_y4, robot is located at loc_x4_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y1 and loc_x4_y0 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot undergoes the following sequence of movements: it moves from loc_x4_y2 to loc_x3_y2, then to loc_x2_y2, followed by loc_x2_y1, then loc_x3_y1, loc_x4_y1, loc_x4_y0, loc_x3_y0, loc_x2_y0, loc_x1_y0, loc_x0_y0, loc_x0_y1, loc_x1_y1, loc_x1_y2, loc_x1_y3, loc_x0_y3, loc_x0_y4, loc_x1_y4, loc_x2_y4, and finally loc_x2_y3. Now, if the robot moves from loc_x2_y3 to loc_x3_y3, the question is whether loc_x3_y3 is marked as visited or not.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x1_y3, loc_x0_y3 and loc_x0_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y4 and loc_x0_y4, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y2, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x2_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x4_y1, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, the robot is at loc_x4_y2. Additionally, connections exist between loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x2_y0 and loc_x1_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x1_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x4_y4, loc_x4_y1 and loc_x4_y0, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "10afa241-e621-4098-9ff9-c41eac8c485a", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x3_y2 from loc_x4_y2, robot moves from loc_x3_y2 to loc_x2_y2, from loc_x2_y2, the robot moves to loc_x2_y1, moves from loc_x2_y1 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, moves to loc_x3_y0 from loc_x4_y0, from loc_x3_y0, the robot moves to loc_x2_y0, robot moves from loc_x2_y0 to loc_x1_y0 and moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, if moves from loc_x0_y0 to loc_x0_y1, is it True or False that loc_x3_y3 is marked as visited?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y4, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x4_y1 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 is connected to loc_x3_y4, robot is located at loc_x4_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y1 and loc_x4_y0 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot executes the following sequence of actions: it moves from loc_x4_y2 to loc_x3_y2, then to loc_x2_y2, followed by loc_x2_y1, then loc_x3_y1, next loc_x4_y1, then loc_x4_y0, after that loc_x3_y0, followed by loc_x2_y0, then loc_x1_y0, and finally loc_x0_y0 to reach the current state. In this state, if the robot moves from loc_x0_y0 to loc_x0_y1, is it True or False that loc_x3_y3 is marked as visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x1_y3, loc_x0_y3 and loc_x0_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y4 and loc_x0_y4, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y2, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x2_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x4_y1, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, the robot is at loc_x4_y2. Additionally, connections exist between loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x2_y0 and loc_x1_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x1_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x4_y4, loc_x4_y1 and loc_x4_y0, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "4d65dcf6-3140-4cf5-ad2d-0f8018f6adf9", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, if from loc_x0_y0, the robot moves to loc_x0_y1, is it True or False that robot is not at loc_x4_y3?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 and loc_x1_y2 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is visited, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 and loc_x4_y3 are connected, loc_x4_y2 is connected to loc_x5_y2, loc_x4_y3 and loc_x4_y2 are connected, loc_x4_y3 and loc_x4_y4 are connected, loc_x4_y3 and loc_x5_y3 are connected, loc_x4_y3 is connected to loc_x3_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x5_y0 are connected, loc_x5_y1 is connected to loc_x4_y1, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y3 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y2, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 and loc_x5_y3 are connected, robot is placed at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4 and there is a connection between loc_x5_y3 and loc_x5_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: the robot moves from loc_x1_y0 to loc_x0_y0 to reach the current state. In this state, if the robot moves from loc_x0_y0 to loc_x0_y1, is it True or False that the robot is not at loc_x4_y3?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y2 and loc_x0_y1, loc_x0_y2 and loc_x1_y2, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 has been visited, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y2 and loc_x2_y2, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x3_y0 and loc_x2_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x3_y4, loc_x3_y3 and loc_x4_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x4_y1, loc_x4_y0 and loc_x5_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y1 and loc_x5_y1, loc_x4_y2 and loc_x4_y3, loc_x4_y2 and loc_x5_y2, loc_x4_y3 and loc_x4_y2, loc_x4_y3 and loc_x4_y4, loc_x4_y3 and loc_x5_y3, loc_x4_y3 and loc_x3_y3, loc_x4_y4 and loc_x4_y3, loc_x5_y0 and loc_x4_y0, loc_x5_y0 and loc_x5_y1, loc_x5_y1 and loc_x5_y0, loc_x5_y1 and loc_x4_y1, loc_x5_y1 and loc_x5_y2, loc_x5_y2 and loc_x5_y3, loc_x5_y2 and loc_x4_y2, loc_x5_y2 and loc_x5_y1, loc_x5_y3 and loc_x4_y3, loc_x5_y3 and loc_x5_y2, loc_x5_y4 and loc_x4_y4, loc_x5_y4 and loc_x5_y3, the robot is at loc_x1_y0, a connection exists between loc_x0_y0 and loc_x1_y0, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y3 and loc_x0_y2, a connection exists between loc_x0_y4 and loc_x0_y3, a connection exists between loc_x1_y1 and loc_x0_y1, a connection exists between loc_x1_y2 and loc_x0_y2, a connection exists between loc_x1_y2 and loc_x1_y1, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x1_y1, a connection exists between loc_x2_y2 and loc_x2_y3, a connection exists between loc_x2_y2 and loc_x3_y2, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y4 and loc_x4_y4, a connection exists between loc_x4_y0 and loc_x3_y0, a connection exists between loc_x4_y1 and loc_x3_y1, a connection exists between loc_x4_y1 and loc_x4_y0, a connection exists between loc_x4_y2 and loc_x3_y2, a connection exists between loc_x4_y2 and loc_x4_y1, a connection exists between loc_x4_y4 and loc_x3_y4, a connection exists between loc_x4_y4 and loc_x5_y4, and a connection exists between loc_x5_y3 and loc_x5_y4."}
{"question_id": "78896284-9eb2-4fa7-96fe-14e41a0b6d18", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x0_y3 to loc_x0_y4 to reach the current state. In this state, if robot moves from loc_x0_y4 to loc_x0_y3, is it True or False that robot is located at loc_x0_y3 and robot is not at loc_x0_y4?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is visited, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, robot is placed at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y3 and loc_x3_y4, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: the robot transitions from loc_x0_y3 to loc_x0_y4 to reach the current state. In this state, if the robot then moves from loc_x0_y4 to loc_x0_y3, is it True or False that the robot is now located at loc_x0_y3 and no longer at loc_x0_y4?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 has been visited, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is also connected to loc_x0_y3, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is also connected to loc_x2_y4, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 is also connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is also connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y0 is also connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is also connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y2 is also connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y1 is also connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, the robot is currently at loc_x0_y3, a connection exists between loc_x0_y0 and loc_x0_y1, a connection exists between loc_x0_y0 and loc_x1_y0, a connection exists between loc_x1_y0 and loc_x1_y1, a connection exists between loc_x1_y0 and loc_x2_y0, a connection exists between loc_x1_y1 and loc_x0_y1, a connection exists between loc_x1_y1 and loc_x1_y0, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y3 and loc_x2_y3, a connection exists between loc_x1_y4 and loc_x0_y4, a connection exists between loc_x2_y0 and loc_x1_y0, a connection exists between loc_x2_y1 and loc_x1_y1, a connection exists between loc_x2_y1 and loc_x2_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x3_y2, a connection exists between loc_x2_y3 and loc_x1_y3, a connection exists between loc_x2_y3 and loc_x2_y2, a connection exists between loc_x2_y3 and loc_x3_y3, a connection exists between loc_x2_y4 and loc_x1_y4, a connection exists between loc_x3_y3 and loc_x3_y4, a connection exists between loc_x3_y4 and loc_x3_y3, a connection exists between loc_x4_y0 and loc_x4_y1, and a connection exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "d7384339-ce87-4cd9-9fca-9cc8a5ec2bfc", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y0 from loc_x1_y0, robot moves from loc_x0_y0 to loc_x0_y1, moves to loc_x1_y1 from loc_x0_y1, robot moves from loc_x1_y1 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, from loc_x3_y0, the robot moves to loc_x3_y1, moves to loc_x3_y2 from loc_x3_y1, moves to loc_x3_y1 from loc_x3_y2, robot moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, moves to loc_x5_y0 from loc_x4_y0, moves to loc_x5_y1 from loc_x5_y0, robot moves from loc_x5_y1 to loc_x5_y2, moves from loc_x5_y2 to loc_x4_y2, from loc_x4_y2, the robot moves to loc_x4_y3, moves from loc_x4_y3 to loc_x5_y3, moves from loc_x5_y3 to loc_x5_y4 and from loc_x5_y4, the robot moves to loc_x4_y4 to reach the current state. In this state, if from loc_x4_y4, the robot moves to loc_x3_y4, is it True or False that robot is not located at loc_x4_y2 and robot is placed at loc_x4_y0?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 and loc_x1_y2 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is visited, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 and loc_x4_y3 are connected, loc_x4_y2 is connected to loc_x5_y2, loc_x4_y3 and loc_x4_y2 are connected, loc_x4_y3 and loc_x4_y4 are connected, loc_x4_y3 and loc_x5_y3 are connected, loc_x4_y3 is connected to loc_x3_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x5_y0 are connected, loc_x5_y1 is connected to loc_x4_y1, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y3 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y2, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 and loc_x5_y3 are connected, robot is placed at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4 and there is a connection between loc_x5_y3 and loc_x5_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot relocates from loc_x1_y0 to loc_x0_y0, then proceeds from loc_x0_y0 to loc_x0_y1, followed by a move from loc_x0_y1 to loc_x1_y1, then from loc_x1_y1 to loc_x2_y1, and subsequently from loc_x2_y1 to loc_x2_y0. The robot continues by moving from loc_x2_y0 to loc_x3_y0, then from loc_x3_y0 to loc_x3_y1, followed by a move from loc_x3_y1 to loc_x3_y2, and then back to loc_x3_y1. The robot then proceeds from loc_x3_y1 to loc_x4_y1, followed by a move from loc_x4_y1 to loc_x4_y0, then from loc_x4_y0 to loc_x5_y0, and from loc_x5_y0 to loc_x5_y1. The sequence continues with the robot moving from loc_x5_y1 to loc_x5_y2, then from loc_x5_y2 to loc_x4_y2, followed by a move from loc_x4_y2 to loc_x4_y3, then from loc_x4_y3 to loc_x5_y3, and from loc_x5_y3 to loc_x5_y4. Finally, the robot moves from loc_x5_y4 to loc_x4_y4, reaching the current state. In this state, if the robot moves from loc_x4_y4 to loc_x3_y4, the question arises: is it True or False that the robot is not at loc_x4_y2 and is instead located at loc_x4_y0?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y2 and loc_x0_y1, loc_x0_y2 and loc_x1_y2, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 has been visited, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y1 and loc_x4_y1 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x3_y3 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 and loc_x5_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 and loc_x5_y1 are connected, loc_x4_y2 and loc_x4_y3 are connected, loc_x4_y2 and loc_x5_y2 are connected, loc_x4_y3 and loc_x4_y2 are connected, loc_x4_y3 and loc_x4_y4 are connected, loc_x4_y3 and loc_x5_y3 are connected, loc_x4_y3 and loc_x3_y3 are connected, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 and loc_x4_y0 are connected, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y1 and loc_x5_y0 are connected, loc_x5_y1 and loc_x4_y1 are connected, loc_x5_y1 and loc_x5_y2 are connected, loc_x5_y2 and loc_x5_y3 are connected, loc_x5_y2 and loc_x4_y2 are connected, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y3 and loc_x4_y3 are connected, loc_x5_y3 and loc_x5_y2 are connected, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 and loc_x5_y3 are connected. The robot is placed at loc_x1_y0. Additionally, the following connections exist: loc_x0_y0 and loc_x1_y0, loc_x0_y2 and loc_x0_y3, loc_x0_y3 and loc_x0_y2, loc_x0_y4 and loc_x0_y3, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 and loc_x1_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x1_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y4 and loc_x3_y4, loc_x4_y4 and loc_x5_y4, and loc_x5_y3 and loc_x5_y4."}
{"question_id": "7c5b82d5-e5f0-4733-a803-5423fee234fe", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x4_y2 to loc_x3_y2, from loc_x3_y2, the robot moves to loc_x2_y2, moves from loc_x2_y2 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x3_y1, from loc_x3_y1, the robot moves to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, moves to loc_x3_y0 from loc_x4_y0, moves to loc_x2_y0 from loc_x3_y0, moves to loc_x1_y0 from loc_x2_y0, moves from loc_x1_y0 to loc_x0_y0, moves to loc_x0_y1 from loc_x0_y0, robot moves from loc_x0_y1 to loc_x1_y1, moves from loc_x1_y1 to loc_x1_y2, from loc_x1_y2, the robot moves to loc_x1_y3, from loc_x1_y3, the robot moves to loc_x0_y3, moves to loc_x0_y4 from loc_x0_y3, from loc_x0_y4, the robot moves to loc_x1_y4, from loc_x1_y4, the robot moves to loc_x2_y4 and moves from loc_x2_y4 to loc_x2_y3 to reach the current state. In this state, if robot moves from loc_x2_y3 to loc_x3_y3, is it True or False that robot is located at loc_x3_y3 and robot is not at loc_x2_y3?", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y3 is connected to loc_x1_y4, loc_x1_y4 and loc_x0_y4 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x4_y1 are connected, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x2_y3 are connected, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 is connected to loc_x3_y4, robot is located at loc_x4_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y2 and loc_x2_y2, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y0 and loc_x4_y0, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y1 and loc_x4_y0 and there is a connection between loc_x4_y2 and loc_x3_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot performs the following sequence of actions: it moves from loc_x4_y2 to loc_x3_y2, then from loc_x3_y2 to loc_x2_y2, followed by a move from loc_x2_y2 to loc_x2_y1, then from loc_x2_y1 to loc_x3_y1, and from loc_x3_y1 to loc_x4_y1. The robot continues by moving from loc_x4_y1 to loc_x4_y0, then to loc_x3_y0 from loc_x4_y0, and subsequently to loc_x2_y0 from loc_x3_y0. It then moves to loc_x1_y0 from loc_x2_y0, followed by a move to loc_x0_y0 from loc_x1_y0. The robot then changes direction, moving to loc_x0_y1 from loc_x0_y0, then to loc_x1_y1 from loc_x0_y1, and from loc_x1_y1 to loc_x1_y2. From loc_x1_y2, the robot moves to loc_x1_y3, then to loc_x0_y3 from loc_x1_y3, and from loc_x0_y3 to loc_x0_y4. The robot then moves to loc_x1_y4 from loc_x0_y4, followed by a move to loc_x2_y4 from loc_x1_y4, and finally from loc_x2_y4 to loc_x2_y3 to reach its current state. In this state, if the robot moves from loc_x2_y3 to loc_x3_y3, is it True or False that the robot is now located at loc_x3_y3 and no longer at loc_x2_y3?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x1_y3, loc_x0_y3 and loc_x0_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y1 and loc_x1_y2, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y4 and loc_x0_y4, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x2_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y2, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x2_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x4_y1, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y4, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x3_y0, loc_x4_y0 and loc_x4_y1, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, the robot is at loc_x4_y2. Additionally, connections exist between loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y2 and loc_x2_y2, loc_x1_y3 and loc_x1_y2, loc_x1_y3 and loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x2_y0 and loc_x1_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y2 and loc_x3_y2, loc_x2_y3 and loc_x1_y3, loc_x2_y4 and loc_x1_y4, loc_x3_y0 and loc_x4_y0, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x4_y4, loc_x4_y1 and loc_x4_y0, and loc_x4_y2 and loc_x3_y2."}
{"question_id": "cd144db0-c217-4a3d-9e8f-6a27edec0690", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y2 to loc_x0_y1, moves from loc_x0_y1 to loc_x0_y0, moves to loc_x1_y0 from loc_x0_y0, robot moves from loc_x1_y0 to loc_x1_y1, moves from loc_x1_y1 to loc_x1_y2, robot moves from loc_x1_y2 to loc_x0_y2, robot moves from loc_x0_y2 to loc_x0_y3, from loc_x0_y3, the robot moves to loc_x0_y4, moves to loc_x0_y5 from loc_x0_y4 and moves from loc_x0_y5 to loc_x1_y5 to reach the current state. In this state, if robot moves from loc_x1_y5 to loc_x1_y4, is it True or False that robot is at loc_x0_y0 and robot is not placed at loc_x2_y3?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 is connected to loc_x1_y5, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y5, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y5 and loc_x0_y4, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y3 and loc_x3_y4 and there is a connection between loc_x3_y4 and loc_x3_y3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot executes the following sequence of actions: it moves from loc_x0_y2 to loc_x0_y1, then to loc_x0_y0, followed by a move to loc_x1_y0, then to loc_x1_y1, and subsequently to loc_x1_y2. The robot then moves from loc_x1_y2 back to loc_x0_y2, and from there, it proceeds to loc_x0_y3, then to loc_x0_y4, and next to loc_x0_y5. Finally, it moves from loc_x0_y5 to loc_x1_y5, reaching the current state. In this state, if the robot moves from loc_x1_y5 to loc_x1_y4, is it True or False that the robot is at loc_x0_y0 and not at loc_x2_y3?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y2 is visited, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 and loc_x1_y5 are connected, loc_x2_y5 and loc_x2_y4 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, the robot is at loc_x0_y2, a connection exists between loc_x0_y1 and loc_x0_y2, a connection exists between loc_x0_y1 and loc_x1_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y3 and loc_x0_y4, a connection exists between loc_x0_y5 and loc_x0_y4, a connection exists between loc_x1_y1 and loc_x2_y1, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y4 and loc_x0_y4, a connection exists between loc_x1_y5 and loc_x0_y5, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x1_y2, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y2 and loc_x2_y3, a connection exists between loc_x2_y3 and loc_x3_y3, a connection exists between loc_x2_y4 and loc_x2_y3, a connection exists between loc_x2_y4 and loc_x3_y4, a connection exists between loc_x3_y0 and loc_x2_y0, a connection exists between loc_x3_y1 and loc_x3_y2, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, a connection exists between loc_x3_y3 and loc_x3_y4, and a connection exists between loc_x3_y4 and loc_x3_y3."}
{"question_id": "4afcac06-3148-46f6-a0a3-ac3c3a372c29", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_4", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x0_y2 to loc_x0_y1 to reach the current state. In this state, if moves from loc_x0_y1 to loc_x0_y0, is it True or False that loc_x0_y0 is not marked as visited?", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 is connected to loc_x1_y5, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y5, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y5 and loc_x0_y4, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y3 and loc_x3_y4 and there is a connection between loc_x3_y4 and loc_x3_y3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: a transition is made from loc_x0_y2 to loc_x0_y1 to attain the current state. In this state, if a transition is made from loc_x0_y1 to loc_x0_y0, is it True or False that loc_x0_y0 remains unvisited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y2 is visited, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 and loc_x1_y5 are connected, loc_x2_y5 and loc_x2_y4 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, the robot is at loc_x0_y2, a connection exists between loc_x0_y1 and loc_x0_y2, a connection exists between loc_x0_y1 and loc_x1_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y3 and loc_x0_y4, a connection exists between loc_x0_y5 and loc_x0_y4, a connection exists between loc_x1_y1 and loc_x2_y1, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y4 and loc_x0_y4, a connection exists between loc_x1_y5 and loc_x0_y5, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x1_y2, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y2 and loc_x2_y3, a connection exists between loc_x2_y3 and loc_x3_y3, a connection exists between loc_x2_y4 and loc_x2_y3, a connection exists between loc_x2_y4 and loc_x3_y4, a connection exists between loc_x3_y0 and loc_x2_y0, a connection exists between loc_x3_y1 and loc_x3_y2, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, a connection exists between loc_x3_y3 and loc_x3_y4, and a connection exists between loc_x3_y4 and loc_x3_y3."}
{"question_id": "4e204b79-9b15-4d8f-a0df-dd43e462935c", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x0_y2 to loc_x0_y1, from loc_x0_y1, the robot moves to loc_x0_y0, moves to loc_x1_y0 from loc_x0_y0, robot moves from loc_x1_y0 to loc_x1_y1, from loc_x1_y1, the robot moves to loc_x1_y2, moves to loc_x0_y2 from loc_x1_y2, robot moves from loc_x0_y2 to loc_x0_y3, moves from loc_x0_y3 to loc_x0_y4, moves to loc_x0_y5 from loc_x0_y4 and robot moves from loc_x0_y5 to loc_x1_y5 to reach the current state. In this state, if from loc_x1_y5, the robot moves to loc_x1_y4, is it True or False that robot is located at loc_x1_y4 and robot is not at loc_x1_y5?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 is connected to loc_x1_y5, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y5, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y5 and loc_x0_y4, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y3 and loc_x3_y4 and there is a connection between loc_x3_y4 and loc_x3_y3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot undergoes a series of movements: it transitions from loc_x0_y2 to loc_x0_y1, then from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y0 to loc_x1_y0. The robot continues by moving from loc_x1_y0 to loc_x1_y1, then to loc_x1_y2 from loc_x1_y1, and subsequently from loc_x1_y2 to loc_x0_y2. Next, the robot moves from loc_x0_y2 to loc_x0_y3, then to loc_x0_y4 from loc_x0_y3, and further to loc_x0_y5 from loc_x0_y4. Finally, the robot moves from loc_x0_y5 to loc_x1_y5, reaching its current state. In this state, if the robot moves from loc_x1_y5 to loc_x1_y4, the question arises: is it True or False that the robot is now located at loc_x1_y4 and no longer at loc_x1_y5?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y2 is visited, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 and loc_x1_y5 are connected, loc_x2_y5 and loc_x2_y4 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, the robot is at loc_x0_y2, a connection exists between loc_x0_y1 and loc_x0_y2, a connection exists between loc_x0_y1 and loc_x1_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y3 and loc_x0_y4, a connection exists between loc_x0_y5 and loc_x0_y4, a connection exists between loc_x1_y1 and loc_x2_y1, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y4 and loc_x0_y4, a connection exists between loc_x1_y5 and loc_x0_y5, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x1_y2, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y2 and loc_x2_y3, a connection exists between loc_x2_y3 and loc_x3_y3, a connection exists between loc_x2_y4 and loc_x2_y3, a connection exists between loc_x2_y4 and loc_x3_y4, a connection exists between loc_x3_y0 and loc_x2_y0, a connection exists between loc_x3_y1 and loc_x3_y2, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, a connection exists between loc_x3_y3 and loc_x3_y4, and a connection exists between loc_x3_y4 and loc_x3_y3."}
{"question_id": "8bd9b613-ab50-4863-bb1a-3aa3588ed579", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "effects", "question_name": "iter_1_question_6", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x1_y0 to loc_x0_y0, moves to loc_x0_y1 from loc_x0_y0, moves to loc_x1_y1 from loc_x0_y1, robot moves from loc_x1_y1 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, from loc_x3_y1, the robot moves to loc_x3_y2, from loc_x3_y2, the robot moves to loc_x3_y1 and moves from loc_x3_y1 to loc_x4_y1 to reach the current state. In this state, if moves to loc_x4_y0 from loc_x4_y1, is it True or False that robot is placed at loc_x5_y3?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 and loc_x1_y2 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is visited, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y3 and loc_x3_y4 are connected, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 and loc_x3_y3 are connected, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x5_y0, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x5_y1, loc_x4_y2 and loc_x4_y3 are connected, loc_x4_y2 is connected to loc_x5_y2, loc_x4_y3 and loc_x4_y2 are connected, loc_x4_y3 and loc_x4_y4 are connected, loc_x4_y3 and loc_x5_y3 are connected, loc_x4_y3 is connected to loc_x3_y3, loc_x4_y4 and loc_x4_y3 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y1 and loc_x5_y0 are connected, loc_x5_y1 is connected to loc_x4_y1, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y3 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y3 is connected to loc_x4_y3, loc_x5_y3 is connected to loc_x5_y2, loc_x5_y4 and loc_x4_y4 are connected, loc_x5_y4 and loc_x5_y3 are connected, robot is placed at loc_x1_y0, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x0_y4 and loc_x0_y3, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y2 and loc_x0_y2, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x4_y4, there is a connection between loc_x4_y0 and loc_x3_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x5_y4 and there is a connection between loc_x5_y3 and loc_x5_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot undergoes the following sequence of actions: it moves from loc_x1_y0 to loc_x0_y0, then proceeds to loc_x0_y1, followed by a move to loc_x1_y1 from loc_x0_y1. The robot then continues to loc_x2_y1 from loc_x1_y1, then to loc_x2_y0 from loc_x2_y1, and subsequently to loc_x3_y0 from loc_x2_y0. Next, it moves to loc_x3_y1 from loc_x3_y0, then to loc_x3_y2 from loc_x3_y1, and back to loc_x3_y1 from loc_x3_y2. Finally, it reaches the current state by moving to loc_x4_y1 from loc_x3_y1. In this state, if the robot moves to loc_x4_y0 from loc_x4_y1, is it True or False that the robot is placed at loc_x5_y3?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y1 and loc_x0_y0, loc_x0_y2 and loc_x0_y1, loc_x0_y2 and loc_x1_y2, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x2_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 has been visited, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y2 and loc_x2_y2, loc_x1_y4 and loc_x0_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x2_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x2_y2, loc_x2_y3 and loc_x3_y3, loc_x3_y0 and loc_x2_y0, loc_x3_y0 and loc_x3_y1, loc_x3_y0 and loc_x4_y0, loc_x3_y1 and loc_x2_y1, loc_x3_y1 and loc_x3_y0, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x4_y1, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y2 and loc_x4_y2, loc_x3_y3 and loc_x3_y4, loc_x3_y3 and loc_x4_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x3_y3, loc_x4_y0 and loc_x4_y1, loc_x4_y0 and loc_x5_y0, loc_x4_y1 and loc_x4_y2, loc_x4_y1 and loc_x5_y1, loc_x4_y2 and loc_x4_y3, loc_x4_y2 and loc_x5_y2, loc_x4_y3 and loc_x4_y2, loc_x4_y3 and loc_x4_y4, loc_x4_y3 and loc_x5_y3, loc_x4_y3 and loc_x3_y3, loc_x4_y4 and loc_x4_y3, loc_x5_y0 and loc_x4_y0, loc_x5_y0 and loc_x5_y1, loc_x5_y1 and loc_x5_y0, loc_x5_y1 and loc_x4_y1, loc_x5_y1 and loc_x5_y2, loc_x5_y2 and loc_x5_y3, loc_x5_y2 and loc_x4_y2, loc_x5_y2 and loc_x5_y1, loc_x5_y3 and loc_x4_y3, loc_x5_y3 and loc_x5_y2, loc_x5_y4 and loc_x4_y4, loc_x5_y4 and loc_x5_y3. \n\nAdditionally, the robot is placed at loc_x1_y0. There are connections between the following locations: loc_x0_y0 and loc_x1_y0, loc_x0_y2 and loc_x0_y3, loc_x0_y3 and loc_x0_y2, loc_x0_y4 and loc_x0_y3, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 and loc_x1_y1, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x1_y1, loc_x2_y2 and loc_x2_y3, loc_x2_y2 and loc_x3_y2, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y4 and loc_x4_y4, loc_x4_y0 and loc_x3_y0, loc_x4_y1 and loc_x3_y1, loc_x4_y1 and loc_x4_y0, loc_x4_y2 and loc_x3_y2, loc_x4_y2 and loc_x4_y1, loc_x4_y4 and loc_x3_y4, loc_x4_y4 and loc_x5_y4, and loc_x5_y3 and loc_x5_y4."}
{"question_id": "b5502435-3135-41ae-9751-299e04385347", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y1 from loc_x0_y2, moves from loc_x0_y1 to loc_x0_y0, robot moves from loc_x0_y0 to loc_x1_y0, from loc_x1_y0, the robot moves to loc_x1_y1, moves to loc_x1_y2 from loc_x1_y1, robot moves from loc_x1_y2 to loc_x0_y2, robot moves from loc_x0_y2 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y5 and robot moves from loc_x0_y5 to loc_x1_y5 to reach the current state. In this state, if moves to loc_x1_y4 from loc_x1_y5, is it True or False that loc_x1_y4 is visited?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 is connected to loc_x1_y5, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y5, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y5 and loc_x0_y4, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y3 and loc_x3_y4 and there is a connection between loc_x3_y4 and loc_x3_y3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot relocates from loc_x0_y2 to loc_x0_y1, then proceeds from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y0 to loc_x1_y0. From loc_x1_y0, the robot transitions to loc_x1_y1, then to loc_x1_y2 from loc_x1_y1, and subsequently returns to loc_x0_y2 from loc_x1_y2. The robot then continues its path from loc_x0_y2 to loc_x0_y3, then to loc_x0_y4, and from loc_x0_y4 to loc_x0_y5. Finally, the robot moves from loc_x0_y5 to loc_x1_y5, reaching the current state. In this state, if the robot moves from loc_x1_y5 to loc_x1_y4, is it True or False that loc_x1_y4 has been visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y2 is visited, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 and loc_x1_y5 are connected, loc_x2_y5 and loc_x2_y4 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, the robot is at loc_x0_y2, and the following connections exist: loc_x0_y1 and loc_x0_y2, loc_x0_y1 and loc_x1_y1, loc_x0_y2 and loc_x0_y3, loc_x0_y2 and loc_x1_y2, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x0_y5, loc_x1_y1 and loc_x2_y1, loc_x1_y3 and loc_x1_y4, loc_x1_y4 and loc_x0_y4, loc_x1_y5 and loc_x0_y5, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y1 and loc_x2_y2, loc_x2_y2 and loc_x2_y3, loc_x2_y3 and loc_x3_y3, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x3_y4, loc_x3_y0 and loc_x2_y0, loc_x3_y1 and loc_x3_y2, loc_x3_y1 and loc_x3_y2, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, loc_x3_y3 and loc_x3_y2, loc_x3_y3 and loc_x3_y4, and loc_x3_y4 and loc_x3_y3."}
{"question_id": "7b08011d-b6f5-4d62-b371-596d978ed86b", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y3 to loc_x0_y4, moves from loc_x0_y4 to loc_x0_y3, moves from loc_x0_y3 to loc_x1_y3, robot moves from loc_x1_y3 to loc_x1_y4, moves from loc_x1_y4 to loc_x2_y4, moves to loc_x3_y4 from loc_x2_y4, robot moves from loc_x3_y4 to loc_x3_y3, robot moves from loc_x3_y3 to loc_x2_y3, robot moves from loc_x2_y3 to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, from loc_x2_y1, the robot moves to loc_x1_y1, robot moves from loc_x1_y1 to loc_x0_y1, moves from loc_x0_y1 to loc_x0_y0, from loc_x0_y0, the robot moves to loc_x1_y0, moves from loc_x1_y0 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, moves to loc_x3_y2 from loc_x3_y1 and moves from loc_x3_y2 to loc_x4_y2 to reach the current state. In this state, if from loc_x4_y2, the robot moves to loc_x4_y1, is it True or False that loc_x4_y1 is visited?", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is visited, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, robot is placed at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y3 and loc_x3_y4, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot undergoes the following sequence of movements: it moves from loc_x0_y3 to loc_x0_y4, then back to loc_x0_y3, followed by a move to loc_x1_y3. The robot then proceeds from loc_x1_y3 to loc_x1_y4, then to loc_x2_y4, and subsequently to loc_x3_y4. From loc_x3_y4, the robot moves to loc_x3_y3, then to loc_x2_y3, followed by loc_x2_y2, and then loc_x2_y1. Next, it moves from loc_x2_y1 to loc_x1_y1, then to loc_x0_y1, and then to loc_x0_y0. The robot then moves from loc_x0_y0 to loc_x1_y0, followed by loc_x2_y0, and then loc_x3_y0. From loc_x3_y0, it moves to loc_x3_y1, then to loc_x3_y2, and finally to loc_x4_y2 to reach the current state. In this state, if the robot moves from loc_x4_y2 to loc_x4_y1, is it True or False that loc_x4_y1 has been visited?", "initial_state_nl_paraphrased": "loc_x0_y1 is adjacent to loc_x0_y0, loc_x0_y1 is adjacent to loc_x1_y1, loc_x0_y3 is linked with loc_x0_y4, loc_x0_y3 is linked with loc_x1_y3, loc_x0_y3 has been visited, loc_x0_y4 is adjacent to loc_x1_y4, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 is adjacent to loc_x0_y0, loc_x1_y1 is adjacent to loc_x2_y1, loc_x1_y3 is linked with loc_x0_y3, loc_x1_y4 is adjacent to loc_x1_y3, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 is adjacent to loc_x3_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is adjacent to loc_x2_y2, loc_x2_y2 is adjacent to loc_x2_y1, loc_x2_y2 is linked with loc_x2_y3, loc_x2_y3 is adjacent to loc_x2_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 is adjacent to loc_x2_y0, loc_x3_y0 is adjacent to loc_x4_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is adjacent to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 is adjacent to loc_x2_y2, loc_x3_y2 is linked with loc_x3_y3, loc_x3_y2 is adjacent to loc_x4_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is adjacent to loc_x4_y0, loc_x4_y1 is adjacent to loc_x4_y2, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 is adjacent to loc_x3_y2, the robot's current position is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x0_y0 and loc_x1_y0, a path exists between loc_x1_y0 and loc_x1_y1, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y1 and loc_x1_y0, a path exists between loc_x1_y3 and loc_x1_y4, a path exists between loc_x1_y3 and loc_x2_y3, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x1_y0, a path exists between loc_x2_y1 and loc_x1_y1, a path exists between loc_x2_y1 and loc_x2_y0, a path exists between loc_x2_y1 and loc_x3_y1, a path exists between loc_x2_y2 and loc_x3_y2, a path exists between loc_x2_y3 and loc_x1_y3, a path exists between loc_x2_y3 and loc_x2_y2, a path exists between loc_x2_y3 and loc_x3_y3, a path exists between loc_x2_y4 and loc_x1_y4, a path exists between loc_x3_y3 and loc_x3_y4, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y0 and loc_x4_y1, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "5c9430f6-06ff-44af-9c1d-3685552b6d0d", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y4 from loc_x0_y3, robot moves from loc_x0_y4 to loc_x0_y3, moves to loc_x1_y3 from loc_x0_y3, moves from loc_x1_y3 to loc_x1_y4, moves to loc_x2_y4 from loc_x1_y4, robot moves from loc_x2_y4 to loc_x3_y4, moves from loc_x3_y4 to loc_x3_y3, from loc_x3_y3, the robot moves to loc_x2_y3, moves to loc_x2_y2 from loc_x2_y3 and robot moves from loc_x2_y2 to loc_x2_y1 to reach the current state. In this state, if robot moves from loc_x2_y1 to loc_x1_y1, is it True or False that robot is at loc_x1_y1 and robot is not placed at loc_x2_y1?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is visited, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, robot is placed at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y3 and loc_x3_y4, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions occurs: the robot transitions from loc_x0_y3 to loc_x0_y4, then back to loc_x0_y3, followed by a move to loc_x1_y3, then to loc_x1_y4, then to loc_x2_y4, and subsequently to loc_x3_y4. The robot then moves to loc_x3_y3, then to loc_x2_y3, followed by loc_x2_y2, and finally to loc_x2_y1, resulting in the current state. In this state, if the robot moves from loc_x2_y1 to loc_x1_y1, is it True or False that the robot is now at loc_x1_y1 and no longer at loc_x2_y1?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 has been visited, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is also connected to loc_x0_y3, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is also connected to loc_x2_y4, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 is also connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is also connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y0 is also connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is also connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is also connected to loc_x4_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y1 is also connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, the robot is currently at loc_x0_y3, a connection exists between loc_x0_y0 and loc_x0_y1, a connection exists between loc_x0_y0 and loc_x1_y0, a connection exists between loc_x1_y0 and loc_x1_y1, a connection exists between loc_x1_y0 and loc_x2_y0, a connection exists between loc_x1_y1 and loc_x0_y1, a connection exists between loc_x1_y1 and loc_x1_y0, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y3 and loc_x2_y3, a connection exists between loc_x1_y4 and loc_x0_y4, a connection exists between loc_x2_y0 and loc_x1_y0, a connection exists between loc_x2_y1 and loc_x1_y1, a connection exists between loc_x2_y1 and loc_x2_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x3_y2, a connection exists between loc_x2_y3 and loc_x1_y3, a connection exists between loc_x2_y3 and loc_x2_y2, a connection exists between loc_x2_y3 and loc_x3_y3, a connection exists between loc_x2_y4 and loc_x1_y4, a connection exists between loc_x3_y3 and loc_x3_y4, a connection exists between loc_x3_y4 and loc_x3_y3, a connection exists between loc_x4_y0 and loc_x4_y1, and a connection exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "6a983fc0-31a6-4ad9-acb3-5e9a5f470906", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "effects", "question_name": "iter_1_question_5", "fluent_type": "persistent_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y4 from loc_x0_y3, robot moves from loc_x0_y4 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x1_y3, robot moves from loc_x1_y3 to loc_x1_y4, moves to loc_x2_y4 from loc_x1_y4, moves to loc_x3_y4 from loc_x2_y4, moves to loc_x3_y3 from loc_x3_y4, moves from loc_x3_y3 to loc_x2_y3, robot moves from loc_x2_y3 to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, from loc_x2_y1, the robot moves to loc_x1_y1, moves from loc_x1_y1 to loc_x0_y1, moves to loc_x0_y0 from loc_x0_y1, moves to loc_x1_y0 from loc_x0_y0, robot moves from loc_x1_y0 to loc_x2_y0, moves from loc_x2_y0 to loc_x3_y0, moves to loc_x3_y1 from loc_x3_y0, robot moves from loc_x3_y1 to loc_x3_y2 and robot moves from loc_x3_y2 to loc_x4_y2 to reach the current state. In this state, if moves from loc_x4_y2 to loc_x4_y1, is it True or False that robot is not located at loc_x4_y2 and robot is placed at loc_x4_y1?", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is visited, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 and loc_x4_y2 are connected, loc_x4_y1 is connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, robot is placed at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y3 and loc_x2_y3, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x3_y2, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x3_y3 and loc_x3_y4, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y0 and loc_x4_y1 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot transitions from loc_x0_y3 to loc_x0_y4, then from loc_x0_y4 back to loc_x0_y3, followed by a move from loc_x0_y3 to loc_x1_y3, then to loc_x1_y4, and subsequently to loc_x2_y4 from loc_x1_y4, then to loc_x3_y4 from loc_x2_y4, then to loc_x3_y3 from loc_x3_y4, and then to loc_x2_y3 from loc_x3_y3. The robot continues by moving from loc_x2_y3 to loc_x2_y2, then to loc_x2_y1, and from loc_x2_y1 to loc_x1_y1, followed by a move to loc_x0_y1 from loc_x1_y1, then to loc_x0_y0 from loc_x0_y1, then to loc_x1_y0 from loc_x0_y0, and then to loc_x2_y0 from loc_x1_y0, followed by a move to loc_x3_y0 from loc_x2_y0. The robot then moves to loc_x3_y1 from loc_x3_y0, then to loc_x3_y2 from loc_x3_y1, and finally to loc_x4_y2 from loc_x3_y2, reaching the current state. In this state, if the robot moves from loc_x4_y2 to loc_x4_y1, is it True or False that the robot is no longer at loc_x4_y2 and is now located at loc_x4_y1?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 has been visited, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is also connected to loc_x0_y3, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y3 is connected to loc_x0_y3, loc_x1_y4 is connected to loc_x1_y3, loc_x1_y4 is also connected to loc_x2_y4, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 is also connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x2_y4 is also connected to loc_x3_y4, loc_x3_y0 and loc_x2_y0 are connected, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y0 is also connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y1 is also connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is also connected to loc_x4_y2, loc_x3_y2 is connected to loc_x3_y1, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y2, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y1 is also connected to loc_x3_y1, loc_x4_y2 and loc_x3_y2 are connected, the robot is currently at loc_x0_y3, a connection exists between loc_x0_y0 and loc_x0_y1, a connection exists between loc_x0_y0 and loc_x1_y0, a connection exists between loc_x1_y0 and loc_x1_y1, a connection exists between loc_x1_y0 and loc_x2_y0, a connection exists between loc_x1_y1 and loc_x0_y1, a connection exists between loc_x1_y1 and loc_x1_y0, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y3 and loc_x2_y3, a connection exists between loc_x1_y4 and loc_x0_y4, a connection exists between loc_x2_y0 and loc_x1_y0, a connection exists between loc_x2_y1 and loc_x1_y1, a connection exists between loc_x2_y1 and loc_x2_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x3_y2, a connection exists between loc_x2_y3 and loc_x1_y3, a connection exists between loc_x2_y3 and loc_x2_y2, a connection exists between loc_x2_y3 and loc_x3_y3, a connection exists between loc_x2_y4 and loc_x1_y4, a connection exists between loc_x3_y3 and loc_x3_y4, a connection exists between loc_x3_y4 and loc_x3_y3, a connection exists between loc_x4_y0 and loc_x4_y1, and a connection exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "29b66c8e-c26a-428b-a804-0973d7c944c9", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "effects", "question_name": "iter_1_question_3", "fluent_type": "derived_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are performed: from loc_x0_y2, the robot moves to loc_x0_y1, robot moves from loc_x0_y1 to loc_x0_y0, robot moves from loc_x0_y0 to loc_x1_y0, moves from loc_x1_y0 to loc_x1_y1, moves to loc_x1_y2 from loc_x1_y1, robot moves from loc_x1_y2 to loc_x0_y2, robot moves from loc_x0_y2 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y5, from loc_x0_y5, the robot moves to loc_x1_y5, robot moves from loc_x1_y5 to loc_x1_y4, moves to loc_x1_y3 from loc_x1_y4, robot moves from loc_x1_y3 to loc_x2_y3, robot moves from loc_x2_y3 to loc_x2_y2, from loc_x2_y2, the robot moves to loc_x2_y1, robot moves from loc_x2_y1 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x3_y0, moves to loc_x3_y1 from loc_x3_y0 and from loc_x3_y1, the robot moves to loc_x3_y2 to reach the current state. In this state, if moves to loc_x3_y3 from loc_x3_y2, is it True or False that loc_x3_y3 is visited?", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is visited, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 is connected to loc_x1_y5, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 is connected to loc_x0_y1, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 is connected to loc_x2_y4, loc_x1_y5 is connected to loc_x1_y4, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y3 is connected to loc_x2_y2, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y5, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y1 and loc_x0_y2, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y2 and loc_x1_y2, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y5 and loc_x0_y4, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x2_y3 and loc_x3_y3, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x3_y3 and loc_x3_y4 and there is a connection between loc_x3_y4 and loc_x3_y3.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: starting from loc_x0_y2, the robot proceeds to loc_x0_y1, then to loc_x0_y0, followed by loc_x1_y0, then loc_x1_y1, and loc_x1_y2. From loc_x1_y2, the robot returns to loc_x0_y2, then moves to loc_x0_y3, loc_x0_y4, and loc_x0_y5. Next, the robot moves from loc_x0_y5 to loc_x1_y5, then to loc_x1_y4, loc_x1_y3, and loc_x2_y3. The robot continues to loc_x2_y2, loc_x2_y1, and loc_x2_y0, before moving to loc_x3_y0, loc_x3_y1, and finally loc_x3_y2, reaching the current state. In this state, if the robot moves from loc_x3_y2 to loc_x3_y3, is it True or False that loc_x3_y3 has been visited?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y2 is visited, loc_x0_y2 and loc_x0_y3 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x0_y5 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y1 and loc_x1_y2 are connected, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x1_y3 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y2 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 and loc_x1_y5 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y5 and loc_x1_y4 are connected, loc_x1_y5 and loc_x2_y5 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y4 and loc_x1_y4 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 and loc_x1_y5 are connected, loc_x2_y5 and loc_x2_y4 are connected, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, the robot is at loc_x0_y2, a connection exists between loc_x0_y1 and loc_x0_y2, a connection exists between loc_x0_y1 and loc_x1_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y2 and loc_x1_y2, a connection exists between loc_x0_y3 and loc_x0_y4, a connection exists between loc_x0_y4 and loc_x0_y5, a connection exists between loc_x1_y1 and loc_x2_y1, a connection exists between loc_x1_y3 and loc_x1_y4, a connection exists between loc_x1_y4 and loc_x0_y4, a connection exists between loc_x1_y5 and loc_x0_y5, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x1_y2, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y2 and loc_x2_y3, a connection exists between loc_x2_y3 and loc_x3_y3, a connection exists between loc_x2_y4 and loc_x2_y3, a connection exists between loc_x2_y4 and loc_x3_y4, a connection exists between loc_x3_y0 and loc_x2_y0, a connection exists between loc_x3_y1 and loc_x3_y2, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, a connection exists between loc_x3_y3 and loc_x3_y4, and a connection exists between loc_x3_y4 and loc_x3_y3."}
