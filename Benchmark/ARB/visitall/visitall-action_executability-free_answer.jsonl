{"question_id": "4e4e2855-a36e-4302-b332-9719a066495d", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y2 to loc_x0_y1, moves from loc_x0_y1 to loc_x0_y0, robot moves from loc_x0_y0 to loc_x1_y0, robot moves from loc_x1_y0 to loc_x1_y1, moves to loc_x2_y0 from loc_x3_y4, robot moves from loc_x1_y2 to loc_x0_y2, robot moves from loc_x0_y2 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x0_y4, from loc_x0_y4, the robot moves to loc_x0_y5 and moves to loc_x1_y5 from loc_x0_y5 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "moves from loc_x3_y4 to loc_x2_y0", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x0_y2, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y5, loc_x0_y5 and loc_x0_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x2_y2, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: the robot transitions from loc_x0_y2 to loc_x0_y1, then from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y0 to loc_x1_y0, and then from loc_x1_y0 to loc_x1_y1. Subsequently, the robot moves from loc_x3_y4 to loc_x2_y0, then from loc_x1_y2 to loc_x0_y2, followed by a transition from loc_x0_y2 to loc_x0_y3, and then from loc_x0_y3 to loc_x0_y4. Finally, the robot moves from loc_x0_y4 to loc_x0_y5 and then from loc_x0_y5 to loc_x1_y5 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y1 to loc_x0_y2, loc_x0_y1 to loc_x1_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y2 to loc_x0_y3, loc_x0_y2 to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3, loc_x0_y3 to loc_x0_y4, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x0_y4 to loc_x0_y5, loc_x0_y4 and loc_x0_y5, loc_x0_y5 and loc_x1_y5, loc_x1_y0 to loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 to loc_x1_y3, loc_x1_y2 to loc_x2_y2, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 to loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 to loc_x0_y4, loc_x1_y4 to loc_x1_y5, loc_x1_y5 to loc_x2_y5, loc_x2_y0 to loc_x2_y1, loc_x2_y1 to loc_x1_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y2 to loc_x2_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x2_y5, loc_x2_y5 to loc_x1_y5, loc_x2_y5 to loc_x2_y4, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x3_y1, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y0, loc_x3_y1 to loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, the robot is at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y2, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x2_y4, loc_x1_y5 and loc_x0_y5, loc_x1_y5 and loc_x1_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x3_y4, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, and loc_x3_y3 and loc_x3_y4."}
{"question_id": "a06c1433-667c-437d-a3be-2c0dc4a445c5", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x4_y2 from loc_x3_y3 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "moves from loc_x3_y3 to loc_x4_y2", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, robot is at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y4 and loc_x4_y4 and there is a connection between loc_x4_y1 and loc_x3_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: moves from loc_x3_y3 to loc_x4_y2 to reach the current state. What is the first action in the sequence that cannot be executed? Write None if there are no such actions.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y0 to loc_x1_y0, loc_x0_y1 to loc_x1_y1, loc_x0_y3 to loc_x0_y4, loc_x0_y3 to loc_x1_y3, loc_x0_y4 to loc_x0_y3, loc_x1_y0 to loc_x0_y0, loc_x1_y0 to loc_x2_y0, loc_x1_y0 to loc_x1_y1, loc_x1_y1 to loc_x0_y1, loc_x1_y1 to loc_x1_y2, loc_x1_y1 to loc_x2_y1, loc_x1_y2 to loc_x2_y2, loc_x1_y3 to loc_x0_y3, loc_x1_y3 to loc_x2_y3, loc_x1_y3 to loc_x1_y2, loc_x1_y4 to loc_x0_y4, loc_x2_y0 to loc_x2_y1, loc_x2_y0 to loc_x1_y0, loc_x2_y1 to loc_x2_y2, loc_x2_y2 to loc_x2_y1, loc_x2_y2 to loc_x2_y3, loc_x2_y2 to loc_x1_y2, loc_x2_y2 to loc_x3_y2, loc_x2_y3 to loc_x2_y2, loc_x2_y3 to loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y3 to loc_x1_y3, loc_x2_y4 to loc_x1_y4, loc_x2_y4 to loc_x3_y4, loc_x3_y0 to loc_x3_y1, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x4_y0, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y2, loc_x3_y1 to loc_x4_y1, loc_x3_y2 to loc_x2_y2, loc_x3_y2 to loc_x3_y3, loc_x3_y2 to loc_x4_y2, loc_x3_y3 to loc_x3_y2, loc_x3_y3 to loc_x2_y3, loc_x3_y3 to loc_x3_y4, loc_x3_y4 to loc_x3_y3, loc_x3_y4 to loc_x2_y4, loc_x4_y0 to loc_x4_y1, loc_x4_y0 to loc_x3_y0, loc_x4_y1 to loc_x4_y0, loc_x4_y1 to loc_x4_y2, loc_x4_y2 to loc_x3_y2, loc_x4_y2 to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 to loc_x3_y4, the robot is at loc_x4_y2. Additionally, the following connections exist: loc_x0_y1 to loc_x0_y0, loc_x0_y4 to loc_x1_y4, loc_x1_y1 to loc_x1_y0, loc_x1_y2 to loc_x1_y1, loc_x1_y2 to loc_x1_y3, loc_x1_y3 to loc_x1_y4, loc_x1_y4 to loc_x1_y3, loc_x1_y4 to loc_x2_y4, loc_x2_y0 to loc_x3_y0, loc_x2_y1 to loc_x1_y1, loc_x2_y1 to loc_x2_y0, loc_x2_y1 to loc_x3_y1, loc_x2_y4 to loc_x2_y3, loc_x3_y1 to loc_x3_y0, loc_x3_y2 to loc_x3_y1, loc_x3_y4 to loc_x4_y4, and loc_x4_y1 to loc_x3_y1."}
{"question_id": "59c41bae-446d-4fc2-818d-7d7d13b229c0", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y5 from loc_x3_y1 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "moves from loc_x3_y1 to loc_x0_y5", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x0_y2, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y5, loc_x0_y5 and loc_x0_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x2_y2, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the subsequent actions taken are: moving from loc_x3_y1 to loc_x0_y5 to attain the current state. What is the first action in the sequence that cannot be executed? If there are none, write None.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y1 to loc_x0_y2, loc_x0_y1 to loc_x1_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y2 to loc_x0_y3, loc_x0_y2 to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3, loc_x0_y3 to loc_x0_y4, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x0_y4 to loc_x0_y5, loc_x0_y4 and loc_x0_y5, loc_x0_y5 and loc_x1_y5, loc_x1_y0 to loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 to loc_x1_y3, loc_x1_y2 to loc_x2_y2, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 to loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 to loc_x0_y4, loc_x1_y4 to loc_x1_y5, loc_x1_y5 to loc_x2_y5, loc_x2_y0 to loc_x2_y1, loc_x2_y1 to loc_x1_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y2 to loc_x2_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x2_y5, loc_x2_y5 to loc_x1_y5, loc_x2_y5 to loc_x2_y4, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x3_y1, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y0, loc_x3_y1 to loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, the robot is at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x1_y0, a connection exists between loc_x0_y1 and loc_x0_y0, a connection exists between loc_x0_y3 and loc_x0_y2, a connection exists between loc_x1_y0 and loc_x0_y0, a connection exists between loc_x1_y0 and loc_x1_y1, a connection exists between loc_x1_y1 and loc_x0_y1, a connection exists between loc_x1_y1 and loc_x1_y2, a connection exists between loc_x1_y1 and loc_x2_y1, a connection exists between loc_x1_y2 and loc_x1_y1, a connection exists between loc_x1_y3 and loc_x1_y2, a connection exists between loc_x1_y4 and loc_x2_y4, a connection exists between loc_x1_y5 and loc_x0_y5, a connection exists between loc_x1_y5 and loc_x1_y4, a connection exists between loc_x2_y0 and loc_x1_y0, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x2_y0, a connection exists between loc_x2_y1 and loc_x2_y2, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x1_y2, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y3 and loc_x1_y3, a connection exists between loc_x2_y3 and loc_x2_y2, a connection exists between loc_x2_y4 and loc_x1_y4, a connection exists between loc_x2_y4 and loc_x3_y4, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, and a connection exists between loc_x3_y3 and loc_x3_y4."}
{"question_id": "79ca20d1-3b21-4ff9-a606-79a776d45322", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x4_y0 to loc_x3_y0, moves to loc_x2_y2 from loc_x3_y2, robot moves from loc_x2_y2 to loc_x2_y1, moves from loc_x2_y1 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, moves to loc_x3_y0 from loc_x4_y0, robot moves from loc_x3_y0 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x1_y0, from loc_x1_y0, the robot moves to loc_x0_y0, moves from loc_x0_y0 to loc_x0_y1, moves to loc_x1_y1 from loc_x0_y1, robot moves from loc_x1_y1 to loc_x1_y2, from loc_x1_y2, the robot moves to loc_x1_y3, moves to loc_x0_y3 from loc_x1_y3, robot moves from loc_x0_y3 to loc_x0_y4, moves to loc_x1_y4 from loc_x0_y4, moves to loc_x2_y4 from loc_x1_y4 and moves from loc_x2_y4 to loc_x2_y3 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "robot moves from loc_x4_y0 to loc_x3_y0", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, robot is at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y4 and loc_x4_y4 and there is a connection between loc_x4_y1 and loc_x3_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot performs the following sequence of actions: it moves from loc_x4_y0 to loc_x3_y0, then from loc_x3_y2 to loc_x2_y2, followed by a move from loc_x2_y2 to loc_x2_y1, then from loc_x2_y1 to loc_x3_y1, and from loc_x3_y1 to loc_x4_y1. The robot continues by moving from loc_x4_y1 to loc_x4_y0, then from loc_x4_y0 to loc_x3_y0, and from loc_x3_y0 to loc_x2_y0. It then proceeds to loc_x1_y0, and from there, it moves to loc_x0_y0, followed by a move to loc_x0_y1. The robot then moves from loc_x0_y1 to loc_x1_y1, then to loc_x1_y2, and from loc_x1_y2 to loc_x1_y3. Next, it moves from loc_x1_y3 to loc_x0_y3, then from loc_x0_y3 to loc_x0_y4, and from loc_x0_y4 to loc_x1_y4. Finally, it moves from loc_x1_y4 to loc_x2_y4 and then from loc_x2_y4 to loc_x2_y3 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y0 to loc_x1_y0, loc_x0_y1 to loc_x1_y1, loc_x0_y3 to loc_x0_y4, loc_x0_y3 to loc_x1_y3, loc_x0_y4 to loc_x0_y3, loc_x1_y0 to loc_x0_y0, loc_x1_y0 to loc_x2_y0, loc_x1_y0 to loc_x1_y1, loc_x1_y1 to loc_x0_y1, loc_x1_y1 to loc_x1_y2, loc_x1_y1 to loc_x2_y1, loc_x1_y2 to loc_x2_y2, loc_x1_y3 to loc_x0_y3, loc_x1_y3 to loc_x2_y3, loc_x1_y3 to loc_x1_y2, loc_x1_y4 to loc_x0_y4, loc_x2_y0 to loc_x2_y1, loc_x2_y0 to loc_x1_y0, loc_x2_y1 to loc_x2_y2, loc_x2_y2 to loc_x2_y1, loc_x2_y2 to loc_x2_y3, loc_x2_y2 to loc_x1_y2, loc_x2_y2 to loc_x3_y2, loc_x2_y3 to loc_x2_y2, loc_x2_y3 to loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y3 to loc_x1_y3, loc_x2_y4 to loc_x1_y4, loc_x2_y4 to loc_x3_y4, loc_x3_y0 to loc_x3_y1, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x4_y0, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y2, loc_x3_y1 to loc_x4_y1, loc_x3_y2 to loc_x2_y2, loc_x3_y2 to loc_x3_y3, loc_x3_y2 to loc_x4_y2, loc_x3_y3 to loc_x3_y2, loc_x3_y3 to loc_x2_y3, loc_x3_y3 to loc_x3_y4, loc_x3_y4 to loc_x3_y3, loc_x3_y4 to loc_x2_y4, loc_x4_y0 to loc_x4_y1, loc_x4_y0 to loc_x3_y0, loc_x4_y1 to loc_x4_y0, loc_x4_y1 to loc_x4_y2, loc_x4_y2 to loc_x3_y2, loc_x4_y2 to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 to loc_x3_y4, the robot is at loc_x4_y2. Additionally, the following connections exist: loc_x0_y1 to loc_x0_y0, loc_x0_y4 to loc_x1_y4, loc_x1_y1 to loc_x1_y0, loc_x1_y2 to loc_x1_y1, loc_x1_y2 to loc_x1_y3, loc_x1_y3 to loc_x1_y4, loc_x1_y4 to loc_x1_y3, loc_x1_y4 to loc_x2_y4, loc_x2_y0 to loc_x3_y0, loc_x2_y1 to loc_x1_y1, loc_x2_y1 to loc_x2_y0, loc_x2_y1 to loc_x3_y1, loc_x2_y4 to loc_x2_y3, loc_x3_y1 to loc_x3_y0, loc_x3_y2 to loc_x3_y1, loc_x3_y4 to loc_x4_y4, and loc_x4_y1 to loc_x3_y1."}
{"question_id": "968f9fa1-0f08-4e47-bf16-e17f5426bca8", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y1 from loc_x0_y2, from loc_x0_y1, the robot moves to loc_x0_y0, from loc_x0_y0, the robot moves to loc_x1_y0, moves from loc_x1_y0 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x1_y2, moves from loc_x1_y2 to loc_x0_y2, robot moves from loc_x0_y2 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x0_y4, from loc_x0_y4, the robot moves to loc_x0_y5 and moves from loc_x0_y5 to loc_x1_y5 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "robot moves from loc_x1_y5 to loc_x0_y5, robot moves from loc_x1_y5 to loc_x2_y5 and moves to loc_x1_y4 from loc_x1_y5", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x0_y2, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y5, loc_x0_y5 and loc_x0_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x2_y2, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot transitions from loc_x0_y2 to loc_x0_y1, then from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y0 to loc_x1_y0, then to loc_x1_y1, and subsequently to loc_x1_y2, after which it moves to loc_x0_y2, then to loc_x0_y3, and then to loc_x0_y4, from loc_x0_y4 it proceeds to loc_x0_y5, and finally from loc_x0_y5 to loc_x1_y5, resulting in the current state. In this state, list all possible actions that can be executed. If there are no executable actions, indicate None.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y1 to loc_x0_y2, loc_x0_y1 to loc_x1_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y2 to loc_x0_y3, loc_x0_y2 to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3, loc_x0_y3 to loc_x0_y4, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x0_y4 to loc_x0_y5, loc_x0_y4 and loc_x0_y5, loc_x0_y5 and loc_x1_y5, loc_x1_y0 to loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 to loc_x1_y3, loc_x1_y2 to loc_x2_y2, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 to loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 to loc_x0_y4, loc_x1_y4 to loc_x1_y5, loc_x1_y5 to loc_x2_y5, loc_x2_y0 to loc_x2_y1, loc_x2_y1 to loc_x1_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y2 to loc_x2_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x2_y5, loc_x2_y5 to loc_x1_y5, loc_x2_y5 to loc_x2_y4, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x3_y1, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y0, loc_x3_y1 to loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, the robot is at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y2, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x2_y4, loc_x1_y5 and loc_x0_y5, loc_x1_y5 and loc_x1_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x3_y4, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, and loc_x3_y3 and loc_x3_y4."}
{"question_id": "01d0bcdf-211f-4c9c-a298-653102cefd2f", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from loc_x0_y3, the robot moves to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x1_y3, moves from loc_x1_y3 to loc_x1_y4, robot moves from loc_x1_y4 to loc_x2_y4, robot moves from loc_x2_y4 to loc_x3_y4, moves to loc_x3_y3 from loc_x3_y4, moves from loc_x3_y3 to loc_x2_y3, robot moves from loc_x2_y3 to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, from loc_x2_y1, the robot moves to loc_x1_y1, moves from loc_x2_y4 to loc_x2_y0, robot moves from loc_x0_y1 to loc_x0_y0, moves to loc_x1_y0 from loc_x0_y0, from loc_x1_y0, the robot moves to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x3_y0, moves from loc_x3_y0 to loc_x3_y1, moves from loc_x3_y1 to loc_x3_y2 and robot moves from loc_x3_y2 to loc_x4_y2 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "robot moves from loc_x2_y4 to loc_x2_y0", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: starting from loc_x0_y3, the robot proceeds to loc_x0_y4, then returns to loc_x0_y3, and continues to loc_x1_y3, followed by loc_x1_y4, then loc_x2_y4, and loc_x3_y4. From loc_x3_y4, the robot moves to loc_x3_y3, then to loc_x2_y3, followed by loc_x2_y2, and loc_x2_y1. Next, the robot moves from loc_x2_y1 to loc_x1_y1, and from loc_x2_y4 to loc_x2_y0. Additionally, the robot moves from loc_x0_y1 to loc_x0_y0, then to loc_x1_y0, followed by loc_x2_y0, and loc_x3_y0. The robot then proceeds from loc_x3_y0 to loc_x3_y1, then loc_x3_y2, and finally from loc_x3_y2 to loc_x4_y2 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "b5dffa36-0758-42d5-9279-47f44102d30a", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y2 to loc_x0_y1, robot moves from loc_x0_y1 to loc_x0_y0, robot moves from loc_x0_y0 to loc_x1_y0, from loc_x1_y0, the robot moves to loc_x1_y1, moves to loc_x1_y2 from loc_x1_y1, from loc_x1_y2, the robot moves to loc_x0_y2, robot moves from loc_x0_y2 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y5, robot moves from loc_x0_y5 to loc_x1_y5, moves to loc_x1_y4 from loc_x1_y5, robot moves from loc_x1_y4 to loc_x1_y3, robot moves from loc_x1_y3 to loc_x2_y3, moves from loc_x2_y3 to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, moves to loc_x2_y0 from loc_x2_y1, moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1 and from loc_x3_y1, the robot moves to loc_x3_y2 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "robot moves from loc_x3_y2 to loc_x3_y1, robot moves from loc_x3_y2 to loc_x3_y3 and moves to loc_x2_y2 from loc_x3_y2", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x0_y2, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y5, loc_x0_y5 and loc_x0_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x2_y2, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: the robot moves from loc_x0_y2 to loc_x0_y1, then from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y0 to loc_x1_y0. From loc_x1_y0, the robot proceeds to loc_x1_y1, then to loc_x1_y2, and from loc_x1_y2, it returns to loc_x0_y2. The robot then moves from loc_x0_y2 to loc_x0_y3, then to loc_x0_y4, and subsequently to loc_x0_y5. From loc_x0_y5, the robot moves to loc_x1_y5, then to loc_x1_y4, followed by a move to loc_x1_y3. The robot continues to loc_x2_y3, then to loc_x2_y2, and then to loc_x2_y1. From loc_x2_y1, it moves to loc_x2_y0, then to loc_x3_y0, and from loc_x3_y0, it proceeds to loc_x3_y1, and finally from loc_x3_y1 to loc_x3_y2 to reach the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y1 to loc_x0_y2, loc_x0_y1 to loc_x1_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y2 to loc_x0_y3, loc_x0_y2 to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3, loc_x0_y3 to loc_x0_y4, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x0_y4 to loc_x0_y5, loc_x0_y4 and loc_x0_y5, loc_x0_y5 and loc_x1_y5, loc_x1_y0 to loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 to loc_x1_y3, loc_x1_y2 to loc_x2_y2, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 to loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 to loc_x0_y4, loc_x1_y4 to loc_x1_y5, loc_x1_y5 to loc_x2_y5, loc_x2_y0 to loc_x2_y1, loc_x2_y1 to loc_x1_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y2 to loc_x2_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x2_y5, loc_x2_y5 to loc_x1_y5, loc_x2_y5 to loc_x2_y4, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x3_y1, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y0, loc_x3_y1 to loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, the robot is at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y2, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x2_y4, loc_x1_y5 and loc_x0_y5, loc_x1_y5 and loc_x1_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x3_y4, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, and loc_x3_y3 and loc_x3_y4."}
{"question_id": "545e119b-5a28-4c41-b8af-24674b66071f", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y2 to loc_x0_y1 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "robot moves from loc_x0_y1 to loc_x1_y1, moves to loc_x0_y0 from loc_x0_y1 and robot moves from loc_x0_y1 to loc_x0_y2", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x0_y2, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y5, loc_x0_y5 and loc_x0_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x2_y2, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the robot takes the following steps: it moves from loc_x0_y2 to loc_x0_y1 to attain the current state. In this state, identify all possible actions that can be executed. If there are no actions, indicate None.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y1 to loc_x0_y2, loc_x0_y1 to loc_x1_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y2 to loc_x0_y3, loc_x0_y2 to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3, loc_x0_y3 to loc_x0_y4, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x0_y4 to loc_x0_y5, loc_x0_y4 and loc_x0_y5, loc_x0_y5 and loc_x1_y5, loc_x1_y0 to loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 to loc_x1_y3, loc_x1_y2 to loc_x2_y2, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 to loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 to loc_x0_y4, loc_x1_y4 to loc_x1_y5, loc_x1_y5 to loc_x2_y5, loc_x2_y0 to loc_x2_y1, loc_x2_y1 to loc_x1_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y2 to loc_x2_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x2_y5, loc_x2_y5 to loc_x1_y5, loc_x2_y5 to loc_x2_y4, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x3_y1, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y0, loc_x3_y1 to loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, the robot is at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y2, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x2_y4, loc_x1_y5 and loc_x0_y5, loc_x1_y5 and loc_x1_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x3_y4, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, and loc_x3_y3 and loc_x3_y4."}
{"question_id": "fd487071-7168-4282-be02-d522395eb22a", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from loc_x0_y3, the robot moves to loc_x0_y4 to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "moves from loc_x3_y4 to loc_x3_y3, from loc_x0_y1, the robot moves to loc_x2_y1, robot moves from loc_x2_y1 to loc_x0_y1, from loc_x2_y4, the robot moves to loc_x1_y1, from loc_x1_y4, the robot moves to loc_x0_y1, from loc_x0_y1, the robot moves to loc_x3_y2, from loc_x1_y3, the robot moves to loc_x2_y3, from loc_x0_y4, the robot moves to loc_x3_y2, robot moves from loc_x2_y4 to loc_x3_y4, robot moves from loc_x1_y1 to loc_x0_y4, moves to loc_x2_y2 from loc_x4_y2, moves to loc_x0_y1 from loc_x2_y2, robot moves from loc_x3_y3 to loc_x1_y4, from loc_x1_y0, the robot moves to loc_x4_y0, moves from loc_x2_y0 to loc_x1_y3, from loc_x1_y4, the robot moves to loc_x2_y2, robot moves from loc_x0_y3 to loc_x1_y3, robot moves from loc_x1_y4 to loc_x1_y1, from loc_x0_y1, the robot moves to loc_x0_y4, robot moves from loc_x1_y1 to loc_x0_y1, moves to loc_x3_y0 from loc_x3_y2, moves from loc_x1_y1 to loc_x1_y3, moves to loc_x0_y1 from loc_x3_y0, robot moves from loc_x3_y0 to loc_x4_y1, robot moves from loc_x1_y4 to loc_x4_y2, robot moves from loc_x2_y2 to loc_x1_y1, from loc_x0_y1, the robot moves to loc_x3_y4, robot moves from loc_x4_y0 to loc_x2_y4, robot moves from loc_x2_y3 to loc_x4_y0, moves to loc_x2_y2 from loc_x4_y0, robot moves from loc_x2_y3 to loc_x2_y0, robot moves from loc_x2_y4 to loc_x3_y0, moves to loc_x0_y3 from loc_x3_y3, robot moves from loc_x0_y0 to loc_x2_y2, robot moves from loc_x3_y4 to loc_x0_y4, robot moves from loc_x0_y0 to loc_x4_y1, robot moves from loc_x1_y0 to loc_x2_y3, robot moves from loc_x2_y2 to loc_x2_y4, robot moves from loc_x2_y3 to loc_x0_y3, moves from loc_x4_y0 to loc_x1_y4, from loc_x1_y3, the robot moves to loc_x3_y1, moves from loc_x2_y2 to loc_x2_y1, moves from loc_x4_y1 to loc_x4_y2, from loc_x4_y0, the robot moves to loc_x2_y0, moves from loc_x0_y0 to loc_x0_y1, moves from loc_x0_y1 to loc_x1_y0, moves to loc_x1_y4 from loc_x4_y1, moves from loc_x1_y0 to loc_x4_y1, moves to loc_x3_y3 from loc_x0_y0, moves from loc_x1_y1 to loc_x1_y0, robot moves from loc_x4_y1 to loc_x0_y3, robot moves from loc_x3_y2 to loc_x0_y1, moves from loc_x0_y4 to loc_x3_y3, robot moves from loc_x4_y1 to loc_x1_y3, moves to loc_x4_y1 from loc_x2_y0, robot moves from loc_x0_y1 to loc_x1_y1, from loc_x4_y0, the robot moves to loc_x2_y3, robot moves from loc_x2_y2 to loc_x0_y3, robot moves from loc_x3_y1 to loc_x0_y1, from loc_x4_y1, the robot moves to loc_x0_y1, robot moves from loc_x3_y0 to loc_x4_y2, from loc_x4_y2, the robot moves to loc_x3_y4, moves to loc_x1_y4 from loc_x1_y0, from loc_x0_y0, the robot moves to loc_x1_y4, moves from loc_x1_y0 to loc_x3_y3, moves from loc_x4_y2 to loc_x3_y1, robot moves from loc_x3_y3 to loc_x2_y1, robot moves from loc_x3_y1 to loc_x0_y4, moves to loc_x0_y0 from loc_x4_y2, moves from loc_x2_y4 to loc_x2_y2, robot moves from loc_x0_y3 to loc_x3_y1, from loc_x0_y3, the robot moves to loc_x3_y4, from loc_x2_y3, the robot moves to loc_x3_y0, moves to loc_x2_y0 from loc_x2_y2, moves from loc_x3_y1 to loc_x2_y2, robot moves from loc_x2_y3 to loc_x1_y0, robot moves from loc_x4_y1 to loc_x2_y4, moves to loc_x4_y2 from loc_x3_y1, moves from loc_x2_y0 to loc_x0_y3, from loc_x1_y1, the robot moves to loc_x1_y4, robot moves from loc_x2_y4 to loc_x3_y3, from loc_x2_y0, the robot moves to loc_x0_y4, from loc_x3_y2, the robot moves to loc_x4_y1, from loc_x1_y4, the robot moves to loc_x3_y3, moves from loc_x3_y0 to loc_x2_y4, moves from loc_x1_y3 to loc_x3_y0, robot moves from loc_x1_y3 to loc_x1_y1, robot moves from loc_x3_y1 to loc_x2_y3, moves to loc_x0_y0 from loc_x3_y4, moves to loc_x2_y0 from loc_x3_y2, robot moves from loc_x1_y3 to loc_x1_y0, moves from loc_x1_y1 to loc_x2_y4, from loc_x4_y2, the robot moves to loc_x2_y0, moves to loc_x3_y1 from loc_x2_y1, from loc_x0_y1, the robot moves to loc_x2_y0, robot moves from loc_x0_y3 to loc_x0_y1, robot moves from loc_x0_y4 to loc_x0_y1, moves to loc_x1_y4 from loc_x0_y1, moves from loc_x2_y3 to loc_x1_y1, moves to loc_x4_y1 from loc_x0_y4, moves to loc_x0_y4 from loc_x4_y0, robot moves from loc_x0_y0 to loc_x3_y0, moves from loc_x2_y0 to loc_x1_y0, moves from loc_x0_y1 to loc_x3_y1, moves from loc_x4_y1 to loc_x3_y4, robot moves from loc_x2_y2 to loc_x3_y1, robot moves from loc_x4_y1 to loc_x1_y1, moves to loc_x4_y1 from loc_x1_y4, robot moves from loc_x3_y1 to loc_x1_y0, from loc_x1_y3, the robot moves to loc_x4_y0, robot moves from loc_x2_y3 to loc_x0_y1, robot moves from loc_x3_y1 to loc_x2_y0, robot moves from loc_x0_y4 to loc_x2_y4, moves to loc_x2_y2 from loc_x1_y0, moves to loc_x4_y2 from loc_x2_y1, from loc_x1_y4, the robot moves to loc_x3_y1, robot moves from loc_x0_y4 to loc_x4_y0, robot moves from loc_x1_y4 to loc_x2_y3, from loc_x1_y1, the robot moves to loc_x0_y3, robot moves from loc_x4_y2 to loc_x2_y4, from loc_x2_y0, the robot moves to loc_x3_y1, robot moves from loc_x4_y2 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x3_y3, from loc_x2_y4, the robot moves to loc_x0_y3, robot moves from loc_x4_y1 to loc_x2_y3, from loc_x3_y1, the robot moves to loc_x0_y3, robot moves from loc_x3_y4 to loc_x2_y2, robot moves from loc_x0_y0 to loc_x3_y2, from loc_x4_y2, the robot moves to loc_x1_y3, robot moves from loc_x4_y0 to loc_x3_y3, from loc_x0_y3, the robot moves to loc_x1_y1, moves to loc_x1_y4 from loc_x3_y1, moves to loc_x3_y2 from loc_x1_y1, robot moves from loc_x0_y0 to loc_x3_y1, robot moves from loc_x3_y4 to loc_x0_y1, moves to loc_x2_y1 from loc_x3_y4, from loc_x4_y2, the robot moves to loc_x3_y0, moves to loc_x1_y1 from loc_x1_y0, from loc_x1_y0, the robot moves to loc_x0_y1, robot moves from loc_x2_y3 to loc_x2_y1, robot moves from loc_x3_y0 to loc_x1_y0, robot moves from loc_x0_y4 to loc_x1_y1, moves to loc_x4_y0 from loc_x2_y1, from loc_x2_y4, the robot moves to loc_x1_y3, from loc_x1_y1, the robot moves to loc_x3_y1, from loc_x2_y2, the robot moves to loc_x1_y3, robot moves from loc_x1_y4 to loc_x4_y0, moves to loc_x2_y0 from loc_x3_y4, moves from loc_x2_y3 to loc_x0_y4, robot moves from loc_x3_y4 to loc_x4_y2, moves to loc_x3_y1 from loc_x3_y0, moves from loc_x1_y3 to loc_x2_y4, from loc_x2_y3, the robot moves to loc_x2_y4, moves from loc_x0_y0 to loc_x1_y1, moves from loc_x2_y2 to loc_x0_y4, from loc_x3_y4, the robot moves to loc_x1_y1, from loc_x2_y0, the robot moves to loc_x4_y2, moves from loc_x1_y0 to loc_x2_y0, moves from loc_x3_y0 to loc_x2_y1, from loc_x3_y4, the robot moves to loc_x3_y1, moves to loc_x1_y0 from loc_x3_y4, robot moves from loc_x1_y0 to loc_x3_y2, robot moves from loc_x1_y3 to loc_x1_y4, moves to loc_x2_y1 from loc_x3_y2, from loc_x1_y3, the robot moves to loc_x0_y4, robot moves from loc_x2_y3 to loc_x4_y1, from loc_x1_y4, the robot moves to loc_x2_y4, robot moves from loc_x2_y3 to loc_x2_y2, robot moves from loc_x3_y1 to loc_x3_y4, moves to loc_x2_y1 from loc_x0_y4, robot moves from loc_x3_y1 to loc_x1_y1, moves from loc_x1_y4 to loc_x0_y0, robot moves from loc_x0_y1 to loc_x4_y1, robot moves from loc_x1_y3 to loc_x0_y1, robot moves from loc_x2_y4 to loc_x4_y1, from loc_x0_y3, the robot moves to loc_x4_y2, robot moves from loc_x2_y1 to loc_x4_y1, moves from loc_x0_y1 to loc_x0_y3, moves from loc_x4_y2 to loc_x2_y1, from loc_x4_y2, the robot moves to loc_x3_y3, robot moves from loc_x1_y4 to loc_x1_y0, from loc_x3_y1, the robot moves to loc_x2_y1, moves to loc_x2_y2 from loc_x1_y1, moves to loc_x4_y0 from loc_x2_y2, moves from loc_x4_y0 to loc_x0_y1, robot moves from loc_x0_y4 to loc_x3_y1, from loc_x0_y4, the robot moves to loc_x3_y0, robot moves from loc_x1_y4 to loc_x2_y0, from loc_x3_y2, the robot moves to loc_x4_y2, robot moves from loc_x3_y0 to loc_x3_y2, from loc_x3_y4, the robot moves to loc_x2_y4, robot moves from loc_x0_y0 to loc_x2_y1, robot moves from loc_x0_y3 to loc_x2_y3, moves from loc_x2_y3 to loc_x3_y4, from loc_x2_y1, the robot moves to loc_x0_y3, robot moves from loc_x2_y0 to loc_x3_y2, robot moves from loc_x4_y2 to loc_x4_y1, robot moves from loc_x2_y2 to loc_x3_y3, moves from loc_x1_y1 to loc_x4_y0, robot moves from loc_x2_y3 to loc_x3_y1, robot moves from loc_x3_y0 to loc_x2_y0, from loc_x1_y4, the robot moves to loc_x3_y2, moves to loc_x3_y0 from loc_x2_y2, from loc_x1_y0, the robot moves to loc_x1_y3, from loc_x4_y2, the robot moves to loc_x1_y4, moves from loc_x0_y0 to loc_x2_y3, moves from loc_x1_y1 to loc_x2_y3, from loc_x0_y3, the robot moves to loc_x3_y2, robot moves from loc_x0_y0 to loc_x1_y0, from loc_x2_y4, the robot moves to loc_x2_y1, from loc_x4_y1, the robot moves to loc_x3_y0, moves to loc_x1_y1 from loc_x3_y0, robot moves from loc_x2_y0 to loc_x2_y3, moves to loc_x3_y1 from loc_x2_y4, robot moves from loc_x0_y3 to loc_x2_y0, robot moves from loc_x4_y2 to loc_x2_y3, moves to loc_x1_y3 from loc_x3_y3, moves to loc_x3_y3 from loc_x1_y1, moves to loc_x2_y0 from loc_x1_y3, robot moves from loc_x3_y3 to loc_x1_y1, moves to loc_x4_y2 from loc_x3_y3, robot moves from loc_x0_y1 to loc_x2_y4, from loc_x2_y2, the robot moves to loc_x0_y0, from loc_x3_y0, the robot moves to loc_x1_y3, moves from loc_x4_y1 to loc_x3_y1, moves to loc_x3_y2 from loc_x4_y1, moves from loc_x1_y1 to loc_x4_y1, robot moves from loc_x2_y1 to loc_x2_y2, from loc_x4_y0, the robot moves to loc_x3_y2, moves from loc_x0_y4 to loc_x3_y4, robot moves from loc_x3_y2 to loc_x3_y1, from loc_x1_y0, the robot moves to loc_x3_y0, robot moves from loc_x3_y2 to loc_x4_y0, moves to loc_x4_y2 from loc_x1_y1, moves to loc_x4_y0 from loc_x3_y1, robot moves from loc_x3_y4 to loc_x4_y0, robot moves from loc_x2_y0 to loc_x1_y4, robot moves from loc_x4_y2 to loc_x0_y4, moves from loc_x3_y3 to loc_x3_y4, moves from loc_x2_y0 to loc_x4_y0, from loc_x2_y1, the robot moves to loc_x0_y0, moves from loc_x0_y3 to loc_x2_y2, from loc_x0_y4, the robot moves to loc_x0_y0, from loc_x0_y1, the robot moves to loc_x2_y2, from loc_x2_y4, the robot moves to loc_x0_y0, robot moves from loc_x3_y1 to loc_x2_y4, robot moves from loc_x3_y4 to loc_x3_y2, moves to loc_x1_y3 from loc_x0_y4, from loc_x3_y1, the robot moves to loc_x0_y0, moves to loc_x2_y2 from loc_x3_y2, robot moves from loc_x3_y1 to loc_x3_y2, from loc_x2_y3, the robot moves to loc_x1_y4, moves to loc_x2_y2 from loc_x3_y0, robot moves from loc_x0_y0 to loc_x2_y0, robot moves from loc_x3_y0 to loc_x3_y4, from loc_x1_y3, the robot moves to loc_x3_y3, robot moves from loc_x1_y1 to loc_x0_y0, robot moves from loc_x1_y3 to loc_x4_y2, robot moves from loc_x3_y4 to loc_x2_y3, robot moves from loc_x0_y3 to loc_x4_y1, robot moves from loc_x0_y0 to loc_x4_y0, moves from loc_x3_y0 to loc_x0_y4, robot moves from loc_x0_y1 to loc_x3_y3, robot moves from loc_x1_y0 to loc_x0_y0, robot moves from loc_x2_y2 to loc_x3_y4, moves from loc_x2_y0 to loc_x2_y1, from loc_x1_y0, the robot moves to loc_x3_y4, moves from loc_x4_y2 to loc_x0_y1, from loc_x0_y1, the robot moves to loc_x1_y3, moves from loc_x2_y3 to loc_x4_y2, robot moves from loc_x3_y2 to loc_x1_y3, robot moves from loc_x3_y0 to loc_x0_y0, from loc_x4_y2, the robot moves to loc_x4_y0, moves to loc_x0_y4 from loc_x3_y2, moves from loc_x1_y3 to loc_x4_y1, from loc_x0_y3, the robot moves to loc_x2_y1, moves to loc_x3_y4 from loc_x2_y1, moves to loc_x0_y0 from loc_x4_y1, robot moves from loc_x3_y0 to loc_x4_y0, robot moves from loc_x1_y4 to loc_x2_y1, moves from loc_x3_y4 to loc_x1_y4, robot moves from loc_x2_y1 to loc_x2_y3, moves from loc_x2_y2 to loc_x1_y4, robot moves from loc_x4_y1 to loc_x3_y3, robot moves from loc_x3_y2 to loc_x0_y0, moves to loc_x3_y3 from loc_x2_y3, robot moves from loc_x2_y1 to loc_x2_y4, robot moves from loc_x3_y3 to loc_x2_y3, robot moves from loc_x0_y0 to loc_x0_y4, robot moves from loc_x0_y0 to loc_x2_y4, robot moves from loc_x0_y0 to loc_x1_y3, from loc_x0_y1, the robot moves to loc_x4_y0, from loc_x1_y3, the robot moves to loc_x0_y3, moves from loc_x4_y0 to loc_x0_y3, robot moves from loc_x3_y2 to loc_x3_y3, moves to loc_x4_y1 from loc_x2_y2, moves from loc_x1_y0 to loc_x2_y1, robot moves from loc_x0_y3 to loc_x3_y0, moves to loc_x2_y0 from loc_x2_y4, from loc_x1_y3, the robot moves to loc_x3_y2, from loc_x2_y0, the robot moves to loc_x0_y1, robot moves from loc_x3_y3 to loc_x2_y4, robot moves from loc_x1_y0 to loc_x2_y4, moves from loc_x1_y4 to loc_x3_y0, from loc_x4_y1, the robot moves to loc_x1_y0, moves to loc_x4_y2 from loc_x0_y4, robot moves from loc_x1_y3 to loc_x0_y0, moves to loc_x2_y2 from loc_x4_y1, moves to loc_x3_y4 from loc_x1_y3, robot moves from loc_x3_y0 to loc_x3_y3, moves from loc_x1_y4 to loc_x0_y4, moves from loc_x4_y0 to loc_x1_y3, moves from loc_x1_y3 to loc_x2_y2, moves from loc_x3_y3 to loc_x4_y1, moves to loc_x2_y0 from loc_x4_y1, from loc_x1_y0, the robot moves to loc_x0_y3, from loc_x2_y2, the robot moves to loc_x1_y0, moves from loc_x2_y0 to loc_x2_y4, robot moves from loc_x2_y0 to loc_x1_y1, robot moves from loc_x4_y0 to loc_x2_y1, from loc_x3_y2, the robot moves to loc_x2_y4, moves to loc_x0_y3 from loc_x1_y4, robot moves from loc_x2_y0 to loc_x0_y0, from loc_x2_y1, the robot moves to loc_x1_y3, robot moves from loc_x2_y1 to loc_x3_y2, robot moves from loc_x4_y0 to loc_x3_y0, moves to loc_x2_y2 from loc_x3_y3, robot moves from loc_x4_y2 to loc_x3_y2, from loc_x0_y3, the robot moves to loc_x2_y4, robot moves from loc_x2_y1 to loc_x2_y0, moves from loc_x3_y3 to loc_x3_y1, from loc_x4_y1, the robot moves to loc_x4_y0, robot moves from loc_x0_y3 to loc_x1_y0, robot moves from loc_x1_y0 to loc_x0_y4, robot moves from loc_x1_y4 to loc_x1_y3, moves from loc_x3_y2 to loc_x1_y0, from loc_x0_y3, the robot moves to loc_x4_y0, from loc_x0_y0, the robot moves to loc_x3_y4, robot moves from loc_x2_y4 to loc_x0_y4, moves to loc_x2_y1 from loc_x1_y3, moves to loc_x4_y1 from loc_x3_y1, moves to loc_x1_y0 from loc_x4_y0, moves to loc_x3_y0 from loc_x3_y1, from loc_x0_y4, the robot moves to loc_x1_y0, moves to loc_x4_y2 from loc_x2_y4, robot moves from loc_x4_y2 to loc_x1_y1, moves to loc_x3_y2 from loc_x2_y4, moves from loc_x3_y2 to loc_x3_y4, moves from loc_x3_y2 to loc_x2_y3, robot moves from loc_x2_y3 to loc_x1_y3, moves from loc_x3_y4 to loc_x3_y0, from loc_x3_y4, the robot moves to loc_x4_y1, moves from loc_x3_y3 to loc_x1_y0, from loc_x0_y0, the robot moves to loc_x0_y3, robot moves from loc_x4_y2 to loc_x1_y0, robot moves from loc_x4_y1 to loc_x0_y4, moves from loc_x3_y1 to loc_x3_y3, moves to loc_x4_y2 from loc_x0_y0, robot moves from loc_x0_y1 to loc_x3_y0, robot moves from loc_x3_y3 to loc_x0_y1, robot moves from loc_x0_y4 to loc_x2_y0, from loc_x2_y2, the robot moves to loc_x2_y3, robot moves from loc_x3_y4 to loc_x0_y3, robot moves from loc_x0_y1 to loc_x0_y0, moves to loc_x1_y4 from loc_x2_y1, moves from loc_x0_y1 to loc_x2_y3, robot moves from loc_x3_y2 to loc_x1_y1, robot moves from loc_x2_y4 to loc_x4_y0, robot moves from loc_x2_y4 to loc_x1_y0, robot moves from loc_x2_y1 to loc_x3_y0, robot moves from loc_x2_y4 to loc_x1_y4, moves from loc_x4_y1 to loc_x2_y1, robot moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x2_y3 to loc_x3_y2, robot moves from loc_x2_y0 to loc_x3_y4, moves to loc_x4_y2 from loc_x4_y0, robot moves from loc_x3_y1 to loc_x1_y3, moves from loc_x2_y3 to loc_x0_y0, robot moves from loc_x3_y3 to loc_x0_y4, robot moves from loc_x1_y1 to loc_x3_y4, moves from loc_x1_y0 to loc_x3_y1, robot moves from loc_x3_y3 to loc_x4_y0, robot moves from loc_x2_y2 to loc_x4_y2, moves to loc_x1_y4 from loc_x0_y3, robot moves from loc_x3_y2 to loc_x0_y3, moves to loc_x2_y0 from loc_x3_y3, moves from loc_x0_y3 to loc_x0_y0, robot moves from loc_x3_y3 to loc_x3_y2, robot moves from loc_x2_y1 to loc_x0_y4, moves to loc_x3_y4 from loc_x4_y0, moves to loc_x1_y4 from loc_x3_y2, from loc_x2_y1, the robot moves to loc_x1_y1, from loc_x3_y4, the robot moves to loc_x1_y3, moves from loc_x2_y0 to loc_x3_y3, moves to loc_x4_y2 from loc_x1_y0, moves to loc_x0_y1 from loc_x2_y4, moves from loc_x3_y0 to loc_x0_y3, moves to loc_x3_y3 from loc_x2_y1, moves to loc_x1_y1 from loc_x4_y0, robot moves from loc_x2_y1 to loc_x1_y0, robot moves from loc_x4_y0 to loc_x3_y1, from loc_x4_y0, the robot moves to loc_x0_y0, robot moves from loc_x0_y4 to loc_x2_y2, moves from loc_x3_y3 to loc_x0_y0, robot moves from loc_x0_y4 to loc_x2_y3, robot moves from loc_x0_y3 to loc_x0_y4, moves to loc_x1_y4 from loc_x3_y0, from loc_x4_y0, the robot moves to loc_x4_y1, from loc_x2_y2, the robot moves to loc_x3_y2, robot moves from loc_x2_y0 to loc_x2_y2, robot moves from loc_x0_y1 to loc_x4_y2, robot moves from loc_x1_y4 to loc_x3_y4, robot moves from loc_x1_y1 to loc_x2_y0, moves to loc_x3_y0 from loc_x1_y1, moves to loc_x3_y0 from loc_x3_y3, robot moves from loc_x1_y1 to loc_x2_y1, robot moves from loc_x2_y4 to loc_x2_y3 and robot moves from loc_x3_y0 to loc_x2_y3", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following steps are taken: the robot transitions from loc_x0_y3 to loc_x0_y4 to attain the current state. In this state, identify all actions that cannot be executed. If there are no such actions, indicate None.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "5ccb52af-d0e8-434d-b471-48fbcef3387e", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y4 from loc_x0_y3, robot moves from loc_x0_y4 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x1_y3, from loc_x1_y3, the robot moves to loc_x1_y4, from loc_x1_y4, the robot moves to loc_x2_y4, robot moves from loc_x2_y4 to loc_x3_y4, moves from loc_x3_y4 to loc_x3_y3, from loc_x3_y3, the robot moves to loc_x2_y3, robot moves from loc_x2_y3 to loc_x2_y2 and robot moves from loc_x2_y2 to loc_x2_y1 to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "moves from loc_x3_y4 to loc_x3_y3, robot moves from loc_x0_y1 to loc_x2_y1, moves from loc_x2_y1 to loc_x0_y1, moves to loc_x1_y1 from loc_x2_y4, moves to loc_x0_y1 from loc_x1_y4, moves from loc_x0_y1 to loc_x3_y2, robot moves from loc_x1_y3 to loc_x2_y3, robot moves from loc_x0_y4 to loc_x3_y2, from loc_x2_y4, the robot moves to loc_x3_y4, moves from loc_x1_y1 to loc_x0_y4, robot moves from loc_x4_y2 to loc_x2_y2, moves to loc_x0_y1 from loc_x2_y2, robot moves from loc_x3_y3 to loc_x1_y4, moves to loc_x4_y0 from loc_x1_y0, robot moves from loc_x2_y0 to loc_x1_y3, robot moves from loc_x1_y4 to loc_x2_y2, robot moves from loc_x0_y3 to loc_x1_y3, moves to loc_x1_y1 from loc_x1_y4, robot moves from loc_x0_y1 to loc_x0_y4, from loc_x1_y1, the robot moves to loc_x0_y1, robot moves from loc_x3_y2 to loc_x3_y0, robot moves from loc_x1_y1 to loc_x1_y3, robot moves from loc_x3_y0 to loc_x0_y1, from loc_x3_y0, the robot moves to loc_x4_y1, from loc_x1_y4, the robot moves to loc_x4_y2, from loc_x2_y2, the robot moves to loc_x1_y1, robot moves from loc_x0_y1 to loc_x3_y4, moves from loc_x4_y0 to loc_x2_y4, moves from loc_x2_y3 to loc_x4_y0, robot moves from loc_x4_y0 to loc_x2_y2, moves from loc_x2_y3 to loc_x2_y0, robot moves from loc_x2_y4 to loc_x3_y0, robot moves from loc_x3_y3 to loc_x0_y3, robot moves from loc_x0_y0 to loc_x2_y2, robot moves from loc_x3_y4 to loc_x0_y4, robot moves from loc_x0_y0 to loc_x4_y1, robot moves from loc_x1_y0 to loc_x2_y3, robot moves from loc_x2_y2 to loc_x2_y4, moves from loc_x2_y3 to loc_x0_y3, moves to loc_x1_y4 from loc_x4_y0, robot moves from loc_x1_y3 to loc_x3_y1, moves to loc_x2_y1 from loc_x2_y2, moves from loc_x4_y1 to loc_x4_y2, moves to loc_x2_y0 from loc_x4_y0, from loc_x0_y0, the robot moves to loc_x0_y1, moves to loc_x1_y0 from loc_x0_y1, moves to loc_x1_y4 from loc_x4_y1, moves to loc_x4_y1 from loc_x1_y0, moves to loc_x3_y3 from loc_x0_y0, from loc_x1_y1, the robot moves to loc_x1_y0, robot moves from loc_x4_y1 to loc_x0_y3, robot moves from loc_x3_y2 to loc_x0_y1, moves to loc_x3_y3 from loc_x0_y4, moves from loc_x4_y1 to loc_x1_y3, moves to loc_x4_y1 from loc_x2_y0, from loc_x0_y1, the robot moves to loc_x1_y1, robot moves from loc_x4_y0 to loc_x2_y3, robot moves from loc_x2_y2 to loc_x0_y3, moves to loc_x0_y1 from loc_x3_y1, moves to loc_x0_y1 from loc_x4_y1, moves to loc_x4_y2 from loc_x3_y0, moves to loc_x3_y4 from loc_x4_y2, from loc_x1_y0, the robot moves to loc_x1_y4, moves to loc_x1_y4 from loc_x0_y0, moves to loc_x3_y3 from loc_x1_y0, from loc_x4_y2, the robot moves to loc_x3_y1, robot moves from loc_x3_y3 to loc_x2_y1, moves from loc_x3_y1 to loc_x0_y4, from loc_x4_y2, the robot moves to loc_x0_y0, robot moves from loc_x2_y4 to loc_x2_y2, moves from loc_x0_y3 to loc_x3_y1, robot moves from loc_x0_y3 to loc_x3_y4, robot moves from loc_x2_y3 to loc_x3_y0, moves from loc_x2_y2 to loc_x2_y0, robot moves from loc_x3_y1 to loc_x2_y2, moves from loc_x2_y3 to loc_x1_y0, moves from loc_x4_y1 to loc_x2_y4, moves to loc_x4_y2 from loc_x3_y1, robot moves from loc_x2_y0 to loc_x0_y3, from loc_x1_y1, the robot moves to loc_x1_y4, robot moves from loc_x2_y4 to loc_x3_y3, moves to loc_x0_y4 from loc_x2_y0, moves to loc_x4_y1 from loc_x3_y2, moves to loc_x3_y3 from loc_x1_y4, moves to loc_x2_y4 from loc_x3_y0, robot moves from loc_x1_y3 to loc_x3_y0, from loc_x1_y3, the robot moves to loc_x1_y1, robot moves from loc_x3_y1 to loc_x2_y3, moves from loc_x3_y4 to loc_x0_y0, from loc_x3_y2, the robot moves to loc_x2_y0, robot moves from loc_x1_y3 to loc_x1_y0, robot moves from loc_x1_y1 to loc_x2_y4, from loc_x4_y2, the robot moves to loc_x2_y0, moves from loc_x0_y1 to loc_x2_y0, moves to loc_x0_y1 from loc_x0_y3, from loc_x0_y4, the robot moves to loc_x0_y1, robot moves from loc_x0_y1 to loc_x1_y4, robot moves from loc_x2_y3 to loc_x1_y1, moves from loc_x0_y4 to loc_x4_y1, moves from loc_x4_y0 to loc_x0_y4, moves to loc_x3_y0 from loc_x0_y0, from loc_x2_y0, the robot moves to loc_x1_y0, moves from loc_x0_y1 to loc_x3_y1, from loc_x4_y1, the robot moves to loc_x3_y4, robot moves from loc_x2_y2 to loc_x3_y1, from loc_x4_y1, the robot moves to loc_x1_y1, moves to loc_x4_y1 from loc_x1_y4, moves to loc_x1_y0 from loc_x3_y1, moves from loc_x1_y3 to loc_x4_y0, robot moves from loc_x2_y3 to loc_x0_y1, from loc_x3_y1, the robot moves to loc_x2_y0, moves to loc_x2_y4 from loc_x0_y4, moves to loc_x2_y2 from loc_x1_y0, from loc_x2_y1, the robot moves to loc_x4_y2, moves from loc_x1_y4 to loc_x3_y1, robot moves from loc_x0_y4 to loc_x4_y0, robot moves from loc_x1_y4 to loc_x2_y3, moves from loc_x1_y1 to loc_x0_y3, robot moves from loc_x4_y2 to loc_x2_y4, robot moves from loc_x2_y0 to loc_x3_y1, robot moves from loc_x4_y2 to loc_x0_y3, from loc_x0_y3, the robot moves to loc_x3_y3, robot moves from loc_x0_y4 to loc_x1_y4, moves to loc_x0_y3 from loc_x2_y4, from loc_x4_y1, the robot moves to loc_x2_y3, moves to loc_x0_y3 from loc_x3_y1, from loc_x3_y4, the robot moves to loc_x2_y2, moves from loc_x0_y0 to loc_x3_y2, robot moves from loc_x4_y2 to loc_x1_y3, from loc_x4_y0, the robot moves to loc_x3_y3, from loc_x0_y3, the robot moves to loc_x1_y1, robot moves from loc_x3_y1 to loc_x1_y4, from loc_x1_y1, the robot moves to loc_x3_y2, robot moves from loc_x0_y0 to loc_x3_y1, robot moves from loc_x3_y4 to loc_x0_y1, robot moves from loc_x3_y4 to loc_x2_y1, moves to loc_x3_y0 from loc_x4_y2, moves to loc_x1_y1 from loc_x1_y0, moves from loc_x1_y0 to loc_x0_y1, robot moves from loc_x2_y3 to loc_x2_y1, robot moves from loc_x3_y0 to loc_x1_y0, from loc_x0_y4, the robot moves to loc_x1_y1, robot moves from loc_x2_y1 to loc_x4_y0, robot moves from loc_x2_y4 to loc_x1_y3, robot moves from loc_x1_y1 to loc_x3_y1, robot moves from loc_x2_y2 to loc_x1_y3, moves to loc_x4_y0 from loc_x1_y4, moves from loc_x3_y4 to loc_x2_y0, robot moves from loc_x2_y3 to loc_x0_y4, robot moves from loc_x3_y4 to loc_x4_y2, from loc_x3_y0, the robot moves to loc_x3_y1, moves from loc_x1_y3 to loc_x2_y4, robot moves from loc_x2_y3 to loc_x2_y4, moves from loc_x0_y0 to loc_x1_y1, robot moves from loc_x2_y2 to loc_x0_y4, robot moves from loc_x3_y4 to loc_x1_y1, robot moves from loc_x2_y0 to loc_x4_y2, moves to loc_x2_y0 from loc_x1_y0, robot moves from loc_x3_y0 to loc_x2_y1, moves to loc_x3_y1 from loc_x3_y4, robot moves from loc_x3_y4 to loc_x1_y0, from loc_x1_y0, the robot moves to loc_x3_y2, moves from loc_x1_y3 to loc_x1_y4, robot moves from loc_x3_y2 to loc_x2_y1, moves from loc_x1_y3 to loc_x0_y4, robot moves from loc_x2_y3 to loc_x4_y1, from loc_x1_y4, the robot moves to loc_x2_y4, moves from loc_x2_y3 to loc_x2_y2, from loc_x3_y1, the robot moves to loc_x3_y4, from loc_x0_y4, the robot moves to loc_x2_y1, moves from loc_x3_y1 to loc_x1_y1, robot moves from loc_x1_y4 to loc_x0_y0, moves from loc_x0_y1 to loc_x4_y1, robot moves from loc_x1_y3 to loc_x0_y1, moves to loc_x4_y1 from loc_x2_y4, from loc_x0_y3, the robot moves to loc_x4_y2, robot moves from loc_x2_y1 to loc_x4_y1, robot moves from loc_x0_y1 to loc_x0_y3, moves from loc_x4_y2 to loc_x2_y1, from loc_x4_y2, the robot moves to loc_x3_y3, robot moves from loc_x1_y4 to loc_x1_y0, moves to loc_x2_y1 from loc_x3_y1, robot moves from loc_x1_y1 to loc_x2_y2, robot moves from loc_x2_y2 to loc_x4_y0, robot moves from loc_x4_y0 to loc_x0_y1, moves to loc_x3_y1 from loc_x0_y4, robot moves from loc_x0_y4 to loc_x3_y0, moves from loc_x1_y4 to loc_x2_y0, moves from loc_x3_y2 to loc_x4_y2, moves to loc_x3_y2 from loc_x3_y0, robot moves from loc_x3_y4 to loc_x2_y4, moves to loc_x2_y1 from loc_x0_y0, robot moves from loc_x0_y3 to loc_x2_y3, robot moves from loc_x2_y3 to loc_x3_y4, robot moves from loc_x2_y1 to loc_x0_y3, from loc_x2_y0, the robot moves to loc_x3_y2, moves from loc_x4_y2 to loc_x4_y1, moves from loc_x2_y2 to loc_x3_y3, moves to loc_x4_y0 from loc_x1_y1, from loc_x2_y3, the robot moves to loc_x3_y1, moves to loc_x2_y0 from loc_x3_y0, robot moves from loc_x1_y4 to loc_x3_y2, robot moves from loc_x2_y2 to loc_x3_y0, robot moves from loc_x1_y0 to loc_x1_y3, robot moves from loc_x4_y2 to loc_x1_y4, from loc_x0_y0, the robot moves to loc_x2_y3, robot moves from loc_x1_y1 to loc_x2_y3, robot moves from loc_x0_y3 to loc_x3_y2, from loc_x0_y0, the robot moves to loc_x1_y0, from loc_x2_y4, the robot moves to loc_x2_y1, moves from loc_x4_y1 to loc_x3_y0, from loc_x3_y0, the robot moves to loc_x1_y1, moves to loc_x2_y3 from loc_x2_y0, moves from loc_x2_y4 to loc_x3_y1, moves from loc_x0_y4 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x2_y0, robot moves from loc_x4_y2 to loc_x2_y3, moves from loc_x3_y3 to loc_x1_y3, moves from loc_x1_y1 to loc_x3_y3, from loc_x1_y3, the robot moves to loc_x2_y0, robot moves from loc_x3_y3 to loc_x1_y1, robot moves from loc_x3_y3 to loc_x4_y2, moves from loc_x0_y1 to loc_x2_y4, robot moves from loc_x2_y2 to loc_x0_y0, robot moves from loc_x3_y0 to loc_x1_y3, moves from loc_x4_y1 to loc_x3_y1, moves from loc_x4_y1 to loc_x3_y2, from loc_x1_y1, the robot moves to loc_x4_y1, robot moves from loc_x4_y0 to loc_x3_y2, robot moves from loc_x0_y4 to loc_x3_y4, from loc_x3_y2, the robot moves to loc_x3_y1, moves from loc_x1_y0 to loc_x3_y0, moves to loc_x4_y0 from loc_x3_y2, robot moves from loc_x1_y1 to loc_x4_y2, robot moves from loc_x3_y1 to loc_x4_y0, moves from loc_x3_y4 to loc_x4_y0, robot moves from loc_x2_y0 to loc_x1_y4, robot moves from loc_x4_y2 to loc_x0_y4, robot moves from loc_x3_y3 to loc_x3_y4, moves from loc_x2_y0 to loc_x4_y0, robot moves from loc_x2_y1 to loc_x0_y0, moves from loc_x0_y3 to loc_x2_y2, robot moves from loc_x0_y4 to loc_x0_y0, from loc_x0_y1, the robot moves to loc_x2_y2, moves from loc_x2_y4 to loc_x0_y0, robot moves from loc_x3_y1 to loc_x2_y4, robot moves from loc_x3_y4 to loc_x3_y2, moves to loc_x1_y3 from loc_x0_y4, moves from loc_x3_y1 to loc_x0_y0, from loc_x3_y2, the robot moves to loc_x2_y2, from loc_x3_y1, the robot moves to loc_x3_y2, robot moves from loc_x2_y3 to loc_x1_y4, moves from loc_x3_y0 to loc_x2_y2, robot moves from loc_x0_y0 to loc_x2_y0, from loc_x3_y0, the robot moves to loc_x3_y4, moves from loc_x1_y3 to loc_x3_y3, robot moves from loc_x1_y1 to loc_x0_y0, robot moves from loc_x1_y3 to loc_x4_y2, from loc_x3_y4, the robot moves to loc_x2_y3, from loc_x0_y3, the robot moves to loc_x4_y1, robot moves from loc_x0_y0 to loc_x4_y0, moves from loc_x3_y0 to loc_x0_y4, robot moves from loc_x0_y1 to loc_x3_y3, robot moves from loc_x1_y0 to loc_x0_y0, moves to loc_x3_y4 from loc_x2_y2, moves from loc_x2_y0 to loc_x2_y1, from loc_x1_y0, the robot moves to loc_x3_y4, robot moves from loc_x4_y2 to loc_x0_y1, robot moves from loc_x0_y1 to loc_x1_y3, moves to loc_x4_y2 from loc_x2_y3, robot moves from loc_x3_y2 to loc_x1_y3, robot moves from loc_x3_y0 to loc_x0_y0, robot moves from loc_x4_y2 to loc_x4_y0, moves from loc_x3_y2 to loc_x0_y4, moves from loc_x1_y3 to loc_x4_y1, moves to loc_x2_y1 from loc_x0_y3, robot moves from loc_x2_y1 to loc_x3_y4, from loc_x4_y1, the robot moves to loc_x0_y0, robot moves from loc_x3_y0 to loc_x4_y0, moves from loc_x1_y4 to loc_x2_y1, robot moves from loc_x3_y4 to loc_x1_y4, moves from loc_x2_y1 to loc_x2_y3, moves to loc_x1_y4 from loc_x2_y2, from loc_x4_y1, the robot moves to loc_x3_y3, robot moves from loc_x3_y2 to loc_x0_y0, from loc_x2_y3, the robot moves to loc_x3_y3, moves from loc_x2_y1 to loc_x2_y4, moves to loc_x2_y3 from loc_x3_y3, from loc_x0_y0, the robot moves to loc_x0_y4, robot moves from loc_x0_y0 to loc_x2_y4, moves from loc_x0_y0 to loc_x1_y3, robot moves from loc_x0_y1 to loc_x4_y0, moves from loc_x1_y3 to loc_x0_y3, robot moves from loc_x4_y0 to loc_x0_y3, from loc_x3_y2, the robot moves to loc_x3_y3, robot moves from loc_x2_y2 to loc_x4_y1, robot moves from loc_x1_y0 to loc_x2_y1, robot moves from loc_x0_y3 to loc_x3_y0, from loc_x2_y4, the robot moves to loc_x2_y0, moves to loc_x3_y2 from loc_x1_y3, robot moves from loc_x2_y0 to loc_x0_y1, from loc_x3_y3, the robot moves to loc_x2_y4, moves from loc_x1_y0 to loc_x2_y4, moves to loc_x3_y0 from loc_x1_y4, robot moves from loc_x4_y1 to loc_x1_y0, moves to loc_x4_y2 from loc_x0_y4, from loc_x1_y3, the robot moves to loc_x0_y0, moves to loc_x2_y2 from loc_x4_y1, robot moves from loc_x1_y3 to loc_x3_y4, robot moves from loc_x3_y0 to loc_x3_y3, moves to loc_x0_y4 from loc_x1_y4, robot moves from loc_x4_y0 to loc_x1_y3, from loc_x1_y3, the robot moves to loc_x2_y2, from loc_x3_y3, the robot moves to loc_x4_y1, robot moves from loc_x4_y1 to loc_x2_y0, robot moves from loc_x1_y0 to loc_x0_y3, from loc_x2_y2, the robot moves to loc_x1_y0, from loc_x2_y0, the robot moves to loc_x2_y4, moves to loc_x1_y1 from loc_x2_y0, robot moves from loc_x4_y0 to loc_x2_y1, robot moves from loc_x3_y2 to loc_x2_y4, robot moves from loc_x1_y4 to loc_x0_y3, robot moves from loc_x2_y0 to loc_x0_y0, robot moves from loc_x2_y1 to loc_x1_y3, moves to loc_x3_y2 from loc_x2_y1, robot moves from loc_x4_y0 to loc_x3_y0, robot moves from loc_x3_y3 to loc_x2_y2, moves from loc_x4_y2 to loc_x3_y2, moves from loc_x0_y3 to loc_x2_y4, from loc_x3_y3, the robot moves to loc_x3_y1, robot moves from loc_x4_y1 to loc_x4_y0, from loc_x0_y3, the robot moves to loc_x1_y0, from loc_x1_y0, the robot moves to loc_x0_y4, moves to loc_x1_y3 from loc_x1_y4, robot moves from loc_x3_y2 to loc_x1_y0, moves to loc_x4_y0 from loc_x0_y3, moves from loc_x0_y0 to loc_x3_y4, robot moves from loc_x2_y4 to loc_x0_y4, moves to loc_x2_y1 from loc_x1_y3, moves to loc_x4_y1 from loc_x3_y1, robot moves from loc_x4_y0 to loc_x1_y0, robot moves from loc_x3_y1 to loc_x3_y0, robot moves from loc_x0_y4 to loc_x1_y0, robot moves from loc_x2_y4 to loc_x4_y2, robot moves from loc_x4_y2 to loc_x1_y1, from loc_x2_y4, the robot moves to loc_x3_y2, robot moves from loc_x3_y2 to loc_x3_y4, robot moves from loc_x3_y2 to loc_x2_y3, robot moves from loc_x2_y3 to loc_x1_y3, robot moves from loc_x3_y4 to loc_x3_y0, robot moves from loc_x3_y4 to loc_x4_y1, robot moves from loc_x3_y3 to loc_x1_y0, moves to loc_x0_y3 from loc_x0_y0, robot moves from loc_x4_y2 to loc_x1_y0, robot moves from loc_x4_y1 to loc_x0_y4, robot moves from loc_x3_y1 to loc_x3_y3, from loc_x0_y0, the robot moves to loc_x4_y2, moves to loc_x3_y0 from loc_x0_y1, robot moves from loc_x3_y3 to loc_x0_y1, from loc_x0_y4, the robot moves to loc_x2_y0, robot moves from loc_x2_y2 to loc_x2_y3, robot moves from loc_x3_y4 to loc_x0_y3, robot moves from loc_x0_y1 to loc_x0_y0, moves to loc_x1_y4 from loc_x2_y1, robot moves from loc_x0_y1 to loc_x2_y3, moves from loc_x3_y2 to loc_x1_y1, moves to loc_x4_y0 from loc_x2_y4, robot moves from loc_x2_y4 to loc_x1_y0, robot moves from loc_x2_y1 to loc_x3_y0, moves from loc_x2_y4 to loc_x1_y4, from loc_x4_y1, the robot moves to loc_x2_y1, from loc_x2_y0, the robot moves to loc_x3_y0, moves from loc_x2_y3 to loc_x3_y2, moves to loc_x3_y4 from loc_x2_y0, moves to loc_x4_y2 from loc_x4_y0, from loc_x3_y1, the robot moves to loc_x1_y3, from loc_x2_y3, the robot moves to loc_x0_y0, moves from loc_x3_y3 to loc_x0_y4, from loc_x1_y1, the robot moves to loc_x3_y4, moves to loc_x3_y1 from loc_x1_y0, from loc_x3_y3, the robot moves to loc_x4_y0, from loc_x2_y2, the robot moves to loc_x4_y2, robot moves from loc_x0_y3 to loc_x1_y4, moves from loc_x3_y2 to loc_x0_y3, from loc_x3_y3, the robot moves to loc_x2_y0, robot moves from loc_x0_y3 to loc_x0_y0, robot moves from loc_x3_y3 to loc_x3_y2, moves from loc_x2_y1 to loc_x0_y4, robot moves from loc_x4_y0 to loc_x3_y4, moves to loc_x1_y4 from loc_x3_y2, robot moves from loc_x3_y4 to loc_x1_y3, robot moves from loc_x2_y0 to loc_x3_y3, from loc_x1_y0, the robot moves to loc_x4_y2, from loc_x2_y4, the robot moves to loc_x0_y1, moves to loc_x0_y3 from loc_x3_y0, from loc_x2_y1, the robot moves to loc_x3_y3, from loc_x4_y0, the robot moves to loc_x1_y1, from loc_x2_y1, the robot moves to loc_x1_y0, from loc_x4_y0, the robot moves to loc_x3_y1, robot moves from loc_x4_y0 to loc_x0_y0, moves to loc_x2_y2 from loc_x0_y4, moves from loc_x3_y3 to loc_x0_y0, robot moves from loc_x0_y4 to loc_x2_y3, robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x3_y0 to loc_x1_y4, robot moves from loc_x4_y0 to loc_x4_y1, robot moves from loc_x2_y2 to loc_x3_y2, robot moves from loc_x2_y0 to loc_x2_y2, robot moves from loc_x0_y1 to loc_x4_y2, moves to loc_x3_y4 from loc_x1_y4, robot moves from loc_x1_y1 to loc_x2_y0, robot moves from loc_x1_y1 to loc_x3_y0, moves to loc_x3_y0 from loc_x3_y3, robot moves from loc_x1_y1 to loc_x2_y1, robot moves from loc_x2_y4 to loc_x2_y3 and from loc_x3_y0, the robot moves to loc_x2_y3", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot executes the following sequence of actions: it moves from loc_x0_y3 to loc_x0_y4, then back to loc_x0_y3, followed by a move to loc_x1_y3, then to loc_x1_y4, then to loc_x2_y4, then to loc_x3_y4, then to loc_x3_y3, then to loc_x2_y3, then to loc_x2_y2, and finally to loc_x2_y1 to reach the current state. In this state, list all actions that cannot be executed. Write None if there are none.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "a7a781a3-2b65-415a-b6e2-bba34b55f306", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "question_5", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from loc_x0_y2, the robot moves to loc_x0_y1 to reach the current state. In this state, list all inexecutable actions. Write None if there are none.", "answer": "moves to loc_x0_y4 from loc_x0_y5, moves from loc_x3_y2 to loc_x3_y1, moves to loc_x2_y5 from loc_x2_y4, robot moves from loc_x1_y2 to loc_x1_y4, robot moves from loc_x3_y3 to loc_x2_y3, from loc_x0_y0, the robot moves to loc_x0_y1, moves from loc_x1_y5 to loc_x3_y0, robot moves from loc_x0_y2 to loc_x2_y0, moves to loc_x2_y2 from loc_x2_y1, robot moves from loc_x2_y3 to loc_x2_y1, robot moves from loc_x0_y4 to loc_x1_y0, robot moves from loc_x1_y0 to loc_x0_y4, robot moves from loc_x3_y1 to loc_x0_y4, robot moves from loc_x0_y5 to loc_x1_y3, from loc_x3_y0, the robot moves to loc_x0_y5, robot moves from loc_x2_y1 to loc_x3_y4, robot moves from loc_x2_y5 to loc_x1_y4, robot moves from loc_x0_y0 to loc_x1_y1, from loc_x1_y2, the robot moves to loc_x2_y5, from loc_x0_y2, the robot moves to loc_x3_y1, from loc_x0_y3, the robot moves to loc_x0_y0, robot moves from loc_x2_y2 to loc_x0_y3, robot moves from loc_x1_y0 to loc_x1_y5, moves to loc_x1_y2 from loc_x3_y4, from loc_x2_y0, the robot moves to loc_x2_y1, moves to loc_x3_y0 from loc_x0_y5, robot moves from loc_x3_y0 to loc_x1_y5, robot moves from loc_x2_y3 to loc_x1_y5, moves from loc_x0_y5 to loc_x2_y5, moves from loc_x0_y0 to loc_x2_y0, moves to loc_x1_y1 from loc_x1_y0, moves from loc_x1_y0 to loc_x2_y2, from loc_x3_y2, the robot moves to loc_x1_y2, from loc_x1_y2, the robot moves to loc_x0_y4, robot moves from loc_x2_y1 to loc_x0_y0, from loc_x1_y2, the robot moves to loc_x2_y1, moves to loc_x3_y4 from loc_x0_y3, moves to loc_x1_y4 from loc_x1_y1, moves from loc_x1_y1 to loc_x2_y4, robot moves from loc_x3_y1 to loc_x2_y2, from loc_x1_y2, the robot moves to loc_x0_y1, moves to loc_x2_y5 from loc_x1_y0, moves to loc_x1_y0 from loc_x0_y5, from loc_x2_y4, the robot moves to loc_x0_y4, moves to loc_x1_y3 from loc_x1_y1, robot moves from loc_x3_y4 to loc_x2_y4, robot moves from loc_x0_y5 to loc_x1_y4, robot moves from loc_x1_y3 to loc_x1_y1, moves to loc_x1_y2 from loc_x3_y3, robot moves from loc_x2_y0 to loc_x1_y5, robot moves from loc_x3_y4 to loc_x0_y2, robot moves from loc_x2_y5 to loc_x1_y5, robot moves from loc_x1_y2 to loc_x3_y0, robot moves from loc_x0_y4 to loc_x0_y2, robot moves from loc_x1_y4 to loc_x3_y2, robot moves from loc_x3_y3 to loc_x2_y2, moves to loc_x3_y3 from loc_x3_y0, from loc_x0_y4, the robot moves to loc_x2_y3, robot moves from loc_x3_y2 to loc_x2_y1, moves from loc_x3_y1 to loc_x1_y1, moves from loc_x2_y3 to loc_x0_y3, robot moves from loc_x1_y3 to loc_x3_y0, moves from loc_x3_y2 to loc_x0_y2, moves to loc_x0_y5 from loc_x0_y1, moves to loc_x3_y4 from loc_x2_y4, moves from loc_x1_y0 to loc_x3_y3, robot moves from loc_x2_y0 to loc_x0_y4, moves from loc_x2_y2 to loc_x3_y3, robot moves from loc_x1_y3 to loc_x3_y4, moves from loc_x3_y3 to loc_x2_y1, moves from loc_x0_y2 to loc_x0_y1, from loc_x1_y5, the robot moves to loc_x0_y5, robot moves from loc_x3_y3 to loc_x1_y4, robot moves from loc_x2_y1 to loc_x0_y3, from loc_x0_y1, the robot moves to loc_x0_y3, robot moves from loc_x2_y0 to loc_x3_y4, from loc_x1_y5, the robot moves to loc_x2_y5, moves to loc_x3_y3 from loc_x2_y0, from loc_x2_y0, the robot moves to loc_x0_y5, from loc_x2_y1, the robot moves to loc_x1_y2, moves from loc_x2_y4 to loc_x2_y2, robot moves from loc_x0_y5 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x2_y3, from loc_x2_y3, the robot moves to loc_x0_y4, robot moves from loc_x2_y1 to loc_x1_y5, moves to loc_x2_y1 from loc_x2_y4, moves from loc_x1_y0 to loc_x2_y1, robot moves from loc_x2_y3 to loc_x1_y0, moves to loc_x1_y2 from loc_x1_y1, moves to loc_x2_y4 from loc_x0_y3, moves to loc_x0_y1 from loc_x1_y4, robot moves from loc_x3_y1 to loc_x0_y1, from loc_x1_y1, the robot moves to loc_x0_y4, moves from loc_x2_y4 to loc_x1_y0, robot moves from loc_x1_y5 to loc_x1_y1, robot moves from loc_x0_y3 to loc_x1_y5, robot moves from loc_x0_y3 to loc_x3_y2, moves to loc_x3_y4 from loc_x2_y2, moves to loc_x3_y2 from loc_x1_y3, robot moves from loc_x0_y5 to loc_x3_y3, robot moves from loc_x3_y4 to loc_x2_y2, moves from loc_x2_y5 to loc_x3_y3, moves to loc_x2_y1 from loc_x0_y0, from loc_x3_y2, the robot moves to loc_x1_y1, moves from loc_x0_y1 to loc_x0_y4, robot moves from loc_x0_y2 to loc_x2_y5, robot moves from loc_x2_y2 to loc_x3_y0, from loc_x2_y1, the robot moves to loc_x2_y4, moves from loc_x2_y2 to loc_x1_y2, robot moves from loc_x3_y2 to loc_x2_y4, from loc_x2_y3, the robot moves to loc_x1_y3, from loc_x3_y2, the robot moves to loc_x0_y5, moves to loc_x0_y2 from loc_x1_y1, moves to loc_x3_y1 from loc_x2_y1, moves to loc_x2_y3 from loc_x1_y4, robot moves from loc_x1_y2 to loc_x2_y0, moves from loc_x3_y3 to loc_x0_y0, robot moves from loc_x3_y1 to loc_x2_y4, robot moves from loc_x0_y3 to loc_x2_y2, from loc_x3_y2, the robot moves to loc_x2_y3, robot moves from loc_x2_y1 to loc_x0_y1, robot moves from loc_x2_y2 to loc_x2_y5, moves to loc_x1_y1 from loc_x3_y3, robot moves from loc_x3_y0 to loc_x1_y4, moves to loc_x2_y4 from loc_x0_y0, from loc_x2_y5, the robot moves to loc_x3_y2, robot moves from loc_x2_y5 to loc_x2_y4, robot moves from loc_x1_y2 to loc_x2_y4, moves to loc_x2_y2 from loc_x0_y5, robot moves from loc_x3_y4 to loc_x0_y5, moves from loc_x2_y5 to loc_x3_y0, robot moves from loc_x0_y3 to loc_x2_y0, from loc_x2_y4, the robot moves to loc_x1_y2, moves from loc_x0_y4 to loc_x3_y0, from loc_x0_y3, the robot moves to loc_x1_y1, moves from loc_x1_y0 to loc_x1_y2, moves to loc_x2_y3 from loc_x0_y1, moves to loc_x3_y3 from loc_x0_y1, from loc_x0_y4, the robot moves to loc_x2_y1, from loc_x3_y3, the robot moves to loc_x0_y2, from loc_x2_y5, the robot moves to loc_x2_y0, robot moves from loc_x2_y3 to loc_x2_y0, moves from loc_x2_y4 to loc_x1_y5, moves from loc_x3_y3 to loc_x3_y4, robot moves from loc_x2_y0 to loc_x0_y0, robot moves from loc_x0_y4 to loc_x1_y1, robot moves from loc_x3_y0 to loc_x1_y2, from loc_x1_y0, the robot moves to loc_x0_y2, moves to loc_x3_y2 from loc_x0_y1, from loc_x1_y1, the robot moves to loc_x3_y0, robot moves from loc_x1_y5 to loc_x2_y4, moves from loc_x2_y1 to loc_x2_y0, moves from loc_x1_y4 to loc_x1_y2, robot moves from loc_x0_y0 to loc_x1_y0, from loc_x3_y1, the robot moves to loc_x0_y3, moves from loc_x2_y3 to loc_x2_y4, from loc_x3_y2, the robot moves to loc_x3_y3, robot moves from loc_x0_y5 to loc_x2_y1, robot moves from loc_x3_y4 to loc_x3_y3, moves from loc_x2_y2 to loc_x0_y5, moves to loc_x1_y2 from loc_x3_y1, robot moves from loc_x0_y2 to loc_x3_y0, moves to loc_x0_y2 from loc_x1_y3, moves from loc_x1_y3 to loc_x2_y0, moves to loc_x0_y1 from loc_x2_y2, from loc_x0_y2, the robot moves to loc_x1_y4, robot moves from loc_x3_y0 to loc_x2_y2, moves from loc_x0_y2 to loc_x0_y4, moves to loc_x1_y3 from loc_x2_y0, from loc_x3_y0, the robot moves to loc_x1_y3, moves from loc_x2_y2 to loc_x2_y3, robot moves from loc_x2_y5 to loc_x0_y2, moves to loc_x2_y4 from loc_x0_y4, from loc_x1_y0, the robot moves to loc_x2_y0, robot moves from loc_x2_y4 to loc_x1_y1, moves from loc_x3_y4 to loc_x1_y1, moves to loc_x0_y1 from loc_x2_y4, robot moves from loc_x1_y4 to loc_x2_y4, moves to loc_x1_y4 from loc_x3_y4, robot moves from loc_x3_y4 to loc_x1_y5, robot moves from loc_x3_y4 to loc_x1_y3, robot moves from loc_x2_y2 to loc_x2_y1, robot moves from loc_x3_y4 to loc_x1_y0, from loc_x1_y1, the robot moves to loc_x2_y0, robot moves from loc_x0_y2 to loc_x1_y2, robot moves from loc_x1_y4 to loc_x2_y0, from loc_x1_y3, the robot moves to loc_x0_y3, moves from loc_x0_y3 to loc_x0_y1, moves from loc_x2_y1 to loc_x1_y4, robot moves from loc_x1_y3 to loc_x0_y4, from loc_x2_y5, the robot moves to loc_x0_y5, robot moves from loc_x3_y0 to loc_x1_y0, moves to loc_x3_y2 from loc_x1_y5, moves from loc_x2_y3 to loc_x1_y1, moves from loc_x0_y1 to loc_x3_y4, from loc_x2_y5, the robot moves to loc_x1_y1, robot moves from loc_x0_y3 to loc_x0_y2, from loc_x1_y0, the robot moves to loc_x3_y1, robot moves from loc_x1_y5 to loc_x3_y4, robot moves from loc_x1_y3 to loc_x2_y4, moves from loc_x3_y1 to loc_x3_y2, from loc_x1_y2, the robot moves to loc_x2_y2, robot moves from loc_x0_y2 to loc_x2_y1, robot moves from loc_x2_y4 to loc_x3_y0, moves from loc_x0_y0 to loc_x3_y1, robot moves from loc_x1_y1 to loc_x0_y1, moves from loc_x1_y5 to loc_x1_y2, moves from loc_x3_y2 to loc_x1_y0, moves from loc_x0_y3 to loc_x2_y1, from loc_x2_y3, the robot moves to loc_x3_y1, robot moves from loc_x3_y1 to loc_x3_y3, moves from loc_x0_y2 to loc_x0_y3, moves from loc_x2_y4 to loc_x1_y3, robot moves from loc_x1_y2 to loc_x3_y4, robot moves from loc_x2_y1 to loc_x1_y1, moves from loc_x0_y3 to loc_x1_y4, moves from loc_x2_y5 to loc_x0_y4, robot moves from loc_x2_y5 to loc_x0_y0, robot moves from loc_x2_y2 to loc_x1_y4, robot moves from loc_x1_y0 to loc_x0_y5, robot moves from loc_x3_y4 to loc_x2_y5, moves to loc_x2_y2 from loc_x0_y0, robot moves from loc_x2_y1 to loc_x1_y3, moves from loc_x3_y1 to loc_x3_y0, from loc_x2_y3, the robot moves to loc_x0_y2, from loc_x0_y5, the robot moves to loc_x2_y4, moves from loc_x1_y4 to loc_x2_y2, moves from loc_x0_y5 to loc_x0_y0, moves to loc_x1_y0 from loc_x1_y3, moves to loc_x3_y2 from loc_x2_y4, robot moves from loc_x0_y4 to loc_x0_y3, robot moves from loc_x3_y3 to loc_x0_y5, moves to loc_x0_y3 from loc_x1_y4, moves from loc_x0_y0 to loc_x0_y4, from loc_x3_y0, the robot moves to loc_x3_y2, robot moves from loc_x0_y5 to loc_x1_y2, moves to loc_x1_y0 from loc_x2_y0, from loc_x0_y4, the robot moves to loc_x0_y5, moves to loc_x2_y2 from loc_x1_y5, moves from loc_x1_y3 to loc_x3_y3, moves from loc_x1_y1 to loc_x2_y5, from loc_x2_y3, the robot moves to loc_x2_y5, moves from loc_x2_y1 to loc_x0_y2, moves from loc_x2_y1 to loc_x2_y3, from loc_x1_y3, the robot moves to loc_x0_y1, robot moves from loc_x0_y4 to loc_x3_y1, robot moves from loc_x0_y4 to loc_x3_y3, robot moves from loc_x3_y0 to loc_x2_y4, moves to loc_x2_y3 from loc_x0_y5, robot moves from loc_x0_y1 to loc_x2_y4, robot moves from loc_x1_y0 to loc_x1_y4, from loc_x1_y5, the robot moves to loc_x2_y0, from loc_x1_y4, the robot moves to loc_x2_y1, robot moves from loc_x1_y3 to loc_x1_y2, robot moves from loc_x0_y2 to loc_x2_y2, moves to loc_x0_y1 from loc_x2_y5, moves from loc_x1_y2 to loc_x1_y1, robot moves from loc_x0_y0 to loc_x0_y2, robot moves from loc_x1_y1 to loc_x3_y1, robot moves from loc_x1_y0 to loc_x2_y3, robot moves from loc_x0_y1 to loc_x1_y0, robot moves from loc_x3_y1 to loc_x3_y4, from loc_x2_y4, the robot moves to loc_x0_y0, robot moves from loc_x3_y3 to loc_x1_y3, from loc_x1_y4, the robot moves to loc_x1_y5, moves to loc_x2_y3 from loc_x0_y3, robot moves from loc_x1_y1 to loc_x1_y5, moves from loc_x2_y0 to loc_x1_y2, moves to loc_x0_y1 from loc_x3_y0, from loc_x2_y3, the robot moves to loc_x2_y2, moves to loc_x0_y2 from loc_x1_y4, moves from loc_x2_y2 to loc_x3_y2, moves to loc_x1_y4 from loc_x0_y4, from loc_x2_y2, the robot moves to loc_x2_y0, from loc_x1_y4, the robot moves to loc_x3_y1, from loc_x2_y3, the robot moves to loc_x1_y4, moves from loc_x0_y5 to loc_x0_y3, from loc_x0_y3, the robot moves to loc_x0_y5, robot moves from loc_x3_y2 to loc_x0_y3, from loc_x3_y1, the robot moves to loc_x1_y5, robot moves from loc_x1_y2 to loc_x1_y5, from loc_x3_y0, the robot moves to loc_x2_y3, from loc_x1_y1, the robot moves to loc_x2_y2, moves from loc_x0_y2 to loc_x2_y3, robot moves from loc_x1_y1 to loc_x1_y0, moves from loc_x2_y4 to loc_x0_y5, robot moves from loc_x1_y2 to loc_x3_y3, from loc_x3_y4, the robot moves to loc_x0_y0, moves to loc_x3_y0 from loc_x1_y0, robot moves from loc_x1_y5 to loc_x1_y4, from loc_x0_y3, the robot moves to loc_x1_y2, from loc_x3_y0, the robot moves to loc_x3_y1, moves from loc_x0_y0 to loc_x0_y3, robot moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x2_y5 to loc_x0_y3, from loc_x1_y2, the robot moves to loc_x0_y3, robot moves from loc_x2_y2 to loc_x2_y4, robot moves from loc_x1_y1 to loc_x0_y0, moves to loc_x3_y3 from loc_x2_y4, robot moves from loc_x1_y5 to loc_x3_y1, robot moves from loc_x2_y5 to loc_x3_y4, robot moves from loc_x1_y3 to loc_x2_y5, from loc_x2_y3, the robot moves to loc_x3_y3, moves from loc_x1_y3 to loc_x2_y3, robot moves from loc_x3_y4 to loc_x0_y4, from loc_x2_y5, the robot moves to loc_x1_y3, robot moves from loc_x0_y4 to loc_x1_y5, moves to loc_x2_y0 from loc_x3_y0, robot moves from loc_x2_y1 to loc_x0_y4, robot moves from loc_x0_y2 to loc_x3_y3, moves to loc_x2_y1 from loc_x0_y1, robot moves from loc_x3_y2 to loc_x1_y4, robot moves from loc_x0_y2 to loc_x1_y1, robot moves from loc_x3_y2 to loc_x2_y5, moves to loc_x2_y1 from loc_x1_y3, robot moves from loc_x1_y1 to loc_x0_y3, robot moves from loc_x1_y1 to loc_x3_y3, moves from loc_x2_y1 to loc_x2_y5, moves to loc_x3_y0 from loc_x1_y4, moves to loc_x1_y3 from loc_x1_y4, robot moves from loc_x2_y0 to loc_x2_y4, from loc_x3_y1, the robot moves to loc_x0_y5, robot moves from loc_x3_y0 to loc_x2_y5, robot moves from loc_x1_y0 to loc_x3_y4, moves to loc_x3_y4 from loc_x0_y5, robot moves from loc_x1_y4 to loc_x0_y0, from loc_x3_y1, the robot moves to loc_x2_y3, robot moves from loc_x0_y3 to loc_x1_y3, robot moves from loc_x2_y4 to loc_x0_y3, robot moves from loc_x1_y3 to loc_x0_y0, robot moves from loc_x3_y4 to loc_x0_y1, robot moves from loc_x3_y0 to loc_x0_y0, robot moves from loc_x0_y1 to loc_x2_y2, moves from loc_x1_y3 to loc_x0_y5, from loc_x1_y1, the robot moves to loc_x3_y4, moves from loc_x2_y0 to loc_x3_y2, from loc_x0_y2, the robot moves to loc_x2_y4, from loc_x0_y4, the robot moves to loc_x2_y2, moves from loc_x3_y2 to loc_x0_y0, moves from loc_x0_y3 to loc_x3_y1, robot moves from loc_x0_y3 to loc_x1_y0, robot moves from loc_x2_y0 to loc_x2_y3, from loc_x0_y2, the robot moves to loc_x1_y0, moves from loc_x1_y2 to loc_x3_y2, moves from loc_x0_y4 to loc_x3_y4, robot moves from loc_x1_y0 to loc_x0_y3, moves to loc_x2_y1 from loc_x3_y0, moves from loc_x3_y3 to loc_x3_y2, robot moves from loc_x1_y2 to loc_x0_y0, robot moves from loc_x2_y0 to loc_x2_y5, from loc_x3_y4, the robot moves to loc_x0_y3, robot moves from loc_x0_y1 to loc_x2_y5, moves to loc_x2_y1 from loc_x3_y1, robot moves from loc_x2_y1 to loc_x3_y0, from loc_x2_y1, the robot moves to loc_x1_y0, robot moves from loc_x3_y3 to loc_x0_y3, moves from loc_x2_y3 to loc_x0_y0, moves from loc_x3_y2 to loc_x2_y2, moves from loc_x0_y4 to loc_x2_y5, from loc_x3_y1, the robot moves to loc_x2_y5, robot moves from loc_x0_y4 to loc_x1_y2, robot moves from loc_x1_y5 to loc_x0_y0, from loc_x0_y4, the robot moves to loc_x2_y0, moves to loc_x1_y1 from loc_x3_y0, robot moves from loc_x1_y4 to loc_x2_y5, robot moves from loc_x1_y5 to loc_x1_y3, moves to loc_x3_y4 from loc_x2_y3, from loc_x1_y2, the robot moves to loc_x3_y1, moves from loc_x2_y0 to loc_x0_y2, moves from loc_x2_y2 to loc_x3_y1, robot moves from loc_x3_y3 to loc_x2_y0, robot moves from loc_x1_y4 to loc_x0_y4, moves to loc_x2_y0 from loc_x3_y2, moves to loc_x3_y2 from loc_x1_y0, moves to loc_x1_y1 from loc_x1_y4, moves from loc_x1_y1 to loc_x2_y1, from loc_x1_y3, the robot moves to loc_x3_y1, moves to loc_x1_y4 from loc_x0_y0, from loc_x2_y5, the robot moves to loc_x2_y1, robot moves from loc_x1_y4 to loc_x3_y3, robot moves from loc_x0_y1 to loc_x3_y1, moves to loc_x3_y1 from loc_x2_y4, moves from loc_x2_y3 to loc_x1_y2, robot moves from loc_x1_y2 to loc_x1_y3, moves to loc_x0_y1 from loc_x1_y5, robot moves from loc_x0_y0 to loc_x3_y4, moves to loc_x0_y4 from loc_x0_y3, from loc_x0_y2, the robot moves to loc_x1_y5, from loc_x3_y2, the robot moves to loc_x3_y4, robot moves from loc_x2_y2 to loc_x0_y0, robot moves from loc_x3_y0 to loc_x0_y2, from loc_x1_y1, the robot moves to loc_x0_y5, from loc_x0_y3, the robot moves to loc_x3_y3, robot moves from loc_x1_y4 to loc_x3_y4, moves from loc_x2_y3 to loc_x0_y5, from loc_x0_y5, the robot moves to loc_x0_y1, robot moves from loc_x3_y4 to loc_x3_y1, moves from loc_x1_y0 to loc_x2_y4, robot moves from loc_x0_y2 to loc_x1_y3, from loc_x3_y4, the robot moves to loc_x2_y3, robot moves from loc_x3_y2 to loc_x0_y1, moves from loc_x3_y1 to loc_x0_y2, robot moves from loc_x3_y0 to loc_x0_y3, from loc_x0_y4, the robot moves to loc_x1_y3, robot moves from loc_x3_y1 to loc_x1_y0, moves to loc_x0_y2 from loc_x2_y2, moves from loc_x3_y1 to loc_x1_y3, moves to loc_x0_y4 from loc_x3_y3, robot moves from loc_x0_y2 to loc_x0_y0, robot moves from loc_x2_y2 to loc_x1_y1, robot moves from loc_x3_y3 to loc_x0_y1, robot moves from loc_x3_y3 to loc_x3_y0, moves from loc_x0_y1 to loc_x2_y0, from loc_x2_y2, the robot moves to loc_x0_y4, robot moves from loc_x3_y3 to loc_x2_y5, moves from loc_x0_y0 to loc_x1_y5, robot moves from loc_x3_y2 to loc_x0_y4, from loc_x1_y0, the robot moves to loc_x0_y0, moves to loc_x1_y0 from loc_x1_y5, robot moves from loc_x1_y5 to loc_x3_y3, from loc_x1_y3, the robot moves to loc_x1_y5, moves from loc_x2_y3 to loc_x3_y2, robot moves from loc_x2_y1 to loc_x3_y2, moves from loc_x2_y5 to loc_x3_y1, moves from loc_x3_y4 to loc_x2_y1, from loc_x1_y5, the robot moves to loc_x2_y3, moves to loc_x3_y2 from loc_x0_y0, robot moves from loc_x0_y0 to loc_x0_y5, robot moves from loc_x1_y0 to loc_x1_y3, from loc_x0_y5, the robot moves to loc_x3_y1, moves from loc_x2_y5 to loc_x1_y0, from loc_x1_y4, the robot moves to loc_x1_y0, robot moves from loc_x3_y1 to loc_x0_y0, moves to loc_x3_y3 from loc_x2_y1, moves to loc_x2_y2 from loc_x2_y5, from loc_x2_y4, the robot moves to loc_x2_y0, robot moves from loc_x0_y0 to loc_x3_y0, robot moves from loc_x3_y4 to loc_x3_y0, from loc_x2_y3, the robot moves to loc_x0_y1, robot moves from loc_x3_y0 to loc_x0_y4, robot moves from loc_x1_y2 to loc_x0_y2, moves from loc_x1_y2 to loc_x1_y0, moves from loc_x0_y1 to loc_x1_y3, moves from loc_x2_y0 to loc_x0_y3, moves to loc_x1_y1 from loc_x2_y0, robot moves from loc_x1_y3 to loc_x1_y4, moves from loc_x3_y4 to loc_x2_y0, robot moves from loc_x0_y0 to loc_x1_y2, moves to loc_x0_y5 from loc_x1_y2, moves to loc_x3_y0 from loc_x3_y2, moves from loc_x0_y0 to loc_x3_y3, robot moves from loc_x1_y5 to loc_x0_y4, robot moves from loc_x2_y2 to loc_x1_y3, from loc_x0_y0, the robot moves to loc_x2_y5, moves from loc_x0_y5 to loc_x2_y0, robot moves from loc_x0_y3 to loc_x2_y5, moves to loc_x0_y0 from loc_x0_y4, robot moves from loc_x3_y3 to loc_x2_y4, robot moves from loc_x2_y4 to loc_x0_y2, robot moves from loc_x2_y0 to loc_x3_y1, robot moves from loc_x2_y0 to loc_x2_y2, moves from loc_x3_y1 to loc_x1_y4, from loc_x0_y1, the robot moves to loc_x1_y5, moves from loc_x0_y5 to loc_x3_y2, from loc_x3_y3, the robot moves to loc_x3_y1, robot moves from loc_x3_y3 to loc_x1_y0, robot moves from loc_x3_y3 to loc_x1_y5, robot moves from loc_x2_y5 to loc_x1_y2, moves to loc_x2_y2 from loc_x1_y3, moves to loc_x1_y0 from loc_x2_y2, moves to loc_x3_y2 from loc_x1_y1, robot moves from loc_x1_y5 to loc_x2_y1, moves to loc_x3_y2 from loc_x3_y4, from loc_x0_y0, the robot moves to loc_x2_y3, robot moves from loc_x1_y2 to loc_x2_y3, robot moves from loc_x3_y2 to loc_x1_y3, moves to loc_x1_y2 from loc_x0_y1, robot moves from loc_x0_y5 to loc_x1_y5, moves to loc_x1_y5 from loc_x2_y2, robot moves from loc_x2_y4 to loc_x1_y4, moves from loc_x2_y0 to loc_x0_y1, robot moves from loc_x3_y0 to loc_x3_y4, from loc_x0_y2, the robot moves to loc_x0_y5, robot moves from loc_x2_y1 to loc_x0_y5, robot moves from loc_x1_y4 to loc_x0_y5, robot moves from loc_x2_y4 to loc_x2_y3, moves from loc_x1_y0 to loc_x0_y1, moves to loc_x3_y2 from loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y1, moves from loc_x2_y3 to loc_x3_y0, moves to loc_x0_y2 from loc_x0_y5, robot moves from loc_x0_y2 to loc_x3_y2, moves to loc_x3_y0 from loc_x0_y3, robot moves from loc_x0_y1 to loc_x1_y4, from loc_x3_y2, the robot moves to loc_x1_y5, moves from loc_x1_y5 to loc_x0_y2, moves to loc_x2_y3 from loc_x2_y5, moves from loc_x3_y1 to loc_x2_y0, moves from loc_x0_y0 to loc_x1_y3, robot moves from loc_x0_y1 to loc_x3_y0, from loc_x2_y0, the robot moves to loc_x1_y4, robot moves from loc_x1_y5 to loc_x0_y3 and from loc_x0_y2, the robot moves to loc_x3_y4", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x0_y2, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y5, loc_x0_y5 and loc_x0_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x2_y2, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: the robot moves from loc_x0_y2 to loc_x0_y1 to reach the current state. In this state, identify all actions that cannot be executed. If there are no such actions, indicate None.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y1 to loc_x0_y2, loc_x0_y1 to loc_x1_y1, loc_x0_y2 to loc_x0_y1, loc_x0_y2 to loc_x0_y3, loc_x0_y2 to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 to loc_x1_y3, loc_x0_y3 to loc_x0_y4, loc_x0_y4 to loc_x0_y3, loc_x0_y4 to loc_x1_y4, loc_x0_y4 to loc_x0_y5, loc_x0_y5 to loc_x0_y4, loc_x0_y5 to loc_x1_y5, loc_x1_y0 to loc_x2_y0, loc_x1_y1 to loc_x0_y1, loc_x1_y2 to loc_x0_y2, loc_x1_y2 to loc_x1_y3, loc_x1_y2 to loc_x2_y2, loc_x1_y3 to loc_x0_y3, loc_x1_y3 to loc_x1_y4, loc_x1_y3 to loc_x2_y3, loc_x1_y4 to loc_x1_y3, loc_x1_y4 to loc_x0_y4, loc_x1_y4 to loc_x1_y5, loc_x1_y5 to loc_x2_y5, loc_x2_y0 to loc_x2_y1, loc_x2_y1 to loc_x1_y1, loc_x2_y2 to loc_x3_y2, loc_x2_y2 to loc_x2_y3, loc_x2_y3 to loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y4 to loc_x2_y3, loc_x2_y4 to loc_x2_y5, loc_x2_y5 to loc_x1_y5, loc_x2_y5 to loc_x2_y4, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x3_y1, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y0, loc_x3_y1 to loc_x3_y2, loc_x3_y2 to loc_x2_y2, loc_x3_y2 to loc_x3_y1, loc_x3_y3 to loc_x3_y2, loc_x3_y4 to loc_x2_y4, loc_x3_y4 to loc_x3_y3, the robot is at loc_x0_y2. Additionally, the following connections exist: loc_x0_y0 to loc_x1_y0, loc_x0_y1 to loc_x0_y0, loc_x0_y3 to loc_x0_y2, loc_x1_y0 to loc_x0_y0, loc_x1_y0 to loc_x1_y1, loc_x1_y1 to loc_x1_y0, loc_x1_y1 to loc_x1_y2, loc_x1_y1 to loc_x2_y1, loc_x1_y2 to loc_x1_y1, loc_x1_y3 to loc_x1_y2, loc_x1_y4 to loc_x2_y4, loc_x1_y5 to loc_x0_y5, loc_x1_y5 to loc_x1_y4, loc_x2_y0 to loc_x1_y0, loc_x2_y0 to loc_x3_y0, loc_x2_y1 to loc_x2_y0, loc_x2_y1 to loc_x2_y2, loc_x2_y1 to loc_x3_y1, loc_x2_y2 to loc_x1_y2, loc_x2_y2 to loc_x2_y1, loc_x2_y3 to loc_x1_y3, loc_x2_y3 to loc_x2_y2, loc_x2_y4 to loc_x1_y4, loc_x2_y4 to loc_x3_y4, loc_x3_y2 to loc_x3_y3, loc_x3_y3 to loc_x2_y3, and loc_x3_y3 to loc_x3_y4."}
{"question_id": "9addce4c-81c3-426d-9e70-5a7173bdd837", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x1_y3, moves from loc_x1_y3 to loc_x1_y4, moves to loc_x2_y4 from loc_x1_y4, robot moves from loc_x2_y4 to loc_x3_y4, robot moves from loc_x3_y4 to loc_x3_y3, moves to loc_x2_y3 from loc_x3_y3, robot moves from loc_x2_y3 to loc_x2_y2 and moves to loc_x2_y1 from loc_x2_y2 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "moves from loc_x2_y1 to loc_x3_y1, moves to loc_x2_y2 from loc_x2_y1, robot moves from loc_x2_y1 to loc_x2_y0 and moves to loc_x1_y1 from loc_x2_y1", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are executed: a move from loc_x0_y3 to loc_x0_y4 is performed, followed by the robot moving from loc_x0_y4 back to loc_x0_y3, then from loc_x0_y3 to loc_x1_y3, then from loc_x1_y3 to loc_x1_y4, then from loc_x1_y4 to loc_x2_y4, the robot proceeds from loc_x2_y4 to loc_x3_y4, then from loc_x3_y4 to loc_x3_y3, then from loc_x3_y3 to loc_x2_y3, then from loc_x2_y3 to loc_x2_y2, and finally from loc_x2_y2 to loc_x2_y1 to reach the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "8f675458-0e96-4881-9124-1b39a0010bf9", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y4 from loc_x0_y3, moves to loc_x0_y3 from loc_x0_y4, from loc_x0_y3, the robot moves to loc_x1_y3, moves to loc_x1_y4 from loc_x1_y3, from loc_x1_y4, the robot moves to loc_x2_y4, moves from loc_x2_y4 to loc_x3_y4, moves from loc_x3_y3 to loc_x2_y4, robot moves from loc_x3_y3 to loc_x2_y3, robot moves from loc_x2_y3 to loc_x2_y2 and robot moves from loc_x2_y2 to loc_x2_y1 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "robot moves from loc_x3_y3 to loc_x2_y4", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are taken: the robot transitions from loc_x0_y3 to loc_x0_y4 and then back to loc_x0_y3, then proceeds from loc_x0_y3 to loc_x1_y3, followed by a move from loc_x1_y3 to loc_x1_y4, then from loc_x1_y4 to loc_x2_y4, and from loc_x2_y4 to loc_x3_y4. The sequence continues with a move from loc_x3_y3 to loc_x2_y4, then from loc_x3_y3 to loc_x2_y3, followed by a transition from loc_x2_y3 to loc_x2_y2, and finally from loc_x2_y2 to loc_x2_y1 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 is adjacent to loc_x0_y4, loc_x0_y3 is adjacent to loc_x1_y3, loc_x0_y3 has been visited, loc_x0_y3 and loc_x0_y4 are adjacent, loc_x0_y4 and loc_x1_y4 are adjacent, loc_x1_y0 is adjacent to loc_x1_y1, loc_x1_y1 is adjacent to loc_x1_y0, loc_x1_y1 is adjacent to loc_x2_y1, loc_x1_y3 is adjacent to loc_x0_y3, loc_x1_y3 is adjacent to loc_x1_y4, loc_x1_y3 is adjacent to loc_x2_y3, loc_x1_y4 is adjacent to loc_x2_y4, loc_x1_y4 is adjacent to loc_x1_y3, loc_x2_y0 is adjacent to loc_x1_y0, loc_x2_y1 is adjacent to loc_x2_y2, loc_x2_y1 is adjacent to loc_x3_y1, loc_x2_y1 is adjacent to loc_x1_y1, loc_x2_y1 is adjacent to loc_x2_y0, loc_x2_y2 is adjacent to loc_x3_y2, loc_x2_y3 is adjacent to loc_x1_y3, loc_x2_y3 is adjacent to loc_x2_y2, loc_x2_y3 is adjacent to loc_x2_y4, loc_x2_y3 is adjacent to loc_x3_y3, loc_x2_y4 is adjacent to loc_x3_y4, loc_x2_y4 is adjacent to loc_x1_y4, loc_x2_y4 is adjacent to loc_x2_y3, loc_x3_y0 is adjacent to loc_x4_y0, loc_x3_y2 is adjacent to loc_x3_y1, loc_x3_y2 is adjacent to loc_x3_y3, loc_x3_y2 is adjacent to loc_x4_y2, loc_x3_y3 is adjacent to loc_x3_y2, loc_x3_y3 is adjacent to loc_x3_y4, loc_x3_y4 is adjacent to loc_x2_y4, loc_x4_y0 is adjacent to loc_x3_y0, loc_x4_y0 is adjacent to loc_x4_y1, loc_x4_y1 is adjacent to loc_x3_y1, loc_x4_y1 is adjacent to loc_x4_y2, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "f6bdd68a-a777-47af-96cb-f0c900d84ecf", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x1_y0 to loc_x0_y0, robot moves from loc_x0_y0 to loc_x0_y1, moves from loc_x0_y1 to loc_x1_y1, moves to loc_x2_y1 from loc_x1_y1, moves to loc_x2_y0 from loc_x2_y1, moves from loc_x2_y0 to loc_x3_y0, from loc_x3_y0, the robot moves to loc_x3_y1, robot moves from loc_x3_y1 to loc_x3_y2, moves to loc_x3_y1 from loc_x3_y2, robot moves from loc_x3_y1 to loc_x4_y1, moves to loc_x4_y0 from loc_x4_y1, from loc_x4_y0, the robot moves to loc_x5_y0, robot moves from loc_x5_y0 to loc_x5_y1, robot moves from loc_x5_y1 to loc_x5_y2, from loc_x5_y2, the robot moves to loc_x4_y2, from loc_x4_y2, the robot moves to loc_x4_y3, from loc_x4_y3, the robot moves to loc_x5_y3, moves from loc_x5_y3 to loc_x5_y4 and robot moves from loc_x5_y4 to loc_x4_y4 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "from loc_x4_y4, the robot moves to loc_x4_y3, moves from loc_x4_y4 to loc_x5_y4 and moves to loc_x3_y4 from loc_x4_y4", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x5_y3 are connected, loc_x5_y4 is connected to loc_x4_y4, robot is at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3 and there is a connection between loc_x5_y3 and loc_x5_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot undergoes the following sequence of movements: it moves from loc_x1_y0 to loc_x0_y0, then to loc_x0_y1, followed by loc_x1_y1, then loc_x2_y1, and subsequently loc_x2_y0. The robot continues to loc_x3_y0, then to loc_x3_y1, followed by loc_x3_y2, and then returns to loc_x3_y1. It then proceeds to loc_x4_y1, followed by loc_x4_y0, then loc_x5_y0, and subsequently loc_x5_y1 and loc_x5_y2. From there, the robot moves to loc_x4_y2, then loc_x4_y3, followed by loc_x5_y3, then loc_x5_y4, and finally loc_x4_y4 to reach the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x0_y0, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is also connected to loc_x1_y2, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 is connected to loc_x4_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 is connected to loc_x5_y3, loc_x5_y4 is connected to loc_x4_y4, the robot is currently at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3, and there is a connection between loc_x5_y3 and loc_x5_y2."}
{"question_id": "3cffbfbc-a59d-47e0-b8ed-a4f3198a7c1f", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x1_y0 to loc_x0_y0, moves from loc_x0_y1 to loc_x0_y0, moves to loc_x1_y1 from loc_x0_y1, moves from loc_x1_y1 to loc_x2_y1, moves from loc_x2_y1 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, moves from loc_x3_y0 to loc_x3_y1, from loc_x3_y1, the robot moves to loc_x3_y2, from loc_x3_y2, the robot moves to loc_x3_y1, robot moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, robot moves from loc_x4_y0 to loc_x5_y0, robot moves from loc_x5_y0 to loc_x5_y1, from loc_x5_y1, the robot moves to loc_x5_y2, moves from loc_x5_y2 to loc_x4_y2, robot moves from loc_x4_y2 to loc_x4_y3, robot moves from loc_x4_y3 to loc_x5_y3, moves to loc_x5_y4 from loc_x5_y3 and moves from loc_x5_y4 to loc_x4_y4 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "moves from loc_x0_y1 to loc_x0_y0", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x5_y3 are connected, loc_x5_y4 is connected to loc_x4_y4, robot is at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3 and there is a connection between loc_x5_y3 and loc_x5_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot performs the following sequence of actions: it moves from loc_x1_y0 to loc_x0_y0, then from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y1 to loc_x1_y1, then from loc_x1_y1 to loc_x2_y1, and from loc_x2_y1 to loc_x2_y0. Next, the robot moves from loc_x2_y0 to loc_x3_y0, then to loc_x3_y1, and from loc_x3_y1, it proceeds to loc_x3_y2, only to return to loc_x3_y1. The robot then moves from loc_x3_y1 to loc_x4_y1, followed by a move to loc_x4_y0, then to loc_x5_y0, and from loc_x5_y0 to loc_x5_y1. From loc_x5_y1, the robot moves to loc_x5_y2, then to loc_x4_y2, followed by a move to loc_x4_y3, then to loc_x5_y3, and from loc_x5_y3, it moves to loc_x5_y4, and finally from loc_x5_y4 to loc_x4_y4 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x0_y0, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is also connected to loc_x1_y2, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 is connected to loc_x4_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 is connected to loc_x5_y3, loc_x5_y4 is connected to loc_x4_y4, the robot is currently at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3, and there is a connection between loc_x5_y3 and loc_x5_y2."}
{"question_id": "86ab0129-d4aa-4279-a52f-e393230ee86d", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_3_question_1", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: moves to loc_x0_y4 from loc_x0_y3, robot moves from loc_x0_y4 to loc_x0_y3, from loc_x0_y3, the robot moves to loc_x1_y3, robot moves from loc_x1_y3 to loc_x1_y4, moves to loc_x2_y4 from loc_x1_y4, robot moves from loc_x2_y4 to loc_x3_y4, robot moves from loc_x3_y4 to loc_x3_y3, moves to loc_x2_y3 from loc_x3_y3, moves to loc_x2_y2 from loc_x2_y3, robot moves from loc_x2_y2 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x0_y1, robot moves from loc_x0_y1 to loc_x0_y0, from loc_x0_y0, the robot moves to loc_x1_y0, robot moves from loc_x1_y0 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x3_y2 and robot moves from loc_x3_y2 to loc_x4_y2 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "None", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is executed: the robot transitions from loc_x0_y3 to loc_x0_y4, then back to loc_x0_y3, followed by a move to loc_x1_y3, then to loc_x1_y4, then to loc_x2_y4, and subsequently to loc_x3_y4, then to loc_x3_y3, then to loc_x2_y3, then to loc_x2_y2, then to loc_x2_y1, then to loc_x1_y1, then to loc_x0_y1, then to loc_x0_y0, then to loc_x1_y0, then to loc_x2_y0, then to loc_x3_y0, then to loc_x3_y1, then to loc_x3_y2, and finally to loc_x4_y2 to reach the current state. What is the first action in the sequence that cannot be executed? If none exist, write None.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 is adjacent to loc_x0_y4, loc_x0_y3 is adjacent to loc_x1_y3, loc_x0_y3 has been visited, loc_x0_y3 and loc_x0_y4 are adjacent, loc_x0_y4 and loc_x1_y4 are adjacent, loc_x1_y0 is adjacent to loc_x1_y1, loc_x1_y1 is adjacent to loc_x1_y0, loc_x1_y1 is adjacent to loc_x2_y1, loc_x1_y3 is adjacent to loc_x0_y3, loc_x1_y3 is adjacent to loc_x1_y4, loc_x1_y3 is adjacent to loc_x2_y3, loc_x1_y4 is adjacent to loc_x2_y4, loc_x1_y4 is adjacent to loc_x1_y3, loc_x2_y0 is adjacent to loc_x1_y0, loc_x2_y1 is adjacent to loc_x2_y2, loc_x2_y1 is adjacent to loc_x3_y1, loc_x2_y1 is adjacent to loc_x1_y1, loc_x2_y1 is adjacent to loc_x2_y0, loc_x2_y2 is adjacent to loc_x3_y2, loc_x2_y3 is adjacent to loc_x1_y3, loc_x2_y3 is adjacent to loc_x2_y2, loc_x2_y3 is adjacent to loc_x2_y4, loc_x2_y3 is adjacent to loc_x3_y3, loc_x2_y4 is adjacent to loc_x3_y4, loc_x2_y4 is adjacent to loc_x1_y4, loc_x2_y4 is adjacent to loc_x2_y3, loc_x3_y0 is adjacent to loc_x4_y0, loc_x3_y2 is adjacent to loc_x3_y1, loc_x3_y2 is adjacent to loc_x3_y3, loc_x3_y2 is adjacent to loc_x4_y2, loc_x3_y3 is adjacent to loc_x3_y2, loc_x3_y3 is adjacent to loc_x3_y4, loc_x3_y4 is adjacent to loc_x2_y4, loc_x4_y0 is adjacent to loc_x3_y0, loc_x4_y0 is adjacent to loc_x4_y1, loc_x4_y1 is adjacent to loc_x3_y1, loc_x4_y1 is adjacent to loc_x4_y2, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "54572b1b-4849-41a6-9398-f4df8af0bae4", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x1_y3, robot moves from loc_x1_y3 to loc_x1_y4, from loc_x1_y4, the robot moves to loc_x2_y4, robot moves from loc_x2_y4 to loc_x3_y4, robot moves from loc_x3_y4 to loc_x3_y3, moves from loc_x3_y3 to loc_x2_y3, robot moves from loc_x2_y3 to loc_x2_y2, from loc_x2_y2, the robot moves to loc_x2_y1, robot moves from loc_x2_y1 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x0_y1, moves to loc_x0_y0 from loc_x0_y1, from loc_x0_y0, the robot moves to loc_x1_y0, from loc_x1_y0, the robot moves to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, moves from loc_x3_y0 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x3_y2 and robot moves from loc_x3_y2 to loc_x4_y2 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "moves to loc_x4_y1 from loc_x4_y2 and robot moves from loc_x4_y2 to loc_x3_y2", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot performs the following sequence of actions: it moves from loc_x0_y3 to loc_x0_y4, then back to loc_x0_y3, followed by a move to loc_x1_y3, then to loc_x1_y4, and from there to loc_x2_y4. The robot continues by moving to loc_x3_y4, then to loc_x3_y3, followed by a move to loc_x2_y3, then to loc_x2_y2, and from there to loc_x2_y1. Next, it moves to loc_x1_y1, then to loc_x0_y1, and from there to loc_x0_y0. The robot then moves to loc_x1_y0, followed by loc_x2_y0, then to loc_x3_y0, and from there to loc_x3_y1, then to loc_x3_y2, and finally to loc_x4_y2 to reach the current state. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "f3badcb8-6622-44a6-ab0f-3811b7fc7d2f", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: from loc_x1_y0, the robot moves to loc_x0_y0 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "robot moves from loc_x0_y0 to loc_x1_y0 and moves from loc_x0_y0 to loc_x0_y1", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x5_y3 are connected, loc_x5_y4 is connected to loc_x4_y4, robot is at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3 and there is a connection between loc_x5_y3 and loc_x5_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are performed: starting from loc_x1_y0, the robot proceeds to loc_x0_y0 to attain the current state. In this state, enumerate all possible executable actions, or specify None if none exist.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x0_y0, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is also connected to loc_x1_y2, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 is connected to loc_x4_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 is connected to loc_x5_y3, loc_x5_y4 is connected to loc_x4_y4, the robot is currently at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3, and there is a connection between loc_x5_y3 and loc_x5_y2."}
{"question_id": "9c5488ab-996d-4de5-a1df-38ba247bb690", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_3_question_2", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: robot moves from loc_x0_y2 to loc_x0_y1, moves to loc_x0_y0 from loc_x0_y1, moves to loc_x1_y0 from loc_x0_y0, moves to loc_x1_y1 from loc_x1_y0, from loc_x1_y1, the robot moves to loc_x1_y2, moves to loc_x3_y2 from loc_x1_y2, moves from loc_x0_y2 to loc_x0_y3, moves to loc_x0_y4 from loc_x0_y3, robot moves from loc_x0_y4 to loc_x0_y5, robot moves from loc_x0_y5 to loc_x1_y5, robot moves from loc_x1_y5 to loc_x1_y4, robot moves from loc_x1_y4 to loc_x1_y3, moves to loc_x2_y3 from loc_x1_y3, moves to loc_x2_y2 from loc_x2_y3, moves to loc_x2_y1 from loc_x2_y2, robot moves from loc_x2_y1 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x3_y0, from loc_x3_y0, the robot moves to loc_x3_y1 and robot moves from loc_x3_y1 to loc_x3_y2 to reach the current state. What is the first inexecutable action in the sequence? Write None if there are none.", "answer": "from loc_x1_y2, the robot moves to loc_x3_y2", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x0_y2, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y5, loc_x0_y5 and loc_x0_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x2_y2, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot performs the following sequence of actions: it moves from loc_x0_y2 to loc_x0_y1, then to loc_x0_y0, followed by loc_x1_y0, then loc_x1_y1, and loc_x1_y2. Next, it moves to loc_x3_y2 from loc_x1_y2, then to loc_x0_y3 from loc_x0_y2, followed by loc_x0_y4, loc_x0_y5, loc_x1_y5, loc_x1_y4, and loc_x1_y3. The robot then proceeds to loc_x2_y3, loc_x2_y2, loc_x2_y1, loc_x2_y0, loc_x3_y0, loc_x3_y1, and finally loc_x3_y2 to reach the current state. What is the first action in the sequence that cannot be executed? If none, write None.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y1 to loc_x0_y2, loc_x0_y1 to loc_x1_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y2 to loc_x0_y3, loc_x0_y2 to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3, loc_x0_y3 to loc_x0_y4, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x0_y4 to loc_x0_y5, loc_x0_y4 and loc_x0_y5, loc_x0_y5 and loc_x1_y5, loc_x1_y0 to loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 to loc_x1_y3, loc_x1_y2 to loc_x2_y2, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 to loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 to loc_x0_y4, loc_x1_y4 to loc_x1_y5, loc_x1_y5 to loc_x2_y5, loc_x2_y0 to loc_x2_y1, loc_x2_y1 to loc_x1_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y2 to loc_x2_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x2_y5, loc_x2_y5 to loc_x1_y5, loc_x2_y5 to loc_x2_y4, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x3_y1, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y0, loc_x3_y1 to loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, the robot is at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y2, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x2_y4, loc_x1_y5 and loc_x0_y5, loc_x1_y5 and loc_x1_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x3_y4, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, and loc_x3_y3 and loc_x3_y4."}
{"question_id": "95411a53-3538-4d6a-b38f-7f17b334fe6d", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "question_4", "fluent_type": "all_fluents", "answer_type": "free_answer", "question": "Given the initial condition, the following actions are performed: moves from loc_x1_y0 to loc_x0_y0, robot moves from loc_x0_y0 to loc_x0_y1, robot moves from loc_x0_y1 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x2_y1, moves from loc_x2_y1 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x3_y0, moves from loc_x3_y0 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x3_y2, moves to loc_x3_y1 from loc_x3_y2 and from loc_x3_y1, the robot moves to loc_x4_y1 to reach the current state. In this state, list all executable actions. Write None if there are none.", "answer": "robot moves from loc_x4_y1 to loc_x4_y2, moves to loc_x4_y0 from loc_x4_y1, moves from loc_x4_y1 to loc_x5_y1 and moves from loc_x4_y1 to loc_x3_y1", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x5_y3 are connected, loc_x5_y4 is connected to loc_x4_y4, robot is at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3 and there is a connection between loc_x5_y3 and loc_x5_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the robot executes the following sequence of actions: it moves from loc_x1_y0 to loc_x0_y0, then from loc_x0_y0 to loc_x0_y1, followed by a move from loc_x0_y1 to loc_x1_y1, and then from loc_x1_y1 to loc_x2_y1. Next, the robot moves from loc_x2_y1 to loc_x2_y0, and from loc_x2_y0, it proceeds to loc_x3_y0. The robot then moves from loc_x3_y0 to loc_x3_y1, followed by a move from loc_x3_y1 to loc_x3_y2. After that, it moves back from loc_x3_y2 to loc_x3_y1, and finally, from loc_x3_y1, the robot reaches the current state by moving to loc_x4_y1. In this state, list all executable actions. Write None if there are none.", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x0_y0, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is also connected to loc_x1_y2, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 is connected to loc_x4_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 is connected to loc_x5_y3, loc_x5_y4 is connected to loc_x4_y4, the robot is currently at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3, and there is a connection between loc_x5_y3 and loc_x5_y2."}
