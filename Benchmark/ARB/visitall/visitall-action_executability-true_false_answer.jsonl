{"question_id": "5259cba0-1480-4eda-a485-10fd80b24442", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: moves from loc_x1_y0 to loc_x0_y0, moves to loc_x0_y1 from loc_x0_y0, from loc_x0_y1, the robot moves to loc_x1_y1, moves to loc_x2_y1 from loc_x1_y1, moves to loc_x2_y0 from loc_x2_y1, robot moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, moves to loc_x3_y2 from loc_x3_y1, moves to loc_x3_y1 from loc_x3_y2, from loc_x3_y1, the robot moves to loc_x4_y1, moves to loc_x4_y0 from loc_x4_y1, moves from loc_x4_y0 to loc_x5_y0, from loc_x5_y0, the robot moves to loc_x5_y1, from loc_x5_y1, the robot moves to loc_x5_y2, from loc_x5_y2, the robot moves to loc_x4_y2, from loc_x4_y2, the robot moves to loc_x4_y3, robot moves from loc_x4_y3 to loc_x5_y3, moves to loc_x5_y4 from loc_x5_y3 and moves to loc_x4_y4 from loc_x5_y4. Is the action: robot moves from loc_x1_y1 to loc_x2_y1 executable at step 4, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x5_y3 are connected, loc_x5_y4 is connected to loc_x4_y4, robot is at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3 and there is a connection between loc_x5_y3 and loc_x5_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 19: the robot will move from loc_x1_y0 to loc_x0_y0, then to loc_x0_y1, followed by a move to loc_x1_y1, then to loc_x2_y1, then to loc_x2_y0, then to loc_x3_y0, then to loc_x3_y1, then to loc_x3_y2, then back to loc_x3_y1, then to loc_x4_y1, then to loc_x4_y0, then to loc_x5_y0, then to loc_x5_y1, then to loc_x5_y2, then to loc_x4_y2, then to loc_x4_y3, then to loc_x5_y3, then to loc_x5_y4, and finally to loc_x4_y4. Is the action: a move from loc_x1_y1 to loc_x2_y1 executable at step 4, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x0_y0, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is also connected to loc_x1_y2, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 is connected to loc_x4_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 is connected to loc_x5_y3, loc_x5_y4 is connected to loc_x4_y4, the robot is currently at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3 and there is a connection between loc_x5_y3 and loc_x5_y2."}
{"question_id": "3520221f-4a02-4086-a4d2-8efcb202784e", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: from loc_x1_y0, the robot moves to loc_x0_y0, robot moves from loc_x0_y0 to loc_x0_y1, robot moves from loc_x0_y1 to loc_x1_y1, moves from loc_x1_y1 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x2_y0, moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, moves from loc_x3_y1 to loc_x3_y2, robot moves from loc_x3_y2 to loc_x3_y1 and robot moves from loc_x3_y1 to loc_x4_y1. Is the action: robot moves from loc_x1_y1 to loc_x2_y1 executable at step 4, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x5_y3 are connected, loc_x5_y4 is connected to loc_x4_y4, robot is at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3 and there is a connection between loc_x5_y3 and loc_x5_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 10: starting from loc_x1_y0, the robot will proceed to loc_x0_y0, then to loc_x0_y1, followed by loc_x1_y1, then loc_x2_y1, then loc_x2_y0, then loc_x3_y0, then loc_x3_y1, then loc_x3_y2, then back to loc_x3_y1, and finally to loc_x4_y1. Is the action of moving the robot from loc_x1_y1 to loc_x2_y1 executable at step 4, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x5_y3 are connected, loc_x5_y4 is connected to loc_x4_y4, the robot's current location is loc_x1_y0, a connection exists between loc_x0_y1 and loc_x1_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y3 and loc_x0_y4, a connection exists between loc_x0_y4 and loc_x1_y4, a connection exists between loc_x1_y0 and loc_x2_y0, a connection exists between loc_x1_y1 and loc_x0_y1, a connection exists between loc_x1_y1 and loc_x2_y1, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y3 and loc_x2_y2, a connection exists between loc_x3_y1 and loc_x4_y1, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, a connection exists between loc_x4_y0 and loc_x4_y1, a connection exists between loc_x4_y0 and loc_x5_y0, a connection exists between loc_x4_y1 and loc_x3_y1, a connection exists between loc_x4_y1 and loc_x5_y1, a connection exists between loc_x4_y2 and loc_x4_y1, a connection exists between loc_x4_y2 and loc_x5_y2, a connection exists between loc_x4_y3 and loc_x3_y3, a connection exists between loc_x4_y3 and loc_x4_y4, a connection exists between loc_x4_y3 and loc_x5_y3, a connection exists between loc_x4_y4 and loc_x3_y4, a connection exists between loc_x4_y4 and loc_x4_y3, a connection exists between loc_x4_y4 and loc_x5_y4, a connection exists between loc_x5_y1 and loc_x4_y1, a connection exists between loc_x5_y3 and loc_x4_y3, and a connection exists between loc_x5_y3 and loc_x5_y2."}
{"question_id": "1da4331e-8dc5-4527-ad93-0bc26321cfff", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: moves to loc_x0_y1 from loc_x0_y2, robot moves from loc_x0_y1 to loc_x0_y0, moves from loc_x0_y0 to loc_x1_y0, moves to loc_x1_y1 from loc_x1_y0, moves to loc_x1_y2 from loc_x1_y1, robot moves from loc_x1_y2 to loc_x0_y2, robot moves from loc_x0_y2 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x1_y2 to loc_x1_y5 and moves to loc_x1_y5 from loc_x0_y5. Is the action: from loc_x1_y2, the robot moves to loc_x1_y5 executable at step 9, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x0_y2, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y5, loc_x0_y5 and loc_x0_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x2_y2, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: the robot will move from loc_x0_y2 to loc_x0_y1, then from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y0 to loc_x1_y0, then to loc_x1_y1 from loc_x1_y0, and to loc_x1_y2 from loc_x1_y1. Next, the robot will move from loc_x1_y2 to loc_x0_y2, then to loc_x0_y3 from loc_x0_y2, to loc_x0_y4 from loc_x0_y3, and from loc_x1_y2 to loc_x1_y5. Lastly, the robot will move from loc_x0_y5 to loc_x1_y5. Is the action of moving from loc_x1_y2 to loc_x1_y5 executable at step 9, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y1 to loc_x0_y2, loc_x0_y1 to loc_x1_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y2 to loc_x0_y3, loc_x0_y2 to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3, loc_x0_y3 to loc_x0_y4, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x0_y4 to loc_x0_y5, loc_x0_y4 and loc_x0_y5, loc_x0_y5 and loc_x1_y5, loc_x1_y0 to loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 to loc_x1_y3, loc_x1_y2 to loc_x2_y2, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 to loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 to loc_x0_y4, loc_x1_y4 to loc_x1_y5, loc_x1_y5 to loc_x2_y5, loc_x2_y0 to loc_x2_y1, loc_x2_y1 to loc_x1_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y2 to loc_x2_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x2_y5, loc_x2_y5 to loc_x1_y5, loc_x2_y5 to loc_x2_y4, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x3_y1, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y0, loc_x3_y1 to loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, the robot is at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y2, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x2_y4, loc_x1_y5 and loc_x0_y5, loc_x1_y5 and loc_x1_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x3_y4, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, and loc_x3_y3 and loc_x3_y4."}
{"question_id": "f0e55f34-26fe-4de2-b9eb-286513d9dfff", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from loc_x3_y1, the robot moves to loc_x3_y2, from loc_x5_y4, the robot moves to loc_x2_y3, moves to loc_x3_y1 from loc_x3_y0, robot moves from loc_x0_y0 to loc_x0_y1, robot moves from loc_x0_y1 to loc_x1_y1, robot moves from loc_x1_y0 to loc_x0_y0, robot moves from loc_x1_y1 to loc_x2_y1, robot moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x2_y1 to loc_x2_y0 and robot moves from loc_x3_y2 to loc_x3_y1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x5_y3 are connected, loc_x5_y4 is connected to loc_x4_y4, robot is at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3 and there is a connection between loc_x5_y3 and loc_x5_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled: the robot will move from loc_x3_y1 to loc_x3_y2, then from loc_x5_y4 to loc_x2_y3, and from loc_x3_y0 to loc_x3_y1. Additionally, the robot will move from loc_x0_y0 to loc_x0_y1, then to loc_x1_y1, and from loc_x1_y0 back to loc_x0_y0. The robot will also move from loc_x1_y1 to loc_x2_y1, from loc_x2_y0 to loc_x3_y0, from loc_x2_y1 to loc_x2_y0, and finally from loc_x3_y2 to loc_x3_y1. Can this sequence be executed, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x0_y0, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is also connected to loc_x1_y2, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 is connected to loc_x4_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 is connected to loc_x5_y3, loc_x5_y4 is connected to loc_x4_y4, the robot is currently at loc_x1_y0, a connection exists between loc_x0_y1 and loc_x1_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y3 and loc_x0_y4, a connection exists between loc_x0_y4 and loc_x1_y4, a connection exists between loc_x1_y0 and loc_x2_y0, a connection exists between loc_x1_y1 and loc_x0_y1, a connection exists between loc_x1_y1 and loc_x2_y1, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y3 and loc_x2_y2, a connection exists between loc_x3_y1 and loc_x4_y1, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, a connection exists between loc_x4_y0 and loc_x4_y1, a connection exists between loc_x4_y0 and loc_x5_y0, a connection exists between loc_x4_y1 and loc_x3_y1, a connection exists between loc_x4_y1 and loc_x5_y1, a connection exists between loc_x4_y2 and loc_x4_y1, a connection exists between loc_x4_y2 and loc_x5_y2, a connection exists between loc_x4_y3 and loc_x3_y3, a connection exists between loc_x4_y3 and loc_x4_y4, a connection exists between loc_x4_y3 and loc_x5_y3, a connection exists between loc_x4_y4 and loc_x3_y4, a connection exists between loc_x4_y4 and loc_x4_y3, a connection exists between loc_x4_y4 and loc_x5_y4, a connection exists between loc_x5_y1 and loc_x4_y1, a connection exists between loc_x5_y3 and loc_x4_y3, and a connection exists between loc_x5_y3 and loc_x5_y2."}
{"question_id": "7c18ea0a-1f4a-44cd-8f8e-b3ba8a43a9f1", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot moves from loc_x1_y1 to loc_x2_y4. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x0_y2, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y5, loc_x0_y5 and loc_x0_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x2_y2, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the proposed actions are as follows: the robot will move from loc_x1_y1 to loc_x2_y4. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y1 to loc_x0_y2, loc_x0_y1 to loc_x1_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y2 to loc_x0_y3, loc_x0_y2 to loc_x1_y2, loc_x0_y2 has been visited, loc_x0_y3 and loc_x1_y3, loc_x0_y3 to loc_x0_y4, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x0_y4 to loc_x0_y5, loc_x0_y4 and loc_x0_y5, loc_x0_y5 and loc_x1_y5, loc_x1_y0 to loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 to loc_x1_y3, loc_x1_y2 to loc_x2_y2, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 to loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 to loc_x0_y4, loc_x1_y4 to loc_x1_y5, loc_x1_y5 to loc_x2_y5, loc_x2_y0 to loc_x2_y1, loc_x2_y1 to loc_x1_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y2 to loc_x2_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x2_y5, loc_x2_y5 to loc_x1_y5, loc_x2_y5 to loc_x2_y4, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x3_y1, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y0, loc_x3_y1 to loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, the robot's current location is loc_x0_y2, a connection exists between loc_x0_y0 and loc_x1_y0, a connection exists between loc_x0_y1 and loc_x0_y0, a connection exists between loc_x0_y3 and loc_x0_y2, a connection exists between loc_x1_y0 and loc_x0_y0, a connection exists between loc_x1_y0 and loc_x1_y1, a connection exists between loc_x1_y1 and loc_x1_y0, a connection exists between loc_x1_y1 and loc_x1_y2, a connection exists between loc_x1_y1 and loc_x2_y1, a connection exists between loc_x1_y2 and loc_x1_y1, a connection exists between loc_x1_y3 and loc_x1_y2, a connection exists between loc_x1_y4 and loc_x2_y4, a connection exists between loc_x1_y5 and loc_x0_y5, a connection exists between loc_x1_y5 and loc_x1_y4, a connection exists between loc_x2_y0 and loc_x1_y0, a connection exists between loc_x2_y0 and loc_x3_y0, a connection exists between loc_x2_y1 and loc_x2_y0, a connection exists between loc_x2_y1 and loc_x2_y2, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y2 and loc_x1_y2, a connection exists between loc_x2_y2 and loc_x2_y1, a connection exists between loc_x2_y3 and loc_x1_y3, a connection exists between loc_x2_y3 and loc_x2_y2, a connection exists between loc_x2_y4 and loc_x1_y4, a connection exists between loc_x2_y4 and loc_x3_y4, a connection exists between loc_x3_y2 and loc_x3_y3, a connection exists between loc_x3_y3 and loc_x2_y3, and a connection exists between loc_x3_y3 and loc_x3_y4."}
{"question_id": "bd7cf521-cc0f-4dd0-b3e1-971a1536fc39", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: from loc_x0_y3, the robot moves to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y3, moves from loc_x0_y3 to loc_x1_y3, moves to loc_x1_y4 from loc_x1_y3, robot moves from loc_x1_y4 to loc_x2_y4, moves to loc_x3_y4 from loc_x2_y4, from loc_x3_y4, the robot moves to loc_x3_y3, robot moves from loc_x3_y3 to loc_x2_y3, moves from loc_x2_y3 to loc_x2_y2 and moves to loc_x2_y1 from loc_x2_y2. Is the action: moves to loc_x2_y1 from loc_x2_y2 executable at step 10, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed for steps 1 through 10: starting from loc_x0_y3, the robot will move to loc_x0_y4, then return to loc_x0_y3, proceed to loc_x1_y3, move up to loc_x1_y4, then continue to loc_x2_y4, followed by loc_x3_y4, then move down to loc_x3_y3, backtrack to loc_x2_y3, move down to loc_x2_y2, and finally move down to loc_x2_y1 from loc_x2_y2. Is the action of moving to loc_x2_y1 from loc_x2_y2 executable at step 10, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "f5884ac0-cfbf-4c41-be44-a15cbb9e6520", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from loc_x1_y3, the robot moves to loc_x0_y3, from loc_x3_y0, the robot moves to loc_x2_y0, from loc_x4_y0, the robot moves to loc_x3_y0, moves from loc_x0_y0 to loc_x0_y1, moves from loc_x1_y4 to loc_x2_y4, moves from loc_x2_y1 to loc_x3_y1, moves from loc_x2_y4 to loc_x2_y3, moves from loc_x4_y1 to loc_x4_y0, moves to loc_x0_y0 from loc_x1_y0, moves to loc_x1_y1 from loc_x0_y1, moves to loc_x1_y2 from loc_x1_y1, robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x1_y4, robot moves from loc_x1_y2 to loc_x1_y3, robot moves from loc_x2_y0 to loc_x1_y0, robot moves from loc_x2_y2 to loc_x2_y1, robot moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x3_y2 to loc_x2_y2 and robot moves from loc_x4_y2 to loc_x3_y2. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, robot is at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y4 and loc_x4_y4 and there is a connection between loc_x4_y1 and loc_x3_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled: starting from loc_x1_y3, the robot will proceed to loc_x0_y3, then from loc_x3_y0, it will move to loc_x2_y0, and from loc_x4_y0, it will move to loc_x3_y0. Additionally, the robot will move from loc_x0_y0 to loc_x0_y1, from loc_x1_y4 to loc_x2_y4, from loc_x2_y1 to loc_x3_y1, from loc_x2_y4 to loc_x2_y3, and from loc_x4_y1 to loc_x4_y0. Furthermore, the robot will move from loc_x1_y0 to loc_x0_y0, from loc_x0_y1 to loc_x1_y1, and from loc_x1_y1 to loc_x1_y2. The robot will also move from loc_x0_y3 to loc_x0_y4, then to loc_x1_y4, and from loc_x1_y2 to loc_x1_y3. Moreover, the robot will move from loc_x2_y0 to loc_x1_y0, from loc_x2_y2 to loc_x2_y1, from loc_x3_y1 to loc_x4_y1, from loc_x3_y2 to loc_x2_y2, and from loc_x4_y2 to loc_x3_y2. Is the execution of these actions possible, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y0 to loc_x1_y0, loc_x0_y1 to loc_x1_y1, loc_x0_y3 to loc_x0_y4, loc_x0_y3 to loc_x1_y3, loc_x0_y4 to loc_x0_y3, loc_x1_y0 to loc_x0_y0, loc_x1_y0 to loc_x2_y0, loc_x1_y0 to loc_x1_y1, loc_x1_y1 to loc_x0_y1, loc_x1_y1 to loc_x1_y2, loc_x1_y1 to loc_x2_y1, loc_x1_y2 to loc_x2_y2, loc_x1_y3 to loc_x0_y3, loc_x1_y3 to loc_x2_y3, loc_x1_y3 to loc_x1_y2, loc_x1_y4 to loc_x0_y4, loc_x2_y0 to loc_x2_y1, loc_x2_y0 to loc_x1_y0, loc_x2_y1 to loc_x2_y2, loc_x2_y2 to loc_x2_y1, loc_x2_y2 to loc_x2_y3, loc_x2_y2 to loc_x1_y2, loc_x2_y2 to loc_x3_y2, loc_x2_y3 to loc_x2_y2, loc_x2_y3 to loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y3 to loc_x1_y3, loc_x2_y4 to loc_x1_y4, loc_x2_y4 to loc_x3_y4, loc_x3_y0 to loc_x3_y1, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x4_y0, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y2, loc_x3_y1 to loc_x4_y1, loc_x3_y2 to loc_x2_y2, loc_x3_y2 to loc_x3_y3, loc_x3_y2 to loc_x4_y2, loc_x3_y3 to loc_x3_y2, loc_x3_y3 to loc_x2_y3, loc_x3_y3 to loc_x3_y4, loc_x3_y4 to loc_x3_y3, loc_x3_y4 to loc_x2_y4, loc_x4_y0 to loc_x4_y1, loc_x4_y0 to loc_x3_y0, loc_x4_y1 to loc_x4_y0, loc_x4_y1 to loc_x4_y2, loc_x4_y2 to loc_x3_y2, loc_x4_y2 to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 to loc_x3_y4, the robot is at loc_x4_y2. Additionally, the following connections exist: loc_x0_y1 to loc_x0_y0, loc_x0_y4 to loc_x1_y4, loc_x1_y1 to loc_x1_y0, loc_x1_y2 to loc_x1_y1, loc_x1_y2 to loc_x1_y3, loc_x1_y3 to loc_x1_y4, loc_x1_y4 to loc_x1_y3, loc_x1_y4 to loc_x2_y4, loc_x2_y0 to loc_x3_y0, loc_x2_y1 to loc_x1_y1, loc_x2_y1 to loc_x2_y0, loc_x2_y1 to loc_x3_y1, loc_x2_y4 to loc_x2_y3, loc_x3_y1 to loc_x3_y0, loc_x3_y2 to loc_x3_y1, loc_x3_y4 to loc_x4_y4, and loc_x4_y1 to loc_x3_y1."}
{"question_id": "678b351f-af44-45a1-9c1c-76af94fa7313", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from loc_x4_y2, the robot moves to loc_x3_y2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, robot is at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y4 and loc_x4_y4 and there is a connection between loc_x4_y1 and loc_x3_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: the robot will move from loc_x4_y2 to loc_x3_y2. Is the execution of this action feasible, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y0 to loc_x1_y0, loc_x0_y1 to loc_x1_y1, loc_x0_y3 to loc_x0_y4, loc_x0_y3 to loc_x1_y3, loc_x0_y4 to loc_x0_y3, loc_x1_y0 to loc_x0_y0, loc_x1_y0 to loc_x2_y0, loc_x1_y0 to loc_x1_y1, loc_x1_y1 to loc_x0_y1, loc_x1_y1 to loc_x1_y2, loc_x1_y1 to loc_x2_y1, loc_x1_y2 to loc_x2_y2, loc_x1_y3 to loc_x0_y3, loc_x1_y3 to loc_x2_y3, loc_x1_y3 to loc_x1_y2, loc_x1_y4 to loc_x0_y4, loc_x2_y0 to loc_x2_y1, loc_x2_y0 to loc_x1_y0, loc_x2_y1 to loc_x2_y2, loc_x2_y2 to loc_x2_y1, loc_x2_y2 to loc_x2_y3, loc_x2_y2 to loc_x1_y2, loc_x2_y2 to loc_x3_y2, loc_x2_y3 to loc_x2_y2, loc_x2_y3 to loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y3 to loc_x1_y3, loc_x2_y4 to loc_x1_y4, loc_x2_y4 to loc_x3_y4, loc_x3_y0 to loc_x3_y1, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x4_y0, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y2, loc_x3_y1 to loc_x4_y1, loc_x3_y2 to loc_x2_y2, loc_x3_y2 to loc_x3_y3, loc_x3_y2 to loc_x4_y2, loc_x3_y3 to loc_x3_y2, loc_x3_y3 to loc_x2_y3, loc_x3_y3 to loc_x3_y4, loc_x3_y4 to loc_x3_y3, loc_x3_y4 to loc_x2_y4, loc_x4_y0 to loc_x4_y1, loc_x4_y0 to loc_x3_y0, loc_x4_y1 to loc_x4_y0, loc_x4_y1 to loc_x4_y2, loc_x4_y2 to loc_x3_y2, loc_x4_y2 to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 to loc_x3_y4, the robot is at loc_x4_y2. Additionally, the following connections exist: loc_x0_y1 and loc_x0_y0, loc_x0_y4 and loc_x1_y4, loc_x1_y1 and loc_x1_y0, loc_x1_y2 and loc_x1_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y4 and loc_x2_y3, loc_x3_y1 and loc_x3_y0, loc_x3_y2 and loc_x3_y1, loc_x3_y4 and loc_x4_y4, and loc_x4_y1 and loc_x3_y1."}
{"question_id": "af8ae775-95e7-4c9a-b08a-cd256d988abd", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from loc_x0_y3, the robot moves to loc_x1_y3, from loc_x1_y4, the robot moves to loc_x2_y4, from loc_x2_y2, the robot moves to loc_x2_y1, moves from loc_x2_y3 to loc_x2_y2, moves from loc_x3_y3 to loc_x2_y3, moves to loc_x0_y3 from loc_x0_y4, moves to loc_x0_y4 from loc_x0_y3, robot moves from loc_x1_y3 to loc_x1_y4, robot moves from loc_x2_y4 to loc_x3_y4 and robot moves from loc_x3_y4 to loc_x3_y3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled: the robot will move from loc_x0_y3 to loc_x1_y3, then from loc_x1_y4 to loc_x2_y4, followed by a move from loc_x2_y2 to loc_x2_y1, then from loc_x2_y3 to loc_x2_y2, and from loc_x3_y3 to loc_x2_y3. Additionally, the robot will move from loc_x0_y4 to loc_x0_y3, then from loc_x0_y3 to loc_x0_y4, from loc_x1_y3 to loc_x1_y4, from loc_x2_y4 to loc_x3_y4, and finally from loc_x3_y4 to loc_x3_y3. Can this sequence be executed, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "8e57f140-2bc4-42f3-8963-1d0f74227f63", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: moves from loc_x4_y2 to loc_x3_y2, robot moves from loc_x3_y2 to loc_x2_y2, from loc_x2_y2, the robot moves to loc_x2_y1, moves from loc_x2_y1 to loc_x3_y1, from loc_x4_y4, the robot moves to loc_x0_y3, from loc_x4_y1, the robot moves to loc_x4_y0, moves from loc_x4_y0 to loc_x3_y0, moves to loc_x2_y0 from loc_x3_y0, robot moves from loc_x2_y0 to loc_x1_y0 and from loc_x1_y0, the robot moves to loc_x0_y0. Is the action: moves to loc_x0_y3 from loc_x4_y4 executable at step 5, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, robot is at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y4 and loc_x4_y4 and there is a connection between loc_x4_y1 and loc_x3_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: the robot will move from loc_x4_y2 to loc_x3_y2, then from loc_x3_y2 to loc_x2_y2, followed by a move from loc_x2_y2 to loc_x2_y1, then from loc_x2_y1 to loc_x3_y1, from loc_x4_y4 to loc_x0_y3, from loc_x4_y1 to loc_x4_y0, from loc_x4_y0 to loc_x3_y0, from loc_x3_y0 to loc_x2_y0, from loc_x2_y0 to loc_x1_y0, and finally from loc_x1_y0 to loc_x0_y0. Is the action of moving from loc_x4_y4 to loc_x0_y3 executable at step 5, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y0 to loc_x1_y0, loc_x0_y1 to loc_x1_y1, loc_x0_y3 to loc_x0_y4, loc_x0_y3 to loc_x1_y3, loc_x0_y4 to loc_x0_y3, loc_x1_y0 to loc_x0_y0, loc_x1_y0 to loc_x2_y0, loc_x1_y0 to loc_x1_y1, loc_x1_y1 to loc_x0_y1, loc_x1_y1 to loc_x1_y2, loc_x1_y1 to loc_x2_y1, loc_x1_y2 to loc_x2_y2, loc_x1_y3 to loc_x0_y3, loc_x1_y3 to loc_x2_y3, loc_x1_y3 to loc_x1_y2, loc_x1_y4 to loc_x0_y4, loc_x2_y0 to loc_x2_y1, loc_x2_y0 to loc_x1_y0, loc_x2_y1 to loc_x2_y2, loc_x2_y2 to loc_x2_y1, loc_x2_y2 to loc_x2_y3, loc_x2_y2 to loc_x1_y2, loc_x2_y2 to loc_x3_y2, loc_x2_y3 to loc_x2_y2, loc_x2_y3 to loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y3 to loc_x1_y3, loc_x2_y4 to loc_x1_y4, loc_x2_y4 to loc_x3_y4, loc_x3_y0 to loc_x3_y1, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x4_y0, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y2, loc_x3_y1 to loc_x4_y1, loc_x3_y2 to loc_x2_y2, loc_x3_y2 to loc_x3_y3, loc_x3_y2 to loc_x4_y2, loc_x3_y3 to loc_x3_y2, loc_x3_y3 to loc_x2_y3, loc_x3_y3 to loc_x3_y4, loc_x3_y4 to loc_x3_y3, loc_x3_y4 to loc_x2_y4, loc_x4_y0 to loc_x4_y1, loc_x4_y0 to loc_x3_y0, loc_x4_y1 to loc_x4_y0, loc_x4_y1 to loc_x4_y2, loc_x4_y2 to loc_x3_y2, loc_x4_y2 to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 to loc_x3_y4, the robot is at loc_x4_y2. Additionally, the following connections exist: loc_x0_y1 and loc_x0_y0, loc_x0_y4 and loc_x1_y4, loc_x1_y1 and loc_x1_y0, loc_x1_y2 and loc_x1_y1, loc_x1_y2 and loc_x1_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y4 and loc_x1_y3, loc_x1_y4 and loc_x2_y4, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x1_y1, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x3_y1, loc_x2_y4 and loc_x2_y3, loc_x3_y1 and loc_x3_y0, loc_x3_y2 and loc_x3_y1, loc_x3_y4 and loc_x4_y4, and loc_x4_y1 and loc_x3_y1."}
{"question_id": "982f3807-d6bb-4ff9-967e-505d0abeb36b", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from loc_x5_y1, the robot moves to loc_x5_y2, from loc_x5_y2, the robot moves to loc_x4_y2, from loc_x5_y4, the robot moves to loc_x4_y4, moves from loc_x3_y0 to loc_x3_y1, moves from loc_x3_y2 to loc_x3_y1, moves from loc_x4_y1 to loc_x4_y0, moves from loc_x5_y3 to loc_x5_y4, moves to loc_x0_y0 from loc_x1_y0, moves to loc_x3_y2 from loc_x3_y1, moves to loc_x5_y3 from loc_x4_y3, robot moves from loc_x0_y0 to loc_x0_y1, robot moves from loc_x0_y1 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x2_y1, robot moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x2_y1 to loc_x2_y0, robot moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x4_y0 to loc_x5_y0, robot moves from loc_x4_y2 to loc_x4_y3 and robot moves from loc_x5_y0 to loc_x5_y1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x5_y3 are connected, loc_x5_y4 is connected to loc_x4_y4, robot is at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3 and there is a connection between loc_x5_y3 and loc_x5_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: starting from loc_x5_y1, the robot will proceed to loc_x5_y2, then from loc_x5_y2 to loc_x4_y2, from loc_x5_y4 to loc_x4_y4, from loc_x3_y0 to loc_x3_y1, from loc_x3_y2 to loc_x3_y1, from loc_x4_y1 to loc_x4_y0, from loc_x5_y3 to loc_x5_y4, from loc_x1_y0 to loc_x0_y0, from loc_x3_y1 to loc_x3_y2, from loc_x4_y3 to loc_x5_y3, the robot will move from loc_x0_y0 to loc_x0_y1, from loc_x0_y1 to loc_x1_y1, from loc_x1_y1 to loc_x2_y1, from loc_x2_y0 to loc_x3_y0, from loc_x2_y1 to loc_x2_y0, from loc_x3_y1 to loc_x4_y1, from loc_x4_y0 to loc_x5_y0, from loc_x4_y2 to loc_x4_y3, and from loc_x5_y0 to loc_x5_y1. Is the execution of these actions feasible, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x0_y0, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is also connected to loc_x1_y2, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 is connected to loc_x4_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 is connected to loc_x5_y3, loc_x5_y4 is connected to loc_x4_y4, the robot is currently at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3 and there is a connection between loc_x5_y3 and loc_x5_y2."}
{"question_id": "cf1e5d70-38a6-4ff5-a055-9b9cfa3e6535", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: moves from loc_x0_y3 to loc_x0_y4, from loc_x0_y4, the robot moves to loc_x0_y3, moves to loc_x1_y3 from loc_x0_y3, moves to loc_x1_y4 from loc_x1_y3, from loc_x1_y4, the robot moves to loc_x2_y4, moves to loc_x3_y4 from loc_x2_y4, moves from loc_x3_y4 to loc_x3_y3, from loc_x3_y3, the robot moves to loc_x2_y3, moves from loc_x4_y2 to loc_x1_y4, robot moves from loc_x2_y2 to loc_x2_y1, moves from loc_x2_y1 to loc_x1_y1, from loc_x1_y1, the robot moves to loc_x0_y1, from loc_x0_y1, the robot moves to loc_x0_y0, moves to loc_x1_y0 from loc_x0_y0, robot moves from loc_x1_y0 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, moves from loc_x3_y0 to loc_x3_y1, moves from loc_x3_y1 to loc_x3_y2 and from loc_x3_y2, the robot moves to loc_x4_y2. Is the action: robot moves from loc_x4_y2 to loc_x1_y4 executable at step 9, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 19: the robot will move from loc_x0_y3 to loc_x0_y4, then back to loc_x0_y3, then to loc_x1_y3, followed by a move to loc_x1_y4, then to loc_x2_y4, then to loc_x3_y4, then to loc_x3_y3, then to loc_x2_y3, then from loc_x4_y2 to loc_x1_y4, then from loc_x2_y2 to loc_x2_y1, then to loc_x1_y1, then to loc_x0_y1, then to loc_x0_y0, then to loc_x1_y0, then to loc_x2_y0, then to loc_x3_y0, then to loc_x3_y1, then to loc_x3_y2, and finally from loc_x3_y2 to loc_x4_y2. Is the action: robot moves from loc_x4_y2 to loc_x1_y4 executable at step 9, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "a359c1b5-887f-4699-824c-8ac924aa914e", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from loc_x0_y0, the robot moves to loc_x1_y0, from loc_x1_y0, the robot moves to loc_x1_y1, moves from loc_x0_y2 to loc_x0_y1, moves from loc_x0_y2 to loc_x0_y3, moves from loc_x0_y4 to loc_x0_y5, moves from loc_x1_y4 to loc_x1_y3, moves from loc_x2_y1 to loc_x2_y0, moves to loc_x0_y4 from loc_x0_y3, robot moves from loc_x0_y1 to loc_x0_y0, robot moves from loc_x0_y5 to loc_x1_y5, robot moves from loc_x1_y1 to loc_x1_y2, robot moves from loc_x1_y2 to loc_x0_y2, robot moves from loc_x1_y3 to loc_x2_y3, robot moves from loc_x1_y5 to loc_x1_y4, robot moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x2_y2 to loc_x2_y1, robot moves from loc_x2_y3 to loc_x2_y2, robot moves from loc_x3_y0 to loc_x3_y1 and robot moves from loc_x3_y1 to loc_x3_y2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x0_y2, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y5, loc_x0_y5 and loc_x0_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x2_y2, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: starting from loc_x0_y0, the robot will proceed to loc_x1_y0, then from loc_x1_y0 to loc_x1_y1, followed by movements from loc_x0_y2 to loc_x0_y1, loc_x0_y2 to loc_x0_y3, loc_x0_y4 to loc_x0_y5, loc_x1_y4 to loc_x1_y3, loc_x2_y1 to loc_x2_y0, loc_x0_y3 to loc_x0_y4, loc_x0_y1 to loc_x0_y0, loc_x0_y5 to loc_x1_y5, loc_x1_y1 to loc_x1_y2, loc_x1_y2 to loc_x0_y2, loc_x1_y3 to loc_x2_y3, loc_x1_y5 to loc_x1_y4, loc_x2_y0 to loc_x3_y0, loc_x2_y2 to loc_x2_y1, loc_x2_y3 to loc_x2_y2, loc_x3_y0 to loc_x3_y1, and finally from loc_x3_y1 to loc_x3_y2. Is the execution of this sequence possible, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y1 to loc_x0_y2, loc_x0_y1 to loc_x1_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y2 to loc_x0_y3, loc_x0_y2 to loc_x1_y2, loc_x0_y2 has been visited, loc_x0_y3 and loc_x1_y3, loc_x0_y3 to loc_x0_y4, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x0_y4 to loc_x0_y5, loc_x0_y4 and loc_x0_y5, loc_x0_y5 and loc_x1_y5, loc_x1_y0 to loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 to loc_x1_y3, loc_x1_y2 to loc_x2_y2, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 to loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 to loc_x0_y4, loc_x1_y4 to loc_x1_y5, loc_x1_y5 to loc_x2_y5, loc_x2_y0 to loc_x2_y1, loc_x2_y1 to loc_x1_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y2 to loc_x2_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x2_y5, loc_x2_y5 to loc_x1_y5, loc_x2_y5 to loc_x2_y4, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x3_y1, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y0, loc_x3_y1 to loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3. The robot is currently at loc_x0_y2. Additionally, the following connections exist: loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y2, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x2_y4, loc_x1_y5 and loc_x0_y5, loc_x1_y5 and loc_x1_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x3_y4, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, and loc_x3_y3 and loc_x3_y4."}
{"question_id": "309571fe-77b3-4cff-b4de-1bf607cc3263", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: moves from loc_x0_y3 to loc_x0_y4, from loc_x0_y4, the robot moves to loc_x0_y3, robot moves from loc_x0_y3 to loc_x1_y3, moves to loc_x1_y4 from loc_x1_y3, moves from loc_x1_y4 to loc_x2_y4, moves to loc_x3_y4 from loc_x2_y4, from loc_x3_y4, the robot moves to loc_x3_y3, robot moves from loc_x3_y3 to loc_x2_y3, moves to loc_x2_y2 from loc_x2_y3, moves to loc_x2_y1 from loc_x2_y2, from loc_x2_y1, the robot moves to loc_x1_y1, robot moves from loc_x1_y1 to loc_x0_y1, from loc_x0_y1, the robot moves to loc_x0_y0, moves from loc_x0_y0 to loc_x1_y0, moves from loc_x1_y0 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, moves from loc_x3_y0 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x3_y2 and moves to loc_x4_y2 from loc_x3_y2. Is the action: robot moves from loc_x0_y0 to loc_x1_y0 executable at step 14, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 19: the robot will move from loc_x0_y3 to loc_x0_y4, then back to loc_x0_y3, followed by a move to loc_x1_y3, then to loc_x1_y4, then to loc_x2_y4, then to loc_x3_y4, then back to loc_x3_y3, then to loc_x2_y3, then to loc_x2_y2, then to loc_x2_y1, then to loc_x1_y1, then to loc_x0_y1, then to loc_x0_y0, then to loc_x1_y0, then to loc_x2_y0, then to loc_x3_y0, then to loc_x3_y1, then to loc_x3_y2, and finally to loc_x4_y2 from loc_x3_y2. Is the action of moving the robot from loc_x0_y0 to loc_x1_y0 executable at step 14, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "98fdb253-e54a-408a-af6e-ae19b3692d53", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: moves to loc_x0_y1 from loc_x0_y2, robot moves from loc_x0_y1 to loc_x0_y0, moves from loc_x0_y0 to loc_x1_y0, moves from loc_x1_y0 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x1_y2, robot moves from loc_x1_y2 to loc_x0_y2, robot moves from loc_x0_y2 to loc_x0_y3, from loc_x0_y3, the robot moves to loc_x0_y4, moves to loc_x0_y5 from loc_x0_y4 and moves to loc_x1_y5 from loc_x0_y5. Is the action: moves from loc_x1_y1 to loc_x1_y2 executable at step 5, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x0_y2, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y5, loc_x0_y5 and loc_x0_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x2_y2, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: the robot will move from loc_x0_y2 to loc_x0_y1, then from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y0 to loc_x1_y0, then from loc_x1_y0 to loc_x1_y1, next from loc_x1_y1 to loc_x1_y2, then from loc_x1_y2 to loc_x0_y2, followed by a move from loc_x0_y2 to loc_x0_y3, then from loc_x0_y3 to loc_x0_y4, from loc_x0_y4 to loc_x0_y5, and finally from loc_x0_y5 to loc_x1_y5. Is the action of moving from loc_x1_y1 to loc_x1_y2 executable at step 5, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y1 to loc_x0_y2, loc_x0_y1 to loc_x1_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y2 to loc_x0_y3, loc_x0_y2 to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3, loc_x0_y3 to loc_x0_y4, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x0_y4 to loc_x0_y5, loc_x0_y4 and loc_x0_y5, loc_x0_y5 and loc_x1_y5, loc_x1_y0 to loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 to loc_x1_y3, loc_x1_y2 to loc_x2_y2, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 to loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 to loc_x0_y4, loc_x1_y4 to loc_x1_y5, loc_x1_y5 to loc_x2_y5, loc_x2_y0 to loc_x2_y1, loc_x2_y1 to loc_x1_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y2 to loc_x2_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x2_y5, loc_x2_y5 to loc_x1_y5, loc_x2_y5 to loc_x2_y4, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x3_y1, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y0, loc_x3_y1 to loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, the robot is at loc_x0_y2, connections exist between loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y2, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x2_y4, loc_x1_y5 and loc_x0_y5, loc_x1_y5 and loc_x1_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x3_y4, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, and loc_x3_y3 and loc_x3_y4."}
{"question_id": "eeccd9f2-da66-40ee-92a8-bda363bbbd2a", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot moves from loc_x0_y3 to loc_x0_y4. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: the robot will move from loc_x0_y3 to loc_x0_y4. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "5feae563-f5be-4896-a2e5-0bc91c36cedc", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from loc_x0_y2, the robot moves to loc_x0_y3, from loc_x0_y5, the robot moves to loc_x1_y5, from loc_x2_y0, the robot moves to loc_x3_y0, from loc_x2_y2, the robot moves to loc_x2_y1, from loc_x3_y0, the robot moves to loc_x0_y1, moves to loc_x0_y0 from loc_x0_y1, moves to loc_x0_y1 from loc_x0_y2, moves to loc_x0_y2 from loc_x1_y2, moves to loc_x0_y5 from loc_x0_y4, moves to loc_x2_y0 from loc_x2_y1, robot moves from loc_x0_y0 to loc_x1_y0, robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x1_y0 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x1_y2, robot moves from loc_x1_y4 to loc_x1_y3, robot moves from loc_x1_y5 to loc_x1_y4, robot moves from loc_x2_y3 to loc_x2_y2, robot moves from loc_x3_y0 to loc_x3_y1 and robot moves from loc_x3_y1 to loc_x3_y2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x0_y2, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y5, loc_x0_y5 and loc_x0_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x2_y2, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are proposed: starting from loc_x0_y2, the robot will proceed to loc_x0_y3, from loc_x0_y5 it will move to loc_x1_y5, from loc_x2_y0 it will move to loc_x3_y0, from loc_x2_y2 it will move to loc_x2_y1, from loc_x3_y0 it will move to loc_x0_y1, then from loc_x0_y1 it will move to loc_x0_y0, from loc_x0_y1 it will move back to loc_x0_y2, from loc_x1_y2 it will move to loc_x0_y2, from loc_x0_y4 it will move to loc_x0_y5, from loc_x2_y1 it will move to loc_x2_y0, the robot will move from loc_x0_y0 to loc_x1_y0, from loc_x0_y3 to loc_x0_y4, from loc_x1_y0 to loc_x1_y1, from loc_x1_y1 to loc_x1_y2, from loc_x1_y4 to loc_x1_y3, from loc_x1_y5 to loc_x1_y4, from loc_x2_y3 to loc_x2_y2, from loc_x3_y0 to loc_x3_y1, and from loc_x3_y1 to loc_x3_y2. Is the execution of these actions possible, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y1 to loc_x0_y2, loc_x0_y1 to loc_x1_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y2 to loc_x0_y3, loc_x0_y2 to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3, loc_x0_y3 to loc_x0_y4, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x0_y4 to loc_x0_y5, loc_x0_y4 and loc_x0_y5, loc_x0_y5 and loc_x1_y5, loc_x1_y0 to loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 to loc_x1_y3, loc_x1_y2 to loc_x2_y2, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 to loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 to loc_x0_y4, loc_x1_y4 to loc_x1_y5, loc_x1_y5 to loc_x2_y5, loc_x2_y0 to loc_x2_y1, loc_x2_y1 to loc_x1_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y2 to loc_x2_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x2_y5, loc_x2_y5 to loc_x1_y5, loc_x2_y5 to loc_x2_y4, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x3_y1, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y0, loc_x3_y1 to loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, the robot is at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y2, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x2_y4, loc_x1_y5 and loc_x0_y5, loc_x1_y5 and loc_x1_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x3_y4, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, and loc_x3_y3 and loc_x3_y4."}
{"question_id": "a7e0f763-885d-4d4f-bc26-c56c74079f94", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from loc_x0_y4, the robot moves to loc_x0_y3, from loc_x2_y2, the robot moves to loc_x2_y1, moves from loc_x1_y4 to loc_x2_y4, robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y3 to loc_x1_y3, robot moves from loc_x1_y3 to loc_x1_y4, robot moves from loc_x2_y0 to loc_x2_y3, robot moves from loc_x2_y3 to loc_x2_y2, robot moves from loc_x3_y3 to loc_x2_y3 and robot moves from loc_x3_y4 to loc_x3_y3. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled: the robot will move from loc_x0_y4 to loc_x0_y3, then from loc_x2_y2 to loc_x2_y1, followed by a move from loc_x1_y4 to loc_x2_y4. Additionally, the robot will move from loc_x0_y3 to loc_x0_y4, then to loc_x1_y3, and from loc_x1_y3 to loc_x1_y4. The robot will also move from loc_x2_y0 to loc_x2_y3, then to loc_x2_y2, and from loc_x3_y3 to loc_x2_y3, and finally from loc_x3_y4 to loc_x3_y3. Is the execution of these actions possible, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "f931a4b5-dd5d-498e-ba9a-820240150860", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: moves from loc_x0_y2 to loc_x0_y3, moves from loc_x0_y4 to loc_x0_y5, moves to loc_x0_y0 from loc_x0_y1, moves to loc_x0_y1 from loc_x0_y2, moves to loc_x0_y4 from loc_x0_y3, moves to loc_x1_y0 from loc_x0_y0, robot moves from loc_x0_y5 to loc_x1_y5, robot moves from loc_x1_y0 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x1_y2 and robot moves from loc_x1_y2 to loc_x0_y2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x0_y2, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y5, loc_x0_y5 and loc_x0_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x2_y2, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: relocation from loc_x0_y2 to loc_x0_y3, relocation from loc_x0_y4 to loc_x0_y5, relocation from loc_x0_y1 to loc_x0_y0, relocation from loc_x0_y2 to loc_x0_y1, relocation from loc_x0_y3 to loc_x0_y4, relocation from loc_x0_y0 to loc_x1_y0, the robot relocates from loc_x0_y5 to loc_x1_y5, the robot relocates from loc_x1_y0 to loc_x1_y1, the robot relocates from loc_x1_y1 to loc_x1_y2, and the robot relocates from loc_x1_y2 to loc_x0_y2. Is the execution of these actions feasible, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y1 to loc_x0_y2, loc_x0_y1 to loc_x1_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y2 to loc_x0_y3, loc_x0_y2 to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3, loc_x0_y3 to loc_x0_y4, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x0_y4 to loc_x0_y5, loc_x0_y4 and loc_x0_y5, loc_x0_y5 and loc_x1_y5, loc_x1_y0 to loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 to loc_x1_y3, loc_x1_y2 to loc_x2_y2, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 to loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 to loc_x0_y4, loc_x1_y4 to loc_x1_y5, loc_x1_y5 to loc_x2_y5, loc_x2_y0 to loc_x2_y1, loc_x2_y1 to loc_x1_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y2 to loc_x2_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x2_y5, loc_x2_y5 to loc_x1_y5, loc_x2_y5 to loc_x2_y4, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x3_y1, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y0, loc_x3_y1 to loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, the robot is at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y2, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x2_y4, loc_x1_y5 and loc_x0_y5, loc_x1_y5 and loc_x1_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x3_y4, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, and loc_x3_y3 and loc_x3_y4."}
{"question_id": "1b0e7159-3ead-4f08-aab5-df51ef7ef2f0", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from loc_x3_y1, the robot moves to loc_x4_y1, moves from loc_x3_y0 to loc_x2_y0, moves to loc_x0_y0 from loc_x1_y0, moves to loc_x1_y2 from loc_x2_y1, moves to loc_x1_y3 from loc_x1_y2, moves to loc_x3_y0 from loc_x4_y0, robot moves from loc_x0_y0 to loc_x0_y1, robot moves from loc_x0_y1 to loc_x1_y1, robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x1_y4, robot moves from loc_x1_y3 to loc_x0_y3, robot moves from loc_x1_y4 to loc_x2_y4, robot moves from loc_x2_y0 to loc_x1_y0, robot moves from loc_x2_y1 to loc_x3_y1, robot moves from loc_x2_y2 to loc_x2_y1, robot moves from loc_x2_y4 to loc_x2_y3, robot moves from loc_x3_y2 to loc_x2_y2, robot moves from loc_x4_y1 to loc_x4_y0 and robot moves from loc_x4_y2 to loc_x3_y2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, robot is at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y4 and loc_x4_y4 and there is a connection between loc_x4_y1 and loc_x3_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled to take place: starting from loc_x3_y1, the robot will proceed to loc_x4_y1, then move from loc_x3_y0 to loc_x2_y0, followed by a move from loc_x1_y0 to loc_x0_y0, then from loc_x2_y1 to loc_x1_y2, then from loc_x1_y2 to loc_x1_y3, then from loc_x4_y0 to loc_x3_y0, the robot will move from loc_x0_y0 to loc_x0_y1, then from loc_x0_y1 to loc_x1_y1, then from loc_x0_y3 to loc_x0_y4, then from loc_x0_y4 to loc_x1_y4, then from loc_x1_y3 to loc_x0_y3, then from loc_x1_y4 to loc_x2_y4, then from loc_x2_y0 to loc_x1_y0, then from loc_x2_y1 to loc_x3_y1, then from loc_x2_y2 to loc_x2_y1, then from loc_x2_y4 to loc_x2_y3, then from loc_x3_y2 to loc_x2_y2, then from loc_x4_y1 to loc_x4_y0, and finally from loc_x4_y2 to loc_x3_y2. Is the execution of these actions possible, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y0 to loc_x1_y0, loc_x0_y1 to loc_x1_y1, loc_x0_y3 to loc_x0_y4, loc_x0_y3 to loc_x1_y3, loc_x0_y4 to loc_x0_y3, loc_x1_y0 to loc_x0_y0, loc_x1_y0 to loc_x2_y0, loc_x1_y0 to loc_x1_y1, loc_x1_y1 to loc_x0_y1, loc_x1_y1 to loc_x1_y2, loc_x1_y1 to loc_x2_y1, loc_x1_y2 to loc_x2_y2, loc_x1_y3 to loc_x0_y3, loc_x1_y3 to loc_x2_y3, loc_x1_y3 to loc_x1_y2, loc_x1_y4 to loc_x0_y4, loc_x2_y0 to loc_x2_y1, loc_x2_y0 to loc_x1_y0, loc_x2_y1 to loc_x2_y2, loc_x2_y2 to loc_x2_y1, loc_x2_y2 to loc_x2_y3, loc_x2_y2 to loc_x1_y2, loc_x2_y2 to loc_x3_y2, loc_x2_y3 to loc_x2_y2, loc_x2_y3 to loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y3 to loc_x1_y3, loc_x2_y4 to loc_x1_y4, loc_x2_y4 to loc_x3_y4, loc_x3_y0 to loc_x3_y1, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x4_y0, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y2, loc_x3_y1 to loc_x4_y1, loc_x3_y2 to loc_x2_y2, loc_x3_y2 to loc_x3_y3, loc_x3_y2 to loc_x4_y2, loc_x3_y3 to loc_x3_y2, loc_x3_y3 to loc_x2_y3, loc_x3_y3 to loc_x3_y4, loc_x3_y4 to loc_x3_y3, loc_x3_y4 to loc_x2_y4, loc_x4_y0 to loc_x4_y1, loc_x4_y0 to loc_x3_y0, loc_x4_y1 to loc_x4_y0, loc_x4_y1 to loc_x4_y2, loc_x4_y2 to loc_x3_y2, loc_x4_y2 to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 to loc_x3_y4, the robot is at loc_x4_y2. Additionally, the following connections exist: loc_x0_y1 to loc_x0_y0, loc_x0_y4 to loc_x1_y4, loc_x1_y1 to loc_x1_y0, loc_x1_y2 to loc_x1_y1, loc_x1_y2 to loc_x1_y3, loc_x1_y3 to loc_x1_y4, loc_x1_y4 to loc_x1_y3, loc_x1_y4 to loc_x2_y4, loc_x2_y0 to loc_x3_y0, loc_x2_y1 to loc_x1_y1, loc_x2_y1 to loc_x2_y0, loc_x2_y1 to loc_x3_y1, loc_x2_y4 to loc_x2_y3, loc_x3_y1 to loc_x3_y0, loc_x3_y2 to loc_x3_y1, loc_x3_y4 to loc_x4_y4, and loc_x4_y1 to loc_x3_y1."}
{"question_id": "70dc85f3-4235-4f35-81fe-91448bd82870", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: moves from loc_x2_y0 to loc_x5_y1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x5_y3 are connected, loc_x5_y4 is connected to loc_x4_y4, robot is at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3 and there is a connection between loc_x5_y3 and loc_x5_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the proposed actions are as follows: moving from loc_x2_y0 to loc_x5_y1. Can this be executed, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x0_y0, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is also connected to loc_x1_y2, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 is connected to loc_x4_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 is connected to loc_x5_y3, loc_x5_y4 is connected to loc_x4_y4, the robot is currently at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3, and there is a connection between loc_x5_y3 and loc_x5_y2."}
{"question_id": "41e65638-4404-41c3-bdfd-955e473e37ef", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: moves from loc_x1_y0 to loc_x0_y0, moves to loc_x0_y1 from loc_x0_y0, robot moves from loc_x0_y1 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x2_y1, moves from loc_x2_y1 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x3_y2, robot moves from loc_x3_y2 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, moves to loc_x5_y0 from loc_x4_y0, moves to loc_x1_y0 from loc_x5_y4, from loc_x5_y1, the robot moves to loc_x5_y2, from loc_x5_y2, the robot moves to loc_x4_y2, from loc_x4_y2, the robot moves to loc_x4_y3, from loc_x4_y3, the robot moves to loc_x5_y3, moves from loc_x5_y3 to loc_x5_y4 and from loc_x5_y4, the robot moves to loc_x4_y4. Is the action: moves from loc_x5_y4 to loc_x1_y0 executable at step 13, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x5_y3 are connected, loc_x5_y4 is connected to loc_x4_y4, robot is at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3 and there is a connection between loc_x5_y3 and loc_x5_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed for steps 1 through 19: the robot will move from loc_x1_y0 to loc_x0_y0, then to loc_x0_y1, followed by a move to loc_x1_y1, then to loc_x2_y1, then to loc_x2_y0, then to loc_x3_y0, then to loc_x3_y1, then to loc_x3_y2, then back to loc_x3_y1, then to loc_x4_y1, then to loc_x4_y0, then to loc_x5_y0, then to loc_x1_y0, then to loc_x5_y1, then to loc_x5_y2, then to loc_x4_y2, then to loc_x4_y3, then to loc_x5_y3, then to loc_x5_y4, and finally to loc_x4_y4. Is the action of moving from loc_x5_y4 to loc_x1_y0 executable at step 13, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x0_y0, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is also connected to loc_x1_y2, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 is connected to loc_x4_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 is connected to loc_x5_y3, loc_x5_y4 is connected to loc_x4_y4, the robot is currently at loc_x1_y0, a connection exists between loc_x0_y1 and loc_x1_y1, a connection exists between loc_x0_y2 and loc_x0_y3, a connection exists between loc_x0_y3 and loc_x0_y4, a connection exists between loc_x0_y4 and loc_x1_y4, a connection exists between loc_x1_y0 and loc_x2_y0, a connection exists between loc_x1_y1 and loc_x0_y1, a connection exists between loc_x1_y1 and loc_x2_y1, a connection exists between loc_x2_y1 and loc_x3_y1, a connection exists between loc_x2_y3 and loc_x2_y2, a connection exists between loc_x3_y1 and loc_x4_y1, a connection exists between loc_x3_y2 and loc_x3_y1, a connection exists between loc_x3_y3 and loc_x2_y3, a connection exists between loc_x3_y3 and loc_x3_y2, a connection exists between loc_x4_y0 and loc_x4_y1, a connection exists between loc_x4_y0 and loc_x5_y0, a connection exists between loc_x4_y1 and loc_x3_y1, a connection exists between loc_x4_y1 and loc_x5_y1, a connection exists between loc_x4_y2 and loc_x4_y1, a connection exists between loc_x4_y2 and loc_x5_y2, a connection exists between loc_x4_y3 and loc_x3_y3, a connection exists between loc_x4_y3 and loc_x4_y4, a connection exists between loc_x4_y3 and loc_x5_y3, a connection exists between loc_x4_y4 and loc_x3_y4, a connection exists between loc_x4_y4 and loc_x4_y3, a connection exists between loc_x4_y4 and loc_x5_y4, a connection exists between loc_x5_y1 and loc_x4_y1, a connection exists between loc_x5_y3 and loc_x4_y3, and a connection exists between loc_x5_y3 and loc_x5_y2."}
{"question_id": "5b9fdadf-90d0-4a99-9e26-20b6d6549b8b", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: moves from loc_x3_y3 to loc_x5_y1, moves to loc_x0_y1 from loc_x0_y0, robot moves from loc_x0_y1 to loc_x1_y1, moves from loc_x1_y1 to loc_x2_y1, moves from loc_x2_y1 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, moves from loc_x3_y0 to loc_x3_y1, moves from loc_x3_y1 to loc_x3_y2, from loc_x3_y2, the robot moves to loc_x3_y1 and moves from loc_x3_y1 to loc_x4_y1. Is the action: robot moves from loc_x3_y3 to loc_x5_y1 executable at step 1, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x5_y3 are connected, loc_x5_y4 is connected to loc_x4_y4, robot is at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3 and there is a connection between loc_x5_y3 and loc_x5_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: the robot will move from loc_x3_y3 to loc_x5_y1, then from loc_x0_y0 to loc_x0_y1, followed by a move from loc_x0_y1 to loc_x1_y1, then to loc_x2_y1, then to loc_x2_y0, then to loc_x3_y0, then to loc_x3_y1, then to loc_x3_y2, then back to loc_x3_y1, and finally to loc_x4_y1. Is the action of moving the robot from loc_x3_y3 to loc_x5_y1 executable at step 1, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x0_y0, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is also connected to loc_x1_y2, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 is connected to loc_x4_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 is connected to loc_x5_y3, loc_x5_y4 is connected to loc_x4_y4, the robot is currently at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3, and there is a connection between loc_x5_y3 and loc_x5_y2."}
{"question_id": "80c01cff-af43-4dc1-b937-82eef2f26e5b", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from loc_x0_y3, the robot moves to loc_x4_y4. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, robot is at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y4 and loc_x4_y4 and there is a connection between loc_x4_y1 and loc_x3_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: the robot will move from loc_x0_y3 to loc_x4_y4. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y0 to loc_x1_y0, loc_x0_y1 to loc_x1_y1, loc_x0_y3 to loc_x0_y4, loc_x0_y3 to loc_x1_y3, loc_x0_y4 to loc_x0_y3, loc_x1_y0 to loc_x0_y0, loc_x1_y0 to loc_x2_y0, loc_x1_y0 to loc_x1_y1, loc_x1_y1 to loc_x0_y1, loc_x1_y1 to loc_x1_y2, loc_x1_y1 to loc_x2_y1, loc_x1_y2 to loc_x2_y2, loc_x1_y3 to loc_x0_y3, loc_x1_y3 to loc_x2_y3, loc_x1_y3 to loc_x1_y2, loc_x1_y4 to loc_x0_y4, loc_x2_y0 to loc_x2_y1, loc_x2_y0 to loc_x1_y0, loc_x2_y1 to loc_x2_y2, loc_x2_y2 to loc_x2_y1, loc_x2_y2 to loc_x2_y3, loc_x2_y2 to loc_x1_y2, loc_x2_y2 to loc_x3_y2, loc_x2_y3 to loc_x2_y2, loc_x2_y3 to loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y3 to loc_x1_y3, loc_x2_y4 to loc_x1_y4, loc_x2_y4 to loc_x3_y4, loc_x3_y0 to loc_x3_y1, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x4_y0, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y2, loc_x3_y1 to loc_x4_y1, loc_x3_y2 to loc_x2_y2, loc_x3_y2 to loc_x3_y3, loc_x3_y2 to loc_x4_y2, loc_x3_y3 to loc_x3_y2, loc_x3_y3 to loc_x2_y3, loc_x3_y3 to loc_x3_y4, loc_x3_y4 to loc_x3_y3, loc_x3_y4 to loc_x2_y4, loc_x4_y0 to loc_x4_y1, loc_x4_y0 to loc_x3_y0, loc_x4_y1 to loc_x4_y0, loc_x4_y1 to loc_x4_y2, loc_x4_y2 to loc_x3_y2, loc_x4_y2 to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 to loc_x3_y4, the robot is at loc_x4_y2. Additionally, the following connections exist: loc_x0_y1 to loc_x0_y0, loc_x0_y4 to loc_x1_y4, loc_x1_y1 to loc_x1_y0, loc_x1_y2 to loc_x1_y1, loc_x1_y2 to loc_x1_y3, loc_x1_y3 to loc_x1_y4, loc_x1_y4 to loc_x1_y3, loc_x1_y4 to loc_x2_y4, loc_x2_y0 to loc_x3_y0, loc_x2_y1 to loc_x1_y1, loc_x2_y1 to loc_x2_y0, loc_x2_y1 to loc_x3_y1, loc_x2_y4 to loc_x2_y3, loc_x3_y1 to loc_x3_y0, loc_x3_y2 to loc_x3_y1, loc_x3_y4 to loc_x4_y4, and loc_x4_y1 to loc_x3_y1."}
{"question_id": "772745e7-b0a6-46cc-bed5-b0af386e2041", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot moves from loc_x3_y1 to loc_x1_y4. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: the robot will move from loc_x3_y1 to loc_x1_y4. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "e0b38538-89f1-43ee-b695-f9d1407d5475", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from loc_x2_y1, the robot moves to loc_x2_y0, moves from loc_x0_y1 to loc_x1_y1, moves from loc_x1_y0 to loc_x0_y0, moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x0_y0 to loc_x0_y1, robot moves from loc_x1_y1 to loc_x2_y1, robot moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x3_y0 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x3_y2 and robot moves from loc_x3_y2 to loc_x3_y1. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x5_y3 are connected, loc_x5_y4 is connected to loc_x4_y4, robot is at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3 and there is a connection between loc_x5_y3 and loc_x5_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled: the robot will move from loc_x2_y1 to loc_x2_y0, then from loc_x0_y1 to loc_x1_y1, followed by a move from loc_x1_y0 to loc_x0_y0, then from loc_x3_y1 to loc_x4_y1. Subsequent actions include moving from loc_x0_y0 to loc_x0_y1, from loc_x1_y1 to loc_x2_y1, from loc_x2_y0 to loc_x3_y0, from loc_x3_y0 to loc_x3_y1, from loc_x3_y1 to loc_x3_y2, and finally from loc_x3_y2 back to loc_x3_y1. Can this sequence be executed, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x0_y0, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is also connected to loc_x1_y2, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 is connected to loc_x4_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 is connected to loc_x5_y3, loc_x5_y4 is connected to loc_x4_y4, the robot is currently at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3, and there is a connection between loc_x5_y3 and loc_x5_y2."}
{"question_id": "5c3f1a43-5f39-40d4-a20b-6a3b3b4601c1", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: moves from loc_x0_y2 to loc_x0_y1, from loc_x0_y1, the robot moves to loc_x0_y0, moves to loc_x1_y0 from loc_x0_y0, robot moves from loc_x1_y0 to loc_x1_y1, robot moves from loc_x1_y1 to loc_x1_y2, from loc_x1_y2, the robot moves to loc_x0_y2, robot moves from loc_x0_y2 to loc_x0_y3, moves to loc_x0_y4 from loc_x0_y3, robot moves from loc_x0_y4 to loc_x0_y5, robot moves from loc_x0_y5 to loc_x1_y5, robot moves from loc_x1_y5 to loc_x1_y4, moves from loc_x1_y4 to loc_x1_y3, from loc_x1_y3, the robot moves to loc_x2_y3, robot moves from loc_x2_y3 to loc_x2_y2, from loc_x1_y3, the robot moves to loc_x2_y2, robot moves from loc_x2_y1 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x3_y0, moves from loc_x3_y0 to loc_x3_y1 and from loc_x3_y1, the robot moves to loc_x3_y2. Is the action: moves to loc_x2_y2 from loc_x1_y3 executable at step 15, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x0_y2, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y5, loc_x0_y5 and loc_x0_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x2_y2, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed for steps 1 through 19: the robot will move from loc_x0_y2 to loc_x0_y1, then from loc_x0_y1 to loc_x0_y0, followed by a move from loc_x0_y0 to loc_x1_y0, then to loc_x1_y1, then to loc_x1_y2, and from loc_x1_y2 back to loc_x0_y2. The robot will then proceed from loc_x0_y2 to loc_x0_y3, then to loc_x0_y4, followed by a move to loc_x0_y5, then to loc_x1_y5, then to loc_x1_y4, then to loc_x1_y3, and from loc_x1_y3 to loc_x2_y3. Next, the robot will move from loc_x2_y3 to loc_x2_y2, and then from loc_x1_y3 to loc_x2_y2, followed by a move from loc_x2_y1 to loc_x2_y0, then to loc_x3_y0, then to loc_x3_y1, and finally from loc_x3_y1 to loc_x3_y2. Is the action of moving from loc_x1_y3 to loc_x2_y2 executable at step 15, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y1 to loc_x0_y2, loc_x0_y1 to loc_x1_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y2 to loc_x0_y3, loc_x0_y2 to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3, loc_x0_y3 to loc_x0_y4, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x0_y4 to loc_x0_y5, loc_x0_y4 and loc_x0_y5, loc_x0_y5 and loc_x1_y5, loc_x1_y0 to loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 to loc_x1_y3, loc_x1_y2 to loc_x2_y2, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 to loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 to loc_x0_y4, loc_x1_y4 to loc_x1_y5, loc_x1_y5 to loc_x2_y5, loc_x2_y0 to loc_x2_y1, loc_x2_y1 to loc_x1_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y2 to loc_x2_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x2_y5, loc_x2_y5 to loc_x1_y5, loc_x2_y5 to loc_x2_y4, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x3_y1, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y0, loc_x3_y1 to loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, the robot is at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y2, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x2_y4, loc_x1_y5 and loc_x0_y5, loc_x1_y5 and loc_x1_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x3_y4, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, and loc_x3_y3 and loc_x3_y4."}
{"question_id": "1799ce32-409b-4f51-bb52-c01475034022", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: robot moves from loc_x0_y3 to loc_x0_y4. Is the action: moves from loc_x0_y3 to loc_x0_y4 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the planned actions are as follows: the robot will move from loc_x0_y3 to loc_x0_y4. Is the action of moving from loc_x0_y3 to loc_x0_y4 executable at step 1, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "51b938d1-a285-486d-b79e-66d3eaba28a7", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: robot moves from loc_x1_y0 to loc_x0_y0. Is it possible to execute it, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x5_y3 are connected, loc_x5_y4 is connected to loc_x4_y4, robot is at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3 and there is a connection between loc_x5_y3 and loc_x5_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, the following actions are proposed: the robot will move from loc_x1_y0 to loc_x0_y0. Can this action be executed, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x0_y0, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is also connected to loc_x1_y2, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 is connected to loc_x4_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 is connected to loc_x5_y3, loc_x5_y4 is connected to loc_x4_y4, the robot is currently at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3, and there is a connection between loc_x5_y3 and loc_x5_y2."}
{"question_id": "fd7f96c2-18b3-49cd-af58-aa0a9567c494", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: moves from loc_x1_y0 to loc_x0_y0. Is the action: robot moves from loc_x1_y0 to loc_x0_y0 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x5_y3 are connected, loc_x5_y4 is connected to loc_x4_y4, robot is at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3 and there is a connection between loc_x5_y3 and loc_x5_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the planned actions are as follows: moving from loc_x1_y0 to loc_x0_y0. Is the action of the robot moving from loc_x1_y0 to loc_x0_y0 executable at step 1, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x5_y3 are connected, loc_x5_y4 is connected to loc_x4_y4, the robot's current location is loc_x1_y0, a path exists between loc_x0_y1 and loc_x1_y1, a path exists between loc_x0_y2 and loc_x0_y3, a path exists between loc_x0_y3 and loc_x0_y4, a path exists between loc_x0_y4 and loc_x1_y4, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y1 and loc_x2_y1, a path exists between loc_x2_y1 and loc_x3_y1, a path exists between loc_x2_y3 and loc_x2_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x3_y1, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y3 and loc_x3_y2, a path exists between loc_x4_y0 and loc_x4_y1, a path exists between loc_x4_y0 and loc_x5_y0, a path exists between loc_x4_y1 and loc_x3_y1, a path exists between loc_x4_y1 and loc_x5_y1, a path exists between loc_x4_y2 and loc_x4_y1, a path exists between loc_x4_y2 and loc_x5_y2, a path exists between loc_x4_y3 and loc_x3_y3, a path exists between loc_x4_y3 and loc_x4_y4, a path exists between loc_x4_y3 and loc_x5_y3, a path exists between loc_x4_y4 and loc_x3_y4, a path exists between loc_x4_y4 and loc_x4_y3, a path exists between loc_x4_y4 and loc_x5_y4, a path exists between loc_x5_y1 and loc_x4_y1, a path exists between loc_x5_y3 and loc_x4_y3, and a path exists between loc_x5_y3 and loc_x5_y2."}
{"question_id": "d842a77f-2b9b-46f9-b71a-1c1018dc2580", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: robot moves from loc_x2_y4 to loc_x2_y0. Is the action: moves to loc_x2_y0 from loc_x2_y4 executable at step 1, True or False?", "answer": "False", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the planned actions are as follows: the robot will move from loc_x2_y4 to loc_x2_y0. Is the action of moving from loc_x2_y4 to loc_x2_y0 executable at step 1, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2 and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "554b5629-a5f7-4550-b0c7-5167198d3c65", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from loc_x0_y4, the robot moves to loc_x0_y5, moves to loc_x1_y1 from loc_x1_y0, moves to loc_x1_y2 from loc_x1_y1, robot moves from loc_x0_y0 to loc_x1_y0, robot moves from loc_x0_y1 to loc_x0_y0, robot moves from loc_x0_y2 to loc_x0_y1, robot moves from loc_x0_y2 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y5 to loc_x1_y5 and robot moves from loc_x2_y5 to loc_x3_y0. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x0_y2, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y5, loc_x0_y5 and loc_x0_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x2_y2, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Based on the initial condition, the following actions are scheduled: starting from loc_x0_y4, the robot will proceed to loc_x0_y5, then from loc_x1_y0 it will move to loc_x1_y1, followed by a move from loc_x1_y1 to loc_x1_y2. Additionally, the robot will move from loc_x0_y0 to loc_x1_y0, from loc_x0_y1 to loc_x0_y0, from loc_x0_y2 to loc_x0_y1, from loc_x0_y2 to loc_x0_y3, from loc_x0_y3 to loc_x0_y4, from loc_x0_y5 to loc_x1_y5, and from loc_x2_y5 to loc_x3_y0. Can these actions be executed, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y1 to loc_x0_y2, loc_x0_y1 to loc_x1_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y2 to loc_x0_y3, loc_x0_y2 to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3, loc_x0_y3 to loc_x0_y4, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x0_y4 to loc_x0_y5, loc_x0_y4 and loc_x0_y5, loc_x0_y5 and loc_x1_y5, loc_x1_y0 to loc_x2_y0, loc_x0_y1 and loc_x1_y1, loc_x0_y2 and loc_x1_y2, loc_x1_y2 to loc_x1_y3, loc_x1_y2 to loc_x2_y2, loc_x0_y3 and loc_x1_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 to loc_x2_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y4 to loc_x0_y4, loc_x1_y4 to loc_x1_y5, loc_x1_y5 to loc_x2_y5, loc_x2_y0 to loc_x2_y1, loc_x2_y1 to loc_x1_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y2 to loc_x2_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y4 and loc_x2_y5, loc_x2_y5 to loc_x1_y5, loc_x2_y5 to loc_x2_y4, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x3_y1, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y0, loc_x3_y1 to loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3.\n\nAdditionally, the robot is at loc_x0_y2. Furthermore, connections exist between the following locations: loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y2, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x2_y4, loc_x1_y5 and loc_x0_y5, loc_x1_y5 and loc_x1_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x3_y4, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, and loc_x3_y3 and loc_x3_y4."}
{"question_id": "b94809cd-82fd-4bbb-a1f3-ccea366a01b9", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: moves to loc_x3_y2 from loc_x4_y2, from loc_x3_y2, the robot moves to loc_x2_y2, from loc_x2_y2, the robot moves to loc_x2_y1, robot moves from loc_x2_y1 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, from loc_x4_y0, the robot moves to loc_x3_y0, moves from loc_x3_y0 to loc_x2_y0, from loc_x2_y0, the robot moves to loc_x1_y0, moves from loc_x1_y0 to loc_x0_y0, robot moves from loc_x0_y0 to loc_x0_y1, robot moves from loc_x0_y1 to loc_x1_y1, moves from loc_x1_y1 to loc_x1_y2, robot moves from loc_x1_y2 to loc_x1_y3, moves from loc_x1_y3 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x1_y4, moves to loc_x2_y4 from loc_x1_y4 and robot moves from loc_x2_y4 to loc_x2_y3. Is the action: robot moves from loc_x2_y0 to loc_x1_y0 executable at step 9, True or False?", "answer": "True", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, robot is at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y4 and loc_x4_y4 and there is a connection between loc_x4_y1 and loc_x3_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following sequence of actions is planned for steps 1 through 19: the robot will move from loc_x4_y2 to loc_x3_y2, then from loc_x3_y2 to loc_x2_y2, followed by a move from loc_x2_y2 to loc_x2_y1, then from loc_x2_y1 to loc_x3_y1, from loc_x3_y1 to loc_x4_y1, from loc_x4_y1 to loc_x4_y0, from loc_x4_y0 to loc_x3_y0, from loc_x3_y0 to loc_x2_y0, from loc_x2_y0 to loc_x1_y0, from loc_x1_y0 to loc_x0_y0, then from loc_x0_y0 to loc_x0_y1, from loc_x0_y1 to loc_x1_y1, from loc_x1_y1 to loc_x1_y2, from loc_x1_y2 to loc_x1_y3, from loc_x1_y3 to loc_x0_y3, from loc_x0_y3 to loc_x0_y4, from loc_x0_y4 to loc_x1_y4, from loc_x1_y4 to loc_x2_y4, and finally from loc_x2_y4 to loc_x2_y3. Is the action of moving the robot from loc_x2_y0 to loc_x1_y0 executable at step 9, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y0 to loc_x1_y0, loc_x0_y1 to loc_x1_y1, loc_x0_y3 to loc_x0_y4, loc_x0_y3 to loc_x1_y3, loc_x0_y4 to loc_x0_y3, loc_x1_y0 to loc_x0_y0, loc_x1_y0 to loc_x2_y0, loc_x1_y0 to loc_x1_y1, loc_x1_y1 to loc_x0_y1, loc_x1_y1 to loc_x1_y2, loc_x1_y1 to loc_x2_y1, loc_x1_y2 to loc_x2_y2, loc_x1_y3 to loc_x0_y3, loc_x1_y3 to loc_x2_y3, loc_x1_y3 to loc_x1_y2, loc_x1_y4 to loc_x0_y4, loc_x2_y0 to loc_x2_y1, loc_x2_y0 to loc_x1_y0, loc_x2_y1 to loc_x2_y2, loc_x2_y2 to loc_x2_y1, loc_x2_y2 to loc_x2_y3, loc_x2_y2 to loc_x1_y2, loc_x2_y2 to loc_x3_y2, loc_x2_y3 to loc_x2_y2, loc_x2_y3 to loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y3 to loc_x1_y3, loc_x2_y4 to loc_x1_y4, loc_x2_y4 to loc_x3_y4, loc_x3_y0 to loc_x3_y1, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x4_y0, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y2, loc_x3_y1 to loc_x4_y1, loc_x3_y2 to loc_x2_y2, loc_x3_y2 to loc_x3_y3, loc_x3_y2 to loc_x4_y2, loc_x3_y3 to loc_x3_y2, loc_x3_y3 to loc_x2_y3, loc_x3_y3 to loc_x3_y4, loc_x3_y4 to loc_x3_y3, loc_x3_y4 to loc_x2_y4, loc_x4_y0 to loc_x4_y1, loc_x4_y0 to loc_x3_y0, loc_x4_y1 to loc_x4_y0, loc_x4_y1 to loc_x4_y2, loc_x4_y2 to loc_x3_y2, loc_x4_y2 to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 to loc_x3_y4, the robot is at loc_x4_y2. Additionally, the following connections exist: loc_x0_y1 to loc_x0_y0, loc_x0_y4 to loc_x1_y4, loc_x1_y1 to loc_x1_y0, loc_x1_y2 to loc_x1_y1, loc_x1_y2 to loc_x1_y3, loc_x1_y3 to loc_x1_y4, loc_x1_y4 to loc_x1_y3, loc_x1_y4 to loc_x2_y4, loc_x2_y0 to loc_x3_y0, loc_x2_y1 to loc_x1_y1, loc_x2_y1 to loc_x2_y0, loc_x2_y1 to loc_x3_y1, loc_x2_y4 to loc_x2_y3, loc_x3_y1 to loc_x3_y0, loc_x3_y2 to loc_x3_y1, loc_x3_y4 to loc_x4_y4, and loc_x4_y1 to loc_x3_y1."}
{"question_id": "5d8f74d8-5bf8-4de5-bc6a-b6293c5f9874", "domain_name": "visitall", "instance_id": "Instance_2", "question_category": "action_executability", "question_name": "iter_1_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from loc_x1_y0, the robot moves to loc_x0_y0, from loc_x1_y1, the robot moves to loc_x2_y1, from loc_x2_y1, the robot moves to loc_x2_y0, from loc_x4_y0, the robot moves to loc_x5_y0, from loc_x4_y2, the robot moves to loc_x0_y4, from loc_x5_y0, the robot moves to loc_x5_y1, from loc_x5_y1, the robot moves to loc_x5_y2, moves to loc_x1_y1 from loc_x0_y1, moves to loc_x3_y0 from loc_x2_y0, moves to loc_x3_y2 from loc_x3_y1, moves to loc_x4_y1 from loc_x3_y1, moves to loc_x4_y3 from loc_x4_y2, moves to loc_x4_y4 from loc_x5_y4, moves to loc_x5_y3 from loc_x4_y3, moves to loc_x5_y4 from loc_x5_y3, robot moves from loc_x3_y0 to loc_x3_y1, robot moves from loc_x3_y2 to loc_x3_y1, robot moves from loc_x4_y1 to loc_x4_y0 and robot moves from loc_x5_y2 to loc_x4_y2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x0_y1 are connected, loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y2 are connected, loc_x0_y1 is connected to loc_x0_y0, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y3 and loc_x0_y2 are connected, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 and loc_x1_y1 are connected, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y4 and loc_x0_y4 are connected, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y0 and loc_x3_y0 are connected, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 and loc_x1_y1 are connected, loc_x2_y1 and loc_x2_y0 are connected, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 and loc_x1_y2 are connected, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y0 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 and loc_x4_y3 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y1 and loc_x4_y0 are connected, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 and loc_x5_y1 are connected, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 and loc_x5_y1 are connected, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 and loc_x5_y3 are connected, loc_x5_y4 is connected to loc_x4_y4, robot is at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3 and there is a connection between loc_x5_y3 and loc_x5_y2.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned: starting from loc_x1_y0, the robot will proceed to loc_x0_y0, from loc_x1_y1 it will move to loc_x2_y1, then from loc_x2_y1 it will go to loc_x2_y0, from loc_x4_y0 it will move to loc_x5_y0, from loc_x4_y2 it will proceed to loc_x0_y4, from loc_x5_y0 it will move to loc_x5_y1, then from loc_x5_y1 it will go to loc_x5_y2, it will move from loc_x0_y1 to loc_x1_y1, from loc_x2_y0 to loc_x3_y0, from loc_x3_y1 to loc_x3_y2, from loc_x3_y1 to loc_x4_y1, from loc_x4_y2 to loc_x4_y3, from loc_x5_y4 to loc_x4_y4, from loc_x4_y3 to loc_x5_y3, from loc_x5_y3 to loc_x5_y4, the robot will move from loc_x3_y0 to loc_x3_y1, from loc_x3_y2 to loc_x3_y1, from loc_x4_y1 to loc_x4_y0, and from loc_x5_y2 to loc_x4_y2. Is the execution of these actions possible, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x0_y1, loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y2, loc_x0_y1 is also connected to loc_x0_y0, loc_x0_y2 is connected to loc_x0_y1, loc_x0_y2 is also connected to loc_x1_y2, loc_x0_y3 is connected to loc_x0_y2, loc_x0_y4 is connected to loc_x0_y3, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y0 is connected to loc_x0_y0, loc_x1_y0 is marked as visited, loc_x1_y1 is connected to loc_x1_y0, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y2 is connected to loc_x0_y2, loc_x1_y2 is connected to loc_x1_y1, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y0 is connected to loc_x3_y0, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y1 is connected to loc_x2_y2, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y2 is connected to loc_x2_y1, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 is connected to loc_x3_y3, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 is connected to loc_x3_y3, loc_x3_y2 is connected to loc_x4_y2, loc_x3_y2 is connected to loc_x2_y2, loc_x3_y3 is connected to loc_x4_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 is connected to loc_x3_y3, loc_x3_y4 is connected to loc_x4_y4, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 is connected to loc_x3_y2, loc_x4_y2 is connected to loc_x4_y3, loc_x4_y3 is connected to loc_x4_y2, loc_x5_y0 is connected to loc_x5_y1, loc_x5_y0 is connected to loc_x4_y0, loc_x5_y1 is connected to loc_x5_y0, loc_x5_y1 is connected to loc_x5_y2, loc_x5_y2 is connected to loc_x5_y1, loc_x5_y2 is connected to loc_x4_y2, loc_x5_y2 is connected to loc_x5_y3, loc_x5_y3 is connected to loc_x5_y4, loc_x5_y4 is connected to loc_x5_y3, loc_x5_y4 is connected to loc_x4_y4, the robot is currently at loc_x1_y0, there is a connection between loc_x0_y1 and loc_x1_y1, there is a connection between loc_x0_y2 and loc_x0_y3, there is a connection between loc_x0_y3 and loc_x0_y4, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y3 and loc_x3_y2, there is a connection between loc_x4_y0 and loc_x4_y1, there is a connection between loc_x4_y0 and loc_x5_y0, there is a connection between loc_x4_y1 and loc_x3_y1, there is a connection between loc_x4_y1 and loc_x5_y1, there is a connection between loc_x4_y2 and loc_x4_y1, there is a connection between loc_x4_y2 and loc_x5_y2, there is a connection between loc_x4_y3 and loc_x3_y3, there is a connection between loc_x4_y3 and loc_x4_y4, there is a connection between loc_x4_y3 and loc_x5_y3, there is a connection between loc_x4_y4 and loc_x3_y4, there is a connection between loc_x4_y4 and loc_x4_y3, there is a connection between loc_x4_y4 and loc_x5_y4, there is a connection between loc_x5_y1 and loc_x4_y1, there is a connection between loc_x5_y3 and loc_x4_y3, and there is a connection between loc_x5_y3 and loc_x5_y2."}
{"question_id": "ac32a6b8-6c80-4a15-b311-6b54c86ecc51", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: robot moves from loc_x0_y2 to loc_x0_y1, moves to loc_x0_y0 from loc_x0_y1, moves from loc_x0_y0 to loc_x1_y0, from loc_x1_y0, the robot moves to loc_x1_y1, moves to loc_x1_y2 from loc_x1_y1, robot moves from loc_x1_y2 to loc_x0_y2, robot moves from loc_x0_y2 to loc_x0_y3, robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y4 to loc_x0_y5, moves from loc_x0_y5 to loc_x1_y5, from loc_x1_y5, the robot moves to loc_x1_y4, robot moves from loc_x1_y4 to loc_x1_y3, robot moves from loc_x1_y3 to loc_x2_y3, moves to loc_x2_y2 from loc_x2_y3, moves to loc_x2_y1 from loc_x2_y2, robot moves from loc_x2_y1 to loc_x2_y0, moves from loc_x2_y0 to loc_x3_y0, from loc_x3_y0, the robot moves to loc_x3_y1 and robot moves from loc_x3_y1 to loc_x3_y2. Is the action: robot moves from loc_x0_y2 to loc_x0_y1 executable at step 1, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x0_y2, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y5, loc_x0_y5 and loc_x0_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x2_y2, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 19: the robot will move from loc_x0_y2 to loc_x0_y1, then to loc_x0_y0, followed by a move to loc_x1_y0, then to loc_x1_y1, and then to loc_x1_y2. From loc_x1_y2, the robot will move back to loc_x0_y2, then to loc_x0_y3, loc_x0_y4, and loc_x0_y5. Next, it will move to loc_x1_y5, then to loc_x1_y4, loc_x1_y3, and loc_x2_y3. The robot will then proceed to loc_x2_y2, loc_x2_y1, loc_x2_y0, loc_x3_y0, loc_x3_y1, and finally to loc_x3_y2. Is the action of moving the robot from loc_x0_y2 to loc_x0_y1 executable at step 1, True or False?", "initial_state_nl_paraphrased": "Loc_x0_y0 is adjacent to loc_x0_y1, loc_x0_y1 is adjacent to loc_x0_y2, loc_x0_y1 is also adjacent to loc_x1_y1, loc_x0_y1 and loc_x0_y2 share a connection, loc_x0_y2 is adjacent to loc_x0_y3, loc_x0_y2 is also adjacent to loc_x1_y2, loc_x0_y2 has been visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is adjacent to loc_x0_y4, loc_x0_y3 and loc_x0_y4 share a connection, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is adjacent to loc_x0_y5, loc_x0_y4 and loc_x0_y5 share a connection, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is adjacent to loc_x2_y0, loc_x1_y1 and loc_x0_y1 share a connection, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is adjacent to loc_x1_y3, loc_x1_y2 is also adjacent to loc_x2_y2, loc_x1_y3 and loc_x0_y3 share a connection, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is adjacent to loc_x2_y3, loc_x1_y4 and loc_x1_y3 share a connection, loc_x1_y4 is adjacent to loc_x0_y4, loc_x1_y4 is also adjacent to loc_x1_y5, loc_x1_y5 is adjacent to loc_x2_y5, loc_x2_y0 is adjacent to loc_x2_y1, loc_x2_y1 is adjacent to loc_x1_y1, loc_x2_y2 and loc_x3_y2 share a connection, loc_x2_y2 is adjacent to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is adjacent to loc_x3_y3, loc_x2_y4 and loc_x2_y3 share a connection, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is adjacent to loc_x1_y5, loc_x2_y5 is also adjacent to loc_x2_y4, loc_x3_y0 is adjacent to loc_x2_y0, loc_x3_y0 is adjacent to loc_x3_y1, loc_x3_y1 is adjacent to loc_x2_y1, loc_x3_y1 is also adjacent to loc_x3_y0, loc_x3_y1 is adjacent to loc_x3_y2, loc_x3_y2 and loc_x2_y2 share a connection, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 share a connection, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 share a connection, the robot is currently at loc_x0_y2, there is a link between loc_x0_y0 and loc_x1_y0, there is a link between loc_x0_y1 and loc_x0_y0, there is a link between loc_x0_y3 and loc_x0_y2, there is a link between loc_x1_y0 and loc_x0_y0, there is a link between loc_x1_y0 and loc_x1_y1, there is a link between loc_x1_y1 and loc_x1_y0, there is a link between loc_x1_y1 and loc_x1_y2, there is a link between loc_x1_y1 and loc_x2_y1, there is a link between loc_x1_y2 and loc_x1_y1, there is a link between loc_x1_y3 and loc_x1_y2, there is a link between loc_x1_y4 and loc_x2_y4, there is a link between loc_x1_y5 and loc_x0_y5, there is a link between loc_x1_y5 and loc_x1_y4, there is a link between loc_x2_y0 and loc_x1_y0, there is a link between loc_x2_y0 and loc_x3_y0, there is a link between loc_x2_y1 and loc_x2_y0, there is a link between loc_x2_y1 and loc_x2_y2, there is a link between loc_x2_y1 and loc_x3_y1, there is a link between loc_x2_y2 and loc_x1_y2, there is a link between loc_x2_y2 and loc_x2_y1, there is a link between loc_x2_y3 and loc_x1_y3, there is a link between loc_x2_y3 and loc_x2_y2, there is a link between loc_x2_y4 and loc_x1_y4, there is a link between loc_x2_y4 and loc_x3_y4, there is a link between loc_x3_y2 and loc_x3_y3, there is a link between loc_x3_y3 and loc_x2_y3 and there is a link between loc_x3_y3 and loc_x3_y4."}
{"question_id": "c59f94aa-41f5-4684-ab66-60e54b7194b0", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_1_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, the following actions are planned to be performed: from loc_x0_y0, the robot moves to loc_x1_y0, from loc_x3_y4, the robot moves to loc_x3_y3, moves from loc_x1_y1 to loc_x0_y1, moves to loc_x0_y0 from loc_x0_y1, moves to loc_x1_y4 from loc_x1_y3, moves to loc_x2_y2 from loc_x2_y3, moves to loc_x2_y3 from loc_x3_y3, moves to loc_x3_y4 from loc_x2_y4, robot moves from loc_x0_y3 to loc_x0_y4, robot moves from loc_x0_y3 to loc_x1_y3, robot moves from loc_x0_y4 to loc_x0_y3, robot moves from loc_x1_y0 to loc_x2_y0, robot moves from loc_x1_y4 to loc_x2_y4, robot moves from loc_x2_y0 to loc_x3_y0, robot moves from loc_x2_y1 to loc_x1_y1, robot moves from loc_x2_y2 to loc_x2_y1, robot moves from loc_x3_y0 to loc_x3_y1, robot moves from loc_x3_y1 to loc_x3_y2 and robot moves from loc_x3_y2 to loc_x4_y2. Is it possible to execute it, True or False?", "answer": "False", "plan_length": 19, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned to be performed: starting from loc_x0_y0, the robot will proceed to loc_x1_y0, then from loc_x3_y4, it will move to loc_x3_y3, followed by a move from loc_x1_y1 to loc_x0_y1, then back to loc_x0_y0 from loc_x0_y1, then to loc_x1_y4 from loc_x1_y3, to loc_x2_y2 from loc_x2_y3, to loc_x2_y3 from loc_x3_y3, to loc_x3_y4 from loc_x2_y4, the robot will move from loc_x0_y3 to loc_x0_y4, then to loc_x1_y3 from loc_x0_y3, followed by a move from loc_x0_y4 to loc_x0_y3, then from loc_x1_y0 to loc_x2_y0, to loc_x2_y4 from loc_x1_y4, to loc_x3_y0 from loc_x2_y0, to loc_x1_y1 from loc_x2_y1, to loc_x2_y1 from loc_x2_y2, to loc_x3_y1 from loc_x3_y0, to loc_x3_y2 from loc_x3_y1, and finally to loc_x4_y2 from loc_x3_y2. Is it possible to execute it, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 and loc_x0_y4, loc_x0_y3 and loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 and loc_x1_y1 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 and loc_x1_y0 are connected, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 and loc_x4_y0 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 and loc_x3_y0 are connected, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "38337bee-d2c4-4fa4-859e-de47ae7fe11b", "domain_name": "visitall", "instance_id": "Instance_1", "question_category": "action_executability", "question_name": "iter_2_question_2", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: robot moves from loc_x0_y3 to loc_x0_y4, from loc_x0_y4, the robot moves to loc_x0_y3, robot moves from loc_x0_y3 to loc_x1_y3, moves from loc_x1_y3 to loc_x1_y4, moves from loc_x1_y4 to loc_x2_y4, from loc_x2_y4, the robot moves to loc_x3_y4, moves from loc_x3_y4 to loc_x3_y3, moves from loc_x2_y3 to loc_x2_y0, robot moves from loc_x2_y3 to loc_x2_y2 and robot moves from loc_x2_y2 to loc_x2_y1. Is the action: robot moves from loc_x2_y3 to loc_x2_y0 executable at step 8, True or False?", "answer": "False", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 and loc_x1_y0 are connected, loc_x0_y1 and loc_x0_y0 are connected, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y3 is connected to loc_x1_y3, loc_x0_y3 is marked as visited, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x1_y0 are connected, loc_x1_y1 and loc_x2_y1 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y4 and loc_x2_y4 are connected, loc_x1_y4 is connected to loc_x1_y3, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y1 and loc_x3_y1 are connected, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y1 is connected to loc_x2_y0, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y3 and loc_x1_y3 are connected, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 is connected to loc_x2_y4, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x3_y4 are connected, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x2_y3, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x2_y4 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y0 is connected to loc_x4_y1, loc_x4_y1 and loc_x3_y1 are connected, loc_x4_y1 and loc_x4_y2 are connected, robot is located at loc_x0_y3, there is a connection between loc_x0_y0 and loc_x0_y1, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x2_y0, there is a connection between loc_x1_y1 and loc_x0_y1, there is a connection between loc_x1_y4 and loc_x0_y4, there is a connection between loc_x2_y0 and loc_x2_y1, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y2 and loc_x2_y3, there is a connection between loc_x3_y0 and loc_x2_y0, there is a connection between loc_x3_y0 and loc_x3_y1, there is a connection between loc_x3_y1 and loc_x2_y1, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y1 and loc_x3_y2, there is a connection between loc_x3_y1 and loc_x4_y1, there is a connection between loc_x3_y2 and loc_x2_y2, there is a connection between loc_x3_y3 and loc_x2_y3, there is a connection between loc_x3_y4 and loc_x3_y3, there is a connection between loc_x4_y1 and loc_x4_y0, there is a connection between loc_x4_y2 and loc_x3_y2 and there is a connection between loc_x4_y2 and loc_x4_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: the robot will move from loc_x0_y3 to loc_x0_y4, then back to loc_x0_y3, followed by a move to loc_x1_y3, then to loc_x1_y4, then to loc_x2_y4, then to loc_x3_y4, then to loc_x3_y3, then to loc_x2_y0 from loc_x2_y3, then to loc_x2_y2 from loc_x2_y3, and finally to loc_x2_y1 from loc_x2_y2. Is the action of moving the robot from loc_x2_y3 to loc_x2_y0 executable at step 8, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 and loc_x1_y0, loc_x0_y0 and loc_x0_y1, loc_x0_y1 and loc_x1_y1, loc_x0_y3 is adjacent to loc_x0_y4, loc_x0_y3 is adjacent to loc_x1_y3, loc_x0_y3 has been visited, loc_x0_y3 and loc_x0_y4 are adjacent, loc_x0_y4 and loc_x1_y4 are adjacent, loc_x1_y0 is adjacent to loc_x1_y1, loc_x1_y1 is adjacent to loc_x1_y0, loc_x1_y1 is adjacent to loc_x2_y1, loc_x1_y3 is adjacent to loc_x0_y3, loc_x1_y3 is adjacent to loc_x1_y4, loc_x1_y3 is adjacent to loc_x2_y3, loc_x1_y4 is adjacent to loc_x2_y4, loc_x1_y4 is adjacent to loc_x1_y3, loc_x2_y0 is adjacent to loc_x1_y0, loc_x2_y1 is adjacent to loc_x2_y2, loc_x2_y1 is adjacent to loc_x3_y1, loc_x2_y1 is adjacent to loc_x1_y1, loc_x2_y1 is adjacent to loc_x2_y0, loc_x2_y2 is adjacent to loc_x3_y2, loc_x2_y3 is adjacent to loc_x1_y3, loc_x2_y3 is adjacent to loc_x2_y2, loc_x2_y3 is adjacent to loc_x2_y4, loc_x2_y3 is adjacent to loc_x3_y3, loc_x2_y4 is adjacent to loc_x3_y4, loc_x2_y4 is adjacent to loc_x1_y4, loc_x2_y4 is adjacent to loc_x2_y3, loc_x3_y0 is adjacent to loc_x4_y0, loc_x3_y2 is adjacent to loc_x3_y1, loc_x3_y2 is adjacent to loc_x3_y3, loc_x3_y2 is adjacent to loc_x4_y2, loc_x3_y3 is adjacent to loc_x3_y2, loc_x3_y3 is adjacent to loc_x3_y4, loc_x3_y4 is adjacent to loc_x2_y4, loc_x4_y0 is adjacent to loc_x3_y0, loc_x4_y0 is adjacent to loc_x4_y1, loc_x4_y1 is adjacent to loc_x3_y1, loc_x4_y1 is adjacent to loc_x4_y2, the robot's current location is loc_x0_y3, a path exists between loc_x0_y0 and loc_x0_y1, a path exists between loc_x1_y0 and loc_x0_y0, a path exists between loc_x1_y0 and loc_x2_y0, a path exists between loc_x1_y1 and loc_x0_y1, a path exists between loc_x1_y4 and loc_x0_y4, a path exists between loc_x2_y0 and loc_x2_y1, a path exists between loc_x2_y0 and loc_x3_y0, a path exists between loc_x2_y2 and loc_x2_y1, a path exists between loc_x2_y2 and loc_x2_y3, a path exists between loc_x3_y0 and loc_x2_y0, a path exists between loc_x3_y0 and loc_x3_y1, a path exists between loc_x3_y1 and loc_x2_y1, a path exists between loc_x3_y1 and loc_x3_y0, a path exists between loc_x3_y1 and loc_x3_y2, a path exists between loc_x3_y1 and loc_x4_y1, a path exists between loc_x3_y2 and loc_x2_y2, a path exists between loc_x3_y3 and loc_x2_y3, a path exists between loc_x3_y4 and loc_x3_y3, a path exists between loc_x4_y1 and loc_x4_y0, a path exists between loc_x4_y2 and loc_x3_y2, and a path exists between loc_x4_y2 and loc_x4_y1."}
{"question_id": "58584118-206e-42db-a6d3-846c6e03c180", "domain_name": "visitall", "instance_id": "Instance_3", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: moves to loc_x3_y2 from loc_x4_y2, moves from loc_x3_y2 to loc_x2_y2, robot moves from loc_x2_y2 to loc_x2_y1, robot moves from loc_x2_y1 to loc_x3_y1, from loc_x3_y1, the robot moves to loc_x4_y1, robot moves from loc_x4_y1 to loc_x4_y0, robot moves from loc_x4_y0 to loc_x3_y0, moves from loc_x3_y0 to loc_x2_y0, robot moves from loc_x2_y0 to loc_x1_y0 and from loc_x1_y0, the robot moves to loc_x0_y0. Is the action: robot moves from loc_x1_y0 to loc_x0_y0 executable at step 10, True or False?", "answer": "True", "plan_length": 10, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y0 is connected to loc_x1_y0, loc_x0_y1 and loc_x1_y1 are connected, loc_x0_y3 and loc_x0_y4 are connected, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y4 and loc_x0_y3 are connected, loc_x1_y0 and loc_x0_y0 are connected, loc_x1_y0 and loc_x2_y0 are connected, loc_x1_y0 is connected to loc_x1_y1, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y1 is connected to loc_x1_y2, loc_x1_y1 is connected to loc_x2_y1, loc_x1_y2 and loc_x2_y2 are connected, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x2_y3 are connected, loc_x1_y3 is connected to loc_x1_y2, loc_x1_y4 is connected to loc_x0_y4, loc_x2_y0 and loc_x2_y1 are connected, loc_x2_y0 is connected to loc_x1_y0, loc_x2_y1 and loc_x2_y2 are connected, loc_x2_y2 and loc_x2_y1 are connected, loc_x2_y2 and loc_x2_y3 are connected, loc_x2_y2 is connected to loc_x1_y2, loc_x2_y2 is connected to loc_x3_y2, loc_x2_y3 and loc_x2_y2 are connected, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 and loc_x3_y3 are connected, loc_x2_y3 is connected to loc_x1_y3, loc_x2_y4 is connected to loc_x1_y4, loc_x2_y4 is connected to loc_x3_y4, loc_x3_y0 and loc_x3_y1 are connected, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x4_y0, loc_x3_y1 and loc_x2_y1 are connected, loc_x3_y1 and loc_x3_y2 are connected, loc_x3_y1 is connected to loc_x4_y1, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y3 are connected, loc_x3_y2 and loc_x4_y2 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y3 is connected to loc_x2_y3, loc_x3_y3 is connected to loc_x3_y4, loc_x3_y4 and loc_x3_y3 are connected, loc_x3_y4 is connected to loc_x2_y4, loc_x4_y0 and loc_x4_y1 are connected, loc_x4_y0 is connected to loc_x3_y0, loc_x4_y1 is connected to loc_x4_y0, loc_x4_y1 is connected to loc_x4_y2, loc_x4_y2 and loc_x3_y2 are connected, loc_x4_y2 is connected to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 and loc_x3_y4 are connected, robot is at loc_x4_y2, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y4 and loc_x1_y4, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y2 and loc_x1_y3, there is a connection between loc_x1_y3 and loc_x1_y4, there is a connection between loc_x1_y4 and loc_x1_y3, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x1_y1, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y4 and loc_x2_y3, there is a connection between loc_x3_y1 and loc_x3_y0, there is a connection between loc_x3_y2 and loc_x3_y1, there is a connection between loc_x3_y4 and loc_x4_y4 and there is a connection between loc_x4_y1 and loc_x3_y1.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Given the initial condition, the following actions are planned for steps 1 through 10: the robot will move from loc_x4_y2 to loc_x3_y2, then to loc_x2_y2, followed by a move to loc_x2_y1, then to loc_x3_y1, then to loc_x4_y1, then to loc_x4_y0, then to loc_x3_y0, then to loc_x2_y0, then to loc_x1_y0, and finally to loc_x0_y0. Is the action of moving the robot from loc_x1_y0 to loc_x0_y0 executable at step 10, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y0 to loc_x1_y0, loc_x0_y1 to loc_x1_y1, loc_x0_y3 to loc_x0_y4, loc_x0_y3 to loc_x1_y3, loc_x0_y4 to loc_x0_y3, loc_x1_y0 to loc_x0_y0, loc_x1_y0 to loc_x2_y0, loc_x1_y0 to loc_x1_y1, loc_x1_y1 to loc_x0_y1, loc_x1_y1 to loc_x1_y2, loc_x1_y1 to loc_x2_y1, loc_x1_y2 to loc_x2_y2, loc_x1_y3 to loc_x0_y3, loc_x1_y3 to loc_x2_y3, loc_x1_y3 to loc_x1_y2, loc_x1_y4 to loc_x0_y4, loc_x2_y0 to loc_x2_y1, loc_x2_y0 to loc_x1_y0, loc_x2_y1 to loc_x2_y2, loc_x2_y2 to loc_x2_y1, loc_x2_y2 to loc_x2_y3, loc_x2_y2 to loc_x1_y2, loc_x2_y2 to loc_x3_y2, loc_x2_y3 to loc_x2_y2, loc_x2_y3 to loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y3 to loc_x1_y3, loc_x2_y4 to loc_x1_y4, loc_x2_y4 to loc_x3_y4, loc_x3_y0 to loc_x3_y1, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x4_y0, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y2, loc_x3_y1 to loc_x4_y1, loc_x3_y2 to loc_x2_y2, loc_x3_y2 to loc_x3_y3, loc_x3_y2 to loc_x4_y2, loc_x3_y3 to loc_x3_y2, loc_x3_y3 to loc_x2_y3, loc_x3_y3 to loc_x3_y4, loc_x3_y4 to loc_x3_y3, loc_x3_y4 to loc_x2_y4, loc_x4_y0 to loc_x4_y1, loc_x4_y0 to loc_x3_y0, loc_x4_y1 to loc_x4_y0, loc_x4_y1 to loc_x4_y2, loc_x4_y2 to loc_x3_y2, loc_x4_y2 to loc_x4_y1, loc_x4_y2 is visited, loc_x4_y4 to loc_x3_y4, the robot is at loc_x4_y2. Additionally, the following connections exist: loc_x0_y1 to loc_x0_y0, loc_x0_y4 to loc_x1_y4, loc_x1_y1 to loc_x1_y0, loc_x1_y2 to loc_x1_y1, loc_x1_y2 to loc_x1_y3, loc_x1_y3 to loc_x1_y4, loc_x1_y4 to loc_x1_y3, loc_x1_y4 to loc_x2_y4, loc_x2_y0 to loc_x3_y0, loc_x2_y1 to loc_x1_y1, loc_x2_y1 to loc_x2_y0, loc_x2_y1 to loc_x3_y1, loc_x2_y4 to loc_x2_y3, loc_x3_y1 to loc_x3_y0, loc_x3_y2 to loc_x3_y1, loc_x3_y4 to loc_x4_y4, and loc_x4_y1 to loc_x3_y1."}
{"question_id": "1c1fe75e-7b7e-4d53-a235-9fa58cb84b97", "domain_name": "visitall", "instance_id": "Instance_4", "question_category": "action_executability", "question_name": "iter_2_question_1", "fluent_type": "all_fluents", "answer_type": "true_false_answer", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: from loc_x0_y2, the robot moves to loc_x0_y1. Is the action: moves to loc_x0_y1 from loc_x0_y2 executable at step 1, True or False?", "answer": "True", "plan_length": 1, "initial_state_nl": "Loc_x0_y0 is connected to loc_x0_y1, loc_x0_y1 is connected to loc_x0_y2, loc_x0_y1 is connected to loc_x1_y1, loc_x0_y2 and loc_x0_y1 are connected, loc_x0_y2 is connected to loc_x0_y3, loc_x0_y2 is connected to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3 are connected, loc_x0_y3 is connected to loc_x0_y4, loc_x0_y4 and loc_x0_y3 are connected, loc_x0_y4 and loc_x1_y4 are connected, loc_x0_y4 is connected to loc_x0_y5, loc_x0_y5 and loc_x0_y4 are connected, loc_x0_y5 and loc_x1_y5 are connected, loc_x1_y0 is connected to loc_x2_y0, loc_x1_y1 and loc_x0_y1 are connected, loc_x1_y2 and loc_x0_y2 are connected, loc_x1_y2 is connected to loc_x1_y3, loc_x1_y2 is connected to loc_x2_y2, loc_x1_y3 and loc_x0_y3 are connected, loc_x1_y3 and loc_x1_y4 are connected, loc_x1_y3 is connected to loc_x2_y3, loc_x1_y4 and loc_x1_y3 are connected, loc_x1_y4 is connected to loc_x0_y4, loc_x1_y4 is connected to loc_x1_y5, loc_x1_y5 is connected to loc_x2_y5, loc_x2_y0 is connected to loc_x2_y1, loc_x2_y1 is connected to loc_x1_y1, loc_x2_y2 and loc_x3_y2 are connected, loc_x2_y2 is connected to loc_x2_y3, loc_x2_y3 and loc_x2_y4 are connected, loc_x2_y3 is connected to loc_x3_y3, loc_x2_y4 and loc_x2_y3 are connected, loc_x2_y4 and loc_x2_y5 are connected, loc_x2_y5 is connected to loc_x1_y5, loc_x2_y5 is connected to loc_x2_y4, loc_x3_y0 is connected to loc_x2_y0, loc_x3_y0 is connected to loc_x3_y1, loc_x3_y1 is connected to loc_x2_y1, loc_x3_y1 is connected to loc_x3_y0, loc_x3_y1 is connected to loc_x3_y2, loc_x3_y2 and loc_x2_y2 are connected, loc_x3_y2 and loc_x3_y1 are connected, loc_x3_y3 and loc_x3_y2 are connected, loc_x3_y4 and loc_x2_y4 are connected, loc_x3_y4 and loc_x3_y3 are connected, robot is located at loc_x0_y2, there is a connection between loc_x0_y0 and loc_x1_y0, there is a connection between loc_x0_y1 and loc_x0_y0, there is a connection between loc_x0_y3 and loc_x0_y2, there is a connection between loc_x1_y0 and loc_x0_y0, there is a connection between loc_x1_y0 and loc_x1_y1, there is a connection between loc_x1_y1 and loc_x1_y0, there is a connection between loc_x1_y1 and loc_x1_y2, there is a connection between loc_x1_y1 and loc_x2_y1, there is a connection between loc_x1_y2 and loc_x1_y1, there is a connection between loc_x1_y3 and loc_x1_y2, there is a connection between loc_x1_y4 and loc_x2_y4, there is a connection between loc_x1_y5 and loc_x0_y5, there is a connection between loc_x1_y5 and loc_x1_y4, there is a connection between loc_x2_y0 and loc_x1_y0, there is a connection between loc_x2_y0 and loc_x3_y0, there is a connection between loc_x2_y1 and loc_x2_y0, there is a connection between loc_x2_y1 and loc_x2_y2, there is a connection between loc_x2_y1 and loc_x3_y1, there is a connection between loc_x2_y2 and loc_x1_y2, there is a connection between loc_x2_y2 and loc_x2_y1, there is a connection between loc_x2_y3 and loc_x1_y3, there is a connection between loc_x2_y3 and loc_x2_y2, there is a connection between loc_x2_y4 and loc_x1_y4, there is a connection between loc_x2_y4 and loc_x3_y4, there is a connection between loc_x3_y2 and loc_x3_y3, there is a connection between loc_x3_y3 and loc_x2_y3 and there is a connection between loc_x3_y3 and loc_x3_y4.", "fluent_sign_question": "pos_neg", "question_subcategories": [], "question_paraphrased": "Considering the initial condition, for steps 1 through 1, the following actions are planned: the robot will move from loc_x0_y2 to loc_x0_y1. Is the action of moving from loc_x0_y2 to loc_x0_y1 executable at step 1, True or False?", "initial_state_nl_paraphrased": "The following locations are connected: loc_x0_y0 to loc_x0_y1, loc_x0_y1 to loc_x0_y2, loc_x0_y1 to loc_x1_y1, loc_x0_y1 and loc_x0_y2, loc_x0_y2 to loc_x0_y3, loc_x0_y2 to loc_x1_y2, loc_x0_y2 is visited, loc_x0_y3 and loc_x1_y3, loc_x0_y3 to loc_x0_y4, loc_x0_y3 and loc_x0_y4, loc_x0_y4 and loc_x1_y4, loc_x0_y4 to loc_x0_y5, loc_x0_y4 and loc_x0_y5, loc_x0_y5 and loc_x1_y5, loc_x1_y0 to loc_x2_y0, loc_x1_y1 and loc_x0_y1, loc_x1_y2 and loc_x0_y2, loc_x1_y2 to loc_x1_y3, loc_x1_y2 to loc_x2_y2, loc_x1_y3 and loc_x0_y3, loc_x1_y3 and loc_x1_y4, loc_x1_y3 to loc_x2_y3, loc_x1_y4 and loc_x1_y3, loc_x1_y4 to loc_x0_y4, loc_x1_y4 to loc_x1_y5, loc_x1_y5 to loc_x2_y5, loc_x2_y0 to loc_x2_y1, loc_x2_y1 to loc_x1_y1, loc_x2_y2 and loc_x3_y2, loc_x2_y2 to loc_x2_y3, loc_x2_y3 and loc_x2_y4, loc_x2_y3 to loc_x3_y3, loc_x2_y4 and loc_x2_y3, loc_x2_y4 and loc_x2_y5, loc_x2_y5 to loc_x1_y5, loc_x2_y5 to loc_x2_y4, loc_x3_y0 to loc_x2_y0, loc_x3_y0 to loc_x3_y1, loc_x3_y1 to loc_x2_y1, loc_x3_y1 to loc_x3_y0, loc_x3_y1 to loc_x3_y2, loc_x3_y2 and loc_x2_y2, loc_x3_y2 and loc_x3_y1, loc_x3_y3 and loc_x3_y2, loc_x3_y4 and loc_x2_y4, loc_x3_y4 and loc_x3_y3, the robot is at loc_x0_y2, a connection exists between loc_x0_y0 and loc_x1_y0, loc_x0_y1 and loc_x0_y0, loc_x0_y3 and loc_x0_y2, loc_x1_y0 and loc_x0_y0, loc_x1_y0 and loc_x1_y1, loc_x1_y1 and loc_x1_y0, loc_x1_y1 and loc_x1_y2, loc_x1_y1 and loc_x2_y1, loc_x1_y2 and loc_x1_y1, loc_x1_y3 and loc_x1_y2, loc_x1_y4 and loc_x2_y4, loc_x1_y5 and loc_x0_y5, loc_x1_y5 and loc_x1_y4, loc_x2_y0 and loc_x1_y0, loc_x2_y0 and loc_x3_y0, loc_x2_y1 and loc_x2_y0, loc_x2_y1 and loc_x2_y2, loc_x2_y1 and loc_x3_y1, loc_x2_y2 and loc_x1_y2, loc_x2_y2 and loc_x2_y1, loc_x2_y3 and loc_x1_y3, loc_x2_y3 and loc_x2_y2, loc_x2_y4 and loc_x1_y4, loc_x2_y4 and loc_x3_y4, loc_x3_y2 and loc_x3_y3, loc_x3_y3 and loc_x2_y3, and loc_x3_y3 and loc_x3_y4."}
