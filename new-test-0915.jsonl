{"type": "tree_metadata", "domain": "blocksworld", "initial_state": "The white block is on top of the olive block. The turquoise block is on the table. The olive block is on the table. The red block is on top of the turquoise block. The white block is clear. The magenta block is clear. The magenta block is on the table. The red block is clear.", "query": "the turquoise block is not on the table and the olive block is not on the table", "root_id": 0, "total_nodes": 1, "active_nodes": 1, "goal_nodes": 0, "solution": null, "timestamp": "250915_16:19:08", "model": "gpt-4o"}
{"type": "node", "node_id": 0, "parent_id": -1, "children_id": [], "state": "The white block is on top of the olive block. The turquoise block is on the table. The olive block is on the table. The red block is on top of the turquoise block. The white block is clear. The magenta block is clear. The magenta block is on the table. The red block is clear.", "action_taken": [], "history_actions": [], "query": "the turquoise block is not on the table and the olive block is not on the table", "g_cost": 0.0, "h_cost": 1.0, "f_cost": 1.0, "flag": false, "status": true, "pruned_reason": null, "available_actions": [["Moving the red block from the turquoise block onto the table", 4.6], ["Moving the red block from the turquoise block to the magenta block", 4.3], ["Moving the white block from the olive block onto the table", 4.2], ["Moving the white block from the olive block to the magenta block", 4.2], ["Moving the white block from the olive block to the red block", 2.8], ["Moving the red block from the turquoise block to the white block", 2.8], ["Moving the magenta block from the table to the white block", 1.8], ["Moving the magenta block from the table to the red block", 1.5]]}
