import time
import os
import json
import random
import requests
import ssl
from typing import Optional
import openai
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


"""
Utility functions
"""


def DataLoader(file_path) -> list:
    """
    Load data(*.jsonl file) from file_path, convert it into a list.
    """
    with open(file_path, "r", encoding='utf-8') as file:
        dataset = [json.loads(line) for line in file]
    return dataset



def LabelExtracter(response, **kwargs) ->int:
    """
    Extract the label from the response.
    For isSimpleAns=True(Zeroshot), models answer only contains true or false.
    For other cases, models answer contains more information,
    but the final paragraph is "Final Answer: True" or "Final Answer: False".

    Parameters
    ----------
    response : str, model's answer.
    kwargs : dict, additional parameters
    - answer_form: str. `bool` or `mcq`

    Return
    ------
    label : int, 1=True and 0=False.
    If could not determinte the label, return the response itself for manual evaluation.

    """

    answer_form = kwargs.get("answer_form", "bool")

    #Note: response=null if request failed
    if not response or not isinstance(response, str):
        return "Request failed. Null string is recived. Exclude this data sample."

    # Get the last non-empty line, converting to lowercase
    lines = [line.strip() for line in response.lower().split('\n') if line.strip()]
    if not lines:
        return "Request failed. Null string is recived. Exclude this data sample."

    last_line = lines[-1]

    # Extract answer part after "final answer:" if present
    answer_part = last_line.split("final answer:")[-1].strip() if "final answer:" in last_line else last_line

    # Clean the answer part
    answer_part = answer_part.strip().rstrip('.,;:!? ')

    # Direct true/false check
    if answer_form=="bool":
        if answer_part in ['true', 'yes', '1']:
            return 1
        if answer_part in ['false', 'no', '0']:
            return 0
    else:
        if answer_part in ['a', 'b', 'c', 'd']:
            return answer_part.capitalize()
        else:
            print(f"LabelExtracter: Could not extract MCQ answer from '{answer_part}'")

    return "Request failed. Null string is recived. Exclude this data sample."



def RequestSender(prompt: str,
                 model: str = "gpt-4o",
                 api_key: str = 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39',
                 base_url: str = 'https://api.v3.cm/v1',
                 max_retries: int = 6,
                 initial_wait: float = 1.0,
                 timeout: float = 60.0) -> str:
    """
    Description
    -----------
    Send request by calling model API with retry mechanism using OpenAI library

    Parameters
    ----------
    prompt : prompt containing the data
    model : model used, gpt-4o by default
    api_key : api credential
    base_url : api proxy base URL
    max_retries : maximum number of retries
    initial_wait : initial wait time between retries (seconds)

    Returns
    -------
    answer : str, answer from model
    """

    # Ensure base_url has a protocol
    if base_url != "null" and not base_url.startswith(('http://', 'https://')):
        base_url = 'https://' + base_url

    client = openai.OpenAI(
        api_key=api_key,
        base_url=base_url,
        timeout=timeout
    )

    # retry
    session = requests.Session()
    retry_strategy = Retry(
        total=max_retries,
        backoff_factor=initial_wait,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["POST"]
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)


    client.requestssession = session

    for attempt in range(max_retries + 1):
        try:

            try:
                response = client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": prompt}]
                )

                # Check if response is None before accessing its attributes
                if response is None:
                    return "Request Failed: Received None response"
                # Check if choices list is empty
                if not response.choices:
                    return "Request Failed: No choices in response"
                # Check if message or content is None
                if response.choices[0].message is None or response.choices[0].message.content is None:
                    return "Request Failed: Message or content is None"
                return response.choices[0].message.content.strip()
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                # Wait and retry
                wait_time = (2 ** attempt) * initial_wait + random.uniform(0, 1)
                if attempt < max_retries:
                    print(f"Waiting {wait_time:.2f} seconds before retrying...")
                    time.sleep(wait_time)
                    continue
                else:
                    return "Request Failed: JSON decode error"

        except (openai.APIError, openai.APIConnectionError,
                openai.RateLimitError, openai.APITimeoutError,
                openai.InternalServerError, requests.exceptions.RequestException,
                json.JSONDecodeError, requests.exceptions.Timeout,
                requests.exceptions.ConnectionError, ssl.SSLError,
                ConnectionResetError, ConnectionAbortedError, ConnectionRefusedError,
                TimeoutError) as e:
            wait_time = (2 ** attempt) * initial_wait + random.uniform(0, 1)

            error_type = type(e).__name__
            if attempt < max_retries:
                print(f"Attempt {attempt + 1}/{max_retries + 1} failed with {error_type}: {str(e)}")
                print(f"Waiting {wait_time:.2f} seconds before retrying...")
                time.sleep(wait_time)
            else:
                print(f"All {max_retries + 1} attempts failed. Last error ({error_type}): {str(e)}")
                return f"Request Failed: {error_type} - {str(e)}"

    return "Request Failed"



def ExtractResponse(response: str, response_type: str = 'ACTION'):
    """
    Description
    -----------
    Extract information from the last paragraph of LLM response based on response_type.
    For ACTION: splits the last paragraph by ". " to get individual actions.
    For SCORE: extracts a float score (0-5) from the last paragraph.
    For STATE: extracts the state description from the last paragraph.

    Parameters
    ----------
    response : str, the original LLM response
    response_type: `ACTION`, `SCORE`, `STATE`, `BOOL`.

    Returns
    -------
    For ACTION: list, a list of extracted actions from the last paragraph
    For SCORE: float, LLM's score about this action (0-5)
    For STATE: str, the new state after taking an action
    For BOOL: bool, True or False
    """
    if not response or not isinstance(response, str):
        if response_type == 'ACTION':
            return []
        elif response_type == 'SCORE':
            return 0.0
        elif response_type == 'STATE':
            return ""
        elif response_type == 'BOOL':
            return False
        else:
            return ""

    # Split response into paragraphs (by double newlines or single newlines)
    paragraphs = [p.strip() for p in response.split('\n\n') if p.strip()]

    # If no double newlines found, try single newlines and take the last non-empty line
    if len(paragraphs) <= 1:
        lines = [line.strip() for line in response.split('\n') if line.strip()]
        if not lines:
            if response_type == 'ACTION':
                return []
            elif response_type == 'SCORE':
                return 0.0
            elif response_type == 'STATE':
                return ""
            elif response_type == 'BOOL':
                return False
            else:
                return ""
        last_paragraph = lines[-1]
    else:
        last_paragraph = paragraphs[-1]

    # If the last paragraph contains a colon, take the part after the last colon
    # This handles cases like "Final actions: action1. action2. action3." or "Score: 4.5"
    if ':' in last_paragraph:
        last_paragraph = last_paragraph.split(':')[-1].strip()

    # Handle different response types
    if response_type == 'ACTION':
        # Try different splitting patterns for actions
        actions = []

        # First try splitting by ". " (period followed by space)
        if '. ' in last_paragraph:
            actions = [action.strip() for action in last_paragraph.split('. ') if action.strip()]
        # If that doesn't work, try splitting by just "." (period)
        elif '.' in last_paragraph:
            actions = [action.strip() for action in last_paragraph.split('.') if action.strip()]
        # If no periods, treat the whole paragraph as one action
        else:
            actions = [last_paragraph.strip()] if last_paragraph.strip() else []

        # Clean up actions - remove any that are too short or don't look like actions
        cleaned_actions = []
        for action in actions:
            # Remove trailing periods
            action = action.rstrip('.')
            # Skip very short actions or empty ones
            if len(action.strip()) > 3:
                cleaned_actions.append(action.strip())

        return cleaned_actions

    elif response_type == 'SCORE':
        # Extract score from the last paragraph
        # Look for float numbers in the text
        import re

        # Clean the paragraph and look for numbers
        score_text = last_paragraph.strip().rstrip('.,;:!? ')

        # Try to find a float number (0-5 with one decimal place)
        score_pattern = r'\b([0-5](?:\.[0-9])?)\b'
        matches = re.findall(score_pattern, score_text)

        if matches:
            try:
                score = float(matches[-1])  # Take the last match
                # Ensure score is within valid range
                if 0.0 <= score <= 5.0:
                    return score
            except ValueError:
                pass

        # If no valid score found, try to extract any number
        number_pattern = r'\b(\d+(?:\.\d+)?)\b'
        numbers = re.findall(number_pattern, score_text)

        if numbers:
            try:
                score = float(numbers[-1])
                # Clamp to valid range
                score = max(0.0, min(5.0, score))
                return score
            except ValueError:
                pass

        # Default score if nothing found
        return 0.0

    elif response_type == 'BOOL':
        # Extract boolean value from the last paragraph
        # Clean the paragraph and convert to lowercase for comparison
        bool_text = last_paragraph.strip().rstrip('.,;:!? ').lower()

        # First check for exact matches
        if bool_text == 'true':
            return True
        elif bool_text == 'false':
            return False

        # Check for negations first (more specific)
        if any(phrase in bool_text for phrase in ['not true', 'not correct', 'not right']):
            return False
        elif any(phrase in bool_text for phrase in ['not false', 'not incorrect', 'not wrong']):
            return True

        # Check for false indicators first (to avoid conflicts)
        if any(word in bool_text for word in ['false', 'no', 'incorrect', 'wrong']):
            return False

        # Then check for true indicators
        if any(word in bool_text for word in ['true', 'yes', 'correct', 'right']):
            return True

        # Default to False if unclear
        return False

    else:  # STATE
        # For STATE, return the cleaned last paragraph as the new state
        state = last_paragraph.strip().rstrip('.,;:!? ')
        return state if state else ""


def Logger(data:dict, task:str, response:str, predicted_label:int, save_name:str):
    """
    Description
    -----------
    log model's answer for each data entry

    Parameters
    ----------
    data :  an data sample
    task :  the task name, 'projection', 'legality'(Executability in Paper),
                                    'planning', 'goalrecognition'
    save_name :  the name of the jsonl file
    response :  model's answer
    predicted_label :  predicted label extracted from reponse

    Return
    ------
    None, results will be saved directly
    """
    answer = {"problem_id": data["problem_id"], "answer": response,  "predicted_label": predicted_label, "label": data["label"]}

    #save answer and use '{task}-{save_name}-response.jsonl' as file name,
    #and do not need to write task in the save_name
    with open(f'{task}-{save_name}-response.jsonl', 'a', encoding='utf-8') as f:
        f.write(json.dumps(answer, ensure_ascii=False) + '\n')

    print("Log successfully.")


